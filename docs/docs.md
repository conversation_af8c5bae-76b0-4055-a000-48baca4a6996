# Protocol Documentation
<a name="top"></a>

## Table of Contents

- [category.proto](#category-proto)
    - [CatIdReq](#pb-CatIdReq)
    - [CatInfoListResp](#pb-CatInfoListResp)
    - [CatInfoResp](#pb-CatInfoResp)
    - [CatOptsListResp](#pb-CatOptsListResp)
    - [CatOptsListResp.Cat](#pb-CatOptsListResp-Cat)
    - [CatProbSaveReq](#pb-CatProbSaveReq)
    - [CatTplPrefer](#pb-CatTplPrefer)
    - [CatTplPrefer.MarkEntry](#pb-CatTplPrefer-MarkEntry)
    - [Lang](#pb-Lang)
    - [Lang.CategoryEntry](#pb-Lang-CategoryEntry)
  
    - [CatLevel](#pb-CatLevel)
  
- [common.proto](#common-proto)
    - [BaseInfo](#pb-BaseInfo)
    - [Empty](#pb-Empty)
    - [EnableReq](#pb-EnableReq)
    - [Language](#pb-Language)
    - [Language.LanguageEntry](#pb-Language-LanguageEntry)
    - [Project](#pb-Project)
    - [ProjectLang](#pb-ProjectLang)
    - [ProjectTagLib](#pb-ProjectTagLib)
    - [Props](#pb-Props)
    - [Response](#pb-Response)
    - [ServerSplit](#pb-ServerSplit)
    - [ServerSplit.ServerBtw](#pb-ServerSplit-ServerBtw)
  
- [dict.proto](#dict-proto)
    - [DictAddReq](#pb-DictAddReq)
    - [DictId](#pb-DictId)
    - [DictInfoResp](#pb-DictInfoResp)
    - [DictListResp](#pb-DictListResp)
    - [DictListResp.Dict](#pb-DictListResp-Dict)
    - [DictOptsResp](#pb-DictOptsResp)
    - [DictOptsResp.Opts](#pb-DictOptsResp-Opts)
  
- [egress.proto](#egress-proto)
    - [CatOptRequest](#pb-CatOptRequest)
  
    - [EgressAPI](#pb-EgressAPI)
  
- [egress_ticket.proto](#egress_ticket-proto)
    - [NoticeFpxRequestV3](#pb-NoticeFpxRequestV3)
    - [NoticeRequestV2](#pb-NoticeRequestV2)
    - [NoticeResp](#pb-NoticeResp)
    - [TicketDetailModel](#pb-TicketDetailModel)
    - [TkAppraiseFeedbackReq](#pb-TkAppraiseFeedbackReq)
    - [TkAppraiseReq](#pb-TkAppraiseReq)
    - [TkCommunicateReq](#pb-TkCommunicateReq)
    - [TkCreateReq](#pb-TkCreateReq)
    - [TkCreateResp](#pb-TkCreateResp)
    - [TkDetailReq](#pb-TkDetailReq)
    - [TkDetailResp](#pb-TkDetailResp)
    - [TkDetailResp.Commu](#pb-TkDetailResp-Commu)
    - [TkDetailResp.Reopen](#pb-TkDetailResp-Reopen)
    - [TkDetailResp.Replenish](#pb-TkDetailResp-Replenish)
    - [TkMineReq](#pb-TkMineReq)
    - [TkMineResp](#pb-TkMineResp)
    - [TkMineResp.TkList](#pb-TkMineResp-TkList)
    - [TkReplenishReq](#pb-TkReplenishReq)
  
- [enum.proto](#enum-proto)
    - [RetEnum](#pb-RetEnum)
  
    - [Bind](#pb-Bind)
    - [CommuType](#pb-CommuType)
    - [CreatorType](#pb-CreatorType)
    - [CsiLabel](#pb-CsiLabel)
    - [CsiLevel](#pb-CsiLevel)
    - [FilterEnum](#pb-FilterEnum)
    - [FilterTagEnum](#pb-FilterTagEnum)
    - [NpsLevel](#pb-NpsLevel)
    - [Origin](#pb-Origin)
    - [RelateType](#pb-RelateType)
    - [SceneType](#pb-SceneType)
    - [Status](#pb-Status)
    - [TicketEvent](#pb-TicketEvent)
    - [TicketStage](#pb-TicketStage)
    - [TicketSys](#pb-TicketSys)
    - [TkClosedRole](#pb-TkClosedRole)
    - [TkEvent](#pb-TkEvent)
    - [TkProgress](#pb-TkProgress)
    - [TkStage](#pb-TkStage)
    - [TkStatus](#pb-TkStatus)
    - [UserLoginStatus](#pb-UserLoginStatus)
    - [UserRole](#pb-UserRole)
    - [Workbench](#pb-Workbench)
  
- [group.proto](#group-proto)
    - [GroupCat](#pb-GroupCat)
    - [GroupOptsResp](#pb-GroupOptsResp)
    - [GroupOptsResp.Opts](#pb-GroupOptsResp-Opts)
    - [GroupUser](#pb-GroupUser)
    - [GroupUserStateDf](#pb-GroupUserStateDf)
    - [LoginStatus](#pb-LoginStatus)
  
- [indices.proto](#indices-proto)
    - [IndicesCfgResp](#pb-IndicesCfgResp)
    - [IndicesCfgResp.CatTypesEntry](#pb-IndicesCfgResp-CatTypesEntry)
    - [IndicesDelReq](#pb-IndicesDelReq)
    - [IndicesEnableReq](#pb-IndicesEnableReq)
    - [IndicesListResp](#pb-IndicesListResp)
    - [IndicesListResp.item](#pb-IndicesListResp-item)
    - [IndicesSaveReq](#pb-IndicesSaveReq)
  
    - [IndicesApi](#pb-IndicesApi)
  
- [label_group.proto](#label_group-proto)
    - [GroupDelReq](#pb-GroupDelReq)
    - [GroupIdReq](#pb-GroupIdReq)
    - [GroupInfoResp](#pb-GroupInfoResp)
    - [GroupListReq](#pb-GroupListReq)
    - [GroupListResp](#pb-GroupListResp)
    - [GroupListResp.Group](#pb-GroupListResp-Group)
    - [GroupSaveReq](#pb-GroupSaveReq)
    - [GroupUserReq](#pb-GroupUserReq)
    - [GroupUserResp](#pb-GroupUserResp)
    - [TagLibAddReq](#pb-TagLibAddReq)
    - [TagLibID](#pb-TagLibID)
    - [TagLibInfoResp](#pb-TagLibInfoResp)
    - [TagLibListReq](#pb-TagLibListReq)
    - [TagLibListResp](#pb-TagLibListResp)
    - [TagLibListResp.TagLib](#pb-TagLibListResp-TagLib)
    - [TagLibOptsResp](#pb-TagLibOptsResp)
    - [TagLibOptsResp.TagLib](#pb-TagLibOptsResp-TagLib)
    - [TagLibSaveReq](#pb-TagLibSaveReq)
    - [TagOptsReq](#pb-TagOptsReq)
    - [TagOptsResp](#pb-TagOptsResp)
    - [TagOptsResp.Tag](#pb-TagOptsResp-Tag)
  
- [service.proto](#service-proto)
    - [AllocGroupApi](#pb-AllocGroupApi)
    - [PunlicApi](#pb-PunlicApi)
    - [TagLibApi](#pb-TagLibApi)
    - [TicketApi](#pb-TicketApi)
  
- [tag.proto](#tag-proto)
    - [TagAddReq](#pb-TagAddReq)
    - [TagGroup](#pb-TagGroup)
    - [TagGroups](#pb-TagGroups)
    - [TagId](#pb-TagId)
    - [TagNode](#pb-TagNode)
  
- [team.proto](#team-proto)
    - [TeamOptsResp](#pb-TeamOptsResp)
    - [TeamOptsResp.Opts](#pb-TeamOptsResp-Opts)
  
- [ticket.proto](#ticket-proto)
    - [AssignmentReq](#pb-AssignmentReq)
    - [CustomerService](#pb-CustomerService)
    - [TicketCountReq](#pb-TicketCountReq)
    - [TicketCountResp](#pb-TicketCountResp)
    - [TicketDialogueInfoResp](#pb-TicketDialogueInfoResp)
    - [TicketDialogueResp](#pb-TicketDialogueResp)
    - [TicketGroupInfoResp](#pb-TicketGroupInfoResp)
    - [TicketHistResp](#pb-TicketHistResp)
    - [TicketHistoriesReq](#pb-TicketHistoriesReq)
    - [TicketHistoriesResp](#pb-TicketHistoriesResp)
    - [TicketHistoryInfo](#pb-TicketHistoryInfo)
    - [TicketIdReq](#pb-TicketIdReq)
    - [TicketInfo](#pb-TicketInfo)
    - [TicketIsUserReq](#pb-TicketIsUserReq)
    - [TicketIsUserResp](#pb-TicketIsUserResp)
    - [TicketLabel](#pb-TicketLabel)
    - [TicketPoolHistoryListReq](#pb-TicketPoolHistoryListReq)
    - [TicketPoolListResp](#pb-TicketPoolListResp)
    - [TicketPoolNewListReq](#pb-TicketPoolNewListReq)
    - [TicketPoolNewListResp](#pb-TicketPoolNewListResp)
    - [TicketPoolNewListResp.TicketPoolInfo](#pb-TicketPoolNewListResp-TicketPoolInfo)
    - [TicketPoolNewTopResp](#pb-TicketPoolNewTopResp)
    - [TicketPoolNewTopResp.UserInfoResp](#pb-TicketPoolNewTopResp-UserInfoResp)
    - [TicketPoolNewTopResp.top](#pb-TicketPoolNewTopResp-top)
    - [TicketPoolTopResp](#pb-TicketPoolTopResp)
    - [TicketRecordResp](#pb-TicketRecordResp)
    - [TicketRecordResp.Record](#pb-TicketRecordResp-Record)
    - [TicketRemarkReq](#pb-TicketRemarkReq)
    - [TicketResponse](#pb-TicketResponse)
    - [TicketRetaggingReq](#pb-TicketRetaggingReq)
    - [TicketStatusResp](#pb-TicketStatusResp)
    - [TicketTagRes](#pb-TicketTagRes)
    - [TicketTagsReq](#pb-TicketTagsReq)
    - [TicketTagsResp](#pb-TicketTagsResp)
    - [TicketTransferReq](#pb-TicketTransferReq)
    - [TicketUpgradeReq](#pb-TicketUpgradeReq)
    - [TicketUserListReq](#pb-TicketUserListReq)
    - [UserInfoResp](#pb-UserInfoResp)
  
    - [FillStatus](#pb-FillStatus)
    - [TkPoolSort](#pb-TkPoolSort)
    - [TkTransferOpType](#pb-TkTransferOpType)
    - [WorkbenchPart](#pb-WorkbenchPart)
  
- [user.proto](#user-proto)
    - [UserListResp](#pb-UserListResp)
  
- [Scalar Value Types](#scalar-value-types)



<a name="category-proto"></a>
<p align="right"><a href="#top">Top</a></p>

## category.proto



<a name="pb-CatIdReq"></a>

### CatIdReq
CatIdReq 类别Id 请求参数


| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| cat_id | [uint32](#uint32) |  | 分类ID @gotags: validate:&#34;required&#34; |






<a name="pb-CatInfoListResp"></a>

### CatInfoListResp
CatInfoListResp 三级类别列表


| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| current_page | [uint32](#uint32) |  | 当前页 |
| per_page | [uint32](#uint32) |  | 页大小 |
| total | [uint32](#uint32) |  | 总数 |
| data | [CatInfoResp](#pb-CatInfoResp) | repeated | 数据列表 |






<a name="pb-CatInfoResp"></a>

### CatInfoResp
CatInfo 类别信息


| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| cat_id | [uint32](#uint32) |  |  |
| category | [string](#string) |  | 分类名称 |
| one_level | [uint32](#uint32) |  | 一级分类 |
| second_level | [uint32](#uint32) |  | 二级分类 |
| cat_level | [uint32](#uint32) |  | 分类层级 |
| tpl_id | [uint32](#uint32) |  | 模版ID |
| priority | [uint32](#uint32) |  | 紧急度 1:高 2:中 3:低 |
| deadline | [uint64](#uint64) |  | 完成时间 |
| enable | [bool](#bool) |  | 启用 0:false 1:true |
| evaluation | [bool](#bool) |  | 是否评价 是:true 否:false |
| operator | [string](#string) |  | op @gotags: json:&#34;operator,omitempty&#34; |
| updated_at | [string](#string) |  | 更新时间 @gotags: json:&#34;updated_at,omitempty&#34; |
| langs | [Lang](#pb-Lang) |  | 多语言 @gotags: json:&#34;langs,omitempty&#34; |
| tpl_list | [CatTplPrefer](#pb-CatTplPrefer) | repeated | 个性化模板配置 |
| reply_tpl_id | [uint32](#uint32) |  | 回复模板id |
| relate_type | [uint32](#uint32) |  | 关联类型 1:表单 2:流程 |
| process_id | [uint32](#uint32) |  | 流程id |
| reopen | [bool](#bool) |  | 是否重开 1：是 0：否 |
| reopen_num | [uint32](#uint32) |  | 重开次数 |






<a name="pb-CatOptsListResp"></a>

### CatOptsListResp
CatOptsList 问题分类配置(下拉级联多选)列表


| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| data | [CatOptsListResp.Cat](#pb-CatOptsListResp-Cat) | repeated |  |






<a name="pb-CatOptsListResp-Cat"></a>

### CatOptsListResp.Cat



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| id | [uint32](#uint32) |  | 类别ID |
| label | [string](#string) |  | 类别名称 |
| level | [uint32](#uint32) |  |  |
| tpl_id | [uint32](#uint32) |  |  |
| relate_type | [uint32](#uint32) |  | 关联类型 1:表单 2:流程 |
| process_id | [uint32](#uint32) |  | 自动化流程id |
| children | [CatOptsListResp.Cat](#pb-CatOptsListResp-Cat) | repeated | 子结构 @gotags: json:&#34;children,omitempty&#34; |






<a name="pb-CatProbSaveReq"></a>

### CatProbSaveReq
CatProbSaveReq 修改三级分类 请求参数


| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| cat_id | [uint32](#uint32) |  | 分类ID @gotags: validate:&#34;required&#34; |
| category | [string](#string) |  | 分类名称 @gotags: validate:&#34;required&#34; |
| tpl_id | [uint32](#uint32) |  | 模版ID @gotags: validate:&#34;required_if=RelateType 1&#34; |
| group_id | [uint32](#uint32) |  | 技能组ID |
| priority | [uint32](#uint32) |  | 紧急度 1:高 2:中 3:低 @gotags: validate:&#34;required_if=CatLevel 3&#34; |
| deadline | [uint64](#uint64) |  | 完成时间 |
| cat_level | [uint32](#uint32) |  | 分类的等级 @gotags: validate:&#34;required,oneof=1 2 3&#34; |
| evaluation | [bool](#bool) |  | 是否评价 是:true 否:false |
| langs | [Lang](#pb-Lang) |  | 多语言 |
| tpl_list | [CatTplPrefer](#pb-CatTplPrefer) | repeated | 个性化模板配置 |
| reply_tpl_id | [uint32](#uint32) |  | 回复模板id |
| relate_type | [uint32](#uint32) |  | 关联类型 1:表单 2:流程 @gotags: validate:&#34;required_if=CatLevel 3&#34; |
| process_id | [uint32](#uint32) |  | 流程id @gotags: validate:&#34;required_if=RelateType 2&#34; |
| reopen | [bool](#bool) |  | 是否重开 1：是 0：否 |
| reopen_num | [uint32](#uint32) |  | 重开次数 |






<a name="pb-CatTplPrefer"></a>

### CatTplPrefer
CatTplPrefer 三级分类个性化配置


| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| channel | [string](#string) | repeated |  |
| server_str | [string](#string) |  |  |
| country | [string](#string) | repeated |  |
| tpl_id | [uint32](#uint32) |  | 模版ID @gotags: validate:&#34;required_if=CatLevel 3&#34; |
| mark | [CatTplPrefer.MarkEntry](#pb-CatTplPrefer-MarkEntry) | repeated |  |






<a name="pb-CatTplPrefer-MarkEntry"></a>

### CatTplPrefer.MarkEntry



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| key | [string](#string) |  |  |
| value | [bool](#bool) |  |  |






<a name="pb-Lang"></a>

### Lang



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| category | [Lang.CategoryEntry](#pb-Lang-CategoryEntry) | repeated |  |






<a name="pb-Lang-CategoryEntry"></a>

### Lang.CategoryEntry



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| key | [string](#string) |  |  |
| value | [string](#string) |  |  |





 


<a name="pb-CatLevel"></a>

### CatLevel
分类

| Name | Number | Description |
| ---- | ------ | ----------- |
| UnKnown | 0 |  |
| PrimaryClassification | 1 |  |
| SecondaryClassification | 2 |  |
| ThreeClassification | 3 |  |


 

 

 



<a name="common-proto"></a>
<p align="right"><a href="#top">Top</a></p>

## common.proto



<a name="pb-BaseInfo"></a>

### BaseInfo



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| game_id | [uint64](#uint64) |  | 游戏id |
| game_project | [string](#string) |  | 游戏区分 |
| lang | [string](#string) |  | lang 语言 |
| uuid | [string](#string) |  | uuid 设备ID |
| os | [string](#string) |  | os |
| sdk_version | [string](#string) |  | sdk_version |
| fpid | [uint64](#uint64) |  | fpid |
| uid | [uint64](#uint64) |  | uid 用户ID |
| account_id | [string](#string) |  | account_id |
| role_id | [string](#string) |  | role_id 角色ID |
| sid | [string](#string) |  | sid 区服 |
| channel | [string](#string) |  | channel 渠道 |
| nickname | [string](#string) |  | nickname 昵称 |
| country | [string](#string) |  |  |
| total_pay | [double](#double) |  |  |
| country_code | [string](#string) |  |  |
| fpx_app_id | [string](#string) |  | fpx_app_id |
| bi_app_id | [string](#string) |  | bi_app_id |
| scene | [uint32](#uint32) |  | 场景枚举 |






<a name="pb-Empty"></a>

### Empty
Empty 空对象






<a name="pb-EnableReq"></a>

### EnableReq
EnableReq 启用禁用


| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| object_id | [uint32](#uint32) |  | 对象ID @gotags: validate:&#34;required&#34; |
| enable | [bool](#bool) |  | 启用 true false |






<a name="pb-Language"></a>

### Language



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| language | [Language.LanguageEntry](#pb-Language-LanguageEntry) | repeated |  |






<a name="pb-Language-LanguageEntry"></a>

### Language.LanguageEntry



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| key | [string](#string) |  |  |
| value | [string](#string) |  |  |






<a name="pb-Project"></a>

### Project



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| project_name | [string](#string) |  | 项目名称 |
| lib_name | [string](#string) |  | 标签库名称 |






<a name="pb-ProjectLang"></a>

### ProjectLang



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| project | [string](#string) |  | 项目 @gotags: validate:&#34;required&#34; |
| lang | [string](#string) |  | 语言 |






<a name="pb-ProjectTagLib"></a>

### ProjectTagLib



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| project_name | [string](#string) |  | 项目名称 @gotags: validate:&#34;required&#34; |
| lib_id | [uint32](#uint32) |  | 组id |






<a name="pb-Props"></a>

### Props
Props 枚举


| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| label | [string](#string) |  | 枚举名 |
| value | [string](#string) |  | 枚举值 |






<a name="pb-Response"></a>

### Response



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| code | [int32](#int32) |  | 状态码 |
| msg | [string](#string) |  | 错误信息 |
| data | [google.protobuf.Any](#google-protobuf-Any) |  | 数据 |






<a name="pb-ServerSplit"></a>

### ServerSplit



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| ids | [int64](#int64) | repeated |  |
| btw | [ServerSplit.ServerBtw](#pb-ServerSplit-ServerBtw) | repeated |  |






<a name="pb-ServerSplit-ServerBtw"></a>

### ServerSplit.ServerBtw



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| start | [int64](#int64) |  |  |
| end | [int64](#int64) |  |  |





 

 

 

 



<a name="dict-proto"></a>
<p align="right"><a href="#top">Top</a></p>

## dict.proto



<a name="pb-DictAddReq"></a>

### DictAddReq



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| dict_name | [string](#string) |  | 字段名称 @gotags: validate:&#34;required&#34; |
| dict_key | [string](#string) |  | 字段Key @gotags: validate:&#34;required&#34; |






<a name="pb-DictId"></a>

### DictId



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| dict_id | [uint32](#uint32) |  | 字段Id @gotags: validate:&#34;required&#34; |






<a name="pb-DictInfoResp"></a>

### DictInfoResp



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| dict_id | [uint32](#uint32) |  | 字段Id @gotags: validate:&#34;required&#34; |
| dict_name | [string](#string) |  | 字段名称 @gotags: validate:&#34;required&#34; |
| dict_key | [string](#string) |  | 字段Key @gotags: validate:&#34;required&#34; |






<a name="pb-DictListResp"></a>

### DictListResp



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| list | [DictListResp.Dict](#pb-DictListResp-Dict) | repeated |  |






<a name="pb-DictListResp-Dict"></a>

### DictListResp.Dict



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| dict_id | [uint32](#uint32) |  | 团队ID |
| dict_name | [string](#string) |  | 入口名称 |
| dict_key | [string](#string) |  | 入口描述 |
| updated_at | [string](#string) |  | 更新时间 |
| operator | [string](#string) |  | 操作人员 |
| enable | [bool](#bool) |  | 状态 |






<a name="pb-DictOptsResp"></a>

### DictOptsResp



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| list | [DictOptsResp.Opts](#pb-DictOptsResp-Opts) | repeated |  |






<a name="pb-DictOptsResp-Opts"></a>

### DictOptsResp.Opts



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| dict_id | [uint32](#uint32) |  | 字段ID |
| dict_name | [string](#string) |  | 字段名称 |
| dict_key | [string](#string) |  | 字段Key |





 

 

 

 



<a name="egress-proto"></a>
<p align="right"><a href="#top">Top</a></p>

## egress.proto



<a name="pb-CatOptRequest"></a>

### CatOptRequest



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| game_id | [uint32](#uint32) |  | 项目标识 @gotags: validate:&#34;required_without=FpxAppId&#34; |
| fpx_app_id | [string](#string) |  | 项目标识 @gotags: validate:&#34;required_without=GameId&#34; |
| lang | [string](#string) |  | 语言标识 @gotags: validate:&#34;required&#34; |





 

 

 


<a name="pb-EgressAPI"></a>

### EgressAPI
对外服务接口-老工单接口实现

| Method Name | Request Type | Response Type | Description |
| ----------- | ------------ | ------------- | ------------|
| TkSdkNoticeV2 | [NoticeRequestV2](#pb-NoticeRequestV2) | [NoticeResp](#pb-NoticeResp) | 工单 - sdk - 红点v2 |
| TkCreate | [TkCreateReq](#pb-TkCreateReq) | [TkCreateResp](#pb-TkCreateResp) | 工单 - 创建工单接口 |
| TkMine | [TkMineReq](#pb-TkMineReq) | [TkMineResp](#pb-TkMineResp) | 工单 - 创建工单接口 |
| TkDetail | [TkDetailReq](#pb-TkDetailReq) | [TkDetailResp](#pb-TkDetailResp) | 工单 - 工单详情接口 |
| TkReplenish | [TkReplenishReq](#pb-TkReplenishReq) | [Empty](#pb-Empty) | 工单 - 工单补填接口 |
| TkReopen | [TkReplenishReq](#pb-TkReplenishReq) | [Empty](#pb-Empty) | 工单 - 工单重开接口 |
| TkAppraise | [TkAppraiseReq](#pb-TkAppraiseReq) | [Empty](#pb-Empty) | 工单 - 工单评价接口 |
| TkAppraiseFeedback | [TkAppraiseFeedbackReq](#pb-TkAppraiseFeedbackReq) | [Empty](#pb-Empty) | 工单 - 工单评价反馈接口 |
| TkCommunicate | [TkCommunicateReq](#pb-TkCommunicateReq) | [Empty](#pb-Empty) | 工单 - 给客服发消息 |

 



<a name="egress_ticket-proto"></a>
<p align="right"><a href="#top">Top</a></p>

## egress_ticket.proto



<a name="pb-NoticeFpxRequestV3"></a>

### NoticeFpxRequestV3
NoticeFpxRequest fpx 消息通知


| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| appId | [string](#string) |  | 项目标识 @gotags: validate:&#34;required&#34; |
| fpx_app_id | [string](#string) |  | 项目标识:对应kg:game_id/fpx:fpx_app_id @gotags: validate:&#34;required&#34; |
| device_id | [string](#string) |  | 唯一标识：游戏外场景为uuid @gotags: validate:&#34;required&#34; |
| account_id | [string](#string) |  | account_id - 对应kg:fpid/fpx:account_id |
| sid | [string](#string) |  | sid 区服ID |
| role_id | [string](#string) |  | role_id 角色ID |
| ts | [uint64](#uint64) |  | 时间戳 |
| auth | [string](#string) |  | 客户端版 |
| scene | [uint32](#uint32) |  | 场景 |






<a name="pb-NoticeRequestV2"></a>

### NoticeRequestV2



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| appId | [string](#string) |  | 项目标识 @gotags: validate:&#34;required&#34; |
| gameId | [uint32](#uint32) |  | 项目标识 @gotags: validate:&#34;required&#34; |
| device_id | [string](#string) |  | 唯一标识 @gotags: validate:&#34;required_without=Fpid&#34; |
| fpid | [uint64](#uint64) |  | fpid @gotags: validate:&#34;required_without=DeviceId&#34; |
| uid | [uint64](#uint64) |  | uid |
| sid | [string](#string) |  | 区服标识 |
| channel | [string](#string) |  | 渠道标识 |
| lang | [string](#string) |  | 语言标识 |
| sdk_version | [string](#string) |  | Sdk版本 |
| os | [string](#string) |  | 系统版本 |
| ts | [uint64](#uint64) |  | 时间戳 |
| auth | [string](#string) |  | 客户端版 |
| scene | [uint32](#uint32) |  | 场景 |






<a name="pb-NoticeResp"></a>

### NoticeResp



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| notice_id | [uint64](#uint64) |  | 消息ID @gotags: json:&#34;notice_id,omitempty&#34; |
| from | [uint32](#uint32) |  | 来源 @gotags: json:&#34;from,omitempty&#34; |
| object_id | [uint64](#uint64) |  | 对象ID @gotags: json:&#34;object_id,omitempty&#34; |
| scene | [uint32](#uint32) |  | 场景 |
| ticket_sys_type | [TicketSys](#pb-TicketSys) |  | 使用哪个工单系统 |






<a name="pb-TicketDetailModel"></a>

### TicketDetailModel
TicketDetailModel


| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| ticket_id | [uint64](#uint64) |  |  |
| uuid | [string](#string) |  |  |
| origin | [uint32](#uint32) |  |  |
| cat_id | [uint32](#uint32) |  |  |
| evaluation | [uint32](#uint32) |  |  |
| csi | [uint32](#uint32) |  |  |
| proof | [uint32](#uint32) |  |  |
| stage | [uint32](#uint32) |  |  |
| status | [uint32](#uint32) |  |  |
| closed | [uint32](#uint32) |  |  |
| closed_at | [uint64](#uint64) |  |  |
| field | [string](#string) |  |  |
| account_id | [string](#string) |  | account_id |
| project | [string](#string) |  | project |
| gm_id | [string](#string) |  | game_id |
| scene | [uint32](#uint32) |  | project |
| reopen_num | [uint32](#uint32) |  | 重开次数 |






<a name="pb-TkAppraiseFeedbackReq"></a>

### TkAppraiseFeedbackReq
TkAppraiseReq 工单评价


| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| ticket_id | [uint64](#uint64) |  | 工单ID @gotags: validate:&#34;required&#34; |
| game_id | [uint32](#uint32) |  | Deprecated: 废弃，后续整合到fpx_app_id中 游戏ID @gotags: validate:&#34;required_without=FpxAppId&#34; |
| fpx_app_id | [string](#string) |  | 项目标识 @gotags: validate:&#34;required_without=GameId&#34; |
| lang | [string](#string) |  | lang 语言 @gotags: validate:&#34;required&#34; |
| fpid | [uint64](#uint64) |  | Deprecated: 废弃，fpid 后续整合到 account_id中 |
| uid | [uint64](#uint64) |  | uid 用户ID |
| account_id | [string](#string) |  | account_id |
| role_id | [string](#string) |  | role_id 角色ID |
| sid | [string](#string) |  | sid 区服 |
| channel | [string](#string) |  | channel 渠道 |
| resolved | [uint32](#uint32) |  | 是否解决 1：解决 2：未解决 |






<a name="pb-TkAppraiseReq"></a>

### TkAppraiseReq
TkAppraiseReq 评价


| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| ticket_id | [uint64](#uint64) |  | 工单ID @gotags: validate:&#34;required&#34; |
| game_id | [uint32](#uint32) |  | Deprecated: 废弃，后续整合到fpx_app_id中 游戏ID @gotags: validate:&#34;required_without=FpxAppId&#34; |
| fpx_app_id | [string](#string) |  | 项目标识 @gotags: validate:&#34;required_without=GameId&#34; |
| lang | [string](#string) |  | lang 语言 @gotags: validate:&#34;required&#34; |
| fpid | [uint64](#uint64) |  | Deprecated: 废弃，fpid 后续整合到 account_id中 |
| uid | [uint64](#uint64) |  | uid 用户ID |
| account_id | [string](#string) |  | account_id |
| role_id | [string](#string) |  | role_id 角色ID |
| sid | [string](#string) |  | sid 区服 |
| channel | [string](#string) |  | channel 渠道 |
| appraise | [uint32](#uint32) |  | 评价等级 @gotags: validate:&#34;required&#34; |
| service_rating | [uint32](#uint32) |  | 服务态度评分 - 老接口字段 - 废弃 |
| service_time_rating | [uint32](#uint32) |  | 处理速度评分 - 老接口字段 - 废弃 |
| service_solution_rating | [uint32](#uint32) |  | 处理方案评分 - 老接口字段 - 废弃 |
| recommendation_level | [uint32](#uint32) |  | 推荐给别人的意愿程度 @gotags: validate:&#34;required&#34; |
| remark | [string](#string) |  | 评价内容 |






<a name="pb-TkCommunicateReq"></a>

### TkCommunicateReq
TkCommunicateReq 沟通发送消息


| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| ticket_id | [uint64](#uint64) |  | 工单ID @gotags: validate:&#34;required&#34; |
| game_id | [uint32](#uint32) |  | Deprecated: 废弃，后续整合到fpx_app_id中 游戏ID @gotags: validate:&#34;required_without=FpxAppId&#34; |
| fpx_app_id | [string](#string) |  | 项目标识 @gotags: validate:&#34;required_without=GameId&#34; |
| lang | [string](#string) |  | lang 语言 @gotags: validate:&#34;required&#34; |
| fpid | [uint64](#uint64) |  | Deprecated: 废弃，fpid 后续整合到 account_id中 |
| uid | [uint64](#uint64) |  | uid 用户ID |
| account_id | [string](#string) |  | account_id |
| role_id | [string](#string) |  | role_id 角色ID |
| sid | [string](#string) |  | sid 区服 |
| channel | [string](#string) |  | channel 渠道 |
| content | [string](#string) |  | 发送消息内容 |






<a name="pb-TkCreateReq"></a>

### TkCreateReq
TkCreateReq 创建工单


| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| origin | [uint32](#uint32) |  | 工单来源 1:玩家 2:VIP客服 3:普通客服 @gotags: validate:&#34;required&#34; |
| scene | [uint32](#uint32) |  | 场景枚举 |
| game_id | [uint32](#uint32) |  | Deprecated: 废弃，后续整合到fpx_app_id中 游戏ID @gotags: validate:&#34;required_without=FpxAppId&#34; |
| fpx_app_id | [string](#string) |  | 项目标识 @gotags: validate:&#34;required_without=GameId&#34; |
| lang | [string](#string) |  | lang 语言 @gotags: validate:&#34;required&#34; |
| uuid | [string](#string) |  | uuid 设备ID |
| fpid | [uint64](#uint64) |  | Deprecated: 废弃，fpid 后续整合到 account_id中 |
| uid | [uint64](#uint64) |  | uid 用户ID |
| account_id | [string](#string) |  | account_id |
| role_id | [string](#string) |  | role_id 角色ID |
| sid | [string](#string) |  | sid 区服 |
| channel | [string](#string) |  | channel 渠道 |
| nickname | [string](#string) |  | nickname 昵称 |
| cat_id | [uint32](#uint32) |  | cat_id 问题类别ID @gotags: validate:&#34;required&#34; |
| fields | [string](#string) |  | fields 自定义字段 @gotags: validate:&#34;required&#34; |
| device_type | [string](#string) |  |  |
| os | [string](#string) |  |  |
| os_version | [string](#string) |  |  |
| app_version | [string](#string) |  |  |
| rom_gb | [string](#string) |  |  |
| remain_rom | [string](#string) |  |  |
| ram_mb | [string](#string) |  |  |
| network_info | [string](#string) |  |  |
| sdk_version | [string](#string) |  |  |
| country | [string](#string) |  | 国家 |
| total_pay | [double](#double) |  | 历史充值总额 |
| label_id | [uint32](#uint32) | repeated |  |
| extend | [string](#string) |  |  |
| pay_amount | [double](#double) |  | Deprecated: 废弃，后续 使用 total_pay |
| country_code | [string](#string) |  | Deprecated: 废弃，后续 使用country |
| related_cat | [uint32](#uint32) |  | 重提工单ID |
| from_ticket_id | [uint64](#uint64) |  |  |
| subchannel | [string](#string) |  | subchannel 子渠道 |
| openid | [string](#string) |  | 微信内小游戏 - openid |






<a name="pb-TkCreateResp"></a>

### TkCreateResp
TkCreateResp 工单ID


| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| ticket_id | [uint64](#uint64) |  | 工单ID @gotags: validate:&#34;required&#34; |
| ticket_sys_type | [TicketSys](#pb-TicketSys) |  | 使用哪个工单系统 |






<a name="pb-TkDetailReq"></a>

### TkDetailReq
TkDetailReq 工单详情


| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| ticket_id | [uint64](#uint64) |  | 项目标识 @gotags: validate:&#34;required&#34; |
| game_id | [uint32](#uint32) |  | Deprecated: 废弃，后续整合到fpx_app_id中 游戏ID @gotags: validate:&#34;required_without=FpxAppId&#34; |
| fpx_app_id | [string](#string) |  | 项目标识 @gotags: validate:&#34;required_without=GameId&#34; |
| lang | [string](#string) |  | lang 语言 @gotags: validate:&#34;required&#34; |
| uuid | [string](#string) |  | uuid 设备ID |
| fpid | [uint64](#uint64) |  | Deprecated: 废弃，fpid 后续整合到 account_id中 |
| uid | [uint64](#uint64) |  | uid 用户ID |
| account_id | [string](#string) |  | account_id |
| role_id | [string](#string) |  | role_id 角色ID |
| sid | [string](#string) |  | sid 区服 |
| channel | [string](#string) |  | channel 渠道 |






<a name="pb-TkDetailResp"></a>

### TkDetailResp
TicketDetailResp 工单详细信息


| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| ticket_id | [uint64](#uint64) |  | 工单ID |
| category | [string](#string) |  | 问题分类 |
| filed | [string](#string) |  | 表单信息 |
| transfer | [uint32](#uint32) |  | 流转 |
| solution | [string](#string) |  | 解决方案 |
| done | [bool](#bool) |  | 状态 true:已结单 |
| closed | [uint32](#uint32) |  | 关闭 0:未关闭 1:结单 2:关闭 3:超时关闭 |
| evidence | [bool](#bool) |  | 补充举证 true:需举证 - 去补填按钮 |
| appraise | [bool](#bool) |  | 评价 true：需评价 |
| replenish | [TkDetailResp.Replenish](#pb-TkDetailResp-Replenish) | repeated | 补充列表 -- 存在 空着 - 不需要 |
| commu | [TkDetailResp.Commu](#pb-TkDetailResp-Commu) | repeated | 沟通记录 |
| csi | [uint32](#uint32) |  | 评分 |
| label | [uint32](#uint32) | repeated | 评价标签 |
| relate_type | [uint32](#uint32) |  | 关联类型,1:表单 2:自动化流程 |
| reopen | [TkDetailResp.Reopen](#pb-TkDetailResp-Reopen) | repeated | 重开信息 |
| reopen_disabled | [bool](#bool) |  | 超过重开次数&#43;不支持重开：true |
| cat_id | [uint32](#uint32) |  | 问题类别ID |






<a name="pb-TkDetailResp-Commu"></a>

### TkDetailResp.Commu
对话记录


| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| content | [string](#string) |  | 消息内容 |
| role | [UserRole](#pb-UserRole) |  | 消息来源: 1:玩家端； 2客服回复 |
| custom_nick_name | [string](#string) |  | 客服回复人员名称 |
| created_at | [string](#string) |  | 消息时间 |






<a name="pb-TkDetailResp-Reopen"></a>

### TkDetailResp.Reopen



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| reopen_id | [uint64](#uint64) |  | 重开id |
| fill_content | [string](#string) |  | 重开提交内容 |
| files | [string](#string) |  | 重开附件 |
| replenish | [TkDetailResp.Replenish](#pb-TkDetailResp-Replenish) | repeated | 补填&#43;回复信息 |
| created_at | [string](#string) |  | 创建时间 |
| reply_id | [uint64](#uint64) |  | 回复id - 执行重开 - commu |
| response_reply_id | [uint64](#uint64) |  | 重开单 - 客服回复&amp;关单 的id - commu |






<a name="pb-TkDetailResp-Replenish"></a>

### TkDetailResp.Replenish



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| replenish_id | [uint64](#uint64) |  |  |
| ticket_id | [uint64](#uint64) |  | 工单ID |
| remark | [string](#string) |  | 备注信息 |
| fill_content | [string](#string) |  | 补填内容 |
| files | [string](#string) |  | 文件 |
| op | [uint32](#uint32) |  | 操作 9:回复信息 7:打回补填 8:关闭工单 6:处理完成 |
| created_at | [string](#string) |  | 创建时间 |
| created_ts | [uint64](#uint64) |  | 创建时间ts |






<a name="pb-TkMineReq"></a>

### TkMineReq
TkMineReq 我的工单列表


| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| scene | [uint32](#uint32) |  | 场景枚举 |
| game_id | [uint32](#uint32) |  | Deprecated: 废弃，后续整合到fpx_app_id中 游戏ID @gotags: validate:&#34;required_without=FpxAppId&#34; |
| fpx_app_id | [string](#string) |  | 项目标识 @gotags: validate:&#34;required_without=GameId&#34; |
| lang | [string](#string) |  | lang 语言 @gotags: validate:&#34;required&#34; |
| uuid | [string](#string) |  | uuid 设备ID |
| fpid | [uint64](#uint64) |  | Deprecated: 废弃，fpid 后续整合到 account_id中 |
| uid | [uint64](#uint64) |  | uid 用户ID |
| account_id | [string](#string) |  | account_id |
| role_id | [string](#string) |  | role_id 角色ID |
| sid | [string](#string) |  | sid 区服 |
| channel | [string](#string) |  | channel 渠道 |






<a name="pb-TkMineResp"></a>

### TkMineResp
TkMineResp 工单历史 - 老工单字段及定义


| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| data | [TkMineResp.TkList](#pb-TkMineResp-TkList) | repeated |  |






<a name="pb-TkMineResp-TkList"></a>

### TkMineResp.TkList



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| ticket_id | [uint64](#uint64) |  | 工单ID |
| created_at | [string](#string) |  | 创建时间 |
| category | [string](#string) |  | 类别 |
| progress | [TkProgress](#pb-TkProgress) |  | 当前状态 1：已完成 2：待补充 3：处理中 4：补充超时 5：重新打开 |
| status | [TkStatus](#pb-TkStatus) |  | 当前数据状态： 前端忽略 |
| read | [uint32](#uint32) |  | 消息已读状态 1:已读 0:未读 |






<a name="pb-TkReplenishReq"></a>

### TkReplenishReq
TkReplenishReq 补填信息


| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| ticket_id | [uint64](#uint64) |  | 工单ID @gotags: validate:&#34;required&#34; |
| content | [string](#string) |  | 工单内容 @gotags: validate:&#34;required&#34; |
| files | [string](#string) |  | 工单附件 @gotags: validate:&#34;required&#34; |
| game_id | [uint32](#uint32) |  | Deprecated: 废弃，后续整合到fpx_app_id中 游戏ID @gotags: validate:&#34;required_without=FpxAppId&#34; |
| fpx_app_id | [string](#string) |  | 项目标识 @gotags: validate:&#34;required_without=GameId&#34; |
| lang | [string](#string) |  | lang 语言 @gotags: validate:&#34;required&#34; |
| uuid | [string](#string) |  | uuid 设备ID |
| fpid | [uint64](#uint64) |  | Deprecated: 废弃，fpid 后续整合到 account_id中 |
| uid | [uint64](#uint64) |  | uid 用户ID |
| account_id | [string](#string) |  | account_id |
| role_id | [string](#string) |  | role_id 角色ID |
| sid | [string](#string) |  | sid 区服 |
| channel | [string](#string) |  | channel 渠道 |
| nickname | [string](#string) |  | nickname 昵称 |
| subchannel | [string](#string) |  | subchannel 子渠道 |





 

 

 

 



<a name="enum-proto"></a>
<p align="right"><a href="#top">Top</a></p>

## enum.proto



<a name="pb-RetEnum"></a>

### RetEnum
RetEnum 枚举


| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| name | [string](#string) |  | 枚举名 |
| value | [uint32](#uint32) |  | 枚举值 |





 


<a name="pb-Bind"></a>

### Bind


| Name | Number | Description |
| ---- | ------ | ----------- |
| UnBind | 0 |  |
| Bound | 1 |  |
| Update | 2 |  |



<a name="pb-CommuType"></a>

### CommuType
会话数据分类

| Name | Number | Description |
| ---- | ------ | ----------- |
| CommuTypeUnknown | 0 |  |
| CommuTypeDialogue | 1 | 正常会话 |
| CommuTypeRemark | 2 | 备注消息 |



<a name="pb-CreatorType"></a>

### CreatorType
CreatorType

| Name | Number | Description |
| ---- | ------ | ----------- |
| No | 0 |  |
| UID | 1 |  |
| Fpid | 2 |  |
| Nickname | 3 |  |



<a name="pb-CsiLabel"></a>

### CsiLabel


| Name | Number | Description |
| ---- | ------ | ----------- |
| NullCsiLb | 0 |  |
| TheService | 2 |  |
| TheOutcome | 3 |  |
| ProcessingSpeed | 4 |  |
| FastProcessingSpeed | 5 |  |
| SatisfiedResult | 6 |  |
| SatisfiedService | 7 |  |



<a name="pb-CsiLevel"></a>

### CsiLevel
CsiLevel 工单评星

| Name | Number | Description |
| ---- | ------ | ----------- |
| ZeroStar | 0 |  |
| OneStar | 1 | 一星 |
| SecondStar | 2 | 二星 |
| ThreeStar | 3 | 三星 |
| FourStar | 4 | 四星 |
| FiveStar | 5 | 五星 |



<a name="pb-FilterEnum"></a>

### FilterEnum
FilterEnum 基础过滤枚举

| Name | Number | Description |
| ---- | ------ | ----------- |
| FilterAll | 0 | 全部 |
| FilterIsNull | 1 | 空白 |
| FilterIsIn | 2 | 包含 |
| FilterIsNotIn | 3 | 不包含 |
| FilterSystem | 4 | 系统 |



<a name="pb-FilterTagEnum"></a>

### FilterTagEnum
FilterTagEnum 标签过滤枚举

| Name | Number | Description |
| ---- | ------ | ----------- |
| All | 0 | 全部 |
| IsNull | 1 | 空白 |
| IsIn | 2 | 包含 |
| IsNotIn | 3 | 不包含 |
| IsSystem | 4 | 系统标签 |
| IsHist | 5 | 历史标签 |



<a name="pb-NpsLevel"></a>

### NpsLevel
NpsLevel NPS 评分

| Name | Number | Description |
| ---- | ------ | ----------- |
| NpsScoreZero | 0 |  |
| NpsOneScore | 1 | 1分 |
| NpsTwoScore | 2 | 2分 |
| NpsThreeScore | 3 | 3分 |
| NpsFourScore | 4 | 4分 |
| NpsFiveScore | 5 | 5分 |



<a name="pb-Origin"></a>

### Origin
工单来源

| Name | Number | Description |
| ---- | ------ | ----------- |
| Nobody | 0 |  |
| Player | 1 | 玩家端提交 - 目前只有此来源

VipService = 2; NormalService = 3; |



<a name="pb-RelateType"></a>

### RelateType
RelateType 分类类型

| Name | Number | Description |
| ---- | ------ | ----------- |
| RelateTypeDefault | 0 | 默认为 tpl |
| RelateTypeTpl | 1 | tpl 模版 |
| RelateTypeProcess | 2 | 自动化流程 |



<a name="pb-SceneType"></a>

### SceneType
场景

| Name | Number | Description |
| ---- | ------ | ----------- |
| General | 0 | 通用场景 - 废弃 |
| Loading | 1 | 游戏外 - 加载入口 |
| AccountBan | 2 | 游戏外 - 封号入口 |
| InGame | 3 | 游戏内 - 游戏内入口 |



<a name="pb-Status"></a>

### Status
工单状态

| Name | Number | Description |
| ---- | ------ | ----------- |
| Untreated | 0 | 待接入工单 |
| Processing | 1 | 处理中工单 |
| Done | 2 | 已完成 |



<a name="pb-TicketEvent"></a>

### TicketEvent
工单事件

| Name | Number | Description |
| ---- | ------ | ----------- |
| Create | 0 | 工单创建 |
| Allocation | 1 | 一线接单自动分配 |
| Receipt | 2 | 一线接单 |
| Transfered | 3 | 工单流转 |
| DoneCase | 4 | 工单结案 有满意度评价 |
| Assign | 5 | 工单指派 |
| Emergency | 6 | 调整紧急度 |
| Remark | 7 | 工单备注 |
| Reply | 8 | 工单回复 |
| Closed | 9 | 工单关闭 无满意度评价 |
| Tag | 10 | 标签操作 |
| Relate | 11 | 推送 |
| Revoke | 12 | 撤回 |
| Reopen | 13 | 重开 |
| RectifyCat | 14 | 修正问题分类 |



<a name="pb-TicketStage"></a>

### TicketStage
工单流转节点部分

| Name | Number | Description |
| ---- | ------ | ----------- |
| Pending | 0 | 待处理 |
| FirstTierProcessing | 1 | 一线处理中 |
| FirstTierRefill | 2 | 一线补填中 |
| SecondTierProcessing | 3 | 二线处理中 |
| SecondTierAudit | 4 | 二线审核区 |
| VipAgentRefill | 5 | VIP补填中 |
| TicketResolved | 6 | 处理完成 玩家有评价 |
| TicketRefill | 7 | 玩家补填 |
| TicketClosed | 8 | 关闭工单 玩家无评价 |
| TemporaryReply | 9 | 暂时回复玩家 |
| VipAgentProcessing | 10 | vip处理 |
| SecondTierRefill | 11 | 二线补填中 |
| ThirdTierProcessing | 12 | 三线处理 |
| ThirdTierAudit | 13 | 三线审核区 |
| FirstTierAdminProcessing | 14 | 一线管理处理中 |
| FirstTierAdminRefill | 15 | 一线管理补填中 |
| VipAgentResolved | 16 | VIP处理完成 |



<a name="pb-TicketSys"></a>

### TicketSys
TicketSys 使用哪个工单系统

| Name | Number | Description |
| ---- | ------ | ----------- |
| TicketSysOld | 0 | 默认老版本工单 |
| ticketSysNew | 1 | 新工单系统 |



<a name="pb-TkClosedRole"></a>

### TkClosedRole
关单角色

| Name | Number | Description |
| ---- | ------ | ----------- |
| TkClosedNone | 0 |  |
| TkPlayerClosed | 1 |  |
| TkServiceClosed | 2 |  |
| TkSystemTimeoutClosed | 3 |  |
| TkSystemReplyClosed | 4 |  |



<a name="pb-TkEvent"></a>

### TkEvent
工单事件

| Name | Number | Description |
| ---- | ------ | ----------- |
| TkEventUnknown | 0 |  |
| TkEventCreate | 1 | 工单创建 |
| TkEventPlayerRefill | 2 | 玩家补填； |
| TkEventAutoAlloc | 3 | 工单 - 自动分配 |
| TkEventDoneCase | 4 | 工单结案 有满意度评价 |
| TkEventAssign | 5 | 工单指派 |
| TkEventTurn | 6 | 工单流转给某人 |
| TkEventUserCommu | 7 | 玩家端回复 |
| TkEventCommu | 8 | 客服 - 回复 |
| TkEventCommuClose | 9 | 客服 - 回复&amp;关单 |
| TkEventRefused | 10 | 客服 - 拒单 |
| TkEventUpgrade | 11 | 客服 - 工单升级/降级 |
| TkEventRemark | 12 | 客服 - 增加备注 |
| TkEventReopen | 13 | 重开 |
| TkEventUserNoAnsTimout | 14 | 玩家3天未回复 - 设置超时 |
| TkEventTagAdd | 15 | 标签 - 新增标签 |
| TkEventTagDel | 16 | 标签 - 删除 |
| TkEventNoOpTimeout | 17 | 超时 - 分配处理人 2h 未处理 |



<a name="pb-TkProgress"></a>

### TkProgress
TkProgress 当前状态

| Name | Number | Description |
| ---- | ------ | ----------- |
| TkProgressUnknown | 0 |  |
| TkProgressDone | 1 | 1：已完成 |
| TkProgressUserRefill | 2 | 2：待补充 |
| TkProgressUserDoing | 3 | 3：处理中 |
| TkProgressUserRefillOverTime | 4 | 4：超时关闭 |
| TkProgressReopen | 5 | 5: 重新打开 |
| TkProgressReject | 6 | 6: 拒单关闭 |



<a name="pb-TkStage"></a>

### TkStage
TkStage 节点流转

| Name | Number | Description |
| ---- | ------ | ----------- |
| TkStageUnknown | 0 |  |
| TkStageNew | 1 | 工单状态 - New 待接单 - 玩家提交工单&amp;未分配 |
| TkStageNewForAgent | 2 | 工单状态 - New For Agent 待处理 - 已分配/指派客服 |
| TkStageAgentReplied | 3 | 工单状态 - Agent Replied 处理中 - 待玩家回复 - 客服已回复待玩家回复 |
| TkStageWaitingForAgent | 4 | 工单状态 - Waiting For Agent 处理中 - 玩家已回复 待客服再次回复 |
| TkStageAgentResolved | 5 | 工单状态 - Agent Resolved 超时关闭 - 玩家3天内未回复，超时关闭 |
| TkStageAgentReopen | 6 | 工单状态 - Reopen 重开 - 玩家重新打开工单 |
| TkStageAgentRejected | 7 | 工单状态 - Rejected 拒单关闭 - 客服点击&#34;拒单&#34;，结束会话 |
| TkStageAgentCompleted | 8 | 工单状态 - Completed 已完成 - 客服回复&amp;关单 |



<a name="pb-TkStatus"></a>

### TkStatus
TkStatus 工单状态定义

| Name | Number | Description |
| ---- | ------ | ----------- |
| TkStatusUnknown | 0 | 未知状态 |
| TkStatusUntreated | 1 | 待接入工单 |
| TkStatusProcessing | 2 | 处理中工单 |
| TkStatusDone | 3 | 已完成 |



<a name="pb-UserLoginStatus"></a>

### UserLoginStatus
客服人员是否在线状态定义

| Name | Number | Description |
| ---- | ------ | ----------- |
| UserLoginStatusUnknown | 0 |  |
| UserLoginStatusYes | 1 | 在线 |
| UserLoginStatusNo | 2 | 离线 |



<a name="pb-UserRole"></a>

### UserRole
UserRole 角色

| Name | Number | Description |
| ---- | ------ | ----------- |
| NobodyRole | 0 |  |
| PlayerRole | 1 | 玩家端 |
| ServiceRole | 2 | 服务端 |
| SystemRole | 3 | 系统操作 |
| VIPServiceRole | 4 |  |



<a name="pb-Workbench"></a>

### Workbench
Workbench 工作台

| Name | Number | Description |
| ---- | ------ | ----------- |
| OverviewWorkStation | 0 | 总览 |
| FirstTierWorkStation | 1 | 一线工作台 |
| SecondTierWorkStation | 2 | 二线工作台 |
| VipWorkStation | 3 | vip工作台 |
| ThirdTierWorkStation | 4 | 三线工作台 |
| FirstTierAdminWorkStation | 5 | 一线管理工作台 |


 

 

 



<a name="group-proto"></a>
<p align="right"><a href="#top">Top</a></p>

## group.proto



<a name="pb-GroupCat"></a>

### GroupCat



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| id | [uint32](#uint32) |  |  |
| game | [string](#string) |  | 游戏项目 @gotags: validate:&#34;required&#34; |
| language | [string](#string) |  | 语言 @gotags: validate:&#34;required&#34; |
| categories | [string](#string) |  | 问题分类 @gotags: validate:&#34;required&#34; |






<a name="pb-GroupOptsResp"></a>

### GroupOptsResp
GroupOptsResp 技能组筛选列表 响应结果


| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| list | [GroupOptsResp.Opts](#pb-GroupOptsResp-Opts) | repeated |  |






<a name="pb-GroupOptsResp-Opts"></a>

### GroupOptsResp.Opts



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| id | [uint32](#uint32) |  | 团队ID |
| group_desc | [string](#string) |  | 团队名称 |
| game | [string](#string) |  | 游戏 |
| language | [string](#string) |  | 语言 |
| user | [string](#string) |  | 人员名称 |
| update_time | [string](#string) |  | 更新时间 |






<a name="pb-GroupUser"></a>

### GroupUser



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| user | [uint32](#uint32) |  | 人员 |






<a name="pb-GroupUserStateDf"></a>

### GroupUserStateDf



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| UpperLimit | [int64](#int64) |  |  |
| Game | [string](#string) |  |  |
| Language | [string](#string) |  |  |
| IsLogin | [int32](#int32) |  |  |






<a name="pb-LoginStatus"></a>

### LoginStatus



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| is_login | [int32](#int32) |  |  |





 

 

 

 



<a name="indices-proto"></a>
<p align="right"><a href="#top">Top</a></p>

## indices.proto



<a name="pb-IndicesCfgResp"></a>

### IndicesCfgResp



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| cat_types | [IndicesCfgResp.CatTypesEntry](#pb-IndicesCfgResp-CatTypesEntry) | repeated | 内容配置json |






<a name="pb-IndicesCfgResp-CatTypesEntry"></a>

### IndicesCfgResp.CatTypesEntry



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| key | [string](#string) |  |  |
| value | [string](#string) |  |  |






<a name="pb-IndicesDelReq"></a>

### IndicesDelReq



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| id | [uint64](#uint64) |  | id @gotags: validate:&#34;required&#34; |






<a name="pb-IndicesEnableReq"></a>

### IndicesEnableReq



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| id | [uint64](#uint64) |  | id @gotags: validate:&#34;required&#34; |
| enable | [bool](#bool) |  | 启用true 禁用false |






<a name="pb-IndicesListResp"></a>

### IndicesListResp



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| data | [IndicesListResp.item](#pb-IndicesListResp-item) | repeated | 列表 |






<a name="pb-IndicesListResp-item"></a>

### IndicesListResp.item



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| id | [uint64](#uint64) |  |  |
| cat_type | [string](#string) |  |  |
| name | [string](#string) |  |  |
| cat_ids | [uint32](#uint32) | repeated |  |
| updated_at | [string](#string) |  |  |
| updated_user | [string](#string) |  |  |
| project | [string](#string) |  |  |
| enable | [bool](#bool) |  | 启用true 禁用false |






<a name="pb-IndicesSaveReq"></a>

### IndicesSaveReq



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| id | [uint64](#uint64) |  | 保存接口必传，新增接口不传 |
| cat_type | [string](#string) |  | 配置内容类型 @gotags: validate:&#34;required&#34; |
| cat_ids | [uint32](#uint32) | repeated | 关联问题分类 @gotags: validate:&#34;required&#34; |
| project | [string](#string) |  | project 新增接口必传 |





 

 

 


<a name="pb-IndicesApi"></a>

### IndicesApi


| Method Name | Request Type | Response Type | Description |
| ----------- | ------------ | ------------- | ------------|
| Config | [ProjectLang](#pb-ProjectLang) | [IndicesCfgResp](#pb-IndicesCfgResp) | 顶部数据-配置内容列表接口 |
| List | [ProjectLang](#pb-ProjectLang) | [IndicesListResp](#pb-IndicesListResp) | 顶部数据-列表接口 |
| Save | [IndicesSaveReq](#pb-IndicesSaveReq) | [Empty](#pb-Empty) | 顶部数据-新增/保存接口 |
| Enable | [IndicesEnableReq](#pb-IndicesEnableReq) | [Empty](#pb-Empty) | 顶部数据-启用/禁用接口 |
| Delete | [IndicesDelReq](#pb-IndicesDelReq) | [Empty](#pb-Empty) | 顶部数据-删除接口 |
| Release | [ProjectLang](#pb-ProjectLang) | [Empty](#pb-Empty) | 顶部数据-发布接口 |

 



<a name="label_group-proto"></a>
<p align="right"><a href="#top">Top</a></p>

## label_group.proto



<a name="pb-GroupDelReq"></a>

### GroupDelReq
删除分单定义


| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| id | [uint32](#uint32) |  |  |
| user | [string](#string) |  |  |






<a name="pb-GroupIdReq"></a>

### GroupIdReq
GroupsIdReq 技能组ID 请求参数


| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| group_id | [uint32](#uint32) |  | 技能组ID @gotags: validate:&#34;required&#34; |






<a name="pb-GroupInfoResp"></a>

### GroupInfoResp
GroupEditReq 技能组信息 响应结果


| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| group_id | [uint32](#uint32) |  | 技能组ID |
| group_desc | [string](#string) |  | 团队名称 |
| user | [string](#string) | repeated | 人员列表 |
| game | [string](#string) | repeated | string user = 3; 游戏列表 |
| language | [string](#string) | repeated | string game = 4; 语言 |
| categories | [string](#string) | repeated | 问题分类 |
| upper_limit | [int32](#int32) |  | 技能组接单上限 |






<a name="pb-GroupListReq"></a>

### GroupListReq
GroupListReq 团队分单列表请求参数


| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| group_desc | [string](#string) |  | 团队名称 |
| user | [string](#string) |  | user |
| page | [uint32](#uint32) |  | 页码 |
| page_size | [uint32](#uint32) |  | 页大小 |






<a name="pb-GroupListResp"></a>

### GroupListResp
GroupListResp 技能组列表】响应结果


| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| current_page | [uint32](#uint32) |  | 当前页 |
| per_page | [uint32](#uint32) |  | 页大小 |
| total | [uint32](#uint32) |  | 总数 |
| data | [GroupListResp.Group](#pb-GroupListResp-Group) | repeated | 数据列表 |






<a name="pb-GroupListResp-Group"></a>

### GroupListResp.Group



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| id | [uint32](#uint32) |  | 团队 user的id |
| group_id | [uint32](#uint32) |  | 团队id |
| group_desc | [string](#string) |  | 团队名称 |
| user | [string](#string) |  | 人员列表 repeated string user = 4; |
| game | [string](#string) | repeated | 游戏列表 |
| updated_at | [string](#string) |  | 更新时间 |
| operator | [string](#string) |  | 更新用户 |
| enable | [bool](#bool) |  | 状态 |
| language | [string](#string) | repeated | 语言 |
| upper_limit | [uint32](#uint32) |  | 技能组接单上限 |






<a name="pb-GroupSaveReq"></a>

### GroupSaveReq
更新分单定义


| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| group_id | [uint32](#uint32) |  | 组id |
| group_desc | [string](#string) |  | 团队 |
| upper_limit | [int32](#int32) |  | 接单上限 |
| id | [int32](#int32) |  | 更新人员Id |
| game | [string](#string) | repeated | 游戏 |
| language | [string](#string) | repeated | 语言 |
| user | [string](#string) | repeated | 人员 |






<a name="pb-GroupUserReq"></a>

### GroupUserReq
客服在线状态请求参数


| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| account | [string](#string) |  |  |
| status | [uint32](#uint32) |  | 1:在线 2:离线 |






<a name="pb-GroupUserResp"></a>

### GroupUserResp
客服在线状态响应参数


| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| status | [string](#string) |  |  |






<a name="pb-TagLibAddReq"></a>

### TagLibAddReq
GroupAddReq 添加标签库请求参数


| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| lib_name | [string](#string) |  | 标签库名称 @gotags: validate:&#34;required&#34; |
| lib_file_url | [string](#string) |  | 标签数据 - 原始文件地址 @gotags: validate:&#34;required&#34; |
| projects | [string](#string) | repeated | 关联游戏 @gotags: validate:&#34;required,gt=0&#34; |






<a name="pb-TagLibID"></a>

### TagLibID
TagLibIDReq 标签库ID请求参数


| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| lib_id | [uint32](#uint32) |  | 标签库ID @gotags: validate:&#34;required&#34; |






<a name="pb-TagLibInfoResp"></a>

### TagLibInfoResp
TagLibInfoResp 标签库信息


| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| lib_id | [uint32](#uint32) |  | 标签库ID |
| lib_name | [string](#string) |  | 标签库名称 |
| lib_file_url | [string](#string) |  | 标签库描述 |
| projects | [string](#string) | repeated | 标签数据 - 原始文件地址 |
| project | [string](#string) | repeated | @gotags: json:&#34;project&#34; |






<a name="pb-TagLibListReq"></a>

### TagLibListReq
TagLibListReq 标签库列表搜索


| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| lib_name | [string](#string) |  | 标签库名称 |






<a name="pb-TagLibListResp"></a>

### TagLibListResp
TagLibListResp 标签库列表响应结果


| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| data | [TagLibListResp.TagLib](#pb-TagLibListResp-TagLib) | repeated |  |






<a name="pb-TagLibListResp-TagLib"></a>

### TagLibListResp.TagLib



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| lib_id | [uint32](#uint32) |  | 标签库ID |
| lib_name | [string](#string) |  | 标签库名称 |
| tag_upload_file | [string](#string) |  | 标签数据 - 原始文件地址 |
| updated_at | [string](#string) |  | 更新时间 |
| op | [string](#string) |  | 更新用户 |
| enable | [bool](#bool) |  | 状态 |
| projects | [string](#string) | repeated | 项目列表 @gotags: gorm:&#34;-&#34; |






<a name="pb-TagLibOptsResp"></a>

### TagLibOptsResp
TagLibOptsResp 标签组列表


| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| data | [TagLibOptsResp.TagLib](#pb-TagLibOptsResp-TagLib) | repeated |  |






<a name="pb-TagLibOptsResp-TagLib"></a>

### TagLibOptsResp.TagLib



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| lib_id | [uint32](#uint32) |  | 标签ID |
| lib_name | [string](#string) |  | 标签名称 |






<a name="pb-TagLibSaveReq"></a>

### TagLibSaveReq
TagLibSaveReq 更新标签库请求参数


| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| lib_id | [uint32](#uint32) |  | 标签库ID |
| lib_name | [string](#string) |  | 标签库名称 @gotags: validate:&#34;required&#34; |
| lib_file_url | [string](#string) |  | 标签数据-原始文件地址 @gotags: validate:&#34;required&#34; |
| projects | [string](#string) | repeated | 关联游戏 @gotags: validate:&#34;required,gt=0&#34; |






<a name="pb-TagOptsReq"></a>

### TagOptsReq



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| project_name | [string](#string) |  | 项目名称-根据项目名称进行搜索 @gotags: validate:&#34;required&#34; |
| lib_id | [uint32](#uint32) |  | 标签库ID - 根据标签库ID进行搜索 |






<a name="pb-TagOptsResp"></a>

### TagOptsResp
TagOptsResp 标签列表


| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| data | [TagOptsResp.Tag](#pb-TagOptsResp-Tag) | repeated |  |






<a name="pb-TagOptsResp-Tag"></a>

### TagOptsResp.Tag



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| tag_id | [uint32](#uint32) |  | 标签ID |
| tag_name | [string](#string) |  | 标签名称 |
| level | [uint32](#uint32) |  | 级别 |
| enable | [bool](#bool) |  | 启用 |
| children | [TagOptsResp.Tag](#pb-TagOptsResp-Tag) | repeated | 子结构 @gotags: json:&#34;children,omitempty&#34; |





 

 

 

 



<a name="service-proto"></a>
<p align="right"><a href="#top">Top</a></p>

## service.proto


 

 

 


<a name="pb-AllocGroupApi"></a>

### AllocGroupApi
分单配置

| Method Name | Request Type | Response Type | Description |
| ----------- | ------------ | ------------- | ------------|
| GroupList | [GroupListReq](#pb-GroupListReq) | [GroupListResp](#pb-GroupListResp) | 分单配置 - 列表 |
| GroupAdd | [GroupSaveReq](#pb-GroupSaveReq) | [Empty](#pb-Empty) | 分单配置 - 修改 |
| GroupInfo | [GroupIdReq](#pb-GroupIdReq) | [GroupInfoResp](#pb-GroupInfoResp) | 分单配置 - 详情 |
| GroupOpts | [Project](#pb-Project) | [GroupOptsResp](#pb-GroupOptsResp) | 分单配置 - 查询团队 |
| AllocGroupEnable | [EnableReq](#pb-EnableReq) | [Empty](#pb-Empty) | 分单配置 - 分单逻辑 |
| AllocGroupDel | [GroupDelReq](#pb-GroupDelReq) | [Empty](#pb-Empty) | 分单删除 - 分单逻辑 |


<a name="pb-PunlicApi"></a>

### PunlicApi
公共组

| Method Name | Request Type | Response Type | Description |
| ----------- | ------------ | ------------- | ------------|
| AddonsEnum | [Empty](#pb-Empty) | [Empty](#pb-Empty) | 基础接口 - 枚举 |
| AddonsLang | [Empty](#pb-Empty) | [Empty](#pb-Empty) | 语言列表 |
| AddonsGameList | [Empty](#pb-Empty) | [Empty](#pb-Empty) | 游戏列表 |
| AddonsUserist | [Empty](#pb-Empty) | [UserListResp](#pb-UserListResp) | 客服列表 |
| AddonsChannelList | [Project](#pb-Project) | [Empty](#pb-Empty) | 渠道列表 |
| AddonsChannelTreeList | [Project](#pb-Project) | [Empty](#pb-Empty) | 渠道&#43;子渠道列表 |


<a name="pb-TagLibApi"></a>

### TagLibApi
标签库

| Method Name | Request Type | Response Type | Description |
| ----------- | ------------ | ------------- | ------------|
| TagOpts | [TagOptsReq](#pb-TagOptsReq) | [TagOptsResp](#pb-TagOptsResp) | 标签列表 - 筛选 |
| TagLibOpts | [TagOptsReq](#pb-TagOptsReq) | [TagLibOptsResp](#pb-TagLibOptsResp) | 标签库 - 筛选列表接口 |
| TagLibList | [TagLibListReq](#pb-TagLibListReq) | [TagLibListResp](#pb-TagLibListResp) | 标签库配置 - 列表接口 |
| TagLibInfo | [TagLibID](#pb-TagLibID) | [TagLibInfoResp](#pb-TagLibInfoResp) | 标签库配置 - 详情 |
| TagLibSave | [TagLibSaveReq](#pb-TagLibSaveReq) | [TagLibID](#pb-TagLibID) | 标签库配置 - 新增&amp;修改 |
| TagLibEnable | [EnableReq](#pb-EnableReq) | [Empty](#pb-Empty) | 标签库配置 - 修改禁用启用 |


<a name="pb-TicketApi"></a>

### TicketApi
工单

| Method Name | Request Type | Response Type | Description |
| ----------- | ------------ | ------------- | ------------|
| TicketWorkPool | [TicketPoolNewListReq](#pb-TicketPoolNewListReq) | [TicketPoolListResp](#pb-TicketPoolListResp) | 工单池 - 搜索接口 |
| TicketCount | [TicketCountReq](#pb-TicketCountReq) | [TicketCountResp](#pb-TicketCountResp) | 工单池 - 工单池数据概览 |
| TicketWorkPoolExport | [TicketPoolNewListReq](#pb-TicketPoolNewListReq) | [Empty](#pb-Empty) | 工单池 - 工单导出接口 |
| Reassign | [AssignmentReq](#pb-AssignmentReq) | [Empty](#pb-Empty) | 工单池 - 指派接口 |
| TicketInfo | [TicketIdReq](#pb-TicketIdReq) | [TicketResponse](#pb-TicketResponse) | 工单池 - 所有基础数据 |
| TicketReTags | [TicketIdReq](#pb-TicketIdReq) | [TicketTagRes](#pb-TicketTagRes) | 工单池 -- 工单已绑定标签 |
| TicketReTagging | [TicketRetaggingReq](#pb-TicketRetaggingReq) | [Empty](#pb-Empty) | 工单池 -- 添加标签 |
| TicketAddRemark | [TicketRemarkReq](#pb-TicketRemarkReq) | [Empty](#pb-Empty) | 工单池 -- 添加备注 |
| TicketUpgrade | [TicketUpgradeReq](#pb-TicketUpgradeReq) | [Empty](#pb-Empty) | 工单池 - 工单升级 |
| TicketTurn | [AssignmentReq](#pb-AssignmentReq) | [Empty](#pb-Empty) | 工单 - 工单流转 |
| TicketReply | [TicketTransferReq](#pb-TicketTransferReq) | [Empty](#pb-Empty) | 工单 - 工单状态变更：工单回复|工单关单|工单拒单 |
| TicketIsAcceptor | [GroupUserReq](#pb-GroupUserReq) | [TicketIsUserResp](#pb-TicketIsUserResp) | 客服是否在线 |

 



<a name="tag-proto"></a>
<p align="right"><a href="#top">Top</a></p>

## tag.proto



<a name="pb-TagAddReq"></a>

### TagAddReq
TagReq 添加标签请求参数


| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| lib_id | [uint32](#uint32) |  | 标签组ID @gotags: validate:&#34;required&#34; |
| level | [uint32](#uint32) |  | 级别 |
| pid | [uint32](#uint32) |  | 父级ID |
| tag_name | [string](#string) |  | 标签名称 @gotags: validate:&#34;required&#34; |






<a name="pb-TagGroup"></a>

### TagGroup



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| level_one | [string](#string) |  |  |
| level_two | [string](#string) |  |  |
| level_three | [string](#string) |  |  |






<a name="pb-TagGroups"></a>

### TagGroups



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| tags | [TagGroup](#pb-TagGroup) | repeated |  |






<a name="pb-TagId"></a>

### TagId



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| tag_id | [uint32](#uint32) |  | 标签ID @gotags: validate:&#34;required&#34; |






<a name="pb-TagNode"></a>

### TagNode



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| name | [string](#string) |  |  |
| children | [TagNode](#pb-TagNode) | repeated |  |





 

 

 

 



<a name="team-proto"></a>
<p align="right"><a href="#top">Top</a></p>

## team.proto



<a name="pb-TeamOptsResp"></a>

### TeamOptsResp



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| list | [TeamOptsResp.Opts](#pb-TeamOptsResp-Opts) | repeated |  |






<a name="pb-TeamOptsResp-Opts"></a>

### TeamOptsResp.Opts



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| team_id | [uint32](#uint32) |  | 团队ID |
| team_name | [string](#string) |  | 团队名称 |





 

 

 

 



<a name="ticket-proto"></a>
<p align="right"><a href="#top">Top</a></p>

## ticket.proto



<a name="pb-AssignmentReq"></a>

### AssignmentReq
AssignmentReq 指派请求参数


| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| ticket_id | [uint64](#uint64) |  | 工单ID @gotags: validate:&#34;required&#34; |
| acceptor | [string](#string) |  | 其他客服 @gotags: validate:&#34;required&#34; |






<a name="pb-CustomerService"></a>

### CustomerService



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| id | [uint32](#uint32) |  |  |
| order_count | [int32](#int32) |  |  |
| wait_time | [int32](#int32) |  |  |






<a name="pb-TicketCountReq"></a>

### TicketCountReq
工单总量请求参数


| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| project | [string](#string) | repeated | 游戏权限 |
| stage | [uint32](#uint32) | repeated | 工单状态 |
| acceptor | [string](#string) | repeated | 受理人 |
| lang | [string](#string) | repeated | 语言 |
| vip | [uint32](#uint32) | repeated | VIP |
| priority | [uint32](#uint32) |  | 升级区 |






<a name="pb-TicketCountResp"></a>

### TicketCountResp
工单总量响应值


| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| pending_count | [uint64](#uint64) |  | 待处理工单条数 |
| user_pending_count | [uint64](#uint64) |  | 当前用户待处理工单量 |
| user_completed_count | [uint64](#uint64) |  | 当前用户已完成工单量 |
| pending_cn_count | [uint64](#uint64) |  | 待处理中文工单条数 |
| pending_vip_count | [uint64](#uint64) |  | 待处理vip工单条数 |
| priority_count | [uint64](#uint64) |  | 升级区工单条数 |






<a name="pb-TicketDialogueInfoResp"></a>

### TicketDialogueInfoResp
TicketDialogueInfoResp 详情对话 todo  带时间戳

被举报人昵称
被举报海域
详情
截图    以上调用接口


| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| dialogue_information | [string](#string) | repeated | 对话信息 |
| remarks | [string](#string) | repeated | 客服备注消息 |
| label | [int32](#int32) | repeated | 标签 |






<a name="pb-TicketDialogueResp"></a>

### TicketDialogueResp
工单表单 对话信息


| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| detail | [string](#string) |  | 对话内容 、工单 form |
| created_at | [string](#string) |  |  |
| operator | [string](#string) |  | 操作员 |
| from_role | [uint32](#uint32) |  | 回复角色类型 1:玩家 2:客服 3:系统&#39;, |
| commu_type | [string](#string) |  | &#39;消息类型： CommuTypeDialogue：对话消息；CommuTypeRemark：增加备注&#39;, |
| is_ticket | [uint32](#uint32) |  | 工单详情 1:是工单form详情; 2: 重开提交信息 |
| files | [string](#string) |  | 重开单 - 图片/视频资源 |
| created_time | [uint64](#uint64) |  | 时间 - 时间戳 |






<a name="pb-TicketGroupInfoResp"></a>

### TicketGroupInfoResp
TicketHistoryInfo 分单日志信息


| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| ticket_id | [uint64](#uint64) |  | 工单ID |
| operate | [string](#string) |  | 操作 |
| op_detail | [string](#string) |  | 子操作 |
| remark | [string](#string) |  | 备注 |
| created_at | [string](#string) |  | 创建时间 |
| from_ticket_id | [uint64](#uint64) |  | 旧工单ID @gotags: json:&#34;from_ticket_id,omitempty&#34; |






<a name="pb-TicketHistResp"></a>

### TicketHistResp
工单 - 日志记录详情


| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| ticket_id | [uint64](#uint64) |  | 工单id |
| type | [string](#string) |  | 分组描述 |
| remark | [string](#string) |  | 详情 |
| created_at | [string](#string) |  | 时间 |






<a name="pb-TicketHistoriesReq"></a>

### TicketHistoriesReq
TicketHistoryReq 工单日志请求


| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| ticket_id | [uint64](#uint64) |  | 工单ID @gotags: validate:&#34;required&#34; |
| relate_type | [uint32](#uint32) |  | 关联类型 1:工单 2:流程 |






<a name="pb-TicketHistoriesResp"></a>

### TicketHistoriesResp
TicketHistoriesResp 工单日志列表


| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| data | [TicketHistoryInfo](#pb-TicketHistoryInfo) | repeated | 操作日志 |






<a name="pb-TicketHistoryInfo"></a>

### TicketHistoryInfo
TicketHistoryInfo 工单日志信息


| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| ticket_id | [uint64](#uint64) |  | 工单ID |
| operate | [string](#string) |  | 操作 |
| op_detail | [string](#string) |  | 子操作 |
| remark | [string](#string) |  | 备注 |
| created_at | [string](#string) |  | 创建时间 |
| from_ticket_id | [uint64](#uint64) |  | 旧工单ID @gotags: json:&#34;from_ticket_id,omitempty&#34; |






<a name="pb-TicketIdReq"></a>

### TicketIdReq
TicketId 工单ID


| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| ticket_id | [uint64](#uint64) |  | 工单ID @gotags: validate:&#34;required&#34; |
| ticket_sys_type | [TicketSys](#pb-TicketSys) |  | 使用哪个工单系统 |
| page | [uint32](#uint32) |  | 页码 |
| page_size | [uint32](#uint32) |  | 页大小 |






<a name="pb-TicketInfo"></a>

### TicketInfo



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| ticket_id | [uint64](#uint64) |  | 工单ID |
| project | [string](#string) |  | 所属项目 |
| origin | [uint32](#uint32) |  | 问题分类 string category = 3; 工单来源 |
| conversion_node | [uint32](#uint32) |  | 工单节点 |
| status | [uint32](#uint32) |  | 状态 |
| created_at | [string](#string) |  | 创建时间 |
| closed_at | [string](#string) |  | 结案时间 |
| creator | [string](#string) |  | 创建人 |
| acceptor | [string](#string) |  | 受理员 |
| lang | [string](#string) |  | 语言 |
| vip | [uint32](#uint32) |  | vip等级 |
| priority | [uint32](#uint32) |  | 紧急度 |
| account_id | [string](#string) |  | account_id |
| csi | [uint32](#uint32) |  | csi |
| comments | [string](#string) |  | 评语

团队 工单池 string team = 18; //todo conversion_node uint32 transfer = 19; //todo 流转对象 todo Transfer transfer_node = 20; |






<a name="pb-TicketIsUserReq"></a>

### TicketIsUserReq
TicketIsUserRsq 客服在线状态


| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| empno | [uint32](#uint32) |  | 客服工号 |






<a name="pb-TicketIsUserResp"></a>

### TicketIsUserResp
TicketIsUserResp 客服在线状态


| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| status | [uint32](#uint32) |  | 客服在线状态 0:不在线 1:在线 |






<a name="pb-TicketLabel"></a>

### TicketLabel



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| ticket_id | [uint64](#uint64) |  | 工单ID @gotags: validate:&#34;required&#34; |
| label_id | [uint32](#uint32) | repeated | 删除的标签ID |






<a name="pb-TicketPoolHistoryListReq"></a>

### TicketPoolHistoryListReq
TicketPoolHistoryListReq 历史工单池请求参数


| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| ticket_id | [uint64](#uint64) |  | 工单ID |
| page | [uint32](#uint32) |  | 页码 |
| page_size | [uint32](#uint32) |  | 页大小 |






<a name="pb-TicketPoolListResp"></a>

### TicketPoolListResp
TicketPoolListReq 工单池列表响应结果


| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| current_page | [uint32](#uint32) |  |  |
| per_page | [uint32](#uint32) |  |  |
| total | [uint32](#uint32) |  |  |
| data | [TicketInfo](#pb-TicketInfo) | repeated |  |






<a name="pb-TicketPoolNewListReq"></a>

### TicketPoolNewListReq
TicketPoolNewListReq 新工单池请求参数


| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| project | [string](#string) | repeated | 游戏 |
| created_at | [string](#string) | repeated | 创建时间 |
| closed_at | [string](#string) | repeated | 结单时间 |
| evaluate_at | [string](#string) | repeated | 评价时间 |
| status | [uint32](#uint32) | repeated | 工单状态 - 工单池 |
| scene | [uint32](#uint32) | repeated | 工单来源 |
| channel | [string](#string) | repeated | 渠道包 |
| label | [uint32](#uint32) | repeated | 标签 |
| nps | [uint32](#uint32) | repeated | NPS |
| csi | [uint32](#uint32) | repeated | 工单评星 |
| language | [string](#string) | repeated | 工单语言 |
| sid | [string](#string) |  | 服务器 |
| ticket_id | [uint64](#uint64) |  | 工单ID |
| acceptor_type | [FilterEnum](#pb-FilterEnum) |  | 处理人类型 1:空白 2:包含 |
| acceptor | [string](#string) |  | 处理人 - 工单池 |
| remark | [string](#string) |  | 备注 |
| creator_type | [CreatorType](#pb-CreatorType) |  | 提交人查询类型 1:玩家fpid 2：玩家姓名 3:客服账号，4：account_id 5:uid |
| creator | [string](#string) |  | 提交人查询值 |
| page | [uint32](#uint32) |  | 页码 |
| page_size | [uint32](#uint32) |  | 页大小 |
| cat_id | [uint32](#uint32) | repeated | 问题分类 |
| is_vip | [bool](#bool) |  | 是否是 VIP: true：表示是 VIP |
| is_upgrade | [bool](#bool) |  | 是否是 升级单： true: 表示是 升级单 |
| sort_by | [TkPoolSort](#pb-TkPoolSort) |  | 数据排序方式： 0：等待时长； 1：创建时间；2：充值金额 |






<a name="pb-TicketPoolNewListResp"></a>

### TicketPoolNewListResp
TicketPoolNewListResp 新工单池响应参数


| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| current_page | [uint32](#uint32) |  |  |
| per_page | [uint32](#uint32) |  |  |
| total | [uint32](#uint32) |  |  |
| data | [TicketPoolNewListResp.TicketPoolInfo](#pb-TicketPoolNewListResp-TicketPoolInfo) | repeated |  |






<a name="pb-TicketPoolNewListResp-TicketPoolInfo"></a>

### TicketPoolNewListResp.TicketPoolInfo



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| project | [string](#string) |  | 游戏 |
| ticket_id | [uint64](#uint64) |  | 工单ID |
| detail | [string](#string) |  | 玩家输入第一句话 |
| recharge | [double](#double) |  | 累计金额 |
| status | [uint32](#uint32) |  | 工单状态 |
| waiting_time | [string](#string) |  | 等待时长 |






<a name="pb-TicketPoolNewTopResp"></a>

### TicketPoolNewTopResp
TicketPoolNewTopResp 顶部信息






<a name="pb-TicketPoolNewTopResp-UserInfoResp"></a>

### TicketPoolNewTopResp.UserInfoResp



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| label | [int32](#int32) | repeated | 标签 |
| category | [string](#string) |  | 问题类型 |
| created_at | [string](#string) |  | 创建时间 |
| channel | [string](#string) |  | 渠道包 |
| device_type | [string](#string) |  | 设备型号 |
| rom_gb | [double](#double) |  | 存储总量 |
| remain_rom | [double](#double) |  | 存储剩余总量 |
| recharge | [string](#string) |  | 充值金额 |
| language | [string](#string) | repeated | 玩家语言 |
| country | [string](#string) | repeated | 国家 |
| os_version | [string](#string) |  | 系统版本 |






<a name="pb-TicketPoolNewTopResp-top"></a>

### TicketPoolNewTopResp.top



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| ticket_id | [uint64](#uint64) | repeated | 工单ID |
| acceptor | [string](#string) |  | - 处理人（当没有处理人时，此处显示：待接单） todo 代码处理 |
| nickname | [uint64](#uint64) | repeated | 玩家昵称 |
| fpid | [uint32](#uint32) | repeated | Fpid |
| uid | [uint32](#uint32) | repeated | Uid |
| game_version | [string](#string) | repeated | 游戏版本 |
| sid | [uint32](#uint32) | repeated | 服务器 |






<a name="pb-TicketPoolTopResp"></a>

### TicketPoolTopResp
TicketPoolNewTopResp 顶部信息


| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| ticket_id | [uint64](#uint64) |  | 工单ID |
| acceptor | [string](#string) |  | - 处理人（当没有处理人时，此处显示：待接单） todo 代码处理 |
| nickname | [string](#string) |  | 玩家昵称 |
| account_id | [string](#string) |  | Fpid |
| uid | [uint64](#uint64) |  | Uid |
| app_version | [string](#string) |  | 游戏版本 |
| sid | [uint32](#uint32) |  | 服务器 |
| project | [string](#string) |  | 游戏 |
| priority | [uint32](#uint32) |  | 升级单： 1：正常单；2：升级单 |
| status | [TkStage](#pb-TkStage) |  | 当前工单流转状态 |






<a name="pb-TicketRecordResp"></a>

### TicketRecordResp
TicketRecordResp 历史工单记录


| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| current_page | [uint32](#uint32) |  |  |
| per_page | [uint32](#uint32) |  |  |
| total | [uint32](#uint32) |  |  |
| data | [TicketRecordResp.Record](#pb-TicketRecordResp-Record) | repeated |  |






<a name="pb-TicketRecordResp-Record"></a>

### TicketRecordResp.Record



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| project | [string](#string) |  | 游戏 |
| ticket_id | [uint64](#uint64) |  | 工单ID |
| detail | [string](#string) |  | 玩家输入第一句话 |
| recharge | [double](#double) |  | 累计金额 |
| status | [uint32](#uint32) |  | 工单状态 |
| waiting_time | [string](#string) |  | 等待时长 |






<a name="pb-TicketRemarkReq"></a>

### TicketRemarkReq
TicketRemarkReq 添加工单备注


| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| ticket_id | [uint64](#uint64) |  | 工单ID @gotags: validate:&#34;required,gte=1&#34; |
| content | [string](#string) |  | 备注 @gotags: validate:&#34;required&#34; |






<a name="pb-TicketResponse"></a>

### TicketResponse
工单详情


| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| top_info | [TicketPoolTopResp](#pb-TicketPoolTopResp) |  | 工单详情 - 顶部信息 |
| user_info | [UserInfoResp](#pb-UserInfoResp) |  | 工单详情 - 基本信息 |
| record_info | [TicketRecordResp](#pb-TicketRecordResp) |  | 工单详情 - 历史工单 |
| commu_info | [TicketDialogueResp](#pb-TicketDialogueResp) | repeated | 工单详情 - 对话 |
| history | [TicketHistResp](#pb-TicketHistResp) | repeated | 工单详情 - 分单日志 |






<a name="pb-TicketRetaggingReq"></a>

### TicketRetaggingReq
TicketTagReq 重新给现有的工单打标签


| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| ticket_id | [uint64](#uint64) |  | 工单ID @gotags: validate:&#34;required&#34; |
| label_id | [uint32](#uint32) | repeated | 更新后的标签ID |






<a name="pb-TicketStatusResp"></a>

### TicketStatusResp
TicketStatusReq 工单状态


| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| status | [uint32](#uint32) |  | 工单状态 1,待接单 2,待处理 3,处理中-待玩家回复 4,处理中-玩家已回复 5,超时关闭 6,重开 7,拒单关闭 8,已完成 |






<a name="pb-TicketTagRes"></a>

### TicketTagRes



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| ticket_id | [uint64](#uint64) |  |  |
| label_id | [uint32](#uint32) | repeated |  |






<a name="pb-TicketTagsReq"></a>

### TicketTagsReq
获取多个工单绑定的 label


| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| ticket_id | [uint64](#uint64) | repeated | 工单ID @gotags: validate:&#34;required,gt=0&#34; |






<a name="pb-TicketTagsResp"></a>

### TicketTagsResp
获取多个工单绑定的 label 相应详情


| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| details | [TicketLabel](#pb-TicketLabel) | repeated | 工单绑定关系 |






<a name="pb-TicketTransferReq"></a>

### TicketTransferReq



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| ticket_id | [uint64](#uint64) |  | 工单ID @gotags: validate:&#34;required&#34; |
| op_type | [TkTransferOpType](#pb-TkTransferOpType) |  | 操作分类：1:客服回复;2:回复&amp;关单;3:拒单 @gotags: validate:&#34;required,gt=0&#34; |
| content | [string](#string) |  | 回复内容 |






<a name="pb-TicketUpgradeReq"></a>

### TicketUpgradeReq



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| ticket_id | [uint64](#uint64) |  | 工单ID @gotags: validate:&#34;required,gt=0&#34; |
| upgrade | [uint32](#uint32) |  | 工单升级/降级：1:降级；2:升级 @gotags: validate:&#34;required,oneof=1 2&#34; |






<a name="pb-TicketUserListReq"></a>

### TicketUserListReq
TicketUserListReq 客服昵称列表


| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| nickname | [uint32](#uint32) |  | 客服昵称 |






<a name="pb-UserInfoResp"></a>

### UserInfoResp
UserInfoResp 基础信息


| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| tag_name | [string](#string) | repeated | 标签 |
| category | [string](#string) |  | 问题类型 |
| created_at | [string](#string) |  | 创建时间 |
| channel | [string](#string) |  | 渠道包 |
| device_type | [string](#string) |  | 设备型号 |
| rom_gb | [uint32](#uint32) |  | 存储总量 |
| remain_rom | [uint32](#uint32) |  | 存储剩余总量 |
| recharge | [double](#double) |  | 充值金额 |
| lang | [string](#string) |  | 玩家语言 |
| country | [string](#string) |  | 国家 |
| os_version | [string](#string) |  | 系统版本 |





 


<a name="pb-FillStatus"></a>

### FillStatus
补填状态

| Name | Number | Description |
| ---- | ------ | ----------- |
| FillNone | 0 |  |
| PlayerRefillSt | 1 | 玩家已补填单 |
| VipRefillSt | 2 | VIP已补填单 |



<a name="pb-TkPoolSort"></a>

### TkPoolSort
工单列表排序方式

| Name | Number | Description |
| ---- | ------ | ----------- |
| TkPoolSortWaitDefault | 0 |  |
| TkPoolSortWaitTm | 1 | 等待时长 - 默认 |
| TkPoolSortCreateTm | 2 | 创建时间 |
| TkPoolSortRecharge | 3 | 充值金额 |



<a name="pb-TkTransferOpType"></a>

### TkTransferOpType


| Name | Number | Description |
| ---- | ------ | ----------- |
| TkTransferOpTypeUnknown | 0 |  |
| TkTransferOpTypeCommu | 1 | 客服回复 |
| TkTransferOpTypeCommuClose | 2 | 回复&amp;关单 |
| TkTransferOpTypeRejected | 3 | 拒单 |



<a name="pb-WorkbenchPart"></a>

### WorkbenchPart
WorkbenchPart 我的工单

| Name | Number | Description |
| ---- | ------ | ----------- |
| AllPart | 0 |  |
| PendingPart | 1 | 1:待处理工单部分 |
| DonePart | 2 | 2:已处理工单部分 |
| FillUpPart | 3 | 3:待补填工单部分 |


 

 

 



<a name="user-proto"></a>
<p align="right"><a href="#top">Top</a></p>

## user.proto



<a name="pb-UserListResp"></a>

### UserListResp



| Field | Type | Label | Description |
| ----- | ---- | ----- | ----------- |
| account | [string](#string) |  | 团队ID |





 

 

 

 



## Scalar Value Types

| .proto Type | Notes | C++ | Java | Python | Go | C# | PHP | Ruby |
| ----------- | ----- | --- | ---- | ------ | -- | -- | --- | ---- |
| <a name="double" /> double |  | double | double | float | float64 | double | float | Float |
| <a name="float" /> float |  | float | float | float | float32 | float | float | Float |
| <a name="int32" /> int32 | Uses variable-length encoding. Inefficient for encoding negative numbers – if your field is likely to have negative values, use sint32 instead. | int32 | int | int | int32 | int | integer | Bignum or Fixnum (as required) |
| <a name="int64" /> int64 | Uses variable-length encoding. Inefficient for encoding negative numbers – if your field is likely to have negative values, use sint64 instead. | int64 | long | int/long | int64 | long | integer/string | Bignum |
| <a name="uint32" /> uint32 | Uses variable-length encoding. | uint32 | int | int/long | uint32 | uint | integer | Bignum or Fixnum (as required) |
| <a name="uint64" /> uint64 | Uses variable-length encoding. | uint64 | long | int/long | uint64 | ulong | integer/string | Bignum or Fixnum (as required) |
| <a name="sint32" /> sint32 | Uses variable-length encoding. Signed int value. These more efficiently encode negative numbers than regular int32s. | int32 | int | int | int32 | int | integer | Bignum or Fixnum (as required) |
| <a name="sint64" /> sint64 | Uses variable-length encoding. Signed int value. These more efficiently encode negative numbers than regular int64s. | int64 | long | int/long | int64 | long | integer/string | Bignum |
| <a name="fixed32" /> fixed32 | Always four bytes. More efficient than uint32 if values are often greater than 2^28. | uint32 | int | int | uint32 | uint | integer | Bignum or Fixnum (as required) |
| <a name="fixed64" /> fixed64 | Always eight bytes. More efficient than uint64 if values are often greater than 2^56. | uint64 | long | int/long | uint64 | ulong | integer/string | Bignum |
| <a name="sfixed32" /> sfixed32 | Always four bytes. | int32 | int | int | int32 | int | integer | Bignum or Fixnum (as required) |
| <a name="sfixed64" /> sfixed64 | Always eight bytes. | int64 | long | int/long | int64 | long | integer/string | Bignum |
| <a name="bool" /> bool |  | bool | boolean | boolean | bool | bool | boolean | TrueClass/FalseClass |
| <a name="string" /> string | A string must always contain UTF-8 encoded or 7-bit ASCII text. | string | String | str/unicode | string | string | string | String (UTF-8) |
| <a name="bytes" /> bytes | May contain any arbitrary sequence of bytes. | string | ByteString | str | []byte | ByteString | string | String (ASCII-8BIT) |

