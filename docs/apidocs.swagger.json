{"swagger": "2.0", "info": {"title": "ai.proto", "version": "version not set"}, "tags": [{"name": "CategoryBackendAPI"}, {"name": "CategoryInnerAPI"}, {"name": "ConversationService"}, {"name": "DscApi"}, {"name": "DCSBotConfigService"}, {"name": "EgressAPI"}, {"name": "ExamineApi"}, {"name": "IndicesApi"}, {"name": "LineApi"}, {"name": "TicketApi"}, {"name": "DataPlatApi"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "TagLibApi"}, {"name": "AllTagLibApi"}, {"name": "UserAssignTicketApi"}, {"name": "ChannelCatApi"}, {"name": "OverTimeApi"}, {"name": "ModuleCatApi"}, {"name": "TeamConfigApi"}, {"name": "TicketModuleApi"}, {"name": "TicketTabApi"}, {"name": "QuestionApi"}, {"name": "TicketStrategyApi"}, {"name": "SurveyApi"}], "consumes": ["application/json"], "produces": ["application/json"], "paths": {"/api/addons/channel": {"post": {"summary": "渠道列表", "operationId": "PunlicApi_AddonsChannelList", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbEmpty"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbProject"}}], "tags": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"]}}, "/api/addons/channel_package_id": {"post": {"summary": "渠道+渠道号列表", "operationId": "PunlicApi_AddonsChannelPackageIDList", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbEmpty"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbProject"}}], "tags": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"]}}, "/api/addons/channel_tree": {"post": {"summary": "渠道+子渠道列表", "operationId": "PunlicApi_AddonsChannelTreeList", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbEmpty"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbProject"}}], "tags": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"]}}, "/api/addons/dsc_bot_list": {"post": {"summary": "discord机器人列表", "operationId": "PunlicApi_AddonsDscBotList", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbEmpty"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbProjects"}}], "tags": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"]}}, "/api/addons/enum": {"post": {"summary": "基础接口 - 枚举", "operationId": "PunlicApi_AddonsEnum", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbEmpty"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbEmpty"}}], "tags": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"]}}, "/api/addons/game_list": {"post": {"summary": "游戏列表", "operationId": "PunlicApi_AddonsGameList", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbEmpty"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbEmpty"}}], "tags": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"]}}, "/api/addons/lang": {"post": {"summary": "语言列表", "operationId": "PunlicApi_AddonsLang", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbEmpty"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbEmpty"}}], "tags": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"]}}, "/api/addons/user_list": {"post": {"summary": "客服列表", "operationId": "PunlicApi_AddonsUserist", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbUserListResp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbEmpty"}}], "tags": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"]}}, "/api/cat/release": {"post": {"summary": "模版发布- 清除缓存", "operationId": "CategoryBackendAPI_CategoryRelease", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbEmpty"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbCatProjectLang"}}], "tags": ["CategoryBackendAPI"]}}, "/api/channel/cat/add": {"post": {"summary": "discord 分类 - 添加分类", "operationId": "ChannelCatApi_ChannelCatAdd", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbEmpty"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbChannelCatAddReq"}}], "tags": ["ChannelCatApi"]}}, "/api/channel/cat/del": {"post": {"summary": "discord 分类 - 删除分类", "operationId": "ChannelCatApi_DscChannelCatDel", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbEmpty"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbChannelCatIdReq"}}], "tags": ["ChannelCatApi"]}}, "/api/channel/cat/save": {"post": {"summary": "discord 分类 - 修改分类", "operationId": "ChannelCatApi_DscChannelCatSave", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbEmpty"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbChannelCatSaveReq"}}], "tags": ["ChannelCatApi"]}}, "/api/channel/cat/tree": {"post": {"summary": "discord 分类 - 获取分类树", "operationId": "ChannelCatApi_ChannelCatTree", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbChannelCatTreeResp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbCatProjectReq"}}], "tags": ["ChannelCatApi"]}}, "/api/data_plat/game_item/batch_save": {"post": {"summary": "游戏 item - 道具ID保存", "operationId": "DataPlatApi_DataPlatGameItemSave", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbEmpty"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbDataPlatGameItemBatchSaveReq"}}], "tags": ["DataPlatApi"]}}, "/api/data_plat/game_item/list": {"post": {"summary": "游戏 item - 道具ID列表", "operationId": "DataPlatApi_DataPlatGameItemList", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbDataPlatGameItemListResp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbDataPlatGameItemListReq"}}], "tags": ["DataPlatApi"]}}, "/api/data_plat/game_item/opts": {"post": {"summary": "游戏 item - 道具ID列表", "operationId": "DataPlatApi_DataPlatGameItemOpts", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbDataPlatGameItemOptsResp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbDataPlatGameItemOptsReq"}}], "tags": ["DataPlatApi"]}}, "/api/data_plat/user/gold_info": {"post": {"summary": "精分玩家数据 - 金币查询", "operationId": "DataPlatApi_DataPlatUserGoldInfo", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbDataPlatUserGoldInfoResp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbDataPlatUserGoldInfoReq"}}], "tags": ["DataPlatApi"]}}, "/api/data_plat/user/item_info": {"post": {"summary": "精分玩家数据 - 道具查询", "operationId": "DataPlatApi_DataPlatUserItemInfo", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbDataPlatUserItemInfoResp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbDataPlatUserItemInfoReq"}}], "tags": ["DataPlatApi"]}}, "/api/data_plat/user/login_info": {"post": {"summary": "精分玩家数据 - 登录查询", "operationId": "DataPlatApi_DataPlatUserLoginInfo", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbDataPlatUserLoginInfoResp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbDataPlatUserLoginInfoReq"}}], "tags": ["DataPlatApi"]}}, "/api/data_plat/user/pay_info": {"post": {"summary": "精分玩家数据 - 支付查询", "operationId": "DataPlatApi_DataPlatUserPayInfo", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbDataPlatUserPayInfoResp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbDataPlatUserPayInfoReq"}}], "tags": ["DataPlatApi"]}}, "/api/dsc/channel/dialog": {"post": {"summary": "discord 玩家对话 - 会话历史", "operationId": "DscApi_DscChannelDialog", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbDscDialogDetailResp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbDscChannelDialogReq"}}], "tags": ["DscApi"]}}, "/api/dsc/channel/dialog_fresh": {"post": {"summary": "discord 玩家对话 - 新增会话+事件", "operationId": "DscApi_DscChannelDialogFresh", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbDscChannelDialogFreshResp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbDscChannelDialogFreshReq"}}], "tags": ["DscApi"]}}, "/api/dsc/channel/message_create": {"post": {"summary": "discord 消息 - 发送/回复消息", "operationId": "DscApi_DscReplyMessage", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbDscChannelMsgCreateResp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbDscChannelMsgCreateReq"}}], "tags": ["DscApi"]}}, "/api/dsc/channel/message_edit": {"post": {"summary": "discord 消息 - 修改消息", "operationId": "DscApi_DscEditMessage", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbDscChannelMsgCreateResp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbDscChannelMsgEditReq"}}], "tags": ["DscApi"]}}, "/api/dsc/channel/send_file": {"post": {"operationId": "DscApi_DscSendFile", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbDscDialogDetail"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbDscChannelFileCreateReq"}}], "tags": ["DscApi"]}}, "/api/dsc/portrait/edit": {"post": {"operationId": "DscApi_DscPortraitEdit", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbEmpty"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbPortraitEditReq"}}], "tags": ["DscApi"]}}, "/api/dsc/portrait/info": {"post": {"operationId": "DscApi_DscPortraitInfo", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbPortraitInfoResp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbPortraitInfoReq"}}], "tags": ["DscApi"]}}, "/api/dsc/tab/count": {"post": {"operationId": "DscApi_DscTabCount", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbDiscordTabCountResp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbEmpty"}}], "tags": ["DscApi"]}}, "/api/dsc/tab/edit": {"post": {"operationId": "DscApi_DscTabEdit", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbEmpty"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbDiscordTabEditReq"}}], "tags": ["DscApi"]}}, "/api/dsc/tag/batch_delete": {"post": {"summary": "Discord -- 批量删除标签", "operationId": "DscApi_DscTagBatchDelete", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbEmpty"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbDiscordTagBatchDelete"}}], "tags": ["DscApi"]}}, "/api/dsc/tag/public": {"post": {"summary": "Discord -- 公共标签", "operationId": "DscApi_DscPublicTag", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbDiscordPublicTagResp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbDiscordPublicTagReq"}}], "tags": ["DscApi"]}}, "/api/dsc/user/detail": {"post": {"summary": "discord 绑定玩家 - 详情查询 undo", "operationId": "DscApi_DscUserDetail", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbDscUserDetailResp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbDscUserDetailReq"}}], "tags": ["DscApi"]}}, "/api/dsc/user/pool": {"post": {"summary": "discord 绑定玩家 - 列表查询", "operationId": "DscApi_DscUserList", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbDscUserListResp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbDscUserListReq"}}], "tags": ["DscApi"]}}, "/api/dsc_bot_config/add": {"post": {"summary": "新增DC机器人配置", "operationId": "DCSBotConfigService_AddDCSBotConfig", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbEmpty"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbDCSBotConfigAddReq"}}], "tags": ["DCSBotConfigService"]}}, "/api/dsc_bot_config/check": {"post": {"summary": "验证DC机器人配置", "operationId": "DCSBotConfigService_CheckDCSBotConfig", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbEmpty"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbDCSBotConfigAddReq"}}], "tags": ["DCSBotConfigService"]}}, "/api/dsc_bot_config/list": {"post": {"summary": "获取DC机器人配置列表", "operationId": "DCSBotConfigService_GetDCSBotConfigList", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbDCSBotConfigListResp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbDCSBotConfigListReq"}}], "tags": ["DCSBotConfigService"]}}, "/api/dsc_bot_config/update_welcome_message": {"post": {"summary": "更新DC机器人配置欢迎消息", "operationId": "DCSBotConfigService_UpdateDCSBotConfigWelcomeMessage", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbEmpty"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbUpdateDCSBotConfigWelcomeMessageReq"}}], "tags": ["DCSBotConfigService"]}}, "/api/examine/order/dsc/detail": {"post": {"summary": "examine 质检单数据 - 详情", "operationId": "ExamineApi_ExamineDscOrderDetail", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbExamineDscOrderDetailResp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbExamineDscOrderDetailReq"}}], "tags": ["ExamineApi"]}}, "/api/examine/order/dsc/list": {"post": {"summary": "examine 质检单数据 - 列表", "operationId": "ExamineApi_ExamineDscOrderList", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbExamineDscOrderListResp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbExamineDscOrderListReq"}}], "tags": ["ExamineApi"]}}, "/api/examine/order/dsc/list_export": {"post": {"summary": "examine 质检单数据 - 列表Export", "operationId": "ExamineApi_ExamineDscOrderListExport", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbEmpty"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbExamineDscOrderListReq"}}], "tags": ["ExamineApi"]}}, "/api/examine/order/dsc/save": {"post": {"summary": "examine 质检单数据 - 保存", "operationId": "ExamineApi_ExamineDscOrderSave", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbEmpty"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbExamineDscOrderSaveReq"}}], "tags": ["ExamineApi"]}}, "/api/examine/order/dsc/stats": {"post": {"summary": "examine 质检单数据 - 统计数据", "operationId": "ExamineApi_ExamineDscOrderStats", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbExamineDscOrderStatsResp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbExamineDscOrderStatsReq"}}], "tags": ["ExamineApi"]}}, "/api/examine/order/notice/acceptor": {"post": {"summary": "examine 质检单红点通知 - 是否有红点", "operationId": "ExamineApi_ExamineOrderNoticeAcceptor", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbExamineOrderNoticeAcceptorResp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbEmpty"}}], "tags": ["ExamineApi"]}}, "/api/examine/order/notice/list": {"post": {"summary": "examine 质检单红点通知 - 列表", "operationId": "ExamineApi_ExamineOrderNoticeList", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbExamineOrderNoticeListResp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbExamineOrderNoticeListReq"}}], "tags": ["ExamineApi"]}}, "/api/examine/task/detail": {"post": {"summary": "examine 任务配置 - 详情", "operationId": "ExamineApi_ExamineTaskDetail", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbExamineTaskDetailResp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbExamineTaskDetailReq"}}], "tags": ["ExamineApi"]}}, "/api/examine/task/dsc/filter_count": {"post": {"summary": "examine 任务配置 - 过滤 全检标准 - 统计", "operationId": "ExamineApi_ExamineTaskDscFilterCount", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbExamineTaskDscFilterCountResp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbExamineTaskDscFilterCountReq"}}], "tags": ["ExamineApi"]}}, "/api/examine/task/list": {"post": {"summary": "examine 任务配置 - 列表", "operationId": "ExamineApi_ExamineTaskList", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbExamineTaskListResp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbExamineTaskListReq"}}], "tags": ["ExamineApi"]}}, "/api/examine/task/save": {"post": {"summary": "examine 任务配置 - 保存 add&save", "operationId": "ExamineApi_ExamineTaskSave", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbEmpty"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbExamineTaskSaveReq"}}], "tags": ["ExamineApi"]}}, "/api/examine/tpl/copy": {"post": {"summary": "examine 质检打分表配置 - 保存 add&save", "operationId": "ExamineApi_ExamineTplCopy", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbEmpty"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbExamineTplCopyReq"}}], "tags": ["ExamineApi"]}}, "/api/examine/tpl/del": {"post": {"summary": "examine 质检打分表配置 - 删除", "operationId": "ExamineApi_ExamineTplDel", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbEmpty"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbExamineTplDelReq"}}], "tags": ["ExamineApi"]}}, "/api/examine/tpl/detail": {"post": {"summary": "examine 质检打分表配置 - 详情", "operationId": "ExamineApi_ExamineTplDetail", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbExamineTplDetailResp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbExamineTplDetailReq"}}], "tags": ["ExamineApi"]}}, "/api/examine/tpl/enable": {"post": {"summary": "examine 质检打分表配置 - enable&disable", "operationId": "ExamineApi_ExamineTplEnable", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbEmpty"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbEnableReq"}}], "tags": ["ExamineApi"]}}, "/api/examine/tpl/list": {"post": {"summary": "examine 质检打分表配置 - 列表", "operationId": "ExamineApi_ExamineTplList", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbExamineTplListResp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbExamineTplListReq"}}], "tags": ["ExamineApi"]}}, "/api/examine/tpl/opts": {"post": {"summary": "examine 质检打分表配置 - 下来选项", "operationId": "ExamineApi_ExamineTplOpts", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbExamineTplOptsResp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbEmpty"}}], "tags": ["ExamineApi"]}}, "/api/examine/tpl/save": {"post": {"summary": "examine 质检打分表配置 - 保存 add&save", "operationId": "ExamineApi_ExamineTplSave", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbEmpty"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbExamineTplSaveReq"}}], "tags": ["ExamineApi"]}}, "/api/indices/cfg": {"post": {"summary": "顶部数据-配置内容列表接口", "operationId": "IndicesApi_Config", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbIndicesCfgResp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbProjectLang"}}], "tags": ["IndicesApi"]}}, "/api/indices/del": {"post": {"summary": "顶部数据-删除接口", "operationId": "IndicesApi_Delete", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbEmpty"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbIndicesDelReq"}}], "tags": ["IndicesApi"]}}, "/api/indices/enable": {"post": {"summary": "顶部数据-启用/禁用接口", "operationId": "IndicesApi_Enable", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbEmpty"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbIndicesEnableReq"}}], "tags": ["IndicesApi"]}}, "/api/indices/list": {"post": {"summary": "顶部数据-列表接口", "operationId": "IndicesApi_List", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbIndicesListResp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbProjectLang"}}], "tags": ["IndicesApi"]}}, "/api/indices/release": {"post": {"summary": "顶部数据-发布接口", "operationId": "IndicesApi_Release", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbEmpty"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbProjectLang"}}], "tags": ["IndicesApi"]}}, "/api/indices/save": {"post": {"summary": "顶部数据-新增/保存接口", "operationId": "IndicesApi_Save", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbEmpty"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbIndicesSaveReq"}}], "tags": ["IndicesApi"]}}, "/api/line/tab/count": {"post": {"operationId": "LineApi_LineTabCount", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbLineTabCountResp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbEmpty"}}], "tags": ["LineApi"]}}, "/api/line/tab/del": {"post": {"operationId": "LineApi_LineTabDel", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbEmpty"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbLineTabDelReq"}}], "tags": ["LineApi"]}}, "/api/line/tab/edit": {"post": {"operationId": "LineApi_LineTabEdit", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbEmpty"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbLineTabEditReq"}}], "tags": ["LineApi"]}}, "/api/line/tab/list": {"post": {"operationId": "LineApi_LineTabList", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbLineTabListResp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbEmpty"}}], "tags": ["LineApi"]}}, "/api/line/tab/save": {"post": {"operationId": "LineApi_LineTabSave", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbEmpty"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbLineTabAddReq"}}], "tags": ["LineApi"]}}, "/api/module/cat/add": {"post": {"summary": "模版分类 - 添加分类", "operationId": "ModuleCatApi_ModuleCatAdd", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbEmpty"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbModuleCatAddReq"}}], "tags": ["ModuleCatApi"]}}, "/api/module/cat/del": {"post": {"summary": "模版分类 - 删除分类", "operationId": "ModuleCatApi_ModuleCatDel", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbEmpty"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbCatIdReq"}}], "tags": ["ModuleCatApi"]}}, "/api/module/cat/save": {"post": {"summary": "模版分类 - 修改分类", "operationId": "ModuleCatApi_ModuleCatSave", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbEmpty"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbModuleCatSaveReq"}}], "tags": ["ModuleCatApi"]}}, "/api/module/cat/tree": {"post": {"summary": "模版分类 - 返回分类树", "operationId": "ModuleCatApi_ModuleCatTree", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbEmpty"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbModuleCatTreeReq"}}], "tags": ["ModuleCatApi"]}}, "/api/module/del": {"post": {"summary": "工单模版 - 删除模版", "operationId": "TicketModuleApi_TicketModuleDel", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbEmpty"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbModuleDelReq"}}], "tags": ["TicketModuleApi"]}}, "/api/module/edit": {"post": {"summary": "工单模版 - 修改模版", "operationId": "TicketModuleApi_TicketModuleEdit", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbEmpty"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbModuleEditReq"}}], "tags": ["TicketModuleApi"]}}, "/api/module/list": {"post": {"summary": "工单模版 - 返回列表", "operationId": "TicketModuleApi_TicketModuleList", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbModuleListResp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbModuleListReq"}}], "tags": ["TicketModuleApi"]}}, "/api/module/save": {"post": {"summary": "工单模版 - 添加模版", "operationId": "TicketModuleApi_TicketModuleSave", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbEmpty"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbModuleSaveReq"}}], "tags": ["TicketModuleApi"]}}, "/api/new/api/tag_lib/info": {"post": {"summary": "标签库配置 - 详情", "operationId": "AllTagLibApi_AllTagLibInfo", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbTagLibInfoResp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbTagLibID"}}], "tags": ["AllTagLibApi"]}}, "/api/new/commu/edit": {"post": {"operationId": "DscApi_DscNewCommuEdit", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbEmpty"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbDiscordNewCommuRecordEditReq"}}], "tags": ["DscApi"]}}, "/api/new/commu/list": {"post": {"operationId": "DscApi_DscNewCommuList", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbDiscordNewCommuRecordListResp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbDiscordNewCommuRecordListReq"}}], "tags": ["DscApi"]}}, "/api/new/commu/save": {"post": {"operationId": "DscApi_DscNewCommuSave", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbEmpty"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbDiscordNewCommuRecordAddReq"}}], "tags": ["DscApi"]}}, "/api/new/tag_lib/enable": {"post": {"summary": "标签库配置 - 修改禁用启用", "operationId": "AllTagLibApi_TagLibEnable", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbEmpty"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbEnableReq"}}], "tags": ["AllTagLibApi"]}}, "/api/new/tag_lib/list": {"post": {"summary": "标签库配置 - 列表接口", "operationId": "AllTagLibApi_AllTagLibList", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbTagLibListResp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbTagLibListReq"}}], "tags": ["AllTagLibApi"]}}, "/api/new/tag_lib/save": {"post": {"summary": "标签库配置 -  新增&修改", "operationId": "AllTagLibApi_TagLibSave", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbTagLibID"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbAllTagLibSaveReq"}}], "tags": ["AllTagLibApi"]}}, "/api/new/tags/opts": {"post": {"summary": "标签列表 - 筛选", "operationId": "AllTagLibApi_AllTagOpts", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbTagOptsResp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbTagOptsReq"}}], "tags": ["AllTagLibApi"]}}, "/api/overtime_tpl/add": {"post": {"summary": "超时模版 - 添加", "operationId": "OverTimeApi_OvertimeTplAdd", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbEmpty"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbOverTimeTplAddReq"}}], "tags": ["OverTimeApi"]}}, "/api/overtime_tpl/delete": {"post": {"summary": "超时模版 - 删除", "operationId": "OverTimeApi_OvertimeTplDel", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbEmpty"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbOverTimeTplDelReq"}}], "tags": ["OverTimeApi"]}}, "/api/overtime_tpl/edit": {"post": {"summary": "超时模版 - 编辑", "operationId": "OverTimeApi_OvertimeTplEdit", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbEmpty"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbOverTimeTplEditReq"}}], "tags": ["OverTimeApi"]}}, "/api/overtime_tpl/enable": {"post": {"summary": "超时模版 - 启用/禁用", "operationId": "OverTimeApi_OvertimeTplEnable", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbEmpty"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbOverTimeTplEnableReq"}}], "tags": ["OverTimeApi"]}}, "/api/overtime_tpl/list": {"post": {"summary": "超时模版 - 列表", "operationId": "OverTimeApi_OvertimeTplList", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbOverTimeTplListResp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbOverTimeTplListReq"}}], "tags": ["OverTimeApi"]}}, "/api/overtime_tpl/opts": {"post": {"summary": "超时模版 - 编辑", "operationId": "OverTimeApi_OvertimeTplOpts", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbOverTimeTplOptsRes"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbOverTimeTplOptsReq"}}], "tags": ["OverTimeApi"]}}, "/api/survey/satisfaction/date/stats": {"post": {"summary": "survey 报表", "operationId": "SurveyApi_SurveyStats", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbEmpty"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbDiscordPlayerSatisfactionStatsReq"}}], "tags": ["SurveyApi"]}}, "/api/survey/satisfaction/date/stats_export": {"post": {"summary": "survey 报表 - 导出", "operationId": "SurveyApi_SurveyStatsExport", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbEmpty"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbDiscordPlayerSatisfactionStatsReq"}}], "tags": ["SurveyApi"]}}, "/api/survey_config/edit": {"post": {"summary": "survey 调查问卷 - 修改", "operationId": "SurveyApi_SurveyConfigEdit", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbEmpty"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbSurveyEditReq"}}], "tags": ["SurveyApi"]}}, "/api/survey_config/enable": {"post": {"summary": "survey 调查问卷 - enable", "operationId": "SurveyApi_SurveyConfigEnable", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbEmpty"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbEnableReq"}}], "tags": ["SurveyApi"]}}, "/api/survey_config/gen_links": {"post": {"summary": "survey 调查问卷 - 生成链接", "operationId": "SurveyApi_SurveyGenLink", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbSurveyGenLinkResp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbSurveyGenLinkReq"}}], "tags": ["SurveyApi"]}}, "/api/survey_config/info": {"post": {"summary": "survey 调查问卷 - 详情", "operationId": "SurveyApi_SurveyConfigInfo", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbSurveyInfoResp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbSurveyInfoReq"}}], "tags": ["SurveyApi"]}}, "/api/survey_config/list": {"post": {"summary": "survey 调查问卷 - 列表", "operationId": "SurveyApi_SurveyConfigList", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbSurveyListResp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbSurveyListReq"}}], "tags": ["SurveyApi"]}}, "/api/tag_lib/enable": {"post": {"summary": "标签库配置 - 修改禁用启用", "operationId": "TagLibApi_TagLibEnable", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbEmpty"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbEnableReq"}}], "tags": ["TagLibApi"]}}, "/api/tag_lib/info": {"post": {"summary": "标签库配置 - 详情", "operationId": "TagLibApi_TagLibInfo", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbTagLibInfoResp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbTagLibID"}}], "tags": ["TagLibApi"]}}, "/api/tag_lib/list": {"post": {"summary": "标签库配置 - 列表接口", "operationId": "TagLibApi_TagLibList", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbTagLibListResp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbTagLibListReq"}}], "tags": ["TagLibApi"]}}, "/api/tag_lib/opts": {"post": {"summary": "标签库 - 筛选列表接口", "operationId": "TagLibApi_TagLibOpts", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbTagLibOptsResp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbTagOptsReq"}}], "tags": ["TagLibApi"]}}, "/api/tag_lib/save": {"post": {"summary": "标签库配置 -  新增&修改", "operationId": "TagLibApi_TagLibSave", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbTagLibID"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbTagLibSaveReq"}}], "tags": ["TagLibApi"]}}, "/api/tags/opts": {"post": {"summary": "标签列表 - 筛选", "operationId": "TagLibApi_TagOpts", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbTagOptsResp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbTagOptsReq"}}], "tags": ["TagLibApi"]}}, "/api/ticket/batch_remark": {"post": {"summary": "工单池 -- 添加备注", "operationId": "TicketApi_TicketBatchRemark", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbEmpty"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbTicketBatchRemarkReq"}}], "tags": ["TicketApi"]}}, "/api/ticket/count": {"post": {"summary": "工单池 - 工单池数据概览", "operationId": "TicketApi_TicketCount", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbTicketCountResp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbTicketCountReq"}}], "tags": ["TicketApi"]}}, "/api/ticket/export": {"post": {"summary": "工单池 - 工单导出接口", "operationId": "TicketApi_TicketWorkPoolExport", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbEmpty"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbTicketPoolNewListReq"}}], "tags": ["TicketApi"]}}, "/api/ticket/info": {"post": {"summary": "工单池 - 所有基础数据", "operationId": "TicketApi_TicketInfo", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbTicketResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbTicketIdReq"}}], "tags": ["TicketApi"]}}, "/api/ticket/is_acceptor": {"post": {"summary": "客服是否在线", "operationId": "TicketApi_TicketIsAcceptor", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbTicketIsUserResp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbGroupUserReq"}}], "tags": ["TicketApi"]}}, "/api/ticket/pool": {"post": {"summary": "工单池 - 搜索接口", "operationId": "TicketApi_TicketWorkPool", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbTicketPoolNewListResp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbTicketPoolNewListReq"}}], "tags": ["TicketApi"]}}, "/api/ticket/reassign": {"post": {"summary": "工单池 - 指派接口", "operationId": "TicketApi_Reassign", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbEmpty"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbAssignmentReq"}}], "tags": ["TicketApi"]}}, "/api/ticket/remark": {"post": {"summary": "工单池 -- 添加备注", "operationId": "TicketApi_TicketAddRemark", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbEmpty"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbTicketRemarkReq"}}], "tags": ["TicketApi"]}}, "/api/ticket/retagging": {"post": {"summary": "工单池 -- 添加标签", "operationId": "TicketApi_TicketReTagging", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbEmpty"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbTicketRetaggingReq"}}], "tags": ["TicketApi"]}}, "/api/ticket/tab/edit": {"post": {"summary": "工单tab编辑", "operationId": "TicketTabApi_TicketTabEdit", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbEmpty"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbTicketTabEditReq"}}], "tags": ["TicketTabApi"]}}, "/api/ticket/tag/public": {"post": {"summary": "工单池 -- 公共标签", "operationId": "TicketApi_TicketPublicTag", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbTicketPublicTagResp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbTicketPublicTagReq"}}], "tags": ["TicketApi"]}}, "/api/ticket/tags": {"post": {"summary": "工单池 -- 工单已绑定标签", "operationId": "TicketApi_TicketReTags", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbTicketTagRes"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbTicketIdReq"}}], "tags": ["TicketApi"]}}, "/api/ticket/transfer": {"post": {"summary": "工单 - 工单状态变更：工单回复|工单关单|工单拒单", "operationId": "TicketApi_TicketReply", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbEmpty"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbTicketTransferReq"}}], "tags": ["TicketApi"]}}, "/api/ticket/turn": {"post": {"summary": "工单 - 工单流转", "operationId": "TicketApi_TicketTurn", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbEmpty"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbAssignmentReq"}}], "tags": ["TicketApi"]}}, "/api/ticket/upgrade": {"post": {"summary": "工单池 - 工单升级", "operationId": "TicketApi_TicketUpgrade", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbEmpty"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbTicketUpgradeReq"}}], "tags": ["TicketApi"]}}, "/api/user_assign_ticket/add": {"post": {"summary": "分单配置 - 添加", "operationId": "UserAssignTicketApi_UserAssignTicketAdd", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbEmpty"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbUserAssignTicketAddReq"}}], "tags": ["UserAssignTicketApi"]}}, "/api/user_assign_ticket/del": {"post": {"summary": "分单配置 - 分单逻辑", "operationId": "UserAssignTicketApi_AllocGroupEnable", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbEmpty"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbUserAssignTicketDelReq"}}], "tags": ["UserAssignTicketApi"]}}, "/api/user_assign_ticket/edit": {"post": {"summary": "分单配置 - 修改", "operationId": "UserAssignTicketApi_GroupInfo", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbEmpty"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbUserAssignTicketEditReq"}}], "tags": ["UserAssignTicketApi"]}}, "/api/user_assign_ticket/info": {"post": {"summary": "分单配置 - 详情", "operationId": "UserAssignTicketApi_GroupOpts", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbUserAssignTicketListRespDetail"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbUserAssignTicketInfoReq"}}], "tags": ["UserAssignTicketApi"]}}, "/api/user_assign_ticket/list": {"post": {"summary": "分单配置 - 列表", "operationId": "UserAssignTicketApi_GroupAdd", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbUserAssignTicketListResp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbUserAssignTicketListReq"}}], "tags": ["UserAssignTicketApi"]}}, "/api/v2/notice": {"post": {"summary": "工单 - sdk - 红点v2", "operationId": "EgressAPI_TkSdkNoticeV2", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbNoticeResp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbNoticeRequestV2"}}], "tags": ["EgressAPI"]}}, "/api/v2/question/batch_import": {"post": {"summary": "工单知识库批量导入", "operationId": "QuestionApi_QuestionBatchImport", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbEmpty"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbQuestionBatchImportReq"}}], "tags": ["QuestionApi"]}}, "/api/v2/question/del": {"post": {"summary": "工单知识库删除", "operationId": "QuestionApi_QuestionDel", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbEmpty"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbQuestionDelReq"}}], "tags": ["QuestionApi"]}}, "/api/v2/question/export": {"post": {"summary": "工单知识库下载", "operationId": "QuestionApi_QuestionExport", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbEmpty"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbQuestionListReq"}}], "tags": ["QuestionApi"]}}, "/api/v2/question/list": {"post": {"summary": "工单知识库列表", "operationId": "QuestionApi_QuestionList", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbQuestionListResp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbQuestionListReq"}}], "tags": ["QuestionApi"]}}, "/api/v2/question/save": {"post": {"summary": "工单知识库保存", "operationId": "QuestionApi_QuestionSave", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbEmpty"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbQuestionSaveReq"}}], "tags": ["QuestionApi"]}}, "/api/v2/question/training": {"post": {"summary": "工单知识库训练", "operationId": "QuestionApi_QuestionTraining", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbEmpty"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbQuestionTrainingReq"}}], "tags": ["QuestionApi"]}}, "/api/v2/question/trainlog": {"post": {"summary": "工单知识库训练结果页面", "operationId": "QuestionApi_QuestionTrainLog", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbQuestionTrainLogResp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbQuestionTrainLogReq"}}], "tags": ["QuestionApi"]}}, "/api/v2/strategy/del": {"post": {"summary": "工单策略删除", "operationId": "TicketStrategyApi_TicketStrategyDel", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbEmpty"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbStrategyDelReq"}}], "tags": ["TicketStrategyApi"]}}, "/api/v2/strategy/enable": {"post": {"summary": "工单策略禁/启用", "operationId": "TicketStrategyApi_TicketStrategyEnable", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbEmpty"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbStrategyEnabelReq"}}], "tags": ["TicketStrategyApi"]}}, "/api/v2/strategy/list": {"post": {"summary": "工单策略列表", "operationId": "TicketStrategyApi_TicketStrategyList", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbStrategyListResp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbStrategyListReq"}}], "tags": ["TicketStrategyApi"]}}, "/api/v2/strategy/save": {"post": {"summary": "工单策略保存", "operationId": "TicketStrategyApi_TicketStrategySave", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbEmpty"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbStrategyAddReq"}}], "tags": ["TicketStrategyApi"]}}, "/egress/cat/info": {"post": {"summary": "三级分类信息", "operationId": "EgressAPI_CategoryInfo", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbEgressCatInfoResp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbCatSubRequest"}}], "tags": ["EgressAPI"]}}, "/egress/conversation/chat": {"post": {"summary": "对话交互", "operationId": "ConversationService_HandleChat", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbChatResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbChatRequest"}}], "tags": ["ConversationService"]}}, "/egress/conversation/close": {"post": {"summary": "egress/conversation/close", "operationId": "ConversationService_CloseChat", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbEmpty"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbCloseChatReq"}}], "tags": ["ConversationService"]}}, "/egress/conversation/continue": {"post": {"summary": "egress/conversation/continue", "operationId": "ConversationService_ContinueChat", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbContinueChatResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbEmpty"}}], "tags": ["ConversationService"]}}, "/egress/conversation/history": {"post": {"summary": "获取对话历史", "operationId": "ConversationService_GetConversationHistory", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbGetHistoryResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "game_id", "description": "项目标识 @gotags: validate:\"required_without=FpxAppId\"", "in": "query", "required": false, "type": "integer", "format": "int64"}, {"name": "fpx_app_id", "description": "项目标识 @gotags: validate:\"required_without=GameId\"", "in": "query", "required": false, "type": "string"}, {"name": "lang", "description": "语言标识 @gotags: validate:\"required\"", "in": "query", "required": false, "type": "string"}, {"name": "conversation_id", "in": "query", "required": false, "type": "string"}], "tags": ["ConversationService"]}}, "/egress/conversation/update": {"post": {"summary": "更新对话", "operationId": "ConversationService_UpdateConversation", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbUpdateConversationResponse"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbUpdateConversationRequest"}}], "tags": ["ConversationService"]}}, "/egress/ticket/appraise": {"post": {"summary": "工单 - 工单评价接口", "operationId": "EgressAPI_TkAppraise", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbEmpty"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbTkAppraiseReq"}}], "tags": ["EgressAPI"]}}, "/egress/ticket/appraise/feedback": {"post": {"summary": "工单 - 工单评价反馈接口", "operationId": "EgressAPI_TkAppraiseFeedback", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbEmpty"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbTkAppraiseFeedbackReq"}}], "tags": ["EgressAPI"]}}, "/egress/ticket/communicate": {"post": {"summary": "工单 - 给客服发消息", "operationId": "EgressAPI_TkCommunicate", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbEmpty"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbTkCommunicateReq"}}], "tags": ["EgressAPI"]}}, "/egress/ticket/create": {"post": {"summary": "工单 - 创建工单接口", "operationId": "EgressAPI_TkCreate", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbTkCreateResp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbTkCreateReq"}}], "tags": ["EgressAPI"]}}, "/egress/ticket/detail": {"post": {"summary": "工单 - 工单详情接口", "operationId": "EgressAPI_TkDetail", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbTkDetailResp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbTkDetailReq"}}], "tags": ["EgressAPI"]}}, "/egress/ticket/mine": {"post": {"summary": "工单 - 创建工单接口", "operationId": "EgressAPI_TkMine", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbTkMineResp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbTkMineReq"}}], "tags": ["EgressAPI"]}}, "/egress/ticket/reopen": {"post": {"summary": "工单 - 工单重开接口", "operationId": "EgressAPI_TkReopen", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbEmpty"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbTkReplenishReq"}}], "tags": ["EgressAPI"]}}, "/egress/ticket/replenish": {"post": {"summary": "工单 - 工单补填接口", "operationId": "EgressAPI_TkReplenish", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbEmpty"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbTkReplenishReq"}}], "tags": ["EgressAPI"]}}, "/egress_survey/dc/submit": {"post": {"summary": "C端接口-转发 - 问卷提交", "operationId": "SurveyApi_SurveySubmit", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbEmpty"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbSurveySubmitReq"}}], "tags": ["SurveyApi"]}}, "/egress_survey/dc/template": {"post": {"summary": "C端接口-转发 - 获取模板详情", "operationId": "SurveyApi_SurveyTemplate", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbSurveyTemplateResp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbSurveyTemplateReq"}}], "tags": ["SurveyApi"]}}, "/inner/api/cat/titles": {"post": {"summary": "分类获取title", "operationId": "CategoryInnerAPI_GetCatTitles", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbEmpty"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbGetCatTitlesReq"}}], "tags": ["CategoryInnerAPI"]}}, "/team_config/list": {"post": {"summary": "团队配置list", "operationId": "TeamConfigApi_TeamConfigList", "responses": {"200": {"description": "A successful response.", "schema": {"$ref": "#/definitions/pbTeamConfigListResp"}}, "default": {"description": "An unexpected error response.", "schema": {"$ref": "#/definitions/googlerpcStatus"}}}, "parameters": [{"name": "body", "in": "body", "required": true, "schema": {"$ref": "#/definitions/pbTeamConfigListReq"}}], "tags": ["TeamConfigApi"]}}}, "definitions": {"DataPlatGameItemListRespItemDetail": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "id"}, "item_id": {"type": "string", "title": "item_id"}, "item_name": {"type": "string", "title": "道具名称"}, "updated_at": {"type": "string", "title": "更新时间"}, "operator": {"type": "string", "title": "变更人"}}}, "DataPlatGameItemOptsRespItem": {"type": "object", "properties": {"value": {"type": "string", "title": "道具 id"}, "label": {"type": "string", "title": "道具名称"}}}, "DiscordTabCountRespDiscordTabCount": {"type": "object", "properties": {"tab": {"type": "array", "items": {"$ref": "#/definitions/pbDiscordTabCountRespTabCountDetail"}}, "project": {"type": "string"}}}, "DscMsgEmbedProvider": {"type": "object", "properties": {"url": {"type": "string", "title": "来源地址"}, "name": {"type": "string", "title": "来源名称"}}}, "DscMsgEmbedThumbnail": {"type": "object", "properties": {"url": {"type": "string", "title": "url"}, "proxy_url": {"type": "string", "title": "proxy url"}, "width": {"type": "integer", "format": "int32", "title": "width"}, "height": {"type": "integer", "format": "int32", "title": "height"}}}, "DscMsgEmbedVideo": {"type": "object", "properties": {"url": {"type": "string"}, "width": {"type": "integer", "format": "int32"}, "height": {"type": "integer", "format": "int32"}}}, "DscUserDetailRespUserDetail": {"type": "object", "properties": {"project": {"type": "string", "title": "游戏"}, "dsc_user_id": {"type": "string", "title": "玩家 DC ID"}, "user_name": {"type": "string", "title": "玩家名称 - 前端展示使用此字段"}, "global_name": {"type": "string", "title": "玩家 global name"}, "dm_channel": {"type": "string", "title": "dm channel"}, "guild_id": {"type": "string", "title": "guild id"}, "processor": {"type": "string", "title": "当前维护人"}}}, "DscUserListRespDscUser": {"type": "object", "properties": {"project": {"type": "string", "title": "游戏"}, "dsc_user_id": {"type": "string", "title": "玩家 DC ID"}, "user_name": {"type": "string", "title": "玩家名称 - 前端展示使用此字段"}, "global_name": {"type": "string", "title": "玩家 global name"}, "dm_channel": {"type": "string", "title": "dm channel"}, "guild_id": {"type": "string", "title": "guild id"}, "processor": {"type": "string", "title": "当前维护人"}, "total_pay": {"type": "number", "format": "double", "title": "累付金额"}, "pay_last_thirty_days": {"type": "number", "format": "double", "title": "最近30天付费金额"}, "last_login": {"type": "string", "title": "最近登录时间"}, "status": {"type": "integer", "format": "int64", "title": "玩家信息回复状态"}, "vip_level": {"type": "integer", "format": "int64", "title": "VIP 等级"}, "fpid": {"type": "string"}, "uid": {"type": "string", "format": "uint64"}, "sid": {"type": "string"}, "bot_id": {"type": "string", "title": "机器人 user_id"}, "bot_show": {"type": "string", "title": "机器人昵称"}, "note": {"type": "string"}, "player_nick": {"type": "string"}, "birthday": {"type": "string"}, "lang": {"type": "string"}, "checked": {"type": "boolean"}, "last_reply_time": {"type": "string", "title": "客服最后回复时间"}, "waiting_time": {"type": "string", "title": "玩家等待时长"}, "ticket_create_count": {"type": "string", "format": "int64", "title": "七天内工单创建个数"}, "last_ticket_create_time": {"type": "string", "title": "七天内最后工单创建时间"}, "ticket_id": {"type": "string", "format": "uint64", "title": "七天内创建最近工单ID"}}}, "ExamineDscOrderListRespExamineDscOrder": {"type": "object", "properties": {"dsc_examine_id": {"type": "string", "format": "uint64", "title": "质检单 ID"}, "task_id": {"type": "string", "format": "uint64", "title": "任务id"}, "tpl_id": {"type": "string", "format": "uint64", "title": "模板id"}, "project": {"type": "string", "title": "游戏"}, "dsc_user_id": {"type": "string", "title": "玩家 DC ID"}, "user_name": {"type": "string", "title": "玩家名称 - 前端展示使用此字段"}, "dm_channel": {"type": "string", "title": "dm channel"}, "total_pay": {"type": "number", "format": "double", "title": "累付金额"}, "pay_last_thirty_days": {"type": "number", "format": "double", "title": "最近30天付费金额"}, "last_login": {"type": "string", "title": "最近登录时间"}, "replied_status": {"type": "integer", "format": "int64", "title": "玩家信息回复状态"}, "vip_state": {"type": "integer", "format": "int64", "title": "玩家VIP状态"}, "fpid": {"type": "string"}, "uid": {"type": "string", "format": "uint64"}, "sid": {"type": "string"}, "bot_id": {"type": "string", "title": "机器人 user_id"}, "bot_show": {"type": "string", "title": "机器人昵称"}, "status": {"$ref": "#/definitions/pbExamineStateDf", "title": "质检状态"}, "final_result": {"$ref": "#/definitions/pbExamineFinalResultDf", "title": "质检结果"}, "created_at": {"type": "string", "title": "创建时间：质检任务的创建时间"}, "finished_at": {"type": "string", "title": "结案时间：质检任务的完成时间"}, "inspector": {"type": "string", "title": "质检员"}}}, "ExamineOrderNoticeListRespExamineNoticeDetail": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "id"}, "msg_group": {"type": "string", "title": "消息类型"}, "task_group": {"$ref": "#/definitions/pbExamineTaskGroupDf", "title": "抽检库分组"}, "detail_id": {"type": "string", "format": "uint64", "title": "抽检单id"}, "to_user": {"type": "string", "title": "通知人"}, "created_at": {"type": "string", "title": "创建时间"}, "has_read": {"type": "boolean", "title": "当前状态-是否已读: true/false"}}}, "ExamineTaskListRespExamineTask": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "id"}, "task_name": {"type": "string", "title": "任务名称"}, "status": {"$ref": "#/definitions/pbExamineTaskStateDf", "title": "任务状态"}, "operator": {"type": "string", "title": "操作人"}, "updated_at": {"type": "string", "title": "操作时间"}, "link": {"type": "string", "title": "快照详情"}}}, "ExamineTplListRespExamineTpl": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "id"}, "tpl_desc": {"type": "string", "title": "打分表名字"}, "operator": {"type": "string", "title": "操作人"}, "updated_at": {"type": "string", "title": "操作时间"}, "enable": {"type": "boolean", "title": "当前状态: true/false"}}}, "ExamineTplOptsRespExamineTplOpts": {"type": "object", "properties": {"tpl_id": {"type": "string", "format": "uint64", "title": "打分表 id"}, "tpl_dsc": {"type": "string", "title": "打分表名字"}}}, "IndicesListRespitem": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}, "cat_type": {"type": "string"}, "name": {"type": "string"}, "cat_ids": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "updated_at": {"type": "string"}, "updated_user": {"type": "string"}, "project": {"type": "string"}, "enable": {"type": "boolean", "title": "启用true 禁用false"}}}, "LineTabCountRespLineTabCount": {"type": "object", "properties": {"tab": {"type": "array", "items": {"$ref": "#/definitions/pbLineTabCountRespTabCountDetail"}}, "project": {"type": "string"}}}, "LineTabListRespLineTabDetail": {"type": "object", "properties": {"tab": {"type": "array", "items": {"$ref": "#/definitions/pbLineTabListRespTabInfo"}}, "project": {"type": "string"}}}, "ModuleListRespModuleInfo": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "模板id"}, "module_name": {"type": "string", "title": "模板名称"}, "content": {"type": "string", "title": "模板内容"}, "game_project": {"type": "string", "title": "关联游戏"}, "operator": {"type": "string", "title": "操作人"}, "enable": {"type": "integer", "format": "int64", "title": "状态 1启用 2禁用"}, "update_time": {"type": "string", "title": "操作时间"}, "category": {"type": "string", "title": "模板分类名称"}, "cat_id": {"type": "integer", "format": "int64", "title": "模版分类id"}}}, "PortraitInfoRespNewLabelDetail": {"type": "object", "properties": {"tag_id": {"type": "string", "format": "uint64"}, "tag_desc": {"type": "string"}}}, "QuestionListRespQuestionRecord": {"type": "object", "properties": {"question_id": {"type": "string", "format": "int64", "title": "语料id"}, "question_content": {"type": "string", "title": "语料内容"}, "lang": {"type": "string", "title": "语种"}, "project": {"type": "string", "title": "游戏"}, "category": {"type": "string", "title": "问题分类"}, "cat_id": {"type": "integer", "format": "int64", "title": "问题分类id,便于回显"}, "update_date": {"type": "string", "title": "更新时间"}, "operator": {"type": "string", "title": "更新人"}, "answer_rich_text": {"type": "string", "title": "富文本答案"}}}, "QuestionTrainLogRespTrainRecord": {"type": "object", "properties": {"created_at": {"type": "string", "title": "训练时间"}, "operator": {"type": "string", "title": "操作人"}, "status": {"type": "integer", "format": "int64", "title": "训练状态"}, "project": {"type": "string", "title": "游戏"}, "lang": {"type": "string", "title": "语种"}}}, "StrategyListRespStrategyRecord": {"type": "object", "properties": {"strategy_id": {"type": "string", "format": "int64", "title": "策略id"}, "strategy_name": {"type": "string", "title": "策略名称"}, "project": {"type": "string", "title": "游戏"}, "enable": {"type": "integer", "format": "int64", "title": "禁/启用"}, "filter_server_lists": {"type": "string", "title": "生效服务器"}, "filter_pay_range_lists": {"type": "string", "title": "充值区间"}, "filter_castle_level_lists": {"type": "string", "title": "城堡等级"}, "type": {"type": "integer", "format": "int64", "title": "服务器是否全部"}}}, "SurveyListRespSurveyInfo": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}, "project": {"type": "string"}, "updated_at": {"type": "string"}, "op": {"type": "string"}, "enable": {"type": "boolean"}, "can_gen_link": {"type": "boolean"}}}, "TagOptsRespTag": {"type": "object", "properties": {"tag_id": {"type": "integer", "format": "int64", "title": "标签ID"}, "tag_name": {"type": "string", "title": "标签名称"}, "level": {"type": "integer", "format": "int64", "title": "级别"}, "enable": {"type": "boolean", "title": "启用"}, "disable": {"type": "boolean"}, "children": {"type": "array", "items": {"$ref": "#/definitions/TagOptsRespTag"}, "title": "子结构 @gotags: json:\"children,omitempty\""}}}, "TicketPoolNewListRespTicketPoolInfo": {"type": "object", "properties": {"project": {"type": "string", "title": "游戏"}, "ticket_id": {"type": "string", "format": "uint64", "title": "工单ID"}, "detail": {"type": "string", "title": "玩家输入第一句话"}, "recharge": {"type": "number", "format": "double", "title": "累计金额"}, "status": {"type": "integer", "format": "int64", "title": "工单状态"}, "waiting_time": {"type": "string", "title": "等待时长"}, "csi": {"type": "integer", "format": "int64", "title": "服务评分"}, "acceptor": {"type": "string", "title": "当前处理人"}, "crm_vip_user_flag": {"type": "boolean", "title": "ss vip绿色通道单"}, "checked": {"type": "boolean"}, "dc_flag": {"type": "boolean", "title": "是否7日内在dc端发起过对话"}, "dc_create_time": {"type": "string", "title": "DC对话创建时间"}, "account_id": {"type": "string", "title": "用户AccountID"}, "system_label": {"type": "array", "items": {"type": "integer", "format": "int64"}, "title": "系统标签"}}}, "TicketRecordRespRecord": {"type": "object", "properties": {"project": {"type": "string", "title": "游戏"}, "ticket_id": {"type": "string", "format": "uint64", "title": "工单ID"}, "detail": {"type": "string", "title": "玩家输入第一句话"}, "recharge": {"type": "number", "format": "double", "title": "累计金额"}, "status": {"type": "integer", "format": "int64", "title": "工单状态"}, "waiting_time": {"type": "string", "title": "等待时长"}}}, "TkDetailRespCommu": {"type": "object", "properties": {"content": {"type": "string", "title": "消息内容"}, "role": {"$ref": "#/definitions/pbUserRole", "title": "消息来源: 1:玩家端； 2客服回复"}, "custom_nick_name": {"type": "string", "title": "客服回复人员名称"}, "created_at": {"type": "string", "title": "消息时间"}, "picture": {"type": "string"}, "video": {"type": "string"}, "op_type": {"type": "integer", "format": "int64", "title": "操作类型"}}, "title": "对话记录"}, "TkDetailRespReopen": {"type": "object", "properties": {"reopen_id": {"type": "string", "format": "uint64", "title": "重开id"}, "fill_content": {"type": "string", "title": "重开提交内容"}, "files": {"type": "string", "title": "重开附件"}, "replenish": {"type": "array", "items": {"$ref": "#/definitions/TkDetailRespReplenish"}, "title": "补填+回复信息"}, "created_at": {"type": "string", "title": "创建时间"}, "reply_id": {"type": "string", "format": "uint64", "title": "回复id - 执行重开 - commu"}, "response_reply_id": {"type": "string", "format": "uint64", "title": "重开单 - 客服回复&关单 的id - commu"}}}, "TkDetailRespReplenish": {"type": "object", "properties": {"replenish_id": {"type": "string", "format": "uint64"}, "ticket_id": {"type": "string", "format": "uint64", "title": "工单ID"}, "remark": {"type": "string", "title": "备注信息"}, "fill_content": {"type": "string", "title": "补填内容"}, "files": {"type": "string", "title": "文件"}, "op": {"type": "integer", "format": "int64", "title": "操作 9:回复信息 7:打回补填 8:关闭工单 6:处理完成"}, "created_at": {"type": "string", "title": "创建时间"}, "created_ts": {"type": "string", "format": "uint64", "title": "创建时间ts"}}}, "TkMineRespTkList": {"type": "object", "properties": {"ticket_id": {"type": "string", "format": "uint64", "title": "工单ID"}, "created_at": {"type": "string", "title": "创建时间"}, "category": {"type": "string", "title": "类别"}, "progress": {"$ref": "#/definitions/pbTkProgress", "title": "当前状态 1：已完成 2：待补充 3：处理中 4：补充超时 5：重新打开"}, "status": {"$ref": "#/definitions/pbTkStatus", "title": "当前数据状态： 前端忽略"}, "read": {"type": "integer", "format": "int64", "title": "消息已读状态 1:已读 0:未读"}, "ticket_sys_type": {"$ref": "#/definitions/pbTicketSys", "title": "使用哪个工单系统"}}}, "googlerpcStatus": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "message": {"type": "string"}, "details": {"type": "array", "items": {"$ref": "#/definitions/protobufAny"}}}}, "pbAllTagLibSaveReq": {"type": "object", "properties": {"lib_id": {"type": "integer", "format": "int64", "title": "标签库ID"}, "lib_name": {"type": "string", "title": "标签库名称 @gotags: validate:\"required\""}, "lib_file_url": {"type": "string", "title": "标签数据-原始文件地址 @gotags: validate:\"required\""}, "projects": {"type": "array", "items": {"type": "string"}, "title": "关联游戏 @gotags: validate:\"required,gt=0\""}, "lib_type": {"type": "integer", "format": "int64", "title": "标签类型 @gotags: validate:\"required,oneof=1 2 3\""}}, "title": "TagLibSaveReq 更新标签库请求参数"}, "pbAssignmentReq": {"type": "object", "properties": {"ticket_id": {"type": "string", "format": "uint64", "title": "工单ID @gotags: validate:\"required\""}, "acceptor": {"type": "string", "title": "其他客服 @gotags: validate:\"required\""}}, "title": "AssignmentReq 指派请求参数"}, "pbCatIdReq": {"type": "object", "properties": {"cat_id": {"type": "integer", "format": "int64", "title": "分类ID @gotags: validate:\"required\""}}, "title": "CatIdReq 类别Id 请求参数"}, "pbCatProjectLang": {"type": "object", "properties": {"project": {"type": "string", "title": "项目 @gotags: validate:\"required\""}, "lang": {"type": "string", "title": "语言"}, "scope": {"$ref": "#/definitions/pbCatScopeEnum", "title": "范围 0:默认 1:内部分类"}}}, "pbCatProjectReq": {"type": "object", "properties": {"project": {"type": "string", "title": "项目 @gotags: validate:\"required\""}, "cat_type": {"type": "integer", "format": "int64", "title": "类别:line or discord @gotags: validate:\"required,oneof=1 2\""}}}, "pbCatScopeEnum": {"type": "integer", "format": "int32", "enum": ["0", "1", "-1"], "default": "0", "title": "工单分类scope"}, "pbCatSubRequest": {"type": "object", "properties": {"game_id": {"type": "integer", "format": "int64", "title": "项目标识 @gotags: validate:\"required_without=FpxAppId\""}, "fpx_app_id": {"type": "string", "title": "项目标识 @gotags: validate:\"required_without=GameId\""}, "lang": {"type": "string", "title": "语言标识 @gotags: validate:\"required\""}, "cat_id": {"type": "integer", "format": "int64", "title": "类别ID @gotags: validate:\"required\""}, "country_code": {"type": "string", "title": "国家code"}, "channel": {"type": "string", "title": "渠道"}, "sid": {"type": "string", "title": "区服"}, "json_data": {"type": "string", "title": "json_data"}}}, "pbChannelCatAddReq": {"type": "object", "properties": {"project": {"type": "string", "title": "项目 @gotags: validate:\"required\""}, "cat_level": {"type": "integer", "format": "int64", "title": "分类的等级 @gotags: validate:\"required,oneof=1 2 3\""}, "category": {"type": "string", "title": "分类名称 @gotags: validate:\"required\""}, "one_level": {"type": "integer", "format": "int64", "title": "一级分类 @gotags: validate:\"required_if=CatLevel 2\""}, "second_level": {"type": "integer", "format": "int64", "title": "二级分类 @gotags: validate:\"required_if=CatLevel 3\""}, "cat_type": {"type": "integer", "format": "int64", "title": "类别:line or discord @gotags: validate:\"required,oneof=1 2\""}}, "title": "CatProbAddReq 添加分类 请求参数"}, "pbChannelCatIdReq": {"type": "object", "properties": {"cat_id": {"type": "integer", "format": "int64", "title": "分类ID @gotags: validate:\"required\""}}, "title": "CatIdReq 类别Id 请求参数"}, "pbChannelCatSaveReq": {"type": "object", "properties": {"cat_id": {"type": "integer", "format": "int64", "title": "分类ID @gotags: validate:\"required\""}, "category": {"type": "string", "title": "分类名称 @gotags: validate:\"required\""}, "cat_level": {"type": "integer", "format": "int64", "title": "分类的等级 @gotags: validate:\"required,oneof=1 2 3\""}}, "title": "ChannelCatSaveReq 修改分类 请求参数"}, "pbChannelCatTreeResp": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/definitions/pbChannelCatTreeRespCat"}}}, "title": "CatOptsResp 问题分类配置(下拉级联多选)列表"}, "pbChannelCatTreeRespCat": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "label": {"type": "string"}, "level": {"type": "integer", "format": "int64"}, "children": {"type": "array", "items": {"$ref": "#/definitions/pbChannelCatTreeRespCat"}, "title": "子结构 @gotags: json:\"children,omitempty\""}}}, "pbChatOrFormType": {"type": "integer", "format": "int32", "enum": ["0", "1", "2"], "default": "0"}, "pbChatRequest": {"type": "object", "properties": {"conversation_id": {"type": "string"}, "now_question_key": {"type": "string"}, "now_question_content": {"type": "string"}, "now_answer_content": {"type": "array", "items": {"$ref": "#/definitions/pbContent"}}, "question_list": {"type": "array", "items": {"$ref": "#/definitions/pbQuestionContent"}}}, "title": "对话请求"}, "pbChatResponse": {"type": "object", "properties": {"history_answer_list": {"type": "array", "items": {"$ref": "#/definitions/pbQuestionGet"}}, "now_question_key": {"type": "string"}, "now_answer_content": {"type": "string"}, "hit_question": {"type": "boolean"}, "completed": {"type": "boolean"}}, "title": "对话响应"}, "pbCloseChatReq": {"type": "object", "properties": {"conversation_id": {"type": "string"}}}, "pbContent": {"type": "object", "properties": {"text": {"type": "string"}}, "title": "内容"}, "pbContinueChatResponse": {"type": "object", "properties": {"has_active_conversation": {"type": "boolean"}, "cat_id": {"type": "integer", "format": "int32"}, "conversation_id": {"type": "string"}}}, "pbCreatorType": {"type": "integer", "format": "int32", "enum": ["0", "1", "2", "3"], "default": "0", "title": "CreatorType"}, "pbDCSBotConfigAddReq": {"type": "object", "properties": {"project": {"type": "string", "title": "项目名称 @gotags: validate:\"required\""}, "dsc_name": {"type": "string", "title": "DC账号"}, "bot_desc": {"type": "string", "title": "机器人描述"}, "app_id": {"type": "string", "title": "Application ID"}, "bot_config": {"$ref": "#/definitions/pbDscBotConfig", "title": "机器人配置信息 @gotags: validate:\"required\""}, "user_id": {"type": "string", "title": "用户ID"}, "username": {"type": "string", "title": "用户名称"}, "discriminator": {"type": "string", "title": "标识"}}, "title": "DCSBotConfigAddReq 新增DC机器人配置"}, "pbDCSBotConfigListReq": {"type": "object", "properties": {"page": {"type": "integer", "format": "int64"}, "page_size": {"type": "integer", "format": "int64"}}, "title": "DCSBotConfigListReq DC机器人配置列表请求"}, "pbDCSBotConfigListResp": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/definitions/pbDscBotDetail"}}, "total": {"type": "integer", "format": "int64"}, "current_page": {"type": "integer", "format": "int64"}, "per_page": {"type": "integer", "format": "int64"}}, "title": "DCSBotConfigListResp DC机器人配置列表响应"}, "pbDataPlatGameItemBatchSaveReq": {"type": "object", "properties": {"project": {"type": "string", "title": "游戏标识 @gotags: validate:\"required\""}, "file_name": {"type": "string", "title": "导入文件地址 @gotags: validate:\"required\""}}, "title": "精分数据 - 游戏item - 道具 ID 保存"}, "pbDataPlatGameItemListReq": {"type": "object", "properties": {"project": {"type": "string", "title": "游戏标识"}, "page": {"type": "integer", "format": "int64", "title": "页码"}, "page_size": {"type": "integer", "format": "int64", "title": "页大小"}}, "title": "精分数据 - 游戏item - 道具 ID list"}, "pbDataPlatGameItemListResp": {"type": "object", "properties": {"current_page": {"type": "integer", "format": "int64"}, "per_page": {"type": "integer", "format": "int64"}, "total": {"type": "integer", "format": "int64"}, "data": {"type": "array", "items": {"$ref": "#/definitions/DataPlatGameItemListRespItemDetail"}}}, "title": "精分数据 - 游戏item - 道具 ID list resp"}, "pbDataPlatGameItemOptsReq": {"type": "object", "properties": {"project": {"type": "string", "title": "游戏标识 @gotags: validate:\"required\""}}}, "pbDataPlatGameItemOptsResp": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/definitions/DataPlatGameItemOptsRespItem"}}}}, "pbDataPlatUserGoldInfoReq": {"type": "object", "properties": {"project": {"type": "string", "title": "游戏标识"}, "uid": {"type": "string", "format": "uint64", "title": "uid"}, "created_at": {"type": "array", "items": {"type": "string"}, "title": "查询时间段 1天数据"}}, "title": "精分数据 - 金币查询 req"}, "pbDataPlatUserGoldInfoResp": {"type": "object", "properties": {"project": {"type": "string", "title": "游戏标识"}, "change": {"type": "string", "title": "变化量"}, "before": {"type": "string", "title": "变化前"}, "after": {"type": "string", "title": "变化后"}, "change_time": {"type": "string", "title": "时间"}, "reason": {"type": "string", "title": "原因"}}, "title": "精分数据 - 金币查询 resp"}, "pbDataPlatUserItemInfoReq": {"type": "object", "properties": {"project": {"type": "string", "title": "游戏标识"}, "uid": {"type": "string", "format": "uint64", "title": "uid"}, "item_id": {"type": "string", "title": "道具 id"}, "created_at": {"type": "array", "items": {"type": "string"}, "title": "查询时间段"}}, "title": "精分数据 - 物品(道具)查询 req"}, "pbDataPlatUserItemInfoResp": {"type": "object", "properties": {"project": {"type": "string", "title": "游戏标识"}, "item_id": {"type": "string", "title": "物品 id"}, "item_name": {"type": "string", "title": "物品名称"}, "change": {"type": "string", "title": "变化量"}, "before": {"type": "string", "title": "变化前"}, "after": {"type": "string", "title": "变化后"}, "change_time": {"type": "string", "title": "时间"}, "reason": {"type": "string", "title": "原因"}}, "title": "精分数据 - 物品(道具)查询 resp"}, "pbDataPlatUserLoginInfoReq": {"type": "object", "properties": {"project": {"type": "string", "title": "游戏标识"}, "uid": {"type": "string", "format": "uint64", "title": "uid"}, "created_at": {"type": "array", "items": {"type": "string"}, "title": "查询时间段"}}, "title": "精分数据 - 登录查询 req"}, "pbDataPlatUserLoginInfoResp": {"type": "object", "properties": {"project": {"type": "string", "title": "游戏标识"}, "fp_device_id": {"type": "string", "title": "登录设备"}, "device_type": {"type": "string", "title": "设备型号"}, "ip": {"type": "string", "title": "登录 IP"}, "ip_loc": {"type": "string", "title": "IP 位置"}, "login_at": {"type": "string", "title": "登录时间"}}, "title": "精分数据 - 登录查询 resp"}, "pbDataPlatUserPayInfoReq": {"type": "object", "properties": {"project": {"type": "string", "title": "游戏标识"}, "uid": {"type": "string", "format": "uint64", "title": "uid"}, "created_at": {"type": "array", "items": {"type": "string"}, "title": "查询时间段"}}, "title": "精分数据 - 支付查询 req"}, "pbDataPlatUserPayInfoResp": {"type": "object", "properties": {"project": {"type": "string", "title": "游戏标识"}, "product_name": {"type": "string", "title": "商品名称 - iap_product_name"}, "pay_channel": {"type": "string", "title": "支付渠道 - payment_processor"}, "price": {"type": "string", "title": "价格 - price"}, "basic_price": {"type": "string", "title": "基础价格 - base_price"}, "currency": {"type": "string", "title": "币种 - currency"}, "status": {"type": "string", "title": "支付结果 - result"}, "paid_at": {"type": "string", "title": "支付时间 - finish_time"}}, "title": "精分数据 - 支付查询 resp"}, "pbDcPoolSort": {"type": "integer", "format": "int32", "enum": ["0", "1", "2", "3"], "default": "0", "description": "- 1: 等待时长 - 默认\n - 2: 客服最后回复时间\n - 3: 累付金额", "title": "DC列表排序方式"}, "pbDiscordNewCommuRecordAddReq": {"type": "object", "properties": {"commu_date": {"type": "string", "title": "沟通日期 @gotags: validate:\"required\""}, "project": {"type": "string", "title": "游戏 @gotags: validate:\"required\""}, "question": {"type": "string", "title": "沟通问题 @gotags: validate:\"required\""}, "cat_id": {"type": "integer", "format": "int64", "title": "问题类型Id @gotags: validate:\"required\""}, "handle_status": {"type": "integer", "format": "int32", "title": "处理状态 @gotags: validate:\"required\""}, "remark": {"type": "string", "title": "备注"}, "msg_ids": {"type": "string", "title": "涉及对话信息 @gotags: validate:\"required\""}, "dsc_user_id": {"type": "string", "title": "玩家dsc_user_id @gotags: validate:\"required\""}, "uid": {"type": "string", "format": "int64", "title": "玩家uid"}, "sid": {"type": "string", "title": "玩家所在的服务器"}, "nick_name": {"type": "string", "title": "玩家昵称 @gotags: validate:\"required\""}, "pay_all": {"type": "number", "format": "double", "title": "玩家累付金额"}, "cat_type": {"type": "integer", "format": "int64", "title": "类别:line or discord @gotags: validate:\"required,oneof=1 2\""}}}, "pbDiscordNewCommuRecordEditReq": {"type": "object", "properties": {"id": {"type": "string", "format": "int64", "title": "@gotags: validate:\"required\""}, "commu_date": {"type": "string"}, "question": {"type": "string"}, "cat_id": {"type": "integer", "format": "int64"}, "handle_status": {"type": "integer", "format": "int32"}, "remark": {"type": "string"}}}, "pbDiscordNewCommuRecordListReq": {"type": "object", "properties": {"project": {"type": "string"}, "commu_date": {"type": "array", "items": {"type": "string"}}, "operator": {"type": "array", "items": {"type": "string"}}, "uid": {"type": "string", "format": "int64"}, "page": {"type": "integer", "format": "int64", "title": "@gotags: validate:\"required\""}, "page_size": {"type": "integer", "format": "int64", "title": "@gotags: validate:\"required\""}, "cat_id": {"type": "integer", "format": "int64"}, "handle_status": {"type": "array", "items": {"type": "integer", "format": "int32"}}, "cat_type": {"type": "integer", "format": "int64", "title": "类别:line or discord @gotags: validate:\"required,oneof=1 2\""}}}, "pbDiscordNewCommuRecordListResp": {"type": "object", "properties": {"current_page": {"type": "integer", "format": "int64"}, "per_page": {"type": "integer", "format": "int64"}, "total": {"type": "integer", "format": "int64"}, "data": {"type": "array", "items": {"$ref": "#/definitions/pbDiscordNewCommuRecordListRespDiscordCommuRecord"}}}}, "pbDiscordNewCommuRecordListRespDialogueItem": {"type": "object", "properties": {"role": {"type": "string"}, "content": {"type": "string"}}}, "pbDiscordNewCommuRecordListRespDiscordCommuRecord": {"type": "object", "properties": {"commu_date": {"type": "string"}, "project": {"type": "string"}, "uid": {"type": "string", "format": "int64"}, "sid": {"type": "string"}, "nick_name": {"type": "string"}, "pay_all": {"type": "number", "format": "double"}, "question": {"type": "string"}, "question_type": {"type": "integer", "format": "int32"}, "handle_status": {"type": "integer", "format": "int32"}, "remark": {"type": "string"}, "dialogue": {"type": "array", "items": {"$ref": "#/definitions/pbDiscordNewCommuRecordListRespDialogueItem"}}, "operator": {"type": "string"}, "id": {"type": "string", "format": "int64"}, "maintainer": {"type": "string"}, "cat_id": {"type": "integer", "format": "int32"}, "category": {"type": "string"}}}, "pbDiscordPlayerSatisfactionStatsReq": {"type": "object", "properties": {"project": {"type": "array", "items": {"type": "string"}, "title": "游戏项目"}, "date": {"type": "array", "items": {"type": "string"}, "title": "日期"}, "evaluation_target": {"type": "array", "items": {"$ref": "#/definitions/pbSurveyQstType"}, "title": "评价对象"}, "operator": {"type": "array", "items": {"type": "string"}, "title": "处理人"}, "maintainer": {"type": "array", "items": {"type": "string"}, "title": "维护人"}, "stat_type": {"$ref": "#/definitions/pbSurveyStatType", "title": "报表维度 @gotags: validate:\"required,oneof=1 2 3\""}}}, "pbDiscordPublicTagReq": {"type": "object", "properties": {"dsc_user_id_list": {"type": "array", "items": {"type": "string"}, "title": "@gotags: validate:\"required\""}}}, "pbDiscordPublicTagResp": {"type": "object", "properties": {"tags": {"type": "array", "items": {"$ref": "#/definitions/pbDiscordPublicTagRespTagInfo"}}}}, "pbDiscordPublicTagRespTagInfo": {"type": "object", "properties": {"tag_id": {"type": "integer", "format": "int64", "title": "标签ID"}, "tag_name": {"type": "string", "title": "标签名称"}}}, "pbDiscordTabCountResp": {"type": "object", "properties": {"detail": {"type": "array", "items": {"$ref": "#/definitions/DiscordTabCountRespDiscordTabCount"}}}}, "pbDiscordTabCountRespTabCountDetail": {"type": "object", "properties": {"tab_name": {"type": "string"}, "count": {"type": "string", "format": "uint64"}}}, "pbDiscordTabEditReq": {"type": "object", "properties": {"id": {"type": "string", "format": "int64", "title": "@gotags: validate:\"required\""}, "tab_name": {"type": "string", "title": "@gotags: validate:\"required\""}, "public": {"type": "integer", "format": "int32", "title": "@gotags: validate:\"required,oneof=1 2\""}}}, "pbDiscordTagBatchDelete": {"type": "object", "properties": {"dsc_user_id_list": {"type": "array", "items": {"type": "string"}, "title": "待删除dscuserID @gotags: validate:\"required\""}, "tag_ids": {"type": "array", "items": {"type": "integer", "format": "int64"}, "title": "待删除标签ID @gotags: validate:\"required\""}, "project": {"type": "string", "title": "游戏 @gotags: validate:\"required\""}}, "title": "批量删除DC标签"}, "pbDscAuthor": {"type": "object", "properties": {"id": {"type": "string", "title": "dsc_user_id-用户id"}, "username": {"type": "string", "title": "用户名称"}, "avatar": {"type": "string", "title": "用户头像"}, "global_name": {"type": "string", "title": "global name"}, "bot": {"type": "boolean", "title": "是否是机器人"}}}, "pbDscBotConfig": {"type": "object", "properties": {"client_id": {"type": "string", "title": "Application ID"}, "public_key": {"type": "string", "title": "Public Key"}, "bot_token": {"type": "string", "title": "Bot <PERSON>"}, "guild_id": {"type": "string", "title": "Sever ID"}, "guild_desc": {"type": "string", "title": "服务器名称"}, "project": {"type": "string", "title": "项目名称"}, "welcome_message": {"type": "string", "title": "欢迎消息"}}, "title": "DscBotConfig Discord机器人配置"}, "pbDscBotDetail": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "ID"}, "project": {"type": "string", "title": "项目名称"}, "dsc_name": {"type": "string", "title": "DC账号"}, "bot_desc": {"type": "string", "title": "机器人描述"}, "app_id": {"type": "string", "title": "Application ID"}, "bot_config": {"$ref": "#/definitions/pbDscBotConfig", "title": "机器人配置信息"}, "is_delete": {"type": "boolean", "title": "是否删除"}, "user_id": {"type": "string", "title": "用户ID"}, "username": {"type": "string", "title": "用户名称"}, "discriminator": {"type": "string", "title": "标识"}, "updated_by": {"type": "string", "title": "操作人"}, "created_at": {"type": "string", "title": "创建时间"}, "updated_at": {"type": "string", "title": "更新时间"}, "bot_status": {"type": "integer", "format": "int64", "title": "机器人状态 0处理中，1已监听，2异常"}}}, "pbDscChannelDialogFreshReq": {"type": "object", "properties": {"channel_id": {"type": "string", "title": "玩家 DC ID - 必传"}, "after": {"type": "string", "title": "向下翻页 - 开始消息id - 对应 msg_id"}}}, "pbDscChannelDialogFreshResp": {"type": "object", "properties": {"dialog_list": {"type": "array", "items": {"$ref": "#/definitions/pbDscDialogDetail"}, "title": "新增会话列表"}, "fresh_event": {"type": "array", "items": {"$ref": "#/definitions/pbDscDialogFreshEvent"}, "title": "新增事件数据"}}}, "pbDscChannelDialogReq": {"type": "object", "properties": {"channel_id": {"type": "string", "title": "玩家 DC ID - 必传"}, "before": {"type": "string", "title": "向上翻页 - 开始消息id - 对应 msg_id"}, "after": {"type": "string", "title": "向下翻页 - 开始消息id - 对应 msg_id"}, "limit": {"type": "string", "format": "int64", "title": "单页限定条数 -  默认 50条"}, "msg_ids": {"type": "array", "items": {"type": "string"}, "title": "指定查询 msg_id"}}}, "pbDscChannelFileCreateReq": {"type": "object", "properties": {"channel_id": {"type": "string", "title": "玩家 DC_CHANNEL_ID-必传 @gotags: validate:\"required\""}, "bot_id": {"type": "string", "title": "机器人信息 @gotags: validate:\"required\""}}}, "pbDscChannelMsgCreateReq": {"type": "object", "properties": {"channel_id": {"type": "string", "title": "玩家 DC_CHANNEL_ID-必传 @gotags: validate:\"required\""}, "bot_id": {"type": "string", "title": "机器人信息  @gotags: validate:\"required\""}, "content": {"type": "string", "title": "回复内容 @gotags: validate:\"required\""}, "ignore_state": {"type": "integer", "format": "int32", "title": "是否强制忽略 回复状态: 1不调整回复状态"}}}, "pbDscChannelMsgCreateResp": {"type": "object", "properties": {"msg_id": {"type": "string", "title": "回复消息id"}, "channel_id": {"type": "string", "title": "发送渠道"}}}, "pbDscChannelMsgEditReq": {"type": "object", "properties": {"channel_id": {"type": "string", "title": "玩家 DC_CHANNEL_ID-必传 @gotags: validate:\"required\""}, "bot_id": {"type": "string", "title": "机器人信息  @gotags: validate:\"required\""}, "msg_id": {"type": "string", "title": "消息id @gotags: validate:\"required\""}, "content": {"type": "string", "title": "回复内容 @gotags: validate:\"required\""}}}, "pbDscDialogDetail": {"type": "object", "properties": {"msg_id": {"type": "string", "title": "消息 id"}, "project": {"type": "string", "title": "project"}, "from_user_id": {"type": "string", "title": "消息来源id"}, "channel_id": {"type": "string", "title": "dm channel_id"}, "created_at": {"type": "string", "title": "消息时间格式"}, "is_edited": {"type": "boolean", "title": "消息是否被修改"}, "author": {"$ref": "#/definitions/pbDscAuthor", "title": "author : 消息发送人详情"}, "content": {"type": "string", "title": "文本消息 - 内容"}, "attach": {"type": "array", "items": {"$ref": "#/definitions/pbDscMsgAttach"}, "title": "附件消息 - 附件列表"}, "embed": {"type": "array", "items": {"$ref": "#/definitions/pbDscMsgEmbed"}, "title": "embeds - any embedded content"}, "stickers": {"type": "array", "items": {"$ref": "#/definitions/pbDscMsgSticker"}, "title": "贴纸 - sticker_items"}, "reactions": {"type": "array", "items": {"$ref": "#/definitions/pbDscMsgReaction"}, "title": "所有反应"}, "poll": {"$ref": "#/definitions/pbDscMsgPoll", "title": "投票 @gotags: json:\"poll,omitempty\""}, "referenced_msg_id": {"type": "string", "title": "针对特定一条消息回复"}, "referenced_msg": {"$ref": "#/definitions/pbDscDialogDetail", "title": "针对特定一条消息回复-原消息详情 @gotags: json:\"referenced_msg,omitempty\""}, "checked": {"type": "boolean"}}}, "pbDscDialogDetailResp": {"type": "object", "properties": {"dialog_list": {"type": "array", "items": {"$ref": "#/definitions/pbDscDialogDetail"}}}}, "pbDscDialogFreshEvent": {"type": "object", "properties": {"event_type": {"$ref": "#/definitions/pbDscEvTpDf", "title": "事件类型：详细看 DscEvTpDf"}, "msg_detail": {"$ref": "#/definitions/pbDscDialogDetail", "title": "新增/修改消息-详情 @gotags: json:\"msg_detail,omitempty\""}, "del_msg_ids": {"type": "array", "items": {"type": "string"}, "title": "删除消息 - 具体详情 @gotags: json:\"del_msg_ids,omitempty\""}, "add_reaction": {"type": "array", "items": {"$ref": "#/definitions/pbDscMsgReaction"}, "title": "新增表情 - 详细信息 @gotags: json:\"add_reaction,omitempty\""}, "del_reaction": {"type": "array", "items": {"$ref": "#/definitions/pbDscMsgReaction"}, "title": "删除表情 - 详情 @gotags: json:\"del_reaction,omitempty\""}}}, "pbDscEvTpDf": {"type": "integer", "format": "int32", "enum": ["0", "1", "2", "3", "4", "5"], "default": "0", "title": "- 0: 未知事件\n - 1: 消息 - 新增\n - 2: 消息 - 编辑\n - 3: 消息 - 删除\n - 4: 反应 - 新增\n - 5: 反应 - 删除"}, "pbDscMsgAttach": {"type": "object", "properties": {"id": {"type": "string", "title": "附件id"}, "url": {"type": "string", "title": "附件url"}, "proxy_url": {"type": "string", "title": "附件代理url"}, "filename": {"type": "string", "title": "附件名称"}, "content_type": {"type": "string", "title": "附件类型: the attachment's media type : https://en.wikipedia.org/wiki/Media_type"}, "width": {"type": "integer", "format": "int32", "title": "附件宽度"}, "height": {"type": "integer", "format": "int32", "title": "附件高度"}, "size": {"type": "integer", "format": "int32", "title": "附件大小"}, "ephemeral": {"type": "boolean", "title": "是否是临时附件"}}}, "pbDscMsgEmbed": {"type": "object", "properties": {"title": {"type": "string", "title": "标题"}, "type": {"type": "string", "title": "type: \"rich\", \"image\", \"video\", \"gifv\", \"article\", \"link\""}, "description": {"type": "string", "title": "描述"}, "url": {"type": "string", "title": "url"}, "color": {"type": "string", "format": "int64", "title": "color"}, "provider": {"$ref": "#/definitions/DscMsgEmbedProvider", "description": "来源标识：MessageEmbedProvider is a part of a MessageEmbed struct."}, "thumbnail": {"$ref": "#/definitions/DscMsgEmbedThumbnail", "description": "MessageEmbedThumbnail is a part of a MessageEmbed struct."}, "video": {"$ref": "#/definitions/DscMsgEmbedVideo", "description": "MessageEmbedVideo is a part of a MessageEmbed struct."}}}, "pbDscMsgPoll": {"type": "object", "properties": {"question_text": {"type": "string", "title": "问题描述"}, "answers": {"type": "array", "items": {"$ref": "#/definitions/pbDscMsgPollAnswer"}, "title": "答案列表"}, "allow_multiselect": {"type": "boolean", "title": "是否允许多选"}, "duration": {"type": "integer", "format": "int32", "description": "NOTE: should be set only on creation, when fetching use Expiry."}, "expiry": {"type": "string", "format": "uint64", "description": "NOTE: as Discord documentation notes, this field might be null even when fetching."}, "results": {"$ref": "#/definitions/pbDscMsgPollResult", "title": "results"}}}, "pbDscMsgPollAnswer": {"type": "object", "properties": {"answer_id": {"type": "integer", "format": "int64", "title": "答案 id"}, "answer_text": {"type": "string", "title": "答案描述"}}}, "pbDscMsgPollAnswerCount": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "title": "答案 id"}, "count": {"type": "integer", "format": "int64", "title": "答案数量"}, "me_voted": {"type": "boolean", "title": "是否我投票"}}}, "pbDscMsgPollResult": {"type": "object", "properties": {"is_finalized": {"type": "boolean", "title": "是否已经过期"}, "answer_count": {"type": "array", "items": {"$ref": "#/definitions/pbDscMsgPollAnswerCount"}, "title": "投票结果"}}}, "pbDscMsgReaction": {"type": "object", "properties": {"msg_id": {"type": "string", "title": "消息id"}, "name": {"type": "string", "title": "表情"}, "user_id": {"type": "string", "title": "添加表情的用户 user_id"}, "user_name": {"type": "string", "title": "用户名称"}, "created_at": {"type": "string", "title": "表情添加时间"}}}, "pbDscMsgSticker": {"type": "object", "properties": {"id": {"type": "string", "title": "贴纸id"}, "name": {"type": "string", "title": "贴纸name"}, "format_type": {"type": "integer", "format": "int32", "title": "贴纸 类型：StickerFormat： 1: PNG, 2: APNG, 3: LOTTIE， 4: GIF ： 参考：https://discord.com/developers/docs/resources/sticker#sticker-object-sticker-format-types"}}}, "pbDscUserDetailReq": {"type": "object", "properties": {"dsc_user_id": {"type": "string", "title": "玩家 DC ID"}}}, "pbDscUserDetailResp": {"type": "object", "properties": {"user_detail": {"$ref": "#/definitions/DscUserDetailRespUserDetail", "title": "基本信息"}}}, "pbDscUserListReq": {"type": "object", "properties": {"project": {"type": "array", "items": {"type": "string"}, "title": "游戏"}, "replied_at": {"type": "array", "items": {"type": "string"}, "title": "回复时间"}, "status": {"type": "array", "items": {"type": "integer", "format": "int64"}, "title": "状态 - 1:未回复、2:已回复"}, "user_content": {"type": "string", "title": "玩家输入信息 - 模糊查询"}, "user_detail_remark": {"type": "string", "title": "备注信息 - 玩家备注信息 - 模糊查询"}, "processor": {"type": "array", "items": {"type": "string"}, "title": "维护人"}, "dsc_user_nickname": {"type": "array", "items": {"type": "string"}, "title": "玩家 DC昵称"}, "dsc_user_id": {"type": "string", "title": "玩家 DC ID"}, "uid": {"type": "string", "format": "uint64"}, "fpid": {"type": "string"}, "sid": {"type": "string"}, "last_login": {"type": "array", "items": {"type": "string"}, "title": "最近登录时间"}, "pay_all": {"type": "array", "items": {"type": "string", "format": "int64"}, "title": "累计付费金额"}, "pay_last_thirty_days": {"type": "array", "items": {"type": "string", "format": "int64"}, "title": "最近30天付费金额"}, "vip_state": {"type": "integer", "format": "int64", "title": "玩家VIP状态"}, "tag": {"type": "array", "items": {"type": "string"}, "title": "玩家画像标签--弃用"}, "birthday": {"type": "array", "items": {"type": "string"}}, "lang": {"type": "string"}, "last_reply_service": {"type": "array", "items": {"type": "string"}, "title": "最近处理人"}, "sort_field": {"type": "string", "title": "排序字段(废弃)"}, "order": {"type": "string", "title": "排序顺序 asc升序，desc降序"}, "page": {"type": "integer", "format": "int64", "title": "@gotags: validate:\"required\""}, "page_size": {"type": "integer", "format": "int64", "title": "@gotags: validate:\"required\""}, "bot_ids": {"type": "array", "items": {"type": "string"}, "title": "机器人bot_id"}, "uids": {"type": "string", "title": "新用字段"}, "tags": {"type": "array", "items": {"type": "integer", "format": "int64"}, "title": "新用标签"}, "tag_type": {"$ref": "#/definitions/pbFilterTagEnum", "title": "标签类型"}, "sort_by": {"$ref": "#/definitions/pbDcPoolSort", "title": "排序方式"}, "language": {"type": "array", "items": {"type": "string"}, "title": "工单语言"}}}, "pbDscUserListResp": {"type": "object", "properties": {"current_page": {"type": "integer", "format": "int64"}, "per_page": {"type": "integer", "format": "int64"}, "total": {"type": "integer", "format": "int64"}, "data": {"type": "array", "items": {"$ref": "#/definitions/DscUserListRespDscUser"}}}}, "pbEgressCatInfoResp": {"type": "object", "properties": {"cat_id": {"type": "integer", "format": "int64"}, "cat_name": {"type": "string"}, "relate_type": {"type": "integer", "format": "int64"}, "process_id": {"type": "integer", "format": "int64"}, "tpl_id": {"type": "integer", "format": "int64"}, "tpl": {"type": "string"}, "fields": {"type": "string"}, "category": {"type": "string"}, "reply_content": {"type": "object", "additionalProperties": {"type": "string"}}, "question_list": {"type": "array", "items": {"$ref": "#/definitions/pbQuestionInfo"}, "title": "问题列表"}, "chat_or_form": {"$ref": "#/definitions/pbChatOrFormType", "title": "chatOrForm 1: 聊天 2: 表单"}}}, "pbEmpty": {"type": "object", "title": "Empty 空对象"}, "pbEnableReq": {"type": "object", "properties": {"object_id": {"type": "integer", "format": "int64", "title": "对象ID @gotags: validate:\"required\""}, "enable": {"type": "boolean", "title": "启用 true false"}}, "title": "EnableReq 启用禁用"}, "pbExamineDscOrderDetailReq": {"type": "object", "properties": {"dsc_examine_id": {"type": "string", "format": "uint64", "title": "质检单 ID"}}, "title": "质检单 - 详情 - req"}, "pbExamineDscOrderDetailResp": {"type": "object", "properties": {"dsc_examine_id": {"type": "string", "format": "uint64", "title": "质检单 - id"}, "task_id": {"type": "string", "format": "uint64", "title": "任务id"}, "tpl_id": {"type": "string", "format": "uint64", "title": "模板id"}, "project": {"type": "string", "title": "项目"}, "dsc_user_id": {"type": "string", "title": "玩家 DC ID"}, "dsc_bot_id": {"type": "string", "title": "机器人 id"}, "dsc_channel_id": {"type": "string", "title": "渠道id"}, "inspector": {"type": "string", "title": "质检员"}, "status": {"$ref": "#/definitions/pbExamineStateDf", "title": "质检状态"}, "created_at": {"type": "string", "title": "创建时间"}, "finished_at": {"type": "string", "title": "结案时间"}, "can_edited": {"type": "boolean", "title": "是否可以编辑：true:可编辑； false:不可编辑"}, "define_field": {"$ref": "#/definitions/protobufAny", "title": "自定义表单数据"}, "common_field": {"$ref": "#/definitions/pbExamineDscOrderDetailRespCommon", "title": "通用结果字段"}}, "title": "质检单 - 详情 - resp"}, "pbExamineDscOrderDetailRespCommon": {"type": "object", "properties": {"final_result": {"$ref": "#/definitions/pbExamineFinalResultDf", "title": "质检结果 @gotags: validate:\"required,gt=0\""}, "final_score": {"type": "integer", "format": "int32", "title": "质检分数"}, "final_reason": {"type": "string", "title": "错误根源"}, "related_account": {"type": "array", "items": {"type": "string"}, "title": "质检结果关联员工：下拉多选，支持搜索"}, "notice_account": {"type": "array", "items": {"type": "string"}, "title": "同步质检结果："}, "final_desc": {"type": "string", "title": "质检问题描述"}, "final_result_modify_comment": {"type": "string", "title": "修改原因备注"}}}, "pbExamineDscOrderListReq": {"type": "object", "properties": {"project": {"type": "array", "items": {"type": "string"}, "title": "游戏"}, "dsc_examine_id": {"type": "string", "format": "uint64", "title": "质检单 ID"}, "status": {"type": "array", "items": {"$ref": "#/definitions/pbExamineStateDf"}, "title": "质检状态"}, "final_result": {"type": "array", "items": {"$ref": "#/definitions/pbExamineFinalResultDf"}, "title": "质检结果"}, "related_account": {"type": "array", "items": {"type": "string"}, "title": "被检员：质检打分时关联的员工"}, "inspector": {"type": "array", "items": {"type": "string"}, "title": "质检员：进行质检打分的人员"}, "created_at": {"type": "array", "items": {"type": "string"}, "title": "创建时间：质检任务的创建时间"}, "finished_at": {"type": "array", "items": {"type": "string"}, "title": "结案时间：质检任务的完成时间"}, "vip_state": {"type": "array", "items": {"type": "integer", "format": "int64"}, "title": "玩家VIP状态"}, "page": {"type": "integer", "format": "int64", "title": "页码"}, "page_size": {"type": "integer", "format": "int64", "title": "页大小"}}, "title": "质检单 - 搜索 - req"}, "pbExamineDscOrderListResp": {"type": "object", "properties": {"current_page": {"type": "integer", "format": "int64"}, "per_page": {"type": "integer", "format": "int64"}, "total": {"type": "string", "format": "int64"}, "data": {"type": "array", "items": {"$ref": "#/definitions/ExamineDscOrderListRespExamineDscOrder"}}}, "title": "质检单 - 搜索 - resp"}, "pbExamineDscOrderSaveReq": {"type": "object", "properties": {"dsc_examine_id": {"type": "string", "format": "uint64", "title": "质检单-id"}, "define_field": {"$ref": "#/definitions/protobufAny", "title": "自定义表单数据 eg: {\"操作1\": [\"xxxx\",\"xx\"],\"操作2\": \"xxxx\"}"}, "common_field": {"$ref": "#/definitions/pbExamineDscOrderSaveReqCommon", "title": "通用结果字段"}}, "title": "质检单 - 保存 - req"}, "pbExamineDscOrderSaveReqCommon": {"type": "object", "properties": {"final_result": {"$ref": "#/definitions/pbExamineFinalResultDf", "title": "质检结果 @gotags: validate:\"required,gt=0\""}, "final_score": {"type": "integer", "format": "int32", "title": "质检分数"}, "final_reason": {"type": "string", "title": "错误根源"}, "related_account": {"type": "array", "items": {"type": "string"}, "title": "质检结果关联员工：下拉多选，支持搜索 @gotags: validate:\"required\""}, "notice_account": {"type": "array", "items": {"type": "string"}, "title": "同步质检结果："}, "final_desc": {"type": "string", "title": "质检问题描述"}, "final_result_modify_comment": {"type": "string", "title": "修改原因备注"}}}, "pbExamineDscOrderStatsReq": {"type": "object", "properties": {"project": {"type": "array", "items": {"type": "string"}}}, "title": "discord 质检单统计数据 - req"}, "pbExamineDscOrderStatsResp": {"type": "object", "properties": {"examine_dsc_count": {"type": "string", "format": "int64", "title": "全部数据量"}, "examine_dsc_wait_count": {"type": "string", "format": "int64", "title": "待处理质检任务"}, "examine_dsc_mine_wait_count": {"type": "string", "format": "int64", "title": "我的待处理质检任务"}, "examine_dsc_mine_done_count": {"type": "string", "format": "int64", "title": "我的已完成质检任务"}, "examine_dsc_unqualified_count": {"type": "string", "format": "int64", "title": "质检不合格任务"}, "examine_dsc_mine_unqualified_count": {"type": "string", "format": "int64", "title": "我的质检不合格任务"}}, "title": "discord 质检单统计数据 - resp"}, "pbExamineFieldDt": {"type": "object", "properties": {"field_name": {"type": "string", "title": "字段名"}, "field_type": {"$ref": "#/definitions/pbExamineFieldTpDf", "title": "字段类型"}, "field_opts": {"type": "array", "items": {"type": "string"}, "title": "字段值选项"}}, "title": "打分表配置 - 详情 - 模块 字段定义"}, "pbExamineFieldTpDf": {"type": "integer", "format": "int32", "enum": ["0", "1", "2", "3", "4"], "default": "0", "description": "- 0: 不需要定义\n - 1: 下拉单选\n - 2: 下拉多选\n - 3: 单行文本\n - 4: 多行文档 - 富文本textarea", "title": "质检打分表-字段值类型定义"}, "pbExamineFinalResultDf": {"type": "integer", "format": "int32", "enum": ["0", "1", "2"], "default": "0", "description": "- 1: 通过\n - 2: 不通过", "title": "ExamineFinalResultDf 质检单 最终结果"}, "pbExamineModuleDt": {"type": "object", "properties": {"name": {"type": "string", "title": "模块名称 gotags: validate:\"required\""}, "field_detail": {"type": "array", "items": {"$ref": "#/definitions/pbExamineFieldDt"}, "title": "考核项 gotags: validate:\"required\""}}, "title": "打分表配置 - 详情 - 模块定义"}, "pbExamineOrderNoticeAcceptorResp": {"type": "object", "properties": {"new_notice": {"type": "boolean", "title": "是否有 未读消息"}}, "title": "质检 - 红点"}, "pbExamineOrderNoticeListReq": {"type": "object", "properties": {"page": {"type": "integer", "format": "int64", "title": "页码"}, "page_size": {"type": "integer", "format": "int64", "title": "页大小"}}}, "pbExamineOrderNoticeListResp": {"type": "object", "properties": {"current_page": {"type": "integer", "format": "int64"}, "per_page": {"type": "integer", "format": "int64"}, "total": {"type": "string", "format": "int64"}, "data": {"type": "array", "items": {"$ref": "#/definitions/ExamineOrderNoticeListRespExamineNoticeDetail"}}}, "title": "打分表配置 - 列表 - resp"}, "pbExamineStateDf": {"type": "integer", "format": "int32", "enum": ["0", "1", "10", "100"], "default": "0", "description": "- 1: 未开始\n - 10: 进行中\n - 100: 已完成", "title": "ExamineStateDf 质检单 当前状态"}, "pbExamineTaskDetailReq": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}}, "title": "任务配置 - 详情 - req"}, "pbExamineTaskDetailResp": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "任务id"}, "task_name": {"type": "string", "title": "任务名称"}, "project": {"type": "string", "title": "游戏"}, "tpl_id": {"type": "string", "format": "uint64", "title": "关联打分表"}, "task_group": {"$ref": "#/definitions/pbExamineTaskGroupDf", "title": "抽检库分组"}, "filter_dsc_replied_at": {"type": "array", "items": {"type": "string"}, "title": "discord 抽查规则 - 回复时间"}, "filter_dsc_all_total_pay_gte": {"type": "string", "format": "int64", "title": "discord 抽查规则 - 全检标准：充值金额"}, "filter_dsc_other_all": {"type": "boolean", "title": "discord 抽查规则 - 剩下抽检范围 - 是否全部： true 全部；false 部分"}, "filter_dsc_other_vip_state": {"type": "array", "items": {"type": "integer", "format": "int64"}, "title": "// discord 抽查规则 - 剩下抽检范围-部分-圈选规则-标签\n   repeated string filter_dsc_other_label = 23;\ndiscord 抽查规则 - 剩下抽检范围-部分-圈选规则-vip 状态"}, "filter_dsc_other_maintainer": {"type": "array", "items": {"type": "string"}, "title": "discord 抽查规则 - 剩下抽检范围-部分-圈选规则-维护人"}, "filter_dsc_other_num": {"type": "integer", "format": "int32", "title": "discord 抽查规则 - 剩下抽检范围-部分-圈选规则-抽检数量"}, "filter_dsc_assign_account": {"type": "array", "items": {"type": "string"}, "title": "discord 抽查规则 - 剩下抽检范围-部分-圈选规则-分配人员"}}, "title": "任务配置 - 详情 - resp"}, "pbExamineTaskDscFilterCountReq": {"type": "object", "properties": {"project": {"type": "string", "title": "游戏 @gotags: validate:\"required\""}, "replied_at": {"type": "array", "items": {"type": "string"}, "title": "回复时间 @gotags: validate:\"required\""}, "total_pay": {"type": "string", "format": "int64", "title": "充值金额 @gotags: validate:\"required,gte=1\""}}}, "pbExamineTaskDscFilterCountResp": {"type": "object", "properties": {"num": {"type": "string", "format": "int64", "title": "数量"}}}, "pbExamineTaskGroupDf": {"type": "integer", "format": "int32", "enum": ["0", "1", "2"], "default": "0", "description": "- 1: 工单\n - 2: discord", "title": "质检任务-任务类型"}, "pbExamineTaskListReq": {"type": "object", "properties": {"task_name": {"type": "string", "title": "质检任务名称"}, "page": {"type": "integer", "format": "int64", "title": "页码"}, "page_size": {"type": "integer", "format": "int64", "title": "页大小"}}, "title": "任务配置 - 列表 - req"}, "pbExamineTaskListResp": {"type": "object", "properties": {"current_page": {"type": "integer", "format": "int64"}, "per_page": {"type": "integer", "format": "int64"}, "total": {"type": "string", "format": "int64"}, "data": {"type": "array", "items": {"$ref": "#/definitions/ExamineTaskListRespExamineTask"}}}, "title": "任务配置 - 列表 - resp"}, "pbExamineTaskRulesDf": {"type": "integer", "format": "int32", "enum": ["0", "1", "2"], "default": "0", "description": "- 1: 无差别随机抽检\n - 2: 定向等量抽检", "title": "质检任务-抽检规则"}, "pbExamineTaskSaveReq": {"type": "object", "properties": {"task_name": {"type": "string", "title": "任务名称 @gotags: validate:\"required\""}, "project": {"type": "string", "title": "游戏 @gotags: validate:\"required\""}, "tpl_id": {"type": "string", "format": "uint64", "title": "关联打分表 @gotags: validate:\"required\""}, "task_group": {"$ref": "#/definitions/pbExamineTaskGroupDf", "title": "抽检库分组 @gotags: validate:\"required,gte=1\""}, "task_rule": {"$ref": "#/definitions/pbExamineTaskRulesDf", "title": "discord 抽查规则 - 抽检方式 @gotags: validate:\"required\""}, "filter_dsc_replied_at": {"type": "array", "items": {"type": "string"}, "title": "discord抽查规则-回复时间 @gotags: validate:\"required_if=TaskGroup 2\""}, "filter_dsc_all_total_pay_gte": {"type": "string", "format": "int64", "title": "discord抽查规则-全检标准:充值金额 @gotags: validate:\"required_if=TaskGroup 2\""}, "filter_dsc_other_all": {"type": "boolean", "title": "discord抽查规则-剩下抽检范围-是否全部：true:全部；false:部分"}, "filter_dsc_other_maintainer": {"type": "array", "items": {"type": "string"}, "title": "// discord抽查规则-剩下抽检范围-部分-圈选规则-标签\n   repeated string filter_dsc_other_tag = 23;\ndiscord抽查规则-剩下抽检范围-部分-圈选规则-维护人"}, "filter_dsc_other_vip_state": {"type": "array", "items": {"type": "integer", "format": "int64"}, "title": "discord抽查规则-剩下抽检范围-部分-圈选规则-vip状态"}, "filter_dsc_other_num": {"type": "integer", "format": "int64", "title": "discord抽查规则-剩下抽检范围-部分-圈选规则-抽检数量\""}, "filter_dsc_assign_account": {"type": "array", "items": {"type": "string"}, "title": "discord抽查规则-剩下抽检范围-部分-圈选规则-分配人员\" @gotags: validate:\"required_if=TaskGroup 2\""}, "link": {"type": "string", "title": "详情快照图地址"}}, "title": "任务配置 - 保存 - req"}, "pbExamineTaskStateDf": {"type": "integer", "format": "int32", "enum": ["0", "1", "10", "100", "200"], "default": "0", "description": "- 1: 未开始\n - 10: 进行中\n - 100: 成功\n - 200: 失败", "title": "质检任务-状态定义"}, "pbExamineTplCopyReq": {"type": "object", "properties": {"from_tpl_id": {"type": "string", "format": "uint64", "title": "打分表 id @gotags: validate:\"required\""}, "tpl_desc": {"type": "string", "title": "复制打分表字段信息： @gotags: validate:\"required\""}}, "title": "打分表配置 - 复制 - req"}, "pbExamineTplDelReq": {"type": "object", "properties": {"tpl_id": {"type": "string", "format": "uint64", "title": "打分表 id"}}, "title": "打分表配置 - del - req"}, "pbExamineTplDetailReq": {"type": "object", "properties": {"tpl_id": {"type": "string", "format": "uint64", "title": "打分表 id"}}, "title": "打分表配置 - 详情 - req"}, "pbExamineTplDetailResp": {"type": "object", "properties": {"tpl_desc": {"type": "string", "title": "打分表名字："}, "module_detail": {"type": "array", "items": {"$ref": "#/definitions/pbExamineModuleDt"}, "title": "模块定义"}}, "title": "打分表配置 - 详情 - resp"}, "pbExamineTplListReq": {"type": "object", "properties": {"tpl_desc": {"type": "string", "title": "标题"}, "page": {"type": "integer", "format": "int64", "title": "页码"}, "page_size": {"type": "integer", "format": "int64", "title": "页大小"}}, "title": "打分表配置 - 列表 - req"}, "pbExamineTplListResp": {"type": "object", "properties": {"current_page": {"type": "integer", "format": "int64"}, "per_page": {"type": "integer", "format": "int64"}, "total": {"type": "string", "format": "int64"}, "data": {"type": "array", "items": {"$ref": "#/definitions/ExamineTplListRespExamineTpl"}}}, "title": "打分表配置 - 列表 - resp"}, "pbExamineTplOptsResp": {"type": "object", "properties": {"list": {"type": "array", "items": {"$ref": "#/definitions/ExamineTplOptsRespExamineTplOpts"}, "title": "打分表选项"}}}, "pbExamineTplSaveReq": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "id"}, "tpl_desc": {"type": "string", "title": "打分表名字 @gotags: validate:\"required\""}, "module_detail": {"type": "array", "items": {"$ref": "#/definitions/pbExamineModuleDt"}, "title": "模块定义 @gotags: validate:\"required\""}}, "title": "打分表配置 - 保存 - req"}, "pbFilterEnum": {"type": "integer", "format": "int32", "enum": ["0", "1", "2", "3", "4"], "default": "0", "description": "- 0: 全部\n - 1: 空白\n - 2: 包含\n - 3: 不包含\n - 4: 系统", "title": "FilterEnum 基础过滤枚举"}, "pbFilterTagEnum": {"type": "integer", "format": "int32", "enum": ["0", "1", "2", "3", "4"], "default": "0", "description": "- 0: 全部, 只是用来占位, 无意义\n - 1: 空白\n - 2: 包含\n - 3: 不包含\n - 4: 全部有标签的", "title": "FilterTagEnum 标签过滤枚举"}, "pbGameCategory": {"type": "object", "properties": {"game": {"type": "string", "title": "@gotags: validate:\"required\""}, "categories": {"type": "array", "items": {"type": "string"}, "title": "@gotags: validate:\"required\""}, "system_tag": {"type": "array", "items": {"$ref": "#/definitions/pbTicketSystemTag"}, "title": "@gotags: validate:\"required\""}}, "title": "游戏、问题分类和系统标签"}, "pbGetCatTitlesReq": {"type": "object", "properties": {"cat_ids": {"type": "array", "items": {"type": "integer", "format": "int32"}}, "lang": {"type": "string"}, "game_project": {"type": "string"}}}, "pbGetHistoryResponse": {"type": "object", "properties": {"history": {"type": "array", "items": {"$ref": "#/definitions/pbHistoryItem"}}, "question_hit_list": {"type": "array", "items": {"$ref": "#/definitions/pbQuestionHit"}}, "question_get_list": {"type": "array", "items": {"$ref": "#/definitions/pbQuestionGet"}}, "ticket_id": {"type": "string", "format": "int64", "title": "ticket_id"}, "conversation_status": {"type": "integer", "format": "int32"}}, "title": "获取历史响应"}, "pbGroupUserReq": {"type": "object", "properties": {"account": {"type": "string"}, "status": {"type": "integer", "format": "int64"}}, "title": "客服在线状态请求参数"}, "pbHistory": {"type": "object", "properties": {"role": {"type": "string"}, "question_key": {"type": "string"}, "content": {"type": "string"}}, "title": "历史记录"}, "pbHistoryItem": {"type": "object", "properties": {"role": {"type": "string"}, "content": {"type": "string"}, "question_key": {"type": "string"}, "timestamp": {"type": "string", "format": "int64"}, "uuid": {"type": "string"}}, "title": "历史记录项"}, "pbIndicesCfgResp": {"type": "object", "properties": {"cat_types": {"type": "object", "additionalProperties": {"type": "string"}, "title": "内容配置json"}}}, "pbIndicesDelReq": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "id @gotags: validate:\"required\""}}}, "pbIndicesEnableReq": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "id @gotags: validate:\"required\""}, "enable": {"type": "boolean", "title": "启用true 禁用false"}}}, "pbIndicesListResp": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/definitions/IndicesListRespitem"}, "title": "列表"}}}, "pbIndicesSaveReq": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "保存接口必传，新增接口不传"}, "cat_type": {"type": "string", "title": "配置内容类型 @gotags: validate:\"required\""}, "cat_ids": {"type": "array", "items": {"type": "integer", "format": "int64"}, "title": "关联问题分类 @gotags: validate:\"required\""}, "project": {"type": "string", "title": "project 新增接口必传"}}}, "pbLineTabAddReq": {"type": "object", "properties": {"tab_name": {"type": "string", "title": "@gotags: validate:\"required\""}, "public": {"type": "integer", "format": "int32", "title": "@gotags: validate:\"required,oneof=1 2\""}, "detail": {"$ref": "#/definitions/pbLineUserListReq", "title": "搜索条件组合 @gotags: validate:\"required\""}}}, "pbLineTabCountResp": {"type": "object", "properties": {"detail": {"type": "array", "items": {"$ref": "#/definitions/LineTabCountRespLineTabCount"}}}}, "pbLineTabCountRespTabCountDetail": {"type": "object", "properties": {"tab_name": {"type": "string"}, "count": {"type": "string", "format": "uint64"}}}, "pbLineTabDelReq": {"type": "object", "properties": {"id": {"type": "string", "format": "int64", "title": "@gotags: validate:\"required\""}}}, "pbLineTabEditReq": {"type": "object", "properties": {"id": {"type": "string", "format": "int64", "title": "@gotags: validate:\"required\""}, "tab_name": {"type": "string", "title": "@gotags: validate:\"required\""}, "public": {"type": "integer", "format": "int32", "title": "@gotags: validate:\"required,oneof=1 2\""}}}, "pbLineTabListResp": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/definitions/LineTabListRespLineTabDetail"}}}}, "pbLineTabListRespTabInfo": {"type": "object", "properties": {"id": {"type": "string", "format": "int64"}, "tab_name": {"type": "string"}, "detail": {"$ref": "#/definitions/pbLineUserListReq"}, "public": {"type": "integer", "format": "int32"}, "operator": {"type": "string"}}}, "pbLineUserListReq": {"type": "object", "properties": {"project": {"type": "array", "items": {"type": "string"}, "title": "游戏"}, "replied_at": {"type": "array", "items": {"type": "string"}, "title": "回复时间"}, "status": {"type": "array", "items": {"type": "integer", "format": "int64"}, "title": "状态 - 1:未回复、2:已回复"}, "user_content": {"type": "string", "title": "玩家输入信息 - 模糊查询"}, "user_detail_remark": {"type": "string", "title": "备注信息 - 玩家备注信息 - 模糊查询"}, "maintainer": {"type": "array", "items": {"type": "string"}, "title": "维护人"}, "display_name": {"type": "string", "title": "玩家 line昵称"}, "line_user_id": {"type": "string", "title": "玩家 line ID"}, "uid": {"type": "string", "format": "uint64"}, "fpid": {"type": "string"}, "sid": {"type": "string"}, "last_login": {"type": "array", "items": {"type": "string"}, "title": "最近登录时间"}, "pay_all": {"type": "array", "items": {"type": "string", "format": "int64"}, "title": "累计付费金额"}, "pay_last_thirty_days": {"type": "array", "items": {"type": "string", "format": "int64"}, "title": "最近30天付费金额"}, "vip_state": {"type": "integer", "format": "int64", "title": "玩家VIP状态"}, "birthday": {"type": "array", "items": {"type": "string"}}, "lang": {"type": "string"}, "last_reply_service": {"type": "array", "items": {"type": "string"}, "title": "最近处理人"}, "sort_by": {"$ref": "#/definitions/pbDcPoolSort", "title": "排序字段"}, "order": {"type": "string", "title": "排序顺序 asc升序，desc降序"}, "page": {"type": "integer", "format": "int64", "title": "@gotags: validate:\"required\""}, "page_size": {"type": "integer", "format": "int64", "title": "@gotags: validate:\"required\""}, "bot_ids": {"type": "array", "items": {"type": "string"}, "title": "机器人bot_id"}, "uids": {"type": "string", "title": "新用字段"}, "channel_id": {"type": "array", "items": {"type": "string"}, "title": "channel_id"}, "tags": {"type": "array", "items": {"type": "integer", "format": "int64"}, "title": "新用标签"}, "tag_type": {"$ref": "#/definitions/pbFilterTagEnum", "title": "标签类型"}}}, "pbModuleCatAddReq": {"type": "object", "properties": {"project": {"type": "string", "title": "项目 @gotags: validate:\"required\""}, "cat_level": {"type": "integer", "format": "int64", "title": "分类的等级 @gotags: validate:\"required,oneof=1 2 3\""}, "category": {"type": "string", "title": "分类名称 @gotags: validate:\"required\""}, "parent_id": {"type": "integer", "format": "int64", "title": "父分类ID"}}, "title": "ModuleCatAddReq 添加分类 请求参数"}, "pbModuleCatSaveReq": {"type": "object", "properties": {"cat_id": {"type": "integer", "format": "int64", "title": "分类ID @gotags: validate:\"required\""}, "category": {"type": "string", "title": "分类名称 @gotags: validate:\"required\""}, "cat_level": {"type": "integer", "format": "int64", "title": "分类的等级 @gotags: validate:\"required,oneof=1 2 3\""}}, "title": "ModuleCatSaveReq 修改分类 请求参数"}, "pbModuleCatTreeReq": {"type": "object", "properties": {"project": {"type": "string", "title": "项目 @gotags: validate:\"required\""}}}, "pbModuleDelReq": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "模板id @gotags: validate:\"required\""}}, "title": "ModuleEditReq 删除模版"}, "pbModuleEditReq": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "模板id @gotags: validate:\"required\""}, "module_name": {"type": "string"}, "game_project": {"type": "string"}, "content": {"type": "string"}, "enable": {"type": "integer", "format": "int64", "title": "启用或禁用模板"}, "cat_id": {"type": "integer", "format": "int64", "title": "模板分类id"}}, "title": "ModuleEditReq 编辑模板"}, "pbModuleListReq": {"type": "object", "properties": {"module_name": {"type": "string", "title": "模板名称,支持模糊搜索"}, "content": {"type": "string", "title": "模板内容,支持模糊搜索"}, "game_project": {"type": "string", "title": "游戏, 支持多选"}, "enable": {"type": "integer", "format": "int64"}, "page": {"type": "integer", "format": "int64"}, "page_size": {"type": "integer", "format": "int64"}, "cat_id": {"type": "array", "items": {"type": "integer", "format": "int64"}, "title": "模版分类"}}, "title": "ModuleListReq 模板查询请求参数"}, "pbModuleListResp": {"type": "object", "properties": {"current_page": {"type": "integer", "format": "int64"}, "per_page": {"type": "integer", "format": "int64"}, "total": {"type": "integer", "format": "int64"}, "data": {"type": "array", "items": {"$ref": "#/definitions/ModuleListRespModuleInfo"}}}, "title": "ModuleListResp 新工单池响应参数"}, "pbModuleSaveReq": {"type": "object", "properties": {"module_name": {"type": "string", "title": "@gotags: validate:\"required\""}, "game_project": {"type": "string", "title": "@gotags: validate:\"required\""}, "content": {"type": "string", "title": "@gotags: validate:\"required\""}, "cat_id": {"type": "integer", "format": "int64", "title": "模板分类id @gotags: validate:\"required\""}}, "title": "ModuleSaveReq 新增模板"}, "pbNoticeRequestV2": {"type": "object", "properties": {"appId": {"type": "string", "title": "项目标识 @gotags: validate:\"required\""}, "gameId": {"type": "integer", "format": "int64", "title": "项目标识 @gotags: validate:\"required\""}, "device_id": {"type": "string", "title": "唯一标识 @gotags: validate:\"required_without=Fpid\""}, "fpid": {"type": "string", "format": "uint64", "title": "fpid @gotags: validate:\"required_without=DeviceId\""}, "uid": {"type": "string", "format": "uint64", "title": "uid"}, "sid": {"type": "string", "title": "区服标识"}, "channel": {"type": "string", "title": "渠道标识"}, "lang": {"type": "string", "title": "语言标识"}, "sdk_version": {"type": "string", "title": "Sdk版本"}, "os": {"type": "string", "title": "系统版本"}, "ts": {"type": "string", "format": "uint64", "title": "时间戳"}, "auth": {"type": "string", "title": "客户端版"}, "scene": {"type": "integer", "format": "int64", "title": "场景"}}}, "pbNoticeResp": {"type": "object", "properties": {"notice_id": {"type": "string", "format": "uint64", "title": "消息ID @gotags: json:\"notice_id,omitempty\""}, "from": {"type": "integer", "format": "int64", "title": "来源 @gotags: json:\"from,omitempty\""}, "object_id": {"type": "string", "format": "uint64", "title": "对象ID @gotags: json:\"object_id,omitempty\""}, "scene": {"type": "integer", "format": "int64", "title": "场景"}, "ticket_sys_type": {"$ref": "#/definitions/pbTicketSys", "title": "使用哪个工单系统"}, "notice_count": {"type": "string", "format": "uint64", "title": "消息未读数量 @gotags: json:\"notice_count\""}}}, "pbOverTimeTplAddReq": {"type": "object", "properties": {"tpl_name": {"type": "string", "title": "@gotags: validate:\"required\""}, "game_project": {"type": "array", "items": {"type": "string"}, "title": "@gotags: validate:\"min=1\""}, "remind_time": {"type": "integer", "format": "int64", "title": "预提醒时间"}, "content": {"type": "object", "additionalProperties": {"type": "string"}, "title": "@gotags: validate:\"required\""}}, "title": "OverTimeTplAddReq 新增模板"}, "pbOverTimeTplDelReq": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "模板id @gotags: validate:\"required\""}}, "title": "OverTimeTplDelReq 删除模版"}, "pbOverTimeTplEditReq": {"type": "object", "properties": {"tpl_id": {"type": "string", "format": "uint64", "title": "模板id @gotags: validate:\"required\""}, "tpl_name": {"type": "string", "title": "@gotags: validate:\"required\""}, "game_project": {"type": "array", "items": {"type": "string"}, "title": "@gotags: validate:\"min=1\""}, "remind_time": {"type": "integer", "format": "int64", "title": "预提醒时间"}, "content": {"type": "object", "additionalProperties": {"type": "string"}, "title": "@gotags: validate:\"required\""}}, "title": "OverTimeTplEditReq 编辑模板"}, "pbOverTimeTplEnableReq": {"type": "object", "properties": {"object_id": {"type": "integer", "format": "int64", "title": "对象ID @gotags: validate:\"required\""}, "enable": {"type": "integer", "format": "int64", "title": "启用1禁用0"}}, "title": "OverTimeTplEnableReq 启用/禁用模版"}, "pbOverTimeTplListReq": {"type": "object", "properties": {"tpl_name": {"type": "string", "title": "模板名称,支持模糊搜索"}, "game_project": {"type": "array", "items": {"type": "string"}, "title": "游戏, 支持多选"}, "page": {"type": "integer", "format": "int64"}, "page_size": {"type": "integer", "format": "int64"}}, "title": "OverTimeTplListReq 模板查询请求参数"}, "pbOverTimeTplListResp": {"type": "object", "properties": {"current_page": {"type": "integer", "format": "int64"}, "per_page": {"type": "integer", "format": "int64"}, "total": {"type": "integer", "format": "int64"}, "data": {"type": "array", "items": {"$ref": "#/definitions/pbOverTimeTplListRespTplInfo"}}}, "title": "OverTimeTplListResp 模版列表响应"}, "pbOverTimeTplListRespTplInfo": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "模板id"}, "tpl_name": {"type": "string", "title": "模板名称"}, "game_project": {"type": "array", "items": {"type": "string"}, "title": "关联游戏"}, "operator": {"type": "string", "title": "操作人"}, "enable": {"type": "integer", "format": "int64", "title": "状态 1启用 0禁用"}, "update_time": {"type": "string", "title": "操作时间"}, "remind_time": {"type": "integer", "format": "int64", "title": "超时时间"}, "tpl_content": {"type": "object", "additionalProperties": {"type": "string"}, "title": "模版文案"}}}, "pbOverTimeTplOptsReq": {"type": "object", "properties": {"game_project": {"type": "string"}}, "title": "OverTimeTplOptsReq 模板配置筛选列表-请求"}, "pbOverTimeTplOptsRes": {"type": "object", "properties": {"list": {"type": "array", "items": {"$ref": "#/definitions/pbOverTimeTplOptsResOpts"}}}, "title": "OverTimeTplOptsReq 模板配置筛选列表-响应结果"}, "pbOverTimeTplOptsResOpts": {"type": "object", "properties": {"tpl_id": {"type": "integer", "format": "int64"}, "tpl_name": {"type": "string"}}}, "pbPortraitEditReq": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}, "game_project": {"type": "string"}, "label": {"type": "string"}, "gender": {"type": "integer", "format": "int64"}, "birthday": {"type": "string"}, "career": {"type": "string"}, "education_level": {"type": "integer", "format": "int64"}, "married_state": {"type": "integer", "format": "int64"}, "fertility_state": {"type": "integer", "format": "int64"}, "remark": {"type": "string"}, "dsc_user_id": {"type": "string", "title": "@gotags: validate:\"required_without=Id\""}}, "title": "PortraitEditReq 添加/编辑 画像信息"}, "pbPortraitInfoReq": {"type": "object", "properties": {"dsc_user_id": {"type": "string", "title": "@gotags: validate:\"required\""}, "game_project": {"type": "string", "title": "@gotags: validate:\"required\""}}, "title": "PortraitInfoReq 获取玩家画像信息"}, "pbPortraitInfoResp": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64"}, "label": {"type": "array", "items": {"$ref": "#/definitions/pbPortraitInfoRespLabelDetail"}}, "gender": {"type": "integer", "format": "int64"}, "birthday": {"type": "string"}, "career": {"type": "string"}, "education_level": {"type": "integer", "format": "int64"}, "married_state": {"type": "integer", "format": "int64"}, "fertility_state": {"type": "integer", "format": "int64"}, "remark": {"type": "string"}, "dsc_user_id": {"type": "string"}, "game_project": {"type": "string"}, "new_label": {"type": "array", "items": {"$ref": "#/definitions/PortraitInfoRespNewLabelDetail"}}, "ticket_info": {"type": "array", "items": {"$ref": "#/definitions/pbPortraitInfoRespTicketInfo"}}}}, "pbPortraitInfoRespLabelDetail": {"type": "object", "properties": {"tag_id": {"type": "string", "format": "uint64"}, "tag_name": {"type": "string"}}}, "pbPortraitInfoRespTicketInfo": {"type": "object", "properties": {"project": {"type": "string", "title": "游戏"}, "ticket_id": {"type": "string", "format": "uint64", "title": "工单IDz"}, "detail": {"type": "string", "title": "玩家输入第一句话"}, "recharge": {"type": "number", "format": "double", "title": "累计金额"}, "status": {"type": "integer", "format": "int64", "title": "工单状态"}, "waiting_time": {"type": "string", "title": "等待时长"}}}, "pbProject": {"type": "object", "properties": {"project_name": {"type": "string", "title": "项目名称"}, "lib_name": {"type": "string", "title": "标签库名称"}}}, "pbProjectLang": {"type": "object", "properties": {"project": {"type": "string", "title": "项目 @gotags: validate:\"required\""}, "lang": {"type": "string", "title": "语言"}}}, "pbProjects": {"type": "object", "properties": {"projects": {"type": "array", "items": {"type": "string"}, "title": "项目名称 @gotags: validate:\"required\""}}}, "pbQuestionBatchImportReq": {"type": "object", "properties": {"file_name": {"type": "string", "title": "文件url @gotags: validate:\"required\""}}, "title": "QuestionBatchImportReq 工单知识库批量导入"}, "pbQuestionContent": {"type": "object", "properties": {"question_key": {"type": "string"}, "question_content": {"type": "string"}}, "title": "问题内容"}, "pbQuestionDelReq": {"type": "object", "properties": {"question_id": {"type": "string", "format": "int64", "title": "@gotags: validate:\"required\""}}, "title": "QuestionDelReq 工单知识库删除"}, "pbQuestionGet": {"type": "object", "properties": {"question_key": {"type": "string"}, "answer": {"type": "string"}, "has_answer": {"type": "boolean"}, "question_ask_count": {"type": "integer", "format": "int32"}, "question_content": {"type": "string"}}, "title": "问题获取"}, "pbQuestionHit": {"type": "object", "properties": {"question_key": {"type": "string"}, "has_answer": {"type": "boolean"}}, "title": "问题命中"}, "pbQuestionInfo": {"type": "object", "properties": {"question_key": {"type": "string"}, "question_name": {"type": "string"}}}, "pbQuestionListReq": {"type": "object", "properties": {"project": {"type": "array", "items": {"type": "string"}, "title": "游戏"}, "cat_id": {"type": "array", "items": {"type": "integer", "format": "int64"}, "title": "问题分类id"}, "question_id": {"type": "string", "format": "int64", "title": "语料id"}, "question_content": {"type": "string", "title": "语料内容"}, "lang": {"type": "array", "items": {"type": "string"}, "title": "语种"}, "update_date": {"type": "array", "items": {"type": "string"}, "title": "更新时间"}, "page": {"type": "integer", "format": "int64", "title": "@gotags: validate:\"required\""}, "page_size": {"type": "integer", "format": "int64", "title": "@gotags: validate:\"required\""}}, "title": "QuestionListReq 工单知识库列表req"}, "pbQuestionListResp": {"type": "object", "properties": {"current_page": {"type": "integer", "format": "int64"}, "per_page": {"type": "integer", "format": "int64"}, "total": {"type": "integer", "format": "int64"}, "data": {"type": "array", "items": {"$ref": "#/definitions/QuestionListRespQuestionRecord"}}}, "title": "QuestionAddResp 工单知识库列表resp"}, "pbQuestionSaveReq": {"type": "object", "properties": {"question_id": {"type": "string", "format": "int64", "title": "问题id"}, "project": {"type": "string", "title": "@gotags: validate:\"required\""}, "question_content": {"type": "string", "title": "@gotags: validate:\"required\""}, "lang": {"type": "string", "title": "@gotags: validate:\"required\""}, "cat_id": {"type": "integer", "format": "int64", "title": "@gotags: validate:\"required\""}, "answer": {"type": "string", "title": "@gotags: validate:\"required\""}}, "title": "QuestionAddReq 语料新增"}, "pbQuestionTrainLogReq": {"type": "object", "properties": {"page": {"type": "integer", "format": "int64", "title": "@gotags: validate:\"required\""}, "page_size": {"type": "integer", "format": "int64", "title": "@gotags: validate:\"required\""}}}, "pbQuestionTrainLogResp": {"type": "object", "properties": {"current_page": {"type": "integer", "format": "int64"}, "per_page": {"type": "integer", "format": "int64"}, "total": {"type": "string", "format": "int64"}, "data": {"type": "array", "items": {"$ref": "#/definitions/QuestionTrainLogRespTrainRecord"}}}, "title": "QuestionAddResp 训练列表resp"}, "pbQuestionTrainingReq": {"type": "object", "properties": {"project": {"type": "string", "title": "@gotags: validate:\"required\""}, "is_all": {"type": "boolean", "title": "true代表训练该游戏下的所有语种"}, "lang": {"type": "array", "items": {"type": "string"}}}, "title": "QuestionAddReq 工单知识库训练"}, "pbStrategyAddReq": {"type": "object", "properties": {"strategy_id": {"type": "string", "format": "uint64", "title": "策略id"}, "project": {"type": "string", "title": "@gotags: validate:\"required\""}, "strategy_name": {"type": "string", "title": "@gotags: validate:\"required\""}, "filter_server_lists": {"type": "string"}, "filter_pay_range_lists": {"type": "string", "title": "@gotags: validate:\"required\""}, "filter_castle_level_lists": {"type": "string"}, "type": {"type": "integer", "format": "int64", "title": "服务器是否为全部  @gotags: validate:\"required\""}}, "title": "StrategyAddReq 策略新增"}, "pbStrategyDelReq": {"type": "object", "properties": {"strategy_id": {"type": "string", "format": "int64", "title": "@gotags: validate:\"required\""}}, "title": "StrategyDelReq 策略删除"}, "pbStrategyEnabelReq": {"type": "object", "properties": {"strategy_id": {"type": "string", "format": "int64", "title": "@gotags: validate:\"required\""}, "enable": {"type": "integer", "format": "int64", "title": "启用 true false"}}, "title": "StrategyEnabelReq 策略禁/启用"}, "pbStrategyListReq": {"type": "object", "properties": {"project": {"type": "array", "items": {"type": "string"}, "title": "游戏"}, "strategy_name": {"type": "string", "title": "策略名称"}, "page": {"type": "integer", "format": "int64", "title": "@gotags: validate:\"required\""}, "page_size": {"type": "integer", "format": "int64", "title": "@gotags: validate:\"required\""}}, "title": "StrategyListReq 工单策略列表req"}, "pbStrategyListResp": {"type": "object", "properties": {"current_page": {"type": "integer", "format": "int64"}, "per_page": {"type": "integer", "format": "int64"}, "total": {"type": "integer", "format": "int64"}, "data": {"type": "array", "items": {"$ref": "#/definitions/StrategyListRespStrategyRecord"}}}, "title": "StrategyListResp 工单策略列表resp"}, "pbSurveyEditReq": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "问卷id @gotags: validate:\"required,gt=0\""}, "project": {"type": "string", "title": "游戏 @gotags: validate:\"required\""}, "push_cycle": {"$ref": "#/definitions/pbSurveyPushCycle", "title": "推送周期 @gotags: validate:\"required,gt=0\""}, "push_week": {"type": "string", "format": "uint64", "title": "推送时间-星期 @gotags: validate:\"required\""}, "push_time": {"type": "string", "format": "uint64", "title": "推送时间-小时"}, "push_time_web": {"type": "string", "title": "推送时间-前端传递 @gotags: validate:\"required\""}, "effective_time": {"type": "string", "title": "生效时间 @gotags: validate:\"required\""}, "expire_time": {"$ref": "#/definitions/pbSurveyEffective", "title": "有效期 @gotags: validate:\"required,gt=0\""}, "survey_titles": {"type": "object", "additionalProperties": {"type": "string"}, "title": "问卷标题 多语言"}, "push_contents": {"type": "object", "additionalProperties": {"type": "string"}, "title": "推送文案 多语言"}, "product_questions": {"type": "object", "additionalProperties": {"type": "string"}, "title": "产品题 多语言"}, "service_questions": {"type": "object", "additionalProperties": {"type": "string"}, "title": "服务题 多语言"}, "reasons": {"type": "object", "additionalProperties": {"type": "string"}, "title": "填写理由 多语言"}}, "title": "SurveyEditResp 调查问卷配置修改-请求参数"}, "pbSurveyEffective": {"type": "integer", "format": "int32", "enum": ["0", "3", "5", "7"], "default": "0", "description": "- 3: 三天\n - 5: 五天\n - 7: 七天", "title": "问卷有效期"}, "pbSurveyGenLinkReq": {"type": "object", "properties": {"project": {"type": "string", "title": "项目名称 @gotags: validate:\"required\""}, "uid": {"type": "string", "format": "int64"}, "lang": {"type": "string"}, "survey_id": {"type": "string", "format": "int64", "title": "问卷ID @gotags: validate:\"required,gt=0\""}, "is_public": {"type": "boolean", "title": "是否公开 浏览器打开 为 true"}, "dsc_user_id": {"type": "string", "title": "对应 dsc_user_id"}, "expire_at": {"type": "string", "format": "int64", "title": "有效的截止日期"}, "account_name": {"type": "string", "title": "客服账号 - 只有对外公开需要携带客服账号是才需要"}, "batch_id": {"type": "string", "format": "uint64", "title": "生成批次 ID"}}, "title": "SurveyGenLinkReq 问卷调查生成链接-请求参数"}, "pbSurveyGenLinkResp": {"type": "object", "properties": {"link": {"type": "string"}}, "title": "SurveyGenLinkResp 问卷调查生成链接-响应参数"}, "pbSurveyInfoReq": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "问卷id @gotags: validate:\"required\""}}, "title": "SurveyInfoReq 调查问卷配置详情-请求参数"}, "pbSurveyInfoResp": {"type": "object", "properties": {"data": {"$ref": "#/definitions/pbSurveyEditReq"}}, "title": "SurveyInfoResp 调查问卷配置详情-响应参数"}, "pbSurveyListReq": {"type": "object", "properties": {"page": {"type": "integer", "format": "int64", "title": "页码"}, "page_size": {"type": "integer", "format": "int64", "title": "页大小"}}, "title": "SurveyListReq 问卷调查配置列表-请求参数"}, "pbSurveyListResp": {"type": "object", "properties": {"current_page": {"type": "integer", "format": "int64"}, "per_page": {"type": "integer", "format": "int64"}, "total": {"type": "integer", "format": "int64"}, "data": {"type": "array", "items": {"$ref": "#/definitions/SurveyListRespSurveyInfo"}}}, "title": "SurveyListResp 问卷调查配置列表-响应结果"}, "pbSurveyPushCycle": {"type": "integer", "format": "int32", "enum": ["0", "1", "2", "3"], "default": "0", "description": "- 1: / 每周推送一次\n - 2: 每两周推送一次\n - 3: 每月推送一次", "title": "问卷推送周期"}, "pbSurveyQstType": {"type": "integer", "format": "int32", "enum": ["0", "1", "2"], "default": "0", "title": "- 1: 产品题\n - 2: 服务题"}, "pbSurveyStatType": {"type": "integer", "format": "int32", "enum": ["0", "1", "2", "3"], "default": "0", "description": "- 1: 日期\n - 2: 游戏\n - 3: 客服", "title": "报表维度"}, "pbSurveySubmitReq": {"type": "object", "properties": {"survey_token": {"type": "string", "title": "唯一标识 token @gotags: validate:\"required\""}, "ts_rank": {"type": "string", "format": "int64", "title": "当前时间戳 毫秒 ts_rank"}, "language": {"type": "string", "title": "语言"}, "uid": {"type": "string", "format": "int64", "title": "uid - 选填"}, "product_rating": {"type": "integer", "format": "int32", "title": "产品题-评星"}, "product_answer": {"type": "string", "title": "产品题-回复"}, "service_rating": {"type": "integer", "format": "int32", "title": "服务题-评星"}, "service_answer": {"type": "string", "title": "服务题-回复"}}, "title": "SurveySubmitReq 调查问卷配置修改-请求参数"}, "pbSurveyTemplateReq": {"type": "object", "properties": {"survey_token": {"type": "string", "title": "问卷Token @gotags: validate:\"required\""}, "lang": {"type": "string", "title": "语言"}, "ts_rank": {"type": "string", "format": "int64", "title": "当前时间戳 毫秒 ts_rank"}}, "title": "SurveyDetailReq 调查问卷模版-请求参数"}, "pbSurveyTemplateResp": {"type": "object", "properties": {"survey_id": {"type": "string", "format": "uint64", "title": "问卷配置id @gotags: validate:\"required\""}, "survey_token": {"type": "string", "title": "问卷 token 标识 ："}, "project": {"type": "string", "title": "游戏"}, "lang": {"type": "string", "title": "语言-为空时展示语言下拉框"}, "is_show_product": {"type": "boolean", "title": "是否展示产品题"}, "is_show_service": {"type": "boolean", "title": "是否展示服务题"}, "survey_titles": {"type": "string", "title": "问卷标题 多语言"}, "push_contents": {"type": "string", "title": "推送文案 多语言"}, "product_questions": {"type": "string", "title": "产品题 多语言"}, "service_questions": {"type": "string", "title": "服务题 多语言"}, "reasons": {"type": "string", "title": "填写理由 多语言"}, "can_submit": {"type": "boolean", "title": "是否可以填写"}, "need_uid": {"type": "boolean", "title": "是否需要填写 uid"}}, "title": "SurveyTemplateResp 调查问卷模版-响应参数"}, "pbTagLibID": {"type": "object", "properties": {"lib_id": {"type": "integer", "format": "int64", "title": "标签库ID @gotags: validate:\"required\""}}, "title": "TagLibIDReq 标签库ID请求参数"}, "pbTagLibInfoResp": {"type": "object", "properties": {"lib_id": {"type": "integer", "format": "int64", "title": "标签库ID"}, "lib_name": {"type": "string", "title": "标签库名称"}, "lib_file_url": {"type": "string", "title": "标签库描述"}, "projects": {"type": "array", "items": {"type": "string"}, "title": "标签数据 - 原始文件地址"}, "project": {"type": "array", "items": {"type": "string"}, "title": "@gotags: json:\"project\""}}, "title": "TagLibInfoResp 标签库信息"}, "pbTagLibListReq": {"type": "object", "properties": {"lib_name": {"type": "string", "title": "标签库名称"}, "lib_type": {"type": "integer", "format": "int64", "title": "标签类型 @gotags: validate:\"required,oneof=1 2 3\""}}, "title": "TagLibListReq 标签库列表搜索"}, "pbTagLibListResp": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/definitions/pbTagLibListRespTagLib"}}}, "title": "TagLibListResp 标签库列表响应结果"}, "pbTagLibListRespTagLib": {"type": "object", "properties": {"lib_id": {"type": "integer", "format": "int64", "title": "标签库ID"}, "lib_name": {"type": "string", "title": "标签库名称"}, "tag_upload_file": {"type": "string", "title": "标签数据 - 原始文件地址"}, "updated_at": {"type": "string", "title": "更新时间"}, "op": {"type": "string", "title": "更新用户"}, "enable": {"type": "boolean", "title": "状态"}, "projects": {"type": "array", "items": {"type": "string"}, "title": "项目列表 @gotags: gorm:\"-\""}}}, "pbTagLibOptsResp": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/definitions/pbTagLibOptsRespTagLib"}}}, "title": "TagLibOptsResp 标签组列表"}, "pbTagLibOptsRespTagLib": {"type": "object", "properties": {"lib_id": {"type": "integer", "format": "int64", "title": "标签ID"}, "lib_name": {"type": "string", "title": "标签名称"}}}, "pbTagLibSaveReq": {"type": "object", "properties": {"lib_id": {"type": "integer", "format": "int64", "title": "标签库ID"}, "lib_name": {"type": "string", "title": "标签库名称 @gotags: validate:\"required\""}, "lib_file_url": {"type": "string", "title": "标签数据-原始文件地址 @gotags: validate:\"required\""}, "projects": {"type": "array", "items": {"type": "string"}, "title": "关联游戏 @gotags: validate:\"required,gt=0\""}}, "title": "TagLibSaveReq 更新标签库请求参数"}, "pbTagOptsReq": {"type": "object", "properties": {"project_name": {"type": "string", "title": "项目名称-根据项目名称进行搜索 @gotags: validate:\"required\""}, "lib_id": {"type": "integer", "format": "int64", "title": "标签库ID - 根据标签库ID进行搜索"}, "lib_type": {"type": "integer", "format": "int64", "title": "标签类型 @gotags: validate:\"required,oneof=1 2 3\""}}}, "pbTagOptsResp": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/definitions/TagOptsRespTag"}}}, "title": "TagOptsResp 标签列表"}, "pbTeamConfigListReq": {"type": "object", "properties": {"team_name": {"type": "string"}, "team_member": {"type": "string"}, "page": {"type": "integer", "format": "int64"}, "page_size": {"type": "integer", "format": "int64"}, "isAll": {"type": "boolean"}}}, "pbTeamConfigListResp": {"type": "object", "properties": {"current_page": {"type": "integer", "format": "int64"}, "per_page": {"type": "integer", "format": "int64"}, "total": {"type": "integer", "format": "int64"}, "data": {"type": "array", "items": {"$ref": "#/definitions/pbTeamConfigListRespDetail"}}}}, "pbTeamConfigListRespDetail": {"type": "object", "properties": {"team_id": {"type": "string", "format": "int64"}, "team_name": {"type": "string"}, "team_member": {"type": "string"}, "update_time": {"type": "string"}, "updater": {"type": "string"}}}, "pbTicketAppraiseResp": {"type": "object", "properties": {"created_at": {"type": "string"}, "csi": {"type": "integer", "format": "int64"}, "recommendation_level": {"type": "integer", "format": "int64"}, "remark": {"type": "string"}}, "title": "TicketAppraiseResp 工单评价信息"}, "pbTicketBatchRemarkReq": {"type": "object", "properties": {"ticket_ids": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "工单ID @gotags: validate:\"required\""}, "content": {"type": "string", "title": "备注 @gotags: validate:\"required\""}}, "title": "TicketBatchRemarkReq 批量"}, "pbTicketCountReq": {"type": "object", "properties": {"project": {"type": "array", "items": {"type": "string"}, "title": "游戏权限"}, "stage": {"type": "array", "items": {"type": "integer", "format": "int64"}, "title": "工单状态"}, "acceptor": {"type": "array", "items": {"type": "string"}, "title": "受理人"}, "lang": {"type": "array", "items": {"type": "string"}, "title": "语言"}, "vip": {"type": "array", "items": {"type": "integer", "format": "int64"}, "title": "VIP"}, "priority": {"type": "integer", "format": "int64", "title": "升级区"}}, "title": "工单总量请求参数"}, "pbTicketCountResp": {"type": "object", "properties": {"pending_count": {"type": "string", "format": "uint64", "title": "待处理工单条数"}, "user_pending_count": {"type": "string", "format": "uint64", "title": "当前用户待处理工单量"}, "user_completed_count": {"type": "string", "format": "uint64", "title": "当前用户已完成工单量"}, "pending_cn_count": {"type": "string", "format": "uint64", "title": "待处理中文工单条数"}, "pending_vip_count": {"type": "string", "format": "uint64", "title": "待处理vip工单条数"}, "priority_count": {"type": "string", "format": "uint64", "title": "升级区工单条数"}}, "title": "工单总量响应值"}, "pbTicketDialogueResp": {"type": "object", "properties": {"detail": {"type": "string", "title": "对话内容 、工单 form"}, "created_at": {"type": "string"}, "operator": {"type": "string", "title": "操作员"}, "from_role": {"type": "integer", "format": "int64", "title": "回复角色类型  1:玩家 2:客服 3:系统',"}, "commu_type": {"type": "string", "title": "'消息类型： CommuTypeDialogue：对话消息；CommuTypeRemark：增加备注',"}, "is_ticket": {"type": "integer", "format": "int64", "title": "工单详情 1:是工单form详情; 2: 重开提交信息"}, "files": {"type": "string", "title": "重开单 - 图片/视频资源"}, "created_time": {"type": "string", "format": "uint64", "title": "时间 - 时间戳"}, "picture": {"type": "string", "title": "玩家上传的图片"}, "video": {"type": "string", "title": "玩家上传的视频"}}, "title": "工单表单 对话信息"}, "pbTicketHistResp": {"type": "object", "properties": {"ticket_id": {"type": "string", "format": "uint64", "title": "工单id"}, "type": {"type": "string", "title": "分组描述"}, "remark": {"type": "string", "title": "详情"}, "created_at": {"type": "string", "title": "时间"}}, "title": "工单 - 日志记录详情"}, "pbTicketIdReq": {"type": "object", "properties": {"ticket_id": {"type": "string", "format": "uint64", "title": "工单ID @gotags: validate:\"required\""}, "ticket_sys_type": {"$ref": "#/definitions/pbTicketSys", "title": "使用哪个工单系统"}, "page": {"type": "integer", "format": "int64", "title": "页码"}, "page_size": {"type": "integer", "format": "int64", "title": "页大小"}}, "title": "TicketId 工单ID"}, "pbTicketIsUserResp": {"type": "object", "properties": {"status": {"type": "integer", "format": "int64", "title": "客服在线状态  0:不在线 1:在线"}}, "title": "TicketIsUserResp 客服在线状态"}, "pbTicketPoolNewListReq": {"type": "object", "properties": {"project": {"type": "array", "items": {"type": "string"}, "title": "游戏"}, "created_at": {"type": "array", "items": {"type": "string"}, "title": "创建时间"}, "closed_at": {"type": "array", "items": {"type": "string"}, "title": "结单时间"}, "evaluate_at": {"type": "array", "items": {"type": "string"}, "title": "评价时间"}, "status": {"type": "array", "items": {"type": "integer", "format": "int64"}, "title": "工单状态  - 工单池"}, "scene": {"type": "array", "items": {"type": "integer", "format": "int64"}, "title": "工单来源"}, "channel": {"type": "array", "items": {"type": "string"}, "title": "渠道包"}, "label": {"type": "array", "items": {"type": "integer", "format": "int64"}, "title": "标签"}, "nps": {"type": "array", "items": {"type": "integer", "format": "int64"}, "title": "NPS"}, "csi": {"type": "array", "items": {"type": "integer", "format": "int64"}, "title": "工单评星"}, "language": {"type": "array", "items": {"type": "string"}, "title": "工单语言"}, "sid": {"type": "string", "title": "服务器"}, "ticket_id": {"type": "string", "format": "uint64", "title": "工单ID"}, "acceptor_type": {"$ref": "#/definitions/pbFilterEnum", "title": "处理人类型 1:空白 2:包含"}, "acceptor": {"type": "string", "title": "处理人 - 工单池"}, "remark": {"type": "string", "title": "备注"}, "creator_type": {"$ref": "#/definitions/pbCreatorType", "title": "提交人查询类型 1:玩家fpid 2：玩家姓名 3:客服账号，4：account_id 5:uid"}, "creator": {"type": "string", "title": "提交人查询值"}, "page": {"type": "integer", "format": "int64", "title": "页码"}, "page_size": {"type": "integer", "format": "int64", "title": "页大小"}, "cat_id": {"type": "array", "items": {"type": "integer", "format": "int64"}, "title": "问题分类"}, "system_label": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "field": {"type": "string", "title": "表单信息"}, "is_vip": {"type": "boolean", "title": "是否是 VIP: true：表示是 VIP"}, "is_upgrade": {"type": "boolean", "title": "是否是 升级单： true: 表示是 升级单"}, "tag_type": {"$ref": "#/definitions/pbFilterTagEnum", "title": "标签类型"}, "sort_by": {"$ref": "#/definitions/pbTkPoolSort", "title": "数据排序方式： 1：等待时长； 2：创建时间；3：充值金额"}, "order": {"type": "string", "title": "排序顺序，升序asc 逆序desc 默认asc"}, "search_type": {"type": "integer", "format": "int64", "title": "查询类型"}, "user_type": {"type": "array", "items": {"type": "integer", "format": "int64"}, "title": "用户类型"}, "svip": {"type": "string", "title": "0默认全部，1是，2不是"}, "vip_crm": {"type": "string", "title": "// 0默认全部，1是，2不是"}, "ticket_ids": {"type": "string", "title": "工单号s"}, "acceptors": {"type": "array", "items": {"type": "string"}, "title": "客服s"}, "team_ids": {"type": "array", "items": {"type": "string", "format": "int64"}, "title": "团队"}, "reopen_num": {"type": "string", "title": "重开次数"}, "upgrade_num": {"type": "string", "title": "升级次数"}, "packageId": {"type": "array", "items": {"type": "string"}, "title": "渠道号"}, "game_version": {"type": "array", "items": {"type": "string"}, "title": "游戏版本"}, "solve_type": {"type": "array", "items": {"type": "integer", "format": "int64"}, "title": "工单处理类型"}, "zone_vip_level": {"type": "array", "items": {"type": "string"}, "title": "私域R级参数"}, "pay_all": {"type": "array", "items": {"type": "string", "format": "int64"}, "title": "玩家累付金额"}}, "title": "TicketPoolNewListReq 新工单池请求参数"}, "pbTicketPoolNewListResp": {"type": "object", "properties": {"current_page": {"type": "integer", "format": "int64"}, "per_page": {"type": "integer", "format": "int64"}, "total": {"type": "integer", "format": "int64"}, "data": {"type": "array", "items": {"$ref": "#/definitions/TicketPoolNewListRespTicketPoolInfo"}}}, "title": "TicketPoolNewListResp 新工单池响应参数"}, "pbTicketPoolTopResp": {"type": "object", "properties": {"ticket_id": {"type": "string", "format": "uint64", "title": "工单ID"}, "acceptor": {"type": "string", "title": "- 处理人（当没有处理人时，此处显示：待接单） todo 代码处理"}, "nickname": {"type": "string", "title": "玩家昵称"}, "account_id": {"type": "string", "title": "Fpid"}, "uid": {"type": "string", "format": "uint64", "title": "<PERSON><PERSON>"}, "app_version": {"type": "string", "title": "游戏版本"}, "sid": {"type": "integer", "format": "int64", "title": "服务器"}, "project": {"type": "string", "title": "游戏"}, "priority": {"type": "integer", "format": "int64", "title": "升级单： 1：正常单；2：升级单"}, "status": {"$ref": "#/definitions/pbTkStage", "title": "当前工单流转状态"}, "csi": {"type": "integer", "format": "int64"}}, "title": "TicketPoolNewTopResp 顶部信息"}, "pbTicketPublicTagReq": {"type": "object", "properties": {"ticket_ids": {"type": "array", "items": {"type": "string", "format": "uint64"}, "title": "工单ID @gotags: validate:\"required\""}}, "title": "TicketPublicTagReq 工单公共标签"}, "pbTicketPublicTagResp": {"type": "object", "properties": {"tags": {"type": "array", "items": {"$ref": "#/definitions/pbTicketPublicTagRespTagInfo"}}}}, "pbTicketPublicTagRespTagInfo": {"type": "object", "properties": {"tag_id": {"type": "integer", "format": "int64", "title": "标签ID"}, "tag_name": {"type": "string", "title": "标签名称"}}}, "pbTicketRecordResp": {"type": "object", "properties": {"current_page": {"type": "integer", "format": "int64"}, "per_page": {"type": "integer", "format": "int64"}, "total": {"type": "integer", "format": "int64"}, "data": {"type": "array", "items": {"$ref": "#/definitions/TicketRecordRespRecord"}}}, "title": "TicketRecordResp 历史工单记录"}, "pbTicketRemarkReq": {"type": "object", "properties": {"ticket_id": {"type": "string", "format": "uint64", "title": "工单ID @gotags: validate:\"required,gte=1\""}, "content": {"type": "string", "title": "备注 @gotags: validate:\"required\""}}, "title": "TicketRemarkReq 添加工单备注"}, "pbTicketResponse": {"type": "object", "properties": {"top_info": {"$ref": "#/definitions/pbTicketPoolTopResp", "title": "工单详情 - 顶部信息"}, "user_info": {"$ref": "#/definitions/pbUserInfoResp", "title": "工单详情 - 基本信息"}, "record_info": {"$ref": "#/definitions/pbTicketRecordResp", "title": "工单详情 - 历史工单"}, "commu_info": {"type": "array", "items": {"$ref": "#/definitions/pbTicketDialogueResp"}, "title": "工单详情 - 对话"}, "history": {"type": "array", "items": {"$ref": "#/definitions/pbTicketHistResp"}, "title": "工单详情 - 分单日志"}, "ticket_appraise": {"$ref": "#/definitions/pbTicketAppraiseResp"}}, "title": "工单详情"}, "pbTicketRetaggingReq": {"type": "object", "properties": {"ticket_id": {"type": "string", "format": "uint64", "title": "工单ID @gotags: validate:\"required\""}, "label_id": {"type": "array", "items": {"type": "integer", "format": "int64"}, "title": "更新后的标签ID"}}, "title": "TicketTagReq 重新给现有的工单打标签"}, "pbTicketSys": {"type": "integer", "format": "int32", "enum": ["0", "1"], "default": "0", "description": "- 0: 默认老版本工单\n - 1: 新工单系统", "title": "TicketSys 使用哪个工单系统"}, "pbTicketSystemTag": {"type": "integer", "format": "int32", "enum": ["0", "10", "11", "12", "13"], "default": "0"}, "pbTicketTabEditReq": {"type": "object", "properties": {"id": {"type": "string", "format": "int64", "title": "@gotags: validate:\"required\""}, "tab_name": {"type": "string", "title": "@gotags: validate:\"required\""}, "public": {"type": "integer", "format": "int32", "title": "@gotags: validate:\"required,oneof=1 2\""}}}, "pbTicketTagRes": {"type": "object", "properties": {"ticket_id": {"type": "string", "format": "uint64"}, "label_id": {"type": "array", "items": {"type": "integer", "format": "int64"}}}}, "pbTicketTransferReq": {"type": "object", "properties": {"ticket_id": {"type": "string", "format": "uint64", "title": "工单ID @gotags: validate:\"required\""}, "op_type": {"$ref": "#/definitions/pbTkTransferOpType", "title": "操作分类：1:客服回复;2:回复&关单;3:拒单 @gotags: validate:\"required,gt=0\""}, "content": {"type": "string", "title": "回复内容"}, "is_sync_ai_elfin": {"type": "boolean", "title": "是否同步到ai精灵"}, "question": {"type": "string", "title": "问题"}, "answer": {"type": "string", "title": "答案"}}}, "pbTicketUpgradeReq": {"type": "object", "properties": {"ticket_id": {"type": "string", "format": "uint64", "title": "工单ID @gotags: validate:\"required,gt=0\""}, "upgrade": {"type": "integer", "format": "int64", "title": "工单升级/降级：1:降级；2:升级 @gotags: validate:\"required,oneof=1 2\""}}}, "pbTkAppraiseFeedbackReq": {"type": "object", "properties": {"ticket_id": {"type": "string", "format": "uint64", "title": "工单ID @gotags: validate:\"required\""}, "game_id": {"type": "integer", "format": "int64", "title": "Deprecated: 废弃，后续整合到fpx_app_id中\n游戏ID @gotags: validate:\"required_without=FpxAppId\""}, "fpx_app_id": {"type": "string", "title": "项目标识 @gotags: validate:\"required_without=GameId\""}, "lang": {"type": "string", "title": "lang 语言 @gotags: validate:\"required\""}, "fpid": {"type": "string", "format": "uint64", "title": "Deprecated: 废弃，fpid 后续整合到 account_id中"}, "uid": {"type": "string", "format": "uint64", "title": "uid 用户ID"}, "account_id": {"type": "string", "title": "account_id"}, "role_id": {"type": "string", "title": "role_id 角色ID"}, "sid": {"type": "string", "title": "sid 区服"}, "channel": {"type": "string", "title": "channel 渠道"}, "resolved": {"type": "integer", "format": "int64", "title": "是否解决  1：解决 2：未解决"}}, "title": "TkAppraiseReq 工单评价"}, "pbTkAppraiseReq": {"type": "object", "properties": {"ticket_id": {"type": "string", "format": "uint64", "title": "工单ID @gotags: validate:\"required\""}, "game_id": {"type": "integer", "format": "int64", "title": "Deprecated: 废弃，后续整合到fpx_app_id中\n游戏ID @gotags: validate:\"required_without=FpxAppId\""}, "fpx_app_id": {"type": "string", "title": "项目标识 @gotags: validate:\"required_without=GameId\""}, "lang": {"type": "string", "title": "lang 语言 @gotags: validate:\"required\""}, "fpid": {"type": "string", "format": "uint64", "title": "Deprecated: 废弃，fpid 后续整合到 account_id中"}, "uid": {"type": "string", "format": "uint64", "title": "uid 用户ID"}, "account_id": {"type": "string", "title": "account_id"}, "role_id": {"type": "string", "title": "role_id 角色ID"}, "sid": {"type": "string", "title": "sid 区服"}, "channel": {"type": "string", "title": "channel 渠道"}, "appraise": {"type": "integer", "format": "int64", "title": "评价等级 @gotags: validate:\"required\""}, "service_rating": {"type": "integer", "format": "int64", "title": "服务态度评分 - 老接口字段 - 废弃"}, "service_time_rating": {"type": "integer", "format": "int64", "title": "处理速度评分 - 老接口字段 - 废弃"}, "service_solution_rating": {"type": "integer", "format": "int64", "title": "处理方案评分 - 老接口字段 - 废弃"}, "recommendation_level": {"type": "integer", "format": "int64", "title": "推荐给别人的意愿程度 @gotags: validate:\"required_if=ServiceRating 0\""}, "remark": {"type": "string", "title": "评价内容"}}, "title": "TkAppraiseReq 评价"}, "pbTkCommunicateReq": {"type": "object", "properties": {"ticket_id": {"type": "string", "format": "uint64", "title": "工单ID @gotags: validate:\"required\""}, "game_id": {"type": "integer", "format": "int64", "title": "Deprecated: 废弃，后续整合到fpx_app_id中\n游戏ID @gotags: validate:\"required_without=FpxAppId\""}, "fpx_app_id": {"type": "string", "title": "项目标识 @gotags: validate:\"required_without=GameId\""}, "lang": {"type": "string", "title": "lang 语言 @gotags: validate:\"required\""}, "fpid": {"type": "string", "format": "uint64", "title": "Deprecated: 废弃，fpid 后续整合到 account_id中"}, "uid": {"type": "string", "format": "uint64", "title": "uid 用户ID"}, "account_id": {"type": "string", "title": "account_id"}, "role_id": {"type": "string", "title": "role_id 角色ID"}, "sid": {"type": "string", "title": "sid 区服"}, "channel": {"type": "string", "title": "channel 渠道"}, "content": {"type": "string", "title": "发送消息内容"}, "picture": {"type": "string", "title": "玩家上传的图片url链接"}, "video": {"type": "string", "title": "玩家上传的视频url链接"}, "scene": {"type": "integer", "format": "int64", "title": "场景"}, "uuid": {"type": "string", "title": "uuid 设备ID"}}, "title": "TkCommunicateReq 沟通发送消息"}, "pbTkCreateReq": {"type": "object", "properties": {"origin": {"type": "integer", "format": "int64", "title": "工单来源 1:玩家 2:VIP客服 3:普通客服 @gotags: validate:\"required\""}, "scene": {"type": "integer", "format": "int64", "title": "场景枚举"}, "game_id": {"type": "integer", "format": "int64", "title": "Deprecated: 废弃，后续整合到fpx_app_id中，统一使用`fpx_app_id`\n游戏ID @gotags: validate:\"required_without=FpxAppId\""}, "fpx_app_id": {"type": "string", "title": "项目标识 @gotags: validate:\"required_without=GameId\""}, "lang": {"type": "string", "title": "lang 语言 @gotags: validate:\"required\""}, "uuid": {"type": "string", "title": "uuid 设备ID"}, "fpid": {"type": "string", "format": "uint64", "title": "Deprecated: 废弃，fpid 后续整合到 account_id中，统一使用`account_id`"}, "uid": {"type": "string", "format": "uint64", "title": "uid 用户ID"}, "account_id": {"type": "string", "title": "account_id"}, "role_id": {"type": "string", "title": "role_id 角色ID"}, "sid": {"type": "string", "title": "sid 区服"}, "channel": {"type": "string", "title": "channel 渠道"}, "nickname": {"type": "string", "title": "nickname 昵称"}, "cat_id": {"type": "integer", "format": "int64", "title": "cat_id 问题类别ID @gotags: validate:\"required\""}, "fields": {"type": "string", "title": "fields 自定义字段  @gotags: validate:\"required\""}, "device_type": {"type": "string"}, "os": {"type": "string"}, "os_version": {"type": "string"}, "app_version": {"type": "string"}, "rom_gb": {"type": "string"}, "remain_rom": {"type": "string"}, "ram_mb": {"type": "string"}, "network_info": {"type": "string"}, "sdk_version": {"type": "string"}, "country": {"type": "string", "title": "国家"}, "total_pay": {"type": "number", "format": "double", "title": "历史充值总额"}, "label_id": {"type": "array", "items": {"type": "integer", "format": "int64"}}, "extend": {"type": "string"}, "pay_amount": {"type": "number", "format": "double", "title": "Deprecated: 废弃，后续使用 total_pay，统一使用`total_pay`"}, "country_code": {"type": "string", "title": "Deprecated: 废弃，后续 使用country，统一使用`country`"}, "related_cat": {"type": "integer", "format": "int64", "title": "重提工单ID"}, "from_ticket_id": {"type": "string", "format": "uint64"}, "subchannel": {"type": "string", "title": "subchannel 子渠道"}, "openid": {"type": "string", "title": "微信内小游戏 - openid"}, "process_session": {"type": "string", "title": "流程会话"}, "process_id": {"type": "string", "format": "int64", "title": "流程ID"}, "qfrom": {"type": "string", "title": "ss vip绿色通道标识"}, "zone_from": {"type": "string", "title": "私域来源请求标识"}, "trouble_account_id": {"type": "string", "title": "问题用户 account_id(前端不传)"}, "trouble_uid": {"type": "string", "format": "uint64", "title": "问题用户 uid"}, "packageId": {"type": "string", "title": "渠道号"}, "zone_token": {"type": "string", "title": "私域 zone_token"}, "conversation_id": {"type": "string", "title": "对话ID"}, "ticket_type": {"type": "boolean", "title": "是否为新版ai客服单"}, "is_invalid": {"type": "integer", "format": "int64", "title": "新版客服无效单判断逻辑 (仅为描述与建议这两个key，且结果为暂未收集到有效信息)"}, "zone_vip_level": {"type": "string", "format": "uint64", "title": "私域 R级参数"}, "priv_rights_use": {"type": "boolean", "title": "私域权益使用"}}, "title": "TkCreateReq 创建工单"}, "pbTkCreateResp": {"type": "object", "properties": {"ticket_id": {"type": "string", "format": "uint64", "title": "工单ID @gotags: validate:\"required\""}, "ticket_sys_type": {"$ref": "#/definitions/pbTicketSys", "title": "使用哪个工单系统"}}, "title": "TkCreateResp 工单ID"}, "pbTkDetailReq": {"type": "object", "properties": {"ticket_id": {"type": "string", "format": "uint64", "title": "项目标识 @gotags: validate:\"required\""}, "game_id": {"type": "integer", "format": "int64", "title": "Deprecated: 废弃，后续整合到fpx_app_id中\n游戏ID @gotags: validate:\"required_without=FpxAppId\""}, "fpx_app_id": {"type": "string", "title": "项目标识 @gotags: validate:\"required_without=GameId\""}, "lang": {"type": "string", "title": "lang 语言 @gotags: validate:\"required\""}, "uuid": {"type": "string"}, "fpid": {"type": "string", "format": "uint64", "title": "Deprecated: 废弃，fpid 后续整合到 account_id中"}, "uid": {"type": "string", "format": "uint64", "title": "uid 用户ID"}, "account_id": {"type": "string", "title": "account_id"}, "role_id": {"type": "string", "title": "role_id 角色ID"}, "sid": {"type": "string", "title": "sid 区服"}, "channel": {"type": "string", "title": "channel 渠道"}}, "title": "TkDetailReq 工单详情"}, "pbTkDetailResp": {"type": "object", "properties": {"ticket_id": {"type": "string", "format": "uint64", "title": "工单ID"}, "category": {"type": "string", "title": "问题分类"}, "filed": {"type": "string", "title": "表单信息"}, "transfer": {"type": "integer", "format": "int64", "title": "流转"}, "solution": {"type": "string", "title": "解决方案"}, "done": {"type": "boolean", "title": "状态 true:已结单"}, "closed": {"type": "integer", "format": "int64", "title": "关闭 0:未关闭 1:结单 2:关闭 3:超时关闭"}, "evidence": {"type": "boolean", "title": "补充举证 true:需举证 - 去补填按钮"}, "appraise": {"type": "boolean", "title": "评价 true：需评价"}, "replenish": {"type": "array", "items": {"$ref": "#/definitions/TkDetailRespReplenish"}, "title": "补充列表 -- 存在 空着 - 不需要"}, "commu": {"type": "array", "items": {"$ref": "#/definitions/TkDetailRespCommu"}, "title": "沟通记录"}, "csi": {"type": "integer", "format": "int64", "title": "评分"}, "label": {"type": "array", "items": {"type": "integer", "format": "int64"}, "title": "评价标签"}, "relate_type": {"type": "integer", "format": "int64", "title": "关联类型,1:表单 2:自动化流程"}, "reopen": {"type": "array", "items": {"$ref": "#/definitions/TkDetailRespReopen"}, "title": "重开信息"}, "reopen_disabled": {"type": "boolean", "title": "超过重开次数+不支持重开：true"}, "cat_id": {"type": "integer", "format": "int64", "title": "问题类别ID"}, "ticket_sys_type": {"$ref": "#/definitions/pbTicketSys", "title": "使用哪个工单系统"}, "overtime_reply": {"type": "string", "title": "超时回复文案"}, "overtime": {"type": "string", "title": "超时时间"}, "is_chat_ticket": {"type": "integer", "format": "int64", "title": "是否为新版ai客服 - 0为不是，1为是"}}, "title": "TicketDetailResp 工单详细信息"}, "pbTkMineReq": {"type": "object", "properties": {"scene": {"type": "integer", "format": "int64", "title": "场景枚举"}, "game_id": {"type": "integer", "format": "int64", "title": "Deprecated: 废弃，后续整合到fpx_app_id中\n游戏ID @gotags: validate:\"required_without=FpxAppId\""}, "fpx_app_id": {"type": "string", "title": "项目标识 @gotags: validate:\"required_without=GameId\""}, "lang": {"type": "string", "title": "lang 语言 @gotags: validate:\"required\""}, "uuid": {"type": "string", "title": "uuid 设备ID"}, "fpid": {"type": "string", "format": "uint64", "title": "Deprecated: 废弃，fpid 后续整合到 account_id中"}, "uid": {"type": "string", "format": "uint64", "title": "uid 用户ID"}, "account_id": {"type": "string", "title": "account_id"}, "role_id": {"type": "string", "title": "role_id 角色ID"}, "sid": {"type": "string", "title": "sid 区服"}, "channel": {"type": "string", "title": "channel 渠道"}}, "title": "TkMineReq 我的工单列表"}, "pbTkMineResp": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/definitions/TkMineRespTkList"}}}, "title": "TkMineResp 工单历史 - 老工单字段及定义"}, "pbTkPoolSort": {"type": "integer", "format": "int32", "enum": ["0", "1", "2", "3"], "default": "0", "description": "- 1: 等待时长 - 默认\n - 2: 创建时间\n - 3: 充值金额", "title": "工单列表排序方式"}, "pbTkProgress": {"type": "integer", "format": "int32", "enum": ["0", "1", "2", "3", "4", "5", "6"], "default": "0", "description": "- 1: 1：已完成\n - 2: 2：待补充\n - 3: 3：处理中\n - 4: 4：超时关闭\n - 5: 5: 重新打开\n - 6: 6: 拒单关闭", "title": "TkProgress 当前状态"}, "pbTkReplenishReq": {"type": "object", "properties": {"ticket_id": {"type": "string", "format": "uint64", "title": "工单ID @gotags: validate:\"required\""}, "content": {"type": "string", "title": "工单内容 @gotags: validate:\"required\""}, "files": {"type": "string", "title": "工单附件 @gotags: validate:\"required\""}, "game_id": {"type": "integer", "format": "int64", "title": "Deprecated: 废弃，后续整合到fpx_app_id中\n游戏ID @gotags: validate:\"required_without=FpxAppId\""}, "fpx_app_id": {"type": "string", "title": "项目标识 @gotags: validate:\"required_without=GameId\""}, "lang": {"type": "string", "title": "lang 语言 @gotags: validate:\"required\""}, "uuid": {"type": "string", "title": "uuid 设备ID"}, "fpid": {"type": "string", "format": "uint64", "title": "Deprecated: 废弃，fpid 后续整合到 account_id中"}, "uid": {"type": "string", "format": "uint64", "title": "uid 用户ID"}, "account_id": {"type": "string", "title": "account_id"}, "role_id": {"type": "string", "title": "role_id 角色ID"}, "sid": {"type": "string", "title": "sid 区服"}, "channel": {"type": "string", "title": "channel 渠道"}, "nickname": {"type": "string", "title": "nickname 昵称"}, "subchannel": {"type": "string", "title": "subchannel 子渠道"}}, "title": "TkReplenishReq 补填信息"}, "pbTkStage": {"type": "integer", "format": "int32", "enum": ["0", "1", "2", "3", "4", "5", "6", "7", "8"], "default": "0", "description": "- 1: 工单状态 - New 待接单 - 玩家提交工单&未分配\n - 2: 工单状态 - New For Agent 待处理 - 已分配/指派客服\n - 3: 工单状态 - Agent Replied 处理中 - 待玩家回复 - 客服已回复待玩家回复\n - 4: 工单状态 - Waiting For Agent 处理中 - 玩家已回复 待客服再次回复\n - 5: 工单状态 - Agent Resolved 超时关闭 - 玩家3天内未回复，超时关闭\n - 6: 工单状态 - Reopen 重开 - 玩家重新打开工单\n - 7: 工单状态 - Rejected 拒单关闭 - 客服点击\"拒单\"，结束会话\n - 8: 工单状态 - Completed 已完成 - 客服回复&关单", "title": "TkStage 节点流转"}, "pbTkStatus": {"type": "integer", "format": "int32", "enum": ["0", "1", "2", "3"], "default": "0", "description": "- 0: 未知状态\n - 1: 待接入工单\n - 2: 处理中工单\n - 3: 已完成", "title": "TkStatus 工单状态定义"}, "pbTkTransferOpType": {"type": "integer", "format": "int32", "enum": ["0", "1", "2", "3"], "default": "0", "title": "- 1: 客服回复\n - 2: 回复&关单\n - 3: 拒单"}, "pbUpdateConversationRequest": {"type": "object", "properties": {"conversation_id": {"type": "string"}, "user_id": {"type": "string"}, "cat_id": {"type": "integer", "format": "int32"}, "ticket_id": {"type": "string", "format": "int64"}, "history": {"type": "array", "items": {"$ref": "#/definitions/pbHistory"}}, "question_get_list": {"type": "array", "items": {"$ref": "#/definitions/pbQuestionGet"}}}, "title": "更新对话请求"}, "pbUpdateConversationResponse": {"type": "object", "properties": {"uuid": {"type": "string"}, "conversation_id": {"type": "string"}, "timestamp": {"type": "string", "format": "int64"}}, "title": "更新对话响应"}, "pbUpdateDCSBotConfigWelcomeMessageReq": {"type": "object", "properties": {"id": {"type": "string", "format": "uint64", "title": "机器人ID"}, "welcome_message": {"type": "string", "title": "欢迎消息"}}, "title": "UpdateDCSBotConfigWelcomeMessageReq 更新DC机器人配置欢迎消息"}, "pbUserAssignTicketAddReq": {"type": "object", "properties": {"upper_limit": {"type": "integer", "format": "int64"}, "lang": {"type": "array", "items": {"type": "string"}, "title": "@gotags: validate:\"required\""}, "user": {"type": "array", "items": {"type": "string"}, "title": "@gotags: validate:\"required\""}, "detail": {"type": "array", "items": {"$ref": "#/definitions/pbGameCategory"}}}, "title": "新增用户分单配置"}, "pbUserAssignTicketDelReq": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "title": "@gotags: validate:\"required\""}, "user": {"type": "string", "title": "@gotags: validate:\"required\""}}, "title": "删除用户分单配置"}, "pbUserAssignTicketEditReq": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "title": "@gotags: validate:\"required\""}, "upper_limit": {"type": "integer", "format": "int64"}, "detail": {"type": "array", "items": {"$ref": "#/definitions/pbGameCategory"}}, "lang": {"type": "array", "items": {"type": "string"}}}, "title": "修改用户分单配置"}, "pbUserAssignTicketInfoReq": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}}, "title": "用户分单配置详情"}, "pbUserAssignTicketListReq": {"type": "object", "properties": {"account": {"type": "string", "title": "用户账号"}, "page": {"type": "integer", "format": "int64", "title": "页码"}, "page_size": {"type": "integer", "format": "int64", "title": "页大小"}}, "title": "UserAssignTicketListReq 用户分单列表请求参数"}, "pbUserAssignTicketListResp": {"type": "object", "properties": {"current_page": {"type": "integer", "format": "int64", "title": "当前页"}, "per_page": {"type": "integer", "format": "int64", "title": "页大小"}, "total": {"type": "integer", "format": "int64", "title": "总数"}, "data": {"type": "array", "items": {"$ref": "#/definitions/pbUserAssignTicketListRespDetail"}, "title": "数据列表"}}, "title": "UserAssignTicketListResp 查询列表响应结果"}, "pbUserAssignTicketListRespDetail": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64", "title": "用户的id"}, "account": {"type": "string", "title": "用户账号"}, "game": {"type": "array", "items": {"type": "string"}, "title": "游戏列表"}, "operator": {"type": "string", "title": "操作人"}, "lang": {"type": "array", "items": {"type": "string"}, "title": "语言"}, "upper_limit": {"type": "integer", "format": "int64", "title": "用户接单上限"}, "updated_at": {"type": "string", "title": "更新时间"}, "detail": {"type": "array", "items": {"$ref": "#/definitions/pbGameCategory"}, "title": "游戏、问题分类和系统标签"}}}, "pbUserInfoResp": {"type": "object", "properties": {"tag_name": {"type": "array", "items": {"type": "string"}, "title": "标签"}, "category": {"type": "string", "title": "问题类型"}, "created_at": {"type": "string", "title": "创建时间"}, "channel": {"type": "string", "title": "渠道包"}, "device_type": {"type": "string", "title": "设备型号"}, "rom_gb": {"type": "number", "format": "double", "title": "存储总量"}, "remain_rom": {"type": "number", "format": "double", "title": "存储剩余总量"}, "recharge": {"type": "number", "format": "double", "title": "充值金额"}, "lang": {"type": "string", "title": "玩家语言"}, "country": {"type": "string", "title": "国家"}, "os_version": {"type": "string", "title": "系统版本"}, "ip": {"type": "string", "title": "IP地址"}, "packageId": {"type": "string", "title": "渠道号"}, "zone_vip_level": {"type": "string", "title": "私域R级"}}, "title": "UserInfoResp 基础信息"}, "pbUserListResp": {"type": "object", "properties": {"account": {"type": "string", "title": "团队ID"}}}, "pbUserRole": {"type": "integer", "format": "int32", "enum": ["0", "1", "2", "3", "4"], "default": "0", "description": "- 1: 玩家端\n - 2: 服务端\n - 3: 系统操作", "title": "UserRole 角色"}, "protobufAny": {"type": "object", "properties": {"@type": {"type": "string", "description": "A URL/resource name that uniquely identifies the type of the serialized\nprotocol buffer message. This string must contain at least\none \"/\" character. The last segment of the URL's path must represent\nthe fully qualified name of the type (as in\n`path/google.protobuf.Duration`). The name should be in a canonical form\n(e.g., leading \".\" is not accepted).\n\nIn practice, teams usually precompile into the binary all types that they\nexpect it to use in the context of Any. However, for URLs which use the\nscheme `http`, `https`, or no scheme, one can optionally set up a type\nserver that maps type URLs to message definitions as follows:\n\n* If no scheme is provided, `https` is assumed.\n* An HTTP GET on the URL must yield a [google.protobuf.Type][]\n  value in binary format, or produce an error.\n* Applications are allowed to cache lookup results based on the\n  URL, or have them precompiled into a binary to avoid any\n  lookup. Therefore, binary compatibility needs to be preserved\n  on changes to types. (Use versioned type names to manage\n  breaking changes.)\n\nNote: this functionality is not currently available in the official\nprotobuf release, and it is not used for type URLs beginning with\ntype.googleapis.com. As of May 2023, there are no widely used type server\nimplementations and no plans to implement one.\n\nSchemes other than `http`, `https` (or the empty scheme) might be\nused with implementation specific semantics."}}, "additionalProperties": {}, "description": "`Any` contains an arbitrary serialized protocol buffer message along with a\nURL that describes the type of the serialized message.\n\nProtobuf library provides support to pack/unpack Any values in the form\nof utility functions or additional generated methods of the Any type.\n\nExample 1: Pack and unpack a message in C++.\n\n    Foo foo = ...;\n    Any any;\n    any.PackFrom(foo);\n    ...\n    if (any.UnpackTo(&foo)) {\n      ...\n    }\n\nExample 2: Pack and unpack a message in Java.\n\n    Foo foo = ...;\n    Any any = Any.pack(foo);\n    ...\n    if (any.is(Foo.class)) {\n      foo = any.unpack(Foo.class);\n    }\n    // or ...\n    if (any.isSameTypeAs(Foo.getDefaultInstance())) {\n      foo = any.unpack(Foo.getDefaultInstance());\n    }\n\n Example 3: Pack and unpack a message in Python.\n\n    foo = Foo(...)\n    any = Any()\n    any.Pack(foo)\n    ...\n    if any.Is(Foo.DESCRIPTOR):\n      any.Unpack(foo)\n      ...\n\n Example 4: Pack and unpack a message in Go\n\n     foo := &pb.Foo{...}\n     any, err := anypb.New(foo)\n     if err != nil {\n       ...\n     }\n     ...\n     foo := &pb.Foo{}\n     if err := any.UnmarshalTo(foo); err != nil {\n       ...\n     }\n\nThe pack methods provided by protobuf library will by default use\n'type.googleapis.com/full.type.name' as the type URL and the unpack\nmethods only use the fully qualified type name after the last '/'\nin the type URL, for example \"foo.bar.com/x/y.z\" will yield type\nname \"y.z\".\n\nJSON\n====\nThe JSON representation of an `Any` value uses the regular\nrepresentation of the deserialized, embedded message, with an\nadditional field `@type` which contains the type URL. Example:\n\n    package google.profile;\n    message Person {\n      string first_name = 1;\n      string last_name = 2;\n    }\n\n    {\n      \"@type\": \"type.googleapis.com/google.profile.Person\",\n      \"firstName\": <string>,\n      \"lastName\": <string>\n    }\n\nIf the embedded message type is well-known and has a custom JSON\nrepresentation, that representation will be embedded adding a field\n`value` which holds the custom JSON in addition to the `@type`\nfield. Example (for message [google.protobuf.Duration][]):\n\n    {\n      \"@type\": \"type.googleapis.com/google.protobuf.Duration\",\n      \"value\": \"1.212s\"\n    }"}}}