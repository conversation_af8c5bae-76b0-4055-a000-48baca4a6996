## 修改索引类型:

```
1. 创建临时索引； 
2. 用reindex API将数据从旧索引迁移到新索引； 
3. 删除旧索引；
4. 重新创建索引；
5. 用reindex API将数据从临时索引迁移到新创建的索引
```

1. 创建 Es 索引

```curl
curl -X PUT "http://10.13.200.205:9200/lin_new_ticket_ts" -H 'Content-Type: application/json' -d'
{
  "settings": {
    "number_of_shards": "1",
    "number_of_replicas": "1"
  },
  "mappings": {
    "properties": {
        xxxxxxx
  }
}
'
```

2. 用reindex API将数据从旧索引迁移到新索引

```curl 
curl -X POST "http://10.13.200.205:9200/_reindex" -H 'Content-Type: application/json' -d'
{
  "source": {
    "index": "fp_ops_new_ticket_test"
  },
  "dest": {
    "index": "lin_new_ticket_ts"
  }
}
'

```

3. 删除旧索引 -- (fp_ops_new_tickets_history_test)

```curl
curl -X DELETE "http://10.13.200.205:9200/fp_ops_new_ticket_test"
```

4. 重新创建索引 fp_ops_new_ticket_test

```curl

curl -X PUT "http://10.13.200.205:9200/fp_ops_new_ticket_test" -H 'Content-Type: application/json' -d'
{
  "settings": {
    "number_of_shards": "1",
    "number_of_replicas": "1"
  },
  "mappings": {
    "properties": {
        xxxxxxx
    }
  }
}
'
```

5. 用reindex API将数据从临时索引迁移到新创建的索引

```curl 
curl -X POST "http://10.13.200.205:9200/_reindex" -H 'Content-Type: application/json' -d'
{
  "source": {
    "index": "lin_new_ticket_ts"
  },
  "dest": {
    "index": "fp_ops_new_ticket_test"
  }
}
'

```