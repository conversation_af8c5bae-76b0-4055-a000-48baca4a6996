{"settings": {"number_of_shards": "5", "number_of_replicas": "1"}, "mappings": {"properties": {"bot_id": {"type": "keyword"}, "channel_id": {"type": "keyword"}, "created_at": {"type": "long"}, "dsc_user_id": {"type": "keyword"}, "dsc_user_name": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "examine_dsc_id": {"type": "long"}, "examine_fields": {"type": "nested", "properties": {"field_name": {"type": "keyword"}, "field_type": {"type": "byte"}, "field_value": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}}}, "final_desc": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "final_modified_at": {"type": "long"}, "final_reason": {"type": "keyword"}, "final_result": {"type": "short"}, "final_result_modify_comment": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "final_score": {"type": "short"}, "finished_at": {"type": "long"}, "finished_modified_at": {"type": "long"}, "gen_account_id": {"type": "keyword"}, "gen_last_login": {"type": "long"}, "gen_pay_all": {"type": "long"}, "gen_pay_last_thirty_days": {"type": "long"}, "gen_reply_type": {"type": "long"}, "gen_sid": {"type": "keyword"}, "gen_tags": {"type": "long"}, "gen_uid": {"type": "long"}, "gen_vip_level": {"type": "short"}, "gen_vip_state": {"type": "short"}, "inspector": {"type": "keyword"}, "notice_account": {"type": "keyword"}, "project": {"type": "keyword"}, "relate_account": {"type": "keyword"}, "related_account": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "status": {"type": "short"}, "task_id": {"type": "long"}, "tpl_id": {"type": "long"}, "updated_at": {"type": "long"}}}}