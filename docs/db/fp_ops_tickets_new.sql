-- MySQL dump 10.13  Distrib 8.1.0, for macos12.6 (x86_64)
--
-- Host: all-service-offline.cluster-c6h1gwzealou.us-west-2.rds.amazonaws.com    Database: fp_ops_ticket_new_test
-- ------------------------------------------------------
-- Server version	5.7.12

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;
SET @MYSQLDUMP_TEMP_LOG_BIN = @@SESSION.SQL_LOG_BIN;
SET @@SESSION.SQL_LOG_BIN= 0;

--
-- GTID state at the beginning of the backup 
--

SET @@GLOBAL.GTID_PURGED=/*!80000 '+'*/ '';

--
-- Table structure for table `fp_ops_category`
--

DROP TABLE IF EXISTS `fp_ops_category`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `fp_ops_category` (
  `cat_id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `project` varchar(64) NOT NULL DEFAULT '' COMMENT '所属项目',
  `scope` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '范围 0:默认 1:内部分类',
  `category` varchar(64) NOT NULL DEFAULT '' COMMENT '分类名称',
  `one_level` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '一级分类',
  `second_level` int(11) NOT NULL DEFAULT '0' COMMENT '二级分类',
  `level` tinyint(1) NOT NULL DEFAULT '1' COMMENT '分类层级',
  `relate_type` tinyint(1) NOT NULL DEFAULT '1' COMMENT '关联类型,1:表单 2:流程',
  `tpl_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '模版ID',
  `group_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '技能组ID',
  `enable` tinyint(1) NOT NULL DEFAULT '1' COMMENT '启用 0:false 1:true',
  `operator` varchar(64) NOT NULL DEFAULT '' COMMENT 'op',
  `is_deleted` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '删除',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`cat_id`) USING BTREE,
  KEY `idx_tpl_id` (`tpl_id`) USING BTREE,
  KEY `idx_level` (`one_level`,`second_level`,`level`) USING BTREE,
  KEY `idx_prj_cat` (`project`,`category`,`level`,`is_deleted`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1793 DEFAULT CHARSET=utf8mb4 COMMENT='问题分类配置表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `fp_ops_category`
--

--
-- Table structure for table `fp_ops_category_lang`
--

DROP TABLE IF EXISTS `fp_ops_category_lang`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `fp_ops_category_lang` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `cat_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '类别ID',
  `lang` varchar(8) NOT NULL DEFAULT '' COMMENT '语言',
  `category` varchar(64) NOT NULL DEFAULT '' COMMENT '分类名称',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `idx_cat_lang` (`cat_id`,`lang`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=51911 DEFAULT CHARSET=utf8mb4 COMMENT='问题分类配置多语言表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `fp_ops_category_lang`
--

--
-- Table structure for table `fp_ops_category_map`
--

DROP TABLE IF EXISTS `fp_ops_category_map`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `fp_ops_category_map` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `cat_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '类别ID',
  `object_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '对象ID',
  `from` tinyint(4) unsigned NOT NULL DEFAULT '0' COMMENT '业务id',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `idx_cat_obj` (`cat_id`,`object_id`,`from`),
  KEY `idx_obj` (`object_id`,`from`)
) ENGINE=InnoDB AUTO_INCREMENT=28100 DEFAULT CHARSET=utf8mb4 COMMENT='问题分类配置多语言表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `fp_ops_category_map`
--

--
-- Table structure for table `fp_ops_dict`
--

DROP TABLE IF EXISTS `fp_ops_dict`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `fp_ops_dict` (
  `dict_id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `dict_name` varchar(64) NOT NULL DEFAULT '' COMMENT '字段名称',
  `dict_key` varchar(32) NOT NULL DEFAULT '' COMMENT 'SDK对应key',
  `enable` tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '启用 0:false 1:true',
  `operator` varchar(64) NOT NULL DEFAULT '' COMMENT '更新人员',
  `created_at` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  `updated_at` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`dict_id`) USING BTREE,
  UNIQUE KEY `idx_dict_key` (`dict_name`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COMMENT='表单映射表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `fp_ops_dict`
--

LOCK TABLES `fp_ops_dict` WRITE;
/*!40000 ALTER TABLE `fp_ops_dict` DISABLE KEYS */;
INSERT INTO `fp_ops_dict` VALUES (1,'玩家昵称','role_name',1,'yanyun.geng',1669003442,1669883475),(2,'UID','uid',1,'yanyun.geng',1669003484,1669883408),(3,'roleld','role_id',1,'yanyun.geng',1669883426,1669883487),(4,'区服','sid',1,'yanyun.geng',1669883447,1669883447),(5,'游戏版本','app_version',1,'yanyun.geng',1669883463,1669883463);
/*!40000 ALTER TABLE `fp_ops_dict` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `fp_ops_dispatch_config`
--

DROP TABLE IF EXISTS `fp_ops_dispatch_config`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `fp_ops_dispatch_config` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `max_orders_per_operator` int(11) NOT NULL DEFAULT '0' COMMENT '客服可接单上限',
  `dispatch_type` tinyint(4) NOT NULL DEFAULT '1' COMMENT '分单类型（1表示系统分单，2表示员工接单）',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_max_orders_per_operator` (`max_orders_per_operator`),
  KEY `idx_dispatch_type` (`dispatch_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分单配置表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `fp_ops_dispatch_config`
--

LOCK TABLES `fp_ops_dispatch_config` WRITE;
/*!40000 ALTER TABLE `fp_ops_dispatch_config` DISABLE KEYS */;
/*!40000 ALTER TABLE `fp_ops_dispatch_config` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `fp_ops_group`
--

DROP TABLE IF EXISTS `fp_ops_group`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `fp_ops_group` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `group_desc` varchar(64) NOT NULL DEFAULT '' COMMENT '团队描述',
  `upper_limit` int(4) NOT NULL DEFAULT '0' COMMENT '接单上线',
  `enable` tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '启用 0:false 1:true',
  `operator` varchar(64) NOT NULL DEFAULT '' COMMENT 'op',
  `created_at` datetime NOT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb4 COMMENT='技能组表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `fp_ops_group`
--

--
-- Table structure for table `fp_ops_group_cat`
--

DROP TABLE IF EXISTS `fp_ops_group_cat`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `fp_ops_group_cat` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `group_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '技能组id',
  `game` varchar(64) NOT NULL COMMENT '项目(游戏)',
  `language` varchar(16) NOT NULL COMMENT '语言',
  `categories` varchar(10240) NOT NULL COMMENT '问题类别',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_gp` (`group_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='技能组项目问题权限表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `fp_ops_group_cat`
--

LOCK TABLES `fp_ops_group_cat` WRITE;
/*!40000 ALTER TABLE `fp_ops_group_cat` DISABLE KEYS */;
/*!40000 ALTER TABLE `fp_ops_group_cat` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `fp_ops_group_user`
--

DROP TABLE IF EXISTS `fp_ops_group_user`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `fp_ops_group_user` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `group_id` int(11) unsigned NOT NULL COMMENT '技能组ID',
  `enable` tinyint(1) NOT NULL DEFAULT '1' COMMENT '''启用 0:false 1:true'',',
  `user` varchar(64) NOT NULL DEFAULT '' COMMENT '用户名称',
  `created_at` datetime NOT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  `upper_limit` int(4) NOT NULL DEFAULT '50' COMMENT '接单上线',
  `is_login` tinyint(1) NOT NULL DEFAULT '2' COMMENT '是否登陆：2:离线, 1:在线',
  `game` varchar(1024) NOT NULL COMMENT '项目(游戏)',
  `language` varchar(512) NOT NULL COMMENT '语言',
  `operator` varchar(64) NOT NULL DEFAULT '' COMMENT 'op',
  `last_alloc_tk_at` bigint(20) NOT NULL DEFAULT '0' COMMENT '最后一次分单时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `idx_gp_user` (`group_id`,`user`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8mb4 COMMENT='技能组用户表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `fp_ops_group_user`
--

LOCK TABLES `fp_ops_group_user` WRITE;
/*!40000 ALTER TABLE `fp_ops_group_user` DISABLE KEYS */;
/*!40000 ALTER TABLE `fp_ops_group_user` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `fp_ops_language`
--

DROP TABLE IF EXISTS `fp_ops_language`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `fp_ops_language` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `code` varchar(8) NOT NULL COMMENT '标识',
  `name` varchar(16) NOT NULL COMMENT '名称',
  `enable` tinyint(1) NOT NULL COMMENT '启用禁用',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_code` (`code`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=22 DEFAULT CHARSET=utf8mb4 COMMENT='语言表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `fp_ops_language`
--

LOCK TABLES `fp_ops_language` WRITE;
/*!40000 ALTER TABLE `fp_ops_language` DISABLE KEYS */;
INSERT INTO `fp_ops_language` VALUES (1,'en','英语',1),(2,'ru','俄语',1),(3,'de','德语',1),(4,'fr','法语',1),(5,'ko','韩语',1),(6,'ja','日语',1),(7,'it','意语',1),(8,'zh-cn','简体中文',1),(9,'zh-tw','繁体中文',1),(10,'pl','波兰语',1),(11,'pt','葡语',1),(12,'nl','荷兰语',1),(13,'id','印尼语',1),(14,'th','泰语',1),(15,'no','挪威语',0),(16,'sv','瑞典语',1),(17,'es','西班牙语',1),(18,'tr','土耳其语',1),(19,'ar','阿拉伯语',1),(20,'vi','越南语',1),(21,'my','马来语',1);
/*!40000 ALTER TABLE `fp_ops_language` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `fp_ops_project_cfg`
--

DROP TABLE IF EXISTS `fp_ops_project_cfg`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `fp_ops_project_cfg` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `project` varchar(64) NOT NULL DEFAULT '' COMMENT '所属项目',
  `gateway_name` varchar(64) NOT NULL DEFAULT '' COMMENT '网关游戏名称',
  `ticket_name` varchar(64) NOT NULL DEFAULT '' COMMENT '工单游戏名称',
  `enable` tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '0:禁用 1:启用',
  `operator` varchar(64) NOT NULL DEFAULT '' COMMENT '操作人员',
  `created_at` datetime DEFAULT NULL COMMENT '更新时间',
  `updated_at` datetime DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_project` (`project`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=24 DEFAULT CHARSET=utf8mb4 COMMENT='工单项目配置表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `fp_ops_project_cfg`
--

LOCK TABLES `fp_ops_project_cfg` WRITE;
/*!40000 ALTER TABLE `fp_ops_project_cfg` DISABLE KEYS */;
/*!40000 ALTER TABLE `fp_ops_project_cfg` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `fp_ops_tags`
--

DROP TABLE IF EXISTS `fp_ops_tags`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `fp_ops_tags` (
  `tag_id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `lib_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '标签库ID',
  `tag_name` varchar(64) NOT NULL DEFAULT '' COMMENT '标签名称',
  `parent_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '父级ID',
  `lft` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '左值',
  `rgt` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '右值',
  `level` int(4) unsigned NOT NULL DEFAULT '0' COMMENT '层级',
  `enable` tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '启用 0:false 1:true',
  `operator` varchar(64) NOT NULL DEFAULT '' COMMENT '更新人员',
  `created_at` bigint(20) NOT NULL DEFAULT '0' COMMENT '创建时间',
  `updated_at` bigint(20) NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`tag_id`) USING BTREE,
  KEY `idx_node` (`lib_id`,`lft`,`rgt`) USING BTREE,
  KEY `idx_lib_id` (`lib_id`,`tag_name`,`level`,`parent_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=10562 DEFAULT CHARSET=utf8mb4 COMMENT='标签表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `fp_ops_tags`
--

LOCK TABLES `fp_ops_tags` WRITE;
/*!40000 ALTER TABLE `fp_ops_tags` DISABLE KEYS */;
/*!40000 ALTER TABLE `fp_ops_tags` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `fp_ops_tags_lib`
--

DROP TABLE IF EXISTS `fp_ops_tags_lib`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `fp_ops_tags_lib` (
  `lib_id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '标签库ID',
  `lib_name` varchar(64) NOT NULL DEFAULT '' COMMENT '名称',
  `tag_upload_file` varchar(512) NOT NULL DEFAULT '' COMMENT '上传文件原始地址',
  `operator` varchar(64) NOT NULL DEFAULT '' COMMENT '操作人员',
  `enable` tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '启用 0:false 1:true',
  `created_at` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  `updated_at` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`lib_id`) USING BTREE,
  KEY `idx_lib_name` (`lib_name`,`operator`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=11 DEFAULT CHARSET=utf8mb4 COMMENT='标签组表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `fp_ops_tags_lib`
--

LOCK TABLES `fp_ops_tags_lib` WRITE;
/*!40000 ALTER TABLE `fp_ops_tags_lib` DISABLE KEYS */;
/*!40000 ALTER TABLE `fp_ops_tags_lib` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `fp_ops_tags_lib_gm`
--

DROP TABLE IF EXISTS `fp_ops_tags_lib_gm`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `fp_ops_tags_lib_gm` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `project` varchar(64) NOT NULL DEFAULT '' COMMENT '描述',
  `lib_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '标签库ID',
  `enable` tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '启用 0:false 1:true',
  `created_at` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  `updated_at` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `idx_project_lib` (`lib_id`,`project`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=34 DEFAULT CHARSET=utf8mb4 COMMENT='标签库游戏关联表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `fp_ops_tags_lib_gm`
--

LOCK TABLES `fp_ops_tags_lib_gm` WRITE;
/*!40000 ALTER TABLE `fp_ops_tags_lib_gm` DISABLE KEYS */;
/*!40000 ALTER TABLE `fp_ops_tags_lib_gm` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `fp_ops_tickets`
--

DROP TABLE IF EXISTS `fp_ops_tickets`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `fp_ops_tickets` (
  `ticket_id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `uuid` varchar(128) NOT NULL DEFAULT '' COMMENT '设备指纹',
  `account_id` varchar(128) NOT NULL DEFAULT '' COMMENT 'fpid/account_id',
  `uid` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT 'uid',
  `vip` smallint(2) unsigned NOT NULL DEFAULT '0' COMMENT 'vip单标识： 0:普通用户；2:VIP 用户',
  `recharge` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '充值金额 单位 美金/元*10000',
  `nickname` varchar(64) NOT NULL DEFAULT '' COMMENT '用户昵称',
  `project` varchar(64) NOT NULL DEFAULT '' COMMENT '游戏',
  `gm_id` varchar(128) NOT NULL DEFAULT '' COMMENT '游戏标识: game_id(kg)/app_id(fpx)',
  `channel` varchar(64) NOT NULL DEFAULT '' COMMENT '渠道',
  `sub_channel` varchar(64) NOT NULL DEFAULT '' COMMENT '子渠道',
  `sid` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '区服ID',
  `role_id` varchar(128) NOT NULL DEFAULT '' COMMENT '角色ID',
  `role_name` varchar(128) NOT NULL DEFAULT '' COMMENT '角色名称',
  `origin` tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '工单来源 1:玩家  2:VIP  3: 普通客服',
  `scene` tinyint(2) unsigned NOT NULL DEFAULT '0' COMMENT '入口场景枚举: :游戏加载入口；2:游戏封号入口；3:游戏内入口',
  `cat_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '分类ID',
  `evaluation` tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '允许评价 0:false 1:true',
  `tpl_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '模版ID',
  `priority` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '是否是升级单： 0:否 ,1:是',
  `lang` varchar(16) NOT NULL DEFAULT '' COMMENT '语言',
  `creator` varchar(64) NOT NULL DEFAULT '' COMMENT '创建人',
  `acceptor` varchar(64) NOT NULL DEFAULT '' COMMENT '受理员',
  `csi` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '用户满意度',
  `nps` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '推荐给别人的意愿度',
  `evaluate_at` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '评价时间',
  `resolved` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '解决 0:false 1:true',
  `remark` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '备注 0:false 1:true',
  `proof` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '打回补填 0:false 1:true',
  `proof_at` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '补填时间',
  `status` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '工单状态 0:待分配 1:处理中 2:已完成',
  `conversion_node` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '流转节点 - 对应：TkStage',
  `replied` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '问题回复状态 0:未回复 1:已回复',
  `closed` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '结案状态:0:空 1:玩家结案 2:客服结案  3:系统',
  `closed_at` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '结案时间',
  `first_closed_at` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '首次结案时间',
  `reopen_num` tinyint(4) unsigned NOT NULL DEFAULT '0' COMMENT '重开次数',
  `reopen_interval` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '重开间隔',
  `resolve_confirmed` tinyint(4) NOT NULL DEFAULT '0' COMMENT '-1：未选择 1：解决 2：未解决',
  `reply_from` tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '回复玩家工作台 - 废弃字段',
  `first_reply_at` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '最初回复时间',
  `last_reply_at` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '最近回复时间',
  `sort_wait_start_at` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '搜索排序字段：等待开始时间',
  `is_deleted` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '1:删除 0:正常',
  `created_at` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  `updated_at` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`ticket_id`) USING BTREE,
  KEY `idx_prj` (`project`,`ticket_id`) USING BTREE,
  KEY `idx_prj_cat` (`project`,`cat_id`,`created_at`) USING BTREE,
  KEY `idx_cnv` (`closed`,`origin`,`cat_id`,`acceptor`) USING BTREE,
  KEY `idx_account` (`account_id`)
) ENGINE=InnoDB AUTO_INCREMENT=118 DEFAULT CHARSET=utf8mb4 COMMENT='工单表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `fp_ops_tickets`
--

LOCK TABLES `fp_ops_tickets` WRITE;
/*!40000 ALTER TABLE `fp_ops_tickets` DISABLE KEYS */;
/*!40000 ALTER TABLE `fp_ops_tickets` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `fp_ops_tickets_appraise`
--

DROP TABLE IF EXISTS `fp_ops_tickets_appraise`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `fp_ops_tickets_appraise` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `ticket_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '工单ID',
  `csi` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '用户满意度',
  `service_rating` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '服务态度评分',
  `service_time_rating` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '处理速度评分',
  `service_solution_rating` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '处理方案评分',
  `recommendation_level` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '推荐给别人的意愿程度',
  `remark` varchar(1024) NOT NULL DEFAULT '' COMMENT '评语',
  `created_at` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  `updated_at` bigint(20) DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `idx_ticket_id` (`ticket_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=13 DEFAULT CHARSET=utf8mb4 COMMENT='工单评价表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `fp_ops_tickets_appraise`
--

LOCK TABLES `fp_ops_tickets_appraise` WRITE;
/*!40000 ALTER TABLE `fp_ops_tickets_appraise` DISABLE KEYS */;
/*!40000 ALTER TABLE `fp_ops_tickets_appraise` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `fp_ops_tickets_commu`
--

DROP TABLE IF EXISTS `fp_ops_tickets_commu`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `fp_ops_tickets_commu` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `ticket_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '工单ID',
  `from_role` tinyint(255) unsigned NOT NULL DEFAULT '1' COMMENT '回复角色类型  1:玩家 2:客服 3:系统',
  `detail` text COMMENT '回复内容',
  `remark` text NOT NULL COMMENT '备注信息',
  `operator` varchar(64) NOT NULL COMMENT '操作员',
  `read` tinyint(1) NOT NULL COMMENT '消息已读 0:false 1:true',
  `is_del` tinyint(1) NOT NULL DEFAULT '0' COMMENT '删除 0:false 1:true',
  `created_at` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
  `updated_at` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
  `commu_type` varchar(64) NOT NULL DEFAULT '' COMMENT '消息类型： 1：对话消息；2：增加备注',
  `op_type` tinyint(2) unsigned NOT NULL DEFAULT '0' COMMENT '具体操作:  对应 tkEvent',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_ticket_id` (`ticket_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=159 DEFAULT CHARSET=utf8mb4 COMMENT='工单关联对话记录表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `fp_ops_tickets_commu`
--
