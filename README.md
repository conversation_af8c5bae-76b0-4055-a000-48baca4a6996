# Ops-Ticket-API

工单系统API服务，用于处理和管理运营工单流程。

## 项目概述

Ops-Ticket-API是一个基于Go语言开发的工单系统后端服务，旨在提供高效、可靠的工单管理解决方案。该系统支持多种通信渠道（如Discord、Line等），实现工单的创建、分配、处理和跟踪等功能，为运营团队提供便捷的工单管理体验。

## 技术栈

- **Go版本**：Go 1.19
- **Web框架**：Echo v4.7.2（高性能、可扩展的Web框架）
- **数据库**：MySQL（存储工单和用户数据）
- **缓存**：Redis v8.11.5（提高系统响应速度）
- **消息队列**：Redis（用于异步任务处理）
- **搜索引擎**：Elasticsearch v7.0.31（用于工单搜索和分析）
- **链路追踪**：Jaeger（分布式追踪系统）
- **国际化**：go-i18n/v2（支持多语言）
- **第三方集成**：
  - Discord API（通过自定义的discordgo库）
  - Line API（用于Line消息处理）
- **容器化**：Docker（便于部署和扩展）

## 项目结构

```
ops-ticket-api/
├── commands/       # 命令行工具和脚本
├── config/         # 配置文件目录
├── docs/           # 文档和图表
├── entity/         # 数据实体定义
├── handlers/       # HTTP请求处理器
├── internal/       # 内部包和框架
├── models/         # 数据模型
├── pkg/            # 可重用的包
├── proto/          # 协议定义文件
├── services/       # 业务逻辑服务
├── utils/          # 工具函数
├── config.yml      # 主配置文件
├── Dockerfile      # Docker构建文件
├── go.mod          # Go模块定义
├── go.sum          # 依赖版本锁定
├── main.go         # 应用入口
├── Makefile        # 构建脚本
└── build.sh        # 部署脚本
```

## 功能模块

1. **工单管理**：创建、分配、处理、关闭工单
2. **用户管理**：用户认证、权限控制
3. **通知系统**：通过Discord、Line等渠道发送通知
4. **报表统计**：工单处理效率、响应时间等数据分析
5. **集成服务**：与Discord、Line等第三方平台集成
6. **数据同步**：与旧系统数据同步、ES数据同步

## 数据库设计

![数据库表](./docs/db/db.png)

## 工单操作基础流程

![工单操作基础流程](./docs/op.png)

## 安装与部署

### 前置条件

- Go 1.19或更高版本
- MySQL数据库
- Redis服务
- Elasticsearch（可选，用于搜索功能）
- Jaeger（可选，用于链路追踪）

### 编译

```sh
make
```

### 执行

快速启动（开发环境）：

```sh
go run main.go serve -c=config/global-test.yaml
```

生产环境：

```sh
sh ./build.sh
./main serve
```

定时任务

```sh
开启定时任务
./main cron
手动触发单个任务
./main cron ban-daily-report
```

### Docker部署

```sh
docker build -t ops-ticket-api .
docker run -p 9000:9000 -v /path/to/config:/app/config ops-ticket-api
```

## 环境与域名

### Prod环境域名

* Global: `ops-ticket-api.funplus.com`
* CN: `ops-ticket-api.funplus.com.cn`

### Stage环境域名

* Global: `ops-ticket-api-stage.funplus.com`
* CN: `ops-ticket-api-stage.funplus.com.cn`

### Test环境域名

* Global: `ops-ticket-api-test.funplus.com`
* CN: `ops-ticket-api-test.funplus.com.cn`

### 网络访问

* 内网访问、不需要外网暴露

### 业务端口

* 9000

## API文档

API文档位于 `/docs` 目录下，或者可以通过访问以下地址查看：

* 开发环境：`http://localhost:9000/swagger/index.html`
* 测试环境：`http://ops-ticket-api-test.funplus.com/swagger/index.html`

## 健康检测

```sh
curl -v http://127.0.0.1:9000/health
```

## 主要命令

系统提供多种命令行工具：

* `serve` - 启动API服务
* `DscGatewayEvent` - Discord网关事件监听
* `DscSyncAllUserAndCommu` - 同步Discord用户和社区信息
* `LineGatewayEvent` - Line网关事件监听
* `syncCategoryFromOld` - 从旧系统同步工单分类
* `syncOpsTicketReport` - 同步工单报表数据
* `SyncTicketByIdToEs` - 同步工单详情到ES

## 开发指南

### 代码规范

* 遵循Go语言官方推荐的代码规范
* 使用gofmt格式化代码
* 添加适当的注释和文档

### 提交规范

* 使用语义化的提交信息
* 每次提交前进行代码审查
* 确保测试通过

## 贡献指南

1. Fork项目
2. 创建特性分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add some amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 创建Pull Request

## 许可证

版权所有 © 2021 funplus Authors. 保留所有权利。
