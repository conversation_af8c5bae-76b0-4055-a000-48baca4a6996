package weixin

import (
	"context"
	"errors"
	"fmt"
	mcache "github.com/astaxie/beego/cache"
	"github.com/spf13/cast"
	"github.com/tidwall/gjson"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"gitlab-ee.funplus.io/ops-tools/compkg/httpclient"
	"go.uber.org/zap"
	"time"
)

const (
	sendCustomMessageUrl = "https://api.weixin.qq.com/cgi-bin/message/custom/send?access_token=%s"
)

type (
	MsgT string
	WxAk struct {
		AccessToken string `json:"access_token"`
		ExpiresIn   int    `json:"expires_in"`
	}
)

var (
	MsgTypeText MsgT = "text"

	wxAkCache = mcache.NewMemoryCache()
	getAkFail = errors.New("get wx access_token fail")
	httpCli   = httpclient.New()
)

func (m MsgT) String() string {
	return string(m)
}

func GetAccessToken(wxAppId, wxAppSecret string, refresh bool) (string, error) {
	atCacheKey := fmt.Sprintf("wxak:%v", wxAppId)
	hitRes := wxAkCache.Get(atCacheKey)
	if refresh {
		hitRes = nil
	}

	if hitRes == nil {
		newToken, err := getAccessToken(wxAppId, wxAppSecret)
		if err != nil && newToken != nil && newToken.AccessToken != "" {
			return "", err
		}
		wxAkCache.Put(atCacheKey, *newToken, time.Duration(cast.ToInt64(newToken.ExpiresIn)-60)*time.Second)
		return newToken.AccessToken, nil
	} else {
		if cacheTokenRes, ok := hitRes.(WxAk); ok {
			return cacheTokenRes.AccessToken, nil
		}
	}
	return "", nil
}

func getAccessToken(wxAppId, wxAppSecret string) (*WxAk, error) {
	getAkUrl := "https://api.weixin.qq.com/cgi-bin/stable_token"
	params := map[string]interface{}{
		"grant_type": "client_credential",
		"appid":      wxAppId,
		"secret":     wxAppSecret,
	}
	ret, err := httpclient.New().PostJson(context.TODO(), getAkUrl, params)
	if err == nil && ret != nil {
		var resp = map[string]interface{}{}
		if err := ret.Decode(&resp); err != nil {

		}
		if resp["access_token"] != "" {
			ac := WxAk{AccessToken: cast.ToString(resp["access_token"]), ExpiresIn: cast.ToInt(resp["expires_in"])}
			return &ac, nil
		}
	}
	return nil, getAkFail
}

func sendMinProgramCustomerMsg(ctx context.Context, msgInfo map[string]interface{}, gameProject, wxAppId,
	wxAppSecret string, msgType MsgT, openId string) error {
	wxAk, err := GetAccessToken(wxAppId, wxAppSecret, false)
	if wxAk == "" || err != nil {
		logger.Error(ctx, "GetAccessToken return err OR empty.",
			zap.String("game_project", gameProject), zap.String("wx_app_id", wxAppId),
			zap.String("token", wxAk), zap.String("err", err.Error()))
		return err
	}

	reqParam := make(map[string]interface{})
	reqParam["touser"] = openId
	reqParam["msgtype"] = msgType.String()
	reqParam[msgType.String()] = msgInfo
	api := fmt.Sprintf(sendCustomMessageUrl, wxAk)
	ret, err := httpCli.PostJson(ctx, api, reqParam)
	if err != nil {
		return err
	}
	resBody := string(ret.Bytes())
	logger.Info(ctx, "sendMinProgramCustomerMsg result", zap.String("openId", openId), zap.String("resp_body", resBody))
	errCode := gjson.Get(resBody, "errcode")
	// 如果返回ak 失效 刷新内存ak再次重试
	if 40001 == errCode.Int() {
		nwWxAk, _ := GetAccessToken(wxAppId, wxAppSecret, true)
		api = fmt.Sprintf(sendCustomMessageUrl, nwWxAk)
		if ret, err := httpCli.PostJson(ctx, api, reqParam); ret != nil {
			errCode = gjson.Get(string(ret.Bytes()), "errcode")
		} else {
			logger.Error(ctx, "reSend customMessage fail. ", zap.String("game_project", gameProject),
				zap.String("wx_app_id", wxAppId),
				zap.String("token", wxAk), zap.String("err", err.Error()))
		}
	}

	if !errCode.Exists() || errCode.Int() != 0 {
		logger.Error(ctx, "wechat sendCustomMessage fail ", zap.String("game_project", gameProject),
			zap.String("wx_app_id", wxAppId), zap.Any("param", reqParam), zap.String("resBody", resBody))
		return nil
	}
	return nil
}
