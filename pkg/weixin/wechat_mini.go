package weixin

import (
	"context"
	"errors"
	"fmt"
	"github.com/spf13/viper"
)

var (
	wxMiniPrgKey         = "weixin_miniprg.%s"
	wxAppIdKey           = "wx_app_id"
	wxAppSecretKey       = "wx_app_secret"
	NoFoundGPCfg         = errors.New("no found game_project wx config")
	WxMiniCsNoticeMsgFmt = "亲爱的玩家您好，您有新的客服消息请点击下方链接进行查看～\n<a href=\"%s\">点击此处查看消息</a>"
)

// todo push message
func SendCustomMessage(ctx context.Context, gameProject string, msgType MsgT, msgInfo map[string]interface{}, fromOpenId string) error {
	wxCfg := viper.GetStringMapString(fmt.Sprintf(wxMiniPrgKey, gameProject))
	var wxAppId, wxAppSecret string
	if wxCfg != nil {
		wxAppId = wxCfg[wxAppIdKey]
		wxAppSecret = wxCfg[wxAppSecretKey]
	}
	if wxAppId == "" || wxAppSecretKey == "" {
		return NoFoundGPCfg
	}

	return sendMinProgramCustomerMsg(ctx, msgInfo, gameProject, wxAppId, wxAppSecret, msgType, fromOpenId)
}
