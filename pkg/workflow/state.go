// Copyright 2021 funplus Authors. All Rights Reserved.
// @Description:
// @Author: Darcy
// @Date: 2022/8/19 15:26

package workflow

import (
	"ops-ticket-api/proto/pb"
)

type State pb.TicketStage

var (
	RefillState = map[pb.TkStage]pb.FillStatus{
		pb.TkStage_TkStageAgentReplied:    pb.FillStatus_VipRefillSt,
		pb.TkStage_TkStageWaitingForAgent: pb.FillStatus_PlayerRefillSt,
	}

	RefillStageTrans = map[pb.TicketStage]pb.TicketStage{
		pb.TicketStage_Pending:                  pb.TicketStage_FirstTierProcessing,
		pb.TicketStage_TemporaryReply:           pb.TicketStage_FirstTierProcessing,
		pb.TicketStage_FirstTierProcessing:      pb.TicketStage_FirstTierProcessing,
		pb.TicketStage_FirstTierRefill:          pb.TicketStage_FirstTierProcessing,
		pb.TicketStage_FirstTierAdminProcessing: pb.TicketStage_FirstTierAdminProcessing,
		pb.TicketStage_FirstTierAdminRefill:     pb.TicketStage_FirstTierAdminProcessing,
		pb.TicketStage_SecondTierProcessing:     pb.TicketStage_SecondTierProcessing,
		pb.TicketStage_SecondTierRefill:         pb.TicketStage_SecondTierProcessing,
		pb.TicketStage_SecondTierAudit:          pb.TicketStage_SecondTierProcessing,
		pb.TicketStage_ThirdTierProcessing:      pb.TicketStage_ThirdTierProcessing,
		pb.TicketStage_ThirdTierAudit:           pb.TicketStage_ThirdTierProcessing,
	}

	RefillStageList = []uint8{
		uint8(pb.TicketStage_FirstTierRefill),
		uint8(pb.TicketStage_FirstTierAdminRefill),
		uint8(pb.TicketStage_SecondTierRefill),
		uint8(pb.TicketStage_VipAgentRefill),
	}

	StageWorkStation = map[pb.TicketStage]pb.Workbench{
		// 一线工作台
		pb.TicketStage_Pending:             pb.Workbench_FirstTierWorkStation,
		pb.TicketStage_FirstTierProcessing: pb.Workbench_FirstTierWorkStation,
		pb.TicketStage_FirstTierRefill:     pb.Workbench_FirstTierWorkStation,
		//  一线管理工作台
		pb.TicketStage_FirstTierAdminProcessing: pb.Workbench_FirstTierAdminWorkStation,
		pb.TicketStage_FirstTierAdminRefill:     pb.Workbench_FirstTierAdminWorkStation,
		// 二线工作台
		pb.TicketStage_SecondTierProcessing: pb.Workbench_SecondTierWorkStation,
		pb.TicketStage_SecondTierAudit:      pb.Workbench_SecondTierWorkStation,
		pb.TicketStage_SecondTierRefill:     pb.Workbench_SecondTierWorkStation,
		// 三线工作台
		pb.TicketStage_ThirdTierProcessing: pb.Workbench_ThirdTierWorkStation,
		pb.TicketStage_ThirdTierAudit:      pb.Workbench_ThirdTierWorkStation,
		// vip
		pb.TicketStage_VipAgentProcessing: pb.Workbench_SecondTierWorkStation,
		pb.TicketStage_VipAgentRefill:     pb.Workbench_SecondTierWorkStation,

		// 玩家操作
		//pb.TicketStage_TicketResolved: pb.Workbench_FirstTierWorkStation,
		//pb.TicketStage_TicketRefill: pb.Workbench_FirstTierWorkStation,
		//pb.TicketStage_TicketClosed:   pb.Workbench_FirstTierWorkStation,
		//pb.TicketStage_TemporaryReply: pb.Workbench_FirstTierWorkStation,
	}

	WorkStationStatements = map[pb.TicketStage]string{
		// 一线工作台
		pb.TicketStage_Pending:             "FirstTierWorkStation",
		pb.TicketStage_FirstTierProcessing: "FirstTierWorkStation",
		pb.TicketStage_FirstTierRefill:     "FirstTierWorkStation",
		//  一线管理工作台
		pb.TicketStage_FirstTierAdminProcessing: "FirstTierAdminWorkStation",
		pb.TicketStage_FirstTierAdminRefill:     "FirstTierAdminWorkStation",
		// 二线工作台
		pb.TicketStage_SecondTierProcessing: "SecondTierWorkStation",
		pb.TicketStage_SecondTierRefill:     "SecondTierWorkStation",
		// 二线审核区
		pb.TicketStage_SecondTierAudit: "SecondTierAuditWorkStation",
		// 三线工作台
		pb.TicketStage_ThirdTierProcessing: "ThirdTierWorkStation",
		// 三线审核区
		pb.TicketStage_ThirdTierAudit: "ThirdTierAuditWorkStation",
	}

	// StageTransfer 工单接单特殊节点流转
	StageTransfer = map[pb.TicketStage]pb.TicketStage{
		pb.TicketStage_Pending:         pb.TicketStage_FirstTierProcessing,
		pb.TicketStage_SecondTierAudit: pb.TicketStage_SecondTierProcessing,
		pb.TicketStage_ThirdTierAudit:  pb.TicketStage_ThirdTierProcessing,
	}
)
