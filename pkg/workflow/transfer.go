// Copyright 2021 funplus Authors. All Rights Reserved.
// @Description:
// @Author: Darcy
// @Date: 2021/12/27 8:19 PM

package workflow

import (
	"ops-ticket-api/proto/pb"
)

var (
	// TransferTips 流转提示
	TransferTips = map[pb.TicketStage]uint8{
		pb.TicketStage_FirstTierProcessing:  2,
		pb.TicketStage_FirstTierRefill:      2,
		pb.TicketStage_VipAgentRefill:       2,
		pb.TicketStage_VipAgentProcessing:   2,
		pb.TicketStage_SecondTierProcessing: 3,
		pb.TicketStage_SecondTierAudit:      4,
		pb.TicketStage_ThirdTierProcessing:  3,
		pb.TicketStage_ThirdTierAudit:       4,
	}
	// 流转提示 -- todo 待补充
	TransferTipsToShow = map[pb.TkStage]uint8{
		pb.TkStage_TkStageNew:             1,
		pb.TkStage_TkStageNewForAgent:     2,
		pb.TkStage_TkStageAgentReplied:    2,
		pb.TkStage_TkStageWaitingForAgent: 2,
		pb.TkStage_TkStageAgentResolved:   4,
		pb.TkStage_TkStageAgentReopen:     1,
		pb.TkStage_TkStageAgentRejected:   4,
		pb.TkStage_TkStageAgentCompleted:  4,
	}
	TkDoneStage = []uint32{ // 已完成状态
		uint32(pb.TkStage_TkStageAgentResolved),
		uint32(pb.TkStage_TkStageAgentRejected),
		uint32(pb.TkStage_TkStageAgentCompleted),
	}
	TkUserCommuChangeStage = []uint32{ // 玩家回复后 需修改状态
		uint32(pb.TkStage_TkStageAgentReplied),
		uint32(pb.TkStage_TkStageWaitingForAgent),
	}
	TkCanGcAcceptorStage = []uint32{ // 可以回收重新分配的状态
		uint32(pb.TkStage_TkStageNewForAgent),
		uint32(pb.TkStage_TkStageWaitingForAgent),
		uint32(pb.TkStage_TkStageAgentReopen),
	}

	TkStageToProgress = map[pb.TkStage]pb.TkProgress{
		pb.TkStage_TkStageNew:             pb.TkProgress_TkProgressUserDoing,
		pb.TkStage_TkStageNewForAgent:     pb.TkProgress_TkProgressUserDoing,
		pb.TkStage_TkStageAgentReplied:    pb.TkProgress_TkProgressUserDoing,
		pb.TkStage_TkStageWaitingForAgent: pb.TkProgress_TkProgressUserDoing,
		pb.TkStage_TkStageAgentResolved:   pb.TkProgress_TkProgressUserRefillOverTime,
		pb.TkStage_TkStageAgentRejected:   pb.TkProgress_TkProgressReject,
		pb.TkStage_TkStageAgentCompleted:  pb.TkProgress_TkProgressDone,
		pb.TkStage_TkStageAgentReopen:     pb.TkProgress_TkProgressReopen,
	}

	// 玩家操作history的 operate
	TkEventToUserOpEvent = []uint8{
		uint8(pb.TkEvent_TkEventCreate),
		uint8(pb.TkEvent_TkEventPlayerRefill),
		uint8(pb.TkEvent_TkEventReopen),
		uint8(pb.TkEvent_TkEventUserCommu),
	}
	// 后端操作回复玩家/结束单history的 operate
	TkEventCsOpEvent = []uint8{
		uint8(pb.TkEvent_TkEventCommu),
		uint8(pb.TkEvent_TkEventCommuClose),
		uint8(pb.TkEvent_TkEventRefused),
	}
)
