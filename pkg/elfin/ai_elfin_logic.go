package aielfin

import (
	"context"
	"fmt"
	"github.com/pkg/errors"
	"github.com/spf13/viper"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"gitlab-ee.funplus.io/ops-tools/compkg/httpclient"
	"gitlab-ee.funplus.io/ops-tools/compkg/sign"
	"ops-ticket-api/utils"
	"sync"
	"time"
)

var (
	elfinCfg = &elfinConfig{Once: sync.Once{}}
)

type (
	elfinConfig struct {
		Once     sync.Once
		Debug    bool   `json:"debug"`
		Host     string `json:"host"`
		Secret   string `json:"secret"`
		Env      string `json:"env"`
		Operator string `json:"operator"`
		From     string `json:"from"`
	}
	elfinResult struct {
		Code int    `json:"code"`
		Msg  string `json:"msg"`
	}
	elfinWithData struct {
		Code int    `json:"code"`
		Msg  string `json:"msg"`
		Data string `json:"data"`
	}
	// ElfinTicketKnowledgeSaveReq 保存/更新数据
	ElfinTicketKnowledgeSaveReq struct {
		QstId          int64  `json:"qst_id"`
		Uuid           string `json:"uuid"`
		GameProject    string `json:"game_project"`
		Lang           string `json:"lang"`
		CatID          uint32 `json:"cat_id"` // 问题分类ID
		QstDesc        string `json:"qst_desc"`
		AnswerRichText string `json:"answer_rich_text"`
		From           string `json:"from"`
	}
	// ElfinTicketKnowledgeDelReq 删除数据
	ElfinTicketKnowledgeDelReq struct {
		GameProject string  `json:"game_project"`
		QstIds      []int64 `json:"qst_ids"`
		From        string  `json:"from"`
	}

	// ElfinTicketKnowledgeSearchElfReq 向量搜索数据
	ElfinTicketKnowledgeSearchElfReq struct {
		GameProject    string  `json:"game_project"`
		Lang           string  `json:"lang"`
		CatID          uint32  `json:"cat_id"` // 问题分类ID
		QstDesc        string  `json:"qst_desc"`
		ScoreThreshold float32 `json:"scoreThreshold"` // 阈值
	}
)

func onceInit() {
	elfinCfg.Once.Do(func() {
		var host = viper.GetString("thirdparty.ops_backend_api.ai_elfin_backend_server_host")
		elfinCfg.Host = host
		elfinCfg.Secret = viper.GetString("auth.admin_gateway")
		elfinCfg.Env = viper.GetString("app.env")
		elfinCfg.Debug = viper.GetString("app.env") != "prod"
		elfinCfg.Operator = "system"
		elog.Infof("aiElfin.Init --> elfinCfg:%+v", elfinCfg)
	})
}

func ImportTicketKnowledge(ctx context.Context, account string, req *ElfinTicketKnowledgeSaveReq) error {
	fun := "aiElfin.ImportTicketKnowledge -->"
	onceInit()
	if elfinCfg.Host == "" {
		logger.Infof(ctx, "%s host is empty. req:%+v", fun, req)
		return nil
	}
	if account == "" {
		account = elfinCfg.Operator
	}
	req.From = "cs_ticket"
	claims := sign.NewClaims()
	claims.Time = time.Now().UTC()
	claims.Set("username", account)
	claims.Set("nickname", account)
	claims.Set("prd_id", "cs_ticket")
	claims.Set("game_project", req.GameProject)
	token, err := sign.JwtEncode(claims, elfinCfg.Secret)
	if err != nil {
		logger.Errorf(ctx, "%s sync elfin jwt encode fail. %v", fun, err)
		return err
	}

	var path = elfinCfg.Host + "/elfin_egress/knowledge/ticket/save"
	client := httpclient.New().SetHeader("Authorization", "Bearer "+token)
	res, err := client.PostJson(ctx, path, req)
	if err != nil {
		logger.Errorf(ctx, "%s sync elfin import ticket knowledge err. err:%v, req: %v", fun, err, req)
		return err
	}

	if elfinCfg.Debug {
		fmt.Println("ImportTicketKnowledge req:", utils.ToJson(req))
		fmt.Println("ImportTicketKnowledge res:", res.String())
		elog.Infof("%s response. res:%+v", fun, res)
	}

	respData := &elfinResult{}
	if err := res.Decode(&respData); err != nil {
		return err
	}
	if respData.Code != 0 {
		return errors.Errorf("%s ImportTicketKnowledge code=%d. msg:%s", fun, respData.Code, respData.Msg)
	}
	return nil
}

func DeleteKnowledge(ctx context.Context, account string, req *ElfinTicketKnowledgeDelReq) error {
	fun := "aiElfin.DeleteKnowledge -->"
	onceInit()
	if elfinCfg.Host == "" {
		logger.Infof(ctx, "%s host is empty. req:%+v", fun, req)
		return nil
	}
	if account == "" {
		account = elfinCfg.Operator
	}
	req.From = "cs_ticket"
	claims := sign.NewClaims()
	claims.Time = time.Now().UTC()
	claims.Set("username", account)
	claims.Set("nickname", account)
	claims.Set("prd_id", "cs_ticket")
	claims.Set("game_project", req.GameProject)
	token, err := sign.JwtEncode(claims, elfinCfg.Secret)
	if err != nil {
		logger.Errorf(ctx, "%s sync elfin jwt encode fail. %v", fun, err)
		return err
	}

	var path = elfinCfg.Host + "/elfin_egress/knowledge/ticket/del"
	client := httpclient.New().SetHeader("Authorization", "Bearer "+token)
	res, err := client.PostJson(ctx, path, req)
	if err != nil {
		logger.Errorf(ctx, "%s sync elfin del ticket knowledge err. err:%v, req: %v", fun, err, req)
		return err
	}

	if elfinCfg.Debug {
		fmt.Println("DeleteTicketKnowledge req:", utils.ToJson(req))
		fmt.Println("DeleteTicketKnowledge res:", res.String())
		elog.Infof("%s response. res:%+v", fun, res)
	}

	respData := &elfinResult{}
	if err := res.Decode(&respData); err != nil {
		return err
	}
	if respData.Code != 0 {
		return errors.Errorf("%s DeleteTicketKnowledge code=%d. msg:%s", fun, respData.Code, respData.Msg)
	}
	return nil
}

// SearchResult 定义搜索结果的类型
type SearchResult struct {
	Code int         `json:"code"`
	Msg  string      `json:"msg"`
	Data interface{} `json:"data"` // 根据实际数据结构调整
}

func TicketKnowledgeSearchElf(ctx context.Context, account string, req *ElfinTicketKnowledgeSearchElfReq) (string, error) {
	fun := "aiElfin.TicketKnowledgeSearchElf -->"
	onceInit()

	// 检查配置
	if elfinCfg.Host == "" {
		logger.Infof(ctx, "%s host is empty. req:%+v", fun, req)
		return "", errors.New("elfin host is empty")
	}

	// 设置默认账号
	if account == "" {
		account = elfinCfg.Operator
	}
	// 生成 JWT Token
	claims := sign.NewClaims()
	claims.Time = time.Now().UTC()
	claims.Set("username", account)
	claims.Set("nickname", account)
	claims.Set("prd_id", "cs_ticket")
	claims.Set("game_project", req.GameProject)
	token, err := sign.JwtEncode(claims, elfinCfg.Secret)
	if err != nil {
		logger.Errorf(ctx, "%s jwt encode failed. err: %v", fun, err)
		return "", err
	}

	// 构造请求路径
	path := elfinCfg.Host + "/elfin_egress/knowledge/ticket/search_elf"
	// 发送请求
	client := httpclient.New().SetHeader("Authorization", "Bearer "+token)
	res, err := client.PostJson(ctx, path, req)
	if err != nil {
		logger.Errorf(ctx, "%s search request failed. err: %v, req: %v", fun, err, req)
		return "", err
	}

	// 调试模式下打印请求和响应
	if elfinCfg.Debug {
		fmt.Println("TicketKnowledgeSearchElf req:", utils.ToJson(req))
		fmt.Println("TicketKnowledgeSearchElf res:", res.String())
		elog.Infof("%s response. res:%+v", fun, res)
	}

	// 解析响应
	respData := &elfinWithData{}
	if err := res.Decode(&respData); err != nil {
		logger.Errorf(ctx, "%s decode response failed. err: %v", fun, err)
		return "", err
	}

	// 检查返回码
	if respData.Code != 0 {
		errMsg := fmt.Sprintf("%s search failed. code=%d, msg: %s", fun, respData.Code, respData.Msg)
		logger.Errorf(ctx, errMsg)
		return "", errors.New(errMsg)
	}

	// 返回搜索结果中的 Answer
	return respData.Data, nil
}
