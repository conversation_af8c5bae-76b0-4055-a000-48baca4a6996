// Copyright 2021 funplus Authors. All Rights Reserved.
// @Description: ticket-api metrics
// @Author: Darcy
// @Date: 2022/8/16 16:14

package reporter

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/spf13/viper"
	fplog "gitlab-ee.funplus.io/ops-tools/compkg/fplog/logger"
	"gitlab-ee.funplus.io/ops-tools/compkg/network"
	"sync"
	"time"

	"github.com/prometheus/client_golang/prometheus"
	"github.com/spf13/cast"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"gitlab-ee.funplus.io/ops-tools/compkg/metrics/metricsapi"
)

var (
	DefaultTkMetrics = newTicketMetrics()
	once             sync.Once
	cm               *ticketMetrics
	ticketLabel      = []string{"game_project", "scene", "first_cat", "second_cat", "third_cat", "code"}
)

type ticketMetrics struct {
	Create metricsapi.Counter
}

// NewTicketMetrics will be initialized TicketMetrics
func newTicketMetrics() *ticketMetrics {
	once.Do(func() {
		cm = &ticketMetrics{
			Create: metricsapi.NewCounter("ticket_create", ticketLabel),
		}
	})
	return cm
}

func (metric *ticketMetrics) CreateTicket(project, code string, scene, catLevel1, catLevel2, catLevel3 uint32, fields string) {
	defer func() {
		if err := recover(); err != nil {
			logger.Errorf(context.Background(), "recover err:%v", err)
			return
		}
	}()
	k := fmt.Sprintf("fplog.ticket_category.%s", project)
	if viper.IsSet(k) && fields != "" && fields != "{}" {
		catK := fmt.Sprintf("%s.%d_scene", k, scene)
		logAgentLevel := viper.GetString(catK)
		if logAgentLevel == "" && catLevel3 > 0 {
			catK := fmt.Sprintf("%s.%d", k, catLevel3)
			logAgentLevel = viper.GetString(catK)
		}
		if logAgentLevel == "" && catLevel2 > 0 {
			catK := fmt.Sprintf("%s.%d", k, catLevel2)
			logAgentLevel = viper.GetString(catK)
		}
		if logAgentLevel == "" && catLevel1 > 0 {
			catK := fmt.Sprintf("%s.%d", k, catLevel1)
			logAgentLevel = viper.GetString(catK)
		}
		if logAgentLevel != "" {
			route, tag := viper.GetString(fmt.Sprintf("fplog.log_agent.dispatcher.%s.route", project)), viper.GetString(fmt.Sprintf("fplog.log_agent.dispatcher.%s.tag", project))
			// log agent 打点
			logMsg := map[string]interface{}{
				"msg":       fields,
				"ts":        time.Now().UnixNano() / 1e6,
				"tag":       tag,
				"server_ip": network.OutboundIP(),
				"level":     logAgentLevel,
			}
			logMsgJson, _ := json.Marshal(logMsg)
			fplog.GetFPLogger().LogWrite(&fplog.LogRecord{
				Message: logMsgJson,
				Route:   route,
			})
		}
	}
	metric.Create.With(prometheus.Labels{
		"game_project": project,
		"scene":        cast.ToString(scene),
		"first_cat":    cast.ToString(catLevel1),
		"second_cat":   cast.ToString(catLevel2),
		"third_cat":    cast.ToString(catLevel3),
		"code":         code,
	}).Inc()
}
