package privatezone

import (
	"context"
	"encoding/json"
	"github.com/pudongping/go-crypto"
	"github.com/spf13/cast"
	"github.com/spf13/viper"
	"github.com/tidwall/gjson"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"gitlab-ee.funplus.io/ops-tools/compkg/httpclient"
	"go.uber.org/zap"
)

func PrivateVipLevel(ctx context.Context, uid uint64, gameProject string) uint64 {

	api := viper.GetString("thirdparty.ops_backend_api.private_zone_vip_level")
	if api == "" {
		return 0
	}

	key := viper.GetString("thirdparty.ops_backend_api.private_zone_Vip_level_key")
	if key == "" {
		return 0
	}

	// 构造请求数据
	requestData := map[string]interface{}{
		"uid":          uid,
		"game_project": gameProject,
	}

	jsonData, err := json.Marshal(requestData)
	if err != nil {
		logger.Errorf(ctx, "marshal failed: %v", err)
		return 0
	}

	// 加密请求数据
	encryptedData, err := go_crypto.AESCBCEncrypt(string(jsonData), key)
	if err != nil {
		logger.Errorf(ctx, "encrypt failed: %v", err)
		return 0
	}
	client := httpclient.New()
	client.SetHeader("Content-Type", "application/json")
	// 发送加密的POST请求，使用application/json
	ret, err := client.PostJson(ctx, api, map[string]interface{}{
		"data": encryptedData,
	})
	if err != nil {
		logger.Errorf(ctx, "PrivateVipLevel api call error. err:%v", zap.Error(err))
		return 0
	}
	respBody := ret.String()
	logger.Info(ctx, "PrivateVipLevel api call success", zap.String("data", respBody))

	// 解析响应数据
	jsonBody := gjson.Parse(respBody)
	var VipLevelRes string
	if jsonBody.Get("data.vip_level").Exists() {
		VipLevelRes = jsonBody.Get("data.vip_level").String()
	} else {
		return 0
	}
	return cast.ToUint64(VipLevelRes)
}
