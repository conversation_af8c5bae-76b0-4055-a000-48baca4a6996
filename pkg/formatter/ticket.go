// Copyright 2021 funplus Authors. All Rights Reserved.
// @Description:
// @Author: Darcy
// @Date: 2022/5/27 19:38

package formatter

import (
	"encoding/json"
	"fmt"
	"github.com/labstack/echo/v4"
	"github.com/olivere/elastic/v7"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"go.uber.org/zap"
	"ops-ticket-api/internal/code"
	"ops-ticket-api/internal/framework/cache/keys"
	"ops-ticket-api/internal/framework/cache/rds"
	"ops-ticket-api/internal/framework/xerrors"
	"ops-ticket-api/internal/persistence"
	"ops-ticket-api/models"
	"ops-ticket-api/proto/pb"
	"ops-ticket-api/utils"
	"strings"
	"sync"
)

var (
	onceTk      sync.Once
	singletonTk *TicketFormatter

	DefaultTicketFormatter = newTicketFormatter()
)

type TicketFormatter struct {
}

func newTicketFormatter() *TicketFormatter {
	onceTk.Do(func() {
		singletonTk = &TicketFormatter{}
	})
	return singletonTk
}

func (f *TicketFormatter) PoolTicketInfoFormatEs(ctx echo.Context, tickets []*elastic.SearchHit) ([]*pb.TicketPoolNewListResp_TicketPoolInfo, error) {
	pool := make([]*pb.TicketPoolNewListResp_TicketPoolInfo, 0)
	now := utils.NowTimestamp()
	for _, ticket := range tickets {
		ticketInfo := &models.TicketsDoc{}
		if err := json.Unmarshal(ticket.Source, &ticketInfo); err != nil {
			logger.Error(ctx.Request().Context(), "Ticket Unmarshal err", logger.String("err", err.Error()))
			return nil, err
		}
		respInfo := &pb.TicketPoolNewListResp_TicketPoolInfo{
			Project:     ticketInfo.Project,
			TicketId:    ticketInfo.TicketID,
			Detail:      "",
			Recharge:    utils.FloatRound(float64(ticketInfo.Recharge)/code.RechargeRate, 2),
			Status:      ticketInfo.Stage,
			Csi:         ticketInfo.Csi,
			Acceptor:    ticketInfo.Acceptor,
			WaitingTime: "-",
			Checked:     false,
			AccountId:   ticketInfo.AccountID,
			SystemLabel: ticketInfo.SystemTags,
		}
		// 判断是否绿色通道单用于高亮展示
		if utils.InArrayAny(uint32(pb.TicketSystemTag_GreenChannelUser), ticketInfo.SystemTags) {
			respInfo.CrmVipUserFlag = true
		}
		//for _, commu := range ticketInfo.Commus {
		//	if commu.CommuType == pb.CommuType_CommuTypeDialogue.String() && commu.FromRole == uint32(pb.UserRole_PlayerRole) {
		//		respInfo.Detail = commu.Detail
		//		break
		//	}
		//}
		var fieldMap map[string]interface{}

		fmt.Printf("field:     %#v\n", ticketInfo.Field)

		if ticketInfo.Field != "" {
			err := json.Unmarshal([]byte(ticketInfo.Field), &fieldMap)
			if err != nil {
				logger.Error(ctx.Request().Context(), "Ticket Field Unmarshal err", logger.String("err", err.Error()))
				return nil, err
			}
		}
		respInfo.Detail = "-"
		for key, value := range fieldMap {
			for _, issue := range models.IssueDescriptionMap {
				if strings.ToLower(key) == issue {
					if _, ok := value.(string); ok {
						respInfo.Detail = value.(string)
					}
					break
				}
			}

		}
		if ticketInfo.SortWaitStartAt <= now {
			respInfo.WaitingTime = utils.ResolveTimeStr(now-ticketInfo.SortWaitStartAt, 1)
		}
		//根据accountid查询dc最近七天是否有沟通
		Initiativer := struct {
			LastCommuTime string `json:"last_commu_time"`
		}{}
		if ticketInfo.AccountID != "" {
			if err := rds.RCli.QueryRow(ctx.Request().Context(), fmt.Sprintf(keys.DiscordAccountLastChatTimeKey, ticketInfo.AccountID), &Initiativer, func(v interface{}) error {
				commuTime, err := persistence.NewDiscordInteract().FetchUserIsInitiativeByAccountId(ticketInfo.Project, ticketInfo.AccountID)
				if err != nil {
					logger.Error(ctx.Request().Context(), "FetchUserIsInitiativeByAccountId err", zap.Any("accountId", ticketInfo.AccountID), zap.String("err", err.Error()))
					return xerrors.New(err.Error(), code.DbError)
				}
				if commuTime != "" {
					Initiativer.LastCommuTime = commuTime
				}
				return nil
			}); err != nil {
				return nil, err
			}
			// 只有LastCommuTime不为空才会返true
			if Initiativer.LastCommuTime != "" {
				respInfo.DcFlag = true
				respInfo.DcCreateTime = Initiativer.LastCommuTime
			}
		}
		pool = append(pool, respInfo)
	}
	return pool, nil
}
