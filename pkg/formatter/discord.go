package formatter

import (
	"encoding/json"
	"fmt"
	"github.com/labstack/echo/v4"
	"github.com/olivere/elastic/v7"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"go.uber.org/zap"
	"ops-ticket-api/internal/code"
	"ops-ticket-api/internal/framework/cache/keys"
	"ops-ticket-api/internal/framework/cache/rds"
	"ops-ticket-api/internal/framework/xerrors"
	"ops-ticket-api/internal/persistence"
	"ops-ticket-api/models"
	"ops-ticket-api/proto/pb"
	"ops-ticket-api/utils"
	"time"
)

var (
	singletonDsc *DiscordFormatter

	DefaultDiscordFormatter = newDiscordFormatter()
)

type DiscordFormatter struct {
}

func newDiscordFormatter() *DiscordFormatter {
	onceTk.Do(func() {
		singletonDsc = &DiscordFormatter{}
	})
	return singletonDsc
}

func (f *DiscordFormatter) PoolDiscordInfoFormatEs(ctx echo.Context, discords []*elastic.SearchHit) ([]*pb.DscPoolInfo, error) {
	pool := make([]*pb.DscPoolInfo, 0)
	now := utils.NowTimestamp()
	for _, discord := range discords {
		discordInfo := &models.FpDscUserDoc{}
		if err := json.Unmarshal(discord.Source, &discordInfo); err != nil {
			logger.Error(ctx.Request().Context(), "Discord Unmarshal err", logger.String("err", err.Error()))
			return nil, err
		}
		respInfo := &pb.DscPoolInfo{
			DscUserId:         discordInfo.DscUserID,
			UserName:          discordInfo.UserName,
			Project:           discordInfo.Project,
			GlobalName:        discordInfo.GlobalName,
			DmChannel:         discordInfo.PrivChannelID,
			GuildId:           discordInfo.GuildID,
			Processor:         discordInfo.Maintainer,
			BotId:             discordInfo.AppID,
			Uid:               discordInfo.UID,
			AccountId:         discordInfo.AccountId,
			Sid:               discordInfo.Sid,
			LastLogin:         utils.TimeFormat(discordInfo.LastLogin),
			PayAll:            discordInfo.PayAll,
			PayLastThirtyDays: discordInfo.PayLastThirtyDays,
			Status:            uint32(discordInfo.ReplyType),
			VipLevel:          uint32(discordInfo.VipLevel),
			Note:              discordInfo.Note,
			PlayerNick:        discordInfo.PlayerNick,
			Birthday:          discordInfo.Birthday,
			Lang:              discordInfo.Lang,
			LastReplyTime:     utils.TimeFormat(discordInfo.LastReplyTime),
			WaitingTime:       "-",
		}
		if respInfo.GlobalName != "" {
			respInfo.UserName = respInfo.GlobalName
		}
		if uint64(discordInfo.SortWaitStartAt) <= now {
			respInfo.WaitingTime = utils.ResolveTimeStr(now-uint64(discordInfo.SortWaitStartAt), 1)
		}

		//根据accountid查询工单最近七天的提单次数
		Initiativer := struct {
			LastCommuTime uint64 `json:"last_commu_time"`
			Count         int64  `json:"count"`
			TicketID      uint64 `json:"ticket_id"`
		}{}
		if discordInfo.AccountId != "" {
			if err := rds.RCli.QueryRow(ctx.Request().Context(), fmt.Sprintf(keys.TicketAccountLastCreateTimeKey, discordInfo.AccountId), &Initiativer, func(v interface{}) error {
				total, createTime, ticketID, err := persistence.NewTicket().GetUserIsCreateTicketByAccountId(discordInfo.Project, discordInfo.AccountId)
				if err != nil {
					logger.Error(ctx.Request().Context(), "GetUserIsCreateTicketByAccountId err", zap.Any("accountId", discordInfo.AccountId), zap.String("err", err.Error()))
					return xerrors.New(err.Error(), code.DbError)
				}
				if total != 0 {
					Initiativer.LastCommuTime = createTime
					Initiativer.Count = total
					Initiativer.TicketID = ticketID
				}
				return nil
			}); err != nil {
				return nil, err
			}
			if Initiativer.Count != 0 {
				// 将时间戳转换为时间
				t := time.Unix(int64(Initiativer.LastCommuTime), 0)
				formattedTime := t.Format("2006-01-02 15:04:05")
				respInfo.TicketCreateCount = Initiativer.Count
				respInfo.LastTicketCreateTime = formattedTime
				respInfo.TicketId = Initiativer.TicketID
			}
		}

		pool = append(pool, respInfo)
	}
	return pool, nil
}
