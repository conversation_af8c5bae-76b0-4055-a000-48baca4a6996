package formatter

import (
	"encoding/json"
	"github.com/labstack/echo/v4"
	"github.com/olivere/elastic/v7"
	"github.com/spf13/cast"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"ops-ticket-api/internal/persistence"
	"ops-ticket-api/models"
	"ops-ticket-api/proto/pb"
	"ops-ticket-api/utils"
)

var (
	singletonLine *LineFormatter

	DefaultLineFormatter = newLineFormatter()
)

type LineFormatter struct {
}

func newLineFormatter() *LineFormatter {
	onceTk.Do(func() {
		singletonLine = &LineFormatter{}
	})
	return singletonLine
}

func (f *LineFormatter) PoolLineInfoFormatEs(ctx echo.Context, lines []*elastic.SearchHit) ([]*pb.LinePoolInfo, error) {
	pool := make([]*pb.LinePoolInfo, 0)
	for _, line := range lines {
		lineInfo := &models.FpLineUserDoc{}
		if err := json.Unmarshal(line.Source, &lineInfo); err != nil {
			logger.Error(ctx.Request().Context(), "line Unmarshal err", logger.String("err", err.Error()))
			return nil, err
		}
		sid := ""
		if lineInfo.Sid > 0 {
			sid = cast.ToString(lineInfo.Sid)
		}
		respInfo := &pb.LinePoolInfo{
			LineUserId:        lineInfo.LineUserID,
			DisplayName:       lineInfo.DisplayName,
			Project:           lineInfo.Project,
			ChannelId:         lineInfo.ChannelID,
			ChannelShow:       "-",
			Maintainer:        lineInfo.Maintainer,
			BotId:             lineInfo.BotID,
			Uid:               lineInfo.UID,
			AccountId:         lineInfo.AccountId,
			Sid:               sid,
			LastLogin:         utils.TimeFormat(lineInfo.LastLogin),
			PayAll:            lineInfo.PayAll,
			PayLastThirtyDays: lineInfo.PayLastThirtyDays,
			Status:            uint32(lineInfo.ReplyType),
			VipLevel:          uint32(lineInfo.VipLevel),
			Note:              lineInfo.Note,
			PlayerNick:        lineInfo.PlayerNick,
			Birthday:          lineInfo.Birthday,
			Lang:              lineInfo.Lang,
			FollowStatus:      int32(lineInfo.FollowStatus),
		}
		pool = append(pool, respInfo)
	}
	var channelIds []string
	var channelHas = map[string]struct{}{}
	for _, v := range pool {
		if _, ok := channelHas[v.ChannelId]; !ok {
			channelIds = append(channelIds, v.ChannelId)
		}
	}
	if len(channelIds) > 0 {
		chs, err := persistence.NewLineInteractions().FetchChannelShows(ctx.Request().Context(), channelIds)
		if err != nil {
			return nil, err
		}
		for i, v := range pool {
			if show, ok := chs[v.ChannelId]; ok {
				pool[i].ChannelShow = show
			}
		}
	}
	return pool, nil
}
