package game

import (
	"context"
	"ops-ticket-api/internal/code"
	"ops-ticket-api/internal/framework/xerrors"
	"sync"

	"github.com/spf13/cast"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"gitlab-ee.funplus.io/ops-tools/compkg/opcenterv2"
	"go.uber.org/zap"
)

var (
	gameInfoCache *gameInfoRpc
	gameInfoOnce  sync.Once
)

type gameInfoRpc struct {
	opCenter *opcenterv2.OpCenter
}

// NewGameInfo init
func NewGameInfo() *gameInfoRpc {
	gameInfoOnce.Do(func() {
		gameInfoCache = &gameInfoRpc{
			opCenter: opcenterv2.New(&opcenterv2.Options{
				KgGameInfoFile:      opcenterv2.ProdJsonFile,
				FpxGameInfoUrl:      opcenterv2.ProdCnFpxConfigUrl,
				OpsGameInfoFile:     opcenterv2.ProdOpsJsonFile,
				FetchInterval:       600,
				OverrideGameProject: map[string]string{"st_global": "mce_global", "ss_cn_wx": "ss_cn"},
			}),
		}
		gameInfoCache.opCenter.Watch()
	})
	return gameInfoCache
}

func (gm *gameInfoRpc) GetGameProjectById(gameAppId string) (string, error) {
	gmInfo := gm.opCenter.GetGameInfoByAppId(gameAppId)
	if gmInfo == nil {
		logger.Error(context.Background(), "Unknown project", zap.String("game_id", gameAppId), zap.Any("ob", gm.opCenter))
		return "", xerrors.New("game info not found", code.NotFound)
	}
	return gmInfo.GameProject, nil
}

func (gm *gameInfoRpc) GetGameProjectByMix(gmId int, gameAppId string) (string, error) {
	if gmId == 0 {
		return gm.GetGameProjectById(gameAppId)
	}

	return gm.GetGameProjectById(cast.ToString(gmId))
}

func (gm *gameInfoRpc) GetChannelByProject(project string) []string {
	return gm.opCenter.GetChannelList(project)
}

func (gm *gameInfoRpc) GetSubChannelByProject(project string) map[string]interface{} {
	chM := gm.opCenter.GetChannelWithSubChanel(project)
	chanList := make(map[string]interface{}, len(chM))
	for ch, subs := range chM {
		var subChans interface{}
		if len(subs.SubChannel) == 0 {
			subChans = make([]struct{}, 0)
		} else {
			subChans = subs.SubChannel
		}
		chanList[ch] = subChans
	}
	return chanList
}

func (gm *gameInfoRpc) GetPackageIDByProject(project string) map[string]interface{} {
	chM := gm.opCenter.GetPackageIDWithChannel(project)
	chanList := make(map[string]interface{}, len(chM))
	for ch, subs := range chM {
		var PaackageIds interface{}
		if len(subs.PackageID) == 0 {
			PaackageIds = make([]struct{}, 0)
		} else {
			PaackageIds = subs.PackageID
		}
		chanList[ch] = PaackageIds
	}
	return chanList
}

func (gm *gameInfoRpc) IsFpxGameById(gameAppId string) (bool, error) {
	gmInfo := gm.opCenter.GetGameInfoByAppId(gameAppId)
	if gmInfo == nil {
		logger.Error(context.Background(), "Unknown project", zap.String("game_id", gameAppId), zap.Any("ob", gm.opCenter))
		return false, xerrors.New("game info not found", code.NotFound)
	}
	return gmInfo.Gameid == 0, nil
}
