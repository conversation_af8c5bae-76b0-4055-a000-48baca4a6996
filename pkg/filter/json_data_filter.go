package filter

import (
	"context"
	jsoniter "github.com/json-iterator/go"
	"github.com/spf13/cast"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"go.uber.org/zap"
	"net/url"
	"strings"
)

type jsonDataFilter struct {
	orgStr interface{}
	data   map[string]interface{}
}

func NewJsonDataFilter(jsonData interface{}) *jsonDataFilter {
	var data = make(map[string]interface{}, 0)
	switch jsonData.(type) {
	case string:
		if jsonData != "" && jsonData != "{}" {
			_jtmp, _ := url.QueryUnescape(jsonData.(string))
			if jsoniter.ConfigFastest.Valid([]byte(_jtmp)) {
				if err := jsoniter.ConfigFastest.UnmarshalFromString(_jtmp, &data); err != nil {
					logger.Error(context.TODO(), "jsonData type.string UnmarshalFromString return err", zap.String("err", err.Error()), zap.String("json_data", jsonData.(string)))
				}
			} else {
				logger.Info(context.TODO(), "jsonData type.string ConfigFastest.Valid false", zap.String("json_data", jsonData.(string)))
			}
		}
	case struct{}:
		if _tmp, err := jsoniter.ConfigFastest.Marshal(jsonData); err != nil {
			logger.Error(context.TODO(), "jsonData type.struct Marshal return err", zap.String("err", err.Error()), zap.Any("json_data", jsonData))
		} else if err := jsoniter.ConfigFastest.Unmarshal(_tmp, &data); err != nil {
			logger.Error(context.TODO(), "jsonData Type.struct Unmarshal return err", zap.String("err", err.Error()), zap.Any("json_data", jsonData))
		}
	case map[string]interface{}:
		data = jsonData.(map[string]interface{})
	case nil:
		logger.Info(context.TODO(), "jsonData Type.nil continue.")
	default:
		logger.Error(context.TODO(), "jsonData Type.unknown return err", zap.Any("json_data", jsonData))
	}
	return &jsonDataFilter{orgStr: jsonData, data: data}
}

func (j *jsonDataFilter) GetCountryCode() string {
	if len(j.data) == 0 {
		return ""
	}
	if v, ok := j.data["country_code"]; ok {
		return cast.ToString(v)
	}
	return ""
}
func (j *jsonDataFilter) GetUid() uint64 {
	return cast.ToUint64(j.data["uid"])
}
func (j *jsonDataFilter) GetFpid() uint64 {
	return cast.ToUint64(j.data["fpid"])
}

func (j *jsonDataFilter) GetTotalPay() float64 {
	if len(j.data) == 0 {
		return 0
	}
	if v, ok := j.data["total_pay"]; ok {
		return cast.ToFloat64(v)
	}
	return 0
}

func (j *jsonDataFilter) GetQfrom() string {
	return strings.TrimSpace(cast.ToString(j.data["qfrom"]))
}
