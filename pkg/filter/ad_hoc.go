// Copyright 2021 funplus Authors. All Rights Reserved.
// @Description:
// @Author: Darcy
// @Date: 2022/11/11 11:28

package filter

import (
	"context"
	"ops-ticket-api/internal/code"
	"ops-ticket-api/internal/elasticsearch"
	"ops-ticket-api/proto/pb"
	"strings"
	"sync"
)

var (
	adhocOnce    sync.Once
	adhocWrapper *AdHocWrapper
)

type AdHocWrapper struct{}

func NewAdHocWrapper() *AdHocWrapper {
	adhocOnce.Do(func() {
		adhocWrapper = &AdHocWrapper{}
	})
	return adhocWrapper
}

// TicketPool 工单池条件过滤
func (ad *AdHocWrapper) TicketPool(ctx context.Context, req *pb.TicketPoolNewListReq, prepareMap map[string][]string, members []string) ([]elasticsearch.Option, error) {
	option := []elasticsearch.Option{

		// 时间 range
		elasticsearch.WithRangeTime(req.CreatedAt, "created_at"),   // 创建时间
		elasticsearch.WithRangeTime(req.ClosedAt, "closed_at"),     // 结单时间
		elasticsearch.WithRangeTime(req.EvaluateAt, "evaluate_at"), // 评价时间
		elasticsearch.WithProperty(req.Project, "project"),         // 项目

		elasticsearch.WithProperty(req.Status, "stage"),                // 工单流转状态
		elasticsearch.WithSolveType(req.SolveType, "solve_type"),       // 工单处理类型
		elasticsearch.WithProperty(req.ZoneVipLevel, "zone_vip_level"), // 私域R级
		elasticsearch.WithRangePay(req.PayAll, "recharge"),             // 玩家累计付费金额

		elasticsearch.WithProperty(req.Scene, "scene"), // 入口
		//elasticsearch.WithProperty(req.Channel, "channel"), // 渠道
		// elasticsearch.WithProperty(req.Label, "tags"),              // 工单标签
		elasticsearch.WithTag(req.TagType, req.Label), // 工单标签
		//elasticsearch.WithProperty(req.SystemLabel, "system_tags"), // 工单系统标签
		elasticsearch.WithSystemTags(req.SystemLabel, "system_tags"),
		elasticsearch.WithProperty(req.Nps, "nps"),                        // nps
		elasticsearch.WithProperty(req.Csi, "csi"),                        // csi
		elasticsearch.WithProperty(req.Language, "lang"),                  // 语种
		elasticsearch.WithProperty(prepareMap["ticket_ids"], "ticket_id"), // 工单id多选
		elasticsearch.WithProperty(req.GameVersion, "app_version"),        // 游戏版本

		elasticsearch.WithSid(req.Sid),                            // 区服
		elasticsearch.WithPropertyTerm(req.TicketId, "ticket_id"), // 工单ID(废弃)
		//elasticsearch.WithPropertyTerm(cast.ToUint64(strings.TrimSpace(req.ReopenNum)), "reopen_num"),   // 重开次数
		//elasticsearch.WithPropertyTerm(cast.ToUint64(strings.TrimSpace(req.UpgradeNum)), "upgrade_num"), // 升级次数

		elasticsearch.WithCreatorExp(req.CreatorType, strings.TrimSpace(req.Creator)),    // 提交人
		elasticsearch.WithAcceptorExp(req.AcceptorType, strings.TrimSpace(req.Acceptor)), // 当前操作人- 工单池(废弃)
		elasticsearch.WithAcceptorsExp(req.AcceptorType, req.Acceptors),                  // 当前操作人- 工单池多选
		elasticsearch.WithProperty(members, "acceptor"),

		//elasticsearch.WithRemarkDetailQuery(strings.TrimSpace(req.Remark), "commus", "commus.detail.keyword", "commus.commu_type"), // 备注
		elasticsearch.WithProperty(req.CatId, "cat_id"), // 问题分类
		elasticsearch.WithIsVip(req.IsVip),              // 是否是 VIP
		elasticsearch.WithIsUpgradeTk(req.IsUpgrade),    // 是否是 升级单
		elasticsearch.WithIsVipCrm(req.VipCrm),          // 是否是vip_crm
		elasticsearch.WithIsSvip(req.Svip),              // 是否是Svip

		// wildCard
		//elasticsearch.WithPropertyMatchPhrase(req.Field, "field"),
	} // 表单

	// 处理工单相关内容(field)的复合查询
	if req.Field != "" {
		field := strings.TrimSpace(req.Field)
		if field != "" {
			if strings.Contains(field, "||") {
				// OR 查询: "投诉"or"举报"
				keywords := strings.Split(field, "||")
				var phrases []string
				for _, k := range keywords {
					k = strings.Trim(k, "\"' \t\n")
					if k != "" {
						phrases = append(phrases, k)
					}
				}
				if len(phrases) > 0 {
					option = append(option, elasticsearch.WithPropertyMatchPhraseOr(phrases, "field"))
				}
			} else if strings.Contains(field, "&&") {
				// AND 查询: "投诉"and"举报"
				keywords := strings.Split(field, "&&")
				var phrases []string
				for _, k := range keywords {
					k = strings.Trim(k, "\"' \t\n")
					if k != "" {
						phrases = append(phrases, k)
					}
				}
				if len(phrases) > 0 {
					option = append(option, elasticsearch.WithPropertyMatchPhraseAnd(phrases, "field"))
				}
			} else {
				// 普通单词查询
				option = append(option, elasticsearch.WithPropertyMatchPhrase(field, "field"))
			}
		}
	}

	if req.SearchType != 0 {
		if req.SearchType == uint32(pb.SearchTypeEnum_SearchTypeSubmitUser) {
			option = append(option, elasticsearch.WithProperty(req.UserType, "user_type")) // user_type
		}
		if req.SearchType == uint32(pb.SearchTypeEnum_SearchTypeQuestionUser) {
			option = append(option, elasticsearch.WithProperty(req.UserType, "trouble_user_type")) // trouble_user_type
		}
	}
	if req.UpgradeNum != "" {
		option = append(option, elasticsearch.WithPropertyTerm(strings.TrimSpace(req.UpgradeNum), "upgrade_num"))
	}
	if req.ReopenNum != "" {
		option = append(option, elasticsearch.WithPropertyTerm(strings.TrimSpace(req.ReopenNum), "reopen_num"))
	}

	// 处理备注信息(remark)的复合查询
	if req.Remark != "" {
		remark := strings.TrimSpace(req.Remark)
		if remark != "" {
			if strings.Contains(remark, "&&") {
				// AND 查询: "投诉"&&"举报"&&"告警"
				keywords := strings.Split(remark, "&&")
				var phrases []string
				for _, k := range keywords {
					k = strings.Trim(k, "\"' \t\n")
					if k != "" {
						phrases = append(phrases, k)
					}
				}
				if len(phrases) > 0 {
					option = append(option, elasticsearch.WithRemarkDetailQueryAnd(phrases))
				}
			} else if strings.Contains(remark, "||") {
				// OR 查询: "投诉"||"举报"
				keywords := strings.Split(remark, "||")
				var phrases []string
				for _, k := range keywords {
					k = strings.Trim(k, "\"' \t\n")
					if k != "" {
						phrases = append(phrases, k)
					}
				}
				if len(phrases) > 0 {
					option = append(option, elasticsearch.WithRemarkDetailQueryOr(phrases))
				}
			} else {
				// 普通单词查询
				option = append(option, elasticsearch.WithRemarkDetailQuery(remark, "commus", "commus.detail.keyword", "commus.commu_type"))
			}
		}
	}

	if len(req.PackageId) > 0 {
		// 渠道号
		option = append(option, elasticsearch.WithProperty(req.PackageId, "package_id.keyword"))
	} else {
		// 渠道
		option = append(option, elasticsearch.WithProperty(req.Channel, "channel"))
	}
	return option, nil
}

// DiscordPool discord条件过滤
func (ad *AdHocWrapper) DiscordPool(ctx context.Context, req *pb.DscUserListReq, prepareMap map[string][]string) ([]elasticsearch.Option, error) {
	option := []elasticsearch.Option{
		// range
		elasticsearch.WithServicePropertyNested(int(pb.DscMsgFromTpDf_DscMsgFromTpDfRobot), req.RepliedAt, "dsc_commu"), // 客服回复时间
		elasticsearch.WithDetailRangeTime(req.LastLogin, "last_login"),
		elasticsearch.WithRangePay(req.PayAll, "pay_all"),                         // 玩家累计付费金额
		elasticsearch.WithRangePay(req.PayLastThirtyDays, "pay_last_thirty_days"), // 玩家最近30天付费金额
		elasticsearch.WithRangeBirthday(req.Birthday, "birthday_month_day"),       // 玩家生日

		// term query
		elasticsearch.WithProperty(req.Status, "reply_type"), // 状态：枚举 待回复、已回复
		elasticsearch.WithProperty(req.Project, "project"),   // 游戏项目, 支持多选

		elasticsearch.WithTags(req.TagType, req.Tags, "new_label"),     // 玩家画像标签, 支持多选
		elasticsearch.WithPropertyTerm(req.Lang, "lang"),               // 玩家语种
		elasticsearch.WithProperty(prepareMap["uids"], "uid"),          // 玩家uid
		elasticsearch.WithPropertyTerm(req.Lang, "lang"),               // 玩家语种(弃用)
		elasticsearch.WithPropertyTerm(req.Fpid, "account_id"),         // 玩家fpid
		elasticsearch.WithPropertyTerm(req.VipState, "vip_state"),      // 玩家vip状态
		elasticsearch.WithPropertyTerm(code.DscNoDelete, "is_deleted"), // 过滤掉已删除的文档
		elasticsearch.WithSid(req.Sid),                                 // 玩家服务器,支持多选

		// Wildcard
		elasticsearch.WithDcOrLineRemark(strings.TrimSpace(req.UserDetailRemark), "portrait_remark.keyword"),                      // 玩家画像备注信息
		elasticsearch.WithUserContent(int(pb.DscMsgFromTpDf_DscMsgFromTpDfUser), strings.TrimSpace(req.UserContent), "dsc_commu"), // 玩家输入信息
		elasticsearch.WithProperty(req.Processor, "maintainer"),                                                                   // 维护人，支持多选
		elasticsearch.WithProperty(req.LastReplyService, "last_reply_service"),                                                    // 最近处理人，支持多选
		elasticsearch.WithDscUserName(req.DscUserNickname),                                                                        // 玩家DC昵称，下拉多选，支持模糊搜索
		elasticsearch.WithPropertyTerm(req.DscUserId, "dsc_user_id"),
		elasticsearch.WithProperty(req.BotIds, "app_id"), // 机器人字段搜索
		elasticsearch.WithProperty(req.Language, "lang.keyword"),
	}
	return option, nil
}

// LinePool line条件过滤
func (ad *AdHocWrapper) LinePool(ctx context.Context, req *pb.LineUserListReq, uids []string) ([]elasticsearch.Option, error) {
	option := []elasticsearch.Option{
		// range
		elasticsearch.WithServicePropertyNested(int(pb.DscMsgFromTpDf_DscMsgFromTpDfRobot), req.RepliedAt, "line_commu"), // 客服回复时间
		elasticsearch.WithDetailRangeTime(req.LastLogin, "last_login"),
		elasticsearch.WithRangePay(req.PayAll, "pay_all"),                         // 玩家累计付费金额
		elasticsearch.WithRangePay(req.PayLastThirtyDays, "pay_last_thirty_days"), // 玩家最近30天付费金额
		elasticsearch.WithRangeBirthday(req.Birthday, "birthday_month_day"),       // 玩家生日

		// term query
		elasticsearch.WithProperty(req.Status, "reply_type"),           // 状态：枚举 待回复、已回复
		elasticsearch.WithProperty(req.Project, "project"),             // 游戏项目, 支持多选
		elasticsearch.WithTags(req.TagType, req.Tags, "tag"),           // 玩家画像标签, 支持多选
		elasticsearch.WithProperty(uids, "uid"),                        // 玩家uid
		elasticsearch.WithPropertyTerm(req.Lang, "lang"),               // 玩家语种
		elasticsearch.WithPropertyTerm(req.Fpid, "account_id"),         // 玩家fpid
		elasticsearch.WithPropertyTerm(req.VipState, "vip_state"),      // 玩家vip状态
		elasticsearch.WithPropertyTerm(code.DscNoDelete, "is_deleted"), // 过滤掉已删除的文档
		elasticsearch.WithSid(req.Sid),                                 // 玩家服务器,支持多选
		elasticsearch.WithProperty(req.ChannelId, "channel_id"),        // 频道 ID，支持多选

		// Wildcard
		elasticsearch.WithDcOrLineRemark(strings.TrimSpace(req.UserDetailRemark), "portrait_remark.keyword"),                       // 玩家画像备注信息
		elasticsearch.WithUserContent(int(pb.DscMsgFromTpDf_DscMsgFromTpDfUser), strings.TrimSpace(req.UserContent), "line_commu"), // 玩家输入信息
		elasticsearch.WithProperty(req.Maintainer, "maintainer"),                                                                   // 维护人，支持多选
		elasticsearch.WithProperty(req.LastReplyService, "last_reply_service"),                                                     // 最近处理人，支持多选
		elasticsearch.WithLineDisplayName([]string{req.DisplayName}),                                                               // 玩家DC昵称，下拉多选，支持模糊搜索
		elasticsearch.WithPropertyTerm(req.LineUserId, "line_user_id"),
		elasticsearch.WithProperty(req.BotIds, "bot_id"), // 机器人字段搜索
	}
	return option, nil
}

func (ad *AdHocWrapper) ExamineDscFilterCount(ctx context.Context, req *pb.ExamineTaskDscFilterCountReq) ([]elasticsearch.Option, error) {
	option := []elasticsearch.Option{
		elasticsearch.WithProperty([]string{req.Project}, "project"),                                                    // 游戏项目, 支持多选
		elasticsearch.WithRangePay([]int64{req.TotalPay, 0}, "pay_all"),                                                 // 玩家累计付费金额
		elasticsearch.WithServicePropertyNested(int(pb.DscMsgFromTpDf_DscMsgFromTpDfRobot), req.RepliedAt, "dsc_commu"), // 客服回复时间
		elasticsearch.WithPropertyTerm(code.DscNoDelete, "is_deleted"),                                                  // 过滤掉已删除的文档
	}
	return option, nil
}

// ExamineDscPool examine dsc 条件过滤
func (ad *AdHocWrapper) ExamineDscPool(ctx context.Context, req *pb.ExamineDscOrderListReq) ([]elasticsearch.Option, error) {
	option := []elasticsearch.Option{
		elasticsearch.WithProperty(req.Project, "project"),                 // 游戏项目, 支持多选
		elasticsearch.WithPropertyTerm(req.DscExamineId, "examine_dsc_id"), // 质检单-ID
		elasticsearch.WithProperty(req.Status, "status"),                   // 质检单状态: 处理中/已完成
		elasticsearch.WithProperty(req.FinalResult, "final_result"),        // 质检结果：通过/不通过
		elasticsearch.WithProperty(req.RelatedAccount, "related_account"),  // 被检员：质检打分时关联的员工
		elasticsearch.WithProperty(req.Inspector, "inspector"),             // 质检员

		elasticsearch.WithRangeTime(req.CreatedAt, "created_at"),   // 创建时间
		elasticsearch.WithRangeTime(req.FinishedAt, "finished_at"), // 结案时间
		elasticsearch.WithProperty(req.VipState, "gen_vip_state"),  // 质检单生成时：玩家vip状态

	}
	return option, nil
}

func (ad *AdHocWrapper) ExamineDscPartFilterCount(ctx context.Context, req *pb.ExamineTaskDscPartFilterCountReq) ([]elasticsearch.Option, error) {
	option := []elasticsearch.Option{
		elasticsearch.WithProperty([]string{req.Project}, "project"),                                                    // 游戏项目, 支持多选
		elasticsearch.WithServicePropertyNested(int(pb.DscMsgFromTpDf_DscMsgFromTpDfRobot), req.RepliedAt, "dsc_commu"), // 客服回复时间
		elasticsearch.WithPropertyTerm(code.DscNoDelete, "is_deleted"),                                                  // 过滤掉已删除的文档
	}
	if req.TotalPay > 0 {
		option = append(option, elasticsearch.WithRangePay([]int64{0, req.TotalPay}, "pay_all")) // 玩家累计付费金额
	}
	if req.IsPart == true {
		option = append(option,
			elasticsearch.WithProperty(req.VipState, "vip_state"),    // 玩家vip状态
			elasticsearch.WithProperty(req.Maintainer, "maintainer"), // 维护人，支持多选
		)
	}
	return option, nil
}
