# Ops-Ticket-API 代码复用索引

本文档提供了项目中各个模块的功能说明和主要方法，帮助开发者快速了解和复用现有代码，避免重复造轮子。

## 目录

- [处理器 (Handlers)](#处理器-handlers)
- [服务层 (Services)](#服务层-services)
- [内部框架 (Internal)](#内部框架-internal)
- [持久层 (Persistence)](#持久层-persistence)
- [工具函数 (Utils)](#工具函数-utils)

## 处理器 (Handlers)

处理器负责接收HTTP请求并调用相应的服务层方法处理业务逻辑。

### 基础处理器

| 文件 | 功能 | 主要方法 |
|------|------|---------|
| `handlers/heath.go` | 健康检查 | `Health(ctx echo.Context) error` - 提供健康检查接口 |

### 内部处理器 (`handlers/inner/`)

| 文件 | 功能 | 主要方法 |
|------|------|---------|
| `account.go` | 账户管理 | 处理用户账户相关请求 |
| `addons.go` | 附加功能 | 处理系统附加功能请求 |
| `ai.go` | AI功能 | 处理AI相关功能请求 |
| `all_tags.go` | 标签管理 | 处理所有标签相关请求 |
| `auth_config.go` | 认证配置 | 处理认证配置相关请求 |
| `catetory.go` | 分类管理 | 处理工单分类相关请求 |
| `channel_category.go` | 渠道分类 | 处理渠道分类相关请求 |
| `data_plat_user_info.go` | 数据平台用户 | 处理数据平台用户信息请求 |
| `dict.go` | 字典管理 | 处理系统字典相关请求 |
| `dsc_commu.go` | Discord社区 | 处理Discord社区相关请求 |
| `dsc_handler.go` | Discord处理 | 处理Discord相关请求 |
| `dsc_new_commu.go` | Discord新社区 | 处理Discord新社区相关请求 |
| `dsc_stats.go` | Discord统计 | 处理Discord统计相关请求 |
| `dsc_tab.go` | Discord标签页 | 处理Discord标签页相关请求 |
| `dsc_tag.go` | Discord标签 | 处理Discord标签相关请求 |
| `group.go` | 分组管理 | 处理分组相关请求 |
| `line_commu.go` | Line社区 | 处理Line社区相关请求 |
| `line_handler.go` | Line处理 | 处理Line相关请求 |
| `line_maintain_config.go` | Line维护配置 | 处理Line维护配置相关请求 |
| `line_portrait.go` | Line用户画像 | 处理Line用户画像相关请求 |
| `line_tab.go` | Line标签页 | 处理Line标签页相关请求 |
| `maintain_config.go` | 维护配置 | 处理维护配置相关请求 |
| `module.go` | 模块管理 | 处理模块相关请求 |
| `module_category.go` | 模块分类 | 处理模块分类相关请求 |
| `overtime_tpl.go` | 超时模板 | 处理超时模板相关请求 |
| `portrait.go` | 用户画像 | 处理用户画像相关请求 |
| `reply_tpl.go` | 回复模板 | 处理回复模板相关请求 |
| `survey.go` | 调查问卷 | 处理调查问卷相关请求 |
| `tags_lib.go` | 标签库 | 处理标签库相关请求 |
| `team_config.go` | 团队配置 | 处理团队配置相关请求 |
| `ticket_operate.go` | 工单操作 | 处理工单操作相关请求 |
| `ticket_tab.go` | 工单标签页 | 处理工单标签页相关请求 |
| `user_assign_ticket.go` | 用户分配工单 | 处理用户分配工单相关请求 |
| `workpool.go` | 工作池 | 处理工作池相关请求 |

## 服务层 (Services)

服务层包含业务逻辑处理，是系统的核心部分。

### 工单服务 (`services/`)

| 文件 | 功能 | 主要方法 |
|------|------|---------|
| `ticket_service.go` | 工单服务 | `TicketDetail` - 获取工单详情<br>`TicketMine` - 获取我的工单<br>`TicketCreate` - 创建工单<br>`TicketReplenish` - 补充工单信息<br>`TicketReopen` - 重新打开工单<br>`TkAppraise` - 工单评价<br>`TkCommunicate` - 工单沟通 |
| `ticket_operate.go` | 工单操作 | `Reassign` - 指派工单<br>`BatchReassign` - 批量指派工单<br>`TicketTurn` - 工单转交<br>`TicketsUpgrade` - 工单升级<br>`TicketReTags` - 工单重新标记<br>`TicketRemark` - 工单备注<br>`TicketReturnPool` - 工单退回池<br>`TicketTransfer` - 工单转移<br>`AutoAllocAcceptor` - 自动分配受理人 |
| `ticket_info.go` | 工单信息 | 处理工单信息相关业务逻辑 |
| `ticket_tab.go` | 工单标签页 | 处理工单标签页相关业务逻辑 |
| `user.go` | 用户服务 | 处理用户相关业务逻辑 |

### Discord服务 (`services/`)

| 文件 | 功能 | 主要方法 |
|------|------|---------|
| `dsc_commu.go` | Discord社区 | 处理Discord社区相关业务逻辑 |
| `dsc_export.go` | Discord导出 | 处理Discord数据导出相关业务逻辑 |
| `dsc_message_task.go` | Discord消息任务 | 处理Discord消息任务相关业务逻辑 |
| `dsc_new_commu.go` | Discord新社区 | 处理Discord新社区相关业务逻辑 |
| `dsc_operate.go` | Discord操作 | 处理Discord操作相关业务逻辑 |
| `dsc_stats.go` | Discord统计 | 处理Discord统计相关业务逻辑 |
| `dsc_tab.go` | Discord标签页 | 处理Discord标签页相关业务逻辑 |
| `dsc_tag.go` | Discord标签 | 处理Discord标签相关业务逻辑 |
| `dsc-info.go` | Discord信息 | 处理Discord信息相关业务逻辑 |

### Line服务 (`services/`)

| 文件 | 功能 | 主要方法 |
|------|------|---------|
| `line_commu.go` | Line社区 | 处理Line社区相关业务逻辑 |
| `line_export.go` | Line导出 | 处理Line数据导出相关业务逻辑 |
| `line_info.go` | Line信息 | 处理Line信息相关业务逻辑 |
| `line_operate.go` | Line操作 | 处理Line操作相关业务逻辑 |
| `line_tab.go` | Line标签页 | 处理Line标签页相关业务逻辑 |

### Discord核心服务 (`services/dsccore/`)

| 文件 | 功能 | 主要方法 |
|------|------|---------|
| `channel_message.go` | 频道消息 | 处理Discord频道消息相关业务逻辑 |
| `channel_message_reaction.go` | 消息反应 | 处理Discord消息反应相关业务逻辑 |
| `channel_message_sync.go` | 消息同步 | 处理Discord消息同步相关业务逻辑 |
| `channel_unread.go` | 未读消息 | 处理Discord未读消息相关业务逻辑 |
| `local_cache_user.go` | 本地缓存用户 | 处理Discord本地缓存用户相关业务逻辑 |

### 工单子服务 (`services/ticket/`)

| 文件 | 功能 | 主要方法 |
|------|------|---------|
| `export.go` | 工单导出 | 处理工单导出相关业务逻辑 |
| `ticket_create_rules.go` | 工单创建规则 | 处理工单创建规则相关业务逻辑 |

### 其他服务

| 目录 | 功能 | 说明 |
|------|------|------|
| `services/ai/` | AI服务 | 处理AI相关业务逻辑 |
| `services/commsrv/` | 通信服务 | 处理通信相关业务逻辑 |
| `services/communicate/` | 沟通服务 | 处理沟通相关业务逻辑 |
| `services/configure/` | 配置服务 | 处理配置相关业务逻辑 |
| `services/dataplatusersrv/` | 数据平台用户服务 | 处理数据平台用户相关业务逻辑 |
| `services/examinesrv/` | 审核服务 | 处理审核相关业务逻辑 |
| `services/monitor/` | 监控服务 | 处理监控相关业务逻辑 |
| `services/reporter/` | 报表服务 | 处理报表相关业务逻辑 |
| `services/survey/` | 调查问卷服务 | 处理调查问卷相关业务逻辑 |

## 内部框架 (Internal)

内部框架提供了系统的基础设施和通用功能。

### 框架组件 (`internal/framework/`)

| 目录 | 功能 | 说明 |
|------|------|------|
| `build/` | 构建工具 | 提供系统构建相关功能 |
| `cache/` | 缓存管理 | 提供Redis缓存管理功能 |
| `cfg/` | 配置管理 | 提供系统配置管理功能 |
| `ctxresp/` | 上下文响应 | 提供HTTP上下文响应处理功能 |
| `database/` | 数据库管理 | 提供数据库连接和操作功能 |
| `lang/` | 语言管理 | 提供国际化和多语言支持 |
| `maxprocs/` | 进程管理 | 提供最大进程数管理功能 |
| `net/` | 网络工具 | 提供网络相关工具函数 |
| `plugins/` | 插件系统 | 提供系统插件管理功能 |
| `proxy/` | 代理管理 | 提供HTTP代理管理功能 |
| `stores/` | 存储管理 | 提供数据存储管理功能 |
| `validator/` | 数据验证 | 提供数据验证功能 |
| `xerrors/` | 错误处理 | 提供统一的错误处理功能 |

### 其他内部组件

| 目录 | 功能 | 说明 |
|------|------|------|
| `internal/code/` | 错误码 | 定义系统错误码 |
| `internal/cst/` | 常量 | 定义系统常量 |
| `internal/dsc/` | Discord集成 | 提供Discord集成功能 |
| `internal/elasticsearch/` | ES集成 | 提供Elasticsearch集成功能 |

## 持久层 (Persistence)

持久层负责数据的存储和检索，是系统与数据库交互的桥梁。

### 主要持久层组件 (`internal/persistence/`)

| 文件 | 功能 | 主要方法 |
|------|------|---------|
| `ticket.go` | 工单持久化 | `GetTicketDetail` - 获取工单详情<br>`GetTicketList` - 获取工单列表<br>`CreateTicket` - 创建工单<br>`UpdateTicket` - 更新工单 |
| `user.go` | 用户持久化 | 处理用户数据的存储和检索 |
| `tags.go` | 标签持久化 | 处理标签数据的存储和检索 |
| `tags_lib.go` | 标签库持久化 | 处理标签库数据的存储和检索 |
| `reply_tpl.go` | 回复模板持久化 | 处理回复模板数据的存储和检索 |
| `module.go` | 模块持久化 | 处理模块数据的存储和检索 |
| `category.go` | 分类持久化 | 处理分类数据的存储和检索 |

### Discord相关持久层

| 文件 | 功能 | 主要方法 |
|------|------|---------|
| `dsc_commu.go` | Discord社区持久化 | 处理Discord社区数据的存储和检索 |
| `dsc_interact.go` | Discord交互持久化 | 处理Discord交互数据的存储和检索 |
| `dsc_interactions.go` | Discord互动持久化 | 处理Discord互动数据的存储和检索 |
| `dsc_message_task.go` | Discord消息任务持久化 | 处理Discord消息任务数据的存储和检索 |
| `dsc_new_commu.go` | Discord新社区持久化 | 处理Discord新社区数据的存储和检索 |
| `dsc_survey.go` | Discord调查持久化 | 处理Discord调查数据的存储和检索 |
| `dsc_tab.go` | Discord标签页持久化 | 处理Discord标签页数据的存储和检索 |
| `dsc_tag.go` | Discord标签持久化 | 处理Discord标签数据的存储和检索 |

### Line相关持久层

| 文件 | 功能 | 主要方法 |
|------|------|---------|
| `line_commu.go` | Line社区持久化 | 处理Line社区数据的存储和检索 |
| `line_interactions.go` | Line互动持久化 | 处理Line互动数据的存储和检索 |
| `line_maintain_config.go` | Line维护配置持久化 | 处理Line维护配置数据的存储和检索 |
| `line_portrait.go` | Line用户画像持久化 | 处理Line用户画像数据的存储和检索 |
| `line_tab.go` | Line标签页持久化 | 处理Line标签页数据的存储和检索 |
| `line_tag.go` | Line标签持久化 | 处理Line标签数据的存储和检索 |

## 工具函数 (Utils)

工具函数提供了系统中常用的辅助功能。

### 主要工具函数

| 功能 | 说明 | 常用方法 |
|------|------|---------|
| 字符串处理 | 提供字符串处理相关功能 | 字符串转换、格式化、验证等 |
| 时间处理 | 提供时间处理相关功能 | 时间格式化、计算、比较等 |
| JSON处理 | 提供JSON处理相关功能 | JSON序列化、反序列化等 |
| 加密解密 | 提供加密解密相关功能 | MD5、SHA、AES等加密算法 |
| HTTP客户端 | 提供HTTP请求相关功能 | GET、POST等HTTP请求方法 |
| 文件处理 | 提供文件处理相关功能 | 文件读写、上传下载等 |

## 代码复用最佳实践

1. **使用现有服务**：在开发新功能时，优先考虑使用现有的服务和方法，避免重复实现相同的功能。

2. **遵循分层架构**：遵循处理器(Handlers) -> 服务层(Services) -> 持久层(Persistence)的分层架构，保持代码的清晰和可维护性。

3. **使用内部框架**：使用项目提供的内部框架组件，如缓存管理、配置管理、错误处理等，保持代码的一致性。

4. **参考现有实现**：在实现新功能时，参考类似功能的现有实现，保持代码风格和实现方式的一致性。

5. **使用工具函数**：使用项目提供的工具函数，避免重复实现常用功能。

## 常见功能实现示例

### 1. 创建新的HTTP处理器

```go
// handlers/your_handler.go
package handlers

import (
    "github.com/labstack/echo/v4"
    "ops-ticket-api/internal/framework/ctxresp"
    "ops-ticket-api/services"
)

// YourHandler 处理某功能请求
func YourHandler(ctx echo.Context) error {
    // 获取请求参数
    param := &YourParam{}
    if err := ctx.Bind(param); err != nil {
        return err
    }
    
    // 调用服务层处理业务逻辑
    result, err := services.NewYourService().YourMethod(ctx, param)
    if err != nil {
        return err
    }
    
    // 返回结果
    return ctxresp.Out(ctx, result)
}
```

### 2. 创建新的服务方法

```go
// services/your_service.go
package services

import (
    "github.com/labstack/echo/v4"
    "ops-ticket-api/internal/persistence"
)

func NewYourService() *yourService {
    return &yourService{}
}

type yourService struct {
}

// YourMethod 处理某业务逻辑
func (srv *yourService) YourMethod(ctx echo.Context, param *YourParam) (*YourResult, error) {
    // 业务逻辑处理
    
    // 调用持久层
    data, err := persistence.NewYourPersistence().GetData(ctx, param.ID)
    if err != nil {
        return nil, err
    }
    
    // 处理结果
    result := &YourResult{
        // ...
    }
    
    return result, nil
}
```

### 3. 使用缓存

```go
// 使用Redis缓存
import (
    "ops-ticket-api/internal/framework/cache/keys"
    "ops-ticket-api/internal/framework/cache/rds"
)

// 设置缓存
err := rds.Set(ctx.Request().Context(), keys.YourKey(id), data, time.Hour)
if err != nil {
    // 处理错误
}

// 获取缓存
var data YourData
exists, err := rds.Get(ctx.Request().Context(), keys.YourKey(id), &data)
if err != nil {
    // 处理错误
}
if !exists {
    // 缓存不存在，从数据库获取
}
```

### 4. 数据库操作

```go
// 使用GORM进行数据库操作
import (
    "ops-ticket-api/internal/framework/database"
    "ops-ticket-api/models"
)

// 查询数据
var data models.YourModel
err := database.DB().Where("id = ?", id).First(&data).Error
if err != nil {
    // 处理错误
}

// 创建数据
newData := &models.YourModel{
    // ...
}
err := database.DB().Create(newData).Error
if err != nil {
    // 处理错误
}

// 更新数据
err := database.DB().Model(&models.YourModel{}).Where("id = ?", id).Updates(map[string]interface{}{
    "field1": value1,
    "field2": value2,
}).Error
if err != nil {
    // 处理错误
}
```

## 结语

本文档提供了项目中各个模块的功能说明和主要方法，帮助开发者快速了解和复用现有代码。在开发新功能时，请优先考虑使用现有的服务和方法，避免重复实现相同的功能。如有任何疑问，请参考相应模块的源代码或咨询相关开发人员。 