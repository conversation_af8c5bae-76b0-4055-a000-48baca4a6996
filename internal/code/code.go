// Copyright 2021 funplus Authors. All Rights Reserved.
// @Description: 状态码
// @Author: Darcy
// @Date: 2021/10/13 2:10 PM

package code

const (
	// StatusTrue 真值
	StatusTrue = 1
	// StatusFalse 假值
	StatusFalse = 0
	// ModuleEnable 启用模板
	ModuleEnable = 1
	// ModuleDisable 禁用模板
	ModuleDisable = 2
	// DscNoDelete discord标记未删除
	DscNoDelete = 1
	// DscDeleted discord标记已删除
	DscDeleted = 2
	// LineFollowed 关注状态
	LineFollowed = 1
	// LineUnFollowed 未关注状态
	LineUnFollowed = 2
	// Success code
	Success = 0
	// Error err
	Error = 1

	// 排序字段 - 已完成订单 排序到最后
	WaitStartAtForFinished = uint64(4102416000) // 2100-01-01 00:00:00

	// InvalidParams 参数错误
	InvalidParams = 1001
	// MissingParams 缺少参数
	MissingParams = 1002

	DbError        = 1101
	NotFound       = 1102
	DataConflict   = 1103
	DbDataUnexpect = 1104

	// IdempotenceErr 幂等
	IdempotenceErr = 1204

	RpcCallErr           = 1501
	RpcRespDecodeErr     = 1502
	RpcRespErr           = 1503
	AgentError           = 1504
	DialDeadlineExceeded = 1505
	DialError            = 1506

	TkClosed       = 2001
	TkReceiptLimit = 2002

	ClosedTkForbidCommu  = 2005
	WorkgroupsConflict   = 2007
	ProofClosedByTimeout = 2008
	AssigneeChanged      = 2009
	ResolvedCannotOp     = 2010
	NoAvailableTickets   = 2011
	TryAgain             = 2012
	RepeatData           = 2013
	NoPermission         = 2014
	ExceededLimit        = 2015
	RecordJumpLimit      = 2016
	RevokeTicketRepeat   = 2017
	UserIsNotOnline      = 2018
	EsQueryErr           = 2019
	TicketCatRepeat      = 2022
	TicketReassignReply  = 2023
	TicketReopenReply    = 2024

	RelateTypeTpl     = 1
	RelateTypeProcess = 2

	ExamineTplForbidden          = 2030
	ExamineOrderModifiedOnlyOnce = 2031
	ExamineOrderInspectorNoMatch = 2032

	TimeLayout           = "2006-01-02 15:04:05"
	RechargeRate         = 10000.0             // 充值金额 rate
	VipRechargePayAmount = 5000 * RechargeRate // 5000 美金

	NoAvailablePrivCard = 200001 // 没有可用的特权卡
)

var statusText = map[int]string{
	Success: "RespSuccess",
	Error:   "RespFail",

	TkClosed:             "RespTkClosed",
	TkReceiptLimit:       "RespTkReceiptLimit",
	TicketCatRepeat:      "RespTicketRepeat",
	WorkgroupsConflict:   "WorkgroupsConflictTip",
	ProofClosedByTimeout: "RespProofClosedByTimeout",
	ClosedTkForbidCommu:  "ClosedTicketForbiddenCommunicate",
	AssigneeChanged:      "AssigneeChangedTip",
	ResolvedCannotOp:     "ResolvedCannotOpTip",
	NoAvailableTickets:   "NoAvailableTicketsTip",
	TryAgain:             "TryAgainTip",
	RepeatData:           "RepeatDataTip",
	NoPermission:         "NoPermissionTip",
	ExceededLimit:        "ExceededLimit",
	RecordJumpLimit:      "RecordJumpLimit",
	RevokeTicketRepeat:   "RevokeTicketRepeatTip",
	TicketReassignReply:  "TicketReassignReplyFailTip",
	TicketReopenReply:    "TicketReopenReplyFailTip",

	InvalidParams: "RespBindParamsFail",
	MissingParams: "RespInvalidParams",

	DbError:  "RespInternalServer",
	NotFound: "RespDataUnExist",

	DataConflict:    "RespDataConflict",
	DbDataUnexpect:  "RespInternalServer",
	IdempotenceErr:  "RespIdempotenceErr",
	UserIsNotOnline: "UserIsNotOnlineErr",

	RpcCallErr:           "RespInnerException",
	RpcRespDecodeErr:     "RespInnerException",
	RpcRespErr:           "RespInnerException",
	AgentError:           "RespInnerException",
	DialDeadlineExceeded: "RespInnerException",
	DialError:            "RespInnerException",

	ExamineTplForbidden:          "ExamineTplForbiddenTip",
	ExamineOrderModifiedOnlyOnce: "ExamineOrderModifiedOnlyOnceTip",
	ExamineOrderInspectorNoMatch: "ExamineOrderInspectorNoMatchTip",

	NoAvailablePrivCard: "NoAvailablePrivCard",
}

func StatusText(code int) string {
	return statusText[code]
}
