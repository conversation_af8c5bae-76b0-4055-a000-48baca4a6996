package ai_model

import (
	"sync"

	"github.com/darcyx/go-openai"
	"github.com/spf13/viper"
)

var (
	FastModel      = openai.GPT3Dot5Turbo
	SmartModel     = openai.GPT4
	EmbeddingModel = "text-embedding-ada-002"
	DallEModel     = "dall-e"

	openAiOnce, openAiSwitchOnce sync.Once
	mu                           sync.RWMutex
	clientList                   = make(map[string]*openai.Client)
	tagClientList                = make(map[string]*openai.Client)
)

func Init() {
	openAiOnce.Do(func() {
		var client *openai.Client
		var tagClient *openai.Client
		// openai api
		if viper.GetString("openai.type") == "openai" {
			client = openai.NewClient(viper.GetString("openai.token"))
			tagClient = openai.NewClient(viper.GetString("openai.token"))
		}
		// azure api proxy
		if viper.GetString("openai.type") == "azure" {
			// 主模型 key
			cfg := openai.DefaultConfig(viper.GetString("openai.azure_apikey"))
			cfg.BaseURL = viper.GetString("openai.azure_endpoint")
			client = openai.NewClientWithConfig(cfg)

			// 专门打标签用的 key
			tagCfg := openai.DefaultConfig(viper.GetString("openai.tag_azure_apikey"))
			tagCfg.BaseURL = viper.GetString("openai.azure_endpoint")
			tagClient = openai.NewClientWithConfig(tagCfg)
		}

		clientList[FastModel] = client
		clientList[SmartModel] = client
		clientList[EmbeddingModel] = client
		clientList[openai.GPT3Dot5Turbo16K] = client
		clientList[openai.GPT432K] = client
		// hacker dallE client
		clientList[DallEModel] = openai.NewClient(viper.GetString("openai.token"))
		clientList[openai.GPT3Dot5Turbo1106] = client
		clientList[openai.GPT4TurboPreview] = client
		clientList[openai.GPT4VisionPreview] = client

		tagClientList[FastModel] = tagClient
		tagClientList[SmartModel] = tagClient
		tagClientList[EmbeddingModel] = tagClient
		tagClientList[openai.GPT3Dot5Turbo16K] = tagClient
		tagClientList[openai.GPT432K] = tagClient
		// hacker dallE client
		tagClientList[DallEModel] = openai.NewClient(viper.GetString("openai.token"))
		tagClientList[openai.GPT3Dot5Turbo1106] = tagClient
		tagClientList[openai.GPT4TurboPreview] = tagClient
		tagClientList[openai.GPT4VisionPreview] = tagClient
	})
}

func Engine(model string) *openai.Client {
	return clientList[model]
}

func EngineTag(model string) *openai.Client {
	return tagClientList[model]
}
