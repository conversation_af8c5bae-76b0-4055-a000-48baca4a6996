package persistence

import (
	"context"
	"strings"
	"time"

	"github.com/spf13/cast"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"ops-ticket-api/internal/code"
	"ops-ticket-api/internal/framework/database"
	"ops-ticket-api/internal/framework/xerrors"
	"ops-ticket-api/models"
	"ops-ticket-api/proto/pb"
)

// ReplyTpl 问题模版
type ReplyTpl struct {
	db *gorm.DB
}

// NewReplyTpl init
func NewReplyTpl(ctx any) *ReplyTpl {
	return &ReplyTpl{
		db: database.DbSession(ctx, database.Db()),
	}
}

// ReplyTplOpts 模版筛选列表
func (dto *ReplyTpl) ReplyTplOpts(ctx context.Context) ([]*pb.TplOptsResp_Opts, error) {
	dest := make([]*pb.TplOptsResp_Opts, 0)
	field := "id, reply_tpl as tpl"
	if err := dto.db.WithContext(ctx).Model(&models.FpOpsReplyTpl{}).Select(field).Where("enable=?", code.StatusTrue).Find(&dest).Error; err != nil {
		logger.Error(ctx, "get reply tpl opts err", zap.String("err", err.Error()))
		return nil, xerrors.New(err.Error(), code.DbError)
	}
	return dest, nil
}

// ReplyTplList 模版列表
func (dto *ReplyTpl) ReplyTplList(ctx context.Context, tplName string, page, pageSize *uint32) ([]*models.FpOpsReplyTpl, uint32, error) {
	dest := make([]*models.FpOpsReplyTpl, 0)
	field := "id, reply_tpl, updated_at, operator, enable"
	query := dto.db.WithContext(ctx).Model(&models.FpOpsReplyTpl{}).Select(field)
	if tplName != "" {
		query = query.Where("reply_tpl like ?", "%"+tplName+"%")
	}
	var count int64
	if err := query.Count(&count).Error; err != nil {
		logger.Error(ctx, "get reply tpl list count err", zap.String("tpl_name", tplName), zap.String("err", err.Error()))
		return nil, 0, err
	}
	if err := query.Scopes(database.Paginate(page, pageSize)).Order("id asc").Find(&dest).Error; err != nil {
		logger.Error(ctx, "get reply tpl list err", zap.String("tpl_name", tplName), zap.String("err", err.Error()))
		return nil, 0, xerrors.New(err.Error(), code.DbError)
	}
	return dest, cast.ToUint32(count), nil
}

// ReplyTplInfo 模版信息
func (dto *ReplyTpl) ReplyTplInfo(ctx context.Context, tplId uint32, langNeed bool) (*models.FpOpsReplyTpl, error) {
	tplInfo := &models.FpOpsReplyTpl{}
	tx := dto.db.WithContext(ctx).Model(&models.FpOpsReplyTpl{})
	if langNeed {
		tx = tx.Preload("Language")
	}
	if err := tx.Where("id=?", tplId).Take(&tplInfo).Error; err != nil {
		logger.Error(ctx, "get reply tpl info err", zap.Uint32("reply_tplId", tplId), zap.String("err", err.Error()))
		return nil, xerrors.New(err.Error(), code.DbError)
	}
	return tplInfo, nil
}

// ReplyTplAdd 模版添加
func (dto *ReplyTpl) ReplyTplAdd(ctx context.Context, tpl *models.FpOpsReplyTpl) error {
	if err := dto.db.WithContext(ctx).Create(&tpl).Error; err != nil {
		errMsg := err.Error()
		logger.Error(ctx, "reply tpl add err", zap.String("reply_tpl", tpl.ReplyTpl), zap.String("err", errMsg))
		if strings.Contains(errMsg, "Error 1062: Duplicate entry") {
			errMsg = "EventDupCatAddLog"
		}
		return xerrors.New(errMsg, code.DbError)
	}
	return nil
}

// ReplyTplSave 模版更新
func (dto *ReplyTpl) ReplyTplSave(ctx context.Context, tplId uint32, tpl *models.FpOpsReplyTpl) (err error) {
	tx := dto.db.WithContext(ctx).Begin()
	defer func() {
		if err != nil {
			tx.Rollback()
		}
	}()
	if err = tx.Where("reply_tpl_id=?", tplId).Delete(&models.FpOpsReplyTplLang{}).Error; err != nil {
		return xerrors.New(err.Error(), code.DbError)
	}
	if err = tx.Model(models.FpOpsReplyTplLang{}).Create(tpl.Language).Error; err != nil {
		return xerrors.New(err.Error(), code.DbError)
	}
	tpl.Language = nil
	if err = tx.Where("id=?", tplId).Updates(tpl).Error; err != nil {
		errMsg := err.Error()
		logger.Error(ctx, "reply tpl save err", zap.Uint32("tplId", tplId), zap.String("err", errMsg))
		if strings.Contains(errMsg, "Error 1062: Duplicate entry") {
			errMsg = "EventDupCatAddLog"
		}
		return xerrors.New(errMsg, code.DbError)
	}
	return tx.Commit().Error
}

// ReplyTplEnable 模版禁用启用接口
func (dto *ReplyTpl) ReplyTplEnable(ctx context.Context, tplId uint32, enable bool, operator string) error {
	update := map[string]interface{}{
		"enable":     enable,
		"operator":   operator,
		"updated_at": time.Now(),
	}
	if err := dto.db.WithContext(ctx).Model(&models.FpOpsReplyTpl{}).Where("id=?", tplId).Updates(update).Error; err != nil {
		logger.Error(ctx, "reply tpl enable err", zap.Uint32("reply_tplId", tplId), zap.Bool("enable", enable), zap.String("err", err.Error()))
		return xerrors.New(err.Error(), code.DbError)
	}
	return nil
}
