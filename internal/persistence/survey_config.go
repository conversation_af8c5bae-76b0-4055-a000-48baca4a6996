package persistence

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	jsoniter "github.com/json-iterator/go"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"ops-ticket-api/internal/code"
	"ops-ticket-api/internal/framework/cache/keys"
	"ops-ticket-api/internal/framework/cache/rds"
	"ops-ticket-api/internal/framework/database"
	"ops-ticket-api/internal/framework/xerrors"
	"ops-ticket-api/models"
	"ops-ticket-api/proto/pb"
	"ops-ticket-api/utils"
	"os"
	"strings"
	"time"
)

// surveyConfig 调查问卷配置
type surveyConfig struct {
	db *gorm.DB
}

// newSurveyConfig init
func NewSurveyConfig() *surveyConfig {
	return &surveyConfig{
		db: database.Db(),
	}
}

// GetSurveyConfigList 获取问卷调查配置列表
func (dto *surveyConfig) GetSurveyConfigList(ctx context.Context, req *pb.SurveyListReq) ([]*models.FpDscSurveyConfig, int64, error) {
	var list []*models.FpDscSurveyConfig
	query := dto.db.WithContext(ctx).Model(&models.FpDscSurveyConfig{})
	var count int64
	if err := query.Count(&count).Error; err != nil {
		logger.Error(ctx, "get survey config list count err", zap.String("err", err.Error()))
		return nil, count, xerrors.New(err.Error(), code.DbError)
	}
	if err := query.Scopes(database.Paginate(&req.Page, &req.PageSize)).Order("id asc").Find(&list).Error; err != nil {
		logger.Error(ctx, "get survey config list err", zap.String("err", err.Error()))
		return nil, count, xerrors.New(err.Error(), code.DbError)
	}
	return list, count, nil
}

// GetSurveyConfigEnableList 获取问卷调查推送配置
func (dto *surveyConfig) GetSurveyConfigEnableList(ctx context.Context) ([]*pb.SurveyInfoResp, error) {
	var list []*models.FpDscSurveyConfig
	query := dto.db.WithContext(ctx).Model(&models.FpDscSurveyConfig{})
	if err := query.Where("enable = ? AND game_project != ?", 1, "").Order("id asc").Find(&list).Error; err != nil {
		logger.Error(ctx, "get survey config list err", zap.String("err", err.Error()))
		return nil, xerrors.New(err.Error(), code.DbError)
	}
	var datas []*pb.SurveyInfoResp
	for _, row := range list {
		data := &pb.SurveyInfoResp{
			Data: &pb.SurveyEditReq{
				Id:               uint64(row.ID),
				Project:          row.GameProject,
				PushCycle:        pb.SurveyPushCycle(row.PushCycle),
				PushWeek:         row.PushWeek,
				PushTime:         row.PushTime,
				EffectiveTime:    time.Unix(int64(row.EffectiveTime), 0).Local().Format("2006-01-02"),
				ExpireTime:       pb.SurveyEffective(row.ExpireTime),
				SurveyTitles:     FormatSurveyI18NText(row.Titles),
				PushContents:     FormatSurveyI18NText(row.Contents),
				ProductQuestions: FormatSurveyI18NText(row.ProductQuestions),
				ServiceQuestions: FormatSurveyI18NText(row.ServiceQuestions),
				Reasons:          FormatSurveyI18NText(row.Reasons),
			},
		}
		datas = append(datas, data)
	}
	return datas, nil
}

// SurveyConfigEnable 调查问卷配置禁用启用接口
func (dto *surveyConfig) EnableSurveyConfig(ctx context.Context, objId uint32, enable bool, account string) error {
	var toEnable = code.StatusFalse
	if enable {
		toEnable = code.StatusTrue
	}
	update := map[string]interface{}{
		"enable":     toEnable,
		"operator":   account,
		"updated_at": time.Now().Unix(),
	}
	if err := dto.db.WithContext(ctx).Model(&models.FpDscSurveyConfig{}).Where("id=?", objId).Updates(update).Error; err != nil {
		logger.Error(ctx, "survey config enable err", zap.Uint32("objId", objId), zap.Bool("enable", enable), zap.String("err", err.Error()))
		return xerrors.New(err.Error(), code.DbError)
	}
	return nil
}

// GetSurveyConfigInfo 调查问卷配置详细信息
func (dto *surveyConfig) GetSurveyConfigInfo(ctx context.Context, id uint64) (*pb.SurveyInfoResp, error) {
	var info *models.FpDscSurveyConfig
	if err := dto.db.WithContext(ctx).Model(&models.FpDscSurveyConfig{}).Where("id=?", id).Scan(&info).Error; err != nil {
		logger.Error(ctx, "get survey config ", zap.Uint64("id", id), zap.String("err", err.Error()))
		return nil, xerrors.New(err.Error(), code.DbError)
	}
	result := &pb.SurveyInfoResp{
		Data: &pb.SurveyEditReq{
			Id:               uint64(info.ID),
			Project:          info.GameProject,
			PushCycle:        pb.SurveyPushCycle(info.PushCycle),
			PushWeek:         info.PushWeek,
			PushTime:         info.PushTime,
			EffectiveTime:    time.Unix(int64(info.EffectiveTime), 0).Local().Format("2006-01-02"),
			ExpireTime:       pb.SurveyEffective(info.ExpireTime),
			SurveyTitles:     FormatSurveyI18NText(info.Titles),
			PushContents:     FormatSurveyI18NText(info.Contents),
			ProductQuestions: FormatSurveyI18NText(info.ProductQuestions),
			ServiceQuestions: FormatSurveyI18NText(info.ServiceQuestions),
			Reasons:          FormatSurveyI18NText(info.Reasons),
		},
	}
	return result, nil
}

// UpdateSurveyConfigInfoById 调查问卷配置详细信息
func (dto *surveyConfig) UpdateSurveyConfigInfoById(ctx context.Context, param *pb.SurveyEditReq, account string) error {
	var titleMapF = func(mDt map[string]string, flag int) (string, error) {
		var err error
		var res string
		if mDt != nil {
			for k, v := range mDt {
				if v == "" {
					delete(mDt, k)
				}
			}
		}
		if len(mDt) > 0 {
			if v, ok := mDt["en"]; !ok || strings.TrimSpace(v) == "" {
				return "", fmt.Errorf("en is empty")
			}
		}

		switch flag {
		case 1: // 不做校验
			if len(mDt) == 0 {
				res, err = "{}", nil
			} else {
				res, err = jsoniter.MarshalToString(mDt)
			}
		default: // 默认 强校验 ，必填，长度大于>0
			if mDt == nil || len(mDt) == 0 {
				return "", fmt.Errorf("mDt is nil")
			}
			res, err = jsoniter.MarshalToString(mDt)
		}

		return res, err
	}
	titleStr, tErr := titleMapF(param.SurveyTitles, 0)
	contentStr, cErr := titleMapF(param.PushContents, 1)
	productStr, pErr := titleMapF(param.ProductQuestions, 1)
	serviceStr, sErr := titleMapF(param.ServiceQuestions, 1)
	reasonStr, rErr := titleMapF(param.Reasons, 0)
	if tErr != nil || cErr != nil || pErr != nil || sErr != nil || rErr != nil {
		return xerrors.New(fmt.Sprintf("titleStr:%v contentStr:%v productStr:%v serviceStr:%v reasonStr:%v", tErr, cErr, pErr, sErr, rErr))
	}

	defer func() {
		rds.NewRCache().Del(ctx, fmt.Sprintf(keys.SurveyConfigCacheKey, param.Id))
		time.Sleep(time.Millisecond * 200)
		rds.NewRCache().Del(ctx, fmt.Sprintf(keys.SurveyConfigCacheKey, param.Id))
	}()
	update := map[string]interface{}{
		"push_cycle":        int64(param.PushCycle),
		"push_week":         param.PushWeek,
		"push_time":         param.PushTime,
		"effective_time":    utils.TimeStrToDayUnix(param.EffectiveTime),
		"expire_time":       param.ExpireTime,
		"titles":            titleStr,
		"contents":          contentStr,
		"product_questions": productStr,
		"service_questions": serviceStr,
		"reasons":           reasonStr,
		"operator":          account,
		"updated_at":        time.Now(),
	}
	if err := dto.db.WithContext(ctx).Model(&models.FpDscSurveyConfig{}).Where("id=?", param.Id).Updates(update).Error; err != nil {
		logger.Error(ctx, "update survey config by id err", zap.Uint64("id", param.Id), zap.Any("param", param), zap.String("err", err.Error()))
		return xerrors.New(err.Error(), code.DbError)
	}
	return nil
}

// InsertSurveyLinksByUid 调查问卷配置详细信息
func (dto *surveyConfig) InsertSurveyLinksByUid(ctx context.Context, record *models.FpDscSurveyLinks) error {
	if err := dto.db.WithContext(ctx).Model(&models.FpDscSurveyLinks{}).Save(&record).Error; err != nil {
		logger.Error(ctx, "InsertSurveyLinksByUid return err", zap.Int64("uid", record.UID), zap.Any("record", record), zap.String("err", err.Error()))
		return xerrors.New(err.Error(), code.DbError)
	}
	return nil
}

// GenBatchSurveyLog 调查问卷 - 生成批次 log、 如果最近两天生成过 则不在直接生成
func (dto *surveyConfig) GenBatchSurveyLog(ctx context.Context, gameProject string) (*models.FpDscOperateLog, error) {
	// 1.0 check last 2 days log
	startTm := time.Now().AddDate(0, 0, -2)
	if os.Getenv("environment") == "test" { // 2 minutes
		startTm = time.Now().Add(0 - time.Minute*2)
	}
	var log models.FpDscOperateLog
	err := dto.db.WithContext(ctx).Model(log).
		Where("game_project = ? AND operation_group = ? AND operation_action = ? AND create_time > ? ",
			gameProject, pb.OpGroup_OpGroupSurveyBatchGen.String(), pb.OpAction_OpActionAdd.String(), startTm).
		Last(&log).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, err
	}
	if log.ID > 0 {
		return &log, nil
	}

	// 2.0 generate log
	log = models.FpDscOperateLog{
		OperationGroup:  pb.OpGroup_OpGroupSurveyBatchGen.String(),
		OperationAction: pb.OpAction_OpActionAdd.String(),
		BaseID:          fmt.Sprintf("svbth.%s.%d", gameProject, time.Now().UnixMilli()),
		UniqueID:        fmt.Sprintf("svbth.%s.%d", gameProject, time.Now().UnixMilli()),
		GameProject:     gameProject,
		BeforeDetail:    "{}",
		AfterDetail:     "{}",
		CreateTime:      time.Now(),
		Operator:        "system",
	}
	err = dto.db.WithContext(ctx).Model(log).Create(&log).Error
	return &log, err
}

func (dto *surveyConfig) SurveyLinksExistCheck(ctx context.Context, where map[string]interface{}) (bool, error) {
	var count int64
	err := dto.db.WithContext(ctx).Model(&models.FpDscSurveyLinks{}).Where(where).Count(&count).Error
	return count > 0, err
}
func (dto *surveyConfig) GetBatchSurveyLinksBySurveyId(ctx context.Context, project string, surveyId int64, batchId uint64) ([]*models.FpDscSurveyLinks, error) {
	var list []*models.FpDscSurveyLinks
	err := dto.db.WithContext(ctx).Model(&models.FpDscSurveyLinks{}).Where("project = ? AND survey_id = ? AND batch_id = ? AND is_public = 0", project, surveyId, batchId).Find(&list).Error
	return list, err
}
func (dto *surveyConfig) GetSurveyLinksByToken(ctx context.Context, token string) (*models.FpDscSurveyLinks, error) {
	var dest = &models.FpDscSurveyLinks{}
	err := dto.db.WithContext(ctx).Model(&models.FpDscSurveyLinks{}).Where("encrypted_token = ?", token).First(dest).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return dest, err
	}
	return dest, nil
}

func FormatSurveyI18NText(text string) map[string]string {
	i18nTextMap := make(map[string]string)
	if text == "" || text == "{}" {
		return i18nTextMap
	}
	_ = json.Unmarshal([]byte(text), &i18nTextMap)
	return i18nTextMap
}
