package persistence

import (
	"context"
	"errors"
	"fmt"
	"sort"
	"strconv"
	"strings"
	"time"

	"ops-ticket-api/internal/code"
	"ops-ticket-api/internal/elasticsearch"
	"ops-ticket-api/internal/framework/cache/rds"
	"ops-ticket-api/internal/framework/database"
	"ops-ticket-api/internal/framework/lang"
	"ops-ticket-api/internal/framework/xerrors"
	"ops-ticket-api/models"
	"ops-ticket-api/pkg/workflow"
	"ops-ticket-api/proto/pb"
	"ops-ticket-api/utils"

	"github.com/go-sql-driver/mysql"
	"github.com/jinzhu/now"
	"github.com/labstack/echo/v4"
	"github.com/spf13/cast"
	"gitlab-ee.funplus.io/ops-tools/compkg/dump"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"gorm.io/plugin/dbresolver"
)

type ticketRepo struct {
	db *gorm.DB
}

// NewTicket init
func NewTicket() *ticketRepo {
	return &ticketRepo{
		db: database.Db(),
	}
}

// TicketStDoneCheck 工单状态 已完成 check
func (dto *ticketRepo) TicketStDoneCheck(ctx context.Context, ticketId uint64) (*models.FpOpsTickets, error) {
	tkInfo, err := dto.GetTicketInfoFromMaster(ctx, ticketId)
	if err != nil {
		return nil, err
	}
	if tkInfo.Status == uint32(pb.TkStatus_TkStatusDone) {
		return nil, xerrors.New(code.StatusText(code.ResolvedCannotOp), code.ResolvedCannotOp)
	}
	return tkInfo, nil
}

// GetDoingTicketsNum 获取正在处理中的订单数据量 - 单用户
func (dto *ticketRepo) GetDoingTicketsNum(ctx context.Context, account []string) (map[string]int64, error) {
	result := make(map[string]int64, len(account))
	for _, ac := range account {
		result[ac] = 0
	}

	condition := map[string]interface{}{
		"acceptor":        account,
		"status":          int32(pb.TkStatus_TkStatusProcessing),
		"conversion_node": []uint32{uint32(pb.TkStage_TkStageAgentReopen), uint32(pb.TkStage_TkStageNewForAgent), uint32(pb.TkStage_TkStageWaitingForAgent)},
	}

	res := make([]map[string]interface{}, 0)
	err := dto.db.WithContext(ctx).Model(&models.FpOpsTickets{}).Where(condition).Group("acceptor").
		Select("acceptor,count(1) as cout").Find(&res).Error
	for _, row := range res {
		ac := cast.ToString(row["acceptor"])
		num := cast.ToInt64(row["cout"])
		if _, ok := result[ac]; ok {
			result[ac] = num
		}
	}
	return result, err
}

func (dto *ticketRepo) GetAllProjects(ctx context.Context) ([]string, error) {
	var projects []string
	err := dto.db.Model(&models.FpOpsTickets{}).Group("project").Pluck("project", &projects).Error
	return projects, err
}

// GetAllPendingTickets 获取可以分单的单子（最新：获取转人工的单子）
func (dto *ticketRepo) GetAllPendingTickets(ctx context.Context, project string) ([]*models.FpOpsTickets, error) {
	var tickets []*models.FpOpsTickets

	err := dto.db.WithContext(ctx).Model(&models.FpOpsTickets{}).
		Where("project = ? AND status = ? AND acceptor = ''", project, uint32(pb.TkStatus_TkStatusUntreated)).
		Where("(solve_type = ? OR (solve_type <> ? AND reopen_num > ?))", int(pb.SolveType_SolveTypeManualTicket), int(pb.SolveType_SolveTypeManualTicket), 0).
		Preload("SystemTags"). // <--- 在这里添加 Preload
		Find(&tickets).Error
	if err != nil {
		return nil, err
	}
	return tickets, nil
}

// GetTicketInfoFromMaster 获取工单信息
func (dto *ticketRepo) GetTicketInfoFromMaster(ctx context.Context, ticketId uint64) (*models.FpOpsTickets, error) {
	tkInfo := &models.FpOpsTickets{}
	if err := dto.db.WithContext(ctx).Model(&models.FpOpsTickets{}).
		Where("ticket_id=?", ticketId).Clauses(dbresolver.Write).Take(&tkInfo).Error; err != nil {
		logger.Error(ctx, "get ticket err. use master", zap.Uint64("ticket_id", ticketId), zap.String("err", err.Error()))
		return nil, err
	}
	return tkInfo, nil
}

func (dto *ticketRepo) GetTicketInfosFromMaster(ctx echo.Context, ticketIds []uint64) ([]*models.FpOpsTickets, error) {
	var dest []*models.FpOpsTickets
	if err := dto.db.WithContext(ctx.Request().Context()).Model(&models.FpOpsTickets{}).
		Where("ticket_id in ?", ticketIds).Clauses(dbresolver.Write).Find(&dest).Error; err != nil {
		logger.Error(ctx.Request().Context(), "get ticket err. use master", zap.Any("ticket_ids", ticketIds), zap.String("err", err.Error()))
		return nil, err
	}
	return dest, nil
}

// GetTicketField 获取工单 表单信息
func (dto *ticketRepo) GetTicketField(ctx context.Context, ticketId uint64) (*models.FpOpsTicketsField, error) {
	detail := &models.FpOpsTicketsField{}
	err := dto.db.WithContext(ctx).Model(detail).Where("ticket_id = ?", ticketId).First(detail).Error
	return detail, err
}

// GetTicketDetail 获取工单信息
func (dto *ticketRepo) GetTicketDetail(ctx echo.Context, ticketId uint64) (*pb.TicketDetailModel, error) {
	tkInfo := &pb.TicketDetailModel{}
	fields := "t.ticket_id,account_id,uuid,origin,cat_id,auto_reply_id,project,scene,gm_id,evaluation,csi,proof,conversion_node as stage,`status`,closed,closed_at,field,reopen_num,priority,ticket_type"
	query := dto.db.WithContext(ctx.Request().Context()).Table("fp_ops_tickets t").
		Joins("LEFT JOIN fp_ops_tickets_field f ON f.ticket_id=t.ticket_id")
	if err := query.Select(fields).Where("t.ticket_id=? and t.is_deleted=0", ticketId).Take(&tkInfo).Error; err != nil {
		logger.Error(ctx.Request().Context(), "get ticket with fields err", zap.Uint64("ticket_id", ticketId), zap.String("err", err.Error()))
		return nil, xerrors.New(err.Error(), code.DbError)
	}
	return tkInfo, nil
}

// ExistTicketByCatId 该用户在类型下是否存在未完成工单
func (dto *ticketRepo) ExistTicketByCatId(ctx echo.Context, gmProject, uuid, accountId string, scene, catId uint32) bool {
	dest := &pb.TkCreateResp{}
	where := map[string]interface{}{
		"project": gmProject,
		"cat_id":  catId,
		"scene":   scene,
		"closed":  code.StatusFalse,
	}
	if pb.SceneType(scene) == pb.SceneType_Loading {
		where["uuid"] = uuid
	} else {
		where["account_id"] = accountId
	}
	if err := dto.db.WithContext(ctx.Request().Context()).Clauses(dbresolver.Write).
		Model(&models.FpOpsTickets{}).Select("ticket_id").Where(where).Take(&dest).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return false
		}
		logger.Error(ctx.Request().Context(), "get the catId ticket err", zap.Uint32("catId", catId), zap.String("err", err.Error()))
		return false
	}
	return dest.TicketId > 0
}

// TicketCreate 创建工单
func (dto *ticketRepo) TicketCreate(ctx echo.Context, tx *gorm.DB, ticket *models.FpOpsTickets) (ticketId uint64, err error) {
	query := tx.WithContext(ctx.Request().Context()).Model(&models.FpOpsTickets{}).Create(ticket)
	if query.Error != nil {
		logger.Error(ctx.Request().Context(), "create ticket err", zap.String("err", query.Error.Error()))
		return 0, xerrors.New(query.Error.Error(), code.DbError)
	}
	return ticket.TicketID, nil
}

func (dto *ticketRepo) MineTickets(ctx context.Context, project, uuid, accountId string, scene pb.SceneType) ([]*models.FpOpsTickets, error) {
	dest := make([]*models.FpOpsTickets, 0)
	where := map[string]interface{}{
		"project":    project,
		"origin":     []uint8{uint8(pb.Origin_Player)},
		"scene":      int32(scene),
		"is_deleted": 0,
	}
	switch scene {
	case pb.SceneType_Loading:
		if uuid == "" {
			logger.Warn(ctx, "MineTickets param err. Loading. uuid is empty.", zap.String("project", project), zap.String("uuid", uuid), zap.String("accountId", accountId), zap.Any("scene", scene))
			return nil, nil
		}
		where["uuid"] = uuid
	default:
		if accountId == "" {
			logger.Warn(ctx, "MineTickets param err. InGameOrBan. account_id is empty.", zap.String("project", project), zap.String("uuid", uuid), zap.String("accountId", accountId), zap.Any("scene", scene))
			return nil, nil
		}
		where["account_id"] = accountId
	}

	if err := dto.db.WithContext(ctx).Model(&models.FpOpsTickets{}).Where(where).Order("created_at desc").Find(&dest).Error; err != nil {
		logger.Errorf(ctx, "dto.MineTickets get db data return err. err:%v. where:%v", err, where)
		return dest, err
	}
	return dest, nil
}

// TicketPropsUpdate 属性更新
func (dto *ticketRepo) TicketPropsUpdate(ctx context.Context, ticketId uint64, dest map[string]interface{}, fc func(txDb *gorm.DB) error) error {
	tx := dto.db.WithContext(ctx).Begin()
	defer tx.Rollback()

	if err := tx.Model(&models.FpOpsTickets{}).Where("ticket_id=?", ticketId).Updates(dest).Error; err != nil {
		logger.Error(ctx, "ticket's props update err",
			zap.Uint64("ticket_id", ticketId), zap.Any("dest", dest), zap.String("err", err.Error()))
		return xerrors.New(err.Error(), code.DbError)
	}

	// extra action
	if fc != nil {
		if err := fc(tx); err != nil {
			return err
		}
	}
	return tx.Commit().Error
}

// TicketTransfer 工单回复 - 状态流转
func (dto *ticketRepo) TicketTransfer(ctx context.Context, ticketId uint64, dest map[string]interface{}, tkCommu *models.FpOpsTicketsCommu, hist *models.FpOpsTicketsHistory, fc func(txDb *gorm.DB) error) error {
	fun := "ticketRepo.TicketTransfer"
	tx := dto.db.WithContext(ctx).Begin()
	defer tx.Rollback()
	if err := tx.Model(&models.FpOpsTickets{}).Where("ticket_id = ?", ticketId).Updates(dest).Error; err != nil {
		logger.Errorf(ctx, "%s update ticket return err. ticketId:%d. dest:%+v. err:%v", fun, ticketId, dest, err)
		return err
	}
	if tkCommu != nil {
		if err := dto.AddTkCommu(ctx, tx, tkCommu); err != nil {
			logger.Errorf(ctx, "%s save tkCommu return err. ticketId:%d. commu:%+v. err:%v", fun, ticketId, tkCommu, err)
			return err
		}
	}
	if err := dto.AddTicketHistory(ctx, tx, hist); err != nil {
		logger.Errorf(ctx, "%s AddTicketHistory return err. ticketId:%d. hist:%+v. err:%v", fun, ticketId, hist, err)
		return err
	}
	if fc != nil {
		if err := fc(tx); err != nil {
			logger.Errorf(ctx, "%s exec fc return err. ticketId:%d. err:%v", fun, ticketId, err)
			return err
		}
	}
	return tx.Commit().Error
}

func (dto *ticketRepo) TicketReturnPool(ctx context.Context, ticketId uint64, dest map[string]interface{}, hist *models.FpOpsTicketsHistory, fc func(txDb *gorm.DB) error) error {
	fun := "ticketRepo.TicketReturnPool"
	tx := dto.db.WithContext(ctx).Begin()
	defer tx.Rollback()
	if err := tx.Model(&models.FpOpsTickets{}).Where("ticket_id = ?", ticketId).Updates(dest).Error; err != nil {
		logger.Errorf(ctx, "%s update ticket return err. ticketId:%d. dest:%+v. err:%v", fun, ticketId, dest, err)
		return err
	}
	if err := dto.AddTicketHistory(ctx, tx, hist); err != nil {
		logger.Errorf(ctx, "%s AddTicketHistory return err. ticketId:%d. hist:%+v. err:%v", fun, ticketId, hist, err)
		return err
	}
	if fc != nil {
		if err := fc(tx); err != nil {
			logger.Errorf(ctx, "%s exec fc return err. ticketId:%d. err:%v", fun, ticketId, err)
			return err
		}
	}
	return tx.Commit().Error
}

func (dto *ticketRepo) Info(ctx context.Context, ticketId uint64, account string) (*models.FpOpsTicketsDraft, error) {
	var info models.FpOpsTicketsDraft
	if err := dto.db.Model(&models.FpOpsTicketsDraft{}).Where("operator=? AND ticket_id=?",
		account, ticketId).Take(&info).Error; err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		logger.Error(ctx, "ticket draft info err", zap.Uint64("ticket_id", ticketId), zap.Error(err))
		return &info, xerrors.New(err.Error(), code.DbError)
	}
	return &info, nil
}

func (dto *ticketRepo) InfoById(ctx context.Context, id uint64) (*models.FpOpsTicketsDraft, error) {
	var info models.FpOpsTicketsDraft
	if err := dto.db.Model(&models.FpOpsTicketsDraft{}).Where("id=?", id).Take(&info).Error; err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		logger.Error(ctx, "ticket draft info by id err", zap.Uint64("id", id), zap.Error(err))
		return &info, xerrors.New(err.Error(), code.DbError)
	}
	return &info, nil
}

// TicketDraftSave 工单草稿保存
func (dto *ticketRepo) TicketDraftSave(ctx echo.Context, draft *models.FpOpsTicketsDraft) error {
	if draft.ID > 0 {
		info, err := dto.InfoById(ctx.Request().Context(), draft.ID)
		if err != nil {
			return err
		}
		if info.ID == 0 || info.TicketID != draft.TicketID {
			return xerrors.New(code.StatusText(code.MissingParams), code.MissingParams)
		}
		if draft.Content == info.Content {
			return nil
		}
		updates := map[string]interface{}{
			"content":    draft.Content,
			"updated_at": draft.UpdatedAt,
		}
		if err = dto.db.Model(models.FpOpsTicketsDraft{}).Where("id=?", draft.ID).Updates(updates).Error; err != nil {
			logger.Error(ctx.Request().Context(), "ticket draft save err", zap.Uint64("ticket_id", draft.TicketID), zap.Error(err))
			return xerrors.New(err.Error(), code.DbError)
		}
		return nil
	}
	if draft.Content == "" {
		// 新增时内容为空退出
		return nil
	}
	if err := dto.db.Model(models.FpOpsTicketsDraft{}).Create(draft).Error; err != nil {
		logger.Error(ctx.Request().Context(), "ticket draft create err", zap.Uint64("ticket_id", draft.TicketID), zap.Error(err))
		return xerrors.New(err.Error(), code.DbError)
	}
	return nil
}

func (dto *ticketRepo) TicketDraftDel(ctx context.Context, tx *gorm.DB, ticketId uint64, operator string) error {
	if err := tx.Model(&models.FpOpsTicketsDraft{}).Where("operator = ? AND ticket_id = ?", operator, ticketId).Delete(&models.FpOpsTicketsDraft{}).Error; err != nil {
		tx.Rollback()
		logger.Error(ctx, "error delete remark draft", zap.String("err", err.Error()))
		return xerrors.New(err.Error(), code.DbError)
	}
	return nil
}

// ticket history
func (dto *ticketRepo) AddTicketHistory(ctx context.Context, tx *gorm.DB, hist *models.FpOpsTicketsHistory) error {
	if tx == nil {
		tx = dto.db.WithContext(ctx)
	}
	hist.CreatedAt = uint64(time.Now().Unix())
	if err := tx.Create(hist).Error; err != nil {
		logger.Error(ctx, "create ticket history info err",
			zap.Uint64("ticket_id", hist.TicketID),
			zap.String("err", err.Error()),
		)
		return xerrors.New(err.Error(), code.DbError)
	}
	elasticsearch.DefaultTicketHistSvc.AddTicketHist(ctx, hist)
	return nil
}

// GetTicketHists 获取工单 历史操作日志
func (dto *ticketRepo) GetTicketHists(ctx context.Context, ticketId uint64, operates ...pb.TkEvent) ([]*models.FpOpsTicketsHistory, error) {
	var details []*models.FpOpsTicketsHistory
	query := dto.db.WithContext(ctx).Model(&models.FpOpsTicketsHistory{}).Where("ticket_id = ?", ticketId)
	if len(operates) > 0 {
		var op []uint8
		for _, _o := range operates {
			op = append(op, uint8(_o))
		}
		query = query.Where("operate in (?)", op)
	}
	err := query.Find(&details).Order("created_at ASC").Error
	return details, err
}

func (dto *ticketRepo) GetTicketAppraiseInfo(ctx context.Context, ticketId uint64) (*models.FpOpsTicketsAppraise, error) {
	record := &models.FpOpsTicketsAppraise{}
	if err := dto.db.WithContext(ctx).Model(&models.FpOpsTicketsAppraise{}).Where("ticket_id = ?", ticketId).Find(record).Error; err != nil {
		return nil, err
	}
	return record, nil
}

// 玩家最近
func (dto *ticketRepo) GetTicketLastUserOpAt(ctx context.Context, ticketId uint64) (uint64, error) {
	fun := "ticketRepo.GetTicketLastUserOpAt"
	var lastAt uint64
	hists, err := dto.GetTicketHists(ctx, ticketId)
	if err != nil {
		return 0, err
	}
	if len(hists) == 0 {
		return 0, fmt.Errorf("current hist empty. ticketId:%d", ticketId)
	}
	sort.SliceStable(hists, func(i, j int) bool {
		return hists[i].CreatedAt < hists[j].CreatedAt
	})
	for _, hist := range hists {
		if utils.InArrayAny(hist.Operate, workflow.TkEventCsOpEvent) {
			lastAt = 0
			continue
		}
		if utils.InArrayAny(hist.Operate, workflow.TkEventToUserOpEvent) && lastAt == 0 {
			lastAt = hist.CreatedAt
		}
	}
	if lastAt == 0 {
		logger.Info(ctx, "get last at return zero. ", zap.String("fun", fun), zap.Any("ticketId", ticketId))
		// lastAt = utils.NowTimestamp()
	}
	return lastAt, nil
}

// GetTicketSolution 获取工单解决方案
func (dto *ticketRepo) GetTicketSolution(ctx context.Context, tx *gorm.DB, ticketId uint64) (*models.FpOpsTicketsCommu, error) {
	if tx == nil {
		tx = dto.db.WithContext(ctx)
	}
	dest := &models.FpOpsTicketsCommu{}
	op := []uint32{uint32(pb.TkEvent_TkEventCommuClose)}
	if err := tx.Where("ticket_id = ? AND from_role = ? AND commu_type =? AND op_type in (?)",
		ticketId, uint32(pb.UserRole_ServiceRole), pb.CommuType_CommuTypeDialogue.String(), op).
		Last(&dest).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		logger.Error(ctx, "get ticket commu info err", zap.Uint64("ticket_id", ticketId), zap.String("err", err.Error()))
		return nil, xerrors.New(err.Error(), code.DbError)
	}
	return dest, nil
}

func (dto *ticketRepo) SetMsgRead(ctx context.Context, scene uint8, gmId, uuid, account string, msgId uint64) error {
	cond := map[string]interface{}{
		"gm_id": gmId,
		"scene": scene,
		"read":  code.StatusFalse,
	}
	_ = cond
	_ = rds.TicketDelNotice(ctx, gmId, uuid, account, scene, msgId, pb.RedPointTypeEnum_RedPointTypeNone)
	return nil
}

// PlayerRefillListByTicketId C端补填信息列表 tickets_proof： C端补填+客服回复 信息
func (dto *ticketRepo) PlayerRefillListByTicketId(ctx echo.Context, ticketId uint64) ([]*pb.TkDetailResp_Replenish, error) {
	dest := make([]*models.FpOpsTicketsProof, 0)
	replenishList := make([]*pb.TkDetailResp_Replenish, 0)
	if err := dto.db.WithContext(ctx.Request().Context()).Model(&models.FpOpsTicketsProof{}).Where(map[string]interface{}{
		"ticket_id": ticketId,
		"from":      uint8(pb.UserRole_PlayerRole),
	}).Order("id desc").Find(&dest).Error; err != nil {
		if !errors.Is(err, gorm.ErrRecordNotFound) {
			logger.Error(ctx.Request().Context(), "get ticket fill info err", zap.Uint64("ticket_id", ticketId), zap.String("err", err.Error()))
		}
		return replenishList, xerrors.New(err.Error(), code.DbError)
	}
	for _, t := range dest {
		replenishList = append(replenishList, &pb.TkDetailResp_Replenish{
			ReplenishId: t.ID,
			TicketId:    t.TicketID,
			Remark:      t.Remark,
			FillContent: t.FillContent,
			Files:       t.Files,
			Op:          uint32(pb.TicketStage_TicketRefill),
			CreatedAt:   utils.TimeFormatInLoc(int64(t.CreatedAt)),
			CreatedTs:   t.CreatedAt,
		})
	}
	return replenishList, nil
}

// GetPlayerProofRefillDest 获取工单待玩家补填信息
func (dto *ticketRepo) GetPlayerProofRefillDest(ctx context.Context, ticketId uint64) (*models.FpOpsTicketsProof, error) {
	dest := &models.FpOpsTicketsProof{}
	cond := map[string]interface{}{
		"ticket_id": ticketId,
		"from":      uint8(pb.UserRole_PlayerRole),
		"status":    code.StatusFalse,
	}
	if err := dto.db.WithContext(ctx).Model(&models.FpOpsTicketsProof{}).Clauses(dbresolver.Write).Where(cond).Order("created_at desc").Take(&dest).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, xerrors.New(code.StatusText(code.NotFound), code.NotFound)
		}
		return nil, xerrors.New(code.StatusText(code.DbError), code.DbError)
	}
	return dest, nil
}

// RefillSave 补填
func (dto *ticketRepo) RefillSave(ctx echo.Context, refillId, ticketId uint64, content, files string, toStage uint32) error {
	tx := dto.db.WithContext(ctx.Request().Context()).Begin()
	dest := map[string]interface{}{
		"fill_content": content,
		"status":       code.StatusTrue,
		"updated_at":   time.Now().Unix(),
	}
	if files != "" {
		dest["files"] = files
	}
	if err := tx.Model(&models.FpOpsTicketsProof{}).Where("id=?", refillId).Updates(&dest).Error; err != nil {
		tx.Rollback()
		logger.Error(ctx.Request().Context(), "cs refill save err", zap.Uint64("refillId", refillId), zap.String("err", err.Error()))
		return xerrors.New(err.Error(), code.DbError)
	}
	update := map[string]interface{}{
		"acceptor":        "",
		"conversion_node": toStage,
	}
	if err := tx.Model(&models.FpOpsTickets{}).Where("ticket_id=?", ticketId).Updates(&update).Error; err != nil {
		tx.Rollback()
		logger.Error(ctx.Request().Context(), "cs refill ticket update err", zap.Uint64("ticket_id", ticketId), zap.String("err", err.Error()))
		return xerrors.New(err.Error(), code.DbError)
	}
	if err := tx.Commit().Error; err != nil {
		tx.Rollback()
		logger.Error(ctx.Request().Context(), "cs refill commit err", zap.Uint64("ticket_id", ticketId), zap.String("err", err.Error()))
		return xerrors.New(err.Error(), code.DbError)
	}
	return nil
}

// GetReopenList 重开单信息
func (dto *ticketRepo) GetReopenList(ctx context.Context, ticketId uint64) ([]*models.FpOpsTicketsReopen, error) {
	dest := make([]*models.FpOpsTicketsReopen, 0)
	if err := dto.db.Model(&models.FpOpsTicketsReopen{}).Where("ticket_id=?", ticketId).Find(&dest).Error; err != nil {
		logger.Error(ctx, "ticket reopen info err", zap.Uint64("ticket_id", ticketId), zap.Error(err))
		return dest, xerrors.New(err.Error(), code.DbError)
	}
	return dest, nil
}

func (dto *ticketRepo) ReopenInfo(ctx context.Context, ticketId uint64) ([]*models.FpOpsTicketsReopen, error) {
	var info []*models.FpOpsTicketsReopen
	if err := dto.db.Model(&models.FpOpsTicketsReopen{}).Where("ticket_id=?", ticketId).Find(&info).Error; err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		logger.Error(ctx, "ticket reopen info err", zap.Uint64("ticket_id", ticketId), zap.Error(err))
		return info, xerrors.New(err.Error(), code.DbError)
	}
	return info, nil
}

func (dto *ticketRepo) LastReopenInfo(ctx context.Context, ticketId uint64) (*models.FpOpsTicketsReopen, error) {
	var info models.FpOpsTicketsReopen
	if err := dto.db.Model(&models.FpOpsTicketsReopen{}).Where("ticket_id=?", ticketId).Last(&info).Error; err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		logger.Error(ctx, "ticket reopen info err", zap.Uint64("ticket_id", ticketId), zap.Error(err))
		return &info, xerrors.New(err.Error(), code.DbError)
	}
	return &info, nil
}

func (dto *ticketRepo) AddReopen(ctx context.Context, tx *gorm.DB, reopenData *models.FpOpsTicketsReopen) error {
	if tx == nil {
		tx = dto.db
	}
	tkDest := map[string]interface{}{
		//"acceptor":           "",
		"conversion_node":    pb.TkStage_TkStageAgentReopen,
		"status":             pb.TkStatus_TkStatusUntreated,
		"closed":             pb.TkClosedRole_TkClosedNone,
		"sort_wait_start_at": utils.NowTimestamp(),
		"closed_at":          0,
		"reopen_num":         reopenData.Num,
	}
	if err := tx.Model(&models.FpOpsTickets{}).Where("ticket_id = ?", reopenData.TicketID).Updates(tkDest).Error; err != nil {
		logger.Errorf(ctx, "ticket reopen update tickets return err. err:=%v. ticketId:%d", err, reopenData.TicketID)
		return xerrors.New(err.Error(), code.DbError)
	}
	if err := tx.Model(&models.FpOpsTicketsReopen{}).Create(reopenData).Error; err != nil {
		logger.Error(ctx, "ticket reopen add err", zap.Uint64("ticket_id", reopenData.TicketID), zap.Error(err))
		return xerrors.New(err.Error(), code.DbError)
	}
	return nil
}

// TkAppraise 工单评价 tickets_appraise
func (dto *ticketRepo) TkAppraise(ctx echo.Context, req *pb.TkAppraiseReq) error {
	tx := dto.db.WithContext(ctx.Request().Context()).Begin()
	now := utils.NowTimestamp()
	tkLabels := &models.FpOpsTicketsAppraise{
		TicketID:              req.TicketId,
		Csi:                   uint8(req.Appraise),
		ServiceRating:         uint8(req.ServiceRating),
		ServiceTimeRating:     uint8(req.ServiceTimeRating),
		ServiceSolutionRating: uint8(req.ServiceSolutionRating),
		RecommendationLevel:   uint8(req.RecommendationLevel),
		Remark:                req.Remark,
		CreatedAt:             now,
	}
	if err := tx.Create(&tkLabels).Error; err != nil {
		tx.Rollback()
		var mysqlErr *mysql.MySQLError
		if errors.As(err, &mysqlErr) && mysqlErr.Number == 1062 { // C端展示 - 多语言
			logger.Info(ctx.Request().Context(), "ticket appraise dup. continue", zap.Uint64("ticketId", req.TicketId), zap.String("err", err.Error()))
			return xerrors.New(lang.FormatText(ctx, "TicketAppraiseRepeat"), code.IdempotenceErr)
		}
		logger.Error(ctx.Request().Context(), "add ticket appraise err", zap.Uint64("ticket_id", req.TicketId), zap.String("err", err.Error()))
		return xerrors.New(err.Error(), code.DbError)
	}
	if err := tx.Model(models.FpOpsTickets{}).Where("ticket_id = ?", req.TicketId).Updates(map[string]interface{}{
		"csi":         uint8(req.Appraise),
		"nps":         uint8(req.RecommendationLevel),
		"evaluate_at": now,
	}).Error; err != nil {
		tx.Rollback()
		logger.Error(ctx.Request().Context(), "update ticket err", zap.Uint64("ticket_id", req.TicketId), zap.String("err", err.Error()))
		return xerrors.New(err.Error(), code.DbError)
	}
	if err := elasticsearch.DefaultTicketSyncSvc.Evaluation(ctx.Request().Context(), req.TicketId, req.Appraise, req.RecommendationLevel, req.Remark, now); err != nil {
		tx.Rollback()
		logger.Error(ctx.Request().Context(), "save DefaultTicketSyncSvc.Evaluation err", zap.Uint64("ticket_id", req.TicketId), zap.String("err", err.Error()))
		return xerrors.New(err.Error(), code.DbError)
	}
	if err := tx.Commit().Error; err != nil {
		tx.Rollback()
		logger.Error(ctx.Request().Context(), "commit err", zap.Uint64("ticket_id", req.TicketId), zap.String("err", err.Error()))
		return xerrors.New(err.Error(), code.DbError)
	}
	return nil
}

// TkAppraiseFeedback return  bool:是否更新数据：true: 更新成功； false：未更新
func (dto *ticketRepo) TkAppraiseFeedback(ctx context.Context, param *pb.TkAppraiseFeedbackReq) (bool, error) {
	fun := "ticketRepo.TkAppraiseFeedback ->"
	// validate
	tkDetail := &models.FpOpsTickets{}
	tx := dto.db.WithContext(ctx)
	if err := tx.Model(tkDetail).Where("ticket_id = ?", param.TicketId).First(tkDetail).Error; err != nil {
		return false, err
	}
	if tkDetail.Status != uint32(pb.TkStatus_TkStatusDone) {
		logger.Errorf(ctx, "%s tickets status not done. %d. %+v", fun, param.TicketId, tkDetail)
		return false, nil
	}
	if tkDetail.Csi > 0 {
		logger.Warn(ctx, "tickets csi gt0.", zap.String("fun", fun), zap.Uint64("ticketId", param.TicketId), zap.Any("detail", tkDetail))
		return false, nil
	}
	up := map[string]interface{}{
		"resolve_confirmed": param.Resolved,
	}
	if err := tx.Model(tkDetail).Where("ticket_id = ?", param.TicketId).Updates(up).Error; err != nil {
		return false, err
	}
	return false, nil
}

func (dto *ticketRepo) AddTkCommu(ctx context.Context, tx *gorm.DB, log *models.FpOpsTicketsCommu) error {
	fun := "ticketRepo.AddTkCommu"
	if tx == nil {
		tx = dto.db.WithContext(ctx)
	}
	if err := tx.Model(log).Create(log).Error; err != nil {
		logger.Error(ctx, "AddTkCommu add err", zap.Uint64("ticket_id", log.TicketID), zap.Any("err", err))
		return xerrors.New(err.Error(), code.DbError)
	}
	if err := elasticsearch.DefaultTicketSyncSvc.AddCommu(ctx, log.TicketID, log); err != nil {
		logger.Errorf(ctx, "%s DefaultTicketSyncSvc.AddCommu return err. ticketId:%d. log:%+v. err:%v", fun, log.TicketID, log, err)
	}
	return nil
}

func (dto *ticketRepo) GetTicketCommuns(ctx context.Context, ticketId uint64) ([]*models.FpOpsTicketsCommu, error) {
	var commus []*models.FpOpsTicketsCommu
	if err := dto.db.Model(&models.FpOpsTicketsCommu{}).Where("ticket_id = ?", ticketId).Order("created_at ASC").Find(&commus).Error; err != nil {
		logger.Errorf(ctx, "ticketRepo.GetTicketCommuns return err. ticketId:%d. err:%v", ticketId, err)
		return nil, err
	}
	return commus, nil
}

func (dto *ticketRepo) TopTicketInfo(ctx context.Context, ticketId uint64) (*pb.TicketPoolTopResp, error) {
	tkInfo := &pb.TicketPoolTopResp{}
	sel := []string{
		"t.ticket_id", "t.project", "t.acceptor", "t.nickname", "t.account_id", "t.uid", "t.sid",
		"t.conversion_node as status", "t.priority", "t.csi",
		"td.app_version",
	}
	err := dto.db.Table("fp_ops_tickets t").Select(strings.Join(sel, ",")).
		Joins("LEFT JOIN fp_ops_tickets_device td ON t.ticket_id = td.ticket_id").
		Where("t.ticket_id = ?", ticketId).Take(&tkInfo).Error
	if err != nil {
		logger.Error(ctx, "ticket device info err", zap.Uint64("ticket_id", ticketId), zap.Error(err))
		return nil, xerrors.New(err.Error(), code.DbError)
	}
	tkInfo.Priority = tkInfo.Priority + 1
	return tkInfo, nil
}

func (dto *ticketRepo) TicketUserInfo(ctx echo.Context, ticketId uint64) (*pb.UserInfoResp, error) {
	tkDetail := &models.FpOpsTickets{}
	err := dto.db.Model(&models.FpOpsTickets{}).
		Preload("Device").Preload("Tags").Preload("Tags.TagItem").
		Where("ticket_id = ?", ticketId).First(&tkDetail).Error
	if err != nil {
		logger.Error(ctx.Request().Context(), "TicketUserInfo ticket detail info err", zap.Uint64("ticket_id", ticketId), zap.Error(err))
		return nil, xerrors.New(err.Error(), code.DbError)
	}

	catDesc, err := NewCat().CatNameBackendSlice(ctx.Request().Context(), tkDetail.CatID)
	if err != nil {
		logger.Errorf(ctx.Request().Context(), "TicketUserInfo get cateShow return err. catId:%d. err:%v", tkDetail.CatID, err)
		return nil, err
	}

	tagDesc := make([]string, 0)
	var tagIds []uint32
	for _, v := range tkDetail.Tags {
		if v.TagID > 0 {
			tagIds = append(tagIds, v.TagID)
		}
	}
	if len(tagIds) > 0 {
		for _, v := range NewTags().GetTagsSlice(ctx.Request().Context(), tagIds) {
			tagDesc = append(tagDesc, strings.Join(v, "-"))
		}
	}

	zoneVipLevelRes := "-"
	if tkDetail.ZoneVipLevel > 0 {
		zoneVipLevelRes = fmt.Sprintf("R%d", tkDetail.ZoneVipLevel)
	}

	userInfo := &pb.UserInfoResp{
		TagName:      tagDesc,
		CreatedAt:    utils.TimeFormat(int64(tkDetail.CreatedAt)),
		Channel:      tkDetail.Channel,
		PackageId:    tkDetail.PackageID,
		DeviceType:   tkDetail.Device.DeviceType,
		RomGb:        utils.MetricsRate(cast.ToUint64(tkDetail.Device.RomGb), 100.0, 4),
		RemainRom:    utils.MetricsRate(cast.ToUint64(tkDetail.Device.RemainRom), 100.0, 4),
		Recharge:     utils.FloatRound(float64(tkDetail.Recharge)/code.RechargeRate, 2),
		Lang:         tkDetail.Lang,
		Country:      tkDetail.Device.Country,
		OsVersion:    tkDetail.Device.OsVersion,
		Ip:           tkDetail.Device.IP,
		Category:     strings.Join(catDesc, "-"),
		ZoneVipLevel: zoneVipLevelRes,
	}
	return userInfo, nil
}

// TicketsUserRecords 对应工单用户名下的所有工单列表
func (dto *ticketRepo) TicketsUserRecords(ctx context.Context, ticketId uint64) (*pb.TicketRecordResp, error) {
	HisInfo := &pb.TicketRecordResp{Data: make([]*pb.TicketRecordResp_Record, 0)}
	var curTkDetail *models.FpOpsTickets
	if err := dto.db.WithContext(ctx).Model(curTkDetail).Where("ticket_id = ?", ticketId).Take(&curTkDetail).Error; err != nil {
		return nil, err
	}

	var usersTk []*models.FpOpsTickets
	query := dto.db.Model(curTkDetail).Where("project = ?", curTkDetail.Project)
	if curTkDetail.AccountID != "" {
		query = query.Where("account_id = ?", curTkDetail.AccountID)
	} else {
		query = query.Where("account_id = ? AND uuid = ?", curTkDetail.AccountID, curTkDetail.UUID)
	}
	if err := query.Order("created_at DESC").Find(&usersTk).Error; err != nil {
		return nil, err
	}
	var tkIds []uint64
	for _, row := range usersTk {
		if row.TicketID == curTkDetail.TicketID {
			continue
		}
		if row.TicketID > 0 {
			tkIds = append(tkIds, row.TicketID)
		}
		HisInfo.Data = append(HisInfo.Data, &pb.TicketRecordResp_Record{
			Project:     row.Project,
			TicketId:    row.TicketID,
			Detail:      "",
			Recharge:    utils.FloatRound(float64(row.Recharge)/code.RechargeRate, 1),
			Status:      row.ConversionNode,
			WaitingTime: "-",
		})
	}
	if len(tkIds) > 0 {
		var commus []*models.FpOpsTicketsCommu
		err := dto.db.Table("(?) as c", dto.db.Model(&models.FpOpsTicketsCommu{}).
			Table("fp_ops_tickets_commu a").
			Select("distinct(a.ticket_id) as cid, a.*").
			Where("a.ticket_id in (?)", tkIds).
			Where("a.from_role = ?", uint32(pb.UserRole_PlayerRole)).
			Order("a.id")).Select("c.id, c.ticket_id, c.detail").
			Find(&commus).Error
		if err != nil {
			return nil, err
		}
		for _, commu := range commus {
			for idx, row := range HisInfo.Data {
				if commu.TicketID == row.TicketId {
					HisInfo.Data[idx].Detail = commu.Detail
				}
			}
		}
	}

	return HisInfo, nil
}

// NewTicketsUserRecords 根据account_id获取对应历史工单
func (dto *ticketRepo) NewTicketsUserRecords(accountId, project string) ([]*pb.PortraitInfoResp_TicketInfo, error) {
	HisInfo := make([]*pb.PortraitInfoResp_TicketInfo, 0)
	if accountId == "" {
		return HisInfo, nil
	}
	var usersTk []*models.FpOpsTickets
	if err := dto.db.Model(&models.FpOpsTickets{}).Where("account_id = ? and project = ?", accountId, project).
		Order("created_at DESC").Find(&usersTk).Error; err != nil {
		return nil, err
	}
	var tkIds []uint64
	for _, row := range usersTk {
		if row.TicketID > 0 {
			tkIds = append(tkIds, row.TicketID)
		}
		HisInfo = append(HisInfo, &pb.PortraitInfoResp_TicketInfo{
			Project:     row.Project,
			TicketId:    row.TicketID,
			Detail:      "",
			Recharge:    utils.FloatRound(float64(row.Recharge)/code.RechargeRate, 1),
			Status:      row.ConversionNode,
			WaitingTime: "-",
		})
	}
	if len(tkIds) > 0 {
		var commus []*models.FpOpsTicketsCommu
		err := dto.db.Table("(?) as c", dto.db.Model(&models.FpOpsTicketsCommu{}).
			Table("fp_ops_tickets_commu a").
			Select("distinct(a.ticket_id) as cid, a.*").
			Where("a.ticket_id in (?)", tkIds).
			Where("a.from_role = ?", uint32(pb.UserRole_PlayerRole)).
			Order("a.id")).Select("c.id, c.ticket_id, c.detail").
			Find(&commus).Error
		if err != nil {
			return nil, err
		}
		for _, commu := range commus {
			for idx, row := range HisInfo {
				if commu.TicketID == row.TicketId {
					HisInfo[idx].Detail = commu.Detail
				}
			}
		}
	}

	return HisInfo, nil
}

func (dto *ticketRepo) TicketsDialogue(ctx context.Context, ticketId uint64) ([]*pb.TicketDialogueResp, error) {
	var res []*pb.TicketDialogueResp
	var dailInfo []*models.FpOpsTicketsCommu
	err := dto.db.Model(&models.FpOpsTicketsCommu{}).
		Select("detail,operator,from_role,created_at,commu_type,picture,video").
		Where("ticket_id = ?", ticketId).
		Where("is_del = ?", code.StatusFalse).
		Order("created_at ASC").
		Find(&dailInfo).Error
	if err != nil {
		logger.Error(ctx, "ticket record info err", zap.Uint64("ticket_id", ticketId), zap.Error(err))
		return nil, xerrors.New(err.Error(), code.DbError)
	}
	for _, info := range dailInfo {
		res = append(res, &pb.TicketDialogueResp{
			Detail:      info.Detail,
			CreatedAt:   utils.TimeFormat(int64(info.CreatedAt)),
			CreatedTime: info.CreatedAt,
			Operator:    info.Operator,
			FromRole:    info.FromRole,
			CommuType:   info.CommuType,
			Picture:     info.Picture,
			Video:       info.Video,
		})
	}
	return res, nil
}

func (dto *ticketRepo) TicketsUpgrade(ctx context.Context, ticketId uint64, upgrade int, operator string, UpgradeNum uint32) (err error) {
	fun := "ticketRepo.TicketsUpgrade"
	tx := dto.db.WithContext(ctx).Begin()
	defer func() {
		if err != nil {
			if _rollErr := tx.Rollback(); _rollErr != nil {
				logger.Errorf(ctx, "%s rollback err. ticketId:%d. upgrade:%d. op:%s. err:%v. _rollErr:%v", fun, ticketId, upgrade, operator, err, _rollErr)
			}
		}
	}()
	now := utils.NowTimestamp()
	dest := map[string]interface{}{
		"priority":   upgrade, // Set the priority to 1
		"updated_at": now,
	}
	// 如果升级，则更新升级次数
	if upgrade > 0 {
		dest["upgrade_num"] = UpgradeNum
	}

	// save ticket
	if err = tx.Model(&models.FpOpsTickets{}).
		Where("ticket_id = ?", ticketId).Updates(dest).Error; err != nil {
		logger.Error(ctx, "ticket's props update err",
			zap.Uint64("ticket_id", ticketId), zap.Any("updates", dest), zap.Error(err))
		return
	}
	// save es
	if err = elasticsearch.DefaultTicketSyncSvc.UpdateTicket(ctx, ticketId, dest); err != nil {
		logger.Errorf(ctx, "%s es UpdateTicket return err. ticketId:%d. dest:%+v. err:%v", fun, ticketId, dest, err)
		return
	}

	// add hist
	hist := &models.FpOpsTicketsHistory{
		TicketID:  ticketId,
		Operate:   uint8(pb.TkEvent_TkEventUpgrade),
		OpDetail:  uint32(upgrade),
		OpRole:    uint8(pb.UserRole_ServiceRole),
		OpObject:  "",
		Remark:    fmt.Sprintf("to=%d", upgrade),
		Operator:  operator,
		CreatedAt: now,
		UpdatedAt: now,
	}
	if err = dto.AddTicketHistory(ctx, tx, hist); err != nil {
		logger.Errorf(ctx, "%s AddTicketHistory return err. ticketId:%d his:%+v. err:%v", fun, ticketId, hist, err)
		return
	}
	if err = tx.Commit().Error; err != nil {
		logger.Error(ctx, "transaction commit error", zap.Error(err))
		return err
	}
	return
}

// GetTicketTags 标签 - 查询
func (dto *ticketRepo) GetTicketTags(ctx context.Context, ticketId uint64) ([]uint32, error) {
	tagList := make([]uint32, 0)
	var dest []*models.FpOpsTicketsTags
	if err := dto.db.WithContext(ctx).Model(&models.FpOpsTicketsTags{}).Where("ticket_id = ?", ticketId).Find(&dest).Error; err != nil {
		return tagList, err
	}
	for _, tag := range dest {
		tagList = append(tagList, tag.TagID)
	}
	return tagList, nil
}

// SaveTicketTags 变更 工单绑定标签
func (dto *ticketRepo) SaveTicketTags(ctx context.Context, ticketId uint64, delTags, addTags []uint32, operator string) error {
	var (
		fun = "ticketRepo.SaveTicketTags ->"
		err error
		now = utils.NowTimestamp()
	)

	defer func() {
		if err != nil {
			logger.Errorf(ctx, "%s transaction return err. err:%v. ticketId:%d. delTags:%s. addTags:%s", fun, ticketId, delTags, addTags)
			return
		}
		if len(delTags) > 0 {
			elasticsearch.DefaultTicketSyncSvc.DeleteTag(ctx, ticketId, delTags)
		}
		if len(addTags) > 0 {
			elasticsearch.DefaultTicketSyncSvc.AddTag(ctx, ticketId, addTags)
		}
	}()

	tx := dto.db.WithContext(ctx).Begin()
	var hist []*models.FpOpsTicketsHistory
	// del
	if len(delTags) > 0 {
		if err = tx.Model(&models.FpOpsTicketsTags{}).
			Where("ticket_id = ? AND tag_id in (?)", ticketId, delTags).
			Delete(&models.FpOpsTicketsTags{}).Error; err != nil {
			return err
		}
		hist = append(hist, &models.FpOpsTicketsHistory{
			TicketID:  ticketId,
			Operate:   uint8(pb.TkEvent_TkEventTagDel),
			OpDetail:  0,
			OpRole:    uint8(pb.UserRole_ServiceRole),
			OpObject:  utils.ToJson(delTags),
			Operator:  operator,
			CreatedAt: now,
			UpdatedAt: now,
		})
	}
	// add
	if len(addTags) > 0 {
		for _, t := range addTags {
			tkTag := &models.FpOpsTicketsTags{
				TicketID:  ticketId,
				TagID:     t,
				Op:        operator,
				CreatedAt: now,
				UpdatedAt: now,
			}
			if err = tx.Model(&models.FpOpsTicketsTags{}).Create(tkTag).Error; err != nil {
				return err
			}
		}
		hist = append(hist, &models.FpOpsTicketsHistory{
			TicketID:  ticketId,
			Operate:   uint8(pb.TkEvent_TkEventTagAdd),
			OpDetail:  0,
			OpRole:    uint8(pb.UserRole_ServiceRole),
			OpObject:  utils.ToJson(addTags),
			Operator:  operator,
			CreatedAt: now,
			UpdatedAt: now,
		})
	}

	// save history
	if len(hist) > 0 {
		for _, hs := range hist {
			if err = dto.AddTicketHistory(ctx, tx, hs); err != nil {
				return err
			}
		}
	}

	// commit
	if err = tx.Commit().Error; err != nil {
		return err
	}
	return err
}

// BatchSaveTicketTags 批量打标签，只新增不覆盖
func (dto *ticketRepo) BatchSaveTicketTags(ctx context.Context, ticketId uint64, addTags []uint32, operator string) error {
	var (
		fun = "ticketRepo.SaveTicketTags ->"
		err error
		now = utils.NowTimestamp()
	)

	defer func() {
		if err != nil {
			logger.Errorf(ctx, "%s transaction return err. err:%v. ticketId:%d. addTags:%s", fun, ticketId, addTags)
			return
		}
		if len(addTags) > 0 {
			elasticsearch.DefaultTicketSyncSvc.AddTag(ctx, ticketId, addTags)
		}
	}()

	tx := dto.db.WithContext(ctx).Begin()
	var hist []*models.FpOpsTicketsHistory
	// add
	if len(addTags) > 0 {
		for _, t := range addTags {
			tkTag := &models.FpOpsTicketsTags{
				TicketID:  ticketId,
				TagID:     t,
				Op:        operator,
				CreatedAt: now,
				UpdatedAt: now,
			}
			if err = tx.Model(&models.FpOpsTicketsTags{}).Create(tkTag).Error; err != nil {
				return err
			}
		}
		hist = append(hist, &models.FpOpsTicketsHistory{
			TicketID:  ticketId,
			Operate:   uint8(pb.TkEvent_TkEventTagAdd),
			OpDetail:  0,
			OpRole:    uint8(pb.UserRole_ServiceRole),
			OpObject:  utils.ToJson(addTags),
			Operator:  operator,
			CreatedAt: now,
			UpdatedAt: now,
		})
	}

	// save history
	if len(hist) > 0 {
		for _, hs := range hist {
			if err = dto.AddTicketHistory(ctx, tx, hs); err != nil {
				return err
			}
		}
	}

	// commit
	if err = tx.Commit().Error; err != nil {
		return err
	}
	return err
}

func (dto *ticketRepo) CheckTagIdBind(ctx context.Context, tagIds []uint32) (bool, error) {
	var count int64
	err := dto.db.WithContext(ctx).Model(&models.FpOpsTicketsTags{}).
		Where("tag_id in (?)", tagIds).Count(&count).Error
	return count > 0, err
}

// TicketRemark 增加备注
func (dto *ticketRepo) TicketRemark(ctx context.Context, req *pb.TicketRemarkReq, operator string) error {
	var (
		now    = utils.NowTimestamp()
		fun    = "ticketRepo.TicketRemark"
		remark = &models.FpOpsTicketsCommu{
			TicketID:  req.TicketId,
			FromRole:  uint32(pb.UserRole_ServiceRole),
			CommuType: pb.CommuType_CommuTypeRemark.String(),
			Detail:    req.Content,
			Operator:  operator,
			Read:      false,
			IsDel:     false,
			CreatedAt: now,
			UpdatedAt: now,
		}

		hist = &models.FpOpsTicketsHistory{
			TicketID:  req.TicketId,
			Operate:   uint8(pb.TkEvent_TkEventRemark),
			OpRole:    uint8(pb.UserRole_ServiceRole),
			OpObject:  "",
			Remark:    req.Content,
			Operator:  operator,
			CreatedAt: now,
			UpdatedAt: now,
		}
	)
	tx := dto.db.WithContext(ctx).Begin()

	// add remark
	if err := dto.AddTkCommu(ctx, tx, remark); err != nil {
		tx.Rollback()
		logger.Error(ctx, "failed to create ticket remark commu", zap.String("fun", fun),
			zap.Uint64("ticket_id", req.TicketId), zap.String("content", req.Content), zap.Error(err))
		return err
	}

	// add history
	if err := dto.AddTicketHistory(ctx, tx, hist); err != nil {
		tx.Rollback()
		logger.Errorf(ctx, "%s AddTicketHistory return err. ticketId:%d. err:%v. hist:%+v", fun, req.TicketId, err, hist)
		return err
	}
	// 删除备注草稿
	if err := dto.TicketDraftDel(ctx, tx, req.TicketId, operator); err != nil {
		tx.Rollback()
		return err
	}
	// commit
	if err := tx.Commit().Error; err != nil {
		tx.Rollback()
		logger.Error(ctx, "transaction commit error", zap.Error(err))
		return xerrors.New(err.Error(), code.DbError)
	}

	return nil
}

// CheckLastAllocUserNoActionOverTm 检测用户最近一次分配用户 N 久没处理过
// 被操作次数
func (dto *ticketRepo) CheckLastAllocUserNoActionOverTm(ctx context.Context, ticketId uint64, ts int64) (int64, error) {
	lastAllocHis := &models.FpOpsTicketsHistory{}
	// 获取最后一次分配
	allocAcceptorOp := []uint8{uint8(pb.TkEvent_TkEventAutoAlloc), uint8(pb.TkEvent_TkEventAssign), uint8(pb.TkEvent_TkEventTurn), uint8(pb.TkEvent_TkEventReopen), uint8(pb.TkEvent_TkEventUserCommu)}
	if err := dto.db.Model(lastAllocHis).Where("ticket_id = ?", ticketId).
		Where("operate in (?)", allocAcceptorOp).Last(lastAllocHis).Error; err != nil {
		return 0, err
	}
	if lastAllocHis.ID == 0 {
		return 0, fmt.Errorf("his acceptor empty")
	}

	if lastAllocHis.CreatedAt > utils.NowTimestamp()-uint64(ts) { // 最后一次分配不超过最后时间
		return 0, fmt.Errorf("latest alloc time lt target time. target:%d. diff:%d", ts, utils.NowTimestamp()-lastAllocHis.CreatedAt)
	}
	// check 最后一次分配后是否有变更状态
	var cout int64
	transOp := []uint8{uint8(pb.TkEvent_TkEventCommu), uint8(pb.TkEvent_TkEventCommuClose), uint8(pb.TkEvent_TkEventRefused), uint8(pb.TkEvent_TkEventUpgrade)}
	if err := dto.db.Model(lastAllocHis).Where("ticket_id = ? AND id > ?", ticketId, lastAllocHis.ID).
		Where("operate in (?)", transOp).Count(&cout).Error; err != nil {
		return 0, err
	}
	return cout, nil
}

// GetTicketCountByElfinReq 根据精灵筛选条件获取游戏工单量
func (dto *ticketRepo) GetTicketCountByElfinReq(ctx echo.Context, tx *gorm.DB, req *pb.TicketCountStatisReq) (*models.TicketProjectIndices, error) {
	if tx == nil {
		tx = dto.db.WithContext(ctx.Request().Context())
	}
	// 转化时间戳
	start, end := getTimeUnix(req.Filter.StartTime, req.Filter.EndTime)
	//
	var dest *models.TicketProjectIndices
	query := tx.Model(&models.FpOpsTickets{})
	query.Select("project, count(1) as cnt").
		Where("created_at between ? and ?", uint64(start), uint64(end))

	query.Where("project = ?", req.Filter.GameProject)
	if len(req.Filter.Lang) > 0 {
		query = query.Where("lang in ?", req.Filter.Lang)
	}
	if len(req.Filter.Channel) > 0 {
		query = query.Where("channel in ?", req.Filter.Channel)
	}
	if req.Filter.ServerIds != "" {
		servs := strings.Split(req.Filter.ServerIds, ",")
		servsUint32 := make([]uint32, len(servs))
		for i, sid := range servs {
			val, _ := strconv.ParseUint(sid, 10, 32)
			servsUint32[i] = uint32(val)
		}
		query = query.Where("sid in ?", servsUint32)
	}
	// 处理 OR 条件
	if len(req.Filter.ServerArr) > 0 {
		orCondition := query.Session(&gorm.Session{NewDB: true})

		for _, spanList := range req.Filter.ServerArr {
			startServe := cast.ToInt(spanList.Gte)
			endServe := cast.ToInt(spanList.Lte)
			if startServe >= 0 && startServe < endServe {
				orCondition = orCondition.Or("sid between ? and ?", uint32(startServe), uint32(endServe))
			}
		}

		query = query.Where(orCondition)
	}
	catIdsUint32 := make([]uint32, len(req.CatIds))
	for i, catId := range req.CatIds {
		catIdsUint32[i] = uint32(catId)
	}
	query = query.Where("cat_id in ?", catIdsUint32)

	if err := query.Find(&dest).Error; err != nil {
		logger.Error(ctx.Request().Context(), "Get ticket count by project err", zap.String("err", err.Error()))
		return dest, err
	}
	return dest, nil
}

// GetTicketListByUser 获取用户工单列表
func (dto *ticketRepo) GetTicketListByUser(ctx echo.Context, account string, status, node int) ([]*models.FpOpsTickets, error) {
	query := dto.db.WithContext(ctx.Request().Context()).Model(&models.FpOpsTickets{}).
		Where("conversion_node=?", node).
		Where("status=?", status).
		Where("acceptor=?", account).
		Where("created_at<?", time.Now().Add(-2*time.Minute).Unix()).
		Where("auto_reply_id > 0")
	ticketList := make([]*models.FpOpsTickets, 0)
	if err := query.Find(&ticketList).Error; err != nil {
		if !errors.Is(err, gorm.ErrRecordNotFound) {
			logger.Error(ctx.Request().Context(), "get ticket list err",
				zap.Int("status", status),
				zap.Int("conversion_node", node),
				zap.String("err", err.Error()))
		}
		return nil, xerrors.New(err.Error(), code.DbError)
	}
	return ticketList, nil
}

func getTimeUnix(startTime, endTime string) (int64, int64) {
	var startTm, endTm time.Time
	BaseTimeFormat := "2006-01-02 15:04:05"
	tomorrow := now.BeginningOfDay().AddDate(0, 0, 1)
	if endTime != "" {
		endTm, _ = now.Parse(endTime)
		endTm = endTm.AddDate(0, 0, 1)
	} else {
		endTm = tomorrow
	}
	if endTm.After(tomorrow) {
		endTm = tomorrow
		endTime = endTm.Format(BaseTimeFormat)
	}
	if startTime != "" {
		startTm, _ = now.Parse(startTime)
	} else {
		startTm = endTm.AddDate(0, 0, -1)
		startTime = startTm.Format(BaseTimeFormat)
	}
	if startTm.After(endTm) {
		startTm = endTm.AddDate(0, 0, -1)
	}
	return startTm.Unix(), endTm.Unix()
}

// AutoReplyCount 获取自动回复工单数
func (dto *ticketRepo) AutoReplyCount(ctx context.Context) (int64, error) {
	var count int64
	if err := dto.db.WithContext(ctx).Model(&models.FpOpsTickets{}).Where("status = ? and auto_reply_id != 0", pb.TkStatus_TkStatusDone).Count(&count).Error; err != nil {
		logger.Errorf(ctx, "dto.AutoReplyCount get db data return err. err:%v.", err)
		return -1, err
	}
	return count, nil
}

// GetUserIsCreateTicketByAccountId 查询用户七日内是否提过工单
func (dto *ticketRepo) GetUserIsCreateTicketByAccountId(project, accountId string) (int64, uint64, uint64, error) {
	res := struct {
		CreatedAt uint64 `gorm:"column:created_at"`
		TicketID  uint64 `gorm:"column:ticket_id"`
	}{}

	var count int64
	sevenDaysAgo := time.Now().Add(-7 * 24 * time.Hour).Truncate(24 * time.Hour).Unix()

	subQuery := dto.db.Model(&models.FpOpsTickets{}).
		Where("project = ? AND account_id = ? AND created_at >= ?", project, accountId, sevenDaysAgo).
		Order("created_at DESC")

	// 先查询数量
	if err := subQuery.Count(&count).Error; err != nil {
		return 0, 0, 0, err
	}
	if count == 0 {
		return 0, 0, 0, nil
	}

	if err := subQuery.Select("created_at,ticket_id").
		First(&res).Debug().Error; err != nil {
		return 0, 0, 0, err
	}
	dump.Dump(res)

	return count, res.CreatedAt, res.TicketID, nil
}

// GetTicketSystemTagFromMaster 获取工单信息及系统标签
func (dto *ticketRepo) GetTicketSystemTagFromMaster(ctx context.Context, ticketId uint64) (*models.FpOpsTickets, error) {
	tkInfo := &models.FpOpsTickets{}
	if err := dto.db.WithContext(ctx).Model(&models.FpOpsTickets{}).
		Where("ticket_id=?", ticketId).Clauses(dbresolver.Write).
		Preload("SystemTags").
		Take(&tkInfo).Error; err != nil {
		logger.Error(ctx, "get ticket err. use master", zap.Uint64("ticket_id", ticketId), zap.String("err", err.Error()))
		return nil, err
	}
	return tkInfo, nil
}

func (dto *ticketRepo) GetPublicTags(ctx context.Context, ticketIds []uint64) ([]*pb.TicketPublicTagResp_TagInfo, error) {
	resp := []*pb.TicketPublicTagResp_TagInfo{}
	ticketTagDetail := []*models.FpOpsTicketsTags{}

	// 1. 查询所有工单的标签
	err := dto.db.Model(&models.FpOpsTicketsTags{}).
		Where("ticket_id in (?)", ticketIds).Find(&ticketTagDetail).Error
	if err != nil {
		logger.Error(ctx, "GetPublicTags ticket detail info err", zap.Error(err))
		return nil, xerrors.New(err.Error(), code.DbError)
	}

	// 2. 统计每个工单的标签
	tagsMap := make(map[uint64][]uint32)
	for _, v := range ticketTagDetail {
		tagsMap[v.TicketID] = append(tagsMap[v.TicketID], uint32(v.TagID))
	}

	// 3. 检查每个传入的工单ID是否都有标签
	// 如果任何一个工单没有标签，直接返回空结果
	for _, ticketId := range ticketIds {
		tags, exists := tagsMap[ticketId]
		if !exists || len(tags) == 0 {
			return resp, nil // 只要有一个工单没有标签，就返回空
		}
	}

	// 4. 计算所有工单标签的交集
	var commonTagIDs []uint32
	isFirst := true

	for _, ticketId := range ticketIds { // 使用传入的ticketIds来遍历，确保顺序
		if isFirst {
			commonTagIDs = tagsMap[ticketId]
			isFirst = false
			continue
		}
		commonTagIDs = utils.IntersectAny(commonTagIDs, tagsMap[ticketId])
		if len(commonTagIDs) == 0 {
			return resp, nil // 如果交集为空，提前返回
		}
	}

	// 5. 获取标签详情并返回
	for tagId, v := range NewTags().GetTagsSlice(ctx, commonTagIDs) {
		tagDesc := strings.Join(v, "-")
		resp = append(resp, &pb.TicketPublicTagResp_TagInfo{
			TagId:   tagId,
			TagName: tagDesc,
		})
	}

	return resp, nil
}

// BatchDeleteTicketTags 批量删除标签
func (dto *ticketRepo) BatchDeleteTicketTags(ctx context.Context, ticketId uint64, delTags []uint32, operator string) error {
	var (
		fun = "ticketRepo.BatchDeleteTicketTags ->"
		err error
		now = utils.NowTimestamp()
	)
	tx := dto.db.WithContext(ctx).Begin()
	defer func() {
		if err != nil {
			logger.Errorf(ctx, "%s transaction return err. err:%v. ticketId:%d", fun, err, ticketId)
			tx.Rollback()
			return
		}
	}()

	var hist []*models.FpOpsTicketsHistory

	// del
	if len(delTags) > 0 {
		if err = tx.Model(&models.FpOpsTicketsTags{}).
			Where("ticket_id = ? AND tag_id in (?)", ticketId, delTags).
			Delete(&models.FpOpsTicketsTags{}).Error; err != nil {
			return err
		}
		hist = append(hist, &models.FpOpsTicketsHistory{
			TicketID:  ticketId,
			Operate:   uint8(pb.TkEvent_TkEventTagDel),
			OpDetail:  0,
			OpRole:    uint8(pb.UserRole_ServiceRole),
			OpObject:  utils.ToJson(delTags),
			Operator:  operator,
			CreatedAt: now,
			UpdatedAt: now,
		})
	}

	// save history
	if len(hist) > 0 {
		for _, hs := range hist {
			if err = dto.AddTicketHistory(ctx, tx, hs); err != nil {
				return err
			}
		}
	}

	// commit
	if err = tx.Commit().Error; err != nil {
		return err
	}

	if len(delTags) > 0 {
		elasticsearch.DefaultTicketSyncSvc.DeleteTag(ctx, ticketId, delTags)
	}
	return err
}

// GetUnSolvedNewAiTicketList 获取新版ai客服要处理的单子
func (dto *ticketRepo) GetUnSolvedNewAiTicketList(ctx echo.Context, newAIType, node int, status []int) ([]*models.FpOpsTickets, error) {
	// type为新版ai客服，且不为自动回复单
	query := dto.db.WithContext(ctx.Request().Context()).Model(&models.FpOpsTickets{}).
		Where("conversion_node=?", node).
		Where("status in (?)", status).
		Where("ticket_type = ? and solve_type = ?", newAIType, pb.SolveType_SolveTypeUnknowTicket).
		Where("auto_reply_id = 0")
	ticketList := make([]*models.FpOpsTickets, 0)
	if err := query.Preload("Fields").Find(&ticketList).Error; err != nil {
		if !errors.Is(err, gorm.ErrRecordNotFound) {
			logger.Error(ctx.Request().Context(), "get ticket list err",
				zap.Ints("status", status),
				zap.Int("conversion_node", node),
				zap.String("err", err.Error()))
		}
		return nil, xerrors.New(err.Error(), code.DbError)
	}
	return ticketList, nil
}

// GetInvalidReplyContent 获取无效单回复模版
func (dto *ticketRepo) GetInvalidReplyContent(ctx echo.Context, project, lang string) (string, error) {
	replyInfo := &models.FpOpsTicketsInvalidReply{}
	// type为新版ai客服，且不为自动回复单
	err := dto.db.WithContext(ctx.Request().Context()).Model(&models.FpOpsTicketsInvalidReply{}).
		Where("project = ? and lang = ?", project, lang).Find(replyInfo).Error
	if err != nil {
		return "", xerrors.New("get invalid reply content err", code.DbError)
	}

	return replyInfo.ReplyTpl, nil
}

// GetTwoWeeksTicketsCount 双周每日工单量
func (dto *ticketRepo) GetTwoWeeksTicketsCount(ctx context.Context, project string, startTs, endTs uint64) ([]*models.DailyCount, error) {
	res := []*models.DailyCount{}
	err := dto.db.
		Model(&models.FpOpsTickets{}).
		WithContext(ctx).
		Select("DATE_FORMAT(FROM_UNIXTIME(created_at), ?) AS date, COUNT(*) AS count", "%Y-%m-%d").
		Where("created_at BETWEEN ? AND ? ", startTs, endTs).
		Where("project = ?", project).
		Group("date").
		Order("date").
		Scan(&res).Error
	if err != nil {
		return res, err
	}
	return res, nil
}
func (dto *ticketRepo) GetWeeklyTickets(ctx context.Context, project string, startTs, endTs uint64) ([]*models.FpOpsTickets, error) {
	var tickets []*models.FpOpsTickets
	if err := dto.db.
		WithContext(ctx).
		Preload("AiTag").
		Preload("Tags", func(db *gorm.DB) *gorm.DB {
			return db.Order("created_at ASC")
		}).
		Preload("Fields").
		Where("created_at BETWEEN ? AND ? ", startTs, endTs).
		Where("project = ?", project).
		Find(&tickets).Error; err != nil {
		return nil, err
	}

	return tickets, nil
}

// GetSpecialTicketTag 特殊标签 - 查询
func (dto *ticketRepo) GetSpecialTicketTag(ctx context.Context, ticketId uint64) (uint32, error) {
	tagList := make([]uint32, 0)
	var dest []*models.FpOpsTicketsTags
	if err := dto.db.WithContext(ctx).Table("fp_ops_tickets_tags a").
		Joins("left join fp_ops_tags b on a.tag_id = b.tag_id").
		Where("a.ticket_id = ? and b.lib_id = ?", ticketId, 13).Find(&dest).Error; err != nil {
		return 0, err
	}
	for _, tag := range dest {
		tagList = append(tagList, tag.TagID)
	}
	if len(tagList) == 0 {
		return 0, nil
	}
	return tagList[0], nil
}

// GetTicketInfoFieldsFromMaster 获取工单信息
func (dto *ticketRepo) GetTicketInfoFieldsFromMaster(ctx context.Context, ticketId uint64) (*models.FpOpsTickets, error) {
	tkInfo := &models.FpOpsTickets{}
	if err := dto.db.WithContext(ctx).Model(&models.FpOpsTickets{}).
		Where("ticket_id=?", ticketId).Clauses(dbresolver.Write).Preload("Fields").Find(&tkInfo).Error; err != nil {
		logger.Error(ctx, "get ticket err. use master", zap.Uint64("ticket_id", ticketId), zap.String("err", err.Error()))
		return nil, err
	}
	return tkInfo, nil
}
