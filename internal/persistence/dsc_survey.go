package persistence

import (
	"context"
	"errors"
	"github.com/labstack/echo/v4"
	"gorm.io/gorm"
	"ops-ticket-api/entity/stats"
	"ops-ticket-api/internal/framework/database"
	"ops-ticket-api/models"
	"ops-ticket-api/proto/pb"
)

// DcSatisfactionSurvey discord玩家满意度调查问卷
type DiscordSatisfactionSurvey struct {
	db *gorm.DB
}

// NewDiscordSatisfactionSurvey init
func NewDcSatisfactionSurvey() *DiscordSatisfactionSurvey {
	return &DiscordSatisfactionSurvey{
		db: database.Db(),
	}
}

func (d *DiscordSatisfactionSurvey) FetchLastReplyMsg() ([]*models.FpDscDmCommu, error) {
	dest := []*models.FpDscDmCommu{}
	query := `
       SELECT t1.project AS project, t1.dsc_user_id AS dsc_user_id, t1.msg_id AS msg_id 
FROM fp_dsc_dm_commu t1
INNER JOIN (
    SELECT project, dsc_user_id, MAX(created_at) AS latest_created_at
    FROM fp_dsc_dm_commu
    WHERE from_user_id = bot_id
    GROUP BY project, dsc_user_id
) t2
ON t1.project = t2.project 
   AND t1.dsc_user_id = t2.dsc_user_id
   AND t1.created_at = t2.latest_created_at
WHERE t1.from_user_id = t1.bot_id
    `
	if err := d.db.Raw(query).Find(&dest).Error; err != nil {
		return dest, err
	}
	return dest, nil
}

func (d *DiscordSatisfactionSurvey) SyncMaintainConfigToInteractStats() error {
	updateInteractSql := "UPDATE fp_dsc_interact_detail a INNER JOIN fp_dsc_player_maintain_config b ON a.dsc_user_id = b.dsc_user_id AND a.project = b.game_project SET a.uid = b.uid, a.account_id = b.account_id WHERE (a.uid = 0 AND a.account_id = '')"
	if err := d.db.Exec(updateInteractSql).Error; err != nil {
		return err
	}
	updateMessageCountSql := "UPDATE fp_dsc_message_count_detail a INNER JOIN fp_dsc_player_maintain_config b ON a.dsc_user_id = b.dsc_user_id AND a.project = b.game_project SET a.uid = b.uid, a.account_id = b.account_id WHERE (a.uid = 0 AND a.account_id = '')"
	return d.db.Exec(updateMessageCountSql).Error
}

func (d *DiscordSatisfactionSurvey) SatisfactionSurveyDateStats(req *pb.DiscordPlayerSatisfactionStatsReq) ([]*stats.SurveyDateRatingCount, error) {
	dest := []*stats.SurveyDateRatingCount{}
	query := d.db.Table(models.TableNameFpDscSurveyDetail).
		Select("evaluation_date, rating, COUNT(id) AS date_count")
	if len(req.Project) > 0 {
		query = query.Where("project IN (?)", req.Project)
	}
	if len(req.Date) > 0 {
		query = query.Where("evaluation_date BETWEEN ? AND ?", req.Date[0], req.Date[1])
	}
	if len(req.EvaluationTarget) > 0 {
		query = query.Where("evaluation_target in (?)", req.EvaluationTarget)
	}
	if len(req.Operator) > 0 {
		query = query.Where("operator IN (?)", req.Operator)
	}
	if len(req.Maintainer) > 0 {
		query = query.Where("maintainer IN (?)", req.Maintainer)
	}
	if err := query.Group("evaluation_date, rating").Order("evaluation_date DESC").Find(&dest).Error; err != nil {
		return dest, err
	}
	return dest, nil
}

func (d *DiscordSatisfactionSurvey) SatisfactionSurveyAccountStats(req *pb.DiscordPlayerSatisfactionStatsReq) ([]*stats.SurveyAccountRatingCount, error) {
	dest := []*stats.SurveyAccountRatingCount{}
	query := d.db.Table(models.TableNameFpDscSurveyDetail).
		Select("CASE WHEN operator = '' THEN '-' ELSE operator END operator, rating, COUNT(id) AS date_count")
	if len(req.Project) > 0 {
		query = query.Where("project IN (?)", req.Project)
	}
	if len(req.Date) > 0 {
		query = query.Where("evaluation_date BETWEEN ? AND ?", req.Date[0], req.Date[1])
	}
	if len(req.EvaluationTarget) > 0 {
		query = query.Where("evaluation_target in (?)", req.EvaluationTarget)
	}
	if len(req.Operator) > 0 {
		query = query.Where("operator IN (?)", req.Operator)
	}
	if len(req.Maintainer) > 0 {
		query = query.Where("maintainer IN (?)", req.Maintainer)
	}
	if err := query.Group("operator, rating").Order("operator DESC").Find(&dest).Error; err != nil {
		return dest, err
	}
	return dest, nil
}
func (d *DiscordSatisfactionSurvey) SatisfactionSurveyGameStats(req *pb.DiscordPlayerSatisfactionStatsReq) ([]*stats.SurveyGameRatingCount, error) {
	dest := []*stats.SurveyGameRatingCount{}
	query := d.db.Table(models.TableNameFpDscSurveyDetail).
		Select("project, rating, COUNT(id) AS date_count")
	if len(req.Project) > 0 {
		query = query.Where("project IN (?)", req.Project)
	}
	if len(req.Date) > 0 {
		query = query.Where("evaluation_date BETWEEN ? AND ?", req.Date[0], req.Date[1])
	}
	if len(req.EvaluationTarget) > 0 {
		query = query.Where("evaluation_target in (?)", req.EvaluationTarget)
	}
	if len(req.Operator) > 0 {
		query = query.Where("operator IN (?)", req.Operator)
	}
	if len(req.Maintainer) > 0 {
		query = query.Where("maintainer IN (?)", req.Maintainer)
	}
	if err := query.Group("project, rating").Order("project DESC").Find(&dest).Error; err != nil {
		return dest, err
	}
	return dest, nil
}

func (d *DiscordSatisfactionSurvey) SatisfactionSurveyDetails(req *pb.DiscordPlayerSatisfactionStatsReq) ([]*models.FpDscSurveyDetail, error) {
	dest := []*models.FpDscSurveyDetail{}
	query := d.db.Table(models.TableNameFpDscSurveyDetail).Select("*")
	if len(req.Project) > 0 {
		query = query.Where("project IN (?)", req.Project)
	}
	if len(req.Date) > 0 {
		query = query.Where("evaluation_date BETWEEN ? AND ?", req.Date[0], req.Date[1])
	}
	if len(req.EvaluationTarget) > 0 {
		query = query.Where("evaluation_target in (?)", req.EvaluationTarget)
	}
	if len(req.Operator) > 0 {
		query = query.Where("operator IN (?)", req.Operator)
	}
	if len(req.Maintainer) > 0 {
		query = query.Where("maintainer IN (?)", req.Maintainer)
	}
	if err := query.Order("evaluation_date DESC").Find(&dest).Error; err != nil {
		return dest, err
	}
	return dest, nil
}

func (d *DiscordSatisfactionSurvey) SaveDiscordSatisfactionSurvey(ctx echo.Context, details []*models.FpDscSurveyDetail) error {
	if err := d.db.WithContext(ctx.Request().Context()).Model(&models.FpDscSurveyDetail{}).
		CreateInBatches(details, len(details)).Error; err != nil {
		return err
	}
	return nil
}
func (d *DiscordSatisfactionSurvey) CheckTokenHasSurveyed(ctx context.Context, token string) (*int64, error) {
	var count int64
	if err := d.db.WithContext(ctx).Model(&models.FpDscSurveyDetail{}).
		Where("encrypted_token = ?", token).Count(&count).Error; err != nil {
		return &count, err
	}
	return &count, nil
}

func (d *DiscordSatisfactionSurvey) GetLastSurveyTime(ctx context.Context, uid int64) (int64, error) {
	detail := &models.FpDscSurveyDetail{}
	err := d.db.WithContext(ctx).Model(&models.FpDscSurveyDetail{}).
		Where("uid = ?", uid).Order("created_at desc").First(detail).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// 如果没查到任何记录，返回 0 让调用方知道可以立即提交
			return 0, nil
		}
		// 其他错误直接返回
		return 0, err
	}
	return detail.CreatedAt.Unix(), nil
}
