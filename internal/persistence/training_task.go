package persistence

import (
	"github.com/labstack/echo/v4"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"ops-ticket-api/internal/code"
	"ops-ticket-api/internal/framework/database"
	"ops-ticket-api/internal/framework/xerrors"
	"ops-ticket-api/models"
	"ops-ticket-api/proto/pb"
	"time"
)

// TrainingTask 工单知识库训练任务
type TrainingTask struct {
	db *gorm.DB
}

// NewTrainingTask init
func NewTrainingTask() *TrainingTask {
	return &TrainingTask{
		db: database.Db(),
	}
}

// CheckHasNotTrain 是否存在 未被训练的模型；
func (q *TrainingTask) CheckHasNotTrain(gameProject string, lang string) (bool, error) {
	var count int64
	err := q.db.Model(&models.FpOpsTrainingTask{}).Where("project = ? and lang = ? and status = ?", gameProject, lang, pb.TrainingTaskStatus_ProcessTicketStatusInit).
		Count(&count).Error
	if err != nil {
		return false, err
	}
	return count > 0, nil
}

func (q *TrainingTask) CreateTrainingLog(log *models.FpOpsTrainingTask) error {
	result := q.db.Create(log)
	if result.Error != nil {
		return result.Error
	}
	return nil
}

func (q *TrainingTask) GetTrainingLogById(logId int64) (*models.FpOpsTrainingTask, error) {
	var detail = &models.FpOpsTrainingTask{}
	if err := q.db.Model(&models.FpOpsTrainingTask{}).Where("log_id = ?", logId).First(detail).Error; err != nil {
		return nil, err
	}
	return detail, nil
}

func (q *TrainingTask) UpdateTrainingLog(up map[string]interface{}, where map[string]interface{}) (int64, error) {
	result := q.db.Model(&models.FpOpsTrainingTask{}).Where(where).Updates(up)
	return result.RowsAffected, result.Error
}

func (q *TrainingTask) GetRowTrainingLog(where map[string]interface{}) (*models.FpOpsTrainingTask, error) {
	var log = &models.FpOpsTrainingTask{}
	err := q.db.Model(&models.FpOpsTrainingTask{}).Where("project = ? and lang = ? and status = ?", where["project"], where["lang"], where["status"]).
		Where("log_id < ?", where["log_id"]).Order("log_id desc").First(log).Error
	return log, err
}

// GetAiChatgptMaxLastTime 获取知识库最后一条改动数据的时间（已成功同步在向量库中）
func (q *TrainingTask) GetAiChatgptMaxLastTime(logId int64, gameProject, lang string) (*models.FpOpsTrainingTaskDetail, error) {
	var log = &models.FpOpsTrainingTaskDetail{}
	err := q.db.Model(log).Where("log_id <= ? AND project = ? AND lang = ?",
		logId, gameProject, lang).Select("max(last_update_time) as last_update_time").Last(log).Error
	return log, err
}

func (q *TrainingTask) BatchSaveTrainingDetailLog(logs []*models.FpOpsTrainingTaskDetail) error {
	return q.db.Model(&models.FpOpsTrainingTaskDetail{}).CreateInBatches(logs, 200).Error
}

func (q *TrainingTask) GetAllTrainingTask(where map[string]interface{}) []*models.FpOpsTrainingTask {
	var result []*models.FpOpsTrainingTask
	_ = q.db.Model(&models.FpOpsTrainingTask{}).Where(where).Find(&result).Error
	return result
}

func (q *TrainingTask) GetAllTrainingTaskList(ctx echo.Context, req *pb.QuestionTrainLogReq) ([]*pb.QuestionTrainLogResp_TrainRecord, int64, error) {
	dest := make([]*pb.QuestionTrainLogResp_TrainRecord, 0)
	tastList := []*models.FpOpsTrainingTask{}
	subQuery := q.db.Model(&models.FpOpsTrainingTask{})
	var total int64
	query := subQuery
	if err := subQuery.Count(&total).Error; err != nil {
		logger.Error(ctx.Request().Context(), "get questionList err", zap.String("err", err.Error()))
		return nil, 0, err
	}

	if err := query.Order("updated_at DESC").Scopes(database.Paginate(&req.Page, &req.PageSize)).Find(&tastList).Error; err != nil {
		logger.Error(ctx.Request().Context(), "get TrainingTask list err", zap.String("err", err.Error()))
		return nil, 0, xerrors.New(err.Error(), code.DbError)
	}

	for _, v := range tastList {
		createdAt := time.Unix(v.CreatedAt, 0).Format("2006-01-02 15:04:05")
		dest = append(dest, &pb.QuestionTrainLogResp_TrainRecord{
			CreatedAt: createdAt,
			Operator:  v.Creator,
			Status:    uint32(v.Status),
			Project:   v.Project,
			Lang:      v.Lang,
		})
	}
	return dest, total, nil
}
