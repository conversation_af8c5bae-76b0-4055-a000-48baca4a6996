package persistence

import (
	"context"
	"errors"
	"github.com/labstack/echo/v4"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"ops-ticket-api/internal/code"
	"ops-ticket-api/internal/framework/database"
	"ops-ticket-api/internal/framework/xerrors"
	"ops-ticket-api/models"
	"ops-ticket-api/proto/pb"
	"sync"
	"time"
)

var (
	moduleCatRepo *ModuleCat
	OnceModuleCat sync.Once
)

// ModuleCat 问题分类配置
type ModuleCat struct {
	db *gorm.DB
}

// NewModuleCat init
func NewModuleCat() *ModuleCat {
	OnceModuleCat.Do(func() {
		moduleCatRepo = &ModuleCat{
			db: database.Db(),
		}
	})
	return moduleCatRepo
}

// CatModuleTree 回复模版分类配置(树)列表接口
func (dto *ModuleCat) CatModuleTree(ctx context.Context, project string) ([]*pb.ModuleCatItems, error) {
	dest := make([]*pb.ModuleCatItems, 0)
	field := "cat_id, category, parent_id, level"
	cond := map[string]interface{}{
		"project":    project,
		"is_deleted": uint16(code.StatusFalse),
	}
	query := dto.db.WithContext(ctx).Table(models.FpOpsModuleCategory{}.TableName()).Where(cond)
	if err := query.Select(field).Order("cat_id asc").Find(&dest).Error; err != nil {
		logger.Error(ctx, "get channel category list tree count err", zap.String("err", err.Error()))
		return nil, xerrors.New(err.Error(), code.DbError)
	}

	return dest, nil
}

// CatModuleAdd 添加问题分类
func (dto *ModuleCat) CatModuleAdd(ctx context.Context, cat *models.FpOpsModuleCategory) error {
	if err := dto.db.WithContext(ctx).Create(cat).Error; err != nil {
		logger.Error(ctx, "cat create err", zap.String("category", cat.Category), zap.String("err", err.Error()))
		return err
	}
	return nil
}

// ModuleDetail 获取问题分类信息
func (dto *ModuleCat) ModuleDetail(ctx echo.Context, catId uint32) (*models.FpOpsModuleCategory, error) {
	dest := &models.FpOpsModuleCategory{}
	err := dto.db.WithContext(ctx.Request().Context()).Model(&models.FpOpsModuleCategory{}).Where("cat_id=?", catId).Take(dest).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		logger.Error(ctx.Request().Context(), "get module cat info err", zap.Uint32("catId", catId), zap.String("err", err.Error()))
		return nil, xerrors.New(code.StatusText(code.DbError), code.DbError)
	}
	return dest, nil
}

// CatModuleSave 更新问题分类
func (dto *ModuleCat) CatModuleSave(ctx context.Context, catId uint32, cat map[string]interface{}) error {
	if err := dto.db.Model(&models.FpOpsModuleCategory{}).Where("cat_id = ?", catId).Updates(cat).Error; err != nil {
		logger.Error(ctx, "module cat save err", zap.Uint32("catId", catId), zap.String("err", err.Error()))
		return err
	}
	return nil
}

// CatModuleDel 问题分类删除
func (dto *ModuleCat) CatModuleDel(ctx context.Context, catId uint32, operator string) error {
	var count int64
	query := dto.db.WithContext(ctx).Model(&models.FpOpsModuleCategory{})
	if err := query.Where("parent_id = ? AND is_deleted=?", catId, code.StatusFalse).Count(&count).Error; err != nil {
		logger.Error(ctx, "get module category list count err", zap.Uint32("catId", catId), zap.String("err", err.Error()))
		return err
	}
	if count > 0 {
		return xerrors.New("存在子类别,不能删除～", code.DataConflict)
	}
	col := map[string]interface{}{
		"is_deleted": code.StatusTrue,
		"operator":   operator,
		"updated_at": time.Now(),
	}
	if err := dto.db.WithContext(ctx).Model(&models.FpOpsModuleCategory{}).Where("cat_id=?", catId).Updates(col).Error; err != nil {
		logger.Error(ctx, "del module cat info err", zap.Uint32("catId", catId), zap.String("err", err.Error()))
		return xerrors.New(err.Error(), code.DbError)
	}
	return nil
}

// CheckCatModuleName 校验同个游戏内是否有相同名称
func (dto *ModuleCat) CheckCatModuleName(ctx context.Context, category, project string, catId uint32) error {
	var has int64
	// 构造查询条件
	query := dto.db.Model(&models.FpOpsModuleCategory{}).Where("category = ? and project = ? and is_deleted = ?", category, project, 0)
	if catId != 0 {
		query = query.Where("cat_id != ?", catId)
	}
	// 查询重复记录
	err := query.Count(&has).Error
	if err != nil {
		logger.Error(ctx, "error querying existing module cat", zap.String("err", err.Error()))
		return xerrors.New(err.Error(), code.DbError)
	}
	// 如果有重复记录，返回错误
	if has > 0 {
		return xerrors.New("保存失败！该游戏下分类名称已存在", code.InvalidParams)
	}
	return nil
}
