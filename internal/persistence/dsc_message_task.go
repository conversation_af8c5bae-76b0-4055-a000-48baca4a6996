package persistence

import (
	"context"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"gorm.io/plugin/dbresolver"
	"ops-ticket-api/internal/code"
	"ops-ticket-api/internal/framework/database"
	"ops-ticket-api/internal/framework/xerrors"
	"ops-ticket-api/models"
	"ops-ticket-api/proto/pb"
	"time"
)

// DscMessageTask 批量私信
type DscMessageTask struct {
	db *gorm.DB
}

// NewDscMessageTask init
func NewDscMessageTask() *DscMessageTask {
	return &DscMessageTask{
		db: database.Db(),
	}
}

// CheckHasProcessInit 是否存在处理中的任务；
func (dto *DscMessageTask) CheckHasProcessInit() (bool, error) {
	var count int64
	err := dto.db.Model(&models.FpDscMessageTask{}).Where("status = ?", int(pb.DscMsgTaskStatus_ProcessStatusInit)).Count(&count).Error
	if err != nil {
		return false, err
	}
	return count > 0, nil
}

// TaskAdd 添加批量私信任务
func (dto *DscMessageTask) TaskAdd(ctx context.Context, task *models.FpDscMessageTask) error {
	if err := dto.db.WithContext(ctx).Create(task).Error; err != nil {
		logger.Errorf(ctx, "TaskAdd err. project:%v. operator:%v. err:%v", task.Project, task.Operator, err)
		return xerrors.New(err.Error(), code.DbError)
	}
	return nil
}

// TaskBatchAdd 批量添加批量私信任务明细
func (dto *DscMessageTask) TaskBatchAdd(ctx context.Context, tasks []*models.FpDscMessageTaskDetail) error {
	// 批量插入任务，使用 CreateInBatches
	if err := dto.db.WithContext(ctx).CreateInBatches(tasks, 100).Error; err != nil {
		logger.Errorf(ctx, "TaskAdd batch insert err. project:%v. uid:%v. channel:%v. err:%v", tasks[0].Project, tasks[0].UID, tasks[0].ChannelID, err)
		return xerrors.New(err.Error(), code.DbError)
	}
	return nil
}

func (dto *DscMessageTask) UpdateMessageTask(up map[string]interface{}, where map[string]interface{}) (int64, error) {
	result := dto.db.Model(&models.FpDscMessageTask{}).Where(where).Updates(up)
	return result.RowsAffected, result.Error
}

func (dto *DscMessageTask) UpdateMessageTaskDetail(up map[string]interface{}, where map[string]interface{}) (int64, error) {
	result := dto.db.Model(&models.FpDscMessageTaskDetail{}).Where(where).Updates(up)
	return result.RowsAffected, result.Error
}

func (dto *DscMessageTask) GetTaskById(ctx context.Context, taskId int64) (*models.FpDscMessageTask, error) {
	taskInfo := models.FpDscMessageTask{}
	tx := dto.db.WithContext(ctx).Model(&models.FpDscMessageTask{})
	if err := tx.Preload("Users").Where("id = ?", uint64(taskId)).Take(&taskInfo).Error; err != nil {
		logger.Errorf(ctx, "GetMessageAndUidsFromId err. taskId:%v. err:%v", taskId, err)
		return nil, xerrors.New(err.Error(), code.DbError)
	}

	return &taskInfo, nil
}

func (dto *DscMessageTask) GetChannelByGameAndUid(ctx context.Context, uid int64, taskId uint64, project, botId string) ([]string, error) {
	var channelIds []string
	err := dto.db.Model(&models.FpDscMessageTaskDetail{}).Where("project = ? and uid = ? and bot_id = ? and task_id = ?", project, uid, botId, taskId).Pluck("channel_id", &channelIds).Error
	if err != nil {
		return nil, xerrors.New(err.Error(), code.DbError)
	}
	return channelIds, nil
}

func (dto *DscMessageTask) CheckHasMsgId(msgId string) (bool, error) {
	var count int64
	err := dto.db.Model(&models.FpDscMessageTaskDetail{}).Clauses(dbresolver.Write).Where("msg_id = ?", msgId).Count(&count).Error
	if err != nil {
		return false, err
	}
	return count > 0, nil
}
func (dto *DscMessageTask) UpdateAllTaskDetail(taskId uint64, failLog string, status int) (int64, error) {
	// 批量更新 Users 中的 Status 和 FinishedAt 字段
	result := dto.db.Model(&models.FpDscMessageTaskDetail{}).
		Where("task_id =  ?", taskId).
		Updates(map[string]interface{}{
			"status":    status,
			"fail_log":  failLog,
			"update_at": time.Now(),
		})
	return result.RowsAffected, result.Error
}

func (dto *DscMessageTask) GetAllMessageTask(where map[string]interface{}) []*models.FpDscMessageTask {
	var result []*models.FpDscMessageTask
	_ = dto.db.Model(&models.FpDscMessageTask{}).Where(where).Find(&result).Error
	return result
}

func (dto *DscMessageTask) MessageTaskList(ctx context.Context, req *pb.DiscordMessageTaskListReq, all bool) ([]*pb.DiscordMessageTaskListResp_DiscordMessageRecord, uint32, error) {
	dest := make([]*pb.DiscordMessageTaskListResp_DiscordMessageRecord, 0)
	// 首先查出符合条件的数据条数
	taskList := []*models.FpDscMessageTask{}
	subQuery := dto.db.Model(&models.FpDscMessageTask{}).
		Preload("Users", "status = ?", int(pb.DscMsgTaskStatus_ProcessStatusFail)) // 只预加载失败状态的用户
	if len(req.Project) > 0 {
		subQuery = subQuery.Where("project IN (?)", req.Project)
	}
	if len(req.Date) > 0 {
		subQuery = subQuery.Where("date(created_at) BETWEEN ? AND ?", req.Date[0], req.Date[1])
	}
	if len(req.Operator) > 0 {
		subQuery = subQuery.Where("operator IN (?)", req.Operator)
	}
	var total int64
	query := subQuery
	if err := subQuery.Count(&total).Error; err != nil {
		logger.Error(ctx, "get dsc task id that qualifies err", zap.String("err", err.Error()))
		return nil, 0, err
	}
	// 导出数据不分页
	if all {
		if err := query.Order("created_at DESC").Find(&taskList).Error; err != nil {
			logger.Error(ctx, "get dsc message task list err", zap.String("err", err.Error()))
			return nil, 0, xerrors.New(err.Error(), code.DbError)
		}
	} else {
		if err := query.Order("created_at DESC").Scopes(database.Paginate(&req.Page, &req.PageSize)).Find(&taskList).Error; err != nil {
			logger.Error(ctx, "get dsc message task list err", zap.String("err", err.Error()))
			return nil, 0, xerrors.New(err.Error(), code.DbError)
		}
	}
	for _, task := range taskList {
		taskRecord := &pb.DiscordMessageTaskListResp_DiscordMessageRecord{
			TaskId:  task.ID,
			Project: task.Project,
			ReplyContent: &pb.DiscordMessageTaskListResp_ReplyContent{
				Content: task.Content,
				FileUrl: task.FileUrl,
			},
			Status: int32(task.Status),
			Total:  int32(task.FailedCount + task.SuccessCount),
			Count: &pb.DiscordMessageTaskListResp_Count{
				SuccessCount: int32(task.SuccessCount),
				FailedCount:  int32(task.FailedCount),
			},
			Operator:   task.Operator,
			CreateAt:   task.CreatedAt.Format("2006-01-02 15:04:05"),
			FinishedAt: task.FinishedAt.Format("2006-01-02 15:04:05"),
		}
		taskRecord.FailIds = []int64{}
		// 获取每个task对应的所有失败用户
		if len(task.Users) != 0 {
			for _, user := range task.Users {
				taskRecord.FailIds = append(taskRecord.FailIds, user.UID)
			}
		}
		// 填充返回
		dest = append(dest, taskRecord)
	}
	return dest, uint32(total), nil
}
