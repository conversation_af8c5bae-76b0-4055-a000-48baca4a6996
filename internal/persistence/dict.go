// Copyright 2021 funplus Authors. All Rights Reserved.
// @Description: 表单映射
// @Author: Darcy
// @Date: 2021/11/5 4:45 PM

package persistence

import (
	"context"
	"errors"
	"github.com/spf13/cast"
	"sync"
	"ops-ticket-api/internal/code"
	"ops-ticket-api/internal/framework/database"
	"ops-ticket-api/internal/framework/xerrors"
	"ops-ticket-api/models"
	"ops-ticket-api/proto/pb"
	"ops-ticket-api/utils"

	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"gorm.io/gorm"
)

var (
	dictOnce sync.Once
	dictRepo *DictRepo
)

// DictRepo 表单映射
type DictRepo struct {
	db *gorm.DB
}

// NewDictRepo init
func NewDictRepo() *DictRepo {
	dictOnce.Do(func() {
		dictRepo = &DictRepo{
			db: database.Db(),
		}
	})
	return dictRepo
}

func (dto *DictRepo) GetDictOps(ctx context.Context) ([]*pb.DictOptsResp_Opts, error) {
	dest := make([]*pb.DictOptsResp_Opts, 0)
	if err := dto.db.WithContext(ctx).Model(&models.FpOpsDict{}).Where("enable=?", code.StatusTrue).Find(&dest).Error; err != nil {
		logger.Error(ctx, "get dict ops err ", logger.String("err", err.Error()))
		return nil, xerrors.New(err.Error(), code.DbError)
	}
	return dest, nil
}

func (dto *DictRepo) DictList(ctx context.Context) ([]*pb.DictListResp_Dict, error) {
	dest := make([]*pb.DictListResp_Dict, 0)
	if err := dto.db.WithContext(ctx).Model(&models.FpOpsDict{}).Find(&dest).Error; err != nil {
		logger.Error(ctx, "get dict list err", logger.String("err", err.Error()))
		return nil, xerrors.New(err.Error(), code.DbError)
	}
	for k, d := range dest {
		dest[k].UpdatedAt = utils.TimeFormat(cast.ToInt64(d.UpdatedAt))
	}
	return dest, nil
}

func (dto *DictRepo) DictInfo(ctx context.Context, dictId uint32) (*pb.DictInfoResp, error) {
	dest := &pb.DictInfoResp{}
	if err := dto.db.WithContext(ctx).Model(&models.FpOpsDict{}).Take(dest, "dict_id=?", dictId).Error; err != nil {
		if !errors.Is(err, gorm.ErrRecordNotFound) {
			logger.Error(ctx, "get dict info err", logger.String("err", err.Error()))
		}
		return nil, xerrors.New(err.Error(), code.DbError)
	}
	return dest, nil
}

func (dto *DictRepo) DictAdd(ctx context.Context, dict *models.FpOpsDict) error {
	if err := dto.db.WithContext(ctx).Create(&dict).Error; err != nil {
		logger.Error(ctx, "create dict info err", logger.String("dict_name", dict.DictName), logger.String("err", err.Error()))
		return err
	}
	return nil
}

func (dto *DictRepo) DictSave(ctx context.Context, dictId uint32, dict map[string]interface{}) error {
	if err := dto.db.WithContext(ctx).Model(&models.FpOpsDict{}).Where("dict_id=?", dictId).Updates(dict).Error; err != nil {
		logger.Error(ctx, "save dict info err", logger.Uint32("dictId", dictId), logger.Any("updates", dict), logger.String("err", err.Error()))
		return err
	}
	return nil
}
