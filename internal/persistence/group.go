// Copyright 2021 funplus Authors. All Rights Reserved.
// @Description: 技能组
// @Author: Darcy
// @Date: 2021/10/26 3:46 PM

package persistence

import (
	"context"
	"github.com/labstack/echo/v4"
	"github.com/spf13/cast"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"go.uber.org/zap" // 引入日志库
	"gorm.io/gorm"
	"gorm.io/plugin/dbresolver"
	"ops-ticket-api/internal/code"
	"ops-ticket-api/internal/cst"
	"ops-ticket-api/internal/framework/database"
	"ops-ticket-api/internal/framework/lang"
	"ops-ticket-api/internal/framework/xerrors"
	"ops-ticket-api/models"
	"ops-ticket-api/proto/pb"
	"ops-ticket-api/utils"
	"strings"
	"time"
)

var (
	minuteLayout = "2006-01-02 15:04"
	baseLayout   = "2006-01-02 15:04:05"
)

// Group 技能组
type Group struct {
	db *gorm.DB
}

// NewGroupRepo init
func NewGroupRepo() *Group {
	return &Group{
		db: database.Db(),
	}
}

func (dto *Group) GroupProjectUser(ctx context.Context, project string, withOnline bool) ([]*models.ProjectGroupUserDf, error) {
	var users []*models.FpOpsGroupUser
	query := dto.db.WithContext(ctx).Clauses(dbresolver.Write).Model(&models.FpOpsGroupUser{}).Where("enable = ?", code.StatusTrue)
	if withOnline {
		query = query.Where("is_login = ?", uint32(pb.UserLoginStatus_UserLoginStatusYes))
	}
	err := query.Find(&users).Error
	if err != nil {
		return nil, err
	}
	var gameUsers = make([]*models.ProjectGroupUserDf, 0)
	var userIdx = make(map[string]bool, 0)
	for _, user := range users {
		if userIdx[user.User] {
			continue
		}
		if utils.GroupGameMatch(project, user.Game) {
			gameUsers = append(gameUsers, &models.ProjectGroupUserDf{
				Id:            user.ID,
				GroupId:       user.GroupID,
				IsLogin:       pb.UserLoginStatus(user.IsLogin),
				UpperLimit:    user.UpperLimit,
				LastAllocTkAt: user.LastAllocTkAt,
				User:          user.User,
				Game:          utils.StrToSlice(user.Game),
				Language:      utils.StrToSlice(user.Language),
			})
			userIdx[user.User] = true
		}
	}
	return gameUsers, nil
}

// GroupList 团队分单配置列表
func (dto *Group) GroupList(ctx context.Context, req *pb.GroupListReq) ([]*pb.GroupListResp_Group, uint32, error) {
	dest := make([]*pb.GroupListResp_Group, 0)
	var catList []*models.FpOpsGroupCatList
	field := "g.id as group_id,gu.id, g.`group_desc`, gu.`game`, gu.`language`, gu.`updated_at`, gu.`user`,gu.operator,gu.enable,gu.upper_limit"
	query := dto.db.WithContext(ctx).Table("fp_ops_group g").Select(field)
	//query.Joins("LEFT JOIN fp_ops_group_cat gc ON gc.group_id=g.id")
	query.Joins("left join fp_ops_group_user gu on gu.group_id = g.id")
	query.Where("gu.enable = ?", 1)
	if len(req.User) > 0 {
		query.Where("gu.user LIKE ?", "%"+req.User+"%")
	}
	if len(req.GroupDesc) > 0 {
		query.Where("g.group_desc  LIKE ?", "%"+req.GroupDesc+"%")
	}

	var count int64
	if err := query.Count(&count).Error; err != nil {
		logger.Error(ctx, "get group list count err", zap.String("err", err.Error()))
		return nil, 0, err
	}
	if err := query.Scopes(database.Paginate(&req.Page, &req.PageSize)).Find(&catList).Error; err != nil {
		logger.Error(ctx, "get group list err", zap.String("err", err.Error()))
		return nil, 0, xerrors.New(err.Error(), code.DbError)
	}
	// utc时间
	for _, v := range catList {
		dest = append(dest, &pb.GroupListResp_Group{
			Id:         v.ID,
			GroupId:    v.GroupID,
			GroupDesc:  v.GroupDesc,
			User:       v.User,
			Game:       utils.StrToSlice(v.Game),
			UpdatedAt:  v.UpdatedAt.Format(time.RFC3339),
			Operator:   v.Operator,
			Enable:     v.Enable,
			Language:   utils.StrToSlice(v.Language),
			UpperLimit: uint32(v.UpperLimit),
		})
	}
	return dest, cast.ToUint32(count), nil
}

// GroupInfo 技能组信息
func (dto *Group) GroupInfo(ctx context.Context, groupId uint32) (*models.FpOpsGroup, error) {
	groupInfo := &models.FpOpsGroup{}
	if err := dto.db.WithContext(ctx).Model(&models.FpOpsGroup{}).Preload("User").Where("id=?", groupId).Take(&groupInfo).Error; err != nil {
		logger.Error(ctx, "get group info err", zap.Uint32("groupId", groupId), zap.String("err", err.Error()))
		return nil, xerrors.New(err.Error(), code.DbError)
	}
	return groupInfo, nil
}

// GroupAdd 技能组添加
func (dto *Group) GroupAdd(ctx echo.Context, req *pb.GroupSaveReq) error {
	var (
		operator = cast.ToString(ctx.Get(cst.AccountInfoCtx))
	)
	if req.UpperLimit > 50 {
		return xerrors.New("UpperLimit cannot be greater than 50", code.InvalidParams)
	}
	groupInfo := models.FpOpsGroup{
		UpperLimit: req.UpperLimit,
		Enable:     true,
		Operator:   operator,
		CreatedAt:  time.Now(),
		UpdatedAt:  time.Now(),
	}

	// 创建 FpOpsGroupUser 列表
	var users []*models.FpOpsGroupUser
	var _userList []string
	var repeat = make(map[string]bool, 0)
	for _, user := range req.User {
		user = strings.TrimSpace(user)
		if user == "" {
			continue
		}
		if _, ok := repeat[user]; ok {
			continue
		}
		repeat[user] = true
		_userList = append(_userList, user)
		users = append(users, &models.FpOpsGroupUser{
			User:       user,
			Game:       utils.ToJson(req.Game),
			Language:   utils.ToJson(req.Language),
			Enable:     true,
			IsLogin:    uint32(pb.UserLoginStatus_UserLoginStatusNo),
			UpperLimit: req.UpperLimit,
			CreatedAt:  time.Now(),
			UpdatedAt:  time.Now(),
			Operator:   operator,
		})
	}
	if len(_userList) == 0 {
		logger.Errorf(ctx.Request().Context(), "group.GroupAdd get userList empty. %v", _userList)
		return xerrors.New(xerrors.New(lang.FormatText(ctx, "RespInvalidParams"), code.MissingParams))
	}

	tx := dto.db.WithContext(ctx.Request().Context()).Begin()
	{
		//var has int64
		//err := tx.Model(&models.FpOpsGroup{}).Where("group_desc = ? AND enable = ?", groupInfo.GroupDesc, code.StatusTrue).Count(&has).Error
		//if err != nil {
		//	tx.Rollback()
		//	logger.Error(ctx.Request().Context(), "error querying existing group", zap.String("err", err.Error()))
		//	return xerrors.New(err.Error(), code.DbError)
		//}
		//if has > 0 {
		//	tx.Rollback()
		//	return xerrors.New("团队名称已存在", code.InvalidParams)
		//}
		var bindUsers []string
		if err := tx.Model(&models.FpOpsGroupUser{}).Where("user in (?) AND enable = ?", _userList, code.StatusTrue).
			Pluck("user", &bindUsers).Error; err != nil {
			tx.Rollback()
			return err
		}
		if len(bindUsers) > 0 {
			return xerrors.New("当前部分人员已绑定: "+strings.Join(bindUsers, ", "), code.IdempotenceErr)
		}
	}

	{
		// 插入 FpOpsGroup 数据
		if err := tx.Create(&groupInfo).Error; err != nil {
			tx.Rollback()
			logger.Error(ctx.Request().Context(), "group add err", zap.String("err", err.Error()))
			return xerrors.New(err.Error(), code.DbError)
		}
	}

	// 插入 FpOpsGroupUser 数据
	for _, user := range users {
		user.GroupID = groupInfo.ID
		if err := tx.Create(user).Error; err != nil {
			tx.Rollback()
			logger.Error(ctx.Request().Context(), "group user add err", zap.String("err", err.Error()))
			return xerrors.New(err.Error(), code.DbError)
		}
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		logger.Error(ctx.Request().Context(), "transaction commit err", zap.String("err", err.Error()))
		return xerrors.New(err.Error(), code.DbError)
	}
	return nil
}

// GetUserState return : UserLoginStatus， upper_limiter:接单量限制，error
func (dto *Group) GetUserState(ctx context.Context, account, gameProject, lang string) ([]*pb.GroupUserStateDf, pb.UserLoginStatus, int64, error) {
	fun := "Group.GetUserState"
	var upperLimit int64
	var loginState = pb.UserLoginStatus_UserLoginStatusNo
	var result []*pb.GroupUserStateDf
	err := dto.db.WithContext(ctx).Table("fp_ops_group g").
		Select("gu.upper_limit upper_limit,gu.game game,gu.language language,gu.is_login").
		Joins("LEFT JOIN fp_ops_group_user gu ON g.id = gu.group_id").
		Where("g.enable = ? AND gu.user = ?", code.StatusTrue, account).Find(&result).Error
	if err != nil {
		logger.Errorf(ctx, "%s get detail return err. account:%s. game:%s. lang:%s. err:%v", fun, account, gameProject, lang, err)
		return result, pb.UserLoginStatus_UserLoginStatusNo, upperLimit, err
	}
	for _, row := range result {
		// is login
		if pb.UserLoginStatus_name[row.GetIsLogin()] == pb.UserLoginStatus_UserLoginStatusYes.String() {
			loginState = pb.UserLoginStatus_UserLoginStatusYes
		}
		// upper limit
		if gameProject != "" {
			if !utils.GroupGameMatch(gameProject, row.Game) {
				continue
			}
			if !utils.GroupLangMatch(lang, row.Language) {
				continue
			}
			if row.UpperLimit > upperLimit {
				upperLimit = row.UpperLimit
			}
		}
	}
	return result, loginState, upperLimit, nil
}

// GroupUpdate 技能组更新
func (dto *Group) GroupUpdate(ctx echo.Context, req *pb.GroupSaveReq) error {
	groupUserInfo := models.FpOpsGroupUser{}
	err := dto.db.WithContext(ctx.Request().Context()).Model(&groupUserInfo).Where("id = ?", req.Id).First(&groupUserInfo).Error
	if err != nil {
		logger.Error(ctx.Request().Context(), "group user update: failed to fetch group info", zap.Uint32("ID", uint32(req.Id)), zap.Error(err))
		return xerrors.New(err.Error(), code.DbError)
	}
	if req.UpperLimit > 50 {
		return xerrors.New("UpperLimit cannot be greater than 50", code.InvalidParams)
	}
	groupInfo := models.FpOpsGroup{}
	if err := dto.db.WithContext(ctx.Request().Context()).Model(groupInfo).Where("id = ?", groupUserInfo.GroupID).First(&groupInfo).Error; err != nil {
		logger.Error(ctx.Request().Context(), "group update: failed to fetch group info", zap.Uint32("ID", uint32(req.Id)), zap.Error(err))
		return xerrors.New(err.Error(), code.DbError)
	}

	//groupInfo.GroupDesc = strings.ReplaceAll(req.GroupDesc, " ", "")
	//groupInfo.UpperLimit = req.UpperLimit
	groupInfo.UpdatedAt = time.Now()

	tx := dto.db.WithContext(ctx.Request().Context()).Begin()

	if err := tx.Save(&groupInfo).Error; err != nil {
		tx.Rollback()
		logger.Error(ctx.Request().Context(), "group update: failed to update group info", zap.Uint32("groupID", req.GroupId), zap.Error(err))
		return xerrors.New(err.Error(), code.DbError)
	}

	{
		groupUserInfo.UpperLimit = req.UpperLimit
		groupUserInfo.Game = utils.ToJson(req.Game)
		groupUserInfo.Language = utils.ToJson(req.Language)
		groupUserInfo.Operator = cast.ToString(ctx.Get(cst.AccountInfoCtx))
		groupUserInfo.UpdatedAt = time.Now()
		if err := tx.Save(&groupUserInfo).Error; err != nil {
			tx.Rollback()
			logger.Error(ctx.Request().Context(), "group_user update: failed to update group info", zap.Uint32("groupID", req.GroupId), zap.Error(err))
			return xerrors.New(err.Error(), code.DbError)
		}
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		logger.Error(ctx.Request().Context(), "group update: transaction commit error", zap.Error(err))
		return xerrors.New(err.Error(), code.DbError)
	}

	return nil
}

// GroupDel 删除分单用户
func (dto *Group) GroupDel(ctx context.Context, req *pb.GroupDelReq) error {
	tx := dto.db.WithContext(ctx).Begin()

	// 查询用户是否有正在处理中的工单
	var count int64
	if err := tx.Model(&models.FpOpsTickets{}).Where("acceptor = ? AND status = ?", req.User, uint32(pb.TkStatus_TkStatusProcessing)).Count(&count).Error; err != nil {
		logger.Error(ctx, "group delete: failed to count processing work orders", zap.String("acceptor", req.User), zap.Error(err))
		tx.Rollback()
		return xerrors.New(err.Error(), code.DbError)
	}
	if count > 0 {
		tx.Rollback()
		return xerrors.New("该用户有处理中工单，暂时无法删除", code.Error)
	}

	userInfo := models.FpOpsGroupUser{}
	err := tx.Model(&models.FpOpsGroupUser{}).Where("user = ? AND enable = ?", req.User, code.StatusTrue).First(&userInfo).Error
	if err != nil {
		logger.Error(ctx, "group delete: failed to fetch user info", zap.String("user", req.User), zap.Error(err))
		tx.Rollback()
		return xerrors.New(err.Error(), code.DbError)
	}

	if err := tx.Model(&userInfo).Where("user = ?", req.User).Updates(map[string]interface{}{
		"enable":     false,
		"updated_at": time.Now(),
	}).Error; err != nil {
		logger.Error(ctx, "group delete: failed to update user info", zap.String("user", req.User), zap.Error(err))
		tx.Rollback()
		return xerrors.New(err.Error(), code.DbError)
	}

	// 检查当前团队下是否还有其他用户，如果没有，则将整个团队标记为删除状态
	var userCount int64
	if err := tx.Model(&models.FpOpsGroupUser{}).Where("group_id = ? AND enable = ?", userInfo.GroupID, code.StatusTrue).Count(&userCount).Error; err != nil {
		logger.Error(ctx, "group delete: failed to count remaining users", zap.Uint32("groupID", userInfo.GroupID), zap.Error(err))
		tx.Rollback()
		return xerrors.New(err.Error(), code.DbError)
	}
	if userCount == 0 {
		groupInfo := models.FpOpsGroup{}
		if err := tx.Model(&models.FpOpsGroup{}).Where("id = ?", userInfo.GroupID).First(&groupInfo).Error; err != nil {
			logger.Error(ctx, "group delete: failed to fetch group info", zap.Uint32("groupID", userInfo.GroupID), zap.Error(err))
			tx.Rollback()
			return xerrors.New(err.Error(), code.DbError)
		}

		if err := tx.Model(&groupInfo).Updates(map[string]interface{}{
			"enable":     false,
			"updated_at": time.Now(),
		}).Error; err != nil {
			logger.Error(ctx, "group delete: failed to update group info", zap.Uint32("groupID", userInfo.GroupID), zap.Error(err))
			tx.Rollback()
			return xerrors.New(err.Error(), code.DbError)
		}
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		logger.Error(ctx, "group delete: transaction commit error", zap.Error(err))
		return xerrors.New(err.Error(), code.DbError)
	}

	return nil
}

func (dto *Group) UserAssignedTicket(ctx context.Context, tx *gorm.DB, user string) error {
	//	// todo 实现 给客服人员分单后，记录最近一次获单时间
	if tx == nil {
		tx = dto.db.WithContext(ctx)
	}
	return tx.Model(&models.FpOpsUsers{}).Where("account = ?", user).Updates(map[string]interface{}{
		"last_alloc_tk_at": utils.NowTimestamp(),
	}).Error
}
