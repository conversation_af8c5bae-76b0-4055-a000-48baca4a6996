// Copyright 2024 funplus Authors. All Rights Reserved.
// @Description: module
// @Author: jun.qiu
// @Date: 2024/06/05 3:02 PM

package persistence

import (
	"context"
	"errors"
	"fmt"
	"ops-ticket-api/internal/code"
	"ops-ticket-api/internal/cst"
	"ops-ticket-api/internal/framework/database"
	"ops-ticket-api/internal/framework/xerrors"
	"ops-ticket-api/models"
	"ops-ticket-api/proto/pb"
	"ops-ticket-api/utils"
	"strings"

	"github.com/labstack/echo/v4"
	"github.com/spf13/cast"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// Module 回复模板
type Module struct {
	db *gorm.DB
}

// NewModule init
func NewModule() *Module {
	return &Module{
		db: database.Db(),
	}
}

func (m *Module) ModuleSave(ctx echo.Context, req *pb.ModuleSaveReq) error {
	operator := cast.ToString(ctx.Get(cst.AccountInfoCtx))
	current := utils.NowTimestamp()
	operateTime := utils.TimeFormat(int64(current))
	moduleInfo := &models.FpOpsTicketsModule{
		ModuleName:  req.ModuleName,
		Content:     req.Content,
		Operator:    operator,
		Enable:      code.ModuleEnable,
		CatID:       req.CatId,
		CreateTime:  current,
		UpdatedTime: operateTime,
	}
	tx := m.db.WithContext(ctx.Request().Context()).Begin()
	var has int64
	err := tx.Model(&models.FpOpsTicketsModule{}).Where("module_name = ?", req.ModuleName).Count(&has).Error
	if err != nil {
		tx.Rollback()
		logger.Error(ctx.Request().Context(), "error querying existing module", zap.String("err", err.Error()))
		return xerrors.New(err.Error(), code.DbError)
	}
	if has > 0 {
		tx.Rollback()
		return xerrors.New("模板名重复", code.InvalidParams)
	}
	if err = tx.Model(&models.FpOpsTicketsModule{}).Create(moduleInfo).Error; err != nil {
		tx.Rollback()
		logger.Error(ctx.Request().Context(), "error create module", zap.String("err", err.Error()))
		return xerrors.New(err.Error(), code.DbError)
	}
	moduleId := moduleInfo.ID
	// 创建游戏关联模板列表
	moduleGame := &models.FpOpsTicketsModuleGame{
		ModuleId:    moduleId,
		GameProject: req.GameProject,
		CreateTime:  current,
		UpdatedTime: operateTime,
	}
	//if err = tx.Model(&models.FpOpsTicketsModuleGame{}).CreateInBatches(moduleGames, len(moduleGames)).Error; err != nil {
	//	tx.Rollback()
	//	logger.Error(ctx.Request().Context(), "error create moduleGames in batch", zap.String("err", err.Error()))
	//	return xerrors.New(err.Error(), code.DbError)
	//}
	if err = tx.Model(&models.FpOpsTicketsModuleGame{}).Create(moduleGame).Error; err != nil {
		tx.Rollback()
		logger.Error(ctx.Request().Context(), "error create moduleGames", zap.String("err", err.Error()))
		return xerrors.New(err.Error(), code.DbError)
	}
	// 提交事务
	if err = tx.Commit().Error; err != nil {
		logger.Error(ctx.Request().Context(), "transaction commit err", zap.String("err", err.Error()))
		return xerrors.New(err.Error(), code.DbError)
	}
	return nil
}

func (m *Module) ModuleEdit(ctx echo.Context, req *pb.ModuleEditReq) error {
	tx := m.db.WithContext(ctx.Request().Context()).Begin()
	// 查询出原记录
	module := &models.FpOpsTicketsModule{}
	err := tx.Model(&models.FpOpsTicketsModule{}).Where("id = ?", req.Id).First(module).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return xerrors.New(err.Error(), code.DbError)
	} else if errors.Is(err, gorm.ErrRecordNotFound) {
		return xerrors.New("模板不存在", code.InvalidParams)
	}

	operator := cast.ToString(ctx.Get(cst.AccountInfoCtx))
	current := utils.NowTimestamp()
	operateTime := utils.TimeFormat(int64(current))
	// 如果是禁启用
	if req.Enable > 0 {
		if uint8(req.Enable) == module.Enable {
			return xerrors.New("模板状态未改变", code.InvalidParams)
		}
		module.Enable = uint8(req.Enable)
		module.Operator = operator
		module.UpdatedTime = operateTime
		err = tx.Model(&models.FpOpsTicketsModule{}).Where("id=?", req.Id).
			Updates(module).Error
		if err != nil {
			tx.Rollback()
			logger.Error(ctx.Request().Context(), "error update module", zap.String("err", err.Error()))
			return xerrors.New(err.Error(), code.DbError)
		}
	} else {
		//若为编辑
		if req.ModuleName != "" {
			var has int64
			err = tx.Model(&models.FpOpsTicketsModule{}).Where("module_name = ? AND id != ?", req.ModuleName, module.ID).Count(&has).Error
			if err != nil {
				tx.Rollback()
				logger.Error(ctx.Request().Context(), "error querying existing module", zap.String("err", err.Error()))
				return xerrors.New(err.Error(), code.DbError)
			}
			if has > 0 {
				tx.Rollback()
				return xerrors.New("模板名重复", code.InvalidParams)
			}
		}
		// 更新模板数据
		module.ModuleName = req.ModuleName
		module.Content = req.Content
		module.CatID = req.CatId
		module.Operator = operator
		module.UpdatedTime = operateTime

		err = tx.Model(&models.FpOpsTicketsModule{}).Where("id=?", req.Id).Updates(module).Error
		if err != nil {
			tx.Rollback()
			logger.Error(ctx.Request().Context(), "error update module", zap.String("err", err.Error()))
			return xerrors.New(err.Error(), code.DbError)
		}
		// 如果有编辑关联游戏，则更新关联模板游戏表，将原记录一律删除，重新插入新数据
		// 查询出该模板关联的游戏列表
		gameProjects := []string{}
		err = tx.Model(&models.FpOpsTicketsModuleGame{}).Select("game_project").Where("module_id = ?", req.Id).Find(&gameProjects).Error
		if err != nil {
			tx.Rollback()
			logger.Error(ctx.Request().Context(), "error query relevant game projects", zap.String("err", err.Error()))
			return xerrors.New(err.Error(), code.DbError)
		}
		less, _, extra := utils.CompareArrays([]string{req.GameProject}, gameProjects)
		// 如果此次关联的游戏列表与原列表不相同，则需要更新关联模板游戏表
		if len(less) > 0 || len(extra) > 0 {
			err = tx.Model(&models.FpOpsTicketsModuleGame{}).Where("module_id = ?", req.Id).Delete(&models.FpOpsTicketsModuleGame{}).Error
			if err != nil {
				tx.Rollback()
				logger.Error(ctx.Request().Context(), "error delete module games", zap.String("err", err.Error()))
				return xerrors.New(err.Error(), code.DbError)
			}
			moduleGame := &models.FpOpsTicketsModuleGame{
				ModuleId:    req.Id,
				GameProject: req.GameProject,
				CreateTime:  current,
				UpdatedTime: operateTime,
			}
			//if err = tx.Model(&models.FpOpsTicketsModuleGame{}).CreateInBatches(moduleGames, len(moduleGames)).Error; err != nil {
			//	tx.Rollback()
			//	logger.Error(ctx.Request().Context(), "error create moduleGames in batch", zap.String("err", err.Error()))
			//	return xerrors.New(err.Error(), code.DbError)
			//}
			if err = tx.Model(&models.FpOpsTicketsModuleGame{}).Create(moduleGame).Error; err != nil {
				tx.Rollback()
				logger.Error(ctx.Request().Context(), "error create moduleGames", zap.String("err", err.Error()))
				return xerrors.New(err.Error(), code.DbError)
			}
		}
	}

	// 提交事务
	if err = tx.Commit().Error; err != nil {
		logger.Error(ctx.Request().Context(), "transaction commit err", zap.String("err", err.Error()))
		return xerrors.New(err.Error(), code.DbError)
	}
	return nil
}

func (m *Module) ModuleDel(ctx echo.Context, req *pb.ModuleDelReq) error {
	tx := m.db.WithContext(ctx.Request().Context()).Begin()
	err := tx.Model(&models.FpOpsTicketsModule{}).Where("id = ?", req.Id).Delete(&models.FpOpsTicketsModule{}).Error
	if err != nil {
		tx.Rollback()
		logger.Error(ctx.Request().Context(), "error delete module", zap.String("err", err.Error()))
		return xerrors.New(err.Error(), code.DbError)
	}
	if err = tx.Model(&models.FpOpsTicketsModuleGame{}).Where("module_id = ?", req.Id).Delete(&models.FpOpsTicketsModuleGame{}).Error; err != nil {
		tx.Rollback()
		logger.Error(ctx.Request().Context(), "error delete module game", zap.String("err", err.Error()))
		return xerrors.New(err.Error(), code.DbError)
	}
	// 提交事务
	if err = tx.Commit().Error; err != nil {
		logger.Error(ctx.Request().Context(), "transaction commit err", zap.String("err", err.Error()))
		return xerrors.New(err.Error(), code.DbError)
	}
	return nil
}

func (m *Module) ModuleList(ctx echo.Context, req *pb.ModuleListReq) ([]*pb.ModuleListResp_ModuleInfo, uint32, error) {
	dest := make([]*pb.ModuleListResp_ModuleInfo, 0)
	// 首先查出符合条件的模板id
	moduleList := []*models.FpOpsTicketsModuleList{}
	field := "DISTINCT m.id"
	subQuery := m.db.Table(fmt.Sprintf("%s AS m", models.GetFpOpsTicketsModuleTableName())).
		Joins(fmt.Sprintf("INNER JOIN %s AS g ON m.id = g.module_id", models.GetFpOpsTicketsModuleGameTableName()), "")
	if req.ModuleName != "" {
		subQuery = subQuery.Where("m.module_name LIKE ?", "%"+req.ModuleName+"%")
	}
	if req.Content != "" {
		subQuery = subQuery.Where("m.content LIKE ?", "%"+req.Content+"%")
	}
	if req.Enable > 0 {
		subQuery = subQuery.Where("m.enable = ?", req.Enable)
	}
	if req.GameProject != "" {
		subQuery = subQuery.Where("g.game_project = ?", req.GameProject)
	}
	if len(req.CatId) > 0 {
		subQuery = subQuery.Where("cat_id IN (?)", req.CatId)
	}
	moduleIds := []uint64{}
	if err := subQuery.Select(field).Find(&moduleIds).Error; err != nil {
		logger.Error(ctx.Request().Context(), "get module id that qualifies err", zap.String("err", err.Error()))
		return nil, 0, err
	}
	count := len(moduleIds)
	// 然后根据这些模板id获取最终的数据
	fields := "m.id, m.module_name, m.content,m.cat_id, m.operator, m.enable, m.update_time, g.game_project AS game_project"
	query := m.db.Table(fmt.Sprintf("%s AS m", models.GetFpOpsTicketsModuleTableName())).
		Joins(fmt.Sprintf("INNER JOIN %s AS g on m.id = g.module_id", models.GetFpOpsTicketsModuleGameTableName()), "")
	query.Select(fields).Where("m.id IN (?)", moduleIds)

	// 仅查出第一个游戏行即可
	if err := query.Group("m.id").Order("m.id DESC").Scopes(database.Paginate(&req.Page, &req.PageSize)).
		Find(&moduleList).Error; err != nil {
		logger.Error(ctx.Request().Context(), "get module list err", zap.String("err", err.Error()))
		return nil, 0, xerrors.New(err.Error(), code.DbError)
	}
	for _, v := range moduleList {
		// 获取category
		categoryPath, err := m.getCategoryPath(ctx.Request().Context(), v.CatID)
		if err != nil {
			logger.Error(ctx.Request().Context(), "get NewModuleCat module detail err", zap.String("err", err.Error()))
			return nil, 0, xerrors.New(err.Error(), code.DbError)
		}
		dest = append(dest, &pb.ModuleListResp_ModuleInfo{
			Id:          v.ID,
			ModuleName:  v.ModuleName,
			Content:     v.Content,
			GameProject: v.GameProject,
			Operator:    v.Operator,
			Enable:      uint32(v.Enable),
			UpdateTime:  v.UpdatedTime,
			Category:    categoryPath,
			CatId:       v.CatID,
		})
	}

	return dest, cast.ToUint32(count), nil
}

// getCategoryPath 获取分类的完整路径
func (m *Module) getCategoryPath(ctx context.Context, catId uint32) (string, error) {
	var categories []models.FpOpsModuleCategory

	// 先获取当前分类
	var currentCat models.FpOpsModuleCategory
	if err := m.db.Where("cat_id = ?", catId).First(&currentCat).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return "", nil
		}
		return "", err
	}

	// 存储所有相关分类
	categories = append(categories, currentCat)
	parentId := currentCat.ParentID

	// 递归获取父级分类
	for parentId != 0 {
		var parentCat models.FpOpsModuleCategory
		if err := m.db.Where("cat_id = ?", parentId).First(&parentCat).Error; err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				break
			}
			return "", err
		}
		categories = append(categories, parentCat)
		parentId = parentCat.ParentID
	}

	// 反转数组，从一级分类开始
	for i := 0; i < len(categories)/2; i++ {
		categories[i], categories[len(categories)-1-i] = categories[len(categories)-1-i], categories[i]
	}

	// 构建分类路径字符串
	var path []string
	for _, cat := range categories {
		path = append(path, cat.Category)
	}

	return strings.Join(path, "-"), nil
}

func (m *Module) ModuleOpts(ctx echo.Context, req *pb.ModuleOptsReq) ([]*pb.ModuleOptsResp, error) {
	var resp = []*pb.ModuleOptsResp{}
	var moduleList []*models.FpOpsTicketsModule

	query := m.db.Table("fp_ops_tickets_module m").
		Select("DISTINCT m.content, m.module_name").
		Joins("Left JOIN fp_ops_tickets_module_game g ON m.id = g.module_id").
		Where("m.enable = ?", code.ModuleEnable).
		Where("g.game_project = ?", req.Project).
		Order("m.id DESC")

	if err := query.Find(&moduleList).Error; err != nil {
		logger.Error(ctx.Request().Context(), "get module opts list err",
			zap.String("project", req.Project),
			zap.Error(err))
		return nil, xerrors.New(err.Error(), code.DbError)
	}

	// 构建返回数据
	for _, module := range moduleList {
		resp = append(resp, &pb.ModuleOptsResp{
			Label: module.ModuleName,
			Value: module.Content,
		})
	}

	return resp, nil
}
