package persistence

import (
	"context"
	"errors"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"ops-ticket-api/internal/code"
	"ops-ticket-api/internal/framework/database"
	"ops-ticket-api/internal/framework/xerrors"
	"ops-ticket-api/models"
	"ops-ticket-api/proto/pb"
	"time"
)

// UserRepo 用户repo
type UserRepo struct {
	db *gorm.DB
}

// NewUserRepo init
func NewUserRepo() *UserRepo {
	return &UserRepo{
		db: database.Db(),
	}
}

func (repo *UserRepo) GetUserInfo(ctx context.Context, account string) (*models.FpOpsUsers, error) {
	accountInfo := &models.FpOpsUsers{}
	err := repo.db.WithContext(ctx).Model(&models.FpOpsUsers{}).Where("account = ?", account).Take(accountInfo).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, err
	}
	return accountInfo, nil
}

// FirstOrCreate 添加用户
func (repo *UserRepo) FirstOrCreate(ctx context.Context, account, nickname string, isAdmin bool) error {
	_ = isAdmin
	// 初始默认为离线状态(离线状态不参与分单)
	accountObj := models.FpOpsUsers{
		Nickname: nickname,
		Account:  account,
		//Admin:       isAdmin,
		IsLogin:     uint32(pb.UserLoginStatus_UserLoginStatusNo),
		LastLoginAt: time.Now().UTC().Unix(),
	}
	if err := repo.db.WithContext(ctx).Clauses(clause.OnConflict{
		Columns: []clause.Column{{Name: "account"}},
		DoUpdates: clause.Assignments(map[string]interface{}{
			"nickname": nickname,
			//"admin":         cast.ToInt(isAdmin),
			"last_login_at": time.Now().UTC().Unix(),
		}),
	}).Create(&accountObj).Error; err != nil {
		logger.Error(ctx, "add account record err",
			logger.String("account", account),
			logger.String("nickname", nickname),
			logger.String("err", err.Error()),
		)
		return xerrors.New(err.Error(), code.DbError)
	}
	return nil
}

// IsAcceptorEdit 修改客服在线状态
func (repo *UserRepo) IsAcceptorEdit(ctx context.Context, account string, status uint32) error {
	now := time.Now().UTC()
	err := repo.db.WithContext(ctx).Model(&models.FpOpsUsers{}).
		Where("account = ?", account).
		Updates(map[string]interface{}{
			"is_login":   status,
			"updated_at": now,
		}).Error
	if err != nil {
		return err
	}
	return nil
}

func (repo *UserRepo) GetAcceptorStatus(ctx context.Context, account string) (*pb.LoginStatus, error) {
	var status *pb.LoginStatus
	err := repo.db.WithContext(ctx).Model(&models.FpOpsUsers{}).
		Where("account = ?", account).
		Pluck("is_login", &status).Error
	if err != nil {
		return nil, err
	}
	return status, nil
}

func (repo *UserRepo) List(ctx context.Context) ([]*pb.UserListResp, error) {
	dest := make([]*pb.UserListResp, 0)
	field := "account"
	if err := repo.db.WithContext(ctx).Model(&models.FpOpsUsers{}).Select(field).Find(&dest).Error; err != nil {
		logger.Error(ctx, "get team opts err", zap.String("err", err.Error()))
		return nil, xerrors.New(err.Error(), code.DbError)
	}
	return dest, nil
}
