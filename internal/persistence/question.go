package persistence

import (
	"errors"
	"fmt"
	"github.com/labstack/echo/v4"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"ops-ticket-api/internal/code"
	"ops-ticket-api/internal/framework/database"
	"ops-ticket-api/internal/framework/xerrors"
	"ops-ticket-api/models"
	"ops-ticket-api/proto/pb"
	"strings"
	"time"
)

// QuestionTicket 工单知识库
type QuestionTicket struct {
	db *gorm.DB
}

// NewQuestionTicket init
func NewQuestionTicket() *QuestionTicket {
	return &QuestionTicket{
		db: database.Db(),
	}
}

func (q *QuestionTicket) GetQuestionTicket(tx *gorm.DB, questionId int64) (*models.FpOpsTicketsQuestion, error) {
	if tx == nil {
		tx = q.db
	}
	var question = &models.FpOpsTicketsQuestion{}
	err := tx.Where("question_id= ?", questionId).First(question).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, xerrors.New("获取工单知识库失败", code.DbError)
	}
	return question, nil
}

func (q *QuestionTicket) CheckQuestionExist(tx *gorm.DB, m interface{}, qid uint64, where map[string]interface{}) (bool, error) {
	// check question has exist
	var count int64
	sub := tx.Model(m).Where(where)
	if qid > 0 {
		sub = sub.Where("question_id != ?", qid)
	}
	err := sub.Count(&count).Error
	if err != nil {
		return false, err
	}
	return count > 0, nil
}

func (q *QuestionTicket) SaveQuestionDetail(tx *gorm.DB, question *models.FpOpsTicketsQuestion) error {
	var boo bool
	boo, err := q.CheckQuestionExist(tx, &models.FpOpsTicketsQuestion{}, question.QuestionID, map[string]interface{}{
		"project":               question.Project,
		"question_content_hash": question.QuestionContentHash})
	if err != nil {
		return err
	}
	if boo == true {
		return xerrors.New("同个游戏下语料不能重复", code.RepeatData)
	}
	if question.QuestionID > 0 {
		err = tx.Where("question_id = ?", question.QuestionID).Updates(question).Error
		if err != nil {
			return xerrors.New(code.StatusText(code.DbError), code.DbError)
		}
	} else {
		err = tx.Create(question).Error
		if err != nil {
			return xerrors.New(code.StatusText(code.DbError), code.DbError)
		}
	}

	return nil
}

// GetAllQuestionLang 获取游戏下所有语种
func (q *QuestionTicket) GetAllQuestionLang(gameProject string) ([]string, error) {
	var list []*models.FpOpsTicketsQuestion
	var allLang []string
	err := q.db.Select("lang").Where("project = ?", gameProject).Group("lang").Find(&list).Error
	if err != nil {
		return allLang, err
	}
	for _, row := range list {
		allLang = append(allLang, row.Lang)
	}
	return allLang, nil
}

func (q *QuestionTicket) GetModifiedQst(gameProject, l string, startTime uint64) ([]*models.FpOpsTicketsQuestion, error) {
	var list []*models.FpOpsTicketsQuestion
	// 查找时段内编辑及新增的
	err := q.db.Model(&models.FpOpsTicketsQuestion{}).Where("project = ? AND lang = ? AND updated_at > ?", gameProject, l, startTime).
		Select(strings.Join([]string{"question_id", "project", "cat_id", "lang", "question_content", "updated_at", "answer_rich_text"}, ",")).
		Order("updated_at DESC").Find(&list).Error
	return list, err
}

func (q *QuestionTicket) QuestionList(ctx echo.Context, req *pb.QuestionListReq, all bool) ([]*pb.QuestionListResp_QuestionRecord, uint32, error) {
	dest := make([]*pb.QuestionListResp_QuestionRecord, 0)
	// 首先查出符合条件的数据条数
	questionList := []*models.FpOpsTicketsQuestion{}
	subQuery := q.db.Model(&models.FpOpsTicketsQuestion{})
	if len(req.Project) > 0 {
		subQuery = subQuery.Where("project IN (?)", req.Project)
	}
	if len(req.UpdateDate) > 0 {
		subQuery = subQuery.Where("FROM_UNIXTIME(updated_at) BETWEEN ? AND ?", req.UpdateDate[0], req.UpdateDate[1])
	}
	if len(req.CatId) > 0 {
		subQuery = subQuery.Where("cat_id IN (?)", req.CatId)
	}
	if req.QuestionId > 0 {
		subQuery = subQuery.Where("question_id = ?", req.QuestionId)
	}
	if len(req.Lang) > 0 {
		subQuery = subQuery.Where("lang IN (?)", req.Lang)
	}
	if req.QuestionContent != "" {
		subQuery = subQuery.Where("question_content like ?", "%"+req.QuestionContent+"%")
	}
	var total int64
	query := subQuery
	if err := subQuery.Count(&total).Error; err != nil {
		logger.Error(ctx.Request().Context(), "get questionList err", zap.String("err", err.Error()))
		return nil, 0, err
	}
	// 导出数据不分页
	if all {
		if err := query.Order("updated_at DESC").Find(&questionList).Error; err != nil {
			logger.Error(ctx.Request().Context(), "get question list err", zap.String("err", err.Error()))
			return nil, 0, xerrors.New(err.Error(), code.DbError)
		}
	} else {
		if err := query.Order("updated_at DESC").Scopes(database.Paginate(&req.Page, &req.PageSize)).Find(&questionList).Error; err != nil {
			logger.Error(ctx.Request().Context(), "get question list err", zap.String("err", err.Error()))
			return nil, 0, xerrors.New(err.Error(), code.DbError)
		}
	}
	for _, v := range questionList {
		catInfo, err := NewCat().GetDetailById(ctx, v.CatID)
		if err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				logger.Errorf(ctx.Request().Context(), "GetDetailById err, err:%+v", err)
				return nil, 0, xerrors.New(code.StatusText(code.NotFound), code.NotFound)
			}
			logger.Error(ctx.Request().Context(), "get Cat config detail err", zap.String("err", err.Error()))
			return nil, 0, xerrors.New(err.Error(), code.DbError)
		}
		loc, _ := time.LoadLocation("Asia/Shanghai")                                         // 加载东8区时区
		updateTime := time.Unix(int64(v.UpdatedAt), 0).In(loc).Format("2006-01-02 15:04:05") // 转换为东8区时间
		dest = append(dest, &pb.QuestionListResp_QuestionRecord{
			QuestionId:      int64(v.QuestionID),
			Project:         v.Project,
			QuestionContent: v.QuestionContent,
			Lang:            v.Lang,
			UpdateDate:      updateTime,
			Operator:        v.Creator,
			Category:        catInfo.Category,
			CatId:           v.CatID,
			AnswerRichText:  v.AnswerRichText,
		})
	}
	return dest, uint32(total), nil
}

func (q *QuestionTicket) DelQuestion(tx *gorm.DB, id int64) (err error) {
	if id == 0 {
		return fmt.Errorf("delete question id empty")
	}
	return tx.Where("question_id = ?", id).Delete(&models.FpOpsTicketsQuestion{}).Error
}

func (q *QuestionTicket) GetQuestionById(id int64) (*models.FpOpsTicketsQuestion, error) {
	var detail = &models.FpOpsTicketsQuestion{}
	err := q.db.Model(detail).Where("question_id = ?", id).Find(detail).Error
	if err != nil {
		return nil, err
	}
	return detail, nil
}

func (q *QuestionTicket) GetQuestionByProjectAndQst(tx *gorm.DB, project, qstHash string) (*models.FpOpsTicketsQuestion, error) {
	if tx == nil {
		tx = q.db
	}
	var detail = &models.FpOpsTicketsQuestion{}
	err := tx.Model(detail).Where("project = ? and question_content_hash = ?", project, qstHash).Find(detail).Error
	if err != nil {
		return nil, err
	}
	return detail, nil
}
