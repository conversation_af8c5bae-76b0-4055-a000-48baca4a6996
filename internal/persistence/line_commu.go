package persistence

import (
	"errors"
	"github.com/labstack/echo/v4"
	"github.com/spf13/cast"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"ops-ticket-api/internal/code"
	"ops-ticket-api/internal/cst"
	"ops-ticket-api/internal/framework/database"
	"ops-ticket-api/internal/framework/xerrors"
	"ops-ticket-api/models"
	"ops-ticket-api/proto/pb"
	"strings"
	"time"
)

// LineCommu line沟通记录
type LineCommu struct {
	db *gorm.DB
}

// NewLineCommu init
func NewLineCommu() *LineCommu {
	return &LineCommu{
		db: database.Db(),
	}
}

func (lc *LineCommu) LineCommuSave(ctx echo.Context, req *pb.LineCommuRecordAddReq) error {
	operator := cast.ToString(ctx.Get(cst.AccountInfoCtx))
	record := &models.FpLineCommuRecord{
		Project:      req.Project,
		CommuDate:    req.CommuDate,
		LineUserId:   req.LineUserId,
		UID:          req.Uid,
		Sid:          req.Sid,
		NickName:     req.NickName,
		PayAll:       req.PayAll,
		Question:     req.Question,
		CatID:        req.CatId,
		CatType:      uint16(req.CatType),
		HandleStatus: uint8(req.HandleStatus),
		Remark:       req.Remark,
		RelevantMsg:  req.MsgIds,
		Operator:     operator,
		CreatedAt:    time.Now().UTC(),
		UpdatedAt:    time.Now().UTC(),
	}
	if err := lc.db.Table(models.GetFpLineCommuRecordTableName()).Create(record).Error; err != nil {
		logger.Error(ctx.Request().Context(), "error create line commu", zap.String("err", err.Error()))
		return xerrors.New(err.Error(), code.DbError)
	}
	return nil
}

func (lc *LineCommu) LineCommuEdit(ctx echo.Context, req *pb.LineCommuRecordEditReq) error {
	// 查询出原记录
	record := &models.FpLineCommuRecord{}
	err := lc.db.Table(models.GetFpLineCommuRecordTableName()).Where("id = ?", req.Id).First(record).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return xerrors.New(err.Error(), code.DbError)
	} else if errors.Is(err, gorm.ErrRecordNotFound) {
		return xerrors.New("沟通记录不存在", code.InvalidParams)
	}
	// 更新沟通记录数据
	record.Operator = cast.ToString(ctx.Get(cst.AccountInfoCtx))
	record.CommuDate = req.CommuDate
	record.Question = req.Question
	record.CatID = req.CatId
	record.HandleStatus = uint8(req.HandleStatus)
	record.Remark = req.Remark
	record.UpdatedAt = time.Now().UTC()
	err = lc.db.Table(models.GetFpLineCommuRecordTableName()).Where("id=?", req.Id).Updates(record).Error
	if err != nil {
		logger.Error(ctx.Request().Context(), "error update line commu", zap.String("err", err.Error()))
		return xerrors.New(err.Error(), code.DbError)
	}
	return nil
}

func (lc *LineCommu) LineCommuList(ctx echo.Context, req *pb.LineCommuRecordListReq, all bool) ([]*pb.LineCommuRecordListResp_LineCommuRecord, uint32, error) {
	dest := make([]*pb.LineCommuRecordListResp_LineCommuRecord, 0)
	// 首先查出符合条件的数据条数
	commuList := []*models.FpLineCommuRecord{}
	subQuery := lc.db.Table(models.GetFpLineCommuRecordTableName()+" as c").Where("c.cat_type = ?", req.CatType)
	if len(req.Project) > 0 {
		projectSli := strings.Split(req.Project, ",")
		subQuery = subQuery.Where("c.project IN (?)", projectSli)
	}
	if len(req.CommuDate) > 0 {
		subQuery = subQuery.Where("c.commu_date BETWEEN ? AND ?", req.CommuDate[0], req.CommuDate[1])
	}
	if len(req.Operator) > 0 {
		subQuery = subQuery.Where("c.operator IN (?)", req.Operator)
	}
	if req.Uid > 0 {
		subQuery = subQuery.Where("c.uid = ?", req.Uid)
	}
	if len(req.HandleStatus) > 0 {
		subQuery = subQuery.Where("c.handle_status IN (?)", req.HandleStatus)
	}
	if req.CatId > 0 {
		subQuery = subQuery.Where("c.cat_id = ?", req.CatId)
	}
	// 连表查标签
	if len(req.Label) > 0 {
		subQuery = subQuery.Joins("JOIN fp_line_player_tag t ON c.line_user_id = t.line_user_id").
			Where("t.tag_id IN (?)", req.Label).
			Group("c.id")
	}
	var total int64
	query := subQuery
	if err := subQuery.Count(&total).Error; err != nil {
		logger.Error(ctx.Request().Context(), "get line commu count that qualifies err", zap.String("err", err.Error()))
		return nil, 0, err
	}
	// 导出数据不分页
	if all {
		if err := query.Order("c.commu_date DESC").Preload("Tags").Find(&commuList).Error; err != nil {
			logger.Error(ctx.Request().Context(), "get line commu list err", zap.String("err", err.Error()))
			return nil, 0, xerrors.New(err.Error(), code.DbError)
		}
	} else {
		if err := query.Order("c.commu_date DESC").Scopes(database.Paginate(&req.Page, &req.PageSize)).Preload("Tags").Find(&commuList).Error; err != nil {
			logger.Error(ctx.Request().Context(), "get line commu list err", zap.String("err", err.Error()))
			return nil, 0, xerrors.New(err.Error(), code.DbError)
		}
	}
	for _, v := range commuList {
		// 获取相关对话
		msgIds := strings.Split(v.RelevantMsg, ",")
		msgList, _ := lc.GetMsgContentByMsgIds(msgIds)
		dialogueList := make([]*pb.LineCommuRecordListResp_DialogueItem, len(msgList))
		for i := range msgList {
			msg := msgList[i]
			if msg.FromUserID == msg.LineUserID {
				dialogueList[i] = &pb.LineCommuRecordListResp_DialogueItem{
					Role:    "player",
					Content: msg.Content,
				}
			}
			if msg.FromUserID == msg.BotID {
				dialogueList[i] = &pb.LineCommuRecordListResp_DialogueItem{
					Role:    "service",
					Content: msg.Content,
				}
			}
		}
		//查询维护专员
		maintainConfig, err := NewLineMaintainConfig().GetMaintainConfigDetail(ctx.Request().Context(), v.LineUserId, v.Project)
		if err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				logger.Infof(ctx.Request().Context(), "get line maintain config detail err", zap.String("err", err.Error()))
			} else {
				logger.Error(ctx.Request().Context(), "get line maintain config detail err", zap.String("err", err.Error()))
				return nil, 0, xerrors.New(err.Error(), code.DbError)
			}
		}
		//查找问题分类名
		catInfo, err := NewChannelCat().Detail(ctx, v.CatID)
		if err != nil {
			logger.Error(ctx.Request().Context(), "get channel cat detail err", zap.String("err", err.Error()))
			return nil, 0, xerrors.New(err.Error(), code.DbError)
		}
		record := []*pb.LineCommuRecordListResp_NewLabel{}
		// 查询标签信息
		labels := []uint32{}
		for _, tag := range v.Tags {
			labels = append(labels, cast.ToUint32(tag.TagId))
		}
		tagSlice := NewTags().GetTagsSlice(ctx.Request().Context(), labels)
		for tagId, tagName := range tagSlice {
			record = append(record, &pb.LineCommuRecordListResp_NewLabel{
				TagId:   tagId,
				TagName: strings.Join(tagName, "-"),
			})
		}
		dest = append(dest, &pb.LineCommuRecordListResp_LineCommuRecord{
			Id:           v.ID,
			CommuDate:    v.CommuDate,
			Project:      v.Project,
			Uid:          v.UID,
			Sid:          v.Sid,
			NickName:     v.NickName,
			PayAll:       v.PayAll,
			Question:     v.Question,
			HandleStatus: int32(v.HandleStatus),
			Remark:       v.Remark,
			Dialogue:     dialogueList,
			Operator:     v.Operator,
			Maintainer:   maintainConfig.Maintainer,
			Category:     catInfo.Category,
			CatId:        int32(v.CatID),
			Label:        record,
		})
	}
	return dest, uint32(total), nil
}

func (lc *LineCommu) GetMsgContentByMsgIds(msgIds []string) ([]*models.FpLineCommu, error) {
	dest := []*models.FpLineCommu{}
	if err := lc.db.Model(&models.FpLineCommu{}).Where("msg_id IN (?)", msgIds).Find(&dest).Error; err != nil {
		return dest, err
	}
	return dest, nil
}
