// Copyright 2024 funplus Authors. All Rights Reserved.
// @Description: discord commu
// @Author: jun.qiu
// @Date: 2024/08/19 11:52 AM

package persistence

import (
	"errors"
	"fmt"
	"github.com/labstack/echo/v4"
	"github.com/spf13/cast"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"ops-ticket-api/internal/code"
	"ops-ticket-api/internal/cst"
	"ops-ticket-api/internal/framework/database"
	"ops-ticket-api/internal/framework/xerrors"
	"ops-ticket-api/models"
	"ops-ticket-api/proto/pb"
	"strings"
	"time"
)

// DiscordNewCommu discord沟通记录
type DiscordNewCommu struct {
	db *gorm.DB
}

// NewDiscordCommu init
func NewDiscordNewCommu() *DiscordNewCommu {
	return &DiscordNewCommu{
		db: database.Db(),
	}
}

func (dc *DiscordNewCommu) DiscordCommuSave(ctx echo.Context, req *pb.DiscordNewCommuRecordAddReq) error {
	operator := cast.ToString(ctx.Get(cst.AccountInfoCtx))
	record := &models.FpDscCommuRecord{
		Project:      req.Project,
		CommuDate:    req.CommuDate,
		DscUserID:    req.DscUserId,
		UID:          req.Uid,
		Sid:          req.Sid,
		NickName:     req.NickName,
		PayAll:       req.PayAll,
		Question:     req.Question,
		CatID:        req.CatId,
		CatType:      uint16(req.CatType),
		HandleStatus: uint8(req.HandleStatus),
		Remark:       req.Remark,
		RelevantMsg:  req.MsgIds,
		Operator:     operator,
		CreatedAt:    time.Now().UTC(),
		UpdatedAt:    time.Now().UTC(),
	}
	if err := dc.db.Table(models.GetFpDscCommuRecordTableName()).Create(record).Error; err != nil {
		logger.Error(ctx.Request().Context(), "error create dsc commu", zap.String("err", err.Error()))
		return xerrors.New(err.Error(), code.DbError)
	}
	return nil
}

func (dc *DiscordNewCommu) DiscordCommuEdit(ctx echo.Context, req *pb.DiscordNewCommuRecordEditReq) error {
	// 查询出原记录
	record := &models.FpDscCommuRecord{}
	err := dc.db.Table(models.GetFpDscCommuRecordTableName()).Where("id = ?", req.Id).First(record).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return xerrors.New(err.Error(), code.DbError)
	} else if errors.Is(err, gorm.ErrRecordNotFound) {
		return xerrors.New("沟通记录不存在", code.InvalidParams)
	}
	// 更新沟通记录数据
	record.Operator = cast.ToString(ctx.Get(cst.AccountInfoCtx))
	record.CommuDate = req.CommuDate
	record.Question = req.Question
	record.CatID = req.CatId
	record.HandleStatus = uint8(req.HandleStatus)
	record.Remark = req.Remark
	record.UpdatedAt = time.Now().UTC()
	err = dc.db.Table(models.GetFpDscCommuRecordTableName()).Where("id=?", req.Id).Updates(record).Error
	if err != nil {
		logger.Error(ctx.Request().Context(), "error update dsc commu", zap.String("err", err.Error()))
		return xerrors.New(err.Error(), code.DbError)
	}
	return nil
}

func (dc *DiscordNewCommu) DiscordCommuList(ctx echo.Context, req *pb.DiscordNewCommuRecordListReq, all bool) ([]*pb.DiscordNewCommuRecordListResp_DiscordCommuRecord, uint32, error) {
	dest := make([]*pb.DiscordNewCommuRecordListResp_DiscordCommuRecord, 0)
	// 首先查出符合条件的数据条数
	commuList := []*models.FpDscCommuRecord{}
	subQuery := dc.db.Table(models.GetFpDscCommuRecordTableName()).Where("cat_type = ?", req.CatType)
	if len(req.Project) > 0 {
		projectSli := strings.Split(req.Project, ",")
		subQuery = subQuery.Where("project IN (?)", projectSli)
	}
	if len(req.CommuDate) > 0 {
		subQuery = subQuery.Where("commu_date BETWEEN ? AND ?", req.CommuDate[0], req.CommuDate[1])
	}
	if len(req.Operator) > 0 {
		subQuery = subQuery.Where("operator IN (?)", req.Operator)
	}
	if req.Uid > 0 {
		subQuery = subQuery.Where("uid = ?", req.Uid)
	}
	if len(req.HandleStatus) > 0 {
		subQuery = subQuery.Where("handle_status IN (?)", req.HandleStatus)
	}
	if req.CatId > 0 {
		subQuery = subQuery.Where("cat_id = ?", req.CatId)
	}
	var total int64
	query := subQuery
	if err := subQuery.Count(&total).Error; err != nil {
		logger.Error(ctx.Request().Context(), "get dsc commu id that qualifies err", zap.String("err", err.Error()))
		return nil, 0, err
	}
	// 导出数据不分页
	if all {
		if err := query.Order("commu_date DESC").Find(&commuList).Error; err != nil {
			logger.Error(ctx.Request().Context(), "get dsc commu list err", zap.String("err", err.Error()))
			return nil, 0, xerrors.New(err.Error(), code.DbError)
		}
	} else {
		if err := query.Order("commu_date DESC").Scopes(database.Paginate(&req.Page, &req.PageSize)).Find(&commuList).Error; err != nil {
			logger.Error(ctx.Request().Context(), "get dsc commu list err", zap.String("err", err.Error()))
			return nil, 0, xerrors.New(err.Error(), code.DbError)
		}
	}
	for _, v := range commuList {
		// 获取相关对话
		msgIds := strings.Split(v.RelevantMsg, ",")
		msgList, _ := dc.GetMsgContentByMsgIds(msgIds)
		dialogueList := make([]*pb.DiscordNewCommuRecordListResp_DialogueItem, len(msgList))
		for i := range msgList {
			msg := msgList[i]
			if msg.FromUserID == msg.DscUserID {
				dialogueList[i] = &pb.DiscordNewCommuRecordListResp_DialogueItem{
					Role:    "player",
					Content: msg.Content,
				}
			}
			if msg.FromUserID == msg.BotID {
				dialogueList[i] = &pb.DiscordNewCommuRecordListResp_DialogueItem{
					Role:    "service",
					Content: msg.Content,
				}
			}
		}
		//查询维护专员
		maintainConfig, err := NewMaintainConfig().GetMaintainConfigDetail(ctx.Request().Context(), v.DscUserID, v.Project)
		if err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				logger.Infof(ctx.Request().Context(), "get maintain config detail list err", zap.String("err", err.Error()))
			} else {
				logger.Error(ctx.Request().Context(), "get maintain config detail list err", zap.String("err", err.Error()))
				return nil, 0, xerrors.New(err.Error(), code.DbError)
			}
		}
		detail, err := NewChannelCat().Detail(ctx, v.CatID)
		if err != nil {
			logger.Error(ctx.Request().Context(), "get NewChannelCat config detail err", zap.String("err", err.Error()))
			return nil, 0, xerrors.New(err.Error(), code.DbError)
		}
		dest = append(dest, &pb.DiscordNewCommuRecordListResp_DiscordCommuRecord{
			Id:           v.ID,
			CommuDate:    v.CommuDate,
			Project:      v.Project,
			Uid:          v.UID,
			Sid:          v.Sid,
			NickName:     v.NickName,
			PayAll:       v.PayAll,
			Question:     v.Question,
			QuestionType: int32(v.QuestionType), //弃用
			HandleStatus: int32(v.HandleStatus),
			Remark:       v.Remark,
			Dialogue:     dialogueList,
			Operator:     v.Operator,
			Maintainer:   maintainConfig.Maintainer,
			CatId:        int32(v.CatID),
			Category:     detail.Category,
		})
	}
	return dest, uint32(total), nil
}

func (dc *DiscordNewCommu) GetMsgContentByMsgIds(msgIds []string) ([]*models.FpDscDmCommu, error) {
	dest := []*models.FpDscDmCommu{}
	if err := dc.db.Model(&models.FpDscDmCommu{}).Where("msg_id IN (?)", msgIds).Find(&dest).Error; err != nil {
		return dest, err
	}
	return dest, nil
}

func (dc *DiscordNewCommu) GetChannelIdsByProjectUid(project string, uid int64) ([]*pb.DscUserChannelDf, error) {
	var channels []*pb.DscUserChannelDf
	err := dc.db.Model(&models.FpDscUser{}).Table(fmt.Sprintf("%s as fdu", models.FpDscUser{}.TableName())).
		Joins(fmt.Sprintf("INNER JOIN %s fpmc ON fdu.dsc_user_id = fpmc.dsc_user_id AND fdu.project=fpmc.game_project", models.GetFpDscPlayerMaintainConfigTableName())).
		Select("distinct(fdu.priv_channel_id) as priv_channel_id", "fdu.app_id", "fdu.dsc_user_id").
		Where("fpmc.game_project = ? AND fpmc.uid = ? AND fdu.is_deleted = ? AND fdu.priv_channel_id != ''", project, uid, code.DscNoDelete).
		Find(&channels).Error
	if err != nil {
		return channels, err
	}
	return channels, nil
}
