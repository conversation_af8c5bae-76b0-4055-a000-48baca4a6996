package persistence

import (
	"context"
	"github.com/labstack/echo/v4"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"gorm.io/gorm"
	"ops-ticket-api/models"
	"ops-ticket-api/proto/pb"
	"time"
)

func (repo *examineOrders) SetOrderNoticeRead(ctx context.Context, examineGroup pb.ExamineTaskGroupDf, detailId uint64, toUser string) error {
	err := repo.db.WithContext(ctx).Model(&models.FpExamineNoticeLog{}).
		Where("examine_type = ? AND detail_id = ? AND to_user = ?", examineGroup.String(), detailId, toUser).
		Updates(map[string]interface{}{
			"notice_status": int(pb.ExamineNoticeStateDf_ExamineNoticeStateDfRead),
			"read_at":       time.Now(),
			"updated_at":    time.Now(),
		}).Error
	if err != nil {
		logger.Erro<PERSON>(ctx, "SetOrderNoticeRead return err. detailId:%d. examineGroup:%v. toUser:%s. err:%v", detailId, examineGroup, toUser, err)
	}
	return err
}

func (repo *examineOrders) HasUnreadNotice(ctx echo.Context, account string) (bool, error) {
	var count int64
	err := repo.db.WithContext(ctx.Request().Context()).Model(&models.FpExamineNoticeLog{}).
		Where("to_user = ? AND notice_status = ?", account, pb.ExamineNoticeStateDf_ExamineNoticeStateDfUnread).
		Count(&count).Error
	return count > 0, err
}

func (repo *examineOrders) GetOrderNoticeList(ctx echo.Context, account string, page, pageSize *uint32) (int64, []*models.FpExamineNoticeLog, error) {
	var dest []*models.FpExamineNoticeLog
	count, err := GetListWithPage(ctx,
		repo.db.WithContext(ctx.Request().Context()).Model(&models.FpExamineNoticeLog{}),
		&dest, page, pageSize,
		func(tx *gorm.DB) (*gorm.DB, string, error) {
			tx = tx.Model(&models.FpExamineNoticeLog{}).Select("*").Where("to_user = ?", account)
			return tx, "id desc", nil
		})
	if err != nil {
		return 0, nil, err
	}
	return count, dest, err
}

func (repo *examineOrders) SaveNoticeInfo(ctx context.Context, msgGroup pb.ExamineNoticeMsgGroupDf, examineType pb.ExamineTaskGroupDf,
	detailId, taskId, tplId uint64, toUser []string, operator string) error {
	if len(toUser) == 0 {
		return nil
	}

	var notice []*models.FpExamineNoticeLog
	for _, user := range toUser {
		notice = append(notice, &models.FpExamineNoticeLog{
			MsgGroup:     msgGroup.String(),
			ExamineType:  examineType.String(),
			DetailID:     int64(detailId),
			TaskID:       taskId,
			TplID:        tplId,
			ToUser:       user,
			NoticeStatus: int(pb.ExamineNoticeStateDf_ExamineNoticeStateDfUnread),
			CreatedAt:    time.Now(),
			ReadAt:       time.Time{},
			Operator:     operator,
			UpdatedAt:    time.Now(),
		})
	}
	return repo.db.WithContext(ctx).CreateInBatches(notice, len(notice)).Error
}
