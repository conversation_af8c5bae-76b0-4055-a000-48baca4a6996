// Copyright 2021 funplus Authors. All Rights Reserved.
// @Description:配置项
// @Author: Darcy
// @Date: 2021/11/2 2:40 PM

package persistence

import (
	"context"
	"sync"

	"ops-ticket-api/internal/code"
	"ops-ticket-api/internal/framework/cache/keys"
	"ops-ticket-api/internal/framework/cache/rds"
	"ops-ticket-api/internal/framework/database"
	"ops-ticket-api/internal/framework/xerrors"
	"ops-ticket-api/models"

	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// Addons 各配置项
type Addons struct {
	db  *gorm.DB
	mux sync.Mutex
}

// NewAddons init
func NewAddons() *Addons {
	return &Addons{
		db: database.Db(),
	}
}

// LanguageList 语言列表
func (dto *Addons) LanguageList(ctx context.Context) ([]*models.FpOpsLanguage, error) {
	dest := make([]*models.FpOpsLanguage, 0)
	if err := rds.RCli.QueryRow(ctx, keys.TicketAddonsLanguage, &dest, func(v interface{}) error {
		if err := dto.db.WithContext(ctx).Model(&models.FpOpsLanguage{}).Where("`enable`=?", code.StatusTrue).Find(v).Error; err != nil {
			logger.Error(ctx, "get language list err", zap.String("err", err.Error()))
			return xerrors.New(err.Error(), code.DbError)
		}
		return nil
	}); err != nil {
		return nil, err
	}
	return dest, nil
}
