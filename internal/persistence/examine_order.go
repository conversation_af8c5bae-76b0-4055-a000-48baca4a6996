package persistence

import (
	"context"
	"fmt"
	"github.com/labstack/echo/v4"
	"github.com/spf13/cast"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"gorm.io/gorm"
	"ops-ticket-api/internal/cst"
	"ops-ticket-api/internal/elasticsearch"
	"ops-ticket-api/internal/framework/database"
	"ops-ticket-api/models"
	"ops-ticket-api/proto/pb"
	"ops-ticket-api/utils"
	"time"
)

// examineOrders examine 质检单信息
type examineOrders struct {
	db *gorm.DB
}

// NewExamineOrder init
func NewExamineOrder() *examineOrders {
	return &examineOrders{
		db: database.Db(),
	}
}

func (repo *examineOrders) DscTaskOrderExists(ctx context.Context, taskId uint64, botId, dscUserId string) (bool, error) {
	var count int64
	err := repo.db.WithContext(ctx).Model(&models.FpExamineDiscordDetail{}).
		Where("task_id = ? AND bot_id = ? AND dsc_user_id = ?", taskId, botId, dscUserId).Count(&count).Error
	return count > 0, err
}
func (repo *examineOrders) CreateDscTaskOrder(ctx context.Context, detail *models.FpExamineDiscordDetail) error {
	// get dsc user name
	detail.DscUserName = NewDscInteractions().GetDscUserNickName(ctx, detail.DscUserID, detail.BotID)
	err := repo.db.WithContext(ctx).Create(detail).Error
	if err != nil {
		return err
	}

	if err := elasticsearch.DefaultExamineDscEsSvc.CreateExamineDscOrder(ctx, detail); err != nil {
		logger.Errorf(ctx, "CreateDscTaskOrder to Es return err. err:%v. order:%+v", err, detail)
	}
	return err
}

func (repo *examineOrders) SaveDscTaskOrder(ctx echo.Context, detail *models.FpExamineDiscordDetail) (err error) {
	tx := repo.db.WithContext(ctx.Request().Context()).Begin()
	defer func() {
		if err != nil {
			if _rollbackErr := tx.Rollback().Error; _rollbackErr != nil {
				logger.Errorf(ctx.Request().Context(), "%s rollback return err. err:%+v. rollbackErr:%v. detail:%+v", "SaveDscTaskOrder", err, _rollbackErr, detail)
			}
		}
	}()
	err = tx.Model(&detail).Where("id = ?", detail.ID).Save(detail).Error
	if err != nil {
		return err
	}
	err = tx.Model(&models.FpExamineFields{}).Where("examine_type = ? AND detail_id = ?", pb.ExamineTaskGroupDf_ExamineTaskGroupDfDiscord.String(), detail.ID).Delete(&models.FpExamineFields{}).Error
	if err != nil {
		return err
	}
	err = tx.Model(&models.FpExamineFields{}).CreateInBatches(detail.FpExamineFields, len(detail.FpExamineFields)).Error
	if err != nil {
		return err
	}

	//  add log
	opDetail := &models.FpExamineOperateLog{
		OperationGroup:  pb.OpGroup_OpGroupExamineDsc.String(),
		OperationAction: pb.OpAction_OpActionUpdate.String(),
		BaseID:          fmt.Sprintf("%d", detail.ID),
		UniqueID:        fmt.Sprintf("%d", detail.ID),
		GameProject:     detail.Project,
		BeforeDetail:    "{}",
		AfterDetail:     utils.ToJson(detail),
		CreateTime:      time.Now(),
		Operator:        cast.ToString(ctx.Get(cst.AccountInfoCtx)),
	}
	if err = tx.Model(opDetail).Create(opDetail).Error; err != nil {
		return err
	}

	err = tx.Commit().Error
	return err
}

func (repo *examineOrders) DscOrderDetail(ctx context.Context, id uint64) (*models.FpExamineDiscordDetail, error) {
	var detail = &models.FpExamineDiscordDetail{}
	err := repo.db.WithContext(ctx).Model(detail).Where("id = ?", id).Last(&detail).Error
	if err != nil {
		return detail, err
	}
	if err := repo.db.WithContext(ctx).Model(&models.FpExamineFields{}).Where("detail_id = ? AND examine_type = ?", detail.ID, pb.ExamineTaskGroupDf_ExamineTaskGroupDfDiscord.String()).Find(&detail.FpExamineFields).Error; err != nil {
		return detail, err
	}
	return detail, nil
}

// DscOrderCanEdit 只允许：一次质检 + 一次修改
func (repo *examineOrders) DscOrderCanEdit(ctx context.Context, id uint64) (bool, error) {
	//OperationGroup:  pb.OpGroup_OpGroupExamineDsc.String(),
	//	OperationAction: pb.OpAction_OpActionUpdate.String(),
	//		BaseID:          fmt.Sprintf("%d", detail.ID),
	//		UniqueID:        fmt.Sprintf("%d", detail.ID),
	var count int64
	err := repo.db.WithContext(ctx).Model(&models.FpExamineOperateLog{}).Where("operation_group = ? AND operation_action = ? AND unique_id = ?",
		pb.OpGroup_OpGroupExamineDsc.String(), pb.OpAction_OpActionUpdate.String(), fmt.Sprintf("%d", id)).Count(&count).Error
	return count < 2, err
}

func (repo *examineOrders) DscTaskOrderAllFinished(ctx context.Context, taskId uint64) (bool, error) {
	var count int64
	err := repo.db.WithContext(ctx).Model(&models.FpExamineDiscordDetail{}).
		Where("task_id = ? AND status != ?", taskId, pb.ExamineStateDf_ExamineStateDfSuccess).Count(&count).Error
	return err == nil && count == 0, err
}
