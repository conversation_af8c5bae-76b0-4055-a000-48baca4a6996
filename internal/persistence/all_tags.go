package persistence

import (
	"context"
	"errors"
	"github.com/labstack/echo/v4"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"ops-ticket-api/internal/code"
	"ops-ticket-api/internal/framework/database"
	"ops-ticket-api/internal/framework/xerrors"
	"ops-ticket-api/models"
	"ops-ticket-api/proto/pb"
	"ops-ticket-api/utils"
	"sort"
)

// tagLib 标签组
type allTagLib struct {
	db *gorm.DB
}

// NewAllTagLib init
func NewAllTagLib() *allTagLib {
	return &allTagLib{
		db: database.Db(),
	}
}

// AllGetTagLibList 获取标签组列表
func (dto *allTagLib) AllGetTagLibList(ctx context.Context, req *pb.TagLibListReq) ([]*pb.TagLibListResp_TagLib, error) {
	var result = []*pb.TagLibListResp_TagLib{}
	var list []*models.FpOpsTagsLib
	//var projectFilters []interface{}
	//if len(req.ProjectName) > 0 {
	//	projectFilters = []interface{}{"project in (?)", []string{req.ProjectName}}
	//}
	//query := dto.db.WithContext(ctx).Model(&models.FpOpsTagsLib{}).Preload("Projects", projectFilters...)
	query := dto.db.WithContext(ctx).Model(&models.FpOpsTagsLib{}).Preload("Projects")
	if req.LibName != "" {
		query.Where("lib_name like ?", "%"+req.LibName+"%")
	}
	//根据分类展示
	query = query.Where("lib_type = ?", req.LibType)
	if err := query.Find(&list).Error; err != nil {
		logger.Error(ctx, "get tag lib list err", zap.String("err", err.Error()))
		return nil, xerrors.New(err.Error(), code.DbError)
	}

	for _, row := range list {
		lib := &pb.TagLibListResp_TagLib{
			LibId:         row.LibID,
			LibName:       row.LibName,
			TagUploadFile: row.TagUploadFile,
			UpdatedAt:     utils.TimeFormat(int64(row.UpdatedAt)),
			Op:            row.Operator,
			Enable:        row.Enable > 0,
			Projects:      make([]string, 0),
		}
		for _, gm := range row.Projects {
			lib.Projects = append(lib.Projects, gm.Project)
		}
		result = append(result, lib)
	}
	sort.Slice(result, func(i, j int) bool {
		return result[i].LibId > result[j].LibId
	})
	return result, nil
}

// AllGetTagLibByPerms 根据项目/账号获取标签组
func (dto *allTagLib) AllTagLibByPerms(ctx context.Context, account string, param *pb.TagOptsReq) ([]*models.FpOpsTagsLib, error) {
	tagLibs := make([]*models.FpOpsTagsLib, 0)

	// 1. 查询符合条件的 LibID
	libIDs := make([]uint32, 0)
	if err := dto.db.WithContext(ctx).Model(&models.FpOpsTagsLibGm{}).
		Where("project = ? AND enable = ?", param.ProjectName, code.StatusTrue).
		Pluck("lib_id", &libIDs).Error; err != nil {
		logger.Error(ctx, "get lib_id by project err", zap.String("project", param.ProjectName), zap.String("err", err.Error()))
		return nil, xerrors.New(err.Error(), code.DbError)
	}

	// 2. 查询主表记录并加载关联数据
	query := dto.db.WithContext(ctx).Model(&models.FpOpsTagsLib{}).
		Where("lib_id IN ?", libIDs).
		Where("enable = ? AND lib_type = ?", code.StatusTrue, param.LibType).
		Preload("Projects")

	if err := query.Find(&tagLibs).Error; err != nil {
		logger.Error(ctx, "get tag lib by lib_id err", zap.String("err", err.Error()))
		return nil, xerrors.New(err.Error(), code.DbError)
	}
	return tagLibs, nil
}

// AllGetTagLibInfo 获取标签组信息
func (dto *allTagLib) AllGetTagLibInfo(ctx context.Context, tagLibId uint32) (*models.FpOpsTagsLib, error) {
	tagLibInfo := &models.FpOpsTagsLib{}
	if err := dto.db.WithContext(ctx).Model(&models.FpOpsTagsLib{}).Preload("Projects").
		Take(&tagLibInfo, "lib_id=?", tagLibId).Error; err != nil {
		logger.Error(ctx, "get tag lib info err", zap.Uint32("tagLibId", tagLibId), zap.String("err", err.Error()))
		return nil, xerrors.New(err.Error(), code.DbError)
	}
	return tagLibInfo, nil
}

// AllTagLibAdd 标签组添加
func (dto *allTagLib) AllTagLibAdd(ctx echo.Context, libName, tagUploadFile, account string, project []string) (uint32, error) {

	// 查询是否已存在相同名称的标签组
	var has int64
	if err := dto.db.WithContext(ctx.Request().Context()).Model(&models.FpOpsTagsLib{}).
		Where("lib_name = ? AND `enable` = ?", libName, code.StatusTrue).Count(&has).Error; err != nil {
		return 0, err
	}
	if has > 0 {
		return 0, xerrors.New(code.RepeatData)
	}
	now := utils.NowTimestamp()
	tagLibInfo := &models.FpOpsTagsLib{
		LibName:       libName,
		Operator:      account,
		TagUploadFile: tagUploadFile,
		Enable:        code.StatusTrue,
		CreatedAt:     now,
		UpdatedAt:     now,
		Projects:      make([]*models.FpOpsTagsLibGm, 0),
	}
	for _, p := range project {
		tagLibInfo.Projects = append(tagLibInfo.Projects, &models.FpOpsTagsLibGm{
			Project:   p,
			CreatedAt: now,
			UpdatedAt: now,
		})
	}

	if err := dto.db.Create(&tagLibInfo).Error; err != nil {
		logger.Error(ctx.Request().Context(), "tag lib created err", zap.Any("tagLibInfo", tagLibInfo), zap.String("err", err.Error()))
		return 0, err
	}
	return tagLibInfo.LibID, nil
}

// AllTagLibSave 标签组更新
func (dto *allTagLib) AllTagLibSave(ctx echo.Context, tagLibId uint32, libName, account string, project []string) error {
	now := utils.NowTimestamp()
	tagLibInfo := map[string]interface{}{
		"lib_name":   libName,
		"operator":   account,
		"updated_at": now,
	}
	projects := make([]*models.FpOpsTagsLibGm, 0)
	for _, p := range project {
		projects = append(projects, &models.FpOpsTagsLibGm{
			Project:   p,
			LibID:     tagLibId,
			CreatedAt: now,
			UpdatedAt: now,
		})
	}
	tx := dto.db.WithContext(ctx.Request().Context()).Begin()
	if err := tx.Where("lib_id = ?", tagLibId).Delete(&models.FpOpsTagsLibGm{}).Error; err != nil {
		tx.Rollback()
		logger.Error(ctx.Request().Context(), "tag lib project del err", zap.Uint32("tagLibId", tagLibId), zap.String("err", err.Error()))
		return err
	}
	if err := tx.Model(models.FpOpsTagsLib{}).Where("lib_id = ?", tagLibId).Updates(&tagLibInfo).Error; err != nil {
		tx.Rollback()
		logger.Error(ctx.Request().Context(), "tag lib save err", zap.Uint32("tagLibId", tagLibId), zap.String("err", err.Error()))
		return err
	}
	if err := tx.Create(&projects).Error; err != nil {
		logger.Error(ctx.Request().Context(), "tag lib project created err", zap.Any("projects", projects), zap.String("err", err.Error()))
		return err
	}
	if err := tx.Commit().Error; err != nil {
		return err
	}
	return nil
}

// AllTagList 标签列表
func (dto *allTagLib) AllTagList(ctx context.Context, libId uint32, enable bool) ([]*models.FpOpsTags, error) {
	dest := make([]*models.FpOpsTags, 0)
	fields := "tag_id,lib_id,parent_id,level,tag_name,enable"
	where := map[string]interface{}{
		"lib_id": libId,
	}
	if enable {
		where["enable"] = code.StatusTrue
	}
	query := dto.db.WithContext(ctx).Select(fields).Model(&models.FpOpsTags{}).Where(where)
	if err := query.Order("lft asc").Find(&dest).Error; err != nil {
		if !errors.Is(err, gorm.ErrRecordNotFound) {
			logger.Error(ctx, "Get tag list err", zap.String("err", err.Error()))
		}
		return nil, err
	}
	return dest, nil
}

// NewGetDb 获取 db
func (dto *allTagLib) NewGetDb(ctx context.Context) *gorm.DB {
	return dto.db.WithContext(ctx)
}

// AllCheckTagLibNameHasExist 校验 类别下tag_lib 名称 是否已经存在
func (dto *allTagLib) AllCheckTagLibNameHasExist(ctx context.Context, libId, libType uint32, libName string) (bool, error) {
	var count int64
	if err := dto.db.WithContext(ctx).Model(&models.FpOpsTagsLib{}).
		Where("lib_name = ? AND lib_id != ? AND lib_type = ?", libName, libId, libType).Count(&count).Error; err != nil {
		return false, err
	}
	return count > 0, nil
}
