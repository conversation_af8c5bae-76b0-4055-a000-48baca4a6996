package persistence

import (
	"context"
	"errors"
	"fmt"
	"time"

	"ops-ticket-api/internal/code"
	"ops-ticket-api/internal/cst"
	"ops-ticket-api/internal/elasticsearch"
	"ops-ticket-api/internal/framework/database"
	"ops-ticket-api/internal/framework/xerrors"
	"ops-ticket-api/models"
	"ops-ticket-api/proto/pb"
	"ops-ticket-api/utils"

	"github.com/google/uuid"
	"github.com/labstack/echo/v4"
	"github.com/spf13/cast"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"gorm.io/plugin/dbresolver"
)

// MaintainConfig 员工关系维护配置
type MaintainConfig struct {
	db *gorm.DB
}

// NewMaintainConfig init
func NewMaintainConfig() *MaintainConfig {
	return &MaintainConfig{
		db: database.Db(),
	}
}

func (m *MaintainConfig) MaintainConfigSave(ctx echo.Context, req *pb.MaintainConfigNewReq) error {
	// 校验fpid是否重复
	var has int64
	err := m.db.Table(models.GetFpDscPlayerMaintainConfigTableName()).Where("account_id = ?", req.Fpid).Count(&has).Error
	if err != nil {
		logger.Error(ctx.Request().Context(), "error querying existing fpid", zap.String("err", err.Error()))
		return xerrors.New(err.Error(), code.DbError)
	}
	if has > 0 {
		return xerrors.New("fpid重复", code.InvalidParams)
	}
	operator := cast.ToString(ctx.Get(cst.AccountInfoCtx))
	current := utils.NowTimestamp()
	operateTime := utils.TimeFormat(int64(current))
	maintainConfig := models.FpDscPlayerMaintainConfig{
		DscUserId:   req.DscUserId,
		AccountId:   req.Fpid,
		GameProject: req.GameProject,
		NickName:    req.NickName,
		Maintainer:  req.Maintainer,
		Operator:    operator,
		CreateTime:  current,
		UpdatedTime: operateTime,
	}
	if err := m.db.Table(models.GetFpDscPlayerMaintainConfigTableName()).Create(&maintainConfig).Error; err != nil {
		return err
	}
	// sync to es
	// 获取channelId
	var channelId string
	if err = m.db.Model(models.FpDscUser{}).Select("priv_channel_id").Where("dsc_user_id = ? AND project = ?", req.DscUserId, req.GameProject).Scan(&channelId).Error; err != nil {
		return err
	}
	dest := map[string]interface{}{
		"maintainer": req.Maintainer,
		"updated_at": utils.NowTimestamp(),
	}
	if err = elasticsearch.DefaultDscEsSvc.UpdateDsc(ctx.Request().Context(), channelId, dest); err != nil {
		return err
	}
	return nil
}

func (m *MaintainConfig) MaintainConfigEdit(ctx echo.Context, req *pb.MaintainConfigEditReq, player models.DiscordPlayer) error {
	operator := cast.ToString(ctx.Get(cst.AccountInfoCtx))
	current := utils.NowTimestamp()
	operateTime := utils.TimeFormat(int64(current))
	tx := m.db.WithContext(ctx.Request().Context()).Begin()
	var mc models.FpDscPlayerMaintainConfig
	err := tx.Table(models.GetFpDscPlayerMaintainConfigTableName()).Where("id = ?", req.Id).First(&mc).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return xerrors.New(err.Error(), code.DbError)
	} else if errors.Is(err, gorm.ErrRecordNotFound) {
		return gorm.ErrRecordNotFound
	} else {
		// 校验fpid是否重复
		if req.Fpid != "" && req.Fpid != mc.AccountId {
			var has int64
			err = tx.Table(models.GetFpDscPlayerMaintainConfigTableName()).Where("account_id = ? AND game_project = ?", req.Fpid, mc.GameProject).Count(&has).Error
			if err != nil {
				logger.Error(ctx.Request().Context(), "error querying existing fpid", zap.String("err", err.Error()))
				return xerrors.New(err.Error(), code.DbError)
			}
			if has > 0 {
				return xerrors.New("fpid重复", code.InvalidParams)
			}
		}
		// 保存更新前的详情
		beforeDetail := utils.ToJson(mc)
		// 更新维护配置
		mc.Operator = operator
		mc.UpdatedTime = operateTime
		mc.Maintainer = req.Maintainer
		mc.AccountId = req.Fpid
		mc.UID = req.Uid
		// 服务器,游戏昵称,和语言取玩家在CRM系统的数据
		mc.Sid = player.Sid
		mc.PlayerNick = player.PlayerNick
		mc.Lang = player.Lang
		mc.VipState = uint8(req.VipState)
		if err = tx.Table(models.GetFpDscPlayerMaintainConfigTableName()).Where("id = ?", req.Id).Updates(&mc).Error; err != nil {
			tx.Rollback()
			logger.Error(ctx.Request().Context(), "error edit maintain config", zap.String("err", err.Error()))
			return err
		}
		// 保存更新后的详情
		afterDetail := utils.ToJson(mc)
		// 更新沟通记录表中的uid和sid数据
		if req.Uid > 0 || player.Sid != "" {
			mark := map[string]interface{}{
				"uid": req.Uid,
				"sid": player.Sid,
			}
			if err = tx.Table(models.GetFpDscCommuRecordTableName()).Where("project = ? AND dsc_user_id = ? AND nick_name = ?", mc.GameProject, mc.DscUserId, mc.NickName).Updates(mark).Error; err != nil {
				tx.Rollback()
				logger.Error(ctx.Request().Context(), "error upate commu record", zap.String("err", err.Error()))
				return err
			}
		}
		// 同步维护配置到ES
		channelIds := []string{}
		if err = tx.Model(&models.FpDscUser{}).Where("dsc_user_id = ? AND project = ? AND priv_channel_id != ?", mc.DscUserId, mc.GameProject, "").Pluck("priv_channel_id", &channelIds).Error; err != nil {
			tx.Rollback()
			return err
		}
		if len(channelIds) == 0 {
			tx.Rollback()
			return errors.New("chanelId is empty")
		}
		// 更新 es 中 channel_id 关联的数据
		if err = m.MaintainConfigSyncEs(ctx.Request().Context(), tx, &mc); err != nil {
			tx.Rollback()
			return err
		}

		//// 使用脚本更新
		//scripts := []string{
		//	"ctx._source.uid=params.uid",
		//	"ctx._source.account_id=params.account_id",
		//	"ctx._source.sid=params.sid",
		//	"ctx._source.maintainer=params.maintainer",
		//	"ctx._source.vip_state=params.vip_state",
		//	"ctx._source.player_nick=params.player_nick",
		//	"ctx._source.lang=params.lang",
		//	"ctx._source.updated_at=params.updated_at",
		//}
		//dest := map[string]interface{}{
		//	"uid":         req.Uid,
		//	"account_id":  req.Fpid,
		//	"sid":         player.Sid,
		//	"maintainer":  req.Maintainer,
		//	"vip_state":   req.VipState,
		//	"player_nick": player.PlayerNick,
		//	"lang":        player.Lang,
		//	"updated_at":  utils.NowTimestamp(),
		//}
		//for _, channelId := range channelIds {
		//	if err = elasticsearch.DefaultDscEsSvc.UpdateMaintainConfig(ctx.Request().Context(), channelId, scripts, dest); err != nil {
		//		tx.Rollback()
		//		logger.Error(ctx.Request().Context(), "error updating maintain config to ES", zap.String("channelId", channelId), zap.String("err", err.Error()))
		//		return err
		//	}
		//}
		// 记录操作日志
		opDetail := &models.FpDscOperateLog{
			OperationGroup:  pb.OpGroup_OpGroupMaintainConfig.String(),
			OperationAction: pb.OpAction_OpActionUpdate.String(),
			BaseID:          mc.DscUserId,
			UniqueID:        uuid.New().String(),
			GameProject:     mc.GameProject,
			BeforeDetail:    beforeDetail,
			AfterDetail:     afterDetail,
			CreateTime:      time.Now(),
			Operator:        cast.ToString(ctx.Get(cst.AccountInfoCtx)),
		}
		err = tx.Model(&models.FpDscOperateLog{}).Create(opDetail).Error
		if err != nil {
			tx.Rollback()
			logger.Error(ctx.Request().Context(), "[MaintainConfigEdit] error add operate log", zap.String("err", err.Error()))
			return xerrors.New(err.Error(), code.DbError)
		}
		// 提交事务
		if err = tx.Commit().Error; err != nil {
			logger.Error(ctx.Request().Context(), "[MaintainConfigEdit] transaction commit err", zap.String("err", err.Error()))
			return xerrors.New(err.Error(), code.DbError)
		}
		return nil
	}
}

func (m *MaintainConfig) GetMaintainConfigDetail(ctx context.Context, dscUserId, gameProject string) (*models.FpDscPlayerMaintainConfig, error) {
	detail := &models.FpDscPlayerMaintainConfig{}
	err := m.db.WithContext(ctx).Model(detail).Clauses(dbresolver.Write).
		Where("dsc_user_id = ? AND game_project = ?", dscUserId, gameProject).First(detail).Error
	return detail, err
}

func (m *MaintainConfig) MaintainConfigSyncEs(ctx context.Context, tx *gorm.DB, mc *models.FpDscPlayerMaintainConfig) error {
	if tx == nil {
		tx = m.db.WithContext(ctx)
	}
	// 同步维护配置到ES
	channelIds := []string{}
	if err := tx.Model(&models.FpDscUser{}).Clauses(dbresolver.Write).
		Where("dsc_user_id = ? AND project = ? AND priv_channel_id != ?", mc.DscUserId, mc.GameProject, "").
		Pluck("priv_channel_id", &channelIds).Error; err != nil {
		return err
	}
	if len(channelIds) == 0 {
		return errors.New("chanelId is empty")
	}
	// 使用脚本更新
	scripts := []string{
		"ctx._source.uid=params.uid",
		"ctx._source.account_id=params.account_id",
		"ctx._source.sid=params.sid",
		"ctx._source.maintainer=params.maintainer",
		"ctx._source.vip_state=params.vip_state",
		"ctx._source.player_nick=params.player_nick",
		"ctx._source.lang=params.lang",
		"ctx._source.updated_at=params.updated_at",
	}
	dest := map[string]interface{}{
		"uid":         mc.UID,
		"account_id":  mc.AccountId,
		"sid":         mc.Sid,
		"maintainer":  mc.Maintainer,
		"vip_state":   mc.VipState,
		"player_nick": mc.PlayerNick,
		"lang":        mc.Lang,
		"updated_at":  utils.NowTimestamp(),
	}
	for _, channelId := range channelIds {
		if err := elasticsearch.DefaultDscEsSvc.UpdateMaintainConfig(ctx, channelId, scripts, dest); err != nil {
			logger.Error(ctx, "error updating maintain config to ES", zap.String("channelId", channelId), zap.String("err", err.Error()))
			return err
		}
	}
	return nil
}

func (m *MaintainConfig) MaintainConfigDel(ctx echo.Context, req *pb.MaintainConfigDelReq) ([]models.FpDscUser, error) {
	tx := m.db.WithContext(ctx.Request().Context()).Begin()
	detail := models.FpDscPlayerMaintainConfig{}
	if err := tx.Table(models.GetFpDscPlayerMaintainConfigTableName()).Where("id = ?", req.Id).Find(&detail).Error; err != nil {
		logger.Error(ctx.Request().Context(), "error query maintain config", zap.String("err", err.Error()))
		return nil, xerrors.New(err.Error(), code.DbError)
	}
	// 1 MaintainConfig 数据删除
	err := tx.Table(models.GetFpDscPlayerMaintainConfigTableName()).Where("id = ?", req.Id).Delete(&models.FpDscPlayerMaintainConfig{}).Error
	if err != nil {
		tx.Rollback()
		logger.Error(ctx.Request().Context(), "error delete maintain config", zap.String("err", err.Error()))
		return nil, xerrors.New(err.Error(), code.DbError)
	}
	// 删除前查出channel_id
	channelIds := []string{}
	if err = tx.Model(&models.FpDscUser{}).Where("dsc_user_id = ? AND project = ? AND priv_channel_id != ?", req.DscUserId, req.GameProject, "").Pluck("priv_channel_id", &channelIds).Error; err != nil {
		tx.Rollback()
		return nil, err
	}
	// 2 fp_dsc_user表相关数据进行软删除
	err = tx.Model(&models.FpDscUser{}).Where("dsc_user_id = ? AND project = ?", req.DscUserId, req.GameProject).Updates(map[string]interface{}{
		"priv_channel_id": "",
		"is_deleted":      code.DscDeleted,
		"updated_at":      time.Now(),
	}).Error
	if err != nil {
		tx.Rollback()
		logger.Error(ctx.Request().Context(), "error delete fp_dsc_user", zap.String("err", err.Error()))
		return nil, xerrors.New(err.Error(), code.DbError)
	}
	// 3 将该玩家从服务器剔除
	users := []models.FpDscUser{}
	if err = tx.Model(&models.FpDscUser{}).Where("dsc_user_id = ? AND project = ?", req.DscUserId, req.GameProject).Find(&users).Error; err != nil {
		tx.Rollback()
		return nil, err
	}
	// 4 es中将该channel_id关联的数据进行软删除
	// channel_id不置为空，只标记为删除
	scripts := []string{
		"ctx._source.is_deleted=params.is_deleted",
		"ctx._source.uid=params.uid",
		"ctx._source.account_id=params.account_id",
		"ctx._source.sid=params.sid",
		"ctx._source.maintainer=params.maintainer",
		"ctx._source.vip_state=params.vip_state",
		"ctx._source.pay_all=params.pay_all",
		"ctx._source.pay_last_thirty_days=params.pay_last_thirty_days",
		"ctx._source.last_login=params.last_login",
		"ctx._source.vip_level=params.vip_level",
		"ctx._source.updated_at=params.updated_at",
	}
	dest := map[string]interface{}{
		"is_deleted":           code.DscDeleted,
		"uid":                  0,
		"account_id":           "",
		"sid":                  "",
		"maintainer":           "",
		"vip_state":            code.NonVip,
		"pay_all":              0,
		"pay_last_thirty_days": 0,
		"last_login":           0,
		"vip_level":            0,
		"updated_at":           utils.NowTimestamp(),
	}
	for _, channelId := range channelIds {
		if err = elasticsearch.DefaultDscEsSvc.UpdateMaintainConfig(ctx.Request().Context(), channelId, scripts, dest); err != nil {
			tx.Rollback()
			return nil, err
		}
	}
	// 5 记录操作日志
	opDetail := &models.FpDscOperateLog{
		OperationGroup:  pb.OpGroup_OpGroupMaintainConfig.String(),
		OperationAction: pb.OpAction_OpActionDelete.String(),
		BaseID:          detail.DscUserId,
		UniqueID:        uuid.New().String(),
		GameProject:     req.GameProject,
		BeforeDetail:    utils.ToJson(detail),
		AfterDetail:     "{}",
		CreateTime:      time.Now(),
		Operator:        cast.ToString(ctx.Get(cst.AccountInfoCtx)),
	}
	err = tx.Model(&models.FpDscOperateLog{}).Create(opDetail).Error
	if err != nil {
		tx.Rollback()
		logger.Error(ctx.Request().Context(), "error add operate log", zap.String("err", err.Error()))
		return nil, xerrors.New(err.Error(), code.DbError)
	}
	// 提交事务
	if err = tx.Commit().Error; err != nil {
		logger.Error(ctx.Request().Context(), "[MaintainConfigDel] transaction commit err", zap.String("err", err.Error()))
		return nil, xerrors.New(err.Error(), code.DbError)
	}
	return users, nil
}

func (m *MaintainConfig) MaintainConfigList(ctx echo.Context, req *pb.MaintainConfigListReq, all bool) ([]*pb.MaintainConfigListResp_MaintainConfigInfo, uint32, error) {
	maintainConfigList := []*models.FpDscPlayerMaintainConfigList{}
	fields := "c.*, p.birthday"
	query := m.db.Table(fmt.Sprintf("%s AS c", models.GetFpDscPlayerMaintainConfigTableName())).
		Joins(fmt.Sprintf("LEFT JOIN %s AS p ON c.dsc_user_id = p.dsc_user_id AND c.game_project = p.game_project", models.GetFpDscPlayerPortraitTableName()), "")
	query = query.Select(fields)
	if len(req.GameProject) > 0 {
		query = query.Where("c.game_project IN (?)", req.GameProject)
	}
	if len(req.Maintainer) > 0 {
		query = query.Where("c.maintainer IN (?)", req.Maintainer)
	}
	if len(req.NickName) > 0 {
		query = query.Where("c.nick_name IN (?)", req.NickName)
	}
	if req.DscUserId != "" {
		query = query.Where("c.dsc_user_id = ?", req.DscUserId)
	}
	if req.VipState > 0 {
		query = query.Where("c.vip_state = ?", req.VipState)
	}
	var count int64
	if err := query.Count(&count).Error; err != nil {
		logger.Error(ctx.Request().Context(), "get maintain config list count err", zap.String("err", err.Error()))
		return nil, 0, err
	}
	// 导出数据不分页，查询才分页
	if !all {
		if err := query.Scopes(database.Paginate(&req.Page, &req.PageSize)).Find(&maintainConfigList).Error; err != nil {
			logger.Error(ctx.Request().Context(), "get maintain config list err", zap.String("err", err.Error()))
			return nil, 0, xerrors.New(err.Error(), code.DbError)
		}
	} else {
		if err := query.Find(&maintainConfigList).Error; err != nil {
			logger.Error(ctx.Request().Context(), "get all maintain config list err", zap.String("err", err.Error()))
			return nil, 0, xerrors.New(err.Error(), code.DbError)
		}
	}
	dest := make([]*pb.MaintainConfigListResp_MaintainConfigInfo, len(maintainConfigList))
	for i := range maintainConfigList {
		maintainConfig := maintainConfigList[i]
		dest[i] = &pb.MaintainConfigListResp_MaintainConfigInfo{
			Id:          maintainConfig.ID,
			DscUserId:   maintainConfig.DscUserId,
			NickName:    maintainConfig.NickName,
			Uid:         maintainConfig.UID,
			Fpid:        maintainConfig.AccountId,
			Sid:         maintainConfig.Sid,
			Maintainer:  maintainConfig.Maintainer,
			GameProject: maintainConfig.GameProject,
			Operator:    maintainConfig.Operator,
			VipState:    uint32(maintainConfig.VipState),
			UpdateTime:  maintainConfig.UpdatedTime,
			Lang:        maintainConfig.Lang,
			Birthday:    maintainConfig.Birthday,
		}
	}
	return dest, cast.ToUint32(count), nil
}

func (m *MaintainConfig) GetFpidByDscUserId(ctx echo.Context, dscUserId string) (string, error) {
	var fpid string
	err := m.db.Table(models.GetFpDscPlayerMaintainConfigTableName()).Select("account_id").Where("dsc_user_id = ?", dscUserId).Take(&fpid).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		logger.Error(ctx.Request().Context(), "error get fpid from maintain config", zap.String("err", err.Error()))
		return fpid, err
	}
	return fpid, nil
}

func (m *MaintainConfig) DiscordPlayerAccounts(ctx echo.Context, req *pb.DiscordPlayerAccountsReq) ([]string, error) {
	query := m.db.Model(models.FpDscUser{}).Select("DISTINCT dsc_user_id").Where("project = ?", req.GameProject)
	if req.DscUserId != "" {
		query = query.Where("dsc_user_id LIKE ?", "%"+req.DscUserId+"%")
	}
	data := []string{}
	if err := query.Find(&data).Error; err != nil {
		return data, err
	}
	return data, nil
}

func (m *MaintainConfig) DiscordPlayerUidList(ctx echo.Context, req *pb.DiscordPlayerUidReq) ([]int64, error) {
	query := m.db.Model(models.FpDscPlayerMaintainConfig{}).Select("uid").Where("game_project IN (?)", req.Project)
	if req.Uid > 0 {
		query = query.Where("uid = ?", req.Uid)
	}
	data := []int64{}
	if err := query.Find(&data).Error; err != nil {
		return data, err
	}
	return data, nil
}

func (m *MaintainConfig) FetchAllDscPlayers() ([]*models.FpDscPlayerMaintainConfig, error) {
	dest := []*models.FpDscPlayerMaintainConfig{}
	if err := m.db.Table(models.GetFpDscPlayerMaintainConfigTableName()).Select("*").Where("account_id != ?", "").Find(&dest).Error; err != nil {
		return dest, err
	}
	return dest, nil
}

// TicketDailyCount 每日工单数量
type TicketDailyCount struct {
	Day         string `json:"day"`
	ShortDay    string `json:"short_day"`
	TicketCount int64  `json:"ticket_count"`
}

func (m *MaintainConfig) GetTicketStatByCatId(ctx context.Context, catId int, project string) ([]TicketDailyCount, error) {
	var results []TicketDailyCount
	if err := m.db.Table("fp_ops_tickets").
		Select("DATE(FROM_UNIXTIME(created_at)) AS day, DATE_FORMAT(FROM_UNIXTIME(created_at), '%c.%e') as short_day, COUNT(*) as ticket_count").
		Where("cat_id = ? AND project = ? ", catId, project).
		Where("DATE(FROM_UNIXTIME(created_at)) >= CURDATE() - INTERVAL 15 DAY AND DATE(FROM_UNIXTIME(created_at)) < CURDATE()").
		Group("day").
		Order("day asc").
		Scan(&results).Error; err != nil {
		return nil, err
	}

	// 创建一个map用于快速查找已有数据
	dataMap := make(map[string]int64)
	for _, r := range results {
		dataMap[r.ShortDay] = r.TicketCount
	}

	// 生成最近14天的完整数据（不含当天）
	var fullResults []TicketDailyCount
	for i := 14; i > 0; i-- {
		date := time.Now().AddDate(0, 0, -i)
		dateStr := date.Format("2006-01-02")
		shortDay := date.Format("1.2")

		count, ok := dataMap[shortDay]
		if !ok {
			count = 0
		}
		fullResults = append(fullResults, TicketDailyCount{
			Day:         dateStr,
			ShortDay:    shortDay,
			TicketCount: count,
		})
	}

	return fullResults, nil
}

func (m *MaintainConfig) UpdatesCrmData(ctx echo.Context, mc map[string]interface{}) error {
	if err := m.db.Table(models.GetFpDscPlayerMaintainConfigTableName()).Where("id = ?", mc["id"]).Updates(mc).Error; err != nil {
		return err
	}
	// sync maintainConfig to es
	channelIds := []string{}
	if err := m.db.Model(&models.FpDscUser{}).Where("dsc_user_id = ? AND project = ? AND priv_channel_id != ?", mc["dsc_user_id"], mc["game_project"], "").Pluck("priv_channel_id", &channelIds).Error; err != nil {
		return err
	}
	// 使用脚本更新
	scripts := []string{
		"ctx._source.uid=params.uid",
		"ctx._source.account_id=params.account_id",
		"ctx._source.pay_all=params.pay_all",
		"ctx._source.sid=params.sid",
		"ctx._source.pay_last_thirty_days=params.pay_last_thirty_days",
		"ctx._source.last_login=params.last_login",
		"ctx._source.vip_level=params.vip_level",
		"ctx._source.player_nick=params.player_nick",
		"ctx._source.lang=params.lang",
		"ctx._source.updated_at=params.updated_at",
	}
	lastLogin := uint64(0)
	if mc["last_login"] == "0000-00-00 00:00:00" || mc["last_login"] == "" {
		lastLogin = 0
	} else {
		lastLogin = utils.TimeStrToUnix(mc["last_login"].(string))
	}
	dest := map[string]interface{}{
		"uid":                  mc["uid"],
		"account_id":           mc["account_id"],
		"pay_all":              mc["pay_all"],
		"sid":                  mc["sid"],
		"pay_last_thirty_days": mc["pay_last_thirty_days"],
		"last_login":           lastLogin,
		"vip_level":            mc["vip_level"],
		"player_nick":          mc["player_nick"],
		"lang":                 mc["lang"],
		"updated_at":           utils.NowTimestamp(),
	}
	for _, channelId := range channelIds {
		if err := elasticsearch.DefaultDscEsSvc.UpdateMaintainConfig(ctx.Request().Context(), channelId, scripts, dest); err != nil {
			return err
		}
	}
	return nil
}
