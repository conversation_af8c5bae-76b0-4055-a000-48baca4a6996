package persistence

import (
	"github.com/labstack/echo/v4"
	"github.com/spf13/cast"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"ops-ticket-api/internal/code"
	"ops-ticket-api/internal/cst"
	"ops-ticket-api/internal/framework/database"
	"ops-ticket-api/internal/framework/xerrors"
	"ops-ticket-api/models"
	"ops-ticket-api/proto/pb"
	"ops-ticket-api/utils"
	"strings"
	"time"
)

func NewTeamConfigRepo() *TeamConfig {
	return &TeamConfig{
		db: database.Db(),
	}
}

type TeamConfig struct {
	db *gorm.DB
}

func (dto *TeamConfig) AddTeamConfig(ctx echo.Context, param *pb.TeamConfigAddReq) error {
	tx := dto.db.WithContext(ctx.Request().Context()).Begin()
	operator := cast.ToString(ctx.Get(cst.AccountInfoCtx))
	updateTime := utils.TimeFormat(time.Now().Unix())
	record := models.FpOpsTeamConfig{
		TeamName:   param.TeamName,
		TeamMember: param.TeamMember,
		Updater:    operator,
		UpdateTime: updateTime,
	}
	if err := tx.Model(&models.FpOpsTeamConfig{}).Create(&record).Error; err != nil {
		elog.Errorf("Add teamConfig err:%v", err.Error())
		tx.Rollback()
		return err
	}
	members := strings.Split(param.TeamMember, ",")
	teamMembers := make([]*models.FpOpsTeamMembers, len(members))
	for i, member := range members {
		teamMembers[i] = &models.FpOpsTeamMembers{
			TeamID:     record.ID,
			TeamMember: member,
			Updater:    operator,
			UpdateTime: updateTime,
		}
	}
	if err := tx.Model(&models.FpOpsTeamMembers{}).CreateInBatches(teamMembers, len(teamMembers)).Error; err != nil {
		elog.Errorf("Add teamMembers err:%v", err.Error())
		tx.Rollback()
		return err
	}
	if err := tx.Commit().Error; err != nil {
		elog.Errorf("Add teamConfig commit err:%v", err.Error())
		tx.Rollback()
	}
	return nil
}

func (dto *TeamConfig) TeamConfigList(ctx echo.Context, param *pb.TeamConfigListReq) (*pb.TeamConfigListResp, error) {
	// filter data
	dest := []*models.FpOpsTeamConfig{}
	query := dto.db.WithContext(ctx.Request().Context()).Table(models.GetFpOpsTeamConfigTableName()).Select("*")
	if param.TeamName != "" {
		query.Where("team_name LIKE ?", "%"+param.TeamName+"%")
	}
	if param.TeamMember != "" {
		query.Where("team_member LIKE ?", "%"+param.TeamMember+"%")
	}
	var total int64
	if err := query.Count(&total).Error; err != nil {
		logger.Error(ctx.Request().Context(), "get team_config list count err", zap.String("err", err.Error()))
		return nil, err
	}
	if param.IsAll {
		if err := query.Find(&dest).Error; err != nil {
			logger.Error(ctx.Request().Context(), "get team_config all list err", zap.String("err", err.Error()))
			return nil, xerrors.New(err.Error(), code.DbError)
		}
	} else {
		if err := query.Scopes(database.Paginate(&param.Page, &param.PageSize)).Find(&dest).Error; err != nil {
			logger.Error(ctx.Request().Context(), "get team_config page list err", zap.String("err", err.Error()))
			return nil, xerrors.New(err.Error(), code.DbError)
		}
	}

	result := &pb.TeamConfigListResp{Data: []*pb.TeamConfigListResp_Detail{}}
	result.CurrentPage = param.Page
	result.PerPage = param.PageSize
	result.Total = uint32(total)
	for _, cfg := range dest {
		rowDetail := &pb.TeamConfigListResp_Detail{
			TeamId:     cfg.ID,
			TeamName:   cfg.TeamName,
			TeamMember: cfg.TeamMember,
			Updater:    cfg.Updater,
			UpdateTime: cfg.UpdateTime,
		}
		result.Data = append(result.Data, rowDetail)
	}
	return result, nil
}

func (dto *TeamConfig) EditTeamConfig(ctx echo.Context, param *pb.TeamConfigEditReq) error {
	tx := dto.db.WithContext(ctx.Request().Context()).Begin()
	dest := make(map[string]interface{})
	if param.TeamName != "" {
		dest["team_name"] = param.TeamName
	}
	if param.TeamMember != "" {
		dest["team_member"] = param.TeamMember
	}
	if len(dest) == 0 {
		return nil
	}
	operator := cast.ToString(ctx.Get(cst.AccountInfoCtx))
	updateTime := utils.TimeFormat(time.Now().Unix())
	dest["updater"] = operator
	dest["update_time"] = updateTime
	// 更新团队配置表
	if err := tx.Table(models.GetFpOpsTeamConfigTableName()).Where("id = ?", param.TeamId).Updates(dest).Error; err != nil {
		elog.Errorf("Edit teamConfig err:%v", err.Error())
		return err
	}
	// 修改团队成员表
	if param.TeamMember != "" {
		// 删除原团队成员
		if err := tx.Table(models.GetFpOpsTeamMembersTableName()).Where("team_id = ?", param.TeamId).Delete(&models.FpOpsTeamMembers{}).Error; err != nil {
			elog.Errorf("Delete original team members err:%v", err.Error())
			return err
		}
		// 重新插入团队成员
		members := strings.Split(param.TeamMember, ",")
		teamMembers := make([]*models.FpOpsTeamMembers, len(members))
		for i, member := range members {
			teamMembers[i] = &models.FpOpsTeamMembers{
				TeamID:     int64(param.TeamId),
				TeamMember: member,
				Updater:    operator,
				UpdateTime: updateTime,
			}
		}
		if err := tx.Model(&models.FpOpsTeamMembers{}).CreateInBatches(teamMembers, len(teamMembers)).Error; err != nil {
			elog.Errorf("Add teamMembers err:%v", err.Error())
			tx.Rollback()
			return err
		}
	}
	if err := tx.Commit().Error; err != nil {
		elog.Errorf("Edit teamConfig commit err:%v", err.Error())
		tx.Rollback()
	}
	return nil
}

func (dto *TeamConfig) DeleteTeamConfig(ctx echo.Context, param *pb.TeamConfigDelReq) error {
	tx := dto.db.WithContext(ctx.Request().Context()).Begin()
	if err := tx.Table(models.GetFpOpsTeamConfigTableName()).Where("id = ?", param.TeamId).Delete(&models.FpOpsTeamConfig{}).Error; err != nil {
		elog.Errorf("DeleteTeamConfig err:%v", err.Error())
		tx.Rollback()
		return err
	}
	if err := tx.Table(models.GetFpOpsTeamMembersTableName()).Where("team_id =?", param.TeamId).Delete(&models.FpOpsTeamMembers{}).Error; err != nil {
		elog.Errorf("Delete teamMembers err:%v", err.Error())
		tx.Rollback()
		return err
	}
	if err := tx.Commit().Error; err != nil {
		elog.Errorf("Delete teamConfig commit err:%v", err.Error())
		tx.Rollback()
	}
	return nil
}

func (dto *TeamConfig) GetMembersWithIds(ctx echo.Context, teamIDs []int64) ([]string, error) {
	var members []string
	err := dto.db.WithContext(ctx.Request().Context()).
		Table(models.GetFpOpsTeamMembersTableName()).
		Where("team_id IN (?)", teamIDs).
		Distinct("team_member").
		Pluck("team_member", &members).Error
	if err != nil {
		elog.Errorf("GetMembersWithIds err:%v", err.Error())
		return nil, err
	}
	return members, nil
}
