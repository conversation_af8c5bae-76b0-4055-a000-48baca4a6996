package persistence

import (
	"encoding/json"
	"errors"
	"ops-ticket-api/internal/cst"
	"ops-ticket-api/internal/framework/database"
	"ops-ticket-api/models"
	"ops-ticket-api/proto/pb"

	"github.com/bwmarrin/discordgo"

	"ops-ticket-api/internal/dsc"

	"github.com/labstack/echo/v4"
	"github.com/spf13/cast"
	"gorm.io/gorm"
)

func NewDcsBotConfigRepo() *dcsBotConfigRepo {
	return &dcsBotConfigRepo{
		db: database.Db(),
	}
}

type dcsBotConfigRepo struct {
	db *gorm.DB
}

// Add 新增Discord机器人配置
func (r *dcsBotConfigRepo) Add(ctx echo.Context, req *pb.DCSBotConfigAddReq) error {
	req.BotConfig.Project = req.Project

	botConfigBytes, err := json.Marshal(req.BotConfig)
	if err != nil {
		return err
	}

	//如果appid 已被使用，则返回错误
	if err := r.Check(ctx, req); err != nil {
		return err
	}

	account := cast.ToString(ctx.Get(cst.AccountNameCtx))

	bot := &models.FpDscBot{
		Project:       req.Project,
		DscName:       req.DscName,
		BotDesc:       req.BotDesc,
		AppID:         req.AppId,
		BotConfig:     string(botConfigBytes),
		UserID:        req.UserId,
		Username:      req.Username,
		Discriminator: req.Discriminator,
		UpdatedBy:     account,
	}
	return r.db.Create(bot).Error
}

func (r *dcsBotConfigRepo) Check(ctx echo.Context, req *pb.DCSBotConfigAddReq) error {
	var count int64
	if err := r.db.Model(&models.FpDscBot{}).Where("app_id = ? and is_delete = 0", req.AppId).Count(&count).Error; err != nil {
		return err
	}
	if count > 0 {
		return errors.New("appid 已被使用")
	}

	//如果token已被添加，则检查token是否正确
	if req.BotConfig.BotToken != "" {
		if err := r.db.Model(&models.FpDscBot{}).Where("bot_config->'$.bot_token' = ? and is_delete = 0", req.BotConfig.BotToken).Count(&count).Error; err != nil {
			return err
		}
	}
	if count > 0 {
		return errors.New("token 已被使用")
	}

	sess, err := discordgo.New("Bot " + req.BotConfig.BotToken)
	if err != nil {
		return err
	}
	if _, err := sess.User("@me"); err != nil {
		return errors.New("token 错误")
	}

	return nil
}

// List 获取Discord机器人配置列表
func (r *dcsBotConfigRepo) List(ctx echo.Context, req *pb.DCSBotConfigListReq) (*pb.DCSBotConfigListResp, error) {
	var total int64
	var bots []*models.FpDscBot

	db := r.db.Model(&models.FpDscBot{}).Where("is_delete = ?", false)
	if err := db.Count(&total).Error; err != nil {
		return nil, err
	}

	offset := (req.Page - 1) * req.PageSize
	if err := db.Offset(int(offset)).Limit(int(req.PageSize)).Find(&bots).Error; err != nil {
		return nil, err
	}

	resp := &pb.DCSBotConfigListResp{
		Total:       uint32(total),
		CurrentPage: req.Page,
		PerPage:     req.PageSize,
		Data:        make([]*pb.DscBotDetail, 0, len(bots)),
	}

	for _, bot := range bots {
		botConfig := bot.DecodeBotConfig()
		botStatus := uint32(0)
		if sess := dsc.GetClient(ctx.Request().Context(), bot.AppID); sess != nil {
			if _, err := sess.User("@me"); err != nil {
				botStatus = 2
			} else {
				botStatus = 1
			}
		}

		detail := &pb.DscBotDetail{
			Id:      bot.ID,
			Project: bot.Project,
			DscName: bot.DscName,
			BotDesc: bot.BotDesc,
			AppId:   bot.AppID,
			BotConfig: &pb.DscBotConfig{
				ClientId:       botConfig.ClientId,
				PublicKey:      botConfig.PublicKey,
				BotToken:       botConfig.BotToken,
				GuildId:        botConfig.GuildId,
				GuildDesc:      botConfig.GuildDesc,
				Project:        botConfig.Project,
				WelcomeMessage: botConfig.WelcomeMessage,
			},
			IsDelete:      bot.IsDelete,
			UserId:        bot.UserID,
			Username:      bot.Username,
			Discriminator: bot.Discriminator,
			CreatedAt:     bot.CreatedAt.Format("2006-01-02 15:04:05"),
			UpdatedAt:     bot.UpdatedAt.Format("2006-01-02 15:04:05"),
			UpdatedBy:     bot.UpdatedBy,
			BotStatus:     botStatus,
		}
		resp.Data = append(resp.Data, detail)
	}

	return resp, nil
}

// UpdateWelcomeMessage 更新DC机器人配置欢迎消息
func (r *dcsBotConfigRepo) UpdateWelcomeMessage(ctx echo.Context, req *pb.UpdateDCSBotConfigWelcomeMessageReq) error {
	bot := &models.FpDscBot{}
	if err := r.db.Where("id = ?", req.Id).First(bot).Error; err != nil {
		return err
	}

	botConfig := bot.DecodeBotConfig()
	botConfig.WelcomeMessage = req.WelcomeMessage
	botConfig.Project = bot.Project

	botConfigBytes, err := json.Marshal(botConfig)
	if err != nil {
		return err
	}

	return r.db.Model(&models.FpDscBot{}).Where("id = ?", req.Id).Update("bot_config", string(botConfigBytes)).Error
}
