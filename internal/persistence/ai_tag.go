package persistence

import (
	"context"
	"gorm.io/gorm"
	"ops-ticket-api/internal/framework/database"
	"ops-ticket-api/models"
)

// aiTag ai打标
type aiTag struct {
	db *gorm.DB
}

// NewAITag init
func NewAITag() *aiTag {
	return &aiTag{
		db: database.Db(),
	}
}

func (dto *aiTag) SaveAIPortrait(ctx context.Context, aiTagInfo *models.FpOpsTicketsAiTag) error {
	return dto.db.WithContext(ctx).Create(aiTagInfo).Error
}

func (dto *aiTag) CheckHasLabel(ctx context.Context, ticketId uint64) (bool, error) {
	var count int64
	err := dto.db.WithContext(ctx).Model(&models.FpOpsTicketsAiTag{}).Where("ticket_id = ?", ticketId).Count(&count).Error
	if err != nil {
		return false, err
	}
	return count > 0, nil
}
