package persistence

import (
	"context"
	"errors"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"log"
	"ops-ticket-api/internal/code"
	"ops-ticket-api/internal/elasticsearch"
	"ops-ticket-api/internal/framework/cache/keys"
	"ops-ticket-api/internal/framework/cache/rds"
	"ops-ticket-api/internal/framework/database"
	"ops-ticket-api/internal/framework/xerrors"
	"ops-ticket-api/models"
	"ops-ticket-api/proto/pb"
	"ops-ticket-api/utils"
	"sync"
	"time"
)

var (
	lineInteractionsOnce sync.Once
	lineInteractionsRepo *LineInteractions
)

type LineInteractions struct {
	db *gorm.DB
}

// NewLineInteractions init
func NewLineInteractions() *LineInteractions {
	lineInteractionsOnce.Do(func() {
		lineInteractionsRepo = &LineInteractions{
			db: database.Db(),
		}
	})
	return lineInteractionsRepo
}

func (repo *LineInteractions) CheckRepeatEvent(ctx context.Context, eventId string) (bool, error) {
	record := &models.FpLineCommu{}
	err := repo.db.Table(models.GetFpLineCommuTableName()).Where("event_id=?", eventId).First(&record).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		logger.Error(ctx, "failed to check repeat event by event id", zap.String("err", err.Error()))
		return false, err
	}
	log.Printf("line commu record is %v\n", record.ID)
	return record.ID > 0, nil
}

func (repo *LineInteractions) FetchChanelById(ctx context.Context, channelId string) (*models.FpLineChannel, error) {
	channel := &models.FpLineChannel{}
	err := repo.db.Table(models.GetFpLineChannelTableName()).Where("channel_id=?", channelId).First(&channel).Error
	if err != nil {
		logger.Error(ctx, "failed to get line channel by channel id", zap.String("err", err.Error()))
		return nil, err
	}
	return channel, nil
}
func (repo *LineInteractions) FetchChannelShows(ctx context.Context, channelIds []string) (map[string]string, error) {
	if len(channelIds) == 0 {
		return map[string]string{}, nil
	}
	var dest []*models.FpLineChannel
	err := repo.db.Table(models.GetFpLineChannelTableName()).Where("channel_id IN (?)", channelIds).Find(&dest).Error
	if err != nil {
		logger.Error(ctx, "failed to get line channel by channel ids", zap.String("err", err.Error()))
		return map[string]string{}, err
	}
	var channelMap = make(map[string]string, len(dest))
	for _, row := range dest {
		channelMap[row.ChannelID] = row.Provider
	}
	return channelMap, nil
}

// GetLineChannelList 获取 line channel list
func (repo *LineInteractions) GetLineChannelList(ctx context.Context, gms []string) ([]*models.FpLineChannel, error) {
	var bots []*models.FpLineChannel
	tx := repo.db.WithContext(ctx)
	if len(gms) > 0 {
		tx = tx.Where("project IN (?)", gms)
	}
	err := tx.Find(&bots).Error
	return bots, err
}
func (repo *LineInteractions) FetchBotById(ctx context.Context, botId string) (*models.FpLineBot, error) {
	bot := &models.FpLineBot{}
	err := repo.db.Table(models.GetFpLineBotTableName()).Where("bot_id=?", botId).First(&bot).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		logger.Error(ctx, "failed to get line bot by bot id", zap.String("err", err.Error()))
		return nil, err
	}
	return bot, nil
}

/*
Bot表新增一条记录(只新增一次) 缓存
channel表更新bot_id数据，也只更新一次
新增user记录 EventTypeFollow事件
commu表新增消息,发送欢迎语
同步数据到ES
*/

// SaveUser 对应EventTypeFollow事件
func (repo *LineInteractions) SaveUser(ctx context.Context, user *models.FpLineUser) error {
	current := time.Now().UTC()
	tx := repo.db.WithContext(ctx).Begin()
	// 查询user表是否有这个人，如果没有则新增，否则是取关后再加的，更新为未删除
	lineUser, err := repo.FetchLineUserById(ctx, user.LineUserID, user.Project)
	if err != nil {
		return err
	}
	log.Printf("lineUser is %+v\n", lineUser)
	createTime := uint64(current.Unix())
	operateTime := utils.TimeFormat(int64(createTime))
	// 插入关系配置表
	mc := models.FpLinePlayerMaintainConfig{
		LineUserId: user.LineUserID,
		Project:    user.Project,
		NickName:   user.DisplayName,
		VipState:   code.NonVip,
		Operator:   "system",
		CreateTime: createTime,
		UpdateTime: operateTime,
	}
	if err = tx.Table(models.GetFpLinePlayerMaintainConfigTableName()).Clauses(clause.OnConflict{DoNothing: true}).Create(&mc).Error; err != nil {
		logger.Errorf(ctx, "failed to add line player maintain config err:", zap.String("err", err.Error()))
		tx.Rollback()
		return err
	}
	// 更新
	if lineUser.ID > 0 {
		dest := map[string]interface{}{
			"is_deleted":    code.DscNoDelete,
			"follow_status": code.LineFollowed, // 1:关注
			"updated_at":    current,
		}
		err = tx.Table(models.GetFpLineUserTableName()).Where("line_user_id = ? AND bot_id = ?", user.LineUserID, user.BotID).Updates(dest).Error
		if err != nil {
			tx.Rollback()
			logger.Error(ctx, "failed to update line user", zap.String("err", err.Error()))
			return err
		}
		doc := map[string]interface{}{
			"is_deleted":    code.DscNoDelete,
			"follow_status": code.LineFollowed,
			"updated_at":    utils.NowTimestamp(),
		}
		// 同步至es
		if err = elasticsearch.DefaultLineEsSvc.UpdateLine(ctx, user.LineUserID, doc); err != nil {
			tx.Rollback()
			return err
		}
		// 提交事务
		if err = tx.Commit().Error; err != nil {
			logger.Error(ctx, "[SaveLineUser] transaction commit err", zap.String("err", err.Error()))
			return xerrors.New(err.Error(), code.DbError)
		}
		return nil
	}
	// 新增
	if err = tx.Table(models.GetFpLineUserTableName()).Create(user).Error; err != nil {
		logger.Error(ctx, "[SaveLineUser] err", zap.String("err", err.Error()))
		tx.Rollback()
		return err
	}
	// 同步至es
	if err = elasticsearch.DefaultLineEsSvc.UpsertLineUser(ctx, user); err != nil {
		tx.Rollback()
		return err
	}
	// 提交事务
	if err = tx.Commit().Error; err != nil {
		logger.Error(ctx, "[SaveLineUser] transaction commit err", zap.String("err", err.Error()))
		return xerrors.New(err.Error(), code.DbError)
	}
	return nil
}

// UnfollowLineUser 对应EventTypeUnfollow事件
func (repo *LineInteractions) UnfollowLineUser(ctx context.Context, project, lineUserId, botId string) error {
	// 将用户标记为未关注
	current := time.Now().UTC()
	tx := repo.db.WithContext(ctx).Begin()
	dest := map[string]interface{}{
		"follow_status": code.LineUnFollowed,
		"updated_at":    current,
	}
	err := tx.Table(models.GetFpLineUserTableName()).Where("line_user_id = ? AND bot_id = ?", lineUserId, botId).Updates(dest).Error
	if err != nil {
		tx.Rollback()
		logger.Error(ctx, "failed to unfollow line user", zap.String("err", err.Error()), zap.String("lineUserId", lineUserId))
		return err
	}
	// 同步至es
	doc := map[string]interface{}{
		"follow_status": code.LineUnFollowed,
		"updated_at":    current.Unix(),
	}
	if err = elasticsearch.DefaultLineEsSvc.UpdateLine(ctx, lineUserId, doc); err != nil {
		tx.Rollback()
		return err
	}
	// 提交事务
	if err = tx.Commit().Error; err != nil {
		logger.Error(ctx, "[UnfollowLineUser] transaction commit err", zap.String("err", err.Error()))
		return xerrors.New(err.Error(), code.DbError)
	}
	return nil
}

func (repo *LineInteractions) DelUser(ctx context.Context, project, lineUserId, botId string) error {
	// 将用户标记为删除
	current := time.Now().UTC()
	tx := repo.db.WithContext(ctx).Begin()
	dest := map[string]interface{}{
		"is_deleted": code.DscDeleted,
		"updated_at": current,
	}
	err := tx.Table(models.GetFpLineUserTableName()).Where("line_user_id = ? AND bot_id = ?", lineUserId, botId).Updates(dest).Error
	if err != nil {
		tx.Rollback()
		logger.Error(ctx, "failed to delete line user", zap.String("err", err.Error()))
		return err
	}
	doc := map[string]interface{}{
		"is_deleted": code.DscDeleted,
		"updated_at": current.Unix(),
	}
	// 删除关系配置表中的相关数据
	if err = tx.Table(models.GetFpLinePlayerMaintainConfigTableName()).Where("line_user_id=? AND project=?", lineUserId, project).Delete(&models.FpLinePlayerMaintainConfig{}).Error; err != nil {
		logger.Error(ctx, "failed to delete Line maintain config", zap.String("err", err.Error()))
		tx.Rollback()
	}
	// 同步至es
	if err = elasticsearch.DefaultLineEsSvc.UpdateLine(ctx, lineUserId, doc); err != nil {
		tx.Rollback()
		return err
	}
	// 提交事务
	if err = tx.Commit().Error; err != nil {
		logger.Error(ctx, "[DelLineUser] transaction commit err", zap.String("err", err.Error()))
		return xerrors.New(err.Error(), code.DbError)
	}
	return nil
}

func (repo *LineInteractions) SaveUserCommu(ctx context.Context, commu *models.FpLineCommu) error {
	tx := repo.db.WithContext(ctx).Begin()
	// 新增commu消息记录
	if err := tx.Table(models.GetFpLineCommuTableName()).Create(commu).Error; err != nil {
		logger.Error(ctx, "[SaveUserCommu] err", zap.String("err", err.Error()))
		tx.Rollback()
		return err
	}
	logger.Info(ctx, "Reached UpsertLineCommus")
	// 同步至es
	if err := elasticsearch.DefaultLineEsSvc.UpsertLineCommus(ctx, commu, true, ""); err != nil {
		tx.Rollback()
		return err
	}
	logger.Info(ctx, "Reached SaveUserCommu commit transaction")
	// 提交事务
	if err := tx.Commit().Error; err != nil {
		logger.Error(ctx, "[SaveUserCommu] transaction commit err", zap.String("err", err.Error()))
		return xerrors.New(err.Error(), code.DbError)
	}
	return nil
}

func (repo *LineInteractions) SaveBotCommu(ctx context.Context, commu *models.FpLineCommu, send *models.FpLineSend, opDetail *models.FpLineOperateLog) error {
	tx := repo.db.WithContext(ctx).Begin()
	// 新增commu消息记录
	if err := tx.Table(models.GetFpLineCommuTableName()).Create(commu).Error; err != nil {
		logger.Error(ctx, "[SaveBotCommu] err", zap.String("err", err.Error()))
		tx.Rollback()
		return err
	}
	// 新增客服line消息绑定记录
	if err := tx.Table(models.GetFpLineSendTableName()).Create(send).Error; err != nil {
		logger.Error(ctx, "[SaveBotCommu] err", zap.String("err", err.Error()))
		tx.Rollback()
		return err
	}
	// 同步至es
	if err := elasticsearch.DefaultLineEsSvc.UpsertLineCommus(ctx, commu, true, send.Operator); err != nil {
		tx.Rollback()
		return err
	}
	// 记录操作日志
	if err := tx.Table(models.GetFpLineOperateLogTableName()).Create(opDetail).Error; err != nil {
		tx.Rollback()
		logger.Error(ctx, "[SaveBotCommu] error add operate log", zap.String("err", err.Error()))
		return xerrors.New(err.Error(), code.DbError)
	}
	// 提交事务
	if err := tx.Commit().Error; err != nil {
		logger.Error(ctx, "[SaveBotCommu] transaction commit err", zap.String("err", err.Error()))
		return xerrors.New(err.Error(), code.DbError)
	}
	return nil
}

func (repo *LineInteractions) SaveBot(ctx context.Context, bot *models.FpLineBot) error {
	// 先检查缓存中的botId是否已存在
	if rds.Exists(ctx, bot.BotID) {
		return nil
	}
	// Bot表新增一条记录(只新增一次)
	// channel表更新bot_id数据，也只更新一次
	botInfo, err := repo.FetchBotById(ctx, bot.BotID)
	if err != nil {
		return err
	}
	// 已存在则跳过
	if botInfo.ID > 0 {
		// 缓存botId
		if e := rds.Set(ctx, bot.BotID, "exist", keys.ExpireOneWeek); e != nil {
			logger.Errorf(ctx, "set redis key:%v error:%v", bot.BotID, e)
		}
		return nil
	}
	// 不存在则新增
	current := time.Now().UTC()
	tx := repo.db.WithContext(ctx).Begin()
	// 新增
	if err = tx.Table(models.GetFpLineBotTableName()).Create(bot).Error; err != nil {
		logger.Error(ctx, "[SaveBot] err", zap.String("err", err.Error()))
		tx.Rollback()
		return err
	}
	dest := map[string]interface{}{
		"bot_id":     bot.BotID,
		"updated_at": current,
	}
	err = tx.Table(models.GetFpLineChannelTableName()).Where("channel_id = ?", bot.ChannelID).Updates(dest).Error
	if err != nil {
		tx.Rollback()
		logger.Error(ctx, "[Update Channel] bot id error", zap.String("err", err.Error()))
		return err
	}
	// 提交事务
	if err = tx.Commit().Error; err != nil {
		logger.Error(ctx, "[SaveBot] transaction commit err", zap.String("err", err.Error()))
		return xerrors.New(err.Error(), code.DbError)
	}
	// 缓存botId
	if e := rds.Set(ctx, bot.BotID, "exist", keys.ExpireOneWeek); e != nil {
		logger.Errorf(ctx, "set redis key:%v error:%v", bot.BotID, e)
	}
	return nil
}

// RevokeMessage EventTypeUnsend: 当用户撤回一条已经发送的消息时触发
func (repo *LineInteractions) RevokeMessage(ctx context.Context, messageId string) error {
	// 将该消息标记为已撤回
	current := time.Now().UTC()
	dest := map[string]interface{}{
		"revoke":     true,
		"updated_at": current,
	}
	err := repo.db.Table(models.GetFpLineCommuTableName()).Where("msg_id = ?", messageId).Updates(dest).Error
	if err != nil {
		logger.Error(ctx, "failed to mark message revoke", zap.String("err", err.Error()))
		return err
	}
	return nil
}

func (repo *LineInteractions) GetLineCommusHistory(ctx context.Context, req *pb.LineDialogueHistoryReq) (int64, []*models.FpLineCommu, error) {
	subQuery := repo.db.Table(models.GetFpLineCommuTableName()).
		Where("line_user_id = ? AND bot_id = ? AND channel_id = ?", req.LineUserId, req.BotId, req.ChannelId)
	// 向上翻页
	if req.Before {
		subQuery = repo.db.Table(models.GetFpLineCommuTableName()).
			Where("line_user_id = ? AND bot_id = ? AND channel_id = ? AND tick_time < ?", req.LineUserId, req.BotId, req.ChannelId, req.TickTime)
	}
	var total int64
	query := subQuery
	if err := subQuery.Count(&total).Error; err != nil {
		logger.Error(ctx, "get line commus error", zap.String("err", err.Error()))
		return 0, nil, err
	}
	dest := []*models.FpLineCommu{}
	if err := query.Order("tick_time").Scopes(database.Paginate(&req.Page, &req.PageSize)).Find(&dest).Error; err != nil {
		logger.Error(ctx, "get line commu list err", zap.String("err", err.Error()))
		return 0, nil, xerrors.New(err.Error(), code.DbError)
	}
	return total, dest, nil
}

func (repo *LineInteractions) GetSendersByMsdId(ctx context.Context, msgIds []string) (map[string]string, error) {
	var sends []*models.FpLineSend
	err := repo.db.WithContext(ctx).Model(&models.FpLineSend{}).
		Select("msg_id", "operator").
		Where("msg_id IN (?)", msgIds).Find(&sends).Error
	if err != nil {
		return map[string]string{}, err
	}
	var sendMap = make(map[string]string, len(sends))
	for _, v := range sends {
		sendMap[v.MsgID] = v.Operator
	}
	return sendMap, nil
}

func (repo *LineInteractions) GetPlayerDisplayNameByLineUserId(ctx context.Context, lineUserIds []string) (map[string]string, error) {
	var users []*models.FpLineUser
	err := repo.db.WithContext(ctx).Model(&models.FpLineUser{}).
		Select("line_user_id", "display_name").
		Where("line_user_id IN (?)", lineUserIds).Find(&users).Error
	if err != nil {
		return map[string]string{}, err
	}
	var userMap = make(map[string]string, len(users))
	for _, v := range users {
		userMap[v.LineUserID] = v.DisplayName
	}
	return userMap, nil
}

func (repo *LineInteractions) GetLineRefreshCommus(ctx context.Context, req *pb.LineDialogFreshReq) ([]*models.FpLineCommu, error) {
	dest := []*models.FpLineCommu{}
	if err := repo.db.Table(models.GetFpLineCommuTableName()).
		Where("line_user_id = ? AND bot_id = ? AND channel_id = ? AND tick_time > ?", req.LineUserId, req.BotId, req.ChannelId, req.TickTime).
		Order("tick_time").Find(&dest).Error; err != nil {
		logger.Error(ctx, "get line refresh commus err", zap.String("err", err.Error()))
		return nil, xerrors.New(err.Error(), code.DbError)
	}
	return dest, nil
}

func (repo *LineInteractions) FetchLineUserById(ctx context.Context, lineUserId, project string) (*models.FpLineUser, error) {
	user := &models.FpLineUser{}
	err := repo.db.Table(models.GetFpLineUserTableName()).Where("line_user_id=? AND project=?", lineUserId, project).First(&user).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		logger.Error(ctx, "failed to get line user by line_user_id", zap.String("err", err.Error()))
		return nil, err
	}
	return user, nil
}
