package persistence

import (
	"context"
	"errors"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"ops-ticket-api/internal/code"
	"ops-ticket-api/internal/framework/database"
	"ops-ticket-api/internal/framework/xerrors"
	"ops-ticket-api/models"
)

// ReplyTplLang 自动回复模版
type ReplyTplLang struct {
	db *gorm.DB
}

// NewReplyTplLang init
func NewReplyTplLang(ctx any, useNewDb bool) *ReplyTplLang {
	var db *gorm.DB
	if useNewDb {
		db = database.DbSession(ctx, database.Db())
	} else {
		db = database.DbSession(ctx, database.GetOldTicketDb())
	}
	return &ReplyTplLang{
		db: db,
	}
}

// TplInfoMultiLang 自动回复多语言模版信息
func (dto *ReplyTplLang) TplInfoMultiLang(ctx context.Context, tplId uint32, language string) (*models.FpOpsReplyTplLang, error) {
	tplInfo := &models.FpOpsReplyTplLang{}
	where := map[string]interface{}{
		"reply_tpl_id": tplId,
		"lang":         language,
	}
	if err := dto.db.Model(&models.FpOpsReplyTplLang{}).Where(where).Take(&tplInfo).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			if language == "en" {
				return nil, xerrors.New(code.StatusText(code.NotFound), code.NotFound)
			}
			logger.Info(ctx, "reply tpl lang info not found, start get en info",
				zap.Uint32("tplId", tplId),
				zap.String("lang", language))
			return dto.TplInfoMultiLang(ctx, tplId, "en")
		}
		logger.Error(ctx, "get reply tpl lang info err",
			zap.Uint32("tplId", tplId),
			zap.String("lang", language),
			zap.String("err", err.Error()))
		return nil, xerrors.New(code.StatusText(code.DbError), code.DbError)
	}
	return tplInfo, nil
}
