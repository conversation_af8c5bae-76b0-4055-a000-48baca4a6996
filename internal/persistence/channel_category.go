package persistence

import (
	"context"
	"errors"
	"github.com/labstack/echo/v4"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"ops-ticket-api/internal/code"
	"ops-ticket-api/internal/framework/database"
	"ops-ticket-api/internal/framework/xerrors"
	"ops-ticket-api/models"
	"ops-ticket-api/proto/pb"
	"sync"
	"time"
)

var (
	channelCatRepo *ChannelCat
	OnceChannelCat sync.Once
)

// ChannelCat 问题分类配置
type ChannelCat struct {
	db *gorm.DB
}

// NewChannelCat init
func NewChannelCat() *ChannelCat {
	OnceChannelCat.Do(func() {
		channelCatRepo = &ChannelCat{
			db: database.Db(),
		}
	})
	return channelCatRepo
}

// CatChannelTree 问题分类配置(树)列表接口
func (dto *ChannelCat) CatChannelTree(ctx context.Context, project string, catType uint32) ([]*pb.ChannelCatItems, error) {
	dest := make([]*pb.ChannelCatItems, 0)
	field := "cat_id, category, one_level, second_level, level"
	cond := map[string]interface{}{
		"project":    project,
		"cat_type":   uint16(catType),
		"is_deleted": uint16(code.StatusFalse),
	}
	query := dto.db.WithContext(ctx).Table(models.FpOpsChannelCategory{}.TableName()).Where(cond)
	if err := query.Select(field).Order("cat_id asc").Find(&dest).Error; err != nil {
		logger.Error(ctx, "get channel category list tree count err", zap.String("err", err.Error()))
		return nil, xerrors.New(err.Error(), code.DbError)
	}

	return dest, nil
}

// CatChannelAdd 添加问题分类
func (dto *ChannelCat) CatChannelAdd(ctx context.Context, cat *models.FpOpsChannelCategory) error {
	if err := dto.db.WithContext(ctx).Create(cat).Error; err != nil {
		logger.Error(ctx, "cat create err", zap.String("category", cat.Category), zap.String("err", err.Error()))
		return err
	}
	return nil
}

// Detail 获取问题分类信息
func (dto *ChannelCat) Detail(ctx echo.Context, catId uint32) (*models.FpOpsChannelCategory, error) {
	dest := &models.FpOpsChannelCategory{}
	if err := dto.db.WithContext(ctx.Request().Context()).Model(&models.FpOpsChannelCategory{}).Where("cat_id=?", catId).Take(dest).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, xerrors.New(code.StatusText(code.NotFound), code.NotFound)
		}
		logger.Error(ctx.Request().Context(), "get cat info err", zap.Uint32("catId", catId), zap.String("err", err.Error()))
		return nil, xerrors.New(code.StatusText(code.DbError), code.DbError)
	}
	return dest, nil
}

// CatChannelSave 更新问题分类
func (dto *ChannelCat) CatChannelSave(ctx context.Context, catId uint32, cat map[string]interface{}) error {
	if err := dto.db.Model(&models.FpOpsChannelCategory{}).Where("cat_id = ?", catId).Updates(cat).Error; err != nil {
		logger.Error(ctx, "cat save err", zap.Uint32("catId", catId), zap.String("err", err.Error()))
		return err
	}
	return nil
}

// CatChannelDel 问题分类删除
func (dto *ChannelCat) CatChannelDel(ctx context.Context, catId uint32, operator string) error {
	var count int64
	query := dto.db.WithContext(ctx).Model(&models.FpOpsChannelCategory{})
	if err := query.Where("(one_level=? or second_level=?) AND is_deleted=?", catId, catId, code.StatusFalse).Count(&count).Error; err != nil {
		logger.Error(ctx, "get category list count err", zap.Uint32("catId", catId), zap.String("err", err.Error()))
		return err
	}
	if count > 0 {
		return xerrors.New("存在子类别,不能删除～", code.DataConflict)
	}
	col := map[string]interface{}{
		"is_deleted": code.StatusTrue,
		"operator":   operator,
		"updated_at": time.Now(),
	}
	if err := dto.db.WithContext(ctx).Model(&models.FpOpsChannelCategory{}).Where("cat_id=?", catId).Updates(col).Error; err != nil {
		logger.Error(ctx, "del cat info err", zap.Uint32("catId", catId), zap.String("err", err.Error()))
		return xerrors.New(err.Error(), code.DbError)
	}
	return nil
}
