package persistence

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/avast/retry-go"
	"github.com/labstack/echo/v4"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"gorm.io/gorm"
	"gorm.io/plugin/dbresolver"
	"ops-ticket-api/entity/stats"
	"ops-ticket-api/internal/code"
	"ops-ticket-api/internal/elasticsearch"
	"ops-ticket-api/internal/framework/database"
	"ops-ticket-api/models"
	"ops-ticket-api/proto/pb"
	"ops-ticket-api/utils"
	"strings"
	"time"
)

// DiscordInteract discord玩家交互
type DiscordInteract struct {
	db *gorm.DB
}

// NewDiscordInteract init
func NewDiscordInteract() *DiscordInteract {
	return &DiscordInteract{
		db: database.Db(),
	}
}

func (d *DiscordInteract) SyncHistoryLastReplyService() error {
	dest, err := d.FetchLastReplyMsg()
	if err != nil {
		return err
	}
	ctx := context.Background()
	for i := range dest {
		detail := dest[i]
		// 查询最近一次回复玩家的客服
		operator, _ := d.FetchOperatorByMsgId(detail.MsgID)
		if operator == "" {
			operator = "others"
		}
		// 查询玩家对应的channel_id
		channelIds := []string{}
		if err = d.db.Model(&models.FpDscUser{}).Where("dsc_user_id = ? AND project = ? AND priv_channel_id != ?", detail.DscUserID, detail.Project, "").Pluck("priv_channel_id", &channelIds).Error; err != nil {
			elog.Errorf("fetch channelIds err:%+v", err)
			continue
		}
		doc := map[string]interface{}{
			"last_reply_service": operator,
			"updated_at":         utils.NowTimestamp(),
		}
		for _, channelId := range channelIds {
			if err = elasticsearch.DefaultDscEsSvc.UpdateDsc(ctx, channelId, doc); err != nil {
				return err
			}
		}
	}
	return nil
}

func (d *DiscordInteract) FetchLastReplyMsg() ([]*models.FpDscDmCommu, error) {
	dest := []*models.FpDscDmCommu{}
	query := `
       SELECT t1.project AS project, t1.dsc_user_id AS dsc_user_id, t1.msg_id AS msg_id 
FROM fp_dsc_dm_commu t1
INNER JOIN (
    SELECT project, dsc_user_id, MAX(created_at) AS latest_created_at
    FROM fp_dsc_dm_commu
    WHERE from_user_id = bot_id
    GROUP BY project, dsc_user_id
) t2
ON t1.project = t2.project 
   AND t1.dsc_user_id = t2.dsc_user_id
   AND t1.created_at = t2.latest_created_at
WHERE t1.from_user_id = t1.bot_id
    `
	if err := d.db.Raw(query).Find(&dest).Error; err != nil {
		return dest, err
	}
	return dest, nil
}

func (d *DiscordInteract) SyncHistoryInteractDetailData() error {
	elog.Info("SyncHistoryInteractDetailData start")
	// 获取fp_dsc_dm_commu表中的历史交互数据，按照创建时间升序排序
	interactList, err := d.FetchHistoryInteractMsg()
	if err != nil {
		elog.Errorf("FetchHistoryInteractMsg err:%+v", err)
	}
	// playerMark标识玩家某日是否发送过消息
	playerMark := make(map[string]bool)
	// playerOperatorMark标识玩家某日给最后一个客服发送的消息
	playerOperatorMark := make(map[string]string)
	// 玩家给客服发消息，客服回复的情况
	for i := range interactList {
		// playerMark的key要加上interact_date
		msgDetail := interactList[i]
		interactDate := utils.TransferTimeToDate(msgDetail.CreatedAt)
		playerKey := fmt.Sprintf("%s@%s@%s", msgDetail.Project, msgDetail.DscUserID, interactDate)
		// 标记玩家发送的消息
		if msgDetail.FromUserID == msgDetail.DscUserID {
			playerMark[playerKey] = true
		}
		// 客服发送消息
		if msgDetail.FromUserID == msgDetail.BotID {
			operator, e := d.FetchOperatorByMsgId(msgDetail.MsgID)
			if e != nil {
				// 找不到的处理人的一律标记为others
				othersKey := fmt.Sprintf("%s@%s@%s@%s", interactDate, "others", msgDetail.DscUserID, msgDetail.Project)
				playerOperatorMark[playerKey] = othersKey
				elog.Errorf("FetchOperatorByMsgId err:%+v", e)
				continue
			}
			// 如果找到客服给玩家发消息的情况，则覆盖playerOperatorMark中与该玩家交互的客服，确保最后留下的一定是最后给这个玩家回复的客服
			operatorKey := fmt.Sprintf("%s@%s@%s@%s", interactDate, operator, msgDetail.DscUserID, msgDetail.Project)
			playerOperatorMark[playerKey] = operatorKey
		}
		// 如果一直是玩家发消息，没有客服回复，则标记为玩家与others(空客服)的交互
		if playerMark[playerKey] && playerOperatorMark[playerKey] == "" {
			othersKey := fmt.Sprintf("%s@%s@%s@%s", interactDate, "others", msgDetail.DscUserID, msgDetail.Project)
			playerOperatorMark[playerKey] = othersKey
		}
	}
	// 玩家没给客服发消息，只是客服给玩家发消息的情况
	for i := range interactList {
		// playerMark的key要加上interact_date
		msgDetail := interactList[i]
		interactDate := utils.TransferTimeToDate(msgDetail.CreatedAt)
		playerKey := fmt.Sprintf("%s@%s@%s", msgDetail.Project, msgDetail.DscUserID, interactDate)
		// 客服向玩家发送消息
		if msgDetail.FromUserID == msgDetail.BotID && !playerMark[playerKey] {
			operator, e := d.FetchOperatorByMsgId(msgDetail.MsgID)
			if e != nil {
				// 找不到处理人的，处理人一律标记为others
				othersKey := fmt.Sprintf("%s@%s@%s@%s", interactDate, "others", msgDetail.DscUserID, msgDetail.Project)
				playerOperatorMark[playerKey] = othersKey
				elog.Errorf("FetchOperatorByMsgId err:%+v", e)
				continue
			}
			// 如果有多个客服给玩家发消息的情况，则覆盖playerOperatorMark中与该玩家交互的客服，确保最后留下的一定是最后给这个玩家发消息的客服
			operatorKey := fmt.Sprintf("%s@%s@%s@%s", interactDate, operator, msgDetail.DscUserID, msgDetail.Project)
			playerOperatorMark[playerKey] = operatorKey
		}
	}
	insertSql := "INSERT INTO fp_dsc_interact_detail(project, interact_date, operator, dsc_user_id, nick_name, uid, account_id) VALUES(?,?,?,?,?,?,?) ON DUPLICATE KEY UPDATE operator = ?"
	for _, operatorKey := range playerOperatorMark {
		l := strings.Split(operatorKey, "@")
		interactDate, operator, dscUserId, project := l[0], l[1], l[2], l[3]
		if operator == "" {
			operator = "others"
		}
		// 查询玩家的nick_name和uid,account_id
		uid, accountId, nickName, e := d.FetchNickNameAndUidByDscUserId(project, dscUserId)
		if e != nil {
			elog.Errorf("FetchNickNameAndUidByDscUserId err:%+v", e)
		}
		if err = d.db.Exec(insertSql, project, interactDate, operator, dscUserId, nickName, uid, accountId, operator).Error; err != nil {
			elog.Errorf("insert fp_dsc_message_count_detail err:%+v", err)
			continue
		}
	}
	elog.Info("SyncHistoryInteractDetailData done")
	return nil
}

func (d *DiscordInteract) SyncYesterdayInteractDetailData() error {
	elog.Info("SyncYesterdayInteractDetailData start")
	// 获取fp_dsc_dm_commu表中的历史交互数据，按照创建时间升序排序
	interactList, err := d.FetchYesterdayInteractMsg()
	if err != nil {
		elog.Errorf("FetchYesterdayInteractMsg err:%+v", err)
	}
	// playerMark标识玩家某日是否发送过消息
	playerMark := make(map[string]bool)
	// playerOperatorMark标识玩家某日给最后一个客服发送的消息
	playerOperatorMark := make(map[string]string)
	// 玩家给客服发消息，客服回复的情况
	for i := range interactList {
		// playerMark的key要加上interact_date
		msgDetail := interactList[i]
		interactDate := utils.TransferTimeToDate(msgDetail.CreatedAt)
		playerKey := fmt.Sprintf("%s@%s@%s", msgDetail.Project, msgDetail.DscUserID, interactDate)
		// 标记玩家发送的消息
		if msgDetail.FromUserID == msgDetail.DscUserID {
			playerMark[playerKey] = true
		}
		// 客服发送消息
		if msgDetail.FromUserID == msgDetail.BotID {
			operator, e := d.FetchOperatorByMsgId(msgDetail.MsgID)
			if e != nil {
				// 找不到的处理人的一律标记为others
				othersKey := fmt.Sprintf("%s@%s@%s@%s", interactDate, "others", msgDetail.DscUserID, msgDetail.Project)
				playerOperatorMark[playerKey] = othersKey
				elog.Errorf("FetchOperatorByMsgId err:%+v", e)
				continue
			}
			// 如果找到客服给玩家发消息的情况，则覆盖playerOperatorMark中与该玩家交互的客服，确保最后留下的一定是最后给这个玩家回复的客服
			operatorKey := fmt.Sprintf("%s@%s@%s@%s", interactDate, operator, msgDetail.DscUserID, msgDetail.Project)
			playerOperatorMark[playerKey] = operatorKey
		}
		// 如果一直是玩家发消息，没有客服回复，则标记为玩家与others(空客服)的交互
		if playerMark[playerKey] && playerOperatorMark[playerKey] == "" {
			othersKey := fmt.Sprintf("%s@%s@%s@%s", interactDate, "others", msgDetail.DscUserID, msgDetail.Project)
			playerOperatorMark[playerKey] = othersKey
		}
	}
	// 玩家没给客服发消息，只是客服给玩家发消息的情况
	for i := range interactList {
		// playerMark的key要加上interact_date
		msgDetail := interactList[i]
		interactDate := utils.TransferTimeToDate(msgDetail.CreatedAt)
		playerKey := fmt.Sprintf("%s@%s@%s", msgDetail.Project, msgDetail.DscUserID, interactDate)
		// 客服向玩家发送消息
		if msgDetail.FromUserID == msgDetail.BotID && !playerMark[playerKey] {
			operator, e := d.FetchOperatorByMsgId(msgDetail.MsgID)
			if e != nil {
				// 找不到处理人的，处理人一律标记为others
				othersKey := fmt.Sprintf("%s@%s@%s@%s", interactDate, "others", msgDetail.DscUserID, msgDetail.Project)
				playerOperatorMark[playerKey] = othersKey
				elog.Errorf("FetchOperatorByMsgId err:%+v", e)
				continue
			}
			// 如果有多个客服给玩家发消息的情况，则覆盖playerOperatorMark中与该玩家交互的客服，确保最后留下的一定是最后给这个玩家发消息的客服
			operatorKey := fmt.Sprintf("%s@%s@%s@%s", interactDate, operator, msgDetail.DscUserID, msgDetail.Project)
			playerOperatorMark[playerKey] = operatorKey
		}
	}
	insertSql := "INSERT INTO fp_dsc_interact_detail(project, interact_date, operator, dsc_user_id, nick_name, uid, account_id) VALUES(?,?,?,?,?,?,?) ON DUPLICATE KEY UPDATE operator = ?"
	for _, operatorKey := range playerOperatorMark {
		l := strings.Split(operatorKey, "@")
		interactDate, operator, dscUserId, project := l[0], l[1], l[2], l[3]
		if operator == "" {
			operator = "others"
		}
		// 查询玩家的nick_name和uid,account_id
		uid, accountId, nickName, e := d.FetchNickNameAndUidByDscUserId(project, dscUserId)
		if e != nil {
			elog.Errorf("FetchNickNameAndUidByDscUserId err:%+v", e)
		}
		if err = d.db.Exec(insertSql, project, interactDate, operator, dscUserId, nickName, uid, accountId, operator).Error; err != nil {
			elog.Errorf("insert fp_dsc_message_count_detail err:%+v", err)
			continue
		}
	}
	elog.Info("SyncYesterdayInteractDetailData done")
	return nil
}

func (d *DiscordInteract) SyncHistoryMsgCountDetail() error {
	elog.Info("SyncHistoryMsgCountDetail start")
	// 先计算并插入客服发送给玩家的信息量
	if err := d.FetchHistoryOperatorInteractMsg(); err != nil {
		elog.Errorf("FetchHistoryOperatorInteractMsg err:%+v", err)
		return err
	}
	// 获取fp_dsc_dm_commu表中昨天的交互数据，按照创建时间升序排序
	// for循环遍历每一条数据，如果是客服发的，则记录客服给玩家发了1条消息；如果是玩家发的，则继续遍历，直到找到
	// 客服给该玩家发的第一条消息为止
	interactList, err := d.FetchHistoryInteractMsg()
	if err != nil {
		elog.Errorf("FetchHistoryInteractMsg err:%+v", err)
	}
	// playerMark标识玩家某日发送的消息数
	playerMark := make(map[string]int)
	// playerOperatorMark标识玩家某日给某个客服发送的消息数
	playerOperatorMark := make(map[string]int)
	for i := range interactList {
		// playerMark的key要加上interact_date
		msgDetail := interactList[i]
		interactDate := utils.TransferTimeToDate(msgDetail.CreatedAt)
		// 标记玩家发送的消息条数
		if msgDetail.FromUserID == msgDetail.DscUserID {
			playerMark[fmt.Sprintf("%s@%s@%s", msgDetail.Project, msgDetail.DscUserID, interactDate)] += 1
		}
		// 客服发送消息
		if msgDetail.FromUserID == msgDetail.BotID && playerMark[fmt.Sprintf("%s@%s@%s", msgDetail.Project, msgDetail.DscUserID, interactDate)] >= 1 {
			operator, e := d.FetchOperatorByMsgId(msgDetail.MsgID)
			if e != nil {
				// 找不到处理人的，处理人一律标记为others
				key := fmt.Sprintf("%s@%s@%s@%s", interactDate, "others", msgDetail.DscUserID, msgDetail.Project)
				playerOperatorMark[key] = playerMark[fmt.Sprintf("%s@%s@%s", msgDetail.Project, msgDetail.DscUserID, interactDate)]
				playerMark[fmt.Sprintf("%s@%s@%s", msgDetail.Project, msgDetail.DscUserID, interactDate)] = 0
				elog.Errorf("FetchOperatorByMsgId err:%+v", e)
				continue
			}
			key := fmt.Sprintf("%s@%s@%s@%s", interactDate, operator, msgDetail.DscUserID, msgDetail.Project)
			if cnt, ok := playerOperatorMark[key]; ok {
				playerOperatorMark[key] = cnt + playerMark[fmt.Sprintf("%s@%s@%s", msgDetail.Project, msgDetail.DscUserID, interactDate)]
			} else {
				playerOperatorMark[key] = playerMark[fmt.Sprintf("%s@%s@%s", msgDetail.Project, msgDetail.DscUserID, interactDate)]
			}
			playerMark[fmt.Sprintf("%s@%s@%s", msgDetail.Project, msgDetail.DscUserID, interactDate)] = 0
			// 有处理人则删掉处理人为others的记录
			othersKey := fmt.Sprintf("%s@%s@%s@%s", interactDate, "others", msgDetail.DscUserID, msgDetail.Project)
			if _, ok := playerOperatorMark[othersKey]; ok {
				delete(playerOperatorMark, othersKey)
			}
		}
		// 如果一直是玩家发消息，没有客服回复，则标记为玩家向others发送消息
		if playerMark[fmt.Sprintf("%s@%s@%s", msgDetail.Project, msgDetail.DscUserID, interactDate)] >= 1 {
			key := fmt.Sprintf("%s@%s@%s@%s", interactDate, "others", msgDetail.DscUserID, msgDetail.Project)
			playerOperatorMark[key] = playerMark[fmt.Sprintf("%s@%s@%s", msgDetail.Project, msgDetail.DscUserID, interactDate)]
		}
	}
	insertSql := "INSERT INTO fp_dsc_message_count_detail(project, interact_date, dsc_user_id, player_message_count, operator, service_message_count, nick_name, uid, account_id) VALUES(?,?,?,?,?,?,?,?,?) ON DUPLICATE KEY UPDATE player_message_count = ?"
	updateSql := "UPDATE fp_dsc_message_count_detail SET player_message_count = ? WHERE interact_date = ? AND operator = ? AND dsc_user_id = ? AND project = ?"
	for key, playerMessageCnt := range playerOperatorMark {
		l := strings.Split(key, "@")
		interactDate, operator, dscUserId, project := l[0], l[1], l[2], l[3]
		if operator == "others" {
			// 查询玩家的nick_name和uid,account_id
			uid, accountId, nickName, e := d.FetchNickNameAndUidByDscUserId(project, dscUserId)
			if e != nil {
				elog.Errorf("FetchNickNameAndUidByDscUserId err:%+v", e)
			}
			if err = d.db.Exec(insertSql, project, interactDate, dscUserId, playerMessageCnt, operator, 0, nickName, uid, accountId, playerMessageCnt).Error; err != nil {
				elog.Errorf("insert fp_dsc_message_count_detail err:%+v", err)
				continue
			}
		} else {
			if err = d.db.Exec(updateSql, playerMessageCnt, interactDate, operator, dscUserId, project).Error; err != nil {
				elog.Errorf("update fp_dsc_message_count_detail err:%+v", err)
				continue
			}
		}
	}
	elog.Info("SyncHistoryMsgCountDetail done")
	return nil
}

func (d *DiscordInteract) SyncYesterdayMsgCountDetail() error {
	elog.Info("SyncYesterdayMsgCountDetail start")
	// 先计算并插入客服发送给玩家的信息量
	if err := d.FetchYesterdayOperatorInteractMsg(); err != nil {
		elog.Errorf("FetchYesterdayOperatorInteractMsg err:%+v", err)
		return err
	}
	// 获取fp_dsc_dm_commu表中昨天的交互数据，按照创建时间升序排序
	// for循环遍历每一条数据，如果是客服发的，则记录客服给玩家发了1条消息；如果是玩家发的，则继续遍历，直到找到
	// 客服给该玩家发的第一条消息为止
	interactList, err := d.FetchYesterdayInteractMsg()
	if err != nil {
		elog.Errorf("FetchYesterdayInteractMsg err:%+v", err)
	}
	playerMark := make(map[string]int)
	playerOperatorMark := make(map[string]int)
	for i := range interactList {
		// playerMark的key要加上interact_date
		msgDetail := interactList[i]
		interactDate := utils.TransferTimeToDate(msgDetail.CreatedAt)
		// 标记玩家发送的消息条数
		if msgDetail.FromUserID == msgDetail.DscUserID {
			playerMark[fmt.Sprintf("%s@%s@%s", msgDetail.Project, msgDetail.DscUserID, interactDate)] += 1
		}
		// 客服发送消息
		if msgDetail.FromUserID == msgDetail.BotID && playerMark[fmt.Sprintf("%s@%s@%s", msgDetail.Project, msgDetail.DscUserID, interactDate)] >= 1 {
			operator, e := d.FetchOperatorByMsgId(msgDetail.MsgID)
			if e != nil {
				// 找不到处理人的，处理人一律标记为others
				key := fmt.Sprintf("%s@%s@%s@%s", interactDate, "others", msgDetail.DscUserID, msgDetail.Project)
				playerOperatorMark[key] = playerMark[fmt.Sprintf("%s@%s@%s", msgDetail.Project, msgDetail.DscUserID, interactDate)]
				playerMark[fmt.Sprintf("%s@%s@%s", msgDetail.Project, msgDetail.DscUserID, interactDate)] = 0
				elog.Errorf("FetchOperatorByMsgId err:%+v", e)
				continue
			}
			key := fmt.Sprintf("%s@%s@%s@%s", interactDate, operator, msgDetail.DscUserID, msgDetail.Project)
			if cnt, ok := playerOperatorMark[key]; ok {
				playerOperatorMark[key] = cnt + playerMark[fmt.Sprintf("%s@%s@%s", msgDetail.Project, msgDetail.DscUserID, interactDate)]
			} else {
				playerOperatorMark[key] = playerMark[fmt.Sprintf("%s@%s@%s", msgDetail.Project, msgDetail.DscUserID, interactDate)]
			}
			playerMark[fmt.Sprintf("%s@%s@%s", msgDetail.Project, msgDetail.DscUserID, interactDate)] = 0
			// 有处理人则删掉处理人为others的记录
			othersKey := fmt.Sprintf("%s@%s@%s@%s", interactDate, "others", msgDetail.DscUserID, msgDetail.Project)
			if _, ok := playerOperatorMark[othersKey]; ok {
				delete(playerOperatorMark, othersKey)
			}
		}
		// 如果一直是玩家发消息，没有客服回复，则标记为玩家向others发送消息
		if playerMark[fmt.Sprintf("%s@%s@%s", msgDetail.Project, msgDetail.DscUserID, interactDate)] >= 1 {
			key := fmt.Sprintf("%s@%s@%s@%s", interactDate, "others", msgDetail.DscUserID, msgDetail.Project)
			playerOperatorMark[key] = playerMark[fmt.Sprintf("%s@%s@%s", msgDetail.Project, msgDetail.DscUserID, interactDate)]
		}
	}
	insertSql := "INSERT INTO fp_dsc_message_count_detail(project, interact_date, dsc_user_id, player_message_count, operator, service_message_count, nick_name, uid, account_id) VALUES(?,?,?,?,?,?,?,?,?) ON DUPLICATE KEY UPDATE player_message_count = ?"
	updateSql := "UPDATE fp_dsc_message_count_detail SET player_message_count = ? WHERE interact_date = ? AND operator = ? AND dsc_user_id = ? AND project = ?"
	for key, playerMessageCnt := range playerOperatorMark {
		l := strings.Split(key, "@")
		interactDate, operator, dscUserId, project := l[0], l[1], l[2], l[3]
		if operator == "others" {
			// 查询玩家的nick_name和uid,account_id
			uid, accountId, nickName, e := d.FetchNickNameAndUidByDscUserId(project, dscUserId)
			if e != nil {
				elog.Errorf("FetchNickNameAndUidByDscUserId err:%+v", e)
			}
			if err = d.db.Exec(insertSql, project, interactDate, dscUserId, playerMessageCnt, operator, 0, nickName, uid, accountId, playerMessageCnt).Error; err != nil {
				elog.Errorf("insert fp_dsc_message_count_detail err:%+v", err)
				continue
			}
		} else {
			if err = d.db.Exec(updateSql, playerMessageCnt, interactDate, operator, dscUserId, project).Error; err != nil {
				elog.Errorf("update fp_dsc_message_count_detail err:%+v", err)
				continue
			}
		}
	}
	elog.Info("SyncYesterdayMsgCountDetail done")
	return nil
}

func (d *DiscordInteract) SyncMaintainConfigToInteractStats() error {
	updateInteractSql := "UPDATE fp_dsc_interact_detail a INNER JOIN fp_dsc_player_maintain_config b ON a.dsc_user_id = b.dsc_user_id AND a.project = b.game_project SET a.uid = b.uid, a.account_id = b.account_id WHERE (a.uid = 0 AND a.account_id = '')"
	if err := d.db.Exec(updateInteractSql).Error; err != nil {
		return err
	}
	updateMessageCountSql := "UPDATE fp_dsc_message_count_detail a INNER JOIN fp_dsc_player_maintain_config b ON a.dsc_user_id = b.dsc_user_id AND a.project = b.game_project SET a.uid = b.uid, a.account_id = b.account_id WHERE (a.uid = 0 AND a.account_id = '')"
	return d.db.Exec(updateMessageCountSql).Error
}

func (d *DiscordInteract) ProcessorDateStats(req *pb.DiscordPlayerInteractStatsReq) ([]*stats.OperatorDateCountDetail, error) {
	dest := []*stats.OperatorDateCountDetail{}
	query := d.db.Table(models.GetFpDscInteractDetailTableName()).Select("interact_date,operator,COUNT(id) AS operator_date_count")
	if len(req.Project) > 0 {
		query = query.Where("project IN (?)", req.Project)
	}
	if len(req.Date) > 0 {
		query = query.Where("interact_date BETWEEN ? AND ?", req.Date[0], req.Date[1])
	}
	if len(req.Operator) > 0 {
		query = query.Where("operator IN (?)", req.Operator)
	}
	if err := query.Group("interact_date,operator").Order("interact_date DESC").Find(&dest).Error; err != nil {
		return dest, err
	}
	return dest, nil
}

func (d *DiscordInteract) ProcessorStats(req *pb.DiscordPlayerInteractStatsReq) ([]*stats.OperatorCountDetail, error) {
	dest := []*stats.OperatorCountDetail{}
	query := d.db.Table(models.GetFpDscInteractDetailTableName()).Select("operator,COUNT(id) AS operator_count")
	if len(req.Project) > 0 {
		query = query.Where("project IN (?)", req.Project)
	}
	if len(req.Date) > 0 {
		query = query.Where("interact_date BETWEEN ? AND ?", req.Date[0], req.Date[1])
	}
	if len(req.Operator) > 0 {
		query = query.Where("operator IN (?)", req.Operator)
	}
	if err := query.Group("operator").Find(&dest).Error; err != nil {
		return dest, err
	}
	return dest, nil
}

func (d *DiscordInteract) InteractDateStats(req *pb.DiscordPlayerInteractStatsReq) ([]*stats.DateCountDetail, error) {
	dest := []*stats.DateCountDetail{}
	query := d.db.Table(models.GetFpDscInteractDetailTableName()).Select("interact_date,COUNT(id) AS date_count")
	if len(req.Project) > 0 {
		query = query.Where("project IN (?)", req.Project)
	}
	if len(req.Date) > 0 {
		query = query.Where("interact_date BETWEEN ? AND ?", req.Date[0], req.Date[1])
	}
	if len(req.Operator) > 0 {
		query = query.Where("operator IN (?)", req.Operator)
	}
	if err := query.Group("interact_date").Order("interact_date DESC").Find(&dest).Error; err != nil {
		return dest, err
	}
	return dest, nil
}

func (d *DiscordInteract) InteractSumStats(req *pb.DiscordPlayerInteractStatsReq) (int64, error) {
	var sumCount int64
	query := d.db.Table(models.GetFpDscInteractDetailTableName()).Select("COUNT(id) AS sum_count")
	if len(req.Project) > 0 {
		query = query.Where("project IN (?)", req.Project)
	}
	if len(req.Date) > 0 {
		query = query.Where("interact_date BETWEEN ? AND ?", req.Date[0], req.Date[1])
	}
	if len(req.Operator) > 0 {
		query = query.Where("operator IN (?)", req.Operator)
	}
	if err := query.Scan(&sumCount).Error; err != nil {
		return 0, err
	}
	return sumCount, nil
}

func (d *DiscordInteract) InteractDetails(req *pb.DiscordPlayerInteractStatsReq) ([]*models.FpDscInteractDetail, error) {
	dest := []*models.FpDscInteractDetail{}
	query := d.db.Table(models.GetFpDscInteractDetailTableName()).Select("*")
	if len(req.Project) > 0 {
		query = query.Where("project IN (?)", req.Project)
	}
	if len(req.Date) > 0 {
		query = query.Where("interact_date BETWEEN ? AND ?", req.Date[0], req.Date[1])
	}
	if len(req.Operator) > 0 {
		query = query.Where("operator IN (?)", req.Operator)
	}
	if err := query.Order("interact_date DESC").Find(&dest).Error; err != nil {
		return dest, err
	}
	return dest, nil
}

func (d *DiscordInteract) DateMessageCountStats(req *pb.DiscordMessageCountReq) ([]*pb.DiscordMessageCountResp_MessageCountDetail, error) {
	dest := []*pb.DiscordMessageCountResp_MessageCountDetail{}
	query := d.db.Table(models.GetFpDscMessageCountDetailTableName()).Select("interact_date AS row_name,SUM(player_message_count) AS player_message_count, SUM(service_message_count) AS service_message_count")
	if len(req.Project) > 0 {
		query = query.Where("project IN (?)", req.Project)
	}
	if len(req.Date) > 0 {
		query = query.Where("interact_date BETWEEN ? AND ?", req.Date[0], req.Date[1])
	}
	if len(req.Operator) > 0 {
		query = query.Where("operator IN (?)", req.Operator)
	}
	if len(req.Uid) > 0 {
		query = query.Where("uid IN (?)", req.Uid)
	}
	if err := query.Group("interact_date").Order("interact_date DESC").Find(&dest).Error; err != nil {
		return dest, err
	}
	return dest, nil
}

func (d *DiscordInteract) OperatorMessageCountStats(req *pb.DiscordMessageCountReq) ([]*pb.DiscordMessageCountResp_MessageCountDetail, error) {
	dest := []*pb.DiscordMessageCountResp_MessageCountDetail{}
	query := d.db.Table(models.GetFpDscMessageCountDetailTableName()).Select("operator AS row_name,SUM(player_message_count) AS player_message_count, SUM(service_message_count) AS service_message_count")
	if len(req.Project) > 0 {
		query = query.Where("project IN (?)", req.Project)
	}
	if len(req.Date) > 0 {
		query = query.Where("interact_date BETWEEN ? AND ?", req.Date[0], req.Date[1])
	}
	if len(req.Operator) > 0 {
		query = query.Where("operator IN (?)", req.Operator)
	}
	if len(req.Uid) > 0 {
		query = query.Where("uid IN (?)", req.Uid)
	}
	if err := query.Group("operator").Find(&dest).Error; err != nil {
		return dest, err
	}
	return dest, nil
}

func (d *DiscordInteract) SumMessageCountStats(req *pb.DiscordMessageCountReq) (*models.SumMessageCountStats, error) {
	var dest *models.SumMessageCountStats
	query := d.db.Table(models.GetFpDscMessageCountDetailTableName()).Select("SUM(player_message_count) AS player_message_count, SUM(service_message_count) AS service_message_count")
	if len(req.Project) > 0 {
		query = query.Where("project IN (?)", req.Project)
	}
	if len(req.Date) > 0 {
		query = query.Where("interact_date BETWEEN ? AND ?", req.Date[0], req.Date[1])
	}
	if len(req.Operator) > 0 {
		query = query.Where("operator IN (?)", req.Operator)
	}
	if len(req.Uid) > 0 {
		query = query.Where("uid IN (?)", req.Uid)
	}
	if err := query.Find(&dest).Error; err != nil {
		return nil, err
	}
	return dest, nil
}

func (d *DiscordInteract) MessageCountDetails(req *pb.DiscordMessageCountReq) ([]*models.FpDscMessageCountDetail, error) {
	dest := []*models.FpDscMessageCountDetail{}
	query := d.db.Table(models.GetFpDscMessageCountDetailTableName()).Select("*")
	if len(req.Project) > 0 {
		query = query.Where("project IN (?)", req.Project)
	}
	if len(req.Date) > 0 {
		query = query.Where("interact_date BETWEEN ? AND ?", req.Date[0], req.Date[1])
	}
	if len(req.Operator) > 0 {
		query = query.Where("operator IN (?)", req.Operator)
	}
	if len(req.Uid) > 0 {
		query = query.Where("uid IN (?)", req.Uid)
	}
	if err := query.Order("interact_date DESC").Find(&dest).Error; err != nil {
		return dest, err
	}
	return dest, nil
}

func (d *DiscordInteract) FetchHistoryInteractMsg() ([]*models.FpDscDmCommu, error) {
	dest := []*models.FpDscDmCommu{}
	if err := d.db.Model(&models.FpDscDmCommu{}).Select("*").Where("DATE(created_at) <= ?", "2024-08-15").Order("created_at").Find(&dest).Error; err != nil {
		return dest, err
	}
	return dest, nil
}

func (d *DiscordInteract) FetchYesterdayInteractMsg() ([]*models.FpDscDmCommu, error) {
	dest := []*models.FpDscDmCommu{}
	yesterdayStart, yesterdayEnd := utils.GetYesterdayStartAndEnd()
	if err := d.db.Model(&models.FpDscDmCommu{}).Select("*").Where("created_at BETWEEN ? AND ?", yesterdayStart, yesterdayEnd).Order("created_at").Find(&dest).Error; err != nil {
		return dest, err
	}
	return dest, nil
}

func (d *DiscordInteract) FetchHistoryOperatorInteractMsg() error {
	dest := []*models.FpDscMessageCountDetail{}
	query := "SELECT COALESCE(s.operator, 'others') AS operator, c.dsc_user_id AS dsc_user_id, DATE(c.created_at) AS interact_date, c.project AS project, COUNT(c.msg_id) AS service_message_count FROM fp_dsc_dm_commu c LEFT JOIN fp_dsc_dm_send s ON c.msg_id = s.msg_id WHERE c.from_user_id = c.bot_id AND DATE(c.created_at) <= '2024-08-15' GROUP BY s.operator, c.dsc_user_id, interact_date, c.project ORDER BY interact_date"
	if err := d.db.Raw(query).Scan(&dest).Error; err != nil {
		return err
	}
	insertSql := "INSERT INTO fp_dsc_message_count_detail(project, interact_date, dsc_user_id, nick_name, uid, account_id, player_message_count, operator, service_message_count) VALUES(?,?,?,?,?,?,?,?,?)"
	for i := range dest {
		detail := dest[i]
		interactDate := strings.Split(detail.InteractDate, "T")[0]
		// 查询玩家的nick_name和uid,account_id
		uid, accountId, nickName, e := d.FetchNickNameAndUidByDscUserId(detail.Project, detail.DscUserID)
		if e != nil {
			elog.Errorf("FetchNickNameAndUidByDscUserId err:%+v", e)
		}
		if err := d.db.Exec(insertSql, detail.Project, interactDate, detail.DscUserID, nickName, uid, accountId, 0, detail.Operator, detail.ServiceMessageCount).Error; err != nil {
			elog.Errorf("FetchHistoryOperatorInteractMsg insert err:%+v", err)
			continue
		}
	}
	elog.Info("FetchHistoryOperatorInteractMsg done")
	return nil
}

func (d *DiscordInteract) FetchYesterdayOperatorInteractMsg() error {
	dest := []*models.FpDscMessageCountDetail{}
	query := "SELECT COALESCE(s.operator, 'others') AS operator, c.dsc_user_id AS dsc_user_id, DATE(c.created_at) AS interact_date, c.project AS project, COUNT(c.msg_id) AS service_message_count FROM fp_dsc_dm_commu c LEFT JOIN fp_dsc_dm_send s ON c.msg_id = s.msg_id WHERE c.from_user_id = c.bot_id AND DATE(c.created_at) = CURDATE() - INTERVAL 1 DAY GROUP BY s.operator, c.dsc_user_id, interact_date, c.project ORDER BY interact_date"
	if err := d.db.Raw(query).Scan(&dest).Error; err != nil {
		return err
	}
	insertSql := "INSERT INTO fp_dsc_message_count_detail(project, interact_date, dsc_user_id, nick_name, uid, account_id, player_message_count, operator, service_message_count) VALUES(?,?,?,?,?,?,?,?,?)"
	for i := range dest {
		detail := dest[i]
		interactDate := strings.Split(detail.InteractDate, "T")[0]
		// 查询玩家的nick_name和uid,account_id
		uid, accountId, nickName, e := d.FetchNickNameAndUidByDscUserId(detail.Project, detail.DscUserID)
		if e != nil {
			elog.Errorf("FetchNickNameAndUidByDscUserId err:%+v", e)
		}
		if err := d.db.Exec(insertSql, detail.Project, interactDate, detail.DscUserID, nickName, uid, accountId, 0, detail.Operator, detail.ServiceMessageCount).Error; err != nil {
			elog.Errorf("FetchHistoryOperatorInteractMsg insert err:%+v", err)
			continue
		}
	}
	elog.Info("FetchYesterdayOperatorInteractMsg done")
	return nil
}

func (d *DiscordInteract) FetchOperatorByMsgId(msgId string) (string, error) {
	operator := ""
	if err := d.db.Clauses(dbresolver.Write).Model(&models.FpDscDmSend{}).Select("operator").Where("msg_id = ?", msgId).Scan(&operator).Error; err != nil {
		return operator, err
	}
	return operator, nil
}

func (d *DiscordInteract) FetchNickNameAndUidByDscUserId(project, dscUserId string) (uint64, string, string, error) {
	mc := &models.FpDscPlayerMaintainConfig{}
	if err := d.db.Table(models.GetFpDscPlayerMaintainConfigTableName()).Select("*").Where("dsc_user_id = ? AND game_project = ?", dscUserId, project).First(mc).Error; err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return 0, "", "", err
	}
	nickName := ""
	if err := d.db.Model(&models.FpDscUser{}).Select("COALESCE(global_name, user_name) AS nick_name").Where("dsc_user_id = ? AND project = ?", dscUserId, project).Scan(&nickName).Error; err != nil {
		return 0, "", "", err
	}
	return mc.UID, mc.AccountId, nickName, nil
}

// FetchUserInfoByUID 根据uid获取用户相关信息
func (d *DiscordInteract) FetchUserInfoByUID(uid int64, project string) (string, error) {
	mc := &models.FpDscPlayerMaintainConfig{}
	if err := d.db.Table(models.GetFpDscPlayerMaintainConfigTableName()).Select("*").Where("uid = ? AND game_project = ?", uint64(uid), project).First(mc).Error; err != nil {
		return "", err
	}
	//nickName := ""
	//if err := d.db.Model(&models.FpDscUser{}).Select("COALESCE(global_name, user_name) AS nick_name").Where("dsc_user_id = ? AND project = ?", mc.DscUserId, project).Scan(&nickName).Error; err != nil {
	//	return "", "", "", err
	//}
	return mc.AccountId, nil
}

// FetchUserInfoByUID 根据uid获取用户相关信息
func (d *DiscordInteract) FetchUserInfo(uid int64, fpid string, project string) (*models.FpDscPlayerMaintainConfig, error) {
	mc := &models.FpDscPlayerMaintainConfig{}
	if err := d.db.Table(models.GetFpDscPlayerMaintainConfigTableName()).Select("*").Where("uid=? and account_id = ? AND game_project = ?", uint64(uid), fpid, project).First(mc).Error; err != nil {
		return nil, err
	}
	return mc, nil
}

func (d *DiscordInteract) CheckReplyStatusByDscUserId(ctx echo.Context, channelId, dscUserId string, newReplyStatus uint32) (bool, error) {
	// 找到与该玩家相关的最新一条消息
	dest := &models.FpDscDmCommu{}
	if err := d.db.Clauses(dbresolver.Write).Model(&models.FpDscDmCommu{}).Where("dsc_user_id = ? AND channel_id =?", dscUserId, channelId).Order("created_at DESC").Take(dest).Error; err != nil {
		return false, err
	}
	//msgid如果在批量私信任务中 或 推送调查问卷，则不改变回复状态
	if isTask, checkErr := NewDscMessageTask().CheckHasMsgId(dest.MsgID); checkErr != nil {
		logger.Errorf(ctx.Request().Context(), "CheckHasMsgId err:%+v", checkErr)
		return false, checkErr
	} else if isTask == true { // 存在
		return false, nil
	}
	if ignore, checkErr := NewDscInteractions().CheckIsIgnoreMsgId(ctx.Request().Context(), dest.MsgID); checkErr != nil {
		if !errors.Is(checkErr, gorm.ErrRecordNotFound) {
			logger.Errorf(ctx.Request().Context(), "CheckIgnoreMsgId err:%+v", checkErr)
		}
	} else if ignore == true {
		return false, nil
	}

	// 如果该消息是客服发给玩家的，则表示是已回复，否则表示未回复
	realReplyStatus := pb.DscReplyTpDf_DscReplyTpDfUnReply
	if dest.FromUserID == dest.BotID {
		realReplyStatus = pb.DscReplyTpDf_DscReplyTpDfReplied
	}
	return newReplyStatus == uint32(realReplyStatus), nil
}

// SyncYesterdayDiscordReplyTimeDetail 查询出前一天的所有conversation明细后插入discord回复时间表，通过定时任务做增量更新。
func (d *DiscordInteract) SyncYesterdayDiscordReplyTimeDetail(tm time.Time) error {
	//获取当天的头和尾
	start, end := utils.GetWhichDayStartAndEnd(tm)
	//将昨天的数据删除
	delSql := fmt.Sprintf(`delete from fp_dsc_reply_time where reply_at BETWEEN ? AND ?`)
	fmt.Println("delSql:::::", delSql)
	if err := d.db.Exec(delSql, start, end).Error; err != nil {
		elog.Errorf("delete fp_dsc_reply_time err:%+v", err)
		return err
	}
	elog.Info("SyncYesterdayDiscordReplyTimeDetail start")
	//查出所有的当天涉及的dsc用户id
	users, err := d.FetchYesterdayAllUsers(start, end)
	if err != nil {
		elog.Errorf("FetchYesterdayAllUsers err:%+v", err)
		return err
	}
	for _, user := range users {
		//找到第一个统计的user
		firstUser, err, isExit := d.FetchFirstUser(user, start, end)
		if err != nil {
			elog.Errorf("FetchFirstUser err:%+v", err)
			return err
		}
		//1.第一天且只有bot  2.找到最后一个bot后，后面也没有user  这两种情况直接退出
		if isExit {
			continue
		}
		commus, err := d.FetchTodayCommus(user, firstUser.CreatedAt, end)
		if err != nil {
			elog.Errorf("FetchTodayCommus err:%+v", err)
			return err
		}
		//双指针插入
		var i, j int
		for i = 0; i < len(commus); i++ {
			//如果j + 1不是user，继续下一个i
			if commus[i].FromUserID != commus[i].DscUserID {
				continue
			}
			for j = i + 1; j < len(commus) && commus[j].FromUserID == commus[j].DscUserID; j++ {
			}

			if j >= len(commus) {
				break
			} else {
				userA, botA := commus[i], commus[j]
				//成功配对，执行插入
				insertSql := fmt.Sprintf(`INSERT INTO fp_dsc_reply_time(project,bot_id,dsc_user_id,operator,channel_id,reply_at,reply_time_seconds,day) VALUES(?,?,?,?,?,?,?,?)`)
				fmt.Println("insertSql:::::", insertSql)
				//获取客服
				operator, err := d.FetchOperatorByMsgId(botA.MsgID)
				if err != nil {
					elog.Errorf("FetchOperatorByBot err:%+v", err)
					return err
				}
				if operator == "" {
					i = j
					continue
				}
				//获取回复时间
				replyTimeSeconds := uint(botA.CreatedAt.Sub(userA.CreatedAt).Seconds())
				day := botA.CreatedAt.Format("2006-01-02")
				//插入
				if err := d.db.Exec(insertSql, userA.Project, botA.BotID, userA.DscUserID, operator, userA.ChannelID, botA.CreatedAt, replyTimeSeconds, day).Error; err != nil {
					elog.Errorf("insert fp_dsc_reply_time err:%+v", err)
					return err
				}
			}
			i = j
		}
	}
	return nil
}

func (d *DiscordInteract) FetchYesterdayAllUsers(start, end time.Time) (users []*models.FpDscDmCommu, err error) {
	if err = d.db.Model(&models.FpDscDmCommu{}).Select("dsc_user_id,channel_id").Where("created_at BETWEEN ? and ?", start, end).
		Group("channel_id").Find(&users).Error; err != nil {
		return nil, err
	}
	return
}

// FetchFirstUser 获取小于tm的最后一个bot后第一个user
func (d *DiscordInteract) FetchFirstUser(user *models.FpDscDmCommu, start, end time.Time) (*models.FpDscDmCommu, error, bool) {
	var firstBot models.FpDscDmCommu
	var firstUser models.FpDscDmCommu
	//获取最后一个bot
	if err := d.db.Model(&models.FpDscDmCommu{}).Select("created_at").Where("dsc_user_id = ? and channel_id = ? and created_at < ?", user.DscUserID, user.ChannelID, start).
		Where("from_user_id = bot_id").
		Order("created_at desc").First(&firstBot).Error; err != nil {
		if !errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, err, true
		}
		//如果没有bot，需找到表里第一条user数据
		if err := d.db.Model(&models.FpDscDmCommu{}).Where("dsc_user_id = ? and channel_id = ? and created_at <= ? ", user.DscUserID, user.ChannelID, end).
			Where("from_user_id = dsc_user_id").
			Order("created_at asc").First(&firstUser).Error; err != nil {
			if !errors.Is(err, gorm.ErrRecordNotFound) {
				return nil, err, true
			}
			//如果未找到user，可以直接退出今天的任务
			return nil, nil, true
		}
		return &firstUser, nil, false
	}
	//获取botid后的第一个user
	if err := d.db.Model(&models.FpDscDmCommu{}).Where("dsc_user_id = ? and channel_id = ? and created_at >= ? and created_at <= ? ", user.DscUserID, user.ChannelID, firstBot.CreatedAt, end).
		Where("from_user_id = dsc_user_id").
		Order("created_at asc").First(&firstUser).Error; err != nil {
		if !errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, err, true
		}
		//如果未找到user，可以直接退出今天的任务
		return nil, nil, true
	}
	return &firstUser, nil, false
}
func (d *DiscordInteract) FetchTodayCommus(user *models.FpDscDmCommu, createdAt, end time.Time) ([]*models.FpDscDmCommu, error) {
	var dest = []*models.FpDscDmCommu{}
	//获取最后一个bot
	if err := d.db.Model(&models.FpDscDmCommu{}).Select("*").Where("dsc_user_id = ? and channel_id = ? and created_at <= ?", user.DscUserID, user.ChannelID, end).
		Where("created_at >= ?", createdAt).
		Order("created_at asc").Find(&dest).Error; err != nil {
		return nil, err
	}
	return dest, nil
}

// ReplyTimeDetailStats 获取时间段的回复次数和占比
func (d *DiscordInteract) ReplyTimeDetailStats(req *pb.DiscordReplyTimeReq) ([]*pb.DiscordReplyTimeResp_ReplyTimeCountDetail, error) {

	var timeRanges = []struct {
		Label     string // 用于前端展示
		RangeName string // 用于数据库查询
	}{
		{"8h外", "8h_out"},
		{"7-8h", "7-8h"},
		{"6-7h", "6-7h"},
		{"5-6h", "5-6h"},
		{"4-5h", "4-5h"},
		{"3-4h", "3-4h"},
		{"2-3h", "2-3h"},
		{"1-2h", "1-2h"},
		{"0-1h", "0-1h"},
	}
	dest := make([]*pb.DiscordReplyTimeResp_ReplyTimeCountDetail, len(timeRanges)+1)

	// 根据筛选条件及时间分段，查出存在的数据
	records, err := d.FetchReplyStatsByWhere(req)
	if err != nil {
		return nil, err
	}
	replyCountMap := make(map[string]int64)
	var totalCount int64
	var totalRate, originDiv float64
	for _, record := range records {
		replyCountMap[record.RowName] = record.ReplyCount
		totalCount += record.ReplyCount
	}

	// 补齐缺失的时间段
	for i, rangeInfo := range timeRanges {
		replyCount := replyCountMap[rangeInfo.RangeName]
		dest[i] = &pb.DiscordReplyTimeResp_ReplyTimeCountDetail{
			RowName:          rangeInfo.Label,
			ReplyCountDetail: replyCount,
			ReplyCountRate:   0.0, // 初始化占比
		}
	}

	// 计算占比
	if totalCount > 0 {
		for j := range dest {
			if j == len(dest)-2 {
				dest[j].ReplyCountRate = utils.FloatRound(100.0-totalRate, 2)
				break
			} else {
				originDiv = utils.OriginDivInt(dest[j].ReplyCountDetail, totalCount)
				dest[j].ReplyCountRate = utils.FloatRound(originDiv, 2)
				totalRate += originDiv
			}

		}
	}

	// 添加合计项
	dest[len(timeRanges)] = &pb.DiscordReplyTimeResp_ReplyTimeCountDetail{
		RowName:          "合计",
		ReplyCountDetail: totalCount,
		ReplyCountRate:   100,
	}

	return dest, nil
}

// FetchReplyStatsByWhere 根据筛选条件和时间段分割查询
func (d *DiscordInteract) FetchReplyStatsByWhere(req *pb.DiscordReplyTimeReq) ([]*models.ReplyStats, error) {
	res := []*models.ReplyStats{}
	query := d.db.Table(models.FpDscReplyTime{}.TableName()).Select(`
		CASE
			WHEN reply_time_seconds > 0 AND reply_time_seconds <= 3600 THEN '0-1h'
			WHEN reply_time_seconds > 3600 AND reply_time_seconds <= 7200 THEN '1-2h'
			WHEN reply_time_seconds > 7200 AND reply_time_seconds <= 10800 THEN '2-3h'
			WHEN reply_time_seconds > 10800 AND reply_time_seconds <= 14400 THEN '3-4h'
			WHEN reply_time_seconds > 14400 AND reply_time_seconds <= 18000 THEN '4-5h'
			WHEN reply_time_seconds > 18000 AND reply_time_seconds <= 21600 THEN '5-6h'
			WHEN reply_time_seconds > 21600 AND reply_time_seconds <= 25200 THEN '6-7h'
			WHEN reply_time_seconds > 25200 AND reply_time_seconds <= 28800 THEN '7-8h'
			ELSE '8h_out'
		END AS row_name,
		COUNT(*) AS reply_count
	`)

	// 根据条件进行筛选
	query = query.Where("day BETWEEN ? AND ?", req.Date[0], req.Date[1])
	if len(req.Project) > 0 {
		query = query.Where("project IN (?)", req.Project)
	}
	if len(req.TagIds) > 0 {
		//连表查询用户
		userIds, err := d.FetchUserIdsByTagIds(req.TagIds)
		if err != nil {
			return nil, err
		}
		query = query.Where("dsc_user_id IN (?)", userIds)
	}

	// 按时间段分组并返回结果
	if err := query.Group("row_name").Find(&res).Error; err != nil {
		return nil, err
	}
	return res, nil
}

// FetchUserIdsByTagIds 根据多个标签数组查询符合条件的 dsc_user_id
func (d *DiscordInteract) FetchUserIdsByTagIds(tagIds []uint64) ([]string, error) {
	var dscUserIds []string
	query := d.db.Table("fp_dsc_reply_time AS r").
		Select("r.dsc_user_id").
		Joins("JOIN fp_dsc_player_tag AS t ON r.dsc_user_id = t.dsc_user_id AND r.project = t.project").
		Where("t.tag_id IN (?)", tagIds).
		Group("r.dsc_user_id")
	// 执行查询并获取结果
	if err := query.Scan(&dscUserIds).Error; err != nil {
		return nil, err
	}
	return dscUserIds, nil
}

func (d *DiscordInteract) ReplyTimeAvgDayDetailStats(req *pb.DiscordReplyTimeReq) ([]*pb.DiscordReplyTimeResp_ReplyTimeAvgDayDetail, error) {
	dest := []*pb.DiscordReplyTimeResp_ReplyTimeAvgDayDetail{}
	records := []*models.ReplyTime{}
	query := d.db.Table(models.FpDscReplyTime{}.TableName()).Select("day AS date,reply_time_seconds").
		Where("day BETWEEN ? AND ?", req.Date[0], req.Date[1])

	// 根据条件进行筛选
	if len(req.Project) > 0 {
		query = query.Where("project IN (?)", req.Project)
	}
	if len(req.TagIds) > 0 {
		//连表查询用户
		userIds, err := d.FetchUserIdsByTagIds(req.TagIds)
		if err != nil {
			return nil, err
		}
		query = query.Where("dsc_user_id IN (?)", userIds)
	}
	if err := query.Order("date").Find(&records).Error; err != nil {
		return nil, err
	}

	// 计算平均值
	avgMap := make(map[string]struct {
		TotalSeconds uint
		Count        uint
	})

	for _, record := range records {
		avgMap[record.Date] = struct {
			TotalSeconds uint
			Count        uint
		}{
			TotalSeconds: avgMap[record.Date].TotalSeconds + record.ReplyTimeSeconds,
			Count:        avgMap[record.Date].Count + 1,
		}
	}

	stTm, _ := time.Parse("2006-01-02", req.Date[0])
	enTm, _ := time.Parse("2006-01-02", req.Date[1])
	for tm := stTm; tm.Before(enTm.AddDate(0, 0, 1)); tm = tm.AddDate(0, 0, 1) {
		day := tm.Format("2006-01-02")
		if stats, ok := avgMap[day]; ok {
			avgReplySecond := float64(stats.TotalSeconds) / float64(stats.Count)
			avgReplyTime := utils.FloatRound(avgReplySecond/3600.0, 2)
			dest = append(dest, &pb.DiscordReplyTimeResp_ReplyTimeAvgDayDetail{
				Date:         day,
				AvgReplyTime: avgReplyTime, // 保留两位小数
			})
		} else {
			dest = append(dest, &pb.DiscordReplyTimeResp_ReplyTimeAvgDayDetail{
				Date:         day,
				AvgReplyTime: 0,
			})
		}
	}
	return dest, nil
}

func (d *DiscordInteract) FetchPushCycleInteractPlayers(ctx context.Context, project string, start, end int64, forkDscUserIds []string) ([]*pb.DscSurveyPlayerDetail, error) {
	dscUserIds := []string{}
	begin := utils.TransferSecondTimestampToTime(start)
	finish := utils.TransferSecondTimestampToTime(end)
	// 获取本周期（本次推送时间-上次推送时间）内向客服机器人发送过消息的玩家discord账号
	if len(forkDscUserIds) > 0 {
		dscUserIds = forkDscUserIds
	} else {
		_, _ = begin, finish
		if err := d.db.Model(&models.FpDscDmCommu{}).Select("DISTINCT dsc_user_id").
			Where("created_at BETWEEN ? AND ? AND from_user_id = dsc_user_id AND project = ?", begin, finish, project).
			//Where("dsc_user_id = ? AND from_user_id = dsc_user_id AND project = ?", "1045629060488892476", project).
			Find(&dscUserIds).Error; err != nil {
			return nil, err
		}
	}

	var players = make([]*pb.DscSurveyPlayerDetail, 0)
	if err := d.db.WithContext(ctx).Model(&models.FpDscUser{}).Table(fmt.Sprintf("%s as fdu", models.FpDscUser{}.TableName())).
		Joins(fmt.Sprintf("INNER JOIN %s fpmc ON fdu.dsc_user_id = fpmc.dsc_user_id AND fdu.project=fpmc.game_project", models.GetFpDscPlayerMaintainConfigTableName())).
		Select("distinct(fdu.priv_channel_id) as priv_channel_id, fdu.app_id, fdu.dsc_user_id, COALESCE(fdu.global_name, fdu.user_name) AS dsc_nick_name, "+
			"fpmc.uid, fpmc.account_id, fpmc.lang, fpmc.maintainer").
		Where("fdu.dsc_user_id IN (?) AND fdu.project = ? AND fdu.is_deleted = ? AND fpmc.uid > 0 AND fpmc.vip_state = ?", dscUserIds, project, code.DscNoDelete, code.Vip).
		Find(&players).Error; err != nil {
		return nil, err
	}
	for i, row := range players { // 回填 processors
		var prs []string
		if err := d.db.Model(&models.FpDscDmSend{}).Where("channel_id = ? AND unix_timestamp(created_at) > ? AND unix_timestamp(created_at) < ?",
			row.PrivChannelId, start, end).Distinct("operator").Pluck("operator", &prs).Error; err != nil {
			logger.Errorf(ctx, "FetchPushCycleInteractPlayers operator return err:%+v, row:%+v", err, row)
			continue
		} else {
			players[i].Processors = prs
		}
	}

	{ // 获取 last reply service
		var privChannelIds []string = []string{}
		for _, row := range players {
			privChannelIds = append(privChannelIds, row.PrivChannelId)
		}

		retry.Do(func() error {
			replies, _, _innerErr := elasticsearch.DefaultDscEsSvc.GetDscLastReplyService(ctx, privChannelIds)
			if _innerErr != nil {
				logger.Error(ctx, "Discord last record err", logger.String("err", _innerErr.Error()))
				return _innerErr
			}
			for _, doc := range replies {
				_dscDoc := &models.FpDscUserDoc{}
				if err := json.Unmarshal(doc.Source, &_dscDoc); err != nil {
					logger.Error(ctx, "Discord last record Unmarshal err", logger.String("err", err.Error()))
					continue
				}
				//dump.Dump(_dscDoc)
				for _, row := range players {
					if row.PrivChannelId == _dscDoc.PrivChannelID {
						row.LastReplyService = _dscDoc.LastReplyService
						break
					}
				}
			}
			return nil
		}, retry.Attempts(3), retry.Delay(time.Second), retry.LastErrorOnly(true))
	}

	return players, nil
}

func (d *DiscordInteract) FetchSurveyDscPlayerDetail(ctx context.Context, project string, uid int64, start, end int64) (*pb.DscSurveyPlayerDetail, error) {
	channels, err := NewDiscordCommu().GetChannelIdsByProjectUid(project, uid)
	if err != nil {
		return nil, err
	}
	if len(channels) != 1 {
		return nil, nil
	}
	var player = &pb.DscSurveyPlayerDetail{
		PrivChannelId:    channels[0].PrivChannelId,
		AppId:            channels[0].AppId,
		DscUserId:        channels[0].DscUserId,
		DscNickName:      "",
		Uid:              0,
		AccountId:        "",
		Lang:             "",
		Maintainer:       "",
		Processors:       make([]string, 0),
		LastReplyService: "",
	}
	if err := d.db.WithContext(ctx).Model(&models.FpDscUser{}).Select("COALESCE(global_name, user_name) AS dsc_nick_name").Where("priv_channel_id = ? AND dsc_user_id = ? AND project = ?", player.PrivChannelId, player.DscUserId, project).Take(&player).Error; err != nil {
		return nil, err
	}

	if err := d.db.WithContext(ctx).Model(&models.FpDscPlayerMaintainConfig{}).Select("uid, account_id, lang, maintainer").Where("dsc_user_id = ? AND game_project = ?", player.DscUserId, project).Take(&player).Error; err != nil {
		return nil, err
	}
	// 回填 processors
	if err := d.db.Model(&models.FpDscDmSend{}).Where("channel_id = ? AND unix_timestamp(created_at) > ? AND unix_timestamp(created_at) < ?",
		player.PrivChannelId, start, end).Distinct("operator").Pluck("operator", &player.Processors).Error; err != nil {
		logger.Errorf(ctx, "FetchSurveyDscPlayerDetail operator return err:%+v, player:%+v", err, player)
		return nil, err
	}

	{ // 获取 last reply service

		retry.Do(func() error {
			replies, _, _innerErr := elasticsearch.DefaultDscEsSvc.GetDscLastReplyService(ctx, []string{player.PrivChannelId})
			if _innerErr != nil {
				logger.Error(ctx, "Discord last record err", logger.String("err", _innerErr.Error()))
				return _innerErr
			}
			for _, doc := range replies {
				_dscDoc := &models.FpDscUserDoc{}
				if err := json.Unmarshal(doc.Source, &_dscDoc); err != nil {
					logger.Error(ctx, "Discord last record Unmarshal err", logger.String("err", err.Error()))
					continue
				}
				//dump.Dump(_dscDoc)
				if _dscDoc.PrivChannelID == player.PrivChannelId {
					player.LastReplyService = _dscDoc.LastReplyService
				}
			}
			return nil
		}, retry.Attempts(3), retry.Delay(time.Second), retry.LastErrorOnly(true))
	}
	return player, nil
}

// FetchUserIsInitiativeByAccountId 查询用户七日内是否发起dc会话时间
func (d *DiscordInteract) FetchUserIsInitiativeByAccountId(project, accountId string) (string, error) {
	mc := &models.FpDscPlayerMaintainConfig{}
	// 获取dscuserid
	if err := d.db.Table(models.GetFpDscPlayerMaintainConfigTableName()).Select("dsc_user_id").
		Where("account_id = ? and game_project = ?", accountId, project).First(mc).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// 未绑定过缓存存储空字符串
			return "", nil
		}
		return "", err
	}

	var createdAt time.Time
	sevenDaysAgo := time.Now().Add(-7 * 24 * time.Hour).Truncate(24 * time.Hour)
	if err := d.db.Model(&models.FpDscDmCommu{}).
		Where("project = ? AND dsc_user_id = ? AND from_user_id = ? AND created_at >= ?", project, mc.DscUserId, mc.DscUserId, sevenDaysAgo).
		Order("created_at DESC").
		Select("created_at").
		First(&createdAt).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// 七日内未沟通过缓存存储空字符串
			return "", nil
		}
		return "", err
	}

	return createdAt.Format("2006-01-02 15:04:05"), nil
}
