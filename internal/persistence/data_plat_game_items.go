package persistence

import (
	"context"
	"github.com/labstack/echo/v4"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"ops-ticket-api/internal/code"
	"ops-ticket-api/internal/framework/database"
	"ops-ticket-api/internal/framework/xerrors"
	"ops-ticket-api/models"
	"ops-ticket-api/proto/pb"
	"time"
)

// dataPlatGameItems
type dataPlatGameItems struct {
	db *gorm.DB
}

// NewDataPlatGameItems init
func NewDataPlatGameItems() *dataPlatGameItems {
	return &dataPlatGameItems{
		db: database.Db(),
	}
}

func (dto *dataPlatGameItems) GetItemList(ctx echo.Context, param *pb.DataPlatGameItemListReq) (*pb.DataPlatGameItemListResp, error) {
	// filter data
	dest := []*models.FpDataplatGameItem{}
	query := dto.db.WithContext(ctx.Request().Context()).Model(&models.FpDataplatGameItem{}).Select("*")
	if param.Project != "" {
		query.Where("project = ?", param.Project)
	}
	var total int64
	if err := query.Count(&total).Error; err != nil {
		logger.Error(ctx.Request().Context(), "get data_plat_game_item list count err", zap.String("err", err.Error()))
		return nil, err
	}
	if err := query.Scopes(database.Paginate(&param.Page, &param.PageSize)).Find(&dest).Error; err != nil {
		logger.Error(ctx.Request().Context(), "get data_plat_game_item list err", zap.String("err", err.Error()))
		return nil, xerrors.New(err.Error(), code.DbError)
	}
	result := &pb.DataPlatGameItemListResp{Data: []*pb.DataPlatGameItemListResp_ItemDetail{}}
	result.CurrentPage = param.Page
	result.PerPage = param.PageSize
	result.Total = uint32(total)
	for _, row := range dest {
		rowDetail := &pb.DataPlatGameItemListResp_ItemDetail{
			Id:        row.ID,
			ItemId:    row.ItemID,
			ItemName:  row.ItemName,
			UpdatedAt: row.UpdateTime.Format(time.DateTime),
			Operator:  row.Operator,
		}
		result.Data = append(result.Data, rowDetail)
	}
	return result, nil
}

func (dto *dataPlatGameItems) GetItemOpts(ctx echo.Context, project string) ([]*models.FpDataplatGameItem, error) {
	var dest []*models.FpDataplatGameItem
	err := dto.db.WithContext(ctx.Request().Context()).Model(&models.FpDataplatGameItem{}).
		Select("item_id,item_name").Where("project = ?", project).Find(&dest).Error
	return dest, err
}
func (dto *dataPlatGameItems) UpsertGameItem(ctx echo.Context, detail *models.FpDataplatGameItem) error {
	err := dto.db.WithContext(ctx.Request().Context()).Model(detail).Clauses(clause.OnConflict{
		Columns:   []clause.Column{{Name: "item_id"}, {Name: "project"}},
		DoUpdates: clause.AssignmentColumns([]string{"item_name", "operator", "update_time"}),
	}).Create(detail).Error
	return err
}

func (dto *dataPlatGameItems) GetItems(ctx context.Context, project string) ([]*models.FpDataplatGameItem, error) {
	var dest []*models.FpDataplatGameItem
	tx := dto.db.WithContext(ctx).Model(&models.FpDataplatGameItem{})
	if project != "" {
		tx = tx.Where("project = ?", project)
	}
	err := tx.Select("item_id,item_name,project").Find(&dest).Error
	return dest, err
}

func (dto *dataPlatGameItems) GetI18n(ctx context.Context, project string) ([]*models.FpDataplatGameI18n, error) {
	var dest []*models.FpDataplatGameI18n
	tx := dto.db.WithContext(ctx).Model(&models.FpDataplatGameI18n{})
	if project != "" {
		tx = tx.Where("project = ?", project)
	}
	err := tx.Select("unique_key,show_msg,project").Find(&dest).Error
	return dest, err
}
