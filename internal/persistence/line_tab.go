package persistence

import (
	"encoding/json"
	"errors"
	"fmt"
	"github.com/labstack/echo/v4"
	"github.com/spf13/cast"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"ops-ticket-api/internal/code"
	"ops-ticket-api/internal/cst"
	"ops-ticket-api/internal/framework/database"
	"ops-ticket-api/internal/framework/xerrors"
	"ops-ticket-api/models"
	"ops-ticket-api/proto/pb"
	"ops-ticket-api/services/commsrv"
	"ops-ticket-api/utils"
	"sort"
)

// LineTab line搜索tab
type LineTab struct {
	db *gorm.DB
}

// NewLineTab init
func NewLineTab() *LineTab {
	return &LineTab{
		db: database.Db(),
	}
}

func (l *LineTab) LineTabSave(ctx echo.Context, req *pb.LineTabAddReq) error {
	operator := cast.ToString(ctx.Get(cst.AccountInfoCtx))
	current := utils.NowTimestamp()
	updateTime := utils.TimeFormat(int64(current))
	// 该操作人名下每个游戏的现有tab不能超过限制(10个)
	var cnt, rptCnt int64
	tx := l.db.Table(models.GetFpLineTabTableName()).Where("operator = ? and project = ?", operator, &req.Detail.Project[0])
	if err := tx.Count(&cnt).Error; err != nil {
		logger.Error(ctx.Request().Context(), "error query tab count", zap.String("err", err.Error()))
		return xerrors.New(err.Error(), code.DbError)
	}
	if cnt >= code.TabLimit {
		return errors.New("tab limit exceeded")
	}
	// 需要校验同一个游戏下的tab是否重复
	if err := l.db.Table(models.GetFpLineTabTableName()).Where("tab_name = ? and project = ?", req.TabName, &req.Detail.Project[0]).Count(&rptCnt).Error; err != nil {
		logger.Error(ctx.Request().Context(), "error query same tab name count", zap.String("err", err.Error()))
		return xerrors.New(err.Error(), code.DbError)
	}
	if rptCnt > 0 {
		return errors.New("tab name repeated for single game")
	}
	detailBytes, err := json.Marshal(req.Detail)
	if err != nil {
		return err
	}
	record := &models.FpLineTab{
		TabName:     req.TabName,
		Project:     req.Detail.Project[0],
		Operator:    operator,
		Detail:      string(detailBytes),
		Public:      uint8(req.Public),
		CreateTime:  int64(current),
		UpdatedTime: updateTime,
	}
	newTx := l.db.WithContext(ctx.Request().Context()).Begin()
	defer newTx.Rollback()
	if err := newTx.Table(models.GetFpLineTabTableName()).Create(record).Error; err != nil {
		logger.Error(ctx.Request().Context(), "error create line tab", zap.String("err", err.Error()))
		return xerrors.New(err.Error(), code.DbError)
	}
	// 更新 fp_ops_tickets_tab_sort 表，更新 detail 字段保存最新的 Tab 排序信息
	project := req.Detail.Project[0]
	newTabID := cast.ToInt64(record.ID)
	sortRecord := &models.FpLineTabSort{}
	err = newTx.Table(models.GetFpLineTabSort()).Where("operator = ?", operator).First(&sortRecord).Error
	if err != nil {
		if !errors.Is(err, gorm.ErrRecordNotFound) {
			logger.Error(ctx.Request().Context(), "error query tab sort record", zap.String("err", err.Error()))
			return xerrors.New(err.Error(), code.DbError)
		}
		//  1.不存在该operator需要创建新纪录
		initSort := []*models.SortDetail{
			{project, []int64{newTabID}},
		}
		sortJSON, _ := json.Marshal(initSort)
		newSortRecord := &models.FpLineTabSort{
			Operator:    operator,
			Detail:      string(sortJSON),
			CreatedTime: updateTime,
			UpdatedTime: updateTime,
		}
		if err := newTx.Table(models.GetFpLineTabSort()).Create(newSortRecord).Error; err != nil {
			logger.Error(ctx.Request().Context(), "error create sort record", zap.String("err", err.Error()))
			return xerrors.New(err.Error(), code.DbError)
		}
	} else {
		initSort := []*models.SortDetail{
			{"", []int64{}},
		}
		_ = json.Unmarshal([]byte(sortRecord.Detail), &initSort)
		found := false
		// 遍历查找是否存在该项目的记录
		for i, entry := range initSort {
			if entry.Project == project {
				// 存在则追加新 Tab ID
				initSort[i].Tab = append(initSort[i].Tab, newTabID)
				found = true
				break
			}
		}
		// 如果不存在，则新增该项目的排序记录
		if !found {
			initSort = append(initSort, &models.SortDetail{
				Project: project,
				Tab:     []int64{newTabID},
			})
		}
		// 序列化回 JSON
		updatedSortJSON, _ := json.Marshal(initSort)
		if err := newTx.Table(models.GetFpLineTabSort()).Where("operator = ?", operator).
			Update("detail", string(updatedSortJSON)).Error; err != nil {
			logger.Error(ctx.Request().Context(), "error update sort record", zap.String("err", err.Error()))
			return xerrors.New(err.Error(), code.DbError)
		}

	}
	return newTx.Commit().Error
}

func (l *LineTab) LineTabList(ctx echo.Context) (*pb.LineTabListResp, error) {
	// 1. 获取当前用户游戏权限列表
	permList, _ := commsrv.LoadPermGmList(ctx.Request().Context(), ctx.Get(cst.AccountInfoCtx).(string))
	operator := cast.ToString(ctx.Get(cst.AccountInfoCtx))

	// 2. 查询所有 Tab 记录
	dest := []*models.FpLineTab{}
	if err := l.db.Table(models.GetFpLineTabTableName()).
		Where("operator = ? OR public = ?", operator, 1).
		Find(&dest).Error; err != nil {
		return nil, err
	}

	// 3. 按 project 分组
	tabsByGame := make(map[string][]*models.FpLineTab)
	for _, tab := range dest {
		if len(permList) == 0 || utils.InArrayAny(tab.Project, permList) {
			tabsByGame[tab.Project] = append(tabsByGame[tab.Project], tab)
		}
	}
	defaultProjects := make([]string, 0, len(tabsByGame))
	for project := range tabsByGame {
		defaultProjects = append(defaultProjects, project)
	}
	sort.Strings(defaultProjects)

	// 4. 查询当前用户的排序记录
	sortRecord := &models.FpLineTabSort{}
	err := l.db.Table(models.GetFpLineTabSort()).
		Where("operator = ?", operator).
		First(sortRecord).Error

	// 解析排序数据（若存在）
	sortData := []models.SortDetail{}
	if err == nil {
		_ = json.Unmarshal([]byte(sortRecord.Detail), &sortData)
	} else if !errors.Is(err, gorm.ErrRecordNotFound) {
		// 其他查询错误
		logger.Error(ctx.Request().Context(), "error query tab sort record", zap.String("err", err.Error()))
		return nil, xerrors.New(err.Error(), code.DbError)
	}

	// 构建外层项目最终顺序
	orderedProjects := make([]string, 0, len(sortData))
	for _, item := range sortData {
		orderedProjects = append(orderedProjects, item.Project)
	}
	seen := make(map[string]bool, len(orderedProjects))
	for _, p := range orderedProjects {
		seen[p] = true
	}
	for _, p := range defaultProjects {
		if !seen[p] {
			orderedProjects = append(orderedProjects, p)
		}
	}

	// 构建项目->Tab 排序映射
	projectTabOrder := make(map[string][]int64, len(sortData))
	for _, item := range sortData {
		projectTabOrder[item.Project] = item.Tab
	}

	// 5. 按排序结果组装响应
	resp := &pb.LineTabListResp{
		Data: make([]*pb.LineTabListResp_LineTabDetail, 0, len(orderedProjects)),
	}
	for _, project := range orderedProjects {
		tabs := tabsByGame[project]

		// 内层 Tab 排序
		if order, ok := projectTabOrder[project]; ok && len(order) > 0 {
			m := make(map[int64]*models.FpLineTab, len(tabs))
			for _, t := range tabs {
				m[t.ID] = t
			}
			sorted := make([]*models.FpLineTab, 0, len(tabs))
			for _, id := range order {
				if t, exists := m[id]; exists {
					sorted = append(sorted, t)
					delete(m, id)
				}
			}
			// 剩余未排序的
			rem := make([]*models.FpLineTab, 0, len(m))
			for _, t := range m {
				rem = append(rem, t)
			}
			sort.Slice(rem, func(i, j int) bool { return rem[i].ID < rem[j].ID })
			tabs = append(sorted, rem...)
		} else {
			sort.Slice(tabs, func(i, j int) bool { return tabs[i].ID < tabs[j].ID })
		}

		// 填充项目数据
		detailList := make([]*pb.LineTabListResp_TabInfo, 0, len(tabs))
		for _, tab := range tabs {
			var sd pb.LineUserListReq
			_ = json.Unmarshal([]byte(tab.Detail), &sd)
			detailList = append(detailList, &pb.LineTabListResp_TabInfo{
				Id:       tab.ID,
				TabName:  tab.TabName,
				Public:   int32(tab.Public),
				Operator: tab.Operator,
				Detail:   &sd,
			})
		}
		resp.Data = append(resp.Data, &pb.LineTabListResp_LineTabDetail{
			Project: project,
			Tab:     detailList,
		})
	}

	now := utils.TimeFormat(int64(utils.NowTimestamp()))
	if errors.Is(err, gorm.ErrRecordNotFound) {
		// 排序记录不存在，生成默认排序并写入表
		defaultList := make([]models.SortDetail, 0, len(orderedProjects))
		for _, project := range orderedProjects {
			ts := tabsByGame[project]
			sort.Slice(ts, func(i, j int) bool { return ts[i].ID < ts[j].ID })
			ids := make([]int64, len(ts))
			for i, t := range ts {
				ids[i] = t.ID
			}
			defaultList = append(defaultList, models.SortDetail{
				Project: project,
				Tab:     ids,
			})
		}
		detailBytes, _ := json.Marshal(defaultList)
		newRecord := &models.FpLineTabSort{
			Operator:    operator,
			Detail:      string(detailBytes),
			CreatedTime: now,
			UpdatedTime: now,
		}
		if err := l.db.Table(models.GetFpLineTabSort()).Create(newRecord).Error; err != nil {
			logger.Error(ctx.Request().Context(), "create default sort record error", zap.String("err", err.Error()))
		}
	}

	return resp, nil
}

func (l *LineTab) LineTabDel(ctx echo.Context, req *pb.LineTabDelReq) error {
	// 查询出原记录
	record := &models.FpLineTab{}
	err := l.db.Table(models.GetFpLineTabTableName()).Where("id = ?", req.Id).First(record).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return xerrors.New(err.Error(), code.DbError)
	} else if errors.Is(err, gorm.ErrRecordNotFound) {
		return xerrors.New("tab不存在", code.InvalidParams)
	}
	delTabID := record.ID
	project := record.Project
	operator := cast.ToString(ctx.Get(cst.AccountInfoCtx))
	// 只能删除自己创建的tab
	if operator != record.Operator {
		return fmt.Errorf("operator:%v can not delete others:%v tab", operator, record.Operator)
	}
	tx := l.db.Begin()
	defer tx.Rollback()
	err = tx.Table(models.GetFpLineTabSort()).Where("id=?", req.Id).Delete(record).Error
	if err != nil {
		logger.Error(ctx.Request().Context(), "error delete line tab", zap.String("err", err.Error()))
		return xerrors.New(err.Error(), code.DbError)
	}
	// 同步删除排序表的记录
	sortRecord := &models.FpLineTabSort{}
	initSort := []*models.SortDetail{}
	err = tx.Table(models.GetFpLineTabSort()).Where("operator = ?", operator).First(&sortRecord).Error
	if err != nil {
		logger.Error(ctx.Request().Context(), "error query tab sort record", zap.String("err", err.Error()))
		return xerrors.New(err.Error(), code.DbError)
	}
	_ = json.Unmarshal([]byte(sortRecord.Detail), &initSort)

	// 5. 遍历排序数据，找到对应项目并删除待删除 Tab 的记录
	for i, item := range initSort {
		if item.Project == project {
			// 找到项目对应的 tab 数组，过滤掉待删除的 tab id
			newTabs := []int64{}
			for _, tabID := range item.Tab {
				if tabID != delTabID {
					newTabs = append(newTabs, tabID)
				}
			}
			// 更新当前项目的 tab 数组
			initSort[i].Tab = newTabs
			break
		}
	}
	newSortJSON, _ := json.Marshal(initSort)
	if err := tx.Table(models.GetFpLineTabSort()).Where("operator = ?", operator).
		Update("detail", string(newSortJSON)).Error; err != nil {
		logger.Error(ctx.Request().Context(), "error update sort record", zap.String("err", err.Error()))
		return xerrors.New(err.Error(), code.DbError)
	}

	return tx.Commit().Error
}

func (l *LineTab) GetAllTabInfo(ctx echo.Context, permList []string) (res []*models.FpLineTab, err error) {
	//获取所有tab内容
	operator := cast.ToString(ctx.Get(cst.AccountInfoCtx))
	dest := []*models.FpLineTab{}
	tx := l.db.Table(models.GetFpLineTabTableName()).Where("operator = ? OR public = ?", operator, 1)
	if len(permList) > 0 {
		tx = tx.Where("project in (?)", permList)
	}
	if err = tx.Find(&dest).Error; err != nil {
		return nil, xerrors.New(err.Error(), code.DbError)
	}
	return dest, nil
}

func (l *LineTab) LineTabEdit(ctx echo.Context, req *pb.LineTabEditReq) error {
	operator := cast.ToString(ctx.Get(cst.AccountInfoCtx))
	current := utils.NowTimestamp()
	updateTime := utils.TimeFormat(int64(current))
	// 根据id查找tab
	tab := &models.FpLineTab{}
	err := l.db.Table(models.GetFpLineTabTableName()).Where("id = ?", req.Id).
		First(tab).Error
	if err != nil {
		if !errors.Is(err, gorm.ErrRecordNotFound) {
			logger.Error(ctx.Request().Context(), "error query line tab", zap.String("err", err.Error()))
			return xerrors.New(err.Error(), code.DbError)
		}
		return xerrors.New("tab不存在", code.InvalidParams)
	}

	var rptCnt int64
	// 需要校验同一个游戏下的tab是否重复
	if err := l.db.Model(&models.FpLineTab{}).
		Where("tab_name = ? and id != ? and project = ?", req.TabName, req.Id, tab.Project).
		Count(&rptCnt).Error; err != nil {
		logger.Error(ctx.Request().Context(), "error query same tab name count", zap.String("err", err.Error()))
		return xerrors.New(err.Error(), code.DbError)
	}
	if rptCnt > 0 {
		return errors.New("tab name repeated for single game")
	}
	//更新
	updates := map[string]interface{}{
		"tab_name":    req.TabName,
		"operator":    operator,
		"update_time": updateTime,
		"public":      uint8(req.Public),
	}
	if err := l.db.Table(models.GetFpLineTabTableName()).Where("id = ?", req.Id).Updates(updates).Error; err != nil {
		logger.Error(ctx.Request().Context(), "error update dsc tab", zap.String("err", err.Error()))
		return xerrors.New(err.Error(), code.DbError)
	}
	return nil
}

func (l *LineTab) LineTabUpdateSort(ctx echo.Context, req *pb.LineTabUpdateSortReq) error {
	operator := cast.ToString(ctx.Get(cst.AccountInfoCtx))

	// 直接更新当前操作员对应的排序详情
	if err := l.db.Table(models.GetFpLineTabSort()).
		Where("operator = ?", operator).
		Update("detail", req.SortSetting).Error; err != nil {
		logger.Error(ctx.Request().Context(), "update sort failed", zap.String("err", err.Error()))
		return xerrors.New(err.Error(), code.DbError)
	}
	return nil
}
