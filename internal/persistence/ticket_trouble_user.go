package persistence

import (
	"github.com/labstack/echo/v4"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"ops-ticket-api/internal/code"
	"ops-ticket-api/internal/framework/database"
	"ops-ticket-api/internal/framework/xerrors"
	"ops-ticket-api/models"
	"sync"
)

var (
	troubleUserRepo        *TicketTroubleUserRepo
	troubleUserRepoOnce    sync.Once
	DefaultTroubleUserRepo = newTicketTroubleRepo()
)

type TicketTroubleUserRepo struct {
	db *gorm.DB
}

func newTicketTroubleRepo() *TicketTroubleUserRepo {
	troubleUserRepoOnce.Do(func() {
		troubleUserRepo = &TicketTroubleUserRepo{
			db: database.Db(),
		}
	})
	return troubleUserRepo
}

func (dto *TicketTroubleUserRepo) AddTicketTroubleUser(ctx echo.Context, dest *models.FpOpsTicketsTroubleUser) error {
	if err := dto.db.Create(&dest).Error; err != nil {
		logger.Error(ctx.Request().Context(), "add ticket trouble user info err", zap.Uint64("ticket_id", dest.TicketID), zap.String("err", err.Error()))
		return xerrors.New(err.Error(), code.DbError)
	}
	return nil
}
