package persistence

import (
	"context"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"ops-ticket-api/internal/code"
	"ops-ticket-api/internal/framework/database"
	"ops-ticket-api/internal/framework/xerrors"
	"ops-ticket-api/models"
	"sync"
)

var (
	strategyTplRepo *StrategyTpl
	OnceStrategyTpl sync.Once
)

// StrategyTpl 工单分级模版回复
type StrategyTpl struct {
	db *gorm.DB
}

// NewStrategyTpl init
func NewStrategyTpl() *StrategyTpl {
	OnceStrategyTpl.Do(func() {
		strategyTplRepo = &StrategyTpl{
			db: database.Db(),
		}
	})
	return strategyTplRepo
}

// GetStrategyReplyContent 根据游戏mo和语种去获取回复模版
func (dto *StrategyTpl) GetStrategyReplyContent(ctx context.Context, project, lang string, firstCatID uint32) (*models.FpOpsStrategyTplLang, error) {
	var tplInfo = &models.FpOpsStrategyTplLang{}
	if err := dto.db.WithContext(ctx).Model(&models.FpOpsStrategyTplLang{}).
		Where("project = ? and lang = ? and first_cat_id = ?", project, lang, firstCatID).Find(tplInfo).Error; err != nil {
		logger.Errorf(ctx, "get reply content err", zap.String("err", err.Error()))
		return nil, xerrors.New(err.Error(), code.DbError)
	}
	return tplInfo, nil
}
