package persistence

import (
	"errors"
	"fmt"
	jsoniter "github.com/json-iterator/go"
	"github.com/labstack/echo/v4"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"ops-ticket-api/internal/code"
	"ops-ticket-api/internal/cst"
	"ops-ticket-api/internal/framework/database"
	"ops-ticket-api/internal/framework/xerrors"
	"ops-ticket-api/models"
	"ops-ticket-api/proto/pb"
	"ops-ticket-api/utils"
	"time"
)

// StrategyDao 工单策略
type StrategyDao struct {
	db *gorm.DB
}

// NewStrategyDao init
func NewStrategyDao() *StrategyDao {
	return &StrategyDao{
		db: database.Db(),
	}
}

func (s *StrategyDao) CheckIfHasProject(req *pb.StrategyAddReq, isEdit bool) (int64, error) {
	var count int64
	query := s.db.Model(&models.FpOpsTicketsStrategy{}).Where("project = ?", req.Project)
	if isEdit {
		query = query.Where("id != ?", req.StrategyId)
	}
	if err := query.Count(&count).Error; err != nil {
		return -1, xerrors.New(code.StatusText(code.DbError), code.DbError)
	}
	return count, nil
}

func (s *StrategyDao) SaveStrategyDetail(strategy *models.FpOpsTicketsStrategy) error {
	if strategy.ID > 0 {
		err := s.db.Where("id = ?", strategy.ID).Updates(strategy).Error
		if err != nil {
			return xerrors.New(code.StatusText(code.DbError), code.DbError)
		}
	} else {
		err := s.db.Create(strategy).Error
		if err != nil {
			return xerrors.New(code.StatusText(code.DbError), code.DbError)
		}
	}
	return nil
}

func (s *StrategyDao) GetStrategyById(id int64) (*models.FpOpsTicketsStrategy, error) {
	var detail = &models.FpOpsTicketsStrategy{}
	err := s.db.Model(detail).Where("id = ?", id).First(detail).Error
	if err != nil {
		return nil, err
	}
	return detail, nil
}

func (s *StrategyDao) DelStrategy(id int64) (err error) {
	if id == 0 {
		return fmt.Errorf("delete strategy id empty")
	}
	return s.db.Where("id = ?", id).Delete(&models.FpOpsTicketsStrategy{}).Error
}

func (s *StrategyDao) EnableStrategy(ctx echo.Context, req *pb.StrategyEnabelReq) (err error) {

	update := map[string]interface{}{
		"enable":     req.Enable,
		"creator":    ctx.Get(cst.AccountInfoCtx).(string),
		"updated_at": time.Now().Unix(),
	}
	if err := s.db.Model(&models.FpOpsTicketsStrategy{}).Where("id=?", req.StrategyId).Updates(update).Error; err != nil {
		return xerrors.New(err.Error(), code.DbError)
	}
	return nil
}

func (s *StrategyDao) StrategyList(ctx echo.Context, req *pb.StrategyListReq, all bool) ([]*pb.StrategyListResp_StrategyRecord, uint32, error) {
	dest := make([]*pb.StrategyListResp_StrategyRecord, 0)
	// 首先查出符合条件的数据条数
	strategyList := []*models.FpOpsTicketsStrategy{}
	subQuery := s.db.Model(&models.FpOpsTicketsStrategy{})
	if len(req.Project) > 0 {
		subQuery = subQuery.Where("project IN (?)", req.Project)
	}
	if req.StrategyName != "" {
		subQuery = subQuery.Where("strategy_name like ?", "%"+req.StrategyName+"%")
	}
	var total int64
	query := subQuery
	if err := subQuery.Count(&total).Error; err != nil {
		logger.Error(ctx.Request().Context(), "get strategyList err", zap.String("err", err.Error()))
		return nil, 0, err
	}
	// 导出数据不分页
	if all {
		if err := query.Order("updated_at DESC").Find(&strategyList).Error; err != nil {
			logger.Error(ctx.Request().Context(), "get strategyList list err", zap.String("err", err.Error()))
			return nil, 0, xerrors.New(err.Error(), code.DbError)
		}
	} else {
		if err := query.Order("updated_at DESC").Scopes(database.Paginate(&req.Page, &req.PageSize)).Find(&strategyList).Error; err != nil {
			logger.Error(ctx.Request().Context(), "get strategyList list err", zap.String("err", err.Error()))
			return nil, 0, xerrors.New(err.Error(), code.DbError)
		}
	}
	for _, v := range strategyList {
		// 2. 反序列化 Filters 字段
		var filters pb.StrategyFilters
		if err := jsoniter.UnmarshalFromString(v.Filters, &filters); err != nil {
			logger.Errorf(ctx.Request().Context(), "UnmarshalFromString err. filters:%s, err:%v", v.Filters, err)
			return nil, 0, err
		}
		// 3. 将过滤器数据转换为字符串
		var filterServerLists string
		if !filters.IsSeverAll {
			filterServerLists = utils.ConvertFilterToString(filters.Server)
		}
		strategyRecord := &pb.StrategyListResp_StrategyRecord{
			StrategyId:             int64(v.ID),
			StrategyName:           v.StrategyName,
			Project:                v.Project,
			Enable:                 uint32(v.Enable),
			FilterServerLists:      filterServerLists,
			FilterCastleLevelLists: utils.ConvertFilterToString(filters.CastleLevel),
			FilterPayRangeLists:    utils.ConvertFilterToString(filters.PayRange),
			Type:                   1,
		}
		if !filters.IsSeverAll {
			strategyRecord.Type = 2
		}
		dest = append(dest, strategyRecord)
	}
	return dest, uint32(total), nil
}
func (s *StrategyDao) GetStrategyByProject(project string) (*models.FpOpsTicketsStrategy, error) {
	var detail = &models.FpOpsTicketsStrategy{}
	err := s.db.Model(detail).Where("project = ? and enable = ?", project, 1).First(detail).Error
	if err != nil {
		if !errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, xerrors.New(err.Error(), code.DbError)
		} else {
			return nil, nil
		}
	}
	return detail, nil
}
