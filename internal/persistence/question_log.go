package persistence

import (
	"github.com/spf13/cast"
	"gorm.io/gorm"
	"ops-ticket-api/internal/code"
	"ops-ticket-api/internal/framework/database"
	"ops-ticket-api/internal/framework/xerrors"
	"ops-ticket-api/models"
	"ops-ticket-api/proto/pb"
)

// QuestionTicketLog 工单知识库日志
type QuestionTicketLog struct {
	db *gorm.DB
}

// NewQuestionTicketLog init
func NewQuestionTicketLog() *QuestionTicketLog {
	return &QuestionTicketLog{
		db: database.Db(),
	}
}

func (q *QuestionTicketLog) AddQuestionOpLog(tx *gorm.DB, log *models.FpOpsTicketsQuestionLog) error {
	err := tx.Create(log).Error
	if err != nil {
		return xerrors.New(code.StatusText(code.DbError), code.DbError)
	}
	return nil
}

func (q *QuestionTicketLog) GetEditAndDeleteQuestionId(gameProject string, l string, startTime uint64) ([]int64, error) {
	var uniqueIds []string
	tx := q.db.Model(&models.FpOpsTicketsQuestionLog{}).Where("operation_action in (?,?) and project = ? and "+
		"lang = ? and created_at > ?", pb.OpAction_OpActionUpdate, pb.OpAction_OpActionDelete, gameProject, l, startTime)
	err := tx.Select("unique_id").Pluck("unique_id", &uniqueIds).Error
	if err != nil {
		return nil, err
	}
	var qIds []int64
	for _, v := range uniqueIds {
		qIds = append(qIds, cast.ToInt64(v))
	}
	return qIds, nil
}
