package persistence

import (
	"context"
	"errors"
	"fmt"
	"ops-ticket-api/internal/code"
	"ops-ticket-api/internal/cst"
	"ops-ticket-api/internal/elasticsearch"
	"ops-ticket-api/internal/framework/database"
	"ops-ticket-api/internal/framework/xerrors"
	"ops-ticket-api/models"
	"ops-ticket-api/proto/pb"
	"ops-ticket-api/utils"
	"strings"
	"sync"

	"github.com/labstack/echo/v4"
	"github.com/spf13/cast"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// DiscordTag discord标签
type DiscordTag struct {
	db *gorm.DB
}

// NewDiscordTag init
func NewDiscordTag() *DiscordTag {
	return &DiscordTag{
		db: database.Db(),
	}
}

func (d *DiscordTag) DiscordTagSave(ctx echo.Context, req *pb.DiscordTagAddReq) error {
	operator := cast.ToString(ctx.Get(cst.AccountInfoCtx))
	current := utils.NowTimestamp()
	updateTime := utils.TimeFormat(int64(current))
	record := &models.FpDscTag{
		TagName:     req.TagName,
		TagDesc:     req.TagDesc,
		Operator:    operator,
		Enable:      uint8(req.Status),
		CreateTime:  current,
		UpdatedTime: updateTime,
	}
	if err := d.db.Table(models.GetFpDscTagTableName()).Create(record).Error; err != nil {
		logger.Error(ctx.Request().Context(), "error create dsc tag", zap.String("err", err.Error()))
		return xerrors.New(err.Error(), code.DbError)
	}
	return nil
}

func (d *DiscordTag) DiscordTagEdit(ctx echo.Context, req *pb.DiscordTagEditReq) error {
	// 查询出原记录
	record := &models.FpDscTag{}
	err := d.db.Table(models.GetFpDscTagTableName()).Where("id = ?", req.Id).First(record).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return xerrors.New(err.Error(), code.DbError)
	} else if errors.Is(err, gorm.ErrRecordNotFound) {
		return xerrors.New("标签不存在", code.InvalidParams)
	}
	operator := cast.ToString(ctx.Get(cst.AccountInfoCtx))
	updateTime := utils.TimeFormat(int64(utils.NowTimestamp()))
	// 更新discord标签数据
	record.TagName = req.TagName
	record.TagDesc = req.TagDesc
	record.Operator = operator
	record.Enable = uint8(req.Status)
	record.UpdatedTime = updateTime
	err = d.db.Table(models.GetFpDscTagTableName()).Where("id=?", req.Id).Updates(record).Error
	if err != nil {
		logger.Error(ctx.Request().Context(), "error update dsc tag", zap.String("err", err.Error()))
		return xerrors.New(err.Error(), code.DbError)
	}
	return nil
}

func (d *DiscordTag) DiscordTagList(ctx echo.Context, req *pb.DiscordTagListReq) (*pb.DiscordTagListResp, error) {
	dest := make([]*pb.DiscordTagListResp_TagDetail, 0)
	tagList := []*models.FpDscTag{}
	query := d.db.Table(models.GetFpDscTagTableName())
	if req.TagName != "" {
		query = query.Where("tag_name LIKE ?", "%"+req.TagName+"%")
	}
	if req.Operator != "" {
		query = query.Where("operator = ?", req.Operator)
	}
	if req.Status > 0 {
		query = query.Where("enable = ?", req.Status)
	}
	if err := query.Order("update_time DESC").Find(&tagList).Error; err != nil {
		logger.Error(ctx.Request().Context(), "get dsc tag list err", zap.String("err", err.Error()))
		return nil, xerrors.New(err.Error(), code.DbError)
	}
	resp := new(pb.DiscordTagListResp)
	for _, v := range tagList {
		dest = append(dest, &pb.DiscordTagListResp_TagDetail{
			Id:         int32(v.ID),
			TagName:    v.TagName,
			TagDesc:    v.TagDesc,
			UpdateTime: v.UpdatedTime,
			Operator:   v.Operator,
			Status:     int32(v.Enable),
		})
	}
	resp.Data = dest
	return resp, nil
}

func (d *DiscordTag) GetDiscordTagById(ctx echo.Context, tagIds []int64) ([]string, error) {
	tagNameList := []string{}
	if err := d.db.Table(models.FpOpsTags{}.TableName()).Where("tag_id IN (?)", tagIds).Pluck("tag_name", &tagNameList).Error; err != nil {
		logger.Error(ctx.Request().Context(), "get dsc tag name list err", zap.String("err", err.Error()))
		return nil, xerrors.New(err.Error(), code.DbError)
	}
	return tagNameList, nil
}

func (d *DiscordTag) GetDiscordTagsById(ctx echo.Context, tagIds []int64) ([]*pb.PortraitInfoResp_LabelDetail, error) {
	dest := []*pb.PortraitInfoResp_LabelDetail{}
	if err := d.db.Table(models.FpOpsTags{}.TableName()).Where("tag_id IN (?)", tagIds).Find(&dest).Error; err != nil {
		logger.Error(ctx.Request().Context(), "get dsc tags err", zap.String("err", err.Error()))
		return nil, xerrors.New(err.Error(), code.DbError)
	}
	return dest, nil
}

func (d *DiscordTag) DiscordBatchTag(ctx echo.Context, req *pb.DiscordBatchTagReq) error {
	current := utils.NowTimestamp()
	updateTime := utils.TimeFormat(int64(current))
	var wg sync.WaitGroup
	errCh := make(chan error, len(req.DscUserIdList))
	tagRecordsChan := make(chan []*models.FpDscPlayerTag, len(req.DscUserIdList))
	for _, dscUserId := range req.DscUserIdList {
		wg.Add(1)
		go func(dscUserId string) {
			defer wg.Done()
			// 在每个goroutine中开启事务
			tx := d.db.WithContext(ctx.Request().Context()).Begin()
			defer func() {
				// 如果panic，确保回滚
				if r := recover(); r != nil {
					tx.Rollback()
					errCh <- fmt.Errorf("panic: %v", r)
				}
			}()
			dest := &models.FpDscPlayerPortrait{}
			// 画像数据有可能还没有
			err := tx.Table(models.GetFpDscPlayerPortraitTableName()).Where("dsc_user_id = ? AND game_project = ?", dscUserId, req.Project).First(&dest).Error
			if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
				tx.Rollback()
				errCh <- err
				return
			}
			// 将玩家原有的标签与新打的标签去重后整合到一起
			labelList := strings.Split(dest.NewLabel, ",")
			labelList = append(labelList, strings.Split(req.Tag, ",")...)
			mark := make(map[string]bool)
			uniqueLabelList := []string{}
			labelIds := []int64{}
			for _, label := range labelList {
				if label == "" {
					continue
				}
				if !mark[label] {
					uniqueLabelList = append(uniqueLabelList, label)
					labelIds = append(labelIds, cast.ToInt64(label))
					mark[label] = true
				}
			}
			dscLabels := strings.Join(uniqueLabelList, ",")
			attribute := map[string]interface{}{
				"new_label":   dscLabels,
				"update_time": updateTime,
			}
			// 没有画像数据的，新增画像数据，填充标签信息
			if dest.ID == 0 {
				record := models.FpDscPlayerPortrait{
					DscUserId:   dscUserId,
					GameProject: req.Project,
					NewLabel:    dscLabels,
					CreateTime:  current,
					UpdatedTime: updateTime,
				}
				if err = tx.Table(models.GetFpDscPlayerPortraitTableName()).Save(&record).Error; err != nil {
					tx.Rollback()
					logger.Error(ctx.Request().Context(), "error create portrait", zap.String("err", err.Error()))
					errCh <- err
					return
				}
			} else {
				// 有画像数据的，更新标签数据
				if err = tx.Table(models.GetFpDscPlayerPortraitTableName()).Where("id = ?", dest.ID).Updates(attribute).Error; err != nil {
					tx.Rollback()
					logger.Error(ctx.Request().Context(), "error update portrait", zap.String("err", err.Error()))
					errCh <- err
					return
				}
			}
			// 收集玩家discord绑定标签数据
			records := make([]*models.FpDscPlayerTag, len(labelIds))
			for i, id := range labelIds {
				dscTagRecord := &models.FpDscPlayerTag{
					Project:     req.Project,
					DscUserId:   dscUserId,
					LabelId:     id,
					CreateTime:  current,
					UpdatedTime: updateTime,
				}
				records[i] = dscTagRecord
			}
			tagRecordsChan <- records
			// 获取channelId
			channelIds := []string{}
			if err = tx.Model(&models.FpDscUser{}).Where("dsc_user_id = ? AND project = ? AND priv_channel_id != ?", dscUserId, req.Project, "").Pluck("priv_channel_id", &channelIds).Error; err != nil {
				tx.Rollback()
				errCh <- err
				return
			}
			// sync to es
			if err != nil {
				tx.Rollback()
				errCh <- err
				return
			}
			if err = elasticsearch.DefaultDscEsSvc.UpdateTag(ctx.Request().Context(), channelIds, labelIds); err != nil {
				errCh <- err
				return
			}
			// 在goroutine中提交事务
			if err = tx.Commit().Error; err != nil {
				logger.Error(ctx.Request().Context(), "[BatchTag] transaction commit err", zap.String("err", err.Error()))
				errCh <- err
				return
			}
		}(dscUserId)
	}

	// 等待所有并发任务执行完成
	wg.Wait()
	// 关闭通道
	close(errCh)
	close(tagRecordsChan)
	// 检查错误
	for err := range errCh {
		if err != nil {
			return err
		}
	}
	// 处理玩家discord绑定标签数据
	// 先删除原来的标签
	tx := d.db.WithContext(ctx.Request().Context()).Begin()
	for _, dscUserId := range req.DscUserIdList {
		if err := tx.Table(models.GetFpDscPlayerTagTableName()).Where("project = ? AND dsc_user_id = ?", req.Project, dscUserId).Delete(&models.FpDscPlayerTag{}).Error; err != nil {
			logger.Error(ctx.Request().Context(), "[BatchTag] delete old tags err", zap.String("err", err.Error()))
			tx.Rollback()
			return err
		}
	}
	// 再插入新的标签数据
	for records := range tagRecordsChan {
		if err := tx.Table(models.GetFpDscPlayerTagTableName()).CreateInBatches(records, len(records)).Error; err != nil {
			logger.Error(ctx.Request().Context(), "[BatchTag] insert new tags err", zap.String("err", err.Error()))
			tx.Rollback()
			return err
		}
	}
	if err := tx.Commit().Error; err != nil {
		logger.Error(ctx.Request().Context(), "[BatchBindTag] transaction commit err", zap.String("err", err.Error()))
		return err
	}
	return nil
}

func (d *DiscordTag) SyncHistoryTags() error {
	current := utils.NowTimestamp()
	updateTime := utils.TimeFormat(int64(current))
	// 查询出打过标签的所有玩家画像数据
	data := []*models.FpDscPlayerPortrait{}
	if err := d.db.Table(models.GetFpDscPlayerPortraitTableName()).Where("label != ?", "").Find(&data).Error; err != nil {
		return err
	}
	for i := range data {
		// 获取画像下的标签id数据
		portrait := data[i]
		labelList := strings.Split(portrait.Label, ",")
		labelIds := []int64{}
		for _, label := range labelList {
			if label == "" {
				continue
			}
			labelIds = append(labelIds, cast.ToInt64(label))
		}
		if len(labelIds) == 0 {
			continue
		}
		// 处理玩家discord绑定标签数据
		records := make([]*models.FpDscPlayerTag, len(labelIds))
		for j, id := range labelIds {
			dscTagRecord := &models.FpDscPlayerTag{
				Project:     portrait.GameProject,
				DscUserId:   portrait.DscUserId,
				TagId:       id,
				CreateTime:  current,
				UpdatedTime: updateTime,
			}
			records[j] = dscTagRecord
		}
		// 保存玩家绑定的标签数据
		if err := d.db.Table(models.GetFpDscPlayerTagTableName()).CreateInBatches(records, len(records)).Error; err != nil {
			return err
		}
	}
	return nil
}

func (d *DiscordTag) GetPublicTags(ctx context.Context, dscUserIds []string) ([]*pb.DiscordPublicTagResp_TagInfo, error) {
	var resp = []*pb.DiscordPublicTagResp_TagInfo{}
	var DscTagDetail = []*models.FpDscPlayerTag{}

	// 1. 查询所有用户的标签
	err := d.db.Model(&models.FpDscPlayerTag{}).
		Where("dsc_user_id in (?)", dscUserIds).Find(&DscTagDetail).Error
	if err != nil {
		logger.Error(ctx, "GetPublicTags ticket detail info err", zap.Error(err))
		return nil, xerrors.New(err.Error(), code.DbError)
	}

	// 2. 统计每个用户的标签
	tagsMap := make(map[string][]uint32)
	for _, v := range DscTagDetail {
		tagsMap[v.DscUserId] = append(tagsMap[v.DscUserId], uint32(v.LabelId))
	}

	// 3. 检查每个传入的用户ID是否都有标签
	// 如果任何一个用户没有标签，直接返回空结果
	for _, dscUserId := range dscUserIds {
		tags, exists := tagsMap[dscUserId]
		if !exists || len(tags) == 0 {
			return resp, nil // 只要有一个用户没有标签，就返回空
		}
	}

	// 4. 计算所有用户标签的交集
	var commonTagIDs []uint32
	isFirst := true

	for _, dscUserId := range dscUserIds { // 使用传入的dscUserIds来遍历，确保顺序
		if isFirst {
			commonTagIDs = tagsMap[dscUserId]
			isFirst = false
			continue
		}
		commonTagIDs = utils.IntersectAny(commonTagIDs, tagsMap[dscUserId])
		if len(commonTagIDs) == 0 {
			return resp, nil // 如果交集为空，提前返回
		}
	}

	// 5. 获取标签详情并返回
	for tagId, v := range NewTags().GetTagsSlice(ctx, commonTagIDs) {
		tagDesc := strings.Join(v, "-")
		resp = append(resp, &pb.DiscordPublicTagResp_TagInfo{
			TagId:   tagId,
			TagName: tagDesc,
		})
	}

	return resp, nil
}

// BatchDeleteDiscordTags 批量删除标签
func (d *DiscordTag) BatchDeleteDiscordTags(ctx context.Context, dscUserID string, delTags []uint32, operator, project string) error {
	var (
		fun = "discord.BatchDeleteDiscordTags ->"
		err error
	)

	tx := d.db.WithContext(ctx).Begin()

	defer func() {
		if err != nil {
			logger.Errorf(ctx, "%s transaction return err. err:%v. dscUserId:%d", fun, err, dscUserID)
			tx.Rollback()
			return
		}
	}()

	// del
	if len(delTags) > 0 {
		// 删除标签
		if err = tx.Model(&models.FpDscPlayerTag{}).
			Where("project = ? AND dsc_user_id = ? AND label_id in (?)", project, dscUserID, delTags).
			Delete(&models.FpOpsTicketsTags{}).Error; err != nil {
			return err
		}
		// 获取删除后的剩余标签
		var labelIds []int64
		if err = tx.Model(&models.FpDscPlayerTag{}).
			Where("project = ? AND dsc_user_id = ?", project, dscUserID).
			Pluck("label_id", &labelIds).Error; err != nil {
			return err
		}
		newLabels := strings.Trim(strings.Join(utils.Int64SliceToStringSlice(labelIds), ","), ",")

		// 同步更新画像表的标签
		if err = tx.Model(&models.FpDscPlayerPortrait{}).
			Where("game_project = ? AND dsc_user_id = ?", project, dscUserID).
			Update("new_label", newLabels).Error; err != nil {
			return err
		}
	}

	// 获取channelId
	channelIds := []string{}
	if err = tx.Model(&models.FpDscUser{}).Where("dsc_user_id = ? AND project = ? AND priv_channel_id != ?", dscUserID, project, "").
		Pluck("priv_channel_id", &channelIds).Error; err != nil {
		tx.Rollback()
		return err
	}
	// commit
	if err = tx.Commit().Error; err != nil {
		return err
	}

	if len(delTags) > 0 {
		elasticsearch.DefaultDscEsSvc.DeleteTag(ctx, channelIds, delTags)
	}
	return err
}
