package persistence

import (
	"errors"
	"github.com/google/uuid"
	"github.com/labstack/echo/v4"
	"github.com/spf13/cast"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"go.uber.org/zap"
	"golang.org/x/sync/errgroup"
	"gorm.io/gorm"
	"ops-ticket-api/internal/code"
	"ops-ticket-api/internal/cst"
	"ops-ticket-api/internal/elasticsearch"
	"ops-ticket-api/internal/framework/database"
	"ops-ticket-api/internal/framework/xerrors"
	"ops-ticket-api/models"
	"ops-ticket-api/proto/pb"
	"ops-ticket-api/utils"
	"strings"
	"time"
)

// Portrait 玩家画像信息
type Portrait struct {
	db *gorm.DB
}

// NewPortrait init
func NewPortrait() *Portrait {
	return &Portrait{
		db: database.Db(),
	}
}

func (p *Portrait) DiscordPortraitInfo(ctx echo.Context, req *pb.PortraitInfoReq) (*pb.PortraitInfoResp, error) {
	dest := &models.FpDscPlayerPortrait{}
	err := p.db.Table(models.GetFpDscPlayerPortraitTableName()).Where("dsc_user_id = ? AND game_project = ?", req.DscUserId, req.GameProject).First(&dest).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, xerrors.New(err.Error(), code.DbError)
	} else if errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, nil
	}

	resp := &pb.PortraitInfoResp{
		Id:             dest.ID,
		Label:          make([]*pb.PortraitInfoResp_LabelDetail, 0),
		Gender:         uint32(dest.Gender),
		Birthday:       dest.Birthday,
		Career:         dest.Career,
		EducationLevel: uint32(dest.EducationLevel),
		MarriedState:   uint32(dest.MarriedState),
		FertilityState: uint32(dest.FertilityState),
		Remark:         dest.Remark,
		DscUserId:      dest.DscUserId,
		GameProject:    dest.GameProject,
	}
	var wg = errgroup.Group{}
	wg.Go(func() error { // 标签信息
		labels := getTagByLabelId(ctx, dest.NewLabel)
		resp.NewLabel = labels
		return nil
	})
	wg.Go(func() error { // 历史工单信息
		_, accountId, _, err := NewDiscordInteract().FetchNickNameAndUidByDscUserId(req.GameProject, req.DscUserId)
		if err != nil {
			return err
		}
		ticketInfo, err := NewTicket().NewTicketsUserRecords(accountId, req.GameProject)
		if err != nil {
			return err
		}
		if ticketInfo != nil {
			resp.TicketInfo = ticketInfo
		}
		return err
	})
	if err := wg.Wait(); err != nil {
		return nil, err
	}
	return resp, nil
}

func (p *Portrait) EditPortraitInfo(ctx echo.Context, req *pb.PortraitEditReq) error {
	currentTime := utils.NowTimestamp()
	tx := p.db.WithContext(ctx.Request().Context()).Begin()
	// 编辑
	if req.Id > 0 {
		var pi models.FpDscPlayerPortrait
		err := tx.Table(models.GetFpDscPlayerPortraitTableName()).Where("id = ?", req.Id).First(&pi).Error
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return xerrors.New(err.Error(), code.DbError)
		} else if errors.Is(err, gorm.ErrRecordNotFound) {
			return gorm.ErrRecordNotFound
		}
		remark := pi.Remark
		detail := pi
		pi.NewLabel = req.Label
		pi.Gender = uint8(req.Gender)
		pi.Birthday = req.Birthday
		pi.Career = req.Career
		pi.EducationLevel = uint8(req.EducationLevel)
		pi.MarriedState = uint8(req.MarriedState)
		pi.FertilityState = uint8(req.FertilityState)
		pi.Remark = req.Remark
		pi.DscUserId = req.DscUserId
		pi.UpdatedTime = utils.TimeFormat(int64(currentTime))
		if err = tx.Table(models.GetFpDscPlayerPortraitTableName()).Select("*").Where("id = ?", req.Id).Updates(pi).Error; err != nil {
			tx.Rollback()
			logger.Error(ctx.Request().Context(), "error update portrait", zap.String("err", err.Error()))
			return err
		}
		// 获取channelId
		channelIds := []string{}
		if err = tx.Model(&models.FpDscUser{}).Where("dsc_user_id = ? AND project = ? AND priv_channel_id != ?", pi.DscUserId, pi.GameProject, "").Pluck("priv_channel_id", &channelIds).Error; err != nil {
			tx.Rollback()
			return err
		}
		// sync to es
		// 更新标签信息
		if req.Label != "" {
			// 如果 req.Label 不为空，则按照原先的方式更新标签
			dscLabels := strings.Split(req.Label, ",")
			labelIds := make([]int64, len(dscLabels))
			for i := range dscLabels {
				labelIds[i] = cast.ToInt64(dscLabels[i])
			}
			records := make([]*models.FpDscPlayerTag, len(labelIds))
			for i, id := range labelIds {
				dscTagRecord := &models.FpDscPlayerTag{
					Project:     req.GameProject,
					DscUserId:   req.DscUserId,
					LabelId:     id,
					CreateTime:  currentTime,
					UpdatedTime: utils.TimeFormat(int64(currentTime)),
				}
				records[i] = dscTagRecord
			}
			// 先删除原来的标签
			if err = tx.Table(models.GetFpDscPlayerTagTableName()).Where("project = ? AND dsc_user_id = ?", req.GameProject, req.DscUserId).Delete(&models.FpDscPlayerTag{}).Error; err != nil {
				tx.Rollback()
				logger.Error(ctx.Request().Context(), "error delete player tags", zap.String("err", err.Error()))
				return err
			}
			// 再插入新的标签数据
			if err = tx.Table(models.GetFpDscPlayerTagTableName()).CreateInBatches(records, len(records)).Error; err != nil {
				tx.Rollback()
				logger.Error(ctx.Request().Context(), "error create player tags", zap.String("err", err.Error()))
				return err
			}
			if err = elasticsearch.DefaultDscEsSvc.UpdateTag(ctx.Request().Context(), channelIds, labelIds); err != nil {
				tx.Rollback()
				return err
			}
		} else {
			// 如果 req.Label 为空，删除现有标签
			if err = tx.Table(models.GetFpDscPlayerTagTableName()).Where("project = ? AND dsc_user_id = ?", req.GameProject, req.DscUserId).Delete(&models.FpDscPlayerTag{}).Error; err != nil {
				tx.Rollback()
				logger.Error(ctx.Request().Context(), "error delete player tags", zap.String("err", err.Error()))
				return err
			}
			// 在 Elasticsearch 中更新标签为空（删除标签）
			if err = elasticsearch.DefaultDscEsSvc.UpdateTag(ctx.Request().Context(), channelIds, []int64{}); err != nil {
				tx.Rollback()
				return err
			}
		}

		// 更新备注信息
		if req.Remark != "" {
			dest := map[string]interface{}{
				"portrait_remark": req.Remark,
				"updated_at":      utils.NowTimestamp(),
			}
			for _, channelId := range channelIds {
				if err = elasticsearch.DefaultDscEsSvc.UpdateDsc(ctx.Request().Context(), channelId, dest); err != nil {
					tx.Rollback()
					return err
				}
			}
		}
		// 清空备注信息
		if remark != "" && req.Remark == "" {
			updatedAt := utils.NowTimestamp()
			for _, channelId := range channelIds {
				if err = elasticsearch.DefaultDscEsSvc.ClearRemark(ctx.Request().Context(), channelId, updatedAt); err != nil {
					tx.Rollback()
					return err
				}
			}
		}
		// 更新生日信息
		if req.Birthday != "" {
			dest := map[string]interface{}{
				"birthday":           req.Birthday,
				"birthday_month_day": req.Birthday[5:],
			}
			for _, channelId := range channelIds {
				if err = elasticsearch.DefaultDscEsSvc.UpdateDsc(ctx.Request().Context(), channelId, dest); err != nil {
					tx.Rollback()
					return err
				}
			}
		}
		// 记录操作日志
		opDetail := &models.FpDscOperateLog{
			OperationGroup:  pb.OpGroup_OpGroupPortrait.String(),
			OperationAction: pb.OpAction_OpActionUpdate.String(),
			BaseID:          detail.DscUserId,
			UniqueID:        uuid.New().String(),
			GameProject:     req.GameProject,
			BeforeDetail:    utils.ToJson(detail),
			AfterDetail:     utils.ToJson(pi),
			CreateTime:      time.Now(),
			Operator:        cast.ToString(ctx.Get(cst.AccountInfoCtx)),
		}
		err = tx.Model(&models.FpDscOperateLog{}).Create(opDetail).Error
		if err != nil {
			tx.Rollback()
			logger.Error(ctx.Request().Context(), "error add operate log", zap.String("err", err.Error()))
			return xerrors.New(err.Error(), code.DbError)
		}
		// 提交事务
		if err = tx.Commit().Error; err != nil {
			logger.Error(ctx.Request().Context(), "[PortraitEdit] transaction commit err", zap.String("err", err.Error()))
			return xerrors.New(err.Error(), code.DbError)
		}
		return nil
	} else {
		// 新增
		var cnt int64
		err := tx.Table(models.GetFpDscPlayerPortraitTableName()).Where("dsc_user_id = ? AND game_project = ?", req.DscUserId, req.GameProject).Count(&cnt).Error
		if err != nil {
			return err
		}
		if cnt >= 1 {
			return xerrors.New("该玩家的画像信息已存在", code.RepeatData)
		}
		record := models.FpDscPlayerPortrait{
			DscUserId:      req.DscUserId,
			GameProject:    req.GameProject,
			NewLabel:       req.Label,
			Gender:         uint8(req.Gender),
			Birthday:       req.Birthday,
			Career:         req.Career,
			EducationLevel: uint8(req.EducationLevel),
			MarriedState:   uint8(req.MarriedState),
			FertilityState: uint8(req.FertilityState),
			Remark:         req.Remark,
			CreateTime:     currentTime,
			UpdatedTime:    utils.TimeFormat(int64(currentTime)),
		}
		if err = tx.Table(models.GetFpDscPlayerPortraitTableName()).Save(&record).Error; err != nil {
			tx.Rollback()
			logger.Error(ctx.Request().Context(), "error create portrait", zap.String("err", err.Error()))
			return err
		}
		// 获取channelId
		channelIds := []string{}
		if err = tx.Model(&models.FpDscUser{}).Where("dsc_user_id = ? AND project = ? AND priv_channel_id != ?", req.DscUserId, req.GameProject, "").Pluck("priv_channel_id", &channelIds).Error; err != nil {
			tx.Rollback()
			return err
		}
		// 新增标签、备注和生日信息
		// sync to es
		if req.Label != "" {
			dscLabels := strings.Split(req.Label, ",")
			labelIds := make([]int64, len(dscLabels))
			for i := range dscLabels {
				labelIds[i] = cast.ToInt64(dscLabels[i])
			}
			records := make([]*models.FpDscPlayerTag, len(labelIds))
			for i, id := range labelIds {
				dscTagRecord := &models.FpDscPlayerTag{
					Project:     req.GameProject,
					DscUserId:   req.DscUserId,
					LabelId:     id,
					CreateTime:  currentTime,
					UpdatedTime: utils.TimeFormat(int64(currentTime)),
				}
				records[i] = dscTagRecord
			}
			if err = tx.Table(models.GetFpDscPlayerTagTableName()).CreateInBatches(records, len(records)).Error; err != nil {
				tx.Rollback()
				logger.Error(ctx.Request().Context(), "error create player tags", zap.String("err", err.Error()))
				return err
			}
			if err != nil {
				tx.Rollback()
				return err
			}
			if err = elasticsearch.DefaultDscEsSvc.UpdateTag(ctx.Request().Context(), channelIds, labelIds); err != nil {
				tx.Rollback()
				return err
			}
		}
		if req.Remark != "" {
			dest := map[string]interface{}{
				"portrait_remark": req.Remark,
				"updated_at":      utils.NowTimestamp(),
			}
			for _, channelId := range channelIds {
				if err = elasticsearch.DefaultDscEsSvc.UpdateDsc(ctx.Request().Context(), channelId, dest); err != nil {
					tx.Rollback()
					return err
				}
			}
		}
		// 生日
		if req.Birthday != "" {
			dest := map[string]interface{}{
				"birthday":           req.Birthday,
				"birthday_month_day": req.Birthday[5:],
			}
			for _, channelId := range channelIds {
				if err = elasticsearch.DefaultDscEsSvc.UpdateDsc(ctx.Request().Context(), channelId, dest); err != nil {
					tx.Rollback()
					return err
				}
			}
		}
		// 记录操作日志
		opDetail := &models.FpDscOperateLog{
			OperationGroup:  pb.OpGroup_OpGroupPortrait.String(),
			OperationAction: pb.OpAction_OpActionAdd.String(),
			BaseID:          record.DscUserId,
			UniqueID:        uuid.New().String(),
			GameProject:     req.GameProject,
			BeforeDetail:    "{}",
			AfterDetail:     utils.ToJson(record),
			CreateTime:      time.Now(),
			Operator:        cast.ToString(ctx.Get(cst.AccountInfoCtx)),
		}
		err = tx.Model(&models.FpDscOperateLog{}).Create(opDetail).Error
		if err != nil {
			tx.Rollback()
			logger.Error(ctx.Request().Context(), "error add operate log", zap.String("err", err.Error()))
			return xerrors.New(err.Error(), code.DbError)
		}
		// 提交事务
		if err = tx.Commit().Error; err != nil {
			logger.Error(ctx.Request().Context(), "[PortraitEdit] transaction commit err", zap.String("err", err.Error()))
			return xerrors.New(err.Error(), code.DbError)
		}
		return nil
	}
}
func getTagByLabelId(ctx echo.Context, newLabel string) []*pb.PortraitInfoResp_NewLabelDetail {
	labels := []*pb.PortraitInfoResp_NewLabelDetail{}
	if newLabel == "" {
		labels = make([]*pb.PortraitInfoResp_NewLabelDetail, 0)
	} else {
		dscLabels := strings.Split(newLabel, ",")
		labelIds := make([]uint32, len(dscLabels))
		for i := range dscLabels {
			labelIds[i] = cast.ToUint32(dscLabels[i])
		}

		for id, v := range NewTags().GetTagsSlice(ctx.Request().Context(), labelIds) {
			label := &pb.PortraitInfoResp_NewLabelDetail{
				TagId:   uint64(id),
				TagDesc: strings.Join(v, "-"),
			}
			labels = append(labels, label)
		}
	}
	return labels
}
