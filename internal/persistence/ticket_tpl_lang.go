// Copyright 2021 funplus Authors. All Rights Reserved.
// @Description: 模板配置
// @Author: Darcy
// @Date: 2021/10/26 3:49 PM

package persistence

import (
	"context"
	"errors"
	"ops-ticket-api/internal/code"
	"ops-ticket-api/internal/framework/database"
	"ops-ticket-api/internal/framework/xerrors"
	"ops-ticket-api/models"

	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// TplLang 问题模版
type TplLang struct {
	db *gorm.DB
}

// NewTplLang init
func NewTplLang(c context.Context) *TplLang {
	return &TplLang{
		db: database.GetOldTicketDb(),
	}
}

// TplInfoMultiLang 多语言模版信息
func (dto *TplLang) TplInfoMultiLang(ctx context.Context, tplId uint32, language string) (*models.FpOpsTplLang, error) {
	tplInfo := &models.FpOpsTplLang{}
	where := map[string]interface{}{
		"tpl_id": tplId,
		"lang":   language,
	}
	if err := dto.db.Model(&models.FpOpsTplLang{}).Where(where).Take(&tplInfo).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			if language == "en" {
				return nil, xerrors.New(code.StatusText(code.NotFound), code.NotFound)
			}
			return dto.TplInfoMultiLang(ctx, tplId, "en")
		}
		logger.Error(ctx, "get tpl info err", zap.Uint32("tplId", tplId), zap.String("err", err.Error()))
		return nil, xerrors.New(code.StatusText(code.DbError), code.DbError)
	}
	return tplInfo, nil
}
