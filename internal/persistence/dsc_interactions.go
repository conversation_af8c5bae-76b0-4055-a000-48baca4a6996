package persistence

import (
	"context"
	"errors"
	"fmt"
	"github.com/google/uuid"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"go.uber.org/zap"
	"gorm.io/datatypes"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/plugin/dbresolver"
	"ops-ticket-api/internal/code"
	"ops-ticket-api/internal/framework/database"
	"ops-ticket-api/internal/framework/xerrors"
	"ops-ticket-api/models"
	"ops-ticket-api/proto/pb"
	"ops-ticket-api/utils"
	"sync"
	"time"
)

var (
	dscInteractionsOnce sync.Once
	dscInteractionsRepo *dscInteractions
)

// NewDscInteractions init
func NewDscInteractions() *dscInteractions {
	dscInteractionsOnce.Do(func() {
		dscInteractionsRepo = &dscInteractions{
			db: database.Db(),
		}
	})
	return dscInteractionsRepo
}

type dscInteractions struct {
	db *gorm.DB
}

func (repo *dscInteractions) GetDb(ctx context.Context) *gorm.DB {
	return repo.db.WithContext(ctx)
}

func (repo *dscInteractions) CreateEventLog(ctx context.Context, appId, mGroup, typ, log, extra string) error {
	var detail = &models.FpDscEventNotice{
		AppID:     appId,
		MGroup:    mGroup,
		T:         typ,
		Log:       datatypes.JSON([]byte(log)),
		Extra:     extra,
		CreatedAt: time.Now(),
	}
	return repo.db.WithContext(ctx).Create(detail).Error
}

func (repo *dscInteractions) AddDscNotice(ctx context.Context, data *models.FpDsNotice) error {
	return repo.db.WithContext(ctx).Create(data).Error
}

func (repo *dscInteractions) GetDscBotAll(ctx context.Context) ([]*models.FpDscBot, error) {
	var data []*models.FpDscBot
	err := repo.db.WithContext(ctx).Where("is_delete = ?", 0).Find(&data).Error
	return data, err
}

func (repo *dscInteractions) GetDscBotList(ctx context.Context, gms []string) ([]*models.FpDscBot, error) {
	var bots []*models.FpDscBot
	tx := repo.db.WithContext(ctx)
	if len(gms) > 0 {
		tx = tx.Where("project IN (?)", gms)
	}
	err := tx.Find(&bots).Error
	return bots, err
}

func (repo *dscInteractions) GetDscUserByChannel(ctx context.Context, channelId string) (*models.FpDscUser, error) {
	var data *models.FpDscUser
	err := repo.db.WithContext(ctx).Where("priv_channel_id = ?", channelId).Last(&data).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, err
	}
	return data, nil
}

func (repo *dscInteractions) GetDscUsers(ctx context.Context, appId string) ([]*models.FpDscUser, error) {
	var data []*models.FpDscUser
	err := repo.db.WithContext(ctx).Where("app_id = ? AND is_deleted = ?", appId, code.DscNoDelete).Find(&data).Error
	return data, err
}

func (repo *dscInteractions) GetDscUserForMaster(ctx context.Context, dscUserId, botUserId string) (*models.FpDscUser, error) {
	var detail *models.FpDscUser
	var query = repo.db.WithContext(ctx).Model(detail).Clauses(dbresolver.Write).Where("dsc_user_id = ?", dscUserId)
	if botUserId != "" {
		query = query.Where("app_id = ?", botUserId)
	}
	err := query.Last(&detail).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, err
	}
	return detail, nil
}

func (repo *dscInteractions) GetDscUserNickName(ctx context.Context, dscUserId, botUserId string) string {
	detail, err := repo.GetDscUserForMaster(ctx, dscUserId, botUserId)
	if err != nil || detail == nil {
		logger.Errorf(ctx, "GetDscUserNickName return err Or nil. err:%v. detail:%+v", err, detail)
	}
	nickName := detail.GlobalName
	if detail.GlobalName == "" {
		nickName = detail.UserName
	}
	return nickName
}

func (repo *dscInteractions) GetDscUserWithChannelForMaster(ctx context.Context, channelId string) (*models.FpDscUser, error) {
	var detail *models.FpDscUser
	err := repo.db.WithContext(ctx).Model(detail).
		Where("priv_channel_id = ?", channelId).Last(&detail).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, err
	}
	return detail, nil
}
func (repo *dscInteractions) AddDscUser(ctx context.Context, tx *gorm.DB, detail *models.FpDscUser) error {
	if tx == nil {
		tx = repo.db.WithContext(ctx)
	}
	if err := tx.Create(detail).Error; err != nil {
		return err
	}
	return repo.CreateMaintainConfig(ctx, tx, detail)
}

func (repo *dscInteractions) CreateMaintainConfig(ctx context.Context, tx *gorm.DB, detail *models.FpDscUser) error {
	// 初始化玩家维护配置
	current := utils.NowTimestamp()
	operateTime := utils.TimeFormat(int64(current))
	nickName := detail.GlobalName
	if detail.GlobalName == "" {
		nickName = detail.UserName
	}
	maintainConfig := models.FpDscPlayerMaintainConfig{
		DscUserId:         detail.DscUserID,
		GameProject:       detail.Project,
		NickName:          nickName,
		Operator:          "system",
		VipState:          code.NonVip,
		PayAll:            0,
		PayLastThirtyDays: 0,
		CreateTime:        current,
		UpdatedTime:       operateTime,
	}
	if err := tx.Table(models.GetFpDscPlayerMaintainConfigTableName()).Clauses(clause.OnConflict{DoNothing: true}).Create(&maintainConfig).Error; err != nil {
		return err
	}
	return nil
}

func (repo *dscInteractions) UpdateDscUser(ctx context.Context, tx *gorm.DB, up map[string]interface{}, where []clause.Expression) error {
	if tx == nil {
		tx = repo.db.WithContext(ctx)
	}
	tx = tx.Model(&models.FpDscUser{})
	for _, w := range where {
		tx = tx.Where(w)
	}
	return tx.Save(up).Error
}

func (repo *dscInteractions) GetChannelMessageByMsgId(ctx context.Context, msgId string) (*models.FpDscDmCommu, error) {
	var detail *models.FpDscDmCommu
	err := repo.db.WithContext(ctx).Model(detail).Preload("Reactions").
		Where("msg_id = ?", msgId).Last(&detail).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, err
	}
	return detail, nil
}
func (repo *dscInteractions) ChannelMessageDel(ctx context.Context, msgIds []string) (err error) {
	tx := repo.db.WithContext(ctx).Begin()
	defer func() {
		if err != nil {
			if _rollErr := tx.Rollback(); _rollErr != nil {
				logger.Errorf(ctx, "ChannelMessageDel rollback err. msgIds:%+v. err:%v", msgIds, _rollErr)
			}
		}
	}()
	err = tx.Exec(fmt.Sprintf("INSERT INTO %s SELECT * FROM %s WHERE msg_id IN (?)", models.FpDscDmCommuDel{}.TableName(), models.FpDscDmCommu{}.TableName()), msgIds).Error
	if err != nil {
		return err
	}
	err = tx.Model(&models.FpDscDmCommu{}).Where("msg_id IN (?)", msgIds).Delete(&models.FpDscDmCommu{}).Error
	if err != nil {
		return err
	}

	err = tx.Model(&models.FpDscDmCommuReaction{}).Where("msg_id IN (?)", msgIds).Delete(&models.FpDscDmCommuReaction{}).Error
	if err != nil {
		return err
	}
	err = tx.Commit().Error
	return err
}

func (repo *dscInteractions) ChannelMessageSave(ctx context.Context, tx *gorm.DB, detail *models.FpDscDmCommu, reaction *models.FpDscDmCommuReaction, dscChannel *models.FpDscDmChannel) error {
	if tx == nil {
		tx = repo.db.WithContext(ctx)
	}
	// dm channel commu
	if detail != nil {
		if detail.ID == 0 {
			if err := tx.Create(detail).Error; err != nil {
				return err
			}
		} else {
			if len(detail.Reactions) > 0 {
				if err := tx.Where("msg_id = ?", detail.MsgID).Delete(&models.FpDscDmCommuReaction{}).Error; err != nil {
					return err
				}
			}
			if err := tx.Save(detail).Error; err != nil {
				return err
			}
		}
	}
	if reaction != nil {
		if reaction.ID == 0 {
			if err := tx.Create(reaction).Error; err != nil {
				return err
			}
		} else {
			if err := tx.Save(reaction).Error; err != nil {
				return err
			}
		}
	}
	if dscChannel != nil {
		if err := tx.Clauses(clause.OnConflict{
			Columns:   []clause.Column{{Name: "channel_id"}},
			DoNothing: true,
		}).Create(dscChannel).Error; err != nil {
			return err
		}
	}
	return nil
}

func (repo *dscInteractions) DmChannelCommuReactionRemove(ctx context.Context, msgId, userId, channelId, emojiNameMd5 string) error {
	return repo.db.WithContext(ctx).Model(&models.FpDscDmCommuReaction{}).
		Where("msg_id = ? AND from_user_id = ? AND  channel_id = ? AND emoji_name_md5 = ?", msgId, userId, channelId, emojiNameMd5).
		Delete(&models.FpDscDmCommuReaction{}).Error
}

func (repo *dscInteractions) DmCommuUnreadSave(ctx context.Context, channelId string, userId string, uniqType pb.DscEvTpDf, log string) error {
	_zipLog, err := utils.Compress([]byte(log))
	if err != nil {
		return err
	}
	return repo.db.WithContext(ctx).Create(&models.FpDscDmUnread{
		ChannelID: channelId,
		UserID:    userId,
		UniqType:  uniqType.String(),
		Log:       _zipLog,
	}).Error
}

func (repo *dscInteractions) CheckIsIgnoreMsgId(ctx context.Context, msgId string) (bool, error) {
	var detail = models.FpDscDmSend{}
	err := repo.db.WithContext(ctx).Model(&detail).Clauses(dbresolver.Write).
		Select("ignore_state").Where("msg_id = ?", msgId).First(&detail).Error
	return detail.IgnoreState == 1, err
}

func (repo *dscInteractions) DmCommuAccountSend(ctx context.Context, msgId, botId, channelId, op, opGroup, opAction string, ignoreState int32) error {
	tx := repo.db.WithContext(ctx).Begin()
	record := models.FpDscDmSend{
		MsgID:       msgId,
		BotID:       botId,
		ChannelID:   channelId,
		Operator:    op,
		IgnoreState: ignoreState,
		CreatedAt:   time.Now(),
	}
	err := tx.Create(&record).Error
	if err != nil {
		tx.Rollback()
		logger.Errorf(ctx, "DmCommuAccountSend err. msgId:%s. botId:%s. channel:%s. op:%s. err:%v", msgId, botId, channelId, op, err)
		return xerrors.New(err.Error(), code.DbError)
	}
	// 5 记录操作日志
	opDetail := &models.FpDscOperateLog{
		OperationGroup:  opGroup,
		OperationAction: opAction,
		BaseID:          msgId,
		UniqueID:        uuid.New().String(),
		GameProject:     "",
		BeforeDetail:    "{}",
		AfterDetail:     utils.ToJson(record),
		CreateTime:      time.Now(),
		Operator:        op,
	}
	err = tx.Model(&models.FpDscOperateLog{}).Create(opDetail).Error
	if err != nil {
		tx.Rollback()
		logger.Error(ctx, "[DmCommuAccountSend] error add operate log", zap.String("err", err.Error()))
		return xerrors.New(err.Error(), code.DbError)
	}
	// 提交事务
	if err = tx.Commit().Error; err != nil {
		logger.Error(ctx, "[DmCommuAccountSend] transaction commit err", zap.String("err", err.Error()))
		return xerrors.New(err.Error(), code.DbError)
	}
	return nil
}

// AddSendSurveyLinkLog 调查问卷 - 发送消息 增加 log
func (repo *dscInteractions) AddSendSurveyLinkLog(surveyId int64, dscUserId string, project string, batchId uint64, detail *models.FpDscSurveyLinks) error {
	oplog := &models.FpDscOperateLog{
		OperationGroup:  pb.OpGroup_OpGroupSurveySend.String(),
		OperationAction: pb.OpAction_OpActionAdd.String(),
		BaseID:          fmt.Sprintf("%d.%d.%s", surveyId, batchId, dscUserId),
		UniqueID:        fmt.Sprintf("%d.%d.%s", surveyId, batchId, dscUserId),
		GameProject:     project,
		BeforeDetail:    utils.ToJson(detail),
		AfterDetail:     "{}",
		CreateTime:      time.Now(),
		Operator:        "system",
	}
	return repo.db.Create(oplog).Error
}

func (repo *dscInteractions) OpLogExistCheck(opGroup pb.OpGroup, opAction pb.OpAction, uniqueId string) (bool, error) {
	var count int64
	err := repo.db.Model(&models.FpDscOperateLog{}).Where("operation_group = ? AND operation_action = ? AND unique_id = ?", opGroup.String(), opAction.String(), uniqueId).Count(&count).Error
	return count > 0, err
}
