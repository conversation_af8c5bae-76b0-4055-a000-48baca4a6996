package persistence

import (
	"context"
	"errors"
	"github.com/labstack/echo/v4"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"ops-ticket-api/internal/code"
	"ops-ticket-api/internal/framework/database"
	"ops-ticket-api/internal/framework/xerrors"
	"ops-ticket-api/models"
	"ops-ticket-api/proto/pb"
	"ops-ticket-api/utils"
	"sort"
)

// tagLib 标签组
type tagLib struct {
	db *gorm.DB
}

// NewTagLib init
func NewTagLib() *tagLib {
	return &tagLib{
		db: database.Db(),
	}
}

// GetTagLibList 获取标签组列表
func (dto *tagLib) GetTagLibList(ctx context.Context, req *pb.Project) ([]*pb.TagLibListResp_TagLib, error) {
	var result []*pb.TagLibListResp_TagLib
	var list []*models.FpOpsTagsLib
	var projectFilters []interface{}
	if len(req.ProjectName) > 0 {
		projectFilters = []interface{}{"project in (?)", []string{req.ProjectName}}
	}
	query := dto.db.WithContext(ctx).Model(&models.FpOpsTagsLib{}).Preload("Projects", projectFilters...)
	if req.LibName != "" {
		query.Where("lib_name like ?", "%"+req.LibName+"%")
	}
	if err := query.Find(&list).Error; err != nil {
		logger.Error(ctx, "get tag lib list err", zap.String("err", err.Error()))
		return nil, xerrors.New(err.Error(), code.DbError)
	}

	for _, row := range list {
		lib := &pb.TagLibListResp_TagLib{
			LibId:         row.LibID,
			LibName:       row.LibName,
			TagUploadFile: row.TagUploadFile,
			UpdatedAt:     utils.TimeFormat(int64(row.UpdatedAt)),
			Op:            row.Operator,
			Enable:        row.Enable > 0,
			Projects:      make([]string, 0),
		}
		for _, gm := range row.Projects {
			lib.Projects = append(lib.Projects, gm.Project)
		}
		result = append(result, lib)
	}
	sort.Slice(result, func(i, j int) bool {
		return result[i].LibId > result[j].LibId
	})
	return result, nil
}

// GetTagLibByPerms 根据项目/账号获取标签组
func (dto *tagLib) GetTagLibByPerms(ctx context.Context, project, account string, libId uint32) ([]*models.FpOpsTagsLib, error) {
	tagLibs := make([]*models.FpOpsTagsLib, 0)
	query := dto.db.WithContext(ctx).Table("fp_ops_tags_lib lib").
		Select("lib.lib_id,lib.lib_name").
		Joins("INNER JOIN fp_ops_tags_lib_gm gm ON gm.lib_id = lib.lib_id")
	where := map[string]interface{}{
		"gm.enable":  code.StatusTrue,
		"lib.enable": code.StatusTrue,
	}
	if project != "" {
		where["gm.project"] = project
	}
	if libId != 0 {
		where["lib.lib_id"] = libId
	}

	if err := query.Where(where).Find(&tagLibs).Error; err != nil {
		logger.Error(ctx, "get tag lib by project err", zap.String("project", project), zap.String("err", err.Error()))
		return nil, xerrors.New(err.Error(), code.DbError)
	}
	return tagLibs, nil
}

// GetTagLibInfo 获取标签组信息
func (dto *tagLib) GetTagLibInfo(ctx context.Context, tagLibId uint32) (*models.FpOpsTagsLib, error) {
	tagLibInfo := &models.FpOpsTagsLib{}
	if err := dto.db.WithContext(ctx).Model(&models.FpOpsTagsLib{}).Preload("Projects").
		Take(&tagLibInfo, "lib_id=?", tagLibId).Error; err != nil {
		logger.Error(ctx, "get tag lib info err", zap.Uint32("tagLibId", tagLibId), zap.String("err", err.Error()))
		return nil, xerrors.New(err.Error(), code.DbError)
	}
	return tagLibInfo, nil
}

// TagLibAdd 标签组添加
func (dto *tagLib) TagLibAdd(ctx echo.Context, libName, tagUploadFile, account string, project []string) (uint32, error) {

	// 查询是否已存在相同名称的标签组
	var has int64
	if err := dto.db.WithContext(ctx.Request().Context()).Model(&models.FpOpsTagsLib{}).
		Where("lib_name = ? AND `enable` = ?", libName, code.StatusTrue).Count(&has).Error; err != nil {
		return 0, err
	}
	if has > 0 {
		return 0, xerrors.New(code.RepeatData)
	}
	now := utils.NowTimestamp()
	tagLibInfo := &models.FpOpsTagsLib{
		LibName:       libName,
		Operator:      account,
		TagUploadFile: tagUploadFile,
		Enable:        code.StatusTrue,
		CreatedAt:     now,
		UpdatedAt:     now,
		Projects:      make([]*models.FpOpsTagsLibGm, 0),
	}
	for _, p := range project {
		tagLibInfo.Projects = append(tagLibInfo.Projects, &models.FpOpsTagsLibGm{
			Project:   p,
			CreatedAt: now,
			UpdatedAt: now,
		})
	}

	if err := dto.db.Create(&tagLibInfo).Error; err != nil {
		logger.Error(ctx.Request().Context(), "tag lib created err", zap.Any("tagLibInfo", tagLibInfo), zap.String("err", err.Error()))
		return 0, err
	}
	return tagLibInfo.LibID, nil
}

// TagLibSave 标签组更新
func (dto *tagLib) TagLibSave(ctx echo.Context, tagLibId uint32, libName, account string, project []string) error {
	now := utils.NowTimestamp()
	tagLibInfo := map[string]interface{}{
		"lib_name":   libName,
		"operator":   account,
		"updated_at": now,
	}
	projects := make([]*models.FpOpsTagsLibGm, 0)
	for _, p := range project {
		projects = append(projects, &models.FpOpsTagsLibGm{
			Project:   p,
			LibID:     tagLibId,
			CreatedAt: now,
			UpdatedAt: now,
		})
	}
	tx := dto.db.WithContext(ctx.Request().Context()).Begin()
	if err := tx.Where("lib_id = ?", tagLibId).Delete(&models.FpOpsTagsLibGm{}).Error; err != nil {
		tx.Rollback()
		logger.Error(ctx.Request().Context(), "tag lib project del err", zap.Uint32("tagLibId", tagLibId), zap.String("err", err.Error()))
		return err
	}
	if err := tx.Model(models.FpOpsTagsLib{}).Where("lib_id = ?", tagLibId).Updates(&tagLibInfo).Error; err != nil {
		tx.Rollback()
		logger.Error(ctx.Request().Context(), "tag lib save err", zap.Uint32("tagLibId", tagLibId), zap.String("err", err.Error()))
		return err
	}
	if err := tx.Create(&projects).Error; err != nil {
		logger.Error(ctx.Request().Context(), "tag lib project created err", zap.Any("projects", projects), zap.String("err", err.Error()))
		return err
	}
	if err := tx.Commit().Error; err != nil {
		return err
	}
	return nil
}

// TagList 标签列表
func (dto *tags) TagList(ctx context.Context, libId uint32, enable bool) ([]*models.FpOpsTags, error) {
	dest := make([]*models.FpOpsTags, 0)
	fields := "tag_id,lib_id,parent_id,level,tag_name,enable"
	where := map[string]interface{}{
		"lib_id": libId,
	}
	if enable {
		where["enable"] = code.StatusTrue
	}
	query := dto.db.WithContext(ctx).Select(fields).Model(&models.FpOpsTags{}).Where(where)
	if err := query.Order("lft asc").Find(&dest).Error; err != nil {
		if !errors.Is(err, gorm.ErrRecordNotFound) {
			logger.Error(ctx, "Get tag list err", zap.String("err", err.Error()))
		}
		return nil, err
	}
	return dest, nil
}

// GetDb 获取 db
func (dto *tagLib) GetDb(ctx context.Context) *gorm.DB {
	return dto.db.WithContext(ctx)
}

// CheckTagLibNameHasExist 校验 tag_lib 名称 是否已经存在
func (dto *tagLib) CheckTagLibNameHasExist(ctx context.Context, libId uint32, libName string) (bool, error) {
	var count int64
	if err := dto.db.WithContext(ctx).Model(&models.FpOpsTagsLib{}).
		Where("lib_name = ? AND lib_id != ?", libName, libId).Count(&count).Error; err != nil {
		return false, err
	}
	return count > 0, nil
}
