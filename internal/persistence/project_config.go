package persistence

import (
	"context"
	"fmt"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"gorm.io/plugin/dbresolver"
	"ops-ticket-api/internal/code"
	"ops-ticket-api/internal/framework/database"
	"ops-ticket-api/internal/framework/xerrors"
	"ops-ticket-api/models"
)

// ProjectCfg 顶部数据配置
type ProjectCfg struct {
	db *gorm.DB
}

// NewProjectCfg init
func NewProjectCfg(ctx any) *ProjectCfg {
	return &ProjectCfg{
		db: database.DbSession(ctx, database.Db()),
	}
}

// List 列表
func (dto *ProjectCfg) List(ctx context.Context) (map[string]*models.FpOpsProjectCfg, error) {
	dest := make([]*models.FpOpsProjectCfg, 0)
	if err := dto.db.Clauses(dbresolver.Write).Model(&models.FpOpsProjectCfg{}).Find(&dest).Error; err != nil {
		logger.Error(ctx, "get project list err", zap.Error(err))
		return nil, xerrors.New(err.Error(), code.DbError)
	}
	result := make(map[string]*models.FpOpsProjectCfg, len(dest))
	for _, prj := range dest {
		result[prj.Project] = prj
	}
	return result, nil
}

// SaveAll 更新
func (dto *ProjectCfg) SaveAll(ctx context.Context, allData []*models.FpOpsProjectCfg) error {
	tx := dto.db.Begin()
	defer tx.Rollback()

	stmt := &gorm.Statement{DB: tx}
	stmt.Parse(&models.FpOpsProjectCfg{})
	if err := tx.Exec(fmt.Sprintf("TRUNCATE TABLE %s", stmt.Schema.Table)).Error; err != nil {
		return xerrors.New(err.Error(), code.DbError)
	}
	if err := tx.Create(allData).Error; err != nil {
		return xerrors.New(err.Error(), code.DbError)
	}
	return tx.Commit().Error
}
