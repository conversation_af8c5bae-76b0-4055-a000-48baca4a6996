package persistence

import (
	"context"
	"ops-ticket-api/proto/pb"
	"time"

	"ops-ticket-api/models"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

// ConversationRepo 对话仓储
type ConversationRepo struct {
	db *gorm.DB
}

// NewConversationRepo 创建对话仓储
func NewConversationRepo(db *gorm.DB) *ConversationRepo {
	return &ConversationRepo{db: db}
}

// Transaction 事务
func (r *ConversationRepo) Transaction(ctx context.Context, fc func(tx *gorm.DB) error) error {
	return r.db.WithContext(ctx).Transaction(fc)
}

// UpsertConversation 创建或更新对话
func (r *ConversationRepo) UpsertConversation(ctx context.Context, tx *gorm.DB, conversation *models.Conversation) error {
	return tx.WithContext(ctx).Model(&models.Conversation{}).
		Clauses(clause.OnConflict{
			Columns:   []clause.Column{{Name: "conversation_id"}},
			DoUpdates: clause.AssignmentColumns([]string{"question_progress", "updated_at"}),
		}).
		Create(conversation).Error
}

// GetConversation 获取对话
func (r *ConversationRepo) GetConversation(ctx context.Context, conversationID string) (*models.Conversation, error) {
	var conversation models.Conversation
	err := r.db.WithContext(ctx).Where("conversation_id = ?", conversationID).First(&conversation).Error
	if err != nil {
		return nil, err
	}
	return &conversation, nil
}

// UpdateConversation 更新对话
func (r *ConversationRepo) UpdateConversation(ctx context.Context, conversation *models.Conversation) error {
	return r.db.WithContext(ctx).Save(conversation).Error
}

// BatchSaveHistory 批量保存历史记录
func (r *ConversationRepo) BatchSaveHistory(ctx context.Context, tx *gorm.DB, histories *models.ConversationHistory) error {
	return tx.WithContext(ctx).Create(histories).Error
}

// SaveHistory 保存历史记录
func (r *ConversationRepo) SaveHistory(ctx context.Context, tx *gorm.DB, history *models.ConversationHistory) error {
	return tx.WithContext(ctx).Create(history).Error
}

// GetHistories 获取历史记录列表
func (r *ConversationRepo) GetHistories(ctx context.Context, conversationID string) ([]*models.ConversationHistory, error) {
	var histories []*models.ConversationHistory
	err := r.db.WithContext(ctx).Where("conversation_id = ?", conversationID).Order("id asc").Find(&histories).Error
	if err != nil {
		return nil, err
	}
	return histories, nil
}

func (r *ConversationRepo) UpdateConversationStatus(ctx context.Context, conversationID string, status int32, ticketID uint64) error {
	return r.db.WithContext(ctx).Model(&models.Conversation{}).Where("conversation_id = ?", conversationID).
		Updates(map[string]interface{}{"status": status, "ticket_id": ticketID}).Error
}

// GetUnFinishConversion 获取未完成的对话
func (r *ConversationRepo) GetUnFinishConversion(ctx context.Context, gameProject, account string) (*models.Conversation, error) {
	var conversation models.Conversation
	err := r.db.WithContext(ctx).Where("game_project = ? AND account_id = ? AND  status < ? and created_at > ?",
		gameProject,
		account,
		int32(pb.ConversationStatus_ConversationStatusClose),
		time.Now().Add(-time.Minute*30),
	).Order("id desc").First(&conversation).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		return nil, err
	}
	return &conversation, nil
}

// CloseConversation 关闭对话
func (r *ConversationRepo) CloseConversation(ctx context.Context, conversationID string) error {
	return r.db.WithContext(ctx).Model(&models.Conversation{}).Where("conversation_id = ?", conversationID).Update("status", int32(pb.ConversationStatus_ConversationStatusClose)).Error
}

// 关闭conversationID之前的会话
func (r *ConversationRepo) CloseEarlyConversation(ctx context.Context, conversationID string, account string) error {
	return r.db.WithContext(ctx).Model(&models.Conversation{}).Where("conversation_id != ? and account_id = ?", conversationID, account).Update("status", int32(pb.ConversationStatus_ConversationStatusClose)).Error
}
