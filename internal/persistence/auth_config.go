package persistence

import (
	"context"
	"gorm.io/gorm"
	"ops-ticket-api/internal/code"
	"ops-ticket-api/internal/framework/cache/local"
	"ops-ticket-api/internal/framework/database"
	"ops-ticket-api/internal/framework/xerrors"
	"ops-ticket-api/models"
	"ops-ticket-api/proto/pb"
	"ops-ticket-api/utils"
)

// AuthConfig 游戏auth配置
type AuthConfig struct {
	db *gorm.DB
}

// NewAuthConfig init
func NewAuthConfig() *AuthConfig {
	return &AuthConfig{
		db: database.Db(),
	}
}

func (ac *AuthConfig) AuthConfigList(ctx context.Context) ([]*models.FpAuthConfig, error) {
	dest := []*models.FpAuthConfig{}
	if err := ac.db.Model(models.FpAuthConfig{}).Select("*").Find(&dest).Error; err != nil {
		return dest, err
	}
	return dest, nil
}

func (ac *AuthConfig) AuthConfigSave(ctx context.Context, req *pb.AuthConfigSyncReq) error {
	var has int64
	err := ac.db.Model(&models.FpAuthConfig{}).Where("auth_key = ? AND secret = ?", req.AuthKey, req.Secret).Count(&has).Error
	if err != nil {
		return xerrors.New(err.Error(), code.DbError)
	}
	// 已有则不需要新增
	if has > 0 {
		return nil
	}
	// 新增
	current := utils.NowTimestamp()
	operateTime := utils.TimeFormat(int64(current))
	record := &models.FpAuthConfig{
		GameProject: req.GameProject,
		AuthKey:     req.AuthKey,
		Secret:      req.Secret,
		CreateTime:  current,
		UpdatedTime: operateTime,
	}
	if err = ac.db.Model(models.FpAuthConfig{}).Create(record).Error; err != nil {
		return err
	}
	// 将新增的auth配置数据加载到本地内存中
	local.AuthConfigCache.ReSetAuthConfig(record.AuthKey, record)
	return nil
}
