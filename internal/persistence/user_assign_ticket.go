// Copyright 2024 funplus Authors. All Rights Reserved.
// @Description: 用户分单配置
// @Author: qiujun
// @Date: 2024/10/17 11:45 AM

package persistence

import (
	"context"
	"encoding/json"
	"github.com/labstack/echo/v4"
	"github.com/spf13/cast"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"go.uber.org/zap" // 引入日志库
	"gorm.io/gorm"
	"gorm.io/plugin/dbresolver"
	"ops-ticket-api/internal/code"
	"ops-ticket-api/internal/cst"
	"ops-ticket-api/internal/framework/database"
	"ops-ticket-api/internal/framework/xerrors"
	"ops-ticket-api/models"
	"ops-ticket-api/proto/pb"
	"ops-ticket-api/utils"
	"strings"
	"time"
)

// UserAssignTicket 用户分单配置
type UserAssignTicket struct {
	db *gorm.DB
}

// NewUserAssignTicketRepo init
func NewUserAssignTicketRepo() *UserAssignTicket {
	return &UserAssignTicket{
		db: database.Db(),
	}
}

func (dto *UserAssignTicket) ProjectUser(ctx context.Context, project string, withOnline bool) ([]*models.ProjectUserDf, error) {
	var users []*models.FpOpsUserData
	query := dto.db.WithContext(ctx).Clauses(dbresolver.Write).Table("fp_ops_users u").
		Joins("LEFT JOIN fp_ops_user_assign_ticket a ON u.account = a.account").Select("a.*, u.is_login, u.last_alloc_tk_at")
	if withOnline {
		query = query.Where("u.is_login = ?", uint32(pb.UserLoginStatus_UserLoginStatusYes))
	}
	err := query.Find(&users).Error
	if err != nil {
		return nil, err
	}
	var gameUsers = make([]*models.ProjectUserDf, 0)
	var userIdx = make(map[string]bool, 0)
	for _, user := range users {
		if userIdx[user.Account] {
			continue
		}
		var gameCats []*models.GameCategory
		if err := json.Unmarshal([]byte(user.GameCat), &gameCats); err != nil {
			logger.Infof(ctx, "unmarshal game err: %v", err)
		}
		for _, gameCat := range gameCats {
			if project == gameCat.Game {
				gameUsers = append(gameUsers, &models.ProjectUserDf{
					Id:            user.ID,
					IsLogin:       pb.UserLoginStatus(user.IsLogin),
					UpperLimit:    int32(user.UpperLimit),
					LastAllocTkAt: user.LastAllocTkAt,
					User:          user.Account,
					GameCat:       gameCats,
					Lang:          utils.StrToSlice(user.Lang),
				})
				userIdx[user.Account] = true
			}
		}
	}

	return gameUsers, nil
}

// UserAssignTicketList 用户分单配置列表
func (dto *UserAssignTicket) UserAssignTicketList(ctx context.Context, req *pb.UserAssignTicketListReq) (*pb.UserAssignTicketListResp, error) {
	dest := make([]*models.FpOpsUserAssignTicket, 0)
	query := dto.db.Table(models.GetFpOpsUserAssignTicketTableName()).Select("*")
	if req.Account != "" {
		query = query.Where("account LIKE?", "%"+req.Account+"%")
	}
	var count int64
	if err := query.Count(&count).Error; err != nil {
		logger.Error(ctx, "get user assign ticket count err", zap.String("err", err.Error()))
		return nil, err
	}
	if err := query.Scopes(database.Paginate(&req.Page, &req.PageSize)).Find(&dest).Error; err != nil {
		logger.Error(ctx, "get UserAssignTicket list err", zap.String("err", err.Error()))
		return nil, xerrors.New(err.Error(), code.DbError)
	}
	resp := new(pb.UserAssignTicketListResp)
	data := make([]*pb.UserAssignTicketListResp_Detail, len(dest))
	// utc时间
	for i, v := range dest {
		data[i] = &pb.UserAssignTicketListResp_Detail{
			Id:         v.ID,
			Account:    v.Account,
			Game:       utils.StrToSlice(v.Game),
			Operator:   v.Operator,
			Lang:       utils.StrToSlice(v.Lang),
			UpperLimit: v.UpperLimit,
			UpdatedAt:  v.UpdatedAt.Format(time.RFC3339),
		}
		gameCat := []*pb.GameCategory{}
		if e := json.Unmarshal([]byte(v.GameCat), &gameCat); e != nil {
			logger.Infof(ctx, "unmarshal game cat err", zap.String("err", e.Error()))
		}
		data[i].Detail = gameCat
	}
	resp.Total = uint32(count)
	resp.CurrentPage = req.Page
	resp.PerPage = req.PageSize
	resp.Data = data
	return resp, nil
}

// UserAssignTicketInfo 特定用户分单配置信息
func (dto *UserAssignTicket) UserAssignTicketInfo(ctx context.Context, req *pb.UserAssignTicketInfoReq) (*models.FpOpsUserAssignTicket, error) {
	dest := &models.FpOpsUserAssignTicket{}
	if err := dto.db.WithContext(ctx).Model(&models.FpOpsUserAssignTicket{}).Where("id=?", req.Id).Take(&dest).Error; err != nil {
		logger.Error(ctx, "get user assign ticket info err", zap.Uint32("Id", req.Id), zap.String("err", err.Error()))
		return nil, xerrors.New(err.Error(), code.DbError)
	}
	return dest, nil
}

// UserAssignTicketAdd 用户分单配置添加
func (dto *UserAssignTicket) UserAssignTicketAdd(ctx echo.Context, req *pb.UserAssignTicketAddReq) error {
	operator := cast.ToString(ctx.Get(cst.AccountInfoCtx))
	if req.UpperLimit > 50 {
		return xerrors.New("UpperLimit cannot be greater than 50", code.InvalidParams)
	}
	//游戏必选，问题分类可空
	if len(req.Detail) < 1 {
		return xerrors.New("At least one game is required.", code.InvalidParams)
	}
	var users []*models.FpOpsUserAssignTicket
	var repeat = make(map[string]bool)
	var userList []string
	for _, user := range req.User {
		user = strings.TrimSpace(user)
		if user == "" {
			continue
		}
		if _, ok := repeat[user]; ok {
			continue
		}
		repeat[user] = true
		userList = append(userList, user)
		detailBytes, err := json.Marshal(req.Detail)
		if err != nil {
			return err
		}
		users = append(users, &models.FpOpsUserAssignTicket{
			Account:    user,
			UpperLimit: req.UpperLimit,
			Lang:       utils.ToJson(req.Lang),
			Game:       "",
			GameCat:    string(detailBytes),
			Operator:   operator,
			CreatedAt:  time.Now(),
			UpdatedAt:  time.Now(),
		})
	}

	var bindUsers []string
	if err := dto.db.Model(&models.FpOpsUserAssignTicket{}).Where("account IN (?)", userList).
		Pluck("account", &bindUsers).Error; err != nil {
		return err
	}
	if len(bindUsers) > 0 {
		return xerrors.New("当前部分人员已绑定: "+strings.Join(bindUsers, ", "), code.IdempotenceErr)
	}
	if err := dto.db.Model(&models.FpOpsUserAssignTicket{}).CreateInBatches(users, len(users)).Error; err != nil {
		logger.Error(ctx.Request().Context(), "create user assign ticket in batches err", zap.String("err", err.Error()))
		return err
	}
	return nil
}

// UserAssignTicketEdit 用户分单配置修改
func (dto *UserAssignTicket) UserAssignTicketEdit(ctx echo.Context, req *pb.UserAssignTicketEditReq) error {
	userInfo := models.FpOpsUserAssignTicket{}
	err := dto.db.Model(&models.FpOpsUserAssignTicket{}).Where("id = ?", req.Id).First(&userInfo).Error
	if err != nil {
		logger.Error(ctx.Request().Context(), "UserAssignTicket user edit: failed to fetch UserAssignTicket info", zap.Uint32("ID", uint32(req.Id)), zap.Error(err))
		return xerrors.New(err.Error(), code.DbError)
	}
	if req.UpperLimit > 50 {
		return xerrors.New("UpperLimit cannot be greater than 50", code.InvalidParams)
	}
	if len(req.Detail) < 1 {
		return xerrors.New("At least one game is required.", code.InvalidParams)
	}
	dest := map[string]interface{}{
		"upper_limit": req.UpperLimit,
		"game":        "",
		"lang":        utils.ToJson(req.Lang),
		"operator":    cast.ToString(ctx.Get(cst.AccountInfoCtx)),
		"updated_at":  time.Now(),
	}
	detailBytes, err := json.Marshal(req.Detail)
	if err != nil {
		logger.Error(ctx.Request().Context(), "UserAssignTicket user edit: failed to Marshal UserAssignTicket info", zap.Uint32("ID", uint32(req.Id)), zap.Error(err))
		return err
	}
	dest["game_cat"] = string(detailBytes)
	if err = dto.db.Model(&models.FpOpsUserAssignTicket{}).Where("id = ?", req.Id).Updates(dest).Error; err != nil {
		logger.Error(ctx.Request().Context(), "UserAssignTicketEdit: failed to update UserAssignTicket info", zap.Uint32("ID", req.Id), zap.Error(err))
		return xerrors.New(err.Error(), code.DbError)
	}
	return nil
}

// UserAssignTicketDel 删除分单用户配置
func (dto *UserAssignTicket) UserAssignTicketDel(ctx context.Context, req *pb.UserAssignTicketDelReq) error {
	// 查询用户是否有正在处理中的工单
	var count int64
	if err := dto.db.WithContext(ctx).Model(&models.FpOpsTickets{}).Where("acceptor = ? AND status = ?", req.User, uint32(pb.TkStatus_TkStatusProcessing)).Count(&count).Error; err != nil {
		logger.Error(ctx, "UserAssignTicket delete: failed to count processing tickets", zap.String("acceptor", req.User), zap.Error(err))
		return xerrors.New(err.Error(), code.DbError)
	}
	if count > 0 {
		return xerrors.New("该用户有处理中工单，暂时无法删除", code.Error)
	}
	if err := dto.db.WithContext(ctx).Model(&models.FpOpsUserAssignTicket{}).Where("id = ?", req.Id).Delete(&models.FpOpsUserAssignTicket{}).Error; err != nil {
		logger.Error(ctx, "error delete user assign ticket", zap.String("err", err.Error()))
		return xerrors.New(err.Error(), code.DbError)
	}
	return nil
}

func (dto *UserAssignTicket) FetchUserState(ctx context.Context, account, gameProject, lang string) ([]*pb.UserStateDf, pb.UserLoginStatus, int64, error) {
	fun := "UserAssignTicket.FetchUserState"
	var upperLimit int64
	var loginState = pb.UserLoginStatus_UserLoginStatusNo
	var result []*pb.UserStateDf
	err := dto.db.WithContext(ctx).Table("fp_ops_users u").
		Joins("LEFT JOIN fp_ops_user_assign_ticket a ON u.account = a.account").
		Select("a.upper_limit AS upper_limit, a.game AS game, a.lang AS lang, u.is_login AS is_login").
		Where("u.account = ?", account).Find(&result).Error
	if err != nil {
		logger.Errorf(ctx, "%s get detail return err. account:%s. game:%s. lang:%s. err:%v", fun, account, gameProject, lang, err)
		return result, pb.UserLoginStatus_UserLoginStatusNo, upperLimit, err
	}
	for _, row := range result {
		// is login
		if pb.UserLoginStatus_name[row.IsLogin] == pb.UserLoginStatus_UserLoginStatusYes.String() {
			loginState = pb.UserLoginStatus_UserLoginStatusYes
		}
		// upper limit
		if gameProject != "" {
			if !utils.GroupGameMatch(gameProject, row.Game) {
				continue
			}
			if !utils.GroupLangMatch(lang, row.Lang) {
				continue
			}
			if row.UpperLimit > upperLimit {
				upperLimit = row.UpperLimit
			}
		}
	}
	return result, loginState, upperLimit, nil
}

func (dto *UserAssignTicket) UserAssignedTicket(ctx context.Context, tx *gorm.DB, user string) error {
	//	// todo 实现 给客服人员分单后，记录最近一次获单时间
	if tx == nil {
		tx = dto.db.WithContext(ctx)
	}
	return tx.Model(&models.FpOpsUsers{}).Where("account = ?", user).Updates(map[string]interface{}{
		"last_alloc_tk_at": utils.NowTimestamp(),
	}).Error
}
