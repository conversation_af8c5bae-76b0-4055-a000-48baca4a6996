package persistence

import (
	"context"
	"errors"
	"fmt"
	"github.com/google/uuid"
	"github.com/labstack/echo/v4"
	"github.com/spf13/cast"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"gorm.io/plugin/dbresolver"
	"ops-ticket-api/internal/code"
	"ops-ticket-api/internal/cst"
	"ops-ticket-api/internal/elasticsearch"
	"ops-ticket-api/internal/framework/database"
	"ops-ticket-api/internal/framework/xerrors"
	"ops-ticket-api/models"
	"ops-ticket-api/proto/pb"
	"ops-ticket-api/utils"
	"time"
)

// LineMaintainConfig 员工关系维护配置
type LineMaintainConfig struct {
	db *gorm.DB
}

// NewLineMaintainConfig init
func NewLineMaintainConfig() *LineMaintainConfig {
	return &LineMaintainConfig{
		db: database.Db(),
	}
}

func (m *LineMaintainConfig) MaintainConfigEdit(ctx echo.Context, req *pb.LineMaintainConfigEditReq, player models.DiscordPlayer) error {
	operator := cast.ToString(ctx.Get(cst.AccountInfoCtx))
	current := utils.NowTimestamp()
	operateTime := utils.TimeFormat(int64(current))
	tx := m.db.WithContext(ctx.Request().Context()).Begin()
	var mc models.FpLinePlayerMaintainConfig
	err := tx.Table(models.GetFpLinePlayerMaintainConfigTableName()).Where("id = ?", req.Id).First(&mc).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return xerrors.New(err.Error(), code.DbError)
	} else if errors.Is(err, gorm.ErrRecordNotFound) {
		return gorm.ErrRecordNotFound
	} else {
		// 校验fpid是否重复
		if req.Fpid != "" && req.Fpid != mc.AccountId {
			var has int64
			err = tx.Table(models.GetFpLinePlayerMaintainConfigTableName()).Where("account_id = ? AND project = ?", req.Fpid, mc.Project).Count(&has).Error
			if err != nil {
				logger.Error(ctx.Request().Context(), "error querying existing fpid", zap.String("err", err.Error()))
				return xerrors.New(err.Error(), code.DbError)
			}
			if has > 0 {
				return xerrors.New("fpid重复", code.InvalidParams)
			}
		}
		// 保存更新前的详情
		beforeDetail := utils.ToJson(mc)
		// 更新维护配置
		mc.Operator = operator
		mc.UpdateTime = operateTime
		mc.Maintainer = req.Maintainer
		mc.AccountId = req.Fpid
		mc.UID = req.Uid
		// 服务器,游戏昵称,和语言取玩家在CRM系统的数据
		mc.Sid = player.Sid
		mc.PlayerNick = player.PlayerNick
		mc.Lang = player.Lang
		mc.VipState = uint8(req.VipState)
		mc.PayAll = player.TotalPay
		mc.PayLastThirtyDays = player.LastThirtyDaysPay
		mc.LastLogin = player.LastLogin
		if err = tx.Table(models.GetFpLinePlayerMaintainConfigTableName()).Where("id = ?", req.Id).Updates(&mc).Error; err != nil {
			tx.Rollback()
			logger.Error(ctx.Request().Context(), "error edit maintain config", zap.String("err", err.Error()))
			return err
		}
		// 保存更新后的详情
		afterDetail := utils.ToJson(mc)
		// 更新沟通记录表中的uid和sid数据
		if req.Uid > 0 || player.Sid != "" {
			mark := map[string]interface{}{
				"uid": req.Uid,
				"sid": player.Sid,
			}
			if err = tx.Table(models.GetFpLineCommuRecordTableName()).Where("project = ? AND line_user_id = ? AND nick_name = ?", mc.Project, mc.LineUserId, mc.NickName).Updates(mark).Error; err != nil {
				tx.Rollback()
				logger.Error(ctx.Request().Context(), "error upate commu record", zap.String("err", err.Error()))
				return err
			}
		}
		// 同步维护配置到ES
		if mc.LineUserId == "" {
			tx.Rollback()
			return errors.New("lineUserId is empty")
		}
		// 更新 es 中 line_user_id 关联的数据
		if err = m.MaintainConfigSyncEs(ctx.Request().Context(), tx, &mc); err != nil {
			tx.Rollback()
			return err
		}
		// 记录操作日志
		opDetail := &models.FpLineOperateLog{
			OperationGroup:  pb.OpGroup_OpGroupMaintainConfig.String(),
			OperationAction: pb.OpAction_OpActionUpdate.String(),
			BaseID:          mc.LineUserId,
			UniqueID:        uuid.New().String(),
			Project:         mc.Project,
			BeforeDetail:    beforeDetail,
			AfterDetail:     afterDetail,
			CreateTime:      time.Now(),
			Operator:        cast.ToString(ctx.Get(cst.AccountInfoCtx)),
		}
		err = tx.Model(&models.FpLineOperateLog{}).Create(opDetail).Error
		if err != nil {
			tx.Rollback()
			logger.Error(ctx.Request().Context(), "[LineMaintainConfigEdit] error add operate log", zap.String("err", err.Error()))
			return xerrors.New(err.Error(), code.DbError)
		}
		// 提交事务
		if err = tx.Commit().Error; err != nil {
			logger.Error(ctx.Request().Context(), "[LineMaintainConfigEdit] transaction commit err", zap.String("err", err.Error()))
			return xerrors.New(err.Error(), code.DbError)
		}
		return nil
	}
}

func (m *LineMaintainConfig) GetLineMaintainConfigDetail(ctx context.Context, lineUserId, project string) (*models.FpLinePlayerMaintainConfig, error) {
	var detail = &models.FpLinePlayerMaintainConfig{}
	err := m.db.WithContext(ctx).Model(detail).Clauses(dbresolver.Write).
		Where("line_user_id = ? AND project = ?", lineUserId, project).First(detail).Error
	return detail, err
}

func (m *LineMaintainConfig) MaintainConfigSyncEs(ctx context.Context, tx *gorm.DB, mc *models.FpLinePlayerMaintainConfig) error {
	if tx == nil {
		tx = m.db.WithContext(ctx)
	}
	lastLogin := uint64(0)
	if mc.LastLogin == "0000-00-00 00:00:00" || mc.LastLogin == "" {
		lastLogin = 0
	} else {
		lastLogin = utils.TimeStrToUnix(mc.LastLogin)
	}
	// 同步维护配置到ES
	// 使用脚本更新
	scripts := []string{
		"ctx._source.uid=params.uid",
		"ctx._source.account_id=params.account_id",
		"ctx._source.sid=params.sid",
		"ctx._source.maintainer=params.maintainer",
		"ctx._source.vip_state=params.vip_state",
		"ctx._source.player_nick=params.player_nick",
		"ctx._source.lang=params.lang",
		"ctx._source.pay_all=params.pay_all",
		"ctx._source.pay_last_thirty_days=params.pay_last_thirty_days",
		"ctx._source.last_login=params.last_login",
		"ctx._source.updated_at=params.updated_at",
	}
	dest := map[string]interface{}{
		"uid":                  mc.UID,
		"account_id":           mc.AccountId,
		"sid":                  cast.ToInt(mc.Sid),
		"maintainer":           mc.Maintainer,
		"vip_state":            mc.VipState,
		"player_nick":          mc.PlayerNick,
		"lang":                 mc.Lang,
		"pay_all":              mc.PayAll,
		"pay_last_thirty_days": mc.PayLastThirtyDays,
		"last_login":           lastLogin,
		"updated_at":           utils.NowTimestamp(),
	}
	if err := elasticsearch.DefaultLineEsSvc.UpdateMaintainConfig(ctx, mc.LineUserId, scripts, dest); err != nil {
		logger.Error(ctx, "error updating line maintain config to ES", zap.String("lineUserId", mc.LineUserId), zap.String("err", err.Error()))
		return err
	}
	return nil
}

func (m *LineMaintainConfig) MaintainConfigDel(ctx echo.Context, req *pb.LineMaintainConfigDelReq) error {
	tx := m.db.WithContext(ctx.Request().Context()).Begin()
	detail := models.FpLinePlayerMaintainConfig{}
	if err := tx.Table(models.GetFpLinePlayerMaintainConfigTableName()).Where("id = ?", req.Id).Find(&detail).Error; err != nil {
		logger.Error(ctx.Request().Context(), "error query line maintain config", zap.String("err", err.Error()))
		return xerrors.New(err.Error(), code.DbError)
	}
	// 1 LineMaintainConfig 数据删除
	err := tx.Table(models.GetFpLinePlayerMaintainConfigTableName()).Where("id = ?", req.Id).Delete(&models.FpLinePlayerMaintainConfig{}).Error
	if err != nil {
		tx.Rollback()
		logger.Error(ctx.Request().Context(), "error delete line maintain config", zap.String("err", err.Error()))
		return xerrors.New(err.Error(), code.DbError)
	}
	// 2 fp_dsc_user表相关数据进行软删除
	err = tx.Model(&models.FpLineUser{}).Where("line_user_id = ? AND project = ?", req.LineUserId, req.Project).Updates(map[string]interface{}{
		"is_deleted": code.DscDeleted,
		"updated_at": time.Now(),
	}).Error
	if err != nil {
		tx.Rollback()
		logger.Error(ctx.Request().Context(), "error delete fp_line_user", zap.String("err", err.Error()))
		return xerrors.New(err.Error(), code.DbError)
	}
	// 3 es中将该line_user_id关联的数据进行软删除
	scripts := []string{
		"ctx._source.is_deleted=params.is_deleted",
		"ctx._source.uid=params.uid",
		"ctx._source.account_id=params.account_id",
		"ctx._source.sid=params.sid",
		"ctx._source.maintainer=params.maintainer",
		"ctx._source.vip_state=params.vip_state",
		"ctx._source.pay_all=params.pay_all",
		"ctx._source.pay_last_thirty_days=params.pay_last_thirty_days",
		"ctx._source.last_login=params.last_login",
		"ctx._source.vip_level=params.vip_level",
		"ctx._source.updated_at=params.updated_at",
	}
	dest := map[string]interface{}{
		"is_deleted":           code.DscDeleted,
		"uid":                  0,
		"account_id":           "",
		"sid":                  "",
		"maintainer":           "",
		"vip_state":            code.NonVip,
		"pay_all":              0,
		"pay_last_thirty_days": 0,
		"last_login":           0,
		"vip_level":            0,
		"updated_at":           utils.NowTimestamp(),
	}
	if err = elasticsearch.DefaultLineEsSvc.UpdateMaintainConfig(ctx.Request().Context(), req.LineUserId, scripts, dest); err != nil {
		tx.Rollback()
		return err
	}
	// 4 记录操作日志
	opDetail := &models.FpLineOperateLog{
		OperationGroup:  pb.OpGroup_OpGroupMaintainConfig.String(),
		OperationAction: pb.OpAction_OpActionDelete.String(),
		BaseID:          detail.LineUserId,
		UniqueID:        uuid.New().String(),
		Project:         req.Project,
		BeforeDetail:    utils.ToJson(detail),
		AfterDetail:     "{}",
		CreateTime:      time.Now(),
		Operator:        cast.ToString(ctx.Get(cst.AccountInfoCtx)),
	}
	err = tx.Model(&models.FpLineOperateLog{}).Create(opDetail).Error
	if err != nil {
		tx.Rollback()
		logger.Error(ctx.Request().Context(), "error add line operate log", zap.String("err", err.Error()))
		return xerrors.New(err.Error(), code.DbError)
	}
	// 提交事务
	if err = tx.Commit().Error; err != nil {
		logger.Error(ctx.Request().Context(), "[LineMaintainConfigDel] transaction commit err", zap.String("err", err.Error()))
		return xerrors.New(err.Error(), code.DbError)
	}
	return nil
}

func (m *LineMaintainConfig) MaintainConfigList(ctx echo.Context, req *pb.LineMaintainConfigListReq, all bool) ([]*pb.LineMaintainConfigListResp_LineMaintainConfigInfo, uint32, error) {
	maintainConfigList := []*models.FpLinePlayerMaintainConfigList{}
	fields := "c.*, p.birthday"
	query := m.db.Table(fmt.Sprintf("%s AS c", models.GetFpLinePlayerMaintainConfigTableName())).
		Joins(fmt.Sprintf("LEFT JOIN %s AS p ON c.line_user_id = p.line_user_id AND c.project = p.project", models.GetFpLinePlayerPortraitTableName()), "")
	query = query.Select(fields)
	if len(req.Project) > 0 {
		query = query.Where("c.project IN (?)", req.Project)
	}
	if len(req.Maintainer) > 0 {
		query = query.Where("c.maintainer IN (?)", req.Maintainer)
	}
	if len(req.NickName) > 0 {
		query = query.Where("c.nick_name IN (?)", req.NickName)
	}
	if req.LineUserId != "" {
		query = query.Where("c.line_user_id = ?", req.LineUserId)
	}
	if req.VipState > 0 {
		query = query.Where("c.vip_state = ?", req.VipState)
	}
	var count int64
	if err := query.Count(&count).Error; err != nil {
		logger.Error(ctx.Request().Context(), "get line maintain config list count err", zap.String("err", err.Error()))
		return nil, 0, err
	}
	// 导出数据不分页，查询才分页
	if !all {
		if err := query.Scopes(database.Paginate(&req.Page, &req.PageSize)).Find(&maintainConfigList).Error; err != nil {
			logger.Error(ctx.Request().Context(), "get line maintain config list err", zap.String("err", err.Error()))
			return nil, 0, xerrors.New(err.Error(), code.DbError)
		}
	} else {
		if err := query.Find(&maintainConfigList).Error; err != nil {
			logger.Error(ctx.Request().Context(), "get all line maintain config list err", zap.String("err", err.Error()))
			return nil, 0, xerrors.New(err.Error(), code.DbError)
		}
	}
	dest := make([]*pb.LineMaintainConfigListResp_LineMaintainConfigInfo, len(maintainConfigList))
	for i := range maintainConfigList {
		maintainConfig := maintainConfigList[i]
		dest[i] = &pb.LineMaintainConfigListResp_LineMaintainConfigInfo{
			Id:         maintainConfig.ID,
			LineUserId: maintainConfig.LineUserId,
			NickName:   maintainConfig.NickName,
			Uid:        maintainConfig.UID,
			Fpid:       maintainConfig.AccountId,
			Sid:        maintainConfig.Sid,
			Maintainer: maintainConfig.Maintainer,
			Project:    maintainConfig.Project,
			Operator:   maintainConfig.Operator,
			VipState:   uint32(maintainConfig.VipState),
			UpdateTime: maintainConfig.UpdateTime,
			Lang:       maintainConfig.Lang,
			Birthday:   maintainConfig.Birthday,
		}
	}
	return dest, cast.ToUint32(count), nil
}

func (m *LineMaintainConfig) GetFpidByDscUserId(ctx echo.Context, lineUserId string) (string, error) {
	var fpid string
	err := m.db.Table(models.GetFpLinePlayerMaintainConfigTableName()).Select("account_id").Where("line_user_id = ?", lineUserId).Take(&fpid).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		logger.Error(ctx.Request().Context(), "error get fpid from line maintain config", zap.String("err", err.Error()))
		return fpid, err
	}
	return fpid, nil
}

func (m *LineMaintainConfig) LinePlayerAccounts(ctx echo.Context, req *pb.LinePlayerAccountsReq) ([]string, error) {
	query := m.db.Model(models.FpLineUser{}).Select("DISTINCT line_user_id").Where("project = ? AND is_deleted = ?", req.Project, code.DscNoDelete)
	if req.LineUserId != "" {
		query = query.Where("line_user_id LIKE ?", "%"+req.LineUserId+"%")
	}
	data := []string{}
	if err := query.Find(&data).Error; err != nil {
		return data, err
	}
	return data, nil
}

func (m *LineMaintainConfig) LinePlayerUidList(ctx echo.Context, req *pb.DiscordPlayerUidReq) ([]int64, error) {
	query := m.db.Model(models.FpLinePlayerMaintainConfig{}).Select("uid").Where("project IN (?)", req.Project)
	if req.Uid > 0 {
		query = query.Where("uid = ?", req.Uid)
	}
	data := []int64{}
	if err := query.Find(&data).Error; err != nil {
		return data, err
	}
	return data, nil
}

func (m *LineMaintainConfig) FetchAllLinePlayers() ([]*models.FpLinePlayerMaintainConfig, error) {
	dest := []*models.FpLinePlayerMaintainConfig{}
	if err := m.db.Table(models.GetFpLinePlayerMaintainConfigTableName()).Select("*").Where("account_id != ?", "").Find(&dest).Error; err != nil {
		return dest, err
	}
	return dest, nil
}

func (m *LineMaintainConfig) UpdatesCrmData(ctx echo.Context, mc map[string]interface{}) error {
	if err := m.db.Table(models.GetFpLinePlayerMaintainConfigTableName()).Where("id = ?", mc["id"]).Updates(mc).Error; err != nil {
		return err
	}
	// sync LineMaintainConfig to es
	// 使用脚本更新
	scripts := []string{
		"ctx._source.uid=params.uid",
		"ctx._source.account_id=params.account_id",
		"ctx._source.sid=params.sid",
		"ctx._source.pay_all=params.pay_all",
		"ctx._source.pay_last_thirty_days=params.pay_last_thirty_days",
		"ctx._source.last_login=params.last_login",
		"ctx._source.vip_level=params.vip_level",
		"ctx._source.player_nick=params.player_nick",
		"ctx._source.lang=params.lang",
		"ctx._source.updated_at=params.updated_at",
	}
	lastLogin := uint64(0)
	if mc["last_login"] == "0000-00-00 00:00:00" || mc["last_login"] == "" {
		lastLogin = 0
	} else {
		lastLogin = utils.TimeStrToUnix(mc["last_login"].(string))
	}
	dest := map[string]interface{}{
		"uid":                  mc["uid"],
		"account_id":           mc["account_id"],
		"sid":                  mc["sid"],
		"pay_all":              mc["pay_all"],
		"pay_last_thirty_days": mc["pay_last_thirty_days"],
		"last_login":           lastLogin,
		"vip_level":            mc["vip_level"],
		"player_nick":          mc["player_nick"],
		"lang":                 mc["lang"],
		"updated_at":           utils.NowTimestamp(),
	}
	if err := elasticsearch.DefaultLineEsSvc.UpdateMaintainConfig(ctx.Request().Context(), cast.ToString(mc["line_user_id"]), scripts, dest); err != nil {
		return err
	}
	return nil
}

func (m *LineMaintainConfig) GetMaintainConfigDetail(ctx context.Context, lineUserId, project string) (*models.FpLinePlayerMaintainConfig, error) {
	var detail = &models.FpLinePlayerMaintainConfig{}
	err := m.db.WithContext(ctx).Model(detail).Clauses(dbresolver.Write).
		Where("line_user_id = ? AND project = ?", lineUserId, project).First(detail).Error
	return detail, err
}
