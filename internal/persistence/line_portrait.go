package persistence

import (
	"errors"
	"github.com/google/uuid"
	"github.com/labstack/echo/v4"
	"github.com/spf13/cast"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"ops-ticket-api/internal/code"
	"ops-ticket-api/internal/cst"
	"ops-ticket-api/internal/elasticsearch"
	"ops-ticket-api/internal/framework/database"
	"ops-ticket-api/internal/framework/xerrors"
	"ops-ticket-api/models"
	"ops-ticket-api/proto/pb"
	"ops-ticket-api/utils"
	"strings"
	"time"
)

// LinePortrait 玩家画像信息
type LinePortrait struct {
	db *gorm.DB
}

// NewLinePortrait init
func NewLinePortrait() *LinePortrait {
	return &LinePortrait{
		db: database.Db(),
	}
}

func (p *LinePortrait) LinePortraitInfo(ctx echo.Context, req *pb.LinePortraitInfoReq) (*pb.LinePortraitInfoResp, error) {
	dest := &models.FpLinePlayerPortrait{}
	err := p.db.Table(models.GetFpLinePlayerPortraitTableName()).Where("line_user_id = ? AND project = ?", req.LineUserId, req.Project).First(&dest).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, xerrors.New(err.Error(), code.DbError)
	} else if errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, nil
	}
	lineTags := strings.Split(dest.Tag, ",")
	tagIds := make([]uint32, 0)
	for i := range lineTags {
		if _tid := cast.ToUint32(lineTags[i]); _tid > 0 {
			tagIds = append(tagIds, _tid)
		}
	}
	labels := []*pb.LinePortraitInfoResp_LabelDetail{}
	tagsSlice := NewTags().GetTagsSlice(ctx.Request().Context(), tagIds)
	for id, v := range tagsSlice {
		label := &pb.LinePortraitInfoResp_LabelDetail{
			TagId:   uint64(id),
			TagDesc: strings.Join(v, "-"),
		}
		labels = append(labels, label)
	}
	resp := &pb.LinePortraitInfoResp{
		Id:             dest.ID,
		Label:          labels,
		Gender:         uint32(dest.Gender),
		Birthday:       dest.Birthday,
		Career:         dest.Career,
		EducationLevel: uint32(dest.EducationLevel),
		MarriedState:   uint32(dest.MarriedState),
		FertilityState: uint32(dest.FertilityState),
		Remark:         dest.Remark,
		LineUserId:     dest.LineUserId,
		Project:        dest.Project,
	}
	return resp, nil
}

func (p *LinePortrait) EditPortraitTag(ctx echo.Context, req *pb.LinePortraitEditTagReq) error {
	tagIds := make([]int64, 0)
	currentTime := utils.NowTimestamp()
	tx := p.db.WithContext(ctx.Request().Context()).Begin()
	var lineUserId string
	// 编辑
	if req.Id > 0 {
		var pi models.FpLinePlayerPortrait
		err := tx.Table(models.GetFpLinePlayerPortraitTableName()).Where("id = ?", req.Id).First(&pi).Error
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return xerrors.New(err.Error(), code.DbError)
		} else if errors.Is(err, gorm.ErrRecordNotFound) {
			return gorm.ErrRecordNotFound
		}
		detail := pi
		pi.Tag = req.Tag
		pi.UpdateTime = utils.TimeFormat(int64(currentTime))
		if err = tx.Table(models.GetFpLinePlayerPortraitTableName()).Where("id = ?", req.Id).Save(pi).Error; err != nil {
			tx.Rollback()
			logger.Error(ctx.Request().Context(), "error update LinePortrait tag", zap.String("err", err.Error()))
			return err
		}
		// 先删除原来的标签
		if err = tx.Table(models.GetFpLinePlayerTagTableName()).Where("project = ? AND line_user_id = ?", pi.Project, pi.LineUserId).Delete(&models.FpLinePlayerTag{}).Error; err != nil {
			tx.Rollback()
			logger.Error(ctx.Request().Context(), "error delete line player tags", zap.String("err", err.Error()))
			return err
		}

		// 更新标签信息
		if req.Tag != "" {
			lineTags := strings.Split(req.Tag, ",")
			tagIds = make([]int64, len(lineTags))
			for i := range lineTags {
				tagIds[i] = cast.ToInt64(lineTags[i])
			}
			records := make([]*models.FpLinePlayerTag, len(tagIds))
			for i, id := range tagIds {
				lineTagRecord := &models.FpLinePlayerTag{
					Project:    pi.Project,
					LineUserId: pi.LineUserId,
					TagId:      id,
					CreateTime: currentTime,
					UpdateTime: utils.TimeFormat(int64(currentTime)),
				}
				records[i] = lineTagRecord
			}

			// 再插入新的标签数据
			if err = tx.Table(models.GetFpLinePlayerTagTableName()).CreateInBatches(records, len(records)).Error; err != nil {
				tx.Rollback()
				logger.Error(ctx.Request().Context(), "error create line player tags", zap.String("err", err.Error()))
				return err
			}
		}

		// 记录操作日志
		opDetail := &models.FpLineOperateLog{
			OperationGroup:  pb.OpGroup_OpGroupPortrait.String(),
			OperationAction: pb.OpAction_OpActionUpdate.String(),
			BaseID:          pi.LineUserId,
			UniqueID:        uuid.New().String(),
			Project:         pi.Project,
			BeforeDetail:    utils.ToJson(detail),
			AfterDetail:     utils.ToJson(pi),
			CreateTime:      time.Now(),
			Operator:        cast.ToString(ctx.Get(cst.AccountInfoCtx)),
		}
		err = tx.Model(&models.FpLineOperateLog{}).Create(opDetail).Error
		if err != nil {
			tx.Rollback()
			logger.Error(ctx.Request().Context(), "error add line operate log", zap.String("err", err.Error()))
			return xerrors.New(err.Error(), code.DbError)
		}
		lineUserId = pi.LineUserId
	} else {
		// 新增
		var cnt int64
		err := tx.Table(models.GetFpLinePlayerPortraitTableName()).Where("line_user_id = ? AND project = ?", req.LineUserId, req.Project).Count(&cnt).Error
		if err != nil {
			logger.Error(ctx.Request().Context(), "error count LinePortrait", zap.String("err", err.Error()))
			return err
		}
		if cnt >= 1 {
			return xerrors.New("该玩家的画像信息已存在", code.RepeatData)
		}
		record := models.FpLinePlayerPortrait{
			LineUserId: req.LineUserId,
			Project:    req.Project,
			Tag:        req.Tag,
			CreateTime: currentTime,
			UpdateTime: utils.TimeFormat(int64(currentTime)),
		}
		if err = tx.Table(models.GetFpLinePlayerPortraitTableName()).Save(&record).Error; err != nil {
			tx.Rollback()
			logger.Error(ctx.Request().Context(), "error create LinePortrait", zap.String("err", err.Error()))
			return err
		}
		// sync to es
		if req.Tag != "" {
			lineTags := strings.Split(req.Tag, ",")
			tagIds = make([]int64, len(lineTags))
			for i := range lineTags {
				tagIds[i] = cast.ToInt64(lineTags[i])
			}
			records := make([]*models.FpLinePlayerTag, len(tagIds))
			for i, id := range tagIds {
				dscTagRecord := &models.FpLinePlayerTag{
					Project:    req.Project,
					LineUserId: req.LineUserId,
					TagId:      id,
					CreateTime: currentTime,
					UpdateTime: utils.TimeFormat(int64(currentTime)),
				}
				records[i] = dscTagRecord
			}
			if err = tx.Table(models.GetFpLinePlayerTagTableName()).CreateInBatches(records, len(records)).Error; err != nil {
				tx.Rollback()
				logger.Error(ctx.Request().Context(), "error create player tags", zap.String("err", err.Error()))
				return err
			}
		}
		// 记录操作日志
		opDetail := &models.FpLineOperateLog{
			OperationGroup:  pb.OpGroup_OpGroupPortrait.String(),
			OperationAction: pb.OpAction_OpActionAdd.String(),
			BaseID:          req.LineUserId,
			UniqueID:        uuid.New().String(),
			Project:         req.Project,
			BeforeDetail:    "{}",
			AfterDetail:     utils.ToJson(record),
			CreateTime:      time.Now(),
			Operator:        cast.ToString(ctx.Get(cst.AccountInfoCtx)),
		}
		err = tx.Model(&models.FpLineOperateLog{}).Create(opDetail).Error
		if err != nil {
			tx.Rollback()
			logger.Error(ctx.Request().Context(), "error add line operate log", zap.String("err", err.Error()))
			return xerrors.New(err.Error(), code.DbError)
		}
		lineUserId = req.LineUserId
	}
	// sync to es
	if lineUserId == "" {
		tx.Rollback()
		logger.Error(ctx.Request().Context(), "error lineUserId is empty")
		return xerrors.New("lineUserId is empty", code.InvalidParams)
	}
	if err := elasticsearch.DefaultLineEsSvc.UpdateTag(ctx.Request().Context(), []string{lineUserId}, tagIds); err != nil {
		tx.Rollback()
		return err
	}
	// 提交事务
	if err := tx.Commit().Error; err != nil {
		logger.Error(ctx.Request().Context(), "[LinePortraitEdit] transaction commit err", zap.String("err", err.Error()))
		return xerrors.New(err.Error(), code.DbError)
	}
	return nil
}

func (p *LinePortrait) EditPortraitBasic(ctx echo.Context, req *pb.LinePortraitEditBasicReq) error {
	currentTime := utils.NowTimestamp()
	tx := p.db.WithContext(ctx.Request().Context()).Begin()
	var lineUserId string

	// 编辑
	if req.Id > 0 {
		var pi models.FpLinePlayerPortrait
		err := tx.Table(models.GetFpLinePlayerPortraitTableName()).Where("id = ?", req.Id).First(&pi).Error
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return xerrors.New(err.Error(), code.DbError)
		} else if errors.Is(err, gorm.ErrRecordNotFound) {
			return gorm.ErrRecordNotFound
		}
		detail := pi
		pi.Gender = uint8(req.Gender)
		pi.Birthday = req.Birthday
		pi.Career = req.Career
		pi.EducationLevel = uint8(req.EducationLevel)
		pi.MarriedState = uint8(req.MarriedState)
		pi.FertilityState = uint8(req.FertilityState)
		pi.UpdateTime = utils.TimeFormat(int64(currentTime))
		if err = tx.Table(models.GetFpLinePlayerPortraitTableName()).Where("id = ?", req.Id).Save(pi).Error; err != nil {
			tx.Rollback()
			logger.Error(ctx.Request().Context(), "error update LinePortrait basic", zap.String("err", err.Error()))
			return err
		}

		// 记录操作日志
		opDetail := &models.FpLineOperateLog{
			OperationGroup:  pb.OpGroup_OpGroupPortrait.String(),
			OperationAction: pb.OpAction_OpActionUpdate.String(),
			BaseID:          detail.LineUserId,
			UniqueID:        uuid.New().String(),
			Project:         pi.Project,
			BeforeDetail:    utils.ToJson(detail),
			AfterDetail:     utils.ToJson(pi),
			CreateTime:      time.Now(),
			Operator:        cast.ToString(ctx.Get(cst.AccountInfoCtx)),
		}
		err = tx.Model(&models.FpLineOperateLog{}).Create(opDetail).Error
		if err != nil {
			tx.Rollback()
			logger.Error(ctx.Request().Context(), "error add line operate log", zap.String("err", err.Error()))
			return xerrors.New(err.Error(), code.DbError)
		}
		lineUserId = pi.LineUserId
	} else {
		// 新增
		var cnt int64
		err := tx.Table(models.GetFpLinePlayerPortraitTableName()).Where("line_user_id = ? AND project = ?", req.LineUserId, req.Project).Count(&cnt).Error
		if err != nil {
			return err
		}
		if cnt >= 1 {
			return xerrors.New("该玩家的画像信息已存在", code.RepeatData)
		}
		record := models.FpLinePlayerPortrait{
			LineUserId:     req.LineUserId,
			Project:        req.Project,
			Gender:         uint8(req.Gender),
			Birthday:       req.Birthday,
			Career:         req.Career,
			EducationLevel: uint8(req.EducationLevel),
			MarriedState:   uint8(req.MarriedState),
			FertilityState: uint8(req.FertilityState),
			CreateTime:     currentTime,
			UpdateTime:     utils.TimeFormat(int64(currentTime)),
		}
		if err = tx.Table(models.GetFpLinePlayerPortraitTableName()).Save(&record).Error; err != nil {
			tx.Rollback()
			logger.Error(ctx.Request().Context(), "error create LinePortrait", zap.String("err", err.Error()))
			return err
		}
		// 记录操作日志
		opDetail := &models.FpLineOperateLog{
			OperationGroup:  pb.OpGroup_OpGroupPortrait.String(),
			OperationAction: pb.OpAction_OpActionAdd.String(),
			BaseID:          req.LineUserId,
			UniqueID:        uuid.New().String(),
			Project:         req.Project,
			BeforeDetail:    "{}",
			AfterDetail:     utils.ToJson(record),
			CreateTime:      time.Now(),
			Operator:        cast.ToString(ctx.Get(cst.AccountInfoCtx)),
		}
		err = tx.Model(&models.FpLineOperateLog{}).Create(opDetail).Error
		if err != nil {
			tx.Rollback()
			logger.Error(ctx.Request().Context(), "error add line operate log", zap.String("err", err.Error()))
			return xerrors.New(err.Error(), code.DbError)
		}
		lineUserId = req.LineUserId
	}

	// sync to es 更新生日信息
	{
		if lineUserId == "" {
			tx.Rollback()
			logger.Error(ctx.Request().Context(), "error lineUserId is empty")
			return xerrors.New("lineUserId is empty", code.InvalidParams)
		}
		var dest = map[string]interface{}{}
		if req.Birthday != "" {
			dest = map[string]interface{}{
				"birthday":           req.Birthday,
				"birthday_month_day": req.Birthday[5:],
				"updated_at":         currentTime,
			}
		} else {
			dest = map[string]interface{}{
				"birthday":           "",
				"birthday_month_day": "",
				"updated_at":         currentTime,
			}
		}
		if err := elasticsearch.DefaultLineEsSvc.UpdateLine(ctx.Request().Context(), lineUserId, dest); err != nil {
			tx.Rollback()
			logger.Error(ctx.Request().Context(), "error update line info to es", zap.String("err", err.Error()))
			return err
		}
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		logger.Error(ctx.Request().Context(), "[LinePortraitEdit] transaction commit err", zap.String("err", err.Error()))
		return xerrors.New(err.Error(), code.DbError)
	}
	return nil
}

func (p *LinePortrait) EditPortraitRemark(ctx echo.Context, req *pb.LinePortraitEditRemarkReq) error {
	currentTime := utils.NowTimestamp()
	tx := p.db.WithContext(ctx.Request().Context()).Begin()
	// 编辑
	if req.Id > 0 {
		var pi models.FpLinePlayerPortrait
		err := tx.Table(models.GetFpLinePlayerPortraitTableName()).Where("id = ?", req.Id).First(&pi).Error
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return xerrors.New(err.Error(), code.DbError)
		} else if errors.Is(err, gorm.ErrRecordNotFound) {
			return gorm.ErrRecordNotFound
		}
		remark := pi.Remark
		detail := pi
		pi.Remark = req.Remark
		pi.UpdateTime = utils.TimeFormat(int64(currentTime))
		if err = tx.Table(models.GetFpLinePlayerPortraitTableName()).Select("*").Where("id = ?", req.Id).Updates(pi).Error; err != nil {
			tx.Rollback()
			logger.Error(ctx.Request().Context(), "error update LinePortrait basic", zap.String("err", err.Error()))
			return err
		}
		// sync to es
		// 更新备注信息
		if req.Remark != "" {
			dest := map[string]interface{}{
				"portrait_remark": req.Remark,
				"updated_at":      utils.NowTimestamp(),
			}
			if err = elasticsearch.DefaultLineEsSvc.UpdateLine(ctx.Request().Context(), pi.LineUserId, dest); err != nil {
				tx.Rollback()
				return err
			}
		}
		// 清空备注信息
		if remark != "" && req.Remark == "" {
			updatedAt := utils.NowTimestamp()
			if err = elasticsearch.DefaultLineEsSvc.ClearRemark(ctx.Request().Context(), pi.LineUserId, updatedAt); err != nil {
				tx.Rollback()
				return err
			}
		}
		// 记录操作日志
		opDetail := &models.FpLineOperateLog{
			OperationGroup:  pb.OpGroup_OpGroupPortrait.String(),
			OperationAction: pb.OpAction_OpActionUpdate.String(),
			BaseID:          detail.LineUserId,
			UniqueID:        uuid.New().String(),
			Project:         pi.Project,
			BeforeDetail:    utils.ToJson(detail),
			AfterDetail:     utils.ToJson(pi),
			CreateTime:      time.Now(),
			Operator:        cast.ToString(ctx.Get(cst.AccountInfoCtx)),
		}
		err = tx.Model(&models.FpLineOperateLog{}).Create(opDetail).Error
		if err != nil {
			tx.Rollback()
			logger.Error(ctx.Request().Context(), "error add line operate log", zap.String("err", err.Error()))
			return xerrors.New(err.Error(), code.DbError)
		}
		// 提交事务
		if err = tx.Commit().Error; err != nil {
			logger.Error(ctx.Request().Context(), "[LinePortraitEdit] transaction commit err", zap.String("err", err.Error()))
			return xerrors.New(err.Error(), code.DbError)
		}
		return nil
	} else {
		// 新增
		var cnt int64
		err := tx.Table(models.GetFpLinePlayerPortraitTableName()).Where("line_user_id = ? AND project = ?", req.LineUserId, req.Project).Count(&cnt).Error
		if err != nil {
			return err
		}
		if cnt >= 1 {
			return xerrors.New("该玩家的画像信息已存在", code.RepeatData)
		}
		record := models.FpLinePlayerPortrait{
			LineUserId: req.LineUserId,
			Project:    req.Project,
			Remark:     req.Remark,
			CreateTime: currentTime,
			UpdateTime: utils.TimeFormat(int64(currentTime)),
		}
		if err = tx.Table(models.GetFpLinePlayerPortraitTableName()).Save(&record).Error; err != nil {
			tx.Rollback()
			logger.Error(ctx.Request().Context(), "error create LinePortrait", zap.String("err", err.Error()))
			return err
		}
		// 备注
		if req.Remark != "" {
			dest := map[string]interface{}{
				"portrait_remark": req.Remark,
				"updated_at":      utils.NowTimestamp(),
			}
			if err = elasticsearch.DefaultLineEsSvc.UpdateLine(ctx.Request().Context(), req.LineUserId, dest); err != nil {
				tx.Rollback()
				return err
			}
		}
		// 记录操作日志
		opDetail := &models.FpLineOperateLog{
			OperationGroup:  pb.OpGroup_OpGroupPortrait.String(),
			OperationAction: pb.OpAction_OpActionAdd.String(),
			BaseID:          req.LineUserId,
			UniqueID:        uuid.New().String(),
			Project:         req.Project,
			BeforeDetail:    "{}",
			AfterDetail:     utils.ToJson(record),
			CreateTime:      time.Now(),
			Operator:        cast.ToString(ctx.Get(cst.AccountInfoCtx)),
		}
		err = tx.Model(&models.FpLineOperateLog{}).Create(opDetail).Error
		if err != nil {
			tx.Rollback()
			logger.Error(ctx.Request().Context(), "error add line operate log", zap.String("err", err.Error()))
			return xerrors.New(err.Error(), code.DbError)
		}
		// 提交事务
		if err = tx.Commit().Error; err != nil {
			logger.Error(ctx.Request().Context(), "[LinePortraitEdit] transaction commit err", zap.String("err", err.Error()))
			return xerrors.New(err.Error(), code.DbError)
		}
		return nil
	}
}
