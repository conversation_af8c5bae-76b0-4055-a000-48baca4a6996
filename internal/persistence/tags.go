package persistence

import (
	"context"
	"errors"
	"fmt"
	"github.com/spf13/cast"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"gorm.io/plugin/dbresolver"
	"ops-ticket-api/internal/code"
	"ops-ticket-api/internal/framework/database"
	"ops-ticket-api/internal/framework/xerrors"
	"ops-ticket-api/models"
	"ops-ticket-api/proto/pb"
	"ops-ticket-api/utils"
	"strings"
	"sync"
	"time"
)

var (
	tagsOnce sync.Once
	tagsRepo *tags
)

// tag 标签
type tags struct {
	db *gorm.DB
}

// NewTags init
func NewTags() *tags {
	tagsOnce.Do(func() {
		tagsRepo = &tags{
			db: database.Db(),
		}
	})
	return tagsRepo
}

// TagLibEnable 标签组禁用启用接口
func (dto *tagLib) TagLibEnable(ctx context.Context, objId uint32, enable bool, account string) error {
	var toEnable = code.StatusFalse
	if enable {
		toEnable = code.StatusTrue
	}
	update := map[string]interface{}{
		"enable":     toEnable,
		"operator":   account,
		"updated_at": time.Now().Unix(),
	}
	if err := dto.db.WithContext(ctx).Model(&models.FpOpsTagsLib{}).Where("lib_id=?", objId).Updates(update).Error; err != nil {
		logger.Error(ctx, "TagLib enable err", zap.Uint32("objId", objId), zap.Bool("enable", enable), zap.String("err", err.Error()))
		return xerrors.New(err.Error(), code.DbError)
	}
	if err := dto.db.WithContext(ctx).Model(&models.FpOpsTags{}).Where("lib_id=?", objId).Updates(update).Error; err != nil {
		logger.Error(ctx, "TagLib TagTable enable err", zap.Uint32("objId", objId), zap.Bool("enable", enable), zap.String("err", err.Error()))
		return xerrors.New(err.Error(), code.DbError)
	}
	delete(update, "operator")
	if err := dto.db.WithContext(ctx).Model(&models.FpOpsTagsLibGm{}).Where("lib_id=?", objId).Updates(update).Error; err != nil {
		logger.Error(ctx, "TagLibGm enable err", zap.Uint32("objId", objId), zap.Bool("enable", enable), zap.String("err", err.Error()))
		return xerrors.New(err.Error(), code.DbError)
	}
	return nil
}

func (dto *tags) GetSiblingNode(ctx context.Context, libId, pid uint32) (*models.FpOpsTags, error) {
	dest := &models.FpOpsTags{}
	if err := dto.db.WithContext(ctx).Clauses(dbresolver.Write).Where("lib_id=? and parent_id=?", libId, pid).Order("rgt desc").Take(&dest).Error; err != nil {
		if !errors.Is(err, gorm.ErrRecordNotFound) {
			logger.Error(ctx, "Get Sibling tag info err", zap.String("err", err.Error()))
		}
		return nil, err
	}
	return dest, nil
}

// TagInfo 标签信息
func (dto *tags) TagInfo(ctx context.Context, tagId uint32) (*models.FpOpsTags, error) {
	dest := &models.FpOpsTags{}
	if err := dto.db.WithContext(ctx).Where("tag_id=?", tagId).Take(&dest).Error; err != nil {
		if !errors.Is(err, gorm.ErrRecordNotFound) {
			logger.Error(ctx, "get tag info err", zap.String("err", err.Error()))
		}
		return nil, err
	}
	return dest, nil
}

// TagAdd 标签添加
func (dto *tags) TagAdd(ctx context.Context, tag *models.FpOpsTags) (uint32, error) {
	tx := dto.db.WithContext(ctx).Begin()
	//UPDATE tag SET lft=lft+2 WHERE lft > @lft;
	if err := tx.Model(models.FpOpsTags{}).Where("lib_id=? and lft>?", tag.LibID, tag.Lft).Update("lft", gorm.Expr("lft + ?", 2)).Error; err != nil {
		tx.Rollback()
		logger.Error(ctx, "update label lft err", zap.String("tag", tag.TagName), zap.String("err", err.Error()))
		return 0, err
	}
	if tag.ParentID != 0 {
		//UPDATE tag SET rgt=rgt+2 WHERE rgt >= @lft;
		if err := tx.Model(models.FpOpsTags{}).Where("lib_id=? and rgt>=?", tag.LibID, tag.Lft).Update("rgt", gorm.Expr("rgt + ?", 2)).Error; err != nil {
			tx.Rollback()
			logger.Error(ctx, "update tag rgt err", zap.String("tag", tag.TagName), zap.String("err", err.Error()))
			return 0, err
		}
	}
	if err := tx.Create(&tag).Error; err != nil {
		tx.Rollback()
		logger.Error(ctx, "create tag info err", zap.String("tag", tag.TagName), zap.String("err", err.Error()))
		return 0, err
	}
	if err := tx.Commit().Error; err != nil {
		tx.Rollback()
		logger.Error(ctx, "commit tag rgt err", zap.String("tag", tag.TagName), zap.String("err", err.Error()))
		return 0, err
	}

	return tag.TagID, nil
}

func (tags *tags) GetTagsByLibID(ctx context.Context, libID uint32) ([]*models.FpOpsTags, error) {
	var tagsList []*models.FpOpsTags
	if err := tags.db.WithContext(ctx).Where("lib_id = ?", libID).
		Find(&tagsList).Error; err != nil {
		logger.Error(ctx, "Failed to get tags by lib_id", zap.String("err", err.Error()))
		return nil, err
	}
	return tagsList, nil
}

func (tags *tags) GetTagsSlice(ctx context.Context, tagIds []uint32) map[uint32][]string {
	fun := "tags.GetTagsSlice"
	type tagDf struct {
		TagName  string `json:"tag_name"`
		TagId    uint32 `json:"tag_id"`
		ParentId uint32 `json:"parent_id"`
		LibId    uint32 `json:"lib_id"`
	}
	var libNames = make(map[uint32]string, 0)
	var tagLibIdsMap = make(map[uint32]uint32, 0)
	var libIds = make([]uint32, 0)

	var tagMap = make(map[uint32]*tagDf, 0)
	var res = make(map[uint32][]string, 0)
	var f = func(ids []uint32) ([]*tagDf, error) {
		var _tagData []*tagDf
		_err := tags.db.Model(&models.FpOpsTags{}).Where("tag_id in (?)", ids).Select("lib_id,tag_name,tag_id,parent_id").Find(&_tagData).Error
		return _tagData, _err
	}
	for _, tid := range tagIds {
		res[tid] = make([]string, 0)
	}
	for {
		if len(tagIds) == 0 { // return
			break
		}
		tagsD, err := f(tagIds)
		if err != nil {
			logger.Errorf(ctx, "%s func exec return err. err:%v. ids:%+v", fun, err, tagIds)
			return res
		}
		var pTagIds []uint32
		for _, t := range tagsD {
			tagMap[t.TagId] = t
			if t.ParentId != 0 {
				pTagIds = append(pTagIds, t.ParentId)
			}
			// libs
			tagLibIdsMap[t.TagId] = t.LibId
			libIds = append(libIds, t.LibId)
		}
		tagIds = pTagIds
	}

	// 整理数据
	for key := range res {
		id := key
	again:
		if v, ok := tagMap[id]; ok {
			res[key] = append([]string{v.TagName}, res[key]...)
			if v.ParentId != 0 {
				id = v.ParentId
				goto again
			}
		}
	}

	// lib_name
	if len(libIds) > 0 {
		var libData = make([]map[string]interface{}, 0)
		err := tags.db.Model(&models.FpOpsTagsLib{}).
			Where("lib_id in (?)", utils.RmDupAny(libIds)).Select("lib_id,lib_name").
			Find(&libData).Error
		if err != nil {
			logger.Errorf(ctx, "%s func exec return err. err:%v. ids:%+v", fun, err, libIds)
		}
		for _, v := range libData {
			if v == nil {
				continue
			}
			libNames[cast.ToUint32(v["lib_id"])] = cast.ToString(v["lib_name"])
		}
	}

	for id, v := range res {
		res[id] = append([]string{libNames[tagLibIdsMap[id]]}, v...)
	}
	// return
	return res
}

func (dto *tags) TagDel(ctx context.Context, tagId uint32) error {
	// 如果被删除的标签还有子层级标签，不允许删除
	var childrenCount int64
	if err := dto.db.Model(&models.FpOpsTags{}).Where("parent_id =?", tagId).Count(&childrenCount).Error; err != nil {
		logger.Errorf(ctx, "query tags count error:%v", err)
		return err
	}
	if childrenCount > 0 {
		return errors.New("存在子层级标签，不允许删除")
	}
	if err := dto.db.Model(&models.FpOpsTags{}).Where("tag_id =?", tagId).Delete(&models.FpOpsTags{}).Error; err != nil {
		return err
	}
	return nil
}

func (dto *tags) TagEdit(ctx context.Context, req *pb.TagConfigEditReq) error {
	dest := map[string]interface{}{
		"tag_name":   req.TagName,
		"updated_at": utils.NowTimestamp(),
	}
	if err := dto.db.Model(&models.FpOpsTags{}).Where("tag_id =?", req.TagId).Updates(dest).Error; err != nil {
		return err
	}
	return nil
}

func (dto *tags) FetchTagsById(ctx context.Context, tagIds []uint32) ([]*pb.PortraitInfoResp_LabelDetail, error) {
	dest := []*pb.PortraitInfoResp_LabelDetail{}
	if err := dto.db.Model(&models.FpOpsTags{}).Where("tag_id IN (?)", tagIds).Find(&dest).Error; err != nil {
		logger.Error(ctx, "get tags by id err", zap.String("err", err.Error()))
		return nil, xerrors.New(err.Error(), code.DbError)
	}
	return dest, nil
}

func (dto *tags) FetchTagNameById(ctx context.Context, tagIds []uint32) ([]string, error) {
	var tagsList []string
	if err := dto.db.Model(&models.FpOpsTags{}).Where("tag_id IN (?)", tagIds).Pluck("tag_name", &tagsList).Error; err != nil {
		logger.Error(ctx, "get tagName by id err", zap.String("err", err.Error()))
		return nil, xerrors.New(err.Error(), code.DbError)
	}
	return tagsList, nil
}

// 1. 根据游戏模块库下不同的一级分类名称查询
// 2. 根据非游戏模块库下的所有查询
// GetAllTagPathsByLibID 查询指定 libID 下所有标签，并返回每条标签的“一级-二级-…-本级”路径列表
func (dto *tags) GetAllTagPathsByLibID(ctx context.Context, libID uint32) ([]string, error) {
	all := []*models.FpOpsTags{}
	if err := dto.db.WithContext(ctx).
		Model(&models.FpOpsTags{}).
		Select("tag_id, tag_name, parent_id").
		Where("lib_id = ?", libID).
		Find(&all).Error; err != nil {
		return nil, fmt.Errorf("load tags for lib %d: %w", libID, err)
	}
	libName, err := dto.FetchLibNameByLidID(ctx, libID)
	if err != nil {
		return nil, err
	}

	// 1. 建立 id -> *tag 映射
	tagMap := make(map[uint32]*models.FpOpsTags, len(all))
	for _, t := range all {
		tagMap[t.TagID] = t
	}
	// 2. 建立 parentID -> children 列表，用于判断“有没有子标签”
	childrenMap := make(map[uint32][]*models.FpOpsTags)
	for _, t := range all {
		childrenMap[t.ParentID] = append(childrenMap[t.ParentID], t)
	}

	// 3. 对每个标签，向上追溯拼接路径，但跳过那些：
	//    – 深度为1或2（一级/二级）且在 childrenMap 中有子标签的项
	var paths []string
	for _, rec := range all {
		// 先算出这条记录的完整“从根到当前”的 names 列表
		var names []string
		cur := rec
		for cur != nil {
			names = append([]string{cur.TagName}, names...)
			if cur.ParentID == 0 {
				break
			}
			cur = tagMap[cur.ParentID]
		}

		depth := len(names) // 1=一级，2=二级，以此类推
		if (depth == 1 || depth == 2) &&
			len(childrenMap[rec.TagID]) > 0 {
			// 如果是一级或二级标签，并且还有子标签，就跳过
			continue
		}

		// 只留下“叶子”级，以及深度>2 的中间节点
		tagStr := strings.Join(names, "-")
		paths = append(paths, fmt.Sprintf("%s-%s", libName, tagStr))
	}
	return paths, nil
}

func (dto *tags) FetchLibNameByLidID(ctx context.Context, libId uint32) (string, error) {
	lib := &models.FpOpsTagsLib{}
	if err := dto.db.WithContext(ctx).Model(&models.FpOpsTagsLib{}).Where("lib_id = ?", libId).First(lib).Error; err != nil {
		return "", err
	}
	return lib.LibName, nil
}

// GetTagPathsByTopCategories 在库 libID 下，只返回以 topCategoryName 作为一级标签的那一条完整路径
func (dto *tags) GetTagPathsByTopCategories(ctx context.Context, libID uint32, topCategoryName string) ([]string, error) {
	// 1. 拿到所有的“库名-一级-二级-…-本级”路径
	allPaths, err := dto.GetAllTagPathsByLibID(ctx, libID)
	if err != nil {
		return nil, err
	}
	fmt.Println(len(allPaths))

	// 2. 过滤：只有当一级标签 == topCategoryName 时才保留
	var filtered []string
	for _, p := range allPaths {
		// 路径格式："<库名>-<一级>-<二级>-...-<本级>"
		parts := strings.Split(p, "-")
		if len(parts) >= 3 && parts[2] == topCategoryName {
			filtered = append(filtered, p)
		}
	}

	return filtered, nil
}

func (dto *tags) GetTagLibNameByTagId(ctx context.Context, tagId uint32) (lib, first, second, third string, all []string, err error) {
	// 1. 查叶子标签，获取 LibID、TagName、ParentID
	var leaf models.FpOpsTags
	if err = dto.db.WithContext(ctx).
		Where("tag_id = ?", tagId).
		First(&leaf).Error; err != nil {
		return
	}

	// 2. 查标签库名称
	var libRec models.FpOpsTagsLib
	if err = dto.db.WithContext(ctx).
		Where("lib_id = ?", leaf.LibID).
		First(&libRec).Error; err != nil {
		return
	}
	lib = libRec.LibName

	// 3. 回溯父级，收集从根到叶的标签名称
	names := []string{leaf.TagName}
	cur := leaf
	for cur.ParentID != 0 {
		var parent models.FpOpsTags
		if err = dto.db.WithContext(ctx).
			Where("tag_id = ?", cur.ParentID).
			First(&parent).Error; err != nil {
			return
		}
		// 把父级插到 slice 前面
		names = append([]string{parent.TagName}, names...)
		cur = parent
	}
	all = names
	// 4. 根据 names 长度填充 first/second/third
	switch len(names) {
	case 1:
		first = names[0]
	case 2:
		first, second = names[0], names[1]
	default:
		first, second, third = names[0], names[1], names[2]
	}
	return
}

// GetSpecialTagFirstTime 根据特殊标签库下的某标签名查找第一次打标时间
func (dto *tags) GetSpecialTagFirstTime(ctx context.Context, tagName string) (uint64, error) {
	tag := &models.FpOpsTags{}
	ticketTag := &models.FpOpsTicketsTags{}
	err := dto.db.WithContext(ctx).Model(&models.FpOpsTags{}).Where("tag_name like ?", "%"+tagName+"%").
		First(&tag).Error
	if err != nil {
		return 0, err
	}
	err = dto.db.WithContext(ctx).Model(&models.FpOpsTicketsTags{}).Where("tag_id = ?", tag.TagID).
		Order("created_at asc").
		Limit(1).
		First(&ticketTag).Error
	if err != nil {
		return 0, err
	}
	return ticketTag.CreatedAt, nil
}
