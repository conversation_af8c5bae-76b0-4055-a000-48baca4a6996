package persistence

import (
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"golang.org/x/net/context"
	"ops-ticket-api/models"
	"ops-ticket-api/proto/pb"
)

func (repo *dscInteractions) ChannelMessageHistories(ctx context.Context, param *pb.DscChannelDialogReq) ([]*models.FpDscDmCommu, error) {
	var (
		communs []*models.FpDscDmCommu
		fun     = "dscInteractions.ChannelMessageHistories"
	)
	commuQuery := repo.db.WithContext(ctx).Where("channel_id = ?", param.ChannelId).Limit(int(param.Limit))
	if param.Before != "" {
		commuQuery = commuQuery.Where("msg_id < ?", param.Before).Order("msg_id desc")
	} else if param.After != "" {
		commuQuery = commuQuery.Where("msg_id > ?", param.After).Order("msg_id asc")
	} else {
		commuQuery = commuQuery.Order("msg_id desc")
	}
	if len(param.MsgIds) > 0 {
		commuQuery = commuQuery.Where("msg_id in (?)", param.MsgIds)
	}

	err := commuQuery.Limit(int(param.Limit)).Find(&communs).Error
	if err != nil {
		return nil, err
	}
	if param.Before == "" && param.After == "" { // 后台查看最新消息 - 清理未读事件his
		if _err := repo.MsgUnreadRemove(ctx, param.ChannelId); _err != nil {
			logger.Errorf(ctx, "%s MsgUnreadRemove err. err:%v. channelId:%s", _err, fun, param.ChannelId)
		}
	}
	return communs, nil
}

func (repo *dscInteractions) CommuReactionList(ctx context.Context, msgIds []string) (map[string][]*models.FpDscDmCommuReaction, error) {
	var (
		reactions   []*models.FpDscDmCommuReaction
		reactionMap = make(map[string][]*models.FpDscDmCommuReaction)
	)
	for _, msgId := range msgIds {
		reactionMap[msgId] = make([]*models.FpDscDmCommuReaction, 0)
	}
	err := repo.db.WithContext(ctx).Where("msg_id in (?)", msgIds).Find(&reactions).Error
	if err != nil {
		return nil, err
	}

	for _, reaction := range reactions {
		reactionMap[reaction.MsgID] = append(reactionMap[reaction.MsgID], reaction)
	}
	return reactionMap, nil
}

func (repo *dscInteractions) MsgUnreadList(ctx context.Context, channelId string) ([]*models.FpDscDmUnread, error) {
	var (
		unreads []*models.FpDscDmUnread
	)
	err := repo.db.WithContext(ctx).Where("channel_id = ?", channelId).Find(&unreads).Error
	if err != nil {
		return nil, err
	}
	if err := repo.MsgUnreadRemove(ctx, channelId); err != nil {
		logger.Errorf(ctx, "MsgUnreadRemove err. err:%v. channelId:%s", err, channelId)
	}
	return unreads, nil
}

func (repo *dscInteractions) MsgUnreadRemove(ctx context.Context, channelId string) error {
	return repo.db.WithContext(ctx).Where("channel_id = ?", channelId).Delete(&models.FpDscDmUnread{}).Error
}

func (repo *dscInteractions) GetMsgIdSendUsers(ctx context.Context, msgIds []string) (map[string]string, error) {
	var sends []*models.FpDscDmSend
	err := repo.db.WithContext(ctx).Model(&models.FpDscDmSend{}).
		Where("msg_id in (?)", msgIds).Select("msg_id", "operator").Find(&sends).Error
	if err != nil {
		return map[string]string{}, err
	}
	var sendMap = make(map[string]string, len(sends))
	for _, v := range sends {
		sendMap[v.MsgID] = v.Operator
	}
	return sendMap, nil
}
