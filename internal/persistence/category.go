package persistence

import (
	"context"
	"errors"
	"fmt"
	"sync"

	"ops-ticket-api/internal/code"
	"ops-ticket-api/internal/framework/cache/keys"
	"ops-ticket-api/internal/framework/cache/rds"
	"ops-ticket-api/internal/framework/database"
	"ops-ticket-api/internal/framework/xerrors"
	"ops-ticket-api/models"
	"ops-ticket-api/proto/pb"

	"github.com/labstack/echo/v4"
	"github.com/spf13/cast"
	"github.com/spf13/viper"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// todo fix 目前使用 老工单数据的数据，二期迁移出去。 此处只做查询，不允许变更

var (
	onceCat        sync.Once
	catRepo        *Cat
	DefaultCatRepo = NewCat()
)

// Cat 问题分类配置
type Cat struct {
	db *gorm.DB
}

// NewCat init
func NewCat() *Cat {
	onceCat.Do(func() {
		catRepo = &Cat{
			db: database.GetOldTicketDb(),
		}
	})
	return catRepo
}

// OldTicketCatInfoByIdS 获取问题分类信息
func (dto *Cat) OldTicketCatTitleByIdS(ctx context.Context, project, lang string, catIds []int32) ([]*models.FpOpsCategoryLang, error) {
	list := make([]*models.FpOpsCategoryLang, 0)
	if err := dto.db.WithContext(ctx).Model(&models.FpOpsCategory{}).Select("fp_ops_category_lang.cat_id,fp_ops_category_lang.id,fp_ops_category_lang.category,fp_ops_category_lang.lang ").Joins("LEFT JOIN fp_ops_category_lang ON fp_ops_category_lang.cat_id = fp_ops_category.cat_id AND fp_ops_category_lang.lang = ?", lang).
		Where("fp_ops_category.project = ? AND fp_ops_category.cat_id IN (?)", project, catIds).Find(&list).Error; err != nil {
		logger.Errorf(ctx, "get cat info err %v catId %v", err, catIds)
		return nil, xerrors.New(code.StatusText(code.DbError), code.DbError)
	}
	return list, nil
}

// OldTicketCatInfoById 获取问题分类信息
func (dto *Cat) OldTicketCatInfoById(ctx context.Context, project string, catId uint32) (*models.FpOpsCategory, error) {
	detail := &models.FpOpsCategory{}
	if err := rds.RCli.QueryRowHash(ctx, keys.TicketCatInfo+project, cast.ToString(catId), &detail, false, func(v interface{}) error {
		if err := dto.db.WithContext(ctx).Model(detail).
			Where("project=? AND cat_id=?", project, catId).Take(detail).Error; err != nil {
			logger.Error(ctx, "get cat info err", zap.Uint32("catId", catId), zap.String("err", err.Error()))
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return xerrors.New(code.StatusText(code.NotFound), code.NotFound)
			}
			return xerrors.New(code.StatusText(code.DbError), code.DbError)
		}
		return nil
	}); err != nil {
		return nil, err
	}

	if detail != nil && detail.RelateType == 0 {
		detail.RelateType = uint32(pb.RelateType_RelateTypeTpl)
	}
	return detail, nil
}

// CatInfoById 获取问题分类信息
func (dto *Cat) CatInfoById(ctx context.Context, project string, catId uint32) (*models.FpOpsCategory, error) {
	detail := &models.FpOpsCategory{}
	if err := dto.db.WithContext(ctx).Model(detail).
		Where("project=? AND cat_id=?", project, catId).Take(detail).Error; err != nil {
		logger.Error(ctx, "get cat info err", zap.Uint32("catId", catId), zap.String("err", err.Error()))
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, xerrors.New(code.StatusText(code.NotFound), code.NotFound)
		}
		return nil, xerrors.New(code.StatusText(code.DbError), code.DbError)
	}

	if detail != nil && detail.RelateType == 0 {
		detail.RelateType = uint32(pb.RelateType_RelateTypeTpl)
	}
	// todo delete - 新工单不支持自动化流程 ；自动化流程直接报错
	if detail.RelateType != uint32(pb.RelateType_RelateTypeTpl) && detail.RelateType != uint32(pb.RelateType_RelateTypeProcess) {
		logger.Error(ctx, "category relate_type neq1|2.", logger.String("project", project), logger.Any("catId", catRepo), logger.Any("detail", detail))
		return nil, xerrors.New(code.StatusText(code.DbDataUnexpect), code.DbError)
	}
	return detail, nil
}

func (dto *Cat) CatNameBackendSlice(ctx context.Context, catId uint32) ([]string, error) {
	tcInfo := &struct {
		CatId     uint32 `json:"cat_id"`
		FrontCat  string `json:"front_cat"`
		SecondCat string `json:"second_cat"`
		Cat       string `json:"cat"`
	}{}

	fields := "tc.cat_id,tc1.category as front_cat,tc2.category as second_cat, tc.category as cat"
	query := dto.db.WithContext(ctx).Table("fp_ops_category as tc").
		Joins("LEFT JOIN fp_ops_category as tc1 ON tc1.cat_id=tc.one_level").
		Joins("LEFT JOIN fp_ops_category as tc2 ON tc2.cat_id=tc.second_level")
	if err := query.Select(fields).Where("tc.cat_id=?", catId).Take(&tcInfo).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			tcInfo.CatId = catId
			tcInfo.Cat = "Unknown"
		} else {
			return nil, err
		}
	}
	return []string{tcInfo.FrontCat, tcInfo.SecondCat, tcInfo.Cat}, nil
}

func (dto *Cat) CatNameSlice(ctx context.Context, catId uint32, language string) ([]string, error) {
	if language == "" {
		language = viper.GetString("app.lang")
	}
	tcInfo := &struct {
		CatId     uint32 `json:"cat_id"`
		FrontCat  string `json:"front_cat"`
		SecondCat string `json:"second_cat"`
		Cat       string `json:"cat"`
	}{}
	subKey := fmt.Sprintf("%s_%s_%s", keys.TicketCatLangV2, language, cast.ToString(catId))
	if err := rds.RCli.QueryRow(ctx, subKey, &tcInfo, func(v interface{}) error {
		fields := "tc.cat_id,tcl1.category as front_cat,tcl2.category as second_cat, tcl3.category as cat"
		query := dto.db.WithContext(ctx).Table("fp_ops_category as tc").
			Joins("LEFT JOIN fp_ops_category_lang as tcl1 ON tcl1.cat_id=tc.one_level AND tcl1.lang=?", language).
			Joins("LEFT JOIN fp_ops_category_lang as tcl2 ON tcl2.cat_id=tc.second_level AND tcl2.lang=?", language).
			Joins("LEFT JOIN fp_ops_category_lang as tcl3 ON tcl3.cat_id=tc.cat_id AND tcl3.lang=?", language)
		if err := query.Select(fields).Where("tc.cat_id=?", catId).Take(&tcInfo).Error; err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				tcInfo.CatId = catId
				tcInfo.Cat = "Unknown"
				return nil
			}
			logger.Error(ctx, "get cat info err", zap.Uint32("catId", catId), zap.String("err", err.Error()))
			return xerrors.New(err.Error(), code.DbError)
		}

		return nil
	}); err != nil {
		return []string{"Unknown"}, err
	}
	if tcInfo.SecondCat == "" && tcInfo.Cat != "" {
		tcInfo.SecondCat = tcInfo.Cat
		tcInfo.Cat = ""
	}
	return []string{tcInfo.FrontCat, tcInfo.SecondCat, tcInfo.Cat}, nil
}

// GetCatExistByTplId 根据模板id获取问题分类
func (dto *Cat) GetCatExistByTplId(ctx echo.Context, query map[string]interface{}) (bool, error) {
	var count int64
	query["enable"] = code.StatusTrue
	query["is_deleted"] = code.StatusFalse
	if err := dto.db.WithContext(ctx.Request().Context()).Model(&models.FpOpsCategory{}).Where(query).Count(&count).Error; err != nil {
		logger.Errorf(ctx.Request().Context(), "get category enabeled count err", zap.String("err", err.Error()))
		return false, xerrors.New(err.Error(), code.DbError)
	}
	if count == 0 {
		return false, nil
	}
	return true, nil
}

// CatTree 问题分类配置(树)列表接口
func (dto *Cat) CatTree(ctx context.Context, project, language string) ([]*pb.CatItems, error) {
	dest := make([]*pb.CatItems, 0)
	field := "c.cat_id, c.category, c.one_level, c.second_level, c.`level`"
	cond := map[string]interface{}{
		"c.project":    project,
		"c.enable":     code.StatusTrue,
		"c.is_deleted": code.StatusFalse,
	}

	query := dto.db.WithContext(ctx).Table("fp_ops_category c").Where(cond)

	if language != "" {
		query.Joins("LEFT JOIN fp_ops_category_lang l on l.cat_id=c.cat_id AND l.lang=?", language)
		field += ",l.category as lang_category"
	}
	if err := query.Select(field).Order("c.cat_id asc").Find(&dest).Error; err != nil {
		logger.Error(ctx, "get category list tree count err", zap.String("err", err.Error()))
		return nil, xerrors.New(err.Error(), code.DbError)
	}

	return dest, nil
}

// GetDetailById 获取问题分类信息
func (dto *Cat) GetDetailById(ctx echo.Context, catId uint32) (*models.FpOpsCategory, error) {
	dest := &models.FpOpsCategory{}
	if err := dto.db.WithContext(ctx.Request().Context()).Table("fp_ops_category").Where("cat_id=?", catId).Take(dest).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return dest, err
		}
		logger.Error(ctx.Request().Context(), "get cat info err", zap.Uint32("catId", catId), zap.String("err", err.Error()))
		return dest, xerrors.New(code.StatusText(code.DbError), code.DbError)
	}
	return dest, nil
}
