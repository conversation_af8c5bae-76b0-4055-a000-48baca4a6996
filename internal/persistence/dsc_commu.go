// Copyright 2024 funplus Authors. All Rights Reserved.
// @Description: discord commu
// @Author: jun.qiu
// @Date: 2024/08/19 11:52 AM

package persistence

import (
	"errors"
	"fmt"
	"github.com/labstack/echo/v4"
	"github.com/spf13/cast"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"ops-ticket-api/internal/code"
	"ops-ticket-api/internal/cst"
	"ops-ticket-api/internal/framework/database"
	"ops-ticket-api/internal/framework/xerrors"
	"ops-ticket-api/models"
	"ops-ticket-api/proto/pb"
	"strings"
	"time"
)

// DiscordCommu discord沟通记录
type DiscordCommu struct {
	db *gorm.DB
}

// NewDiscordCommu init
func NewDiscordCommu() *DiscordCommu {
	return &DiscordCommu{
		db: database.Db(),
	}
}

func (dc *DiscordCommu) DiscordCommuSave(ctx echo.Context, req *pb.DiscordCommuRecordAddReq) error {
	operator := cast.ToString(ctx.Get(cst.AccountInfoCtx))
	record := &models.FpDscCommuRecord{
		Project:      req.Project,
		CommuDate:    req.CommuDate,
		DscUserID:    req.DscUserId,
		UID:          req.Uid,
		Sid:          req.Sid,
		NickName:     req.NickName,
		PayAll:       req.PayAll,
		Question:     req.Question,
		QuestionType: uint8(req.QuestionType),
		HandleStatus: uint8(req.HandleStatus),
		Remark:       req.Remark,
		RelevantMsg:  req.MsgIds,
		Operator:     operator,
		CreatedAt:    time.Now().UTC(),
		UpdatedAt:    time.Now().UTC(),
	}
	if err := dc.db.Table(models.GetFpDscCommuRecordTableName()).Create(record).Error; err != nil {
		logger.Error(ctx.Request().Context(), "error create dsc commu", zap.String("err", err.Error()))
		return xerrors.New(err.Error(), code.DbError)
	}
	return nil
}

func (dc *DiscordCommu) DiscordCommuEdit(ctx echo.Context, req *pb.DiscordCommuRecordEditReq) error {
	// 查询出原记录
	record := &models.FpDscCommuRecord{}
	err := dc.db.Table(models.GetFpDscCommuRecordTableName()).Where("id = ?", req.Id).First(record).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return xerrors.New(err.Error(), code.DbError)
	} else if errors.Is(err, gorm.ErrRecordNotFound) {
		return xerrors.New("沟通记录不存在", code.InvalidParams)
	}
	// 更新沟通记录数据
	record.Operator = cast.ToString(ctx.Get(cst.AccountInfoCtx))
	record.CommuDate = req.CommuDate
	record.Question = req.Question
	record.QuestionType = uint8(req.QuestionType)
	record.HandleStatus = uint8(req.HandleStatus)
	record.Remark = req.Remark
	record.UpdatedAt = time.Now().UTC()
	err = dc.db.Table(models.GetFpDscCommuRecordTableName()).Where("id=?", req.Id).Updates(record).Error
	if err != nil {
		logger.Error(ctx.Request().Context(), "error update dsc commu", zap.String("err", err.Error()))
		return xerrors.New(err.Error(), code.DbError)
	}
	return nil
}

func (dc *DiscordCommu) DiscordCommuList(ctx echo.Context, req *pb.DiscordCommuRecordListReq, all bool) ([]*pb.DiscordCommuRecordListResp_DiscordCommuRecord, uint32, error) {
	dest := make([]*pb.DiscordCommuRecordListResp_DiscordCommuRecord, 0)
	// 首先查出符合条件的数据条数
	commuList := []*models.FpDscCommuRecord{}
	subQuery := dc.db.Table(models.GetFpDscCommuRecordTableName())
	if len(req.Project) > 0 {
		subQuery = subQuery.Where("project IN (?)", req.Project)
	}
	if len(req.CommuDate) > 0 {
		subQuery = subQuery.Where("commu_date BETWEEN ? AND ?", req.CommuDate[0], req.CommuDate[1])
	}
	if len(req.Operator) > 0 {
		subQuery = subQuery.Where("operator IN (?)", req.Operator)
	}
	if req.Uid > 0 {
		subQuery = subQuery.Where("uid = ?", req.Uid)
	}
	var total int64
	query := subQuery
	if err := subQuery.Count(&total).Error; err != nil {
		logger.Error(ctx.Request().Context(), "get dsc commu id that qualifies err", zap.String("err", err.Error()))
		return nil, 0, err
	}
	// 导出数据不分页
	if all {
		if err := query.Order("commu_date DESC").Find(&commuList).Error; err != nil {
			logger.Error(ctx.Request().Context(), "get dsc commu list err", zap.String("err", err.Error()))
			return nil, 0, xerrors.New(err.Error(), code.DbError)
		}
	} else {
		if err := query.Order("commu_date DESC").Scopes(database.Paginate(&req.Page, &req.PageSize)).Find(&commuList).Error; err != nil {
			logger.Error(ctx.Request().Context(), "get dsc commu list err", zap.String("err", err.Error()))
			return nil, 0, xerrors.New(err.Error(), code.DbError)
		}
	}
	for _, v := range commuList {
		// 获取相关对话
		msgIds := strings.Split(v.RelevantMsg, ",")
		msgList, _ := dc.GetMsgContentByMsgIds(msgIds)
		dialogueList := make([]*pb.DiscordCommuRecordListResp_DialogueItem, len(msgList))
		for i := range msgList {
			msg := msgList[i]
			if msg.FromUserID == msg.DscUserID {
				dialogueList[i] = &pb.DiscordCommuRecordListResp_DialogueItem{
					Role:    "player",
					Content: msg.Content,
				}
			}
			if msg.FromUserID == msg.BotID {
				dialogueList[i] = &pb.DiscordCommuRecordListResp_DialogueItem{
					Role:    "service",
					Content: msg.Content,
				}
			}
		}
		//查询维护专员
		maintainConfig, err := NewMaintainConfig().GetMaintainConfigDetail(ctx.Request().Context(), v.DscUserID, v.Project)
		if err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				logger.Infof(ctx.Request().Context(), "get maintain config detail list err", zap.String("err", err.Error()))
			} else {
				logger.Error(ctx.Request().Context(), "get maintain config detail list err", zap.String("err", err.Error()))
				return nil, 0, xerrors.New(err.Error(), code.DbError)
			}
		}

		dest = append(dest, &pb.DiscordCommuRecordListResp_DiscordCommuRecord{
			Id:           v.ID,
			CommuDate:    v.CommuDate,
			Project:      v.Project,
			Uid:          v.UID,
			Sid:          v.Sid,
			NickName:     v.NickName,
			PayAll:       v.PayAll,
			Question:     v.Question,
			QuestionType: int32(v.QuestionType),
			HandleStatus: int32(v.HandleStatus),
			Remark:       v.Remark,
			Dialogue:     dialogueList,
			Operator:     v.Operator,
			Maintainer:   maintainConfig.Maintainer,
		})
	}
	return dest, uint32(total), nil
}

func (dc *DiscordCommu) GetMsgContentByMsgIds(msgIds []string) ([]*models.FpDscDmCommu, error) {
	dest := []*models.FpDscDmCommu{}
	if err := dc.db.Model(&models.FpDscDmCommu{}).Where("msg_id IN (?)", msgIds).Find(&dest).Error; err != nil {
		return dest, err
	}
	return dest, nil
}

func (dc *DiscordCommu) GetChannelIdsByProjectUid(project string, uid int64) ([]*pb.DscUserChannelDf, error) {
	var channels []*pb.DscUserChannelDf
	err := dc.db.Model(&models.FpDscUser{}).Table(fmt.Sprintf("%s as fdu", models.FpDscUser{}.TableName())).
		Joins(fmt.Sprintf("INNER JOIN %s fpmc ON fdu.dsc_user_id = fpmc.dsc_user_id AND fdu.project=fpmc.game_project", models.GetFpDscPlayerMaintainConfigTableName())).
		Select("distinct(fdu.priv_channel_id) as priv_channel_id", "fdu.app_id", "fdu.dsc_user_id").
		Where("fpmc.game_project = ? AND fpmc.uid = ? AND fdu.is_deleted = ? AND fdu.priv_channel_id != ''", project, uid, code.DscNoDelete).
		Find(&channels).Error
	if err != nil {
		return channels, err
	}
	return channels, nil
}
