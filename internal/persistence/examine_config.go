package persistence

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/labstack/echo/v4"
	"github.com/spf13/cast"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"ops-ticket-api/internal/code"
	"ops-ticket-api/internal/cst"
	"ops-ticket-api/internal/framework/database"
	"ops-ticket-api/internal/framework/xerrors"
	"ops-ticket-api/models"
	"ops-ticket-api/proto/pb"
	"ops-ticket-api/utils"
	"sort"
	"time"
)

// examineSettings examine配置信息
type examineSettings struct {
	db *gorm.DB
}

// NewDiscordCommu init
func NewExamineSettings() *examineSettings {
	return &examineSettings{
		db: database.Db(),
	}
}

func GetListWithPage(ctx echo.Context, db *gorm.DB, dest interface{}, page, pageSize *uint32, fn func(tx *gorm.DB) (*gorm.DB, string, error)) (int64, error) {
	query, order, err := fn(db)
	if err != nil {
		return 0, err
	}
	var count int64
	if err := query.Count(&count).Error; err != nil {
		logger.Error(ctx.Request().Context(), "get GetListWithPage list count err", zap.String("err", err.Error()))
		return 0, err
	}
	if count == 0 {
		return 0, nil
	}
	if order != "" {
		query = query.Order(order)
	}
	if err := query.Scopes(database.Paginate(page, pageSize)).Find(dest).Error; err != nil {
		return 0, err
	}
	return count, nil
}

func (repo *examineSettings) GetTplList(ctx echo.Context, desc string, page, pageSize *uint32) (int64, []*models.FpExamineTplConfig, error) {
	var dest []*models.FpExamineTplConfig
	count, err := GetListWithPage(ctx,
		repo.db.WithContext(ctx.Request().Context()).Model(&models.FpExamineTplConfig{}),
		&dest, page, pageSize,
		func(tx *gorm.DB) (*gorm.DB, string, error) {
			tx = tx.Model(&models.FpExamineTplConfig{}).Select("*")
			if desc != "" {
				tx = tx.Where("tpl_desc LIKE ?", "%"+desc+"%")
			}
			return tx, "id desc", nil
		})
	if err != nil {
		return 0, nil, err
	}
	return count, dest, err
}

func (repo *examineSettings) TplSave(ctx echo.Context, tplConfig *models.FpExamineTplConfig) (err error) {
	var (
		tx  = repo.db.WithContext(ctx.Request().Context())
		fun = "examineSettings.TplSave -->"
	)

	// check has exist
	var cout int64
	if err = tx.Model(&models.FpExamineTplConfig{}).Where("tpl_desc = ? AND id != ?", tplConfig.TplDesc, tplConfig.ID).Count(&cout).Error; err != nil {
		return err
	}
	if cout > 0 {
		return xerrors.New(code.StatusText(code.RepeatData), code.RepeatData)
	}

	// begin transaction
	tx = tx.Begin()
	defer func() { // rollback
		if err != nil {
			logger.Errorf(ctx.Request().Context(), "%s TplSave rollback. err:%v", fun, err)
			if _rollErr := tx.Rollback().Error; _rollErr != nil {
				logger.Errorf(ctx.Request().Context(), "%s TplSave rollback err:%v. rollErr:%v", fun, err, _rollErr)
			}
		}
	}()
	// examine tpl config
	if tplConfig.ID > 0 {
		if err = tx.Model(&models.FpExamineTplConfig{}).Where("id = ?", tplConfig.ID).
			Updates(tplConfig).Error; err != nil {
			return err
		}
	} else {
		if err = tx.Model(tplConfig).Omit("ModuleDetails").Create(tplConfig).Error; err != nil {
			return err
		}
	}
	// examine tpl config detail
	if len(tplConfig.ModuleDetails) > 0 {
		if err = tx.Where("tpl_id = ?", tplConfig.ID).Delete(&models.FpExamineTplConfigDetail{}).Error; err != nil {
			return err
		}
	}
	for i, _ := range tplConfig.ModuleDetails {
		tplConfig.ModuleDetails[i].TplID = tplConfig.ID
	}
	if err = tx.Model(&models.FpExamineTplConfigDetail{}).CreateInBatches(tplConfig.ModuleDetails, len(tplConfig.ModuleDetails)).Error; err != nil {
		return err
	}
	// add log

	//  add log
	opDetail := &models.FpExamineOperateLog{
		OperationGroup:  pb.OpGroup_OpGroupExamineTpl.String(),
		OperationAction: pb.OpAction_OpActionUpdate.String(),
		BaseID:          fmt.Sprintf("%d", tplConfig.ID),
		UniqueID:        fmt.Sprintf("%d", tplConfig.ID),
		GameProject:     "default",
		BeforeDetail:    "{}",
		AfterDetail:     utils.ToJson(tplConfig),
		CreateTime:      time.Now(),
		Operator:        cast.ToString(ctx.Get(cst.AccountInfoCtx)),
	}
	if err = tx.Model(opDetail).Create(opDetail).Error; err != nil {
		return err
	}

	// commit
	err = tx.Commit().Error
	return err
}

func (repo *examineSettings) TplEnable(ctx echo.Context, param *pb.EnableReq) error {
	tx := repo.db.WithContext(ctx.Request().Context())
	// check
	var detail = &models.FpExamineTplConfig{}
	if err := tx.Model(detail).Where("id = ?", param.GetObjectId()).Last(&detail).Error; err != nil {
		return err
	}
	if detail.ID == 0 {
		return xerrors.New(code.StatusText(code.NotFound), code.NotFound)
	}
	// update
	toStatus := code.ModuleEnable
	if !param.GetEnable() {
		toStatus = code.ModuleDisable
	}
	ups := map[string]any{
		"status":     toStatus,
		"updated_at": time.Now(),
		"operator":   ctx.Get(cst.AccountInfoCtx).(string),
	}
	if err := tx.Model(detail).Where("id = ?", param.GetObjectId()).Updates(ups).Error; err != nil {
		return err
	}

	//  add log
	opDetail := &models.FpExamineOperateLog{
		OperationGroup:  pb.OpGroup_OpGroupExamineTpl.String(),
		OperationAction: pb.OpAction_OpActionStatus.String(),
		BaseID:          fmt.Sprintf("%d", param.GetObjectId()),
		UniqueID:        fmt.Sprintf("%d", param.GetObjectId()),
		GameProject:     "default",
		BeforeDetail:    utils.ToJson(detail),
		AfterDetail:     utils.ToJson(ups),
		CreateTime:      time.Now(),
		Operator:        cast.ToString(ctx.Get(cst.AccountInfoCtx)),
	}
	if err := tx.Model(&models.FpDscOperateLog{}).Create(opDetail).Error; err != nil {
		logger.Errorf(ctx.Request().Context(), "TplEnable create operate log err. id:%d. err:%v", param.ObjectId, err)
	}

	// return
	return nil
}

func (repo *examineSettings) TplDel(ctx echo.Context, tplId uint64) error {
	tx := repo.db.WithContext(ctx.Request().Context())
	// check
	var detail = &models.FpExamineTplConfig{}
	if err := tx.Model(detail).Where("id = ?", tplId).Last(&detail).Error; err != nil {
		return err
	}
	if detail.ID == 0 {
		return xerrors.New(code.StatusText(code.NotFound), code.NotFound)
	}
	if detail.CanDelete != code.ModuleEnable { // check has been used
		return xerrors.New(code.StatusText(code.ExamineTplForbidden), code.ExamineTplForbidden)
	}
	// del
	if err := tx.Model(detail).Where("id = ?", tplId).Delete(&models.FpExamineTplConfigDetail{}).Error; err != nil {
		return err
	}
	//  add log
	opDetail := &models.FpExamineOperateLog{
		OperationGroup:  pb.OpGroup_OpGroupExamineTpl.String(),
		OperationAction: pb.OpAction_OpActionDelete.String(),
		BaseID:          fmt.Sprintf("%d", tplId),
		UniqueID:        fmt.Sprintf("%d", tplId),
		GameProject:     "default",
		BeforeDetail:    utils.ToJson(detail),
		AfterDetail:     "{}",
		CreateTime:      time.Now(),
		Operator:        cast.ToString(ctx.Get(cst.AccountInfoCtx)),
	}
	if err := tx.Model(&models.FpDscOperateLog{}).Create(opDetail).Error; err != nil {
		logger.Errorf(ctx.Request().Context(), "TplDel create operate log err. id:%d. err:%v", tplId, err)
	}
	return nil
}

func (repo *examineSettings) TplOpts(ctx echo.Context) ([]*pb.ExamineTplOptsResp_ExamineTplOpts, error) {
	var list []*models.FpExamineTplConfig
	err := repo.db.WithContext(ctx.Request().Context()).Model(&models.FpExamineTplConfig{}).
		Select("id, tpl_desc").Where("status = ?", code.ModuleEnable).Find(&list).Error
	if err != nil {
		return nil, err
	}
	var converts = make([]*pb.ExamineTplOptsResp_ExamineTplOpts, 0, len(list))
	for _, row := range list {
		converts = append(converts, &pb.ExamineTplOptsResp_ExamineTplOpts{TplId: row.ID, TplDsc: row.TplDesc})
	}
	return converts, nil
}

func (repo *examineSettings) GetExamineDetailByTplId(ctx echo.Context, tplId uint64) (*models.FpExamineTplConfig, error) {
	var detail models.FpExamineTplConfig
	err := repo.db.WithContext(ctx.Request().Context()).Model(detail).Preload("ModuleDetails").
		Where("id = ?", tplId).Last(&detail).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, err
	} else if errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, nil
	}
	if len(detail.ModuleDetails) > 0 {
		sort.SliceStable(detail.ModuleDetails, func(i, j int) bool {
			return detail.ModuleDetails[i].FieldSort < detail.ModuleDetails[j].FieldSort
		})
	}
	return &detail, nil
}

func (repo *examineSettings) TaskSave(ctx echo.Context, task *models.FpExamineTask) error {
	var (
		tx    = repo.db.WithContext(ctx.Request().Context())
		count int64
	)
	if err := tx.Model(&models.FpExamineTask{}).Where("task_name = ? AND id != ?", task.TaskName, task.ID).Count(&count).Error; err != nil {
		return err
	}
	if count > 0 {
		return xerrors.New(code.StatusText(code.RepeatData), code.RepeatData)
	}
	if err := tx.Model(task).Create(task).Error; err != nil {
		return err
	}

	if err := tx.Model(&models.FpExamineTplConfig{}).Where("id = ?", task.TplID).
		Updates(map[string]any{"can_delete": code.ModuleDisable, "updated_at": time.Now()}).Error; err != nil {
		return err
	}

	return nil
}

func (repo *examineSettings) TaskUpdates(ctx context.Context, where, ups map[string]interface{}) error {
	tx := repo.db.WithContext(ctx)
	if err := tx.Model(&models.FpExamineTask{}).Where(where).Updates(ups).Error; err != nil {
		return err
	}
	return nil
}

func (repo *examineSettings) GetTaskDetail(ctx echo.Context, taskId uint64) (*models.FpExamineTask, *models.ExamineTaskFilterDf, error) {
	var (
		task models.FpExamineTask
		df   models.ExamineTaskFilterDf
	)
	tx := repo.db.WithContext(ctx.Request().Context())
	if err := tx.Model(&models.FpExamineTask{}).Where("id = ?", taskId).Last(&task).Error; err != nil {
		return nil, nil, err
	}
	if err := json.Unmarshal([]byte(task.Filters), &df); err != nil {
		return nil, nil, err
	}
	return &task, &df, nil
}

func (repo *examineSettings) GetTaskList(ctx echo.Context, desc string, page, pageSize *uint32) (int64, []*models.FpExamineTask, error) {
	var dest []*models.FpExamineTask
	count, err := GetListWithPage(ctx,
		repo.db.WithContext(ctx.Request().Context()).Model(&models.FpExamineTask{}),
		&dest, page, pageSize,
		func(tx *gorm.DB) (*gorm.DB, string, error) {
			tx = tx.Model(&models.FpExamineTask{}).Select("*")
			if desc != "" {
				tx = tx.Where("task_name LIKE ?", "%"+desc+"%")
			}
			return tx, "id desc", nil
		})
	if err != nil {
		return 0, nil, err
	}
	return count, dest, err
}

func (repo *examineSettings) GetUnfinishedToQue(ctx echo.Context) ([]*models.FpExamineTask, error) {
	var dst []*models.FpExamineTask
	var now = time.Now().Local()
	err := repo.db.WithContext(ctx.Request().Context()).Model(&models.FpExamineTask{}).
		Where("created_at >=? AND created_at <= ? AND task_status in ?", now.Add(0-time.Hour*24), now, []int{int(pb.ExamineTaskStateDf_ExamineTaskStateDfInit), int(pb.ExamineTaskStateDf_ExamineTaskStateDfDoing)}).
		Find(&dst).Error
	logger.Infof(ctx.Request().Context(), "getDoingExamineTaskToQue result. count:%d. err:%v", len(dst), err)
	return dst, err
}
