// Copyright 2024 funplus Authors. All Rights Reserved.
// @Description: 超时回复模版配置
// @Author: zyh
// @Date: 2024/12/04 3:34 PM

package persistence

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/labstack/echo/v4"
	"github.com/spf13/cast"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"ops-ticket-api/internal/code"
	"ops-ticket-api/internal/cst"
	"ops-ticket-api/internal/framework/database"
	"ops-ticket-api/internal/framework/xerrors"
	"ops-ticket-api/models"
	"ops-ticket-api/proto/pb"
	"ops-ticket-api/utils"
	"strings"
	"time"
)

// OvertimeTpl 超时回复模板
type OvertimeTpl struct {
	db *gorm.DB
}

// NewOvertimeTpl init
func NewOvertimeTpl() *OvertimeTpl {
	return &OvertimeTpl{
		db: database.Db(),
	}
}

func (o *OvertimeTpl) OvertimeTplCheck(ctx echo.Context, tplName string, tplId uint64) error {
	var has int64

	// 构造查询条件
	query := o.db.Model(&models.FpOpsOvertimeTpl{}).Where("tpl_name = ?", tplName)
	if tplId != 0 {
		query = query.Where("id != ?", tplId)
	}

	// 查询重复记录
	err := query.Count(&has).Error
	if err != nil {
		logger.Error(ctx.Request().Context(), "error querying existing tpl", zap.String("err", err.Error()))
		return xerrors.New(err.Error(), code.DbError)
	}

	// 如果有重复记录，返回错误
	if has > 0 {
		return xerrors.New("保存失败！模板名称重复", code.InvalidParams)
	}

	return nil
}

func (o *OvertimeTpl) GetOvertimeInfoIfExist(tplId uint64) (tplInfo *models.FpOpsOvertimeTpl, err error) {
	dest := new(models.FpOpsOvertimeTpl)
	err = o.db.Model(&models.FpOpsOvertimeTpl{}).Where("id = ?", tplId).First(dest).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, xerrors.New(err.Error(), code.DbError)
	} else if errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, xerrors.New("模板不存在", code.InvalidParams)
	}
	return dest, nil
}

func (o *OvertimeTpl) OvertimeTplAdd(ctx echo.Context, tplInfo *models.FpOpsOvertimeTpl) error {
	if err := o.db.Model(&models.FpOpsOvertimeTpl{}).Create(tplInfo).Error; err != nil {
		logger.Error(ctx.Request().Context(), "error create overtime tpl", zap.String("err", err.Error()))
		return xerrors.New(err.Error(), code.DbError)
	}
	return nil
}

// OvertimeTplEdit 模版更新
func (o *OvertimeTpl) OvertimeTplEdit(ctx context.Context, tplId uint64, tpl *models.FpOpsOvertimeTpl) (err error) {
	tx := o.db.WithContext(ctx).Begin()
	defer tx.Rollback()
	if err = tx.Where("tpl_id=?", tplId).Delete(&models.FpOpsOvertimeTplGame{}).Error; err != nil {
		return xerrors.New(err.Error(), code.DbError)
	}
	if err = tx.Model(models.FpOpsOvertimeTplGame{}).Create(tpl.GameProject).Error; err != nil {
		return xerrors.New(err.Error(), code.DbError)
	}
	tpl.GameProject = nil
	if err = tx.Where("id=?", tplId).Updates(tpl).Error; err != nil {
		errMsg := err.Error()
		logger.Error(ctx, "overtime tpl save err", zap.Uint64("tplId", tplId), zap.String("err", errMsg))
		if strings.Contains(errMsg, "Error 1062: Duplicate entry") {
			errMsg = "EventDupCatAddLog"
		}
		return xerrors.New(errMsg, code.DbError)
	}
	// 提交事务
	if err = tx.Commit().Error; err != nil {
		logger.Error(ctx, "transaction commit err", zap.String("err", err.Error()))
		return xerrors.New(err.Error(), code.DbError)
	}
	return
}

func (o *OvertimeTpl) OvertimeTplDel(ctx echo.Context, req *pb.OverTimeTplDelReq) (err error) {
	tx := o.db.WithContext(ctx.Request().Context()).Begin()
	defer tx.Rollback()

	err = tx.Model(&models.FpOpsOvertimeTpl{}).Where("id = ?", req.Id).Delete(&models.FpOpsOvertimeTpl{}).Error
	if err != nil {
		logger.Error(ctx.Request().Context(), "error delete overtime tpl", zap.String("err", err.Error()))
		return xerrors.New(err.Error(), code.DbError)
	}
	if err = tx.Model(&models.FpOpsOvertimeTplGame{}).Where("tpl_id = ?", req.Id).Delete(&models.FpOpsOvertimeTplGame{}).Error; err != nil {
		logger.Error(ctx.Request().Context(), "error delete module game", zap.String("err", err.Error()))
		return xerrors.New(err.Error(), code.DbError)
	}
	// 提交事务
	if err = tx.Commit().Error; err != nil {
		logger.Error(ctx.Request().Context(), "transaction commit err", zap.String("err", err.Error()))
		return xerrors.New(err.Error(), code.DbError)
	}
	return nil
}

func (o *OvertimeTpl) OvertimeTplList(ctx echo.Context, req *pb.OverTimeTplListReq) ([]*pb.OverTimeTplListResp_TplInfo, uint32, error) {
	dest := make([]*pb.OverTimeTplListResp_TplInfo, 0)
	// 首先查出符合条件的模板id
	tplList := []*models.FpOpsOvertimeTplList{}
	field := "DISTINCT m.id"
	subQuery := o.db.Table(fmt.Sprintf("%s AS m", models.GetFpOpsOvertimeTplTableName())).
		Joins(fmt.Sprintf("INNER JOIN %s AS g ON m.id = g.tpl_id", models.GetFpOpsOvertimeTplGameTableName()), "")
	if req.TplName != "" {
		subQuery = subQuery.Where("m.tpl_name LIKE ?", "%"+req.TplName+"%")
	}
	if len(req.GameProject) > 0 {
		subQuery = subQuery.Where("g.game_project IN (?)", req.GameProject)
	}
	moduleIds := []uint64{}
	if err := subQuery.Select(field).Find(&moduleIds).Error; err != nil {
		logger.Error(ctx.Request().Context(), "get tpl id that qualifies err", zap.String("err", err.Error()))
		return nil, 0, err
	}
	count := len(moduleIds)
	// 然后根据这些模板id获取最终的数据
	fields := "m.id, m.tpl_name, m.operator, m.enable, m.update_time, GROUP_CONCAT(g.game_project SEPARATOR ',') AS game_project,m.overtime,m.content"
	query := o.db.Table(fmt.Sprintf("%s AS m", models.GetFpOpsOvertimeTplTableName())).
		Joins(fmt.Sprintf("INNER JOIN %s AS g on m.id = g.tpl_id", models.GetFpOpsOvertimeTplGameTableName()), "")
	query.Select(fields)
	query.Where("m.id IN (?)", moduleIds)
	if err := query.Group("m.id").Order("m.update_time DESC").Scopes(database.Paginate(&req.Page, &req.PageSize)).Find(&tplList).Error; err != nil {
		logger.Error(ctx.Request().Context(), "get tpl list err", zap.String("err", err.Error()))
		return nil, 0, xerrors.New(err.Error(), code.DbError)
	}
	for _, v := range tplList {
		updateTime := time.Unix(int64(v.UpdatedTime), 0).Format("2006-01-02 15:04:05")
		// 获取content 的map
		var contentMap map[string]string
		if err := json.Unmarshal([]byte(v.Content), &contentMap); err != nil {
			logger.Error(ctx.Request().Context(), "overtime tpl content unmarshal err", zap.Uint64("tplId", v.ID), zap.String("err", err.Error()))
			return nil, 0, err
		}
		dest = append(dest, &pb.OverTimeTplListResp_TplInfo{
			Id:          v.ID,
			TplName:     v.TplName,
			GameProject: strings.Split(v.GameProject, ","),
			Operator:    v.Operator,
			Enable:      uint32(v.Enable),
			UpdateTime:  updateTime,
			RemindTime:  uint32(v.Overtime),
			TplContent:  contentMap,
		})
	}
	return dest, cast.ToUint32(count), nil
}

func (o *OvertimeTpl) OvertimeTplEnable(ctx echo.Context, req *pb.OverTimeTplEnableReq) (err error) {
	current := utils.NowTimestamp()
	// 1. 请求和本身的状态不能一样
	detail := models.FpOpsOvertimeTplGame{}
	err = o.db.Model(&models.FpOpsOvertimeTplGame{}).
		Where("tpl_id = ?", req.ObjectId).
		First(&detail).Error
	if err != nil {
		return err
	}
	if detail.Enable == uint8(req.Enable) {
		return xerrors.New("参数错误", code.InvalidParams)
	}
	// 2. 当用户发出请求为启用时，一个游戏仅能启用一个模版
	if req.Enable == uint32(code.StatusFalse) {
		// 3. 当用户发出请求为禁用时，被引用的模版不能被禁用
		query := map[string]interface{}{
			"overtime_reply_id": req.ObjectId,
		}
		exist, err := NewCat().GetCatExistByTplId(ctx, query)
		if err != nil {
			return err
		}
		if exist {
			return errors.New("该模板正在使用，无法禁用")
		}
	}
	// 4. 更新状态
	tx := o.db.WithContext(ctx.Request().Context()).Begin()
	defer tx.Rollback()
	var overtimeInfo = new(models.FpOpsOvertimeTpl)
	var overtimeGameInfo = new(models.FpOpsOvertimeTplGame)
	var operator = cast.ToString(ctx.Get(cst.AccountInfoCtx))
	// 更新主表
	err = o.db.Model(overtimeInfo).Where("id = ?", req.ObjectId).
		Updates(map[string]interface{}{
			"enable":      req.Enable,
			"operator":    operator,
			"update_time": current,
		}).Error
	if err != nil {
		return
	}
	// 更新game表
	err = o.db.Model(overtimeGameInfo).Where("tpl_id = ?", req.ObjectId).
		Updates(map[string]interface{}{
			"enable":      req.Enable,
			"update_time": current,
		}).Error
	if err != nil {
		return
	}
	// 提交事务
	if err = tx.Commit().Error; err != nil {
		logger.Error(ctx.Request().Context(), "transaction commit err", zap.String("err", err.Error()))
		return xerrors.New(err.Error(), code.DbError)
	}
	return nil
}

// OvertimeTplOpts 模版下拉选
func (o *OvertimeTpl) OvertimeTplOpts(ctx context.Context, gameProject string) (*pb.OverTimeTplOptsRes, error) {
	var dest []models.FpOpsOvertimeTplList
	fields := "m.id, m.tpl_name"

	// 构建查询
	query := o.db.Table(fmt.Sprintf("%s AS m", models.GetFpOpsOvertimeTplTableName())).
		Joins(fmt.Sprintf("INNER JOIN %s AS g ON m.id = g.tpl_id", models.GetFpOpsOvertimeTplGameTableName())).
		Select(fields).
		Where("g.game_project = ? AND g.enable = ?", gameProject, code.StatusTrue)

	// 查询数据
	if err := query.Find(&dest).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return &pb.OverTimeTplOptsRes{List: []*pb.OverTimeTplOptsRes_Opts{}}, nil
		}
		logger.Error(ctx, "get overtime tpl opts err", zap.String("err", err.Error()))
		return nil, xerrors.New(err.Error(), code.DbError)
	}

	// 构建返回数据
	res := &pb.OverTimeTplOptsRes{
		List: make([]*pb.OverTimeTplOptsRes_Opts, len(dest)),
	}
	for i, tpl := range dest {
		res.List[i] = &pb.OverTimeTplOptsRes_Opts{
			TplId:   uint32(tpl.ID),
			TplName: tpl.TplName,
		}
	}

	return res, nil
}

// GetOvertimeByTplId 根据模版id查询超时时间和文案
func (o *OvertimeTpl) GetOvertimeByTplId(ctx context.Context, tplId uint64) (uint32, map[string]string, error) {
	var contentMap map[string]string
	if tplId == 0 {
		return 0, contentMap, nil
	}
	record := struct {
		OverTime uint32 `gorm:"column:overtime"`
		Content  string `gorm:"column:content"`
	}{}
	// 查询超时时间和文案
	err := o.db.Model(&models.FpOpsOvertimeTpl{}).
		Select("overtime, content").
		Where("id = ?", tplId).
		First(&record).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return 0, contentMap, nil
		}
		logger.Error(ctx, "get overtime by tplId err", zap.String("err", err.Error()))
		return 0, contentMap, xerrors.New(err.Error(), code.DbError)
	}

	if err := json.Unmarshal([]byte(record.Content), &contentMap); err != nil {
		logger.Error(ctx, "unmarshal content err", zap.String("err", err.Error()))
		return 0, contentMap, err
	}
	return record.OverTime, contentMap, nil
}
