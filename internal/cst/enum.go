// Copyright 2021 funplus Authors. All Rights Reserved.
// @Description: 枚举
// @Author: Darcy
// @Date: 2021/10/28 12:00 PM

package cst

import "ops-ticket-api/proto/pb"

var (
	// EnumList enum
	EnumList = map[string]map[int32]string{
		//"Status":         pb.Status_name,   // 工单状态
		"ConversionNode":            pb.TkStage_name,                   // 工单状态 - 流转节点
		"SceneType":                 pb.SceneType_name,                 // 工单来源： SceneType
		"Csi":                       pb.CsiLevel_name,                  // 评星
		"Nps":                       pb.NpsLevel_name,                  // NPS评分
		"PoolSort":                  pb.TkPoolSort_name,                // pool 查询结果 - 下拉排序列表
		"DiscordServiceReplyStatus": pb.DiscordServiceReplyStatus_name, // discord上客服回复(玩家消息)状态
		"PlayerGender":              pb.PlayerGender_name,              // 玩家性别
		"PlayerEducationLevel":      pb.PlayerEducationLevel_name,      // 玩家教育程度
		"PlayerMarriageState":       pb.PlayerMarriageState_name,       // 玩家婚姻状况
		"PlayerFertilityState":      pb.PlayerFertilityState_name,      // 玩家生育状况
		"PlayerVipState":            pb.PlayerVipState_name,            // 玩家VIP状态
		"QuestionType":              pb.QuestionType_name,              // 沟通问题类型
		"QuestionHandleStatus":      pb.QuestionHandleStatus_name,      // 沟通问题处理状态
		"TicketSystemTag":           pb.TicketSystemTag_name,           // 工单系统标签
		"AIPolishLabel":             pb.AIPolishLabel_name,             // ai润色
		"TagType":                   pb.FilterTagEnum_name,             // 标签类型
		"SurveyEffective":           pb.SurveyEffective_name,           // 问卷有效期
		"SurveyPushCycle":           pb.SurveyPushCycle_name,           // 问卷推送周期
		"SurveyQstType":             pb.SurveyQstType_name,             // 问卷 评价类型 产品/服务
		"SurveyStatType":            pb.SurveyStatType_name,            // 问卷 报表维度
		"TicketSvipState":           pb.SVIP_name,                      // 工单系统SVIP状态
		"SearchType":                pb.SearchTypeEnum_name,            // 查询用户类型
		"UserType":                  pb.UserTypeEnum_name,              // 用户类型
		"TicketSolveType":           pb.SolveType_name,                 // 工单解决类型
		"TrainingTaskStatus":        pb.TrainingTaskStatus_name,        // 训练结果类型

		"ExamineFieldType":   pb.ExamineFieldTpDf_name,     // 质检-打分表-字段类型
		"ExamineTaskGroup":   pb.ExamineTaskGroupDf_name,   // 质检-任务配置-抽检库
		"ExamineTaskState":   pb.ExamineTaskStateDf_name,   // 质检-任务配置-任务状态
		"ExamineState":       pb.ExamineStateDf_name,       // 质检-质检单-当前状态
		"ExamineFinalResult": pb.ExamineFinalResultDf_name, // 质检-质检单-本次质检结果

		"DcPoolSort": pb.DcPoolSort_name,
	}
	EnumMapList = map[string]map[string]string{
		"ExamineFinalReason": map[string]string{
			"ReasonKnowledgeLack":   "ReasonKnowledgeLack",   // 知识-游戏只是缺乏
			"ReasonProcessLack":     "ReasonProcessLack",     // 知识-流程掌握不够
			"ReasonResearchLack":    "ReasonResearchLack",    // 技能-对case的调查研究不够充分
			"ReasonCommuSkillsLack": "ReasonCommuSkillsLack", // 技能-语言沟通技巧不足
			"ReasonToolLack":        "ReasonToolLack",        // 技能-工具不熟
			"ReasonServiceLack":     "ReasonServiceLack",     // 行为-服务意识不够
			"ReasonAttitudeLack":    "ReasonAttitudeLack",    // 行为-态度敷衍
			"ReasonInternalLimit":   "ReasonInternalLimit",   // 内部-知识库/流程/工具限制
			"ReasonExternalLimit":   "ReasonExternalLimit",   // 外部-玩家想法/语言不通
		},
		"ZoneVipLevel": {
			"R1": "1",
			"R2": "2",
			"R3": "3",
			"R4": "4",
			"R5": "5",
			"R6": "6",
			"R7": "7",
		},
	}
	EnumMapConfList = map[string]map[string]string{
		"DownloadUrl": { // 下载模板数据
			"TagLabDemo":       "https://kg-web-cdn.kingsgroupgames.com/prod/cs-api/csexcel/650d53e9ce0dc8a5899992390b56f8ed.xlsx", // 标签导入模板
			"DataPlatItemDemo": "https://kg-web-cdn.kingsgroupgames.com/prod/cs-api/csexcel/71649fe223bb5a44a29449c536c94f21.xlsx", // 精分数据-道具导入模板
			"TicketTrainDemo":  "https://kg-web-cdn.kingsgroupgames.com/prod/cs-api/csexcel/b5fe9887ad82219dd7eccfd2d995d6eb.xlsx", // 工单知识库批量导入模版
		},
	}
)
