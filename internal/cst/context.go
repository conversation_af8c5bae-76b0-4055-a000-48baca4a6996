// Copyright 2021 funplus Authors. All Rights Reserved.
// @Description:
// @Author: Darcy
// @Date: 2022/8/22 19:00

package cst

const (
	// AccountClaimsCtx ctx account list
	AccountClaimsCtx = "account:claims:ctx:"
	// AccountInfoCtx ctx account info
	AccountInfoCtx = "account:info:ctx:"
	// AccountWorkgroupCtx ctx account work group
	AccountWorkgroupCtx = "account:workgroup:ctx:"
	// AccountNameCtx ctx account name
	AccountNameCtx = "account:name:ctx:"
	LocalLanguage  = "local:language:ctx:"

	// AccountIsAdminCtx admin ctx
	AccountIsAdminCtx = "account:is_admin:ctx:"
	PlayerClaimsCtx   = "player:claim:ctx:"

	TicketReplyTplCtx = "ticket:system:reply:tpl"

	AutomateModuleTextBkKey = "bk_show"
)
