package proxy

import (
	"bytes"
	"compress/gzip"
	"context"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net"
	"net/http"
	"net/http/httputil"
	"net/url"
	"strconv"
	"strings"
	"time"

	"gitlab-ee.funplus.io/ops-tools/compkg/elog"

	"github.com/spf13/viper"
)

// ProxyHTTP http proxy
type ProxyHTTP struct {
	// BackendHost the backend host
	BackendHost string

	// Path path info
	Path string

	// header
	header map[string][]string
}

// NewSingleHostProxy  init
func NewSingleHostProxy(host, path string, header map[string][]string, token string) *ProxyHTTP {
	header["gateway-proxy-version"] = []string{viper.GetString("app.version")}
	if token != "" {
		header["Authorization"] = []string{"Bearer " + token}
	}
	return &ProxyHTTP{
		BackendHost: host,
		Path:        path,
		header:      header,
	}
}

// SetHeader set header
func (p *ProxyHTTP) SetHeader(key string, value []string) {
	p.header = map[string][]string{
		key: value,
	}
}

// Director proxy redirect
func (p *ProxyHTTP) Director(target *url.URL) func(req *http.Request) {
	targetQuery := target.RawQuery
	return func(req *http.Request) {
		req.URL.Scheme = "http"
		if req.TLS != nil {
			req.URL.Scheme = "https"
		}
		req.URL.Host = target.Host
		req.URL.Path = target.Path
		req.Host = target.Host
		if target.RawPath != "" && target.RawPath == target.Path {
			req.URL.RawPath = target.RawPath
		}

		if targetQuery == "" || req.URL.RawQuery == "" {
			req.URL.RawQuery = targetQuery + req.URL.RawQuery
		} else if req.URL.RawQuery != targetQuery {
			req.URL.RawQuery = targetQuery + "&" + req.URL.RawQuery
		}
		// header
		for k, v := range p.header {
			addHeader(req.Header, k, v, false)
		}
		req.Header.Set("X-Forwarded-Host", req.Header.Get("Host"))
	}
}

// ModifyResponse modify resp
func (p *ProxyHTTP) ModifyResponse() func(resp *http.Response) error {
	return func(resp *http.Response) error {
		if strings.Contains(resp.Header.Get("Connection"), "Upgrade") {
			return nil
		}
		var payload []byte
		var readErr error
		if strings.Contains(resp.Header.Get("Content-Encoding"), "gzip") {
			gr, err := gzip.NewReader(resp.Body)
			if err != nil {
				elog.Errorf("gzip.NewReader_error:%v", err)
				return nil
			}
			payload, readErr = ioutil.ReadAll(gr)
			resp.Header.Del("Content-Encoding")
		} else {
			payload, readErr = ioutil.ReadAll(resp.Body)
		}
		if readErr != nil {
			elog.Warnf("ioutil.ReadAll_error:%v", readErr)
			return readErr
		}
		newPayload := payload
		resp.Body = ioutil.NopCloser(bytes.NewBuffer(newPayload))
		resp.ContentLength = int64(len(newPayload))
		resp.Header.Set("Content-Length", strconv.FormatInt(int64(len(newPayload)), 10))
		removeDuplicateCORSHeader(resp.Header, resp.Header)
		return nil
	}
}

// Transport config
func (p *ProxyHTTP) Transport() http.RoundTripper {
	return &http.Transport{
		DialContext: (&net.Dialer{
			Timeout:   30 * time.Second,
			KeepAlive: 30 * time.Second,
			DualStack: true,
		}).DialContext,
		ForceAttemptHTTP2:     true,
		MaxIdleConns:          100,
		IdleConnTimeout:       90 * time.Second,
		TLSHandshakeTimeout:   10 * time.Second,
		ExpectContinueTimeout: 1 * time.Second,
	}
}

// ErrorHandler err
func (p *ProxyHTTP) ErrorHandler() func(w http.ResponseWriter, req *http.Request, err error) {
	return func(w http.ResponseWriter, req *http.Request, err error) {
		if err.Error() == "context canceled" {
			elog.Warnf("module_proxy_errorhandler:%v", err)
		} else {
			elog.Errorf("module_proxy_errorhandler:%v", err)
		}
		resp := struct {
			Code    int         `json:"status"`
			Message string      `json:"message"`
			Data    interface{} `json:"data"`
		}{
			Code:    http.StatusGatewayTimeout,
			Message: fmt.Sprint(err),
		}
		w.Header().Set("Content-Type", "application/json")
		response, _ := json.Marshal(resp)
		http.Error(w, string(response), http.StatusGatewayTimeout)
		return
	}
}

// ServeRequest request proxy
func (p *ProxyHTTP) ServeRequest(ctx context.Context, req *http.Request, w http.ResponseWriter) {
	target := new(url.URL)
	*target = *req.URL

	target.Host = p.BackendHost
	target.Path = p.Path

	proxy := &httputil.ReverseProxy{
		Director:       p.Director(target),
		ModifyResponse: p.ModifyResponse(),
		Transport:      p.Transport(),
		ErrorHandler:   p.ErrorHandler(),
	}
	proxy.ServeHTTP(w, req)
}
