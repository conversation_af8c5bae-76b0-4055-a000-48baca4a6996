package proxy

import "net/http"

var corsHeaders = []string{
	"Access-Control-Allow-Origin",
	"Access-Control-Expose-Headers",
	"Access-Control-Max-Age",
	"Access-Control-Allow-Credentials",
	"Access-Control-Allow-Methods",
	"Access-Control-Allow-Headers",
	"Vary",
}

func removeDuplicateCORSHeader(dst, src http.Header) {
	for _, v := range corsHeaders {
		keyName := http.CanonicalHeaderKey(v)
		if val := dst.Get(keyName); val != "" {
			src.Del(keyName)
		}
	}
}

func copyHeader(dst, src http.Header, ignoreCanonical bool) {
	removeDuplicateCORSHeader(dst, src)
	for k, vv := range src {
		if ignoreCanonical {
			dst[k] = append(dst[k], vv...)
			continue
		}
		for _, v := range vv {
			dst.Add(k, v)
		}
	}
}

// setHeader set header
func setHeader(h http.Header, key string, value string, ignoreCanonical bool) {
	if ignoreCanonical {
		h[key] = []string{value}
	} else {
		h.Set(key, value)
	}
}

// AddHeader add header
func addHeader(h http.Header, key string, value []string, ignoreCanonical bool) {
	if ignoreCanonical {
		h[key] = append(h[key], value...)
	} else {
		for _, v := range value {
			h.Add(key, v)
		}
	}
}

func cloneHeader(h http.Header) http.Header {
	h2 := make(http.Header, len(h))
	for k, vv := range h {
		vv2 := make([]string, len(vv))
		copy(vv2, vv)
		h2[k] = vv2
	}
	return h2
}
