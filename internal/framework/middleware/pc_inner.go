// Copyright 2021 funplus Authors. All Rights Reserved.
// @Author: <PERSON><PERSON>
// @Date: 2025/4/7 18:56
package middleware

import (
	"bytes"
	"context"
	"errors"
	"github.com/labstack/echo/v4"
	"github.com/spf13/viper"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"gitlab-ee.funplus.io/ops-tools/compkg/sign"
	"io"
)

func PcInnerSign() echo.MiddlewareFunc {
	return func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			body, err := io.ReadAll(c.Request().Body)
			if err != nil {
				return errors.New("签名错误")
			}
			//auth = xxx
			headerSign, ok := c.Request().Header["Ops-Sign"]
			if !ok {
				return errors.New("签名错误")
			}
			if !pcCheckSign(c.Request().Context(), body, headerSign[0]) {
				return errors.New("签名错误")
			}
			//io 流重新赋值
			c.Request().Body = io.NopCloser(bytes.NewBuffer(body))
			return next(c)
		}
	}
}

func pcCheckSign(ctx context.Context, body []byte, headerSign string) bool {
	scrt := viper.GetString("auth.pc_salt")
	if scrt == "" {
		logger.Errorf(ctx, "middleware .checkUserSign curl get secret empty.  body:%s", string(body))
		return false
	}
	//params := struct {
	//	Timestamp int64 `json:"timestamp"`
	//}{}
	//err := json.Unmarshal(body, &params)
	//if err != nil {
	//	return false
	//}
	mysign := sign.Base64WithSha256(string(body), scrt)

	if mysign != headerSign {
		logger.Errorf(ctx, "middleware .checkUserSign sign dismatch.  body:%s. _localSign:%s. _sign:%s", string(body), mysign, headerSign)
		return false
	}
	return true
}
