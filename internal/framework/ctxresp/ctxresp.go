// Copyright 2020 funplus Authors. All Rights Reserved.
// @Description: context response struct
// @Author: darcy

package ctxresp

import (
	"net/http"
	"ops-ticket-api/internal/framework/lang"
	"ops-ticket-api/internal/framework/xerrors"

	"github.com/labstack/echo/v4"
	"gitlab-ee.funplus.io/ops-tools/compkg/xecho"
)

type (
	// Resp resp
	Resp struct {
		Code    int         `json:"code"`
		Message string      `json:"msg"`
		Data    interface{} `json:"data"`
	}
)

const (
	keyFormatType = "_keyFormatType"
	FormatRaw     = "raw"
	FormatHtml    = "html"
	FormatJson    = "json"
	FormatJsonp   = "jsonp"
	FormatXml     = "xml"
)

// IsAjax ajax
func IsAjax(c echo.Context) bool {
	h := c.Request().Header.Get("X-Requested-With")
	return h == "XMLHttpRequest"
}

// GetFormat format
func GetFormat(c echo.Context) string {
	var t = ""
	ret := c.Get(keyFormatType)
	if ret != nil {
		t = ret.(string)
	}
	return t
}

// SetFormat format
func SetFormat(c echo.Context, t string) {
	c.Set(keyFormatType, t)
}

// Out out
func Out(c echo.Context, data interface{}) error {
	out := &Resp{
		Message: "success",
		Data:    data,
	}
	return c.JSON(http.StatusOK, out)
}

// JsonPb out
//func JsonPb(c echo.Context, data proto.Message) error {
//	dataAny, _ := anypb.New(data)
//	out := &pb.Response{
//		Code: 0,
//		Msg:  "success",
//		Data: dataAny,
//	}
//	pj := protojson.MarshalOptions{UseProtoNames: true, UseEnumNumbers: true, EmitUnpopulated: true}
//outData, _ := pj.Marshal(out)
//return c.JSONBlob(http.StatusOK, outData)
//}

// ErrorData error
func ErrorData(c echo.Context, err error, data interface{}) error {
	xe, ok := err.(xerrors.Error)
	if !ok {
		xe = xerrors.New(err)
	}
	out := &Resp{
		Code:    xe.Code(),
		Message: xe.Error(),
		Data:    data,
	}
	err2 := c.JSON(http.StatusOK, out)
	if err2 != nil {
		c.Logger().Error(err2)
	}
	return nil
}

// Error error
func Error(c echo.Context, err error) error {
	xe, ok := err.(xerrors.Error)
	if !ok {
		xe = xerrors.New(err)
	}
	//out := &Resp{
	//	Code:    xe.Code(),
	//	Message: lang.FormatText(c, xe.Error()),
	//}
	out := &Resp{
		Code:    xe.Code(),
		Message: xe.Error(),
	}
	err2 := c.JSON(http.StatusOK, out)
	if err2 != nil {
		c.Logger().Error(err2)
	}
	return nil
}

// Render Render
func Render(c echo.Context, name string, data interface{}) error {
	return c.Render(http.StatusOK, name, data)
}

// ErrorHandler ErrorHandler
func ErrorHandler(err error, c echo.Context) {
	if c.Path() == "/favicon.ico" {
		return
	}
	xe, ok := err.(xerrors.Error)
	if !ok {
		xe = xerrors.New(err)
	}
	// html格式 并且非AJAX请求
	if GetFormat(c) == FormatHtml && !IsAjax(c) {
		err2 := Render(c, xecho.ErrorTemplate, xecho.Map{"err": xe})
		if err2 != nil {
			c.Logger().Error(err2)
		}
		return
	}
	out := &Resp{
		Code:    xe.Code(),
		Message: lang.FormatText(c, xe.Error()),
		Data:    struct{}{},
	}
	var err2 error
	switch out.Code {
	case http.StatusBadRequest:
		err2 = c.JSON(http.StatusBadRequest, out)
	case http.StatusInternalServerError:
		err2 = c.JSON(http.StatusInternalServerError, out)
	default:
		err2 = c.JSON(http.StatusOK, out)
	}
	if err2 != nil {
		c.Logger().Error(err2)
	}
}
