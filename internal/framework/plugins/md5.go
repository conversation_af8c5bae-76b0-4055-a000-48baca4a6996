package plugins

import (
	"github.com/labstack/echo/v4"
	"github.com/labstack/echo/v4/middleware"
	"github.com/spf13/cast"
	"github.com/spf13/viper"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"gitlab-ee.funplus.io/ops-tools/compkg/sign"
	"net/url"
)

type (
	// MD5SignConfig defines the config for md5 sign middleware.
	MD5SignConfig struct {
		// Skipper defines a function to skip middleware.
		Skipper middleware.Skipper

		// AppName app name key
		AppName string

		SigningKey string

		// SignMethod sign method
		SignMethod string

		// SignIdx sign str key
		SignIdx string

		// SignInx request timestamp key
		Ts string
	}
)

var (
	// DefaultMD5SignConfig is the default MD5Sign middleware config.
	DefaultMD5SignConfig = MD5SignConfig{
		Skipper:    middleware.DefaultSkipper,
		SignMethod: "MD5",
		AppName:    "client_id",
		SignIdx:    "sign",
		Ts:         "timestamp",
	}
)

// Md5SignAuth returns an SignAuth middleware.
//
// For valid credentials it calls the next handler.
// For missing or invalid credentials, it sends "401 - Unauthorized" response.
func Md5SignAuth() echo.MiddlewareFunc {
	c := DefaultMD5SignConfig
	return MD5SignWithConfig(c)
}

// MD5SignWithConfig returns an MD5 sign middleware with config.
func MD5SignWithConfig(config MD5SignConfig) echo.MiddlewareFunc {
	if config.Skipper == nil {
		config.Skipper = DefaultMD5SignConfig.Skipper
	}
	if config.AppName == "" {
		config.AppName = DefaultSignConfig.AppName
	}
	if config.SignIdx == "" {
		config.SignIdx = DefaultSignConfig.SignIdx
	}
	if config.Ts == "" {
		config.Ts = DefaultSignConfig.Ts
	}
	return func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			if c.Echo().Debug {
				return next(c)
			}
			if config.Skipper(c) {
				return next(c)
			}
			requestParams, err := getParams(c)
			if err != nil {
				return err
			}
			//if time.Now().UnixNano()/1e6-cast.ToInt64(requestParams[config.Ts]) > 180000 {
			//	logger.Infof(c.Request().Context(), "time limit check not pass now:%v and request ts:%v", time.Now().UnixNano()/1e6, requestParams[config.Ts])
			//	return echo.ErrForbidden
			//}
			encrypt := cast.ToString(requestParams[config.SignIdx])
			delete(requestParams, config.SignIdx)
			signData := make(map[string]string)
			for k, v := range requestParams {
				signData[k] = url.QueryEscape(cast.ToString(v))
			}
			if config.SigningKey == "" {
				config.SigningKey = viper.GetString("auth." + signData[config.AppName])
			}
			localSign := sign.SWT(signData, config.SigningKey)
			if localSign != encrypt {
				logger.Infof(c.Request().Context(), "sign check not pass signData:%v appSecret:%v localSign:%v and request sign:%v", signData, config.SigningKey, localSign, encrypt)
				return echo.ErrUnauthorized
			}
			return next(c)
		}
	}
}
