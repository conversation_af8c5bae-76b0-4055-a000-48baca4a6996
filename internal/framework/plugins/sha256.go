package plugins

import (
	"encoding/base64"
	"ops-ticket-api/internal/framework/cache/local"
	"ops-ticket-api/utils"

	"github.com/labstack/echo/v4"
	"github.com/labstack/echo/v4/middleware"
	"github.com/spf13/cast"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"gitlab-ee.funplus.io/ops-tools/compkg/sign"
)

type (
	// SignConfig defines the config for sign middleware.
	SignConfig struct {
		// Skipper defines a function to skip middleware.
		Skipper middleware.Skipper

		// AppName app name key
		AppName string

		// Signing key to validate token.
		SigningKey string

		// SignMethod sign method
		SignMethod string

		// SignIdx sign str key
		SignIdx string

		// SignInx request timestamp key
		Ts string
	}
)

var (
	// DefaultSignConfig is the default MD5Sign middleware config.
	DefaultSignConfig = SignConfig{
		Skipper:    middleware.DefaultSkipper,
		SignMethod: "HmacSha256",
		AppName:    "appId",
		SignIdx:    "auth",
		Ts:         "ts",
	}
)

// Sha256SignAuth returns an SignAuth middleware.
//
// For valid credentials it calls the next handler.
// For missing or invalid credentials, it sends "401 - Unauthorized" response.
func Sha256SignAuth() echo.MiddlewareFunc {
	c := DefaultSignConfig
	return Sha256SignWithConfig(c)
}

// Sha256SignWithConfig returns an sign middleware with config.
func Sha256SignWithConfig(config SignConfig) echo.MiddlewareFunc {
	if config.Skipper == nil {
		config.Skipper = DefaultSignConfig.Skipper
	}
	if config.AppName == "" {
		config.AppName = DefaultSignConfig.AppName
	}
	if config.SignIdx == "" {
		config.SignIdx = DefaultSignConfig.SignIdx
	}
	if config.Ts == "" {
		config.Ts = DefaultSignConfig.Ts
	}
	return func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			if c.Echo().Debug {
				return next(c)
			}
			if config.Skipper(c) {
				return next(c)
			}

			requestParams, err := getParams(c)

			if err != nil {
				return err
			}
			//if time.Now().UnixNano()/1e6-cast.ToInt64(requestParams[config.Ts]) > 180000 {
			//	logger.Infof(c.Request().Context(), "time limit check not pass now:%v and request ts:%v", time.Now().UnixNano()/1e6, cast.ToInt64(requestParams[config.Ts]))
			//	return echo.ErrForbidden
			//}
			encrypt := cast.ToString(requestParams[config.SignIdx])
			delete(requestParams, config.SignIdx)
			signData := make(map[string]string)
			for k, v := range requestParams {
				signData[k] = cast.ToString(v)
			}
			// sk := viper.GetString("auth." + signData[config.AppName])
			sk := local.AuthConfigCache.FetchAuthConfig(signData[config.AppName])
			// localSign := base64.StdEncoding.EncodeToString(utils.HmacSha256(sign.Lexicographic(signData), sk))
			localSign := base64.StdEncoding.EncodeToString(utils.HmacSha256(sign.Lexicographic(signData), sk))
			if localSign != encrypt {
				logger.Infof(c.Request().Context(), "sign check not pass signData:%v appSecret:%v localSign:%v and request sign:%v", sign.Lexicographic(signData), sk, localSign, encrypt)
				return echo.ErrUnauthorized
			}
			return next(c)
		}
	}
}
