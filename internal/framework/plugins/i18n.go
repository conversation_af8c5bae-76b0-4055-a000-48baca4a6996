// Copyright 2021 funplus Authors. All Rights Reserved.
// @Description:
// @Author: Darcy
// @Date: 2021/12/2 4:28 PM

package plugins

import (
	"ops-ticket-api/internal/framework/lang"

	"github.com/labstack/echo/v4"
	"github.com/nicksnyder/go-i18n/v2/i18n"
)

// LangMiddleware 服务
func LangMiddleware(bundle *i18n.Bundle) echo.MiddlewareFunc {
	return func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) (err error) {
			language := c.Request().Header.Get("lang")
			accept := c.Request().Header.Get("Accept-Language")
			localizer := i18n.NewLocalizer(bundle, language, accept)
			lang.SetLocalizer(c, localizer)
			return next(c)
		}
	}
}
