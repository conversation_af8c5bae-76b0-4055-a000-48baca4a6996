package plugins

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"gitlab-ee.funplus.io/ops-tools/compkg/httpclient"
	"net/http"
	"ops-ticket-api/internal/framework/cache/local"
	"ops-ticket-api/pkg/filter"
	"ops-ticket-api/pkg/game"
	"ops-ticket-api/services/csctx"
	"time"

	"github.com/labstack/echo/v4"
	"github.com/labstack/echo/v4/middleware"
	"github.com/spf13/cast"
	"github.com/spf13/viper"
	"gitlab-ee.funplus.io/ops-tools/compkg/gametoken"
	"gitlab-ee.funplus.io/ops-tools/compkg/gametoken/game_token"
)

type (
	// GameTokenConfig defines the config for md5 sign middleware.
	GameTokenConfig struct {
		// <PERSON><PERSON> defines a function to skip middleware.
		Skipper middleware.Skipper

		// GameTokenKey game token key
		GameTokenKey string

		FpxAppId string

		GameId string
	}
)

type PrivateZoneResponse struct {
	Code int    `json:"code"`
	Msg  string `json:"msg"`
	Data struct {
		Fpid          string `json:"fpid"`
		Uid           string `json:"uid"`
		Avatar        string `json:"avatar"`
		Name          string `json:"name"`
		Server        int    `json:"server"`
		Level         int    `json:"level"`
		LastLogin     string `json:"last_login"`
		Ctime         int64  `json:"ctime"`
		PkgChannel    string `json:"pkg_channel"`
		UcToken       string `json:"uc_token"`
		GameProject   string `json:"game_project"`
		IsActive      bool   `json:"is_active"`
		GameID        string `json:"game_id"`
		OpenID        string `json:"open_id"`
		VipLevel      int    `json:"vip_level"`
		AccountGrowth int    `json:"account_growth"`
		GameGrowth    int    `json:"game_growth"`
		RoleGrowth    int    `json:"role_growth"`
		Coin          int    `json:"coin"`
		FpUid         string `json:"fp_uid"`
		GameName      string `json:"game_name"`
		GameIcon      string `json:"game_icon"`
		Env           string `json:"env"`
		GameAppID     string `json:"game_app_id"`
		UserVersion   int    `json:"user_version"`
	} `json:"data"`
}

var (
	// DefaultGameTokenConfig is the default GameToken middleware config.
	DefaultGameTokenConfig = GameTokenConfig{
		Skipper:      GameTokenSkipper,
		GameTokenKey: "game_token",
		FpxAppId:     "fpx_app_id",
		GameId:       "game_id",
	}
)

// GameTokenAuth returns an SignAuth middleware.
func GameTokenAuth() echo.MiddlewareFunc {
	c := DefaultGameTokenConfig
	return GameTokenWithConfig(c)
}

// GameTokenSkipper returns false which processes the middleware.
func GameTokenSkipper(echo.Context) bool {
	if viper.GetBool("app.skip_auth") {
		return true
	}
	return false
}

// GameTokenWithConfig returns an game token sign middleware with config.
func GameTokenWithConfig(config GameTokenConfig) echo.MiddlewareFunc {
	if config.Skipper == nil {
		config.Skipper = DefaultGameTokenConfig.Skipper
	}
	if config.GameTokenKey == "" {
		config.GameTokenKey = DefaultGameTokenConfig.GameTokenKey
	}
	if config.FpxAppId == "" {
		config.FpxAppId = DefaultGameTokenConfig.FpxAppId
	}
	if config.GameId == "" {
		config.GameId = DefaultGameTokenConfig.GameId
	}
	return func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			if config.Skipper(c) {
				return next(c)
			}
			if viper.GetString("app.env") == "local" {
				return next(c)
			}
			if v := c.Request().Header.Get("x-lin"); v == "lintest" {
				return next(c)
			}

			requestParams, err := getParams(c)
			if err != nil {
				return err
			}
			// 游戏外场景不需要验证
			if scene, ok := requestParams["scene"]; ok && cast.ToInt(scene) == 0 {
				return next(c)
			}
			//rUid uid 或者 role_id
			var fpxAppId string
			//先取参数中的game_id，再取fpx_app_id
			if gameId, ok := requestParams[config.GameId]; ok && cast.ToInt(gameId) != 0 {
				fpxAppId = cast.ToString(gameId)
			}
			//再取fpx_app_id
			if fpxAppId == "" {
				if appId, ok := requestParams[config.FpxAppId]; ok && cast.ToString(appId) != "" {
					fpxAppId = cast.ToString(appId)
				}
			}
			project, err := game.NewGameInfo().GetGameProjectById(fpxAppId)
			if err != nil {
				return echo.ErrBadRequest
			}

			// 设置fpx_app_id 和 game_project 到 ctx
			ctx := context.WithValue(c.Request().Context(), csctx.CtxGameProjectKey, project)
			ctx = context.WithValue(ctx, csctx.CtxGameIDOrFpxAppID, fpxAppId)
			c.SetRequest(c.Request().WithContext(ctx))
			//if ok := utils.InArrayAny(project, viper.GetStringSlice("ticket.game_token_check_list")); !ok {
			//	return next(c)
			//}

			if cast.ToInt(requestParams["scene"]) != 3 && cast.ToInt(requestParams["scene"]) != 0 {
				return next(c)
			}
			gameToken, ok := requestParams[config.GameTokenKey]

			// 没办法保证必传game_token,避免服务不可用线上事故，跳过检查
			if ok && gameToken != "" {
				// 校验game token && todo 设置uid=role_id ctx ，fpid=account_id ctx
				if err := authorizeCheck(cast.ToString(fpxAppId), cast.ToString(gameToken), requestParams); err != nil {
					logger.Errorf(c.Request().Context(), "authorizeCheck failed: %v. requestParams:%+v.", err, requestParams)
					return err
				}
			}

			// 校验私域token
			uid := cast.ToString(requestParams["uid"])
			fpid := cast.ToString(requestParams["fpid"])
			zoneFrom := cast.ToString(requestParams["zone_from"])
			zoneToken := cast.ToString(requestParams["zone_token"])
			check, err := privateZoneTokenCheck(c.Request().Context(), uid, fpid, project, zoneFrom, zoneToken)
			if err != nil {
				logger.Errorf(c.Request().Context(), "privateZoneTokenCheck return err. err:%v. requestParams:%+v.", err, requestParams)
				return echo.ErrBadRequest
			} else if !check {
				//return errors.New("privateZone token check failed")
				logger.Errorf(c.Request().Context(), "privateZone token check failed. requestParams:%+v.", requestParams)
				return echo.ErrUnauthorized
			}

			// 设置fpid和uid到ctx
			c.SetRequest(c.Request().WithContext(setFpidAndUidCtx(ctx, requestParams)))

			return next(c)
		}
	}
}

func privateZoneTokenCheck(ctx context.Context, uid, fpid, project, zoneFrom, privateZoneToken string) (bool, error) {
	if privateZoneToken == "onlyTestForCs" || zoneFrom == "" {
		return true, nil
	}
	if privateZoneToken == "" {
		return false, errors.New("zone token is empty")
	}
	// check zone token for remote logic
	privateZoneInfo := fmt.Sprintf("%v_%v_%v", uid, fpid, privateZoneToken)
	exist, _, _, _ := local.PrivateZoneCache.FetchPrivateZoneC(privateZoneInfo)
	if exist {
		logger.Infof(ctx, "hit local privateZone cache:%v", privateZoneInfo)
		return true, nil
	} else {
		// 请求接口，如果校验通过，则缓存到本地并设置一小时过期时间
		// 发送 HTTP 请求
		client := httpclient.New()
		client.SetHeader("Content-Type", "application/json")
		client.SetHeader("game-project", project)
		client.SetHeader("h5-auth", privateZoneToken)
		client.SetTimeout(time.Second * 5)
		body := map[string]interface{}{
			"token": privateZoneToken,
		}
		response, err := client.PostJson(context.Background(), viper.GetString("thirdparty.ops_backend_api.private_zone_user_info"), body)
		if err != nil {
			return false, fmt.Errorf("localFrontZoneFromTokenCheck request failed: %v", err)
		}
		if response.StatusCode != http.StatusOK {
			return false, fmt.Errorf("localFrontZoneFromTokenCheck unexpected status code: %d", response.StatusCode)
		}
		respBody := response.String()
		// 定义变量存储解析结果
		var privateZoneResp PrivateZoneResponse
		// 解析 JSON 数据
		err = json.Unmarshal([]byte(respBody), &privateZoneResp)
		if err != nil {
			return false, fmt.Errorf("localFrontZoneFromTokenCheck unmarshal error: %d", err)
		}
		logger.Infof(ctx, "privateZone resp is:%+v. uid: %v. fpid: %v", privateZoneResp, uid, fpid)
		if privateZoneResp.Data.Uid != uid || privateZoneResp.Data.Fpid != fpid {
			return false, nil
		}
		// 设置本地缓存
		local.PrivateZoneCache.ReSetPrivateZoneC(privateZoneInfo, true, privateZoneResp.Data.Name, privateZoneResp.Data.Server, privateZoneResp.Data.VipLevel, time.Hour*1)
	}
	return true, nil
}

func setFpidAndUidCtx(c context.Context, params map[string]interface{}) context.Context {

	var accountId string
	// 先取参数中的fpid
	if fpid, ok := params["fpid"]; ok && cast.ToInt64(fpid) > 0 {
		accountId = cast.ToString(fpid)
	}
	// 再取参数中的json_data的fpid
	if accountId == "" { // todo fix uid/fpid 放到jso_data中
		if js, ok := params["json_data"]; ok {
			if fpid := filter.NewJsonDataFilter(js).GetFpid(); fpid > 0 {
				accountId = cast.ToString(fpid)
			}
		}
	}
	//最后取参数中的account_id
	if accountId == "" {
		if accId, ok := params["account_id"]; ok {
			accountId = cast.ToString(accId)
		}
	}

	var uid string
	// 先取参数中的uid
	if u, ok := params["uid"]; ok && cast.ToInt64(u) > 0 {
		uid = cast.ToString(u)
	}
	// 再取参数中的json_data的uid
	if uid == "" { // todo fix uid/fpid 放到jso_data中
		if js, ok := params["json_data"]; ok {
			if u := filter.NewJsonDataFilter(js).GetUid(); u > 0 {
				uid = cast.ToString(u)
			}
		}
	}
	//最后取参数中的role_id
	if uid == "" {
		if roleId, ok := params["role_id"]; ok {
			uid = cast.ToString(roleId)
		}
	}
	if accountId != "" {
		c = context.WithValue(c, csctx.CtxFPIDAccountIDKey, accountId)
	}
	if uid != "" {
		c = context.WithValue(c, csctx.CtxUidRoleIDKey, uid)
	}
	return c
}

func authorizeCheck(fpxAppId, token string, params map[string]interface{}) error {
	if token == "onlyTestForCs" { // only test
		return nil
	}
	var accountId string
	// 先取参数中的fpid
	if fpid, ok := params["fpid"]; ok && cast.ToInt64(fpid) > 0 {
		accountId = cast.ToString(fpid)
	}
	// 再取参数中的json_data的fpid
	if accountId == "" { // todo fix uid/fpid 放到jso_data中
		if js, ok := params["json_data"]; ok {
			if fpid := filter.NewJsonDataFilter(js).GetFpid(); fpid > 0 {
				accountId = cast.ToString(fpid)
			}
		}
	}
	//最后取参数中的account_id
	if accountId == "" {
		if accId, ok := params["account_id"]; ok {
			accountId = cast.ToString(accId)
		}
	}
	if accountId == "" {
		return nil
	}
	if ok, _ := checkGameToken(cast.ToString(fpxAppId), cast.ToString(token), accountId); !ok {
		return echo.ErrUnauthorized
	}
	return nil
}

func checkGameToken(fpxAppId, gameToken, accountId string) (bool, error) {
	info, err := gametoken.DecodeToken(fpxAppId, gameToken, "")
	if err != nil && errors.Is(err, game_token.ErrNoSupport) { // 暂不支持的游戏
		return true, nil
	} else if err != nil {
		return false, err
	}
	return info.AccountId == accountId, nil
}
