// Copyright 2021 funplus Authors. All Rights Reserved.
// @Description:
// @Author: Darcy
// @Date: 2021/12/27 3:39 PM

package plugins

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"mime"
	"net/http"
	"strings"

	jsoniter "github.com/json-iterator/go"
	"github.com/labstack/echo/v4"
	"github.com/spf13/cast"
)

func getParams(c echo.Context) (map[string]interface{}, error) {
	var bodyBytes []byte
	cType := c.Request().Header.Get(echo.HeaderContentType)
	multipart := false
	if cType != "" {
		d, _, _ := mime.ParseMediaType(cType)
		if d == "multipart/form-data" || d == "multipart/mixed" {
			multipart = true
		}
	}
	if !multipart {
		if c.Request().Body != nil { // Read
			bodyBytes, _ = ioutil.ReadAll(c.Request().Body)
		}
		c.Request().Body = ioutil.NopCloser(bytes.NewBuffer(bodyBytes)) // Reset
	}
	requestParams := make(map[string]interface{})
	switch {
	case strings.HasPrefix(cType, echo.MIMEApplicationJSON):
		if err := jsoniter.ConfigFastest.Unmarshal(bodyBytes, &requestParams); err != nil {
			if ute, ok := err.(*json.UnmarshalTypeError); ok {
				return nil, echo.NewHTTPError(http.StatusBadRequest, fmt.Sprintf("Unmarshal type error: expected=%v, got=%v, field=%v, offset=%v", ute.Type, ute.Value, ute.Field, ute.Offset)).SetInternal(err)
			} else if se, ok := err.(*json.SyntaxError); ok {
				return nil, echo.NewHTTPError(http.StatusBadRequest, fmt.Sprintf("Syntax error: offset=%v, error=%v", se.Offset, se.Error())).SetInternal(err)
			}
			return nil, echo.NewHTTPError(http.StatusBadRequest, err.Error()).SetInternal(err)
		}
	case strings.HasPrefix(cType, echo.MIMEMultipartForm), strings.HasPrefix(cType, echo.MIMEMultipartForm):
		params, err := c.FormParams()
		if err != nil {
			return nil, echo.NewHTTPError(http.StatusBadRequest, err.Error()).SetInternal(err)
		}
		for k, v := range params {
			requestParams[k] = cast.ToString(v[0])
		}
	default:
		return nil, echo.ErrUnsupportedMediaType
	}
	return requestParams, nil
}
