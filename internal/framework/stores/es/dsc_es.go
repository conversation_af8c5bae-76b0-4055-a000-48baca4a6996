package es

import (
	"context"
	"net"
	"net/http"
	"sync"
	"time"

	"github.com/spf13/viper"
	"gitlab-ee.funplus.io/ops-tools/compkg/x/stores/es/client"
)

var (
	dscCli           client.Client
	dscProcessor     client.BulkProcessor
	dscProcessorOnce sync.Once
	dscRw            sync.RWMutex
)

// GetDscCli client
func GetDscCli(ctx context.Context) (client.Client, error) {
	dscRw.RLock()
	if dscCli != nil {
		dscRw.RUnlock()
		return dscCli, nil
	}
	dscRw.RUnlock()

	dscRw.Lock()
	defer dscRw.Unlock()
	if dscCli != nil {
		return dscCli, nil
	}

	var err error
	dscCli, err := client.NewClient(
		client.Nodes(viper.GetStringSlice("dsces.node")...),
		client.Username(viper.GetString("dsces.user")),
		client.Password(viper.GetString("dsces.pwd")),
		client.WithHttpClient(
			&http.Client{
				Transport: &http.Transport{
					DialContext: (&net.Dialer{
						Timeout: 5000 * time.Millisecond,
					}).DialContext,
					MaxIdleConns:        50,
					MaxIdleConnsPerHost: 100,
				},
			},
		),
	)
	if err != nil {
		return nil, err
	}
	return dscCli, nil
}
