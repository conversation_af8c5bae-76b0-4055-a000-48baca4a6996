package es

import (
	"context"
	"errors"
	"net"
	"net/http"
	"sync"
	"time"

	"github.com/spf13/viper"
	"gitlab-ee.funplus.io/ops-tools/compkg/x/stores/es/client"
)

var (
	examineDscCli           client.Client
	examineDscProcessor     client.BulkProcessor
	examineDscProcessorOnce sync.Once
	examineDscRw            sync.RWMutex
	EsConfigEmptyErr        error = errors.New("es nodes or index empty")
)

// GetExamineDscCli client
func GetExamineDscCli(ctx context.Context) (client.Client, error) {
	examineDscRw.RLock()
	if examineDscCli != nil {
		examineDscRw.RUnlock()
		return examineDscCli, nil
	}
	examineDscRw.RUnlock()

	examineDscRw.Lock()
	defer examineDscRw.Unlock()
	if examineDscCli != nil {
		return examineDscCli, nil
	}

	var (
		err    error
		_nodes = viper.GetStringSlice("examinees.node")
		_user  = viper.GetString("examinees.user")
		_pwd   = viper.GetString("examinees.pwd")
		_index = viper.GetString("examinees.index.examine_dsc_index")
	)
	if len(_nodes) == 0 || _index == "" {
		return nil, EsConfigEmptyErr
	}
	examineDscCli, err = client.NewClient(
		client.Nodes(_nodes...),
		client.Username(_user),
		client.Password(_pwd),
		client.WithHttpClient(
			&http.Client{
				Transport: &http.Transport{
					DialContext: (&net.Dialer{
						Timeout: 5000 * time.Millisecond,
					}).DialContext,
					MaxIdleConns:        20,
					MaxIdleConnsPerHost: 50,
				},
			},
		),
	)
	if err != nil {
		return nil, err
	}
	return examineDscCli, nil
}
