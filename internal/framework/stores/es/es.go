// Copyright 2021 funplus Authors. All Rights Reserved.
// @Description:
// @Author: Darcy
// @Date: 2022/3/3 4:20 PM

package es

import (
	"context"
	"net"
	"net/http"
	"sync"
	"time"

	"github.com/olivere/elastic/v7"
	"github.com/spf13/viper"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"gitlab-ee.funplus.io/ops-tools/compkg/x/stores/es/client"
	"go.uber.org/zap"
)

var (
	cli           client.Client
	processor     client.BulkProcessor
	processorOnce sync.Once
	rw            sync.RWMutex

	retryInterval    = 200 * time.Millisecond
	maxRetryInterval = 20 * time.Second
)

// GetCli client
func GetCli(ctx context.Context) (client.Client, error) {
	rw.RLock()
	if cli != nil {
		rw.RUnlock()
		return cli, nil
	}
	rw.RUnlock()

	rw.Lock()
	defer rw.Unlock()
	if cli != nil {
		return cli, nil
	}

	//fmt.Println("node:", viper.GetStringSlice("es.node"))
	var err error
	cli, err := client.NewClient(
		client.Nodes(viper.GetStringSlice("es.node")...),
		client.Username(viper.GetString("es.user")),
		client.Password(viper.GetString("es.pwd")),
		client.WithHttpClient(
			&http.Client{
				Transport: &http.Transport{
					DialContext: (&net.Dialer{
						Timeout: 5000 * time.Millisecond,
					}).DialContext,
					MaxIdleConns:        50,
					MaxIdleConnsPerHost: 100,
				},
			},
		),
	)
	if err != nil {
		return nil, err
	}
	return cli, nil
}

// GetProcessor create new processor
func GetProcessor(ctx context.Context) (client.BulkProcessor, error) {
	var err error
	processorOnce.Do(func() {
		cli, err = GetCli(ctx)
		if err != nil {
			logger.Error(ctx, "get bulk processor err", zap.String("err", err.Error()))
			return
		}
		bpParam := &client.BulkProcessorParameters{
			Name:          "ticket_processor",
			NumOfWorkers:  4,
			BulkActions:   1000,
			BulkSize:      1024 * 1024, // 1M
			FlushInterval: 3000 * time.Millisecond,
			Backoff:       elastic.NewExponentialBackoff(retryInterval, maxRetryInterval),
		}
		processor, err = cli.RunBulkProcessor(ctx, bpParam)
		if err != nil {
			logger.Error(ctx, "get bulk processor err", zap.String("err", err.Error()))
			return
		}
	})
	return processor, err
}
