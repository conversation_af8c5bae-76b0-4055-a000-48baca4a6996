package es

import (
	"context"
	"net"
	"net/http"
	"sync"
	"time"

	"github.com/spf13/viper"
	"gitlab-ee.funplus.io/ops-tools/compkg/x/stores/es/client"
)

var (
	lineCli client.Client
	lineRw  sync.RWMutex
)

// GetLineCli client
func GetLineCli(ctx context.Context) (client.Client, error) {
	lineRw.RLock()
	if lineCli != nil {
		lineRw.RUnlock()
		return cli, nil
	}
	lineRw.RUnlock()

	lineRw.Lock()
	defer lineRw.Unlock()
	if lineCli != nil {
		return lineCli, nil
	}

	var err error
	lineEsCli, err := client.NewClient(
		client.Nodes(viper.GetStringSlice("line_es.node")...),
		client.Username(viper.GetString("line_es.user")),
		client.Password(viper.GetString("line_es.pwd")),
		client.WithHttpClient(
			&http.Client{
				Transport: &http.Transport{
					DialContext: (&net.Dialer{
						Timeout: 5000 * time.Millisecond,
					}).DialContext,
					MaxIdleConns:        50,
					MaxIdleConnsPerHost: 100,
				},
			},
		),
	)
	if err != nil {
		return nil, err
	}
	return lineEsCli, nil
}
