// Copyright 2021 funplus Authors. All Rights Reserved.
// @Description: aws s3
// @Author: Darcy
// @Date: 2021/11/4 3:02 PM

package s3

import (
	"bytes"
	"context"
	"net/http"
	"strings"
	"sync"

	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/awserr"
	"github.com/aws/aws-sdk-go/aws/credentials"
	"github.com/aws/aws-sdk-go/aws/request"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/aws/aws-sdk-go/service/s3"
	"github.com/aws/aws-sdk-go/service/s3/s3manager"
	"github.com/spf13/viper"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"go.uber.org/zap"
)

var (
	// Defaults3Sess default
	Defaults3Sess *StoreS3
	s3SessOnce    sync.Once
)

// StoreS3 s3 store
type StoreS3 struct {
	*session.Session
}

func init() {
	s3SessOnce.Do(func() {
		Defaults3Sess = newS3Client()
	})
}

// newS3Client init
func newS3Client() *StoreS3 {
	return &StoreS3{
		Session: session.Must(
			session.NewSession(
				&aws.Config{
					CredentialsChainVerboseErrors: aws.Bool(true),
					EnableEndpointDiscovery:       aws.Bool(true),

					Region:      aws.String(viper.GetString("s3.region")),
					Credentials: credentials.NewStaticCredentials(viper.GetString("s3.access_key"), viper.GetString("s3.access_key_secret"), ""),
				},
			),
		),
	}
}

// Upload upload
func (s *StoreS3) Upload(ctx context.Context, body []byte, key string) (string, error) {
	uploader := s3manager.NewUploader(s.Session, func(u *s3manager.Uploader) {
		u.PartSize = 10 * 1024 * 1024
	})
	result, err := uploader.UploadWithContext(ctx, &s3manager.UploadInput{
		Bucket:      aws.String(viper.GetString("s3.bucket")),
		Key:         aws.String(key),
		Body:        bytes.NewReader(body),
		ContentType: aws.String(http.DetectContentType(body)),
	})
	logger.Info(ctx, "upload file result", zap.Any("result", result))
	if err != nil {
		if aerr, ok := err.(awserr.Error); ok && aerr.Code() == request.CanceledErrorCode {
			logger.Error(ctx, "upload canceled due to timeout", zap.String("uri", key), zap.String("err", err.Error()))
		} else {
			logger.Error(ctx, "failed to upload object", zap.String("uri", key), zap.String("err", err.Error()))
		}
		return "", err
	}
	return viper.GetString("s3.cdn_prefix_url") + strings.TrimLeft(key, "/"), nil
}

// PutObject put
func (s *StoreS3) PutObject(ctx context.Context, body []byte, key string) (string, error) {
	svc := s3.New(s.Session)
	_, err := svc.PutObjectWithContext(ctx, &s3.PutObjectInput{
		Bucket: aws.String(viper.GetString("s3.bucket")),
		Key:    aws.String(key),
		Body:   bytes.NewReader(body),
	})
	if err != nil {
		if aerr, ok := err.(awserr.Error); ok && aerr.Code() == request.CanceledErrorCode {
			logger.Error(ctx, "upload canceled due to timeout", zap.String("uri", key), zap.String("err", err.Error()))
		} else {
			logger.Error(ctx, "failed to upload object", zap.String("uri", key), zap.String("err", err.Error()))
		}
		return "", err
	}

	return viper.GetString("s3.cdn_prefix_url") + strings.TrimLeft(key, "/"), nil
}
