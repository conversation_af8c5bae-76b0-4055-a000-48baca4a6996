// Copyright 2020 Funplus Authors. All Rights Reserved.
// @Description: 框架参数验证器
// @Author: darcy

package validator

import (
	"github.com/go-playground/validator/v10"
)

// CustomValidator 自定义验证
type CustomValidator struct {
	validator *validator.Validate
}

// Validate validate
func (cv *CustomValidator) Validate(i interface{}) error {
	return cv.validator.Struct(i)
}

// NewValidate init
func NewValidate() *CustomValidator {
	return &CustomValidator{validator: validator.New()}
}
