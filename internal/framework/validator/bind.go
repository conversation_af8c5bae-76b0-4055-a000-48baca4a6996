package validator

import (
	jsoniter "github.com/json-iterator/go"
	"github.com/spf13/cast"
	"ops-ticket-api/internal/code"
	"ops-ticket-api/internal/framework/lang"
	"ops-ticket-api/internal/framework/plugins"
	"ops-ticket-api/internal/framework/xerrors"
	"ops-ticket-api/pkg/filter"
	"reflect"

	"github.com/labstack/echo/v4"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"go.uber.org/zap"
)

// BindValidate 服务验证
func BindValidate(ctx echo.Context, param interface{}) error {
	if err := ctx.Bind(param); err != nil {
		return xerrors.New(err.Error(), code.InvalidParams)
	}
	if err := ctx.Validate(param); err != nil {
		return xerrors.New(err.<PERSON><PERSON>r(), code.MissingParams)
	}
	return nil
}

// BindValidateCs c端验证
func BindValidateCs(ctx echo.Context, param interface{}) error {
	if err := ctx.Bind(param); err != nil {
		if ctx.Echo().Debug {
			return err
		}
		logger.Error(ctx.Request().Context(), "bind params err", zap.String("err", err.Error()))
		return xerrors.New(lang.FormatText(ctx, "RespBindParamsFail"), code.InvalidParams)
	}
	if err := fixBindFields(ctx, param); err != nil {
		logger.Error(ctx.Request().Context(), "fixBindFields err", zap.String("err", err.Error()), zap.Any("param", param))
		return xerrors.New(lang.FormatText(ctx, "RespBindParamsFail"), code.InvalidParams)
	}
	if err := mapKgFieldsToFpx(ctx, param); err != nil {
		logger.Error(ctx.Request().Context(), "mapKgFieldsToFpx err", zap.String("err", err.Error()), zap.Any("param", param))
		return xerrors.New(lang.FormatText(ctx, "RespBindParamsFail"), code.InvalidParams)
	}
	if err := ctx.Validate(param); err != nil {
		if ctx.Echo().Debug {
			return err
		}
		logger.Error(ctx.Request().Context(), "bind params err", zap.String("err", err.Error()))
		return xerrors.New(lang.FormatText(ctx, "RespInvalidParams"), code.MissingParams)
	}
	return nil
}

func fixBindFields(ctx echo.Context, param interface{}) error {
	var jsonDataOrg interface{}
	body := ctx.Get(plugins.BodyData)
	if body, ok := body.(string); !ok || body == "" {
		return nil
	} else {
		var _tmp = map[string]interface{}{}
		jsoniter.ConfigFastest.Unmarshal([]byte(body), &_tmp)
		jsonDataOrg = _tmp["json_data"]
	}
	var elem = reflect.ValueOf(param).Elem()
	if elem.Kind() != reflect.Struct || elem.NumField() == 0 {
		logger.Info(ctx.Request().Context(), "elem.Kind noMatch or numField eq0", zap.String("elemKind", elem.Kind().String()))
		return nil
	}

	jsonD := filter.NewJsonDataFilter(jsonDataOrg)
	// uid
	uidField := elem.FieldByName("Uid")
	if uidField.CanSet() && uidField.IsValid() && cast.ToInt64(uidField.Interface()) == 0 {
		if _uid := jsonD.GetUid(); _uid > 0 {
			setReflect(uidField, _uid)
		}
	}
	// fpid
	fpidField := elem.FieldByName("Fpid")
	if fpidField.CanSet() && fpidField.IsValid() && cast.ToInt64(fpidField.Interface()) == 0 {
		if _fpid := jsonD.GetFpid(); _fpid > 0 {
			setReflect(fpidField, _fpid)
		}
	}
	// country_code
	countryField := elem.FieldByName("CountryCode")
	if countryField.CanSet() && countryField.IsValid() && cast.ToString(countryField.Interface()) == "" {
		if _country := jsonD.GetCountryCode(); _country != "" {
			setReflect(countryField, _country)
		}
	}
	// total_pay
	totalPayField := elem.FieldByName("TotalPay")
	if totalPayField.CanSet() && totalPayField.IsValid() && cast.ToFloat64(totalPayField.Interface()) < 1 {
		if _tpay := jsonD.GetTotalPay(); _tpay >= 1 {
			setReflect(totalPayField, _tpay)
		}
	}
	// qfrom 来源
	qfromField := elem.FieldByName("Qfrom")
	if qfromField.CanSet() && qfromField.IsValid() && cast.ToString(qfromField.Interface()) == "" {
		if _qfrom := jsonD.GetQfrom(); _qfrom != "" {
			setReflect(qfromField, _qfrom)
		}
	}
	return nil
}

func mapKgFieldsToFpx(ctx echo.Context, param interface{}) error {
	var elem = reflect.ValueOf(param).Elem()
	if elem.Kind() != reflect.Struct || elem.NumField() == 0 {
		logger.Info(ctx.Request().Context(), "mapKgFpxFields elem.Kind noMatch or numField eq0", zap.String("elemKind", elem.Kind().String()))
		return nil
	}

	// fpid to account_id
	fpidField := elem.FieldByName("Fpid")
	accountFiled := elem.FieldByName("AccountId")
	if fpidField.CanSet() && fpidField.IsValid() && cast.ToInt64(fpidField.Interface()) > 0 {
		if accountFiled.CanSet() && accountFiled.IsValid() && cast.ToString(accountFiled.Interface()) == "" {
			setReflect(accountFiled, cast.ToString(cast.ToInt64(fpidField.Interface())))
		}
	}
	// game_id to fpx_app_id
	gameIdField := elem.FieldByName("GameId")
	fpxAppIdFiled := elem.FieldByName("FpxAppId")
	if gameIdField.CanSet() && gameIdField.IsValid() && cast.ToInt64(gameIdField.Interface()) > 0 {
		if fpxAppIdFiled.CanSet() && fpxAppIdFiled.IsValid() && cast.ToString(fpxAppIdFiled.Interface()) == "" {
			setReflect(fpxAppIdFiled, cast.ToString(gameIdField.Interface()))
		}
	}

	// country_code to country
	countryCodeField := elem.FieldByName("CountryCode")
	countryFiled := elem.FieldByName("Country")
	if countryCodeField.CanSet() && countryCodeField.IsValid() && cast.ToString(countryCodeField.Interface()) != "" {
		if countryFiled.CanSet() && countryFiled.IsValid() && cast.ToString(countryFiled.Interface()) == "" {
			setReflect(countryFiled, cast.ToString(countryCodeField.Interface()))
		}
	}

	// pay_amount to total_pay
	payAmountField := elem.FieldByName("PayAmount")
	totalPayField := elem.FieldByName("TotalPay")
	if payAmountField.CanSet() && payAmountField.IsValid() && cast.ToInt64(payAmountField.Interface()) > 0 {
		if totalPayField.CanSet() && totalPayField.IsValid() && cast.ToInt64(totalPayField.Interface()) == 0 {
			setReflect(totalPayField, cast.ToFloat64(payAmountField.Interface()))
		}
	}

	// 游戏内场景，定制版 = 0 强制指定到 = 3
	sceneField := elem.FieldByName("Scene")
	if sceneField.CanSet() && sceneField.IsValid() && cast.ToInt64(sceneField.Interface()) == 0 {
		logger.Infof(ctx.Request().Context(), "sceneField fork to 3. innerGame. ")
		setReflect(sceneField, 3) // 游戏内统一为=3
	}

	return nil
}

func setReflect(v reflect.Value, real interface{}) {
	switch v.Kind() {
	case reflect.Int:
		v.Set(reflect.ValueOf(cast.ToInt(real)))
	case reflect.Int8:
		v.Set(reflect.ValueOf(cast.ToInt8(real)))
	case reflect.Int16:
		v.Set(reflect.ValueOf(cast.ToInt16(real)))
	case reflect.Int32:
		v.Set(reflect.ValueOf(cast.ToInt32(real)))
	case reflect.Int64:
		v.Set(reflect.ValueOf(cast.ToInt64(real)))
	case reflect.Uint:
		v.Set(reflect.ValueOf(cast.ToUint(real)))
	case reflect.Uint8:
		v.Set(reflect.ValueOf(cast.ToUint8(real)))
	case reflect.Uint16:
		v.Set(reflect.ValueOf(cast.ToUint16(real)))
	case reflect.Uint32:
		v.Set(reflect.ValueOf(cast.ToUint32(real)))
	case reflect.Uint64:
		v.Set(reflect.ValueOf(cast.ToUint64(real)))
	case reflect.Float32:
		v.Set(reflect.ValueOf(cast.ToFloat32(real)))
	case reflect.Float64:
		v.Set(reflect.ValueOf(cast.ToFloat64(real)))
	case reflect.String:
		v.Set(reflect.ValueOf(cast.ToString(real)))
	case reflect.Bool:
		v.Set(reflect.ValueOf(cast.ToBool(real)))
	}
}
