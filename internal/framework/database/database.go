package database

import (
	"context"
	"database/sql"
	"github.com/labstack/echo/v4"
	elogger "gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"

	"sync"
	"time"

	"gorm.io/gorm/logger"
	"gorm.io/gorm/schema"

	// mysql driver
	_ "github.com/go-sql-driver/mysql"
	"github.com/spf13/viper"
	"gitlab-ee.funplus.io/ops-tools/compkg/traceing/gormtrace"
	"go.uber.org/zap"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/plugin/dbresolver"
)

var (
	ticketEngine, oldTicketEngine *gorm.DB
	clientOnce                    sync.Once
)

func init() {
	clientOnce.Do(func() {
		// 连接 global
		ticketEngine = createGroup(
			viper.GetString("db.ops_ticket.master"),
			viper.GetString("db.ops_ticket.slaver"))

		oldTicketEngine = createGroup(
			viper.GetString("db.ops_ticket_old.master"),
			viper.GetString("db.ops_ticket_old.slaver"))
	})
}

// Db 获取 global库
func Db() *gorm.DB {
	return ticketEngine
}
func GetOldTicketDb() *gorm.DB {
	return oldTicketEngine
}

// createGroup 创建主从库
func createGroup(master, slave string) *gorm.DB {
	db, err := gorm.Open(mysql.Open(master), &gorm.Config{
		NamingStrategy: schema.NamingStrategy{
			SingularTable: true,
		},
	})
	if err != nil {
		zap.S().Fatalf("[db] %s ping error : %s", master, err.Error())
		return nil
	}

	// sql日志
	if viper.GetBool("app.debug") {
		db.Logger = logger.Default.LogMode(logger.Info)
	} else {
		db.Logger = logger.Default.LogMode(logger.Silent)
	}
	db.Use(dbresolver.Register(dbresolver.Config{
		Replicas: []gorm.Dialector{mysql.Open(slave)},
		Policy:   dbresolver.RandomPolicy{},
	}).SetMaxIdleConns(viper.GetInt("db.max_idle_conn")).
		SetMaxOpenConns(viper.GetInt("db.max_open_conn")).
		SetConnMaxLifetime(time.Duration(viper.GetInt("db.max_life_time")) * time.Minute),
	)
	//tracing 使用gormTrace
	_ = db.Use(&gormtrace.TracePlugin{})

	return db
}

// CloseDb close
func CloseDb(db *sql.DB) {
	if err := db.Close(); err != nil {
		zap.S().Infof("close err:%v", err)
	}
}

func Paginate(page, pageSize *uint32) func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if *page == 0 {
			*page = 1
		}
		switch {
		case *pageSize > 100:
			*pageSize = 100
		case *pageSize <= 0:
			*pageSize = 20
		}
		offset := (*page - 1) * *pageSize
		return db.Offset(int(offset)).Limit(int(*pageSize))
	}
}
func DbOrderBy(dest interface{}) func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		return db.Order(dest)
	}
}

// SessionWriter gorm增加session customWrite方法，实现sql链路追踪
type SessionWriter struct {
	ctx context.Context
}

// Printf 实现gorm/logger.Writer接口
func (sw *SessionWriter) Printf(format string, args ...interface{}) {
	elogger.Infof(sw.ctx, format, args...)
}

func SessionWrite(ctx context.Context) *SessionWriter {
	return &SessionWriter{ctx: ctx}
}

// DbSession ctx: echo.Context / *context.Context
func DbSession(ctx any, db *gorm.DB) *gorm.DB {
	c := context.TODO()
	switch v := ctx.(type) {
	case echo.Context:
		c = v.Request().Context()
	case *context.Context:
		c = *v
	default:
		return db
	}
	if session, ok := c.Value("gormSession.traceId").(*gorm.DB); ok {
		return session
	}
	session := db.Session(&gorm.Session{Context: c, Logger: logger.New(
		SessionWrite(c),
		logger.Config{
			SlowThreshold: time.Second / 2, // Slow SQL threshold
			LogLevel:      logger.Info,     // Log level
		},
	)})
	c = context.WithValue(c, "gormSession.traceId", session)
	switch v := ctx.(type) {
	case echo.Context:
		v.SetRequest(v.Request().WithContext(c))
	case *context.Context:
		*v = c
	}
	return session
}
