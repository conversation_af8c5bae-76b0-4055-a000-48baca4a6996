package cfg

import (
	"context"
	"fmt"
	"github.com/jinzhu/copier"
	"github.com/labstack/echo/v4"
	"github.com/mitchellh/mapstructure"
	"github.com/spf13/viper"
	"gitlab-ee.funplus.io/ops-tools/compkg/config"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	fplog "gitlab-ee.funplus.io/ops-tools/compkg/fplog/logger"
	"go.uber.org/zap"
	"ops-ticket-api/internal/persistence"
	"ops-ticket-api/services/configure"
	"os"
)

type (
	// 打点上报统一配置文件格式
	appConfig struct {
		Fplog *fpLogger `json:"fplog" yaml:"fplog"` // 打点日志
	}
	fpLogger struct {
		LogAgent *fpLoggerAgentConf `json:"log_agent" yaml:"log_agent"`
	}
	fpLoggerAgentConf struct {
		Enable     bool                      `json:"enable" yaml:"enable"`
		Address    string                    `json:"address" yaml:"address"`
		Dispatcher map[string]*fpLoggerRoute `json:"dispatcher" yaml:"dispatcher"`
	}
	fpLoggerRoute struct {
		Route string `json:"route" yaml:"route"`
		Tag   string `json:"tag" yaml:"tag"`
		Bi    bool   `json:"bi" yaml:"bi"`
	}
)

func init() {
	// Watch
	config.WatchConfig(func() error {
		return nil
	})
	configs := viper.AllSettings()
	for k, v := range configs {
		viper.SetDefault(k, v)
	}
	// biz config
	viper.SetConfigFile("./config/biz.yaml")
	viper.SetConfigType("yaml")
	if err := viper.ReadInConfig(); err != nil {
		logger.Fatal(context.TODO(), "read in config err", zap.String("err", err.Error()))
	}
	// init 点击上报数据
	var fpLogConf fpLogger
	viper.UnmarshalKey("fplog", &fpLogConf, DecodeViperTag())
	if fpLogConf.LogAgent != nil {
		var fpLoggerConf = &fplog.LogConfig{}
		if err := copier.Copy(fpLoggerConf, fpLogConf); err != nil {
			logger.Fatal(context.TODO(), "fplog copy error", zap.Error(err))
		}
		fplog.Init(fpLoggerConf)
	}
	// Watch
	config.WatchConfig(func() error {
		return nil
	})
}

func DecodeViperTag() viper.DecoderConfigOption {
	return func(decoderConfig *mapstructure.DecoderConfig) {
		decoderConfig.TagName = "yaml"
	}
}

func BeGlobal() bool {
	return os.Getenv("release") == "global"
}
func BeCn() bool {
	return os.Getenv("release") == "cn"
}

func GetRefillTimeout() int64 {
	osEnvEnvironment := os.Getenv("environment") // test、stage、prod
	osEnvRelease := os.Getenv("release")         // cn、global
	fmt.Println(osEnvEnvironment, osEnvRelease)
	if osEnvEnvironment == "test" {
		return 1800 // 半个小时
	}
	return 3600 * 24 * 3 // 3天
}

func GetRefillTimeoutByConfig(ctx echo.Context, ticketId uint64, params map[string]string) (uint64, uint64, error) {
	tkInfo, err := persistence.NewTicket().GetTicketInfoFromMaster(ctx.Request().Context(), ticketId)
	if err != nil {
		logger.Errorf(ctx.Request().Context(), "GetRefillTimeoutByConfig tkInfo err. ticketId:%d. err:%v", ticketId, err)
		return 0, 0, err
	}

	tkCatInfo, err := configure.CatInfo(ctx, tkInfo.Project, tkInfo.CatID, params)
	if err != nil {
		logger.Errorf(ctx.Request().Context(), "GetRefillTimeoutByConfig tkCatInfo err. ticketId:%d. err:%v", ticketId, err)
		return 0, 0, err
	}
	if tkCatInfo.Deadline <= 0 {
		return uint64(GetRefillTimeout() / 3600), 0, nil
	}
	info, err := persistence.NewOvertimeTpl().GetOvertimeInfoIfExist(tkCatInfo.OvertimeReplyID)
	if err != nil {
		return uint64(GetRefillTimeout() / 3600), 0, nil
	}
	return tkCatInfo.Deadline, info.Overtime, nil

}

func GetAllocNoOpTimeout() int64 {
	osEnvEnvironment := os.Getenv("environment") // test、stage、prod
	osEnvRelease := os.Getenv("release")         // cn、global
	fmt.Println(osEnvEnvironment, osEnvRelease)

	if osEnvEnvironment == "test" {
		return 3600 * 4 // 2h
	}
	return 3600 * 4 // 4小时
}

func GetAllocNoOpTimeoutCheck() int64 {
	return 3600 * 4 // 4小时
}
