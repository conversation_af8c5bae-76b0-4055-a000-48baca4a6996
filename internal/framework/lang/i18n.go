package lang

import (
	"errors"
	"fmt"

	"github.com/BurntSushi/toml"
	"github.com/labstack/echo/v4"
	"github.com/nicksnyder/go-i18n/v2/i18n"
	"golang.org/x/text/language"
)

const (
	// I18nKey 定义上下文中的键
	I18nKey = "i18n"
)

type (
	// Bundle i18n.Bundle
	Bundle = i18n.Bundle

	// Message i18n.Message
	Message = i18n.Message

	// LocalizeConfig i18n.LocalizeConfig
	LocalizeConfig = i18n.LocalizeConfig

	// Data TemplateData
	Data = map[string]interface{}
)

// NewBundle new bundle
func NewBundle(tag language.Tag, tomls ...string) *Bundle {
	bundle := i18n.NewBundle(tag)
	bundle.RegisterUnmarshalFunc("toml", toml.Unmarshal)
	for _, file := range tomls {
		_, err := bundle.LoadMessageFile(file)
		if err != nil {
			fmt.Println(err)
		}
	}
	return bundle
}

// MustFormat must
func MustFormat(c echo.Context, lc *i18n.LocalizeConfig) string {
	return MustLocalizer(c).MustLocalize(lc)
}

// FormatText ft
func FormatText(c echo.Context, msg string) string {
	return FormatMessage(c, &Message{ID: msg, Other: msg}, nil)
}

// FormatMessage fm
func FormatMessage(c echo.Context, message *Message, args map[string]interface{}) string {
	if localizer, ok := GetLocalizer(c); ok {
		msg, err := localizer.Localize(&i18n.LocalizeConfig{
			DefaultMessage: message,
			TemplateData:   args,
		})
		if err != nil {
			return formatInternalMessage(message, args)
		}
		return msg
	}
	// 加载i18n中间件后,不会进入该分支,简单处理未加载i18n中间件时候的处理内容
	// After loading the i18n middleware, it will not enter this branch and simply handle the unloaded
	return formatInternalMessage(message, args)
}

// MustLocalizer i18n
func MustLocalizer(c echo.Context) *i18n.Localizer {
	localizer, ok := GetLocalizer(c)
	if !ok {
		panic(errors.New("context no has i18n localizer"))
	}
	return localizer
}

// GetLocalizer i18n
func GetLocalizer(c echo.Context) (*i18n.Localizer, bool) {
	if v := c.Get(I18nKey); v != nil {
		if l, b := v.(*i18n.Localizer); b {
			return l, true
		}
	}
	return nil, false
}

// SetLocalizer i18n
func SetLocalizer(c echo.Context, l *i18n.Localizer) {
	c.Set(I18nKey, l)
}

func formatInternalMessage(message *i18n.Message, args map[string]interface{}) string {
	if args == nil {
		return message.Other
	}
	tpl := i18n.NewMessageTemplate(message)
	msg, err := tpl.Execute("other", args, nil)
	if err != nil {
		return "message other not set"
	}
	return msg
}
