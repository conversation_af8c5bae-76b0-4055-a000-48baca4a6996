package local

import (
	"gitlab-ee.funplus.io/ops-tools/compkg/elog"
	"sync"
	"time"
)

// 定义缓存项结构
type cacheItem struct {
	exist     bool      // 缓存数据
	name      string    // 昵称
	server    int       // 服务器
	vipLevel  int       // 私域R级
	expiresAt time.Time // 过期时间
}

// 定义缓存结构
type privateZoneC struct {
	lock      sync.RWMutex
	whiteList map[string]cacheItem
}

// 初始化缓存
var PrivateZoneCache = &privateZoneC{
	lock:      sync.RWMutex{},
	whiteList: make(map[string]cacheItem),
}

// ReSetPrivateZoneC 设置缓存，带过期时间
func (c *privateZoneC) ReSetPrivateZoneC(privateZone string, exist bool, name string, server, vipLevel int, ttl time.Duration) {
	c.lock.Lock()
	defer c.lock.Unlock()
	c.whiteList[privateZone] = cacheItem{
		exist:     exist,
		name:      name,
		server:    server,
		vipLevel:  vipLevel,
		expiresAt: time.Now().Add(ttl), // 设置过期时间
	}
}

// FetchPrivateZoneC 获取缓存，检查是否过期
func (c *privateZoneC) FetchPrivateZoneC(privateZone string) (bool, string, int, int) {
	c.lock.RLock()
	defer c.lock.RUnlock()
	if item, ok := c.whiteList[privateZone]; ok {
		// 检查过期
		if time.Now().Before(item.expiresAt) {
			return item.exist, item.name, item.server, item.vipLevel
		}
		// 如果已过期，删除缓存项
		go c.delete(privateZone)
	}
	return false, "", 0, 0
}

// 删除缓存项
func (c *privateZoneC) delete(privateZone string) {
	c.lock.Lock()
	defer c.lock.Unlock()
	delete(c.whiteList, privateZone)
}

// StartCleanup 后台定期清理过期缓存项
func (c *privateZoneC) StartCleanup(interval time.Duration) {
	ticker := time.NewTicker(interval)
	go func() {
		for range ticker.C {
			c.lock.Lock()
			for key, item := range c.whiteList {
				if time.Now().After(item.expiresAt) {
					delete(c.whiteList, key)
					elog.Printf("backend clean up expired privateZoneCache:%s", key)
				}
			}
			c.lock.Unlock()
		}
	}()
}
