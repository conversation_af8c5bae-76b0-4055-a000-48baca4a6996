package local

import (
	"ops-ticket-api/models"
	"sync"
)

var AuthConfigCache = &authConfigC{lock: sync.RWMutex{}, whiteList: map[string]*models.FpAuthConfig{}}

type authConfigC struct {
	lock      sync.RWMutex
	whiteList map[string]*models.FpAuthConfig
}

func (c *authConfigC) ReSetAuthConfig(authKey string, authConfig *models.FpAuthConfig) {
	c.lock.Lock()
	defer c.lock.Unlock()
	c.whiteList[authKey] = authConfig
}

func (c *authConfigC) FetchAuthConfig(authKey string) string {
	c.lock.RLock()
	defer c.lock.RUnlock()
	if cfg, ok := c.whiteList[authKey]; ok {
		return cfg.Secret
	}
	return ""
}
