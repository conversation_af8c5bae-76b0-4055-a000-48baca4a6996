// Copyright 2020 funplus Authors. All Rights Reserved.
// @Description: redis key
// @Author: darcy

package keys

import "time"

const (
	// ExpiredNotFound 防止缓存穿透
	ExpiredNotFound = 5 * time.Second
	// KeyExpire Expire
	KeyExpire = 86400 * time.Second
	// Expire10M expired
	Expire10M = 600 * time.Second
	// Expire1H expired
	Expire1H        = 3600 * time.Second
	ExpireOneWeek   = time.Hour * 24 * 7
	TicketCatLangV2 = "ops:new:ticket:catV2:name:"
	// TicketAddonsLanguage 语言
	TicketAddonsLanguage = "ops:new:ticket:addons:language:"
	TicketProcessLock    = "ops:new:ticket:lock:process:"

	TicketTagList = "ops:new:ticket:tag:list:"
	// notice to user
	TicketNoticeListV3     = "ops:new:ticket:noticeV3:list"
	TicketNoticeOvertimeV3 = "ops:new:ticket:noticeV3:overtime"

	// 卡片配置--问题分类信息
	TicketCatInfo = "ops:ticket:cat:info:"

	//
	TicketTimeWatchPlayerListV3         = "ops:new:ticket:timeout:watch:playerV3:"
	TicketRefillTimeOutLockV3           = "ops:new:ticket:lock:refillV3:"
	TicketAutoAllocUniqLockV3           = "ops:new:ticket:auto:alloc:V3"
	TicketAcceptor2HNoActionListV3      = "ops:new:ticket:acceptor:2HNoAction:list:V3"
	TicketAcceptor2HNoActionLockV3      = "ops:new:ticket:acceptor:2HNoAction:lock:V3"
	TicketCreateNoRepeat                = "ops:new:ticket:create:noRepeat"
	TicketCreate5MinLimit               = "ops:new:ticket:create:5minLimit:%s:%s"
	TicketCreate1HLimit                 = "ops:new:ticket:create:1hLimit:%s:%s"
	TicketCatOpts                       = "ops:ticket:cat:opts:%s"
	TicketAccountLastCreateTimeKey      = "ops:new:ticket:account:last:create:time:V2:%s"
	TicketOvertimeRemarkWatchPlayerList = "ops:new:ticket:overtime:remark:watch:player"
	TicketOvertimeRemarkKey             = "ops:new:ticket:overtime:remark:key:"
	TicketNewAIUniqLock                 = "ops:new:ticket:new:ai"

	// examine
	ExamineTaskGenLockV3 = "ops:new:examine:task:gen:lock:v3:%d"

	// data_plat_user_info
	DataPlatItemAndI18nPubKey = "ops:new:dataplat:itemi18n:pub"

	//
	ChannelCatTrees = "ops:new:channel:cat:tree:%d"

	//
	DiscordEventWorkLock       = "ops:new:discord:event:work:lock:v3"
	DiscordMsgUnReadUserKey    = "ops:new:discord:msg:u:%s"
	DiscordMsgUnReadChannelKey = "ops:new:discord:msg:c:%s"
	DiscordMsgHasGetKey        = "ops:new:discord:msg:has:%s"
	// DiscordCrmPlayerKey crm玩家fpid
	DiscordCrmPlayerKey                       = "ops:new:discord:crm:player:%s"
	DiscordCrmPlayerSyncKey                   = "ops:new:discord:crm:player:sync"
	DiscordHistoryInteractDetailSyncKey       = "ops:new:discord:history:interact:detail:sync"
	DiscordYesterdayInteractDetailSyncKey     = "ops:new:discord:yesterday:interact:detail:sync"
	DiscordMessageCountDetailHistorySyncKey   = "ops:new:discord:history:message:count:detail:sync"
	DiscordMessageCountDetailYesterdaySyncKey = "ops:new:discord:yesterday:message:count:detail:sync"
	PlayerMaintainConfigSyncKey               = "ops:new:player:maintain:config:sync"
	YesterdayDiscordReplyTimeDSyncKey         = "ops:new:discord:reply:time:d:sync"
	DiscordLastReplyServiceHistorySyncKey     = "ops:new:discord:history:last:reply:service:sync"
	DiscordPlayerTagsSyncKey                  = "ops:new:discord:player:tags:sync"
	DiscordMessageCreateBatchKey              = "ops:new:discord:message:create:batch:sync:%d"
	// DiscordAccountLastChatTimeKey (accountid ->  最近一次DC对话时间)
	DiscordAccountLastChatTimeKey = "ops:new:discord:account:last:chat:time:%s"

	// survey
	SurveyDoGenLinkLock       = "ops:new:survey:do:gen:link:lock"
	SurveyPushCycleEveryWeek  = "ops:new:survey:push:cycle:week:%s:%d.%d"
	SurveyPushCycleEveryMonth = "ops:new:survey:push:cycle:month:%s:%d"
	SurveyConfigCacheKey      = "ops:new:survey:config:key:v1:%d"
	SurveyLinkCacheKey        = "ops:new:survey:link:key:v1:%s"
	SurveyTokenUsedCacheKey   = "ops:new:survey:token:used:v1:%s"

	LineCrmPlayerSyncKey = "ops:new:line:crm:player:sync"

	// 工单监控相关
	TicketMonitorKey = "ops:new:ticket:monitor:%s:%d" // gameId:catId

	// 工单知识库训练
	TicketQuestionTrainKey = "ops:new:ticket:question:train:%d"
)
