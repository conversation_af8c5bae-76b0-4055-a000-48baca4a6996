package rds

import (
	"context"
	"errors"
	"fmt"
	"github.com/go-redis/redis/v8"
	jsoniter "github.com/json-iterator/go"
	"github.com/spf13/cast"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"go.uber.org/zap"
	"ops-ticket-api/internal/framework/cache/keys"
	"ops-ticket-api/proto/pb"
	"ops-ticket-api/utils"
	"time"
)

// TicketAddNotice 工单 - 增加红点通知
func TicketAddNotice(ctx context.Context, replyId, ticketId uint64, appId, uuid, account string, scene uint8, redType pb.RedPointTypeEnum) {

	notice := &pb.NoticeResp{
		ObjectId: ticketId,
		Scene:    uint32(scene),
	}

	if redType == pb.RedPointTypeEnum_RedPointTypeMessage {
		notice.NoticeId = replyId
		notice.From = 1
	} else if redType == pb.RedPointTypeEnum_RedPointTypeOvertime {
		notice.NoticeId = utils.NowTimestamp()
		notice.From = 2
	}

	marshal, err := jsoniter.Marshal(notice)
	if err != nil {
		logger.Error(ctx, "[v3]set ticket notice marshal err", zap.Uint64("ticketId", ticketId), zap.String("err", err.Error()))
		return
	}
	var field = cast.ToString(ticketId)
	var accountKey string
	switch pb.SceneType(scene) {
	case pb.SceneType_Loading:
		accountKey = uuid
	default:
		accountKey = account
	}
	values := map[string]interface{}{
		field: marshal,
	}
	baseKey := keys.TicketNoticeListV3
	if redType == pb.RedPointTypeEnum_RedPointTypeOvertime {
		baseKey = keys.TicketNoticeOvertimeV3
	}
	rdsKey := fmt.Sprintf("%s:%s:%s:%s", baseKey, appId, pb.SceneType(scene).String(), accountKey)
	fmt.Println(rdsKey, "--")
	if err := RCli.HSet(ctx, rdsKey, values).Err(); err != nil {
		logger.Error(ctx, "[v3]set ticket notice err", zap.Uint64("ticketId", ticketId), zap.String("err", err.Error()))
		return
	}
	logger.Info(ctx, "[v3]SetRedPoint", zap.String("rdsKey", rdsKey), zap.Uint8("redType", uint8(redType)))
	return
}

func TicketDelNotice(ctx context.Context, appId, uuid, account string, scene uint8, ticketId uint64, redType pb.RedPointTypeEnum) error {
	var field = cast.ToString(ticketId)
	var accountKey string
	switch pb.SceneType(scene) {
	case pb.SceneType_Loading:
		accountKey = uuid
	default:
		accountKey = account
	}

	if redType != pb.RedPointTypeEnum_RedPointTypeMessage {
		rdsKeyOvertime := fmt.Sprintf("%s:%s:%s:%s", keys.TicketNoticeOvertimeV3, appId, pb.SceneType(scene).String(), accountKey)
		logger.Info(ctx, "[v3]SetMsgRead", zap.String("rdsKey", rdsKeyOvertime), zap.String("field", field))
		if err := RCli.HDel(ctx, rdsKeyOvertime, field).Err(); err != nil {
			logger.Error(ctx, "[v3]del ticket notice err", zap.String("rdsKey", rdsKeyOvertime), zap.String("field", accountKey), zap.String("err", err.Error()))
		}
	}
	if redType != pb.RedPointTypeEnum_RedPointTypeOvertime {
		rdsKeyMsg := fmt.Sprintf("%s:%s:%s:%s", keys.TicketNoticeListV3, appId, pb.SceneType(scene).String(), accountKey)
		logger.Info(ctx, "[v3]SetMsgRead", zap.String("rdsKey", rdsKeyMsg), zap.String("field", field))
		if err := RCli.HDel(ctx, rdsKeyMsg, field).Err(); err != nil {
			logger.Error(ctx, "[v3]del ticket notice err", zap.String("rdsKey", rdsKeyMsg), zap.String("field", accountKey), zap.String("err", err.Error()))
			return err
		}
	}

	return nil
}

func TicketGetNotice(ctx context.Context, appId, uuid, account string, scene uint8) *pb.NoticeResp {
	var accountKey string
	switch pb.SceneType(scene) {
	case pb.SceneType_Loading:
		accountKey = uuid
	default:
		accountKey = account
	}
	start := time.Now()

	keysToCheck := []string{
		fmt.Sprintf("%s:%s:%s:%s", keys.TicketNoticeListV3, appId, pb.SceneType(scene).String(), accountKey),
		fmt.Sprintf("%s:%s:%s:%s", keys.TicketNoticeOvertimeV3, appId, pb.SceneType(scene).String(), accountKey),
	}

	noticeCount := 0
	for _, rdsKey := range keysToCheck {
		vals, _, err := RCli.HScan(ctx, rdsKey, 0, "", 0).Result()
		if err != nil {
			continue
		}
		if len(vals) < 2 {
			continue
		}
		noticeCount += cast.ToInt(len(vals) / 2)
	}

	for _, rdsKey := range keysToCheck {
		vals, _, err := RCli.HScan(ctx, rdsKey, 0, "", 1).Result()
		if err != nil {
			if errors.Is(err, context.Canceled) {
				logger.Warn(ctx, "[v3]get ticket notice canceled", zap.String("rdsKey", rdsKey), zap.String("err", err.Error()), zap.Any("cost", time.Since(start)))
				break
			} else if !errors.Is(err, redis.Nil) {
				logger.Error(ctx, "[v3]get ticket notice err", zap.String("rdsKey", rdsKey), zap.String("err", err.Error()), zap.Any("cost", time.Since(start)))
			}
			continue
		}
		if len(vals) < 2 {
			logger.Info(ctx, "[v3]get ticket notice empty", zap.String("rdsKey", rdsKey))
			continue
		}
		// 解析第一个找到的值
		notice := &pb.NoticeResp{}
		if err := jsoniter.UnmarshalFromString(vals[1], &notice); err != nil {
			logger.Error(ctx, "[v3]get ticket notice unmarshal err", zap.String("rdsKey", rdsKey), zap.String("err", err.Error()))
			continue
		}
		notice.NoticeCount = uint64(noticeCount)
		return notice
	}
	logger.Info(ctx, "[v3]No ticket notices found in any key", zap.Any("cost", time.Since(start)))
	return nil
}

func TicketNoticeAll(ctx context.Context, appId, uuid, account string, scene uint32) map[uint64]struct{} {
	var accountKey string
	switch pb.SceneType(scene) {
	case pb.SceneType_Loading: // 游戏外
		accountKey = uuid
	default:
		accountKey = account
	}

	keysToCheck := []string{
		fmt.Sprintf("%s:%s:%s:%s", keys.TicketNoticeListV3, appId, pb.SceneType(scene).String(), accountKey),
		fmt.Sprintf("%s:%s:%s:%s", keys.TicketNoticeOvertimeV3, appId, pb.SceneType(scene).String(), accountKey),
	}
	tkMp := make(map[uint64]struct{})
	for _, rdsKey := range keysToCheck {
		tkList, err := RCli.HKeys(ctx, rdsKey).Result()
		if err != nil {
			if !errors.Is(err, redis.Nil) {
				logger.Error(ctx, "[v3]get ticket notice err", zap.String("rdsKey", rdsKey), zap.String("err", err.Error()))
			}
			continue
		}
		for _, tk := range tkList {
			tkMp[cast.ToUint64(tk)] = struct{}{}
		}
	}
	return tkMp
}
