// Copyright 2020 funplus Authors. All Rights Reserved.
// @Description: redis init
// @Author: darcy

package rds

import (
	"context"
	"errors"
	"github.com/bsm/redislock"
	"sync"
	"time"

	"ops-ticket-api/internal/framework/cache/keys"

	"github.com/go-redis/redis/v8"
	jsoniter "github.com/json-iterator/go"
	"github.com/klauspost/compress/snappy"
	"github.com/spf13/viper"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"gitlab-ee.funplus.io/ops-tools/compkg/traceing/redistrace"
	"go.uber.org/zap"
)

var (
	// RCli redis cache
	RCli *Cache
	once sync.Once

	notFoundPlaceholder = "*"
	errEmpty            = errors.New("data empty")
	errPlaceholder      = errors.New("placeholder")
)

// Cache redis cache
type (
	// Cache cache
	Cache struct {
		*redis.Client
	}
	queryFn func(v interface{}) error
)

func init() {
	NewRCache()
}

// NewRCache init
func NewRCache() *Cache {
	once.Do(func() {
		var cli *redis.Client
		opt := &redis.Options{
			Addr:         viper.GetString("redis.addr"),
			Password:     viper.GetString("redis.password"),
			PoolSize:     100,
			PoolTimeout:  time.Duration(30) * time.Second,
			IdleTimeout:  time.Duration(50) * time.Second,
			ReadTimeout:  time.Duration(30) * time.Second,
			WriteTimeout: time.Duration(30) * time.Second,
			MaxRetries:   3,
			DB:           viper.GetInt("redis.db"),
		}
		cli = redis.NewClient(opt)
		if _, err := cli.Ping(context.Background()).Result(); err != nil {
			logger.Error(context.Background(), "redis component err", zap.String("error", err.Error()))
			return
		}
		cli.AddHook(redistrace.TraceHook{})
		RCli = &Cache{cli}
	})
	return RCli
}

func GetRdsMux(ctx context.Context, muxKey string) (*redislock.Lock, error) {
	mux := redislock.New(NewRCache().Client)
	muxLock, err := mux.Obtain(ctx, muxKey, 10*time.Second, nil)
	if err != nil {
		return nil, err
	}
	return muxLock, nil
}
func RefreshRdsMux(ctx context.Context, lock *redislock.Lock, close chan struct{}) {
	tm := time.NewTicker(time.Second * 5)
	for {
		select {
		case <-tm.C:
			logger.Infof(ctx, "RefreshRdsMux refresh....")
			if err := lock.Refresh(ctx, 10*time.Second, nil); err != nil {
				logger.Errorf(ctx, "RefreshRdsMux return err .err:%v", err)
			}
		case <-close:
			logger.Infof(ctx, "RefreshRdsMux closed. break")
			return
		}
	}

}
func (cc *Cache) GetCacheVal(ctx context.Context, rdsKey string, dest interface{}) error {
	result, err := cc.Get(ctx, rdsKey).Result()
	if err != nil {
		return err
	}
	if result == "[]" || result == "" {
		return errEmpty
	}
	if result == notFoundPlaceholder {
		cc.Expire(ctx, rdsKey, keys.ExpiredNotFound)
		return errPlaceholder
	}
	if err := jsoniter.ConfigFastest.UnmarshalFromString(result, dest); err != nil {
		return err
	}
	return nil
}

func (cc *Cache) SetCacheVal(ctx context.Context, rdsKey string, dest interface{}, expired time.Duration) error {
	destStr, err := jsoniter.ConfigFastest.MarshalToString(dest)
	if err != nil {
		return err
	}
	err = cc.Set(ctx, rdsKey, destStr, expired).Err()
	if err != nil {
		return err
	}
	return nil
}

// QueryRow get from cache
func (cc *Cache) QueryRow(ctx context.Context, rdsKey string, dest interface{}, query queryFn) error {
	err := cc.GetCacheVal(ctx, rdsKey, dest)
	if err != nil {
		if !errors.Is(err, redis.Nil) {
			logger.Error(ctx, "get data from cache err", zap.String("rds_key", rdsKey), zap.String("err", err.Error()))
		}
		// 缓存穿透
		if errors.Is(err, errPlaceholder) {
			return nil
		}
		if err := query(dest); err != nil {
			return err
		}
		if err := cc.SetCacheVal(ctx, rdsKey, dest, keys.Expire1H); err != nil {
			logger.Error(ctx, "set data to cache err", zap.String("rds_key", rdsKey), zap.String("err", err.Error()))
		}
		return nil
	}
	return nil
}

// QueryRowHash get from hash cache
func (cc *Cache) QueryRowHash(ctx context.Context, rdsKey, field string, dest interface{}, expire bool, query queryFn) error {
	ret, err := cc.HGet(ctx, rdsKey, field).Result()
	if err != nil {
		if !errors.Is(err, redis.Nil) {
			logger.Error(ctx, "get data from cache err", zap.String("rds_key", rdsKey), zap.String("field", field), zap.String("err", err.Error()))
		}
		if err := query(dest); err != nil {
			return err
		}
		value, _ := jsoniter.Marshal(dest)
		destGz := snappy.Encode(nil, value)
		if err := cc.HSet(ctx, rdsKey, field, destGz).Err(); err != nil {
			logger.Error(ctx, "set info to hash cache err", zap.String("rds_key", rdsKey), zap.String("field", field), zap.String("err", err.Error()))
		}
		if expire {
			if err := cc.Expire(ctx, rdsKey, keys.Expire1H).Err(); err != nil {
				logger.Error(ctx, "set info to hash cache expire err", zap.String("rds_key", rdsKey), zap.String("field", field), zap.String("err", err.Error()))
			}
		}
		return nil
	}
	destGz, err := snappy.Decode(nil, []byte(ret))
	if err != nil {
		return err
	}
	if err := jsoniter.Unmarshal(destGz, dest); err != nil {
		logger.Error(ctx, "get info from cache unmarshal err", zap.String("rds_key", rdsKey), zap.String("field", field), zap.String("err", err.Error()))
	}
	return nil
}

// DelHash del hash cache by keys
func DelHash(key string, hashKeys ...string) error {
	return RCli.HDel(context.Background(), key, hashKeys...).Err()
}

// DelKey del cache  by keys
func DelKey(keys ...string) error {
	return RCli.Del(context.Background(), keys...).Err()
}

func Set(ctx context.Context, key string, data interface{}, tm time.Duration) error {
	_, err := RCli.Set(ctx, key, data, tm).Result()
	return err
}

// Exists check a key
func Exists(ctx context.Context, key string) bool {
	exists, err := RCli.Exists(ctx, key).Result()
	if err != nil {
		return false
	}
	return exists > 0
}
