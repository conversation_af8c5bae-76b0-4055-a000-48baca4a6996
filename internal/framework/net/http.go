package net

import (
	"bytes"
	"context"
	"encoding/json"
	"github.com/labstack/echo/v4"
	"github.com/pkg/errors"
	"github.com/spf13/cast"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"go.uber.org/zap"
	"io"
	"net/http"
	"net/url"
	"strings"
	"time"
)

func HttpRequest(ctx context.Context, api, method string, header map[string]string, body map[string]interface{}) ([]byte, error) {
	var reqBody io.Reader
	if method == http.MethodPost {
		switch header[echo.HeaderContentType] {
		case echo.MIMEApplicationJSON:
			body, _ := json.Marshal(body)
			reqBody = bytes.NewReader(body)
		case echo.MIMEApplicationForm:
			uv := make(url.Values)
			for k, v := range body {
				uv.Add(k, cast.ToString(v))
			}
			reqBody = strings.NewReader(uv.Encode())
		default:
			return nil, errors.Errorf("content-type:%s not support", header[echo.HeaderContentType])
		}
	}
	req, _ := http.NewRequestWithContext(ctx, method, api, reqBody)
	for k, v := range header {
		req.Header.Set(k, v)
	}
	cli := &http.Client{
		Timeout: 10 * time.Second,
	}
	resp, err := cli.Do(req)
	if err != nil {
		logger.Error(ctx, "Failed to send request", zap.Error(err))
		return nil, err
	}
	defer resp.Body.Close()

	// 解析响应
	if resp.StatusCode != http.StatusOK {
		logger.Error(ctx, "http response error", zap.Int("status", resp.StatusCode))
		return nil, err
	}
	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		logger.Error(ctx, "Failed to send request", zap.Error(err))
		return nil, err
	}
	logger.Info(ctx, "http response",
		zap.String("api", api),
		zap.Any("header", header),
		zap.Any("request", body),
		zap.ByteString("response", respBody))
	return respBody, nil
}
