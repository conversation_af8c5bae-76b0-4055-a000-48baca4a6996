package xerrors

import (
	"errors"
	"fmt"
	"github.com/go-sql-driver/mysql"
	"ops-ticket-api/internal/code"
)

// Error 扩展的Error接口
type Error interface {
	Prefix(prefix ...string) Error
	Unwrap() error
	Code() int
	Error() string
	Stack() string
}

// warpError warpError对象
type warpError struct {
	err    error
	frame  Frame
	code   int
	prefix string
}

func New(e interface{}, c ...int) Error {
	var code = -1
	if len(c) > 0 {
		code = c[0]
	}
	err, ok := e.(error)
	if !ok {
		err = fmt.Errorf("%v", e)
	}
	return &warpError{
		err:    err,
		frame:  Caller(0),
		code:   code,
		prefix: "",
	}
}

func (e *warpError) Prefix(prefix ...string) Error {
	ne := &warpError{
		err:   e,
		frame: Caller(0),
		code:  e.code,
	}
	if len(prefix) > 0 {
		ne.prefix = prefix[0]
	}
	return ne
}

func (e *warpError) Unwrap() error {
	return e.err
}

func (e *warpError) Code() int {
	return e.code
}

func (e *warpError) Error() string {
	msg := e.err.Error()
	if e.prefix != "" {
		msg = fmt.Sprintf("%s: %s", e.prefix, msg)
	}
	return msg
}

func (e *warpError) Stack() string {
	str := fmt.Sprintf("%s\n", e.frame.String())
	if err, ok := e.err.(Error); ok {
		str = fmt.Sprintf("%s%s", str, err.Stack())
	}
	return str
}

func RepeatDataErr(err error) error {
	var dbErr *mysql.MySQLError
	if errors.As(err, &dbErr) && dbErr.Number == 1062 {
		return New(code.StatusText(code.RepeatData), code.RepeatData)
	}
	return err
}
