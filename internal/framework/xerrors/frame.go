package xerrors

import (
	"fmt"
	"path/filepath"
	"runtime"
)

type (
	Frame struct {
		frames [3]uintptr
	}
)

func Caller(skip int) Frame {
	var s Frame
	runtime.Callers(skip+2, s.frames[:])
	return s
}

func (f Frame) location() (function, file string, line int) {
	frames := runtime.CallersFrames(f.frames[:])
	if _, ok := frames.Next(); !ok {
		return "", "", 0
	}
	fr, ok := frames.Next()
	if !ok {
		return "", "", 0
	}
	return fr.Function, fr.File, fr.Line
}

func (f Frame) String() string {
	function, file, line := f.location()
	var str string
	if file != "" {
		str += fmt.Sprintf("%s:%d\n\t", file, line)
	}
	if function != "" {
		str += filepath.Base(function)
	}
	return str
}
