package dsc

import (
	"context"
	"fmt"
	"ops-ticket-api/models"
	"sync"

	"github.com/bwmarrin/discordgo"
	"github.com/jinzhu/copier"
)

var (
	dscSessions = dscClient{
		once:    sync.Once{},
		clients: sync.Map{},
		show:    make(map[string]string),
		config:  make(map[string]*models.DscBotConfigDf),
	}
	ErrDscSessionEmpty = fmt.Errorf("dsc session empty")
)

type (
	dscClient struct {
		once    sync.Once
		clients sync.Map
		show    map[string]string
		config  map[string]*models.DscBotConfigDf
	}
)

func AddSess(ctx context.Context, appId string, appShow string, config *models.DscBotConfigDf) error {
	if appId == "" || config == nil {
		return fmt.Errorf("appId or config is empty")
	}
	if config.ClientId == "" || config.BotToken == "" || config.PublicKey == "" {
		return fmt.Errorf("config df err. appId:%s. clientId:%s. publicKey:%s. botToken:%s", appId, config.ClientId, config.PublicKey, config.BotToken)
	}
	if _, ok := dscSessions.config[appId]; ok {
		return fmt.Errorf("appId:%s already exists", appId)
	}
	dscSessions.show[appId] = appShow
	sess, err := discordgo.New("Bot " + config.BotToken)
	if err != nil {
		return err
	}
	sess.Identify.Intents = discordgo.IntentsAllWithoutPrivileged | discordgo.IntentGuildMembers | discordgo.IntentMessageContent
	dscSessions.clients.Store(appId, sess)
	dscSessions.config[appId] = config
	return nil
}

func DeleteSess(ctx context.Context, appId string) {
	delete(dscSessions.show, appId)
	delete(dscSessions.config, appId)
	dscSessions.clients.Delete(appId)
}

func UpdateSessBotConfig(ctx context.Context, appId string, config *models.DscBotConfigDf) error {
	if _, ok := dscSessions.config[appId]; !ok {
		return fmt.Errorf("appId:%s not found", appId)
	}
	dscSessions.config[appId] = config
	return nil
}

func GetAllAppIds(ctx context.Context) []string {
	var v []string
	for k := range dscSessions.config {
		v = append(v, k)
	}
	return v
}
func GetAppIdShow(appId string) string {
	if v, ok := dscSessions.show[appId]; ok && v != "" {
		return v
	}
	return appId
}

func GetClient(ctx context.Context, appId string) *discordgo.Session {
	if v, ok := dscSessions.clients.Load(appId); ok {
		return v.(*discordgo.Session)
	}
	return nil
}

func GetAppConfig(ctx context.Context, appId string) *models.DscBotConfigDf {
	var c = &models.DscBotConfigDf{}
	if v, ok := dscSessions.config[appId]; ok {
		copier.CopyWithOption(c, v, copier.Option{DeepCopy: true})
	}
	return c
}

func CloseAllSess(ctx context.Context) {
	dscSessions.clients.Range(func(key, value interface{}) bool {
		if v, ok := value.(*discordgo.Session); ok {
			err := v.Close()
			fmt.Println("discord close sess result. err:", err)
		}
		return true
	})
}
