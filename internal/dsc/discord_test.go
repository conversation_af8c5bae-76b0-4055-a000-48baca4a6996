package dsc

import (
	"context"
	"ops-ticket-api/models"
	"ops-ticket-api/utils"
	"testing"
)

var (
	testBotId     = "1293456513049821205"
	testBotShow   = "test-bot"
	testPubKey    = "6a3dfe24cf81bf46bd847c98c4478ce6c72da5fb0186b91e208b72833a6d3fdf"
	testBotToken  = "MTI5MzQ1NjUxMzA0OTgyMTIwNQ.GmfZI8.R9OjRt4Kq1ZfIdUpkXCp-Mo5FKix-rswb0TjpA"
	testGuildId   = "1293455988212371466"
	testGuildDesc = "ops-ss-test-bot1-guild"
)

func TestRun(t *testing.T) {
	t.Log("test run ...")
	testInit(t)

	// guild
	testGuildMembers(t)
	// guild member del

	//ctx := context.TODO()
	//_, err := GetClient(ctx, testBotId).ChannelMessageSend("1252533290913759284", "hello, world!!!")
	//t.Logf("ChannelMessageSend err:%v", err)
	//GetClient(ctx, testBotId).Close()
	//return
	//testGuildDel(t)
}

func testInit(t *testing.T) {
	err := AddSess(context.TODO(), testBotId, testBotShow, &models.DscBotConfigDf{
		ClientId:  testBotId,
		PublicKey: testPubKey,
		BotToken:  testBotToken,
		GuildId:   testGuildId,
		GuildDesc: testGuildDesc,
		Project:   "ss_global",
	})
	if err != nil {
		t.Fatalf("AddSess err:%v", err)
	}
}
func testGuildDel(t *testing.T) {
	client := GetClient(context.TODO(), testBotId)
	if client == nil {
		t.Fatalf("GetClient err")
	}
	err := client.GuildMemberDelete(testGuildId, "1243471877725487156") // 嗯哼
	t.Log("GuildMemberDelete err:", err)
}

// guild members
func testGuildMembers(t *testing.T) {
	client := GetClient(context.TODO(), testBotId)
	if client == nil {
		t.Fatalf("GetClient err")
	}

	//err := client.MessageReactionAdd("1250430563165147156", "1257581578843062293", "👍")
	//fmt.Println(err)
	//return
	//st, err := client.ChannelMessageSend("1256517303454138378", "hello, world!!!")
	//dump.Dump(st, err)
	//return
	members, err := client.GuildMembers(testGuildId, "", 100)
	if err != nil {
		t.Fatalf("GuildMembers err:%v", err)
	}
	// [{"guild_id":"","joined_at":"2024-06-03T11:31:53.468Z","nick":"","deaf":false,"mute":false,"avatar":"","user":{"id":"115385224119975941","email":"","username":"DiscordServers","avatar":"63194e8218b818bfca928a024563855a","locale":"","discriminator":"2383","global_name":"","token":"","verified":false,"mfa_enabled":false,"banner":"","accent_color":0,"bot":true,"public_flags":589824,"premium_type":0,"system":false,"flags":589824},"roles":["1247150765487685634"],"premium_since":null,"flags":0,"pending":false,"permissions":"0","communication_disabled_until":null},{"guild_id":"","joined_at":"2024-05-15T04:47:42.974Z","nick":"","deaf":false,"mute":false,"avatar":"","user":{"id":"830530156048285716","email":"","username":"Lofi Radio","avatar":"7650d0c9ae84e6b11edc43028b90e392","locale":"","discriminator":"1753","global_name":"","token":"","verified":false,"mfa_enabled":false,"banner":"","accent_color":0,"bot":true,"public_flags":65536,"premium_type":0,"system":false,"flags":65536},"roles":["1240163681862684745"],"premium_since":null,"flags":0,"pending":false,"permissions":"0","communication_disabled_until":null},{"guild_id":"","joined_at":"2024-06-04T10:48:44.429Z","nick":"","deaf":false,"mute":false,"avatar":"","user":{"id":"1039506541956583494","email":"","username":"mengqi.li_01355","avatar":"","locale":"","discriminator":"0","global_name":"mengqi.li","token":"","verified":false,"mfa_enabled":false,"banner":"","accent_color":0,"bot":false,"public_flags":0,"premium_type":0,"system":false,"flags":0},"roles":[],"premium_since":null,"flags":0,"pending":false,"permissions":"0","communication_disabled_until":null},{"guild_id":"","joined_at":"2024-05-15T03:16:19.259Z","nick":"","deaf":false,"mute":false,"avatar":"","user":{"id":"1045629060488892476","email":"","username":"lintest","avatar":"","locale":"","discriminator":"0","global_name":"lintest","token":"","verified":false,"mfa_enabled":false,"banner":"","accent_color":0,"bot":false,"public_flags":0,"premium_type":0,"system":false,"flags":0},"roles":[],"premium_since":null,"flags":0,"pending":false,"permissions":"0","communication_disabled_until":null},{"guild_id":"","joined_at":"2024-06-13T04:17:34.208Z","nick":"","deaf":false,"mute":false,"avatar":"","user":{"id":"1243471877725487156","email":"","username":"wwhstc_49476","avatar":"","locale":"","discriminator":"0","global_name":"嗯哼~","token":"","verified":false,"mfa_enabled":false,"banner":"","accent_color":0,"bot":false,"public_flags":0,"premium_type":0,"system":false,"flags":0},"roles":[],"premium_since":null,"flags":1,"pending":false,"permissions":"0","communication_disabled_until":null},{"guild_id":"","joined_at":"2024-06-05T03:38:00.114Z","nick":"","deaf":false,"mute":false,"avatar":"","user":{"id":"1245279065729138713","email":"","username":"zhuzhudawang_98503","avatar":"","locale":"","discriminator":"0","global_name":"猪猪大王","token":"","verified":false,"mfa_enabled":false,"banner":"","accent_color":0,"bot":false,"public_flags":0,"premium_type":0,"system":false,"flags":0},"roles":[],"premium_since":null,"flags":0,"pending":false,"permissions":"0","communication_disabled_until":null},{"guild_id":"","joined_at":"2024-06-03T08:43:54.454Z","nick":"","deaf":false,"mute":false,"avatar":"","user":{"id":"1246021276687142962","email":"","username":"test-ops-ticket","avatar":"","locale":"","discriminator":"9828","global_name":"","token":"","verified":false,"mfa_enabled":false,"banner":"","accent_color":0,"bot":true,"public_flags":0,"premium_type":0,"system":false,"flags":0},"roles":["1247108490913386550"],"premium_since":null,"flags":0,"pending":false,"permissions":"0","communication_disabled_until":null},{"guild_id":"","joined_at":"2024-06-12T12:43:24.606Z","nick":"","deaf":false,"mute":false,"avatar":"","user":{"id":"1250292748364419154","email":"","username":"test-soc-bot","avatar":"","locale":"","discriminator":"7721","global_name":"","token":"","verified":false,"mfa_enabled":false,"banner":"","accent_color":0,"bot":true,"public_flags":524288,"premium_type":0,"system":false,"flags":524288},"roles":["1250430254405910541"],"premium_since":null,"flags":0,"pending":false,"permissions":"0","communication_disabled_until":null}]
	t.Logf("members:%+v. %s", members, utils.ToJson(members))

}
