package elasticsearch

import (
	"context"
	"fmt"
	"github.com/olivere/elastic/v7"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"go.uber.org/zap"
	"ops-ticket-api/internal/code"
	"ops-ticket-api/internal/framework/xerrors"
	"strings"
)

// UpdateDsc 更新
func (es *dscEsRepo) UpdateDsc(ctx context.Context, channelId string, doc map[string]interface{}) error {
	defer func() {
		if err := recover(); err != nil {
			logger.Errorf(ctx, "recover err:%v", err)
		}
	}()
	resp, err := es.dscEsClient.GetClient().Update().Index(es.idx).Id(channelId).Doc(doc).Refresh("true").Do(ctx)
	if err != nil {
		logger.Error(ctx, "[UpdateDsc] Sync dsc to es err", zap.String("channel_id", channelId), zap.Any("doc", doc), zap.String("err", err.Error()))
		return xerrors.New("EventUpdateFailLog", code.DbError)
	}
	if resp.Result != "updated" {
		logger.Error(ctx, "[UpdateDsc] Sync dsc to es err", zap.String("channel_id", channelId), zap.String("result", resp.Result))
	}
	return nil
}

func (es *dscEsRepo) ClearRemark(ctx context.Context, channelId string, updatedAt uint64) error {
	script := elastic.NewScriptInline("ctx._source.portrait_remark = ''; ctx._source.updated_at = params.updated_at").
		Param("updated_at", updatedAt).Lang("painless")
	resp, err := es.dscEsClient.GetClient().Update().Index(es.idx).Id(channelId).
		Script(script).
		Refresh("true").
		Do(ctx)
	if err != nil {
		logger.Error(ctx, "[ClearRemark] Sync dsc to es err", zap.String("channel_id", channelId), zap.String("err", err.Error()))
		return xerrors.New("EventUpdateFailLog", code.DbError)
	}
	if resp.Result != "updated" {
		logger.Error(ctx, "[ClearRemark] Sync dsc to es err", zap.String("channel_id", channelId), zap.String("result", resp.Result))
	}
	return nil
}

func (es *dscEsRepo) UpdateMaintainConfig(ctx context.Context, channelId string, scripts []string, doc map[string]interface{}) error {
	defer func() {
		if err := recover(); err != nil {
			logger.Errorf(ctx, "recover err:%v", err)
		}
	}()
	resp, err := es.dscEsClient.GetClient().Update().Index(es.idx).Id(channelId).
		Script(elastic.NewScript(strings.Join(scripts, ";")).Params(doc)).Refresh("true").Do(ctx)
	if err != nil {
		logger.Error(ctx, "[UpdateMaintainConfig] Sync maintain config to es err", zap.String("channelId", channelId), zap.Any("doc", doc), zap.String("err", err.Error()))
		return xerrors.New("EventUpdateFailLog", code.DbError)
	}
	if resp.Result != "updated" {
		logger.Error(ctx, "[UpdateMaintainConfig] Sync maintain config to es err", zap.String("channelId", channelId), zap.String("result", resp.Result))
	}
	logger.Info(ctx, "[UpdateMaintainConfig] Sync maintain config to es success", zap.String("channelId", channelId), zap.String("result", resp.Result))
	return nil
}

func (es *dscEsRepo) UpdateTag(ctx context.Context, channelIds []string, tags []int64) error {
	defer func() {
		if err := recover(); err != nil {
			logger.Errorf(ctx, "recover err:%v", err)
		}
	}()
	bulkRequest := es.dscEsClient.GetClient().Bulk()
	for _, channelId := range channelIds {
		request := elastic.NewBulkUpdateRequest().Index(es.idx).Id(channelId)
		script := "ctx._source.new_label = params.tags;"
		params := map[string]interface{}{
			"tags": tags,
		}
		request.Script(elastic.NewScript(script).Params(params))
		bulkRequest.Add(request)
	}
	bulkResponse, err := bulkRequest.Do(ctx)
	if err != nil {
		logger.Error(ctx, "[UpdateTag] Sync dsc to es err", zap.Strings("channelId", channelIds), zap.Int64s("tag", tags), zap.String("err", err.Error()))
		return err
	}
	if bulkResponse == nil || bulkResponse.Errors {
		logger.Error(ctx, "[UpdateTag] Sync dsc to es err", zap.Strings("channelId", channelIds), zap.Int64s("tag", tags), zap.Any("resp", bulkResponse))
		if bulkResponse == nil {
			return fmt.Errorf("sync dsc tags to es err:%v", "bulkResponse nil")
		}
		return fmt.Errorf("sync dsc tags to es err:%v", bulkResponse.Errors)
	}
	return nil
}

func (es *dscEsRepo) DeleteTag(ctx context.Context, channelIds []string, tags []uint32) error {
	defer func() {
		if err := recover(); err != nil {
			logger.Errorf(ctx, "recover err:%v", err)
		}
	}()
	bulkRequest := es.dscEsClient.GetClient().Bulk()

	for _, channelId := range channelIds {
		request := elastic.NewBulkUpdateRequest().Index(es.idx).Id(channelId)
		// 脚本: 从 new_label 字段中移除传入的 tags
		script := `
			if (ctx._source.new_label != null) {
				ctx._source.new_label.removeIf(tag -> params.tags.contains(tag));
			}
		`
		params := map[string]interface{}{
			"tags": tags,
		}
		request.Script(elastic.NewScript(script).Params(params))
		bulkRequest.Add(request)
	}
	bulkResponse, err := bulkRequest.Do(ctx)
	if err != nil {
		logger.Error(ctx, "[UpdateTag] Sync dsc to es err", zap.Strings("channelId", channelIds), zap.Uint32s("tag", tags), zap.String("err", err.Error()))
		return err
	}
	if bulkResponse == nil || bulkResponse.Errors {
		logger.Error(ctx, "[UpdateTag] Sync dsc to es err", zap.Strings("channelId", channelIds), zap.Uint32s("tag", tags), zap.Any("resp", bulkResponse))
		if bulkResponse == nil {
			return fmt.Errorf("sync dsc tags to es err:%v", "bulkResponse nil")
		}
		return fmt.Errorf("sync dsc tags to es err:%v", bulkResponse.Errors)
	}
	return nil
}
