// Copyright 2021 funplus Authors. All Rights Reserved.
// @Description:
// @Author: Darcy
// @Date: 2022/5/17 16:22

package elasticsearch

import (
	"context"
	"github.com/avast/retry-go"
	"github.com/olivere/elastic/v7"
	"github.com/spf13/cast"
	"github.com/spf13/viper"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"gitlab-ee.funplus.io/ops-tools/compkg/x/stores/es/client"
	"go.uber.org/zap"
	"ops-ticket-api/internal/code"
	"ops-ticket-api/internal/framework/stores/es"
	"ops-ticket-api/internal/framework/xerrors"
	"ops-ticket-api/models"
	"ops-ticket-api/pkg/workflow"
	"ops-ticket-api/proto/pb"
	"ops-ticket-api/utils"
	"strings"
	"sync"
)

var (
	onceEs               sync.Once
	esCli                *ticketSyncSvc
	DefaultTicketSyncSvc = newTicketSyncSvc()
)

type ticketSyncSvc struct {
	ticketEs client.Client
}

func newTicketSyncSvc() *ticketSyncSvc {
	onceEs.Do(func() {
		ctx := context.Background()
		cli, err := es.GetCli(ctx)
		if err != nil {
			return
		}
		esCli = &ticketSyncSvc{
			ticketEs: cli,
		}
	})
	return esCli
}

func (es *ticketSyncSvc) GetTicketEs(ctx context.Context) client.Client {
	return es.ticketEs
}

func (es *ticketSyncSvc) CreateTicket(ctx context.Context, ticket *models.FpOpsTickets) {
	defer func() {
		if err := recover(); err != nil {
			logger.Errorf(ctx, "recover err:%v", err)
		}
	}()
	doc := models.TicketAssignDoc(ticket)
	err := retry.Do(func() error {
		if err := es.ticketEs.Add(ctx, viper.GetString("es.index.ticket"), cast.ToString(ticket.TicketID), doc); err != nil {
			logger.Error(ctx, "[Create] Sync ticket to es err", zap.Uint64("ticket_id", ticket.TicketID), zap.String("err", err.Error()))
			return err
		}
		return nil
	}, retry.Attempts(3), retry.LastErrorOnly(true))

	if err != nil {
		logger.Error(ctx, "[Create] Failed to sync ticket to es after retries", zap.Uint64("ticket_id", ticket.TicketID), zap.String("err", err.Error()))
	}
	return
}

// UpdateTicket 更新
func (es *ticketSyncSvc) UpdateTicket(ctx context.Context, ticketId uint64, doc map[string]interface{}) error {
	defer func() {
		if err := recover(); err != nil {
			logger.Errorf(ctx, "recover err:%v", err)
		}
	}()
	if _, ok := doc["conversion_node"]; ok {
		doc["stage"] = doc["conversion_node"]
		delete(doc, "conversion_node")
	}
	delete(doc, "audit")
	resp, err := es.ticketEs.GetClient().Update().Index(viper.GetString("es.index.ticket")).Id(cast.ToString(ticketId)).Doc(doc).Refresh("true").Do(ctx)
	if err != nil {
		logger.Error(ctx, "[UpdateTicket]Sync ticket to es err", zap.Uint64("ticket_id", ticketId), zap.Any("doc", doc), zap.String("err", err.Error()))
		return err
	}
	if resp.Result != "updated" && resp.Result != "noop" {
		logger.Error(ctx, "[UpdateTicket]Sync ticket to es err", zap.Uint64("ticket_id", ticketId), zap.String("result", resp.Result))
	}
	return nil
}

// Evaluation 玩家评价
func (es *ticketSyncSvc) Evaluation(ctx context.Context, ticketId uint64, csi, nps uint32, comment string, now uint64) error {
	defer func() {
		if err := recover(); err != nil {
			logger.Errorf(ctx, "recover err:%v", err)
		}
	}()
	doc := map[string]interface{}{
		"csi":         csi,
		"nps":         nps,
		"evaluate_at": now,
		"comment":     comment,
	}
	if err := es.ticketEs.Update(ctx, viper.GetString("es.index.ticket"), cast.ToString(ticketId), doc); err != nil {
		logger.Error(ctx, "[Evaluation]Sync ticket to es err", zap.Uint64("ticket_id", ticketId), zap.Any("doc", doc), zap.String("err", err.Error()))
		return err
	}
	return nil
}

// RefillStatus 补填状态
func (es *ticketSyncSvc) RefillStatus(ctx context.Context, ticketId uint64, fromStage, toStage uint32) {
	defer func() {
		if err := recover(); err != nil {
			logger.Errorf(ctx, "recover err:%v", err)
		}
	}()
	doc := map[string]interface{}{
		"acceptor": "",
		"stage":    toStage,
		"refill":   uint32(workflow.RefillState[pb.TkStage(toStage)]),
	}
	scripts := []string{
		"ctx._source.stage=params.stage",
		"ctx._source.acceptor=params.acceptor",
		"if(!ctx._source.refill.contains(params.refill)){ctx._source.refill.add(params.refill)}",
	}
	resp, err := es.ticketEs.GetClient().Update().Index(viper.GetString("es.index.ticket")).Id(cast.ToString(ticketId)).
		Script(elastic.NewScript(strings.Join(scripts, ";")).Params(doc)).Refresh("true").Do(ctx)
	if err != nil {
		logger.Error(ctx, "[RefillStatus]Sync ticket to es err", zap.Uint64("ticket_id", ticketId), zap.Any("doc", doc), zap.String("err", err.Error()))
		return
	}
	if resp.Result != "updated" {
		logger.Error(ctx, "[RefillStatus]Sync ticket to es err", zap.Uint64("ticket_id", ticketId), zap.String("result", resp.Result))
	}
}

func (es *ticketSyncSvc) UpdateTicketByScripts(ctx context.Context, ticketId uint64, scripts []string, doc map[string]interface{}) error {
	defer func() {
		if err := recover(); err != nil {
			logger.Errorf(ctx, "recover err:%v", err)
		}
	}()
	resp, err := es.ticketEs.GetClient().Update().Index(viper.GetString("es.index.ticket")).Id(cast.ToString(ticketId)).
		Script(elastic.NewScript(strings.Join(scripts, ";")).Params(doc)).Refresh("true").Do(ctx)
	if err != nil {
		logger.Error(ctx, "[UpdateTicketByScripts] Sync ticket to es err", zap.Uint64("ticketId", ticketId), zap.Any("doc", doc), zap.String("err", err.Error()))
		return xerrors.New("EventUpdateFailLog", code.DbError)
	}
	if resp.Result != "updated" {
		logger.Error(ctx, "[UpdateTicketByScripts] Sync ticket to es err", zap.Uint64("ticketId", ticketId), zap.String("result", resp.Result))
	}
	logger.Info(ctx, "[UpdateTicketByScripts] Sync ticket to es success", zap.Uint64("ticketId", ticketId), zap.String("result", resp.Result))
	return nil
}

// // AddRemark 添加备注
//
//	func (es *ticketSyncSvc) AddRemark(ctx context.Context, ticketId uint64, remark string) {
//		defer func() {
//			if err := recover(); err != nil {
//				logger.Errorf(ctx, "recover err:%v", err)
//			}
//		}()
//		resp, err := es.ticketEs.GetClient().Update().
//			Index(viper.GetString("es.index.ticket")).
//			Id(cast.ToString(ticketId)).
//			Script(elastic.NewScript("ctx._source.remarks.add(params.remarks)").Params(
//				map[string]interface{}{
//					"remarks": remark,
//				})).
//			Do(ctx)
//		if err != nil {
//			logger.Error(ctx, "[AddRemark]Sync ticket to es err", zap.Uint64("ticket_id", ticketId), zap.String("err", err.Error()))
//			return
//		}
//		if resp.Result != "updated" {
//			logger.Error(ctx, "[AddRemark]Sync ticket to es err", zap.Uint64("ticket_id", ticketId), zap.String("result", resp.Result))
//		}
//		return
//	}
//
// // AddReply 回复
//
//	func (es *ticketSyncSvc) AddReply(ctx context.Context, ticketId uint64, reply, account string, cnvNode, role uint8) error {
//		defer func() {
//			if err := recover(); err != nil {
//				logger.Errorf(ctx, "recover err:%v", err)
//			}
//		}()
//		resp, err := es.ticketEs.GetClient().Update().
//			Index(viper.GetString("es.index.ticket")).
//			Id(cast.ToString(ticketId)).
//			Script(elastic.NewScript("ctx._source.replies.add(params.replies)").Params(
//				map[string]interface{}{
//					"replies": models.ReplyDoc{
//						ReplyRole:    role,
//						ReplyContent: reply,
//						Op:           cnvNode,
//						Operator:     account,
//					},
//				})).
//			Do(ctx)
//		if err != nil {
//			logger.Error(ctx, "[AddReply]Sync ticket to es err", zap.Uint64("ticket_id", ticketId), zap.String("err", err.Error()))
//			return xerrors.New("EventUpdateFailLog", code.DbError)
//		}
//		if resp.Result != "updated" {
//			logger.Error(ctx, "[AddReply]Sync ticket to es err", zap.Uint64("ticket_id", ticketId), zap.String("result", resp.Result))
//		}
//		return nil
//	}
//
// AddHist 回复
func (es *ticketSyncSvc) AddHist(ctx context.Context, ticketId uint64, hist *models.FpOpsTicketsHistory) {
	fun := "ticketSyncSvc.AddHist"
	defer func() {
		if err := recover(); err != nil {
			logger.Errorf(ctx, "recover err:%v", err)
		}
	}()
	scripts := make([]string, 0)
	params := make(map[string]interface{})
	query := es.ticketEs.GetClient().Update().Index(viper.GetString("es.index.ticket")).Id(cast.ToString(ticketId))
	scripts = append(scripts, "ctx._source.histories.add(params.histories)")
	params["histories"] = models.HistoryDoc{
		ID:        hist.ID,
		Acceptor:  hist.Acceptor,
		Operate:   hist.Operate,
		OpDetail:  hist.OpDetail,
		OpRole:    hist.OpRole,
		OpObject:  hist.OpObject,
		Operator:  hist.Operator,
		CreatedAt: hist.CreatedAt,
	}
	resp, err := query.Script(elastic.NewScript(strings.Join(scripts, ";")).Params(params)).Refresh("true").Do(ctx)
	if err != nil {
		logger.Error(ctx, "[AddHist]Sync ticket to es err", zap.String("fun", fun), zap.Uint64("ticket_id", ticketId), zap.String("err", err.Error()))
		return
	}
	if resp.Result != "updated" {
		logger.Error(ctx, "[AddHist]Sync ticket to es err", zap.String("fun", fun), zap.Uint64("ticket_id", ticketId), zap.String("result", resp.Result))
	}
	return
}

// DeleteTag 删除标签
func (es *ticketSyncSvc) DeleteTag(ctx context.Context, ticketsId uint64, tags []uint32) {
	defer func() {
		if err := recover(); err != nil {
			logger.Errorf(ctx, "recover err:%v", err)
		}
	}()
	bulkRequest := es.ticketEs.GetClient().Bulk()
	for _, tag := range tags {
		request := elastic.NewBulkUpdateRequest().Index(viper.GetString("es.index.ticket")).Id(cast.ToString(ticketsId))
		request.Script(elastic.NewScript("ctx._source.tags.removeIf(l->l.equals(params.tag))").Param("tag", tag))
		bulkRequest.Add(request)
	}

	bulkResponse, err := bulkRequest.Do(ctx)
	if err != nil {
		logger.Error(ctx, "[DeleteTag]Sync ticket to es err", zap.Uint64("ticketsId", ticketsId), zap.Uint32s("tags", tags), zap.String("err", err.Error()))
		return
	}
	if bulkResponse == nil || bulkResponse.Errors {
		logger.Error(ctx, "[DeleteTag]Sync ticket to es err", zap.Uint64("ticketsId", ticketsId), zap.Uint32s("tags", tags), zap.Any("resp", bulkResponse))
	}
	return
}

// AddTag 添加标签

func (es *ticketSyncSvc) AddTag(ctx context.Context, ticketId uint64, tags []uint32) {
	defer func() {
		if err := recover(); err != nil {
			logger.Errorf(ctx, "recover err:%v", err)
		}
	}()
	bulkRequest := es.ticketEs.GetClient().Bulk()

	request := elastic.NewBulkUpdateRequest().Index(viper.GetString("es.index.ticket")).Id(cast.ToString(ticketId))
	var script string
	for _, tag := range tags {
		script += "ctx._source.tags.add(" + cast.ToString(tag) + ");"
	}
	if script != "" {
		request.Script(elastic.NewScript(script))
	}
	bulkRequest.Add(request)

	bulkResponse, err := bulkRequest.Do(ctx)
	if err != nil {
		logger.Error(ctx, "[AddTag]Sync ticket to es err", zap.Uint64("ticketsId", ticketId), zap.Uint32s("tags", tags), zap.String("err", err.Error()))
		return
	}
	if bulkResponse == nil || bulkResponse.Errors {
		logger.Error(ctx, "[AddTag]Sync ticket to es err", zap.Uint64("ticketsId", ticketId), zap.Uint32s("tags", tags), zap.Any("resp", bulkResponse))
	}
	return
}

// AddReopen 重开
func (es *ticketSyncSvc) AddReopen(ctx context.Context, ticketId uint64, hist *models.FpOpsTicketsHistory, reopenHist *models.FpOpsTicketsReopen) error {
	defer func() {
		if err := recover(); err != nil {
			logger.Errorf(ctx, "[AddReopen] recover err:%v", err)
		}
	}()
	params := map[string]interface{}{
		"histories": models.HistoryDoc{
			ID:        hist.ID,
			Acceptor:  hist.Acceptor,
			Operate:   hist.Operate,
			OpDetail:  hist.OpDetail,
			OpRole:    hist.OpRole,
			OpObject:  hist.OpObject,
			Operator:  hist.Operator,
			CreatedAt: hist.CreatedAt},
		//"acceptor":           "",
		"conversion_node":    pb.TkStage_TkStageAgentReopen,
		"status":             pb.TkStatus_TkStatusUntreated,
		"closed":             pb.TkClosedRole_TkClosedNone,
		"sort_wait_start_at": utils.NowTimestamp(),
		"closed_at":          0,
		"reopen_num":         reopenHist.Num,
	}

	query := es.ticketEs.GetClient().Update().Index(viper.GetString("es.index.ticket")).Id(cast.ToString(ticketId))
	scripts := []string{
		"ctx._source.histories.add(params.histories)",
		//"ctx._source.acceptor=params.acceptor",
		"ctx._source.stage=params.conversion_node",
		"ctx._source.status=params.status",
		"ctx._source.closed=params.closed",
		"ctx._source.closed_at=params.closed_at",
		"ctx._source.reopen_num=params.reopen_num",
		"ctx._source.sort_wait_start_at=params.sort_wait_start_at",
	}
	if reopenHist.Num == 1 {
		//scripts = append(scripts, "ctx._source.labels.add(params.labels)", "ctx._source.reopen_hist=params.reopen_hist")
		scripts = append(scripts, "ctx._source.reopen_hist=params.reopen_hist")
		//params["labels"] = uint32(code.LabelTicketVipReopen)
		params["reopen_hist"] = []models.FpOpsTicketsReopen{*reopenHist}
	} else {
		scripts = append(scripts, "ctx._source.reopen_hist.add(params.reopen_hist)")
		params["reopen_hist"] = *reopenHist
	}
	resp, err := query.Script(elastic.NewScript(strings.Join(scripts, ";")).Params(params)).Refresh("true").Do(ctx)
	if err != nil {
		logger.Error(ctx, "[AddReopen]Sync ticket to es err", zap.Uint64("ticket_id", ticketId), zap.String("err", err.Error()))
		return err
	}
	if resp.Result != "updated" {
		logger.Error(ctx, "[AddReopen]Sync ticket to es failed", zap.Uint64("ticket_id", ticketId), zap.String("result", resp.Result))
	}
	return nil
}

// AddCommu 增加对话记录
func (es *ticketSyncSvc) AddCommu(ctx context.Context, ticketId uint64, tkCommu *models.FpOpsTicketsCommu) error {
	defer func() {
		if err := recover(); err != nil {
			logger.Errorf(ctx, "recover err:%v", err)
		}
	}()
	resp, err := es.ticketEs.GetClient().Update().
		Index(viper.GetString("es.index.ticket")).
		Id(cast.ToString(ticketId)).
		Script(elastic.NewScript("ctx._source.commus.add(params.commus)").Params(
			map[string]interface{}{
				"commus": models.CommuDoc{
					FromRole:  tkCommu.FromRole,
					CommuType: tkCommu.CommuType,
					OpType:    tkCommu.OpType,
					Detail:    tkCommu.Detail,
					Operator:  tkCommu.Operator,
					CreatedAt: tkCommu.CreatedAt,
				},
			})).
		Do(ctx)
	if err != nil {
		logger.Error(ctx, "[AddCommu]Sync ticket to es err", zap.Uint64("ticket_id", ticketId), zap.String("err", err.Error()))
		return xerrors.New("EventUpdateFailLog", code.DbError)
	}
	if resp.Result != "updated" {
		logger.Error(ctx, "[AddCommu]Sync ticket to es err", zap.Uint64("ticket_id", ticketId), zap.String("result", resp.Result))
	}
	return nil
}
