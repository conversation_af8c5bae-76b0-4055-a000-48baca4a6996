// Copyright 2021 funplus Authors. All Rights Reserved.
// @Description:
// @Author: Darcy
// @Date: 2022/6/8 15:48

package elasticsearch

import (
	"context"
	"encoding/json"
	"github.com/olivere/elastic/v7"
	"github.com/spf13/viper"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"gitlab-ee.funplus.io/ops-tools/compkg/x/stores/es/client"
	"go.uber.org/zap"
	"io"
	"ops-ticket-api/internal/code"
	"ops-ticket-api/internal/framework/stores/es"
	"ops-ticket-api/internal/framework/xerrors"
	"ops-ticket-api/models"
	"ops-ticket-api/proto/pb"
	"ops-ticket-api/utils"
)

var (
	DefaultTicketEsRepo     = newTicketRepo()
	scrollKeepAliveInterval = "5m"
	pageSize                = 1000
)

type TicketEsRepo struct {
	idx string
}

// NewTicketRepo init
func newTicketRepo() *TicketEsRepo {
	return &TicketEsRepo{
		idx: viper.GetString("es.index.ticket"),
	}
}

func (repo *TicketEsRepo) GetTicketInfoById(ctx context.Context, ticketId uint64, opts ...Option) ([]byte, error) {
	query := elastic.NewBoolQuery()
	if len(opts) > 0 {
		optList := newOptions(opts...)
		if len(optList.filter) > 0 {
			query.Filter(optList.filter...)
		}
		if len(optList.not) > 0 {
			query.Filter(elastic.NewBoolQuery().MustNot(optList.not...))
		}
		if len(optList.should) > 0 {
			query.Filter(elastic.NewBoolQuery().Should(optList.should...))
		}
		for _, fs := range optList.filterShould {
			query.Filter(elastic.NewBoolQuery().Should(fs...))
		}
		for _, fn := range optList.filterNot {
			query.Filter(elastic.NewBoolQuery().MustNot(fn...))
		}
	}
	query.Filter(elastic.NewTermsQuery("ticket_id", ticketId))

	esCli, err := es.GetCli(ctx)
	if err != nil {
		return nil, err
	}
	search, err := esCli.Search(ctx, &client.SearchParameters{
		Index: repo.idx,
		Query: query,
	})
	if err != nil {
		return nil, err
	}
	if search.Hits.TotalHits.Value == 0 {
		return nil, xerrors.New(code.StatusText(code.NotFound), code.NotFound)
	}
	return search.Hits.Hits[0].Source.MarshalJSON()
}

//
//func (repo *TicketEsRepo) GetTicketInfo(ctx context.Context, ticketId uint64, opts ...Option) (*models.TicketsDoc, error) {
//	data, err := repo.GetTicketInfoById(ctx, ticketId, opts...)
//	if err != nil {
//		return nil, err
//	}
//	ticketInfo := &models.TicketsDoc{}
//	if err := json.Unmarshal(data, &ticketInfo); err != nil {
//		logger.Error(ctx, "json unmarshal err", zap.Uint64("ticketId", ticketId), zap.String("err", err.Error()))
//		return nil, err
//	}
//	return ticketInfo, nil
//}
//
//func (repo *TicketEsRepo) GetTicketUserInfo(ctx context.Context, ticketId uint64, opts ...Option) (*pb.UserInfoResp, error) {
//	data, err := repo.GetTicketInfo(ctx, ticketId, opts...)
//	if err != nil {
//		return nil, err
//	}
//	var vipCrm string
//	if data.VipCrm >= 0 {
//		vipCrm = cast.ToString(data.VipCrm)
//	}
//	userInfo := &pb.UserInfoResp{
//		Fpid:        data.Fpid,
//		Uid:         data.UID,
//		Nickname:    data.Nickname,
//		Vip:         uint32(data.Vip),
//		Recharge:    cast.ToString(data.Recharge),
//		Lang:        data.Lang,
//		Project:     data.Project,
//		Channel:     data.Channel,
//		SubChannel:  data.SubChannel,
//		Sid:         data.Sid,
//		Sdk:         data.SdkVersion,
//		AppVersion:  data.AppVersion,
//		Os:          data.Os,
//		OsVersion:   data.OsVersion,
//		DeviceType:  data.DeviceType,
//		RomGb:       float64(data.RomGb),
//		RemainRom:   float64(data.RemainRom),
//		RamMb:       float64(data.RAMMb),
//		NetworkInfo: data.NetworkInfo,
//		Country:     data.Country,
//		Gm:          cast.ToString(data.GmID),
//		AccountId:   data.AccountID,
//		RoleId:      data.RoleID,
//		VipCrm:      vipCrm,
//	}
//
//	return userInfo, nil
//}
//
//func (repo *TicketEsRepo) GetTicketField(ctx context.Context, ticketId uint64) (*pb.TicketFiledResp, error) {
//	data, err := repo.GetTicketInfo(ctx, ticketId)
//	if err != nil {
//		return nil, err
//	}
//	field := &pb.TicketFiledResp{
//		Filed: data.Field,
//	}
//	return field, nil
//}

func (repo *TicketEsRepo) GetTicketCount(ctx context.Context, req *pb.TicketCountReq) (int64, error) {
	fields := []string{"ticket_id", "project", "origin", "lang", "account_id",
		"vip", "nickname", "cat_id", "priority", "stage", "status",
		"creator", "acceptor", "csi", "comment", "closed_at", "created_at"}

	query := elastic.NewBoolQuery()
	if len(req.Project) > 0 {
		query = query.Filter(elastic.NewTermsQuery("project", utils.ToInterface(req.Project...)...))
	}
	if len(req.Stage) > 0 {
		query = query.Filter(elastic.NewTermsQuery("stage", utils.ToInterface(req.Stage...)...))
	}
	if len(req.Acceptor) > 0 {
		query = query.Filter(elastic.NewTermsQuery("acceptor", utils.ToInterface(req.Acceptor...)...))
	}
	if len(req.Lang) > 0 {
		query = query.Filter(elastic.NewTermsQuery("lang", utils.ToInterface(req.Lang...)...))
	}
	if len(req.Vip) > 0 {
		query = query.Filter(elastic.NewTermsQuery("vip", utils.ToInterface(req.Vip...)...))
	}
	if req.Priority > 0 {
		query = query.Filter(elastic.NewTermQuery("priority", req.Priority))
	}

	cli, err := es.GetCli(ctx)
	if err != nil {
		return 0, err
	}
	// Debugging: Print the query to be executed
	//querySource, err := query.Source()
	//if err != nil {
	//	logger.Error(ctx, "failed to get query source", zap.String("err", err.Error()))
	//	return 0, err
	//}
	//queryJSON, err := json.Marshal(querySource)
	//if err != nil {
	//	logger.Error(ctx, "failed to marshal query to JSON", zap.String("err", err.Error()))
	//	return 0, err
	//}
	//logger.Info(ctx, "executing query", zap.String("query", string(queryJSON)))
	ret, err := cli.GetClient().Search(repo.idx).
		Query(query).
		FetchSourceContext(elastic.NewFetchSourceContext(true).Include(fields...)).
		TrackTotalHits(true).
		SortBy(elastic.NewFieldSort("ticket_id").Desc()).
		Do(ctx)

	if err != nil {
		logger.Warn(ctx, "Elasticsearch query error", zap.Error(err))
		return 0, err
	}
	return ret.Hits.TotalHits.Value, nil
}

func (repo *TicketEsRepo) GetTicketPool(ctx context.Context, page, pageSize int, fieldsExt []string, sort []elastic.Sorter, options ...Option) ([]*elastic.SearchHit, int64, error) {
	fields := []string{"ticket_id", "project", "origin", "lang", "account_id", "vip", "nickname", "recharge",
		"cat_id", "priority", "stage", "status", "creator", "acceptor", "csi", "nps", "evaluate_at", "comment",
		"closed_at", "created_at", "first_reply_at", "sort_wait_start_at", "system_tags", "field"}
	if len(fieldsExt) > 0 {
		fields = append(fields, fieldsExt...)
	}

	opts := newOptions(options...)
	query := elastic.NewBoolQuery()
	if len(opts.filter) > 0 {
		query.Filter(opts.filter...)
	}
	if len(opts.not) > 0 {
		query.Filter(elastic.NewBoolQuery().MustNot(opts.not...))
	}
	if len(opts.should) > 0 {
		query.Filter(elastic.NewBoolQuery().Should(opts.should...))
	}
	for _, fs := range opts.filterShould {
		query.Filter(elastic.NewBoolQuery().Should(fs...))
	}
	for _, fn := range opts.filterNot {
		query.Filter(elastic.NewBoolQuery().MustNot(fn...))
	}
	if sort == nil {
		sort = []elastic.Sorter{elastic.NewFieldSort("ticket_id").Desc()}
	}
	cli, err := es.GetCli(ctx)
	if err != nil {
		return nil, 0, err
	}
	// Debugging: Print the query to be executed
	querySource, err := query.Source()
	if err != nil {
		logger.Error(ctx, "failed to get query source", zap.String("err", err.Error()))
		return nil, 0, err
	}
	queryJSON, err := json.Marshal(querySource)
	if err != nil {
		logger.Error(ctx, "failed to marshal query to JSON", zap.String("err", err.Error()))
		return nil, 0, err
	}
	logger.Info(ctx, "executing query", zap.String("query", string(queryJSON)))
	ret, err := cli.GetClient().Search(repo.idx).Query(query).
		FetchSourceContext(elastic.NewFetchSourceContext(true).Include(fields...)).
		TrackTotalHits(true).
		From((page - 1) * pageSize).
		Size(pageSize).
		SortBy(sort...).
		Do(context.TODO())

	if err != nil {
		logger.Warn(ctx, "get ticket info err", zap.Any("filter", opts), zap.String("err", err.Error()))
		return nil, 0, err
	}
	//s, err := query.Source()
	//fmt.Println(utils.ToJson(s), err)
	//dump.Dump(ret)
	return ret.Hits.Hits, ret.Hits.TotalHits.Value, nil
}

func (repo *TicketEsRepo) GetTicketPoolScroll(ctx context.Context, options ...Option) (<-chan *models.TicketsDoc, error) {
	opts := newOptions(options...)
	query := elastic.NewBoolQuery()
	if len(opts.filter) > 0 {
		query.Filter(opts.filter...)
	}
	if len(opts.not) > 0 {
		query.Filter(elastic.NewBoolQuery().MustNot(opts.not...))
	}
	if len(opts.should) > 0 {
		query.Filter(elastic.NewBoolQuery().Should(opts.should...))
	}
	for _, fs := range opts.filterShould {
		query.Filter(elastic.NewBoolQuery().Should(fs...))
	}
	for _, fn := range opts.filterNot {
		query.Filter(elastic.NewBoolQuery().MustNot(fn...))
	}
	cli, err := es.GetCli(ctx)
	if err != nil {
		return nil, err
	}
	searchResult, scrollErr := cli.OpenScroll(ctx, &client.SearchParameters{
		Index:    repo.idx,
		Query:    query,
		PageSize: pageSize,
		Sorter:   []elastic.Sorter{elastic.NewFieldSort("ticket_id").Desc()},
	}, scrollKeepAliveInterval)
	if err != nil {
		return nil, err
	}
	ticketCh := make(chan *models.TicketsDoc, 256)
	go func() {
		defer func() {
			if err := recover(); err != nil {
				logger.Errorf(ctx, "recover err:%v", err)
			}
			if searchResult != nil {
				if err := cli.CloseScroll(ctx, searchResult.ScrollId); err != nil {
					logger.Error(ctx, "scroll close err", zap.String("err", err.Error()))
				}
			}
			close(ticketCh)
		}()
		var num int
		for {
			select {
			case <-ctx.Done():
				return
			default:
				if searchResult.Hits != nil {
					for _, hit := range searchResult.Hits.Hits {
						ticket := &models.TicketsDoc{}
						source, err := hit.Source.MarshalJSON()
						if err != nil {
							logger.Error(ctx, "json marshal err", zap.String("err", err.Error()))
						}
						if err := json.Unmarshal(source, &ticket); err != nil {
							logger.Error(ctx, "json unmarshal err", zap.String("err", err.Error()))
							return
						}
						ticketCh <- ticket
						num++
					}
					if len(searchResult.Hits.Hits) < pageSize {
						return
					}
					if num >= 30*pageSize { // 做多获取 30页数据
						return
					}
				}
				searchResult, scrollErr = cli.Scroll(ctx, searchResult.ScrollId, scrollKeepAliveInterval)
				if scrollErr == io.EOF {
					return
				}
			}
		}
	}()
	return ticketCh, nil
}

func (repo *TicketEsRepo) UpdateByQuery(ctx context.Context, script *elastic.Script, options ...Option) (int64, error) {
	opts := newOptions(options...)
	query := elastic.NewBoolQuery()
	if len(opts.filter) > 0 {
		query.Filter(opts.filter...)
	}
	if len(opts.not) > 0 {
		query.Filter(elastic.NewBoolQuery().MustNot(opts.not...))
	}
	if len(opts.should) > 0 {
		query.Filter(elastic.NewBoolQuery().Should(opts.should...))
	}
	for _, fs := range opts.filterShould {
		query.Filter(elastic.NewBoolQuery().Should(fs...))
	}
	for _, fn := range opts.filterNot {
		query.Filter(elastic.NewBoolQuery().MustNot(fn...))
	}
	cli, err := es.GetCli(ctx)
	if err != nil {
		return 0, err
	}
	rsp, err := cli.GetClient().UpdateByQuery(repo.idx).Query(query).Script(script).Refresh("true").Do(ctx)
	if err != nil {
		return 0, err
	}
	return rsp.Updated, nil
}

func (repo *TicketEsRepo) GetTicketTabCount(ctx context.Context, options ...Option) (int64, error) {
	opts := newOptions(options...)
	query := elastic.NewBoolQuery()
	if len(opts.filter) > 0 {
		query.Filter(opts.filter...)
	}
	if len(opts.not) > 0 {
		query.Filter(elastic.NewBoolQuery().MustNot(opts.not...))
	}
	if len(opts.should) > 0 {
		query.Filter(elastic.NewBoolQuery().Should(opts.should...))
	}
	for _, fs := range opts.filterShould {
		query.Filter(elastic.NewBoolQuery().Should(fs...))
	}
	for _, fn := range opts.filterNot {
		query.Filter(elastic.NewBoolQuery().MustNot(fn...))
	}

	cli, err := es.GetCli(ctx)
	if err != nil {
		return 0, err
	}
	// Debugging: Print the query to be executed
	querySource, err := query.Source()
	if err != nil {
		logger.Error(ctx, "failed to get query source", zap.String("err", err.Error()))
		return 0, err
	}
	queryJSON, err := json.Marshal(querySource)
	if err != nil {
		logger.Error(ctx, "failed to marshal query to JSON", zap.String("err", err.Error()))
		return 0, err
	}
	logger.Info(ctx, "executing query", zap.String("query", string(queryJSON)))
	ret, err := cli.GetClient().Search(repo.idx).Query(query).
		TrackTotalHits(true).
		Do(ctx)

	if err != nil {
		logger.Warn(ctx, "get ticket info err", zap.Any("filter", opts), zap.String("err", err.Error()))
		return 0, err
	}
	//s, err := query.Source()
	//fmt.Println(utils.ToJson(s), err)
	//dump.Dump(ret)
	return ret.Hits.TotalHits.Value, nil
}
