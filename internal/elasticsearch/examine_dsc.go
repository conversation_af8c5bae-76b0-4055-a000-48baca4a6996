package elasticsearch

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/avast/retry-go"
	"github.com/olivere/elastic/v7"
	"github.com/spf13/cast"
	"github.com/spf13/viper"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"gitlab-ee.funplus.io/ops-tools/compkg/x/stores/es/client"
	"go.uber.org/zap"
	"io"
	"ops-ticket-api/internal/framework/stores/es"
	"ops-ticket-api/models"
	"ops-ticket-api/utils"
	"os"
	"strings"
	"sync"
	"time"
)

var (
	examinDscOnceEx        sync.Once
	examineDscEsCli        *examineDscEsRepo
	DefaultExamineDscEsSvc = newExamineDscRepo()
)

type examineDscEsRepo struct {
	idx                string
	examineDscEsClient client.Client
}

// NewTicketRepo init
func newExamineDscRepo() *examineDscEsRepo {
	release := os.Getenv("release")
	if release == "cn" {
		return nil
	}

	examinDscOnceEx.Do(func() {
		ctx := context.Background()
		cli, err := es.GetExamineDscCli(ctx)
		if err != nil {
			logger.Errorf(ctx, "examinDscOnceEx GetExamineDscCli return err. err:%v", err)
			return
		}
		examineDscEsCli = &examineDscEsRepo{
			examineDscEsClient: cli,
			idx:                viper.GetString("examinees.index.examine_dsc_index"),
		}
	})
	return examineDscEsCli
}

func (es *examineDscEsRepo) CreateExamineDscOrder(ctx context.Context, order *models.FpExamineDiscordDetail) error {
	var (
		fun = "examineDscEsRepo.CreateExamineDscOrder ->"
	)
	defer func() {
		if err := recover(); err != nil {
			logger.Errorf(ctx, "%s recover err:%v", fun, err)
		}
	}()
	doc := models.ExamineDscOrderAssignDoc(order)
	scripts := []string{
		"ctx._source.id = params.id",
		"ctx._source.updated_at = params.updated_at",
	}
	param := map[string]interface{}{
		"id":         order.ID,
		"updated_at": time.Now().Unix(),
	}
	err := retry.Do(func() error {
		resp, err := es.examineDscEsClient.GetClient().Update().Index(es.idx).Id(cast.ToString(order.ID)).
			Script(elastic.NewScript(strings.Join(scripts, ";")).Params(param)).
			Upsert(doc).Refresh("true").Do(ctx)
		//fmt.Printf("order:%+v\n", order)
		//fmt.Printf("doc:%+v\n", doc)
		//dump.Dump(resp, err)
		if err != nil {
			logger.Error(ctx, fmt.Sprintf("%s [Create] Sync ExamineDscOrder Create to es err", fun), zap.Uint64("examine_dsc_id", order.ID), zap.String("err", err.Error()))
			return err
		}
		if resp.Result != "updated" && resp.Result != "created" {
			logger.Warn(ctx, fmt.Sprintf("%s [Create] Sync ExamineDscOrder Create result not updated or created", fun), zap.String("result", resp.Result),
				zap.String("examine_dsc_id", cast.ToString(doc.ExamineDscID)),
				zap.Any("doc", doc), zap.Any("param", param))
		}
		//if err := es.examineDscEsClient.Add(ctx, es.idx, cast.ToString(order.ID), doc); err != nil {
		//	logger.Error(ctx, fmt.Sprintf("%s [Create] Sync ExamineDscOrder Create to es err", fun), zap.Uint64("examine_dsc_id", order.ID), zap.String("err", err.Error()))
		//	return err
		//}
		return nil
	}, retry.Attempts(3), retry.LastErrorOnly(true))

	if err != nil {
		logger.Error(ctx, fmt.Sprintf("%s [Create] Failed to sync Sync ExamineDscOrder to es after retries", fun), zap.Uint64("examine_dsc_id", order.ID), zap.String("err", err.Error()))
	}
	return err
}

func (es *examineDscEsRepo) SaveDscOrderExamine(ctx context.Context, detail *models.FpExamineDiscordDetail) error {
	var (
		fun              = "examineDscEsRepo.SaveDscOrderExamine ->"
		scripts          []string
		param            map[string]interface{}
		fields           = make([]*models.ExamineFieldDoc, 0, len(detail.FpExamineFields))
		_finishedAt      = int64(0)
		_finalModifiedAt = int64(0)
	)
	if !detail.FinishedAt.IsZero() {
		_finishedAt = detail.FinishedAt.Unix()
	}
	if !detail.FinalModifiedAt.IsZero() {
		_finalModifiedAt = detail.FinalModifiedAt.Unix()
	}
	// set field
	for _, fl := range detail.FpExamineFields {
		fields = append(fields, models.ExamineFieldAssignDoc(ctx, fl))
	}

	scripts = []string{
		"ctx._source.examine_fields = params.examine_fields",
		"ctx._source.status = params.status",
		"ctx._source.related_account = params.related_account",
		"ctx._source.notice_account = params.notice_account",
		"ctx._source.final_result = params.final_result",
		"ctx._source.final_score = params.final_score",
		"ctx._source.final_reason = params.final_reason",
		"ctx._source.final_desc = params.final_desc",
		"ctx._source.final_result_modify_comment = params.final_result_modify_comment",
		"ctx._source.finished_at = params.finished_at",
		"ctx._source.final_modified_at = params.final_modified_at",
		"ctx._source.updated_at = params.updated_at",
	}
	param = map[string]interface{}{
		"examine_fields":              fields,
		"status":                      detail.Status,
		"related_account":             utils.JsonToStrSlice(detail.RelatedAccount),
		"notice_account":              utils.JsonToStrSlice(detail.NoticeAccount),
		"final_result":                detail.FinalResult,
		"final_score":                 detail.FinalScore,
		"final_reason":                detail.FinalReason,
		"final_desc":                  detail.FinalDesc,
		"final_result_modify_comment": detail.FinalResultModifyComment,
		"finished_at":                 _finishedAt,
		"final_modified_at":           _finalModifiedAt,
		"updated_at":                  time.Now().Unix(),
	}

	err := retry.Do(func() error {
		result, _err := es.examineDscEsClient.GetClient().Update().Index(es.idx).Id(cast.ToString(detail.ID)).
			Script(elastic.NewScript(strings.Join(scripts, ";")).Params(param)).Refresh("true").Do(ctx)
		if _err != nil {
			logger.Errorf(ctx, "%s. SaveDscOrderExamine err:%v. param:%+v", fun, _err, param)
			return _err
		}

		//dump.Dump("SaveDscOrderExamine:", result, param)
		if result.Result != "updated" && result.Result != "created" {
			logger.Warn(ctx, "SaveDscOrderExamine update examine detail result not updated or created", zap.String("result", result.Result),
				zap.Uint64("examine_dsc_id", detail.ID), zap.Any("param", param), zap.String("fun", fun))
		}
		return nil
	})
	return err
}

func (es *examineDscEsRepo) GetExamineDscPool(ctx context.Context, page, pageSize int, fieldsExt []string, sort []elastic.Sorter, options ...Option) ([]*elastic.SearchHit, int64, error) {
	fun := "examineDscEsRepo.GetExamineDscPool ->"
	fields := []string{"examine_dsc_id", "task_id", "tpl_id", "project", "bot_id", "dsc_user_id", "dsc_user_name", "channel_id", "inspector", "status", "related_account",
		"gen_uid", "gen_account_id", "gen_sid", "gen_pay_all", "gen_pay_last_thirty_days", "gen_reply_type", "gen_last_login", "gen_vip_state", "gen_vip_level",
		"final_result", "created_at", "finished_at", "examine_fields"}
	if len(fieldsExt) > 0 {
		fields = append(fields, fieldsExt...)
	}

	opts := newOptions(options...)
	query := elastic.NewBoolQuery()
	if len(opts.filter) > 0 {
		query.Filter(opts.filter...)
	}
	if len(opts.not) > 0 {
		query.Filter(elastic.NewBoolQuery().MustNot(opts.not...))
	}
	if len(opts.should) > 0 {
		query.Filter(elastic.NewBoolQuery().Should(opts.should...))
	}
	for _, fs := range opts.filterShould {
		query.Filter(elastic.NewBoolQuery().Should(fs...))
	}
	for _, fn := range opts.filterNot {
		query.Filter(elastic.NewBoolQuery().MustNot(fn...))
	}
	if sort == nil {
		sort = []elastic.Sorter{elastic.NewFieldSort("examine_dsc_id").Desc()}
	}
	// Debugging: Print the query to be executed
	querySource, err := query.Source()
	if err != nil {
		logger.Error(ctx, "failed to get query source", zap.String("err", err.Error()), zap.String("fun", fun))
		return nil, 0, err
	}
	queryJSON, err := json.Marshal(querySource)
	if err != nil {
		logger.Error(ctx, "failed to marshal query to JSON", zap.String("err", err.Error()), zap.String("fun", fun))
		return nil, 0, err
	}
	logger.Info(ctx, "executing query", zap.String("query", string(queryJSON)))
	ret, err := es.examineDscEsClient.GetClient().Search(es.idx).Query(query).
		FetchSourceContext(elastic.NewFetchSourceContext(true).Include(fields...)).
		TrackTotalHits(true).
		From((page - 1) * pageSize).
		Size(pageSize).
		SortBy(sort...).
		Do(context.TODO())

	if err != nil {
		logger.Error(ctx, "get examine dsc info err", zap.Any("filter", opts), zap.String("err", err.Error()), zap.String("fun", fun))
		return nil, 0, err
	}
	return ret.Hits.Hits, ret.Hits.TotalHits.Value, nil
}

func (es *examineDscEsRepo) GetExamineDscPoolScroll(ctx context.Context, options ...Option) (<-chan *models.ExamineDscOrderDoc, error) {
	fun := "examineDscEsRepo.GetExamineDscPoolScroll ->"
	opts := newOptions(options...)
	query := elastic.NewBoolQuery()
	if len(opts.filter) > 0 {
		query.Filter(opts.filter...)
	}
	if len(opts.not) > 0 {
		query.Filter(elastic.NewBoolQuery().MustNot(opts.not...))
	}
	if len(opts.should) > 0 {
		query.Filter(elastic.NewBoolQuery().Should(opts.should...))
	}
	for _, fs := range opts.filterShould {
		query.Filter(elastic.NewBoolQuery().Should(fs...))
	}
	for _, fn := range opts.filterNot {
		query.Filter(elastic.NewBoolQuery().MustNot(fn...))
	}
	searchResult, scrollErr := es.examineDscEsClient.OpenScroll(ctx, &client.SearchParameters{
		Index:    es.idx,
		Query:    query,
		PageSize: pageSize,
		Sorter:   []elastic.Sorter{elastic.NewFieldSort("examine_dsc_id").Desc()},
	}, scrollKeepAliveInterval)
	if scrollErr != nil {
		return nil, scrollErr
	}
	examinDscCh := make(chan *models.ExamineDscOrderDoc, 256)
	go func() {
		defer func() {
			if err := recover(); err != nil {
				logger.Errorf(ctx, "%s. recover err:%v", fun, err)
			}
			if searchResult != nil {
				if err := es.examineDscEsClient.CloseScroll(ctx, searchResult.ScrollId); err != nil {
					logger.Error(ctx, "scroll close err", zap.String("err", err.Error()), zap.String("fun", fun))
				}
			}
			close(examinDscCh)
		}()
		var num int
		for {
			select {
			case <-ctx.Done():
				return
			default:
				if searchResult.Hits != nil {
					for _, hit := range searchResult.Hits.Hits {
						dsc := &models.ExamineDscOrderDoc{}
						source, err := hit.Source.MarshalJSON()
						//fmt.Println("source:", string(source))
						if err != nil {
							logger.Error(ctx, "json marshal err", zap.String("err", err.Error()), zap.String("fun", fun))
						}
						if err := json.Unmarshal(source, &dsc); err != nil {
							logger.Error(ctx, "json unmarshal err", zap.String("err", err.Error()), zap.String("fun", fun))
							return
						}
						examinDscCh <- dsc
						num++
					}
					if len(searchResult.Hits.Hits) < pageSize {
						return
					}
					if num >= 10*pageSize { // 做多获取 10页数据
						return
					}
				}
				searchResult, scrollErr = es.examineDscEsClient.Scroll(ctx, searchResult.ScrollId, scrollKeepAliveInterval)
				if scrollErr == io.EOF {
					return
				}
			}
		}
	}()
	return examinDscCh, nil
}

func (es *examineDscEsRepo) GetExamineDscCount(ctx context.Context, options ...Option) (int64, error) {
	fun := "examineDscEsRepo.GetExamineDscCount ->"
	fields := []string{"examine_dsc_id"}
	opts := newOptions(options...)
	query := elastic.NewBoolQuery()
	if len(opts.filter) > 0 {
		query.Filter(opts.filter...)
	}
	if len(opts.not) > 0 {
		query.Filter(elastic.NewBoolQuery().MustNot(opts.not...))
	}
	if len(opts.should) > 0 {
		query.Filter(elastic.NewBoolQuery().Should(opts.should...))
	}
	for _, fs := range opts.filterShould {
		query.Filter(elastic.NewBoolQuery().Should(fs...))
	}
	for _, fn := range opts.filterNot {
		query.Filter(elastic.NewBoolQuery().MustNot(fn...))
	}
	// Debugging: Print the query to be executed
	querySource, err := query.Source()
	if err != nil {
		logger.Error(ctx, "failed to get query source", zap.String("err", err.Error()), zap.String("fun", fun))
		return 0, err
	}
	queryJSON, err := json.Marshal(querySource)
	if err != nil {
		logger.Error(ctx, "failed to marshal query to JSON", zap.String("err", err.Error()), zap.String("fun", fun))
		return 0, err
	}
	logger.Info(ctx, "executing query", zap.String("query", string(queryJSON)), zap.String("fun", fun))
	ret, err := es.examineDscEsClient.GetClient().Search(es.idx).Query(query).
		FetchSourceContext(elastic.NewFetchSourceContext(true).Include(fields...)).
		TrackTotalHits(true).Size(0).Do(context.TODO())

	if err != nil {
		logger.Error(ctx, "get examine dsc info err", zap.Any("filter", opts), zap.String("err", err.Error()), zap.String("fun", fun))
		return 0, err
	}
	return ret.Hits.TotalHits.Value, nil
}
