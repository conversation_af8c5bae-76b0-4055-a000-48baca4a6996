package elasticsearch

import (
	"context"
	"fmt"
	"github.com/olivere/elastic/v7"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"go.uber.org/zap"
	"ops-ticket-api/internal/code"
	"ops-ticket-api/internal/framework/xerrors"
	"strings"
	"time"
)

// UpdateLine 更新
func (es *lineEsRepo) UpdateLine(ctx context.Context, lineUserId string, doc map[string]interface{}) error {
	defer func() {
		if err := recover(); err != nil {
			logger.Errorf(ctx, "recover err:%v", err)
		}
	}()
	resp, err := es.lineEsClient.GetClient().Update().Index(es.idx).Id(lineUserId).Doc(doc).Refresh("true").Do(ctx)
	if err != nil {
		logger.Error(ctx, "[UpdateLine] Sync line to es err", zap.String("line_user_id", lineUserId), zap.Any("doc", doc), zap.String("err", err.Error()))
		return xerrors.New("EventUpdateFailLog", code.DbError)
	}
	if resp.Result != "updated" {
		logger.Error(ctx, "[UpdateLine] Sync line to es err", zap.String("line_user_id", lineUserId), zap.String("result", resp.Result))
	}
	return nil
}

func (es *lineEsRepo) ClearRemark(ctx context.Context, lineUserId string, updatedAt uint64) error {
	script := elastic.NewScriptInline("ctx._source.portrait_remark = ''; ctx._source.updated_at = params.updated_at").
		Param("updated_at", updatedAt).Lang("painless")
	resp, err := es.lineEsClient.GetClient().Update().Index(es.idx).Id(lineUserId).
		Script(script).
		Refresh("true").
		Do(ctx)
	if err != nil {
		logger.Error(ctx, "[LineClearRemark] Sync dsc to es err", zap.String("line_user_id", lineUserId), zap.String("err", err.Error()))
		return xerrors.New("EventUpdateFailLog", code.DbError)
	}
	if resp.Result != "updated" && resp.Result != "noop" {
		logger.Error(ctx, "[LineClearRemark] Sync dsc to es err", zap.String("line_user_id", lineUserId), zap.String("result", resp.Result))
	}
	return nil
}

func (es *lineEsRepo) UpdateMaintainConfig(ctx context.Context, lineUserId string, scripts []string, doc map[string]interface{}) error {
	defer func() {
		if err := recover(); err != nil {
			logger.Errorf(ctx, "recover err:%v", err)
		}
	}()
	resp, err := es.lineEsClient.GetClient().Update().Index(es.idx).Id(lineUserId).
		Script(elastic.NewScript(strings.Join(scripts, ";")).Params(doc)).Refresh("true").Do(ctx)
	if err != nil {
		logger.Error(ctx, "[UpdateLineMaintainConfig] Sync maintain config to es err", zap.String("line_user_id", lineUserId), zap.Any("doc", doc), zap.String("err", err.Error()))
		return xerrors.New("EventUpdateFailLog", code.DbError)
	}
	if resp.Result != "updated" {
		logger.Error(ctx, "[UpdateLineMaintainConfig] Sync maintain config to es err", zap.String("line_user_id", lineUserId), zap.String("result", resp.Result))
	}
	logger.Info(ctx, "[UpdateLineMaintainConfig] Sync maintain config to es success", zap.String("line_user_id", lineUserId), zap.String("result", resp.Result))
	return nil
}

func (es *lineEsRepo) UpdateTag(ctx context.Context, lineUserIds []string, tags []int64) error {
	defer func() {
		if err := recover(); err != nil {
			logger.Errorf(ctx, "recover err:%v", err)
		}
	}()
	bulkRequest := es.lineEsClient.GetClient().Bulk()
	for _, lineUserId := range lineUserIds {
		request := elastic.NewBulkUpdateRequest().Index(es.idx).Id(lineUserId)
		script := `ctx._source.tag = params.tags;`
		script += `ctx._source.updated_at=params.updated_at`
		params := map[string]interface{}{
			"tags":       tags,
			"updated_at": time.Now().Unix(),
		}
		request.Script(elastic.NewScript(script).Params(params))
		bulkRequest.Add(request)
	}
	bulkResponse, err := bulkRequest.Do(ctx)
	if err != nil {
		logger.Error(ctx, "[UpdateLineTag] Sync dsc to es err", zap.Strings("lineUserId", lineUserIds), zap.Int64s("tag", tags), zap.String("err", err.Error()))
		return err
	}
	if bulkResponse == nil || bulkResponse.Errors {
		logger.Error(ctx, "[UpdateLineTag] Sync dsc to es err", zap.Strings("lineUserId", lineUserIds), zap.Int64s("tag", tags), zap.Any("resp", bulkResponse))
		if bulkResponse == nil {
			return fmt.Errorf("sync dsc tags to es err:%v", "bulkResponse nil")
		}
		return fmt.Errorf("sync dsc tags to es err:%v", bulkResponse.Errors)
	}
	return nil
}
