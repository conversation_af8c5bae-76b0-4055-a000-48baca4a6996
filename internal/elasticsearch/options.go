// Copyright 2021 funplus Authors. All Rights Reserved.
// @Description:
// @Author: Darcy
// @Date: 2022/6/8 15:49

package elasticsearch

import (
	"fmt"
	"strings"
	"time"

	"github.com/spf13/cast"

	"ops-ticket-api/proto/pb"
	"ops-ticket-api/utils"

	"github.com/olivere/elastic/v7"
	"github.com/spf13/viper"
	"golang.org/x/exp/constraints"
)

type Options struct {
	filter       []elastic.Query
	not          []elastic.Query
	should       []elastic.Query
	filterNot    map[string][]elastic.Query
	filterShould map[string][]elastic.Query
}

type Option func(*Options)

var (
	minuteLayout = "2006-01-02 15:04"
	secondLayout = "2006-01-02 15:04:05"
	firstTier    = []pb.Workbench{
		pb.Workbench_FirstTierWorkStation,
		pb.Workbench_FirstTierAdminWorkStation,
	}
)

func newOptions(opts ...Option) Options {
	opt := Options{
		filter:       make([]elastic.Query, 0),
		not:          make([]elastic.Query, 0),
		should:       make([]elastic.Query, 0),
		filterNot:    make(map[string][]elastic.Query, 0),
		filterShould: make(map[string][]elastic.Query, 0),
	}
	for _, o := range opts {
		o(&opt)
	}
	return opt
}

// WithWorkStationStage 工作台节点
//func WithWorkStationStage(workstation pb.Workbench) Option {
//	return func(o *Options) {
//		filterStage := viper.GetIntSlice("ticket.workstation_stage." + workstation.String())
//		baseQuery := elastic.NewTermsQuery("stage", utils.ToInterface(filterStage...)...)
//		if len(filterStage) < len(pb.TicketStage_name) {
//			o.filterShould["stage"] = append(o.filterShould["stage"], baseQuery)
//		}
//
//		// 一线、一线管理
//		if utils.InArrayAny(workstation, firstTier) {
//			filterUserStage := viper.GetIntSlice("ticket.workstation_stage.UserStage")
//			bq := elastic.NewBoolQuery()
//			items := utils.ToInterface(filterUserStage...)
//			bq.Must(elastic.NewTermsQuery("stage", items...))
//			bq.Must(elastic.NewTermQuery("reply_from", workstation.Number()))
//			o.filterShould["stage"] = append(o.filterShould["stage"], bq)
//		}
//	}
//}

// WithWorkStationOrigin 来源
func WithWorkStationOrigin(workstation pb.Workbench, origin []uint32) Option {
	return func(o *Options) {
		filterOrigin := viper.GetIntSlice("ticket.workstation_origin." + workstation.String())
		if len(origin) > 0 {
			queryOrigin := utils.IntegerChange[uint32, int](origin)
			filterOrigin = utils.IntersectAny(filterOrigin, queryOrigin)
		}
		originLen := len(filterOrigin)
		if originLen > 0 && originLen != len(pb.Origin_value)-1 {
			items := utils.ToInterface(filterOrigin...)
			o.filter = append(o.filter, elastic.NewTermsQuery("origin", items...))
		}
	}
}

// WithAcceptorExp 处理人
func WithAcceptorExp(acceptorType pb.FilterEnum, acceptor string) Option {
	return func(o *Options) {
		switch acceptorType {
		case pb.FilterEnum_FilterIsNull:
			o.filter = append(o.filter, elastic.NewTermQuery("acceptor", ""))
		case pb.FilterEnum_FilterIsIn:
			if acceptor != "" {
				// 精确匹配 acceptor 字段
				o.filter = append(o.filter,
					elastic.NewTermQuery("acceptor", acceptor))
			}
		case pb.FilterEnum_FilterSystem:
			o.filter = append(o.filter, elastic.NewTermQuery("acceptor", acceptor))
		}
	}
}

// WithAcceptorsExp 处理人多选
func WithAcceptorsExp(acceptorType pb.FilterEnum, acceptors []string) Option {
	return func(o *Options) {
		switch acceptorType {
		case pb.FilterEnum_FilterIsNull:
			o.filter = append(o.filter, elastic.NewTermQuery("acceptor", ""))
		case pb.FilterEnum_FilterIsIn:
			if len(acceptors) != 0 {
				items := utils.ToInterface(acceptors...)
				o.filter = append(o.filter, elastic.NewTermsQuery("acceptor", items...))
			}
		}
	}
}

// WithCreatorExp 提交人
func WithCreatorExp(creatorType pb.CreatorType, creator string) Option {
	return func(o *Options) {
		if creator == "" {
			return
		}
		switch creatorType {
		case pb.CreatorType_UID:
			o.filter = append(o.filter, elastic.NewTermQuery("uid", creator))
		case pb.CreatorType_Fpid:
			o.filter = append(o.filter, elastic.NewTermQuery("account_id", creator))
		case pb.CreatorType_Nickname:
			o.filter = append(o.filter, elastic.NewWildcardQuery("nickname", creator+"*"))
		}
	}
}

// WithRangeTime 时间范围
func WithRangeTime(filter []string, property string) Option {
	return func(o *Options) {
		if len(filter) < 2 {
			return
		}
		start, _er := time.Parse(minuteLayout, filter[0])
		if _er != nil {
			start, _ = time.Parse(secondLayout, filter[0])
		}

		end, _er := time.Parse(minuteLayout, filter[1])
		if _er != nil {
			end, _ = time.Parse(secondLayout, filter[1])
		}
		o.filter = append(o.filter, elastic.NewRangeQuery(property).From(start.Unix()).To(end.Unix()))
	}
}

// WithDetailRangeTime 时间范围，精确到秒
func WithDetailRangeTime(filter []string, property string) Option {
	return func(o *Options) {
		if len(filter) < 2 {
			return
		}
		start, _ := time.Parse(secondLayout, filter[0])
		end, _ := time.Parse(secondLayout, filter[1])
		o.filter = append(o.filter, elastic.NewRangeQuery(property).From(start.Unix()).To(end.Unix()))
	}
}

// WithRangePay 付费金额范围
func WithRangePay(filter []int64, property string) Option {
	return func(o *Options) {
		if len(filter) < 2 {
			return
		}
		if filter[0] == 0 {
			value := utils.PayAmountThin(filter[1])
			o.filter = append(o.filter, elastic.NewRangeQuery(property).Lt(value))
		} else if filter[1] == 0 {
			value := utils.PayAmountThin(filter[0])
			o.filter = append(o.filter, elastic.NewRangeQuery(property).Gt(value))
		} else {
			low, high := utils.PayAmountThin(filter[0]), utils.PayAmountThin(filter[1])
			o.filter = append(o.filter, elastic.NewRangeQuery(property).Gte(low).Lte(high))
		}
	}
}

// WithRangeBirthday 生日日期范围
func WithRangeBirthday(filter []string, property string) Option {
	return func(o *Options) {
		if len(filter) < 2 {
			return
		}
		if filter[0] == "" {
			o.filter = append(o.filter, elastic.NewRangeQuery(property).Lt(filter[1]))
		} else if filter[1] == "" {
			o.filter = append(o.filter, elastic.NewRangeQuery(property).Gt(filter[0]))
		} else {
			o.filter = append(o.filter, elastic.NewRangeQuery(property).Gte(filter[0]).Lte(filter[1]))
		}
	}
}

// WithRange 时间范围
func WithRange[T constraints.Ordered](start, end T, property string) Option {
	return func(o *Options) {
		o.filter = append(o.filter, elastic.NewRangeQuery(property).From(start).To(end))
	}
}

// WithProperty 属性过滤 property: 属性名称
func WithProperty[T uint32 | ~int32 | uint64 | string](filter []T, property string) Option {
	return func(o *Options) {
		if len(filter) == 0 {
			return
		}
		items := utils.ToInterface(filter...)
		o.filter = append(o.filter, elastic.NewTermsQuery(property, items...))
	}
}

func WithPropertyTags[T uint32 | uint64 | string](filter []T, property string) Option {
	return func(o *Options) {
		if len(filter) == 0 {
			return
		}
		if len(filter) == 1 && cast.ToInt(filter[0]) == 0 {
			// 查询 tags 字段为空的数据
			o.filter = append(o.filter, elastic.NewBoolQuery().MustNot(elastic.NewExistsQuery(property)))
		} else {
			items := utils.ToInterface(filter...)
			o.filter = append(o.filter, elastic.NewTermsQuery(property, items...))
		}
	}
}

// WithPropertyWildcard 属性匹配
func WithPropertyWildcard(filter, property string) Option {
	return func(o *Options) {
		if filter == "" {
			return
		}
		o.filter = append(o.filter, elastic.NewWildcardQuery(property, "*"+filter+"*"))
	}
}

// WithPropertyTerm 属性匹配
func WithPropertyTerm[T any](filter T, property string) Option {
	return func(o *Options) {
		switch tt := any(filter).(type) {
		case uint32:
			if tt == 0 {
				return
			}
		case uint64:
			if tt == 0 {
				return
			}
		case string:
			if tt == "" {
				return
			}
		}
		o.filter = append(o.filter, elastic.NewTermQuery(property, filter))
	}
}

// WithPropertyMatch 属性匹配
func WithPropertyMatch(filter, property string) Option {
	return func(o *Options) {
		if filter == "" {
			return
		}
		o.filter = append(o.filter, elastic.NewMatchQuery(property, filter))
	}
}

// WithPropertyMatchPhrase 属性匹配
func WithPropertyMatchPhrase(filter, property string) Option {
	return func(o *Options) {
		if filter == "" {
			return
		}
		o.filter = append(o.filter, elastic.NewMatchPhraseQuery(property, filter))
	}
}

// WithPropertyNested 属性匹配
func WithPropertyNested(filter, path, property string) Option {
	return func(o *Options) {
		if filter == "" {
			return
		}
		bq := elastic.NewMatchPhraseQuery(property, filter)
		o.filter = append(o.filter, elastic.NewNestedQuery(path, bq))
	}
}

// WithRemarkDetailQuery 支持对备注的模糊查询
func WithRemarkDetailQuery(remark, path, detailProperty, commuTypeProperty string) Option {
	return func(o *Options) {
		if remark == "" {
			return
		}
		// 创建WildcardQuery来匹配detail字段
		detailQuery := elastic.NewWildcardQuery(detailProperty, "*"+remark+"*")
		// 创建TermQuery来匹配commu_type字段
		commuTypeQuery := elastic.NewTermQuery(commuTypeProperty, pb.CommuType_CommuTypeRemark.String())
		// 将两个查询组合为一个BoolQuery
		boolQuery := elastic.NewBoolQuery().
			Must(detailQuery).
			Filter(commuTypeQuery)
		// 将BoolQuery嵌套在NestedQuery中
		nestedQuery := elastic.NewNestedQuery(path, boolQuery)
		// 添加到过滤条件中
		o.filter = append(o.filter, nestedQuery)
	}
}

func WithServicePropertyNested(msgFrom int, replyTime []string, path string) Option {
	return func(o *Options) {
		if msgFrom == 0 || len(replyTime) != 2 {
			return
		}
		var queries []elastic.Query
		if msgFrom != 0 {
			msgFromQuery := elastic.NewTermQuery(fmt.Sprintf("%s.msg_from", path), msgFrom)
			queries = append(queries, msgFromQuery)
		}
		if len(replyTime) == 2 {
			start, end := replyTime[0], replyTime[1]
			startTime, endTime := utils.TimeStrToUnix(start), utils.TimeStrToUnix(end)
			replyTimeQuery := elastic.NewRangeQuery(fmt.Sprintf("%s.created_at", path)).From(startTime).To(endTime)
			queries = append(queries, replyTimeQuery)
		}
		nestedQuery := elastic.NewNestedQuery(path, elastic.NewBoolQuery().Must(queries...))
		o.filter = append(o.filter, nestedQuery)
	}
}

func WithPlayerPropertyNested(msgFrom int, content, path string) Option {
	return func(o *Options) {
		if msgFrom == 0 || content == "" {
			return
		}
		var queries []elastic.Query
		if msgFrom != 0 {
			msgFromQuery := elastic.NewTermQuery(fmt.Sprintf("%s.msg_from", path), msgFrom)
			queries = append(queries, msgFromQuery)
		}
		if content != "" {
			contentQuery := elastic.NewMatchQuery(fmt.Sprintf("%s.content", path), content)
			queries = append(queries, contentQuery)
		}
		nestedQuery := elastic.NewNestedQuery(path, elastic.NewBoolQuery().Must(queries...))
		o.filter = append(o.filter, nestedQuery)
	}
}

func WithDscUserName(userNames []string) Option {
	return func(o *Options) {
		if len(userNames) == 0 {
			return
		}
		var userNameQueries []elastic.Query
		for _, userName := range userNames {
			if userName == "" {
				continue
			}
			// Use WildcardQuery for fuzzy search
			userNameQueries = append(userNameQueries,
				elastic.NewWildcardQuery("global_name.keyword", fmt.Sprintf("*%s*", userName)),
				elastic.NewWildcardQuery("user_name.keyword", fmt.Sprintf("*%s*", userName)),
			)
		}
		if len(userNameQueries) == 0 {
			return
		}
		userNameQuery := elastic.NewBoolQuery().Should(userNameQueries...)
		o.filter = append(o.filter, userNameQuery)
	}
}
func WithLineDisplayName(userNames []string) Option {
	return func(o *Options) {
		if len(userNames) == 0 {
			return
		}
		var userNameQueries []elastic.Query
		for _, userName := range userNames {
			if userName == "" {
				continue
			}
			// Use WildcardQuery for fuzzy search
			userNameQueries = append(userNameQueries,
				elastic.NewWildcardQuery("display_name.keyword", fmt.Sprintf("*%s*", userName)),
			)
		}
		if len(userNameQueries) == 0 {
			return
		}
		userNameQuery := elastic.NewBoolQuery().Should(userNameQueries...)
		o.filter = append(o.filter, userNameQuery)
	}
}

// WithTag 工单标签
func WithTag(tagType pb.FilterTagEnum, tags []uint32) Option {
	return func(o *Options) {
		items := utils.ToInterface(tags...)
		switch tagType {
		case pb.FilterTagEnum_IsNull:
			o.not = append(o.not, elastic.NewExistsQuery("tags"))
		case pb.FilterTagEnum_IsIn:
			o.filter = append(o.filter, elastic.NewTermsQuery("tags", items...))
		case pb.FilterTagEnum_IsNotIn:
			o.not = append(o.not, elastic.NewTermsQuery("tags", items...))
		case pb.FilterTagEnum_IsAll:
			// 查询 tags 不为空的文档
			o.filter = append(o.filter,
				elastic.NewExistsQuery("tags"),
				elastic.NewScriptQuery(elastic.NewScript("doc['tags'].length > 0")),
			)
		}
	}
}

// WithTags Dc标签
func WithTags(tagType pb.FilterTagEnum, tags []uint32, property string) Option {
	return func(o *Options) {
		items := utils.ToInterface(tags...)
		switch tagType {
		case pb.FilterTagEnum_IsNull:
			o.not = append(o.not, elastic.NewExistsQuery(property))
		case pb.FilterTagEnum_IsIn:
			o.filter = append(o.filter, elastic.NewTermsQuery(property, items...))
		case pb.FilterTagEnum_IsNotIn:
			o.not = append(o.not, elastic.NewTermsQuery(property, items...))
		case pb.FilterTagEnum_IsAll:
			// 查询 tag 不为空的文档
			o.filter = append(o.filter,
				elastic.NewExistsQuery(property),
				elastic.NewScriptQuery(elastic.NewScript(fmt.Sprintf("doc['%s'].length > 0", property))),
			)
		}
	}
}

func WithExistsQuery(filter bool, property string) Option {
	return func(o *Options) {
		if filter {
			o.filter = append(o.filter, elastic.NewExistsQuery(property))
		}
	}
}

// WithSid 区服
func WithSid(serverStr string) Option {
	return func(o *Options) {
		if len(serverStr) == 0 {
			return
		}
		if len(serverStr) != 0 {
			server := utils.SplitServer(serverStr)
			if len(server.Ids) > 0 {
				items := utils.ToInterface(server.Ids...)
				o.should = append(o.should, elastic.NewTermsQuery("sid", items...))
			}
			for _, btw := range server.Btw {
				o.should = append(o.should, elastic.NewRangeQuery("sid").From(btw.Start).To(btw.End))
			}
		}
	}
}

func WithIsVip(isVip bool) Option {
	return func(o *Options) {
		if isVip == true {
			o.filter = append(o.filter, elastic.NewTermQuery("vip", uint8(2)))
		}
	}
}
func WithIsUpgradeTk(isUpgrade bool) Option {
	return func(o *Options) {
		if isUpgrade {
			o.filter = append(o.filter, elastic.NewTermQuery("priority", uint8(1)))
		}
	}
}

func WithIsVipCrm(isVipCrm string) Option {
	return func(o *Options) {
		if isVipCrm == "" {
			return
		}
		if cast.ToInt(isVipCrm) == 1 {
			o.filter = append(o.filter, elastic.NewTermQuery("vip_crm", uint8(1)))
		} else if cast.ToInt(isVipCrm) == 2 {
			o.filter = append(o.filter, elastic.NewTermQuery("vip_crm", uint8(0)))
		}
	}
}

func WithIsSvip(isSvip string) Option {
	return func(o *Options) {
		if isSvip == "" {
			return
		}
		if cast.ToInt(isSvip) == 1 {
			o.filter = append(o.filter, elastic.NewTermQuery("svip", uint8(1)))
		} else if cast.ToInt(isSvip) == 2 {
			o.filter = append(o.filter, elastic.NewTermQuery("svip", uint8(2)))
		}
	}
}

// WithRefill 补填状态
func WithRefill(fill pb.FillStatus) Option {
	return func(o *Options) {
		if fill == pb.FillStatus_FillNone {
			return
		}
		o.filter = append(o.filter, elastic.NewTermsQuery("refill", uint8(fill)))
	}
}

//// WithGroupData 技能组
//func WithGroupData(project string, langs string, cats []uint32, workgroup uint32) Option {
//	return func(o *Options) {
//		q := elastic.NewBoolQuery()
//		q.Must(elastic.NewTermQuery("project", project))
//		if langs != "" {
//			q.Must(elastic.NewTermQuery("lang", langs))
//		}
//		if len(cats) > 0 {
//			q.Must(elastic.NewTermsQuery("cat_id", utils.ToInterface(cats...)...))
//		}
//		if workgroup != 0 {
//			filterStage := viper.GetIntSlice("ticket.workstation_stage." + pb.Workbench(workgroup).String())
//			// 一线、一线管理
//			if utils.InArrayAny(pb.Workbench(workgroup), firstTier) {
//				filterUserStage := viper.GetIntSlice("ticket.workstation_stage.UserStage")
//				filterStage = append(filterStage, filterUserStage...)
//			}
//			if len(filterStage) < len(pb.TicketStage_name) {
//				q.Must(elastic.NewTermsQuery("stage", utils.ToInterface(filterStage...)...))
//			}
//		}
//		o.filterShould["usergroup"] = append(o.filterShould["usergroup"], q)
//	}
//}

// WithFilter 属性匹配
func WithFilter(filter elastic.Query) Option {
	return func(o *Options) {
		o.filter = append(o.filter, filter)
	}
}

// WithNot 属性匹配
func WithNot(not elastic.Query) Option {
	return func(o *Options) {
		o.not = append(o.not, not)
	}
}

// WithShould 属性匹配
func WithShould(should elastic.Query) Option {
	return func(o *Options) {
		o.should = append(o.should, should)
	}
}

// WithSystemTags
func WithSystemTags(reqLabels []uint32, property string) Option {
	return func(o *Options) {
		if len(reqLabels) == 0 {
			return
		}
		// Separate `12` from other labels
		has12 := false
		otherLabels := []interface{}{}
		for _, label := range reqLabels {
			if label == 12 {
				has12 = true
			} else {
				otherLabels = append(otherLabels, label)
			}
		}
		// Build the query
		boolQuery := elastic.NewBoolQuery()

		// If `12` is included, look for empty `system_tags` or missing field
		if has12 {
			boolQuery.Should(
				elastic.NewBoolQuery().MustNot(elastic.NewExistsQuery(property)), // Field does not exist
			)
		}

		// If there are other labels, include them in the query
		if len(otherLabels) > 0 {
			items := utils.ToInterface(otherLabels...)
			boolQuery.Should(
				elastic.NewTermsQuery(property, items...))
		}

		// Add the query to options
		o.filter = append(o.filter, boolQuery)
	}
}

// WithPropertyMatchPhraseOr 多词 OR 查询
func WithPropertyMatchPhraseOr(phrases []string, field string) Option {
	return func(o *Options) {
		if len(phrases) == 0 {
			return
		}

		// 构建 should 查询
		shouldQueries := make([]elastic.Query, 0, len(phrases))
		for _, phrase := range phrases {
			shouldQueries = append(shouldQueries, elastic.NewMatchPhraseQuery(field, phrase))
		}

		// 使用 bool query 的 should 组合多个短语
		boolQuery := elastic.NewBoolQuery().Should(shouldQueries...)
		// 至少匹配一个条件
		boolQuery.MinimumShouldMatch("1")

		o.filter = append(o.filter, boolQuery)
	}
}

// WithPropertyMatchPhraseAnd 多词 AND 查询
func WithPropertyMatchPhraseAnd(phrases []string, field string) Option {
	return func(o *Options) {
		if len(phrases) == 0 {
			return
		}

		// 构建 must 查询
		mustQueries := make([]elastic.Query, 0, len(phrases))
		for _, phrase := range phrases {
			mustQueries = append(mustQueries, elastic.NewMatchPhraseQuery(field, phrase))
		}

		// 使用 bool query 的 must 组合多个短语
		boolQuery := elastic.NewBoolQuery().Must(mustQueries...)

		o.filter = append(o.filter, boolQuery)
	}
}

// WithRemarkDetailQueryAnd 备注信息多词 AND 查询
func WithRemarkDetailQueryAnd(phrases []string) Option {
	return func(o *Options) {
		if len(phrases) == 0 {
			return
		}

		// 构建 must 查询
		mustQueries := make([]elastic.Query, 0, len(phrases))
		for _, phrase := range phrases {
			// 为每个短语创建一个独立的 bool 查询
			innerBool := elastic.NewBoolQuery().
				Must(
					elastic.NewWildcardQuery("commus.detail.keyword", "*"+phrase+"*"),
					elastic.NewTermQuery("commus.commu_type", pb.CommuType_CommuTypeRemark.String()),
				)
			// 将每个短语的查询包装在 nested query 中
			nestedQuery := elastic.NewNestedQuery("commus", innerBool)
			mustQueries = append(mustQueries, nestedQuery)
		}

		// 创建外层 bool 查询，要求所有嵌套查询都匹配
		boolQuery := elastic.NewBoolQuery().Must(mustQueries...)

		o.filter = append(o.filter, boolQuery)
	}
}

// WithRemarkDetailQueryOr 备注信息多词 OR 查询
func WithRemarkDetailQueryOr(phrases []string) Option {
	return func(o *Options) {
		if len(phrases) == 0 {
			return
		}

		// 构建 should 查询
		shouldQueries := make([]elastic.Query, 0, len(phrases))
		for _, phrase := range phrases {
			// 为每个短语创建通配符查询
			detailQuery := elastic.NewWildcardQuery("commus.detail.keyword", "*"+phrase+"*")
			shouldQueries = append(shouldQueries, detailQuery)
		}

		// 创建 bool query，结合 should 查询和 commu_type 过滤
		boolQuery := elastic.NewBoolQuery().
			Must(
				elastic.NewBoolQuery().Should(shouldQueries...).MinimumShouldMatch("1"),
				elastic.NewTermQuery("commus.commu_type", pb.CommuType_CommuTypeRemark.String()),
			)

		// 将整个查询包装在 nested query 中
		nestedQuery := elastic.NewNestedQuery("commus", boolQuery)

		o.filter = append(o.filter, nestedQuery)
	}
}

func WithDcOrLineRemark(filter, property string) Option {
	return func(o *Options) {
		if filter == "" {
			return
		}

		// 处理 '&&' AND 连接
		andParts := strings.Split(filter, "&&")
		if len(andParts) > 1 {
			var mustQueries []elastic.Query
			for _, part := range andParts {
				trimmedPart := strings.TrimSpace(part)
				if trimmedPart != "" {
					mustQueries = append(mustQueries, elastic.NewWildcardQuery(property, "*"+trimmedPart+"*"))
				}
			}
			if len(mustQueries) > 0 {
				o.filter = append(o.filter, elastic.NewBoolQuery().Must(mustQueries...))
			}
			return
		}

		// 处理 '||' OR 连接
		orParts := strings.Split(filter, "||")
		if len(orParts) > 1 {
			var shouldQueries []elastic.Query
			for _, part := range orParts {
				trimmedPart := strings.TrimSpace(part)
				if trimmedPart != "" {
					shouldQueries = append(shouldQueries, elastic.NewWildcardQuery(property, "*"+trimmedPart+"*"))
				}
			}
			if len(shouldQueries) > 0 {
				o.filter = append(o.filter, elastic.NewBoolQuery().Should(shouldQueries...).MinimumShouldMatch("1"))
			}
			return
		}

		// 处理单个查询
		trimmedFilter := strings.TrimSpace(filter)
		if trimmedFilter != "" {
			o.filter = append(o.filter, elastic.NewWildcardQuery(property, "*"+trimmedFilter+"*"))
		}
	}
}

// WithUserContent 处理聊天内容的查询
func WithUserContent(msgFrom int, content, path string) Option {
	return func(o *Options) {
		if msgFrom == 0 || content == "" {
			return
		}

		content = strings.TrimSpace(content)

		if strings.Contains(content, "||") {
			// OR 查询
			terms := strings.Split(content, "||")
			shouldQueries := make([]elastic.Query, 0, len(terms))
			for _, term := range terms {
				term = strings.TrimSpace(term)
				if term != "" {
					// 为每个词创建一个嵌套查询
					innerBool := elastic.NewBoolQuery().
						Must(
							elastic.NewTermQuery(fmt.Sprintf("%s.msg_from", path), msgFrom),
							elastic.NewMatchPhraseQuery(fmt.Sprintf("%s.content", path), term),
						)
					shouldQueries = append(shouldQueries, elastic.NewNestedQuery(path, innerBool))
				}
			}
			if len(shouldQueries) > 0 {
				// 使用 should 组合多个嵌套查询
				o.filter = append(o.filter, elastic.NewBoolQuery().Should(shouldQueries...).MinimumShouldMatch("1"))
			}
		} else if strings.Contains(content, "&&") {
			// AND 查询
			terms := strings.Split(content, "&&")
			mustQueries := make([]elastic.Query, 0, len(terms))
			for _, term := range terms {
				term = strings.TrimSpace(term)
				if term != "" {
					// 为每个词创建一个嵌套查询
					innerBool := elastic.NewBoolQuery().
						Must(
							elastic.NewTermQuery(fmt.Sprintf("%s.msg_from", path), msgFrom),
							elastic.NewMatchPhraseQuery(fmt.Sprintf("%s.content", path), term),
						)
					mustQueries = append(mustQueries, elastic.NewNestedQuery(path, innerBool))
				}
			}
			if len(mustQueries) > 0 {
				// 使用 must 组合多个嵌套查询
				o.filter = append(o.filter, elastic.NewBoolQuery().Must(mustQueries...))
			}
		} else {
			// 单一条件查询
			boolQuery := elastic.NewBoolQuery().
				Must(
					elastic.NewTermQuery(fmt.Sprintf("%s.msg_from", path), msgFrom),
					elastic.NewMatchPhraseQuery(fmt.Sprintf("%s.content", path), content),
				)
			o.filter = append(o.filter, elastic.NewNestedQuery(path, boolQuery))
		}
	}
}

// WithSolveType 筛选工单处理类型
func WithSolveType(reqSolveType []uint32, property string) Option {
	return func(o *Options) {
		if len(reqSolveType) == 0 {
			return
		}

		// 初始化 bool 查询
		boolQuery := elastic.NewBoolQuery()

		// 遍历 reqSolveType
		for _, solveType := range reqSolveType {
			if solveType == 1 {
				// 处理 solveType = 1 的情况：字段不存在或值为 1
				boolQuery.Should(
					elastic.NewBoolQuery().MustNot(elastic.NewExistsQuery(property)), // 字段不存在
					elastic.NewTermQuery(property, 1),                                // 字段值为 1
				)
			} else {
				// 处理 solveType = 2,3,4,5 的情况：字段值匹配
				boolQuery.Should(elastic.NewTermQuery(property, solveType))
			}
		}

		// 设置最小匹配数为 1，确保满足任意一个条件即可
		boolQuery.MinimumShouldMatch("1")

		// 将查询添加到 options 中
		o.filter = append(o.filter, boolQuery)
	}
}
