package elasticsearch

import (
	"context"
	"encoding/json"
	"github.com/olivere/elastic/v7"
	"github.com/spf13/cast"
	"github.com/spf13/viper"
	"gitlab-ee.funplus.io/ops-tools/compkg/dump"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"gitlab-ee.funplus.io/ops-tools/compkg/x/stores/es/client"
	"go.uber.org/zap"
	"io"
	"ops-ticket-api/internal/code"
	"ops-ticket-api/internal/framework/stores/es"
	"ops-ticket-api/internal/framework/xerrors"
	"ops-ticket-api/models"
	"ops-ticket-api/proto/pb"
	"os"
	"strings"
	"sync"
	"time"
)

var (
	dscOnceEx       sync.Once
	dscEsCli        *dscEsRepo
	DefaultDscEsSvc = newDscRepo()
)

type dscEsRepo struct {
	idx         string
	dscEsClient client.Client
}

// NewTicketRepo init
func newDscRepo() *dscEsRepo {
	release := os.Getenv("release")
	if release == "cn" {
		return nil
	}
	dscOnceEx.Do(func() {
		ctx := context.Background()
		cli, err := es.GetDscCli(ctx)
		if err != nil {
			return
		}
		dscEsCli = &dscEsRepo{
			dscEsClient: cli,
			idx:         viper.GetString("dsces.index.dscindex"),
		}
	})
	return dscEsCli
}

// UpsertDscUser 更新或新增用户
func (es *dscEsRepo) UpsertDscUser(ctx context.Context, dscUser *models.FpDscUser) error {
	var (
		fun = "dscEsRepo.UpsertDscUser"
	)
	defer func() {
		if err := recover(); err != nil {
			logger.Errorf(ctx, "%s recover err:%v", fun, err)
		}
	}()

	doc := models.DscUserAssignDoc(dscUser)
	scripts := []string{
		"ctx._source.id = params.id",
		"ctx._source.dsc_user_id = params.dsc_user_id",
		"ctx._source.priv_channel_id = params.priv_channel_id",
		"ctx._source.app_id = params.app_id",
		"ctx._source.guild_id = params.guild_id",
		"ctx._source.global_name = params.global_name",
		"ctx._source.user_name = params.user_name",
		"ctx._source.is_deleted = params.is_deleted",
		"ctx._source.vip_state = params.vip_state",
		"ctx._source.created_at = params.created_at",
		"ctx._source.updated_at = params.updated_at",
	}
	param := map[string]interface{}{
		"id":              doc.ID,
		"dsc_user_id":     doc.DscUserID,
		"priv_channel_id": doc.PrivChannelID,
		"app_id":          doc.AppID,
		"guild_id":        doc.GuildID,
		"global_name":     doc.GlobalName,
		"user_name":       doc.UserName,
		"is_deleted":      doc.IsDeleted,
		"vip_state":       doc.VipState,
		"created_at":      doc.CreatedAt,
		"updated_at":      time.Now().Unix(),
	}

	resp, err := es.dscEsClient.GetClient().Update().Index(es.idx).Id(cast.ToString(doc.PrivChannelID)).
		Script(elastic.NewScript(strings.Join(scripts, ";")).Params(param)).
		Upsert(doc).Refresh("true").Do(ctx)
	if err != nil {
		logger.Errorf(ctx, "%s upsert detail return err:%v", fun, err)
		return err
	}
	dump.Dump(resp)
	if resp.Result != "updated" && resp.Result != "created" {
		logger.Warn(ctx, "dsc upsert result not updated or created", zap.String("result", resp.Result),
			zap.String("priv_channel_id", cast.ToString(doc.PrivChannelID)),
			zap.Any("doc", doc), zap.Any("param", param))
	}
	return nil
}

func (es *dscEsRepo) UpsertDscCommus(ctx context.Context, commu *models.FpDscDmCommu, setReplyTp bool, lastReplyService string) error {
	var (
		scripts     string
		param       map[string]interface{}
		msgFrom     = pb.DscMsgFromTpDf_DscMsgFromTpDfRobot
		replyType   = pb.DscReplyTpDf_DscReplyTpDfReplied
		sortStartAt uint64
	)
	if commu.ChannelID == "" {
		return nil
	}
	if commu.FromUserID == commu.DscUserID {
		msgFrom = pb.DscMsgFromTpDf_DscMsgFromTpDfUser
		replyType = pb.DscReplyTpDf_DscReplyTpDfUnReply

		sortStartAt = uint64(time.Now().Unix())
		scripts += `
		if (ctx._source.dsc_commu != null && ctx._source.dsc_commu.size() > 0) {
			Map lastCommu = ctx._source.dsc_commu[ctx._source.dsc_commu.size() - 1];
			if (lastCommu.msg_from != params.msg_from) {
				ctx._source.sort_wait_start_at = params.sort_start_at;
			}
		} else {
			ctx._source.sort_wait_start_at = params.sort_start_at;
		}
    `
	}

	if commu.Content != "" { // add/update commun + update dsc user field
		scripts += `if (ctx._source.dsc_commu != null) {
			boolean found = false;
			for (int i = 0; i < ctx._source.dsc_commu.size(); ++i) {
				if (ctx._source.dsc_commu[i].msg_id == params.msg_id) {
					ctx._source.dsc_commu[i].content = params.content;
					found = true;
					break;
				}
			}
			if (!found) {
				Map newNestedObject = new HashMap();
				newNestedObject.put("msg_id", params.msg_id);
				newNestedObject.put("msg_from", params.msg_from);
				newNestedObject.put("from_user_id", params.from_user_id);
				newNestedObject.put("created_at", params.created_at);
				newNestedObject.put("content", params.content);
				ctx._source.dsc_commu.add(newNestedObject);
			}
		}
		ctx._source.updated_at=params.updated_at;
		`
	} else { // del commu + update dsc user field
		scripts += `if (ctx._source.dsc_commu != null) {
			ctx._source.dsc_commu.removeIf(nested -> nested.msg_id.equals(params.msg_id));
		}
		ctx._source.updated_at=params.updated_at;
		`
	}
	if setReplyTp { // update 不修改 回复状态
		scripts += `ctx._source.reply_type=params.reply_type;`
	}

	// 如果这个消息是客服发给玩家的，才需要修改玩家的最近处理人(客服)
	if commu.FromUserID == commu.BotID && lastReplyService != "" {
		sortStartAt = code.WaitStartAtForFinished
		scripts += `ctx._source.sort_wait_start_at = params.sort_start_at;`
		scripts += `ctx._source.last_reply_service=params.last_reply_service;`
		scripts += `ctx._source.last_reply_time = params.last_reply_time`
	}
	param = map[string]interface{}{
		"msg_id":             commu.MsgID,
		"msg_from":           msgFrom,
		"from_user_id":       commu.FromUserID,
		"content":            commu.Content,
		"created_at":         commu.CreatedAt.Unix(),
		"updated_at":         time.Now().Unix(),
		"reply_type":         replyType,
		"last_reply_service": lastReplyService,
		"last_reply_time":    time.Now().Unix(),
		"sort_start_at":      sortStartAt,
	}
	result, err := es.dscEsClient.GetClient().Update().Index(es.idx).Id(commu.ChannelID).
		Script(elastic.NewScript(scripts).Params(param)).Refresh("true").Do(ctx)
	if err != nil {
		logger.Errorf(ctx, "%s. UpsertDscCommus err:%v. param:%+v", err, param)
		return err
	}
	dump.Dump("commu:", result, param)
	if result.Result != "updated" && result.Result != "created" {
		logger.Warn(ctx, "dsc upsert result not updated or created", zap.String("result", result.Result),
			zap.String("msg_id", commu.MsgID), zap.Any("param", param))
	}
	return nil
}

func (es *dscEsRepo) AddDscReaction(ctx context.Context, reaction *models.FpDscDmCommuReaction, replyTp pb.DscReplyTpDf) error {
	var up = map[string]interface{}{
		"reply_type": replyTp,
		"updated_at": time.Now().Unix(),
	}
	err := es.dscEsClient.Update(ctx, es.idx, reaction.ChannelID, up)
	if err != nil {
		logger.Errorf(ctx, "AddDscReaction err:%v. reaction:%+v. up:%v", err, reaction, up)
	}
	//dump.Dump("AddDscReaction:", reaction, up)
	return err
}

func (es *dscEsRepo) DelDscCommus(ctx context.Context, channelId string, msgId []string) error {
	var (
		fun = "dscEsRepo.DelDscCommus"
	)
	if len(msgId) == 0 {
		return nil
	}
	scripts := `if (ctx._source.dsc_commu != null) {
			ctx._source.dsc_commu.removeIf(nested -> params.msg_id.contains(nested.msg_id));
		}
		ctx._source.updated_at=params.updated_at;
		`
	param := map[string]interface{}{
		"msg_id":     msgId,
		"updated_at": time.Now().Unix(),
	}
	resp, err := es.dscEsClient.GetClient().Update().Index(es.idx).Id(channelId).
		Script(elastic.NewScript(scripts).Params(param)).
		Refresh("true").Do(ctx)
	if err != nil {
		logger.Errorf(ctx, "%s del detail return err:%v. param:%+v", fun, err, param)
		return err
	}
	if resp.Result != "updated" {
		logger.Warn(ctx, "dsc del result not deleted or noop", zap.String("result", resp.Result),
			zap.Any("msg_id", msgId), zap.String("channel_id", channelId))
	}
	dump.Dump("DelDscCommus:", resp, param)
	return nil
}

func (es *dscEsRepo) GetDiscordAccountsCount(ctx context.Context, projects []interface{}) (int64, error) {
	fields := []string{"dsc_user_id", "user_name", "global_name", "priv_channel_id", "guild_id", "maintainer", "bot_id"}
	query := elastic.NewBoolQuery()
	if len(projects) > 0 {
		query = query.Filter(elastic.NewTermsQuery("project", projects...))
	}
	query = query.Filter(elastic.NewTermsQuery("is_deleted", code.DscNoDelete))
	ret, err := es.dscEsClient.GetClient().Search(es.idx).
		Query(query).
		FetchSourceContext(elastic.NewFetchSourceContext(true).Include(fields...)).
		TrackTotalHits(true).
		SortBy(elastic.NewFieldSort("id").Desc()).
		Do(ctx)

	if err != nil {
		logger.Error(ctx, "Elasticsearch query GetDiscordAccountsCount error", zap.Error(err))
		return 0, err
	}
	return ret.Hits.TotalHits.Value, nil
}

func (es *dscEsRepo) WaitReplyAccountsCount(ctx context.Context, projects []interface{}) (int64, error) {
	fields := []string{"dsc_user_id", "user_name", "global_name", "priv_channel_id", "guild_id", "maintainer", "bot_id"}
	query := elastic.NewBoolQuery()
	if len(projects) > 0 {
		query = query.Filter(elastic.NewTermsQuery("project", projects...))
	}
	query = query.Filter(elastic.NewTermsQuery("is_deleted", code.DscNoDelete))
	query = query.Filter(elastic.NewTermsQuery("reply_type", pb.DscReplyTpDf_DscReplyTpDfUnReply))
	ret, err := es.dscEsClient.GetClient().Search(es.idx).
		Query(query).
		FetchSourceContext(elastic.NewFetchSourceContext(true).Include(fields...)).
		TrackTotalHits(true).
		SortBy(elastic.NewFieldSort("id").Desc()).
		Do(ctx)

	if err != nil {
		logger.Error(ctx, "Elasticsearch query WaitReplyAccountsCount error", zap.Error(err))
		return 0, err
	}
	return ret.Hits.TotalHits.Value, nil
}

func (es *dscEsRepo) MineWaitReplyAccountsCount(ctx context.Context, maintainer string, projects []interface{}) (int64, error) {
	fields := []string{"dsc_user_id", "user_name", "global_name", "priv_channel_id", "guild_id", "maintainer", "bot_id"}
	query := elastic.NewBoolQuery()
	if len(projects) > 0 {
		query = query.Filter(elastic.NewTermsQuery("project", projects...))
	}
	query = query.Filter(elastic.NewTermsQuery("is_deleted", code.DscNoDelete))
	query = query.Filter(elastic.NewTermsQuery("reply_type", pb.DscReplyTpDf_DscReplyTpDfUnReply))
	// query = query.Filter(elastic.NewTermsQuery("maintainer", maintainer))
	query = query.Filter(elastic.NewTermsQuery("last_reply_service", maintainer))
	ret, err := es.dscEsClient.GetClient().Search(es.idx).
		Query(query).
		FetchSourceContext(elastic.NewFetchSourceContext(true).Include(fields...)).
		TrackTotalHits(true).
		SortBy(elastic.NewFieldSort("id").Desc()).
		Do(ctx)

	if err != nil {
		logger.Warn(ctx, "Elasticsearch query WaitReplyAccountsCount error", zap.Error(err))
		return 0, err
	}
	return ret.Hits.TotalHits.Value, nil
}

func (es *dscEsRepo) GetDscLastReplyService(ctx context.Context, privChannelIds []string, options ...Option) ([]*elastic.SearchHit, int64, error) {
	fields := []string{"priv_channel_id", "last_reply_service"}
	opts := newOptions(append([]Option{WithProperty(privChannelIds, "priv_channel_id")}, options...)...)
	query := elastic.NewBoolQuery()
	if len(opts.filter) > 0 {
		query.Filter(opts.filter...)
	}
	if len(opts.not) > 0 {
		query.Filter(elastic.NewBoolQuery().MustNot(opts.not...))
	}
	if len(opts.should) > 0 {
		query.Filter(elastic.NewBoolQuery().Should(opts.should...))
	}
	for _, fs := range opts.filterShould {
		query.Filter(elastic.NewBoolQuery().Should(fs...))
	}
	for _, fn := range opts.filterNot {
		query.Filter(elastic.NewBoolQuery().MustNot(fn...))
	}
	// Debugging: Print the query to be executed
	querySource, err := query.Source()
	if err != nil {
		logger.Error(ctx, "failed to get query source", zap.String("err", err.Error()))
		return nil, 0, err
	}
	queryJSON, err := json.Marshal(querySource)
	if err != nil {
		logger.Error(ctx, "failed to marshal query to JSON", zap.String("err", err.Error()))
		return nil, 0, err
	}
	logger.Info(ctx, "executing query", zap.String("query", string(queryJSON)))
	ret, err := es.dscEsClient.GetClient().Search(es.idx).Query(query).
		FetchSourceContext(elastic.NewFetchSourceContext(true).Include(fields...)).
		TrackTotalHits(true).
		Size(len(privChannelIds)).
		Do(context.TODO())

	if err != nil {
		logger.Error(ctx, "get dsc info err", zap.Any("filter", opts), zap.String("err", err.Error()))
		return nil, 0, err
	}
	return ret.Hits.Hits, ret.Hits.TotalHits.Value, nil
}

func (es *dscEsRepo) GetDscPool(ctx context.Context, page, pageSize int, fieldsExt []string, sort []elastic.Sorter, options ...Option) ([]*elastic.SearchHit, int64, error) {
	fields := []string{"dsc_user_id", "user_name", "global_name", "priv_channel_id", "guild_id", "maintainer", "app_id", "uid", "account_id", "sid", "reply_type", "pay_all", "pay_last_thirty_days", "last_login", "vip_state", "vip_level", "note", "player_nick", "birthday", "lang", "last_reply_time", "sort_wait_start_at"}
	if len(fieldsExt) > 0 {
		fields = append(fields, fieldsExt...)
	}

	opts := newOptions(options...)
	query := elastic.NewBoolQuery()
	if len(opts.filter) > 0 {
		query.Filter(opts.filter...)
	}
	if len(opts.not) > 0 {
		query.Filter(elastic.NewBoolQuery().MustNot(opts.not...))
	}
	if len(opts.should) > 0 {
		query.Filter(elastic.NewBoolQuery().Should(opts.should...))
	}
	for _, fs := range opts.filterShould {
		query.Filter(elastic.NewBoolQuery().Should(fs...))
	}
	for _, fn := range opts.filterNot {
		query.Filter(elastic.NewBoolQuery().MustNot(fn...))
	}
	if sort == nil {
		sort = []elastic.Sorter{elastic.NewFieldSort("created_at").Desc()}
	}
	// Debugging: Print the query to be executed
	querySource, err := query.Source()
	if err != nil {
		logger.Error(ctx, "failed to get query source", zap.String("err", err.Error()))
		return nil, 0, err
	}
	queryJSON, err := json.Marshal(querySource)
	if err != nil {
		logger.Error(ctx, "failed to marshal query to JSON", zap.String("err", err.Error()))
		return nil, 0, err
	}
	logger.Info(ctx, "executing query", zap.String("query", string(queryJSON)))
	ret, err := es.dscEsClient.GetClient().Search(es.idx).Query(query).
		FetchSourceContext(elastic.NewFetchSourceContext(true).Include(fields...)).
		TrackTotalHits(true).
		From((page - 1) * pageSize).
		Size(pageSize).
		SortBy(sort...).
		Do(context.TODO())

	if err != nil {
		logger.Error(ctx, "get dsc info err", zap.Any("filter", opts), zap.String("err", err.Error()))
		return nil, 0, err
	}
	return ret.Hits.Hits, ret.Hits.TotalHits.Value, nil
}

func (es *dscEsRepo) GetDiscordPoolScroll(ctx context.Context, options ...Option) (<-chan *models.FpDscUserDoc, error) {
	opts := newOptions(options...)
	query := elastic.NewBoolQuery()
	if len(opts.filter) > 0 {
		query.Filter(opts.filter...)
	}
	if len(opts.not) > 0 {
		query.Filter(elastic.NewBoolQuery().MustNot(opts.not...))
	}
	if len(opts.should) > 0 {
		query.Filter(elastic.NewBoolQuery().Should(opts.should...))
	}
	for _, fs := range opts.filterShould {
		query.Filter(elastic.NewBoolQuery().Should(fs...))
	}
	for _, fn := range opts.filterNot {
		query.Filter(elastic.NewBoolQuery().MustNot(fn...))
	}
	searchResult, scrollErr := es.dscEsClient.OpenScroll(ctx, &client.SearchParameters{
		Index:    es.idx,
		Query:    query,
		PageSize: pageSize,
		Sorter:   []elastic.Sorter{elastic.NewFieldSort("id").Desc()},
	}, scrollKeepAliveInterval)
	if scrollErr != nil {
		return nil, scrollErr
	}
	discordCh := make(chan *models.FpDscUserDoc, 256)
	go func() {
		defer func() {
			if err := recover(); err != nil {
				logger.Errorf(ctx, "recover err:%v", err)
			}
			if searchResult != nil {
				if err := es.dscEsClient.CloseScroll(ctx, searchResult.ScrollId); err != nil {
					logger.Error(ctx, "scroll close err", zap.String("err", err.Error()))
				}
			}
			close(discordCh)
		}()
		var num int
		for {
			select {
			case <-ctx.Done():
				return
			default:
				if searchResult.Hits != nil {
					for _, hit := range searchResult.Hits.Hits {
						discord := &models.FpDscUserDoc{}
						source, err := hit.Source.MarshalJSON()
						if err != nil {
							logger.Error(ctx, "json marshal err", zap.String("err", err.Error()))
						}
						if err := json.Unmarshal(source, &discord); err != nil {
							logger.Error(ctx, "json unmarshal err", zap.String("err", err.Error()))
							return
						}
						discordCh <- discord
						num++
					}
					if len(searchResult.Hits.Hits) < pageSize {
						return
					}
					if num >= 10*pageSize { // 做多获取 10页数据
						return
					}
				}
				searchResult, scrollErr = es.dscEsClient.Scroll(ctx, searchResult.ScrollId, scrollKeepAliveInterval)
				if scrollErr == io.EOF {
					return
				}
			}
		}
	}()
	return discordCh, nil
}

func (es *dscEsRepo) QueryCount(ctx context.Context, options ...Option) (int64, error) {
	fields := []string{"dsc_user_id", "user_name", "global_name", "priv_channel_id", "guild_id", "maintainer", "bot_id"}
	opts := newOptions(options...)
	query := elastic.NewBoolQuery()
	if len(opts.filter) > 0 {
		query.Filter(opts.filter...)
	}
	if len(opts.not) > 0 {
		query.Filter(elastic.NewBoolQuery().MustNot(opts.not...))
	}
	if len(opts.should) > 0 {
		query.Filter(elastic.NewBoolQuery().Should(opts.should...))
	}
	for _, fs := range opts.filterShould {
		query.Filter(elastic.NewBoolQuery().Should(fs...))
	}
	for _, fn := range opts.filterNot {
		query.Filter(elastic.NewBoolQuery().MustNot(fn...))
	}

	ret, err := es.dscEsClient.GetClient().Search(es.idx).Query(query).
		FetchSourceContext(elastic.NewFetchSourceContext(true).Include(fields...)).
		TrackTotalHits(true).
		SortBy(elastic.NewFieldSort("id").Desc()).
		Do(ctx)

	if err != nil {
		logger.Error(ctx, "Elasticsearch query Count error", zap.Error(err))
		return 0, err
	}
	return ret.Hits.TotalHits.Value, nil
}

func (es *dscEsRepo) GetDiscordTabCount(ctx context.Context, options ...Option) (int64, error) {
	opts := newOptions(options...)
	query := elastic.NewBoolQuery()
	if len(opts.filter) > 0 {
		query.Filter(opts.filter...)
	}
	if len(opts.not) > 0 {
		query.Filter(elastic.NewBoolQuery().MustNot(opts.not...))
	}
	if len(opts.should) > 0 {
		query.Filter(elastic.NewBoolQuery().Should(opts.should...))
	}
	for _, fs := range opts.filterShould {
		query.Filter(elastic.NewBoolQuery().Should(fs...))
	}
	for _, fn := range opts.filterNot {
		query.Filter(elastic.NewBoolQuery().MustNot(fn...))
	}
	// Debugging: Print the query to be executed
	querySource, err := query.Source()
	if err != nil {
		logger.Error(ctx, "failed to get query source", zap.String("err", err.Error()))
		return 0, err
	}
	queryJSON, err := json.Marshal(querySource)
	if err != nil {
		logger.Error(ctx, "failed to marshal query to JSON", zap.String("err", err.Error()))
		return 0, err
	}
	logger.Info(ctx, "executing query", zap.String("query", string(queryJSON)))
	ret, err := es.dscEsClient.GetClient().Search(es.idx).Query(query).
		TrackTotalHits(true).
		Do(context.TODO())

	if err != nil {
		logger.Error(ctx, "get ticket info err", zap.Any("filter", opts), zap.String("err", err.Error()))
		return 0, err
	}
	return ret.Hits.TotalHits.Value, nil
}

func (es *dscEsRepo) UpdateMaintainer(ctx context.Context, channelId string, maintainer string) error {
	defer func() {
		if err := recover(); err != nil {
			logger.Errorf(ctx, "recover err: %v", err)
		}
	}()

	// 构造更新文档
	updateDoc := map[string]interface{}{
		"maintainer": maintainer,
	}

	// 执行更新请求
	resp, err := es.dscEsClient.GetClient().
		Update().
		Index(es.idx).
		Id(channelId).
		Doc(updateDoc).
		Refresh("true"). // 立即刷新以确保数据可见
		Do(ctx)

	// 错误处理
	if err != nil {
		logger.Error(ctx, "[UpdateMaintainConfig] Failed to update maintainer in ES", zap.String("channelId", channelId), zap.String("maintainer", maintainer), zap.String("err", err.Error()))
		return xerrors.New("EventUpdateFailLog", code.DbError)
	}

	// 检查更新结果
	if resp.Result != "updated" && resp.Result != "noop" {
		logger.Warn(ctx, "[UpdateMaintainConfig] Unexpected result while updating maintainer in ES", zap.String("channelId", channelId), zap.String("result", resp.Result))
	}

	// 成功日志
	logger.Info(ctx, "[UpdateMaintainConfig] Successfully updated maintainer in ES", zap.String("channelId", channelId), zap.String("result", resp.Result))
	return nil
}
