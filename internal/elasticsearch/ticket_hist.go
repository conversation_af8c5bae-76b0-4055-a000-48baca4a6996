// Copyright 2021 funplus Authors. All Rights Reserved.
// @Description:
// @Author: Darcy
// @Date: 2022/5/17 16:22

package elasticsearch

import (
	"context"
	"github.com/spf13/cast"
	"github.com/spf13/viper"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"go.uber.org/zap"
	"ops-ticket-api/models"
	"sync"

	"gitlab-ee.funplus.io/ops-tools/compkg/x/stores/es/client"
	"ops-ticket-api/internal/framework/stores/es"
)

var (
	onceHistEs           sync.Once
	esHistCli            *ticketHistSvc
	DefaultTicketHistSvc = newTicketHistSvc()
)

type ticketHistSvc struct {
	cli client.Client
}

func newTicketHistSvc() *ticketHistSvc {
	onceHistEs.Do(func() {
		ctx := context.Background()
		cli, err := es.GetCli(ctx)
		if err != nil {
			return
		}
		esHistCli = &ticketHistSvc{
			cli: cli,
		}
	})
	return esHistCli
}

func (es *ticketHistSvc) GetTicketHistEs(ctx context.Context) client.Client {
	return es.cli
}

func (es *ticketHistSvc) AddTicketHist(ctx context.Context, hist *models.FpOpsTicketsHistory) {
	fun := "ticketHistSvc.AddTicketHist"
	defer func() {
		if err := recover(); err != nil {
			logger.Errorf(ctx, "%s. recover err:%v", fun, err)
		}
	}()
	DefaultTicketSyncSvc.AddHist(ctx, hist.TicketID, hist)
	if err := es.cli.Add(ctx, viper.GetString("es.index.history"), cast.ToString(hist.ID), hist); err != nil {
		logger.Error(ctx, "[Create]Sync ticket hist to es err", zap.Uint64("ticket_id", hist.TicketID), zap.String("err", err.Error()))
	}
	return
}

//// IndexTicketHist 更新
//func (es *ticketHistSvc) IndexTicketHist(ctx context.Context, hist *models.FpOpsTicketsHistory) {
//	if err := es.cli.Add(ctx, viper.GetString("es.index.history"), cast.ToString(hist.ID), hist); err != nil {
//		logger.Error(ctx, "[Index]Sync ticket hist to es err", zap.Uint64("id", hist.ID), zap.Any("doc", hist), zap.String("err", err.Error()))
//	}
//	return
//}
