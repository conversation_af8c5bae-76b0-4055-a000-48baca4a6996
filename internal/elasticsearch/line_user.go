package elasticsearch

import (
	"context"
	"encoding/json"
	"github.com/olivere/elastic/v7"
	"github.com/spf13/cast"
	"github.com/spf13/viper"
	"gitlab-ee.funplus.io/ops-tools/compkg/dump"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"gitlab-ee.funplus.io/ops-tools/compkg/x/stores/es/client"
	"go.uber.org/zap"
	"io"
	"ops-ticket-api/internal/code"
	"ops-ticket-api/internal/framework/stores/es"
	"ops-ticket-api/models"
	"ops-ticket-api/proto/pb"
	"os"
	"strings"
	"sync"
	"time"
)

var (
	lineOnceEx       sync.Once
	lineEsCli        *lineEsRepo
	DefaultLineEsSvc = newLineRepo()
)

type lineEsRepo struct {
	idx          string
	lineEsClient client.Client
}

// newLineRepo init
func newLineRepo() *lineEsRepo {
	release := os.Getenv("release")
	if release == "cn" {
		return nil
	}
	lineOnceEx.Do(func() {
		ctx := context.Background()
		cli, err := es.GetLineCli(ctx)
		if err != nil {
			return
		}
		lineEsCli = &lineEsRepo{
			lineEsClient: cli,
			idx:          viper.GetString("line_es.index.line_index"),
		}
	})
	return lineEsCli
}

// UpsertLineUser 更新或新增用户
func (es *lineEsRepo) UpsertLineUser(ctx context.Context, lineUser *models.FpLineUser) error {
	var (
		fun = "lineEsRepo.UpsertLineUser"
	)
	defer func() {
		if err := recover(); err != nil {
			logger.Errorf(ctx, "%s recover err:%v", fun, err)
		}
	}()
	doc := models.LineUserAssignDoc(lineUser)
	scripts := []string{
		"ctx._source.id = params.id",
		"ctx._source.line_user_id = params.line_user_id",
		"ctx._source.channel_id = params.channel_id",
		"ctx._source.bot_id = params.bot_id",
		"ctx._source.display_name = params.display_name",
		"ctx._source.is_deleted = params.is_deleted",
		"ctx._source.follow_status = params.follow_status",
		"ctx._source.vip_state = params.vip_state",
		"ctx._source.created_at = params.created_at",
		"ctx._source.updated_at = params.updated_at",
	}
	param := map[string]interface{}{
		"id":            doc.ID,
		"line_user_id":  doc.LineUserID,
		"channel_id":    doc.ChannelID,
		"bot_id":        doc.BotID,
		"display_name":  doc.DisplayName,
		"is_deleted":    doc.IsDeleted,
		"follow_status": doc.FollowStatus,
		"vip_state":     doc.VipState,
		"created_at":    doc.CreatedAt,
		"updated_at":    time.Now().Unix(),
	}

	resp, err := es.lineEsClient.GetClient().Update().Index(es.idx).Id(doc.LineUserID).
		Script(elastic.NewScript(strings.Join(scripts, ";")).Params(param)).
		Upsert(doc).Refresh("true").Do(ctx)
	if err != nil {
		logger.Errorf(ctx, "%s upsert detail return err:%v", fun, err)
		return err
	}
	//dump.Dump(resp)
	if resp.Result != "updated" && resp.Result != "created" {
		logger.Warn(ctx, "line upsert result not updated or created", zap.String("result", resp.Result),
			zap.String("line_user_id", cast.ToString(doc.LineUserID)),
			zap.Any("doc", doc), zap.Any("param", param))
	}
	return nil
}

func (es *lineEsRepo) UpsertLineCommus(ctx context.Context, commu *models.FpLineCommu, setReplyTp bool, lastReplyService string) error {
	var (
		scripts   string
		param     map[string]interface{}
		msgFrom   = pb.DscMsgFromTpDf_DscMsgFromTpDfRobot
		replyType = pb.DscReplyTpDf_DscReplyTpDfReplied
	)
	if commu.LineUserID == "" {
		return nil
	}
	if commu.FromUserID == commu.LineUserID {
		msgFrom = pb.DscMsgFromTpDf_DscMsgFromTpDfUser
		replyType = pb.DscReplyTpDf_DscReplyTpDfUnReply
	}

	if commu.Content != "" { // add/update commun + update line user field
		scripts = `if (ctx._source.line_commu != null) {
			boolean found = false;
			for (int i = 0; i < ctx._source.line_commu.size(); ++i) {
				if (ctx._source.line_commu[i].msg_id == params.msg_id) {
					ctx._source.line_commu[i].content = params.content;
					found = true;
					break;
				}
			}
			if (!found) {
				Map newNestedObject = new HashMap();
				newNestedObject.put("msg_id", params.msg_id);
				newNestedObject.put("msg_from", params.msg_from);
				newNestedObject.put("from_user_id", params.from_user_id);
				newNestedObject.put("created_at", params.created_at);
				newNestedObject.put("content", params.content);
				ctx._source.line_commu.add(newNestedObject);
			}
		}
		ctx._source.updated_at=params.updated_at;
		`
	} else { // del commu + update dsc user field
		scripts = `if (ctx._source.line_commu != null) {
			ctx._source.line_commu.removeIf(nested -> nested.msg_id.equals(params.msg_id));
		}
		ctx._source.updated_at=params.updated_at;
		`
	}
	if setReplyTp { // update 不修改 回复状态
		scripts += `ctx._source.reply_type=params.reply_type;`
	}
	// 如果这个消息是客服发给玩家的，才需要修改玩家的最近处理人(客服)
	if commu.FromUserID == commu.BotID {
		scripts += `ctx._source.last_reply_service=params.last_reply_service;`
	}
	param = map[string]interface{}{
		"msg_id":             commu.MsgID,
		"msg_from":           msgFrom,
		"from_user_id":       commu.FromUserID,
		"content":            commu.Content,
		"created_at":         commu.CreatedAt.Unix(),
		"updated_at":         time.Now().Unix(),
		"reply_type":         replyType,
		"last_reply_service": lastReplyService,
	}
	result, err := es.lineEsClient.GetClient().Update().Index(es.idx).Id(commu.LineUserID).
		Script(elastic.NewScript(scripts).Params(param)).Refresh("true").Do(ctx)
	if err != nil {
		logger.Errorf(ctx, "%s. UpsertLineCommus err:%v. param:%+v", err, param)
		return err
	}
	dump.Dump("commu:", result, param)
	if result.Result != "updated" && result.Result != "created" {
		logger.Warn(ctx, "line upsert result not updated or created", zap.String("result", result.Result),
			zap.String("msg_id", commu.MsgID), zap.Any("param", param))
	}
	return nil
}

func (es *lineEsRepo) DelLineCommus(ctx context.Context, lineUserId string, msgId []string) error {
	var (
		fun = "lineEsRepo.DelLineCommus"
	)
	if len(msgId) == 0 {
		return nil
	}
	scripts := `if (ctx._source.line_commu != null) {
			ctx._source.line_commu.removeIf(nested -> params.msg_id.contains(nested.msg_id));
		}
		ctx._source.updated_at=params.updated_at;
		`
	param := map[string]interface{}{
		"msg_id":     msgId,
		"updated_at": time.Now().Unix(),
	}
	resp, err := es.lineEsClient.GetClient().Update().Index(es.idx).Id(lineUserId).
		Script(elastic.NewScript(scripts).Params(param)).
		Refresh("true").Do(ctx)
	if err != nil {
		logger.Errorf(ctx, "%s del detail return err:%v. param:%+v", fun, err, param)
		return err
	}
	if resp.Result != "updated" {
		logger.Warn(ctx, "line del result not deleted or noop", zap.String("result", resp.Result),
			zap.Any("msg_id", msgId), zap.String("line_user_id", lineUserId))
	}
	dump.Dump("DelLineCommus:", resp, param)
	return nil
}

func (es *lineEsRepo) GetLineAccountsCount(ctx context.Context, projects []interface{}) (int64, error) {
	fields := []string{"line_user_id", "display_name", "channel_id", "maintainer", "bot_id"}
	query := elastic.NewBoolQuery()
	if len(projects) > 0 {
		query = query.Filter(elastic.NewTermsQuery("project", projects...))
	}
	query = query.Filter(elastic.NewTermsQuery("is_deleted", code.DscNoDelete))
	ret, err := es.lineEsClient.GetClient().Search(es.idx).
		Query(query).
		FetchSourceContext(elastic.NewFetchSourceContext(true).Include(fields...)).
		TrackTotalHits(true).
		SortBy(elastic.NewFieldSort("id").Desc()).
		Do(ctx)

	if err != nil {
		logger.Error(ctx, "Elasticsearch query GetLineAccountsCount error", zap.Error(err))
		return 0, err
	}
	return ret.Hits.TotalHits.Value, nil
}

func (es *lineEsRepo) WaitReplyAccountsCount(ctx context.Context, projects []interface{}) (int64, error) {
	fields := []string{"line_user_id", "display_name", "channel_id", "maintainer", "bot_id"}
	query := elastic.NewBoolQuery()
	if len(projects) > 0 {
		query = query.Filter(elastic.NewTermsQuery("project", projects...))
	}
	query = query.Filter(elastic.NewTermsQuery("is_deleted", code.DscNoDelete))
	query = query.Filter(elastic.NewTermsQuery("reply_type", pb.DscReplyTpDf_DscReplyTpDfUnReply))
	ret, err := es.lineEsClient.GetClient().Search(es.idx).
		Query(query).
		FetchSourceContext(elastic.NewFetchSourceContext(true).Include(fields...)).
		TrackTotalHits(true).
		SortBy(elastic.NewFieldSort("id").Desc()).
		Do(ctx)

	if err != nil {
		logger.Error(ctx, "Elasticsearch query line WaitReplyAccountsCount error", zap.Error(err))
		return 0, err
	}
	return ret.Hits.TotalHits.Value, nil
}

func (es *lineEsRepo) MineWaitReplyAccountsCount(ctx context.Context, maintainer string, projects []interface{}) (int64, error) {
	fields := []string{"line_user_id", "display_name", "channel_id", "maintainer", "bot_id"}
	query := elastic.NewBoolQuery()
	if len(projects) > 0 {
		query = query.Filter(elastic.NewTermsQuery("project", projects...))
	}
	query = query.Filter(elastic.NewTermsQuery("is_deleted", code.DscNoDelete))
	query = query.Filter(elastic.NewTermsQuery("reply_type", pb.DscReplyTpDf_DscReplyTpDfUnReply))
	// query = query.Filter(elastic.NewTermsQuery("maintainer", maintainer))
	query = query.Filter(elastic.NewTermsQuery("last_reply_service", maintainer))
	ret, err := es.lineEsClient.GetClient().Search(es.idx).
		Query(query).
		FetchSourceContext(elastic.NewFetchSourceContext(true).Include(fields...)).
		TrackTotalHits(true).
		SortBy(elastic.NewFieldSort("id").Desc()).
		Do(ctx)

	if err != nil {
		logger.Error(ctx, "Elasticsearch query line WaitReplyAccountsCount error", zap.Error(err))
		return 0, err
	}
	return ret.Hits.TotalHits.Value, nil
}

func (es *lineEsRepo) GetLinePool(ctx context.Context, page, pageSize int, fieldsExt []string, sort []elastic.Sorter, options ...Option) ([]*elastic.SearchHit, int64, error) {
	fields := []string{"line_user_id", "display_name", "channel_id", "follow_status", "maintainer", "bot_id", "uid", "account_id", "sid", "reply_type", "pay_all", "pay_last_thirty_days", "last_login", "vip_state", "vip_level", "note", "player_nick", "birthday", "lang"}
	if len(fieldsExt) > 0 {
		fields = append(fields, fieldsExt...)
	}

	opts := newOptions(options...)
	query := elastic.NewBoolQuery()
	if len(opts.filter) > 0 {
		query.Filter(opts.filter...)
	}
	if len(opts.not) > 0 {
		query.Filter(elastic.NewBoolQuery().MustNot(opts.not...))
	}
	if len(opts.should) > 0 {
		query.Filter(elastic.NewBoolQuery().Should(opts.should...))
	}
	for _, fs := range opts.filterShould {
		query.Filter(elastic.NewBoolQuery().Should(fs...))
	}
	for _, fn := range opts.filterNot {
		query.Filter(elastic.NewBoolQuery().MustNot(fn...))
	}
	if sort == nil {
		sort = []elastic.Sorter{elastic.NewFieldSort("created_at").Desc()}
	}
	ret, err := es.lineEsClient.GetClient().Search(es.idx).Query(query).
		FetchSourceContext(elastic.NewFetchSourceContext(true).Include(fields...)).
		TrackTotalHits(true).
		From((page - 1) * pageSize).
		Size(pageSize).
		SortBy(sort...).
		Do(context.TODO())

	if err != nil {
		logger.Error(ctx, "get line info err", zap.Any("filter", opts), zap.String("err", err.Error()))
		return nil, 0, err
	}
	return ret.Hits.Hits, ret.Hits.TotalHits.Value, nil
}

func (es *lineEsRepo) GetLinePoolScroll(ctx context.Context, options ...Option) (<-chan *models.FpLineUserDoc, error) {
	opts := newOptions(options...)
	query := elastic.NewBoolQuery()
	if len(opts.filter) > 0 {
		query.Filter(opts.filter...)
	}
	if len(opts.not) > 0 {
		query.Filter(elastic.NewBoolQuery().MustNot(opts.not...))
	}
	if len(opts.should) > 0 {
		query.Filter(elastic.NewBoolQuery().Should(opts.should...))
	}
	for _, fs := range opts.filterShould {
		query.Filter(elastic.NewBoolQuery().Should(fs...))
	}
	for _, fn := range opts.filterNot {
		query.Filter(elastic.NewBoolQuery().MustNot(fn...))
	}
	searchResult, scrollErr := es.lineEsClient.OpenScroll(ctx, &client.SearchParameters{
		Index:    es.idx,
		Query:    query,
		PageSize: pageSize,
		Sorter:   []elastic.Sorter{elastic.NewFieldSort("id").Desc()},
	}, scrollKeepAliveInterval)
	if scrollErr != nil {
		return nil, scrollErr
	}
	lineCh := make(chan *models.FpLineUserDoc, 256)
	go func() {
		defer func() {
			if err := recover(); err != nil {
				logger.Errorf(ctx, "recover err:%v", err)
			}
			if searchResult != nil {
				if err := es.lineEsClient.CloseScroll(ctx, searchResult.ScrollId); err != nil {
					logger.Error(ctx, "scroll close err", zap.String("err", err.Error()))
				}
			}
			close(lineCh)
		}()
		var num int
		for {
			select {
			case <-ctx.Done():
				return
			default:
				if searchResult.Hits != nil {
					for _, hit := range searchResult.Hits.Hits {
						line := &models.FpLineUserDoc{}
						source, err := hit.Source.MarshalJSON()
						if err != nil {
							logger.Error(ctx, "json marshal err", zap.String("err", err.Error()))
						}
						if err := json.Unmarshal(source, &line); err != nil {
							logger.Error(ctx, "json unmarshal err", zap.String("err", err.Error()))
							return
						}
						lineCh <- line
						num++
					}
					if len(searchResult.Hits.Hits) < pageSize {
						return
					}
					if num >= 10*pageSize { // 做多获取 10页数据
						return
					}
				}
				searchResult, scrollErr = es.lineEsClient.Scroll(ctx, searchResult.ScrollId, scrollKeepAliveInterval)
				if scrollErr == io.EOF {
					return
				}
			}
		}
	}()
	return lineCh, nil
}

func (es *lineEsRepo) GetLineTabCount(ctx context.Context, options ...Option) (int64, error) {
	opts := newOptions(options...)
	query := elastic.NewBoolQuery()
	if len(opts.filter) > 0 {
		query.Filter(opts.filter...)
	}
	if len(opts.not) > 0 {
		query.Filter(elastic.NewBoolQuery().MustNot(opts.not...))
	}
	if len(opts.should) > 0 {
		query.Filter(elastic.NewBoolQuery().Should(opts.should...))
	}
	for _, fs := range opts.filterShould {
		query.Filter(elastic.NewBoolQuery().Should(fs...))
	}
	for _, fn := range opts.filterNot {
		query.Filter(elastic.NewBoolQuery().MustNot(fn...))
	}
	// Debugging: Print the query to be executed
	querySource, err := query.Source()
	if err != nil {
		logger.Error(ctx, "failed to get query source", zap.String("err", err.Error()))
		return 0, err
	}
	queryJSON, err := json.Marshal(querySource)
	if err != nil {
		logger.Error(ctx, "failed to marshal query to JSON", zap.String("err", err.Error()))
		return 0, err
	}
	logger.Info(ctx, "executing query", zap.String("query", string(queryJSON)))
	ret, err := es.lineEsClient.GetClient().Search(es.idx).Query(query).
		TrackTotalHits(true).
		Do(context.TODO())

	if err != nil {
		logger.Error(ctx, "get line info err", zap.Any("filter", opts), zap.String("err", err.Error()))
		return 0, err
	}
	return ret.Hits.TotalHits.Value, nil
}
