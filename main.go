// Copyright 2021 funplus Authors. All Rights Reserved.
// @Description:
// @Author: Darcy
// @Date: 2021/10/13 2:06 PM

package main

import (
	"fmt"
	"github.com/opentracing/opentracing-go"
	"github.com/spf13/viper"
	"github.com/uber/jaeger-client-go"
	jaegerCfg "github.com/uber/jaeger-client-go/config"
	"io"
	ai_model "ops-ticket-api/internal/ai"
	_ "ops-ticket-api/internal/framework/cfg"

	"ops-ticket-api/commands"
	"ops-ticket-api/commands/cmds"
	"ops-ticket-api/commands/scripts"
	"ops-ticket-api/pkg/game"
)

// -
func main() {
	// 初始化trace
	_, closer := initJaeger(viper.GetString("app.name"))
	defer closer.Close()

	// 初始化game
	_ = game.NewGameInfo()
	ai_model.Init()                                                                         // 初始化openai客户端
	commands.Register(commands.Service("serve", cmds.Serve))                                // 启动服务
	commands.Register(commands.Service("DscGatewayEvent", cmds.DscGatewayEvent))            // discord gateway event watch
	commands.Register(commands.Service("DscSyncAllUserAndCommu", cmds.DscSyncUserAndCommu)) // discord sync user and commu
	commands.Register(commands.Service("syncCategoryFromOld", scripts.SyncTicketCategoryFromOldTicket))
	commands.Register(commands.Service("syncOpsTicketReport", scripts.SyncTicketReportTmpLogicGroupByCat))
	commands.Register(commands.Service("syncDataPlatGameItems", scripts.SyncDataPlatGameItems2Db))
	commands.Register(commands.Service("SyncTicketByIdToEs", scripts.SyncTicketDetailToEs))
	//commands.Register(commands.Service("SyncYesterdayDiscordReplyTimeDetail", scripts.SyncYesterdayDiscordReplyTimeDetail)) //同步回复时间表脚本
	commands.Register(commands.Service("LineGatewayEvent", cmds.LineGatewayEvent)) // line gateway event
	//commands.Register(commands.Service("SyncYesterdayDiscordReplyTimeDetail", scripts.SyncYesterdayDiscordReplyTimeDetail)) //todo 同步DC用户标签
	commands.Register(commands.Service("UpdateMaintainerJob", scripts.UpdateMaintainerJob)) //更新批uid维护人脚本
	commands.Register(commands.Service("SyncUpgradeNum", scripts.SyncUpgradeNum))           // line gateway event
	//commands.Register(commands.Service("FetchCsiTicket", scripts.FetchCsiTicket))           // lFetchCsiTicket
	//commands.Register(commands.Service("FetchDscCommu", scripts.FetchDscCommu))             // lFetchCsiTicket
	//commands.Register(commands.Service("AiPortraitTag", scripts.AiPortraitTag))             // AiPortraitTag
	commands.Register(commands.Service("FetchAlgorithmFix", scripts.FetchAlgorithmFix)) // FetchAlgorithmFix
	commands.Register(commands.Service("TwoWeeksDemo", scripts.TwoWeeksDemo))           // FetchAlgorithmFix

	// 注册定时任务相关命令
	commands.Register(cmds.GetCronCommand()) // 注册cron命令及其子命令
	commands.Run()
}

// 初始化trace
func initJaeger(serviceName string) (opentracing.Tracer, io.Closer) {
	cfg := &jaegerCfg.Configuration{
		ServiceName: serviceName,
		Sampler: &jaegerCfg.SamplerConfig{
			Type:  jaeger.SamplerTypeRateLimiting,
			Param: 10,
		},
		//配置Reporter agent
		Reporter: &jaegerCfg.ReporterConfig{
			LogSpans: false,
			//CollectorEndpoint: "http://jaeger-prod-collector-headless.observability.svc:14268/api/traces",
		},
	}
	tracer, closer, err := cfg.NewTracer(jaegerCfg.Logger(jaeger.StdLogger))
	if err != nil {
		panic(fmt.Sprintf("ERROR: cannot init Jaeger: %v\n", err))
	}
	//设置项目全局trace
	opentracing.SetGlobalTracer(tracer)
	return tracer, closer
}
