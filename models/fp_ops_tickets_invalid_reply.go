package models

import "time"

// FpOpsTicketsInvalidReply 无效单模版配置表
type FpOpsTicketsInvalidReply struct {
	ID        uint32    `gorm:"primaryKey;column:id;type:int(11) unsigned;not null"`
	Project   string    `gorm:"column:project;type:varchar(64);not null;default:''"`                // 所属项目
	Lang      string    `gorm:"column:lang;type:varchar(64);not null;default:''"`                   // 语种
	ReplyTpl  string    `gorm:"uniqueIndex:idx_prj_tpl;column:reply_tpl;type:varchar(32);not null"` // 回复模版名称
	Operator  string    `gorm:"column:operator;type:varchar(64);not null;default:''"`               // 更新用户
	CreatedAt time.Time `gorm:"column:created_at;type:datetime"`                                    // 创建时间
	UpdatedAt time.Time `gorm:"column:updated_at;type:datetime"`                                    // 更新时间
}
