package models

// FpOpsTicketsAiTag AI标签表
type FpOpsTicketsAiTag struct {
	TagID     uint32 `gorm:"primaryKey;column:tag_id;type:int(11) unsigned;not null"`
	TicketID  uint64 `gorm:"index:idx_tik;column:ticket_id;type:bigint(20) unsigned;not null"`
	LibName   string `gorm:"column:lib_name;type:varchar(64);not null;default:''"`        // 标签库名称
	FirstTag  string `gorm:"column:first_tag;type:varchar(64);not null;default:''"`       // 一级名称
	SecondTag string `gorm:"column:second_tag;type:varchar(64);not null;default:''"`      // 二级名称
	ThirdTag  string `gorm:"column:third_tag;type:varchar(64);not null;default:''"`       // 三级名称
	TagType   uint8  `gorm:"column:tag_type;type:tinyint(1) unsigned;not null;default:0"` // 标签类型(0未知1客询2客诉3建议4其他)
	CreatedAt uint64 `gorm:"column:created_at;type:bigint(20);not null;default:0"`        // 创建时间
	UpdatedAt uint64 `gorm:"column:updated_at;type:bigint(20);not null;default:0"`        // 更新时间
}
