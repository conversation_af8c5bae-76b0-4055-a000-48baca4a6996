// Copyright 2021 funplus Authors. All Rights Reserved.
// @Description:
// @Author: Darcy
// @Date: 2022/4/15 6:35 PM

package models

// FpOpsTicketsTags 工单标签表
type FpOpsTicketsTags struct {
	ID        uint64    `gorm:"primaryKey;column:id;type:bigint(20) unsigned;not null"`
	TicketID  uint64    `gorm:"uniqueIndex:idx_ticket_tag;column:ticket_id;type:bigint(20);not null"` // 工单ID
	TagID     uint32    `gorm:"uniqueIndex:idx_ticket_tag;column:tag_id;type:int(11);not null"`       // 标签ID
	Op        string    `gorm:"column:op;type:varchar(64);not null;default:''"`                       // 添加人员
	CreatedAt uint64    `gorm:"column:created_at;type:bigint(20) unsigned;not null;default:0"`        // 创建时间
	UpdatedAt uint64    `gorm:"column:updated_at;type:bigint(20) unsigned;not null;default:0"`        // 更新时间
	TagItem   FpOpsTags `gorm:"foreignKey:TagID;references:TagID"`
}
