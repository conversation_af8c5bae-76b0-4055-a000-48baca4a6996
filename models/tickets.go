package models

import "ops-ticket-api/proto/pb"

type (
	TicketCreateParam struct {
		Input       pb.TkCreateReq
		CatIdFirst  uint32 // 一级分类
		CatIdSecond uint32 // 二级分类
		Ip          string // ip地址
		TroubleUser TicketUser
		SubmitUser  TicketUser
	}

	TicketUser struct {
		Uid            int64
		AccountID      string
		VipCrm         int8
		VipCrmPriority int8
		VipType        uint16
		UserType       int8
		SVIP           int8
		TotalPay       float64
		Channel        string
	}

	DwhData struct {
		AccountID       string `json:"FPID"`
		PackageChannel  string `json:"PACKAGE_CHANNEL"`
		TotalRevenueUSD string `json:"TOTAL_REVENUE_USD"`
		Uid             string `json:"UID"`
		VIPLevel        int    `json:"VIP_LEVEL"`
	}
)
