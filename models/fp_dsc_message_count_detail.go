package models

import "time"

// FpDscMessageCountDetail  discord玩家和客服信息量明细表
type FpDscMessageCountDetail struct {
	ID                  uint64    `gorm:"primaryKey;column:id;type:bigint(20) unsigned;not null"`
	Project             string    `gorm:"column:project;type:varchar(32);not null;default:''"`                                                // 所属项目
	InteractDate        string    `gorm:"column:interact_date;type:varchar(32);not null;default:''"`                                          // 交互日期
	DscUserID           string    `gorm:"column:dsc_user_id;type:varchar(32);not null;default:''"`                                            // 玩家 discord_user_id
	NickName            string    `gorm:"column:nick_name;type:varchar(64);not null;default:''" json:"nick_name"`                             // 玩家昵称
	UID                 uint64    `gorm:"column:uid;type:bigint(20) unsigned;not null;default:0" json:"uid"`                                  // 玩家uid
	AccountId           string    `gorm:"column:account_id;type:varchar(64);not null;default:''" json:"account_id"`                           // 玩家的account_id(fpid)
	PlayerMessageCount  int64     `gorm:"column:player_message_count;type:int(10) unsigned;not null;default:0" json:"player_message_count"`   // 玩家信息量
	Operator            string    `gorm:"column:operator;type:varchar(64);not null;default:''"`                                               // 客服处理人
	ServiceMessageCount int64     `gorm:"column:service_message_count;type:int(10) unsigned;not null;default:0" json:"service_message_count"` // 客服信息量
	CreatedAt           time.Time `gorm:"index:idx_create_at;column:created_at;type:datetime;default:null;default:CURRENT_TIMESTAMP"`         // 创建时间
}

func GetFpDscMessageCountDetailTableName() string {
	return "fp_dsc_message_count_detail"
}

type SumMessageCountStats struct {
	PlayerMessageCount  int64 `json:"player_message_count"`
	ServiceMessageCount int64 `json:"service_message_count"`
}
