package models

import (
	"ops-ticket-api/internal/code"
	"ops-ticket-api/proto/pb"
	"time"
)

// FpLineNotice line 服务端通知消息
type FpLineNotice struct {
	ID           uint64    `gorm:"primaryKey;column:id;type:bigint(20) unsigned;not null"`
	AppID        string    `gorm:"index:idx_app_id;column:app_id;type:varchar(128);not null;default:''"`                       // 所属应用
	Handle       string    `gorm:"index:idx_app_id;column:handle;type:varchar(64);not null;default:''"`                        // 请求接口
	HeaderDetail string    `gorm:"column:header_detail;type:varchar(2048);not null;default:''"`                                // header信息
	BodyDetail   string    `gorm:"column:body_detail;type:mediumtext;default:null"`                                            // body详情
	CreatedAt    time.Time `gorm:"index:idx_create_at;column:created_at;type:datetime;default:null;default:CURRENT_TIMESTAMP"` // 创建时间
}

// FpLineBot line bot 机器人信息
type FpLineBot struct {
	ID          uint64    `gorm:"primaryKey;column:id;type:bigint(20) unsigned;not null"`
	Project     string    `gorm:"column:project;type:varchar(64);not null;default:''"`                                        // 所属项目
	BotID       string    `gorm:"column:bot_id;type:varchar(128);not null;default:''"`                                        // 机器人的user_id
	ChannelID   string    `gorm:"column:channel_id;type:varchar(512);not null;default:''"`                                    // 机器人对应的频道id
	BasicID     string    `gorm:"column:basic_id;type:varchar(512);not null;default:''"`                                      // 机器人的basic_id
	PremiumID   string    `gorm:"column:premium_id;type:varchar(512);not null;default:''"`                                    // 付费账户id
	DisplayName string    `gorm:"column:display_name;type:varchar(512);not null;default:''"`                                  // line上展示的用户名
	PictureUrl  string    `gorm:"column:picture_url;type:varchar(512);not null;default:''"`                                   // 头像url
	ChatMode    string    `gorm:"column:chat_mode;type:varchar(64);not null;default:''"`                                      // 聊天模式
	ReadMode    string    `gorm:"column:read_mode;type:varchar(64);not null;default:''"`                                      // 回复模式
	IsDeleted   uint8     `gorm:"column:is_deleted;type:tinyint(1);not null;default:1"`                                       // 是否已删除 1默认正常使用 2已删除
	CreatedAt   time.Time `gorm:"index:idx_create_at;column:created_at;type:datetime;default:null;default:CURRENT_TIMESTAMP"` // 创建时间
	UpdatedAt   time.Time `gorm:"column:updated_at;type:datetime;default:null;default:CURRENT_TIMESTAMP"`                     // 更新时间
}

// FpLineUser Line私信用户维护列表
type FpLineUser struct {
	ID           uint64    `gorm:"primaryKey;column:id;type:bigint(20) unsigned;not null"`
	Project      string    `gorm:"column:project;type:varchar(64);not null;default:''"`                                        // 所属项目
	BotID        string    `gorm:"column:bot_id;type:varchar(128);not null;default:''"`                                        // 机器人的user_id
	LineUserID   string    `gorm:"column:line_user_id;type:varchar(128);not null;default:''"`                                  // 用户 line_user_id
	ChannelID    string    `gorm:"column:channel_id;type:varchar(512);not null;default:''"`                                    // 用户私聊 channel id
	DisplayName  string    `gorm:"column:display_name;type:varchar(512);not null;default:''"`                                  // line 用户名
	PictureUrl   string    `gorm:"column:picture_url;type:varchar(512);not null;default:''"`                                   // 头像url
	JoinedAt     time.Time `gorm:"column:joined_at;type:datetime;default:null;default:CURRENT_TIMESTAMP"`                      // 新加好友时间
	FollowStatus uint8     `gorm:"column:follow_status;type:tinyint(1) unsigned;not null;default:1" json:"follow_status"`      // 关注状态 1:正常follow状态; 2:unfollow玩家解绑状态
	IsDeleted    uint8     `gorm:"column:is_deleted;type:tinyint(1) unsigned;not null;default:1" json:"is_deleted"`            // is_deleted 标记是否已删除，1默认未删除 2已删除
	CreatedAt    time.Time `gorm:"index:idx_create_at;column:created_at;type:datetime;default:null;default:CURRENT_TIMESTAMP"` // 创建时间
	UpdatedAt    time.Time `gorm:"column:updated_at;type:datetime;default:null;default:CURRENT_TIMESTAMP"`                     // 更新时间
}

// FpLineChannel line channel列表
type FpLineChannel struct {
	ID              uint64    `gorm:"primaryKey;column:id;type:bigint(20) unsigned;not null"`
	Project         string    `gorm:"column:project;type:varchar(64);not null;default:''"`                                        // 所属项目
	BotID           string    `gorm:"column:bot_id;type:varchar(128);not null;default:''"`                                        // 所属应用/机器人bot的 user_id
	Provider        string    `gorm:"column:provider;type:varchar(128);not null;default:''"`                                      // 频道归属的provider
	ChannelID       string    `gorm:"column:channel_id;type:varchar(512);not null;default:''"`                                    // 频道id
	Secret          string    `gorm:"column:secret;type:text;not null;default:''"`                                                // 频道密钥
	AccessToken     string    `gorm:"column:access_token;type:text;not null;default:''"`                                          // 频道access token
	WebhookUrl      string    `gorm:"column:webhook_url;type:text;not null;default:''"`                                           // 频道webhook url
	GreetingMessage string    `gorm:"column:greeting_message;type:text;not null;default:''"`                                      // 新加好友的打招呼消息(欢迎语)
	IsDeleted       uint8     `gorm:"column:is_deleted;type:tinyint(1) unsigned;not null;default:1" json:"is_deleted"`            // is_deleted 标记是否已删除，1默认未删除 2已删除
	CreatedAt       time.Time `gorm:"index:idx_create_at;column:created_at;type:datetime;default:null;default:CURRENT_TIMESTAMP"` // 创建时间
	UpdatedAt       time.Time `gorm:"column:updated_at;type:datetime;default:null;default:CURRENT_TIMESTAMP"`                     // 更新时间
}

// FpLineCommu line 消息列表
type FpLineCommu struct {
	ID                 uint64    `gorm:"primaryKey;column:id;type:bigint(20) unsigned;not null" json:"id"`
	MsgID              string    `gorm:"unique;column:msg_id;type:varchar(512);not null;default:''" json:"msg_id"`                                     // 消息id
	Project            string    `gorm:"column:project;type:varchar(64);not null;default:''" json:"project"`                                           // 所属项目
	FromUserID         string    `gorm:"index:idx_frm_uid;column:from_user_id;type:varchar(128);not null;default:''" json:"from_user_id"`              // 消息来源id：user端:user_id, 客服回复:bot_id
	BotID              string    `gorm:"column:bot_id;type:varchar(128);not null;default:''" json:"bot_id"`                                            // 所属应用/机器人bot的 user_id
	LineUserID         string    `gorm:"column:line_user_id;type:varchar(128);not null;default:''" json:"line_user_id"`                                // 用户 line_user_id
	ChannelID          string    `gorm:"index:idx_channel;column:channel_id;type:varchar(512);not null;default:''" json:"channel_id"`                  // 用户私聊 channel id
	Content            string    `gorm:"column:content;type:text;not null" json:"content"`                                                             // 消息内容
	EventId            string    `gorm:"column:event_id;type:varchar(512);not null;default:''" json:"event_id"`                                        // webhook event id
	QuoteToken         string    `gorm:"column:quote_token;type:varchar(512);not null;default:''" json:"quote_token"`                                  // 引用token
	MessageType        uint8     `gorm:"column:message_type;type:tinyint;not null;default:0" json:"message_type"`                                      // 消息类型 1文本和表情;2贴纸;3图片;4视频;5音频;6文件;7位置 8其他
	Redelivery         bool      `gorm:"column:redelivery;type:bool;not null;default:false" json:"redelivery"`                                         // 消息是否是webhook重复推送
	Revoke             bool      `gorm:"column:revoke;type:bool;not null;default:false" json:"revoke"`                                                 // 消息是否已被用户撤回
	ContentProvider    string    `gorm:"column:content_provider;type:varchar(64);default:''" json:"content_provider"`                                  // 文件存储位置 line和external
	OriginalContentUrl string    `gorm:"column:original_content_url;type:text;not null" json:"original_content_url"`                                   // 文件原始url
	PreviewImageUrl    string    `gorm:"column:preview_image_url;type:text;not null" json:"preview_image_url"`                                         // 文件预览url
	Duration           uint64    `gorm:"column:duration;type:int(11);not null;default:0" json:"duration"`                                              // 音频/视频时长
	StickerInfo        string    `gorm:"column:sticker_info;type:text;not null;default:''" json:"sticker_info"`                                        // 贴纸信息,包含package_id和sticker_id以及sticker_resource_type
	Location           string    `gorm:"column:location;type:text;not null;default:''" json:"location"`                                                // 位置
	TickTime           uint64    `gorm:"column:tick_time;type:bigint(20) unsigned;not null;default:0" json:"tick_time"`                                // 消息的时间戳
	CreatedAt          time.Time `gorm:"index:idx_create_at;column:created_at;type:datetime;default:null;default:CURRENT_TIMESTAMP" json:"created_at"` // 创建时间
	UpdatedAt          time.Time `gorm:"column:updated_at;type:datetime;default:null;default:CURRENT_TIMESTAMP" json:"updated_at"`                     // 更新时间
}

func LineUserAssignDoc(lineUser *FpLineUser) *FpLineUserDoc {
	return &FpLineUserDoc{
		ID:             lineUser.ID,
		LineUserID:     lineUser.LineUserID,
		ChannelID:      lineUser.ChannelID,
		BotID:          lineUser.BotID,
		Project:        lineUser.Project,
		DisplayName:    lineUser.DisplayName,
		LineCommu:      make([]*LineCommuDoc, 0),
		PortraitRemark: "",
		Maintainer:     "",
		VipState:       code.NonVip,
		ReplyType:      uint8(pb.DscReplyTpDf_DscReplyTpDfReplied),
		FollowStatus:   lineUser.FollowStatus,
		IsDeleted:      lineUser.IsDeleted,
		CreatedAt:      uint64(time.Now().UTC().Unix()),
		UpdatedAt:      uint64(time.Now().UTC().Unix()),
	}
}

// FpLineSend 客服回复消息绑定
type FpLineSend struct {
	ID        uint64    `gorm:"primaryKey;column:id;type:bigint(20) unsigned;not null"`
	MsgID     string    `gorm:"unique;column:msg_id;type:varchar(512);not null;default:''"`                                 // 消息id
	BotID     string    `gorm:"column:bot_id;type:varchar(128);not null;default:''"`                                        // 所属应用/机器人bot的 user_id
	ChannelID string    `gorm:"index:idx_channel;column:channel_id;type:varchar(512);not null;default:''"`                  // 用户私聊 channel id
	Operator  string    `gorm:"column:operator;type:varchar(64);not null;default:''"`                                       // 操作人
	CreatedAt time.Time `gorm:"index:idx_create_at;column:created_at;type:datetime;default:null;default:CURRENT_TIMESTAMP"` // 创建时间
	UpdatedAt time.Time `gorm:"column:updated_at;type:datetime;default:null;default:CURRENT_TIMESTAMP"`                     // 更新时间
}

// FpLinePlayerMaintainConfig 玩家维护关系配置表
type FpLinePlayerMaintainConfig struct {
	ID                uint64 `gorm:"primaryKey;column:id;type:bigint(20) unsigned;not null" json:"id"`
	LineUserId        string `gorm:"column:line_user_id;type:varchar(128);not null;default:''" json:"line_user_id"`                 // discord上的玩家用户id
	AccountId         string `gorm:"column:account_id;type:varchar(64);not null;default:''" json:"account_id"`                      // discord上玩家的account_id(fpid)
	UID               uint64 `gorm:"column:uid;type:bigint(20) unsigned;not null;default:0" json:"uid"`                             // 玩家uid
	Sid               string `gorm:"column:sid;type:varchar(64);not null;default:''" json:"sid"`                                    // discord上玩家的服务器
	Project           string `gorm:"column:project;type:varchar(64);not null;default:''" json:"project"`                            // 游戏
	NickName          string `gorm:"column:nick_name;type:varchar(64);not null;default:''" json:"nick_name"`                        // 昵称
	VipLevel          uint8  `gorm:"column:vip_level;type:tinyint(3);not null;default:0" json:"vip_level"`                          // vip等级
	PayAll            int64  `gorm:"column:pay_all;type:bigint(20);not null;default:0" json:"pay_all"`                              // 总付费 *10000
	PayLastThirtyDays int64  `gorm:"column:pay_last_thirty_days;type:bigint(20);not null;default:0" json:"pay_last_thirty_days"`    // 最近30天付费 *10000
	LastLogin         string `gorm:"column:last_login;type:varchar(20);not null;default:'0000-00-00 00:00:00'" json:"last_login"`   // 最后登录时间
	VipState          uint8  `gorm:"column:vip_state;type:tinyint(1);not null;default:0" json:"vip_state"`                          // vip状态 1非vip,2vip
	Maintainer        string `gorm:"column:maintainer;type:varchar(64);not null;default:''" json:"maintainer"`                      // 维护专员
	Operator          string `gorm:"column:operator;type:varchar(64);not null;default:''" json:"operator"`                          // 操作人
	PlayerNick        string `gorm:"column:player_nick;type:varchar(64);not null;default:''" json:"player_nick"`                    // 玩家在游戏的昵称
	Lang              string `gorm:"column:lang;type:varchar(64);not null;default:''" json:"lang"`                                  // 语种
	CreateTime        uint64 `gorm:"column:create_time;type:bigint(20) unsigned;not null;default:0" json:"create_time"`             // 创建时间
	UpdateTime        string `gorm:"column:update_time;type:varchar(64);not null;default:'1900-01-01 00:00:00'" json:"update_time"` // 更新时间
}

// FpLineOperateLog line 操作相关日志
type FpLineOperateLog struct {
	ID              uint64    `gorm:"primaryKey;column:id;type:bigint unsigned;not null"`                                      // 自增id
	OperationGroup  string    `gorm:"index:gact_uni_id;column:operation_group;type:varchar(32);not null;default:''"`           // 具体模块分组
	OperationAction string    `gorm:"index:gact_uni_id;column:operation_action;type:varchar(32);not null;default:''"`          // 具体操作action
	BaseID          string    `gorm:"column:base_id;type:varchar(64);not null;default:''"`                                     // 业务id
	UniqueID        string    `gorm:"index:unique_id;index:gact_uni_id;column:unique_id;type:varchar(64);not null;default:''"` // 操作对应唯一id
	Project         string    `gorm:"index:idx_time;column:project;type:varchar(32);not null;default:''"`                      // 游戏项目
	BeforeDetail    string    `gorm:"column:before_detail;type:text;default:null"`                                             // 操作前的数据
	AfterDetail     string    `gorm:"column:after_detail;type:text;default:null"`                                              // 操作后的数据
	CreateTime      time.Time `gorm:"index:idx_time;column:create_time;type:timestamp;not null;default:CURRENT_TIMESTAMP"`     // 添加时间
	Operator        string    `gorm:"column:operator;type:varchar(32);not null;default:''"`                                    // 操作人
}

// FpLinePlayerPortrait 新工单玩家画像表
type FpLinePlayerPortrait struct {
	ID             uint64 `gorm:"primaryKey;column:id;type:bigint(20) unsigned;not null" json:"id"`
	LineUserId     string `gorm:"column:line_user_id;type:varchar(128);not null;default:''" json:"line_user_id"`                 // line上的玩家用户id
	Project        string `gorm:"column:project;type:varchar(64);not null;default:''" json:"project"`                            // 游戏
	Tag            string `gorm:"column:tag;type:text;not null;default:''" json:"tag"`                                           // 标签信息
	Gender         uint8  `gorm:"column:gender;type:tinyint(3);not null;default:0" json:"gender"`                                // 性别 1男 2女
	Birthday       string `gorm:"column:birthday;type:varchar(64);not null;default:''" json:"birthday"`                          // 生日
	Career         string `gorm:"column:career;type:varchar(64);not null;default:''" json:"career"`                              // 职业
	EducationLevel uint8  `gorm:"column:education_level;type:tinyint(3);not null;default:0" json:"education_level"`              // 教育程度：0未知;1 小学;2 初中;3 高中;4 本科;5 研究生;6 博士;7 博士后;8其他
	MarriedState   uint8  `gorm:"column:married_state;type:tinyint(3);not null;default:0" json:"married_state"`                  // 婚姻状态:0未知;1已婚;2未婚;3离异;4丧偶
	FertilityState uint8  `gorm:"column:fertility_state;type:tinyint(3);not null;default:0" json:"fertility_state"`              // 生育状况:0未知;1未育;2 1孩;3 2孩;4 3孩;5 3孩以上
	Remark         string `gorm:"column:remark;type:text;not null;default:''" json:"remark"`                                     // 备注
	CreateTime     uint64 `gorm:"column:create_time;type:bigint(20) unsigned;not null;default:0" json:"create_time"`             // 创建时间
	UpdateTime     string `gorm:"column:update_time;type:varchar(64);not null;default:'1900-01-01 00:00:00'" json:"update_time"` // 更新时间
}

// FpLinePlayerTag line玩家绑定标签表
type FpLinePlayerTag struct {
	ID         uint64 `gorm:"primaryKey;column:id;type:bigint(20) unsigned;not null" json:"id"`
	Project    string `gorm:"column:project;type:varchar(128);not null;default:''" json:"project"`                           // 游戏
	LineUserId string `gorm:"column:line_user_id;type:varchar(128);not null;default:''" json:"line_user_id"`                 // discord上的玩家用户id
	TagId      int64  `gorm:"column:tag_id;type:bigint(20);not null;default:0" json:"tag_id"`                                // 标签ID
	CreateTime uint64 `gorm:"column:create_time;type:bigint(20) unsigned;not null;default:0" json:"create_time"`             // 创建时间
	UpdateTime string `gorm:"column:update_time;type:varchar(64);not null;default:'1900-01-01 00:00:00'" json:"update_time"` // 更新时间
}

// FpLineCommuRecord  line玩家和客服沟通记录表
type FpLineCommuRecord struct {
	ID           int64             `gorm:"primaryKey;column:id;type:bigint(20) unsigned;not null" json:"id"`
	Project      string            `gorm:"column:project;type:varchar(32);not null;default:''" json:"project"`                          // 所属项目
	CommuDate    string            `gorm:"column:commu_date;type:varchar(32);not null;default:''" json:"commu_date"`                    // 沟通日期
	LineUserId   string            `gorm:"column:line_user_id;type:varchar(128);not null;default:''" json:"line_user_id"`               // discord上的玩家用户id
	UID          int64             `gorm:"column:uid;type:bigint(20) unsigned;not null;default:0" json:"uid"`                           // 玩家uid
	Sid          string            `gorm:"column:sid;type:varchar(64);not null;default:''" json:"sid"`                                  // discord上玩家的服务器
	NickName     string            `gorm:"column:nick_name;type:varchar(64);not null;default:''" json:"nick_name"`                      // 玩家昵称
	PayAll       float64           `gorm:"column:pay_all;type:decimal(10,2);not null;default:0" json:"pay_all"`                         // 玩家累付金额
	Question     string            `gorm:"column:question;type:varchar(64);not null;default:''" json:"question"`                        // 沟通问题
	CatID        uint32            `gorm:"index:idx_cat;column:cat_id;type:int(11) unsigned;not null;default:0"`                        // 问题分类ID
	CatType      uint16            `gorm:"index:idx_ctype;column:cat_type;type:smallint(2) unsigned;not null;default:0"`                // 类别: 0.未知 1.Discord 2.Line
	HandleStatus uint8             `gorm:"column:handle_status;type:tinyint(3);not null;default:0" json:"handle_status"`                // 处理状态：0未知;1 处理中;2 已完成
	Remark       string            `gorm:"column:remark;type:text;not null;default:''" json:"remark"`                                   // 备注
	RelevantMsg  string            `gorm:"column:relevant_msg;type:text;not null;default:''" json:"relevant_msg"`                       // 涉及的对话信息id集合
	Operator     string            `gorm:"column:operator;type:varchar(64);not null;default:''" json:"operator"`                        // 处理人
	CreatedAt    time.Time         `gorm:"index:idx_created_at;column:created_at;type:datetime;default:null;default:CURRENT_TIMESTAMP"` // 创建时间
	UpdatedAt    time.Time         `gorm:"column:updated_at;type:datetime;default:null;default:CURRENT_TIMESTAMP"`                      // 更新时间
	Tags         []FpLinePlayerTag `gorm:"foreignKey:LineUserId;references:LineUserId"`
}

func GetFpLineChannelTableName() string {
	return "fp_line_channel"
}

func GetFpLineBotTableName() string {
	return "fp_line_bot"
}

func GetFpLineUserTableName() string {
	return "fp_line_user"
}

func GetFpLineCommuTableName() string {
	return "fp_line_commu"
}

func GetFpLineSendTableName() string {
	return "fp_line_send"
}

func GetFpLineOperateLogTableName() string {
	return "fp_line_operate_log"
}

func GetFpLinePlayerMaintainConfigTableName() string {
	return "fp_line_player_maintain_config"
}

type FpLinePlayerMaintainConfigList struct {
	FpLinePlayerMaintainConfig
	Birthday string `gorm:"column:birthday;type:varchar(64);not null;default:''" json:"birthday"` // 生日
}

func GetFpLinePlayerPortraitTableName() string {
	return "fp_line_player_portrait"
}

func GetFpLinePlayerTagTableName() string {
	return "fp_line_player_tag"
}

func GetFpLineCommuRecordTableName() string {
	return "fp_line_commu_record"
}
