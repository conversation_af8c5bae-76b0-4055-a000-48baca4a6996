package models

// FpOpsTeam 客服团队表
type FpOpsTeam struct {
	TeamID    uint32 `gorm:"primaryKey;column:team_id;type:int(11) unsigned;not null"`
	TeamName  string `gorm:"column:team_name;type:varchar(64);not null;default:''"`         // 团队名称
	TeamDesc  string `gorm:"column:team_desc;type:varchar(255);not null;default:''"`        // 团队描述
	Enable    bool   `gorm:"column:enable;type:tinyint(1) unsigned;not null;default:1"`     // 启用 0:false 1:true
	Operator  string `gorm:"column:operator;type:varchar(64);not null;default:''"`          // 操作人员
	CreatedAt uint64 `gorm:"column:created_at;type:bigint(20) unsigned;not null;default:0"` // 创建时间
	UpdatedAt uint64 `gorm:"column:updated_at;type:bigint(20) unsigned;not null;default:0"` // 更新时间
}
