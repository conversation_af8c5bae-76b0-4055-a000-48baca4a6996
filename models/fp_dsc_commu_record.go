package models

import "time"

// FpDscCommuRecord  discord玩家和客服沟通记录表
type FpDscCommuRecord struct {
	ID           int64     `gorm:"primaryKey;column:id;type:bigint(20) unsigned;not null" json:"id"`
	Project      string    `gorm:"column:project;type:varchar(32);not null;default:''" json:"project"`                          // 所属项目
	CommuDate    string    `gorm:"column:commu_date;type:varchar(32);not null;default:''" json:"commu_date"`                    // 沟通日期
	DscUserID    string    `gorm:"column:dsc_user_id;type:varchar(32);not null;default:''" json:"dsc_user_id"`                  // 玩家 discord_user_id
	UID          int64     `gorm:"column:uid;type:bigint(20) unsigned;not null;default:0" json:"uid"`                           // 玩家uid
	Sid          string    `gorm:"column:sid;type:varchar(64);not null;default:''" json:"sid"`                                  // discord上玩家的服务器
	NickName     string    `gorm:"column:nick_name;type:varchar(64);not null;default:''" json:"nick_name"`                      // 玩家昵称
	PayAll       float64   `gorm:"column:pay_all;type:decimal(10,2);not null;default:0" json:"pay_all"`                         // 玩家累付金额
	Question     string    `gorm:"column:question;type:varchar(64);not null;default:''" json:"question"`                        // 沟通问题
	QuestionType uint8     `gorm:"column:question_type;type:tinyint(3);not null;default:0" json:"question_type"`                // 问题类型：0未知;1 游戏咨询;2 游戏建议;3 游戏异常;4 服务器匹配&合服;5 抱怨/负面反馈;6 其他问题
	CatID        uint32    `gorm:"index:idx_cat;column:cat_id;type:int(11) unsigned;not null;default:0"`                        // 问题分类ID
	CatType      uint16    `gorm:"index:idx_ctype;column:cat_type;type:smallint(2) unsigned;not null;default:0"`                // 类别: 0.未知 1.Discord 2.Line
	HandleStatus uint8     `gorm:"column:handle_status;type:tinyint(3);not null;default:0" json:"handle_status"`                // 处理状态：0未知;1 处理中;2 已完成
	Remark       string    `gorm:"column:remark;type:text;not null;default:''" json:"remark"`                                   // 备注
	RelevantMsg  string    `gorm:"column:relevant_msg;type:text;not null;default:''" json:"relevant_msg"`                       // 涉及的对话信息id集合
	Operator     string    `gorm:"column:operator;type:varchar(64);not null;default:''" json:"operator"`                        // 处理人
	CreatedAt    time.Time `gorm:"index:idx_created_at;column:created_at;type:datetime;default:null;default:CURRENT_TIMESTAMP"` // 创建时间
	UpdatedAt    time.Time `gorm:"index:idx_updated_at;column:updated_at;type:datetime;default:null;default:CURRENT_TIMESTAMP"` // 更新时间
}

func GetFpDscCommuRecordTableName() string {
	return "fp_dsc_commu_record"
}
