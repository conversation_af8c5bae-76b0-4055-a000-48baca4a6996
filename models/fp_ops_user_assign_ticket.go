package models

import (
	"ops-ticket-api/proto/pb"
	"time"
)

// FpOpsUserAssignTicket 用户分单表
type FpOpsUserAssignTicket struct {
	ID         uint32    `gorm:"primaryKey;column:id;type:int(11) unsigned;not null"`
	Account    string    `gorm:"column:account;type:varchar(64);not null;default:''"`  // 用户名称
	UpperLimit uint32    `gorm:"column:upper_limit;type:int(4);not null;default:0"`    // 接单上限
	Game       string    `gorm:"column:game;type:varchar(1024);not null"`              // 游戏（废弃）
	Lang       string    `gorm:"column:lang;type:varchar(512);not null"`               // 语言
	GameCat    string    `gorm:"column:game_cat;type:text;not null"`                   // 游戏、问题分类和系统标签
	Operator   string    `gorm:"column:operator;type:varchar(64);not null;default:''"` // op
	CreatedAt  time.Time `gorm:"column:created_at;type:datetime;not null"`             // 创建时间
	UpdatedAt  time.Time `gorm:"column:updated_at;type:datetime;default:null"`         // 更新时间
}

type ProjectUserDf struct {
	Id             uint32             `json:"id"` //人员 id
	IsLogin        pb.UserLoginStatus `gorm:"column:is_login;type:tinyint;default:2;comment:'是否在线：2:离线, 1:在线'"`
	UpperLimit     int32              `gorm:"column:upper_limit;type:int(4);not null;default:0"`                        // 接单上限
	User           string             `gorm:"uniqueIndex:idx_gp_user;column:user;type:varchar(64);not null;default:''"` // 用户名称
	GameCat        []*GameCategory    `json:"game_cat"`
	Lang           []string           `json:"lang"`
	LastAllocTkAt  uint64             `gorm:"column:last_alloc_tk_at;type:bigint(20);not null;default:0"` // 最后分单时间
	CurrDoingTkNum int32              `json:"curr_doing_tk_num"`                                          // 当前用户处理中工单量
}

type GameCategory struct {
	Game       string   `json:"game"`
	Categories []string `json:"categories"`
	SystemTags []uint32 `json:"system_tag"`
}

func GetFpOpsUserAssignTicketTableName() string {
	return "fp_ops_user_assign_ticket"
}
