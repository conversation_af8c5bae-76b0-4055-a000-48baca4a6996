package models

// FpOpsLanguage 语言表
type FpOpsLanguage struct {
	ID     uint32 `gorm:"primaryKey;column:id;type:int(11) unsigned;not null" json:"-"`
	Code   string `gorm:"unique;column:code;type:varchar(8);not null" json:"code"` // 标识
	Name   string `gorm:"column:name;type:varchar(16);not null" json:"name"`       // 名称
	Enable bool   `gorm:"column:enable;type:tinyint(1);not null" json:"-"`         // 启用禁用
}
