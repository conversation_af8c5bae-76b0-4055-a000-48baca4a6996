package models

import (
	"time"
)

const TableNameFpDscSurveyDetail = "fp_dsc_survey_detail"

// FpDscSurveyDetail 玩家满意度调查表
type FpDscSurveyDetail struct {
	ID               uint64    `gorm:"primaryKey;column:id;type:bigint unsigned;not null"`
	Project          string    `gorm:"uniqueIndex:unique_survey;column:project;type:varchar(32);not null;default:''"`                // 所属项目
	EvaluationDate   string    `gorm:"column:evaluation_date;type:varchar(32);not null;default:''"`                                  // 评价日期
	DscUserID        string    `gorm:"column:dsc_user_id;type:varchar(32);not null;default:''"`                                      // 玩家 discord_user_id
	NickName         string    `gorm:"column:nick_name;type:varchar(64);not null;default:''"`                                        // 玩家昵称
	UID              int64     `gorm:"column:uid;type:bigint;not null;default:0"`                                                    // 玩家uid
	AccountID        string    `gorm:"column:account_id;type:varchar(64);not null;default:''"`                                       // 玩家fpid
	Rating           int8      `gorm:"column:rating;type:tinyint;not null;default:0"`                                                // 评星（1-5）
	EvaluationTarget int8      `gorm:"column:evaluation_target;type:tinyint;not null;default:0"`                                     // 评价对象（1:产品2:服务）
	RatingReason     string    `gorm:"column:rating_reason;type:text;not null"`                                                      // 评星理由
	Operator         string    `gorm:"column:operator;type:varchar(32);not null;default:''"`                                         // 处理人
	Maintainer       string    `gorm:"column:maintainer;type:varchar(32);not null;default:''"`                                       // 维护人
	Operators        string    `gorm:"column:operators;type:varchar(256);not null;default:[]"`                                       // 经手人
	EncryptedToken   string    `gorm:"column:encrypted_token;type:varchar(256);not null;default:''"`                                 // 链接加密串
	SurveyID         int64     `gorm:"column:survey_id;type:bigint;not null;default:0"`                                              // 调查问卷id
	Initiator        string    `gorm:"column:initiator;type:varchar(32);not null;default:''"`                                        // 发起人
	CreatedAt        time.Time `gorm:"uniqueIndex:unique_survey;column:created_at;type:datetime;not null;default:CURRENT_TIMESTAMP"` // 创建时间
}

// TableName FpDscSurveyDetail's table name
func (*FpDscSurveyDetail) TableName() string {
	return TableNameFpDscSurveyDetail
}
