package models

import (
	"time"
)

const TableNameFpDscSurveyConfig = "fp_dsc_survey_config"

// FpDscSurveyConfig 问卷调查配置表
type FpDscSurveyConfig struct {
	ID               uint64    `gorm:"primaryKey;column:id;type:bigint unsigned;not null"`
	GameProject      string    `gorm:"column:game_project;type:varchar(32);not null;default:''"`            // 游戏
	PushCycle        int64     `gorm:"column:push_cycle;type:bigint;not null;default:0"`                    // 推送周期: 每周/每双周/每月
	PushWeek         uint64    `gorm:"column:push_week;type:bigint unsigned;not null;default:0"`            // 推送时间-星期N
	PushTime         uint64    `gorm:"column:push_time;type:bigint unsigned;not null;default:0"`            // 推送时间-小时 00～23;与push_week一起使用
	EffectiveTime    uint64    `gorm:"column:effective_time;type:bigint unsigned;not null;default:0"`       // 生效日期
	ExpireTime       uint64    `gorm:"column:expire_time;type:bigint unsigned;not null;default:0"`          // 问卷有效期：3天/5天/7天
	Enable           uint8     `gorm:"column:enable;type:tinyint unsigned;not null;default:0"`              // 启用 0:false 1:true
	Titles           string    `gorm:"column:titles;type:mediumtext;not null"`                              // 问卷标题（多语言）
	Contents         string    `gorm:"column:contents;type:mediumtext;not null"`                            // 推送文案（多语言）
	ProductQuestions string    `gorm:"column:product_questions;type:mediumtext;not null"`                   // 推送文案（多语言）
	ServiceQuestions string    `gorm:"column:service_questions;type:mediumtext;not null"`                   // 服务题（多语言）
	Reasons          string    `gorm:"column:reasons;type:mediumtext;not null"`                             // 填写理由（多语言）
	Creator          string    `gorm:"column:creator;type:varchar(64);not null;default:''"`                 // 创建人
	Operator         string    `gorm:"column:operator;type:varchar(64);not null;default:''"`                // 操作人
	CreatedAt        time.Time `gorm:"column:created_at;type:timestamp;not null;default:CURRENT_TIMESTAMP"` // 创建时间
	UpdatedAt        time.Time `gorm:"column:updated_at;type:timestamp;not null;default:CURRENT_TIMESTAMP"` // 更新时间
}

func (m *FpDscSurveyConfig) CheckWeekDaySame(weekDay time.Weekday, pushWeek uint64) bool {
	if weekDay == time.Sunday {
		return pushWeek == 7
	} else {
		return uint64(weekDay) == pushWeek
	}
}

func (m *FpDscSurveyConfig) CheckCurrencyHourSame(nowHour int, pushTime uint64) bool {
	return uint64(nowHour) == pushTime
}

// TableName FpDscSurveyConfig's table name
func (*FpDscSurveyConfig) TableName() string {
	return TableNameFpDscSurveyConfig
}
