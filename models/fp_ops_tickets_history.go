package models

import (
	"ops-ticket-api/proto/pb"
	"ops-ticket-api/utils"
)

// FpOpsTicketsHistory 工单日志表
type FpOpsTicketsHistory struct {
	ID        uint64 `gorm:"primaryKey;column:id;type:bigint(20) unsigned;not null" json:"id"`
	TicketID  uint64 `gorm:"index:idx_ticket_id;column:ticket_id;type:bigint(20) unsigned;not null;default:0" json:"ticket_id"` // 工单ID
	Acceptor  string `gorm:"index:acceptor;column:acceptor;type:varchar(64);not null;default:''" json:"acceptor"`               // 工单处理人
	Operate   uint8  `gorm:"column:operate;type:tinyint(2) unsigned;not null;default:0" json:"operate"`                         // 具体操作 对应 pb.TkEvent
	OpDetail  uint32 `gorm:"column:op_detail;type:tinyint(2) unsigned;not null" json:"op_detail"`                               // 操作具体流程
	OpRole    uint8  `gorm:"column:op_role;type:tinyint(1) unsigned;not null;default:1" json:"op_role"`                         // 操作者角色 1:玩家 2:客服 3:系统
	OpObject  string `gorm:"column:op_object;type:varchar(2048);not null;default:''" json:"op_object"`                          // 被操作对象
	Remark    string `gorm:"column:remark;type:text" json:"remark"`                                                             // 操作备注
	Operator  string `gorm:"column:operator;type:varchar(64);not null;default:''" json:"operator"`                              // 操作员
	CreatedAt uint64 `gorm:"index:acceptor;column:created_at;type:bigint(20) unsigned;not null;default:0" json:"created_at"`    // 创建时间
	UpdatedAt uint64 `gorm:"column:updated_at;type:bigint(20) unsigned;not null;default:0" json:"updated_at"`                   // 更新时间
}

func NewHis() *FpOpsTicketsHistory {
	return &FpOpsTicketsHistory{}
}

// HistCreate 工单创建操作
func (h *FpOpsTicketsHistory) HistCreate(ticketId uint64, operator string) *FpOpsTicketsHistory {
	return &FpOpsTicketsHistory{
		TicketID:  ticketId,
		Operate:   uint8(pb.TkEvent_TkEventCreate),
		OpRole:    uint8(pb.UserRole_PlayerRole),
		Operator:  operator,
		CreatedAt: utils.NowTimestamp(),
		UpdatedAt: utils.NowTimestamp(),
	}
}

//
//// HistReassign 指派操作
//func (h *FpOpsTicketsHistory) HistReassign(ticketId uint64, curStage uint8, acceptor, opObject, operator string) *FpOpsTicketsHistory {
//	return &FpOpsTicketsHistory{
//		TicketID: ticketId,
//		Acceptor: acceptor,
//		Operate:  uint8(pb.TicketEvent_Assign),
//		OpDetail: curStage,
//		OpRole:   uint8(pb.UserRole_ServiceRole),
//		OpObject: opObject,
//		Operator: operator,
//	}
//}
//
//// HistPriority 调整优先级操作
//func (h *FpOpsTicketsHistory) HistPriority(ticketId uint64, acceptor, operator string, priority uint8) *FpOpsTicketsHistory {
//	return &FpOpsTicketsHistory{
//		TicketID:  ticketId,
//		Acceptor:  acceptor,
//		Operate:   uint8(pb.TicketEvent_Emergency),
//		OpRole:    uint8(pb.UserRole_ServiceRole),
//		OpObject:  cast.ToString(priority),
//		Operator:  operator,
//		UpdatedAt: uint64(time.Now().Unix()),
//	}
//}
//
//// HistRevoke 工单撤回操作
//func (h *FpOpsTicketsHistory) HistRevoke(ticketId uint64, acceptor, operator string) *FpOpsTicketsHistory {
//	return &FpOpsTicketsHistory{
//		TicketID:  ticketId,
//		Acceptor:  acceptor,
//		Operate:   uint8(pb.TicketEvent_Revoke),
//		OpRole:    uint8(pb.UserRole_ServiceRole),
//		OpObject:  cast.ToString(ticketId),
//		Operator:  operator,
//		UpdatedAt: uint64(time.Now().Unix()),
//	}
//}
//
//// HistReceipt 接单操作
//func (h *FpOpsTicketsHistory) HistReceipt(ticketId uint64, acceptor, operator string, wkg pb.Workbench) *FpOpsTicketsHistory {
//	return &FpOpsTicketsHistory{
//		TicketID:  ticketId,
//		Acceptor:  acceptor,
//		Operate:   uint8(pb.TicketEvent_Receipt),
//		OpRole:    uint8(pb.UserRole_ServiceRole),
//		Workgroup: uint8(wkg),
//		Operator:  operator,
//	}
//}
//
//// HistRemark 工单备注
//func (h *FpOpsTicketsHistory) HistRemark(ticketId uint64, acceptor, operator, remark string, wrk pb.Workbench) *FpOpsTicketsHistory {
//	opRole := pb.UserRole_ServiceRole
//	if wrk == pb.Workbench_VipWorkStation {
//		opRole = pb.UserRole_VIPServiceRole
//	}
//	return &FpOpsTicketsHistory{
//		TicketID:  ticketId,
//		Acceptor:  acceptor,
//		Operate:   uint8(pb.TicketEvent_Remark),
//		OpRole:    uint8(opRole),
//		OpObject:  "",
//		Workgroup: uint8(wrk),
//		Remark:    remark,
//		Operator:  operator,
//		UpdatedAt: uint64(time.Now().Unix()),
//	}
//}
//
//// HistFork 工单流转
//func (h *FpOpsTicketsHistory) HistFork(ticketId uint64, acceptor, opObject, remark, operator string, operate, opDetail uint8, wkg pb.Workbench, opRole pb.UserRole) *FpOpsTicketsHistory {
//	return &FpOpsTicketsHistory{
//		TicketID:  ticketId,
//		Acceptor:  acceptor,
//		Operate:   operate,
//		OpDetail:  opDetail,
//		OpRole:    uint8(opRole),
//		OpObject:  opObject,
//		Workgroup: uint8(wkg),
//		Remark:    remark,
//		Operator:  operator,
//		CreatedAt: uint64(time.Now().Unix()),
//	}
//}
//
//// HistPlayerProof 玩家补填
//func (h *FpOpsTicketsHistory) HistPlayerProof(ticketId uint64, account, content string) *FpOpsTicketsHistory {
//	return &FpOpsTicketsHistory{
//		TicketID: ticketId,
//		Acceptor: "",
//		Operate:  uint8(pb.TicketEvent_Transfered),
//		OpDetail: uint8(pb.TicketStage_FirstTierProcessing),
//		OpRole:   uint8(pb.UserRole_PlayerRole),
//		Operator: cast.ToString(account),
//		Remark:   content,
//	}
//}
//
//// HistServiceProof 客服补填
//func (h *FpOpsTicketsHistory) HistServiceProof(ticketId uint64, acceptor, operator, content string) *FpOpsTicketsHistory {
//	return &FpOpsTicketsHistory{
//		TicketID: ticketId,
//		Acceptor: acceptor,
//		Operate:  uint8(pb.TicketEvent_Transfered),
//		OpDetail: uint8(pb.TicketStage_SecondTierProcessing),
//		OpRole:   uint8(pb.UserRole_ServiceRole),
//		Operator: operator,
//		Remark:   content,
//	}
//}
//
//// HistTag 工单标签
//func (h *FpOpsTicketsHistory) HistTag(ticketId uint64, acceptor, operator string, tags []uint32, tagEvent pb.TagEvent, wkg pb.Workbench) *FpOpsTicketsHistory {
//	opRole := pb.UserRole_ServiceRole
//	if wkg == pb.Workbench_VipWorkStation {
//		opRole = pb.UserRole_VIPServiceRole
//	}
//	jsonStr, _ := jsoniter.MarshalToString(tags)
//	return &FpOpsTicketsHistory{
//		TicketID:  ticketId,
//		Acceptor:  acceptor,
//		Operate:   uint8(pb.TicketEvent_Tag),
//		OpDetail:  uint8(tagEvent),
//		OpRole:    uint8(opRole),
//		OpObject:  jsonStr,
//		Workgroup: uint8(wkg),
//		Operator:  operator,
//		UpdatedAt: uint64(time.Now().UTC().Unix()),
//	}
//}
//
//func (h *FpOpsTicketsHistory) HistClosed(ticketId uint64, acceptor, operator string) *FpOpsTicketsHistory {
//	return &FpOpsTicketsHistory{
//		TicketID:  ticketId,
//		Acceptor:  acceptor,
//		Operate:   uint8(pb.TicketEvent_Closed),
//		OpRole:    uint8(pb.UserRole_SystemRole),
//		Operator:  operator,
//		UpdatedAt: uint64(time.Now().Unix()),
//	}
//}
//
//func (h *FpOpsTicketsHistory) HistCatRectify(current, ticketId, originCatId, newCatId uint64, acceptor, operator string) *FpOpsTicketsHistory {
//	return &FpOpsTicketsHistory{
//		TicketID:  ticketId,
//		Acceptor:  acceptor,
//		Operate:   uint8(pb.TicketEvent_RectifyCat),
//		OpRole:    uint8(pb.UserRole_ServiceRole),
//		OpObject:  fmt.Sprintf("%d_%d", originCatId, newCatId),
//		Operator:  operator,
//		UpdatedAt: current,
//	}
//}
