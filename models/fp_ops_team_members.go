package models

// FpOpsTeamMembers 团队成员表
type FpOpsTeamMembers struct {
	ID         int64  `gorm:"primaryKey;column:id;type:bigint(11) unsigned;not null"`
	TeamID     int64  `gorm:"column:team_id;type:bigint(11) unsigned;not null"`        // 团队id
	TeamMember string `gorm:"column:team_member;type:varchar(64);not null;default:''"` // 团队成员
	Updater    string `gorm:"column:updater;type:varchar(64);not null;default:''"`     // 更新人
	UpdateTime string `gorm:"column:update_time;type:varchar(64);not null;default:''"` // 更新时间
}

func GetFpOpsTeamMembersTableName() string {
	return "fp_ops_team_members"
}
