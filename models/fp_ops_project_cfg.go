package models

import (
	"time"
)

// FpOpsProjectCfg 工单项目配置表
type FpOpsProjectCfg struct {
	ID          uint64    `gorm:"autoIncrement:true;primaryKey;column:id;type:bigint(20) unsigned;not null"`
	Project     string    `gorm:"index:idx_project;column:project;type:varchar(64);not null;default:'';comment:'所属项目'"` // 所属项目
	GatewayName string    `gorm:"column:gateway_name;type:varchar(64);not null;default:'';comment:'网关游戏名称'"`            // 网关游戏名称
	TicketName  string    `gorm:"column:ticket_name;type:varchar(64);not null;default:'';comment:'工单游戏名称'"`             // 工单游戏名称
	Enable      bool      `gorm:"column:enable;type:tinyint(1) unsigned;not null;default:1;comment:'0:禁用 1:启用'"`        // 0:禁用 1:启用
	Operator    string    `gorm:"column:operator;type:varchar(64);not null;default:'';comment:'操作人员'"`                  // 操作人员
	CreatedAt   time.Time `gorm:"column:created_at;type:datetime;default:null;comment:'更新时间'"`                          // 更新时间
	UpdatedAt   time.Time `gorm:"column:updated_at;type:datetime;default:null;comment:'创建时间'"`                          // 创建时间
}
