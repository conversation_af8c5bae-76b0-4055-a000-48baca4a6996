package models

// FpDscPlayerMaintainConfig 玩家维护关系配置表
type FpDscPlayerMaintainConfig struct {
	ID                uint64 `gorm:"primaryKey;column:id;type:bigint(20) unsigned;not null" json:"id"`
	DscUserId         string `gorm:"column:dsc_user_id;type:varchar(255);not null;default:''" json:"dsc_user_id"`                   // discord上的玩家用户id
	AccountId         string `gorm:"column:account_id;type:varchar(64);not null;default:''" json:"account_id"`                      // discord上玩家的account_id(fpid)
	UID               uint64 `gorm:"column:uid;type:bigint(20) unsigned;not null;default:0" json:"uid"`                             // 玩家uid
	Sid               string `gorm:"column:sid;type:varchar(64);not null;default:''" json:"sid"`                                    // discord上玩家的服务器
	GameProject       string `gorm:"column:game_project;type:varchar(64);not null;default:''" json:"game_project"`                  // 游戏
	NickName          string `gorm:"column:nick_name;type:varchar(64);not null;default:''" json:"nick_name"`                        // 昵称
	VipLevel          uint8  `gorm:"column:vip_level;type:tinyint(3);not null;default:0" json:"vip_level"`                          // vip等级
	PayAll            int64  `gorm:"column:pay_all;type:bigint(20);not null;default:0" json:"pay_all"`                              // 总付费 *10000
	PayLastThirtyDays int64  `gorm:"column:pay_last_thirty_days;type:bigint(20);not null;default:0" json:"pay_last_thirty_days"`    // 最近30天付费 *10000
	LastLogin         string `gorm:"column:last_login;type:varchar(20);not null;default:'0000-00-00 00:00:00'" json:"last_login"`   // 最后登录时间
	VipState          uint8  `gorm:"column:vip_state;type:tinyint(3);not null;default:0" json:"vip_state"`                          // vip状态 1非vip,2vip
	Maintainer        string `gorm:"column:maintainer;type:varchar(64);not null;default:''" json:"maintainer"`                      // 维护专员
	Operator          string `gorm:"column:operator;type:varchar(64);not null;default:''" json:"operator"`                          // 操作人
	PlayerNick        string `gorm:"column:player_nick;type:varchar(64);not null;default:''" json:"player_nick"`                    // 玩家在游戏的昵称
	Lang              string `gorm:"column:lang;type:varchar(64);not null;default:''" json:"lang"`                                  // 语种
	CreateTime        uint64 `gorm:"column:create_time;type:bigint(20) unsigned;not null;default:0" json:"create_time"`             // 创建时间
	UpdatedTime       string `gorm:"column:update_time;type:varchar(64);not null;default:'1900-01-01 00:00:00'" json:"update_time"` // 更新时间
}

func GetFpDscPlayerMaintainConfigTableName() string {
	return "fp_dsc_player_maintain_config"
}

type FpDscPlayerMaintainConfigList struct {
	FpDscPlayerMaintainConfig
	Birthday string `gorm:"column:birthday;type:varchar(64);not null;default:''" json:"birthday"` // 生日
}
