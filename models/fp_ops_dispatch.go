package models

import "time"

// FpOpsDispatchConfig 分单配置表
type FpOpsDispatchConfig struct {
	ID                   uint32    `gorm:"primaryKey;column:id;type:int(10) unsigned;not null"`                                              // 配置ID
	MaxOrdersPerOperator int       `gorm:"index:idx_max_orders_per_operator;column:max_orders_per_operator;type:int(11);not null;default:0"` // 客服可接单上限
	DispatchType         int8      `gorm:"index:idx_dispatch_type;column:dispatch_type;type:tinyint(4);not null;default:1"`                  // 分单类型（1表示系统分单，2表示员工接单）
	CreatedAt            time.Time `gorm:"column:created_at;type:timestamp;not null;default:CURRENT_TIMESTAMP"`                              // 创建时间
	UpdatedAt            time.Time `gorm:"column:updated_at;type:timestamp;not null;default:CURRENT_TIMESTAMP"`                              // 更新时间
}

func (m *FpOpsDispatchConfig)TableName() string {
	return "fp_ops_dispatch_config"
}