package models

// FpOpsTicketsAppraise 工单评价表
type FpOpsTicketsAppraise struct {
	ID                    uint32 `gorm:"primaryKey;column:id;type:int(11) unsigned;not null"`
	TicketID              uint64 `gorm:"unique;column:ticket_id;type:bigint(20) unsigned;not null;default:0"`        // 工单ID
	Csi                   uint8  `gorm:"column:csi;type:tinyint(1) unsigned;not null;default:0"`                     // 用户满意度
	ServiceRating         uint8  `gorm:"column:service_rating;type:tinyint(1) unsigned;not null;default:0"`          // 服务态度评分
	ServiceTimeRating     uint8  `gorm:"column:service_time_rating;type:tinyint(1) unsigned;not null;default:0"`     // 处理速度评分
	ServiceSolutionRating uint8  `gorm:"column:service_solution_rating;type:tinyint(1) unsigned;not null;default:0"` // 处理方案评分
	RecommendationLevel   uint8  `gorm:"column:recommendation_level;type:tinyint(1) unsigned;not null;default:0"`    // 推荐给别人的意愿程度
	Remark                string `gorm:"column:remark;type:varchar(255);not null;default:''"`                        // 评语
	CreatedAt             uint64 `gorm:"column:created_at;type:bigint(20) unsigned;not null;default:0"`              // 创建时间
	UpdatedAt             int64  `gorm:"column:updated_at;type:bigint(20)"`                                          // 更新时间
}
