package models

import "time"

// FpDscMessageTaskDetail Discord批量私信任务明细表
type FpDscMessageTaskDetail struct {
	ID         uint64    `gorm:"primaryKey;column:id;type:bigint(20) unsigned;not null;autoIncrement;comment:'自增ID'"`                                              // 自增ID
	TaskID     uint64    `gorm:"column:task_id;type:bigint(20) unsigned;not null;comment:'任务ID'"`                                                                  // 任务ID
	UID        int64     `gorm:"column:uid;type:bigint(20) unsigned;not null;comment:'玩家UID'"`                                                                     // 玩家UID
	MsgID      string    `gorm:"index:msg_id_index;column:msg_id;type:varchar(32);comment:'消息ID'"`                                                                 // 消息ID
	AccountID  string    `gorm:"column:account_id;type:varchar(64);not null;default:'';comment:'Discord上玩家的Account ID (FPID)'"`                                    // Discord上玩家的Account ID (FPID)
	Project    string    `gorm:"column:project;type:varchar(32);not null;default:'';comment:'所属项目（游戏）'"`                                                           // 所属项目（游戏）
	BotID      string    `gorm:"index:idx_bot;column:bot_id;type:varchar(32);not null;default:'';comment:'所属应用/机器人的User ID'"`                                      // 所属应用/机器人的User ID
	DscUserID  string    `gorm:"column:dsc_user_id;type:varchar(32);not null;default:'';comment:'用户的Discord User ID'"`                                             // 用户的Discord User ID
	ChannelID  string    `gorm:"index:idx_channel;column:channel_id;type:varchar(32);default:'';comment:'用户私聊Channel ID'"`                                         // 用户私聊Channel ID
	Status     int       `gorm:"column:status;type:int;not null;comment:'任务状态 10: 未开始; 20: 处理失败; 30: 处理成功'"`                                                       // 任务状态
	FailLog    string    `gorm:"column:fail_log;type:text;comment:'失败原因'"`                                                                                         // 失败原因
	CreatedAt  time.Time `gorm:"index:idx_create_at;column:created_at;type:datetime;not null;default:CURRENT_TIMESTAMP;comment:'创建时间'"`                            // 创建时间
	UpdateAt   time.Time `gorm:"index:idx_update_at;column:update_at;type:datetime;not null;default:CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP;comment:'更新时间'"` // 更新时间
	FinishedAt time.Time `gorm:"column:finished_at;type:datetime;comment:'完成时间'"`                                                                                  // 完成时间
}
