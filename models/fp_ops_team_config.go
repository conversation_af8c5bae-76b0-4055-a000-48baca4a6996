package models

// FpOpsTeamConfig 团队配置表
type FpOpsTeamConfig struct {
	ID         int64  `gorm:"primaryKey;column:id;type:bigint(11) unsigned;not null"`
	TeamName   string `gorm:"column:team_name;type:varchar(64);not null;default:''"`   // 团队名称
	TeamMember string `gorm:"column:team_member;type:text;not null;default:''"`        // 团队成员
	Updater    string `gorm:"column:updater;type:varchar(64);not null;default:''"`     // 更新人
	UpdateTime string `gorm:"column:update_time;type:varchar(64);not null;default:''"` // 更新时间
}

func GetFpOpsTeamConfigTableName() string {
	return "fp_ops_team_config"
}
