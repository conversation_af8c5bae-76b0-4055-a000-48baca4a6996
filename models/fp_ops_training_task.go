package models

// FpOpsTrainingTask 工单知识库-模型训练任务表
type FpOpsTrainingTask struct {
	LogID      int64  `gorm:"primaryKey;column:log_id;type:bigint(20) unsigned;not null"`                          // 单次训练ID
	Project    string `gorm:"index:idx_prj;index:idx_prj_cat;column:project;type:varchar(64);not null;default:''"` // 游戏
	Lang       string `gorm:"column:lang;type:varchar(16);not null;default:''"`                                    // 语言
	FailReason string `gorm:"column:fail_reason;type:varchar(255);not null;default:''"`                            // 失败原因
	Status     int32  `gorm:"column:status;type:int(10);not null;default:0"`                                       // 0:init;  10:调用训练成功;  20：训练中;  30：训练成功;  -10:训练失败
	Remark     string `gorm:"column:remark;type:varchar(255);not null;default:''"`                                 // 说明
	Creator    string `gorm:"column:creator;type:varchar(64);not null;default:''"`                                 // 创建人
	CreatedAt  int64  `gorm:"column:created_at;type:bigint(20) unsigned;not null;default:0"`                       // 创建时间
	UpdatedAt  int64  `gorm:"column:updated_at;type:bigint(20) unsigned;not null;default:0"`                       // 更新时间
}

func (m *FpOpsTrainingTask) TableName() string {
	return "fp_ops_training_task"
}

// FpOpsTrainingTaskDetail 工单知识库-模型训练任务明细表
type FpOpsTrainingTaskDetail struct {
	ID             uint64 `gorm:"primaryKey;column:id;type:bigint unsigned;not null"`                                        // id
	LogID          int64  `gorm:"column:log_id;type:bigint;not null;default:0"`                                              // 对应日志ID
	Project        string `gorm:"index:idx_prj;index:idx_prj_cat;column:project;type:varchar(64);not null;default:''"`       // 游戏
	Lang           string `gorm:"column:lang;type:varchar(16);not null;default:''"`                                          // 语言
	CatID          uint32 `gorm:"index:idx_prj_cat;index:idx_cnv;column:cat_id;type:int(11) unsigned;not null;default:0"`    // 分类ID
	QuestionID     int64  `gorm:"index:idx_project;index:idx_question_id;column:question_id;type:bigint;not null;default:0"` // 知识库ID
	LastUpdateTime uint64 `gorm:"column:last_update_time;type:bigint(20) unsigned;not null;default:0"`                       // 知识库最后更新时间
	Status         int32  `gorm:"column:status;type:int(10);not null;default:0"`                                             // 0:init;   20：成功
	Remark         string `gorm:"column:remark;type:varchar(255);not null;default:''"`                                       // 说明
	Creator        string `gorm:"column:creator;type:varchar(64);not null;default:''"`                                       // 创建人
	CreatedAt      int64  `gorm:"column:created_at;type:bigint(20) unsigned;not null;default:0"`                             // 创建时间
	UpdatedAt      int64  `gorm:"column:updated_at;type:bigint(20) unsigned;not null;default:0"`                             // 更新时间
}

func (m *FpOpsTrainingTaskDetail) TableName() string {
	return "fp_ops_training_task_detail"
}

type QstRelation struct {
	QuestionId      uint64 `gorm:"column:question_id"`
	Project         string `gorm:"column:project"`
	Lang            string `gorm:"column:lang"`
	CatID           uint32 `gorm:"column:cat_id"` // 分类ID
	QuestionContent string `gorm:"column:question_content"`
	UpdateTime      uint64 `gorm:"column:update_time"` // db修改时间
	AnswerRichText  string `gorm:"column:answer_rich_text"`
}
