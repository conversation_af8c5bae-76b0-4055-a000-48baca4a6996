package models

// FpDscPlayerPortrait 新工单玩家画像表
type FpDscPlayerPortrait struct {
	ID             uint64 `gorm:"primaryKey;column:id;type:bigint(20) unsigned;not null" json:"id"`
	DscUserId      string `gorm:"column:dsc_user_id;type:varchar(255);not null;default:''" json:"dsc_user_id"`                   // discord上的玩家用户id
	GameProject    string `gorm:"column:game_project;type:varchar(64);not null;default:''" json:"game_project"`                  // 游戏
	Tag            string `gorm:"column:tag;type:text;not null;default:''" json:"tag"`                                           // 标签信息(废弃)
	Label          string `gorm:"column:label;type:text;not null;default:''" json:"label"`                                       // 标签信息(废弃)
	NewLabel       string `gorm:"column:new_label;type:text;not null;default:''" json:"new_label"`                               // 标签信息
	Gender         uint8  `gorm:"column:gender;type:tinyint(3);not null;default:0" json:"gender"`                                // 性别 1男 2女
	Birthday       string `gorm:"column:birthday;type:varchar(64);not null;default:''" json:"birthday"`                          // 生日
	Career         string `gorm:"column:career;type:varchar(64);not null;default:''" json:"career"`                              // 职业
	EducationLevel uint8  `gorm:"column:education_level;type:tinyint(3);not null;default:0" json:"education_level"`              // 教育程度：0未知;1 小学;2 初中;3 高中;4 本科;5 研究生;6 博士;7 博士后;8其他
	MarriedState   uint8  `gorm:"column:married_state;type:tinyint(3);not null;default:0" json:"married_state"`                  // 婚姻状态:0未知;1已婚;2未婚;3离异;4丧偶
	FertilityState uint8  `gorm:"column:fertility_state;type:tinyint(3);not null;default:0" json:"fertility_state"`              // 生育状况:0未知;1未育;2 1孩;3 2孩;4 3孩;5 3孩以上
	Remark         string `gorm:"column:remark;type:text;not null;default:''" json:"remark"`                                     // 备注
	CreateTime     uint64 `gorm:"column:create_time;type:bigint(20) unsigned;not null;default:0" json:"create_time"`             // 创建时间
	UpdatedTime    string `gorm:"column:update_time;type:varchar(64);not null;default:'1900-01-01 00:00:00'" json:"update_time"` // 更新时间
}

func GetFpDscPlayerPortraitTableName() string {
	return "fp_dsc_player_portrait"
}
