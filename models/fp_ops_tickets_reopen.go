package models

// FpOpsTicketsReopen 工单重开表
type FpOpsTicketsReopen struct {
	ID              uint64 `gorm:"column:id" json:"id"`
	TicketID        uint64 `gorm:"column:ticket_id" json:"ticket_id"`                                               // 工单ID
	ReplyId         uint64 `gorm:"column:reply_id" json:"reply_id"`                                                 // 回复id - 执行重开 - commu
	ResponseReplyId uint64 `gorm:"column:response_reply_id" json:"response_reply_id"`                               // 重开单 - 客服回复&关单 的id - commu
	Content         string `gorm:"column:content" json:"content"`                                                   // 重开内容
	Files           string `gorm:"column:files" json:"files"`                                                       // 附件
	Origin          uint8  `gorm:"column:origin" json:"origin"`                                                     // 来源
	Num             uint32 `gorm:"column:num" json:"num"`                                                           // 重开次数
	CreatedAt       uint64 `gorm:"column:created_at;type:bigint(20) unsigned;not null;default:0" json:"created_at"` // 创建时间
	UpdatedAt       uint64 `gorm:"column:updated_at;type:bigint(20) unsigned;not null;default:0" json:"updated_at"` // 更新时间
}
