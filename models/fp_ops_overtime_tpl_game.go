package models

// FpOpsOvertimeTplGame 超时提醒模板游戏关联表
type FpOpsOvertimeTplGame struct {
	ID          uint64 `gorm:"primaryKey;column:id;type:bigint(20) unsigned;not null" json:"id"`
	TplID       uint64 `gorm:"column:tpl_id;type:bigint(20);not null;default:0" json:"tpl_id"` // 模板id
	GameProject string `gorm:"column:game_project;type:varchar(64);not null;default:''" json:"game_project"`
	Enable      uint8  `gorm:"column:enable;type:tinyint(3);not null;default:0" json:"enable"`                    // 0为禁用，1为启用状态
	CreateTime  uint64 `gorm:"column:create_time;type:bigint(20) unsigned;not null;default:0" json:"create_time"` // 创建时间
	UpdatedTime uint64 `gorm:"column:update_time;type:bigint(20) unsigned;not null;default:0" json:"update_time"` // 更新时间
}

func GetFpOpsOvertimeTplGameTableName() string {
	return "fp_ops_overtime_tpl_game"
}
