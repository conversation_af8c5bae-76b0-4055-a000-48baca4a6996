package models

// FpOpsTicketsModule 工单回复模板表
type FpOpsTicketsModule struct {
	ID          uint64 `gorm:"primaryKey;column:id;type:bigint(20) unsigned;not null" json:"id"`
	ModuleName  string `gorm:"column:module_name;type:varchar(128);not null;default:''" json:"module_name"`                   // 模板名
	Content     string `gorm:"column:content;type:text;not null;default:''" json:"content"`                                   // 模板内容
	CatID       uint32 `gorm:"index:idx_cat;column:cat_id;type:int(11) unsigned;not null;default:0"`                          // 问题分类ID
	Operator    string `gorm:"column:operator;type:varchar(64);not null;default:''" json:"operator"`                          // 操作人
	Enable      uint8  `gorm:"column:enable;type:tinyint(3);not null;default:0" json:"enable"`                                // 1启用 2禁用
	CreateTime  uint64 `gorm:"column:create_time;type:bigint(20) unsigned;not null;default:0" json:"create_time"`             // 创建时间
	UpdatedTime string `gorm:"column:update_time;type:varchar(64);not null;default:'1900-01-01 00:00:00'" json:"update_time"` // 更新时间
}

func GetFpOpsTicketsModuleTableName() string {
	return "fp_ops_tickets_module"
}

type FpOpsTicketsModuleList struct {
	FpOpsTicketsModule
	GameProject string `gorm:"column:game_project;type:text;not null;default:''"` // 关联游戏
}
