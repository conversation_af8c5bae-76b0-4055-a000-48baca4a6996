package models

import (
	"ops-ticket-api/proto/pb"
	"time"
)

// FpOpsGroup 技能组表
type FpOpsGroup struct {
	ID         uint32            `gorm:"primaryKey;column:id;type:int(11) unsigned;not null"`
	GroupDesc  string            `gorm:"column:group_desc;type:varchar(64);not null;default:''"`    // 团队描述
	UpperLimit int32             `gorm:"column:upper_limit;type:int(4);not null;default:0"`         // 接单上限
	Enable     bool              `gorm:"column:enable;type:tinyint(1) unsigned;not null;default:1"` // 启用 0:false 1:true
	Operator   string            `gorm:"column:operator;type:varchar(64);not null;default:''"`      // op
	CreatedAt  time.Time         `gorm:"column:created_at;type:datetime;not null"`                  // 创建时间
	UpdatedAt  time.Time         `gorm:"column:updated_at;type:datetime;default:null"`              // 更新时间
	User       []*FpOpsGroupUser `gorm:"foreignKey:GroupID;references:ID" json:"users,omitempty"`
}

func (m *FpOpsGroup) TableName() string {
	return "fp_ops_group"
}

// FpOpsGroupCat 技能组项目问题权限表
type FpOpsGroupCat struct {
	ID         uint32    `gorm:"primaryKey;column:id;type:int(11) unsigned;not null"`
	GroupID    uint32    `gorm:"index:idx_gp;column:group_id;type:int(11) unsigned;not null;default:0"` // 技能组id
	Game       string    `gorm:"column:game;type:varchar(64);not null"`                                 // 项目(游戏)
	Language   string    `gorm:"column:language;type:varchar(16);not null"`                             // 语言
	Categories string    `gorm:"column:categories;type:varchar(10240);not null"`                        // 问题类别
	CreatedAt  time.Time `gorm:"column:created_at;type:datetime;not null"`                              // 创建时间
	UpdatedAt  time.Time `gorm:"column:updated_at;type:datetime;default:null"`                          // 更新时间
}

type FpOpsGroupCatList struct {
	FpOpsGroupCat
	Enable     bool   `gorm:"column:enable;type:tinyint(1) unsigned;not null;default:1"`                // 启用 0:false 1:true
	GroupDesc  string `gorm:"column:group_desc;type:varchar(64);not null;default:''"`                   // 团队描述
	UpperLimit int32  `gorm:"column:upper_limit;type:int(4);not null;default:0"`                        // 接单上线
	User       string `gorm:"uniqueIndex:idx_gp_user;column:user;type:varchar(64);not null;default:''"` // 用户名称
	Operator   string `gorm:"column:operator;type:varchar(64);not null;default:''"`                     // op
}

func (m *FpOpsGroupCat) TableName() string {
	return "fp_ops_group_cat"
}

// FpOpsGroupUser 技能组用户表
type FpOpsGroupUser struct {
	ID            uint32    `gorm:"primaryKey;column:id;type:int(11) unsigned;not null"`
	GroupID       uint32    `gorm:"uniqueIndex:idx_gp_user;column:group_id;type:int(11) unsigned;not null"` // 技能组ID
	Enable        bool      `gorm:"column:enable;type:tinyint(1) unsigned;not null;default:1"`              // 启用 0:false 1:true
	IsLogin       uint32    `gorm:"column:is_login;type:tinyint;default:0;comment:'是否登陆：0:离线, 1:在线'"`
	UpperLimit    int32     `gorm:"column:upper_limit;type:int(4);not null;default:0"`                        // 接单上线
	User          string    `gorm:"uniqueIndex:idx_gp_user;column:user;type:varchar(64);not null;default:''"` // 用户名称
	CreatedAt     time.Time `gorm:"column:created_at;type:datetime;not null"`                                 // 创建时间
	UpdatedAt     time.Time `gorm:"column:updated_at;type:datetime;default:null"`                             // 更新时间
	Game          string    `gorm:"column:game;type:varchar(1024);not null"`                                  // 项目(游戏)
	Language      string    `gorm:"column:language;type:varchar(512);not null"`                               // 语言
	LastAllocTkAt uint64    `gorm:"column:last_alloc_tk_at;type:bigint(20);not null;default:0"`               // 最后分单时间
	Operator      string    `gorm:"column:operator;type:varchar(64);not null;default:''"`                     // op
}

func (m *FpOpsGroupUser) TableName() string {
	return "fp_ops_group_user"
}

type ProjectGroupUserDf struct {
	Id             uint32             `json:"id"`       // 团队 人员 id
	GroupId        uint32             `json:"group_id"` // 团队 id
	IsLogin        pb.UserLoginStatus `gorm:"column:is_login;type:tinyint;default:0;comment:'是否登陆：0:离线, 1:在线'"`
	UpperLimit     int32              `gorm:"column:upper_limit;type:int(4);not null;default:0"`                        // 接单上线
	User           string             `gorm:"uniqueIndex:idx_gp_user;column:user;type:varchar(64);not null;default:''"` // 用户名称
	Game           []string           `json:"game"`
	Language       []string           `json:"language"`
	LastAllocTkAt  uint64             `gorm:"column:last_alloc_tk_at;type:bigint(20);not null;default:0"` // 最后分单时间
	CurrDoingTkNum int32              `json:"curr_doing_tk_num"`                                          // 当前用户处理中工单量
}

// FpOpsTags 标签表
type FpOpsTags struct {
	TagID     uint32 `gorm:"primaryKey;column:tag_id;type:int(11) unsigned;not null"`
	LibID     uint32 `gorm:"uniqueIndex:idx_lib_id;index:idx_node;column:lib_id;type:int(11) unsigned;not null;default:0"` // 标签库ID
	TagName   string `gorm:"uniqueIndex:idx_lib_id;column:tag_name;type:varchar(64);not null;default:''"`                  // 标签名称
	ParentID  uint32 `gorm:"uniqueIndex:idx_lib_id;column:parent_id;type:int(11) unsigned;not null;default:0"`             // 父级ID
	Lft       uint32 `gorm:"index:idx_node;column:lft;type:int(11) unsigned;not null;default:0"`                           // 左值
	Rgt       uint32 `gorm:"index:idx_node;column:rgt;type:int(11) unsigned;not null;default:0"`                           // 右值
	Level     uint32 `gorm:"uniqueIndex:idx_lib_id;column:level;type:int(4) unsigned;not null;default:0"`                  // 层级
	Enable    uint32 `gorm:"column:enable;type:tinyint(1) unsigned;not null;default:0"`                                    // 启用 0:false 1:true
	Operator  string `gorm:"column:operator;type:varchar(64);not null;default:''"`                                         // 更新人员
	CreatedAt uint64 `gorm:"column:created_at;type:bigint(20);not null;default:0"`                                         // 创建时间
	UpdatedAt uint64 `gorm:"column:updated_at;type:bigint(20);not null;default:0"`                                         // 更新时间
}

func (m FpOpsTags) TableName() string {
	return "fp_ops_tags"
}

// FpOpsTagsLib 标签组表
type FpOpsTagsLib struct {
	LibID         uint32            `gorm:"primaryKey;column:lib_id;type:int(11) unsigned;not null"`       // 标签库ID
	LibName       string            `gorm:"unique;column:lib_name;type:varchar(64);not null;default:''"`   // 名称
	LibType       uint16            `gorm:"column:lib_type;type:smallint(2) unsigned;not null;default:0"`  // 类别: 0.未知 1.工单 2.Discord 3.Line
	TagUploadFile string            `gorm:"column:tag_upload_file;type:varchar(512);not null;default:''"`  // 上传文件原始地址
	Operator      string            `gorm:"column:operator;type:varchar(64);not null;default:''"`          // 操作人员
	Enable        uint32            `gorm:"column:enable;type:tinyint(1) unsigned;not null;default:0"`     // 启用 0:false 1:true
	CreatedAt     uint64            `gorm:"column:created_at;type:bigint(20) unsigned;not null;default:0"` // 创建时间
	UpdatedAt     uint64            `gorm:"column:updated_at;type:bigint(20) unsigned;not null;default:0"` // 更新时间
	Tags          []*FpOpsTags      `gorm:"foreignKey:LibID;references:LibID" json:"tags,omitempty"`       // 关联标签
	Projects      []*FpOpsTagsLibGm `gorm:"foreignKey:LibID;references:LibID" json:"projects,omitempty"`   // 关联游戏
}

func (m *FpOpsTagsLib) TableName() string {
	return "fp_ops_tags_lib"
}

// FpOpsTagsLibGm 标签库游戏关联表
type FpOpsTagsLibGm struct {
	ID        uint32 `gorm:"primaryKey;column:id;type:int(11) unsigned;not null"`
	Project   string `gorm:"uniqueIndex:idx_project_lib;column:project;type:varchar(64);not null;default:''"`    // 描述
	LibID     uint32 `gorm:"uniqueIndex:idx_project_lib;column:lib_id;type:int(11) unsigned;not null;default:0"` // 标签库ID
	Enable    uint32 `gorm:"column:enable;type:tinyint(1) unsigned;not null;default:0"`                          // 启用 0:false 1:true
	CreatedAt uint64 `gorm:"column:created_at;type:bigint(20) unsigned;not null;default:0"`                      // 创建时间
	UpdatedAt uint64 `gorm:"column:updated_at;type:bigint(20) unsigned;not null;default:0"`                      // 更新时间
}

func (m *FpOpsTagsLibGm) TableName() string {
	return "fp_ops_tags_lib_gm"
}
