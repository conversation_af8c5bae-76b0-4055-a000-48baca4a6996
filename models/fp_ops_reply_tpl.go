package models

import "time"

// FpOpsReplyTpl 模板配置表
type FpOpsReplyTpl struct {
	ID        uint32               `gorm:"primaryKey;column:id;type:int(11) unsigned;not null"`
	Project   string               `gorm:"uniqueIndex:idx_prj_tpl;column:project;type:varchar(64);not null;default:''"` // 所属项目
	ReplyTpl  string               `gorm:"uniqueIndex:idx_prj_tpl;column:reply_tpl;type:varchar(32);not null"`          // 回复模版名称
	Enable    bool                 `gorm:"column:enable;type:tinyint(1) unsigned;not null;default:1"`                   // 激活. 0:false 1:true
	Operator  string               `gorm:"column:operator;type:varchar(64);not null;default:''"`                        // 更新用户
	CreatedAt time.Time            `gorm:"column:created_at;type:datetime"`                                             // 创建时间
	UpdatedAt time.Time            `gorm:"column:updated_at;type:datetime"`                                             // 更新时间
	Language  []*FpOpsReplyTplLang `gorm:"foreignKey:ReplyTplID;references:ID"`
}
