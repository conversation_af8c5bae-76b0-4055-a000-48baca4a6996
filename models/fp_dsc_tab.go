package models

// FpDscTab discord搜索tab
type FpDscTab struct {
	ID          int64  `gorm:"primaryKey;column:id;type:bigint(20) unsigned;not null" json:"id"`
	Project     string `gorm:"column:project;type:varchar(32);not null;default:''"`                                           // 所属项目
	TabName     string `gorm:"column:tab_name;type:varchar(128);not null;default:''" json:"tab_name"`                         // tab名
	Operator    string `gorm:"column:operator;type:varchar(64);not null;default:''" json:"operator"`                          // 操作人
	Detail      string `gorm:"column:detail;type:text;not null;default:''" json:"detail"`                                     // 搜索条件信息
	Public      uint8  `gorm:"column:public;type:tinyint(3);not null;default:2" json:"public"`                                // 1公开 2私有
	CreateTime  int64  `gorm:"column:create_time;type:bigint(20) unsigned;not null;default:0" json:"create_time"`             // 创建时间
	UpdatedTime string `gorm:"column:update_time;type:varchar(64);not null;default:'1900-01-01 00:00:00'" json:"update_time"` // 更新时间
}

func GetFpDscTabTableName() string {
	return "fp_dsc_tab"
}
