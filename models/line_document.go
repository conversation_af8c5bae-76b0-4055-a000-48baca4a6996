package models

type (
	FpLineUserDoc struct {
		ID                uint64          `json:"id"`
		LineUserID        string          `json:"line_user_id"`         // line用户user_id
		ChannelID         string          `json:"channel_id"`           // 私信频道ID
		BotID             string          `json:"bot_id"`               // 所属应用/机器人bot的 user_id
		Project           string          `json:"project"`              // 所属项目
		DisplayName       string          `json:"display_name"`         // the user display name
		LineCommu         []*LineCommuDoc `json:"line_commu"`           // 会话列表
		PortraitRemark    string          `json:"portrait_remark"`      // 画像备注
		UID               uint64          `json:"uid"`                  // uid
		AccountId         string          `json:"account_id"`           // fpid
		Sid               int             `json:"sid"`                  // 服务器
		Maintainer        string          `json:"maintainer"`           // 维护人
		ReplyType         uint8           `json:"reply_type"`           // 是否已回复 pb.DscReplyTpDf ： 1:未回复 2:已回复
		VipLevel          uint8           `json:"vip_level"`            // 玩家VIP等级
		PayAll            int64           `json:"pay_all"`              // 玩家累计付费金额
		PayLastThirtyDays int64           `json:"pay_last_thirty_days"` // 玩家最近30天付费金额
		LastLogin         int64           `json:"last_login"`           // 玩家最近登录时间
		VipState          uint8           `json:"vip_state"`            // 玩家VIP状态
		Note              string          `json:"note"`                 // 备注
		PlayerNick        string          `json:"player_nick"`          // 游戏昵称
		Birthday          string          `json:"birthday"`             // 生日
		Lang              string          `json:"lang"`                 // 语言
		LastReplyService  string          `json:"last_reply_service"`   // 最近回复客服
		Tag               []uint32        `json:"tag"`                  // 标签
		IsDeleted         uint8           `json:"is_deleted"`           // is_deleted 标记是否已删除，1默认未删除 2已删除
		FollowStatus      uint8           `json:"follow_status"`        // 关注状态 1:未关注 2:已关注 code.FollowStatusTpDf
		CreatedAt         uint64          `json:"created_at"`           // 创建时间
		UpdatedAt         uint64          `json:"updated_at"`           // 更新时间
	}
	LineCommuDoc struct {
		MsgID      string `json:"msg_id"`       // 消息id
		MsgFrom    uint8  `json:"msg_from"`     // 消息来源 pb.DscMsgFromTpDf ： 1:用户 2:机器人
		FromUserID string `json:"from_user_id"` // 消息来源用户id: user-id/bot-user-id
		CreatedAt  int64  `json:"created_at"`   // 消息创建时间
		Content    string `json:"content"`      // 消息内容
	}
	LinePlayer struct {
		Uid               int64
		Fpid              string
		VipType           uint16
		TotalPay          int64
		LastThirtyDaysPay int64
		LastLogin         string
		VipLevel          uint8
		Channel           string
		Sid               string
		Lang              string
		PlayerNick        string
	}
)
