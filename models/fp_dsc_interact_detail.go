package models

import "time"

// FpDscInteractDetail  discord玩家交互明细表
type FpDscInteractDetail struct {
	ID           uint64    `gorm:"primaryKey;column:id;type:bigint(20) unsigned;not null"`
	Project      string    `gorm:"column:project;type:varchar(32);not null;default:''"`                                        // 所属项目
	InteractDate string    `gorm:"column:interact_date;type:varchar(32);not null;default:''"`                                  // 交互日期
	Operator     string    `gorm:"column:operator;type:varchar(64);not null;default:''"`                                       // 客服处理人
	DscUserID    string    `gorm:"column:dsc_user_id;type:varchar(32);not null;default:''"`                                    // 玩家 discord_user_id
	NickName     string    `gorm:"column:nick_name;type:varchar(64);not null;default:''" json:"nick_name"`                     // 玩家昵称
	UID          uint64    `gorm:"column:uid;type:bigint(20) unsigned;not null;default:0" json:"uid"`                          // 玩家uid
	AccountId    string    `gorm:"column:account_id;type:varchar(64);not null;default:''" json:"account_id"`                   // 玩家的account_id(fpid)
	CreatedAt    time.Time `gorm:"index:idx_create_at;column:created_at;type:datetime;default:null;default:CURRENT_TIMESTAMP"` // 创建时间
}

func GetFpDscInteractDetailTableName() string {
	return "fp_dsc_interact_detail"
}
