// Copyright 2021 funplus Authors. All Rights Reserved.
// @Description: 表单映射表
// @Author: Darcy
// @Date: 2022/11/21 10:53

package models

// FpOpsDict 表单映射表
type FpOpsDict struct {
	DictID    uint32 `gorm:"primaryKey;column:dict_id;type:int(11) unsigned;not null"`
	DictName  string `gorm:"unique;column:dict_name;type:varchar(64);not null;default:''"`  // 字段名称
	DictKey   string `gorm:"column:dict_key;type:varchar(32);not null;default:''"`          // SDK对应key
	Enable    bool   `gorm:"column:enable;type:tinyint(1) unsigned;not null;default:1"`     // 启用 0:false 1:true
	Operator  string `gorm:"column:operator;type:varchar(64);not null;default:''"`          // 更新人员
	CreatedAt uint64 `gorm:"column:created_at;type:bigint(20) unsigned;not null;default:0"` // 创建时间
	UpdatedAt uint64 `gorm:"column:updated_at;type:bigint(20) unsigned;not null;default:0"` // 更新时间
}
