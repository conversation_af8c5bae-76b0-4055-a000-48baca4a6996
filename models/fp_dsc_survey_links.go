package models

import (
	"context"
	jsoniter "github.com/json-iterator/go"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"ops-ticket-api/proto/pb"
	"time"
)

const TableNameFpDscSurveyLink = "fp_dsc_survey_links"

// FpDscSurveyLinks 问卷链接信息表
type FpDscSurveyLinks struct {
	ID             uint64    `gorm:"primaryKey;column:id;type:bigint unsigned;not null"`
	Project        string    `gorm:"column:project;type:varchar(32);not null;default:''"`                 // 所属项目
	IsPublic       int8      `gorm:"column:is_public;type:tinyint;not null;default:0"`                    // 是否为公开问卷（0:私密 1:公开）
	EncryptedToken string    `gorm:"unique;column:encrypted_token;type:varchar(256);not null;default:''"` // 链接加密串
	BatchID        uint64    `gorm:"column:batch_id;type:bigint unsigned;not null;default:0"`             // 批次ID
	UID            int64     `gorm:"column:uid;type:bigint;not null;default:0"`                           // 玩家 UID
	DscUserID      string    `gorm:"column:dsc_user_id;type:varchar(32);not null;default:''"`             // 玩家 dsc_user_id
	SurveyID       int64     `gorm:"column:survey_id;type:bigint;not null;default:0"`                     // 调查问卷id
	Creator        string    `gorm:"column:creator;type:varchar(32);not null;default:''"`                 // 链接创建人（system or 客服）
	CreatedAt      time.Time `gorm:"column:created_at;type:datetime;not null;default:CURRENT_TIMESTAMP"`  // 链接创建时间
	ExpireAt       time.Time `gorm:"column:expire_at;type:datetime;not null;default:CURRENT_TIMESTAMP"`   // 链接失效时间
	LinkURL        string    `gorm:"column:link_url;type:varchar(512);not null;default:''"`               // 链接地址
	TokenParam     string    `gorm:"column:token_param;type:varchar(512);not null;default:''"`            // 详细参数
	UpdatedAt      time.Time `gorm:"column:updated_at;type:datetime;not null;default:CURRENT_TIMESTAMP"`  // 更新时间
}

func (m *FpDscSurveyLinks) DecodeTokenParam(ctx context.Context) *pb.SurveyLinkParamDf {
	var param = &pb.SurveyLinkParamDf{MomentAttrs: &pb.SurveyLinkParamDf_Attrs{}}
	if err := jsoniter.UnmarshalFromString(m.TokenParam, param); err != nil {
		logger.Errorf(ctx, "DecodeTokenParam err: %v. %s", err, m.TokenParam)
	}
	return param
}
func (m *FpDscSurveyLinks) CheckCanSaveSurvey(ctx context.Context) bool {
	if m.IsPublic == 0 {
		return time.Now().Before(m.ExpireAt)
	}
	return true
}

// TableName FpDscSurveyLink's table name
func (*FpDscSurveyLinks) TableName() string {
	return TableNameFpDscSurveyLink
}
