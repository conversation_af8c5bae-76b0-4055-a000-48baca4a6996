package models

import "time"

// FpOpsTicketsDraft 工单草稿表
type FpOpsTicketsDraft struct {
	ID        uint64    `gorm:"column:id"`
	TicketID  uint64    `gorm:"column:ticket_id"`  // 工单ID
	Content   string    `gorm:"column:content"`    // 内容
	Operator  string    `gorm:"column:operator"`   // 操作人员
	CreatedAt time.Time `gorm:"column:created_at"` // 更新时间
	UpdatedAt time.Time `gorm:"column:updated_at"` // 创建时间
}
