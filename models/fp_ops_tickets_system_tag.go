package models

// FpOpsTicketsSystemTag 工单系统标签表
type FpOpsTicketsSystemTag struct {
	ID        uint64 `gorm:"primaryKey;column:id;type:bigint(20);not null"`
	TicketID  uint64 `gorm:"column:ticket_id;type:bigint(20);not null"` // 工单ID
	LabelID   uint32 `gorm:"column:label_id;type:int(11);not null"`     // 工单标签
	Op        string `gorm:"column:op;type:varchar(64);not null"`       // 添加人员
	CreatedAt uint64 `gorm:"column:created_at;type:bigint(20) unsigned;not null;default:0"`
	UpdatedAt uint64 `gorm:"column:updated_at;type:bigint(20) unsigned;not null;default:0"`
}
