package models

// FpOpsOvertimeTpl 超时提醒模板表
type FpOpsOvertimeTpl struct {
	ID          uint64                  `gorm:"primaryKey;column:id;type:bigint(20) unsigned;not null" json:"id"`
	TplName     string                  `gorm:"column:tpl_name;type:varchar(128);not null;default:''" json:"tpl_name"`             // 模板名
	Content     string                  `gorm:"column:content;type:text;not null;default:''" json:"content"`                       // 模板内容
	Overtime    uint64                  `gorm:"column:overtime;type:bigint(20);not null;default:0" json:"overtime"`                // 超时时间
	Operator    string                  `gorm:"column:operator;type:varchar(64);not null;default:''" json:"operator"`              // 操作人
	Enable      uint8                   `gorm:"column:enable;type:tinyint(3);not null;default:0" json:"enable"`                    // 1启用 2禁用
	CreateTime  uint64                  `gorm:"column:create_time;type:bigint(20) unsigned;not null;default:0" json:"create_time"` // 创建时间
	UpdatedTime uint64                  `gorm:"column:update_time;type:bigint(20) unsigned;not null;default:0" json:"update_time"` // 更新时间
	GameProject []*FpOpsOvertimeTplGame `gorm:"foreignKey:TplID;references:ID"`
}

func GetFpOpsOvertimeTplTableName() string {
	return "fp_ops_overtime_tpl"
}

type FpOpsOvertimeTplList struct {
	FpOpsOvertimeTpl
	GameProject string `gorm:"column:game_project;type:text;not null;default:''"` // 关联游戏
}
