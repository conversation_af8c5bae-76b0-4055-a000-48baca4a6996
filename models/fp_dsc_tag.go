package models

// FpDscTag discord标签库
type FpDscTag struct {
	ID          uint64 `gorm:"primaryKey;column:id;type:bigint(20) unsigned;not null" json:"id"`
	TagName     string `gorm:"column:tag_name;type:varchar(128);not null;default:''" json:"tag_name"`                         // 标签名
	TagDesc     string `gorm:"column:tag_desc;type:varchar(128);not null;default:''" json:"tag_desc"`                         // 标签描述
	Operator    string `gorm:"column:operator;type:varchar(64);not null;default:''" json:"operator"`                          // 操作人
	Enable      uint8  `gorm:"column:enable;type:tinyint(3);not null;default:1" json:"enable"`                                // 1启用 2禁用
	CreateTime  uint64 `gorm:"column:create_time;type:bigint(20) unsigned;not null;default:0" json:"create_time"`             // 创建时间
	UpdatedTime string `gorm:"column:update_time;type:varchar(64);not null;default:'1900-01-01 00:00:00'" json:"update_time"` // 更新时间
}

func GetFpDscTagTableName() string {
	return "fp_dsc_tag"
}
