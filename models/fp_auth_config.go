package models

// FpAuthConfig 游戏密钥配置表
type FpAuthConfig struct {
	ID          int64  `gorm:"primaryKey;column:id;type:bigint(20) unsigned;not null"`
	GameProject string `gorm:"column:game_project;type:varchar(64);not null;default:''"`                   // 游戏
	AuthKey     string `gorm:"column:auth_key;type:varchar(255);not null;default:''"`                      // auth_key
	Secret      string `gorm:"column:secret;type:varchar(255);not null;default:''"`                        // 密钥
	CreateTime  uint64 `gorm:"column:create_time;type:bigint(20) unsigned;not null;default:0"`             // 创建时间
	UpdatedTime string `gorm:"column:update_time;type:varchar(64);not null;default:'1900-01-01 00:00:00'"` // 更新时间
}
