// Copyright 2021 funplus Authors. All Rights Reserved.
// @Description: 表单映射表
// @Author: Darcy
// @Date: 2022/11/21 10:53

package models

import (
	"strings"

	"github.com/spf13/cast"
	"ops-ticket-api/internal/code"
	"ops-ticket-api/proto/pb"
	"ops-ticket-api/utils"
)

// FpOpsTickets 工单表
type FpOpsTickets struct {
	TicketID         uint64                   `gorm:"primaryKey;index:idx_prj;column:ticket_id;type:bigint(20) unsigned;not null"`
	UUID             string                   `gorm:"column:uuid;type:varchar(128);not null;default:''"`                                             // 设备指纹
	AccountID        string                   `gorm:"index:idx_account;column:account_id;type:varchar(128);not null;default:''"`                     // fpid/account_id
	UID              uint64                   `gorm:"column:uid;type:bigint(20) unsigned;not null;default:0"`                                        // uid
	Vip              uint16                   `gorm:"column:vip;type:smallint(2) unsigned;not null;default:0"`                                       // vip单标识： 0:普通用户；2:VIP 用户
	Recharge         uint64                   `gorm:"column:recharge;type:bigint(20) unsigned;not null;default:0"`                                   // 充值金额 单位 美金/元*10000
	Nickname         string                   `gorm:"column:nickname;type:varchar(64);not null;default:''"`                                          // 用户昵称
	VipCrm           int8                     `gorm:"column:vip_crm;type:tinyint(1);not null;default:0"`                                             // crm vip
	UserType         int8                     `gorm:"column:user_type;type:smallint(2);not null;default:0"`                                          // 提单用户类型
	TroubleAccountID string                   `gorm:"column:trouble_account_id;type:varchar(128);not null;default:''"`                               // 问题fpx-account
	TroubleUID       uint64                   `gorm:"column:trouble_uid;type:bigint(20) unsigned;not null;default:0"`                                // 问题uid
	SVIP             int8                     `gorm:"column:svip;type:tinyint(2);not null;default:0"`                                                // crm svip
	Project          string                   `gorm:"index:idx_prj;index:idx_prj_cat;column:project;type:varchar(64);not null;default:''"`           // 游戏
	GmID             string                   `gorm:"column:gm_id;type:varchar(128);not null;default:''"`                                            // 游戏标识: game_id(kg)/app_id(fpx)
	Channel          string                   `gorm:"column:channel;type:varchar(64);not null;default:''"`                                           // 渠道
	SubChannel       string                   `gorm:"column:sub_channel;type:varchar(64);not null;default:''"`                                       // 子渠道
	PackageID        string                   `gorm:"column:package_id;type:varchar(64);not null;default:''"`                                        // 渠道号
	Sid              uint32                   `gorm:"column:sid;type:int(11) unsigned;not null;default:0"`                                           // 区服ID
	RoleID           string                   `gorm:"column:role_id;type:varchar(128);not null;default:''"`                                          // 角色ID
	RoleName         string                   `gorm:"column:role_name;type:varchar(128);not null;default:''"`                                        // 角色名称
	Scene            uint8                    `gorm:"column:scene;type:tinyint(2) unsigned;not null;default:0"`                                      // 入口场景枚举 对应：pb.SceneType
	Origin           uint8                    `gorm:"index:idx_cnv;column:origin;type:tinyint(1) unsigned;not null;default:1"`                       // 工单来源 1:玩家  2:VIP  3: 普通客服 // TODO: 字段是否可以废弃
	CatID            uint32                   `gorm:"index:idx_prj_cat;index:idx_cnv;column:cat_id;type:int(11) unsigned;not null;default:0"`        // 分类ID
	RelateType       uint32                   `gorm:"column:relate_type;type:tinyint(1);not null;default:1"`                                         // 关联类型,1:表单 2:流程
	AutoReplyID      uint32                   `gorm:"column:auto_reply_id;type:int(11) unsigned;not null;default:0"`                                 // 自动回复模版ID
	Evaluation       bool                     `gorm:"column:evaluation;type:tinyint(1) unsigned;not null;default:1"`                                 // 允许评价 0:false 1:true
	TplID            uint32                   `gorm:"column:tpl_id;type:int(11) unsigned;not null;default:0"`                                        // 模版ID
	ProcessID        uint32                   `gorm:"column:process_id;type:bigint(20) unsigned;not null;default:0"`                                 // 流程id
	ProcessSession   string                   `gorm:"index:idx_process;column:process_session;type:varchar(255);not null;default:'';comment:'流程会话'"` // 流程会话
	Priority         uint8                    `gorm:"column:priority;type:tinyint(1) unsigned;not null;default:0"`                                   // 是否是升级单： 0:否 ,1:是
	UpgradeNum       uint32                   `gorm:"column:upgrade_num;type:tinyint(4) unsigned;not null;default:0"`                                // 升级次数
	Lang             string                   `gorm:"column:lang;type:varchar(16);not null;default:''"`                                              // 语言
	Creator          string                   `gorm:"column:creator;type:varchar(64);not null;default:''"`                                           // 创建人
	Acceptor         string                   `gorm:"index:idx_cnv;column:acceptor;type:varchar(64);not null;default:''"`                            // 受理员
	Csi              uint32                   `gorm:"column:csi;type:tinyint(1) unsigned;not null;default:0"`                                        // 用户满意度
	Nps              uint32                   `gorm:"column:nps;type:tinyint(1) unsigned;not null;default:0"`                                        // 推荐给别人的意愿度
	Resolved         bool                     `gorm:"column:resolved;type:tinyint(1) unsigned;not null;default:0"`                                   // 解决 0:false 1:true
	Remark           bool                     `gorm:"column:remark;type:tinyint(1) unsigned;not null;default:0"`                                     // 备注 0:false 1:true
	Proof            uint8                    `gorm:"column:proof;type:tinyint(1) unsigned;not null;default:0"`                                      // 打回补填 0:false 1:true
	ProofAt          uint64                   `gorm:"column:proof_at;type:bigint(20) unsigned;not null;default:0"`                                   // 补填时间
	Status           uint32                   `gorm:"column:status;type:tinyint(1) unsigned;not null;default:0"`                                     // 工单状态 对应：pb.TkStatus
	ConversionNode   uint32                   `gorm:"index:idx_cnv;column:conversion_node;type:int(11) unsigned;not null;default:0"`                 // 流转节点 对应： pb.TkStage
	SolveType        uint32                   `gorm:"index:idx_cnv;column:solve_type;type:int(11) unsigned;not null;default:0"`                      // 处理类型 对应： pb.SolveType
	TicketType       uint32                   `gorm:"column:ticket_type;type:int(11) unsigned;not null;default:0"`                                   // 处理类型 对应： pb.TicketType
	IsInvalid        uint32                   `gorm:"column:is_invalid;type:int(11) unsigned;not null;default:0"`                                    // 0：有效，1：无效
	Replied          uint8                    `gorm:"column:replied;type:tinyint(1) unsigned;not null;default:0"`                                    // 问题回复状态 0:未回复 1:已回复
	Closed           uint8                    `gorm:"index:idx_cnv;column:closed;type:tinyint(1) unsigned;not null;default:0"`                       // 结案状态:0:空 1:玩家结案 2:客服结案  3:系统
	ClosedAt         uint64                   `gorm:"column:closed_at;type:bigint(20) unsigned;not null;default:0"`                                  // 结案时间
	FirstClosedAt    uint64                   `gorm:"column:first_closed_at;type:bigint(20) unsigned;not null;default:0"`                            // 首次结案时间
	ReopenNum        uint32                   `gorm:"column:reopen_num;type:tinyint(4) unsigned;not null;default:0"`                                 // 重开次数
	ReopenInterval   uint32                   `gorm:"column:reopen_interval;type:int(11) unsigned;not null;default:0"`                               // 重开间隔
	ResolveConfirmed int8                     `gorm:"column:resolve_confirmed;type:tinyint(4);not null;default:0"`                                   // -1：未选择 1：解决 2：未解决
	ReplyFrom        uint8                    `gorm:"column:reply_from;type:tinyint(1) unsigned;not null;default:1"`                                 // 回复玩家工作台 - 废弃字段
	FirstReplyAt     uint64                   `gorm:"column:first_reply_at;type:bigint(20) unsigned;not null;default:0"`                             // 最初回复时间
	LastReplyAt      uint64                   `gorm:"column:last_reply_at;type:bigint(20) unsigned;not null;default:0"`                              // 最近回复时间
	SortWaitStartAt  uint64                   `gorm:"column:sort_wait_start_at;type:bigint(20) unsigned;not null;default:0"`                         // 搜索排序字段：等待开始时间
	IsDeleted        bool                     `gorm:"column:is_deleted;type:tinyint(1) unsigned;not null;default:0"`                                 // 1:删除 0:正常
	CreatedAt        uint64                   `gorm:"index:idx_prj_cat;column:created_at;type:bigint(20) unsigned;not null;default:0"`               // 创建时间
	UpdatedAt        uint64                   `gorm:"column:updated_at;type:bigint(20) unsigned;not null;default:0"`                                 // 更新时间	Fields           FpOpsTicketsField    `gorm:"foreignKey:TicketID;references:TicketID" json:"fields,omitempty"`                        // 对话
	Fields           FpOpsTicketsField        `gorm:"foreignKey:TicketID;references:TicketID" json:"fields,omitempty"`                               // 表单提交详细字段
	Device           FpOpsTicketsDevice       `gorm:"foreignKey:TicketID;references:TicketID" json:"device,omitempty"`                               // 设备基础信息
	Tags             []*FpOpsTicketsTags      `gorm:"foreignKey:TicketID;references:TicketID" json:"tags,omitempty"`                                 // 新标签
	SystemTags       []*FpOpsTicketsSystemTag `gorm:"foreignKey:TicketID;references:TicketID" json:"system_tags,omitempty"`                          // 系统标签
	Appraise         FpOpsTicketsAppraise     `gorm:"foreignKey:TicketID;references:TicketID" json:"appraise,omitempty"`                             // 评价
	Replies          []*FpOpsTicketsReply     `gorm:"foreignKey:TicketID;references:TicketID" json:"Replies,omitempty"`                              // 工单回复
	Histories        []*FpOpsTicketsHistory   `gorm:"foreignKey:TicketID;references:TicketID" json:"histories,omitempty"`                            // 工单日志
	Proofs           []*FpOpsTicketsProof     `gorm:"foreignKey:TicketID;references:TicketID" json:"proofs,omitempty"`                               // 工单补填
	Commus           []*FpOpsTicketsCommu     `gorm:"foreignKey:TicketID;references:TicketID" json:"commus,omitempty"`                               // 工单关联对话记录
	Cat              FpOpsCategory            `gorm:"foreignKey:CatID;references:CatID" json:"category,omitempty"`                                   // 关联的重开工单
	AiTag            FpOpsTicketsAiTag        `gorm:"foreignKey:TicketID;references:TicketID" json:"ai_tag,omitempty"`                               // 关联的重开工单
	TroubleUser      *FpOpsTicketsTroubleUser `gorm:"-" json:"trouble_user,omitempty"`
	ConversationID   string                   `gorm:"column:conversation_id;type:varchar(128);not null;default:''"`      // 会话ID
	ZoneVipLevel     uint64                   `gorm:"column:zone_vip_level;type:bigint(20) unsigned;not null;default:0"` // 私域R级参数
}

func (s *FpOpsTickets) TableName() string {
	return "fp_ops_tickets"
}

func TicketAssign(gmProject string, category *FpOpsCategory, param *TicketCreateParam) *FpOpsTickets {
	var remainRom uint32
	if strings.Contains(param.Input.RemainRom, "MB") {
		remainRomStr := strings.Replace(param.Input.RemainRom, "MB", "", -1)
		remainRom = cast.ToUint32(utils.MetricsRate(cast.ToUint32(remainRomStr), 1024, 2) * 100)
	} else {
		remainRom = cast.ToUint32(cast.ToFloat32(param.Input.RemainRom) * 100)
	}
	now := utils.NowTimestamp()

	relateType := code.RelateTypeTpl
	if param.Input.ProcessId > 0 {
		relateType = code.RelateTypeProcess
	}
	if param.Input.TotalPay > 0 && param.SubmitUser.TotalPay == 0 {
		param.SubmitUser.TotalPay = param.Input.TotalPay
	}
	userType := int8(pb.UserTypeEnum_UserTypeRegularUser)
	if param.SubmitUser.VipType == 2 {
		userType = int8(pb.UserTypeEnum_UserTypeLongVipUser)
	} else if param.SubmitUser.VipType == 3 {
		userType = int8(pb.UserTypeEnum_UserTypeLimitTimeUser)
	} else if param.SubmitUser.TotalPay > 0 {
		userType = int8(pb.UserTypeEnum_UserTypePaidUser)
	}

	// 如果troubleuid没传或者是本人，则tbfpid和tbuid均为提交者信息
	if param.Input.TroubleUid == 0 || param.Input.TroubleUid == param.Input.Uid {
		if param.Input.AccountId == "" {
			param.Input.TroubleAccountId = cast.ToString(param.Input.Fpid)
		} else {
			param.Input.TroubleAccountId = param.Input.AccountId
		}
		param.Input.TroubleUid = param.Input.Uid
	}

	if param.Input.Channel == "" && param.SubmitUser.Channel != "" {
		param.Input.Channel = param.SubmitUser.Channel
	}
	// 港澳台特殊处理
	country := param.Input.Country
	if country == "TW" || country == "tw" {
		country = "ZH-TW"
	}
	if country == "HK" || country == "hk" {
		country = "ZH-HK"
	}
	if country == "MO" || country == "mo" {
		country = "ZH-MO"
	}
	return &FpOpsTickets{
		UUID:             strings.TrimSpace(param.Input.Uuid),
		AccountID:        param.Input.AccountId,
		UID:              param.Input.Uid,
		Nickname:         param.Input.Nickname,
		Project:          gmProject,
		GmID:             param.Input.FpxAppId,
		VipCrm:           param.SubmitUser.VipCrm,
		UserType:         userType,
		SVIP:             param.SubmitUser.SVIP,
		TroubleAccountID: param.Input.TroubleAccountId,
		TroubleUID:       param.Input.TroubleUid,
		Status:           uint32(pb.TkStatus_TkStatusUntreated),
		ConversionNode:   uint32(pb.TkStage_TkStageNew),
		Channel:          param.Input.Channel,
		SubChannel:       param.Input.Subchannel,
		PackageID:        param.Input.PackageId,
		Sid:              cast.ToUint32(param.Input.Sid),
		RoleID:           param.Input.RoleId,
		Lang:             param.Input.Lang,
		Origin:           uint8(param.Input.Origin),
		Scene:            uint8(param.Input.Scene),
		CatID:            param.Input.CatId,
		RelateType:       uint32(relateType),
		TplID:            category.TplID,
		ProcessID:        uint32(param.Input.ProcessId),
		ProcessSession:   param.Input.ProcessSession,
		Evaluation:       true,
		ResolveConfirmed: -1,
		CreatedAt:        now,
		SortWaitStartAt:  now,
		ZoneVipLevel:     param.Input.ZoneVipLevel,
		Fields: FpOpsTicketsField{
			Field: param.Input.Fields,
		},
		Device: FpOpsTicketsDevice{
			UUID:        strings.TrimSpace(param.Input.Uuid),
			Channel:     param.Input.Channel,
			SubChannel:  param.Input.Subchannel,
			PackageID:   param.Input.PackageId,
			DeviceType:  param.Input.DeviceType,
			IP:          param.Ip,
			Os:          param.Input.Os,
			OsVersion:   param.Input.OsVersion,
			AppVersion:  param.Input.AppVersion,
			SdkVersion:  param.Input.SdkVersion,
			RomGb:       cast.ToUint32(cast.ToFloat32(param.Input.RomGb) * 100),
			RemainRom:   remainRom,
			RAMMb:       cast.ToUint32(cast.ToFloat32(param.Input.RamMb) * 100),
			NetworkInfo: param.Input.NetworkInfo,
			Country:     country,
			CreatedAt:   now,
			UpdatedAt:   now,
		},
		TroubleUser: &FpOpsTicketsTroubleUser{
			UID:       param.Input.TroubleUid,
			AccountID: param.Input.TroubleAccountId,
		},
	}
}

func TicketAssignDoc(ticket *FpOpsTickets) *TicketsDoc {
	tags := make([]uint32, 0)
	for _, tag := range ticket.Tags {
		tags = append(tags, tag.TagID)
	}
	systemTags := make([]uint32, 0)
	for _, tag := range ticket.SystemTags {
		systemTags = append(systemTags, tag.LabelID)
	}
	return &TicketsDoc{
		TicketID:        ticket.TicketID,
		UUID:            ticket.UUID,
		AccountID:       ticket.AccountID,
		UID:             ticket.UID,
		Vip:             ticket.Vip,
		Recharge:        ticket.Recharge,
		VipCrm:          ticket.VipCrm,
		UserType:        ticket.UserType,
		SVIP:            ticket.SVIP,
		TroubleUserType: ticket.TroubleUser.UserType,
		Nickname:        ticket.Nickname,
		Project:         ticket.Project,
		GmID:            ticket.GmID,
		Channel:         ticket.Channel,
		SubChannel:      ticket.SubChannel,
		PackageID:       ticket.PackageID,
		Sid:             ticket.Sid,
		RoleID:          ticket.RoleID,
		RoleName:        ticket.Nickname,
		Lang:            ticket.Lang,
		Origin:          ticket.Origin,
		Scene:           ticket.Scene,
		CatID:           ticket.CatID,
		RelateType:      ticket.RelateType,
		TplID:           ticket.TplID,
		ProcessId:       ticket.ProcessID,
		ProcessSession:  ticket.ProcessSession,
		Priority:        ticket.Priority,
		Evaluation:      ticket.Evaluation,
		Creator:         ticket.Creator,
		Acceptor:        ticket.Acceptor,

		Refill: make([]uint32, 0),
		// Upgrade: make([]uint32, 0),
		// Audit:   make([]uint32, 0),
		// FCR:     true,

		Stage:            ticket.ConversionNode,
		Status:           ticket.Status,
		SolveType:        ticket.SolveType,
		FirstReplyAt:     ticket.FirstReplyAt,
		ResolveConfirmed: ticket.ResolveConfirmed,

		Field:       ticket.Fields.Field,
		DeviceType:  ticket.Device.DeviceType,
		Os:          ticket.Device.Os,
		OsVersion:   ticket.Device.OsVersion,
		AppVersion:  ticket.Device.AppVersion,
		SdkVersion:  ticket.Device.SdkVersion,
		RomGb:       ticket.Device.RomGb,
		RemainRom:   ticket.Device.RemainRom,
		RAMMb:       ticket.Device.RAMMb,
		NetworkInfo: ticket.Device.NetworkInfo,
		Country:     ticket.Device.Country,

		CsiTags:    make([]uint32, 0),
		Tags:       tags,
		SystemTags: systemTags,
		Replies:    make([]ReplyDoc, 0),
		Remarks:    make([]string, 0),
		Histories:  make([]HistoryDoc, 0),
		ReopenHist: make([]FpOpsTicketsReopen, 0),
		Commus:     make([]CommuDoc, 0),

		CreatedAt:       ticket.CreatedAt,
		UpdatedAt:       ticket.UpdatedAt,
		SortWaitStartAt: ticket.SortWaitStartAt,
		ZoneVipLevel:    ticket.ZoneVipLevel,
	}
}

type PendingTkDt struct {
	TicketId uint64 `json:"ticket_id"`
	Project  string `json:"project"`
}

type DailyCount struct {
	Date  string `gorm:"column:date"`  // 格式 "2006-01-02"
	Count int64  `gorm:"column:count"` // 当天工单数
}
