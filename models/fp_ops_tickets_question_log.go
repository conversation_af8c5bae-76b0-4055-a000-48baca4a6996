package models

// FpOpsTicketsQuestionLog 工单知识库变动日志
type FpOpsTicketsQuestionLog struct {
	ID              uint64 `gorm:"primaryKey;column:id;type:bigint(20) unsigned;not null"`
	OperationAction int8   `gorm:"index:gact_uni_id;column:operation_action;type;int(8);not null;default:0"`                // 具体操作action
	Project         string `gorm:"index:idx_prj;index:idx_prj_cat;column:project;type:varchar(64);not null;default:''"`     // 游戏
	UniqueID        string `gorm:"index:unique_id;index:gact_uni_id;column:unique_id;type:varchar(64);not null;default:''"` // 操作对应唯一id
	Lang            string `gorm:"column:lang;type:varchar(16);not null;default:''"`                                        // 语言
	BeforeDetail    string `gorm:"column:before_detail;type:text"`                                                          // operation 操作前的数据
	AfterDetail     string `gorm:"column:after_detail;type:text"`                                                           // operation 操作后的数据
	CreatedAt       uint64 `gorm:"column:created_at;type:bigint(20) unsigned;not null;default:0"`                           // 创建时间
	Operator        string `gorm:"column:operator;type:varchar(32);not null;default:''"`                                    // 操作人

}

func (m *FpOpsTicketsQuestionLog) TableName() string {
	return "fp_ops_tickets_question_log"
}
