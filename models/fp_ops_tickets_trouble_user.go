package models

import (
	"github.com/labstack/echo/v4"
	"time"
)

// FpOpsTicketsTroubleUser 问题用户信息表
type FpOpsTicketsTroubleUser struct {
	ID             uint64
	TicketID       uint64 `gorm:"primaryKey;index:idx_ticket_id;column:ticket_id;type:bigint(20) unsigned;not null"`
	AccountID      string `gorm:"column:account_id;type:varchar(128);not null;default:''"`       // fpx-account
	UID            uint64 `gorm:"column:uid;type:bigint(20) unsigned;not null;default:0"`        // uid
	VIP            uint16 `gorm:"column:vip;type:smallint(2) unsigned;not null;default:0"`       // vip等级
	VipCrm         int8   `gorm:"column:vip_crm;type:tinyint(1);not null;default:0"`             // crm vip
	VipCrmPriority int8   `gorm:"column:vip_crm_priority;type:tinyint(4);not null;default:0"`    // crm vip等级
	VipType        uint16 `gorm:"column:vip_type;type:smallint(2) unsigned;not null;default:0"`  // crm vip类型
	UserType       int8   `gorm:"column:user_type;type:smallint(2);not null;default:0"`          // 问题用户类型
	SVIP           int8   `gorm:"column:svip;type:tinyint(2);not null;default:0"`                // svip
	Recharge       uint64 `gorm:"column:recharge;type:bigint(20) unsigned;not null;default:0"`   // 充值金额
	Project        string `gorm:"column:project;type:varchar(64);not null;default:''"`           // 所属项目
	FpxAppID       string `gorm:"column:fpx_app_id;type:varchar(128);not null;default:''"`       // fpx app_id
	Channel        string `gorm:"column:channel;type:varchar(16);not null;default:''"`           // 渠道
	SubChannel     string `gorm:"column:sub_channel;type:varchar(16);not null;default:''"`       // 子渠道
	Operator       string `gorm:"column:operator;type:varchar(64);not null;default:''"`          // 创建人
	CreatedAt      uint64 `gorm:"column:created_at;type:bigint(20) unsigned;not null;default:0"` // 创建时间
	UpdatedAt      uint64 `gorm:"column:updated_at;type:bigint(20) unsigned;not null;default:0"` // 更新时间
}

func TroubleUser(ctx echo.Context, param *TicketCreateParam, tkObj *FpOpsTickets) *FpOpsTicketsTroubleUser {
	tkObj.TroubleUser.VipCrm = param.TroubleUser.VipCrm
	tkObj.TroubleUser.VipCrmPriority = param.TroubleUser.VipCrmPriority
	tkObj.TroubleUser.VipType = param.TroubleUser.VipType
	tkObj.TroubleUser.UserType = param.TroubleUser.UserType
	tkObj.TroubleUser.SVIP = param.TroubleUser.SVIP
	tkObj.TroubleUser.Channel = param.TroubleUser.Channel
	tkObj.TroubleUser.Project = tkObj.Project
	tkObj.TroubleUser.FpxAppID = tkObj.GmID
	if param.Input.TroubleUid > 0 {
		tkObj.TroubleUser.AccountID = param.Input.TroubleAccountId
		tkObj.TroubleUser.UID = param.Input.TroubleUid
		tkObj.TroubleUser.Operator = tkObj.Creator
		tkObj.TroubleUser.CreatedAt = uint64(time.Now().Unix())
		tkObj.TroubleUser.UpdatedAt = uint64(time.Now().Unix())
	} else {
		tkObj.TroubleUser.AccountID = tkObj.AccountID
		tkObj.TroubleUser.UID = tkObj.UID
		tkObj.TroubleUser.VIP = tkObj.Vip
		tkObj.TroubleUser.Channel = tkObj.Channel
	}
	return tkObj.TroubleUser
}
