package models

import (
	"context"
	"ops-ticket-api/internal/code"
	"ops-ticket-api/proto/pb"
	"time"

	jsoniter "github.com/json-iterator/go"
	"github.com/spf13/cast"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
)

// FpOpsCategory 问题分类配置表
type FpOpsCategory struct {
	CatID       uint32 `gorm:"primaryKey;column:cat_id;type:int(11) unsigned;not null"`
	Project     string `gorm:"index:idx_prj_cat;column:project;type:varchar(64);not null;default:''"`             // 所属项目
	Scope       uint32 `gorm:"column:scope;type:tinyint(1) unsigned;not null;default:0"`                          // 范围 0:默认 1:内部分类
	Category    string `gorm:"index:idx_prj_cat;column:category;type:varchar(64);not null;default:''"`            // 分类名称
	ProcessID   uint32 `gorm:"column:process_id;type:bigint(20) unsigned;not null;default:0"`                     // 流程id
	OneLevel    uint32 `gorm:"index:idx_level;column:one_level;type:int(11) unsigned;not null;default:0"`         // 一级分类
	SecondLevel uint32 `gorm:"index:idx_level;column:second_level;type:int(11);not null;default:0"`               // 二级分类
	Level       uint32 `gorm:"index:idx_level;index:idx_prj_cat;column:level;type:tinyint(1);not null;default:1"` // 分类层级
	RelateType  uint32 `gorm:"column:relate_type;type:tinyint(1);not null;default:1"`                             // 关联类型,1:表单 2:流程
	Deadline    uint64 `gorm:"column:deadline;type:bigint(20);not null;default:0"`                                // 完成时间
	TplID       uint32 `gorm:"index:idx_tpl_id;column:tpl_id;type:int(11) unsigned;not null;default:0"`           // 模版ID
	ReplyTplID  uint32 `gorm:"index:idx_reply_tpl;column:reply_tpl_id;type:int(11) unsigned;not null;default:0"`  // 回复模版ID
	// Deprecated: swift回复模版ID 请使用SwiftReplyTplList 逻辑替代
	//SwiftReplyTplID   uint32               `gorm:"column:swift_reply_tpl_id;type:int(11) unsigned;not null;default:0"`              // swift回复模版ID
	GroupID           uint32               `gorm:"column:group_id;type:int(11) unsigned;not null;default:0"`                        // 技能组ID
	OvertimeReplyID   uint64               `gorm:"column:overtime_reply_id;type:bigint(20);not null;default:0" `                    // 超时回复模版ID
	Enable            bool                 `gorm:"column:enable;type:tinyint(1);not null;default:1"`                                // 启用 0:false 1:true
	Operator          string               `gorm:"column:operator;type:varchar(64);not null;default:''"`                            // op
	TplList           string               `gorm:"column:tpl_list;type:text;not null"`                                              // 备注
	SwiftReplyTplList string               `gorm:"column:swift_reply_tpl_list;type:text;not null"`                                  // swift回复模版列表
	IsDeleted         bool                 `gorm:"index:idx_prj_cat;column:is_deleted;type:tinyint(1) unsigned;not null;default:0"` // 删除
	CreatedAt         time.Time            `gorm:"column:created_at;type:datetime;default:null"`                                    // 创建时间
	UpdatedAt         time.Time            `gorm:"column:updated_at;type:datetime;default:null"`                                    // 更新时间
	Language          []*FpOpsCategoryLang `gorm:"foreignKey:CatID;references:CatID" json:"language,omitempty"`
}

func (m *FpOpsCategory) GetSwiftReplyTplListDetail(ctx context.Context) []*pb.TkCatAutoReplyTplPrefer {
	var detail = make([]*pb.TkCatAutoReplyTplPrefer, 0)
	if m.SwiftReplyTplList == "" || m.SwiftReplyTplList == "[]" {
		return detail
	}
	if _err := jsoniter.UnmarshalFromString(m.SwiftReplyTplList, &detail); _err != nil {
		logger.Info(ctx, "GetSwiftReplyTplListDetail. json unmarshal error", logger.Any("err", _err.Error()), logger.String("swift_reply_tpl_list", m.SwiftReplyTplList))
	}
	return detail
}

// GetSwiftReplyTplID 根据条件，获取当前满足条件的swift回复模板ID，不满足 返回 返回 0
func (m *FpOpsCategory) GetSwiftReplyTplID(ctx context.Context, param *pb.TkCatAutoReplyParamDf) uint32 {
	replyTplList := m.GetSwiftReplyTplListDetail(ctx)
	for _, tpl := range replyTplList {
		if tpl.SwiftReplyTplId == 0 {
			continue
		}
		if tpl.ReplyFlagForFilter == false { // 全部 自动回复
			return tpl.SwiftReplyTplId
		}
		// 部分条件过滤
		if cast.ToUint64(tpl.TotalPay*code.RechargeRate) >= param.TotalPay { // 小于等于 充值金额 则自动回复
			return tpl.SwiftReplyTplId
		}
	}
	return 0
}

func (obj *FpOpsCategory) TableName() string {
	return "fp_ops_category"
}

// FpOpsCategoryLang 问题分类配置多语言表
type FpOpsCategoryLang struct {
	ID        uint32    `gorm:"primaryKey;column:id;type:int(11) unsigned;not null"`
	CatID     uint32    `gorm:"uniqueIndex:idx_cat_lang;column:cat_id;type:int(11) unsigned;not null;default:0"` // 类别ID
	Lang      string    `gorm:"uniqueIndex:idx_cat_lang;column:lang;type:varchar(8);not null;default:''"`        // 语言
	Category  string    `gorm:"column:category;type:varchar(64);not null;default:''"`                            // 分类名称
	CreatedAt time.Time `gorm:"column:created_at;type:datetime;default:null"`                                    // 创建时间
	UpdatedAt time.Time `gorm:"column:updated_at;type:datetime;default:null"`                                    // 更新时间
}

func (obj *FpOpsCategoryLang) TableName() string {
	return "fp_ops_category_lang"
}

// FpOpsCategoryMap 问题分类配置多语言表
type FpOpsCategoryMap struct {
	ID        uint32    `gorm:"primaryKey;column:id;type:int(11) unsigned;not null"`
	CatID     uint32    `gorm:"uniqueIndex:idx_cat_obj;column:cat_id;type:int(11) unsigned;not null;default:0"`                  // 类别ID
	ObjectID  uint32    `gorm:"uniqueIndex:idx_cat_obj;index:idx_obj;column:object_id;type:int(11) unsigned;not null;default:0"` // 对象ID
	From      uint8     `gorm:"uniqueIndex:idx_cat_obj;index:idx_obj;column:from;type:tinyint(4) unsigned;not null;default:0"`   // 业务id
	CreatedAt time.Time `gorm:"column:created_at;type:datetime;default:null"`                                                    // 创建时间
	UpdatedAt time.Time `gorm:"column:updated_at;type:datetime;default:null"`                                                    // 更新时间                                         // 更新时间
}

func (obj *FpOpsCategoryMap) TableName() string {
	return "fp_ops_category_map"
}

// FpOpsTpl 模板配置表
type FpOpsTpl struct {
	ID        uint32          `gorm:"primaryKey;column:id;type:int(11) unsigned;not null"`
	Project   string          `gorm:"uniqueIndex:idx_prj_tpl;column:project;type:varchar(64);not null;default:''"` // 所属项目
	Tpl       string          `gorm:"uniqueIndex:idx_prj_tpl;column:tpl;type:varchar(32);not null"`                // 模版名称
	Fields    string          `gorm:"column:fields;type:text;not null"`                                            // 模版表单
	Enable    bool            `gorm:"column:enable;type:tinyint(1) unsigned;not null;default:1"`                   // 激活. 0:false 1:true
	Operator  string          `gorm:"column:operator;type:varchar(64);not null;default:''"`                        // 更新用户
	CreatedAt time.Time       `gorm:"column:created_at;type:datetime"`                                             // 创建时间
	UpdatedAt time.Time       `gorm:"column:updated_at;type:datetime"`                                             // 更新时间
	Language  []*FpOpsTplLang `gorm:"foreignKey:TplID;references:ID"`
}

// FpOpsTplLang 模板配置多语言表
type FpOpsTplLang struct {
	ID        uint32    `gorm:"primaryKey;column:id;type:int(11) unsigned;not null"`
	TplID     uint32    `gorm:"uniqueIndex:idx_tpl;column:tpl_id;type:int(1) unsigned;not null;default:0"` // 模版ID
	Project   string    `gorm:"uniqueIndex:idx_tpl;column:project;type:varchar(64);not null;default:''"`   // 所属项目
	Lang      string    `gorm:"uniqueIndex:idx_tpl;column:lang;type:varchar(32);not null;default:''"`      // 语言
	Tpl       string    `gorm:"column:tpl;type:varchar(32);not null"`                                      // 模版名称
	Fields    string    `gorm:"column:fields;type:text;not null"`                                          // 模版表单
	Enable    bool      `gorm:"column:enable;type:tinyint(1) unsigned;not null;default:1"`                 // 激活. 0:false 1:true
	Operator  string    `gorm:"column:operator;type:varchar(64);not null;default:''"`                      // 更新用户
	CreatedAt time.Time `gorm:"column:created_at;type:datetime;default:null"`                              // 创建时间
	UpdatedAt time.Time `gorm:"column:updated_at;type:datetime;default:null"`                              // 更新时间
}
