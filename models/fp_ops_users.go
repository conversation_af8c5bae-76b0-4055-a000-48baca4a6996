package models

import "time"

// FpOpsUsers 用户表
type FpOpsUsers struct {
	ID            uint32    `gorm:"primaryKey;column:id;type:int(11) unsigned;not null"`
	Empno         uint32    `gorm:"column:empno;type:int(11) unsigned;not null;default:0"`      // 工号
	Account       string    `gorm:"unique;column:account;type:varchar(64);not null;default:''"` // 用户账号
	Nickname      string    `gorm:"column:nickname;type:varchar(64);not null;default:''"`       // 用户名称
	IsLogin       uint32    `gorm:"column:is_login;type:tinyint;default:2;comment:'是否在线：2:离线, 1:在线'"`
	LastLoginAt   int64     `gorm:"column:last_login_at;type:int(11) unsigned;not null;default:0"` // 登录时间
	LastAllocTkAt uint64    `gorm:"column:last_alloc_tk_at;type:bigint(20);not null;default:0"`    // 最后分单时间
	CreatedAt     time.Time `gorm:"column:created_at;type:datetime;not null;default:CURRENT_TIMESTAMP"`
	UpdatedAt     time.Time `gorm:"column:updated_at;type:datetime;default:null"`
}

type FpOpsUserData struct {
	FpOpsUserAssignTicket
	IsLogin       uint32 `gorm:"column:is_login;type:tinyint;default:2;comment:'是否在线：2:离线, 1:在线'"`
	LastAllocTkAt uint64 `gorm:"column:last_alloc_tk_at;type:bigint(20);not null;default:0"` // 最后分单时间
}
