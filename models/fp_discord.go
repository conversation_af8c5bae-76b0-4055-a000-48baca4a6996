package models

import (
	"context"
	"ops-ticket-api/internal/code"
	"ops-ticket-api/proto/pb"
	"time"

	"github.com/bwmarrin/discordgo"
	jsoniter "github.com/json-iterator/go"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"gorm.io/datatypes"
)

// FpDsNotice discord 服务端通知消息
type FpDsNotice struct {
	ID           uint64    `gorm:"primaryKey;column:id;type:bigint(20) unsigned;not null"`
	AppID        string    `gorm:"index:idx_app_id;column:app_id;type:varchar(128);not null;default:''"`                       // 所属应用
	Handle       string    `gorm:"index:idx_app_id;column:handle;type:varchar(64);not null;default:''"`                        // 请求接口
	HeaderDetail string    `gorm:"column:header_detail;type:varchar(2048);not null;default:''"`                                // header信息
	BodyDetail   string    `gorm:"column:body_detail;type:mediumtext;default:null"`                                            // body详情
	CreatedAt    time.Time `gorm:"index:idx_create_at;column:created_at;type:datetime;default:null;default:CURRENT_TIMESTAMP"` // 创建时间
}

// FpDscEventNotice discord gateway Event 数据
type FpDscEventNotice struct {
	ID        uint64         `gorm:"primaryKey;column:id;type:bigint(20) unsigned;not null"`
	AppID     string         `gorm:"index:idx_app_id;column:app_id;type:varchar(32);not null;default:''"`                        // 所属应用
	MGroup    string         `gorm:"index:idx_app_id;column:m_group;type:varchar(16);not null;default:''"`                       // 业务日志分组: AllEvent:全 event数据
	T         string         `gorm:"column:t;type:varchar(48);not null;default:''"`                                              // 对应 Type 分类
	Log       datatypes.JSON `gorm:"column:log;type:json;default:null"`                                                          // 详情 log
	Extra     string         `gorm:"column:extra;type:varchar(512);not null;default:''"`                                         // 其他信息
	CreatedAt time.Time      `gorm:"index:idx_create_at;column:created_at;type:datetime;default:null;default:CURRENT_TIMESTAMP"` // 创建时间
}

// FpDscBot discord bot 机器人信息
type FpDscBot struct {
	ID            uint64    `gorm:"primaryKey;column:id;type:bigint(20) unsigned;not null"`
	Project       string    `gorm:"column:project;type:varchar(32);not null;default:''"`                                        // 所属项目
	DscName       string    `gorm:"column:dsc_name;type:varchar(32);not null;default:''"`                                       // discord 账号名称
	BotDesc       string    `gorm:"column:bot_desc;type:varchar(32);not null;default:''"`                                       // 机器人描述
	AppID         string    `gorm:"index:idx_app_id;column:app_id;type:varchar(128);not null;default:''"`                       // 所属应用
	BotConfig     string    `gorm:"column:bot_config;type:varchar(128);not null;default:{}"`                                    // 机器人配置信息
	IsDelete      bool      `gorm:"column:is_delete;type:tinyint(1);not null;default:0"`                                        // 是否删除: 0:正常使用; 1:已删除
	UserID        string    `gorm:"column:user_id;type:varchar(32);not null;default:''"`                                        // 机器人对应的user-id
	Username      string    `gorm:"column:username;type:varchar(32);not null;default:''"`                                       // discord.user.username - 用户名称
	Discriminator string    `gorm:"column:discriminator;type:varchar(16);not null;default:''"`                                  // discord.user.discriminator - 标识
	CreatedAt     time.Time `gorm:"index:idx_create_at;column:created_at;type:datetime;default:null;default:CURRENT_TIMESTAMP"` // 创建时间
	UpdatedAt     time.Time `gorm:"column:updated_at;type:datetime;default:null;default:CURRENT_TIMESTAMP"`                     // 更新时间
	UpdatedBy     string    `gorm:"column:updated_by;type:varchar(32);not null;default:''"`                                     // 更新人
}

func (m FpDscBot) DecodeBotConfig() *DscBotConfigDf {
	var c = DscBotConfigDf{}
	err := jsoniter.UnmarshalFromString(m.BotConfig, &c)
	if err != nil {
		logger.Errorf(context.TODO(), "DecodeBotConfig err. detail:%+v. err:%v", m, err)
	}
	c.BotVersion = m.UpdatedAt
	return &c
}

// DscBotConfigDf  FpDscBot的bot 详细配置信息
type DscBotConfigDf struct {
	ClientId       string    `json:"client_id"`
	PublicKey      string    `json:"public_key"`
	BotToken       string    `json:"bot_token"`
	GuildId        string    `json:"guild_id"`
	GuildDesc      string    `json:"guild_desc"`
	Project        string    `json:"project"`
	WelcomeMessage string    `json:"welcome_message"`
	BotVersion     time.Time `json:"-"`
}

// FpDscUser discord私信用户维护列表
type FpDscUser struct {
	ID            uint64    `gorm:"primaryKey;column:id;type:bigint(20) unsigned;not null"`
	Project       string    `gorm:"column:project;type:varchar(32);not null;default:''"`                                        // 所属项目
	AppID         string    `gorm:"uniqueIndex:idx_uid_botid;column:app_id;type:varchar(32);not null;default:''"`               // 所属应用/机器人bot的 user_id
	DscUserID     string    `gorm:"uniqueIndex:idx_uid_botid;column:dsc_user_id;type:varchar(32);not null;default:''"`          // 用户 discord_user_id
	PrivChannelID string    `gorm:"index:idx_prv_chan;column:priv_channel_id;type:varchar(32);not null;default:''"`             // 用户私聊 channel id
	UserName      string    `gorm:"column:user_name;type:varchar(64);not null;default:''"`                                      // discord 用户名 非唯一
	GlobalName    string    `gorm:"column:global_name;type:varchar(64);not null;default:''"`                                    //  the user display name, if it is set. For bots, this is the application name
	Discriminator string    `gorm:"column:discriminator;type:varchar(16);not null;default:''"`                                  // discord.user.discriminator - 标识
	GuildID       string    `gorm:"column:guild_id;type:varchar(32);not null;default:''"`                                       // discord 加入 guild 标识
	JoinedAt      time.Time `gorm:"column:joined_at;type:datetime;default:null;default:CURRENT_TIMESTAMP"`                      // discord:加入guild时间
	IsDeleted     uint8     `gorm:"column:is_deleted;type:tinyint(1) unsigned;not null;default:1" json:"is_deleted"`            // is_deleted 标记是否已删除，1默认未删除 2已删除
	CreatedAt     time.Time `gorm:"index:idx_create_at;column:created_at;type:datetime;default:null;default:CURRENT_TIMESTAMP"` // 创建时间
	UpdatedAt     time.Time `gorm:"column:updated_at;type:datetime;default:null;default:CURRENT_TIMESTAMP"`                     // 更新时间
}

func (m FpDscUser) TableName() string {
	return "fp_dsc_user"
}

// FpDscDmChannel discord channel列表
type FpDscDmChannel struct {
	ID        uint64    `gorm:"primaryKey;column:id;type:bigint(20) unsigned;not null"`
	Project   string    `gorm:"column:project;type:varchar(32);not null;default:''"`                                        // 所属项目
	ChannelID string    `gorm:"unique;column:channel_id;type:varchar(32);not null;default:''"`                              // 用户私聊 channel id
	BotID     string    `gorm:"index:idx_bot;column:bot_id;type:varchar(32);not null;default:''"`                           // 所属应用/机器人bot的 user_id
	DscUserID string    `gorm:"index:idx_user;column:dsc_user_id;type:varchar(32);not null;default:''"`                     // 玩家用户discord user_id
	CreatedAt time.Time `gorm:"index:idx_create_at;column:created_at;type:datetime;default:null;default:CURRENT_TIMESTAMP"` // 创建时间
	EditedAt  time.Time `gorm:"column:edited_at;type:datetime;default:null;default:CURRENT_TIMESTAMP"`                      // 修改时间
	UpdatedAt time.Time `gorm:"column:updated_at;type:datetime;default:null;default:CURRENT_TIMESTAMP"`                     // 更新时间
}

// FpDscDmCommu discord 消息列表
type FpDscDmCommu struct {
	ID              uint64                  `gorm:"primaryKey;column:id;type:bigint(20) unsigned;not null"`
	MsgID           string                  `gorm:"unique;column:msg_id;type:varchar(32);not null;default:''"`                                  // 消息id
	Project         string                  `gorm:"column:project;type:varchar(32);not null;default:''"`                                        // 所属项目
	FromUserID      string                  `gorm:"index:idx_frm_uid;column:from_user_id;type:varchar(32);not null;default:''"`                 // 消息来源id：user端:user_id, 客服回复:bot_id
	BotID           string                  `gorm:"column:bot_id;type:varchar(32);not null;default:''"`                                         // 所属应用/机器人bot的 user_id
	DscUserID       string                  `gorm:"column:dsc_user_id;type:varchar(32);not null;default:''"`                                    // 用户 discord_user_id
	ChannelID       string                  `gorm:"index:idx_channel;column:channel_id;type:varchar(32);not null;default:''"`                   // 用户私聊 channel id
	Content         string                  `gorm:"column:content;type:text;not null"`                                                          // 消息内容
	Attachments     string                  `gorm:"column:attachments;type:text;not null"`                                                      // 附件
	Embeds          string                  `gorm:"column:embeds;type:text;default:null"`                                                       //
	Poll            string                  `gorm:"column:poll;type:text;not null"`                                                             // poll 投票信息详情
	Stickers        string                  `gorm:"column:stickers;type:varchar(1024);not null;default:''"`                                     // 贴纸信息
	Edited          bool                    `gorm:"column:edited;type:tinyint(1);not null;default:0"`                                           // 是否已修改: 0:未修改; 1:已修改
	Author          string                  `gorm:"column:author;type:text;default:null"`                                                       // 发送信息
	ReferencedMsgID string                  `gorm:"column:referenced_msg_id;type:varchar(32);not null;default:''"`                              // 回复消息 msg_id
	CreatedAt       time.Time               `gorm:"index:idx_create_at;column:created_at;type:datetime;default:null;default:CURRENT_TIMESTAMP"` // 创建时间
	EditedAt        time.Time               `gorm:"column:edited_at;type:datetime;default:null;default:CURRENT_TIMESTAMP"`                      // 修改时间
	UpdatedAt       time.Time               `gorm:"column:updated_at;type:datetime;default:null;default:CURRENT_TIMESTAMP"`                     // 更新时间
	Reactions       []*FpDscDmCommuReaction `gorm:"foreignKey:MsgID;references:MsgID" json:"reactions,omitempty"`                               // 新标签
}

func (m FpDscDmCommu) TableName() string {
	return "fp_dsc_dm_commu"
}
func (m *FpDscDmCommu) DecodeAuthor() *pb.DscAuthor {
	var author pb.DscAuthor
	if m.Author != "" && m.Author != "{}" && m.Author != "[]" && m.Author != "null" {
		if err := jsoniter.UnmarshalFromString(m.Author, &author); err != nil {
			logger.Errorf(context.TODO(), "DecodeAuthor err. detail:%+v. err:%v", m, err)
		}
	}
	if author.GlobalName != "" {
		author.Username = author.GlobalName
	}
	return &author
}
func (m *FpDscDmCommu) DecodeAttach() []*pb.DscMsgAttach {
	var attach = make([]*pb.DscMsgAttach, 0)
	if m.Attachments != "" && m.Attachments != "{}" && m.Attachments != "[]" && m.Attachments != "null" {
		if err := jsoniter.UnmarshalFromString(m.Attachments, &attach); err != nil {
			logger.Errorf(context.TODO(), "DecodeAttach err. detail:%+v. err:%v", m, err)
		}
	}
	return attach
}
func (m *FpDscDmCommu) DecodeEmbed() []*pb.DscMsgEmbed {
	var embed = make([]*pb.DscMsgEmbed, 0)
	if m.Embeds != "" && m.Embeds != "{}" && m.Embeds != "[]" && m.Embeds != "null" {
		if err := jsoniter.UnmarshalFromString(m.Embeds, &embed); err != nil {
			logger.Errorf(context.TODO(), "DecodeEmbed err. detail:%+v. err:%v", m, err)
		}
	}
	return embed
}
func (m *FpDscDmCommu) DecodeSticker() []*pb.DscMsgSticker {
	var sticker = make([]*pb.DscMsgSticker, 0)
	if m.Stickers != "" && m.Stickers != "{}" && m.Stickers != "[]" && m.Stickers != "null" {
		if err := jsoniter.UnmarshalFromString(m.Stickers, &sticker); err != nil {
			logger.Errorf(context.TODO(), "DecodeSticker err. detail:%+v. err:%v", m, err)
		}
	}
	return sticker
}
func (m *FpDscDmCommu) DecodePoll() *pb.DscMsgPoll {
	var poll = &pb.DscMsgPoll{}
	if m.Poll != "" && m.Poll != "{}" && m.Poll != "[]" && m.Poll != "null" {
		var dspoll = &discordgo.Poll{}
		if err := jsoniter.UnmarshalFromString(m.Poll, dspoll); err != nil {
			logger.Errorf(context.TODO(), "DecodePoll err. detail:%+v. err:%v", m, err)
		}

		poll.QuestionText = dspoll.Question.Text
		poll.AllowMultiselect = dspoll.AllowMultiselect
		poll.Duration = int32(dspoll.Duration)
		if dspoll.Expiry != nil && !dspoll.Expiry.IsZero() {
			poll.Expiry = uint64(dspoll.Expiry.Local().Unix())
		}
		for _, an := range dspoll.Answers {
			if an.Media == nil {
				an.Media = &discordgo.PollMedia{}
			}
			poll.Answers = append(poll.Answers, &pb.DscMsgPollAnswer{
				AnswerId:   uint32(an.AnswerID),
				AnswerText: an.Media.Text,
			})
		}
		if dspoll.Results != nil {
			poll.Results = &pb.DscMsgPollResult{
				IsFinalized: dspoll.Results.Finalized,
			}
			for _, cot := range dspoll.Results.AnswerCounts {
				poll.Results.AnswerCount = append(poll.Results.AnswerCount, &pb.DscMsgPollAnswerCount{
					Id:      uint32(cot.ID),
					Count:   uint32(cot.Count),
					MeVoted: cot.MeVoted,
				})
			}
		}
	}
	return poll
}

// FpDscDmCommuDel discord 消息列表
type FpDscDmCommuDel struct {
	ID              uint64    `gorm:"primaryKey;column:id;type:bigint(20) unsigned;not null"`
	MsgID           string    `gorm:"unique;column:msg_id;type:varchar(32);not null;default:''"`                  // 消息id
	Project         string    `gorm:"column:project;type:varchar(32);not null;default:''"`                        // 所属项目
	FromUserID      string    `gorm:"index:idx_frm_uid;column:from_user_id;type:varchar(32);not null;default:''"` // 消息来源id：user端:user_id, 客服回复:bot_id
	BotID           string    `gorm:"column:bot_id;type:varchar(32);not null;default:''"`                         // 所属应用/机器人bot的 user_id
	DscUserID       string    `gorm:"column:dsc_user_id;type:varchar(32);not null;default:''"`                    // 用户 discord_user_id
	ChannelID       string    `gorm:"index:idx_channel;column:channel_id;type:varchar(32);not null;default:''"`   // 用户私聊 channel id
	Content         string    `gorm:"column:content;type:text;not null"`                                          // 消息内容
	Attachments     string    `gorm:"column:attachments;type:text;not null"`                                      // 附件
	Embeds          string    `gorm:"column:embeds;type:text;default:null"`
	Poll            string    `gorm:"column:poll;type:text;not null"`                                                             // poll 投票信息详情
	Stickers        string    `gorm:"column:stickers;type:varchar(1024);not null;default:''"`                                     // 贴纸信息
	Edited          bool      `gorm:"column:edited;type:tinyint(1);not null;default:0"`                                           // 是否已修改: 0:未修改; 1:已修改
	Author          string    `gorm:"column:author;type:text;default:null"`                                                       // 发送信息
	ReferencedMsgID string    `gorm:"column:referenced_msg_id;type:varchar(32);not null;default:''"`                              // 回复消息 msg_id
	CreatedAt       time.Time `gorm:"index:idx_create_at;column:created_at;type:datetime;default:null;default:CURRENT_TIMESTAMP"` // 创建时间
	EditedAt        time.Time `gorm:"column:edited_at;type:datetime;default:null;default:CURRENT_TIMESTAMP"`                      // 修改时间
	UpdatedAt       time.Time `gorm:"column:updated_at;type:datetime;default:null;default:CURRENT_TIMESTAMP"`                     // 更新时间
}

func (m FpDscDmCommuDel) TableName() string {
	return "fp_dsc_dm_commu_del"
}

// FpDscDmCommuReaction discord 消息响应
type FpDscDmCommuReaction struct {
	ID           uint64    `gorm:"primaryKey;column:id;type:bigint(20) unsigned;not null"`
	MsgID        string    `gorm:"index:idx_msgid;column:msg_id;type:varchar(32);not null;default:''"`                         // 消息id
	FromUserID   string    `gorm:"index:idx_frm_uid;column:from_user_id;type:varchar(32);not null;default:''"`                 // 消息来源id：user端:user_id, 客服回复:bot_id
	ChannelID    string    `gorm:"index:idx_channel;column:channel_id;type:varchar(32);not null;default:''"`                   // 用户私聊 channel id
	EmojiNameMd5 string    `gorm:"column:emoji_name_md5;type:varchar(32);not null;default:''"`                                 // 表情-md5值
	EmojiDetail  string    `gorm:"column:emoji_detail;type:varchar(512);not null;default:''"`                                  // 响应详情
	CreatedAt    time.Time `gorm:"index:idx_create_at;column:created_at;type:datetime;default:null;default:CURRENT_TIMESTAMP"` // 创建时间
	UpdatedAt    time.Time `gorm:"column:updated_at;type:datetime;default:null;default:CURRENT_TIMESTAMP"`                     // 更新时间
}

func (m *FpDscDmCommuReaction) DecodeEmoji() *discordgo.Emoji {
	var emoji = &discordgo.Emoji{}
	if m.EmojiDetail != "" && m.EmojiDetail != "{}" && m.EmojiDetail != "[]" {
		if err := jsoniter.UnmarshalFromString(m.EmojiDetail, emoji); err != nil {
			logger.Errorf(context.TODO(), "DecodeEmoji err. detail:%+v. err:%v", m, err)
		}
	}
	return emoji
}

// FpDscDmUnread discord channel unread msg
type FpDscDmUnread struct {
	ID        uint64    `gorm:"primaryKey;column:id;type:bigint(20) unsigned;not null"`
	ChannelID string    `gorm:"index:idx_channel;column:channel_id;type:varchar(32);not null;default:''"`                   // 渠道id
	UserID    string    `gorm:"column:user_id;type:varchar(32);not null;default:''"`                                        // discord user_id
	UniqType  string    `gorm:"column:uniq_type;type:varchar(32);not null;default:''"`                                      // 动作分类
	Log       []byte    `gorm:"column:log;type:blob;default:null"`                                                          // 相信信息
	CreatedAt time.Time `gorm:"index:idx_create_at;column:created_at;type:datetime;default:null;default:CURRENT_TIMESTAMP"` // 创建时间
}

func DscUserAssignDoc(dscUser *FpDscUser) *FpDscUserDoc {
	return &FpDscUserDoc{
		ID:             dscUser.ID,
		DscUserID:      dscUser.DscUserID,
		PrivChannelID:  dscUser.PrivChannelID,
		AppID:          dscUser.AppID,
		GuildID:        dscUser.GuildID,
		Project:        dscUser.Project,
		GlobalName:     dscUser.GlobalName,
		UserName:       dscUser.UserName,
		DscCommu:       make([]*DscCommuDoc, 0),
		PortraitRemark: "",
		Maintainer:     "",
		VipState:       code.NonVip,
		ReplyType:      uint8(pb.DscReplyTpDf_DscReplyTpDfReplied),
		IsDeleted:      dscUser.IsDeleted,
		CreatedAt:      uint64(dscUser.CreatedAt.Unix()),
		UpdatedAt:      uint64(dscUser.UpdatedAt.Unix()),
	}
}

// FpDscDmSend 客服回复消息绑定
type FpDscDmSend struct {
	ID          uint64    `gorm:"primaryKey;column:id;type:bigint(20) unsigned;not null"`
	MsgID       string    `gorm:"unique;column:msg_id;type:varchar(32);not null;default:''"`                                  // 消息id
	BotID       string    `gorm:"column:bot_id;type:varchar(32);not null;default:''"`                                         // 所属应用/机器人bot的 user_id
	ChannelID   string    `gorm:"index:idx_channel;column:channel_id;type:varchar(32);not null;default:''"`                   // 用户私聊 channel id
	Operator    string    `gorm:"column:operator;type:varchar(64);not null;default:''"`                                       // 操作人
	IgnoreState int32     `gorm:"column:ignore_state;type:int(10);not null;default:0"`                                        // 是否强制忽略与玩家沟通状态 1:强制忽略
	CreatedAt   time.Time `gorm:"index:idx_create_at;column:created_at;type:datetime;default:null;default:CURRENT_TIMESTAMP"` // 创建时间
	UpdatedAt   time.Time `gorm:"column:updated_at;type:datetime;default:null;default:CURRENT_TIMESTAMP"`                     // 更新时间
}

// FpDscReplyTime discord 回复时间表
type FpDscReplyTime struct {
	ID               uint64    `gorm:"primaryKey;autoIncrement;column:id"`
	Project          string    `gorm:"column:project;type:varchar(32);not null;default:''"`                                      // 所属项目
	BotID            string    `gorm:"column:bot_id;type:varchar(32);not null;default:''"`                                       // 所属应用/机器人bot的 user_id
	DscUserID        string    `gorm:"column:dsc_user_id;type:varchar(32);not null;default:''"`                                  // 用户 discord_user_id
	Operator         string    `gorm:"column:operator;type:varchar(64);not null;default:''"`                                     // 操作人
	ChannelID        string    `gorm:"column:channel_id;type:varchar(32);not null;default:''"`                                   // 用户私聊 channel id
	ReplyAt          time.Time `gorm:"index:idx_reply_at;column:reply_at;type:datetime;not null"`                                // 客服回复时间
	ReplyTimeSeconds uint      `gorm:"column:reply_time_seconds;type:int unsigned;not null;default:0"`                           // 回复时间（秒）
	Day              string    `gorm:"index:idx_day;column:day;type:varchar(32);collate:utf8mb4_general_ci;not null;default:''"` // 回复日期
	CreatedAt        time.Time `gorm:"column:created_at;type:datetime;default:CURRENT_TIMESTAMP;comment:'创建时间'"`                 // 创建时间
}

// TableName 返回表名
func (m FpDscReplyTime) TableName() string {
	return "fp_dsc_reply_time"
}

// ReplyStats 用于存储按时间段分组的回复统计信息
type ReplyStats struct {
	RowName    string `json:"row_name"`    // 时间段
	ReplyCount int64  `json:"reply_count"` // 回复计数
}

// ReplyTime 查询每天回复时间
type ReplyTime struct {
	Date             string `json:"date"`
	ReplyTimeSeconds uint   `json:"reply_time_seconds"`
}
