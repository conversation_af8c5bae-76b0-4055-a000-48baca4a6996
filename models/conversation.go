package models

import (
	"time"
)

const ConversationStatusOn = 1
const ConversationStatusClose = 2

// Conversation 对话会话表
type Conversation struct {
	ID               int64     `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	GameProject      string    `gorm:"column:game_project;type:varchar(64);not null" json:"game_project"`
	UserID           string    `gorm:"column:user_id;type:varchar(64);not null" json:"user_id"`
	AccountID        string    `gorm:"column:account_id;type:varchar(64);not null" json:"account_id"`
	CatID            int32     `gorm:"column:cat_id;type:int;not null;default:0" json:"cat_id"`
	TicketID         int64     `gorm:"column:ticket_id;type:bigint;not null;default:0" json:"ticket_id"`
	ConversationID   string    `gorm:"column:conversation_id;type:varchar(64);not null" json:"conversation_id"`
	Status           int32     `gorm:"column:status;type:tinyint;not null;default:1" json:"status"` // 1-进行中，2-已完成
	QuestionProgress string    `gorm:"column:question_progress;type:text;not null" json:"question_progress"`
	CreatedAt        time.Time `gorm:"column:created_at;type:timestamp;default:CURRENT_TIMESTAMP" json:"created_at"`
	UpdatedAt        time.Time `gorm:"column:updated_at;type:timestamp;default:CURRENT_TIMESTAMP" json:"updated_at"`
}

// TableName 表名
func (c *Conversation) TableName() string {
	return "cs_conversation"
}

// ConversationHistory 对话历史记录表
type ConversationHistory struct {
	ID             int64     `gorm:"column:id;primaryKey;autoIncrement" json:"id"`
	ConversationID string    `gorm:"column:conversation_id;type:varchar(64);not null" json:"conversation_id"`
	HistoryID      string    `gorm:"column:history_id;type:varchar(64);not null" json:"history_id"`
	QuestionKey    string    `gorm:"column:question_key;type:varchar(20);not null" json:"question_key"`
	Role           string    `gorm:"column:role;type:varchar(20);not null" json:"role"`
	Content        string    `gorm:"column:content;type:text;not null" json:"content"`
	CreatedAt      time.Time `gorm:"column:created_at;type:timestamp;default:CURRENT_TIMESTAMP" json:"created_at"`
}

// TableName 表名
func (h *ConversationHistory) TableName() string {
	return "cs_conversation_history"
}

// QuestionProgress 问题进度
type QuestionProgress struct {
	QuestionGetList []*QuestionGet `json:"question_get_list"`
}

// QuestionGet 问题获取
type QuestionGet struct {
	QuestionKey      string `json:"question_key"`
	Answer           string `json:"answer"`
	HasAnswer        bool   `json:"has_answer"`
	QuestionAskCount int32  `json:"question_ask_count"`
}
