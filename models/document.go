package models

type (
	TicketsDoc struct {
		TicketID         uint64 `json:"ticket_id"`
		UUID             string `json:"uuid"`            // 设备指纹
		AccountID        string `json:"account_id"`      // fpx:account_id / kg:fpid
		UID              uint64 `json:"uid"`             // uid
		Vip              uint16 `json:"vip"`             // vip单标识： 0:普通用户；2:VIP 用户
		Recharge         uint64 `json:"recharge"`        // 充值金额 单位 美金/元*10000
		VipCrm           int8   `json:"vip_crm"`         // crm vip
		UserType         int8   `json:"user_type"`       // 提单用户类型
		SVIP             int8   `json:"svip"`            // svip
		Nickname         string `json:"nickname"`        // 用户昵称
		Project          string `json:"project"`         // 所属项目
		GmID             string `json:"gm_id"`           // 游戏ID : fpx:fpx_app_id / kg:game_id
		Channel          string `json:"channel"`         // 渠道
		SubChannel       string `json:"sub_channel"`     // 子渠道
		PackageID        string `json:"package_id"`      // 子渠道
		Sid              uint32 `json:"sid"`             // 区服ID
		RoleID           string `json:"role_id"`         // 角色ID
		RoleName         string `json:"role_name"`       // 角色昵称
		Lang             string `json:"lang"`            // 语言
		Origin           uint8  `json:"origin"`          // 工单来源 1:玩家 2:VIP 3:普通客服
		Scene            uint8  `json:"scene"`           // 入口场景枚举 对应：pb.SceneType
		CatID            uint32 `json:"cat_id"`          // 分类ID
		RelateType       uint32 `json:"relate_type"`     // 关联分类 1:表单 2:流程
		Evaluation       bool   `json:"evaluation"`      // 允许评价 0:false 1:true
		RelateID         uint64 `json:"relate_id"`       // 重提工单ID
		TplID            uint32 `json:"tpl_id"`          // 模版ID
		ProcessId        uint32 `json:"process_id"`      // 流程id
		ProcessSession   string `json:"process_session"` // 流程session
		Priority         uint8  `json:"priority"`        // 是否是升级单： 0:否 ,1:是
		Creator          string `json:"creator"`         // 创建人
		Acceptor         string `json:"acceptor"`        // 受理员
		Csi              uint32 `json:"csi"`             // 用户满意度 - appraise
		Nps              uint32 `json:"nps"`             // 推荐给别人的意愿程度 - appraise
		EvaluateAt       uint64 `json:"evaluate_at"`     // 评价时间
		Comment          string `json:"comment"`         // 评论 - appraise
		Proof            uint8  `json:"proof"`
		ReopenNum        uint8  `json:"reopen_num"`        // 重开次数
		ReopenInterval   uint32 `json:"reopen_interval"`   // 重开间隔时间
		ResolveConfirmed int8   `json:"resolve_confirmed"` // 确认解决 -1：默认未选择 1：解决 2：未解决
		ZoneVipLevel     uint64 `json:"zone_vip_level"`    // 私域R级

		Refill []uint32 `json:"refill"` // 打回补填 -- 暂未用到
		// Upgrade []uint32 `json:"upgrade"` // 升级
		// Audit   []uint32 `json:"audit"`   // 升级审核区
		// FCR     bool     `json:"fcr"`     // 一次解决

		Stage           uint32 `json:"stage"`              // 流转节点 对应 pb.TkStage
		Status          uint32 `json:"status"`             // 工单状态 0:待分配 1:处理中 2:已完成 对应 pb.TkStatus
		SolveType       uint32 `json:"solve_type"`         // 处理类型 0：人工 1：模版自动回复 2：无效单 3：工单策略单 4：工单历史库单 对应 pb.SolveType
		Closed          uint8  `json:"closed"`             // 结案状态:0:空 1:玩家结案 2:客服结案 3:系统
		ClosedAt        uint64 `json:"closed_at"`          // 结案时间
		FirstClosedAt   uint64 `json:"first_closed_at"`    // 首次结案时间
		FirstReplyAt    uint64 `json:"first_reply_at"`     // 最初回复时间
		LastReplyAt     uint64 `json:"last_reply_at"`      // 最后回复时间
		SortWaitStartAt uint64 `json:"sort_wait_start_at"` // 搜索排序字段：等待开始时间

		Field string `json:"field"` // 工单字段

		DeviceType  string `json:"device_type"`  // 机型
		Os          string `json:"os"`           // 系统
		OsVersion   string `json:"os_version"`   // 系统版本
		AppVersion  string `json:"app_version"`  // 游戏版本
		SdkVersion  string `json:"sdk_version"`  // 客服SDK版本
		RomGb       uint32 `json:"rom_gb"`       // 存储总量
		RemainRom   uint32 `json:"remain_rom"`   // 剩余存储量
		RAMMb       uint32 `json:"ram_mb"`       // 内存
		NetworkInfo string `json:"network_info"` // 网络环境
		Country     string `json:"country"`      // 国家

		CsiTags    []uint32             `json:"csi_tag"`     // 评价标签
		Tags       []uint32             `json:"tags"`        // 工单标签
		SystemTags []uint32             `json:"system_tags"` // 工单系统标签
		Replies    []ReplyDoc           `json:"replies"`     // 回复
		Remarks    []string             `json:"remarks"`     // 备注内容
		Histories  []HistoryDoc         `json:"histories"`   // 日志
		ReopenHist []FpOpsTicketsReopen `json:"reopen_hist"` // 重开日志
		Commus     []CommuDoc           `json:"commus"`      // 工单关联对话记录
		// Refills   []RefillDoc  `json:"refills"`    // 补填
		CreatedAt uint64 `json:"created_at"` // 创建时间
		UpdatedAt uint64 `json:"updated_at"` // 更新时间
		// 问题用户信息
		TroubleUserType int8 `json:"trouble_user_type"` // 问题用户类型
	}

	ReplyDoc struct {
		ReplyRole    uint8  `json:"reply_role"`    // 回复角色类型 1:玩家 2:客服 3:系统
		ReplyContent string `json:"reply_content"` // 回复内容
		Op           uint8  `json:"op"`            // 执行操作 9:回复信息 7:打回补填 8:关闭工单 6:处理完成
		Operator     string `json:"operator"`      // 操作人员
	}

	CommuDoc struct {
		FromRole  uint32 `json:"from_role"`  // 回复角色类型  1:玩家 2:客服 3:系统
		CommuType string `json:"commu_type"` // 消息类型： 1：对话消息；2：增加备注
		OpType    uint8  `json:"op_type"`    // 后端会话回复: 对应消息类型 - pb.TkEvent
		Detail    string `json:"detail"`     // 回复内容
		Operator  string `json:"operator"`   // 操作员
		CreatedAt uint64 `json:"created_at"` // 创建时间
	}

	HistoryDoc struct {
		ID        uint64 `json:"id"`
		Acceptor  string `json:"acceptor"`  // 工单处理人
		Operate   uint8  `json:"operate"`   // 操作
		OpDetail  uint32 `json:"op_detail"` // 操作具体流程
		OpRole    uint8  `json:"op_role"`
		OpObject  string `json:"op_object"`  // 被操作对象
		Operator  string `json:"operator"`   // 操作员
		CreatedAt uint64 `json:"created_at"` // 创建时间
	}
	TicketProjectIndices struct {
		Project string `gorm:"column:project"`
		Cnt     uint32 `gorm:"column:cnt"`
	}
)
