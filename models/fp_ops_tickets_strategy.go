package models

import (
	jsoniter "github.com/json-iterator/go"
	"ops-ticket-api/proto/pb"
)

// FpOpsTicketsStrategy 工单分级策略
type FpOpsTicketsStrategy struct {
	ID           uint64 `gorm:"primaryKey;column:id;type:bigint(20) unsigned;not null"`        // 策略id
	Project      string `gorm:"column:project;type:varchar(32);not null;default:''"`           // 游戏
	StrategyName string `gorm:"column:strategy_name;type:varchar(64);not null;default:''"`     // 策略名称
	Filters      string `gorm:"column:filters;type:text;default:null"`                         // 过滤条件
	Enable       uint8  `gorm:"column:enable;type:tinyint(3);not null;default:0"`              // 1启用 2禁用
	Creator      string `gorm:"column:creator;type:varchar(64);not null;default:''"`           // 创建人
	CreatedAt    uint64 `gorm:"column:created_at;type:bigint(20) unsigned;not null;default:0"` // 创建时间
	UpdatedAt    uint64 `gorm:"column:updated_at;type:bigint(20) unsigned;not null;default:0"` // 更新时间
}

func (m *FpOpsTicketsStrategy) TableName() string {
	return "fp_ops_tickets_strategy"
}

func (m *FpOpsTicketsStrategy) DecodeStrategyFilters() *pb.StrategyFilters {
	var detail = &pb.StrategyFilters{}
	jsoniter.UnmarshalFromString(m.Filters, detail)
	return detail
}

func CheckFilter(obj int64, strategy *pb.StrategySplit) bool {
	// 若有选择全部的选项，先校验
	if len(strategy.Btw) == 0 && len(strategy.Ids) == 0 {
		return false
	}
	// 1. 检查 obj 是否在 Ids 列表中
	for _, id := range strategy.Ids {
		if obj == id {
			return true
		}
	}

	// 2. 检查 obj 是否在 Btw 范围内
	for _, btw := range strategy.Btw {
		if obj >= btw.Start && obj <= btw.End {
			return true
		}
	}

	// 3. 如果都不匹配，则未命中
	return false
}
