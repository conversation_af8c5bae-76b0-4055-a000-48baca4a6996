package models

import "time"

// FpOpsChannelCategory DC/Line问题分类配置表
type FpOpsChannelCategory struct {
	CatID       uint32    `gorm:"primaryKey;column:cat_id;type:int(11) unsigned;not null"`
	Project     string    `gorm:"index:idx_prj_cat;column:project;type:varchar(64);not null;default:''"`             // 所属项目
	CatType     uint16    `gorm:"index:idx_prj_cat;column:cat_type;type:smallint(2) unsigned;not null;default:0"`    // 类别: 0.未知 1.Discord 2.Line
	Category    string    `gorm:"index:idx_prj_cat;column:category;type:varchar(64);not null;default:''"`            // 分类名称
	OneLevel    uint32    `gorm:"index:idx_level;column:one_level;type:int(11) unsigned;not null;default:0"`         // 一级分类
	SecondLevel uint32    `gorm:"index:idx_level;column:second_level;type:int(11);not null;default:0"`               // 二级分类
	Level       uint32    `gorm:"index:idx_level;index:idx_prj_cat;column:level;type:tinyint(1);not null;default:1"` // 分类层级
	Operator    string    `gorm:"column:operator;type:varchar(64);not null;default:''"`                              // op
	IsDeleted   uint16    `gorm:"index:idx_prj_cat;column:is_deleted;type:tinyint(1) unsigned;not null;default:0"`   // 删除
	CreatedAt   time.Time `gorm:"column:created_at;type:datetime"`                                                   // 创建时间
	UpdatedAt   time.Time `gorm:"column:updated_at;type:datetime"`                                                   // 更新时间
}

func (obj FpOpsChannelCategory) TableName() string {
	return "fp_ops_channel_category"
}
