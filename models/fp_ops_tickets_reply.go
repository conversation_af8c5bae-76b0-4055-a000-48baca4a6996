package models

// FpOpsTicketsReply 工单回复表
type FpOpsTicketsReply struct {
	ID           uint64 `gorm:"primaryKey;column:id;type:bigint(20) unsigned;not null"`
	TicketID     uint64 `gorm:"index:idx_ticket_id;column:ticket_id;type:bigint(20) unsigned;not null;default:0"` // 工单ID
	GmID         string `gorm:"index:idx_account;column:gm_id;type:varchar(128);not null;default:''"`             // 游戏标识: game_id(kg)/app_id(fpx)
	Scene        uint8  `gorm:"column:scene;type:tinyint(2) unsigned;not null;default:0"`                         // 入口场景枚举
	UUID         string `gorm:"column:uuid;type:varchar(64);not null;default:''"`                                 // 唯一ID
	AccountID    string `gorm:"index:idx_account;column:account_id;type:varchar(128);not null;default:''"`        // fpx-account
	UID          uint64 `gorm:"column:uid;type:bigint(20) unsigned;not null;default:0"`                           // uid
	ReplyRole    uint8  `gorm:"column:reply_role;type:tinyint(255) unsigned;not null;default:1"`                  // 回复角色类型  1:玩家 2:客服 3:系统
	ReplyContent string `gorm:"index:ft_idx_reply,class:FULLTEXT;column:reply_content;type:text;default:null"`    // 回复内容
	FileURL      string `gorm:"column:file_url;type:varchar(255);not null;default:''"`                            // 文件路径
	Op           uint8  `gorm:"column:op;type:tinyint(1);not null"`                                               // 执行操作 9:回复信息 7:打回补填 8:关闭工单 6:处理完成
	Operator     uint8  `gorm:"column:operator;type:varchar(64);not null"`                                        // 操作员
	Read         uint8  `gorm:"index:idx_account;column:read;type:tinyint(1);not null"`                           // 消息已读 0:false 1:true
	IsDel        uint8  `gorm:"column:is_del;type:tinyint(1);not null;default:0"`                                 // 删除 0:false 1:true
	CreatedAt    uint64 `gorm:"column:created_at;type:bigint(20) unsigned;not null;default:0"`                    // 创建时间
	UpdatedAt    uint64 `gorm:"column:updated_at;type:bigint(20) unsigned;not null;default:0"`                    // 更新时间
}

func (m *FpOpsTicketsReply) TableName() string {
	return "fp_ops_tickets_reply"
}
