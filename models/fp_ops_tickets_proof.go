package models

// FpOpsTicketsProof 工单补填表
type FpOpsTicketsProof struct {
	ID          uint64 `gorm:"primaryKey;column:id;type:bigint(20) unsigned;not null"`
	TicketID    uint64 `gorm:"index:idx_ticket_id;column:ticket_id;type:bigint(20) unsigned;not null;default:0"` // 工单ID
	Remark      string `gorm:"column:remark;type:varchar(1024);not null;default:''"`                             // 备注信息
	FillContent string `gorm:"column:fill_content;type:text;not null"`                                           // 补填内容
	Files       string `gorm:"column:files;type:text;not null"`                                                  // 文件
	Status      uint8  `gorm:"column:status;type:tinyint(1) unsigned;not null;default:0"`                        // 补填完成 0:false 1:true
	From        uint8  `gorm:"column:from;type:tinyint(1) unsigned;not null;default:0"`                          // 补填来源 1:玩家  2:客服
	BeforeStage uint8  `gorm:"column:before_stage;type:tinyint(1) unsigned;not null;default:0"`                  // 打回前节点
	AfterStage  uint8  `gorm:"column:after_stage;type:tinyint(1) unsigned;not null;default:0"`                   // 打回后节点
	Op          string `gorm:"column:op;type:varchar(64);not null;default:''"`                                   // 打回操作人
	CreatedAt   uint64 `gorm:"column:created_at;type:bigint(20) unsigned;not null;"`                             // 创建时间
	UpdatedAt   uint64 `gorm:"column:updated_at;type:bigint(20) unsigned;not null;"`                             // 更新时间
}

func (m *FpOpsTicketsProof) TableName() string {
	return "fp_ops_tickets_proof"
}
