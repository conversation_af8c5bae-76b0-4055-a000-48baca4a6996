package models

import "time"

// FpOpsModuleCategory 工单模版分类表
type FpOpsModuleCategory struct {
	CatID     uint32    `gorm:"primaryKey;column:cat_id;type:int(11) unsigned;not null"`
	Project   string    `gorm:"column:project;type:varchar(64);not null;default:''"`                               // 所属项目
	Category  string    `gorm:"column:category;type:varchar(64);not null;default:''"`                              // 分类名称
	ParentID  uint32    `gorm:"column:parent_id;type:int(11) unsigned;not null;default:0"`                         // 父级ID
	Level     uint32    `gorm:"index:idx_level;index:idx_prj_cat;column:level;type:tinyint(1);not null;default:0"` // 分类层级
	Operator  string    `gorm:"column:operator;type:varchar(64);not null;default:''"`                              // op
	IsDeleted uint16    `gorm:"index:idx_prj_cat;column:is_deleted;type:tinyint(1) unsigned;not null;default:0"`   // 删除
	CreatedAt time.Time `gorm:"column:created_at;type:datetime"`                                                   // 创建时间
	UpdatedAt time.Time `gorm:"column:updated_at;type:datetime"`                                                   // 更新时间
}

func (obj FpOpsModuleCategory) TableName() string {
	return "fp_ops_module_category"
}
