package models

import (
	"time"
)

// FpDataplatGameI18n 游戏数据查询-多语言翻译映射
type FpDataplatGameI18n struct {
	ID         uint64    `gorm:"primaryKey;column:id;type:bigint unsigned;not null"`
	Project    string    `gorm:"uniqueIndex:uniq_pk;index:idx_prd;column:project;type:varchar(64);not null;default:''"` // 游戏标识
	UniqueKey  string    `gorm:"uniqueIndex:uniq_pk;column:unique_key;type:varchar(128);not null;default:''"`           // 原因标识
	ShowMsg    string    `gorm:"column:show_msg;type:varchar(10240);not null;default:''"`                               // 原因标识-对应翻译
	Operator   string    `gorm:"column:operator;type:varchar(64);not null;default:''"`                                  // 操作人
	CreateTime time.Time `gorm:"column:create_time;type:datetime;not null"`                                             // 创建时间
	UpdateTime time.Time `gorm:"column:update_time;type:datetime;default:null"`                                         // 更新时间
}

// FpDataplatGameItem 游戏数据查询-物品id列表
type FpDataplatGameItem struct {
	ID         uint64    `gorm:"primaryKey;column:id;type:bigint unsigned;not null"`
	Project    string    `gorm:"uniqueIndex:uniq_pi;index:idx_prd;column:project;type:varchar(64);not null;default:''"` // 游戏标识
	ItemID     string    `gorm:"column:item_id;type:varchar(128);not null;default:''"`                                  // 物品 id
	ItemName   string    `gorm:"uniqueIndex:uniq_pi;column:item_name;type:varchar(256);not null;default:''"`            // 物品描述
	Operator   string    `gorm:"column:operator;type:varchar(64);not null;default:''"`                                  // 操作人
	CreateTime time.Time `gorm:"column:create_time;type:datetime;not null"`                                             // 创建时间
	UpdateTime time.Time `gorm:"column:update_time;type:datetime;default:null"`                                         // 更新时间
}
