package models

// FpOpsTicketsQuestion 工单知识库-标准问题
type FpOpsTicketsQuestion struct {
	QuestionID          uint64 `gorm:"primaryKey;column:question_id;type:bigint(20) unsigned;not null"`
	Project             string `gorm:"index:idx_prj;index:idx_prj_cat;column:project;type:varchar(64);not null;default:''"`                 // 游戏
	CatID               uint32 `gorm:"index:idx_prj_cat;index:idx_cnv;column:cat_id;type:int(11) unsigned;not null;default:0"`              // 分类ID
	Lang                string `gorm:"column:lang;type:varchar(16);not null;default:''"`                                                    // 语言
	QuestionContent     string `gorm:"index:question_content,class:FULLTEXT;column:question_content;type:varchar(256);not null;default:''"` // 标准问题
	QuestionContentHash string `gorm:"index:question_content_hash;column:question_content_hash;type:varchar(32);not null;default:''"`       // 标准问题 hash 加密
	AnswerRichText      string `gorm:"column:answer_rich_text;type:text"`                                                                   // 富文本答案
	Creator             string `gorm:"column:creator;type:varchar(64);not null;default:''"`                                                 // 创建人
	CreatedAt           uint64 `gorm:"column:created_at;type:bigint(20) unsigned;not null;default:0"`                                       // 创建时间
	UpdatedAt           uint64 `gorm:"column:updated_at;type:bigint(20) unsigned;not null;default:0"`                                       // 更新时间
}

func (m *FpOpsTicketsQuestion) TableName() string {
	return "fp_ops_tickets_question"
}
