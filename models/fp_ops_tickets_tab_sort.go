package models

// FpOpsTicketsTabSort ticket搜索tab排序表
type FpOpsTicketsTabSort struct {
	ID          int64  `gorm:"primaryKey;column:id;type:bigint(20) unsigned;not null" json:"id"`
	Operator    string `gorm:"column:operator;type:varchar(64);not null;default:''" json:"operator"`                          // 操作人
	Detail      string `gorm:"column:detail;type:text;not null;default:''" json:"detail"`                                     // 搜索条件信息
	CreatedTime string `gorm:"column:create_time;type:varchar(64);not null;default:'1900-01-01 00:00:00'" json:"create_time"` // 创建时间
	UpdatedTime string `gorm:"column:update_time;type:varchar(64);not null;default:'1900-01-01 00:00:00'" json:"update_time"` // 更新时间
}

type SortDetail struct {
	Project string  `json:"project"`
	Tab     []int64 `json:"tab"`
}

func GetFpOpsTicketsTabSort() string {
	return "fp_ops_tickets_tab_sort"
}
