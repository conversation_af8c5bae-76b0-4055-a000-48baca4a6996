package models

// FpOpsTicketsField 工单对话表
type FpOpsTicketsField struct {
	ID        uint64 `gorm:"primaryKey;column:id;type:bigint(20) unsigned;not null"`
	TicketID  int64  `gorm:"unique;column:ticket_id;type:bigint(20);not null"` // 工单ID
	Field     string `gorm:"column:field;type:mediumtext;not null"`            // 工单字段
	CreatedAt int64  `gorm:"column:created_at;type:bigint(20);not null"`       // 创建时间
	UpdatedAt int64  `gorm:"column:updated_at;type:bigint(20);not null"`       // 更新时间
}
