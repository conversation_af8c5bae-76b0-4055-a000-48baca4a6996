package models

import "time"

// FpDscOperateLog discord 操作相关日志
type FpDscOperateLog struct {
	ID              uint64    `gorm:"primaryKey;column:id;type:bigint unsigned;not null"`                                      // 自增id
	OperationGroup  string    `gorm:"index:gact_uni_id;column:operation_group;type:varchar(32);not null;default:''"`           // 具体模块分组
	OperationAction string    `gorm:"index:gact_uni_id;column:operation_action;type:varchar(32);not null;default:''"`          // 具体操作action
	BaseID          string    `gorm:"column:base_id;type:varchar(64);not null;default:''"`                                     // 业务id
	UniqueID        string    `gorm:"index:unique_id;index:gact_uni_id;column:unique_id;type:varchar(64);not null;default:''"` // 操作对应唯一id
	GameProject     string    `gorm:"index:idx_time;column:game_project;type:varchar(32);not null;default:''"`                 // 游戏项目ID
	BeforeDetail    string    `gorm:"column:before_detail;type:text;default:null"`                                             // 操作前的数据
	AfterDetail     string    `gorm:"column:after_detail;type:text;default:null"`                                              // 操作后的数据
	CreateTime      time.Time `gorm:"index:idx_time;column:create_time;type:timestamp;not null;default:CURRENT_TIMESTAMP"`     // 添加时间
	Operator        string    `gorm:"column:operator;type:varchar(32);not null;default:''"`                                    // 操作人
}
