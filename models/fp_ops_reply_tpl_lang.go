package models

import "time"

// FpOpsReplyTplLang 模板配置多语言表
type FpOpsReplyTplLang struct {
	ID           uint32    `gorm:"primaryKey;column:id;type:int(11) unsigned;not null"`
	ReplyTplID   uint32    `gorm:"uniqueIndex:idx_tpl;column:reply_tpl_id;type:int(1) unsigned;not null;default:0"` // 模版ID
	Project      string    `gorm:"uniqueIndex:idx_tpl;column:project;type:varchar(64);not null;default:''"`         // 所属项目
	Lang         string    `gorm:"uniqueIndex:idx_tpl;column:lang;type:varchar(32);not null;default:''"`            // 语言
	ReplyTpl     string    `gorm:"column:reply_tpl;type:varchar(32);not null"`                                      // 回复模版名称
	ReplyContent string    `gorm:"column:reply_content;type:text;not null"`                                         // 回复模版表单
	Enable       bool      `gorm:"column:enable;type:tinyint(1) unsigned;not null;default:1"`                       // 激活. 0:false 1:true
	Operator     string    `gorm:"column:operator;type:varchar(64);not null;default:''"`                            // 更新用户
	CreatedAt    time.Time `gorm:"column:created_at;type:datetime"`                                                 // 创建时间
	UpdatedAt    time.Time `gorm:"column:updated_at;type:datetime"`                                                 // 更新时间
}
