package models

// FpOpsTicketsDevice 工单表--用户设备表
type FpOpsTicketsDevice struct {
	ID          uint64 `gorm:"primaryKey;column:id;type:bigint(20) unsigned;not null" json:"id"`
	TicketID    uint64 `gorm:"index:idx_prj;column:ticket_id;type:bigint(20) unsigned;not null" json:"ticket_id"` // 工单ID
	UUID        string `gorm:"column:uuid;type:varchar(128);not null;default:''"`                                 // 设备指纹
	Channel     string `gorm:"column:channel;type:varchar(16);not null;default:''"`                               // 渠道
	SubChannel  string `gorm:"column:sub_channel;type:varchar(16);not null;default:''"`                           // 子渠道
	PackageID   string `gorm:"column:package_id;type:varchar(64);not null;default:''"`                            // 渠道号
	DeviceType  string `gorm:"column:device_type;type:varchar(64);not null;default:''" json:"device_type"`        // 机型
	IP          string `gorm:"column:ip;type:varchar(64);not null;default:''" json:"ip"`                          // IP地址
	Os          string `gorm:"column:os;type:varchar(16);not null;default:''" json:"os"`                          // 系统
	OsVersion   string `gorm:"column:os_version;type:varchar(32);not null;default:''" json:"os_version"`          // 系统版本
	AppVersion  string `gorm:"column:app_version;type:varchar(64);not null;default:''" json:"app_version"`        // 游戏版本
	SdkVersion  string `gorm:"column:sdk_version;type:varchar(16);not null;default:''" json:"sdk_version"`        // 客服SDK版本
	RomGb       uint32 `gorm:"column:rom_gb;type:int(4) unsigned;not null;default:0" json:"rom_gb"`               // 存储总量
	RemainRom   uint32 `gorm:"column:remain_rom;type:int(4) unsigned;not null;default:0" json:"remain_rom"`       // 剩余存储量
	RAMMb       uint32 `gorm:"column:ram_mb;type:int(4) unsigned;not null;default:0" json:"ram_mb"`               // 内存
	NetworkInfo string `gorm:"column:network_info;type:varchar(32);not null" json:"network_info"`                 // 网络环境
	Country     string `gorm:"column:country;type:varchar(32);not null" json:"country"`                           // 国家
	CreatedAt   uint64 `gorm:"column:created_at;type:bigint(20) unsigned;not null;" json:"created_at"`            // 创建时间
	UpdatedAt   uint64 `gorm:"column:updated_at;type:bigint(20) unsigned;not null;" json:"updated_at"`            // 更新时间
}
