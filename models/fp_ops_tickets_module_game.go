package models

// FpOpsTicketsModuleGame 工单回复模板游戏关联表
type FpOpsTicketsModuleGame struct {
	ID          uint64 `gorm:"primaryKey;column:id;type:bigint(20) unsigned;not null" json:"id"`
	ModuleId    uint64 `gorm:"column:module_id;type:bigint(20);not null;default:0" json:"module_id"`                          // 模板id
	GameProject string `gorm:"column:game_project;type:varchar(128);not null;default:''" json:"game_project"`                 // 模板关联游戏
	CreateTime  uint64 `gorm:"column:create_time;type:bigint(20) unsigned;not null;default:0" json:"create_time"`             // 创建时间
	UpdatedTime string `gorm:"column:update_time;type:varchar(64);not null;default:'1900-01-01 00:00:00'" json:"update_time"` // 更新时间
}

func GetFpOpsTicketsModuleGameTableName() string {
	return "fp_ops_tickets_module_game"
}
