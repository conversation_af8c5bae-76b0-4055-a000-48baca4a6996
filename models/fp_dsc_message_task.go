package models

import "time"

// FpDscMessageTask Discord批量私信任务表
type FpDscMessageTask struct {
	ID           uint64                    `gorm:"primaryKey;column:id;type:bigint(20) unsigned;not null"`                         // 任务ID
	Project      string                    `gorm:"column:project;type:varchar(32);not null;default:''"`                            // 所属项目（游戏）
	Content      string                    `gorm:"column:content;type:text;default:''"`                                            // 发送的私信内容
	FileUrl      string                    `gorm:"column:file_url;type:varchar(1024);default:''"`                                  // 文件URL
	Status       int                       `gorm:"column:status;type:int(10);not null;default:0"`                                  // 任务状态 0: 处理中; 10: 未开始; 20: 处理失败; 30: 处理成功;
	SuccessCount int                       `gorm:"column:success_count;type:int(10);not null;default:0"`                           // 成功发送的数量
	FailedCount  int                       `gorm:"column:failed_count;type:int(10);not null;default:0"`                            // 失败发送的数量
	Operator     string                    `gorm:"column:operator;type:varchar(64);not null;default:''"`                           // 处理人
	CreatedAt    time.Time                 `gorm:"index:idx_create_at;column:created_at;type:datetime;default:CURRENT_TIMESTAMP"`  // 创建时间
	FinishedAt   time.Time                 `gorm:"column:finished_at;type:datetime"`                                               // 完成时间
	UpdateAt     time.Time                 `gorm:"index:idx_updated_at;column:updated_at;type:datetime;default:CURRENT_TIMESTAMP"` // 更新时间
	Users        []*FpDscMessageTaskDetail `gorm:"foreignKey:TaskID;references:ID"`                                                // 用户明细
}
