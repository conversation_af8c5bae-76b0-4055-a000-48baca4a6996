package models

// FpDscPlayerTag discord玩家绑定标签表
type FpDscPlayerTag struct {
	ID          uint64 `gorm:"primaryKey;column:id;type:bigint(20) unsigned;not null" json:"id"`
	Project     string `gorm:"column:project;type:varchar(128);not null;default:''" json:"project"`                           // 游戏
	DscUserId   string `gorm:"column:dsc_user_id;type:varchar(255);not null;default:''" json:"dsc_user_id"`                   // discord上的玩家用户id
	TagId       int64  `gorm:"column:tag_id;type:bigint(20);not null;default:0" json:"tag_id"`                                // 标签ID（弃用）
	LabelId     int64  `gorm:"column:label_id;type:bigint(20);not null;default:0" json:"label_id"`                            // 新标签Id
	CreateTime  uint64 `gorm:"column:create_time;type:bigint(20) unsigned;not null;default:0" json:"create_time"`             // 创建时间
	UpdatedTime string `gorm:"column:update_time;type:varchar(64);not null;default:'1900-01-01 00:00:00'" json:"update_time"` // 更新时间
}

func GetFpDscPlayerTagTableName() string {
	return "fp_dsc_player_tag"
}
