package models

// FpOpsTicketsCommu 工单关联对话记录表
type FpOpsTicketsCommu struct {
	ID        uint64 `gorm:"primaryKey;column:id;type:bigint(20) unsigned;not null"`
	TicketID  uint64 `gorm:"index:idx_ticket_id;column:ticket_id;type:bigint(20) unsigned;not null;default:0"` // 工单ID
	FromRole  uint32 `gorm:"column:from_role;type:tinyint(255) unsigned;not null;default:1"`                   // 回复角色类型  1:玩家 2:客服 3:系统
	CommuType string `gorm:"column:commu_type;type:varchar(64) not null;default:''"`                           // 消息类型： 1：对话消息；2：增加备注
	OpType    uint8  `gorm:"column:op_type;type:tinyint(2) unsigned;not null;default:0"`                       // 后端会话回复对应消息类型 - pb.TkEvent
	Remark    string `gorm:"column:remark;type:varchar(1024);not null;default:''"`                             // 备注信息
	Detail    string `gorm:"column:detail;type:text;default:null"`                                             // 回复内容
	Operator  string `gorm:"column:operator;type:varchar(64);not null"`                                        // 操作员
	Read      bool   `gorm:"column:read;type:tinyint(1);not null"`                                             // 消息已读 0:false 1:true
	IsDel     bool   `gorm:"column:is_del;type:tinyint(1);not null;default:0"`                                 // 删除 0:false 1:true
	Picture   string `gorm:"column:picture;type:text;not null;default:''"`                                     // 玩家上传的图片url
	Video     string `gorm:"column:video;type:text;not null;default:''"`                                       // 玩家上传的视频url
	CreatedAt uint64 `gorm:"column:created_at;type:bigint(20) unsigned;not null;default:0"`                    // 创建时间
	UpdatedAt uint64 `gorm:"column:updated_at;type:bigint(20) unsigned;not null;default:0"`                    // 更新时间
}

func (m *FpOpsTicketsCommu) TableName() string {
	return "fp_ops_tickets_commu"
}
