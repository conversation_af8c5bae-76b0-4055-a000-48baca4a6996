package models

import (
	"context"
	"encoding/json"
	"fmt"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"ops-ticket-api/proto/pb"
	"ops-ticket-api/utils"
	"time"
)

// FpExamineDiscordDetail 质检打分表配置

// FpExamineDiscordDetail 质检打分表配置
type FpExamineDiscordDetail struct {
	ID                       uint64             `gorm:"primaryKey;column:id;type:bigint unsigned;not null"`
	TaskID                   uint64             `gorm:"uniqueIndex:idx_tbu;column:task_id;type:bigint unsigned;not null;default:0"`                    // 任务批次id
	TplID                    uint64             `gorm:"column:tpl_id;type:bigint unsigned;not null;default:0"`                                         // tpl id
	Project                  string             `gorm:"column:project;type:varchar(32);not null;default:''"`                                           // 所属项目
	BotID                    string             `gorm:"uniqueIndex:idx_tbu;column:bot_id;type:varchar(32);not null;default:''"`                        // 所属应用/机器人bot的 user_id
	DscUserID                string             `gorm:"uniqueIndex:idx_tbu;index:dsc_user_id;column:dsc_user_id;type:varchar(32);not null;default:''"` // 用户 discord_user_id
	DscUserName              string             `gorm:"column:dsc_user_name;type:varchar(128);not null;default:''"`                                    // 机器人昵称
	ChannelID                string             `gorm:"index:idx_channel;column:channel_id;type:varchar(32);not null;default:''"`                      // 用户私聊 channel id
	GenUID                   int64              `gorm:"column:gen_uid;type:bigint;not null;default:0"`                                                 // 生成质检单时: 玩家uid
	GenAccountID             string             `gorm:"column:gen_account_id;type:varchar(64);not null;default:''"`                                    // fpid
	GenSid                   string             `gorm:"column:gen_sid;type:varchar(64);not null;default:''"`                                           // 生成质检单时: 服务器
	GenPayAll                int64              `gorm:"column:gen_pay_all;type:bigint;not null;default:0"`                                             // 生成质检单时: 玩家累付金额
	GenPayLastThirtyDays     int64              `gorm:"column:gen_pay_last_thirty_days;type:bigint;not null;default:0"`                                // 生成质检单时: 玩家最近30天付费金额
	GenReplyType             int64              `gorm:"column:gen_reply_type;type:bigint;not null;default:0"`                                          // 生成质检单时: 当前沟通状态
	GenLastLogin             int64              `gorm:"column:gen_last_login;type:bigint;not null;default:0"`                                          // 生成质检单时: 玩家最近登录时间
	GenVipState              int8               `gorm:"column:gen_vip_state;type:tinyint;not null;default:1"`                                          // vip状态:1非vip, 2vip
	GenVipLevel              int8               `gorm:"column:gen_vip_level;type:tinyint;not null;default:0"`                                          // vip等级
	Inspector                string             `gorm:"column:inspector;type:varchar(32);not null;default:''"`                                         // 质检员
	Status                   int                `gorm:"column:status;type:int;not null;default:0"`                                                     // 当前状态：
	RelatedAccount           string             `gorm:"column:related_account;type:varchar(512);not null;default:''"`                                  // 质检结果关联员工(被质检人s)
	NoticeAccount            string             `gorm:"column:notice_account;type:varchar(512);not null;default:''"`                                   // 通知质检结果的相关人
	FinalResult              int                `gorm:"column:final_result;type:int;not null;default:0"`                                               // 质检结果: 通过；不通过
	FinalScore               int                `gorm:"column:final_score;type:int;not null;default:0"`                                                // 质检结果：分数： 0～100
	FinalReason              string             `gorm:"column:final_reason;type:varchar(64);not null;default:''"`                                      // 质检结果：错误根源
	FinalDesc                string             `gorm:"column:final_desc;type:text;default:null"`                                                      // 质检结果：质检问题描述
	FinalResultModifyComment string             `gorm:"column:final_result_modify_comment;type:varchar(1024);not null;default:''"`                     // 质检结果修改： 修改原因备注
	CreatedAt                time.Time          `gorm:"column:created_at;type:datetime;not null"`                                                      // 创建时间
	FinishedAt               time.Time          `gorm:"column:finished_at;type:datetime;not null"`                                                     // 完成时间
	FinalModifiedAt          time.Time          `gorm:"column:final_modified_at;type:datetime;not null"`                                               // 最后修改时间
	UpdatedAt                time.Time          `gorm:"column:updated_at;type:datetime;default:null"`                                                  // 更新时间
	GenTags                  string             `gorm:"column:gen_tags;type:varchar(1024);not null;default:''"`                                        // 生成质检单时：当前绑定标签
	FpExamineFields          []*FpExamineFields `gorm:"-" json:"fp_examine_fields"`
}

func (m FpExamineDiscordDetail) DecodeRelatedAccount(ctx context.Context, relateAccount string) []string {
	if relateAccount == "" {
		return []string{}
	}
	var ac = make([]string, 0)
	if err := json.Unmarshal([]byte(relateAccount), &ac); err != nil {
		logger.Errorf(ctx, "DecodeRelatedAccount return err. org:%s. err:%v", relateAccount, err)
	}
	return ac
}

func (m FpExamineDiscordDetail) DecodeNoticeAccount(ctx context.Context, noticeAccount string) []string {
	if noticeAccount == "" {
		return []string{}
	}
	var na = make([]string, 0)
	if err := json.Unmarshal([]byte(noticeAccount), &na); err != nil {
		logger.Errorf(ctx, "NoticeAccount return err. org:%s. err:%v", noticeAccount, err)
	}
	return na
}

// FpExamineFields 质检单各项打分详情表
type FpExamineFields struct {
	ID          uint64    `gorm:"primaryKey;column:id;type:bigint unsigned;not null"`
	TaskID      uint64    `gorm:"index:idx_tid;column:task_id;type:bigint unsigned;not null;default:0"`  // 任务批次id
	Project     string    `gorm:"column:project;type:varchar(32);not null;default:''"`                   // 所属项目
	TplID       uint64    `gorm:"column:tpl_id;type:bigint unsigned;not null;default:0"`                 // tpl id
	ExamineType string    `gorm:"index:idx_td;column:examine_type;type:varchar(32);not null;default:''"` // 关联类型: discord审核:ExamineTypeDiscord; 工单审核:ExamineTypeTicket
	DetailID    int64     `gorm:"index:idx_td;column:detail_id;type:bigint;not null;default:0"`          // 对应质检单 id
	FieldShow   string    `gorm:"index:idx_td;column:field_show;type:varchar(512);not null;default:''"`  // 字段名称
	FieldSort   uint      `gorm:"column:field_sort;type:int unsigned;not null;default:0"`                // 字段展示层级圈定 100_100_100 三层级拼接数字
	FieldType   int       `gorm:"column:field_type;type:int;not null;default:0"`                         // 字段类型: 1:不需要(是模块x标题时); 10:下拉单选;20:下拉多选;30:多行文本;40:单行文本
	FieldVal    string    `gorm:"column:field_val;type:varchar(10240);not null;default:''"`              // 字段数据
	CreatedAt   time.Time `gorm:"column:created_at;type:datetime;not null"`                              // 创建时间
	UpdatedAt   time.Time `gorm:"column:updated_at;type:datetime;default:null"`                          // 更新时间
}

// FpExamineOperateLog examine操作日志
type FpExamineOperateLog struct {
	ID              uint64    `gorm:"primaryKey;column:id;type:bigint unsigned;not null"`                                      // 自增id
	OperationGroup  string    `gorm:"index:gact_uni_id;column:operation_group;type:varchar(32);not null;default:''"`           // 具体模块分组
	OperationAction string    `gorm:"index:gact_uni_id;column:operation_action;type:varchar(32);not null;default:''"`          // 具体操作action
	BaseID          string    `gorm:"column:base_id;type:varchar(64);not null;default:''"`                                     // 业务id
	UniqueID        string    `gorm:"index:unique_id;index:gact_uni_id;column:unique_id;type:varchar(64);not null;default:''"` // 操作对应唯一id
	GameProject     string    `gorm:"index:idx_time;column:game_project;type:varchar(32);not null;default:''"`                 // 游戏项目
	BeforeDetail    string    `gorm:"column:before_detail;type:text;default:null"`                                             // 操作前的数据
	AfterDetail     string    `gorm:"column:after_detail;type:text;default:null"`                                              // 操作后的数据
	CreateTime      time.Time `gorm:"index:idx_time;column:create_time;type:timestamp;not null;default:CURRENT_TIMESTAMP"`     // 添加时间
	Operator        string    `gorm:"column:operator;type:varchar(32);not null;default:''"`                                    // 操作人
}

// FpExamineTask 质检打分表配置
type FpExamineTask struct {
	ID         uint64    `gorm:"primaryKey;column:id;type:bigint unsigned;not null"`
	TaskName   string    `gorm:"column:task_name;type:varchar(256);not null;default:''"`     // 任务名称
	Project    string    `gorm:"column:project;type:varchar(32);not null;default:''"`        // 游戏标识
	TplID      uint64    `gorm:"index:idx_tid;column:tpl_id;type:bigint;not null;default:0"` // 关联任务表
	TaskGroup  string    `gorm:"column:task_group;type:varchar(32);not null;default:''"`     // 任务类型：抽检工单、抽检 discord、抽检 line等
	Filters    string    `gorm:"column:filters;type:text;default:null"`                      // 过滤条件
	Link       string    `gorm:"link"`                                                       // 任务详情快照图
	TaskStatus int       `gorm:"column:task_status;type:int;not null;default:0"`             // 当前任务状态
	TaskResult string    `gorm:"column:task_result;type:varchar(10240);not null;default:''"` // 最终执行结果
	Operator   string    `gorm:"column:operator;type:varchar(64);not null;default:''"`       // op
	CreatedAt  time.Time `gorm:"column:created_at;type:datetime;not null"`                   // 创建时间
	FinishedAt time.Time `gorm:"column:finished_at;type:datetime;not null"`                  // 创建时间
	UpdatedAt  time.Time `gorm:"column:updated_at;type:datetime;default:null"`               // 更新时间
}

type ExamineTaskFilterDf struct {
	Project      string                     `json:"project"`
	TaskGroup    pb.ExamineTaskGroupDf      `json:"task_group"`
	DscFilter    *ExamineTaskFilterDscDf    `json:"dsc_filter,omitempty"` // dsc filter
	TicketFilter *ExamineTaskFilterTicketDf `json:"ticket_filter,omitempty"`
}

type ExamineTaskFilterDscDf struct {
	DscRepliedAt       []string              `json:"dsc_replied_at"`        // 回复时间 [start,end]
	DscAllTotalPayGte  int64                 `json:"dsc_all_total_pay_gte"` // 全检标准 - 累充金额>=x
	DscOtherAll        bool                  `json:"dsc_other_all"`         // 抽检范围： true - 全部； false:抽检
	DscOtherRules      pb.ExamineTaskRulesDf `json:"dsc_other_rules"`       // 抽检范围：部分-圈选规则
	DscOtherTag        []string              `json:"dsc_other_label"`       // 抽检范围：部分-圈选规则-标签
	DscOtherMaintainer []string              `json:"dsc_other_maintainer"`  // 抽检范围：部分-维护人
	DscOtherVipState   []uint32              `json:"dsc_other_vip_state"`   // 抽检范围：部分-圈选规则-vip状态
	DscOtherNum        int                   `json:"dsc_other_num"`         // 抽检范围：部分-圈选规则-抽检数量
	DscAssignAccount   []string              `json:"dsc_assign_account"`    // 分配人员：圈选-分配人员
}

type ExamineTaskFilterTicketDf struct {
}

// FpExamineTplConfig 质检打分表配置
type FpExamineTplConfig struct {
	ID            uint64                      `gorm:"primaryKey;column:id;type:bigint unsigned;not null"`
	TplDesc       string                      `gorm:"column:tpl_desc;type:varchar(256);not null;default:''"`   // 标题
	CanDelete     int8                        `gorm:"column:can_delete;type:tinyint;not null;default:1"`       // 1: 可以被删除; 2: 不可被删除(曾经被绑定过的都不允许删除)
	Status        int8                        `gorm:"column:status;type:tinyint;not null;default:1"`           // 当前状态： 1:启用/ 2:禁用
	Operator      string                      `gorm:"column:operator;type:varchar(64);not null;default:''"`    // op
	CreatedAt     time.Time                   `gorm:"column:created_at;type:datetime;not null"`                // 创建时间
	UpdatedAt     time.Time                   `gorm:"column:updated_at;type:datetime;default:null"`            // 更新时间
	ModuleDetails []*FpExamineTplConfigDetail `gorm:"foreignKey:TplID;references:ID" json:"modules,omitempty"` //  模块详情
}

func (m *FpExamineTplConfig) _getSort(levelOneSeqNum, levelTwoSeqNum, levelThreeSeqNum int) int {
	// xxx_xxx_xxx => one_two_three
	return (10e7 + levelOneSeqNum*10e5) + levelTwoSeqNum*10e2 + levelThreeSeqNum
}
func (m *FpExamineTplConfig) GenModuleDetails(ctx context.Context, module []*pb.ExamineModuleDt) ([]*FpExamineTplConfigDetail, error) {
	var (
		now         = time.Now()
		fields      []*FpExamineTplConfigDetail
		hasFieldKey = make(map[string]struct{}, 0)
	)
	for i, v := range module {
		fields = append(fields, &FpExamineTplConfigDetail{
			TplID:      0,
			FieldShow:  v.Name,
			FieldLevel: 1,
			FieldSort:  m._getSort(i+1, 0, 0),
			FieldType:  int(pb.ExamineFieldTpDf_ExamineFieldTpDfUnknown),
			FieldOpts:  "[]",
			FieldExtra: "",
			CreatedAt:  now,
			UpdatedAt:  now,
		})
		for j, field := range v.FieldDetail {
			if _, ok := hasFieldKey[field.FieldName]; ok {
				return nil, fmt.Errorf("field name:%s is duplicate", field.FieldName)
			}
			hasFieldKey[field.FieldName] = struct{}{}
			fields = append(fields, &FpExamineTplConfigDetail{
				TplID:      0,
				FieldShow:  field.FieldName,
				FieldLevel: 2,
				FieldSort:  m._getSort(i+1, j+1, 0),
				FieldType:  int(field.FieldType),
				FieldOpts:  utils.ToJson(field.FieldOpts),
				FieldExtra: "",
				CreatedAt:  now,
				UpdatedAt:  now,
			})
		}
	}
	return fields, nil
}
func (m *FpExamineTplConfig) _isLevelOneRoot(dt *FpExamineTplConfigDetail) bool {
	return dt.FieldSort%1000000 == 0
}
func (m *FpExamineTplConfig) ConvertShowModuleDetails(ctx context.Context, module []*FpExamineTplConfigDetail) ([]*pb.ExamineModuleDt, error) {
	var show = make([]*pb.ExamineModuleDt, 0)
	for _, v := range module {
		if m._isLevelOneRoot(v) {
			show = append(show, &pb.ExamineModuleDt{Name: v.FieldShow, FieldDetail: make([]*pb.ExamineFieldDt, 0)})
		} else {
			show[len(show)-1].FieldDetail = append(show[len(show)-1].FieldDetail, &pb.ExamineFieldDt{
				FieldName: v.FieldShow,
				FieldType: pb.ExamineFieldTpDf(v.FieldType),
				FieldOpts: v.ConvertFieldOpts(ctx, v.FieldOpts),
			})
		}
	}
	return show, nil
}
func (m *FpExamineTplConfigDetail) ConvertFieldOpts(ctx context.Context, opts string) []string {
	var o = []string{}
	if err := json.Unmarshal([]byte(opts), &o); err != nil {
		logger.Errorf(ctx, "ConvertFieldOpts err:%v. orgin:%s. m:%+v", err, opts, m)
	}
	return o
}

// FpExamineTplConfigDetail 质检打分表配置
type FpExamineTplConfigDetail struct {
	ID         uint64    `gorm:"primaryKey;column:id;type:bigint unsigned;not null"`
	TplID      uint64    `gorm:"index:idx_tid;column:tpl_id;type:bigint unsigned;not null;default:0"` // 质检打分表 - id
	FieldShow  string    `gorm:"column:field_show;type:varchar(512);not null;default:''"`             // 字段名称
	FieldLevel int8      `gorm:"column:field_level;type:tinyint;not null;default:0"`                  // 字段模块层级 1:模块标题,2模块下考核项
	FieldSort  int       `gorm:"column:field_sort;type:int unsigned;not null;default:0"`              // 字段展示层级圈定 100_100_100 三层级拼接数字
	FieldType  int       `gorm:"column:field_type;type:int;not null;default:0"`                       // 字段类型: 1:不需要(是模块x标题时); 10:下拉单选;20:下拉多选;30:多行文本;40:单行文本
	FieldOpts  string    `gorm:"column:field_opts;type:varchar(10240);not null;default:''"`           // 字段选项
	FieldExtra string    `gorm:"column:field_extra;type:text;default:null"`                           // 字段其他补充信息
	CreatedAt  time.Time `gorm:"column:created_at;type:datetime;not null"`                            // 创建时间
	UpdatedAt  time.Time `gorm:"column:updated_at;type:datetime;default:null"`                        // 更新时间
}

// FpExamineNoticeLog examine红点通知日志
type FpExamineNoticeLog struct {
	ID           uint64    `gorm:"primaryKey;column:id;type:bigint unsigned;not null"`                                 // 自增id
	MsgGroup     string    `gorm:"index:idx_deg;column:msg_group;type:varchar(32);not null;default:''"`                // 消息分类：质检结果等
	ExamineType  string    `gorm:"index:idx_deg;column:examine_type;type:varchar(32);not null;default:''"`             // 关联类型: discord审核:ExamineTypeDiscord; 工单审核:ExamineTypeTicket
	DetailID     int64     `gorm:"index:idx_deg;column:detail_id;type:bigint;not null;default:0"`                      // 对应质检单 id
	TaskID       uint64    `gorm:"index:idx_tid;column:task_id;type:bigint unsigned;not null;default:0"`               // 任务批次id
	TplID        uint64    `gorm:"index:idx_tu;column:tpl_id;type:bigint unsigned;not null;default:0"`                 // tpl id
	ToUser       string    `gorm:"column:to_user;type:varchar(64);not null;default:''"`                                // 收件人
	NoticeStatus int       `gorm:"column:notice_status;type:int;not null;default:0"`                                   // 当前状态： 未读，已读
	CreatedAt    time.Time `gorm:"index:idx_time;column:created_at;type:timestamp;not null;default:CURRENT_TIMESTAMP"` // 添加时间
	ReadAt       time.Time `gorm:"column:read_at;type:timestamp;not null;default:0000-00-00 00:00:00"`                 // 已读时间
	Operator     string    `gorm:"column:operator;type:varchar(32);not null;default:''"`                               // 操作人
	UpdatedAt    time.Time `gorm:"column:updated_at;type:timestamp;not null;default:CURRENT_TIMESTAMP"`                // 最后操作时间
}

type (
	ExamineDscOrderDoc struct {
		ExamineDscID             uint64             `gorm:"primaryKey;column:id;type:bigint unsigned;not null" json:"examine_dsc_id"`
		TaskID                   uint64             `gorm:"uniqueIndex:idx_tbu;column:task_id;type:bigint unsigned;not null;default:0" json:"task_id"`                        // 任务批次id
		TplID                    uint64             `gorm:"column:tpl_id;type:bigint unsigned;not null;default:0" json:"tpl_id"`                                              // tpl id
		Project                  string             `gorm:"column:project;type:varchar(32);not null;default:''" json:"project"`                                               // 所属项目
		BotID                    string             `gorm:"uniqueIndex:idx_tbu;column:bot_id;type:varchar(32);not null;default:''" json:"bot_id"`                             // 所属应用/机器人bot的 user_id
		DscUserID                string             `gorm:"uniqueIndex:idx_tbu;index:dsc_user_id;column:dsc_user_id;type:varchar(32);not null;default:''" json:"dsc_user_id"` // 用户 discord_user_id
		DscUserName              string             `json:"dsc_user_name"`                                                                                                    // the user display name, if it is set. For bots, this is the application name
		ChannelID                string             `gorm:"index:idx_channel;column:channel_id;type:varchar(32);not null;default:''" json:"channel_id"`                       // 用户私聊 channel id
		GenUID                   int64              `gorm:"column:gen_uid;type:bigint;not null;default:0" json:"gen_uid"'`                                                    // 生成质检单时: 玩家uid
		GenAccountID             string             `gorm:"column:gen_account_id;type:varchar(64);not null;default:''" json:"gen_account_id"`                                 // fpid
		GenSid                   string             `gorm:"column:gen_sid;type:varchar(64);not null;default:''" json:"gen_sid"`                                               // 生成质检单时: 服务器
		GenPayAll                int64              `gorm:"column:gen_pay_all;type:bigint;not null;default:0" json:"gen_pay_all"`                                             // 生成质检单时: 玩家累付金额
		GenPayLastThirtyDays     int64              `gorm:"column:gen_pay_last_thirty_days;type:bigint;not null;default:0" json:"gen_pay_last_thirty_days"`                   // 生成质检单时: 玩家最近30天付费金额
		GenReplyType             int64              `gorm:"column:gen_reply_type;type:bigint;not null;default:0" json:"gen_reply_type"`                                       // 生成质检单时: 当前沟通状态
		GenLastLogin             int64              `gorm:"column:gen_last_login;type:bigint;not null;default:0" json:"gen_last_login"`                                       // 生成质检单时: 玩家最近登录时间
		GenVipState              int8               `gorm:"column:gen_vip_state;type:tinyint;not null;default:1" json:"gen_vip_state"`                                        // vip状态:1非vip, 2vip
		GenVipLevel              int8               `gorm:"column:gen_vip_level;type:tinyint;not null;default:0" json:"gen_vip_level"`                                        // vip等级
		GenTags                  []uint32           `json:"gen_tags" json:"gen_tags"`                                                                                         // 生成质检单时 对应标签列表
		Inspector                string             `gorm:"column:inspector;type:varchar(32);not null;default:''" json:"inspector"`                                           // 质检员
		Status                   int                `gorm:"column:status;type:int;not null;default:0" json:"status"`                                                          // 当前状态：
		RelatedAccount           []string           `gorm:"column:related_account;type:varchar(512);not null;default:''" json:"related_account"`                              // 质检结果关联员工(被质检人s)
		NoticeAccount            []string           `gorm:"column:notice_account;type:varchar(512);not null;default:''" json:"notice_account"`                                // 通知质检结果的相关人
		FinalResult              int                `gorm:"column:final_result;type:int;not null;default:0" json:"final_result"`                                              // 质检结果: 通过；不通过
		FinalScore               int                `gorm:"column:final_score;type:int;not null;default:0" json:"final_score"`                                                // 质检结果：分数： 0～100
		FinalReason              string             `gorm:"column:final_reason;type:varchar(64);not null;default:''" json:"final_reason"`                                     // 质检结果：错误根源
		FinalDesc                string             `gorm:"column:final_desc;type:text;default:null" json:"final_desc"`                                                       // 质检结果：质检问题描述
		FinalResultModifyComment string             `gorm:"column:final_result_modify_comment;type:varchar(1024);not null;default:''" json:"final_result_modify_comment"`     // 质检结果修改： 修改原因备注
		CreatedAt                uint64             `gorm:"column:created_at;type:datetime;not null" json:"created_at"`                                                       // 创建时间
		FinishedAt               uint64             `gorm:"column:finished_at;type:datetime;not null" json:"finished_at"`                                                     // 完成时间
		FinalModifiedAt          uint64             `gorm:"column:final_modified_at;type:datetime;not null" json:"final_modified_at"`                                         // 最后修改时间
		ExamineFields            []*ExamineFieldDoc `json:"examine_fields"`                                                                                                   // 质检 字段定义
		UpdatedAt                uint64             `gorm:"column:updated_at;type:datetime;default:null" json:"updated_at"`
	}
	ExamineFieldDoc struct {
		FieldName  string   `json:"field_name"`
		FieldType  int      `json:"field_type"`
		FieldValue []string `json:"field_value"`
	}
)

func ExamineDscOrderAssignDoc(examineDscOrder *FpExamineDiscordDetail) *ExamineDscOrderDoc {
	genTags := make([]uint32, 0)
	for _, tag := range utils.JsonToUint32Slice(examineDscOrder.GenTags) {
		genTags = append(genTags, tag)
	}
	relateAccount := make([]string, 0)
	for _, ra := range utils.JsonToStrSlice(examineDscOrder.RelatedAccount) {
		relateAccount = append(relateAccount, ra)
	}
	noticeAccount := make([]string, 0)
	for _, na := range utils.JsonToStrSlice(examineDscOrder.NoticeAccount) {
		noticeAccount = append(noticeAccount, na)
	}
	fields := make([]*ExamineFieldDoc, 0)
	for _, fd := range examineDscOrder.FpExamineFields {
		var val []string
		switch pb.ExamineFieldTpDf(fd.FieldType) {
		case pb.ExamineFieldTpDf_ExamineFieldTpText, pb.ExamineFieldTpDf_ExamineFieldTpSel, pb.ExamineFieldTpDf_ExamineFieldTpTextarea:
			val = append(val, fd.FieldVal)
		default:
			for _, vv := range utils.JsonToStrSlice(fd.FieldVal) {
				val = append(val, vv)
			}
		}
		if len(val) > 0 {
			fields = append(fields, &ExamineFieldDoc{
				FieldName:  fd.FieldShow,
				FieldType:  fd.FieldType,
				FieldValue: val,
			})
		}
	}

	return &ExamineDscOrderDoc{
		ExamineDscID:             examineDscOrder.ID,
		TaskID:                   examineDscOrder.TaskID,
		TplID:                    examineDscOrder.TplID,
		Project:                  examineDscOrder.Project,
		BotID:                    examineDscOrder.BotID,
		DscUserID:                examineDscOrder.DscUserID,
		DscUserName:              examineDscOrder.DscUserName,
		ChannelID:                examineDscOrder.ChannelID,
		GenUID:                   examineDscOrder.GenUID,
		GenAccountID:             examineDscOrder.GenAccountID,
		GenSid:                   examineDscOrder.GenSid,
		GenPayAll:                examineDscOrder.GenPayAll,
		GenPayLastThirtyDays:     examineDscOrder.GenPayLastThirtyDays,
		GenReplyType:             examineDscOrder.GenReplyType,
		GenLastLogin:             examineDscOrder.GenLastLogin,
		GenVipState:              examineDscOrder.GenVipState,
		GenVipLevel:              examineDscOrder.GenVipLevel,
		GenTags:                  genTags,
		Inspector:                examineDscOrder.Inspector,
		Status:                   examineDscOrder.Status,
		RelatedAccount:           relateAccount,
		NoticeAccount:            noticeAccount,
		FinalResult:              examineDscOrder.FinalResult,
		FinalScore:               examineDscOrder.FinalScore,
		FinalReason:              examineDscOrder.FinalReason,
		FinalDesc:                examineDscOrder.FinalDesc,
		FinalResultModifyComment: examineDscOrder.FinalResultModifyComment,
		CreatedAt:                uint64(examineDscOrder.CreatedAt.Unix()),
		FinishedAt:               uint64(0),
		FinalModifiedAt:          uint64(0),
		ExamineFields:            fields,
		UpdatedAt:                utils.NowTimestamp(),
	}
}
func ExamineFieldAssignDoc(ctx context.Context, field *FpExamineFields) *ExamineFieldDoc {
	dt := &ExamineFieldDoc{
		FieldName:  field.FieldShow,
		FieldType:  field.FieldType,
		FieldValue: make([]string, 0),
	}
	switch pb.ExamineFieldTpDf(field.FieldType) {
	case pb.ExamineFieldTpDf_ExamineFieldTpText, pb.ExamineFieldTpDf_ExamineFieldTpSel, pb.ExamineFieldTpDf_ExamineFieldTpTextarea:
		dt.FieldValue = []string{field.FieldVal}
	default:
		var fv = make([]string, 0)
		if err := json.Unmarshal([]byte(field.FieldVal), &fv); err != nil {
			logger.Errorf(ctx, "ExamineFieldAssignDoc json.Unmarshal err:%v. fl:%+v", err, field)
			fv = []string{field.FieldVal}
		}
		dt.FieldValue = fv
	}
	return dt
}
