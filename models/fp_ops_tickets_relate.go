// Copyright 2021 funplus Authors. All Rights Reserved.
// @Description:
// @Author: Darcy
// @Date: 2022/7/27 16:47

package models

// FpOpsTicketsRelate 重新提交工单表
type FpOpsTicketsRelate struct {
	ID         uint64 `gorm:"primaryKey;column:id;type:bigint(20) unsigned;not null"`
	TicketID   uint64 `gorm:"unique;column:ticket_id;type:bigint(20) unsigned;not null"` // 工单ID
	RelateCat  uint32 `gorm:"column:relate_cat;type:int(11) unsigned;not null"`          // 推送问题分类ID
	RelateID   uint64 `gorm:"column:relate_id;type:int(20) unsigned;not null"`           // 重新提交工单ID
	FromOrigin uint8  `gorm:"column:from_origin"`
	CreatedAt  int64  `gorm:"column:created_at;type:bigint(20);not null"` // 创建时间
	UpdatedAt  int64  `gorm:"column:updated_at;type:bigint(20);not null"` // 更新时间
}
