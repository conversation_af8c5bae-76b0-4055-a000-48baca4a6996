package models

import "time"

// FpOpsStrategyTplLang 模板配置多语言表
type FpOpsStrategyTplLang struct {
	ID           uint32    `gorm:"primaryKey;column:id;type:int(11) unsigned;not null"`
	Project      string    `gorm:"uniqueIndex:idx_tpl;column:project;type:varchar(64);not null;default:''"` // 所属项目
	Lang         string    `gorm:"uniqueIndex:idx_tpl;column:lang;type:varchar(32);not null;default:''"`    // 语言
	FirstCatID   uint32    `gorm:"column:first_cat_id;type:int(11) unsigned;not null;default:0"`            // 分类ID
	ReplyContent string    `gorm:"column:reply_content;type:text;not null"`                                 // 回复模版表单
	Operator     string    `gorm:"column:operator;type:varchar(64);not null;default:''"`                    // 更新用户
	CreatedAt    time.Time `gorm:"column:created_at;type:datetime"`                                         // 创建时间
	UpdatedAt    time.Time `gorm:"column:updated_at;type:datetime"`                                         // 更新时间
}
