package models

var IssueDescriptionMap = []string{
	"issue description",
	"описание проблемы",
	"problembeschreibung",
	"description du problème",
	"문제 설명",
	"問題の説明",
	"descrizione problema",
	"问题描述",
	"問題描述",
	"opis problemu",
	"descrição do problema",
	"omschrijving van het probleem",
	"deskripsi masalah",
	"คำอธิบายปัญหา",
	"beskrivning av ärendet",
	"descripción del problema",
	"sorun tanımı",
	"وصف المشكلة",
	"mô tả vấn đề",
	"penerangan isu",
	"please describe your suggestion/complaint in detail",                          // 英语
	"просим подробно описать ваше предложение или жалобу",                          // 俄语
	"bitte erläutere deinen vorschlag/deine beschwerde so ausführlich wie möglich", // 德语
	"veuillez décrire votre suggestion ou votre plainte en détails",                // 法语
	"descrivi in dettaglio il tuo suggerimento/reclamo",                            // 意大利语
	"descreva sua sugestão/queixa detalhadamente",                                  // 葡萄牙语
	"omschrijf je suggestie/klacht in detail",                                      // 荷兰语
	"silakan jelaskan saran/komplain anda secara detail",                           // 印尼语
	"โปรดอธิบายข้อเสนอแนะ/การร้องเรียนของคุณอย่างละเอียด", // 泰语
	"describe tu sugerencia/queja con detalle",            // 西班牙语
	"sila huraikan cadangan/aduan anda secara terperinci", // 马来语
	"请详细描述您的建议/抱怨",
	"Przedstaw w szczegółach swoją skargę/sugestię",
	"請詳細描述您的建議/抱怨",
	"제안/불만 사항을 자세히 설명해 주세요", // 韩语
	"Beskriv ditt förslag/klagomål i detalj",
	"Lütfen öneri/şikayetini detaylarıyla anlat",
	"يرجى وصف اقتراحك/شكواك بالتفصيل",
	"Vui lòng mô tả chi tiết đề xuất/phàn nàn của bạn",
	"ご意見・お困り事のご相談の内容を具体的にご説明ください", // 日语
}
