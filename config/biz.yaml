gm:
    ss_global: https://ss-global-gm.kingsgroupgames.com/player/info?fpid=%s
    koa_global:
        2031: https://koa-global-gm.kingsgroupgames.com/index.php?method=loadPlayerInfo&class=player&params={"fpid":%s}
        2077: https://koa-global-gm.kingsgroupgames.com/index.php?method=loadPlayerInfo&class=player&params={"fpid":%s}
        2062: https://koa-global-gm.kingsgroupgames.com/index.php?method=loadPlayerInfo&class=player&params={"fpid":%s}
        2075: https://koa-cn-gm.kingsgroup.cn:555/index.php?method=loadPlayerInfo&class=player&params={"fpid":%s}
        2074: https://koa-forward-gm.kingsgroupgames.com:555/index.php?method=loadPlayerInfo&class=player&params={"fpid":%s}
    gog_global: https://gog-global-gm.kingsgroupgames.com/index.php?method=loadPlayerInfo&class=player&params={"fpid":%s}
    gog_cn: http://gog-cn-gm.ifunplus.cn:555/index.php?method=loadPlayerInfo&class=player&params={"fpid":%s}
    mo_global: http://mo-gm.kingsgroupgames.com/Home/Index
    mc_global: https://mc-global-gm.kingsgroupgames.com/index.php?method=loadPlayerInfo&class=player&params={"fpid":%s}
    mce_global: https://st-global-gm.kingsgroupgames.com/index.php?method=loadPlayerInfo&class=player&params={"fpid":%s}

allow_ext:
    image:
        ext:
            - jpg
            - jpeg
            - png
        size: 5242880
    video:
        ext:
            - avi
            - mov
            - rmvb
            - rm
            - flv
            - mp4
            - 3gp
        size: 52428800

i18n:
    - "config/i18n/zh_CN.toml"
    - "config/i18n/en_US.toml"
    - "config/i18n/zh_TW.toml"
    - "config/i18n/ar.toml"
    - "config/i18n/de.toml"
    - "config/i18n/es.toml"
    - "config/i18n/fr.toml"
    - "config/i18n/id.toml"
    - "config/i18n/it.toml"
    - "config/i18n/ja.toml"
    - "config/i18n/ko.toml"
    - "config/i18n/ms.toml"
    - "config/i18n/my.toml"
    - "config/i18n/nl.toml"
    - "config/i18n/pl.toml"
    - "config/i18n/pt.toml"
    - "config/i18n/ru.toml"
    - "config/i18n/sv.toml"
    - "config/i18n/th.toml"
    - "config/i18n/tr.toml"
    - "config/i18n/vi.toml"

ticket:
    evaluation_time: 604800

# 私域红点接口调用 约定 key
inner_notice_source_app_key:
    - "BjOY2OxjJJhVExR8stOU4xrkIpuOfgr" # 私域请求来源

export_header:
    TicketPoolExport:
        zh-cn:
            - "工单ID"
            - "玩家FPID"
            - "玩家UID"
            - "玩家昵称"
            - "累充金额"
            - "工单状态"
            - "处理类型"
            - "等待时长"
            - "处理时长"
            - "玩家输入内容"
            - "标签"
            - "系统标签"
            - "私域R级"
            - "问题分类"
            - "问题描述"
            - "客服回复"
            - "评分"
            - "NPS"
            - "渠道包"
            - "渠道号"
            - "服务器"
            - "语言"
            - "国家"
            - "游戏版本"
            - "设备型号"
            - "系统版本"
            - "处理人"
            - "创建时间"
            - "完结时间"
            - "已重开次数"
            - "玩家评语"

    DiscordPoolExport:
        zh-cn:
            - "玩家DC账号"
            - "玩家DC昵称"
            - "uid"
            - "服务器"
            - "游戏"
            - "状态"
            - "最近登录时间"
            - "累计付费金额"
            - "最近30天付费金额"
            - "VIP等级"
            - "标签"

    DiscordInteractDetailExport:
        zh-cn:
            - "游戏"
            - "交互日期"
            - "discord账号"
            - "discord昵称"
            - "uid"
            - "fpid"
            - "处理人"

    DiscordMessageCountDetailExport:
        zh-cn:
            - "游戏"
            - "日期"
            - "discord账号"
            - "discord昵称"
            - "uid"
            - "fpid"
            - "玩家信息数"
            - "处理人"
            - "客服消息数"

    DiscordCommuRecordExport:
        zh-cn:
            - "日期"
            - "游戏"
            - "uid"
            - "服务器"
            - "昵称"
            - "累付金额"
            - "沟通问题"
            - "问题类型"
            - "处理状态"
            - "备注"
            - "处理人"
    ExamineDscRecordExport:
        zh-cn:
            - "质检任务创建时间"
            - "质检任务完成时间"
            - "游戏"
            - "质检ID"
            - "质检状态"
            - "质检员"
            - "被检员"
            - "玩家DC账号"
            - "玩家UID"
            - "玩家服务器"
            - "玩家累计付费金额"
            - "标签"
            - "质检结果"
            - "质检分数"
            - "错误根源"
            - "字段定义"
    DiscodMessageTaskRecordExport:
        zh-cn:
            - "id"
            - "游戏"
            - "私信内容"
            - "处理状态"
            - "发送数量"
            - "成功数量"
            - "失败数量"
            - "创建人"
            - "创建时间"
            - "完成时间"

    DiscodMessageTaskDetailRecordExport:
        zh-cn:
            - "DC ID"
            - "昵称"
            - "uid"
            - "fpid"
            - "机器人"
            - "结果"
            - "送达时间"
            - "说明"

    SurveyDscDetailStatExport:
        zh-cn:
            - "游戏"
            - "日期"
            - "提交时间"
            - "Discord账号"
            - "Discord昵称"
            - "UID"
            - "FPID"
            - "处理人"
            - "维护人"
            - "经手人"
            - "发卷人"
            - "评星"
            - "评价对象"
            - "原因"
    LinePoolExport:
        zh-cn:
            - "玩家Line账号"
            - "玩家Line昵称"
            - "uid"
            - "服务器"
            - "Line频道ID"
            - "Line频道名称"
            - "游戏"
            - "状态"
            - "累计付费金额"
            - "最近30天付费金额"
            - "最近登录时间"
            - "VIP等级"
            - "标签"
    LineCommuRecordExport:
        zh-cn:
            - "日期"
            - "游戏"
            - "标签"
            - "uid"
            - "服务器"
            - "昵称"
            - "累付金额"
            - "沟通问题"
            - "问题类型"
            - "处理状态"
            - "备注"
            - "处理人"
    TicketQuestionExport:
        zh-cn:
            - "语料"
            - "语种"
            - "关联游戏"
            - "问题分类"
            - "答案"