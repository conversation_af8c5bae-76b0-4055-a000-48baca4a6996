RespSuccess = "Успех"
RespFail = "Неудача"
RespCheckSignFail = "Ошибка проверки контрольной суммы"
RespAuthFail = "Ошибка подтверждения"
RespInvalidParams = "Недопустимый параметр"
RespBindParamsFail = "Ошибка проверки параметра"
RespInnerException = "Внутренняя ошибка сервера"
RespInternalServer = "Внутренняя ошибка сервера"
RespDataUnExist = "Не удалось найти вашу историю обслуживания"

RespTicketRepeat = "Вы уже отправили вопрос в этой категории. Подождите результата."
RespProofClosedByTimeout = "Мы не получили от вас необходимую информацию. Ваше обращение помечено как завершенное. Вы можете направить свой запрос повторно!"
ClosedTicketForbiddenCommunicate = "К сожалению, этот запрос уже закрыт. Если вам еще нужна помощь, отправьте новый запрос. Спасибо за понимание."
TicketAppraiseRepeat = "Пожалуйста, воздержитесь от повторной оценки"

# v4.2.0
AutomateFilterNumberTip = "Не удалось отправить! Пожалуйста, используйте только цифры."
AutomateFilterTextTip = "Не удалось отправить! Пожалуйста, используйте только буквы."

RespSurveyExpired = "Опрос завершен, и ставить оценку не нужно!"
RespSurveyHasDone = "Опрос отправлен. Пожалуйста, не ставьте оценку несколько раз!"

NoAvailablePrivCard = "Не найдено доступных Карт защиты пополнения. Пожалуйста, проверьте ещё раз и повторите попытку."