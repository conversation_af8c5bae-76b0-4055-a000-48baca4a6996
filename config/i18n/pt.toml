RespSuccess = "Sucesso"
RespFail = "Falha"
RespAuthFail = "A verificação falhou"
RespCheckSignFail = "Falha na verificação de criptografia"
RespInvalidParams = "Parâmetro Inválido"
RespBindParamsFail = "Falha na verificação de parâmetro."
RespInnerException = "Erro Interno de Servidor"
RespInternalServer = "Erro Interno de Servidor"
RespDataUnExist = "Não foi possível encontrar o seu histórico de serviços"

RespTicketRepeat = "Você já enviou um problema dentro desta categoria. Aguarde os resultados."
RespProofClosedByTimeout = "Devido a não termos recebido as informações necessárias, este caso foi encerrado. Entre em contato conosco novamente!"
ClosedTicketForbiddenCommunicate = "O tíquete foi fechado. Se precisar de mais assistência, envie um novo tíquete. Agradecemos sua compreensão."
TicketAppraiseRepeat = "Evite enviar a mesma versão duas vezes"

# v4.2.0
AutomateFilterNumberTip = "Falha ao enviar! Insira apenas números."
AutomateFilterTextTip = "Falha ao enviar! insira apenas letras."

RespSurveyExpired = "O questionário expirou. Não precisa avaliar!"
RespSurveyHasDone = "Pesquisa enviada. Evite enviar mais de uma avaliação!"

NoAvailablePrivCard = "Nenhum Cartão de Proteção de Recarga disponível encontrado. Verifique novamente e repita."