RespSuccess = "Başarılı"
RespFail = "Başarısız"
RespCheckSignFail = "Denetim doğrulama işlemi başarısız old"
RespAuthFail = "Doğrulama işlemi başarısız oldu"
RespInvalidParams = "Geçersiz parametre"
RespBindParamsFail = "Parametre doğrulama işlemi başarısız oldu"
RespInnerException = "Dahili sunucu hatası"
RespInternalServer = "Dahili sunucu hatası"
RespDataUnExist = "Hizmet geçmişiniz bulunamadı"

RespTicketRepeat = "Bu kategoride daha önce bir sorun gönderdiniz. Lütfen sonucu bekleyin."
RespProofClosedByTimeout = "Gerekli bilgileri alamadığımız için bu hizmet talebi kapanmıştır. Bizimle tekrar iletişime geçebilirsiniz!"
ClosedTicketForbiddenCommunicate = "Üzgünüz, destek talebin kapatıldı. Daha fazla yardıma ihtiyacın varsa lütfen yeni bir destek talebi oluştur. Anlayışın için teşekkür ederiz."
TicketAppraiseRepeat = "Lütfen aynı değerlendirmeyi birden fazla kez göndermekten kaçın"

# v4.2.0
AutomateFilterNumberTip = "Gönderme işlemi başarısız oldu! Lütfen sadece sayı gir."
AutomateFilterTextTip = "Gönderme işlemi başarısız oldu! Lütfen sadece harf gir."

RespSurveyExpired = "Anketin süresi doldu. Puan vermene gerek yok."
RespSurveyHasDone = "Anket gönderildi. Lütfen birden fazla puan vermekten kaçın."

NoAvailablePrivCard = "Mevcut Bakiye Yükleme Koruma Kartı bulunamadı. Lütfen bir kez daha kontrol edip tekrar dene."