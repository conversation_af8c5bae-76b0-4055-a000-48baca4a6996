RespAuthFail = "Verification failed"
RespBindParamsFail = "Parameter verification failed"
RespCheckSignFail = "Checksum verification failed"
RespDataUnExist = "Couldn't find your service history"
RespFail = "Fail"
RespInnerException = "Internal server error"
RespInternalServer = "Internal server error"
RespInvalidParams = "Invalid parameter"
RespSuccess = "Success"

RespProofClosedByTimeout = "Because we did not receive the necessary information, this case has now been closed. You are welcome to contact us again!"
ClosedTicketForbiddenCommunicate = "Sorry, the ticket has been closed. If you need further assistance, please submit a new ticket. Thank you for your understanding."
RespTicketRepeat = "You've already submitted an issue under this category. Please wait for the result."
MixTransferNodeForBatch = "The data you want to operate in batches is not the same transfer node, please select the data of the same transfer node!"
TicketAppraiseRepeat = "Please refrain from submitting duplicate evaluations"

#  状态
Untreated = "Pending"
Done = "Completed"
Processing = "Processing"

# 工单来源
Player = "Player Submit"
NormalService = "Agent Submit"
VipService = "VIP Agent Submit"

# 节点
Pending = "Pending"
FirstTierProcessing = "First-tier agent processing"
FirstTierRefill = "First-tier agent refilling"
SecondTierProcessing = "Second-tier agent processing"
SecondTierAudit = "Audit area"
VipAgentRefill = "VIP agent refilling"
TicketResolved = "Resolve ticket"
TicketRefill = "Ticket refill"
TemporaryReply = "Temporary reply"
TicketClosed = "Ticket closed"
VipAgentProcessing = "VIP agent processing"
SecondTierRefill = "Second-tier agent refilling"
ThirdTierProcessing = "Third-tier agent processing"
ThirdTierAudit = "Audit area(Third Tier)"
FirstTierAdminProcessing = "Frontline Management Processing"
FirstTierAdminRefill = "Frontline Management Adding Information"
VipAgentResolved = "VIP agent resolved"

UserIsNotOnlineErr = "当前用户已离线"

# 流转事件
Tr = "Temporary reply"
Refill = "Ticket refill"
Close = "Close Ticket"
Resolved = "Completed"

# 优先级别
High = "High"
Low = "Low"
Middle = "Middle"

# language
EN = "English"
RU = "Russian"
DE = "German"
FR = "French"
KO = "Korean"
JA = "Japanese"
IT = "Italian"
ZH-CN = "Simplified Chinese"
ZH-TW = "Traditional Chinese"
PL = "Polish"
PT = "Portuguese"
NL = "Dutch"
ID = "Indonesian"
TH = "Thai"
SV = "Swedish"
ES = "Spanish"
TR = "Turkish"
AR = "Arabic"
VI = "Vietnamese"
MY = "Malay"

# 事件
Create = "Ticket Created"
Receipt = "Ticket Taken"
Transfered = "Ticket Transfered"
Allocation = "Ticket Distributed"
DoneCase = "Ticket Resolved"
Assign = "Ticket Assigned"
Emergency = "Priority Changed"
Remark = "Ticket Note Added"
Reply = "Ticket Replied"
Closed = "Ticket Closed"
Tag = "Tags operate"
Relate = "Push ticket"
Revoke = "Ticket retraction"
Reopen = "Ticket Reopened"

# 操作角色
FirstTierAgent = "First-Tier Agent"
SecondTierAgent = "Second-Tier Agent"
VIPAgent = "VIP Agent"
CreatorPlayer = "Player: %s"
CreatorAgent = "Agent: %s"
VIPCreatorAgent = "VIP Agent: %s"
ProcessLogPlayer = "Player"
ProcessLogSystem = "System"

# 统计
PendingAssignmentTickets = "Pending Assignment Tickets"
OpenTickets = "Open Tickets"
TicketsUnderReview = "Tickets under review"
LoginProblemStatis = "Login tickets -To be assigned"
AccountProblemStatis = "Account tickets -To be assigned"
RechargeProblemStatis = "Payment tickets -To be assigned"

# 操作日志
EventPriorityLog = "Agent「%s」changed the ticket's priority to 「%s」"
EventTransferedLog = "Agent「%s」assigned the ticket to 「%s」"
EventVIPTransferedLog = "VIP Agent「%s」assigned the ticket to 「%s」"
EventCreatedLog = "Player submitted a ticket"
EventAgentCreateLog = "「%s」created a ticket for player"
EventVipAgentCreateLog = "VIP Agent「%s」created a ticket for player"
EventDistributedLog = "System automatically assigned tickets to agent「%s」"
EventTakenLog = "Agent「%s」took the ticket"
EventRefillLog = "Player followed up and the ticket was assigned back to「%s」"
EventResolvedLog = "Agent「%s」resolved the ticket"
EventVIPResolvedAckLog = "VIP Agent「%s」confirmed the ticket"
EventSystemReplyLog = "Auto-response"
EventDupCatAddLog = "Addition failed! Template name duplicated."
EventDisableCatLog = "Operation failed! This template is in use in 'Issue Category Config'."
EventAssignedLog = "Agent「%s」assigned the ticket to agent「%s」"
EventNoteAddLog = "Agent「%s」added a note"
EventNoteVIPAddLog = "VIP Agent「%s」added a note"
EventRefillPlayerLog = "Agent「%s」assigned ticket back to players"
EventRepliedLog = "Agent「%s」reponsed to player"
EventCloseLog = "Agent「%s」closed the ticket"
EventTimeOutLog = "Player timed out-Ticket closed"
EventTagDelLog = "Tag deleted: Agent「%s」deleted tag(s): %s"
EventTagVIPDelLog = "Tag deleted: VIP Agent「%s」deleted tag(s): %s"
EventTagAddLog = "Tag added: Agent「%s」added tag(s): %s"
EventTagVIPAddLog = "Tag added: VIP Agent「%s」added tag(s): %s"
EventRelateLog = "Agent「%s」pushed a form「%s」to the player"
EventRevokeLog = "Agent 「%s」retracted the last response of ticket「%s」."
EventUpdateFailLog = "Ticket update failed, please try again"
EventReopenLog = "Ticket reopened by player and status returned to [being processed by customer support]"

# tips
TryAgainTip = "The operation is frequent. Please try again later"
NoAvailableTicketsTip = "No available tickets now"
AssigneeChangedTip = "Assignee changed"
ResolvedCannotOpTip = "This ticket is resolved, cannot operate."
WorkgroupsConflictTip = "Failed ! Linked workgroups conflict !"
FillMultilingualTextTip = "Please enter the multilingual text"
FillFormMultilingualTextTip = "Please fill the form and multilingual text"
FaqCategoryTip = "The category is referenced in FAQ, please unlink and delete it."
SystemTagsTip = "System tags,don't modify"
NoPermissionTip = "No permission. Please contact the administrator."
RepeatDataTip = "Repeate data, please have a check"
ExceededLimit = "The number of available orders exceeded, order failed!"
RecordJumpLimit = "You may only navigate between the first 10,000 tickets. Please specify more search criteria."
RevokeTicketRepeatTip = "Failed! Response retraction has been done to this ticket."
TicketNotFoundTip = "Failed! This ticket number doesn't exist."
TicketRevokeExistTip = "Failed! There is no response can be retracted for this ticket."
TicketReassignReplyFailTip = "Operation failed! The auto-reply ticket does not support assignment."
TicketReopenReplyFailTip = "Operation failed! The auto-reply ticket does not support reopen."

# 评价
OneStar = "1 star"
SecondStar = "2 star"
ThreeStar = "3 star"
FourStar = "4 star"
FiveStar = "5 star"

# 评价标签
TheService = "Dissatisfaction with service"
TheOutcome = "Dissatisfaction with the outcome"
ProcessingSpeed = "Dissatisfaction with processing speed"
FastProcessingSpeed = "Fast processing speed"
SatisfiedResult = "Satisfied with the result"
SatisfiedService = "Satisfied with customer service"

# 补填状态
PlayerRefillSt = "Player finished refilling"
FirstTierRefillSt = "First-tier agent finished refilling"
VipRefillSt = "VIP agent finished refilling"
SecondTierRefillSt = "Second-tier agent finished refilling"
FirstTierAdminRefillSt = "Tickets Added by Frontline Management"

To = "To"

# v4.2.0
AutomateFilterNumberTip = "Failed to submit! Please enter numbers only."
AutomateFilterTextTip = "Failed to submit! Please enter letters only."

# v5.0.0
ExamineTplForbiddenTip = "This configuration is referenced and cannot be modified/deleted"
ExamineOrderModifiedOnlyOnceTip = "A Quality check task can only be modified once"
ExamineOrderInspectorNoMatchTip = "Inspector Mismatch"

ExamineFieldTpSel = "Dropdown Single Select"
ExamineFieldTpMulSel = "Dropdown Single Select"
ExamineFieldTpText = "Single Line Text"
ExamineFieldTpTextarea = "Text Area Text"

ExamineTaskStateDfInit = "Not Started"
ExamineTaskStateDfDoing = "In Progress"
ExamineTaskStateDfSuccess = "Successful"
ExamineTaskStateDfFail = "Failed"

ExamineTaskGroupDfTicket = "Ticket Database"
ExamineTaskGroupDfDiscord = "Discord Database"

ExamineStateDfInit = "In Progress"
ExamineStateDfDoing = "In Progress-中间过程"
ExamineStateDfSuccess = "Completed"

ExamineFinalResultDfPass = "Pass"
ExamineFinalResultDfFail = "Fail"

ExamineNoticeMsgGroupDfResult = "Quality Check Result"

ExamineNoticeStateDfUnread = "Unread"
ExamineNoticeStateDfRead = "Read"

ReasonKnowledgeLack = "Knowledge - Lack of Game Knowledge"
ReasonProcessLack = "Knowledge - Insufficient Workflow Mastery"
ReasonResearchLack = "Skill - Inadequate Case Investigation"
ReasonCommuSkillsLack = "Skill - Inadequate Communication Skills"
ReasonToolLack = "Skill - Unfamiliar with Tools"
ReasonServiceLack = "Behavior - Inadequate Service Awareness"
ReasonAttitudeLack = "Behavior - Apathetic Attitude"
ReasonInternalLimit = "Internal - Knowledge Base/Workflow/Tool Limitations"
ReasonExternalLimit = "External - Player Expectations/Language Barrier"

# 新工单
TkStageNew = "Waiting for ticket"
TkStageNewForAgent = "Awaiting processing"
TkStageAgentReplied = "Processing-Waiting for player reply"
TkStageWaitingForAgent = "Processing-Player has replied"
TkStageAgentResolved = "Timeout-Closed""
TkStageAgentReopen = "Reopen"
TkStageAgentRejected = "Ticket rejected-Closed"
TkStageAgentCompleted = "Completed"

Loading = "Loading scene"
AccountBan = "Blacklist scene"
InGame = "In-game"

NpsScoreZero = "Zero score"
NpsOneScore = "One score"
NpsTwoScore = "Two scores"
NpsThreeScore = "Three scores"
NpsFourScore = "Four scores"
NpsFiveScore = "Five scores"

TkPoolSortWaitTm = "Wait time"
TkPoolSortCreateTm = "Create time"
TkPoolSortRecharge = "Recharge amount"

# discord
WaitServiceReply = "Wait service reply"
ServiceHasReplied = "Service has replied"

Male = "male"
Female = "female"

PrimarySchool = "primary school"
JuniorHighSchool = "junior high school"
SeniorHighSchool = "senior high school"
UnderGraduate = "under graduate"
Graduate = "graduate"
Doctor = "doctor"
Postdoc = "postdoc"
Others = "others"

Married = "married"
Single = "single"
Divorced = "divorced"
Widowed = "widowed"

NoChild = "no children"
OneChild = "one child"
TwoKids = "two kids"
ThreeKids = "three kids"
MoreThanThreeKids = "more than three kids"

NonVip = "Non VIP"
Vip = "VIP"

GameConsultation = "game consultation"
GameSuggestion = "game suggestion"
GameException = "game exception"
ServerMatchAndServerMerge = "server match & server merge"
ComplaintOrNegativeFeedback = "complaint or negative feedback"
OtherQuestion = "other question"

UnderProcess = "awaiting processing"
Finished = "finished"

GreenChannelUser = "green channel ticket"
PrivateZoneUser = "private zone ticket"
ElfinTypeUser = "Elfin type ticket"

PolishFriendlyLabel = "more friendly"
PolishConciseLabel = "more concise"
PolishFormalLabel = "more formal"
PolishPoliteLabel = "more polite"
PolishCustomizeLabel = "customize"

defaultAiPreReply = "Sorry, there is no suitable corpus data available."

ProcessStatusInit = "Not Started"
ProcessStatusDoing = "Processing"
ProcessStatusFail = "Failed"
ProcessStatusSuccess = "Succeeded"

DscTaskDetailStatusDoing = "Not Sent"
DscTaskDetailStatusSuccess = "Success"
DscTaskDetailStatusFail = "Failed"

DcPoolSortWaitTm = "Wait time"
DcPoolSortLastReplyTm = "last reply time"
DcPoolSortpaidAmount = "paid amount"

EffectiveDay3 = "three days"
EffectiveDay5 = "five days"
EffectiveDay7 = "seven days"

SurveyPushCycleEveryWeek = "every week"
SurveyPushCycleEveryTwoWeeks = "every two weeks"
SurveyPushCycleEveryMonth = "every month"

SurveyQstProduct = "产品题"
SurveyQstService = "服务题"

SurveyStatDay = "日期"
SurveyStatGame = "游戏"
SurveyStatAccount = "客服"

RespSurveyExpired = "Survey expired. No need to rate!"
RespSurveyHasDone = "Survey submitted. Please avoid giving multiple ratings!"

SVIPYes = "Yes"
SVIPNo = "No"
SearchTypeSubmitUser = "Ticket Submitter"
SearchTypeQuestionUser = "User With Issue"
UserTypeLongVipUser = "Long-Term VIP"
UserTypeLimitTimeUser = "Short-Term VIP"
UserTypePaidUser = "Paying User"
UserTypeRegularUser = "Normal User"

SolveTypeManualTicket = "manual handling"
SolveTypeAutoReplyTicket = "Template Autoresponder"
SolveTypeInvalidTicket = "AI Response - Invalid Ticket"
SolveTypeStrategyTicket = "AI Response - Grading Ticket"

ProcessTicketStatusDoing = "Training"
ProcessTicketStatusFail = "Training failed"
ProcessTicketStatusSuccess = "Training success"


NoAvailablePrivCard = "No available Top-up Protection Card found. Please double check and try again."