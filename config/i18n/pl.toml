RespSuccess = "Powodzenie"
RespFail = "Niepowodzenie"
RespCheckSignFail = "Weryfikacja sumy kontrolnej nieudana"
RespAuthFail = "Weryfikacja nieudana"
RespInvalidParams = "Nieprawidłowy parametr"
RespBindParamsFail = "Weryfikacja parametrów nieudana"
RespInnerException = "Wewnętrzny błąd serwera"
RespInternalServer = "Wewnętrzny błąd serwera"
RespDataUnExist = "Nie mogliśmy znaleźć twojej historii usług"

RespTicketRepeat = "Zgłoszenie w tej kategorii zostało już przez ciebie przesłane. Zaczekaj na wyniki naszych działań."
RespProofClosedByTimeout = "Ponieważ nie otrzymaliśmy niezbędnych informacji, ta sprawa została zamknięta. Możesz skontaktować się z nami ponownie!"
ClosedTicketForbiddenCommunicate = "Niestety to zgłoszenie zostało zamknięte. Jeśli nadal potrzebujesz pomocy, prześlij kolejne zgłoszenie. Dziękujemy za wyrozumiałość."
TicketAppraiseRepeat = "Prosimy nie duplikować zgłoszeń"

# v4.2.0
AutomateFilterNumberTip = "Nie udało się przesłać! Wpisz tylko cyfry."
AutomateFilterTextTip = "Nie udało się przesłać! Wpisz tylko litery."

RespSurveyExpired = "Ankieta wygasła, nie musisz zgłaszać oceny."
RespSurveyHasDone = "Ankieta przesłana. Prosimy nie przesyłać kolejnej oceny."

NoAvailablePrivCard = "Nie znaleziono dostępnych kart z zabezpieczeniem doładowania. Sprawdź jeszcze raz i spróbuj ponownie."