RespSuccess = "Éxito"
RespFail = "Fail"
RespCheckSignFail = "Error en la función de verificación"
RespAuthFail = "Error de verificación"
RespInvalidParams = "Los parámetros no son válidos"
RespBindParamsFail = "Error de verificación de los parámetros"
RespInnerException = "Error interno del servidor"
RespInternalServer = "Error interno del servidor"
RespDataUnExist = "No se ha encontrado tu historial del servicio"

RespTicketRepeat = "Ya has enviado una consulta en esta categoría. Espera al resultado."
RespProofClosedByTimeout = "Como no hemos recibido la información necesaria, se ha cerrado este caso. ¡Puedes volver a contactar con nosotros cuando quieras!"
ClosedTicketForbiddenCommunicate = "Lo sentimos, la solicitud se ha cerrado. Si necesitas de más asistencia, envía una nueva solicitud. Gracias por tu comprensión."
TicketAppraiseRepeat = "Por favor, evita enviar evaluaciones duplicadas"

# v4.2.0
AutomateFilterNumberTip = "¡El envío falló! Por favor, introduce únicamente números."
AutomateFilterTextTip = "¡El envío falló! Por favor, introduce únicamente letras."

# 新工单
TkStageNew = "待接单"
TkStageNewForAgent = "待处理"
TkStageAgentReplied = "处理中-待玩家回复"
TkStageWaitingForAgent = "处理中-玩家已回复"
TkStageAgentResolved = "超时关闭"
TkStageAgentReopen = "重开"
TkStageAgentRejected = "拒单关闭"
TkStageAgentCompleted = "已完成"

Loading = "加载场景"
AccountBan = "拉黑场景"
InGame = "游戏内"

ZeroStar = "0 星"
OneStar = "1星"
SecondStar = "2星"
ThreeStar = "3星"
FourStar = "4星"
FiveStar = "5星"

NpsScoreZero = "0分"
NpsOneScore = "1分"
NpsTwoScore = "2分"
NpsThreeScore = "3分"
NpsFourScore = "4分"
NpsFiveScore = "5分"

RespSurveyExpired = "La encuesta ha caducado. ¡No es necesario que nos califiques!"
RespSurveyHasDone = "Encuesta enviada. ¡Evita ingresar múltiples calificaciones!"

NoAvailablePrivCard = "No se encontró ninguna tarjeta de protección de recarga disponible. Revisa e inténtalo de nuevo."