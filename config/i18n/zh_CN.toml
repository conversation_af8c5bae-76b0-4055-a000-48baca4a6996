# 系统
RespFail = "失败"
RespSuccess = "成功"

RespAuthFail = "验证失败"
RespBindParamsFail = "参数解析校验失败"
RespCheckSignFail = "参数校验失败"
RespDataUnExist = "未查询到您的记录"

RespInnerException = "服务器内部错误"
RespInternalServer = "服务器内部错误"
RespInvalidParams = "参数不合规"

# 业务

#  状态
Untreated = "待处理"
Done = "已完成"
Processing = "处理中"

# 工单来源
NormalService = "客服代提交"
Player = "玩家自助提交"
VipService = "VIP客服代提交"

#工单节点
Pending = "待处理"
FirstTierProcessing = "一线处理中"
FirstTierRefill = "一线补填中"
SecondTierProcessing = "二线处理中"
SecondTierAudit = "二线审核区"
VipAgentRefill = "VIP补填中"
VipAgentProcessing = "VIP处理中"
TicketResolved = "处理完成"
TicketClosed = "工单关闭"
TemporaryReply = "暂时回复"
TicketRefill = "打回补填"
SecondTierRefill = "二线补填中"
ThirdTierProcessing = "三线处理中"
ThirdTierAudit = "三线审核区"
FirstTierAdminProcessing = "一线管理处理中"
FirstTierAdminRefill = "一线管理补填中"
VipAgentResolved = "VIP处理完成"

# 流转事件
Tr = "暂时回复"
Refill = "打回补填"
Close = "关闭工单"
Resolved = "处理完成"

# 优先级
High = "高"
Low = "低"
Middle = "中"

# 满意度
OneStar = "一星"
SecondStar = "两星"
ThreeStar = "三星"
FourStar = "四星"
FiveStar = "五星"

# 评价标签
TheService = "对服务态度不满"
TheOutcome = "对处理结果不满"
ProcessingSpeed = "对处理速度不满"
FastProcessingSpeed = "处理速度快"
SatisfiedResult = "对处理结果满意"
SatisfiedService = "对客服满意"

PlayerRefillSt = "玩家已补填单"
FirstTierRefillSt = "一线已补填单"
SecondTierRefillSt = "二线已补填单"
VipRefillSt = "VIP已补填单"
FirstTierAdminRefillSt = "一线管理已补填单"

UserIsNotOnlineErr = "当前用户已离线"

# 语言
EN = "英语"
RU = "俄语"
DE = "德语"
FR = "法语"
KO = "韩语"
JA = "日语"
IT = "意语"
ZH-CN = "简体中文"
ZH-TW = "繁体中文"
PL = "波兰语"
PT = "葡语"
NL = "荷兰语"
ID = "印尼语"
TH = "泰语"
NO = "挪威语"
SV = "瑞典语"
ES = "西班牙语"
TR = "土耳其语"
AR = "阿拉伯语"
VI = "越南语"
MY = "马来语"

# 事件
Create = "工单创建"
Receipt = "工单接单"
Transfered = "工单流转"
Allocation = "工单分配"
DoneCase = "工单结案"
Assign = "工单指派"
Emergency = "调整紧急度"
Remark = "工单备注"
Reply = "工单回复"
Closed = "工单关闭"
Tag = "标签操作"
Relate = "推送工单"
Revoke = "工单撤回"
Reopen = "工单重开"

# 操作角色
FirstTierAgent = "一线客服"
SecondTierAgent = "二线客服"
VIPAgent = "VIP客服"

# 统计
PendingAssignmentTickets = "待接入工单"
OpenTickets = "处理中工单"
TicketsUnderReview = "审核中工单"
LoginProblemStatis = "登录问题待接入工单"
AccountProblemStatis = "账号问题待待接入工单"
RechargeProblemStatis = "充值问题待接入工单"

# 操作日志
EventCreatedLog = "玩家提交表单自创建工单"
EventAgentCreateLog = "「%s」提交表单创建工单"
EventVipAgentCreateLog = "VIP客服「%s」提交表单创建工单"
EventTakenLog = "客服「%s」主动接单"
EventAssignedLog = "客服「%s」将工单指派给客服「%s」"
EventPriorityLog = "客服「%s」将工单紧急度调至「%s」"
EventTransferedLog = "客服「%s」将工单流转到「%s」"
EventVIPTransferedLog = "VIP客服「%s」将工单流转到「%s」"
EventDistributedLog = "系统自动分单给一线客服「%s」"
EventRefillLog = "玩家举证并流转回「%s」"
EventResolvedLog = "客服「%s」操作结单"
EventVIPResolvedAckLog = "VIP客服「%s」确认结单"
EventSystemReplyLog = "系统自动回复"
EventDupCatAddLog = "添加失败！模板名称相同"
EventDisableCatLog = "操作失败！该模板在问题分类处已被引用"
EventNoteAddLog = "客服「%s」添加备注"
EventNoteVIPAddLog = "VIP客服「%s」添加备注"
EventRefillPlayerLog = "客服「%s」流转回玩家补填"
EventRepliedLog = "客服「%s」回复玩家"
EventCloseLog = "客服「%s」关闭工单"
EventTimeOutLog = "玩家超时-关闭工单"
EventTagDelLog = "工单删除标签：客服「%s」删除标签,标签内容：%s"
EventTagVIPDelLog = "工单删除标签：VIP客服「%s」删除标签,标签内容：%s"
EventTagAddLog = "工单新增标签：客服「%s」新增标签，标签内容：%s"
EventTagVIPAddLog = "工单新增标签：VIP客服「%s」新增标签，标签内容：%s"
EventRelateLog = "客服「%s」推送表单「%s」给玩家"
EventRevokeLog = "客服「%s」将工单「%s」C端回复玩家的最后一条消息撤回"
EventUpdateFailLog = "工单更新失败，请重试"
EventReopenLog = "玩家重开并流转回「一线处理中」"

CreatorPlayer = "玩家: %s"
CreatorAgent = "客服: %s"
VIPCreatorAgent = "VIP客服: %s"
ProcessLogPlayer = "玩家"
ProcessLogSystem = "系统"

To = "至"

# tips
TryAgainTip = "操作频繁,请稍后重试"
NoAvailableTicketsTip = "辛苦了，目前暂无需要处理的工单"
AssigneeChangedTip = "该工单已变更处理人"
ResolvedCannotOpTip = "该工单已结单,无法操作"
WorkgroupsConflictTip = "操作失败！人员关联的工作组冲突！"
FillMultilingualTextTip = "请输入多语言预料"
FillFormMultilingualTextTip = "请填写表单和多语言内容"
FaqCategoryTip = "该问题分类在智能客服答案配置处被引用，请先解除引用关系，再删除该分类"
SystemTagsTip = "系统标签,不能修改～"
NoPermissionTip = "暂无数据权限,请联系管理员配置对应技能组角色～"
MixTransferNodeForBatch = "暂不支持混合流转节点的批量操作，请选择属于当前工作台的相同流转节点的工单~"
RepeatDataTip = "数据重复，请检查"
ExceededLimit = "超出可接单数量，接单失败！"
RespProofClosedByTimeout = "由于您超时未补充信息，当前问题已关闭，欢迎您再次咨询！"
ClosedTicketForbiddenCommunicate = "很抱歉，当前工单已关闭，如需进一步沟通，可重新提交工单联系我们，感谢您的理解。"
RespTicketRepeat = "该问题分类下，您已提交过问题。请耐心等待处理结果"
RecordJumpLimit = "抱歉, 工单列表仅支持前10000条内跳页，请细化查询条件"
RevokeTicketRepeatTip = "操作失败！该工单已操作过撤回。"
TicketNotFoundTip = "操作失败！该工单号不存在。"
TicketRevokeExistTip = "操作失败！该工单没有可以撤回的内容。"
TicketAppraiseRepeat = "请勿重复提交评价信息"
TicketReassignReplyFailTip = "操作失败！自动回复单不支持指派"
TicketReopenReplyFailTip = "操作失败！自动回复单不支持重开"

# v4.0.0
RespAutomateDisableTip = "该分类已被绑定使用，不允许禁用"
RespAutomateForbiddenEditTip = "该类型已被引用，不允许修改"
BaseReferGroupInner = "基准接口"
BaseReferGroupRemote = "远程调用"

AutoOut1 = "否"
AutoOut2 = "是"

ModuleGroupStart = "开始组件"
ModuleGroupEnd = "结束组件"
ModuleGroupText = "富文本组件"
ModuleGroupTicket = "工单组件"
ModuleGroupUserInput = "玩家输入-判断组件"
ModuleGroupMapParam = "映射参数-判断组件"
ModuleGroupReferDefine = "接口封装-判断组件"
ProcessNodeRepeatTip = "流程节点数据校验失败，请排查"
ProcessNodeConnectorRepeatTip = "流程节点连接数据校验失败，请排查"
ProcessStartNodeCheckFailTip = "流程开始节点连接数据校验失败，请排查"
ProcessEndNodeCheckFailTip = "流程部分节点连接数据校验失败，请排查"
ProcessMapNodeCheckFailTip = "流程存在节点出参连接数据校验失败，请排查"
RespAutomateForbiddenEditTip2 = "被引用配置，不允许修改部分敏感字段"
RespAutomateDisableTip2 = "关联子分类为禁用状态，不允许启用"

# v4.2.0
AutomateFilterNumberTip = "提交失败！请输入数字"
AutomateFilterTextTip = "提交失败！请输入文字"

# v5.0.0
ExamineTplForbiddenTip = "该配置被引用过，不允许修改/删除"
ExamineOrderModifiedOnlyOnceTip = "质检任务只允许修改一次"
ExamineOrderInspectorNoMatchTip = "质检员不匹配"

ExamineFieldTpSel = "下拉单选"
ExamineFieldTpMulSel = "下拉多选"
ExamineFieldTpText = "单行文本"
ExamineFieldTpTextarea = "多行文本"

ExamineTaskStateDfInit = "未开始"
ExamineTaskStateDfDoing = "进行中"
ExamineTaskStateDfSuccess = "成功"
ExamineTaskStateDfFail = "失败"

ExamineTaskGroupDfTicket = "工单库"
ExamineTaskGroupDfDiscord = "DC库"

ExamineStateDfInit = "进行中"
ExamineStateDfDoing = "处理中-中间过程"
ExamineStateDfSuccess = "已完成"

ExamineFinalResultDfPass = "通过"
ExamineFinalResultDfFail = "不通过"

ExamineNoticeMsgGroupDfResult = "质检结果"

ExamineNoticeStateDfUnread = "未读"
ExamineNoticeStateDfRead = "已读"

ReasonKnowledgeLack = "知识-游戏知识缺乏"
ReasonProcessLack = "知识-流程掌握不够"
ReasonResearchLack = "技能-对case的调查研究不够充分"
ReasonCommuSkillsLack = "技能-语言沟通技巧不足"
ReasonToolLack = "技能-工具不熟"
ReasonServiceLack = "行为-服务意识不够"
ReasonAttitudeLack = "行为-态度敷衍"
ReasonInternalLimit = "内部-知识库/流程/工具限制"
ReasonExternalLimit = "外部-玩家想法/语言不通"

# 新工单
TkStageNew = "待接单"
TkStageNewForAgent = "待处理"
TkStageAgentReplied = "处理中-待玩家回复"
TkStageWaitingForAgent = "处理中-玩家已回复"
TkStageAgentResolved = "超时关闭"
TkStageAgentReopen = "重开"
TkStageAgentRejected = "拒单关闭"
TkStageAgentCompleted = "已完成"

Loading = "加载场景"
AccountBan = "拉黑场景"
InGame = "游戏内"

NpsScoreZero = "0分"
NpsOneScore = "1分"
NpsTwoScore = "2分"
NpsThreeScore = "3分"
NpsFourScore = "4分"
NpsFiveScore = "5分"

TkPoolSortWaitTm = "等待时长"
TkPoolSortCreateTm = "创建时间"
TkPoolSortRecharge = "充值金额"

# discord
WaitServiceReply = "待回复"
ServiceHasReplied = "已回复"

Male = "男"
Female = "女"

PrimarySchool = "小学"
JuniorHighSchool = "初中"
SeniorHighSchool = "高中"
UnderGraduate = "本科"
Graduate = "研究生"
Doctor = "博士"
Postdoc = "博士后"
Others = "其他"

Married = "已婚"
Single = "未婚"
Divorced = "离异"
Widowed = "丧偶"

NoChild = "未育"
OneChild = "1孩"
TwoKids = "2孩"
ThreeKids = "3孩"
MoreThanThreeKids = "3孩以上"

NonVip = "非VIP"
Vip = "VIP"

GameConsultation = "游戏咨询"
GameSuggestion = "游戏建议"
GameException = "游戏异常"
ServerMatchAndServerMerge = "服务器匹配&合服"
ComplaintOrNegativeFeedback = "抱怨/负面反馈"
OtherQuestion = "其他问题"

UnderProcess = "处理中"
Finished = "已完成"

GreenChannelUser = "绿色通道工单"
PrivateZoneUser = "私域渠道工单"
ElfinTypeUser = "精灵渠道工单"
PrivateZoneCard = "私域权益工单"

PolishFriendlyLabel = "更友好"
PolishConciseLabel = "更简洁"
PolishFormalLabel = "更官方"
PolishPoliteLabel = "更礼貌"
PolishCustomizeLabel = "自定义"

defaultAiPreReply = "抱歉，暂无合适的语料数据"

ProcessStatusInit = "未开始"
ProcessStatusDoing = "处理中"
ProcessStatusFail = "处理失败"
ProcessStatusSuccess = "处理成功"

DscTaskDetailStatusDoing = "未发送"
DscTaskDetailStatusSuccess = "成功"
DscTaskDetailStatusFail = "失败"

IsNull = "空白"
IsIn = "包含"
IsNotIn = "不包含"
IsAll = "全部"

DcPoolSortWaitTm = "等待时长"
DcPoolSortLastReplyTm = "最后回复时间"
DcPoolSortpaidAmount = "累付金额"

EffectiveDay3 = "三天"
EffectiveDay5 = "五天"
EffectiveDay7 = "七天"

SurveyPushCycleEveryWeek = "每周"
SurveyPushCycleEveryTwoWeeks = "每双周"
SurveyPushCycleEveryMonth = "每月"

SurveyQstProduct = "产品题"
SurveyQstService = "服务题"

SurveyStatDay = "日期"
SurveyStatGame = "游戏"
SurveyStatAccount = "客服"

RespSurveyExpired = "当前问卷已过期，不再需要评价～"
RespSurveyHasDone = "当前问卷已提交，请勿重复评价～"

SVIPYes = "是"
SVIPNo = "否"
SearchTypeSubmitUser = "提单用户"
SearchTypeQuestionUser = "问题用户"
UserTypeLongVipUser = "长期VIP"
UserTypeLimitTimeUser = "限时VIP"
UserTypePaidUser = "付费用户"
UserTypeRegularUser = "普通用户"

SolveTypeManualTicket = "人工处理"
SolveTypeAutoReplyTicket = "模板自动回复"
SolveTypeInvalidTicket = "AI回复-无效单"
SolveTypeStrategyTicket = "AI回复-分级单"

ProcessTicketStatusDoing = "训练中"
ProcessTicketStatusFail = "训练失败"
ProcessTicketStatusSuccess = "训练成功"

NoAvailablePrivCard = "无有效充值无忧卡，请确认后重试。"

