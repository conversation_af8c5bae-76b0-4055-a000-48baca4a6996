app:
    name: "ops-ticket-api.funplus.com"
    env: "prod"
    debug: false
    version: "v1.0.0"
    environment: "internal"
    LogMode: 4
    lang: "zh-cn"

server:
    addr: ":9000"

db:
    ops_ticket:
        master: "root:oLhYVo9tB597@tcp(cs-global.cluster-c6h1gwzealou.us-west-2.rds.amazonaws.com:3306)/fp_ops_ticket_new?charset=utf8mb4&parseTime=True&loc=Local"
        slaver: "root:oLhYVo9tB597@tcp(cs-global.cluster-ro-c6h1gwzealou.us-west-2.rds.amazonaws.com:3306)/fp_ops_ticket_new?charset=utf8mb4&parseTime=True&loc=Local"
    ops_ticket_old:
        master: "root:oLhYVo9tB597@tcp(cs-global.cluster-c6h1gwzealou.us-west-2.rds.amazonaws.com:3306)/fp_ops_ticket?charset=utf8mb4&parseTime=True&loc=Local"
        slaver: "root:oLhYVo9tB597@tcp(cs-global.cluster-ro-c6h1gwzealou.us-west-2.rds.amazonaws.com:3306)/fp_ops_ticket?charset=utf8mb4&parseTime=True&loc=Local"
    max_open_conn: 50
    max_idle_conn: 30
    max_life_time: 10

es:
    node:
        - "https://ops-tools-elastic-prod.funplus.com"
    user: "elastic"
    pwd: "m3927jNFYDVWwaL*"
    index:
        ticket: "fp_ops_new_tickets_global"
        history: "fp_ops_new_tickets_history"
dsces:
    node:
        - "https://user-platform-all-games.kingsgroupgames.com"
    user: "elastic"
    pwd: "Ff1Wb35TW4150thgNJ8Y7gg5@"
    index:
        dscindex: "ops_dsc_user_global_prod"

examinees:
    node:
        - "https://user-platform-all-games.kingsgroupgames.com"
    user: "elastic"
    pwd: "Ff1Wb35TW4150thgNJ8Y7gg5@"
    index:
        examine_dsc_index: "ops_examine_dsc_prod"

line_es:
    node:
        - "https://user-platform-all-games.kingsgroupgames.com"
    user: "elastic"
    pwd: "Ff1Wb35TW4150thgNJ8Y7gg5@"
    index:
        line_index: "ops_line_user_prod"

openai:
    type: "azure" # openai  azure
    token: "************************************************************************************************************************************" # openai token
    azure_apikey: "sk-ZuDJzRrq8CVXwfNk496e9f1bE056451dA25f10B8473c34C9"
    tag_azure_apikey: "sk-XpaGuWOPEyidOcNUC3F6D1Ae04894815A2Cf66Be783f5273"
    azure_endpoint: "http://oneapi.funplus.com/v1"


redis:
    addr: "cs-redis.6vjslb.ng.0001.usw2.cache.amazonaws.com:6379"
    password: ""
    db: 2

s3:
    access_key: "********************"
    access_key_secret: "ZGCBasdcsHPFo9ZQ1BxMsZIuBnnafdQoAacGBiHA"
    region: "us-west-2"
    bucket: "kg-webtools/prod/upload/"
    cdn_prefix_url: "https://kg-web-cdn.kingsgroupgames.com/prod/upload/"

pkg:
    gw_gameList_api: "http://cs-admin-gateway.cs-admin-gateway.svc.cluster.local:80/api/gameList"
    gw_perm_api: "http://cs-admin-gateway.cs-admin-gateway.svc.cluster.local:80/api/per/data"

alarm:
    alert: true
    webhook: "https://open.feishu.cn/open-apis/bot/v2/hook/0d6624b4-e6a1-484e-a23e-acca68c864cb"
    keywords:
        - "Alert"
        - "Global"
        - "OpsTicketApi"
        - "Prod"
    rules:
        ERROR:
            duration: 60
            num: 2
        WARN:
            duration: 60
            num: 5

auth:
    admin_gateway: mR34P1MlzTaQhWytUoAbxVdXpw8Fq7NK
    8GyET0HD3cClqNmK: Qw4rUcnk6vJ3ZGWbIXRC
    9ydPt0QEKJGr7zOl: 1uRVBCs2E8M9dbH3tf5q # H项目
    7nSbse2J5CQUlKc2: gRz0sAcvrKpDpY0g2Fl9 # D项目
    U2FsdGVkX18sdTQ6: 03Gb9gWCcegTl7HepAXp # L项目
    BJQxggH958WdplJz: DHTq7Nu4gfEVYX6CEuNT # DC项目
    KGFjKXEssUedumN4: LWZolEIYH3r1CXJYEgkg # SSR项目
    shDUpzqA6jPkXmko: xl908zEEDamljfJcR2cW # Dino项目
    HJKjFieGxigwG6hc: Ws7KbHJKgEF1gZhBZGRg # SSCN Global ssgl.global.prod
    TBg21pbr20hpCGlE: XXAdXyDdjrTULr0fs1R8 # worldx项目
    MzI0V1lqTmpNMk5t: WmpZek5ETXpaR1k9rA4e # foundation项目
    pc_salt: AsHx6XKcLJFQ0XWBrcb3
    priv_secret : 6Etgy78rDegGXAfpWgeX # 私域密钥

ticket_monitor:
    ops_ticket_inner_api_url: "https://ops-ticket-api.funplus.com"
    ban_daily_report_game_projects:
        - "koa_global"
    games:
        ss_global:
            feishu_webhook: "https://open.feishu.cn/open-apis/bot/v2/hook/a3b2d8ba-3484-4f2e-8291-422c46a99650"  # 飞书机器人webhook
            categories:
                login: [46]
                payment_not_credited: [43, 3140]
                payment_cannot_pay: [163]
        koa_global:
            feishu_webhook: "https://open.feishu.cn/open-apis/bot/v2/hook/0657a5a4-ca14-44dd-83d8-0c9290f48171"  # 飞书机器人webhook
            categories:
                login: [499]
                payment_not_credited: [489]
                payment_cannot_pay: [498]
        gog_global:
            feishu_webhook: "https://open.feishu.cn/open-apis/bot/v2/hook/885f74f4-ebd9-4316-82ac-3c5b041176d1"  # 飞书机器人webhook
            categories:
                login: [330]
                payment_not_credited: [325]
                payment_cannot_pay: [329]
        mc_global:
            feishu_webhook: "https://open.feishu.cn/open-apis/bot/v2/hook/5b26cd3b-7f8f-4da4-994f-e8d63cfc76b4"  # 飞书机器人webhook
            categories:
                login: [1354]
                payment_not_credited: [1357]
                payment_cannot_pay: [1361]
        mce_global:
            feishu_webhook: "https://open.feishu.cn/open-apis/bot/v2/hook/80d0f8e8-85bf-4baf-843d-09f658d968b8"  # 飞书机器人webhook
            categories:
                login: [1517]
                payment_not_credited: [1520]
                payment_cannot_pay: [1524]
        mo_global:
            feishu_webhook: "https://open.feishu.cn/open-apis/bot/v2/hook/7446851a-6fab-4120-9774-639bce5aba51"  # 飞书机器人webhook
            categories:
                login: [1083]
                payment_not_credited: [2102]
                payment_cannot_pay: [2103]
        ts_global:
            feishu_webhook: "https://open.feishu.cn/open-apis/bot/v2/hook/d2cc46e9-1557-4d22-b98f-353c43f340ec"  # 飞书机器人webhook
            categories:
                login: [3271]
                payment_not_credited: [3284]
                payment_cannot_pay: [3285]
        pk.global.prod:
            feishu_webhook: "https://open.feishu.cn/open-apis/bot/v2/hook/b422c8f7-3788-44bf-81c2-d176da67115e"  # 飞书机器人webhook
            categories:
                login: []
                payment_not_credited: [2097]
                payment_cannot_pay: []
        dc.global.prod:
            feishu_webhook: "https://open.feishu.cn/open-apis/bot/v2/hook/155bcf27-79ab-4741-8c2f-edbdeeb65e1f"  # 飞书机器人webhook
            categories:
                login: [3159]
                payment_not_credited: [3166]
                payment_cannot_pay: [3167]
        entropy_global:
            feishu_webhook: "https://open.feishu.cn/open-apis/bot/v2/hook/f98ba97d-7b30-4886-88a9-ff9bffeab2c1"  # 飞书机器人webhook
            categories:
                login: [499]
                payment_not_credited: [489]
                payment_cannot_pay: [498]
        foundation.global.prod:
            feishu_webhook: "https://open.feishu.cn/open-apis/bot/v2/hook/347582c1-4fbc-4a69-b11f-27aa12ad929e"  # 飞书机器人webhook
            categories:
                login: [499]
                payment_not_credited: [489]
                payment_cannot_pay: [498]

thirdparty:
    ops_backend_api:
        crm_auth_key: "yWbIKJmgfdvtFV4nTLwDQ0sUSBxXMklC"
        crm_ops_info: "http://opscrm-api.funplus.com/api/player/simplify_detail"
        dwh_ops_info: "https://data-interface.funplus.com/v1/api/getUserInfo"
        crm_discord_info: "http://opscrm-api.funplus.com/api/player/discord"
        ai_elfin_backend_server_host: "http://ai-elfin-api.ops-tools:9000"
        cs_api_server_host: "http://cs-api-front.cs-api.svc.cluster.local:9072"
        private_zone_user_info: "https://zone-api.funplus.com/auth/user_verify"
        private_zone_vip_level: "http://priv-platform-api-inner.funplus.com/inner/api/user/r_level"
        private_zone_Vip_level_key: "58554a59e17aac7d7ee803618df34dc1"
        data_plat_server_host: "https://data-interface-internal.funplus.com"
    algorithm_url: "http://chat-detect-internal.funplus.com"
    ip2loc_url: "https://public-tools-api.funplus.com/api/ip/location"
    survey_base_url: "https://fpcs-web.funplus.com/qn/questionNaire"
    pc_assist:
        host: "https://pc-assist-api.funplus.com"
        #        host: "http://127.0.0.1:9000"
        notice_push: "/api/inner/cs/push"
    priv:
        inner_host: "http://priv-platform-api-inner.funplus.com"
        card_use: "/inner/api/cs_card/use"

# 问题分类配置
strategy_first_cat_id:
    games:
        mo_global:
            login: 1078
            suggestion: 1073


llm:
    base_url: "http://chat-detect-internal.funplus.com"  # 使用已有的算法服务地址
    timeout: 30  # 超时时间(秒)
    retry_count: 3  # 重试次数
    endpoints:
        analyze: "/collect_data"  # 分析答案的endpoint

config:
    pc_notice_url : "https://fpcs-web.funplus.com/pc/ticket-detail"