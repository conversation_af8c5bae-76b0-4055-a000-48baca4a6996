app:
    name: "ticket-api.funplus.com"
    env: "stage"
    debug: false
    version: "v3.0.0"
    environment: "internal"
    LogMode: 4
    lang: "zh-cn"

server:
    addr: ":9000"

db:
    ops_ticket:
        master: "opstools:Funplus@888@tcp(rm-uf6v1cqf32c120ba7.mysql.rds.aliyuncs.com:3306)/fp_ops_ticket_new_stage?charset=utf8mb4&parseTime=True&loc=Local"
        slaver: "opstools:Funplus@888@tcp(rm-uf6v1cqf32c120ba7.mysql.rds.aliyuncs.com:3306)/fp_ops_ticket_new_stage?charset=utf8mb4&parseTime=True&loc=Local"
    ops_ticket_old:
        master: "opstools:Funplus@888@tcp(rm-uf6v1cqf32c120ba7.mysql.rds.aliyuncs.com:3306)/fp_ops_ticket_stage?charset=utf8mb4&parseTime=True&loc=Local"
        slaver: "opstools:Funplus@888@tcp(rm-uf6v1cqf32c120ba7.mysql.rds.aliyuncs.com:3306)/fp_ops_ticket_stage?charset=utf8mb4&parseTime=True&loc=Local"
    max_open_conn: 50
    max_idle_conn: 30
    max_life_time: 10

es:
    node:
        - "http://es-cn-2r42rvrto000p9rb6.elasticsearch.aliyuncs.com:9200"
    user: "fp_ops_tickets"
    pwd: "qRmLIV3dHxwxkGFnkdHY"
    index:
        ticket: "business.fp_ops_new_tickets_stage"
        history: "business.fp_ops_new_tickets_history_stage"

# Redis
redis:
    addr: "r-uf6hs2lkfrwe2lzsrn.redis.rds.aliyuncs.com:6379"
    password: ""
    db: 3

s3:
    access_key: "********************"
    access_key_secret: "ZGCBasdcsHPFo9ZQ1BxMsZIuBnnafdQoAacGBiHA"
    region: "us-west-2"
    bucket: "kg-webtools/prod/upload/"
    cdn_prefix_url: "https://kg-web-cdn.kingsgroupgames.com/prod/upload/"

pkg:
    gw_gameList_api: "http://admin-gateway.ops-tools-stage.svc.cluster.local:8888/api/gameList"
    gw_perm_api: "http://admin-gateway.ops-tools-stage.svc.cluster.local:8888/api/per/data"

alarm:
    alert: false
    webhook: "https://open.feishu.cn/open-apis/bot/v2/hook/c049fe39-4657-44c7-a98b-6dc8fad13393"
    keywords:
        - "Alert"
        - "cn"
        - "stage"
        - "ops-ticket"
    rules:
        ERROR:
            duration: 60
            num: 2
        WARN:
            duration: 60
            num: 5

auth:
    admin_gateway: mR34P1MlzTaQhWytUoAbxVdXpw8Fq7NK
    8GyET0HD3cClqNmK: Qw4rUcnk6vJ3ZGWbIXRC
    U2FsdGVkX1hFwa3G: 8LVQ20ZMaaU9W7FlJ5aV # Fx项目
    qgS9SdYkX19wzrxY: o541M6OE9yoW9XUwDUlw # deep项目
    c9d5BacD2a505812: cA473139697E22a44df8 # sscn.cn.prod

thirdparty:
    survey_base_url: "https://fpcs-web-stage.funplus.com.cn/qn/questionNaire"
    ops_backend_api:
        crm_auth_key: "yWbIKJmgfdvtFV4nTLwDQ0sUSBxXMklC"
        crm_ops_info: "http://opscrm-api-stage.funplus.com.cn/api/player/simplify_detail"
        dwh_ops_info: "https://data-interface.funplus.com/v1/api/getUserInfo"
        data_plat_server_host: "************:8000"
