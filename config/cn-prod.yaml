app:
    name: "ticket-api.funplus.com"
    env: "prod"
    debug: false
    version: "v1.0.0"
    environment: "internal"
    LogMode: 4
    lang: "zh-cn"

# Server
server:
    addr: ":9000"

# Db
db:
    ops_ticket:
        master: "root:oLhYVo9tB597@tcp(rm-uf61hi6s203oox8i1.mysql.rds.aliyuncs.com:3306)/fp_ops_ticket_new?charset=utf8mb4&parseTime=True&loc=Local"
        slaver: "root:oLhYVo9tB597@tcp(rm-uf61hi6s203oox8i1.mysql.rds.aliyuncs.com:3306)/fp_ops_ticket_new?charset=utf8mb4&parseTime=True&loc=Local"
    ops_ticket_old:
        master: "root:oLhYVo9tB597@tcp(rm-uf61hi6s203oox8i1.mysql.rds.aliyuncs.com:3306)/fp_ops_ticket?charset=utf8mb4&parseTime=True&loc=Local"
        slaver: "root:oLhYVo9tB597@tcp(rm-uf61hi6s203oox8i1.mysql.rds.aliyuncs.com:3306)/fp_ops_ticket?charset=utf8mb4&parseTime=True&loc=Local"
    max_open_conn: 50
    max_idle_conn: 30
    max_life_time: 10

es:
    node:
        - "http://es-cn-2r42rvrto000p9rb6.elasticsearch.aliyuncs.com:9200"
    user: "fp_ops_tickets"
    pwd: "qRmLIV3dHxwxkGFnkdHY"
    index:
        ticket: "business.fp_ops_new_tickets"
        history: "business.fp_ops_new_tickets_history"

# Redis
redis:
    addr: "r-uf67m03jdfjkk4o5ly.redis.rds.aliyuncs.com:6379"
    password: ""
    db: 2

s3:
    access_key: "********************"
    access_key_secret: "ZGCBasdcsHPFo9ZQ1BxMsZIuBnnafdQoAacGBiHA"
    region: "us-west-2"
    bucket: "kg-webtools/prod/upload/"
    cdn_prefix_url: "https://kg-web-cdn.kingsgroupgames.com/prod/upload/"

pkg:
    gw_gameList_api: "http://admin-gateway.ops-tools.svc.cluster.local:8888/api/gameList"
    gw_perm_api: "http://admin-gateway.ops-tools.svc.cluster.local:8888/api/per/data"

alarm:
    alert: true
    webhook: "https://open.feishu.cn/open-apis/bot/v2/hook/c049fe39-4657-44c7-a98b-6dc8fad13393"
    keywords:
        - "Alert"
        - "CN"
        - "Prod"
        - "ticket"
    rules:
        ERROR:
            duration: 60
            num: 5
        WARN:
            duration: 60
            num: 10

auth:
    admin_gateway: mR34P1MlzTaQhWytUoAbxVdXpw8Fq7NK
    8GyET0HD3cClqNmK: Qw4rUcnk6vJ3ZGWbIXRC
    U2FsdGVkX1hFwa3G: 8LVQ20ZMaaU9W7FlJ5aV # Fx项目
    qgS9SdYkX19wzrxY: o541M6OE9yoW9XUwDUlw # deep项目
    c9d5BacD2a505812: cA473139697E22a44df8 # sscn.cn.prod

ticket_monitor:
    games:
        gog_cn:
            feishu_webhook: "https://open.feishu.cn/open-apis/bot/v2/hook/3bd5178f-6c5d-4de4-8102-1e95cb4753f5"  # 飞书机器人webhook
            categories:
                login: [481]
                payment_not_credited: [482]
                payment_cannot_pay: [486]


thirdparty:
    survey_base_url: "https://fpcs-web.funplus.com.cn/qn/questionNaire"
    ops_backend_api:
        crm_auth_key: "yWbIKJmgfdvtFV4nTLwDQ0sUSBxXMklC"
        crm_ops_info: "http://opscrm-api.funplus.com.cn/api/player/simplify_detail"
        dwh_ops_info: "https://data-interface.funplus.com/v1/api/getUserInfo"
        data_plat_server_host: "https://data-interface-internal.funplus.com"
