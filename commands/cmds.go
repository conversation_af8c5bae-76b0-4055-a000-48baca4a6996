// Copyright 2021 funplus Authors. All Rights Reserved.
// @Description:
// @Author: Darcy
// @Date: 2022/3/7 5:04 PM

package commands

import (
	"os"

	"ops-ticket-api/commands/cmds"
	"ops-ticket-api/internal/framework/maxprocs"

	"github.com/spf13/cobra"
)

var (
	root *cobra.Command
)

// initRoot bootstrap
func initRoot() {
	if root != nil {
		return
	}
	root = &cobra.Command{
		Use: os.Args[0],
		Run: func(cmd *cobra.Command, args []string) {
			cmd.Run(Service("serve", cmds.Serve), args)
		},
	}
}

// Register register cmd
func Register(cmd ...*cobra.Command) {
	initRoot()
	for _, c := range cmd {
		if c != nil {
			root.AddCommand(c)
		}
	}
}

// Run cmd run
func Run() {
	maxprocs.Set()
	if err := root.Execute(); err != nil {
		root.Printf("Executing failed: %s\n", err.<PERSON><PERSON><PERSON>())
	}
}
