package cron

import (
	"context"
	"encoding/json"
	"github.com/go-redis/redis/v8"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"go.uber.org/zap"
	"ops-ticket-api/internal/framework/cache/keys"
	"ops-ticket-api/internal/framework/cache/rds"
	"ops-ticket-api/services/communicate"
	"ops-ticket-api/services/dataplatusersrv/dataremote"
	"ops-ticket-api/utils"
	"time"
)

func syncDataPlatItemsAndI18nToLocalCommand() {
	var fun = "syncDataPlatItemsAndI18nToLocalCommand->"
	var ctx = context.TODO()
	defer func() {
		if _pErr := recover(); _pErr != nil {
			logger.Errorf(ctx, "%s panic. recover:%v", fun, _pErr)
			logger.Errorf(ctx, "%s panic. recover:%v", fun, _pErr)
		}
	}()

	go func() {
		time.Sleep(time.Second * 5)
		communicate.PublicDataPlatItemI18nChange(ctx, communicate.DataPlatPubTpItem, "")
		communicate.PublicDataPlatItemI18nChange(ctx, communicate.DataPlatPubTpI18n, "")
	}()

	go func() {
		time.Sleep(time.Second * 2)
		sub := rds.NewRCache().Subscribe(ctx, keys.DataPlatItemAndI18nPubKey)
		defer sub.Close()
		var count int
		for {
			iface, err := sub.Receive(ctx)
			if err != nil {
				logger.Errorf(ctx, "%s sub.Receive return err. i:%d. err:%v", fun, count, err)
				if count > 5 {
					logger.Errorf(ctx, "%s sub.Receive return err. fatal return i:%d. err:%v", fun, count, err)
					return
				}
				count++
			}

			switch iface.(type) {
			case *redis.Subscription: // subscribe succeeded
				item := iface.(*redis.Subscription)
				logger.Infof(ctx, "%s redis.Subscription message. item:%s", fun, item.String())
			case *redis.Message: // received first message
				item := iface.(*redis.Message)
				logger.Infof(ctx, "%s redis.Message message. item:%s", fun, item.String())
				if item.Channel != keys.DataPlatItemAndI18nPubKey {
					break
				}
				var log = &communicate.DataPlatItemAndI18nPubDf{}
				if err := json.Unmarshal([]byte(item.Payload), &log); err != nil {
					logger.Errorf(ctx, "%s Unmarshal DataPlatItemI18nChange return err. payload:%s. err:%v", fun, item.Payload, err)
					break
				}
				switch log.Type {
				case communicate.DataPlatPubTpItem:
					dataremote.RefreshItems(log.Project)
				case communicate.DataPlatPubTpI18n:
					dataremote.RefreshI18n(log.Project)
				default:
					logger.Errorf(ctx, "%s Unmarshal DataPlatItemI18nChange return err. payload:%s. log:%+v", fun, item.Payload, log)
				}
			case *redis.Pong: // pong received
				item := iface.(*redis.Pong)
				logger.Infof(ctx, "%s redis.Pong message. item:%s", fun, item.String())
			case nil:
				logger.Warn(ctx, "sub.Receive return nil.", zap.String("fun", fun), zap.Any("detail", utils.ToJson(iface)))
			default:
				count++
				logger.Errorf(ctx, "%s redis.default message. %s", fun, utils.ToJson(iface))
			}
		}
	}()
}
