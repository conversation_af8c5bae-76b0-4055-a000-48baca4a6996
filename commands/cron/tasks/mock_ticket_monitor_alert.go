package tasks

import (
	"context"
	"fmt"
	"time"

	"ops-ticket-api/commands/cron"
	"ops-ticket-api/services/monitor"
)

func init() {
	cron.RegisterCommand(&cron.CronCommand{
		Name:        "mock-ticket-monitor-alert",
		Description: "mock ticket monitor alert",
		Schedule:    "-",
		Run: func(ctx context.Context) error {
			return TicketMonitor()
		},
	})
}

// SyncUserAssignTikcket 更新分单配置加系统标签： go run main.go SyncUserAssignTikcket -c=config/global-test.yaml
func TicketMonitor() error {
	// ctx := echo.New().NewContext(&http.Request{}, &echo.Response{})
	fmt.Println("start....")

	// ticketIds := []uint64{278964, 278987, 279000, 279044, 279071, 279079, 279082, 279161}
	ticketIds := []uint64{829, 830, 831, 832, 833, 834, 835, 222}

	ctx := context.Background()
	for _, ticketId := range ticketIds {
		if err := monitor.NewTicketMonitor().MonitorTicketCreate(
			ctx,
			"ss_global",
			46,
			ticketId,
		); err != nil {
			fmt.Println(err)
		}
	}

	time.Sleep(10 * time.Second)
	fmt.Println("end....")
	return nil
}
