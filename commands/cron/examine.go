package cron

import (
	"context"
	"errors"
	"fmt"
	"github.com/avast/retry-go"
	"github.com/bsm/redislock"
	"github.com/labstack/echo/v4"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"net/http"
	"ops-ticket-api/internal/framework/cache/keys"
	"ops-ticket-api/internal/framework/cache/rds"
	"ops-ticket-api/internal/persistence"
	"ops-ticket-api/proto/pb"
	"ops-ticket-api/services/communicate"
	"ops-ticket-api/services/examinesrv/examinecore"
	"ops-ticket-api/utils"
	"time"
)

func examineInit(ctx echo.Context) {
	go examineOrderAsyncTaskState() // 质检单完成 - 修正质检任务的最后结果

	go examineTaskAlloc()         // 任务自动分单 - 执行分单逻辑
	go getDoingExamineTaskToQue() // 首次启动 - 质检任务未处理单重新推入队列
}

// examineTaskAlloc 自动生成质检任务详情、分配处理人
func examineTaskAlloc() {
	var (
		ctx, cancel = context.WithCancel(context.TODO())
		fun         = "cron.examineTaskAlloc -->"
	)
	defer cancel()
	for tk := range communicate.SubExamineTaskCreate() {
		if tk == nil || tk.TaskId == 0 {
			logger.Errorf(ctx, "%s examineTaskAlloc: detail/detail.TaskId is nil is nil", fun)
			time.Sleep(time.Second)
			continue
		}
		// do logic
		go singleExamineTask(tk.Ctx, tk.TaskId)
	}
}

// singleExamineTask 质检任务生成
func singleExamineTask(ctx echo.Context, taskId uint64) {
	var (
		fun = "cron.genSingleExamineTask -->"
	)
	// get lock
	lock, err := rds.GetRdsMux(ctx.Request().Context(), fmt.Sprintf(keys.ExamineTaskGenLockV3, taskId))
	if err != nil {
		if errors.Is(err, redislock.ErrNotObtained) {
			logger.Warn(ctx.Request().Context(), "get examine task lock failed.", logger.String("fun", fun), logger.Any("err", err), logger.Uint64("task_id", taskId))
		} else {
			logger.Errorf(ctx.Request().Context(), "%s get examine task lock return err. err:%v. taskId:%d", fun, err, taskId)
		}
		return
	}
	refreshC := make(chan struct{})
	defer lock.Release(ctx.Request().Context())
	go rds.RefreshRdsMux(ctx.Request().Context(), lock, refreshC)
	defer close(refreshC)
	// do logic
	err = retry.Do(func() error {
		if innerErr := genSingleExamineTask(ctx, taskId); innerErr != nil {
			logger.Warn(ctx.Request().Context(), "genSingleExamineTask failed.", logger.String("fun", fun), logger.Any("err", innerErr), logger.Uint64("task_id", taskId))
			return innerErr
		}
		return nil
	}, retry.Attempts(3), retry.Delay(1*time.Second), retry.LastErrorOnly(true))
	logger.Infof(ctx.Request().Context(), "%s genSingleExamineTask result. taskId:%d. err:%v", fun, taskId, err)
	if err != nil {
		logger.Errorf(ctx.Request().Context(), "%s genSingleExamineTask return err. err:%v. taskId:%d", fun, err, taskId)
	}
}

// singleExamineTask 质检任务生成
func genSingleExamineTask(ctx echo.Context, taskId uint64) error {
	var (
		fun = "cron.genSingleExamineTask -->"
	)
	<-time.NewTimer(time.Second).C

	// do logic
	// 1.0 get examine task detail
	task, taskFilter, err := persistence.NewExamineSettings().GetTaskDetail(ctx, taskId)
	if err != nil {
		logger.Warn(ctx.Request().Context(), "GetTaskDetail failed.", logger.String("fun", fun), logger.Any("err", err), logger.Uint64("task_id", taskId))
		return err
	}
	if !utils.InArrayAny(pb.ExamineTaskStateDf(task.TaskStatus),
		[]pb.ExamineTaskStateDf{pb.ExamineTaskStateDf_ExamineTaskStateDfDoing, pb.ExamineTaskStateDf_ExamineTaskStateDfInit}) {
		logger.Infof(ctx.Request().Context(), "%s genSingleExamineTask task is not init/doing. taskId:%d", fun, taskId)
		return nil
	}
	// update status to doing && update status to final status
	err = persistence.NewExamineSettings().TaskUpdates(ctx.Request().Context(),
		map[string]interface{}{"id": taskId, "task_status": []int{int(pb.ExamineTaskStateDf_ExamineTaskStateDfInit), int(pb.ExamineTaskStateDf_ExamineTaskStateDfDoing)}},
		map[string]interface{}{"task_status": pb.ExamineTaskStateDf_ExamineTaskStateDfDoing, "updated_at": time.Now()})
	if err != nil {
		logger.Errorf(ctx.Request().Context(), "%s genSingleExamineTask update task status failed. taskId:%d. err:%v. taskDetail:%+v", fun, taskId, err, task)
		return err
	}
	// 1.1 do gen examine task logs
	var taskRes *examinecore.ExamineTaskResult
	var toState = pb.ExamineTaskStateDf_ExamineTaskStateDfSuccess
	defer func() { // update status to final status
		_ = persistence.NewExamineSettings().TaskUpdates(ctx.Request().Context(),
			map[string]interface{}{"id": taskId, "task_status": int(pb.ExamineTaskStateDf_ExamineTaskStateDfDoing)},
			map[string]interface{}{"task_status": toState, "task_result": utils.ToJson(taskRes), "updated_at": time.Now()})
	}()

	// do logic
	taskRes, err = examinecore.GetExamineImpl(taskFilter.TaskGroup).GenExamineTaskLogs(ctx, task, taskFilter)
	logger.Infof(ctx.Request().Context(), "%s genSingleExamineTask result. taskId:%d. result:%+v. err:%v", fun, taskId, taskRes, err)
	if err != nil {
		toState = pb.ExamineTaskStateDf_ExamineTaskStateDfFail
		return err
	}
	return nil
}

func getDoingExamineTaskToQue() {
	fun := "cron.getDoingExamineTaskToQue -->"
	<-time.NewTimer(time.Second * 5).C

	_ctx, _ := context.WithCancel(logger.WithFields(context.TODO(), logger.String("fun", fun), logger.String("op", "system")))
	_ctx = utils.WithNextSpan(_ctx)
	ctx := echo.New().NewContext((&http.Request{}).WithContext(_ctx), &echo.Response{})
	logger.Infof(ctx.Request().Context(), "only for test 10s later.....")
	list, err := persistence.NewExamineSettings().GetUnfinishedToQue(ctx)
	if err != nil {
		logger.Errorf(ctx.Request().Context(), "GetUnfinishedToQue failed. err:%v", err)
	}
	for _, row := range list {
		if err := communicate.PubExamineTaskCreate(ctx, row.ID); err != nil {
			logger.Errorf(ctx.Request().Context(), "PubExamineTaskCreate failed. err:%v", err)
		}
	}
	<-time.NewTimer(time.Second * 3).C
	ctx.SetRequest(ctx.Request().WithContext(utils.WithNextSpan(ctx.Request().Context())))
	logger.Infof(ctx.Request().Context(), "reload unfinished task to que finished. len:%d", len(list))
}

func examineOrderAsyncTaskState() {
	if err := communicate.ExamineOrderFinishedBus.SubscribeAsync(communicate.ExamineOrderFinishedTopic, func(data interface{}) {
		detail, ok := data.(*communicate.ExamineOrderFinishedPubSt)
		if !ok || detail == nil {
			logger.Errorf(context.TODO(), "examineOrderAsyncTaskState: data is nil")
			return
		}
		time.Sleep(time.Second * 1)
		ctx := context.WithValue(detail.Ctx, "_fun_", "cron.examineOrderAsyncTaskState")
		logger.Infof(ctx, "examineOrderAsyncTaskState get data detail:%+v", detail)
		if err := examinecore.ExamineOrderSyncTaskState(ctx, detail.TaskId, detail.ExamineType, detail.DetailId); err != nil {
			logger.Errorf(ctx, "ExamineOrderSyncTaskState failed. err:%v. detail:%+v", err, detail)
		}
	}, false); err != nil {
		logger.Errorf(context.TODO(), "examineOrderAsyncTaskState subscribe failed. please check. err:%v", err)
		logger.Errorf(context.TODO(), "examineOrderAsyncTaskState subscribe failed. please check. err:%v", err)
		logger.Errorf(context.TODO(), "examineOrderAsyncTaskState subscribe failed. please check. err:%v", err)
	}
}
