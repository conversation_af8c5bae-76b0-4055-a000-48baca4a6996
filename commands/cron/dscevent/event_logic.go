package dscevent

import (
	"context"
	"errors"
	"fmt"
	"ops-ticket-api/internal/code"
	"ops-ticket-api/internal/elasticsearch"
	"ops-ticket-api/internal/framework/cache/rds"
	"ops-ticket-api/internal/framework/database"
	"ops-ticket-api/internal/persistence"
	"ops-ticket-api/models"
	"ops-ticket-api/services/dsccore"
	"ops-ticket-api/utils"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/avast/retry-go"
	"github.com/bsm/redislock"
	"github.com/bwmarrin/discordgo"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"gorm.io/gorm/clause"
)

// todo add poll event
// poll messsage detail :{"id":"1260556132309536850","channel_id":"1250430563165147156","content":"","timestamp":"2024-07-10T11:20:01.943Z","edited_timestamp":null,"mention_roles":[],"tts":false,"mention_everyone":false,"author":{"id":"1045629060488892476","email":"","username":"lintest","avatar":"","locale":"","discriminator":"0","global_name":"lintest","token":"","verified":false,"mfa_enabled":false,"banner":"","accent_color":0,"bot":false,"public_flags":0,"premium_type":0,"system":false,"flags":0},"attachments":[],"embeds":[],"mentions":[],"reactions":null,"pinned":false,"type":0,"webhook_id":"","member":null,"mention_channels":null,"activity":null,"application":null,"message_reference":null,"referenced_message":null,"interaction":null,"interaction_metadata":null,"flags":0,"sticker_items":null,"poll":{"question":{"text":"投票问题 1"},"answers":[{"answer_id":1,"poll_media":{"text":"答案 11111","emoji":{"name":"🍉"}}},{"answer_id":2,"poll_media":{"text":"答案 22222","emoji":{"name":"☺️"}}}],"allow_multiselect":false,"layout_type":1,"expiry":"2024-07-11T11:20:01.940265Z"}}
// 发起投票可以通过 gatewayEvent 的 MessageCreate 来获取 (替换成 discordgo包： replace github.com/bwmarrin/discordgo v0.28.1 => github.com/petew9527/discordgo v0.0.0-20240710095817-33270b414fe8)
// poll vote 无法获取;
// 投票结果获取：只能在投票时间结束后，依赖于异步获取 ChannelMessage 来重新拉取数据；

var (
	inGuilds = func(guildId string, ids []string) bool {
		return guildId != "" && utils.InArrayAny(guildId, ids)
	}
	welcomeMessage = map[string]string{
		"mo_global": `Welcome to our Discord!

Hello Captain, I am your VIP exclusive butler Echo, and I'm thrilled to be on this journey with you. 🚢

To ensure a smooth sailing experience, please reply with your【uid】, 【nickname】,【server】in the game, and 【Your birthday date】.

The verification process will be taken immediately once this information is received. Once verified, I will send you an exclusive gift. ✨As this is a manual verification, this process may take time, thank you for your patience.😊`,

		"default": "Hello, welcome to join us, you can communicate with us through this channel!",
	}
	getWelcomeMessage = func(conf *models.DscBotConfigDf) string {
		if conf.WelcomeMessage != "" {
			return conf.WelcomeMessage
		}
		if welcomeMessage[conf.Project] != "" {
			return welcomeMessage[conf.Project]
		}
		return welcomeMessage["default"]
	}
	// https://discord.com/developers/docs/topics/gateway#list-of-intents
	// https://discord.com/developers/docs/topics/gateway-events#receive-events
	skipperEventList = []string{
		"READY",        // 初始化信息:	Contains the initial state information
		"RESUMED",      //
		"TYPING_START", // 玩家端开始输入事件

		// guild
		"GUILD_CREATE",                 // 创建工会 Lazy-load for unavailable guild, guild became available, or user joined a new guild
		"GUILD_UPDATE",                 // 更新工会 Guild was updated
		"GUILD_DELETE",                 // Guild became unavailable, or user left/was removed from a guild
		"GUILD_INTEGRATIONS_UPDATE",    // 公会集成已更新: Guild integration was updated
		"GUILD_AUDIT_LOG_ENTRY_CREATE", // 工会审核日志
		"GUILD_ROLE_CREATE",
		"GUILD_ROLE_UPDATE",
		"GUILD_ROLE_DELETE",

		// message
		"MESSAGE_CREATE", //
		"MESSAGE_UPDATE",
		"MESSAGE_DELETE",
		"MESSAGE_REACTION_ADD",
		"MESSAGE_REACTION_REMOVE",

		// voice
		"VOICE_CHANNEL_STATUS_UPDATE", //
		"VOICE_STATE_UPDATE",

		// other
		"INTEGRATION_DELETE", // integration was deleted
		"INVITE_CREATE",      // invite to a channel was created

	}
)

type dscGatewayEventHandle struct {
	appId string // dsc bot application_id 、bot_user_id、client_id 都是同一个值
	ctx   context.Context
	sess  *discordgo.Session
	conf  *models.DscBotConfigDf
}

func (h *dscGatewayEventHandle) StartEvent() error {
	go h.startEvent()
	return nil

	//tm := time.NewTicker(time.Second * 1)
	//retry := make(chan struct{}, 1)
	//for {
	//	select {
	//	case <-retry:
	//		if err := h.startEvent(); err != nil {
	//			logger.Errorf(h.ctx, "dscGatewayEventHandle.startEvent err. appId:%s. err:%v", h.appId, err)
	//		}
	//	case <-tm.C:
	//		if len(retry) > 0 {
	//			fmt.Println(time.Now(), "channel len gt0. continue...", len(retry))
	//			continue
	//		}
	//		retry <- struct{}{}
	//		fmt.Println(time.Now(), "add channel signal....")
	//	}
	//}
}

func (h *dscGatewayEventHandle) startEvent() error {
	var (
		fun = "dscGatewayEventHandle.startEvent"
	)

	//defer func() {
	//	if err := recover(); err != nil {
	//		s := string(debug.Stack())
	//		logger.Errorf(h.ctx, "fun =%s. err=%v, stack=%s\n", fun, err, s)
	//	}
	//}()

	if h.appId == "" || h.conf == nil || h.conf.ClientId == "" || h.conf.BotToken == "" {
		return fmt.Errorf("%s appId or config is empty", fun)
	}
	if h.sess == nil {
		return errors.New("dsc session is nil")
	}

	var fc = []interface{}{
		h.allEvent(),
		h.ready(), h.connectEvent(), h.disconnectEvent(),
	}
	fc = append(fc, h.guildEvent()...)   // guild member add
	fc = append(fc, h.messageEvent()...) // message
	for _, v := range fc {
		h.sess.AddHandler(v)
	}
	if err := h.sess.Open(); err != nil {
		return err
	}
	defer h.sess.Close()
	fmt.Println("dscGatewayEventHandle.startEvent success. appId:", h.appId)
	// 阻塞进程
	signals := make(chan os.Signal, 1)
	signal.Notify(signals, os.Interrupt, os.Kill, syscall.SIGTERM)
	<-signals
	fmt.Println("dscGatewayEventHandle.startEvent signal closed. appId:", h.appId)
	return nil
}

// connect
func (d *dscGatewayEventHandle) connectEvent() func(s *discordgo.Session, r *discordgo.Connect) {
	return func(s *discordgo.Session, r *discordgo.Connect) {
		logger.Info(d.ctx, "dscGatewayEventHandle.connect event detail", logger.Any("app_id", d.appId), logger.Any("conf", d.conf), logger.Any("detail", r))
	}
}

func (d *dscGatewayEventHandle) disconnectEvent() func(s *discordgo.Session, r *discordgo.Disconnect) {
	return func(s *discordgo.Session, r *discordgo.Disconnect) {
		logger.Info(d.ctx, "dscGatewayEventHandle.disconnect event detail", logger.Any("app_id", d.appId), logger.Any("conf", d.conf), logger.Any("detail", r))

	}
}

func (d *dscGatewayEventHandle) ready() func(s *discordgo.Session, r *discordgo.Ready) {
	return func(s *discordgo.Session, r *discordgo.Ready) {
		logger.Info(d.ctx, "dscGatewayEventHandle.ready event detail", logger.Any("app_id", d.appId), logger.Any("conf", d.conf), logger.Any("detail", r))
	}
}

// messageEvent message 类型消息事件
func (d *dscGatewayEventHandle) messageEvent() []interface{} {
	return []interface{}{
		// message event
		d.messageCreate(), d.MessageUpdate(), d.MessageDel(),
		// message reaction event
		d.MessageReactionAdd(), d.MessageReactionRemove(), d.MessageReactionRemoveAll(),
	}
}

// messageCreate -- channel 发送消息
func (d *dscGatewayEventHandle) messageCreate() func(s *discordgo.Session, r *discordgo.MessageCreate) {
	return func(s *discordgo.Session, r *discordgo.MessageCreate) {
		logger.Info(d.ctx, "dscGatewayEventHandle.messageCreate event detail", logger.Any("message", r), logger.Any("app_id", d.appId), logger.Any("project", d.conf.Project))
		if r.GuildID != "" {
			logger.Info(d.ctx, "messageCreate-> guild message. ignore.", logger.Any("message", r), logger.Any("app_id", d.appId))
			return
		} else if r.Author == nil {
			logger.Warn(d.ctx, "messageCreate->author isNil. ignore.", logger.Any("message", r), logger.Any("app_id", d.appId))
			persistence.NewDscInteractions().CreateEventLog(d.ctx, d.appId, "ReCheckEvent", "MessageCreate", utils.ToJson(r), "AuthorNil")
			return
		}
		err := retry.Do(func() error {
			if _, _innErr := dsccore.ChannelMessageSrv.MessageCreate(d.ctx, d.conf.Project, d.appId, r.Message, true); _innErr != nil {
				logger.Warn(d.ctx, "messageCreate-> err.", logger.Any("err", _innErr), logger.Any("message", r))
				return _innErr
			}

			return nil
		}, retry.Attempts(3), retry.Delay(200*time.Millisecond), retry.LastErrorOnly(true))
		if err != nil {
			logger.Errorf(d.ctx, "messageCreate->retry err. msg:%s. err:%v", utils.ToJson(r), err)
		}
	}
}

func (d *dscGatewayEventHandle) MessageUpdate() func(s *discordgo.Session, r *discordgo.MessageUpdate) {
	return func(s *discordgo.Session, r *discordgo.MessageUpdate) {
		logger.Info(d.ctx, "dscGatewayEventHandle.MessageUpdate event detail", logger.Any("app_id", d.appId), logger.Any("conf", d.conf.Project), logger.Any("detail", r))
		if r.GuildID != "" {
			logger.Warn(d.ctx, "messageUpdate->guildMessage. ignore.", logger.Any("message", r), logger.Any("app_id", d.appId))
			return
		}

		err := retry.Do(func() error {
			if _, _innErr := dsccore.ChannelMessageSrv.MessageUpdate(d.ctx, d.conf.Project, d.appId, r.Message, true); _innErr != nil {
				logger.Warn(d.ctx, "messageUpdate-> err.", logger.Any("err", _innErr), logger.Any("message", r))
				return _innErr
			}
			return nil
		})
		if err != nil {
			logger.Errorf(d.ctx, "messageUpdate->retry err. msg:%+v. err:%v", r, err)
		}
	}
}

func (d *dscGatewayEventHandle) MessageDel() func(s *discordgo.Session, r *discordgo.MessageDelete) {
	return func(s *discordgo.Session, r *discordgo.MessageDelete) {
		logger.Info(d.ctx, "dscGatewayEventHandle.MessageDel event detail", logger.Any("app_id", d.appId), logger.Any("conf", d.conf.Project), logger.Any("detail", r))
		if r.GuildID != "" {
			logger.Warn(d.ctx, "messageDelete->guild message. ignore.", logger.Any("message", r), logger.Any("app_id", d.appId))
			return
		}
		// del message
		err := retry.Do(func() error {
			if _err := dsccore.ChannelMessageSrv.MessageDel(d.ctx, d.conf.Project, d.appId, r.ChannelID, []string{r.ID}); _err != nil {
				logger.Warn(d.ctx, "messageDelete-> err.", logger.Any("err", _err), logger.Any("message", r), logger.Any("msgIds", r.ID))
				return _err
			}
			return nil
		}, retry.Attempts(3), retry.Delay(200*time.Millisecond), retry.LastErrorOnly(true))
		if err != nil {
			logger.Errorf(d.ctx, "messageDelete->retry err. msg:%+v. err:%v", r, err)
		}
	}
}

func (d *dscGatewayEventHandle) MessageReactionAdd() func(s *discordgo.Session, r *discordgo.MessageReactionAdd) {
	return func(s *discordgo.Session, r *discordgo.MessageReactionAdd) {
		logger.Info(d.ctx, "dscGatewayEventHandle.MessageReactionAdd event detail", logger.Any("app_id", d.appId), logger.Any("conf", d.conf.Project), logger.Any("detail", r))
		if r.GuildID != "" {
			logger.Warn(d.ctx, "messageReactionAdd->guild message. ignore.", logger.Any("message", r), logger.Any("app_id", d.appId))
			return
		}
		// add reaction for message
		err := retry.Do(func() error {
			if _, _innErr := dsccore.ChannelMessageSrv.MessageReactionAdd(d.ctx, d.appId, r.MessageReaction); _innErr != nil {
				logger.Warn(d.ctx, "messageReactionAdd-> err.", logger.Any("err", _innErr), logger.Any("message", r))
				return _innErr
			}
			return nil
		}, retry.Attempts(3), retry.Delay(200*time.Millisecond), retry.LastErrorOnly(true))
		if err != nil {
			logger.Errorf(d.ctx, "messageReactionAdd->retry err. msg:%+v. err:%v", r, err)
		}
	}
}

func (d *dscGatewayEventHandle) MessageReactionRemove() func(s *discordgo.Session, r *discordgo.MessageReactionRemove) {
	return func(s *discordgo.Session, r *discordgo.MessageReactionRemove) {
		logger.Info(d.ctx, "dscGatewayEventHandle.MessageReactionRemove event detail", logger.Any("app_id", d.appId), logger.Any("conf", d.conf.Project), logger.Any("detail", r))
		if r.GuildID != "" {
			logger.Warn(d.ctx, "messageReactionRemove->guild message. ignore.", logger.Any("message", r), logger.Any("app_id", d.appId))
			return
		}
		// remove reaction for message
		err := retry.Do(func() error {
			if _innErr := dsccore.ChannelMessageSrv.MessageReactionRemove(d.ctx, d.appId, r.MessageReaction); _innErr != nil {
				logger.Warn(d.ctx, "messageReactionRemove-> err.", logger.Any("err", _innErr), logger.Any("message", r))
				return _innErr
			}
			return nil
		}, retry.Attempts(3), retry.Delay(200*time.Millisecond), retry.LastErrorOnly(true))
		if err != nil {
			logger.Errorf(d.ctx, "messageReactionRemove->retry err. msg:%+v. err:%v", r, err)
		}
	}
}

func (d *dscGatewayEventHandle) MessageReactionRemoveAll() func(s *discordgo.Session, r *discordgo.MessageReactionRemoveAll) {
	return func(s *discordgo.Session, r *discordgo.MessageReactionRemoveAll) {
		logger.Error(d.ctx, "dscGatewayEventHandle.MessageReactionRemoveAll event detail", logger.Any("app_id", d.appId), logger.Any("conf", d.conf.Project), logger.Any("detail", r))
		persistence.NewDscInteractions().CreateEventLog(d.ctx, d.appId, "SpecialEvent", "MessageReactionRemoveAll", utils.ToJson(r), "")
	}
}

func (d *dscGatewayEventHandle) guildEvent() []interface{} {
	return []interface{}{
		// guild event
		d.guildMemberAdd(),
	}
}

// guildMemberAdd -- guild 组中增加新用户
func (d *dscGatewayEventHandle) guildMemberAdd() func(s *discordgo.Session, r *discordgo.GuildMemberAdd) {
	return func(s *discordgo.Session, r *discordgo.GuildMemberAdd) {
		logger.Info(d.ctx, "dscGatewayEventHandle.guildMemberAdd event detail", logger.Any("app_id", d.appId), logger.Any("conf", d.conf.Project), logger.Any("detail", r))
		finalErr := retry.Do(func() error {
			if inGuilds(r.GuildID, []string{d.conf.GuildId}) && r.User != nil && !r.User.Bot { // guild member add && not bot
				// 1. add discord user
				if err := d.AddUser(d.ctx, r.User, r.GuildID, r.JoinedAt, true); err != nil {
					logger.Error(d.ctx, "guildMemberAdd->add user err.", logger.Any("err", err), logger.Any("user", r.User))
					return err
				}
			} else {
				persistence.NewDscInteractions().CreateEventLog(d.ctx, d.appId, "ReCheckEvent", "GuildMemberAdd", utils.ToJson(r), "UnknownBot")
				logger.Errorf(d.ctx, "guildMemberAdd-> unknown guild Or user. please check ---> %s", utils.ToJson(r))
				return errors.New("unknown guild Or user")
			}
			return nil
		}, retry.Attempts(3), retry.Delay(200*time.Millisecond), retry.LastErrorOnly(true))
		if finalErr != nil {
			logger.Errorf(d.ctx, "guildMemberAdd->retry err. err:%v. detail:%s", finalErr, utils.ToJson(r))
		}
	}
}

func (d *dscGatewayEventHandle) AddUser(ctx context.Context, user *discordgo.User, guildId string, joinedAt time.Time, isEvent bool) error {
	var (
		fun          = "dscGatewayEventHandle._guildAddUser"
		userDetail   *models.FpDscUser
		isNewChannel bool
		err          error
	)
	// 1. 新增用户：
	repo := persistence.NewDscInteractions()
	userDetail, err = repo.GetDscUserForMaster(ctx, user.ID, d.appId)
	if err != nil {
		logger.Warn(ctx, "get user detail for master err.", logger.Any("err", err), logger.Any("user", user), logger.Any("fun", fun), logger.Any("app_id", d.appId))
		return err
	}
	tx := database.Db().WithContext(ctx).Begin()
	defer func() {
		if err != nil {
			if _rollErr := tx.Rollback().Error; _rollErr != nil {
				logger.Errorf(ctx, "fun=%s. rollback err. err=%v. rollErr:%v", fun, err, _rollErr)
			}
		}
	}()
	if userDetail == nil || userDetail.DscUserID == "" { // add new user to db
		userDetail = &models.FpDscUser{
			Project:       d.conf.Project,
			AppID:         d.appId,
			DscUserID:     user.ID,
			PrivChannelID: "",
			UserName:      user.Username,
			GlobalName:    user.GlobalName,
			Discriminator: user.Discriminator,
			GuildID:       guildId,
			JoinedAt:      joinedAt,
			IsDeleted:     code.DscNoDelete,
			CreatedAt:     time.Now(),
			UpdatedAt:     time.Now(),
		}
		if err = repo.AddDscUser(ctx, tx, userDetail); err != nil {
			logger.Warn(ctx, "add user detail err.", logger.Any("err", err), logger.Any("user", user), logger.Any("fun", fun), logger.Any("app_id", d.appId))
			return err
		}
	} else if userDetail.IsDeleted == code.DscDeleted {
		if err = repo.UpdateDscUser(ctx, tx,
			map[string]interface{}{"is_deleted": code.DscNoDelete, "updated_at": time.Now()},
			[]clause.Expression{
				clause.Eq{Column: "app_id", Value: userDetail.AppID},
				clause.Eq{Column: "dsc_user_id", Value: userDetail.DscUserID},
				clause.Eq{Column: "is_deleted", Value: code.DscDeleted},
			}); err != nil {
			logger.Warn(ctx, "update dsc user detail err.", logger.Any("err", err), logger.Any("user", user), logger.Any("fun", fun), logger.Any("app_id", d.appId))
			return err
		}
		userDetail.IsDeleted = code.DscNoDelete
		// 删除后再进入需要重新初始化
		if err = repo.CreateMaintainConfig(ctx, tx, userDetail); err != nil {
			logger.Warn(ctx, "add maintain config err.", logger.Any("err", err), logger.Any("user", user), logger.Any("fun", fun), logger.Any("app_id", d.appId))
			return err
		}
	}
	// 2. 发送消息：
	if userDetail.PrivChannelID == "" {
		// 2.1. 获取私信频道
		var ch *discordgo.Channel
		ch, err = d.sess.UserChannelCreate(user.ID)
		logger.Info(ctx, "sess.UserChannelCreate return result.", logger.Any("user", user), logger.Any("channelDetail", ch), logger.Any("err", err))
		if err != nil {
			logger.Warn(ctx, "discord create user channel err.", logger.Any("err", err), logger.Any("user", user), logger.Any("fun", fun), logger.Any("app_id", d.appId))
			return err
		}
		if ch != nil && ch.ID != "" {
			isNewChannel = true
			userDetail.PrivChannelID = ch.ID
			if err = repo.UpdateDscUser(ctx, tx,
				map[string]interface{}{"priv_channel_id": userDetail.PrivChannelID},
				[]clause.Expression{
					clause.Eq{Column: "app_id", Value: userDetail.AppID},
					clause.Eq{Column: "dsc_user_id", Value: userDetail.DscUserID},
					clause.Eq{Column: "priv_channel_id", Value: ""},
				}); err != nil {
				logger.Warn(ctx, "update dsc user detail err.", logger.Any("err", err), logger.Any("user", user), logger.Any("fun", fun), logger.Any("app_id", d.appId))
				return err
			}
		}
	}
	// 3. 发送欢迎消息
	var showSendWelcome bool
	if userDetail.PrivChannelID != "" {
		if _, err = rds.GetRdsMux(ctx, userDetail.PrivChannelID); err != nil {
			if !errors.Is(err, redislock.ErrNotObtained) {
				logger.Error(ctx, "get redis lock err.", logger.Any("err", err), logger.Any("user", user), logger.Any("fun", fun), logger.Any("app_id", d.appId))
				return err
			}
			// 发送频率限制 - 短期内已发过、不发送
			err = nil
		} else {
			showSendWelcome = true
		}
	}
	if showSendWelcome == true && isEvent == false && isNewChannel == false { // 非 event && channel 已存在 - 不发消息
		showSendWelcome = false
	}

	// commit
	if err = tx.Commit().Error; err != nil {
		return err
	}

	// save discord user to es
	defer func() {
		if err == nil {
			if _dscEsErr := elasticsearch.DefaultDscEsSvc.UpsertDscUser(ctx, userDetail); _dscEsErr != nil {
				logger.Warn(ctx, "upsert dsc user to es err.", logger.Any("err", _dscEsErr), logger.Any("userDetail", userDetail), logger.Any("fun", fun), logger.Any("app_id", d.appId))
			}

			// sync maintain config to es
			mcRepo := persistence.NewMaintainConfig()
			if mc, _mcDtErr := mcRepo.GetMaintainConfigDetail(ctx, userDetail.DscUserID, userDetail.Project); _mcDtErr != nil {
				logger.Warn(ctx, "get maintain config detail err.", logger.Any("err", _mcDtErr), logger.Any("userDetail", userDetail), logger.Any("fun", fun), logger.Any("app_id", d.appId))
				return
			} else {
				if _mcSyncEsErr := mcRepo.MaintainConfigSyncEs(ctx, nil, mc); _mcSyncEsErr != nil {
					logger.Errorf(ctx, "maintain config sync es err. err:%v. mc:%+v", _mcSyncEsErr, mc)
				}
			}
		}
	}()

	// todo 强制不发送欢迎语 - 2024.11.22 临时方案后期放开
	if showSendWelcome { // send welcome message
		go d._sendWelcomeMessage(userDetail, getWelcomeMessage(d.conf))
	}
	return nil
}

func (d *dscGatewayEventHandle) _sendWelcomeMessage(userDetail *models.FpDscUser, message string) error {
	time.Sleep(time.Millisecond * 100)
	msg, err := d.sess.ChannelMessageSend(userDetail.PrivChannelID, message)
	if err != nil {
		logger.Errorf(d.ctx, "send welcome message err. user:%s. err:%v", userDetail.DscUserID, err)
		return err
	}
	if msg.ID == "" {
		logger.Errorf(d.ctx, "send welcome message err. user:%s. msg empty", userDetail.DscUserID)
		return errors.New("send welcome message err. msg empty")
	}
	fmt.Println("send welcome message result. msg:", utils.ToJson(msg))
	persistence.NewDscInteractions().CreateEventLog(d.ctx, d.appId, "SendWelcome", "SendWelcomeMessage", utils.ToJson(msg), "")

	// 踢出群聊
	//if userDetail.GuildID != "" {
	//	if err := d.sess.GuildMemberDelete(userDetail.GuildID, userDetail.DscUserID); err != nil {
	//		logger.Errorf(d.ctx, "kick guild member off return err. user:%s. guildId:%s err:%v", userDetail.DscUserID, userDetail.GuildID, err)
	//	}
	//}

	return nil
}

func (d *dscGatewayEventHandle) allEvent() func(s *discordgo.Session, r *discordgo.Event) {
	return func(s *discordgo.Session, r *discordgo.Event) {
		logger.Info(d.ctx, "dscGatewayEventHandle.allEvent event detail", logger.Any("app_id", d.appId), logger.Any("detail", r))
		if !utils.InArrayAny(r.Type, skipperEventList) {
			// no skipper event - add event logic
			persistence.NewDscInteractions().CreateEventLog(d.ctx, d.appId, "AllEvent", r.Type, utils.ToJson(r), "")
		}
	}
}
