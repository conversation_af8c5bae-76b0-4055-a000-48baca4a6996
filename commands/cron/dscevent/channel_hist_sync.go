package dscevent

import (
	"context"
	"github.com/bwmarrin/discordgo"
	"github.com/opentracing/opentracing-go"
	"github.com/zeromicro/go-zero/core/fx"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"ops-ticket-api/internal/dsc"
	"ops-ticket-api/internal/persistence"
	"ops-ticket-api/models"
	"ops-ticket-api/services/dsccore"
	"ops-ticket-api/utils"
	"time"
)

func AsyncChannelDialog() {
	fun := "dscevent.AsyncChannelDialog"
	// 2.0 遍历channel
	ctx, cancel := context.WithCancel(logger.WithFields(context.TODO(), logger.String("fun", fun), logger.String("username", "system")))
	span := opentracing.StartSpan("async_channel_dialog_history")
	ctx = opentracing.ContextWithSpan(ctx, span)
	defer cancel()
	// 1.0 获取所有的appIds
	fx.From(func(source chan<- any) {
		for _, appId := range dsc.GetAllAppIds(ctx) {
			// 3.0 获取channel的最新消息
			source <- appId
		}
	}).Map(func(item any) any {
		appId := item.(string)
		if appId == "" {
			logger.Errorf(ctx, "%s appId is empty :%s", fun, appId)
			return appId
		}
		if cli := dsc.GetClient(ctx, appId); cli == nil {
			logger.Errorf(ctx, "%s GetClient is nil appId:%s", fun, appId)
			return appId
		}

		// 3.0 获取channel的最新消息
		newCtx := logger.WithFields(ctx, logger.String("appId", appId))
		logger.Info(newCtx, "do single botId message sync", logger.String("fun", fun), logger.String("app_id", appId))
		if err := syncDscAppIdAllUserMessages(newCtx, appId); err != nil {
			logger.Errorf(newCtx, "%s syncDscAppIdAllUserMessages err:%v", fun, err)
		}
		return appId
	}, fx.WithWorkers(6)).Done()
}

func syncDscAppIdAllUserMessages(ctx context.Context, appId string) error {
	// 1. get all dsc users
	fx.From(func(source chan<- any) {
		dusers, err := persistence.NewDscInteractions().GetDscUsers(ctx, appId)
		if err != nil {
			return
		}
		for _, user := range dusers {
			if user != nil && user.DscUserID != "" {
				source <- user
			}
		}
	}).Map(func(item any) any {
		user := item.(*models.FpDscUser)
		var res = &syncUserDialogHistResult{User: user}
		if err := syncDscUserDialogHistories(context.WithValue(ctx, "user", user), user); err != nil {
			logger.Errorf(ctx, "syncDscUserDialogHistories err:%v", err)
			res.Err = err
		}
		return res
	}, fx.WithWorkers(6)).Reduce(func(pipe <-chan any) (any, error) {
		for item := range pipe {
			res := item.(*syncUserDialogHistResult)
			if res.Err != nil {
				logger.Errorf(ctx, "syncUserDialogHistResult return err. appId:%s. err:%v. curUser:%+v", appId, res.Err, res.User)
			} else {
				logger.Infof(ctx, "syncUserDialogHistResult success. appId:%s. curUser:%+v", appId, res.User)
			}
		}
		return nil, nil
	})
	return nil
}

func syncDscUserDialogHistories(ctx context.Context, user *models.FpDscUser) error {
	fun := "dscevent.syncDscUserDialogHistories"
	sess := dsc.GetClient(ctx, user.AppID)
	var limit = 90
	var before = ""

	for {
		msgs, err := sess.ChannelMessages(user.PrivChannelID, limit, before, "", "")
		if err != nil {
			logger.Errorf(ctx, "%s get ChannelMessages return err. err:%v. user:%+v", fun, err, user)
			return err
		}
		//
		for _, msg := range msgs {
			// check 最近 7day 数据
			before = msg.ID
			if tm, err := discordgo.SnowflakeTimestamp(msg.ID); err != nil {
				logger.Errorf(ctx, "%s SnowflakeTimestamp err:%v. msgId:%s. msg:%+v", fun, err, msg.ID, msg)
				return err
			} else if tm.Before(time.Now().AddDate(0, 0, -7)) {
				logger.Infof(ctx, "%s SnowflakeTimestamp before 7day. msgId:%s. tm:%+v. msg:%+v", fun, msg.ID, tm, msg)
				break
			}
			// do single msg compare and save
			if err := dsccore.ChannelMessageSrv.CompareAndSaveDscMessageDetail(ctx, user, msg); err != nil {
				logger.Errorf(ctx, "%s CompareAndSaveDscMessageDetail err:%v. userL:%+v. msgId:%s. msg:%+v", fun, err, user, msg.ID, msg)
				return err
			}
		}
		if len(msgs) < limit {
			break
		}
		time.Sleep(utils.RandomSecond())
	}
	return nil
}

type (
	syncUserDialogHistResult struct {
		Err  error
		User *models.FpDscUser
	}
)
