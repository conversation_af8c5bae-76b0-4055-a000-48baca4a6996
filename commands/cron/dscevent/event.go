package dscevent

import (
	"context"
	"ops-ticket-api/internal/dsc"
	"ops-ticket-api/utils"
	"time"

	"github.com/bsm/redislock"
	"github.com/labstack/echo/v4"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
)

var (
	dscEventLockRefreshC = make(chan struct{})
	dscEventLock         *redislock.Lock
	activeSessions       = make(map[string]*dscGatewayEventHandle)
)

func CloseAllLock(ctx context.Context) {
	close(dscEventLockRefreshC)
	if dscEventLock != nil {
		dscEventLock.Release(ctx)
	}
	logger.Infof(ctx, "dscEvent CloseAllLock success.")
}
func ReceiveDscEventsHandle(ctx echo.Context) error {
	var (
		fun = "ReceiveDscEventsHandle"
	)
	// 1.0 get lock
	//var err error
	//for {
	//	dscEventLock, err = rds.GetRdsMux(ctx.Request().Context(), keys.DiscordEventWorkLock)
	//	if err != nil {
	//		if errors.Is(err, redislock.ErrNotObtained) {
	//			logger.Infof(ctx.Request().Context(), "%s get ticket lock fail. has been locked. retry...", fun)
	//			time.Sleep(time.Second * 5)
	//		} else {
	//			logger.Errorf(ctx.Request().Context(), "%s get ticket lock return err. err:%v", fun, err)
	//			time.Sleep(time.Second)
	//		}
	//		continue
	//	}
	//
	//	go rds.RefreshRdsMux(ctx.Request().Context(), dscEventLock, dscEventLockRefreshC)
	//	break
	//}
	logger.Infof(ctx.Request().Context(), "%s start ReceiveDscEvent.", fun)
	startReceiveDscEvent(utils.ContextOnlyValue{ctx.Request().Context()})
	return nil
}

func startReceiveDscEvent(ctx context.Context) {
	appIds := dsc.GetAllAppIds(ctx)
	for _, appId := range appIds {
		appId := appId
		watchSingleDscEvent(ctx, appId)
	}
	logger.Infof(ctx, "startReceiveDscEvent success. appIds:%+v", appIds)

	// 创建定时器
	ticker := time.NewTicker(time.Minute)
	// 在后台运行定时任务
	go func() {
		defer ticker.Stop()
		for {
			select {
			case <-ticker.C:
				syncBotList(ctx)
			case <-ctx.Done():
				logger.Info(ctx, "stopping bot sync ticker")
				return
			}
		}
	}()

}

// 同步bot列表的函数
func syncBotList(ctx context.Context) {
	defer func() {
		if r := recover(); r != nil {
			logger.Error(ctx, "syncBotList panic recovered", logger.Any("error", r))
		}
	}()
	logger.Info(ctx, "syncBotList")
	// 获取当前活跃的bot sessions
	newAppIds := dsc.GetAllAppIds(ctx)
	// 检查新增的bot
	for _, appID := range newAppIds {
		if _, ok := activeSessions[appID]; !ok {
			// 新增bot
			watchSingleDscEvent(ctx, appID)
			logger.Info(ctx, "新bot已添加watchSingleDscEvent", logger.Any("appID", appID))
		} else {
			// 更新bot
			config := dsc.GetAppConfig(ctx, appID)
			if config.BotVersion.After(activeSessions[appID].conf.BotVersion) {
				activeSessions[appID].conf = config
				logger.Info(ctx, "bot已更新 watchSingleDscEvent",
					logger.Any("appID", appID),
					logger.Any("conf", config))
			}
		}
	}

	// 检查需要删除的bot TODO: 暂时关闭
	for appID, _ := range activeSessions {
		found := false
		for _, newAppId := range newAppIds {
			if appID == newAppId {
				found = true
				break
			}
		}
		if !found {
			// 删除不存在的bot session
			activeSessions[appID].sess.Close()
			delete(activeSessions, appID)
			logger.Info(ctx, "已删除不存在的bot,移除watchSingleDscEvent", logger.Any("appID", appID))
		}
	}

}

func watchSingleDscEvent(ctx context.Context, appId string) error {
	dscH := newDscGatewayEventHandle(ctx, appId)
	if err := dscH.StartEvent(); err != nil {
		logger.Fatalf(ctx, "watchSingleDscEvent err. appId:%s err:%v", appId, err)
		return err
	}
	activeSessions[appId] = dscH
	return nil
}

func newDscGatewayEventHandle(ctx context.Context, appId string) *dscGatewayEventHandle {
	conf := dsc.GetAppConfig(ctx, appId)
	if conf == nil {
		return nil
	}
	return &dscGatewayEventHandle{
		ctx:   ctx,
		appId: appId,
		conf:  conf,
		sess:  dsc.GetClient(ctx, appId),
	}
}
