package dscevent

import (
	"context"
	"fmt"
	"github.com/opentracing/opentracing-go"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"ops-ticket-api/internal/dsc"
	"ops-ticket-api/utils"
)

func RefreshGuildUsers() {
	var (
		fun   = "refreshGuildUsers"
		limit = 500
	)
	ctx, cancel := context.WithCancel(logger.WithFields(context.TODO(), logger.String("fun", fun), logger.String("username", "system")))
	span := opentracing.StartSpan("refresh_guild_users")
	ctx = opentracing.ContextWithSpan(ctx, span)
	defer cancel()

	for _, appId := range dsc.GetAllAppIds(ctx) {
		botConf := dsc.GetAppConfig(ctx, appId)
		client := dsc.GetClient(ctx, appId)
		dscEv := newDscGatewayEventHandle(ctx, appId)
		if client == nil {
			logger.Errorf(ctx, "%s appId:%s. client is nil", fun, appId)
			continue
		}
		var lastUserId string
		for {
			fmt.Println("--start get guild member--", lastUserId, limit)
			mm, err := client.GuildMembers(botConf.GuildId, lastUserId, limit)
			if err != nil {
				logger.Errorf(ctx, "%s err:%v", fun, err)
				break
			}
			for _, m := range mm {
				logger.Infof(ctx, "%s guildId:%s memberId:%s. user:%s", fun, botConf.GuildId, m.User.ID, utils.ToJson(m))

				if m == nil || m.User == nil || m.User.ID == "" {
					logger.Errorf(ctx, "%s guild user detail user NilOrEmpty. usser:%s. err:%v", fun, utils.ToJson(m), err)
					continue
				}
				if m.User.ID != "" {
					lastUserId = m.User.ID
				}

				if m.User.Bot {
					logger.Infof(ctx, "%s guild user is bot. ignore. %s.%s user:%s. err:%v", fun, m.User.Username, m.User.GlobalName, utils.ToJson(m), err)
					continue
				}

				if err := dscEv.AddUser(ctx, m.User, botConf.GuildId, m.JoinedAt, false); err != nil {
					logger.Errorf(ctx, "%s AddUser user:%s. err:%v", fun, utils.ToJson(m.User), err)
				}
			}
			if len(mm) < limit {
				logger.Infof(ctx, "%s appId:%s. guildId:%s end. last count:%d", fun, appId, botConf.GuildId, mm)
				break
			}
		}
	}
}
