package cron

import (
	"context"
	"ops-ticket-api/commands/cron/dscevent"
	"ops-ticket-api/internal/dsc"
	"ops-ticket-api/internal/persistence"
	"time"

	"github.com/labstack/echo/v4"
	"github.com/opentracing/opentracing-go"
	"github.com/zeromicro/go-zero/core/stores/redis"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
)

var (
	dscEventLock *redis.RedisLock
)

func InitDscEventConsole(ctx echo.Context) {
	if err := initDscSess(ctx); err != nil { // 初始化 discord client
		logger.Fatalf(ctx.Request().Context(), "initDscSess err:%v", err)
	}

	// reload all guild member
	//dscevent.RefreshGuildUsers()

	//
	//dscevent.AsyncChannelDialog()

	// cron
	go initDscEventCron(ctx)
}
func ShutDownDscEventConsole(ctx context.Context) {
	dscevent.CloseAllLock(ctx)
	dsc.CloseAllSess(ctx)
}

func initDscEventCron(ctx echo.Context) {
	cronJob.AddFunc("@every 10s", func() {
		span := opentracing.StartSpan("cron_dsc_event_run")
		c := opentracing.ContextWithSpan(ctx.Request().Context(), span)
		newC := ctx.Echo().AcquireContext()
		newC.SetRequest(ctx.Request().WithContext(c))
		logger.Info(newC.Request().Context(), "service_discord_gateway_event click log...", logger.Any("time", time.Now()), logger.Any("osEnv", osEnv))
	})
	// 每天同步一次历史数据
	cronJob.AddFunc("10 01 * * *", dscevent.AsyncChannelDialog)
	// 每天 同步下 guild member 的列表
	cronJob.AddFunc("10 00 * * *", dscevent.RefreshGuildUsers)
}

func initDscSess(ctx echo.Context) error {
	list, err := persistence.NewDscInteractions().GetDscBotAll(ctx.Request().Context())
	if err != nil {
		return err
	}
	for _, v := range list {
		err := dsc.AddSess(ctx.Request().Context(), v.AppID, v.BotDesc, v.DecodeBotConfig())
		if err != nil {
			return err
		}
	}

	// 创建定时器
	ticker := time.NewTicker(time.Minute)
	// 在后台运行定时任务
	go func() {
		defer ticker.Stop()
		for {
			select {
			case <-ticker.C:
				syncBotList(ctx)
			case <-ctx.Request().Context().Done():
				logger.Info(ctx.Request().Context(), "stopping bot sync ticker")
				return
			}
		}
	}()
	return nil
}

// 同步bot列表的函数
func syncBotList(ctx echo.Context) {
	defer func() {
		if r := recover(); r != nil {
			logger.Error(ctx.Request().Context(), "syncBotList panic recovered",
				logger.Any("error", r))
		}
	}()
	logger.Info(ctx.Request().Context(), "syncBotList")
	currentBots, err := persistence.NewDscInteractions().GetDscBotAll(ctx.Request().Context())
	if err != nil {
		logger.Error(ctx.Request().Context(), "获取bot列表失败",
			logger.Any("error", err))
		return
	}

	// 获取当前活跃的bot sessions
	activeSessions := dsc.GetAllAppIds(ctx.Request().Context())

	// 检查新增的bot
	for _, bot := range currentBots {
		if !contains(activeSessions, bot.AppID) {
			// 新增bot
			if err := dsc.AddSess(ctx.Request().Context(), bot.AppID, bot.BotDesc, bot.DecodeBotConfig()); err != nil {
				logger.Error(ctx.Request().Context(), "添加新bot失败",
					logger.Any("appID", bot.AppID),
					logger.Any("error", err))
				continue
			}
			logger.Info(ctx.Request().Context(), "新bot已添加",
				logger.Any("appID", bot.AppID))
		} else {
			config := dsc.GetAppConfig(ctx.Request().Context(), bot.AppID)
			// 更新bot
			if bot.UpdatedAt.After(config.BotVersion) {
				if err := dsc.UpdateSessBotConfig(ctx.Request().Context(), bot.AppID, bot.DecodeBotConfig()); err != nil {
					logger.Error(ctx.Request().Context(), "更新bot失败",
						logger.Any("appID", bot.AppID),
						logger.Any("error", err))
					continue
				}
				logger.Info(ctx.Request().Context(), "bot已更新",
					logger.Any("appID", bot.AppID))
			}
		}
	}

	// 检查需要删除的bot TODO: 暂时关闭
	for _, appID := range activeSessions {
		found := false
		for _, bot := range currentBots {
			if bot.AppID == appID {
				found = true
				break
			}
		}
		if !found {
			// 删除不存在的bot session
			dsc.DeleteSess(ctx.Request().Context(), appID)
			logger.Info(ctx.Request().Context(), "已删除不存在的bot",
				logger.Any("appID", appID))
		}
	}
}

func contains(slice []string, str string) bool {
	for _, s := range slice {
		if s == str {
			return true
		}
	}
	return false
}
