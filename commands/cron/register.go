package cron

import (
	"context"

	"github.com/spf13/cobra"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
)

// CronCommand 定义一个定时任务命令
type CronCommand struct {
	Name        string
	Description string
	Schedule    string
	Run         func(ctx context.Context) error
}

var registeredCommands = make(map[string]*CronCommand)

// RegisterCommand 注册一个新的定时任务命令
func RegisterCommand(cmd *CronCommand) {
	registeredCommands[cmd.Name] = cmd
}

// GetCommand 获取已注册的命令
func GetCommand(name string) *CronCommand {
	return registeredCommands[name]
}

// GetAllCommands 获取所有已注册的命令
func GetAllCommands() map[string]*CronCommand {
	return registeredCommands
}

// CreateCobraCommand 为定时任务创建cobra命令
func CreateCobraCommand(cmd *CronCommand) *cobra.Command {
	return &cobra.Command{
		Use:   cmd.Name,
		Short: cmd.Description,
		Long:  cmd.Description + "\nSchedule: " + cmd.Schedule,
		Run: func(c *cobra.Command, args []string) {
			ctx := context.Background()
			logger.Info(ctx, "执行定时任务",
				logger.String("task", cmd.Name),
				logger.String("schedule", cmd.Schedule))

			if err := cmd.Run(ctx); err != nil {
				logger.Error(ctx, "任务执行失败",
					logger.String("task", cmd.Name),
					logger.Any("error", err))
				return
			}
			logger.Info(ctx, "任务执行完成", logger.String("task", cmd.Name))
		},
	}
}
