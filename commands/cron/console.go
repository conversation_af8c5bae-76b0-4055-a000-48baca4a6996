// Copyright 2021 funplus Authors. All Rights Reserved.
// @Description: 异步服务
// @Author: Darcy
// @Date: 2021/11/11 3:15 PM

package cron

import (
	"errors"
	"fmt"
	"github.com/bsm/redislock"
	"github.com/go-redis/redis/v8"
	"github.com/labstack/echo/v4"
	"github.com/opentracing/opentracing-go"
	"github.com/robfig/cron/v3"
	"github.com/spf13/cast"
	"github.com/zeromicro/go-zero/core/fx"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"go.uber.org/zap"
	"net/http"
	"ops-ticket-api/internal/cst"
	"ops-ticket-api/internal/framework/cache/keys"
	"ops-ticket-api/internal/framework/cache/local"
	"ops-ticket-api/internal/framework/cache/rds"
	"ops-ticket-api/internal/persistence"
	"ops-ticket-api/proto/pb"
	"ops-ticket-api/services"
	"ops-ticket-api/services/communication"
	"ops-ticket-api/utils"
	"os"
	"time"
)

var (
	cronJob = cron.New()
	osEnv   = os.Getenv("environment")
)

func isLocal() bool {
	return osEnv == "local"
}

func InitTicketServCmds(ctx echo.Context) {
	go examineInit(ctx)
	go reloadInitMessageTask(ctx)
	go taskMessageServInit(ctx)
	go ticketServCronInit(ctx)
	go surveyInit(ctx)
	go lineServCronInit(ctx)
	go syncDataPlatItemsAndI18nToLocalCommand()
	go ticketCommandInit(ctx)
	go questionTrainingServInit(ctx)         // 处理工单知识库语料训练
	go reloadInitTicketQuestionTask(ctx)     // 重启未完成的训练任务
	if err := initDscSess(ctx); err != nil { // 初始化 discord client
		logger.Fatalf(ctx.Request().Context(), "initDscSess err:%v", err)
	}
}

func ticketCommandInit(ctx echo.Context) {
	// 开启后台线程，定期清理本地缓存的私域信息
	local.PrivateZoneCache.StartCleanup(time.Minute * 10)
}

// 重启工单知识库训练
func reloadInitTicketQuestionTask(ctx echo.Context) {
	time.Sleep(time.Second * 3)
	tasks := persistence.NewTrainingTask().GetAllTrainingTask(map[string]interface{}{
		"status": []int{
			int(pb.TrainingTaskStatus_ProcessTicketStatusInit),
			int(pb.TrainingTaskStatus_ProcessTicketStatusDoing),
		},
	})
	for _, detail := range tasks {
		communication.PushQuestionTraining(detail.LogID)
	}
}

func reloadInitMessageTask(ctx echo.Context) {
	time.Sleep(time.Second * 3)
	tasks := persistence.NewDscMessageTask().GetAllMessageTask(map[string]interface{}{
		"status": []int{
			int(pb.DscMsgTaskStatus_ProcessStatusInit),
			int(pb.DscMsgTaskStatus_ProcessStatusDoing),
		},
	})
	for _, detail := range tasks {
		communication.PushMessageBatchSend(int64(detail.ID))
	}
}

// 批量私信
func taskMessageServInit(ctx echo.Context) {
	go syncDscSendMessage(ctx)
}

// 在这里加锁
func syncDscSendMessage(ctx echo.Context) {
	fun := "DCMessageBatchSend2Seconds"
	time.Sleep(time.Second * 3)
	for {
		newCtx := echo.New().NewContext(&http.Request{}, &echo.Response{})
		// 1. start get learn log id
		id := communication.PopMessageBatchSend()
		if id == 0 {
			logger.Errorf(newCtx.Request().Context(), "%s communication.PopMessageBatchSend return id eq0. %d", fun, id)
			continue
		}
		time.Sleep(time.Second * 2)
		lock, err := rds.GetRdsMux(newCtx.Request().Context(), fmt.Sprintf(keys.DiscordMessageCreateBatchKey, id))
		if err != nil {
			logger.Errorf(newCtx.Request().Context(), "get %s lock return err:%v", fun, err)
			if !errors.Is(err, redislock.ErrNotObtained) { // get lock failed
				time.Sleep(time.Second * 3)
				lock, err = rds.GetRdsMux(newCtx.Request().Context(), fmt.Sprintf(keys.DiscordMessageCreateBatchKey, id))
				if err != nil {
					logger.Errorf(newCtx.Request().Context(), "get %s lock return err:%v", fun, err)
					continue
				}
			} else {
				continue
			}
		}
		refreshC := make(chan struct{})
		go rds.RefreshRdsMux(newCtx.Request().Context(), lock, refreshC)
		// 2. do message create task
		if err := services.ReadyMessageBatchTask(newCtx, id); err != nil {
			logger.Errorf(newCtx.Request().Context(), "%s batchTraining err:%+v", fun, err)
		}
		lock.Release(newCtx.Request().Context())
		close(refreshC)
	}

}

// 工单知识库训练
func questionTrainingServInit(ctx echo.Context) {
	go processTicketQuestionTraining(ctx)
}

// TicketServCronInit 工单服务定时任务
func ticketServCronInit(ctx echo.Context) {
	// ticker log
	cronJob.AddFunc("@every 10s", func() {
		span := opentracing.StartSpan("cron_ticker_run")
		c := opentracing.ContextWithSpan(ctx.Request().Context(), span)
		newC := ctx.Echo().AcquireContext()
		newC.SetRequest(ctx.Request().WithContext(c))
		logger.Info(newC.Request().Context(), "service click log...", logger.Any("time", time.Now()), logger.Any("osEnv", osEnv))
	})
	// 工单 - 执行分单逻辑
	cronJob.AddFunc("@every 1m", func() {
		fun := "TicketAllocAcceptor"
		cc := utils.ContextOnlyValue{ctx.Request().Context()}
		newC := ctx.Echo().AcquireContext()
		newC.SetRequest(ctx.Request().WithContext(cc))
		newC.Set(cst.AccountInfoCtx, "system")
		newC.Set(cst.AccountNameCtx, "system")
		if isLocal() {
			logger.Infof(newC.Request().Context(), "%s is local env, return. osEnv:%s", fun, osEnv)
			return
		}
		if err := services.NewTicketSrv().AutoAllocAcceptor(newC); err != nil {
			logger.Errorf(newC.Request().Context(), "%s AutoAllocAcceptor return err. err:%v", fun, err)
		}
	})

	// 工单 - 玩家端 3天未回复 自动超时
	cronJob.AddFunc("@every 1m", func() {
		fun := "TicketCommun3DWatch->"
		if isLocal() {
			logger.Infof(ctx.Request().Context(), "%s is local env, return. osEnv:%s", fun, osEnv)
			return
		}

		result, err := rds.RCli.ZRangeByScoreWithScores(ctx.Request().Context(), keys.TicketTimeWatchPlayerListV3,
			&redis.ZRangeBy{
				Min:   cast.ToString(time.Now().Add(0 - time.Hour*24).Unix()),
				Max:   "+inf",
				Count: 20,
			}).Result()
		if err != nil {
			logger.Errorf(ctx.Request().Context(), "%s get ticketId err. err:%v", fun, err)
			return
		}
		if len(result) == 0 {
			logger.Infof(ctx.Request().Context(), "%s getListByScore return empty.", fun)
			return
		}
		var rmTicketFromList = func(ticketId uint64) {
			if err := rds.RCli.ZRem(ctx.Request().Context(), keys.TicketTimeWatchPlayerListV3, ticketId).Err(); err != nil {
				logger.Errorf(ctx.Request().Context(), "%s del ticket del err. ticketId:%d. err:%v", fun, ticketId, err)
			}
		}
		for _, v := range result {
			//  min没过期可以直接退出
			if v.Score > float64(utils.NowTimestamp()) {
				return
			}
			lock, err := rds.GetRdsMux(ctx.Request().Context(), keys.TicketRefillTimeOutLockV3+cast.ToString(v.Member))
			if err != nil {
				if errors.Is(err, redislock.ErrNotObtained) {
					logger.Warn(ctx.Request().Context(), "TicketCommun3DWatch get ticket lock return err. notObtained.", zap.Any("ticketId", v.Member), zap.Any("err", err.Error()))
				} else {
					logger.Errorf(ctx.Request().Context(), "%s get ticket lock return err. ticketId:%v. err:%v", fun, v.Member, err)
				}
				continue
			}
			tkRes := services.NewTicketSrv().PlayerCommuTimeOutLogic(ctx, cast.ToUint64(v.Member))
			logger.Infof(ctx.Request().Context(), "%s PlayerCommuTimeOutLogic return result:%+v", fun, tkRes)
			if tkRes.Error != nil {
				lock.Release(ctx.Request().Context())
				logger.Errorf(ctx.Request().Context(), "%s PlayerCommuTimeOutLogic return err. err:%v. result:%+v", fun, tkRes.Error, tkRes)
				continue
			}
			if tkRes.NeedDelWatchKey {
				rmTicketFromList(cast.ToUint64(v.Member))
			}
			lock.Release(ctx.Request().Context())
		}
	})
	// 工单有处理人，2小时内未进行操作，则退回到工单池
	cronJob.AddFunc("@every 1m", func() {
		fun := "TicketAcceptor2HNoAction"
		if isLocal() {
			logger.Infof(ctx.Request().Context(), "%s is local env, return. osEnv:%s", fun, osEnv)
			return
		}

		// get lock
		lock, err := rds.GetRdsMux(ctx.Request().Context(), keys.TicketAcceptor2HNoActionLockV3)
		if err != nil {
			if errors.Is(err, redislock.ErrNotObtained) {
				logger.Warn(ctx.Request().Context(), "get ticket lock failed.", logger.String("fun", fun), logger.Any("err", err))
			} else {
				logger.Errorf(ctx.Request().Context(), "%s get ticket lock return err. err:%v", fun, err)
			}
			return
		}
		refreshC := make(chan struct{})
		defer lock.Release(ctx.Request().Context())
		go rds.RefreshRdsMux(ctx.Request().Context(), lock, refreshC)
		defer close(refreshC)

		// get ticket list
		result, err := rds.RCli.ZRangeByScoreWithScores(ctx.Request().Context(), keys.TicketAcceptor2HNoActionListV3,
			&redis.ZRangeBy{
				Min:   cast.ToString(time.Now().Add(0 - time.Hour*24).Unix()),
				Max:   cast.ToString(time.Now().Add(0 - time.Second).Unix()),
				Count: 100,
			}).Result()
		if err != nil {
			logger.Errorf(ctx.Request().Context(), "%s get ticketId err. err:%v", fun, err)
			return
		}
		if len(result) == 0 {
			logger.Infof(ctx.Request().Context(), "%s getListByScore return empty.", fun)
			return
		}
		var rmTicketFromList = func(ticketId uint64) {
			if err := rds.RCli.ZRem(ctx.Request().Context(), keys.TicketAcceptor2HNoActionListV3, ticketId).Err(); err != nil {
				logger.Errorf(ctx.Request().Context(), "%s del ticket del err. ticketId:%d. err:%v", fun, ticketId, err)
			}
		}

		// mapReduce do logic
		fx.From(func(source chan<- any) {
			for _, v := range result {
				if v.Score > float64(time.Now().Unix()) {
					return
				}
				logger.Infof(ctx.Request().Context(), "%s start dong logic. ticketId:%v. score:%d", fun, v.Member, v.Score)
				source <- cast.ToUint64(v.Member)
			}
		}).Map(func(item any) any {
			// mult do logic
			ticketId, ok := item.(uint64)
			fmt.Println("-----", ticketId, ok)
			res := services.NewTicketSrv().Over2HNoActionLogic(ctx.Request().Context(), ticketId)
			return res
		}, fx.WithWorkers(16)).ForEach(func(item any) { // log the result
			res := item.(pb.PlayerCommuWatchResult)
			if res.Error != nil {
				logger.Errorf(ctx.Request().Context(), "%s mapReduce return err. ticketId:%d. err:%v", fun, res.TicketId, res.Error)
				return
			}
			if res.NeedDelWatchKey == true {
				logger.Infof(ctx.Request().Context(), "%s mapReduce return need del watch key. %+v", fun, res)
				rmTicketFromList(res.TicketId)
			} else {
				logger.Infof(ctx.Request().Context(), "%s mapReduce return No need del watch key. %+v", fun, res)
			}
		})
	})
	// 工单自动回复
	cronJob.AddFunc("@every 10m", func() {

		fun := "TicketSystemReply10Minutes"
		if isLocal() {
			logger.Infof(ctx.Request().Context(), "%s is local env, return. osEnv:%s", fun, osEnv)
			return
		}

		span := opentracing.StartSpan("cron_systemReply_run")
		c := opentracing.ContextWithSpan(ctx.Request().Context(), span)
		newC := ctx.Echo().AcquireContext()
		newC.SetRequest(ctx.Request().WithContext(c))
		account := pb.UserRole_SystemRole.String()
		newC.Set(cst.AccountInfoCtx, account)

		lock, _ := rds.RCli.Client.SetNX(newC.Request().Context(), "ops:ticket:lock:system_reply", 1, 2*time.Minute).Result()
		if !lock {
			logger.Info(newC.Request().Context(), "cron task already running")
			return
		}
		logger.Info(newC.Request().Context(), "cron task start")
		defer logger.Info(newC.Request().Context(), "cron task end")
		ticketList, err := persistence.NewTicket().GetTicketListByUser(newC, account, int(pb.TkStatus_TkStatusProcessing), int(pb.TkStage_TkStageNew))
		if err != nil {
			logger.Error(newC.Request().Context(), "GetTicketListByUser error", zap.String("err", err.Error()))
			return
		}
		if len(ticketList) == 0 {
			logger.Info(newC.Request().Context(), "no system reply ticket need fix")
			return
		}
		for _, tk := range ticketList {
			logger.Info(newC.Request().Context(), "start process system reply ticket",
				zap.Uint64("ticket_id", tk.TicketID), zap.Uint32("cat_id", tk.CatID))
			//tkCatInfo, err := configure.CatInfo(ctx, tk.Project, tk.CatID, map[string]string{
			//	"country": tk.Device.Country,
			//	"channel": tk.Channel,
			//	"sid":     cast.ToString(tk.Sid),
			//})
			//if err != nil {
			//	logger.Error(newC.Request().Context(), "ticket cat info error", zap.String("err", err.Error()))
			//	continue
			//}
			useNewDb := true

			//replyTplID := tkCatInfo.SwiftReplyTplID
			//if replyTplID == 0 {
			//	useNewDb = false
			//	replyTplID = tkCatInfo.ReplyTplID
			//}
			replyTplID := tk.AutoReplyID
			err = services.SystemReply(newC, tk.TicketID, tk.CatID, replyTplID, tk.Lang, useNewDb)
			if err != nil {
				logger.Error(newC.Request().Context(), "system reply ticket process error", zap.String("err", err.Error()))
				continue
			}
		}
	})
	// 新版AI客服工单处理流程
	cronJob.AddFunc("@every 1m", func() {
		fun := "TicketNewAiProcess1Minute"
		if isLocal() {
			logger.Infof(ctx.Request().Context(), "%s is local env, return. osEnv:%s", fun, osEnv)
			return
		}

		cc := utils.ContextOnlyValue{ctx.Request().Context()}
		newC := ctx.Echo().AcquireContext()
		newC.SetRequest(ctx.Request().WithContext(cc))
		newC.Set(cst.AccountInfoCtx, "system")
		newC.Set(cst.AccountNameCtx, "system")
		if err := services.NewTicketSrv().TicketNewAiProcess(newC); err != nil {
			logger.Errorf(newC.Request().Context(), "%s AutoAllocAcceptor return err. err:%v", fun, err)
		}

	})
	// 工单超时回复提醒
	cronJob.AddFunc("@every 1m", func() {
		fun := "TicketOvertimeReply->"
		if isLocal() {
			logger.Infof(ctx.Request().Context(), "%s is local env, return. osEnv:%s", fun, osEnv)
			return
		}

		result, err := rds.RCli.ZRangeByScoreWithScores(ctx.Request().Context(), keys.TicketOvertimeRemarkWatchPlayerList,
			&redis.ZRangeBy{
				Min:   cast.ToString(time.Now().Add(0 - time.Hour*24).Unix()),
				Max:   "+inf",
				Count: 70,
			}).Result()
		if err != nil {
			logger.Errorf(ctx.Request().Context(), "%s get ticketId err. err:%v", fun, err)
			return
		}
		if len(result) == 0 {
			logger.Infof(ctx.Request().Context(), "%s getListByScore return empty.", fun)
			return
		}
		var rmTicketFromList = func(ticketId uint64) {
			if err := rds.RCli.ZRem(ctx.Request().Context(), keys.TicketOvertimeRemarkWatchPlayerList, ticketId).Err(); err != nil {
				logger.Errorf(ctx.Request().Context(), "%s del ticket del err. ticketId:%d. err:%v", fun, ticketId, err)
			}
		}
		for _, v := range result {
			//  最小值没过期可以直接退出
			if v.Score > float64(utils.NowTimestamp()) {
				return
			}
			lock, err := rds.GetRdsMux(ctx.Request().Context(), keys.TicketOvertimeRemarkKey+cast.ToString(v.Member))
			if err != nil {
				if errors.Is(err, redislock.ErrNotObtained) {
					logger.Warn(ctx.Request().Context(), "get ticket lock failed.", logger.String("fun", fun), logger.Any("err", err))
				} else {
					logger.Errorf(ctx.Request().Context(), "%s get ticket lock return err. err:%v", fun, err)
				}
				continue
			}
			// 执行红点逻辑
			tkRes := services.NewTicketSrv().PlayerOverTimeRemindLogic(ctx, cast.ToUint64(v.Member))
			logger.Infof(ctx.Request().Context(), "%s PlayerOverTimeRemindLogic return result:%+v", fun, tkRes)
			if tkRes.Error != nil {
				lock.Release(ctx.Request().Context())
				logger.Errorf(ctx.Request().Context(), "%s PlayerCommuTimeOutLogic return err. err:%v. result:%+v", fun, tkRes.Error, tkRes)
				continue
			}
			if tkRes.NeedDelWatchKey {
				rmTicketFromList(cast.ToUint64(v.Member))
			}
			lock.Release(ctx.Request().Context())
		}
	})

	// 只有global的才执行discord相关定时任务
	release := os.Getenv("release")
	if release == "global" {
		// 将玩家在crm中的付费金额等数据同步到玩家维护关系配置表以及dsc的es中
		cronJob.AddFunc("30 12 * * *", func() {
			logger.Infof(ctx.Request().Context(), "SyncCrmPlayersToDsc start")
			// get lock
			lock, err := rds.GetRdsMux(ctx.Request().Context(), keys.DiscordCrmPlayerSyncKey)
			if err != nil {
				logger.Errorf(ctx.Request().Context(), "get SyncCrmPlayersToDsc lock return err. err:%v", err)
				return
			}
			refreshC := make(chan struct{})
			defer lock.Release(ctx.Request().Context())
			go rds.RefreshRdsMux(ctx.Request().Context(), lock, refreshC)
			defer close(refreshC)
			dest, err := persistence.NewMaintainConfig().FetchAllDscPlayers()
			if err != nil {
				logger.Errorf(ctx.Request().Context(), "FetchAllDscPlayers err:%+v", err)
				return
			}
			if len(dest) == 0 {
				logger.Infof(ctx.Request().Context(), "dsc players empty")
				return
			}
			// 根据dsc 玩家维护关系配置表中的fpid去crm获取累计付费等数据
			// 在玩家维护关系配置表更新这些字段的数据
			// 将这些更新数据同步到es
			for i := range dest {
				dscPlayer := dest[i]
				player, _ := services.NewDscSrv().DiscordPlayer(ctx, dscPlayer.AccountId, dscPlayer.GameProject, dscPlayer.UID)
				mark := map[string]interface{}{
					"id":                   dscPlayer.ID,
					"dsc_user_id":          dscPlayer.DscUserId,
					"game_project":         dscPlayer.GameProject,
					"uid":                  dscPlayer.UID,
					"account_id":           dscPlayer.AccountId,
					"pay_all":              player.TotalPay,
					"pay_last_thirty_days": player.LastThirtyDaysPay,
					"last_login":           player.LastLogin,
					"vip_level":            player.VipLevel,
					"player_nick":          player.PlayerNick,
					"lang":                 player.Lang,
					"sid":                  player.Sid,
				}
				if err = persistence.NewMaintainConfig().UpdatesCrmData(ctx, mark); err != nil {
					logger.Errorf(ctx.Request().Context(), "UpdatesCrmData err:%+v. mark:%+v", err, mark)
					continue
				}
			}
			logger.Infof(ctx.Request().Context(), "SyncCrmPlayersToDsc end")
		})
		// 同步历史discord玩家交互明细数据
		//cronJob.AddFunc("42 03 * * *", func() {
		//	fun := "SyncHistoryDiscordInteractDetail"
		//	elog.Infof("%s cron job start", fun)
		//	// get lock
		//	lock, err := rds.GetRdsMux(ctx.Request().Context(), keys.DiscordHistoryInteractDetailSyncKey)
		//	if err != nil {
		//		logger.Errorf(ctx.Request().Context(), "get %s lock return err:%v", fun, err)
		//		return
		//	}
		//	refreshC := make(chan struct{})
		//	defer lock.Release(ctx.Request().Context())
		//	go rds.RefreshRdsMux(ctx.Request().Context(), lock, refreshC)
		//	defer close(refreshC)
		//	err = persistence.NewDiscordInteract().SyncHistoryInteractDetailData()
		//	if err != nil {
		//		elog.Errorf("%s insert err:%+v", fun, err)
		//		return
		//	}
		//	elog.Infof("%s cron job end", fun)
		//})
		// 查询出昨天玩家的交互明细，插入discord玩家交互明细表，也就是增量更新
		cronJob.AddFunc("30 01 * * *", func() {
			fun := "SyncYesterdayDiscordInteractDetail"
			logger.Infof(ctx.Request().Context(), "%s cron job start", fun)
			// get lock
			lock, err := rds.GetRdsMux(ctx.Request().Context(), keys.DiscordYesterdayInteractDetailSyncKey)
			if err != nil {
				logger.Errorf(ctx.Request().Context(), "get %s lock return err:%v", fun, err)
				return
			}
			refreshC := make(chan struct{})
			defer lock.Release(ctx.Request().Context())
			go rds.RefreshRdsMux(ctx.Request().Context(), lock, refreshC)
			defer close(refreshC)
			err = persistence.NewDiscordInteract().SyncYesterdayInteractDetailData()
			if err != nil {
				logger.Errorf(ctx.Request().Context(), "%s insert err:%+v", fun, err)
				return
			}
			logger.Infof(ctx.Request().Context(), "%s cron job end", fun)
		})
		// 同步历史交互数据量明细数据
		//cronJob.AddFunc("43 03 * * *", func() {
		//	fun := "SyncHistoryDiscordMessageCountDetail"
		//	elog.Infof("%s cron job start", fun)
		//	// get lock
		//	lock, err := rds.GetRdsMux(ctx.Request().Context(), keys.DiscordMessageCountDetailHistorySyncKey)
		//	if err != nil {
		//		logger.Errorf(ctx.Request().Context(), "get %s lock return err:%v", fun, err)
		//		return
		//	}
		//	refreshC := make(chan struct{})
		//	defer lock.Release(ctx.Request().Context())
		//	go rds.RefreshRdsMux(ctx.Request().Context(), lock, refreshC)
		//	defer close(refreshC)
		//	err = persistence.NewDiscordInteract().SyncHistoryMsgCountDetail()
		//	if err != nil {
		//		elog.Errorf("%s insert err:%+v", fun, err)
		//		return
		//	}
		//	elog.Infof("%s cron job end", fun)
		//})
		// 查询出前一天的信息量明细后插入discord信息量明细表，通过定时任务做增量更新。
		cronJob.AddFunc("35 01 * * *", func() {
			fun := "SyncYesterdayDiscordMessageCountDetail"
			logger.Infof(ctx.Request().Context(), "%s cron job start", fun)
			// get lock
			lock, err := rds.GetRdsMux(ctx.Request().Context(), keys.DiscordMessageCountDetailYesterdaySyncKey)
			if err != nil {
				logger.Errorf(ctx.Request().Context(), "get %s lock return err:%v", fun, err)
				return
			}
			refreshC := make(chan struct{})
			defer lock.Release(ctx.Request().Context())
			go rds.RefreshRdsMux(ctx.Request().Context(), lock, refreshC)
			defer close(refreshC)
			err = persistence.NewDiscordInteract().SyncYesterdayMsgCountDetail()
			if err != nil {
				logger.Errorf(ctx.Request().Context(), "%s insert err:%+v", fun, err)
				return
			}
			logger.Infof(ctx.Request().Context(), "%s cron job end", fun)
		})
		// 同步交互明细和信息量明细表里的uid和account_id数据
		cronJob.AddFunc("40 01 * * *", func() {
			fun := "SyncPlayerMaintainConfigToInteractStats"
			logger.Infof(ctx.Request().Context(), "%s cron job start", fun)
			// get lock
			lock, err := rds.GetRdsMux(ctx.Request().Context(), keys.PlayerMaintainConfigSyncKey)
			if err != nil {
				logger.Errorf(ctx.Request().Context(), "get %s lock return err:%v", fun, err)
				return
			}
			refreshC := make(chan struct{})
			defer lock.Release(ctx.Request().Context())
			go rds.RefreshRdsMux(ctx.Request().Context(), lock, refreshC)
			defer close(refreshC)
			err = persistence.NewDiscordInteract().SyncMaintainConfigToInteractStats()
			if err != nil {
				logger.Errorf(ctx.Request().Context(), "%s update err:%+v", fun, err)
				return
			}
			logger.Infof(ctx.Request().Context(), "%s cron job end", fun)
		})
		//查询出前一天的所有conversation明细后插入discord回复时间表，通过定时任务做增量更新。
		cronJob.AddFunc("50 01 * * *", func() {
			fun := "SyncYesterdayDiscordReplyTimeDetail"
			logger.Infof(ctx.Request().Context(), "%s cron job start", fun)
			// get lock
			lock, err := rds.GetRdsMux(ctx.Request().Context(), keys.YesterdayDiscordReplyTimeDSyncKey)
			if err != nil {
				logger.Errorf(ctx.Request().Context(), "get %s lock return err:%v", fun, err)
				return
			}
			refreshC := make(chan struct{})
			defer lock.Release(ctx.Request().Context())
			go rds.RefreshRdsMux(ctx.Request().Context(), lock, refreshC)
			defer close(refreshC)
			tm := time.Now().AddDate(0, 0, -1)
			year, month, day := tm.Date()
			tm = time.Date(year, month, day, 0, 0, 0, 0, tm.Location()) // 只保留年月日部分
			logger.Infof(ctx.Request().Context(), "SyncYesterdayDiscordReplyTimeDetail start")
			err = persistence.NewDiscordInteract().SyncYesterdayDiscordReplyTimeDetail(tm)
			if err != nil {
				logger.Errorf(ctx.Request().Context(), "%s insert err:%+v", fun, err)
				return
			}
			logger.Infof(ctx.Request().Context(), "SyncYesterdayDiscordReplyTimeDetail done")
			logger.Infof(ctx.Request().Context(), "%s cron job end", fun)
		})
		// 更新历史数据中的最后回复人，只在上线时执行一次
		//cronJob.AddFunc("35 03 * * *", func() {
		//	fun := "SyncHistoryLastReplyService"
		//	elog.Infof("%s cron job start", fun)
		//	// get lock
		//	lock, err := rds.GetRdsMux(ctx.Request().Context(), keys.DiscordLastReplyServiceHistorySyncKey)
		//	if err != nil {
		//		logger.Errorf(ctx.Request().Context(), "get %s lock return err:%v", fun, err)
		//		return
		//	}
		//	refreshC := make(chan struct{})
		//	defer lock.Release(ctx.Request().Context())
		//	go rds.RefreshRdsMux(ctx.Request().Context(), lock, refreshC)
		//	defer close(refreshC)
		//	err = persistence.NewDiscordInteract().SyncHistoryLastReplyService()
		//	if err != nil {
		//		elog.Errorf("%s update err:%+v", fun, err)
		//		return
		//	}
		//	elog.Infof("%s cron job end", fun)
		//})
		// 同步标签数据到玩家discord绑定标签表
		//cronJob.AddFunc("35 03 * * *", func() {
		//	fun := "SyncDiscordPlayerTags"
		//	elog.Infof("%s cron job start", fun)
		//	// get lock
		//	lock, err := rds.GetRdsMux(ctx.Request().Context(), keys.DiscordPlayerTagsSyncKey)
		//	if err != nil {
		//		logger.Errorf(ctx.Request().Context(), "get %s lock return err:%v", fun, err)
		//		return
		//	}
		//	refreshC := make(chan struct{})
		//	defer lock.Release(ctx.Request().Context())
		//	go rds.RefreshRdsMux(ctx.Request().Context(), lock, refreshC)
		//	defer close(refreshC)
		//	err = persistence.NewDiscordTag().SyncHistoryTags()
		//	if err != nil {
		//		elog.Errorf("%s sync history tags err:%+v", fun, err)
		//		return
		//	}
		//	elog.Infof("%s cron job end", fun)
		//})

	}
	cronJob.Start()
}
