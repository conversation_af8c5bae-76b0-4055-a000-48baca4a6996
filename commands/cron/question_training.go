package cron

import (
	"context"
	"fmt"
	"github.com/labstack/echo/v4"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"net/http"
	"ops-ticket-api/internal/persistence"
	"ops-ticket-api/proto/pb"
	"ops-ticket-api/services"
	"ops-ticket-api/services/communication"
	"time"
)

// 工单知识库训练流程
func processTicketQuestionTraining(ctx echo.Context) {
	fun := "processTicketQuestionTraining"
	time.Sleep(time.Second * 3)
	for {
		newCtx := echo.New().NewContext(&http.Request{}, &echo.Response{})
		// 1. start get learn log id
		id := communication.PopQuestionTraining()
		if id == 0 {
			logger.Errorf(newCtx.Request().Context(), "%s communication.PopMessageBatchSend return id eq0. %d", fun, id)
			continue
		}
		//lock, err := rds.GetRdsMux(newCtx.Request().Context(), fmt.Sprintf(keys.TicketQuestionTrainKey, id))
		//if err != nil {
		//	logger.Errorf(newCtx.Request().Context(), "get %s lock return err:%v", fun, err)
		//	if !errors.Is(err, redislock.ErrNotObtained) { // get lock failed
		//		time.Sleep(time.Second * 3)
		//		lock, err = rds.GetRdsMux(newCtx.Request().Context(), fmt.Sprintf(keys.TicketQuestionTrainKey, id))
		//		if err != nil {
		//			logger.Errorf(newCtx.Request().Context(), "get %s lock return err:%v", fun, err)
		//			continue
		//		}
		//	} else {
		//		continue
		//	}
		//}
		//refreshC := make(chan struct{})
		//go rds.RefreshRdsMux(newCtx.Request().Context(), lock, refreshC)
		// 2. do message create task
		if err := batchTraining(newCtx.Request().Context(), id); err != nil {
			logger.Errorf(newCtx.Request().Context(), "%s batchTraining err:%+v", fun, err)
		}
		//lock.Release(newCtx.Request().Context())
		//close(refreshC)
	}

}

// batchTraining gpt training_core logic
func batchTraining(ctx context.Context, logId int64) error {
	fun := "batchTraining-->"
	// 等待创建记录完成
	time.Sleep(time.Second)
	// 1.check
	log, err := persistence.NewTrainingTask().GetTrainingLogById(logId)
	if err != nil {
		return fmt.Errorf("%s GetTrainingLogById return err. err:%v. %d", fun, err, logId)
	}
	if log == nil || log.LogID != logId {
		return fmt.Errorf("%s GetTrainingLogById return empty. err:%v. log:%+v %d", fun, err, log, logId)
	}
	if log.Status != int32(pb.TrainingTaskStatus_ProcessTicketStatusInit) && log.Status != int32(pb.TrainingTaskStatus_ProcessTicketStatusDoing) {
		return fmt.Errorf("%s status neq enums.ProcessStatusInit or enums.ProcessStatusDoing log:%+v logId:%d", fun, log, logId)
	}

	// 2. update to doing status
	log.Status = int32(pb.TrainingTaskStatus_ProcessTicketStatusDoing)
	log.UpdatedAt = time.Now().Unix()
	if num, err := persistence.NewTrainingTask().UpdateTrainingLog(map[string]interface{}{
		"status":     log.Status,
		"updated_at": log.UpdatedAt,
	}, map[string]interface{}{
		"log_id": log.LogID,
		"status": int32(pb.TrainingTaskStatus_ProcessTicketStatusInit),
	}); num != 1 || err != nil {
		return fmt.Errorf("%s update status doing fail. log:%+v. effectNum:%d. err:%v", fun, log, num, err)
	}

	// 3. update log status to final
	defer func() {
		log.UpdatedAt = time.Now().Unix()
		if num, err := persistence.NewTrainingTask().UpdateTrainingLog(map[string]interface{}{
			"status":      log.Status,
			"fail_reason": log.FailReason,
			"updated_at":  log.UpdatedAt,
		}, map[string]interface{}{
			"log_id": log.LogID,
			"status": int32(pb.TrainingTaskStatus_ProcessTicketStatusDoing),
		}); num != 1 || err != nil {
			logger.Errorf(ctx, "%s update status final fail. log:%+v. effectNum:%d. err:%v", fun, log, num, err)
		}
	}()

	// 4. do training_core logic
	if err := services.NewBatchTrainingSrv(ctx, log).Handle(); err != nil {
		logger.Errorf(ctx, "%s batchTraining.Handle return err. err:%v. log:%+v. traceid:%v", fun, err, log, ctx.Value("traceid"))
		log.Status = int32(pb.TrainingTaskStatus_ProcessTicketStatusFail)
		log.FailReason = err.Error()
		return err
	} else {
		log.Status = int32(pb.TrainingTaskStatus_ProcessTicketStatusSuccess)
	}
	return nil
}
