package cron

import (
	"context"
	"errors"
	"fmt"
	"github.com/avast/retry-go"
	"github.com/bsm/redislock"
	"github.com/jinzhu/now"
	"github.com/labstack/echo/v4"
	"github.com/opentracing/opentracing-go"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"net/http"
	"ops-ticket-api/internal/cst"
	"ops-ticket-api/internal/framework/cache/keys"
	"ops-ticket-api/internal/framework/cache/rds"
	"ops-ticket-api/internal/persistence"
	"ops-ticket-api/models"
	"ops-ticket-api/proto/pb"
	"ops-ticket-api/services"
	"ops-ticket-api/services/survey"
	"ops-ticket-api/utils"
	"time"
)

var (
	cmdCtx echo.Context
)

func surveyInit(c echo.Context) {
	cmdCtx = c.Echo().AcquireContext()
	cmdCtx.SetRequest((&http.Request{}).WithContext(context.TODO()))
	// discard定时推送调查问卷
	cronJob.AddFunc("01 * * * *", autoGenAllGameSurveyLinks)
	//go func() {
	//	time.Sleep(time.Second * 7)
	//	autoGenAllGameSurveyLinks()
	//}()
}

func autoGenAllGameSurveyLinks() {
	var (
		fun         = "Command.SyncDiscordSurveyDetail"
		ctx         = context.WithValue(context.TODO(), "fun", fun)
		currentTime = utils.NowTimestamp()
		cancel      context.CancelFunc
	)
	ctx, cancel = context.WithCancel(ctx)
	defer cancel()
	span := opentracing.StartSpan("cron_survey_run")
	ctx = opentracing.ContextWithSpan(ctx, span)
	logger.Infof(ctx, "%s cron job start", fun)
	// get lock
	lock, err := rds.GetRdsMux(ctx, keys.SurveyDoGenLinkLock)
	if err != nil {
		if errors.Is(err, redislock.ErrNotObtained) {
			logger.Warn(ctx, "Command.SyncDiscordSurveyDetail get ticket lock failed. notObtained.")
		} else {
			logger.Errorf(ctx, "%s get lock return err. err:%v", fun, err)
		}
		return
	}
	refreshC := make(chan struct{})
	defer lock.Release(ctx)
	go rds.RefreshRdsMux(ctx, lock, refreshC)
	defer close(refreshC)

	// 1. 获取推送任务
	dest, err := persistence.NewSurveyConfig().GetSurveyConfigEnableList(ctx)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return
		}
		logger.Errorf(ctx, "%s GetSurveyConfigEnableList err:%+v", fun, err)
		return
	}
	for _, surveyCfg := range dest {
		// 生效时间在当前时间之后的跳过，只推送生效时间在当前时间之前的
		if utils.TimeStrToDayUnix(surveyCfg.Data.EffectiveTime) >= uint64(currentTime) {
			continue
		}
		// 检查是否是推送日期 -- 星期几
		if !(&models.FpDscSurveyConfig{}).CheckWeekDaySame(time.Now().Weekday(), surveyCfg.Data.PushWeek) {
			continue
		}
		// 检测是否是 推送小时 -- 当前小时
		if !(&models.FpDscSurveyConfig{}).CheckCurrencyHourSame(time.Now().Hour(), surveyCfg.Data.PushTime) {
			continue
		}
		nCtx := cmdCtx.Echo().AcquireContext()
		nCtx.SetRequest((&http.Request{}).WithContext(ctx))
		nCtx.Set(cst.AccountInfoCtx, "system")
		nCtx.Set(cst.AccountNameCtx, "system")

		if err := genSingleGameSurveyRecord(nCtx, surveyCfg.Data); err != nil {
			logger.Errorf(ctx, "%s genSingleGameSurveyRecord err:%+v. detail:%+v. ", fun, err, surveyCfg.Data)
		}
		logger.Infof(ctx, "%s game_project:%s has finished. detail:%+v", fun, surveyCfg.Data.Project, surveyCfg.Data)
	}
	logger.Infof(ctx, "%s cron job end", fun)
}

func genSingleGameSurveyRecord(ctx echo.Context, detail *pb.SurveyEditReq) error {
	var (
		fun                 = "Command.GenSingleGameSurveyRecord"
		_getCheckCacheKeysF = func() []string { // 获取 check cache key
			_year, _week := time.Now().ISOWeek()
			var _ck []string
			switch detail.PushCycle {
			case pb.SurveyPushCycle_SurveyPushCycleEveryWeek:
				_ck = append(_ck, fmt.Sprintf(keys.SurveyPushCycleEveryWeek, detail.Project, _year, _week))
			case pb.SurveyPushCycle_SurveyPushCycleEveryTwoWeeks:
				_ck = append(_ck, fmt.Sprintf(keys.SurveyPushCycleEveryWeek, detail.Project, _year, _week))
				_year, _week = time.Now().Add(0 - time.Hour*24*7).ISOWeek()
				_ck = append(_ck, fmt.Sprintf(keys.SurveyPushCycleEveryWeek, detail.Project, _year, _week))
			case pb.SurveyPushCycle_SurveyPushCycleEveryMonth:
				_ck = append(_ck, fmt.Sprintf(keys.SurveyPushCycleEveryMonth, detail.Project, time.Now().Month()))
			}
			return _ck
		}
		_setCheckCacheKeysF = func() error { // 设置 check cache key
			_year, _week := time.Now().ISOWeek()
			var rdsKey string
			switch detail.PushCycle {
			case pb.SurveyPushCycle_SurveyPushCycleEveryWeek, pb.SurveyPushCycle_SurveyPushCycleEveryTwoWeeks:
				rdsKey = fmt.Sprintf(keys.SurveyPushCycleEveryWeek, detail.Project, _year, _week)
			case pb.SurveyPushCycle_SurveyPushCycleEveryMonth:
				rdsKey = fmt.Sprintf(keys.SurveyPushCycleEveryMonth, detail.Project, time.Now().Month())
			}
			if rdsKey == "" {
				return fmt.Errorf("rdsKey is empty. detail:%+v", detail)
			}

			err := retry.Do(func() error {
				if _innerErr := rds.NewRCache().SetEX(ctx.Request().Context(), rdsKey, 1, time.Hour*24*60).Err(); _innerErr != nil {
					logger.Errorf(ctx.Request().Context(), "%s SetEX survey cache key return err:%+v. key:%s. detail:%+v.", fun, _innerErr, rdsKey, detail)
					return _innerErr
				}
				return nil
			}, retry.Attempts(3), retry.Delay(1*time.Second), retry.LastErrorOnly(true))
			if err != nil {
				logger.Errorf(ctx.Request().Context(), "%s SetEX survey cache key final return err:%+v. key:%s. detail:%+v.", fun, err, rdsKey, detail)
				return err
			}
			return err
		}
		_getStartEndTmF = func() (int64, int64) {
			var (
				todayStart = now.BeginningOfDay()
				_startTm   int64
				_endTm     int64
			)
			switch detail.PushCycle {
			case pb.SurveyPushCycle_SurveyPushCycleEveryWeek:
				_startTm, _endTm = todayStart.AddDate(0, 0, -7).Unix(), todayStart.Unix()
			case pb.SurveyPushCycle_SurveyPushCycleEveryTwoWeeks:
				_startTm, _endTm = todayStart.AddDate(0, 0, -14).Unix(), todayStart.Unix()
			case pb.SurveyPushCycle_SurveyPushCycleEveryMonth:
				_startTm, _endTm = todayStart.AddDate(0, -1, 0).Unix(), todayStart.Unix()
			}
			return _startTm, _endTm
		}
		_genExpireTmF = func() time.Time {
			if osEnv == "test" { //  minutes * 10 * expire_day
				return time.Now().Add(time.Minute * time.Duration(int64(detail.ExpireTime)) * 10)
			}
			return time.Now().Add(time.Hour * time.Duration(int64(detail.ExpireTime)) * 24)
		}
	)

	// 1. check 是否需要生成问卷
	ck := _getCheckCacheKeysF()
	val, err := rds.NewRCache().MGet(ctx.Request().Context(), ck...).Result()
	if err != nil {
		logger.Errorf(ctx.Request().Context(), "%s MGet survey cache key return err:%+v. keys:%+v. detail:%+v.", fun, err, ck, detail)
		return err
	}
	for _, v := range val {
		if v != nil { // 已经生成过， 无需再重复升成 -- return
			logger.Warn(ctx.Request().Context(), "survey has been generated.", zap.String("fun", fun), zap.Any("detail", detail), zap.Any("check_keys", ck))
			return nil
		}
	}
	// 2.0 满足条件 - 保存本次已操作缓存，防止多次执行
	defer _setCheckCacheKeysF()

	// 2. 获取需要生成文件的人员列表
	start, end := _getStartEndTmF()
	// todo here 修改返回数据 当前需要保存的各个字段
	players, err := persistence.NewDiscordInteract().FetchPushCycleInteractPlayers(ctx.Request().Context(), detail.Project, start, end, nil)
	if err != nil {
		logger.Errorf(ctx.Request().Context(), "%s FetchPushCycleInteractPlayers err:%+v. start:%d. end:%d. ", fun, err, start, end)
		return err
	}
	// 3.0 生成 / 获取 batch_log_id
	batchLog, err := persistence.NewSurveyConfig().GenBatchSurveyLog(ctx.Request().Context(), detail.Project)
	if err != nil || batchLog == nil || batchLog.ID == 0 {
		logger.Errorf(ctx.Request().Context(), "%s GenBatchSurveyLog return err. project:%s. err:%v", fun, detail.Project, err)
		return err
	}

	// 3. 生成链接
	uidAndContentMap := make(map[int64]string)
	for _, player := range players {
		if player.Uid == 0 {
			logger.Infof(ctx.Request().Context(), "%s player uid is 0. continue. player:%+v", fun, player)
			continue
		}
		// 3.1 gen request param
		req := &pb.SurveyGenLinkReq{
			Project:     detail.Project,
			Uid:         int64(player.Uid),
			Lang:        player.Lang,
			SurveyId:    int64(detail.Id),
			IsPublic:    false,
			DscUserId:   player.DscUserId,
			ExpireAt:    _genExpireTmF().Unix(), // 失效时间
			AccountName: "",
			BatchId:     batchLog.ID,
		}
		var momentAttrs = &pb.SurveyLinkParamDf_Attrs{
			Maintainer:       player.Maintainer,
			LastReplyService: player.LastReplyService,
			Processors:       player.Processors,
			DscUserId:        player.DscUserId,
			DscChannelId:     player.PrivChannelId,
			DscBotId:         player.AppId,
			DscUserName:      player.DscNickName,
			Uid:              player.Uid,
			AccountId:        player.AccountId,
		}
		// 3.2 check has been generated
		if has, err := persistence.NewSurveyConfig().SurveyLinksExistCheck(ctx.Request().Context(), map[string]interface{}{
			"project":     detail.Project,
			"dsc_user_id": player.DscUserId,
			"survey_id":   detail.Id,
			"batch_id":    batchLog.ID,
			"is_public":   0,
		}); err != nil {
			logger.Errorf(ctx.Request().Context(), "%s SurveyLinksExistCheck return err:%+v. detail:%+v", fun, err, detail)
			continue
		} else if has {
			logger.Infof(ctx.Request().Context(), "%s SurveyLinksExistCheck has been generated. detail:%+v", fun, detail)
			continue
		}
		// 3.3 gen survey link
		resp, err := survey.SurveyGenLinks(ctx, req, momentAttrs)
		if err != nil {
			logger.Errorf(ctx.Request().Context(), "%s SurveyGenLinks return err:%+v. detail:%+v", fun, err, detail)
			return err
		}
		uidAndContentMap[int64(player.Uid)] = resp.Link
	}
	logger.Infof(ctx.Request().Context(), "%s gen survey link len.%d %s. surveyId:%d. detail:%+v. ", fun, len(uidAndContentMap), detail.Project, detail.Id, uidAndContentMap)

	// 4. 推送消息
	time.Sleep(time.Second * 2)
	linkLists, err := persistence.NewSurveyConfig().GetBatchSurveyLinksBySurveyId(ctx.Request().Context(), detail.Project, int64(detail.Id), batchLog.ID)
	if err != nil {
		logger.Errorf(ctx.Request().Context(), "%s GetBatchSurveyLinksBySurveyId return err:%+v. detail:%+v", fun, err, detail)
		return err
	}
	logger.Infof(ctx.Request().Context(), "%s link user lists .len:%d. project:%s", fun, len(linkLists), detail.Project)

	// do send discord message
	if err := services.NewDscSrv().AsyncDiffContentBatchCreate(ctx, detail.Project, linkLists); err != nil {
		logger.Errorf(ctx.Request().Context(), "%s AsyncDiffContentBatchCreate return err:%+v. detail:%+v", fun, err, detail)
		return err
	}
	// 3. 执行生成问卷
	return nil
}
