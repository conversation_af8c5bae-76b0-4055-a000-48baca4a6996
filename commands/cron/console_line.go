package cron

import (
	"github.com/labstack/echo/v4"
	"github.com/spf13/cast"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"go.uber.org/zap"
	"net/http"
	"ops-ticket-api/internal/framework/cache/keys"
	"ops-ticket-api/internal/framework/cache/rds"
	"ops-ticket-api/internal/framework/cfg"
	"ops-ticket-api/internal/persistence"
	"ops-ticket-api/services"
	"time"
)

func lineServCronInit(ctx echo.Context) {
	cronJob.AddFunc("20 12 * * *", syncLineMainPlayerCrmDetail) // crm 玩家数据同步到line
	go func() {
		time.Sleep(time.Second * 10)
		syncLineMainPlayerCrmDetail()
	}()
}

// 将玩家在crm中的付费金额等数据同步到玩家维护关系配置表以及line的es中
func syncLineMainPlayerCrmDetail() {
	ctx := echo.New().NewContext(&http.Request{}, &echo.Response{})
	if !cfg.BeGlobal() {
		logger.Infof(ctx.Request().Context(), "SyncCrmPlayersToLine not in global env, return.")
		return
	}
	logger.Infof(ctx.Request().Context(), "SyncCrmPlayersToLine start...")
	// get lock
	lock, err := rds.GetRdsMux(ctx.Request().Context(), keys.LineCrmPlayerSyncKey)
	if err != nil {
		logger.Warn(ctx.Request().Context(), "get SyncCrmPlayersToLine lock return err", zap.Any("err", err))
		return
	}
	refreshC := make(chan struct{})
	defer lock.Release(ctx.Request().Context())
	go rds.RefreshRdsMux(ctx.Request().Context(), lock, refreshC)
	defer close(refreshC)
	dest, err := persistence.NewLineMaintainConfig().FetchAllLinePlayers()
	if err != nil {
		logger.Errorf(ctx.Request().Context(), "FetchAllLinePlayers err:%+v", err)
		return
	}
	if len(dest) == 0 {
		logger.Warn(ctx.Request().Context(), "FetchAllLinePlayers return dest.len eq0")
		return
	}
	// 根据line玩家维护关系配置表中的fpid去crm获取累计付费等数据
	// 在玩家维护关系配置表更新这些字段的数据
	// 将这些更新数据同步到es
	for i := range dest {
		linePlayer := dest[i]
		player, _ := services.NewDscSrv().DiscordPlayer(ctx, linePlayer.AccountId, linePlayer.Project, linePlayer.UID)
		mark := map[string]interface{}{
			"id":                   linePlayer.ID,
			"line_user_id":         linePlayer.LineUserId,
			"project":              linePlayer.Project,
			"uid":                  linePlayer.UID,
			"account_id":           linePlayer.AccountId,
			"sid":                  cast.ToInt(linePlayer.Sid),
			"pay_all":              player.TotalPay,
			"pay_last_thirty_days": player.LastThirtyDaysPay,
			"last_login":           player.LastLogin,
			"vip_level":            player.VipLevel,
			"player_nick":          player.PlayerNick,
			"lang":                 player.Lang,
		}
		if err = persistence.NewLineMaintainConfig().UpdatesCrmData(ctx, mark); err != nil {
			logger.Errorf(ctx.Request().Context(), "UpdatesCrmData err:%+v. mark:%+v", err, mark)
			continue
		}
	}
	logger.Infof(ctx.Request().Context(), "SyncCrmPlayersToLine end. update count:%d", len(dest))
}
