package scripts

import (
	"context"
	"fmt"
	"github.com/opentracing/opentracing-go"
	"github.com/spf13/cast"
	"github.com/spf13/cobra"
	"github.com/uber/jaeger-client-go"
	"github.com/xuri/excelize/v2"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"go.uber.org/zap"
	"ops-ticket-api/internal/framework/database"
	"ops-ticket-api/models"
	"ops-ticket-api/utils"
	"time"
)

func SyncTicketReportTmpLogicGroupByCat(c *cobra.Command, args []string) {
	fun := "SyncTicketReportTmpLogicGroupByCat "
	ctx := context.TODO()
	//trace := opentracing.GlobalTracer()
	span := opentracing.StartSpan("SyncTicketReportTmpLogicGroupByCat_script")
	defer span.Finish()
	if sc, ok := span.Context().(jaeger.SpanContext); ok {
		fmt.Println(fun, "soc traceid", sc.TraceID().String())
		fmt.Println(fun, "soc traceid", sc.SpanID().String())
	}
	ctx = opentracing.ContextWithSpan(ctx, span)
	ctx = logger.WithFields(ctx, []zap.Field{
		zap.String("type", "scrip"),
		zap.Any("args", args),
		zap.String("func", fun)}...)
	span = opentracing.SpanFromContext(ctx)
	if len(args) < 2 {
		logger.Infof(ctx, "%s. args.len eq0. args:%+v", fun, args)
		return
	}
	gameProject := args[0]
	if gameProject == "" {
		logger.Errorf(ctx, "%s game_project empty. %+v", fun, args)
		return
	}
	if args[1] == "forkDo" {
		logger.Infof(ctx, "%s forkDo logic start. args:%+v", fun, args)
	}
	logger.Infof(ctx, "%s start logic.", fun)

	subCtx, cancel := context.WithCancel(ctx)
	defer cancel()
	// get db
	var newTicketDb, oldTicketDb = database.Db().WithContext(subCtx), database.GetOldTicketDb().WithContext(subCtx)
	//
	type (
		singleCatDf struct {
			Desc          string
			OneLevelCatId uint32
			CatIds        []uint64
		}
		singleTmDf struct {
			Start time.Time
			End   time.Time
		}
		singleTkDtDf struct {
			TicketId       uint64 `json:"ticket_id"`
			Project        string `json:"project"`
			Uuid           string `json:"uuid"`
			CatId          uint32 `json:"cat_id"`
			AccountId      string `json:"account_id"`
			ConversionNode uint   `json:"conversion_node"`
			Status         uint   `json:"status"`
			ClosedAt       uint64 `json:"closed_at"`
			CreatedAt      uint64 `json:"created_at"`
			EvaluateAt     uint64 `json:"evaluate_at"`
			Csi            uint64 `json:"csi"`
			Nps            uint64 `json:"nps"`
		}
	)
	var catOneLevel = make(map[uint32]string, 0)
	var allCatList = make([]singleCatDf, 0)
	{ // get one Cat level
		var _pCat = make([]map[string]interface{}, 0)
		if err := oldTicketDb.Model(&models.FpOpsCategory{}).Where("project = ? AND one_level = 0 AND scope=0", gameProject).Select("cat_id,category").Find(&_pCat).Error; err != nil {
			logger.Errorf(ctx, "%s get one level cat return err. game_project:%s err:%v", fun, gameProject, err)
			return
		}
		for _, v := range _pCat {
			catOneLevel[cast.ToUint32(v["cat_id"])] = cast.ToString(v["category"])
		}
		if len(catOneLevel) == 0 {
			logger.Errorf(ctx, "%s get one level cat count eq0. game_project:%s", fun, gameProject)
			return
		}
		for _pcId, desc := range catOneLevel {
			var cids []uint64
			err := oldTicketDb.Model(&models.FpOpsCategory{}).
				Where("project = ? AND one_level in (?)", gameProject, _pcId).
				Pluck("cat_id", &cids).Error
			logger.Info(ctx, "cids", logger.Any("cids", cids), logger.String("fun", fun), logger.Any("parentCatId", _pcId), logger.Any("parenCatDesc", desc), logger.Any("err", err))
			if err != nil {
				logger.Errorf(ctx, "%s cate count return err. game_project:%s err:%v. count:%d", fun, gameProject, err, len(cids))
				return
			}
			if len(cids) == 0 {
				logger.Errorf(ctx, "%s cate count eq0. game_project:%s. parentId:%d. desc:%s", fun, gameProject, _pcId, desc)
				continue
			}
			allCatList = append(allCatList, singleCatDf{Desc: fmt.Sprintf("%s.%s[%d]", gameProject, desc, _pcId), OneLevelCatId: _pcId, CatIds: cids})
		}
		if len(allCatList) == 0 {
			logger.Errorf(ctx, "%s allCatList eq0. game_project:%s", fun, gameProject)
			return
		}
	}

	var allStatData = make([][]string, 0)
	var allStatDataDetails = make([][][]string, 0)
	for _, tm := range []singleTmDf{
		{time.Date(2024, 05, 01, 0, 0, 0, 0, time.UTC), time.Date(2024, 06, 01, 0, 0, 0, 0, time.UTC)},
		{time.Date(2024, 06, 01, 0, 0, 0, 0, time.UTC), time.Date(2024, 07, 01, 0, 0, 0, 0, time.UTC)},
	} { // 两次
		var oneMothStatDataDetails = make([][]string, 0)
		for _, _catIds := range allCatList {
			// get user detail
			allStatData = append(allStatData, []string{tm.Start.Format("2006-01"), _catIds.Desc, utils.ToJson(_catIds.CatIds)}, []string{"", ""})
			// 1. 查询平均处理时长
			{ // 8:回复&关单；7:拒单；5: 3天未回复-超时关单
				for _, v := range []map[string]interface{}{
					{"desc": "平均处理时长1", "node": []int{8, 7, 5}},
					{"desc": "平均处理时长2", "node": []int{8, 5}},
					{"desc": "平均处理时长3", "node": []int{8}},
				} {
					var tkDetail []*singleTkDtDf
					query := newTicketDb.Model(&models.FpOpsTickets{}).Where("project = ? AND created_at > ? AND created_at < ? AND cat_id in (?)",
						gameProject, tm.Start.Unix(), tm.End.Unix(), _catIds.CatIds).
						Where("closed_at >= created_at").
						Where("conversion_node in (?)", v["node"]).
						Select("ticket_id,project,conversion_node,status,closed_at,created_at")
					if err := query.Find(&tkDetail).Error; err != nil {
						logger.Errorf(ctx, "%s avg processing tm query.Find err:%v", fun, err)
						return
					}
					var _cout, _sumProcessingTime uint64
					for _, row := range tkDetail {
						//fmt.Println(row)
						_cout++
						cost := row.ClosedAt - row.CreatedAt
						_sumProcessingTime = _sumProcessingTime + cost
					}
					allStatData = append(allStatData, []string{
						tm.Start.Format("2006-01"), gameProject, cast.ToString(v["desc"]),
						utils.ResolveTimeStr(_sumProcessingTime, uint32(_cout)),
						"conversion_node" + utils.ToJson(v["node"]),
						fmt.Sprintf("count:%d. sumProcessingTime:%d(s)", _cout, _sumProcessingTime),
					})
				}
			}
			allStatData = append(allStatData, []string{"", ""}, []string{"", ""})

			// 2. 24小时完结单数
			{ // 8:回复&关单；7:拒单；5: 3天未回复-超时关单
				for _, v := range []map[string]interface{}{
					{"desc": "24小时完结单数1", "node": []int{8, 7, 5}},
					{"desc": "24小时完结单数2", "node": []int{8, 5}},
					{"desc": "24小时完结单数3", "node": []int{8}},
				} {
					var tkDetail []*singleTkDtDf
					query := newTicketDb.Model(&models.FpOpsTickets{}).Where("project = ? AND created_at > ? AND created_at < ? AND cat_id in (?)",
						gameProject, tm.Start.Unix(), tm.End.Unix(), _catIds.CatIds).
						Where("closed_at >= created_at").
						Where("closed_at - created_at < ?", 24*3600).
						Where("conversion_node in (?)", v["node"]).
						Select("ticket_id,project,conversion_node,status,closed_at,created_at")
					if err := query.Find(&tkDetail).Error; err != nil {
						logger.Errorf(ctx, "%s avg processing tm query.Find err:%v", fun, err)
						return
					}
					var _cout, _sumProcessingTime uint64
					for _, row := range tkDetail {
						//fmt.Println(row)
						_cout++
						cost := row.ClosedAt - row.CreatedAt
						_sumProcessingTime = _sumProcessingTime + cost
					}
					allStatData = append(allStatData, []string{
						tm.Start.Format("2006-01"), gameProject, cast.ToString(v["desc"]),
						utils.ResolveTimeStr(_sumProcessingTime, uint32(_cout)),
						"conversion_node" + utils.ToJson(v["node"]),
						fmt.Sprintf("count:%d. sumProcessingTime:%d(s)", _cout, _sumProcessingTime),
					})
				}
			}
			allStatData = append(allStatData, []string{"", ""}, []string{"", ""})

			// 3. 工单完成24小时内进行评价的数据明细表
			{
				// detail
				var tkDetail []*singleTkDtDf
				query := newTicketDb.Model(&models.FpOpsTickets{}).Where("project = ? AND created_at > ? AND created_at < ? AND cat_id in (?)",
					gameProject, tm.Start.Unix(), tm.End.Unix(), _catIds.CatIds).
					Where("closed_at >= created_at AND evaluate_at >= closed_at").
					Where("evaluate_at - closed_at < ?", 24*3600).
					Where("conversion_node not in (?)", []int{5, 7}).
					Select("ticket_id,project,cat_id,uuid,account_id,conversion_node,status,closed_at,created_at,evaluate_at,csi,nps")
				if err := query.Find(&tkDetail).Error; err != nil {
					logger.Errorf(ctx, "%s get ticket detail query.Find err:%v", fun, err)
					return
				}
				for _, row := range tkDetail {
					//fmt.Println(row)
					oneMothStatDataDetails = append(oneMothStatDataDetails, []string{cast.ToString(row.TicketId), row.Project,
						row.AccountId, row.Uuid, cast.ToString(_catIds.OneLevelCatId), catOneLevel[_catIds.OneLevelCatId],
						cast.ToString(row.CatId), utils.TimeFormat(int64(row.CreatedAt)), utils.TimeFormat(int64(row.ClosedAt)),
						utils.TimeFormat(int64(row.EvaluateAt)), cast.ToString(row.Csi), cast.ToString(row.Nps)})
				}
			}
		}
		oneMothStatDataDetails = append([][]string{
			[]string{tm.Start.Format("2006-01"), gameProject, "工单完成24小时内进行评价的数据明细表(注：完成时长不含超时和拒单)"},
			[]string{"", ""}, []string{"", ""},
			[]string{"id", "project", "fpid", "uuid", "oneLevelCatId", "oneLevelCatDesc", "catId", "创建时间", "完成时间", "评价时间", "评分", "NPS"},
		}, oneMothStatDataDetails...)
		allStatDataDetails = append(allStatDataDetails, oneMothStatDataDetails)
	}
	// tips
	allStatData = append(allStatData, []string{"", ""}, []string{"", ""},
		[]string{"Tips", "conversion_node数值说明：// 8:回复&关单；7:拒单；5: 3天未回复-超时关单"},
		[]string{"", "所有问题一级分类:", utils.ToJson(catOneLevel)},
	)

	// print stdout
	for i, v := range allStatData {
		fmt.Printf("i=:%d. value=:%+v\n", i, v)
	}
	for i, v := range allStatDataDetails {
		for j, vv := range v {
			fmt.Printf("i=:%d. j=:%d. value=:%+v\n", i, j, vv)
			if j > 10 {
				break
			}
		}
	}
	// save data  to excel
	allStatDataDetails = append([][][]string{allStatData}, allStatDataDetails...)
	file, err := saveDataToExcel(allStatDataDetails...)
	if err != nil {
		logger.Errorf(ctx, "%s saveDataToExcel err:%v", fun, err)
		return
	}
	logger.Infof(ctx, "%s saveDataToExcel success. file:%s", fun, file)
}

func SyncTicketReportTmpLogic(c *cobra.Command, args []string) {
	fun := "SyncTicketReportTmpLogic "
	ctx := context.TODO()
	//trace := opentracing.GlobalTracer()
	span := opentracing.StartSpan("SyncTicketReportTmpLogic_script")
	defer span.Finish()
	if sc, ok := span.Context().(jaeger.SpanContext); ok {
		fmt.Println(fun, "soc traceid", sc.TraceID().String())
		fmt.Println(fun, "soc traceid", sc.SpanID().String())
	}
	ctx = opentracing.ContextWithSpan(ctx, span)
	ctx = logger.WithFields(ctx, []zap.Field{
		zap.String("type", "scrip"),
		zap.Any("args", args),
		zap.String("func", fun)}...)
	span = opentracing.SpanFromContext(ctx)
	if len(args) < 2 {
		logger.Infof(ctx, "%s. args.len eq0. args:%+v", fun, args)
		return
	}
	gameProject := args[0]
	if gameProject == "" {
		logger.Errorf(ctx, "%s game_project empty. %+v", fun, args)
		return
	}
	if args[1] == "forkDo" {
		logger.Infof(ctx, "%s forkDo logic start. args:%+v", fun, args)
	}
	logger.Infof(ctx, "%s start logic.", fun)

	var bugExceptParentCatIds = map[string][]int{
		//"mo_global": {1072, 1073, 2142, 2163, 2183},
		"mo_global": {1073, 2163, 2183},
	}
	if _, ok := bugExceptParentCatIds[gameProject]; !ok {
		logger.Errorf(ctx, "%s game_project not in bugExceptParentCatIds. game_project:%s", fun, gameProject)
		return
	}

	subCtx, cancel := context.WithCancel(ctx)
	defer cancel()
	// get db
	var newTicketDb, oldTicketDb = database.Db().WithContext(subCtx), database.GetOldTicketDb().WithContext(subCtx)
	//
	type (
		singleCatDf struct {
			Desc   string
			CatIds []uint64
		}
		singleTmDf struct {
			Start time.Time
			End   time.Time
		}
		singleTkDtDf struct {
			TicketId       uint64 `json:"ticket_id"`
			Project        string `json:"project"`
			Uuid           string `json:"uuid"`
			AccountId      string `json:"account_id"`
			ConversionNode uint   `json:"conversion_node"`
			Status         uint   `json:"status"`
			ClosedAt       uint64 `json:"closed_at"`
			CreatedAt      uint64 `json:"created_at"`
			EvaluateAt     uint64 `json:"evaluate_at"`
			Csi            uint64 `json:"csi"`
			Nps            uint64 `json:"nps"`
		}
	)
	var exceptCatIds []uint64
	err := oldTicketDb.Model(&models.FpOpsCategory{}).
		Where("project = ? AND one_level in (?)", gameProject, bugExceptParentCatIds[gameProject]).
		Pluck("cat_id", &exceptCatIds).Error
	logger.Info(ctx, "exceptCatIds", logger.Any("exceptCatIds", exceptCatIds), logger.String("fun", fun), logger.Any("err", err))
	if err != nil {
		logger.Errorf(ctx, "%s cate count return err. game_project:%s err:%v. count:%d", fun, gameProject, err, len(exceptCatIds))
		return
	}
	var allStatData = make([][]string, 0)
	var allStatDataDetails = make([][][]string, 0)
	for _, tm := range []singleTmDf{
		{time.Date(2024, 05, 01, 0, 0, 0, 0, time.UTC), time.Date(2024, 06, 01, 0, 0, 0, 0, time.UTC)},
		{time.Date(2024, 06, 01, 0, 0, 0, 0, time.UTC), time.Date(2024, 07, 01, 0, 0, 0, 0, time.UTC)},
	} { // 两次
		for _, _exceptCatIds := range []singleCatDf{
			singleCatDf{Desc: fmt.Sprintf("%s.全部分类-AllTicketStat", gameProject), CatIds: []uint64{0}},
			singleCatDf{Desc: fmt.Sprintf("%s.剔除bug问题分类-ExceptBugCatIds-catIds:%+v", gameProject, exceptCatIds), CatIds: exceptCatIds},
		} {
			// get user detail
			allStatData = append(allStatData, []string{tm.Start.Format("2006-01"), _exceptCatIds.Desc, utils.ToJson(_exceptCatIds.CatIds)}, []string{"", ""})
			// 1. 查询平均处理时长
			{ // 8:回复&关单；7:拒单；5: 3天未回复-超时关单
				for _, v := range []map[string]interface{}{
					{"desc": "平均处理时长1", "node": []int{8, 7, 5}},
					{"desc": "平均处理时长2", "node": []int{8, 5}},
					{"desc": "平均处理时长3", "node": []int{8}},
				} {
					var tkDetail []*singleTkDtDf
					query := newTicketDb.Model(&models.FpOpsTickets{}).Where("project = ? AND created_at > ? AND created_at < ? AND cat_id not in (?)",
						gameProject, tm.Start.Unix(), tm.End.Unix(), _exceptCatIds.CatIds).
						Where("closed_at >= created_at").
						Where("conversion_node in (?)", v["node"]).
						Select("ticket_id,project,conversion_node,status,closed_at,created_at")
					if err := query.Find(&tkDetail).Error; err != nil {
						logger.Errorf(ctx, "%s avg processing tm query.Find err:%v", fun, err)
						return
					}
					var _cout, _sumProcessingTime uint64
					for _, row := range tkDetail {
						//fmt.Println(row)
						_cout++
						cost := row.ClosedAt - row.CreatedAt
						_sumProcessingTime = _sumProcessingTime + cost
					}
					allStatData = append(allStatData, []string{
						tm.Start.Format("2006-01"), gameProject, cast.ToString(v["desc"]),
						utils.ResolveTimeStr(_sumProcessingTime, uint32(_cout)),
						"conversion_node" + utils.ToJson(v["node"]),
						fmt.Sprintf("count:%d. sumProcessingTime:%d(s)", _cout, _sumProcessingTime),
					})
				}
			}
			allStatData = append(allStatData, []string{"", ""}, []string{"", ""})

			// 2. 24小时完结单数
			{ // 8:回复&关单；7:拒单；5: 3天未回复-超时关单
				for _, v := range []map[string]interface{}{
					{"desc": "24小时完结单数1", "node": []int{8, 7, 5}},
					{"desc": "24小时完结单数2", "node": []int{8, 5}},
					{"desc": "24小时完结单数3", "node": []int{8}},
				} {
					var tkDetail []*singleTkDtDf
					query := newTicketDb.Model(&models.FpOpsTickets{}).Where("project = ? AND created_at > ? AND created_at < ? AND cat_id not in (?)",
						gameProject, tm.Start.Unix(), tm.End.Unix(), _exceptCatIds.CatIds).
						Where("closed_at >= created_at").
						Where("closed_at - created_at < ?", 24*3600).
						Where("conversion_node in (?)", v["node"]).
						Select("ticket_id,project,conversion_node,status,closed_at,created_at")
					if err := query.Find(&tkDetail).Error; err != nil {
						logger.Errorf(ctx, "%s avg processing tm query.Find err:%v", fun, err)
						return
					}
					var _cout, _sumProcessingTime uint64
					for _, row := range tkDetail {
						//fmt.Println(row)
						_cout++
						cost := row.ClosedAt - row.CreatedAt
						_sumProcessingTime = _sumProcessingTime + cost
					}
					allStatData = append(allStatData, []string{
						tm.Start.Format("2006-01"), gameProject, cast.ToString(v["desc"]),
						utils.ResolveTimeStr(_sumProcessingTime, uint32(_cout)),
						"conversion_node" + utils.ToJson(v["node"]),
						fmt.Sprintf("count:%d. sumProcessingTime:%d(s)", _cout, _sumProcessingTime),
					})
				}
			}
			allStatData = append(allStatData, []string{"", ""}, []string{"", ""})

			// 3. 工单完成24小时内进行评价的数据明细表
			{
				var currStatDetail = make([][]string, 0)
				currStatDetail = append(currStatDetail,
					[]string{tm.Start.Format("2006-01"), gameProject, "工单完成24小时内进行评价的数据明细表(注：完成时长不含超时和拒单)"},
					[]string{_exceptCatIds.Desc, utils.ToJson(_exceptCatIds.CatIds)},
					[]string{"", ""}, []string{"", ""},
				)
				currStatDetail = append(currStatDetail, []string{"id", "project", "fpid", "uuid", "创建时间", "完成时间", "评价时间", "评分"})
				// detail
				var tkDetail []*singleTkDtDf
				query := newTicketDb.Model(&models.FpOpsTickets{}).Where("project = ? AND created_at > ? AND created_at < ? AND cat_id not in (?)",
					gameProject, tm.Start.Unix(), tm.End.Unix(), _exceptCatIds.CatIds).
					Where("closed_at >= created_at AND evaluate_at >= closed_at").
					Where("evaluate_at - closed_at < ?", 24*3600).
					Where("conversion_node not in (?)", []int{5, 7}).
					Select("ticket_id,project,uuid,account_id,conversion_node,status,closed_at,created_at,evaluate_at,csi,nps")
				if err := query.Find(&tkDetail).Error; err != nil {
					logger.Errorf(ctx, "%s get ticket detail query.Find err:%v", fun, err)
					return
				}
				for _, row := range tkDetail {
					//fmt.Println(row)
					currStatDetail = append(currStatDetail, []string{cast.ToString(row.TicketId), row.Project,
						row.AccountId, row.Uuid, utils.TimeFormat(int64(row.CreatedAt)), utils.TimeFormat(int64(row.ClosedAt)),
						utils.TimeFormat(int64(row.EvaluateAt)), cast.ToString(row.Csi)})
				}
				allStatDataDetails = append(allStatDataDetails, currStatDetail)
			}

		}
	}
	allStatData = append(allStatData, []string{"", ""}, []string{"", ""},
		[]string{"Tips", "conversion_node数值说明：// 8:回复&关单；7:拒单；5: 3天未回复-超时关单"},
		[]string{"", "剔除一级分类包含id:", utils.ToJson(bugExceptParentCatIds[gameProject])},
	)
	// print stdout
	for i, v := range allStatData {
		fmt.Printf("i=:%d. value=:%+v\n", i, v)
	}
	for i, v := range allStatDataDetails {
		for j, vv := range v {
			fmt.Printf("i=:%d. j=:%d. value=:%+v\n", i, j, vv)
			if j > 10 {
				break
			}
		}
	}
	// save data  to excel
	allStatDataDetails = append([][][]string{allStatData}, allStatDataDetails...)
	file, err := saveDataToExcel(allStatDataDetails...)
	if err != nil {
		logger.Errorf(ctx, "%s saveDataToExcel err:%v", fun, err)
		return
	}
	logger.Infof(ctx, "%s saveDataToExcel success. file:%s", fun, file)
}
func saveDataToExcel(data ...[][]string) (string, error) {
	if len(data) == 0 {
		return "", fmt.Errorf("data empty")
	}
	f := excelize.NewFile()
	for i, sht := range data {
		sheetName := fmt.Sprintf("Sheet%d", i+1)
		f.NewSheet(sheetName)
		for j, row := range sht {
			for k, cell := range row {
				col, _ := excelize.ColumnNumberToName(k + 1)
				f.SetCellValue(sheetName, col+cast.ToString(j+1), cell)
			}
		}
	}
	file := fmt.Sprintf("./newTicketStat.%s.xlsx", time.Now().Format("20060102150405"))
	if err := f.SaveAs(file); err != nil {
		return "", err
	}
	return file, nil
}
