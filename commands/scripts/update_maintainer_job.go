package scripts

import (
	"fmt"
	"github.com/labstack/echo/v4"
	"github.com/labstack/gommon/log"
	"github.com/spf13/cobra"
	"github.com/xuri/excelize/v2"
	"net/http"
	"ops-ticket-api/internal/elasticsearch"
	"ops-ticket-api/internal/framework/database"
	"ops-ticket-api/internal/persistence"
	"ops-ticket-api/models"
	"strconv"
	"time"
)

// 根据excel中的uid更新维护人
// 2000条不用批量更新

var db = database.Db()

// UpdateMaintainerJob 更新批uid维护人： go run main.go UpdateMaintainerJob -c=config/global-test.yaml
func UpdateMaintainerJob(c *cobra.Command, args []string) {
	ctx := echo.New().NewContext(&http.Request{}, &echo.Response{})
	fmt.Println("start....")

	failUid := []uint64{}

	//读取excel uids
	file, err := excelize.OpenFile("job/MO专员调整需求.xlsx")
	if err != nil {
		log.Fatalf("failed to open file: %v", err)
	}

	sheetName := file.GetSheetName(0)
	rows, err := file.GetRows(sheetName)
	if err != nil {
		log.Fatalf("failed to get rows: %v", err)
	}
	//遍历行
	for i, row := range rows {
		if i == 0 {
			continue
		}
		if len(row) < 3 {
			fmt.Printf("第%d行没有数据", i)
			continue
		}
		if row[0] == "" || row[1] == "" || row[2] == "" {
			continue
		}
		gameProject := row[1] // 游戏项目
		if gameProject == "MO" {
			gameProject = "mo_global"
		}
		uid, _ := strconv.ParseUint(row[0], 10, 64) // uid
		maintainer := row[2]                        // 维护人
		updateTime := time.Now().Format("2006-01-02 15:04:05")
		// 更新mainter表
		err := db.Model(&models.FpDscPlayerMaintainConfig{}).
			Where("uid = ? and game_project = ?", uid, gameProject).
			Updates(map[string]interface{}{"maintainer": maintainer, "update_time": updateTime}).
			Debug().Error
		fmt.Printf("uid:%d game:%s 准备更新为 %s", uid, gameProject, maintainer)

		if err != nil {
			fmt.Printf("%d  更新错误", uid)
			failUid = append(failUid, uid)
			continue
		}
		//根据uid和游戏获得channelids
		channelIds, err := persistence.NewDiscordCommu().GetChannelIdsByProjectUid(gameProject, int64(uid))
		if err != nil {
			fmt.Printf("%d GetChannelIdsByProjectUid failed", uid)
			failUid = append(failUid, uid)
			continue
		}
		//根据channelid更新es中的mainter字段
		for _, channelDt := range channelIds {
			err := elasticsearch.DefaultDscEsSvc.UpdateMaintainer(ctx.Request().Context(), channelDt.PrivChannelId, maintainer)
			if err != nil {
				fmt.Printf("%d  更新错误", uid)
				failUid = append(failUid, uid)
				continue
			}
			fmt.Println("es成功执行了：", uid)
		}
	}

	fmt.Println("end...")

}
