package scripts

import (
	"context"
	"fmt"
	"github.com/opentracing/opentracing-go"
	"github.com/spf13/cobra"
	"github.com/uber/jaeger-client-go"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"go.uber.org/zap"
	"ops-ticket-api/internal/framework/database"
	"ops-ticket-api/models"
)

func SyncTicketCategoryFromOldTicket(c *cobra.Command, args []string) {
	fun := "SyncTicketCategoryFromOldTicket "
	ctx := context.TODO()
	//trace := opentracing.GlobalTracer()
	span := opentracing.StartSpan("SyncTicketCategoryFromOldTicket_script")
	defer span.Finish()
	if sc, ok := span.Context().(jaeger.SpanContext); ok {
		fmt.Println(fun, "sc traceid", sc.TraceID().String())
		fmt.Println(fun, "sc traceid", sc.SpanID().String())
	}
	ctx = opentracing.ContextWithSpan(ctx, span)
	ctx = logger.WithFields(ctx, []zap.Field{
		zap.String("type", "scrip"),
		zap.Any("args", args),
		zap.String("func", fun)}...)
	span = opentracing.SpanFromContext(ctx)
	if len(args) < 2 {
		logger.Infof(ctx, "%s. args.len eq0. args:%+v", fun, args)
		return
	}
	gameProject := args[0]
	if gameProject == "" {
		logger.Errorf(ctx, "%s game_project empty. %+v", fun, args)
		return
	}
	if args[1] == "forkDo" {
		logger.Infof(ctx, "%s forkDo logic start. args:%+v", fun, args)
	}
	logger.Infof(ctx, "%s start logic.", fun)

	subCtx, cancel := context.WithCancel(ctx)
	defer cancel()
	// get db
	var newTicketDb, oldTicketDb = database.Db().WithContext(subCtx), database.GetOldTicketDb().WithContext(subCtx)
	var cateCount int64
	if err := oldTicketDb.Model(&models.FpOpsCategory{}).Where("project = ?", gameProject).Count(&cateCount).Error; err != nil || cateCount == 0 {
		logger.Errorf(ctx, "%s cate count return err. or 0. game_project:%s err:%v. count:%d", fun, gameProject, err, cateCount)
		return
	}
	var dicts []*models.FpOpsDict
	if err := oldTicketDb.Model(&models.FpOpsDict{}).Find(&dicts).Error; err != nil {
		logger.Errorf(subCtx, "find fp_ops_dict err", logger.Any("err", err))
		return
	}
	var category []*models.FpOpsCategory
	if err := oldTicketDb.Model(&models.FpOpsCategory{}).Where("project = ?", gameProject).Find(&category).Error; err != nil {
		logger.Errorf(subCtx, "find FpOpsCategory err", logger.Any("err", err))
		return
	}

	var categoryLang []*models.FpOpsCategoryLang
	var categoryMap []*models.FpOpsCategoryMap
	var cateIds []uint32
	for _, row := range category {
		cateIds = append(cateIds, row.CatID)
	}
	if len(cateIds) != 0 {
		if err := oldTicketDb.Model(&models.FpOpsCategoryLang{}).Where("cat_id in ?", cateIds).Find(&categoryLang).Error; err != nil {
			logger.Errorf(subCtx, "find FpOpsCategoryLang err", logger.Any("err", err))
			return
		}
		if err := oldTicketDb.Model(&models.FpOpsCategoryMap{}).Where("cat_id in ?", cateIds).Find(&categoryMap).Error; err != nil {
			logger.Errorf(subCtx, "find FpOpsCategoryMap err", logger.Any("err", err))
			return
		}
	}
	// tpl
	var tpl []*models.FpOpsTpl
	var tplLang []*models.FpOpsTplLang
	{
		if err := oldTicketDb.Model(&models.FpOpsTpl{}).Find(&tpl).Error; err != nil {
			logger.Errorf(subCtx, "find FpOpsTpl err", logger.Any("err", err))
			return
		}
		if err := oldTicketDb.Model(&models.FpOpsTplLang{}).Find(&tplLang).Error; err != nil {
			logger.Errorf(subCtx, "find FpOpsTplLang err", logger.Any("err", err))
			return
		}
	}

	// check
	if len(category) == 0 || len(tpl) == 0 || len(dicts) == 0 {
		logger.Errorf(subCtx, "category or tpl or dicts len eq0. %d.%d.%d", len(category), len(tpl), len(dicts))
		return
	}
	var err error
	tx := newTicketDb.Begin()
	defer func() {
		if err != nil {
			logger.Errorf(ctx, "%s new ticket db transaction return err. err:%v", fun, err)
			if _rollErr := tx.Rollback(); _rollErr != nil {
				logger.Errorf(subCtx, "%s rollback return err. err:%v. _rollErr:%v", fun, err, _rollErr)
			}
		}
	}()
	{ // dict
		if err = tx.Model(&models.FpOpsDict{}).Where("1=1").Delete(&models.FpOpsDict{}).Error; err != nil {
			return
		}
		if err = tx.Model(&models.FpOpsDict{}).CreateInBatches(dicts, 100).Error; err != nil {
			return
		}
	}

	{ // category
		if err = tx.Model(&models.FpOpsCategory{}).Where("project = ?", gameProject).
			Delete(&models.FpOpsCategory{}).Error; err != nil {
			return
		}
		if err = tx.Model(&models.FpOpsCategoryLang{}).Where("cat_id in ?", cateIds).
			Delete(&models.FpOpsCategoryLang{}).Error; err != nil {
			return
		}
		if err = tx.Model(&models.FpOpsCategoryMap{}).Where("cat_id in ?", cateIds).
			Delete(&models.FpOpsCategoryMap{}).Error; err != nil {
			return
		}
		if err = tx.Model(&models.FpOpsCategory{}).CreateInBatches(category, 10).Error; err != nil {
			return
		}
		if err = tx.Model(&models.FpOpsCategoryLang{}).CreateInBatches(categoryLang, 100).Error; err != nil {
			return
		}
		if err = tx.Model(&models.FpOpsCategoryMap{}).CreateInBatches(categoryMap, 100).Error; err != nil {
			return
		}
	}

	{ // tpl
		if err = tx.Model(&models.FpOpsTpl{}).Where("1=1").Delete(&models.FpOpsTpl{}).Error; err != nil {
			return
		}
		if err = tx.Model(&models.FpOpsTplLang{}).Where("1=1").Delete(&models.FpOpsTplLang{}).Error; err != nil {
			return
		}
		if err = tx.Model(&models.FpOpsTpl{}).CreateInBatches(tpl, 10).Error; err != nil {
			return
		}
		if err = tx.Model(&models.FpOpsTplLang{}).CreateInBatches(tplLang, 100).Error; err != nil {
			return
		}
	}
	if err = tx.Commit().Error; err != nil {
		return
	}
	logger.Infof(subCtx, "%s do logic success.", fun)
	return

}
