package scripts

import (
	"encoding/json"
	"fmt"
	"github.com/spf13/cobra"
	"gitlab-ee.funplus.io/ops-tools/compkg/dump"
	"ops-ticket-api/internal/framework/database"
	"ops-ticket-api/models"
	"ops-ticket-api/proto/pb"
)

var assignDb = database.Db()

// SyncUserAssignTikcket 更新分单配置加系统标签： go run main.go SyncUserAssignTikcket -c=config/global-test.yaml
func SyncUserAssignTikcket(c *cobra.Command, args []string) {
	//ctx := echo.New().NewContext(&http.Request{}, &echo.Response{})
	fmt.Println("start....")

	dest := make([]*models.FpOpsUserAssignTicket, 0)
	if err := assignDb.Table(models.GetFpOpsUserAssignTicketTableName()).Select("*").Order("id").First(&dest).Error; err != nil {
		fmt.Println(err)
		return
	}

	// 遍历每条，组装edit接口数据，修改gamecat
	for _, item := range dest {
		gameCats := []*pb.GameCategory{}
		if err := json.Unmarshal([]byte(item.GameCat), &gameCats); err != nil {
			fmt.Printf("%v json.Unmarshal failed, err: %v\n", item.ID, err)
			continue
		}
		for i := range gameCats {
			gameCats[i].SystemTag = []pb.TicketSystemTag{}
		}
		dest := map[string]interface{}{}
		detailBytes, err := json.Marshal(gameCats)
		if err != nil {
			fmt.Printf("%v json.marshal failed, err: %v\n", item.ID, err)
			continue
		}
		dest["game_cat"] = string(detailBytes)
		if err = db.Model(&models.FpOpsUserAssignTicket{}).Where("id = ?", item.ID).Updates(dest).Error; err != nil {
			fmt.Printf("%v update failed, err: %v\n", item.ID, err)
			continue
		}
		dump.Dump(dest)
	}
	fmt.Println("end....")

}
