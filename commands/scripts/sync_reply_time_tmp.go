package scripts

import (
	"fmt"
	"github.com/spf13/cobra"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog"
	"ops-ticket-api/internal/framework/database"
	"ops-ticket-api/internal/persistence"
	"ops-ticket-api/utils"
	"time"
)

// SyncYesterdayDiscordReplyTimeDetail 查询出前一天的所有conversation明细后插入discord回复时间表，通过定时任务做增量更新。
// 执行脚本：  go run main.go SyncYesterdayDiscordReplyTimeDetail -c=config/global-test.yaml 2024-09-23
func SyncYesterdayDiscordReplyTimeDetail(c *cobra.Command, args []string) {
	var fun = "SyncYesterdayDiscordReplyTimeDetail"
	// 检查参数长度
	if len(args) < 1 {
		fmt.Printf("%s: args.len == 0. args: %+v", fun, args)
		return
	}

	// 获取昨天的时间
	tm, err := time.Parse("2006-01-02", args[0])
	if err != nil {
		fmt.Printf("%s: invalid date format: %v", fun, err)
		return
	}
	d := persistence.NewDiscordInteract()
	db := database.Db()
	//获取当天的头和尾
	start, end := utils.GetWhichDayStartAndEnd(tm)
	//将昨天的数据删除
	delSql := fmt.Sprintf(`DELETE FROM fp_dsc_reply_time WHERE reply_at BETWEEN ? AND ?`)
	fmt.Println(start)
	fmt.Println(end)
	fmt.Println("delSql:::::", delSql)
	if err := db.Exec(delSql, start, end).Error; err != nil {
		fmt.Printf("delete fp_dsc_reply_time err:%+v", err)
		return
	}
	fmt.Printf("SyncYesterdayDiscordReplyTimeDetail start")
	//查出所有的当天涉及的dsc用户id
	users, err := d.FetchYesterdayAllUsers(start, end)
	if err != nil {
		fmt.Printf("FetchYesterdayAllUsers err:%+v", err)
		return
	}
	for _, user := range users {
		//找到第一个统计的user
		firstUser, err, isExit := d.FetchFirstUser(user, start, end)
		if err != nil {
			fmt.Printf("FetchFirstUser err:%+v", err)
			return
		}
		//1.第一天且只有bot  2.找到最后一个bot后，后面也没有user  这两种情况直接退出
		if isExit {
			continue
		}
		commus, err := d.FetchTodayCommus(user, firstUser.CreatedAt, end)
		if err != nil {
			fmt.Printf("FetchTodayCommus err:%+v", err)
			return
		}
		//双指针插入
		var i, j int
		for i = 0; i < len(commus); i++ {
			//如果j + 1不是user，继续下一个i
			if commus[i].FromUserID != commus[i].DscUserID {
				continue
			}
			for j = i + 1; j < len(commus) && commus[j].FromUserID == commus[j].DscUserID; j++ {
			}

			if j >= len(commus) {
				break
			} else {
				userA, botA := commus[i], commus[j]
				//成功配对，执行插入
				insertSql := fmt.Sprintf(`INSERT INTO fp_dsc_reply_time(project,bot_id,dsc_user_id,operator,channel_id,reply_at,reply_time_seconds,day) VALUES(?,?,?,?,?,?,?,?)`)
				//获取客服
				operator, err := d.FetchOperatorByMsgId(botA.MsgID)
				if err != nil {
					elog.Errorf("FetchOperatorByBot err:%+v", err)
					return
				}
				if operator == "" {
					i = j
					continue
				}
				//获取回复时间
				replyTimeSeconds := uint(botA.CreatedAt.Sub(userA.CreatedAt).Seconds())
				day := botA.CreatedAt.Format("2006-01-02")
				//插入
				if err := db.Exec(insertSql, userA.Project, botA.BotID, userA.DscUserID, operator, userA.ChannelID, botA.CreatedAt, replyTimeSeconds, day).Error; err != nil {
					fmt.Printf("insert fp_dsc_reply_time err:%+v", err)
					return
				}
				fmt.Println("insertSql:::::", insertSql)
				fmt.Println("params:", userA.Project, botA.BotID, userA.DscUserID, operator, userA.ChannelID, botA.CreatedAt, replyTimeSeconds)
			}
			i = j
		}
	}
	fmt.Printf("SyncYesterdayDiscordReplyTimeDetail done")
}
