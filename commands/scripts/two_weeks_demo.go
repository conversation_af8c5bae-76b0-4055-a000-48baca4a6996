package scripts

import (
	"fmt"
	"github.com/labstack/echo/v4"
	"github.com/spf13/cobra"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"net/http"
	"ops-ticket-api/models"
	"ops-ticket-api/services/ai_tag"
	"sync"
)

// TwoWeeksDemo 获取： go run main.go TwoWeeksDemo -c=config/global-prod.yaml
func TwoWeeksDemo(c *cobra.Command, args []string) {

	e := echo.New()
	req := &http.Request{}        // 创建一个空的 HTTP 请求
	res := &echo.Response{}       // 创建一个空的 HTTP 响应
	ctx := e.New<PERSON>ontext(req, res) // 使用 req 和 res 初始化上下文

	tickes := []*models.FpOpsTickets{}
	if err := db.WithContext(ctx.Request().Context()).Model(&models.FpOpsTickets{}).
		Where("created_at between ? and ?", 1747699200, 1747907092).
		Where("project = ?", "mo_global").
		Preload("Fields").
		Order("created_at desc").Limit(3).
		Find(&tickes).Error; err != nil {
		panic(err)
	}
	fmt.Println(len(tickes))

	sem := make(chan struct{}, 10)
	var wg sync.WaitGroup
	for _, ticket := range tickes {
		wg.Add(1)
		sem <- struct{}{}
		go func(tID uint64) {
			defer wg.Done()
			defer func() { <-sem }()

			if err := ai_tag.AiTagHandle(ctx.Request().Context(), tID); err != nil {
				logger.Errorf(ctx.Request().Context(), "ai_tag.AiTagHandle error: %v", err)
				return
			}
		}(ticket.TicketID)
	}
	wg.Wait()

	//err := tag_alert_stats.TagAlertStats(ctx.Request().Context())
	//if err != nil {
	//	fmt.Println(err)
	//	return
	//}
}
