package scripts

import (
	"context"
	"github.com/labstack/echo/v4"
	"github.com/spf13/cobra"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"net/http"
	"ops-ticket-api/internal/elasticsearch"
	"ops-ticket-api/models"
)

// SyncDscCommu 获取： go run main.go SyncDscCommu -c=config/global-prod.yaml
func SyncDscCommu(c *cobra.Command, args []string) {
	// 初始化 Echo 上下文
	e := echo.New()
	req := &http.Request{}        // 创建一个空的 HTTP 请求
	res := &echo.Response{}       // 创建一个空的 HTTP 响应
	ctx := e.NewContext(req, res) // 使用 req 和 res 初始化上下文
	err := MessageCreate(ctx.Request().Context())
	if err != nil {
		panic(err)
	}
}

func MessageCreate(ctx context.Context) error {
	// 查询commu
	commu := &models.FpDscDmCommu{}
	err := db.WithContext(ctx).Model(&models.FpDscDmCommu{}).Where("msg_id = ?", "1374195169267814431").First(&commu).Error
	if err != nil {
		logger.Errorf(ctx, "query commu err:%+v")
		return err
	}
	err = elasticsearch.DefaultDscEsSvc.UpsertDscCommus(ctx, commu, true, "xin.li.cs")
	if err != nil {
		logger.Errorf(ctx, "upsert commu err:%+v")
		return err
	}
	return nil
}
