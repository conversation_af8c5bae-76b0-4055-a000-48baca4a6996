package scripts

import (
	"context"
	"fmt"
	"github.com/opentracing/opentracing-go"
	"github.com/spf13/cast"
	"github.com/spf13/cobra"
	"github.com/uber/jaeger-client-go"
	"gitlab-ee.funplus.io/ops-tools/compkg/dump"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"go.uber.org/zap"
	"ops-ticket-api/internal/elasticsearch"
	"ops-ticket-api/internal/framework/database"
	"ops-ticket-api/models"
	"ops-ticket-api/services/reporter"
	"time"
)

// SyncTicketDetailToEs 同步一条工单到es： create时写 db成功&写es失败 的case
// 执行脚本：  go run main.go SyncTicketByIdToEs -c=config/global-test.yaml 29516 forkDo
func SyncTicketDetailToEs(c *cobra.Command, args []string) {
	fun := "SyncTicketDetailToEs "
	ctx := context.TODO()
	//trace := opentracing.GlobalTracer()
	span := opentracing.StartSpan("SyncTicketDetailToEs_script")
	defer span.Finish()
	if sc, ok := span.Context().(jaeger.SpanContext); ok {
		fmt.Println(fun, "sc traceid", sc.TraceID().String())
		fmt.Println(fun, "sc traceid", sc.SpanID().String())
	}
	ctx = opentracing.ContextWithSpan(ctx, span)
	ctx = logger.WithFields(ctx, []zap.Field{
		zap.String("type", "scrip"),
		zap.Any("args", args),
		zap.String("func", fun)}...)
	span = opentracing.SpanFromContext(ctx)
	if len(args) < 2 {
		logger.Infof(ctx, "%s. args.len eq0. args:%+v", fun, args)
		return
	}
	tkId := args[0]
	if tkId == "" {
		logger.Errorf(ctx, "%s ticketId empty. %+v", fun, args)
		return
	}
	ticketId := cast.ToInt64(tkId)
	if ticketId < 100 {
		logger.Infof(ctx, "%s ticketId less than 100. %+v", fun, args)
		return
	}
	if args[1] == "forkDo" {
		logger.Infof(ctx, "%s forkDo logic start. args:%+v", fun, args)
	} else {
		logger.Infof(ctx, "%s forkDo args check fail. args:%+v", fun, args)
		return
	}
	logger.Infof(ctx, "%s start logic.", fun)

	subCtx, cancel := context.WithCancel(ctx)
	defer cancel()

	// get db
	var tkObj = &models.FpOpsTickets{}
	db := database.Db()
	err := db.WithContext(subCtx).Model(&models.FpOpsTickets{}).
		Preload("Device").Preload("Fields").
		Preload("Tags").Preload("Tags.TagItem").
		Where("ticket_id = ?", ticketId).First(&tkObj).Error
	if err != nil {
		logger.Errorf(ctx, "%s get ticket detail return err. err:%s", fun, err)
		return
	}
	dump.Dump(tkObj)
	elasticsearch.DefaultTicketSyncSvc.CreateTicket(subCtx, tkObj)
	reporter.PubCreate(subCtx, tkObj)
	time.Sleep(time.Second * 10)
}
