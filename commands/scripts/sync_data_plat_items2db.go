package scripts

import (
	"context"
	"fmt"
	"github.com/go-sql-driver/mysql"
	"github.com/opentracing/opentracing-go"
	"github.com/spf13/cobra"
	"github.com/uber/jaeger-client-go"
	"github.com/xuri/excelize/v2"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"go.uber.org/zap"
	"ops-ticket-api/internal/framework/database"
	"ops-ticket-api/models"
	"strings"
	"time"
)

// SyncDataPlatGameItems2Db
// 1. 导入游戏 item 列表： go run main.go syncDataPlatGameItems -c=config/global-test.yaml mo_global item xxxx.xls
// 2. 导入多语言翻译 列表： go run main.go syncDataPlatGameItems -c=config/global-test.yaml mo_global i18n xxxx.xls
func SyncDataPlatGameItems2Db(c *cobra.Command, args []string) {
	fun := "SyncDataPlatGameItems2Db "
	ctx := context.TODO()
	span := opentracing.StartSpan("SyncDataPlatGameItems2Db_script")
	defer span.Finish()
	if sc, ok := span.Context().(jaeger.SpanContext); ok {
		fmt.Println(fun, "sc traceid", sc.TraceID().String())
		fmt.Println(fun, "sc traceid", sc.SpanID().String())
	}
	ctx = opentracing.ContextWithSpan(ctx, span)
	ctx = logger.WithFields(ctx, []zap.Field{
		zap.String("type", "scrip"),
		zap.Any("args", args),
		zap.String("func", fun)}...)
	span = opentracing.SpanFromContext(ctx)
	if len(args) < 2 {
		logger.Infof(ctx, "%s. args.len eq0. args:%+v", fun, args)
		return
	}
	gameProject := args[0]
	if gameProject == "" {
		logger.Errorf(ctx, "%s game_project empty. %+v", fun, args)
		return
	}
	logger.Infof(ctx, "%s. current query params. args.len %d. args:%+v", fun, len(args), args)
	var tp syncDataPlatGameTp
	if _tp := args[1]; _tp == "item" {
		tp = syncDataPlatGameTpItem
	} else if _tp == "i18n" {
		tp = syncDataPlatGameTpI18n
	} else {
		logger.Errorf(ctx, "%s type unknown. %+v", fun, args)
		return
	}
	fileName := args[2]
	if fileName == "" || strings.LastIndex(fileName, ".xlsx") == -1 {
		logger.Errorf(ctx, "%s file name check failed. %+v", fun, args)
		return
	}
	imp := dataPlatGameItemOp{
		Ctx:      ctx,
		Project:  gameProject,
		Type:     tp,
		FileName: fileName,
	}
	if err := imp.DoImport(); err != nil {
		logger.Errorf(ctx, "%s imp.DoImport return err. err:%+v", fun, err)
		return
	}
	logger.Infof(ctx, "%s imp.DoImport do success. args:%+v.", fun, args)
}

var (
	syncDataPlatGameTpItem syncDataPlatGameTp = "item"
	syncDataPlatGameTpI18n syncDataPlatGameTp = "i18n"
)

type (
	syncDataPlatGameTp string
	_dtGDetailDf       struct {
		Key string `json:"key"`
		Val string `json:"val"`
	}
	dataPlatGameItemOp struct {
		Ctx      context.Context
		Project  string
		Type     syncDataPlatGameTp
		FileName string
	}
)

// DoImport do logic
func (c dataPlatGameItemOp) DoImport() error {
	switch c.Type {
	case syncDataPlatGameTpItem:
		return c.doImportItem()
	case syncDataPlatGameTpI18n:
		return c.doImportI18n()
	}
	return fmt.Errorf("unknown type. %s", c.Type)
}

// doImportItem 道具导入
func (c dataPlatGameItemOp) doImportItem() error {
	var fun = "dataPlatGameItemOp.doImportItem"
	// 1. 解析文件得到数据
	list, err := c.getItemList()
	if err != nil {
		return err
	}
	fmt.Println("--- total:", len(list))

	// 3. 保存数据
	var now = time.Now()
	db := database.Db().WithContext(c.Ctx)
	var succCount int
	go func() {
		for {
			<-time.NewTimer(time.Second * 5).C
			fmt.Println("current success count:", succCount)
		}

	}()
	var concurrentChan = make(chan struct{}, 10)
	for _, row := range list {
		detail := &models.FpDataplatGameItem{
			Project:    c.Project,
			ItemID:     row.Key,
			ItemName:   row.Val,
			Operator:   "system",
			CreateTime: now,
			UpdateTime: now,
		}
		concurrentChan <- struct{}{}
		go func(dt *models.FpDataplatGameItem) {
			if err := db.Create(dt).Error; err != nil {
				_sqlErr, ok := err.(*mysql.MySQLError)
				if !ok || _sqlErr.Number != 1062 {
					fmt.Println(_sqlErr, ok, _sqlErr.Number == 1062, "----")
					logger.Errorf(c.Ctx, "%s db create return err. err:%+v. detail%+v", fun, err, dt)
					panic(err)
				}

			}
			<-concurrentChan
		}(detail)
		succCount++
	}
	close(concurrentChan)
	for vv := range concurrentChan {
		fmt.Println("last channel. ", vv)
	}
	// 4. 打印日志&返回
	fmt.Println("打印日志： 保存数据成功条数:", succCount)
	return nil
}
func (c dataPlatGameItemOp) getItemList() ([]*_dtGDetailDf, error) {
	var (
		fun   = "dataPlatGameItemOp.getItemList"
		items = make([]*_dtGDetailDf, 0)
		has   = map[string]struct{}{}
	)
	f, err := excelize.OpenFile(c.FileName) // 替换为你的Excel文件路径
	if err != nil {
		logger.Errorf(c.Ctx, "%s excelize.OpenFile return err . err:%+v", fun, err)
		return nil, err
	}
	defer func() {
		// 关闭文件
		if err := f.Close(); err != nil {
			logger.Errorf(c.Ctx, "%s f.Close() return err. err:%v", fun, err)
		}
	}()

	// 获取Sheet1上的所有单元格
	rows, err := f.GetRows("道具1112更新") // 替换为你的工作表名称
	if err != nil {
		logger.Errorf(c.Ctx, "%s  f.GetRows return err. err:%v", fun, err)
		return nil, err
	}

	// 遍历行数据
	for i, row := range rows {
		if i == 0 {
			if len(row) < 2 {
				return nil, fmt.Errorf("row[0].len <2 row:%+v", row)
			}
			if row[0] != "id" || row[1] != "description_pm^p" {
				return nil, fmt.Errorf("row[0] cel not expect. row:%+v", row)
			}
			continue
		}
		if len(row) < 2 {
			fmt.Println("<<<<< 整行数小于 2", row)
			continue
		}
		id := strings.TrimSpace(row[0])
		desc := strings.TrimSpace(row[1])
		if id == "" || desc == "" {
			fmt.Println(">>>>>> 部分行数据为空", id, desc)
			continue
		}
		if _, ok := has[id]; ok {
			fmt.Println("------- 数据重复： continue. ", id, desc)
			continue
		}
		items = append(items, &_dtGDetailDf{
			Key: id,
			Val: desc,
		})
		has[id] = struct{}{}
	}
	return items, nil
}
func (c dataPlatGameItemOp) doImportI18n() error {
	fun := "dataPlatGameItemOp.doImportI18n"
	// 1. 解析文件得到数据
	list, err := c.getI18nList()
	if err != nil {
		return err
	}
	fmt.Println("--- total:", len(list))

	// 3. 保存数据
	var now = time.Now()
	db := database.Db().WithContext(c.Ctx)
	var succCount int
	go func() {
		for {
			<-time.NewTimer(time.Second * 5).C
			fmt.Println("current success count:", succCount)
		}

	}()
	var concurrentChan = make(chan struct{}, 10)
	for _, row := range list {
		detail := &models.FpDataplatGameI18n{
			Project:    c.Project,
			UniqueKey:  row.Key,
			ShowMsg:    row.Val,
			Operator:   "system",
			CreateTime: now,
			UpdateTime: now,
		}
		concurrentChan <- struct{}{}
		go func(dt *models.FpDataplatGameI18n) {
			if err := db.Create(dt).Error; err != nil {
				_sqlErr, ok := err.(*mysql.MySQLError)
				if !ok || _sqlErr.Number != 1062 {
					fmt.Println(_sqlErr, ok, _sqlErr.Number == 1062, "----")
					logger.Errorf(c.Ctx, "%s db create FpDataplatGameI18n return err. err:%+v. detail%+v", fun, err, dt)
					panic(err)
				}

			}
			<-concurrentChan
		}(detail)
		succCount++
	}
	close(concurrentChan)
	for vv := range concurrentChan {
		fmt.Println("last channel. ", vv)
	}
	// 4. 打印日志&返回
	fmt.Println("打印日志： 保存数据成功条数:", succCount)
	return nil
}

func (c dataPlatGameItemOp) getI18nList() ([]*_dtGDetailDf, error) {
	var (
		fun   = "dataPlatGameItemOp.getI18nList"
		items = make([]*_dtGDetailDf, 0)
		has   = map[string]struct{}{}
	)
	f, err := excelize.OpenFile(c.FileName) // 替换为你的Excel文件路径
	if err != nil {
		logger.Errorf(c.Ctx, "%s excelize.OpenFile return err . err:%+v", fun, err)
		return nil, err
	}
	defer func() {
		// 关闭文件
		if err := f.Close(); err != nil {
			logger.Errorf(c.Ctx, "%s f.Close() return err. err:%v", fun, err)
		}
	}()

	// 获取Sheet1上的所有单元格
	rows, err := f.GetRows("原因") // 替换为你的工作表名称
	if err != nil {
		logger.Errorf(c.Ctx, "%s  f.GetRows return err. err:%v", fun, err)
		return nil, err
	}

	// 遍历行数据
	for i, row := range rows {
		if i == 0 {
			if len(row) < 2 {
				return nil, fmt.Errorf("row[0].len <2 row:%+v", row)
			}
			if row[0] != "ID" || row[1] != "原因-中文" {
				return nil, fmt.Errorf("row[0] cel not expect. row:%+v", row)
			}
			continue
		}
		if len(row) < 2 {
			fmt.Println("<<<<< 整行数小于 2", row)
			continue
		}
		id := strings.TrimRight(strings.TrimLeft(strings.TrimSpace(row[0]), "\""), "\"")
		desc := strings.TrimSpace(row[1])
		if id == "" || desc == "" {
			fmt.Println(">>>>>> 部分行数据为空", id, desc)
			continue
		}
		if _, ok := has[id]; ok {
			fmt.Println("------- 数据重复： continue. ", id, desc)
			continue
		}
		items = append(items, &_dtGDetailDf{
			Key: id,
			Val: desc,
		})
		has[id] = struct{}{}
	}
	return items, nil
}
