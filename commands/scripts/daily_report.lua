local env = os.getenv("POD_RUNMODE")
print(env)
local dataFrom = "GLOBAL"
local nowTime = utils.strtotime("now")
local diffStartDate = params.diff_start_date or utils.date("Y-m-d", utils.strtotime("-2 week"))
local diffEndDate = params.diff_end_date or utils.date("Y-m-d")

print(data)
local testFeishuRobit = "https://open.feishu.cn/open-apis/bot/v2/hook/c260d7d7-47ac-4c0e-8aab-7942e008c4a7"
local prodFeishuRobit = "https://open.feishu.cn/open-apis/bot/v2/hook/5b61253d-7c72-4beb-812f-ee163e3215a0"
local db_conn = db.new("root", "oLhYVo9tB597", "cs-global.cluster-ro-c6h1gwzealou.us-west-2.rds.amazonaws.com","3306", "fp_ops_ticket_new")
-- local data = db_conn:query("select * from fp_ops_tickets order by ticket_id desc limit 1")
-- print(data)
local filterProjectList = utils.join(',', { "'dc.global.prod'","'mo_global'","'ss_global'","'koa_global'","'gog_global'","'mce_global'","'mc_global'","'ts_global'"})
print(diffStartDate, diffEndDate)

local error = ""
-- 捕捉异常 告警
local function errorHandler(err)
    str = utils.sprintf("[%v][%v]\n\n 捕捉异常 line: %v", env, dataFrom, err)
    print(str)
    if string.len(err) > 100 then
        err = string.sub(err, -100)
    end
    str = utils.sprintf("[%v][%v]\n\n 捕捉异常 line: %v", env, dataFrom, err)
    error = str
end

-- 初始函数
function main()
    local success, result = xpcall(handle, errorHandler)
    if success then
        return result
    else
        return { status = 100005, msg = env == "PROD" and "sytem error！" or error }
    end
end

local xdate = {}
function handle()
    toXdate()
    print("---", diffStartDate, xdate)
    -- pv + uv

    local text = ""

    -- 新增工单量
    local yAxisV, vSeries, err = newTicketVolume()
    print(yAxisV, vSeries, err)
    local code, image = ExchangeData(utils.sprintf("最近14天-%v-新工单系统-工单量", dataFrom), xdate, yAxisV, vSeries, true)
    local image_key = utils.upload_remoteurl_feishu(image)
    text = text .. "![新工单系统-新增工单量](" .. image_key .. ")\n --------------\n"

    -- 完结工单量
    local yAxisV, vSeries, err = closedTicketVolume()
    print(yAxisV, vSeries, err)
    local code, image = ExchangeData(utils.sprintf("最近14天-%v-新工单系统-完结工单量", dataFrom), xdate, yAxisV, vSeries, true)
    local image_key = utils.upload_remoteurl_feishu(image)
    text = text .. "![新工单系统-工单量](" .. image_key .. ")\n --------------\n"

    -- 工单平均处理时长
    local yAxisR, rSeries, err = averageTicketResolutionTime()
    local code, image = ExchangeData(utils.sprintf("最近14天-%v-新工单系统-工单平均处理时长", dataFrom), xdate, yAxisR, rSeries, true)
    local image_key = utils.upload_remoteurl_feishu(image)
    text = text .. "![新工单系统-工单平均处理时长](" .. image_key .. ")\n --------------\n"

    print(text)
    utils.send_mark_msg(prodFeishuRobit, text, "新工单系统-日报", "feishu")
end

--select DATE(FROM_UNIXTIME(closed_at)) AS day,count(ticket_id) as count,project from fp_ops_tickets where status = 3 and conversion_node in(5,7,8) and project in('ss_global','koa_global','gog_global','st_global','mo_global','mc_global') and FROM_UNIXTIME(closed_at) >= '2024-05-09 00:00:00' and FROM_UNIXTIME(closed_at) < '2024-05-24 00:00:00' group by project,day order by project,day;
-- 完结工单量
function closedTicketVolume()
    local sData, err = db_conn:query([[SELECT
           DATE(FROM_UNIXTIME(closed_at)) AS day,
           COUNT(ticket_id) as count,
           project
       FROM
           fp_ops_tickets
       WHERE
           status = 3
           AND conversion_node in(5,7,8)
           AND FROM_UNIXTIME(closed_at) >= '%v 00:00:00'
           AND FROM_UNIXTIME(closed_at) < '%v 00:00:00'
           AND project in(%v)
       GROUP BY
           project,
           day
       ORDER BY
           FIELD(project, %v) asc,
           day]], diffStartDate, diffEndDate, filterProjectList, filterProjectList)
    sData = sData == nil and {} or sData

    local vSeries = {}
    local yAxisV = {
        { title = { text = "新工单系统-工单量" } },
    }

    local num = 1
    local gmList = {}
    local gmDayList = {}
    for i, v in pairs(sData) do
        -- print(i,v)
        local gm = v.project
        local day = string.sub(v.day, 1, 10)
        local count = v.count
        --         print("--", gm, day, count)
        if utils.in_array(gm, gmList) == false then
            table.insert(gmList, gm)
        end

        if gmDayList[gm] == nil then
            gmDayList[gm] = {}
        end
        gmDayList[gm][day] = { Count = count }
    end

    for i, gm in pairs(gmList) do
        local countV = {}
        for j, day in pairs(xdate) do
            if gmDayList[gm][day] == nil then
                table.insert(countV, 0)
            else
                local cnt = gmDayList[gm][day].Count ~= nil and tonumber(gmDayList[gm][day].Count) or 0
                table.insert(countV, cnt)
            end
        end
        table.insert(vSeries, { name = gm .. "", type = "line", data = countV })
    end
    return yAxisV, vSeries, err
end


-- 'ss_global','koa_global','gog_global','mce_global','mc_global','mo_global'
--
-- SELECT
--    DATE(FROM_UNIXTIME(created_at)) AS day,
--    COUNT(ticket_id) as count,
--    project
-- FROM
--    fp_ops_tickets
-- WHERE
--    FROM_UNIXTIME(created_at) >= '2024-05-09 00:00:00'
--    AND FROM_UNIXTIME(created_at) < '2024-05-24 00:00:00'
--    AND project in('ss_global','koa_global','gog_global','mce_global','mc_global','mo_global')
-- GROUP BY
--    project,
--    day
-- ORDER BY
--    FIELD(project, 'ss_global','koa_global','gog_global','mce_global','mc_global','mo_global') asc,
--    day

-- 新增工单量
function newTicketVolume()
    local sData, err = db_conn:query([[SELECT
           DATE(FROM_UNIXTIME(created_at)) AS day,
           COUNT(ticket_id) as count,
           project
       FROM
           fp_ops_tickets
       WHERE
           FROM_UNIXTIME(created_at) >= '%v 00:00:00'
           AND FROM_UNIXTIME(created_at) < '%v 00:00:00'
           AND project in(%v)
       GROUP BY
           project,
           day
       ORDER BY
           FIELD(project, %v) asc,
           day]], diffStartDate, diffEndDate, filterProjectList, filterProjectList)
    sData = sData == nil and {} or sData

    local vSeries = {}
    local yAxisV = {
        { title = { text = "新工单系统-工单量" } },
    }

    local num = 1
    local gmList = {}
    local gmDayList = {}
    for i, v in pairs(sData) do
        -- print(i,v)
        local gm = v.project
        local day = string.sub(v.day, 1, 10)
        local count = v.count
        --         print("--", gm, day, count)
        if utils.in_array(gm, gmList) == false then
            table.insert(gmList, gm)
        end

        if gmDayList[gm] == nil then
            gmDayList[gm] = {}
        end
        gmDayList[gm][day] = { Count = count }
    end

    for i, gm in pairs(gmList) do
        local countV = {}
        for j, day in pairs(xdate) do
            if gmDayList[gm][day] == nil then
                table.insert(countV, 0)
            else
                local cnt = gmDayList[gm][day].Count ~= nil and tonumber(gmDayList[gm][day].Count) or 0
                table.insert(countV, cnt)
            end
        end
        table.insert(vSeries, { name = gm .. "", type = "line", data = countV })
    end
    return yAxisV, vSeries, err
end



-- 'ss_global','koa_global','gog_global','mce_global','mc_global','mo_global'

-- SELECT
--    DATE(FROM_UNIXTIME(created_at)) AS day,
--    COUNT(ticket_id) as count,
--    SUM(GREATEST(TIMESTAMPDIFF(SECOND, FROM_UNIXTIME(created_at), FROM_UNIXTIME(closed_at)),0)) AS proc_time,
--    project
-- FROM
--    fp_ops_tickets
-- WHERE
--    status = 3
--    AND FROM_UNIXTIME(created_at) >= '2024-05-09 00:00:00'
--    AND FROM_UNIXTIME(created_at) < '2024-05-24 00:00:00'
--    AND project in('ss_global','koa_global','gog_global','mce_global','mc_global','mo_global')
-- GROUP BY
--    project,
--    day
-- ORDER BY
--    FIELD(project, 'ss_global','koa_global','gog_global','mce_global','mc_global','mo_global') asc,
--    day

-- 工单平均处理时长
function averageTicketResolutionTime()
    local sData, err = db_conn:query([[SELECT
       DATE(FROM_UNIXTIME(closed_at)) AS day,
       COUNT(ticket_id) as count,
       SUM(GREATEST(TIMESTAMPDIFF(SECOND, FROM_UNIXTIME(created_at), FROM_UNIXTIME(closed_at)),0)) AS proc_time,
       project
   FROM
       fp_ops_tickets
   WHERE
       status = 3
       AND FROM_UNIXTIME(closed_at) >= '%v 00:00:00'
       AND FROM_UNIXTIME(closed_at) < '%v 00:00:00'
       AND project in(%v)
   GROUP BY
       project,
       day
   ORDER BY
       FIELD(project, %v) asc,
       day]], diffStartDate, diffEndDate, filterProjectList, filterProjectList)
    sData = sData == nil and {} or sData

    local rSeries = {}
    local yAxisR = {
        { title = { text = "新工单系统-工单平均处理时长(h)" } },
    }

    local num = 1
    local gmList = {}
    local gmDayList = {}
    for i, v in pairs(sData) do
        -- print(i,v)
        local gm = v.project
        local day = string.sub(v.day, 1, 10)
        local count, proc_time = v.count, v.proc_time
        --print("--", gm, day, count, proc_time)
        if utils.in_array(gm, gmList) == false then
            table.insert(gmList, gm)
        end

        if gmDayList[gm] == nil then
            gmDayList[gm] = {}
        end
        gmDayList[gm][day] = { Count = count, ProcTime = proc_time }
    end

    for i, gm in pairs(gmList) do
        local processingTime = {}
        for j, day in pairs(xdate) do
            if gmDayList[gm][day] == nil then
                table.insert(processingTime, 100)
            else
                local cnt = gmDayList[gm][day].Count ~= nil and tonumber(gmDayList[gm][day].Count) or 0
                local prctime = gmDayList[gm][day].ProcTime ~= nil and tonumber(gmDayList[gm][day].ProcTime) or 0
                table.insert(processingTime, toRate(prctime/3600/100, cnt))
            end
        end
        table.insert(rSeries, { name = gm .. "", type = "line", data = processingTime })
    end
    return yAxisR, rSeries, err
end

function toXdate()
    print(string.sub(diffStartDate, 1, 4), string.sub(diffStartDate, 6, 7), string.sub(diffStartDate, 9, 10))
    local currentDate = os.date("*t", os.time({ year = string.sub(diffStartDate, 1, 4), month = string.sub(diffStartDate, 6, 7), day = string.sub(diffStartDate, 9, 10) }))  -- 获取当前日期和时间的表

    for i = 1, 14 do
        local startDate = os.date("%Y-%m-%d", os.time(currentDate))
        table.insert(xdate, startDate)
        currentDate.day = currentDate.day + 1  -- 递增当前日期
    end
end

function ExchangeData(title, categories, yAxis, series, isPlot)
    local charts = {
        title = {
            text = title
        },
        xAxis = {
            categories = categories
        },
        yAxis = yAxis,
        series = series,
    }
    if isPlot then
        charts.plotOptions = {
            line = {
                dataLabels = {
                    enabled = true,
                    verticalAlign = "bottom",
                    allowOverlap = true
                },
            },
            column = {
                dataLabels = {
                    enabled = true,
                    verticalAlign = "top",
                    allowOverlap = true,
                },
            },
            bar = {
                dataLabels = {
                    enabled = true,
                    allowOverlap = true,
                },
            }
        }
    end
    print(charts)
    return GenerateChart(charts)
end

function GenerateChart(infile)
    -- local url = "http://charts-node-export-server-svc.general-cms.svc.cluster.local:81/"
    local url = "https://charts-node-export-server.funplus.com"
    local body = {
        async = true,
        asyncRendering = false,
        callback = "function(chart) {chart.renderer.label('This label is added in the callback', 1200, 1200).attr({fill : '#90ed7d',padding: 10, r: 10,zIndex: 10}).css({color: 'black',width: '1200px'}).add();}",
        constr = "Chart",
        scale = false,
        styledMode = false,
        infile = infile,
        type = "image/png",
        width = 1500
    }
    local conn = http.new("POST", url, body)
    print(utils.json_encode(body))
    conn:set_header("content-type", "application/json") -- POST请求设置请求头部
    local res, err = conn:request() -- 发起请求
    local header = res:header() -- 获取请求返回的header头信息
    local status = res:status() -- 获取HTTP返回码
    local data = res:data() -- 获取返回数据
    print(data)
    if status ~= 200 then
        return { status = -1, msg = utils.sprintf("https://charts-node-export-server.funplus.com/请求返回:%v", status) }
    end

    local img_conn = http.new("GET", utils.sprintf("https://acs-go-test.funplus.com/lua?p1=18&p2=111&path=%v", data))
    conn:set_header("content-type", "application/json") -- POST请求设置请求头部
    local res_img, err = img_conn:request() -- 发起请求
    local body_img = res_img:data() -- 获取返回数据
    body_data = utils.json_decode(body_img)

    print(body_data["data"])
    return 0, body_data["data"] == nil and "https://charts-node-export-server.funplus.com/" .. data or body_data["data"]
end

function toRate(a, b)
    if b == 0 then
        return 100
    end
    local rate = a * 100 / b
    local formattedRate = string.format("%.2f", rate)
    return tonumber(formattedRate)
end

main()