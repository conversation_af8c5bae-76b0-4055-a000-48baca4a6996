package scripts

import (
	"fmt"
	"github.com/labstack/echo/v4"
	"github.com/spf13/cast"
	"github.com/spf13/cobra"
	"github.com/spf13/viper"
	"github.com/xuri/excelize/v2"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"go.uber.org/zap"
	"net/http"
	dto "ops-ticket-api/internal/persistence"
	"ops-ticket-api/models"
	"ops-ticket-api/services"
	"ops-ticket-api/services/ticket_llm"
	"ops-ticket-api/utils"
)

// FetchAlgorithmFix 获取： go run main.go FetchAlgorithmFix -c=config/global-prod.yaml
func FetchAlgorithmFix(c *cobra.Command, args []string) {
	// 初始化 Echo 上下文
	e := echo.New()
	req := &http.Request{}        // 创建一个空的 HTTP 请求
	res := &echo.Response{}       // 创建一个空的 HTTP 响应
	ctx := e.NewContext(req, res) // 使用 req 和 res 初始化上下文

	var records = make([][]interface{}, 0, 0)
	ticketIds := GetTicketIDs()
	fmt.Println(len(ticketIds))
	qsts := GetQsts()
	fmt.Println(len(qsts))

	for i := 0; i < len(ticketIds); i++ {
		ticketId := cast.ToUint64(ticketIds[i])
		issueQst := cast.ToString(qsts[i])
		fmt.Printf("ticketID %d开始\n", ticketId)
		ticket, err := dto.NewTicket().GetTicketInfoFromMaster(ctx.Request().Context(), ticketId)
		if err != nil {
			logger.Error(ctx.Request().Context(), "GetTicketInfoFromMaster error", zap.Uint64("ticketID", ticketId), zap.String("err", err.Error()))
			records = append(records, []interface{}{ticketId, err})
			continue
		}
		solveType := ""
		if issueQst == "-" {
			solveType = "人工处理"
			records = append(records, []interface{}{ticketId, solveType})
			continue
		}
		if ticket_llm.CheckMeaninglessQuery(ctx.Request().Context(), issueQst) {
			solveType = "AI回复-无效单"
			records = append(records, []interface{}{ticketId, solveType})
			continue
		}
		catInfo, err := dto.NewCat().GetDetailById(ctx, ticket.CatID)
		if err != nil {
			logger.Error(ctx.Request().Context(), "GetCatDetailById error", zap.String("err", err.Error()))
			records = append(records, []interface{}{ticketId, err})
			continue
		}
		LoginCat := viper.GetString(fmt.Sprintf("strategy_first_cat_id.games.%s.login", ticket.Project))
		SuggestionCat := viper.GetString(fmt.Sprintf("strategy_first_cat_id.games.%s.suggestion", ticket.Project))

		if catInfo.Level == 3 && (catInfo.OneLevel == cast.ToUint32(LoginCat) || catInfo.OneLevel == cast.ToUint32(SuggestionCat)) {
			isMatch := ticket_llm.CheckCategoryMatch(ctx.Request().Context(), issueQst, catInfo.Category)
			if !isMatch {
				solveType = "人工处理"
				records = append(records, []interface{}{ticketId, solveType})
				continue
			}
			solveType = "AI回复-分级单"
			records = append(records, []interface{}{ticketId, solveType})
			continue
		}
		strategyInfo, err := dto.NewStrategyDao().GetStrategyByProject(ticket.Project)
		if err != nil {
			logger.Errorf(ctx.Request().Context(), "GetStrategyByProject error. %v", err)
			records = append(records, []interface{}{ticketId, err})
			continue
		}
		if strategyInfo != nil {
			strategyFilter := strategyInfo.DecodeStrategyFilters()
			if strategyFilter != nil && (strategyFilter.IsSeverAll ||
				models.CheckFilter(int64(ticket.Sid), strategyFilter.Server)) &&
				models.CheckFilter(int64(ticket.Recharge), strategyFilter.PayRange) {
				content, err := services.NewTicketSrv().MatchTicketKnowledge(ctx, ticket, issueQst)
				if err != nil {
					logger.Errorf(ctx.Request().Context(), "MatchTicketKnowledge error. %v", err)
					records = append(records, []interface{}{ticketId, err})
					continue
				}
				if content == "UNK" {
					solveType = "人工处理"
					records = append(records, []interface{}{ticketId, solveType})
					continue
				}
				solveType = "AI回复-分级单"
				records = append(records, []interface{}{ticketId, solveType})
				continue
			}
		}
		solveType = "人工处理"
		records = append(records, []interface{}{ticketId, solveType})
	}
	utils.CreateCsvFile("ai客服算法优化.csv", []string{"工单id", "新处理类型"}, records)

}

func GetTicketIDs() []string {
	// 打开 Excel 文件
	f, err := excelize.OpenFile("data/数据-验证.xlsx")
	if err != nil {
		elog.Errorf("Failed to open excel file: %v", err)
		return []string{}
	}
	defer func() {
		if err := f.Close(); err != nil {
			elog.Errorf("Failed to close excel file: %v", err)
		}
	}()

	// 获取第一个 sheet
	sheetName := f.GetSheetName(0)

	// 读取所有行
	rows, err := f.GetRows(sheetName)
	if err != nil {
		elog.Errorf("Failed to get rows: %v", err)
		return []string{}
	}

	// 提取第二列(问题列,索引为1)的内容,跳过第一行标题
	ticketIDs := make([]string, 0)
	for i, row := range rows {
		if i == 0 { // 跳过标题行
			continue
		}
		//fixme 空行
		if len(row) > 0 { // 确保行有第二列
			ticketIDs = append(ticketIDs, row[0])
		}
	}

	return ticketIDs
}

func GetQsts() []string {
	// 打开 Excel 文件
	f, err := excelize.OpenFile("data/数据-验证.xlsx")
	if err != nil {
		elog.Errorf("Failed to open excel file: %v", err)
		return []string{}
	}
	defer func() {
		if err := f.Close(); err != nil {
			elog.Errorf("Failed to close excel file: %v", err)
		}
	}()

	// 获取第一个 sheet
	sheetName := f.GetSheetName(0)

	// 读取所有行
	rows, err := f.GetRows(sheetName)
	if err != nil {
		elog.Errorf("Failed to get rows: %v", err)
		return []string{}
	}

	// 提取第一列(语种列,索引为0)的内容,跳过第一行标题
	qsts := make([]string, 0)
	for i, row := range rows {
		if i == 0 { // 跳过标题行
			continue
		}
		if len(row) >= 3 {
			qsts = append(qsts, row[2])
		}
	}

	return qsts
}
