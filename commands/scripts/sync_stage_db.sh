#! /bin/bash

set -x
tim=$(date "+%Y%m%d%H%M")
echo "start dump mysql data $tim"
fileName=./fp-ops-ticket-db.$tim.sql
echo "local file Name: $fileName"

# 1.0 dump mysql data
echo "dump start... "
mysqldump --skip-opt --set-gtid-purged=OFF --add-drop-table --column-statistics=0 -hcs-global-instance-1-read.c6h1gwzealou.us-west-2.rds.amazonaws.com -ureadonly -pQ0envGkl48xbK fp_ops_ticket --ignore-table=fp_ops_ticket.fp_ops_users > $fileName
echo "dump finished"

# 2.0 mysql import data
echo "mysql import start... "
# mysql -h127.0.0.1 -uroot -proot lin_test < $fileName
mysql -hmysql.mysql -uroot -pg2vAC466kr fp_ops_ticket < $fileName
echo "mysql import finished"

# 3.0 rm local file
echo "remove local file: $fileName"
rm $fileName
echo "remove local file finished"

echo "sync db data success."