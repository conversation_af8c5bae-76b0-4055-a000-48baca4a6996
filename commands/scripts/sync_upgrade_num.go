package scripts

import (
	"context"
	"fmt"
	"github.com/spf13/cobra"
	"gitlab-ee.funplus.io/ops-tools/compkg/dump"
	"ops-ticket-api/internal/elasticsearch"
	"ops-ticket-api/internal/framework/database"
	"ops-ticket-api/models"
	"sync"
)

// SyncUpgradeNum 同步所有工单的升级次数到DB和ES中，包括没有升级记录的工单
// go run main.go SyncUpgradeNum -c=config/global-test.yaml
func SyncUpgradeNum(c *cobra.Command, args []string) {
	db := database.Db()

	// 定义 TicketCount 结构
	type TicketCount struct {
		TicketID uint64 `gorm:"column:ticket_id"` // 工单ID
		Count    int    `gorm:"column:count"`     // 升级次数
	}

	// 用于存储查询结果
	var results = []*TicketCount{}
	// 用于记录失败的工单ID
	var failTickets = []uint64{}
	// 互斥锁，保护 failTickets 的并发访问
	var failTicketsMutex sync.Mutex

	// 等待所有 Goroutine 完成
	var wg sync.WaitGroup

	// 查询所有工单及其升级次数
	query := `
		SELECT 
			t.ticket_id AS ticket_id,
			IFNULL(h.count, 0) AS count
		FROM 
			fp_ops_tickets AS t
		LEFT JOIN (
			SELECT 
				ticket_id, COUNT(*) AS count
			FROM 
				fp_ops_tickets_history
			WHERE 
				operate = 11 AND op_detail = 1
			GROUP BY 
				ticket_id
		) AS h
		ON t.ticket_id = h.ticket_id
	`
	if err := db.Raw(query).Scan(&results).Error; err != nil {
		fmt.Printf("查询工单升级次数失败, 错误: %v\n", err)
		return
	}

	// 限制最多 50 个 Goroutine 并发执行
	sem := make(chan struct{}, 50)

	// 遍历查询结果，启动 Goroutine 处理
	for _, result := range results {
		wg.Add(1)
		sem <- struct{}{} // 信号量，限制 Goroutine 数量
		go func(result *TicketCount) {
			defer func() {
				wg.Done()
				<-sem // 释放信号量
			}()

			// 在 Goroutine 内部定义独立的 updateMap，避免共享数据竞争
			updateMap := map[string]interface{}{
				"upgrade_num": result.Count,
			}

			// 更新数据库
			if err := db.Model(&models.FpOpsTickets{}).Where("ticket_id = ?", result.TicketID).Updates(updateMap).Error; err != nil {
				fmt.Printf("更新数据库失败, 工单ID: %d, 错误: %v\n", result.TicketID, err)
				// 加锁保护 failTickets
				failTicketsMutex.Lock()
				failTickets = append(failTickets, result.TicketID)
				failTicketsMutex.Unlock()
				return
			}

			// 更新 Elasticsearch
			if err := elasticsearch.DefaultTicketSyncSvc.UpdateTicket(context.Background(), result.TicketID, updateMap); err != nil {
				fmt.Printf("更新 Elasticsearch 失败, 工单ID: %d, 错误: %v\n", result.TicketID, err)
				// 加锁保护 failTickets
				failTicketsMutex.Lock()
				failTickets = append(failTickets, result.TicketID)
				failTicketsMutex.Unlock()
			}
		}(result)
	}

	// 等待所有 Goroutine 执行完成
	wg.Wait()

	// 输出失败的工单ID
	fmt.Println("同步升级次数完成")
	dump.Dump(failTickets)
}
