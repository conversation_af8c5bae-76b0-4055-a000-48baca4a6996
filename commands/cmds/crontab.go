package cmds

import (
	"context"
	"os"
	"time"

	"ops-ticket-api/commands/cron"
	_ "ops-ticket-api/commands/cron/tasks" // 导入所有任务以触发init()注册

	baseCron "github.com/robfig/cron/v3"
	"github.com/spf13/cobra"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"gitlab-ee.funplus.io/ops-tools/compkg/metrics/metricsserver"
	"gitlab-ee.funplus.io/ops-tools/compkg/shutdown"
)

var (
	cronJob = baseCron.New(baseCron.WithSeconds(), baseCron.WithLocation(time.UTC))
	osEnv   = os.Getenv("environment")
)

// GetCronCommand 返回cron主命令
func GetCronCommand() *cobra.Command {
	cronCmd := &cobra.Command{
		Use:   "cron",
		Short: "Cron task management",
		Run: func(cmd *cobra.Command, args []string) {
			// 当不带子命令运行时，启动cron服务
			Crontab(cmd, args)
		},
	}

	// 为每个注册的任务创建子命令
	for _, cmd := range cron.GetAllCommands() {
		cronCmd.AddCommand(cron.CreateCobraCommand(cmd))
	}

	return cronCmd
}

// Crontab 服务入口
func Crontab(c *cobra.Command, args []string) {
	metricsserver.SetUp()
	ctx := context.Background()
	logger.Info(ctx, "Starting cron service...")

	go func() {
		if err := cronInit(ctx); err != nil {
			logger.Error(ctx, "cron init failed", logger.Any("error", err))
			return
		}
	}()

	shutdown.Wait(time.Second, func() error {
		logger.Info(context.Background(), "exit")
		ShutdownCron()
		return nil
	})
}

func cronInit(ctx context.Context) error {
	// 注册所有已定义的定时任务
	for _, cmd := range cron.GetAllCommands() {
		command := cmd // 创建副本以避免闭包问题
		if command.Schedule == "-" {
			continue
		}

		cronJob.AddFunc(command.Schedule, func() {
			if err := command.Run(ctx); err != nil {
				logger.Error(ctx, "Task execution failed",
					logger.String("task", command.Name),
					logger.Any("error", err))
			}
		})

	}

	cronJob.Start()
	logger.Info(ctx, "Cron service is now running")
	return nil
}

// ShutdownCron 停止定时任务
func ShutdownCron() {
	if cronJob != nil {
		cronJob.Stop()
	}
}
