package cmds

import (
	"context"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog"
	"ops-ticket-api/internal/framework/cache/local"
	"ops-ticket-api/internal/persistence"
	"time"
)

func syncFpxAuthConfigToLocal() {
	var fun = "syncFpxAuthConfigToLocalCommand->"
	defer func() {
		if _pErr := recover(); _pErr != nil {
			elog.Errorf("%s panic. recover:%v", fun, _pErr)
		}
	}()
	const retryCount = 3
	for i := 0; i < retryCount; i++ {
		if err := loadFpxAuthConfigToLocal(); err != nil {
			elog.Errorf("%s init return err. i:%d. err:%v", fun, i, err)
			time.Sleep(2 * time.Second)
			continue
		}
		return
	}
	elog.Errorf("failed to sync auth config to local after %d retries", retryCount)
	return
}

func loadFpxAuthConfigToLocal() error {
	authConfig := persistence.NewAuthConfig()
	list, err := authConfig.AuthConfigList(context.Background())
	if err != nil {
		return err
	}
	for _, cfg := range list {
		local.AuthConfigCache.ReSetAuthConfig(cfg.AuthKey, cfg)
	}
	return nil
}
