package cmds

import (
	"context"
	"fmt"
	"github.com/labstack/echo/v4"
	"github.com/labstack/echo/v4/middleware"
	"github.com/spf13/cobra"
	"github.com/spf13/viper"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"gitlab-ee.funplus.io/ops-tools/compkg/metrics"
	"gitlab-ee.funplus.io/ops-tools/compkg/shutdown"
	"gitlab-ee.funplus.io/ops-tools/compkg/traceing/echotrace"
	"golang.org/x/text/language"
	"net/http"
	"ops-ticket-api/commands/cron"
	"ops-ticket-api/commands/cron/dscevent"
	"ops-ticket-api/handlers"
	"ops-ticket-api/internal/framework/build"
	"ops-ticket-api/internal/framework/ctxresp"
	"ops-ticket-api/internal/framework/lang"
	"ops-ticket-api/internal/framework/plugins"
	"ops-ticket-api/internal/framework/validator"
	"time"
)

// DscGatewayEvent the service.
func DscGatewayEvent(c *cobra.Command, args []string) {
	// 初始化metrics
	//metricsserver.SetUp()

	if viper.GetBool("alarm.alert") {
		initAlarm("DiscordEvent")
	}

	// 初始化引擎
	e = echo.New()
	e.HideBanner = false
	e.Validator = validator.NewValidate()
	e.HTTPErrorHandler = ctxresp.ErrorHandler
	e.Debug = viper.GetBool("app.debug")

	e.Use(echotrace.Trace())
	e.Use(metrics.EchoPrometheus())
	e.Use(middleware.GzipWithConfig(middleware.GzipConfig{}))
	e.Use(plugins.Logger(plugins.LoggerConfig{Skipper: middleware.DefaultSkipper}))
	e.Use(middleware.RecoverWithConfig(middleware.RecoverConfig{StackSize: 1 << 10}))
	e.Use(plugins.LangMiddleware(lang.NewBundle(language.Chinese, viper.GetStringSlice("i18n")...)))

	// task cmds
	cron.InitDscEventConsole(e.NewContext(&http.Request{}, &echo.Response{}))

	// 健康检查
	logger.Info(context.TODO(), "Discord Gateway Event Build info.", logger.String("go-arch", build.InfoData.GoArch), logger.String("go-os", build.InfoData.GoOs), logger.String("go-version", build.InfoData.GoVersion), logger.Bool("cgo-enabled", build.InfoData.CgoEnabled), logger.String("HttpAddr", viper.GetString("server.addr")), logger.Bool("DebugMode", viper.GetBool("app.debug")), logger.String("RunMode", viper.GetString("app.env")))

	e.GET("health", handlers.Health)
	//router.DscEventRouter(e)
	//go func() {
	//	if err := e.Start(viper.GetString("server.addr")); err != nil && err != http.ErrServerClosed {
	//		e.Logger.Fatal("shutting down the server", err)
	//	}
	//}()

	// discord gateway event 监听，获取数据
	dscevent.ReceiveDscEventsHandle(e.NewContext(&http.Request{}, &echo.Response{}))

	shutdown.Wait(time.Second*2, func() error {
		logger.Info(context.Background(), "exit")
		cron.ShutDownDscEventConsole(context.TODO())
		Shutdown()
		return nil
	})
}

// DscSyncUserAndCommu the service.
func DscSyncUserAndCommu(c *cobra.Command, args []string) {
	// 初始化metrics
	//metricsserver.SetUp()

	if viper.GetBool("alarm.alert") {
		initAlarm("DiscordEvent")
	}

	// 初始化引擎
	e = echo.New()
	e.HideBanner = false
	e.Validator = validator.NewValidate()
	e.HTTPErrorHandler = ctxresp.ErrorHandler
	e.Debug = viper.GetBool("app.debug")

	e.Use(echotrace.Trace())
	e.Use(metrics.EchoPrometheus())
	e.Use(middleware.GzipWithConfig(middleware.GzipConfig{}))
	e.Use(plugins.Logger(plugins.LoggerConfig{Skipper: middleware.DefaultSkipper}))
	e.Use(middleware.RecoverWithConfig(middleware.RecoverConfig{StackSize: 1 << 10}))
	e.Use(plugins.LangMiddleware(lang.NewBundle(language.Chinese, viper.GetStringSlice("i18n")...)))

	// task cmds
	cron.InitDscEventConsole(e.NewContext(&http.Request{}, &echo.Response{}))

	// 健康检查
	logger.Info(context.TODO(), "Discord Gateway Event Build info.", logger.String("go-arch", build.InfoData.GoArch), logger.String("go-os", build.InfoData.GoOs), logger.String("go-version", build.InfoData.GoVersion), logger.Bool("cgo-enabled", build.InfoData.CgoEnabled), logger.String("HttpAddr", viper.GetString("server.addr")), logger.Bool("DebugMode", viper.GetBool("app.debug")), logger.String("RunMode", viper.GetString("app.env")))

	// reload all guild member
	dscevent.RefreshGuildUsers()

	// sync user channel
	dscevent.AsyncChannelDialog()
	fmt.Println("DscSyncUserAndCommu over ---->>>>>>>>")
}
