package cmds

import (
	"bytes"
	"context"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/google/uuid"
	"github.com/labstack/echo/v4"
	"github.com/labstack/echo/v4/middleware"
	"github.com/line/line-bot-sdk-go/linebot"
	"github.com/spf13/cast"
	"github.com/spf13/cobra"
	"github.com/spf13/viper"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"gitlab-ee.funplus.io/ops-tools/compkg/metrics"
	"gitlab-ee.funplus.io/ops-tools/compkg/shutdown"
	"gitlab-ee.funplus.io/ops-tools/compkg/traceing/echotrace"
	"io"
	"net/http"
	"ops-ticket-api/handlers"
	"ops-ticket-api/internal/code"
	"ops-ticket-api/internal/framework/build"
	"ops-ticket-api/internal/framework/cache/keys"
	"ops-ticket-api/internal/framework/cache/rds"
	"ops-ticket-api/internal/framework/plugins"
	"ops-ticket-api/internal/persistence"
	"ops-ticket-api/models"
	"ops-ticket-api/proto/pb"
	"ops-ticket-api/utils"
	"os"
	"os/signal"
	"strings"
	"syscall"
	"time"
)

var keyWord = "unfollow"

// 创建Line bot客户端
func newLineBotClient(channelSecret, channelToken string) (*linebot.Client, error) {
	return linebot.New(channelSecret, channelToken)
}

func validateLineSignatureWithBody(c echo.Context, body []byte, channelSecret string) error {
	// 获取请求头中的 x-line-signature
	signature := c.Request().Header.Get("x-line-signature")
	if signature == "" {
		return fmt.Errorf("missing x-line-signature header")
	}
	// 将签名进行base64解码
	decodedSignature, err := base64.StdEncoding.DecodeString(signature)
	if err != nil {
		return fmt.Errorf("failed to decode signature: %v", err)
	}

	// 使用channelSecret和消息体生成HMAC-SHA256哈希
	hash := hmac.New(sha256.New, []byte(channelSecret))
	hash.Write(body)
	computedSignature := hash.Sum(nil)

	// 比较计算出的签名和Line提供的签名是否匹配
	if !hmac.Equal(computedSignature, decodedSignature) {
		return fmt.Errorf("invalid signature")
	}
	return nil
}

func handleTextMessage(ctx context.Context, webhookEvent utils.Event, commu *models.FpLineCommu) error {
	logger.Info(ctx, "Reached handleTextMessage")
	content := ""
	eventMessage := webhookEvent.Message
	logger.Infof(ctx, "message text is:%v", eventMessage.Text)
	if len(eventMessage.Emojis) == 0 {
		content = eventMessage.Text
	} else {
		content = utils.ReconstructMessage(eventMessage)
		logger.Infof(ctx, "construct message is:%v", content)
	}
	// 写入消息记录表
	record := &models.FpLineCommu{
		MsgID:       commu.MsgID,
		Project:     commu.Project,
		FromUserID:  commu.FromUserID,
		BotID:       commu.BotID,
		LineUserID:  commu.LineUserID,
		ChannelID:   commu.ChannelID,
		Content:     content,
		EventId:     commu.EventId,
		QuoteToken:  commu.QuoteToken,
		MessageType: code.LineMessageTypeText,
		Redelivery:  commu.Redelivery,
		TickTime:    commu.TickTime,
		CreatedAt:   commu.CreatedAt,
		UpdatedAt:   commu.UpdatedAt,
	}
	if err := persistence.NewLineInteractions().SaveUserCommu(ctx, record); err != nil {
		return err
	}
	return nil
}

func handleImageMessage(ctx context.Context, accessToken string, webhookEvent utils.Event, commu *models.FpLineCommu) (err error) {
	contentProvider := webhookEvent.Message.ContentProvider.Type
	originalContentUrl, previewImageUrl := "", ""
	if contentProvider == "line" {
		// 调用get content API获取文件内容，上传OSS, 获取文件url
		originalContentUrl, err = utils.LineGetOriginalContentUrl(ctx, accessToken, "image", webhookEvent.Message.ID)
		if err != nil {
			return err
		}
		previewImageUrl, err = utils.LineGetPreviewImageUrl(ctx, accessToken, "image", webhookEvent.Message.ID)
		if err != nil {
			return err
		}
	}
	if contentProvider == "external" {
		// 获取external media url
		originalContentUrl = webhookEvent.Message.ContentProvider.OriginalContentUrl
		previewImageUrl = webhookEvent.Message.ContentProvider.PreviewImageUrl
	}
	// 写入消息记录表
	record := &models.FpLineCommu{
		MsgID:              commu.MsgID,
		Project:            commu.Project,
		FromUserID:         commu.FromUserID,
		BotID:              commu.BotID,
		LineUserID:         commu.LineUserID,
		ChannelID:          commu.ChannelID,
		EventId:            commu.EventId,
		QuoteToken:         commu.QuoteToken,
		Redelivery:         commu.Redelivery,
		ContentProvider:    contentProvider,
		OriginalContentUrl: originalContentUrl,
		PreviewImageUrl:    previewImageUrl,
		MessageType:        code.LineMessageTypeImage,
		TickTime:           commu.TickTime,
		CreatedAt:          commu.CreatedAt,
		UpdatedAt:          commu.UpdatedAt,
	}
	if err = persistence.NewLineInteractions().SaveUserCommu(ctx, record); err != nil {
		return err
	}
	return nil
}

func handleVideoMessage(ctx context.Context, accessToken string, webhookEvent utils.Event, commu *models.FpLineCommu) (err error) {
	contentProvider := webhookEvent.Message.ContentProvider.Type
	originalContentUrl, previewImageUrl := "", ""
	duration := webhookEvent.Message.Duration
	if contentProvider == "line" {
		// 调用get content API获取文件内容，上传OSS, 获取文件url
		originalContentUrl, err = utils.LineGetOriginalContentUrl(ctx, accessToken, "video", webhookEvent.Message.ID)
		if err != nil {
			return err
		}
		previewImageUrl, err = utils.LineGetPreviewImageUrl(ctx, accessToken, "video", webhookEvent.Message.ID)
		if err != nil {
			return err
		}
	}
	if contentProvider == "external" {
		// 获取external media url
		originalContentUrl = webhookEvent.Message.ContentProvider.OriginalContentUrl
		previewImageUrl = webhookEvent.Message.ContentProvider.PreviewImageUrl
	}
	// 写入消息记录表
	record := &models.FpLineCommu{
		MsgID:              commu.MsgID,
		Project:            commu.Project,
		FromUserID:         commu.FromUserID,
		BotID:              commu.BotID,
		LineUserID:         commu.LineUserID,
		ChannelID:          commu.ChannelID,
		EventId:            commu.EventId,
		QuoteToken:         commu.QuoteToken,
		Redelivery:         commu.Redelivery,
		ContentProvider:    contentProvider,
		OriginalContentUrl: originalContentUrl,
		PreviewImageUrl:    previewImageUrl,
		Duration:           uint64(duration),
		MessageType:        code.LineMessageTypeVideo,
		TickTime:           commu.TickTime,
		CreatedAt:          commu.CreatedAt,
		UpdatedAt:          commu.UpdatedAt,
	}
	if err = persistence.NewLineInteractions().SaveUserCommu(ctx, record); err != nil {
		return err
	}
	return nil
}

func handleAudioMessage(ctx context.Context, accessToken string, webhookEvent utils.Event, commu *models.FpLineCommu) (err error) {
	contentProvider := webhookEvent.Message.ContentProvider.Type
	originalContentUrl := ""
	duration := webhookEvent.Message.Duration
	if contentProvider == "line" {
		// 调用get content API获取文件内容，上传OSS, 获取文件url
		originalContentUrl, err = utils.LineGetOriginalContentUrl(ctx, accessToken, "audio", webhookEvent.Message.ID)
		if err != nil {
			return err
		}
	}
	if contentProvider == "external" {
		// 获取external media url
		originalContentUrl = webhookEvent.Message.ContentProvider.OriginalContentUrl
	}
	// 写入消息记录表
	record := &models.FpLineCommu{
		MsgID:              commu.MsgID,
		Project:            commu.Project,
		FromUserID:         commu.FromUserID,
		BotID:              commu.BotID,
		LineUserID:         commu.LineUserID,
		ChannelID:          commu.ChannelID,
		EventId:            commu.EventId,
		QuoteToken:         commu.QuoteToken,
		Redelivery:         commu.Redelivery,
		ContentProvider:    contentProvider,
		OriginalContentUrl: originalContentUrl,
		Duration:           uint64(duration),
		MessageType:        code.LineMessageTypeAudio,
		TickTime:           commu.TickTime,
		CreatedAt:          commu.CreatedAt,
		UpdatedAt:          commu.UpdatedAt,
	}
	if err = persistence.NewLineInteractions().SaveUserCommu(ctx, record); err != nil {
		return err
	}
	return nil
}

func handleFileMessage(ctx context.Context, accessToken string, webhookEvent utils.Event, commu *models.FpLineCommu) (err error) {
	originalContentUrl, err := utils.LineGetOriginalContentUrl(ctx, accessToken, "file", webhookEvent.Message.ID)
	if err != nil {
		return err
	}
	file := map[string]interface{}{
		"fileName": webhookEvent.Message.FileName,
		"fileSize": webhookEvent.Message.FileSize,
	}
	fileInfo, err := json.Marshal(file)
	if err != nil {
		logger.Errorf(ctx, "json marshal file err %v", err)
		return err
	}
	// 写入消息记录表
	record := &models.FpLineCommu{
		MsgID:              commu.MsgID,
		Project:            commu.Project,
		FromUserID:         commu.FromUserID,
		BotID:              commu.BotID,
		LineUserID:         commu.LineUserID,
		ChannelID:          commu.ChannelID,
		EventId:            commu.EventId,
		QuoteToken:         commu.QuoteToken,
		Redelivery:         commu.Redelivery,
		Content:            string(fileInfo),
		OriginalContentUrl: originalContentUrl,
		MessageType:        code.LineMessageTypeFile,
		TickTime:           commu.TickTime,
		CreatedAt:          commu.CreatedAt,
		UpdatedAt:          commu.UpdatedAt,
	}
	if err = persistence.NewLineInteractions().SaveUserCommu(ctx, record); err != nil {
		return err
	}
	return nil
}

func handleStickerMessage(ctx context.Context, webhookEvent utils.Event, commu *models.FpLineCommu) error {
	sticker := map[string]interface{}{
		"stickerId":           webhookEvent.Message.StickerId,
		"packageId":           webhookEvent.Message.PackageId,
		"stickerResourceType": webhookEvent.Message.StickerResourceType,
		"keywords":            webhookEvent.Message.Keywords,
	}
	stickerInfo, err := json.Marshal(sticker)
	if err != nil {
		logger.Errorf(ctx, "json marshal sticker err %v", err)
		return err
	}
	// 写入消息记录表
	record := &models.FpLineCommu{
		MsgID:       commu.MsgID,
		Project:     commu.Project,
		FromUserID:  commu.FromUserID,
		BotID:       commu.BotID,
		LineUserID:  commu.LineUserID,
		ChannelID:   commu.ChannelID,
		EventId:     commu.EventId,
		QuoteToken:  commu.QuoteToken,
		Redelivery:  commu.Redelivery,
		MessageType: code.LineMessageTypeSticker,
		StickerInfo: string(stickerInfo),
		TickTime:    commu.TickTime,
		CreatedAt:   commu.CreatedAt,
		UpdatedAt:   commu.UpdatedAt,
	}
	if err = persistence.NewLineInteractions().SaveUserCommu(ctx, record); err != nil {
		return err
	}
	return nil
}

func handleLocationMessage(ctx context.Context, webhookEvent utils.Event, commu *models.FpLineCommu) error {
	location := map[string]interface{}{
		"address":   webhookEvent.Message.Address,
		"latitude":  webhookEvent.Message.Latitude,
		"longitude": webhookEvent.Message.Longitude,
		"title":     webhookEvent.Message.Title,
	}
	locationInfo, err := json.Marshal(location)
	if err != nil {
		logger.Errorf(ctx, "json marshal location err %v", err)
		return err
	}
	// 写入消息记录表
	record := &models.FpLineCommu{
		MsgID:       commu.MsgID,
		Project:     commu.Project,
		FromUserID:  commu.FromUserID,
		BotID:       commu.BotID,
		LineUserID:  commu.LineUserID,
		ChannelID:   commu.ChannelID,
		EventId:     commu.EventId,
		QuoteToken:  commu.QuoteToken,
		Redelivery:  commu.Redelivery,
		MessageType: code.LineMessageTypeLocation,
		Location:    string(locationInfo),
		TickTime:    commu.TickTime,
		CreatedAt:   commu.CreatedAt,
		UpdatedAt:   commu.UpdatedAt,
	}
	if err = persistence.NewLineInteractions().SaveUserCommu(ctx, record); err != nil {
		return err
	}
	return nil
}

func handleMessageEvent(ctx context.Context, accessToken, project, botId, channelId string, webhookEvent utils.Event, profile *linebot.UserProfileResponse) (err error) {
	currentTime := time.Now().UTC()
	commu := &models.FpLineCommu{
		MsgID:      webhookEvent.Message.ID,
		Project:    project,
		FromUserID: webhookEvent.Source.UserId,
		BotID:      botId,
		LineUserID: webhookEvent.Source.UserId,
		ChannelID:  channelId,
		EventId:    webhookEvent.WebhookEventId,
		QuoteToken: webhookEvent.Message.QuoteToken,
		Redelivery: webhookEvent.DeliveryContext.IsRedelivery,
		TickTime:   uint64(webhookEvent.Timestamp),
		CreatedAt:  currentTime,
		UpdatedAt:  currentTime,
	}
	logger.Info(ctx, "Reached switch message type")
	switch webhookEvent.Message.Type {
	case "text":
		err = handleTextMessage(ctx, webhookEvent, commu)
	case "image":
		err = handleImageMessage(ctx, accessToken, webhookEvent, commu)
	case "video":
		err = handleVideoMessage(ctx, accessToken, webhookEvent, commu)
	case "audio":
		err = handleAudioMessage(ctx, accessToken, webhookEvent, commu)
	case "file":
		err = handleFileMessage(ctx, accessToken, webhookEvent, commu)
	case "sticker":
		err = handleStickerMessage(ctx, webhookEvent, commu)
	case "location":
		err = handleLocationMessage(ctx, webhookEvent, commu)
	default:
		// 处理其他类型的消息
		logger.Infof(ctx, "receive unsupported message type %v", webhookEvent.Message.Type)
	}
	if err != nil {
		logger.Errorf(ctx, "failed to handle %s message messageId %s err:%v", webhookEvent.Message.Type, webhookEvent.Message.ID, err)
		return err
	}
	return nil
}

func handleFollowEvent(ctx context.Context, profile *linebot.UserProfileResponse, bot *models.FpLineBot, channel *models.FpLineChannel) error {
	logger.Infof(ctx, "enter handleFollowEvent userId is %v", profile.UserID)
	now := time.Now()
	lineUser := &models.FpLineUser{
		Project:      bot.Project,
		BotID:        bot.BotID,
		LineUserID:   profile.UserID,
		ChannelID:    channel.ChannelID,
		DisplayName:  profile.DisplayName,
		PictureUrl:   profile.PictureURL,
		FollowStatus: code.LineFollowed,
		IsDeleted:    code.DscNoDelete,
		JoinedAt:     bot.CreatedAt,
		CreatedAt:    bot.CreatedAt,
		UpdatedAt:    bot.UpdatedAt,
	}
	if err := persistence.NewLineInteractions().SaveUser(ctx, lineUser); err != nil {
		return err
	}
	// 不能使用官方的SDK发送消息，否则拿不到消息id和quote_token,只能通过HTTP调用的方式
	// 向用户发送一条欢迎消息
	message := []interface{}{
		utils.NewTextMessage(channel.GreetingMessage, nil),
	}
	logger.Info(ctx, "enter push message")
	resp, err := utils.LinePushMessage(profile.UserID, channel.AccessToken, message)
	if err != nil {
		logger.Errorf(ctx, "send greeting message err:%v", err)
		return err
	}
	if len(resp.SentMessages) > 0 {
		respInfo := resp.SentMessages[0]
		msgId, quoteMsgToken := respInfo.ID, respInfo.QuoteToken
		// 写入消息记录表
		record := &models.FpLineCommu{
			MsgID:       msgId,
			Project:     bot.Project,
			FromUserID:  bot.BotID,
			BotID:       bot.BotID,
			LineUserID:  profile.UserID,
			ChannelID:   channel.ChannelID,
			Content:     channel.GreetingMessage,
			QuoteToken:  quoteMsgToken,
			MessageType: code.LineMessageTypeText,
			TickTime:    utils.NowTimestampMill(),
			CreatedAt:   now,
			UpdatedAt:   now,
		}
		// 写入客服绑定消息记录表
		send := &models.FpLineSend{
			MsgID:     msgId,
			BotID:     bot.BotID,
			ChannelID: channel.ChannelID,
			Operator:  "system",
			CreatedAt: now,
			UpdatedAt: now,
		}
		// 记录操作日志
		opDetail := &models.FpLineOperateLog{
			OperationGroup:  pb.OpGroup_OpGroupSendTextMessage.String(),
			OperationAction: pb.OpAction_OpActionAdd.String(),
			BaseID:          msgId,
			UniqueID:        uuid.New().String(),
			Project:         bot.Project,
			BeforeDetail:    "{}",
			AfterDetail:     utils.ToJson(send),
			CreateTime:      now,
			Operator:        "system",
		}
		if err = persistence.NewLineInteractions().SaveBotCommu(ctx, record, send, opDetail); err != nil {
			return err
		}
		return nil
	}
	return fmt.Errorf("line push message err:%v", resp.SentMessages)
}

func handleUnfollowEvent(ctx context.Context, project, lineUserId, botId string) error {
	// unfollow用户相关数据
	if err := persistence.NewLineInteractions().UnfollowLineUser(ctx, project, lineUserId, botId); err != nil {
		return err
	}
	lineUser, err := persistence.NewLineInteractions().FetchLineUserById(ctx, lineUserId, project)
	if err != nil {
		return err
	}
	// 用户取消关注飞书告警
	alertTime := utils.TimeFormat(int64(utils.NowTimestamp()))
	title := fmt.Sprintf("#Line用户取消关注告警推送-%v", alertTime)
	var msg strings.Builder
	msg.WriteString(keyWord)
	msg.WriteString("\n")
	msg.WriteString(fmt.Sprintf("project:%s, user_id:%s, display_name:%s", project, lineUserId, lineUser.DisplayName))
	msg.WriteString("\n")
	utils.RobotTaskLineUnfollowPush.SendMarkdown(ctx, title, utils.MSG_CARD_HEADER_COLOR_orange, msg.String())
	return nil
}

func lineWebhookHandler(c echo.Context) error {
	fun := "lineWebhookHandler"
	// 从查询字符串中获取project和channel_id
	project := c.QueryParam("project")
	channelId := c.QueryParam("channel_id")
	if project == "" || channelId == "" {
		logger.Errorf(c.Request().Context(), "%s project or channel_id is empty", fun)
		return c.String(http.StatusBadRequest, "Missing project or channel_id")
	}
	// 获取频道配置
	channel, err := persistence.NewLineInteractions().FetchChanelById(c.Request().Context(), channelId)
	if err != nil {
		return err
	}
	// 读取请求体并保存
	body, err := io.ReadAll(c.Request().Body)
	if err != nil {
		logger.Errorf(c.Request().Context(), "%s Failed to read request body: %v", fun, err)
		return c.String(http.StatusBadRequest, "Invalid Request Body")
	}
	// 解析请求体获取需要的数据
	var webhookReq utils.WebhookRequest
	if err = json.Unmarshal(body, &webhookReq); err != nil {
		logger.Errorf(c.Request().Context(), "%s Failed to parse webhook request JSON error:%v", fun, err)
		return err
	}
	// 校验Line签名
	if err = validateLineSignatureWithBody(c, body, channel.Secret); err != nil {
		logger.Errorf(c.Request().Context(), "%s Signature validation failed: %v", fun, err)
		return c.String(http.StatusBadRequest, "Invalid Signature")
	}
	// 重置请求体供bot.ParseRequest使用
	c.Request().Body = io.NopCloser(bytes.NewBuffer(body))
	logger.Infof(c.Request().Context(), "Signature validation passed!")
	logger.Infof(c.Request().Context(), "line request body is %v", string(body))
	// 创建Line bot客户端
	botCli, err := newLineBotClient(channel.Secret, channel.AccessToken)
	if err != nil {
		logger.Errorf(c.Request().Context(), "%s start lineEvent err:%v", fun, err)
		return err
	}
	// 获取机器人信息
	botInfo, err := botCli.GetBotInfo().Do()
	if err != nil {
		logger.Errorf(c.Request().Context(), "%s get bot info error:%v", fun, err)
		return err
	}
	bot := &models.FpLineBot{
		Project:     project,
		BotID:       botInfo.UserID,
		ChannelID:   channelId,
		BasicID:     botInfo.BasicID,
		PremiumID:   botInfo.PremiumID,
		DisplayName: botInfo.DisplayName,
		PictureUrl:  botInfo.PictureURL,
		ChatMode:    cast.ToString(botInfo.ChatMode),
		ReadMode:    cast.ToString(botInfo.MarkAsReadMode),
	}
	// 创建带超时的上下文, 设置60秒超时

	timeoutCtx, cancel := context.WithTimeout(utils.ContextOnlyValue{c.Request().Context()}, 60*time.Second)
	// 确保超时后取消
	defer cancel()
	if err = persistence.NewLineInteractions().SaveBot(timeoutCtx, bot); err != nil {
		return err
	}
	// 从请求中解析事件
	events, err := botCli.ParseRequest(c.Request())
	if err != nil {
		if errors.Is(err, linebot.ErrInvalidSignature) {
			logger.Errorf(timeoutCtx, "%s Invalid Signature", fun)
			return c.String(http.StatusBadRequest, "Invalid Signature")
		}
		return c.String(http.StatusInternalServerError, "Parse Error")
	}
	logger.Info(c.Request().Context(), "Reached bot.ParseRequest", logger.Any("events", events))
	for i, event := range events {
		// 因为webhook有可能重复推送，所以需要去重
		webhookEvent := webhookReq.Events[i]
		logger.Infof(c.Request().Context(), "webhookEvent is %+v", webhookEvent)
		//eventId := webhookEvent.WebhookEventId
		//repeat, checkErr := persistence.NewLineInteractions().CheckRepeatEvent(timeoutCtx, eventId)
		//if checkErr != nil {
		//	return checkErr
		//}
		//if repeat {
		//	logger.Infof(timeoutCtx, "repeat eventId is %v\n", eventId)
		//	continue
		//}
		if rds.Exists(timeoutCtx, webhookEvent.WebhookEventId) {
			continue
		}
		// If a user gave no consent to access their user profile information, the webhook contains no user ID.
		if webhookEvent.Source.UserId == "" {
			logger.Errorf(timeoutCtx, "%s Failed to get line user id", fun)
			return errors.New("failed to get line user id")
		}
		// 获取用户信息
		profile, profileErr := botCli.GetProfile(webhookEvent.Source.UserId).Do()
		// 取关后没有权限获取用户信息，所以这里有error不能return，否则会一直重复推送取关事件，出现混乱
		if profileErr != nil {
			logger.Errorf(c.Request().Context(), "%s Failed to get user profile error:%v", fun, profileErr)
		}
		if profile == nil {
			profile = &linebot.UserProfileResponse{}
		}
		logger.Info(c.Request().Context(), "Reached bot.GetProfile", logger.Any("profile", profile))
		lineUser, err := persistence.NewLineInteractions().FetchLineUserById(timeoutCtx, webhookEvent.Source.UserId, project)
		if err != nil {
			return err
		}
		logger.Info(c.Request().Context(), "Reached FetchLineUserById first")
		logger.Info(c.Request().Context(), "Reached switch event type")
		switch event.Type {
		// 消息事件
		case linebot.EventTypeMessage:
			// 如果该用户已被删除，则不再解析该用户的webhook事件
			if lineUser.ID > 0 && lineUser.IsDeleted == code.DscDeleted {
				logger.Infof(timeoutCtx, "line user is already deleted, userId: %s", profile.UserID)
				continue
			}
			err = handleMessageEvent(timeoutCtx, channel.AccessToken, project, bot.BotID, channelId, webhookEvent, profile)
		// 用户关注事件(加好友)
		case linebot.EventTypeFollow:
			err = handleFollowEvent(timeoutCtx, profile, bot, channel)
		// 用户取消关注事件(删除好友)
		case linebot.EventTypeUnfollow:
			err = handleUnfollowEvent(timeoutCtx, bot.Project, webhookEvent.Source.UserId, bot.BotID)
		// 用户撤回消息事件
		case linebot.EventTypeUnsend:
			err = persistence.NewLineInteractions().RevokeMessage(timeoutCtx, webhookEvent.Unsend.MessageId)
		default:
			// 处理其他类型的消息
			logger.Infof(timeoutCtx, "receive unsupported event type %v", cast.ToString(event.Type))
		}
		if err != nil {
			logger.Errorf(timeoutCtx, "failed to handle %s event messageId %s err:%v", cast.ToString(event.Type), webhookEvent.Message.ID, err)
			return err
		}
		// 标记该webhook事件为已处理
		if err = rds.Set(timeoutCtx, webhookEvent.WebhookEventId, "done", keys.Expire1H); err != nil {
			logger.Errorf(timeoutCtx, "set redis key:%v error:%v", webhookEvent.WebhookEventId, err)
		}
	}
	return c.NoContent(http.StatusOK)
}

// LineGatewayEvent the service.
func LineGatewayEvent(c *cobra.Command, args []string) {
	// 初始化metrics
	if viper.GetBool("alarm.alert") {
		initAlarm("LineEvent")
	}
	// Echo实例
	e = echo.New()
	// 中间件
	e.Use(middleware.Logger())
	e.Use(plugins.Logger(plugins.LoggerConfig{}))
	e.Use(middleware.Recover())
	e.Use(echotrace.Trace())
	e.Use(metrics.EchoPrometheus())

	// 健康检查
	logger.Info(context.TODO(), "Line Gateway Event Build info.", logger.String("go-arch", build.InfoData.GoArch), logger.String("go-os", build.InfoData.GoOs), logger.String("go-version", build.InfoData.GoVersion), logger.Bool("cgo-enabled", build.InfoData.CgoEnabled), logger.String("HttpAddr", viper.GetString("server.addr")), logger.Bool("DebugMode", viper.GetBool("app.debug")), logger.String("RunMode", viper.GetString("app.env")))
	e.GET("health", handlers.Health)
	// Line Webhook URL的路由
	e.POST("/line/webhook", lineWebhookHandler)
	// 启动 HTTP 服务器
	go func() {
		if err := e.Start(":9000"); err != nil && !errors.Is(err, http.ErrServerClosed) {
			e.Logger.Fatal("shutting down the server")
		}
	}()
	// 设置系统信号监听器
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	// 优雅关闭服务器，等待当前处理完成
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()
	if err := e.Shutdown(ctx); err != nil {
		e.Logger.Fatal(err)
	}
	shutdown.Wait(time.Second*2, func() error {
		logger.Info(context.Background(), "line event exit")
		Shutdown()
		return nil
	})
}
