// Copyright 2021 funplus Authors. All Rights Reserved.
// @Description: server
// @Author: Darcy
// @Date: 2021/10/13 2:07 PM

package cmds

import (
	"context"
	"github.com/spf13/cobra"
	"gitlab-ee.funplus.io/ops-tools/compkg/metrics"
	"net/http"
	"ops-ticket-api/commands/cron"
	"strings"
	"sync"
	"time"

	"ops-ticket-api/commands/cmds/router"
	"ops-ticket-api/handlers"
	"ops-ticket-api/internal/framework/build"
	"ops-ticket-api/internal/framework/ctxresp"
	"ops-ticket-api/internal/framework/lang"
	"ops-ticket-api/internal/framework/plugins"
	"ops-ticket-api/internal/framework/validator"

	"github.com/labstack/echo/v4"
	"github.com/labstack/echo/v4/middleware"
	"github.com/spf13/cast"
	"github.com/spf13/viper"
	"gitlab-ee.funplus.io/ops-tools/compkg/alert"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"gitlab-ee.funplus.io/ops-tools/compkg/shutdown"
	"gitlab-ee.funplus.io/ops-tools/compkg/traceing/echotrace"
	"golang.org/x/text/language"
)

// 参数
var e *echo.Echo
var alarmOnce sync.Once

// Serve the service.
func Serve(c *cobra.Command, args []string) {
	// 初始化metrics
	//metricsserver.SetUp()

	if viper.GetBool("alarm.alert") {
		initAlarm("Serv")
	}

	// 初始化引擎
	e = echo.New()
	e.Validator = validator.NewValidate()
	e.HTTPErrorHandler = ctxresp.ErrorHandler
	e.Debug = viper.GetBool("app.debug")
	e.HideBanner = true

	e.Use(middleware.GzipWithConfig(middleware.GzipConfig{}))
	if !viper.GetBool("app.debug") {
		e.Use(middleware.RecoverWithConfig(middleware.RecoverConfig{StackSize: 1 << 10}))
	}

	e.Use(plugins.Logger(plugins.LoggerConfig{
		Skipper: func(context echo.Context) bool {
			if strings.Contains(context.Path(), "export") ||
				strings.Contains(context.Path(), "Export") ||
				strings.Contains(context.Path(), "/data_plat/") ||
				strings.Contains(context.Path(), "/opts") ||
				strings.Contains(context.Path(), "proxy/resource") ||
				strings.Contains(context.Path(), "user") {
				return true
			}
			return false
		}}))
	e.Use(echotrace.Trace())
	e.Use(metrics.EchoPrometheus())

	i18nToml := viper.GetStringSlice("i18n")
	e.Use(plugins.LangMiddleware(lang.NewBundle(language.Chinese, i18nToml...)))

	// 将游戏的auth config数据加载到本地内存
	go syncFpxAuthConfigToLocal()

	// 内存任务 + cron
	cron.InitTicketServCmds(e.NewContext(&http.Request{}, &echo.Response{}))
	// 健康检查
	e.GET("health", handlers.Health)

	router.BackendRouter(e)
	router.EgressRouter(e)

	//内部服务路由
	router.InnerRouter(e)

	logger.Info(context.Background(), "Build info.",
		logger.String("go-arch", build.InfoData.GoArch),
		logger.String("go-os", build.InfoData.GoOs),
		logger.String("go-version", build.InfoData.GoVersion),
		logger.Bool("cgo-enabled", build.InfoData.CgoEnabled),
		logger.String("HttpAddr", viper.GetString("server.addr")),
		logger.Bool("DebugMode", viper.GetBool("app.debug")),
		logger.String("RunMode", viper.GetString("app.env")),
	)

	go func() {
		if err := e.Start(viper.GetString("server.addr")); err != nil && err != http.ErrServerClosed {
			e.Logger.Fatal("shutting down the server", err)
		}
	}()

	shutdown.Wait(time.Second, func() error {
		logger.Info(context.Background(), "exit")
		Shutdown()
		return nil
	})
}

func Shutdown() {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()
	if err := e.Shutdown(ctx); err != nil {
		e.Logger.Fatal(err)
	}
}

func initAlarm(identifyName string) {
	alarmOnce.Do(func() {
		rulesMap := viper.GetStringMap("alarm.rules")
		rules := make([]alert.AlertRule, 0)
		for level, rule := range rulesMap {
			rules = append(rules, alert.AlertRule{
				Level:    level,
				Duration: cast.ToInt(rule.(map[string]interface{})["duration"]),
				Num:      cast.ToInt(rule.(map[string]interface{})["num"]),
			})
		}

		var keyWords = viper.GetStringSlice("alarm.keywords")
		if identifyName != "" {
			if len(keyWords) > 0 {
				keyWords = append([]string{identifyName}, keyWords...)
			} else {
				keyWords = append(keyWords, identifyName)
			}
		}
		logger.GiveAnAlarm(context.Background(), alert.New(alert.ATFeishu).
			SetServiceName(viper.GetString("app.name")).
			SetKeywords(keyWords...).
			SetDingWebhook(viper.GetString("alarm.webhook")).
			SetRules(rules),
		)
	})
}
