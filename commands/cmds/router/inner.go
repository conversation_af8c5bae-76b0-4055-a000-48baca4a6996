// Copyright 2021 funplus Authors. All Rights Reserved.
// @Author: <PERSON><PERSON>
// @Date: 2025/4/7 18:50
package router

import (
	"github.com/labstack/echo/v4"
	"github.com/labstack/echo/v4/middleware"
	"ops-ticket-api/handlers/inner_api"
	mid2 "ops-ticket-api/internal/framework/middleware"
)

func InnerRouter(e *echo.Echo) {
	// 内部服务路由
	inner := e.Group("/inner/api")
	inner.Use(middleware.Recover())

	inner.Use(mid2.InnerSign())

	// 用户信息更新
	inner.POST("/cat/titles", inner_api.GetCatTitles)

}
