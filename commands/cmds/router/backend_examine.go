package router

import (
	"github.com/labstack/echo/v4"
	"ops-ticket-api/handlers/examine"
)

func examineRouter(backendApi *echo.Group) {
	examineApi := backendApi.Group("/examine")
	examineHandle := examine.NewExamineHandle()
	// 质检打分表配置
	examineApi.POST("/tpl/save", examineHandle.TplSave)
	examineApi.POST("/tpl/copy", examineHandle.TplCopy)
	examineApi.POST("/tpl/list", examineHandle.TplList)
	examineApi.POST("/tpl/detail", examineHandle.TplDetail)
	examineApi.POST("/tpl/opts", examineHandle.TplOpts)
	examineApi.POST("/tpl/enable", examineHandle.TplEnable)
	examineApi.POST("/tpl/del", examineHandle.TplDel)

	// 质检任务 task
	examineApi.POST("/task/list", examineHandle.TaskList)
	examineApi.POST("/task/detail", examineHandle.TaskDetail) // 暂时未使用
	examineApi.POST("/task/save", examineHandle.TaskSave)
	examineApi.POST("/task/dsc/filter_count", examineHandle.TaskDscFilterCount)
	examineApi.POST("/task/dsc/filter_part_count", examineHandle.TaskDscPartFilterCount)

	// 质检单 质检
	examineApi.POST("/order/dsc/list", examineHandle.DscOrderList)
	examineApi.POST("/order/dsc/list_export", examineHandle.DscOrderListExport)
	examineApi.POST("/order/dsc/stats", examineHandle.DscOrderStats)
	examineApi.POST("/order/dsc/detail", examineHandle.DscOrderDetail)
	examineApi.POST("/order/dsc/save", examineHandle.DscOrderSave)

	// 质检单 消息通知红点
	examineApi.POST("/order/notice/acceptor", examineHandle.OrderNoticeAcceptor)
	examineApi.POST("/order/notice/list", examineHandle.OrderNoticeList)
}
