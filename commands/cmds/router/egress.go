// Copyright 2021 funplus Authors. All Rights Reserved.
// @Description:
// @Author: Darcy
// @Date: 2022/5/10 15:40

package router

import (
	"net/http"
	"ops-ticket-api/handlers/egress"
	"ops-ticket-api/internal/cst"
	"ops-ticket-api/internal/framework/plugins"

	"github.com/labstack/echo/v4"
	"github.com/labstack/echo/v4/middleware"
	"github.com/spf13/viper"
	"gitlab-ee.funplus.io/ops-tools/compkg/sign"
)

// EgressRouter c端路由
func EgressRouter(e *echo.Echo) {
	// c端消息通知接口,反代
	e.POST("/api/v2/notice", egress.NoticeV2, plugins.Sha256SignAuth())
	// e.POST("/api/v1/notice", egress.Notice, plugins.Sha256SignAuth())
	e.POST("/api/fpx/notice", egress.NoticeFpx, plugins.Sha256SignAuth())
	e.POST("/api/inner/notice", egress.NoticeInner)

	// 实现不通 -- 忽略
	ds := e.Group("/discord")
	ds.POST("/notice/interactions", egress.DsNoticeLog)
	ds.POST("/notice/verify-user", egress.DsNoticeLog)

	e.Use(middleware.CORSWithConfig(middleware.CORSConfig{
		AllowOrigins:     []string{"*"},
		AllowCredentials: true,
		AllowMethods:     []string{http.MethodGet, http.MethodHead, http.MethodPut, http.MethodPatch, http.MethodPost, http.MethodDelete, http.MethodOptions},
	}))

	egressRoute := e.Group("/egress", middleware.JWTWithConfig(middleware.JWTConfig{
		Skipper: func(context echo.Context) bool {
			if e.Debug {
				return true
			}
			if context.Path() == "/egress/health" {
				return true
			}
			return false
		},
		ParseTokenFunc: func(auth string, c echo.Context) (interface{}, error) {
			claims, err := sign.JwtDecode(auth, viper.GetString("auth.ticket_api_key"))
			if err != nil {
				return nil, err
			}
			if err := claims.CheckExpired(); err != nil {
				return nil, err
			}
			return claims, nil
		},
		ContextKey: cst.PlayerClaimsCtx,
	}))
	ticketHandle := egress.NewTicketHandle()
	// front health check
	egressRoute.GET("/health", egress.HealthCheck)
	egressRoute.GET("/discord/vip-link", egress.DiscordVipLink) // 客户连接

	//egressRoute.POST("/cat/map", egress.CategoryMap)                  // 问题分类映射
	//egressRoute.POST("/cat/overload_map", egress.OverloadCategoryMap) // 问题分类映射
	//egressRoute.POST("/cat/opts", egress.CategoryOptions)             // 问题分类
	//
	//egressRoute.POST("/auth/secret", egress.AuthSecretList)                                           // 当前环境所配置秘钥
	//egressRoute.POST("/cat/sub", egress.CategoryChild, plugins.GameTokenAuth())                       // 问题分类
	//egressRoute.POST("/scene/cats", egress.SceneCategory, plugins.GameTokenAuth())                    // 问题分类树 - 场景
	//egressRoute.POST("/cat/info", egress.CategoryInfo, plugins.GameTokenAuth())                       // 三级问题信息
	//egressRoute.POST("/tpl/info", egress.TemplateDetail, plugins.GameTokenAuth())                     // 问题模版信息
	//egressRoute.POST("/process/next", egress.ProcessNext, plugins.GameTokenAuth())                    // 问题模版信息
	//egressRoute.POST("/ticket/statis", egress.TicketStatis) // 客诉情况
	egressRoute.POST("/msg/notice", egress.NoticeWeb, plugins.GameTokenAuth())             // 我的未读消息
	egressRoute.POST("/ticket/mine", ticketHandle.TicketMine, plugins.GameTokenAuth())     // 工单 - 我的工单列表
	egressRoute.POST("/ticket/create", ticketHandle.TicketCreate, plugins.GameTokenAuth()) // 工单 - 创建
	egressRoute.POST("/ticket/detail", ticketHandle.TicketDetail, plugins.GameTokenAuth())
	// 工单 - 详情
	egressRoute.POST("/ticket/rdo/detail", ticketHandle.TicketDetail) // 工单 - 详情
	//egressRoute.POST("/ticket/rdo/detail", ticketHandle.TicketDetail) // 工单 - 详情

	egressRoute.POST("/ticket/replenish", ticketHandle.TicketReplenish, plugins.GameTokenAuth())            // 工单 - 补填
	egressRoute.POST("/ticket/reopen", ticketHandle.TicketReopen, plugins.GameTokenAuth())                  // 工单 - 重开
	egressRoute.POST("/ticket/appraise", ticketHandle.TicketAppraise, plugins.GameTokenAuth())              // 工单 - 评价接口
	egressRoute.POST("/ticket/appraise/feedback", ticketHandle.TkAppraiseFeedback, plugins.GameTokenAuth()) // 工单 - 评价反馈接口
	egressRoute.POST("/ticket/communicate", ticketHandle.TkCommunicate, plugins.GameTokenAuth())            // 工单 - 给客服发消息

	surveyEgressRoute := e.Group("/egress_survey")
	surveyEgressRoute.POST("/dc/config", egress.DcSurveyConfig)     // 调查问卷 - 配置信息
	surveyEgressRoute.POST("/dc/template", egress.DcSurveyTemplate) // 获取问卷模版 - 缺鉴权
	surveyEgressRoute.POST("/dc/submit", egress.DcSurveySubmit)     // 问卷调查提交 - 缺鉴权

	// 会被cs-api v4 直接转发到的新版本接口
	egressRoute.POST("/cat/info", egress.CategoryInfo, plugins.GameTokenAuth())

	// 智能客服对话系统路由
	conversationGroup := egressRoute.Group("/conversation")
	{
		conversationGroup.POST("/update", egress.UpdateConversation, plugins.GameTokenAuth())        // 创建/更新对话
		conversationGroup.POST("/history", egress.GetConversationHistory, plugins.GameTokenAuth())   // 获取对话历史
		conversationGroup.POST("/chat", egress.HandleChat, plugins.GameTokenAuth())                  // 对话交互
		conversationGroup.POST("/continue", egress.GetUnFinishConversation, plugins.GameTokenAuth()) // 获取未完成对话
		conversationGroup.POST("/close", egress.CloseConversation, plugins.GameTokenAuth())          // 关闭对话
	}
}
