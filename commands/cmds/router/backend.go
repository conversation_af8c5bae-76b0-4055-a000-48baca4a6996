// Copyright 2021 funplus Authors. All Rights Reserved.
// @Description:
// @Author: Darcy
// @Date: 2022/5/10 15:40

package router

import (
	"context"
	"ops-ticket-api/handlers/inner"
	"ops-ticket-api/internal/cst"

	"github.com/labstack/echo/v4"
	"github.com/labstack/echo/v4/middleware"
	"github.com/spf13/cast"
	"github.com/spf13/viper"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"gitlab-ee.funplus.io/ops-tools/compkg/sign"
)

// BackendRouter route
func BackendRouter(e *echo.Echo) {
	e.GET("/inner/ticket/ban-export", inner.TicketBanExport) // 飞书下载工单，内部接口
	apiRoute := e.Group("/api", middleware.JWTWithConfig(middleware.JWTConfig{
		Skipper: func(c echo.Context) bool {
			if viper.GetBool("app.skip_auth") || c.Request().Header.Get("x-test") == "lintest" {
				c.Set(cst.AccountInfoCtx, "yiheng.zhang")
				c.Set(cst.AccountNameCtx, "yiheng.zhang")
				c.Set(cst.AccountIsAdminCtx, true)
				return true
			}
			return false
		},
		ParseTokenFunc: func(auth string, c echo.Context) (interface{}, error) {
			claims, err := sign.JwtDecode(auth, viper.GetString("auth.admin_gateway"))
			if err != nil {
				return nil, err
			}
			if err := claims.CheckExpired(); err != nil {
				return nil, err
			}

			c.Set(cst.AccountInfoCtx, cast.ToString(claims.Data["username"]))
			c.Set(cst.AccountNameCtx, cast.ToString(claims.Data["username"]))
			c.Set(cst.AccountIsAdminCtx, cast.ToBool(claims.Data["admin"]))

			c.Request().Header.Set("account", cast.ToString(claims.Data["username"]))
			c.Request().Header.Set("is_admin", cast.ToString(claims.Data["admin"]))

			lang := c.Request().Header.Get("lang")
			if lang == "" {
				lang = viper.GetString("app.lang")
			}
			c.Set(cst.LocalLanguage, lang)
			c.SetRequest(c.Request().WithContext(logger.WithFields(c.Request().Context(),
				logger.Any("username", claims.Data["username"]),
				logger.Any("is_admin", claims.Data["admin"]),
				logger.Any("lang", lang),
			)))
			c.SetRequest(c.Request().WithContext(context.WithValue(c.Request().Context(), cst.AccountInfoCtx, claims.Data["username"])))
			return claims, nil
		},
		ContextKey:     cst.AccountClaimsCtx,
		SuccessHandler: inner.UserInfoSave,
	}))

	// examine - 质检部分接口定义 & 共用 middleware
	examineRouter(apiRoute)

	// 老工单同步authKey和密钥配置接口
	apiRoute.POST("/auth_config/sync", inner.AuthConfigSync)

	// 基础配置接口
	apiRoute.POST("/addons/enum", inner.SysEnum)
	apiRoute.POST("/addons/game_list", inner.GameList) // 个人有权限的游戏列表
	apiRoute.POST("/addons/lang", inner.LanguageList)
	apiRoute.POST("/addons/user_list", inner.UserList)
	apiRoute.POST("/addons/channel", inner.ChannelList)
	apiRoute.POST("/addons/channel_tree", inner.ChannelTreeList)
	apiRoute.POST("/addons/channel_package_id", inner.ChannelPackageIdList)
	apiRoute.POST("/addons/dsc_bot_list", inner.DscBotIdList)         // dsc 项目对应机器人列表
	apiRoute.POST("/addons/line_channel_list", inner.LineChannelList) // line 项目对应频道列表

	// 字段映射
	apiRoute.POST("/dict/opts", inner.DictOpts)
	apiRoute.POST("/dict/list", inner.DictList)
	apiRoute.POST("/dict/info", inner.DictInfo)
	apiRoute.POST("/dict/add", inner.DictAdd)
	apiRoute.POST("/dict/save", inner.DictSave)
	apiRoute.POST("/dict/enable", inner.DictEnable)

	// 工单
	apiRoute.POST("/ticket/is_acceptor", inner.IsAcceptor)
	apiRoute.POST("/ticket/count", inner.TicketCount)                // 工单 - 基础统计
	apiRoute.POST("/ticket/pool", inner.TicketWorkPool)              // 工单 - 列表
	apiRoute.POST("/ticket/export", inner.TicketWorkPoolExport)      // 工单 - 导出
	apiRoute.POST("/ticket/info", inner.TicketInfo)                  // 工单 - 详情
	apiRoute.POST("/ticket/tags", inner.TicketTags)                  // 工单 - 查询绑定标签
	apiRoute.POST("/ticket/count_by_game", inner.TicketsCountByGame) // 工单 - 根据精灵报表条件查询

	// 工单操作
	apiRoute.POST("/ticket/reassign", inner.TicketReassign)               // 工单 - 指派
	apiRoute.POST("/ticket/batch_reassign", inner.TicketBatchReassign)    // 工单 - 批量指派
	apiRoute.POST("/ticket/turn", inner.TicketTurn)                       // 工单 - 流转给他其他人
	apiRoute.POST("/ticket/transfer", inner.TicketTransfer)               // 工单 - 回复/状态流转
	apiRoute.POST("/ticket/batch_transfer", inner.TicketBatchTransfer)    // 工单 - 批量回复/关单/拒单
	apiRoute.POST("/ticket/upgrade", inner.TicketsUpgrade)                // 工单 - 升/降级
	apiRoute.POST("/ticket/remark", inner.TicketsRemark)                  // 工单 - 添加备注
	apiRoute.POST("/ticket/batch_remark", inner.TicketBatchRemark)        // 工单 - 批量备注·
	apiRoute.POST("/ticket/retagging", inner.TicketBindTag)               // 工单 - 绑定标签 - 每次全量覆盖
	apiRoute.POST("/ticket/batch_retagging", inner.TicketBatchBindTag)    // 工单 - 批量打标签
	apiRoute.POST("/ticket/return_pool", inner.TicketReturnPool)          // 工单 - 退回工单池
	apiRoute.POST("/ticket/draft/save", inner.TicketDraftSave)            // 工单草稿保存
	apiRoute.POST("/ticket/draft/info", inner.TicketDraftInfo)            // 工单草稿信息
	apiRoute.POST("/ticket/tag/public", inner.TicketPublicTag)            // 工单 - 公共标签
	apiRoute.POST("/ticket/tag/batch_delete", inner.TicketTagBatchDelete) // 工单 - 批量删除

	// 工单多维数据概览(搜索tab)
	apiRoute.POST("/ticket/tab/save", inner.TicketTabSave)              // 搜索tab
	apiRoute.POST("/ticket/tab/del", inner.TicketTabDel)                // 删除tab
	apiRoute.POST("/ticket/tab/list", inner.TicketTabList)              // 查询tab列表
	apiRoute.POST("/ticket/tab/count", inner.TicketTabCount)            // 所有tab数量统计
	apiRoute.POST("/ticket/tab/edit", inner.TicketTabEdit)              // 编辑tab
	apiRoute.POST("/ticket/tab/update_sort", inner.TicketTabUpdateSort) // tab排序接口

	// 标签（废弃）
	// apiRoute.POST("/tags/opts", inner.TagOpts)
	// apiRoute.POST("/tag_lib/list", inner.TagLibList)
	// apiRoute.POST("/tag_lib/info", inner.TagLibInfo) // 未使用 -- 已废弃
	// apiRoute.POST("/tag_lib/enable", inner.TagLibEnable)
	// apiRoute.POST("/tag_lib/save", inner.TagLibSave) // 标签

	// 新标签库配置(整合工单、DC和Line)
	apiRoute.POST("/new/tags/opts", inner.AllTagOpts)
	apiRoute.POST("/new/tag_lib/list", inner.AllTagLibList)
	apiRoute.POST("/new/tag_lib/info", inner.AllTagLibInfo) // 未使用 -- 已废弃
	apiRoute.POST("/new/tag_lib/enable", inner.AllTagLibEnable)
	apiRoute.POST("/new/tag_lib/save", inner.AllTagLibSave) // 标签

	// 标签配置
	apiRoute.POST("/tag_config/add", inner.TagConfigSave)  // 新增标签配置
	apiRoute.POST("/tag_config/info", inner.TagConfigInfo) // 标签配置信息
	apiRoute.POST("/tag_config/list", inner.TagConfigList) // 标签配置列表
	apiRoute.POST("/tag_config/del", inner.TagConfigDel)   // 删除标签配置
	apiRoute.POST("/tag_config/edit", inner.TagConfigEdit) // 编辑标签配置

	// Discord和Line的问题分类接口
	apiRoute.POST("/channel/cat/tree", inner.CatChannelTree)
	apiRoute.POST("/channel/cat/add", inner.CatChannelAdd)
	apiRoute.POST("/channel/cat/save", inner.CatChannelSave)
	apiRoute.POST("/channel/cat/del", inner.CatChannelDel)

	// 团队分单(废弃)
	// apiRoute.POST("/group/list", inner.GroupList) // 接口 - list
	// apiRoute.POST("/group/save", inner.GroupSave) // 保存 -
	// apiRoute.POST("/group/info", inner.GroupInfo) // 详情 -
	// apiRoute.POST("/group/del", inner.GroupDel)   // 删除 -

	// 团队配置
	apiRoute.POST("/team_config/add", inner.AddTeamConfig)   // 新增
	apiRoute.POST("/team_config/edit", inner.EditTeamConfig) // 修改
	apiRoute.POST("/team_config/list", inner.TeamConfigList) // 查询
	apiRoute.POST("/team_config/del", inner.DelTeamConfig)   // 删除

	// Discord机器人配置
	apiRoute.POST("/dsc_bot_config/add", inner.AddDCSBotConfig)                                     // 新增
	apiRoute.POST("/dsc_bot_config/check", inner.CheckDCSBotConfig)                                 // 验证
	apiRoute.POST("/dsc_bot_config/list", inner.DCSBotConfigList)                                   // 查询
	apiRoute.POST("/dsc_bot_config/update_welcome_message", inner.UpdateDCSBotConfigWelcomeMessage) // 更新DC机器人配置欢迎消息

	// 客服(用户)分单配置(新逻辑，与团队解耦)
	apiRoute.POST("/user_assign_ticket/list", inner.UserAssignTicketList) // 查询 -
	apiRoute.POST("/user_assign_ticket/add", inner.UserAssignTicketAdd)   // 新增 -
	apiRoute.POST("/user_assign_ticket/edit", inner.UserAssignTicketEdit) // 修改 -
	apiRoute.POST("/user_assign_ticket/info", inner.UserAssignTicketInfo) // 详情 -
	apiRoute.POST("/user_assign_ticket/del", inner.UserAssignTicketDel)   // 删除 -

	// 回复引用模板
	apiRoute.POST("/module/list", inner.ModuleList)    // 接口 - list
	apiRoute.POST("/module/save", inner.ModuleSave)    // 新增 -
	apiRoute.POST("/module/edit", inner.ModuleEdit)    // 编辑 -
	apiRoute.POST("/module/delete", inner.ModuleDel)   // 删除 -
	apiRoute.POST("/module/options", inner.ModuleOpts) // 下拉列表 -

	// 回复模版分类配置
	apiRoute.POST("/module/cat/tree", inner.CatModuleTree)
	apiRoute.POST("/module/cat/add", inner.CatModuleAdd)
	apiRoute.POST("/module/cat/save", inner.CatModuleSave)
	apiRoute.POST("/module/cat/del", inner.CatModuleDel)

	// 自动回复模版
	apiRoute.POST("/reply_tpl/opts", inner.ReplyTplOpts)
	apiRoute.POST("/reply_tpl/list", inner.ReplyTplList)
	apiRoute.POST("/reply_tpl/info", inner.ReplyTplInfo)
	apiRoute.POST("/reply_tpl/add", inner.ReplyTplAdd)
	apiRoute.POST("/reply_tpl/save", inner.ReplyTplSave)
	apiRoute.POST("/reply_tpl/enable", inner.ReplyTplEnable)

	// ai功能
	apiRoute.POST("/ai/summary", inner.AISummary)    // ai - 总结
	apiRoute.POST("/ai/polish", inner.AIPolish)      // ai - 润色 (带参数- 更友好，更简洁，更官方，更礼貌)
	apiRoute.POST("/ai/pre_reply", inner.AIPreReply) // ai - 预回复
	apiRoute.POST("/ai/faq", inner.AIFaq)            // ai - 生成faq(总结问答数据)

	// 调查问卷配置
	apiRoute.POST("/survey_config/edit", inner.SurveyConfigEdit)                           // 修改
	apiRoute.POST("/survey_config/list", inner.SurveyConfigList)                           // 查询
	apiRoute.POST("/survey_config/info", inner.SurveyConfigInfo)                           // 详情
	apiRoute.POST("/survey_config/enable", inner.SurveyConfigEnable)                       // 禁用启用
	apiRoute.POST("/survey_config/gen_links", inner.SurveyGenLinks)                        // 生成链接
	apiRoute.POST("/survey/satisfaction/date/stats", inner.SatisfactionStats)              // dc调查问卷满意度报表
	apiRoute.POST("/survey/satisfaction/date/stats_export", inner.SatisfactionStatsExport) // dc调查问卷满意度报表导出

	// 精分道具查询
	dpUserInfoH := inner.NewDataPlatUserInfoHandler()
	apiRoute.POST("/data_plat/game_item/list", dpUserInfoH.GameItemList)            // 精分数据 - 游戏item - 道具ID 列表
	apiRoute.POST("/data_plat/game_item/batch_save", dpUserInfoH.GameItemBatchSave) // 精分数据 - 游戏item - 道具 ID 保存
	apiRoute.POST("/data_plat/game_item/opts", dpUserInfoH.GameItemOpts)            // 精分数据 - 游戏item - 道具ID opts
	apiRoute.POST("/data_plat/user/gold_info", dpUserInfoH.GoldInfo)                // 精分数据 - 游戏玩家 - 金币查询
	apiRoute.POST("/data_plat/user/item_info", dpUserInfoH.ItemInfo)                // 精分数据 - 游戏玩家 - 道具查询
	apiRoute.POST("/data_plat/user/pay_info", dpUserInfoH.PayInfo)                  // 精分数据 - 游戏玩家 - 支付信息查询
	apiRoute.POST("/data_plat/user/login_info", dpUserInfoH.LoginInfo)              // 精分数据 - 游戏玩家 - 登录信息查询

	// 超时提醒模版
	apiRoute.POST("/overtime_tpl/opts", inner.OvertimeTplOpts)     // 老工单下拉选
	apiRoute.POST("/overtime_tpl/list", inner.OvertimeTplList)     // 列表 -
	apiRoute.POST("/overtime_tpl/add", inner.OvertimeTplAdd)       // 新增 -
	apiRoute.POST("/overtime_tpl/edit", inner.OvertimeTplEdit)     // 编辑 -
	apiRoute.POST("/overtime_tpl/delete", inner.OvertimeTplDel)    // 删除 -
	apiRoute.POST("/overtime_tpl/enable", inner.OvertimeTplEnable) // 启用/禁用 -

	// discord 功能集成
	dscRoute := apiRoute.Group("/dsc")
	dscHandle := inner.NewDscHandle()

	dscRoute.POST("/user/pool", dscHandle.UserList)                                         // 用户列表
	dscRoute.POST("/user/pool/export", dscHandle.UserListExport)                            // 用户列表导出
	dscRoute.POST("/user/stats", dscHandle.UserStats)                                       // 用户数据概览
	dscRoute.POST("/user/remark/save", dscHandle.UserRemarkSave)                            // 用户添加备注
	dscRoute.POST("/reply_status/rectify", dscHandle.ReplyStatusRectify)                    // 客服对玩家消息回复状态修正
	dscRoute.POST("/channel/dialog", dscHandle.ChannelDialog)                               // 渠道 - 对话历史
	dscRoute.POST("/channel/dialog_fresh", dscHandle.DialogFresh)                           // 渠道 - 刷新对话
	dscRoute.POST("/channel/message_create", dscHandle.MessageCreate)                       // 渠道 - 发送消息
	dscRoute.POST("/channel/batch/message_create", dscHandle.MessageBatchCreate)            // 渠道 - 批量发送消息(文本和表情符号)
	dscRoute.POST("/channel/batch/send_file", dscHandle.BatchSendFile)                      // 渠道 - 批量发送文件(图片/视频)
	dscRoute.POST("/channel/batch/async_message_create", dscHandle.AsyncMessageBatchCreate) // 渠道 - 批量异步发送消息(文本和表情符号)
	dscRoute.POST("/message/task/list", dscHandle.MessageTaskList)                          // 批量私信 - 列表
	dscRoute.POST("/message/task/list/export", dscHandle.MessageTaskListExport)             // 批量私信 - 列表导出
	dscRoute.POST("/message/task/detail/export", dscHandle.MessageTaskDetailExport)         // 批量私信 - 下载明细
	dscRoute.POST("/channel/message_edit", dscHandle.MessageEdit)                           // 渠道 - 编辑消息
	dscRoute.POST("/channel/send_file", dscHandle.SendFile)                                 // 渠道 - 发送文件
	dscRoute.POST("/channel/message_reply", dscHandle.MessageReply)                         // 渠道 - 回复消息
	dscRoute.POST("/proxy/resource", dscHandle.ProxyResource)                               // discord - 资源代理

	// 玩家画像
	dscRoute.POST("/portrait/info", inner.DiscordPortraitInfo)      // 玩家画像信息
	dscRoute.POST("/portrait/edit", inner.EditDiscordPortrait)      // 编辑玩家画像信息
	dscRoute.POST("/batch-tag", inner.DiscordBatchTag)              // 给discord玩家批量打标签
	dscRoute.POST("/tag/public", inner.DiscordPublicTag)            // 公共标签
	dscRoute.POST("/tag/batch_delete", inner.DiscordTagBatchDelete) // 批量删除
	// 玩家关系维护配置
	// dscRoute.POST("/maintain/config/save", inner.MaintainConfigSave)              // 新增玩家关系维护配置
	dscRoute.POST("/maintain/config/edit", inner.MaintainConfigEdit)              // 修改玩家关系维护配置
	dscRoute.POST("/maintain/config/del", inner.MaintainConfigDel)                // 删除玩家关系维护配置
	dscRoute.POST("/maintain/config/list", inner.MaintainConfigList)              // 查询玩家关系维护配置
	dscRoute.POST("/maintain/config/list_export", inner.MaintainConfigListExport) // 导出玩家关系维护配置
	dscRoute.POST("/player/account", inner.DiscordPlayerAccounts)                 // 获取下拉数据(dsc_user_id)
	dscRoute.POST("/player/uid", inner.DiscordPlayerUidList)                      // 获取下拉数据(uid)
	// 报表
	dscRoute.POST("/interaction/stats", inner.InteractStats)                        // 玩家discord交互数据报表
	dscRoute.POST("/interaction/detail/export", inner.InteractDetailExport)         // 玩家discord交互明细导出
	dscRoute.POST("/message/count/date/stats", inner.MessageCountDateStats)         // 每天的信息量报表
	dscRoute.POST("/message/count/operator/stats", inner.MessageCountOperatorStats) // 每个处理人的信息量报表
	dscRoute.POST("/message/count/detail/export", inner.MessageCountDetailExport)   // 信息量明细导出
	dscRoute.POST("/replytime/detail/stats", inner.ReplyTimeDetailStats)            // 客服回复时间数据报表
	dscRoute.POST("/replytime/detail/export", inner.ReplyTimeDetailExport)          // 客服回复时间明细导出

	// 沟通记录 (弃用)
	// dscRoute.POST("/commu/save", inner.DiscordCommuSave)              // 创建discord沟通记录
	// dscRoute.POST("/commu/edit", inner.DiscordCommuEdit)              // 编辑discord沟通记录
	// dscRoute.POST("/commu/list", inner.DiscordCommuList)              // 查询discord沟通记录
	// dscRoute.POST("/commu/list/export", inner.DiscordCommuListExport) // 导出discord沟通记录

	// 新沟通记录
	dscRoute.POST("/new/commu/save", inner.DiscordNewCommuSave)              // 创建discord沟通记录
	dscRoute.POST("/new/commu/edit", inner.DiscordNewCommuEdit)              // 编辑discord沟通记录
	dscRoute.POST("/new/commu/list", inner.DiscordNewCommuList)              // 查询discord沟通记录
	dscRoute.POST("/new/commu/list/export", inner.DiscordNewCommuListExport) // 导出discord沟通记录
	// 标签（弃用，已整合到标签配置）
	dscRoute.POST("/tag/save", inner.DiscordTagSave) // 创建disqcord标签
	dscRoute.POST("/tag/list", inner.DiscordTagList) // 查询discord标签
	// 多维数据概览(搜索tab)
	dscRoute.POST("/tab/save", inner.DiscordTabSave)          // 创建discord 搜索tab
	dscRoute.POST("/tab/del", inner.DiscordTabDel)            // 删除discord 搜索tab
	dscRoute.POST("/tab/list", inner.DiscordTabList)          // 查询discord 搜索tab
	dscRoute.POST("/tab/count", inner.DiscordTabCount)        // 所有tab数量统计
	dscRoute.POST("/tab/edit", inner.DiscordTabEdit)          // 编辑tab
	dscRoute.POST("/tab/update_sort", inner.DscTabUpdateSort) // 编辑tab

	// line 功能集成
	lineRoute := apiRoute.Group("/line")
	lineHandle := inner.NewLineHandler()
	lineRoute.POST("/user/pool", lineHandle.UserList)                       // 用户列表
	lineRoute.POST("/user/pool/export", lineHandle.UserListExport)          // 用户列表导出
	lineRoute.POST("/user/stats", lineHandle.UserStats)                     // 用户数据概览
	lineRoute.POST("/user/remark/save", lineHandle.UserRemarkSave)          // 用户添加备注
	lineRoute.POST("/channel/send_message", lineHandle.SendTextMessage)     // 渠道 - 发送文本消息和表情符号
	lineRoute.POST("/channel/send_file", lineHandle.SendFile)               // 渠道 - 发送文件
	lineRoute.POST("/channel/dialogue", lineHandle.DialogueHistory)         // 渠道 - 历史聊天
	lineRoute.POST("/channel/dialogue_refresh", lineHandle.DialogueRefresh) // 渠道 - 刷新获取最新的消息

	// 玩家画像信息
	lineRoute.POST("/portrait/info", inner.LinePortraitInfo)              // 玩家画像信息
	lineRoute.POST("/portrait/edit_tag", inner.LinePortraitEditTag)       // 编辑玩家画像标签信息
	lineRoute.POST("/portrait/edit_basic", inner.LinePortraitEditBasic)   // 编辑玩家画像基础信息
	lineRoute.POST("/portrait/edit_remark", inner.LinePortraitEditRemark) // 编辑玩家画像备注信息
	// 玩家关系维护配置
	lineRoute.POST("/maintain/config/edit", inner.LineMaintainConfigEdit)              // 修改玩家关系维护配置
	lineRoute.POST("/maintain/config/del", inner.LineMaintainConfigDel)                // 删除玩家关系维护配置
	lineRoute.POST("/maintain/config/list", inner.LineMaintainConfigList)              // 查询玩家关系维护配置
	lineRoute.POST("/maintain/config/list_export", inner.LineMaintainConfigListExport) // 导出玩家关系维护配置
	// 玩家沟通记录任务相关
	lineRoute.POST("/commu/save", inner.LineCommuSave)              // 创建line沟通记录
	lineRoute.POST("/commu/edit", inner.LineCommuEdit)              // 编辑line沟通记录
	lineRoute.POST("/commu/list", inner.LineCommuList)              // 查询line沟通记录
	lineRoute.POST("/commu/list/export", inner.LineCommuListExport) // 导出line沟通记录

	lineRoute.POST("/player/account", inner.LinePlayerAccounts) // 获取下拉数据(dsc_user_id)
	lineRoute.POST("/player/uid", inner.LinePlayerUidList)      // 获取下拉数据(uid)

	// 多维数据概览(搜索tab)
	lineRoute.POST("/tab/save", inner.LineTabSave)              // 创建Line 搜索tab
	lineRoute.POST("/tab/del", inner.LineTabDel)                // 删除Line 搜索tab
	lineRoute.POST("/tab/list", inner.LineTabList)              // 查询Line 搜索tab
	lineRoute.POST("/tab/count", inner.LineTabCount)            // 所有tab数量统计
	lineRoute.POST("/tab/edit", inner.LineTabEdit)              // 编辑tab
	lineRoute.POST("/tab/update_sort", inner.LineTabUpdateSort) // 排序tab
	// 问题分类
	apiRoute.POST("/cat/release", inner.CategoryRelease)
	apiRoute.POST("/cat/opts", inner.CatOpts) // 获取问题级联类型

	backendv2 := apiRoute.Group("/v2")
	{
		// 工单知识库
		backendv2.POST("/question/save", inner.QuestionSave)                // 工单知识库-保存
		backendv2.POST("/question/list", inner.QuestionList)                // 工单知识库-列表
		backendv2.POST("/question/list/export", inner.QuestionExport)       // 工单知识库-下载
		backendv2.POST("/question/del", inner.QuestionDel)                  // 工单知识库-删除
		backendv2.POST("/question/batch_import", inner.QuestionBatchImport) // 工单知识库-批量上传

		backendv2.POST("/question/training", inner.QuestionTraining) // 工单知识库-训练
		backendv2.POST("/question/trainlog", inner.QuestionTrainLog) // 工单知识库-训练结果

		// url -excel
		// index -pc  根据excel文件去向量存储
		// backendv2.POST("/elfin_vector_db/save", inner.QuestionList) // 工单知识库-列表

		// 工单分级策略
		backendv2.POST("/strategy/save", inner.StrategySave)     // 工单分级策略-保存
		backendv2.POST("/strategy/del", inner.StrategyDel)       // 工单分级策略-删除
		backendv2.POST("/strategy/enable", inner.StrategyEnable) // 工单分级策略-禁/启用
		backendv2.POST("/strategy/list", inner.StrategyList)     // 工单分级策略-列表
	}
}
