// Copyright 2021 funplus Authors. All Rights Reserved.
// @Description:
// @Author: Darcy
// @Date: 2022/3/7 5:36 PM

package commands

import (
	"github.com/spf13/cobra"
)

// Service service
func Service(name string, f func(c *cobra.Command, args []string)) *cobra.Command {
	return &cobra.Command{
		Use:   name,
		Short: `http service`,
		Run: func(c *cobra.Command, args []string) {
			f(c, args)
		},
	}
}
