package stats

type OperatorDateCountDetail struct {
	// 交互日期
	InteractDate string `json:"interact_date"`
	// 处理人
	Operator string `json:"operator"`
	// 交互数
	OperatorDateCount int64 `json:"operator_date_count"`
}

type DateCountDetail struct {
	// 交互日期
	InteractDate string `json:"interact_date"`
	// 交互数
	DateCount int64 `json:"date_count"`
}

type OperatorCountDetail struct {
	// 处理人
	Operator string `json:"operator"`
	// 交互数
	OperatorCount int64 `json:"operator_count"`
}

type DiscordPlayerInteractStatsResp struct {
	Data []map[string]interface{} `json:"data"`
}

type DiscordPlayerSatisfactionStatsResp struct {
	Data []map[string]interface{} `json:"data"`
}

type SurveyDateRatingCount struct {
	// 评价日期
	EvaluationDate string `json:"evaluation_date"`
	// 评分
	Rating int64 `json:"rating"`
	// 评价数
	DateCount int64 `json:"date_count"`
}

type SurveyAccountRatingCount struct {
	// 处理人
	Operator string `json:"operator"`
	// 评分
	Rating int64 `json:"rating"`
	// 评价数
	DateCount int64 `json:"date_count"`
}

type SurveyGameRatingCount struct {
	// 游戏
	Project string `json:"project"`
	// 评分
	Rating int64 `json:"rating"`
	// 评价数
	DateCount int64 `json:"date_count"`
}
