version: '3.8'

services:
  mysql:
    image: mysql:8.0
    container_name: ops-ticket-mysql
    environment:
      MYSQL_ROOT_PASSWORD: mysqluser
      MYSQL_DATABASE: fp_ops_ticket_new
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
    command: --default-authentication-plugin=mysql_native_password

  redis:
    image: redis:7-alpine
    container_name: ops-ticket-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

  elasticsearch:
    image: elasticsearch:7.17.0
    container_name: ops-ticket-elasticsearch
    environment:
      - discovery.type=single-node
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
      - xpack.security.enabled=false
    ports:
      - "9200:9200"
    volumes:
      - es_data:/usr/share/elasticsearch/data

volumes:
  mysql_data:
  redis_data:
  es_data:
