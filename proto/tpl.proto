syntax = "proto3";

package pb;
option go_package = ".;pb";

// TplListReq 模板配置列表-请求参数
message TplListReq {
  // 模版名称
  string tpl = 1;
  // 页码
  uint32 page = 2;
  // 页大小
  uint32 page_size = 3;
}

// TplListResp 模板配置列表-响应结果
message TplListResp {
  message TplInfo {
    uint32 id = 1;          // 模版ID
    string tpl = 2;         // 模版名称
    string updated_at = 3;  // 更新时间
    string op = 4;          // 更新用户
    bool enable = 5;        // 状态
  }
  uint32 current_page = 1;
  uint32 per_page = 2;
  uint32 total = 3;
  repeated TplInfo data = 4;
}

// TplOptsResp 模板配置筛选列表-响应结果
message TplOptsResp {
  message Opts {
    uint32 id = 1;   // 模版ID
    string tpl = 2;  // 模版名称
  }
  repeated Opts list = 2;
}

// GroupAddReq 添加模板配置-请求参数
message TplAddReq {
  // 模版名称 @gotags: validate:"required"
  string tpl = 1;
  // 模版表单
  string fields = 2;
  // project 所属项目
  string project = 3;
}

// ReplyTplAddReq 添加回复模板配置-请求参数
message ReplyTplAddReq {
  // 模版名称 @gotags: validate:"required"
  string tpl = 1;
  // project 所属项目
  string project = 2;
  // 回复模版
  map<string,string> reply_content = 3;
}

// TplId 模板id 请求参数
message TplIdReq {
  // 模版ID @gotags: validate:"required"
  uint32 tpl_id = 1;
}

// TplInfoResp 模板配置信息
message TplInfoResp {
  uint32 tpl_id = 1;    // 模版ID
  string tpl = 2;       // 模版名称
  string fields = 3;    // 模版表单
  string category = 4;  // 类别信息
  map<string,string> reply_content = 5;  // 回复模版
}

// GroupEditReq 修改技能组请求参数
message TplSaveReq {
  // 模版ID @gotags: validate:"required"
  uint32 tpl_id = 1;
  // 模版名称 @gotags: validate:"required"
  string tpl = 2;
  // 模版表单
  string fields = 3;
}

// ReplyTplSaveReq 添加回复模板配置-请求参数
message ReplyTplSaveReq {
  // 模版名称 @gotags: validate:"required"
  uint32 tpl_id = 1;
  // project 所属项目
  string tpl = 2;
  // 回复模版
  map<string,string> reply_content = 3;
}
