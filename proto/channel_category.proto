syntax = "proto3";

package pb;
option go_package = ".;pb";



message CatProjectReq {
  // 项目 @gotags: validate:"required"
  string project = 1;
  // 类别:line or discord @gotags: validate:"required,oneof=1 2"
  uint32 cat_type = 2;
}


// CatOptsResp 问题分类配置(下拉级联多选)列表
message ChannelCatTreeResp {
  message Cat {
    uint32 id = 1;     // 类别ID
    string label = 2;  // 类别名称
    uint32 level = 3;
    // 子结构 @gotags: json:"children,omitempty"
    repeated Cat children = 7;
  }
  repeated Cat data = 1;
}

// CatItems cat item
message ChannelCatItems {
  uint32 cat_id = 1;
  uint32 one_level = 2;
  uint32 second_level = 3;
  uint32 level = 4;
  string category = 6;
}

// CatProbAddReq 添加分类 请求参数
message ChannelCatAddReq {
  // 项目 @gotags: validate:"required"
  string project = 1;
  // 分类的等级 @gotags: validate:"required,oneof=1 2 3"
  uint32 cat_level = 2;
  // 分类名称 @gotags: validate:"required"
  string category = 3;
  // 一级分类 @gotags: validate:"required_if=CatLevel 2"
  uint32 one_level = 4;
  // 二级分类 @gotags: validate:"required_if=CatLevel 3"
  uint32 second_level = 5;
  // 类别:line or discord @gotags: validate:"required,oneof=1 2"
  uint32 cat_type = 6;
}

// ChannelCatSaveReq 修改分类 请求参数
message ChannelCatSaveReq {
  // 分类ID @gotags: validate:"required"
  uint32 cat_id = 1;
  // 分类名称 @gotags: validate:"required"
  string category = 2;
  // 分类的等级 @gotags: validate:"required,oneof=1 2 3"
  uint32 cat_level = 7;
}

// CatIdReq 类别Id 请求参数
message ChannelCatIdReq {
  // 分类ID @gotags: validate:"required"
  uint32 cat_id = 1;
}

