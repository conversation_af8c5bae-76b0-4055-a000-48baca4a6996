syntax = "proto3";

package pb;
option go_package = ".;pb";

message TagId {
    // 标签ID @gotags: validate:"required"
    uint32 tag_id = 1;
}


// TagReq 添加标签请求参数
message TagAddReq {
    // 标签组ID @gotags: validate:"required"
    uint32 lib_id = 1;
    // 级别
    uint32 level = 2;
    // 父级ID
    uint32 pid = 3;
    // 标签名称 @gotags: validate:"required"
    string tag_name = 4;
}

message TagNode {
    string name = 1;
    repeated TagNode children = 2;
}

message TagGroups {
    repeated TagGroup tags = 1;
}

message TagGroup {
    string level_one = 1;
    string level_two = 2;
    string level_three = 3;
}