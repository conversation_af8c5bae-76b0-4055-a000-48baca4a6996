syntax = "proto3";

package pb;
option go_package = ".;pb";

import "google/api/annotations.proto";
import "common.proto";
import "survey_enum.proto";

// survey Dc调查问卷 - 接口 API
service SurveyApi {
    // survey 调查问卷 - 列表
    rpc SurveyConfigList(SurveyListReq) returns (SurveyListResp) {
        option (google.api.http) = {
            post: "/api/survey_config/list"
            body: "*"
        };
    }
    // survey 调查问卷 - 修改
    rpc SurveyConfigEdit(SurveyEditReq) returns (Empty) {
        option (google.api.http) = {
            post: "/api/survey_config/edit"
            body: "*"
        };
    }
    // survey 调查问卷 - 详情
    rpc SurveyConfigInfo(SurveyInfoReq) returns (SurveyInfoResp) {
        option (google.api.http) = {
            post: "/api/survey_config/info"
            body: "*"
        };
    }
    // survey 调查问卷 - enable
    rpc SurveyConfigEnable(EnableReq) returns (Empty) {
        option (google.api.http) = {
            post: "/api/survey_config/enable"
            body: "*"
        };
    }
    // survey 调查问卷 - 生成链接
    rpc SurveyGenLink(SurveyGenLinkReq) returns (SurveyGenLinkResp) {
        option (google.api.http) = {
            post: "/api/survey_config/gen_links"
            body: "*"
        };
    }


    // survey 报表
    rpc SurveyStats(DiscordPlayerSatisfactionStatsReq) returns (Empty) {
        option (google.api.http) = {
            post: "/api/survey/satisfaction/date/stats"
            body: "*"
        };
    }
    // survey 报表 - 导出
    rpc SurveyStatsExport(DiscordPlayerSatisfactionStatsReq) returns (Empty) {
        option (google.api.http) = {
            post: "/api/survey/satisfaction/date/stats_export"
            body: "*"
        };
    }

    // C端接口-转发 - 获取模板详情
    rpc SurveyTemplate(SurveyTemplateReq) returns (SurveyTemplateResp) {
        option (google.api.http) = {
            post: "/egress_survey/dc/template"
            body: "*"
        };
    }
    // C端接口-转发 - 问卷提交
    rpc SurveySubmit(SurveySubmitReq) returns (Empty) {
        option (google.api.http) = {
            post: "/egress_survey/dc/submit"
            body: "*"
        };
    }
}

// SurveyListReq 问卷调查配置列表-请求参数
message SurveyListReq {
    // 页码
    uint32 page = 1;
    // 页大小
    uint32 page_size = 2;
}

// SurveyListResp 问卷调查配置列表-响应结果
message SurveyListResp {
    message SurveyInfo {
        uint64 id = 1;          // 模版ID
        string project = 2;     // 游戏名称
        string updated_at = 3;  // 更新时间
        string op = 4;          // 更新用户
        bool enable = 5;        // 状态
        bool can_gen_link = 6;  // 是否可以生成链接
    }
    uint32 current_page = 1;
    uint32 per_page = 2;
    uint32 total = 3;
    repeated SurveyInfo data = 4;
}

// SurveyInfoReq 调查问卷配置详情-请求参数
message SurveyInfoReq {
    // 问卷id @gotags: validate:"required"
    uint64 id = 1;
}

// SurveyInfoResp 调查问卷配置详情-响应参数
message SurveyInfoResp {
    SurveyEditReq data = 1;
}

message DscSurveyPlayerDetail {
    // dsc priv channel id
    string priv_channel_id = 1;
    // app id
    string app_id = 2;
    // dsc user id
    string dsc_user_id = 3;
    // dsc nick name
    string dsc_nick_name = 4;
    // uid
    uint64 uid = 6;
    // account id
    string account_id = 7;
    // lang
    string lang = 8;
    // maintainer - 维护人
    string maintainer = 9;
    // 经办人
    repeated string processors = 10;
    // 处理人
    string last_reply_service = 11;
}
// SurveyLinkParamDf 问卷调查链接参数
message SurveyLinkParamDf {
    message Attrs {
        // 生成问卷时 - 维护人
        string maintainer = 2;
        // 生成问卷时 - 最近处理人
        string last_reply_service = 3;
        // 生成问卷时 - 经办人
        repeated string processors = 4;
        // discord 账号 id
        string dsc_user_id = 5;
        // discord 账号昵称
        string dsc_user_name = 6;
        // uid
        uint64  uid = 7;
        // fpid
        string account_id = 8;
        // priv channel id
        string dsc_channel_id = 9;
        // dsc_bot_id
        string dsc_bot_id = 10;
    }
    uint64 survey_id = 1;
    string project = 2;
    string lang = 3;
    int64 uid = 4;
    string account = 6;
    // 当前此刻的锁定信息
    Attrs moment_attrs = 7;
}

// SurveyEditResp 调查问卷配置修改-请求参数
message SurveyEditReq {
    // 问卷id @gotags: validate:"required,gt=0"
    uint64 id = 1;
    // 游戏 @gotags: validate:"required"
    string project = 2;
    // 推送周期 @gotags: validate:"required,gt=0"
    SurveyPushCycle push_cycle = 3;
    // 推送时间-星期 @gotags: validate:"required"
    uint64 push_week = 4;
    // 推送时间-小时
    uint64 push_time = 5;
    // 推送时间-前端传递 @gotags: validate:"required"
    string push_time_web = 13;
    // 生效时间 @gotags: validate:"required"
    string effective_time = 6;
    // 有效期 @gotags: validate:"required,gt=0"
    SurveyEffective expire_time = 7;
    // 问卷标题 多语言
    map<string, string> survey_titles = 8;
    // 推送文案 多语言
    map<string, string> push_contents = 9;
    // 产品题 多语言
    map<string, string> product_questions = 10;
    // 服务题 多语言
    map<string, string> service_questions = 11;
    // 填写理由 多语言
    map<string, string> reasons = 12;
}

// SurveyEgressConfigResp 问卷调查配置-响应参数
message SurveyEgressConfigResp{
    message EnumLang {
        string code = 1;
        string name = 2;
    }
    repeated EnumLang langs = 1;
}
// SurveySubmitReq 调查问卷配置修改-请求参数
message SurveySubmitReq {
    // 唯一标识 token @gotags: validate:"required"
    string survey_token = 1;
    // 当前时间戳 毫秒 ts_rank
    int64 ts_rank = 2;
    // 语言
    string language = 3;
    // uid - 选填
    int64 uid = 6;
    // 产品题-评星
    int32 product_rating = 7;
    // 产品题-回复
    string product_answer = 8;
    // 服务题-评星
    int32 service_rating = 9;
    // 服务题-回复
    string service_answer = 10;
}

// SurveyDetailReq 调查问卷模版-请求参数
message SurveyTemplateReq {
    // 问卷Token @gotags: validate:"required"
    string survey_token = 1;
    // 语言
    string lang = 4; // 语言为空则返回全部语种模版
    // 当前时间戳 毫秒 ts_rank
    int64 ts_rank = 5;
}

// SurveyTemplateResp 调查问卷模版-响应参数
message SurveyTemplateResp {
    // 问卷配置id @gotags: validate:"required"
    uint64 survey_id = 1;
    // 问卷 token 标识 ：
    string survey_token = 2;
    // 游戏
    string project = 3;
    // 语言-为空时展示语言下拉框
    string lang = 4;
    // 是否展示产品题
    bool is_show_product = 5;
    // 是否展示服务题
    bool is_show_service = 6;
    // 问卷标题 多语言
    string survey_titles = 7;
    // 推送文案 多语言
    string push_contents = 8;
    // 产品题 多语言
    string product_questions = 9;
    // 服务题 多语言
    string service_questions = 10;
    // 填写理由 多语言
    string reasons = 11;
    // 是否可以填写
    bool can_submit = 12;
    // 是否需要填写 uid
    bool need_uid = 13;
    //    // 免责声明 多语言
    //    string disclaimer = 12;
}

// SurveyGenLinkReq 问卷调查生成链接-请求参数
message SurveyGenLinkReq {
    // 项目名称 @gotags: validate:"required"
    string project = 1;
    int64 uid = 2;
    string lang = 3;
    // 问卷ID @gotags: validate:"required,gt=0"
    int64 survey_id = 4;
    // 是否公开 浏览器打开 为 true
    bool is_public = 5;
    // 对应 dsc_user_id
    string dsc_user_id = 6;
    // 有效的截止日期
    int64 expire_at = 7;
    // 客服账号 - 只有对外公开需要携带客服账号是才需要
    string account_name = 8;
    // 生成批次 ID
    uint64 batch_id = 9;
}

// SurveyGenLinkResp 问卷调查生成链接-响应参数
message SurveyGenLinkResp {
    string link = 1;
}

message DiscordPlayerSatisfactionStatsReq {
    // 游戏项目
    repeated string project = 1;
    // 日期
    repeated string date = 2;
    // 评价对象
    repeated SurveyQstType evaluation_target = 3;
    // 处理人
    repeated string operator = 4;
    // 维护人
    repeated string maintainer = 5;
    // 报表维度 @gotags: validate:"required,oneof=1 2 3"
    SurveyStatType stat_type = 6;
}
message DiscordPlayerSatisfactionStatsResp {
    message Stats {
        // 日期类型：demo:
        string aa = 1;
    }
    repeated Stats data = 1;
}