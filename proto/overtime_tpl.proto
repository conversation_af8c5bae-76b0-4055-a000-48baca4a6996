syntax = "proto3";

package pb;
option go_package = ".;pb";


// OverTimeTplAddReq 新增模板
message OverTimeTplAddReq {
  // @gotags: validate:"required"
  string tpl_name = 1;
  // @gotags: validate:"min=1"
  repeated string game_project = 2;
  // 预提醒时间
  uint32 remind_time = 3;
  // @gotags: validate:"required"
  map<string,string> content = 4;
}

// OverTimeTplListReq 模板查询请求参数
message OverTimeTplListReq {
  // 模板名称,支持模糊搜索
  string tpl_name = 1;
  // 游戏, 支持多选
  repeated string game_project = 2;
  uint32  page = 3;
  uint32  page_size = 4;
}

// OverTimeTplListResp 模版列表响应
message OverTimeTplListResp {
  message TplInfo{
    // 模板id
    uint64 id = 1;
    // 模板名称
    string tpl_name = 2;
    // 关联游戏
    repeated string game_project = 4;
    // 操作人
    string operator = 5;
    // 状态 1启用 0禁用
    uint32 enable = 6;
    // 操作时间
    string update_time = 7;
    // 超时时间
    uint32 remind_time = 8;
    // 模版文案
    map<string,string> tpl_content = 9;  // 回复模版
  }

  uint32 current_page = 1;
  uint32 per_page = 2;
  uint32 total = 3;
  repeated TplInfo data = 4;
}

// OverTimeTplEditReq 编辑模板
message OverTimeTplEditReq {
  // 模板id @gotags: validate:"required"
  uint64 tpl_id = 1;
  // @gotags: validate:"required"
  string tpl_name = 2;
  // @gotags: validate:"min=1"
  repeated string game_project = 3;
  // 预提醒时间
  uint32 remind_time = 4;
  // @gotags: validate:"required"
  map<string,string> content = 5;
}

// OverTimeTplDelReq 删除模版
message OverTimeTplDelReq {
  // 模板id @gotags: validate:"required"
  uint64 id = 1;
}

// OverTimeTplOptsReq 模板配置筛选列表-请求
message OverTimeTplOptsReq {
  string game_project = 1;
}

// OverTimeTplOptsReq 模板配置筛选列表-响应结果
message OverTimeTplOptsRes {
  message Opts {
    uint32 tpl_id = 1;   // 模版ID
    string tpl_name = 2;  // 模版名称
  }
  repeated Opts list = 2;
}

// OverTimeTplEnableReq 启用/禁用模版
message OverTimeTplEnableReq {
  // 对象ID @gotags: validate:"required"
  uint32 object_id = 1;
  // 启用1禁用0
  uint32 enable = 2;
}