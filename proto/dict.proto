syntax = "proto3";

package pb;
option go_package = ".;pb";

message DictId {
    // 字段Id @gotags: validate:"required"
    uint32 dict_id = 1;
}

message DictOptsResp {
    message Opts {
        // 字段ID
        uint32 dict_id = 1;
        // 字段名称
        string dict_name = 2;
        // 字段Key
        string dict_key = 3;
    }
    repeated Opts list = 2;
}

message DictListResp {
    message Dict {
        // 团队ID
        uint32 dict_id = 1;
        // 入口名称
        string dict_name = 2;
        // 入口描述
        string dict_key = 3;
        // 更新时间
        string updated_at = 4;
        // 操作人员
        string operator = 5;
        // 状态
        bool enable = 6;
    }
    repeated Dict list = 1;
}

message DictAddReq {
    // 字段名称 @gotags: validate:"required"
    string dict_name = 1;
    // 字段Key @gotags: validate:"required"
    string dict_key = 2;
}

message DictInfoResp {
    // 字段Id @gotags: validate:"required"
    uint32 dict_id = 1;
    // 字段名称 @gotags: validate:"required"
    string dict_name = 2;
    // 字段Key @gotags: validate:"required"
    string dict_key = 3;
}