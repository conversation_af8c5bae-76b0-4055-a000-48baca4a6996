syntax = "proto3";

package pb;
option go_package = ".;pb";


// PortraitInfoReq 获取玩家画像信息
message PortraitInfoReq {
  // @gotags: validate:"required"
  string dsc_user_id = 1;
  // @gotags: validate:"required"
  string game_project = 2;
}

message PortraitInfoResp {
  message LabelDetail {
    uint64 tag_id = 1;
    string tag_name = 2;
  }
  message NewLabelDetail {
    uint64 tag_id = 1;
    string tag_desc = 2;
  }
  message TicketInfo{
    // 游戏
    string project = 1;
    // 工单IDz
    uint64 ticket_id = 2;
    // 玩家输入第一句话
    string detail = 3;
    // 累计金额
    double recharge = 4;
    // 工单状态
    uint32 status = 5;
    // 等待时长
    string waiting_time = 6;
  }
  uint64 id = 1;
  repeated LabelDetail label = 2;
  uint32 gender = 3;
  string birthday = 4;
  string career = 5;
  uint32 education_level = 6;
  uint32 married_state = 7;
  uint32 fertility_state = 8;
  string remark = 9;
  string dsc_user_id = 10;
  string game_project = 11;
  repeated NewLabelDetail new_label = 12;
  repeated TicketInfo ticket_info = 13;
}

// PortraitEditReq 添加/编辑 画像信息
message PortraitEditReq {
  uint64 id = 1;
  string game_project = 2;
  string label = 3;
  uint32 gender = 4;
  string birthday = 5;
  string career = 6;
  uint32 education_level = 7;
  uint32 married_state = 8;
  uint32 fertility_state = 9;
  string remark = 10;
  // @gotags: validate:"required_without=Id"
  string dsc_user_id = 11;
}

// MaintainConfigDelReq 删除玩家维护配置
message MaintainConfigDelReq {
  // id @gotags: validate:"required"
  uint64 id = 1;
  // @gotags: validate:"required"
  string dsc_user_id = 2;
  //  @gotags: validate:"required"
  string game_project = 3;
}

// MaintainConfigListReq 玩家维护配置查询请求参数
message MaintainConfigListReq {
  // 游戏, 支持多选
  repeated string game_project = 1;
  // 维护专员, 支持多选
  repeated string maintainer = 2;
  // 昵称, 支持多选
  repeated string nick_name = 3;
  // 玩家账号,单选
  string dsc_user_id = 4;
  uint32 vip_state = 5;
  uint32  page = 6;
  uint32  page_size = 7;
}

// MaintainConfigListResp 玩家维护配置查询响应
message MaintainConfigListResp {
  message MaintainConfigInfo{
    uint64 id = 1;
    // 玩家discord_id
    string dsc_user_id = 2;
    // 玩家discord 昵称
    string nick_name = 3;
    // 玩家fpid
    string fpid = 4;
    uint64 uid = 5;
    // 服务器
    string sid = 6;
    // 维护专员
    string maintainer = 7;
    // 游戏
    string game_project = 8;
    // 操作人
    string operator = 9;
    uint32 vip_state = 10;
    // 操作时间
    string update_time = 11;
    string lang = 12;
    string birthday = 13;
  }

  uint32 current_page = 1;
  uint32 per_page = 2;
  uint32 total = 3;
  repeated MaintainConfigInfo data = 4;
}

message MaintainConfigNewReq {
  // 玩家discord_id @gotags: validate:"required"
  string dsc_user_id = 1;
  // 玩家discord 昵称
  string nick_name = 2;
  // 玩家fpid @gotags: validate:"required"
  string fpid = 3;
  // 维护专员 @gotags: validate:"required"
  string maintainer = 4;
  // 游戏 @gotags: validate:"required"
  string game_project = 5;
}

message MaintainConfigEditReq {
  // @gotags: validate:"required"
  uint64 id = 1;
  // 玩家fpid
  string fpid = 2;
  // 维护专员
  string maintainer = 3;
  uint64 uid = 4;
  uint32 vip_state = 5;
  // @gotags: validate:"required"
  string game_project = 6;
}

message DiscordPlayerAccountsReq {
  // @gotags: validate:"required"
  string game_project = 1;
  // 搜索值
  string dsc_user_id = 2;
}

message DiscordPlayerAccountsResp {
  repeated string dsc_user_ids = 1;
}

message DiscordCommuRecordAddReq {
  // 沟通日期 @gotags: validate:"required"
  string commu_date = 1;
  // 游戏 @gotags: validate:"required"
  string project = 2;
  // 沟通问题 @gotags: validate:"required"
  string question = 3;
  // 问题类型 @gotags: validate:"required"
  int32 question_type = 4;
  // 处理状态 @gotags: validate:"required"
  int32 handle_status = 5;
  // 备注
  string remark = 6;
  // 涉及对话信息 @gotags: validate:"required"
  string msg_ids = 7;
  // 玩家dsc_user_id @gotags: validate:"required"
  string dsc_user_id = 8;
  // 玩家uid
  int64 uid = 9;
  // 玩家所在的服务器
  string sid = 10;
  // 玩家昵称 @gotags: validate:"required"
  string nick_name = 11;
  // 玩家累付金额
  double pay_all = 12;
}


message DiscordCommuRecordListReq {
  repeated string project = 1;
  repeated string commu_date = 2;
  repeated string operator = 3;
  int64 uid = 4;
  // @gotags: validate:"required"
  uint32 page = 5;
  // @gotags: validate:"required"
  uint32 page_size = 6;
}


message DiscordCommuRecordListResp {
  message DialogueItem {
    string role = 1;
    string content = 2;
  }
  message DiscordCommuRecord {
    string commu_date = 1;
    string project = 2;
    int64 uid = 3;
    string sid = 4;
    string nick_name = 5;
    double pay_all = 6;
    string question = 7;
    int32 question_type = 8;
    int32 handle_status = 9;
    string remark = 10;
    repeated DialogueItem dialogue = 11;
    string operator = 12;
    int64 id = 13;
    string maintainer = 14;
  }
  uint32 current_page = 1;
  uint32 per_page = 2;
  uint32 total = 3;
  repeated DiscordCommuRecord data = 4;
}


message DiscordCommuRecordEditReq {
  // @gotags: validate:"required"
  int64 id = 1;
  string commu_date = 2;
  string question = 3;
  int32 question_type = 4;
  int32 handle_status = 5;
  string remark = 6;
}


message DiscordTagAddReq {
  // @gotags: validate:"required"
  string tag_name = 1;
  string tag_desc = 2;
  // @gotags: validate:"required,oneof=1 2"
  int32 status = 3;
}

message DiscordTagEditReq {
  // @gotags: validate:"required"
  int32 id = 1;
  string tag_name = 2;
  string tag_desc = 3;
  int32 status = 4;
}

message DiscordTagListReq {
  string tag_name = 1;
  string operator = 2;
  int32 status = 3;
}

message DiscordTagListResp {
  message TagDetail {
    int32 id = 1;
    string tag_name = 2;
    string tag_desc = 3;
    string update_time =4;
    string operator = 5;
    int32 status = 6;
  }
  repeated TagDetail data = 1;
}

message DiscordBatchTagReq {
  // @gotags: validate:"required"
  repeated string dsc_user_id_list = 1;
  // @gotags: validate:"required"
  string tag = 2;
  // @gotags: validate:"required"
  string project = 3;
}


message DiscordMessageTaskListReq {
  repeated string project = 1;
  repeated string date = 2;
  repeated string operator = 3;
  // @gotags: validate:"required"
  uint32 page = 5;
  // @gotags: validate:"required"
  uint32 page_size = 6;
}

message DiscordMessageTaskListResp {
  message ReplyContent{
    string content = 3;
    string file_url = 4;
  }
  message Count{
    int32 success_count = 7;
    int32 failed_count = 8;
  }
  message DiscordMessageRecord {
    uint64 task_id = 1;
    string project = 2;
    ReplyContent reply_content = 3;
    int32 status = 5;
    int32 total = 6;
    Count count = 7;
    string operator = 9;
    string create_at = 10;
    string finished_at = 11;
    repeated int64 fail_ids = 12;
  }
  uint32 current_page = 1;
  uint32 per_page = 2;
  uint32 total = 3;
  repeated DiscordMessageRecord data = 4;
}

message DiscordMessageTaskDetailExportReq {
  // @gotags: validate:"required"
  uint64 task_id = 1;
}

message DiscordNewCommuRecordAddReq {
  // 沟通日期 @gotags: validate:"required"
  string commu_date = 1;
  // 游戏 @gotags: validate:"required"
  string project = 2;
  // 沟通问题 @gotags: validate:"required"
  string question = 3;
  // 问题类型Id @gotags: validate:"required"
  uint32 cat_id = 4;
  // 处理状态 @gotags: validate:"required"
  int32 handle_status = 5;
  // 备注
  string remark = 6;
  // 涉及对话信息 @gotags: validate:"required"
  string msg_ids = 7;
  // 玩家dsc_user_id @gotags: validate:"required"
  string dsc_user_id = 8;
  // 玩家uid
  int64 uid = 9;
  // 玩家所在的服务器
  string sid = 10;
  // 玩家昵称 @gotags: validate:"required"
  string nick_name = 11;
  // 玩家累付金额
  double pay_all = 12;
  // 类别:line or discord @gotags: validate:"required,oneof=1 2"
  uint32 cat_type = 13;
}

message DiscordNewCommuRecordEditReq {
  // @gotags: validate:"required"
  int64 id = 1;
  string commu_date = 2;
  string question = 3;
  uint32 cat_id = 4;
  int32 handle_status = 5;
  string remark = 6;
}

message DiscordNewCommuRecordListReq {
  string project = 1;
  repeated string commu_date = 2;
  repeated string operator = 3;
  int64 uid = 4;
  // @gotags: validate:"required"
  uint32 page = 5;
  // @gotags: validate:"required"
  uint32 page_size = 6;
  uint32 cat_id = 7;
  repeated int32 handle_status = 8;
  // 类别:line or discord @gotags: validate:"required,oneof=1 2"
  uint32 cat_type = 13;
}

message DiscordNewCommuRecordListResp {
  message DialogueItem {
    string role = 1;
    string content = 2;
  }
  message DiscordCommuRecord {
    string commu_date = 1;
    string project = 2;
    int64 uid = 3;
    string sid = 4;
    string nick_name = 5;
    double pay_all = 6;
    string question = 7;
    int32 question_type = 8;
    int32 handle_status = 9;
    string remark = 10;
    repeated DialogueItem dialogue = 11;
    string operator = 12;
    int64 id = 13;
    string maintainer = 14;
    int32 cat_id = 15;
    string category = 16;
  }
  uint32 current_page = 1;
  uint32 per_page = 2;
  uint32 total = 3;
  repeated DiscordCommuRecord data = 4;
}

message DiscordPublicTagReq {
  // @gotags: validate:"required"
  repeated string dsc_user_id_list = 1;
}

message DiscordPublicTagResp {
  message TagInfo{
    // 标签ID
    uint32 tag_id = 1;
    // 标签名称
    string tag_name = 2;
  }
  repeated TagInfo tags = 1;
}

// 批量删除DC标签
message DiscordTagBatchDelete{
  // 待删除dscuserID @gotags: validate:"required"
  repeated string dsc_user_id_list = 1;
  // 待删除标签ID @gotags: validate:"required"
  repeated uint32 tag_ids = 2;
  // 游戏 @gotags: validate:"required"
  string project = 3;
}