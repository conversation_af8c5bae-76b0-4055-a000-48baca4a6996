syntax = "proto3";

package pb;
option go_package = ".;pb";

// ModuleCatAddReq 添加分类 请求参数
message ModuleCatAddReq {
  // 项目 @gotags: validate:"required"
  string project = 1;
  // 分类的等级 @gotags: validate:"required,oneof=1 2 3"
  uint32 cat_level = 2;
  // 分类名称 @gotags: validate:"required"
  string category = 3;
  // 父分类ID
  uint32 parent_id = 4;
}

// ModuleCatSaveReq 修改分类 请求参数
message ModuleCatSaveReq {
  // 分类ID @gotags: validate:"required"
  uint32 cat_id = 1;
  // 分类名称 @gotags: validate:"required"
  string category = 2;
  // 分类的等级 @gotags: validate:"required,oneof=1 2 3"
  uint32 cat_level = 7;
}

message ModuleCatTreeReq {
  // 项目 @gotags: validate:"required"
  string project = 1;
}


// ModuleCatTreeResp 问题分类配置(下拉级联多选)列表
message ModuleCatTreeResp {
  message Cat {
    uint32 cat_id = 1;     // 类别ID
    string category = 2;  // 类别名称
    uint32 level = 3;
    // 子结构 @gotags: json:"children,omitempty"
    repeated Cat children = 7;
  }
  repeated Cat data = 1;
}

// ModuleCatItems
message ModuleCatItems {
  uint32 cat_id = 1;
  uint32 parent_id = 2;
  uint32 level = 4;
  string category = 6;
}