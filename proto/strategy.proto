syntax = "proto3";

package pb;
option go_package = ".;pb";



message StrategySplit {
  message ObjBtw {
    int64 start = 1;
    int64 end = 2;
  }
  repeated int64 ids = 1;
  repeated ObjBtw btw = 2;
}

message StrategyFilters {
  bool IsSeverAll  = 1;
  StrategySplit server = 2;
  StrategySplit pay_range = 3;
  StrategySplit castle_level = 4;
}


// StrategyAddReq 策略新增
message StrategyAddReq {
  // 策略id
  uint64 strategy_id = 1;
  // @gotags: validate:"required"
  string project = 2;
  // @gotags: validate:"required"
  string strategy_name = 3;
  string filter_server_lists = 5;
  // @gotags: validate:"required"
  string filter_pay_range_lists = 6;
  string filter_castle_level_lists = 7;
  // 服务器是否为全部  @gotags: validate:"required"
  uint32 type = 8;
}

// StrategyDelReq 策略删除
message StrategyDelReq{
  // @gotags: validate:"required"
  int64 strategy_id = 1;
}

// StrategyEnabelReq 策略禁/启用
message StrategyEnabelReq{
  // @gotags: validate:"required"
  int64 strategy_id = 1;
  // 启用 true false
  uint32 enable = 2;
}

// StrategyListReq 工单策略列表req
message StrategyListReq {
  // 游戏
  repeated string project = 1;
  // 策略名称
  string strategy_name = 2;
  // @gotags: validate:"required"
  uint32 page = 7;
  // @gotags: validate:"required"
  uint32 page_size = 8;

}
// StrategyListResp 工单策略列表resp
message StrategyListResp {
  message StrategyRecord {
    // 策略id
    int64 strategy_id = 1;
    // 策略名称
    string strategy_name = 2;
    // 游戏
    string project = 3;
    // 禁/启用
    uint32 enable = 4;
    // 生效服务器
    string filter_server_lists = 5;
    // 充值区间
    string filter_pay_range_lists = 6;
    // 城堡等级
    string filter_castle_level_lists = 7;
    // 服务器是否全部
    uint32 type = 8;
  }
  uint32 current_page = 1;
  uint32 per_page = 2;
  uint32 total = 3;
  repeated StrategyRecord data = 4;
}