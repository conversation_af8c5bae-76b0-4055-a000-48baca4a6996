syntax = "proto3";

package pb;
option go_package = ".;pb";
import "google/protobuf/any.proto";

// Empty 空对象
message Empty {}

// EnableReq 启用禁用
message EnableReq {
    // 对象ID @gotags: validate:"required"
    uint32 object_id = 1;
    // 启用 true false
    bool enable = 2;
}

message Language {
    map<string, string> language = 1;
}

message ProjectLang {
    // 项目 @gotags: validate:"required"
    string project = 1;
    // 语言
    string lang = 2;
}

// Props 枚举
message Props {
    // 枚举名
    string label = 1;
    // 枚举值
    string value = 2;
}

message BaseInfo {
    // 游戏id
    uint64 game_id = 1;
    // 游戏区分
    string game_project = 2;
    // lang 语言
    string lang = 3;
    // uuid 设备ID
    string uuid = 4;
    // os
    string os = 5;
    // sdk_version
    string sdk_version = 6;
    // fpid
    uint64 fpid = 7;
    // uid 用户ID
    uint64 uid = 8;
    // account_id
    string account_id = 9;
    // role_id 角色ID
    string role_id = 10;
    // sid 区服
    string sid = 11;
    // channel 渠道
    string channel = 12;
    // nickname 昵称
    string nickname = 13;
    string country = 14;
    double total_pay = 15;
    string country_code = 16;
    // fpx_app_id
    string fpx_app_id = 17;
    // bi_app_id
    string bi_app_id = 18;
    // 场景枚举
    uint32 scene = 29;
}

message Response {
    // 状态码
    int32 code = 1;
    // 错误信息
    string msg = 2;
    // 数据
    google.protobuf.Any data = 3;
}

message ServerSplit {
    message ServerBtw {
        int64 start = 1;
        int64 end = 2;
    }
    repeated int64 ids = 1;
    repeated ServerBtw btw = 2;
}

message Project {
    // 项目名称
    string project_name = 1;
    // 标签库名称
    string lib_name = 2;
}
message Projects {
    // 项目名称 @gotags: validate:"required"
    repeated string projects = 1;
}


message ProjectTagLib {
    // 项目名称 @gotags: validate:"required"
    string project_name = 1;
    // 组id
    uint32 lib_id = 2;
}