syntax = "proto3";

package pb;
option go_package = ".;pb";

// 问卷有效期
enum SurveyEffective {
    UnknownEffectiveDay = 0;
    // 三天
    EffectiveDay3 = 3;
    // 五天
    EffectiveDay5 = 5;
    // 七天
    EffectiveDay7 = 7;
}

// 问卷推送周期
enum SurveyPushCycle {
    UnknownSurveyPushCycle = 0;
    /// 每周推送一次
    SurveyPushCycleEveryWeek = 1;
    // 每两周推送一次
    SurveyPushCycleEveryTwoWeeks = 2;
    // 每月推送一次
    SurveyPushCycleEveryMonth = 3;
}
enum SurveyQstType {
    UnknownSurveyQstType = 0;
    // 产品题
    SurveyQstProduct = 1;
    // 服务题
    SurveyQstService = 2;
}
// 报表维度
enum SurveyStatType {
    UnknownSurveyStatType = 0;
    // 日期
    SurveyStatDay = 1;
    // 游戏
    SurveyStatGame = 2;
    // 客服
    SurveyStatAccount = 3;
}