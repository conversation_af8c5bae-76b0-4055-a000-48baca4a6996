syntax = "proto3";

package pb;
option go_package = ".;pb";

// GroupOptsResp 技能组筛选列表 响应结果
message GroupOptsResp {
    message Opts {
        uint32 id = 1;     // 团队ID
        string group_desc = 2;  // 团队名称
        string game = 3;   // 游戏
        string language = 4;   // 语言
        string user = 5;   // 人员名称
        string update_time = 6;   // 更新时间
    }
    repeated Opts list = 1;
}
//
//// GroupListReq 技能组列表】请求参数
//message GroupListReq {
//    // 工作组类型 1:一线 2:二线 3:vip
//    repeated uint32 workgroup = 1;
//    // 技能组名称
//    string group = 2;
//    // user
//    string user = 3;
//    // 页码
//    uint32 page = 4;
//    // 页大小
//    uint32 page_size = 5;
//}
//
//// GroupListResp 技能组列表】响应结果
//message GroupListResp {
//    message Group {
//        // 技能组ID
//        uint32 id = 1;
//        // 技能组类型 1:一线 2:二线 3:vip
//        uint32 workgroup = 2;
//        // 技能组名称
//        string group = 3;
//        // 技能组描述
//        string desc = 4;
//        // 关联人员数量
//        uint32 users = 5;
//        // 更新时间
//        string updated_at = 6;
//        // 更新用户
//        string op = 7;
//        // 状态
//        bool enable = 8;
//        // 技能组接单上限
//        uint32 upper_limit = 9;
//    }
//    // 当前页
//    uint32 current_page = 1;
//    // 页大小
//    uint32 per_page = 2;
//    // 总数
//    uint32 total = 3;
//    // 数据列表
//    repeated Group data = 4;
//}
//
//// GroupsIdReq 技能组ID 请求参数
//message GroupIdReq {
//    // 技能组ID @gotags: validate:"required"
//    uint32 group_id = 1;
//}
//
//// GroupPerms 技能组权限
//message GroupCat {
//    uint32 id = 1;
//    // 游戏项目 @gotags: validate:"required"
//    string game = 2;
//    // 语言 @gotags: validate:"required"
//    string language = 3;
//    // 问题分类 @gotags: validate:"required"
//    string categories = 4;
//}
//
//// GroupEditReq 技能组信息 响应结果
//message GroupInfoResp {
//    // 技能组ID
//    uint32 group_id = 1;
//    // 技能组类型 1:一线 2:二线 3:vip
//    uint32 workgroup = 2;
//    // 技能组名称
//    string group = 3;
//    // 技能组描述
//    string desc = 4;
//    // 技能组接单上限
//    uint32 upper_limit = 5;
//    // 技能组权限
//    repeated GroupCat cat = 6;
//    // 用户列表 @gotags: json:"users,omitempty"
//    repeated string users = 7;
//}
//
//// GroupAddReq 添加技能组 请求参数
//message GroupAddReq {
//    // 技能组类型 1:一线 2:二线 3:vip @gotags: validate:"required"
//    uint32 workgroup = 1;
//    // 技能组名称 @gotags: validate:"required"
//    string group = 2;
//    // 技能组描述
//    string desc = 3;
//    // 技能组权限
//    repeated GroupCat cat = 4;
//    // 技能组接单上限
//    uint32 upper_limit = 5;
//}
//
//// GroupSaveReq 修改技能组 请求参数
//message GroupSaveReq {
//    // 技能组ID @gotags: validate:"required"
//    uint32 group_id = 1;
//    // 技能组类型 1:一线 2:二线 3:vip @gotags: validate:"required"
//    uint32 workgroup = 2;
//    // 技能组名称 @gotags: validate:"required"
//    string group = 3;
//    // 技能组描述
//    string desc = 4;
//    // 技能组接单上限
//    uint32 upper_limit = 5;
//    // 技能组权限
//    repeated GroupCat cat = 6;
//}
//
//// GroupUserReq 技能组用户
//message GroupUserReq {
//    // 技能组ID @gotags: validate:"required"
//    uint32 group_id = 1;
//    // 用户列表
//    repeated string user = 2;
//}
//
//// GroupUserResp 技能组关联人员 响应结果
//message GroupUserResp {
//    message UserInfo {
//        uint32 id = 1;
//        string user = 2;
//    }
//    repeated UserInfo List = 1;
//}
//
//// 用户技能组权限
//message UserGroupPerms {
//    uint32 group_id = 1;
//    string user = 2;
//    string group = 3;
//    uint32 upper_limit = 4;
//    string game = 5;
//    string language = 6;
//    string categories = 7;
//    uint32 workgroup = 8;
//}

message GroupCat {
    uint32 id = 1;
    // 游戏项目 @gotags: validate:"required"
    string game = 2;
    // 语言 @gotags: validate:"required"
    string language = 3;
    // 问题分类 @gotags: validate:"required"
    string categories = 4;
}

message GroupUser {
    // 人员
    uint32 user = 1;
}

message LoginStatus {
    int32 is_login = 1;
}
message GroupUserStateDf{
    int64 UpperLimit = 1;
    string Game = 2;
    string Language = 3;
    int32 IsLogin = 4;
}