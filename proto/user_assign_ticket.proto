syntax = "proto3";

package pb;
option go_package = ".;pb";
import "enum.proto";

// 游戏、问题分类和系统标签
message GameCategory {
  // @gotags: validate:"required"
  string game = 1; // 游戏
  // @gotags: validate:"required"
  repeated string categories = 2; // 分类
  // @gotags: validate:"required"
  repeated TicketSystemTag system_tag = 3; // 系统标签
}

// 新增用户分单配置
message UserAssignTicketAddReq {

  uint32 upper_limit = 1; // 接单上限
  // @gotags: validate:"required"
  repeated string lang = 3; // 语言
  // @gotags: validate:"required"
  repeated string user = 4;  // 人员
  repeated GameCategory detail = 5; // 游戏与问题分类
}

// 修改用户分单配置
message UserAssignTicketEditReq {
  // @gotags: validate:"required"
  uint32 id = 1; // 更新人员Id
  uint32 upper_limit = 2; // 接单上限
  repeated GameCategory detail = 3; // 游戏与问题分类
  repeated string lang = 4; // 语言
}

// 删除用户分单配置
message UserAssignTicketDelReq {
  // @gotags: validate:"required"
  uint32 id = 1;
  // @gotags: validate:"required"
  string user = 2;
}

// 用户分单配置详情
message UserAssignTicketInfoReq {
  uint32 id = 1;
}

// UserAssignTicketListReq 用户分单列表请求参数
message UserAssignTicketListReq {
  // 用户账号
  string account = 1;
  // 页码
  uint32 page = 2;
  // 页大小
  uint32 page_size = 3;
}

// UserAssignTicketListResp 查询列表响应结果
message UserAssignTicketListResp {
  message Detail {
    // 用户的id
    uint32 id = 1;
    // 用户账号
    string account = 2;
    // 游戏列表
    repeated string game = 3;
    // 操作人
    string operator = 4;
    // 语言
    repeated string lang = 5;
    // 用户接单上限
    uint32 upper_limit = 6;
    // 更新时间
    string updated_at = 7;
    // 游戏、问题分类和系统标签
    repeated GameCategory detail = 8;
  }
  // 当前页
  uint32 current_page = 1;
  // 页大小
  uint32 per_page = 2;
  // 总数
  uint32 total = 3;
  // 数据列表
  repeated Detail data = 4;
}


message UserStateDf{
  int64 UpperLimit = 1;
  string Game = 2;
  string Lang = 3;
  int32 IsLogin = 4;
}