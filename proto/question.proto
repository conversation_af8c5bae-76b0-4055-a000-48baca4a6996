syntax = "proto3";

package pb;
option go_package = ".;pb";


// QuestionAddReq 语料新增
message QuestionSaveReq {
  // 问题id
  int64 question_id = 1;
  // @gotags: validate:"required"
  string project = 2;
  // @gotags: validate:"required"
  string question_content = 3;
  // @gotags: validate:"required"
  string lang = 4;
  // @gotags: validate:"required"
  uint32 cat_id = 5;
  // @gotags: validate:"required"
  string answer = 6;
}

// QuestionAddReq 工单知识库训练
message QuestionTrainingReq {
  // @gotags: validate:"required"
  string project = 1;
  // true代表训练该游戏下的所有语种
  bool is_all = 2;
  repeated string lang = 3;
}

// QuestionListReq 工单知识库列表req
message QuestionListReq {
  // 游戏
  repeated string project = 1;
  // 问题分类id
  repeated uint32 cat_id = 2;
  // 语料id
  int64 question_id = 3;
  // 语料内容
  string question_content = 4;
  // 语种
  repeated string lang = 5;
  // 更新时间
  repeated string update_date = 6;
  // @gotags: validate:"required"
  uint32 page = 7;
  // @gotags: validate:"required"
  uint32 page_size = 8;

}
// QuestionAddResp 工单知识库列表resp
message QuestionListResp {
  message QuestionRecord {
    // 语料id
    int64 question_id = 1;
    // 语料内容
    string question_content = 2;
    // 语种
    string lang = 3;
    // 游戏
    string project = 4;
    // 问题分类
    string category = 5;
    // 问题分类id,便于回显
    uint32 cat_id = 6;
    // 更新时间
    string update_date = 7;
    // 更新人
    string operator = 8;
    // 富文本答案
    string answer_rich_text = 9;
  }
  uint32 current_page = 1;
  uint32 per_page = 2;
  uint32 total = 3;
  repeated QuestionRecord data = 4;
}

// QuestionDelReq 工单知识库删除
message QuestionDelReq {
  // @gotags: validate:"required"
  int64 question_id = 1;
}

// QuestionBatchImportReq 工单知识库批量导入
message QuestionBatchImportReq{
  // 文件url @gotags: validate:"required"
  string file_name = 1;
}

message QuestionTrainLogReq{
  // @gotags: validate:"required"
  uint32 page = 1;
  // @gotags: validate:"required"
  uint32 page_size = 2;
}

// QuestionAddResp 训练列表resp
message QuestionTrainLogResp {
  message TrainRecord {
    // 训练时间
    string created_at = 1;
    // 操作人
    string operator = 2;
    // 训练状态
    uint32 status = 3;
    // 游戏
    string project = 4;
    // 语种
    string lang = 5;
  }
  uint32 current_page = 1;
  uint32 per_page = 2;
  int64 total = 3;
  repeated TrainRecord data = 4;
}
