syntax = "proto3";

package pb;
option go_package = ".;pb";


// ModuleSaveReq 新增模板
message ModuleSaveReq {
  // @gotags: validate:"required"
  string module_name = 1;
  // @gotags: validate:"required"
  string game_project = 2;
  // @gotags: validate:"required"
  string content = 3;
  // 模板分类id @gotags: validate:"required"
  uint32 cat_id = 4;
}

// ModuleEditReq 编辑模板
message ModuleEditReq {
  // 模板id @gotags: validate:"required"
  uint64 id = 1;
  string module_name = 2;
  string game_project = 3;
  string content = 4;
  // 启用或禁用模板
  uint32 enable = 5;
  // 模板分类id
  uint32 cat_id = 6;
}

// ModuleEditReq 删除模版
message ModuleDelReq {
  // 模板id @gotags: validate:"required"
  uint64 id = 1;
}

// ModuleListReq 模板查询请求参数
message ModuleListReq {
  // 模板名称,支持模糊搜索
  string module_name = 1;
  // 模板内容,支持模糊搜索
  string content = 2;
  // 游戏, 支持多选
  string game_project = 3;
  uint32 enable = 4;
  uint32  page = 5;
  uint32  page_size = 6;
  // 模版分类
  repeated uint32 cat_id = 7;
}

// ModuleListResp 新工单池响应参数
message ModuleListResp {
  message ModuleInfo{
    // 模板id
    uint64 id = 1;
    // 模板名称
    string module_name = 2;
    // 模板内容
    string content = 3;
    // 关联游戏
    string game_project = 4;
    // 操作人
    string operator = 5;
    // 状态 1启用 2禁用
    uint32 enable = 6;
    // 操作时间
    string update_time = 7;
    // 模板分类名称
    string category = 8;
    // 模版分类id
    uint32 cat_id = 9;
  }

  uint32 current_page = 1;
  uint32 per_page = 2;
  uint32 total = 3;
  repeated ModuleInfo data = 4;
}

message ModuleOptsReq {
  // 项目 @gotags: validate:"required"
  string project = 1;
}

message ModuleOptsResp {
    // 模版名称
    string label = 1;
    // 模版id
    string value = 2;
}
