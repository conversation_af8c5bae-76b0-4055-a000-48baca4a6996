// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.14.0
// source: service.proto

package pb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	TicketApi_TicketWorkPool_FullMethodName       = "/pb.TicketApi/TicketWorkPool"
	TicketApi_TicketCount_FullMethodName          = "/pb.TicketApi/TicketCount"
	TicketApi_TicketWorkPoolExport_FullMethodName = "/pb.TicketApi/TicketWorkPoolExport"
	TicketApi_Reassign_FullMethodName             = "/pb.TicketApi/Reassign"
	TicketApi_TicketInfo_FullMethodName           = "/pb.TicketApi/TicketInfo"
	TicketApi_TicketReTags_FullMethodName         = "/pb.TicketApi/TicketReTags"
	TicketApi_TicketReTagging_FullMethodName      = "/pb.TicketApi/TicketReTagging"
	TicketApi_TicketPublicTag_FullMethodName      = "/pb.TicketApi/TicketPublicTag"
	TicketApi_TicketAddRemark_FullMethodName      = "/pb.TicketApi/TicketAddRemark"
	TicketApi_TicketBatchRemark_FullMethodName    = "/pb.TicketApi/TicketBatchRemark"
	TicketApi_TicketUpgrade_FullMethodName        = "/pb.TicketApi/TicketUpgrade"
	TicketApi_TicketTurn_FullMethodName           = "/pb.TicketApi/TicketTurn"
	TicketApi_TicketReply_FullMethodName          = "/pb.TicketApi/TicketReply"
	TicketApi_TicketIsAcceptor_FullMethodName     = "/pb.TicketApi/TicketIsAcceptor"
)

// TicketApiClient is the client API for TicketApi service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type TicketApiClient interface {
	// 工单池 - 搜索接口
	TicketWorkPool(ctx context.Context, in *TicketPoolNewListReq, opts ...grpc.CallOption) (*TicketPoolNewListResp, error)
	// 工单池 - 工单池数据概览
	TicketCount(ctx context.Context, in *TicketCountReq, opts ...grpc.CallOption) (*TicketCountResp, error)
	// 工单池 - 工单导出接口
	TicketWorkPoolExport(ctx context.Context, in *TicketPoolNewListReq, opts ...grpc.CallOption) (*Empty, error)
	// 工单池 - 指派接口
	Reassign(ctx context.Context, in *AssignmentReq, opts ...grpc.CallOption) (*Empty, error)
	// 工单池 - 所有基础数据
	TicketInfo(ctx context.Context, in *TicketIdReq, opts ...grpc.CallOption) (*TicketResponse, error)
	// 工单池 -- 工单已绑定标签
	TicketReTags(ctx context.Context, in *TicketIdReq, opts ...grpc.CallOption) (*TicketTagRes, error)
	// 工单池 -- 添加标签
	TicketReTagging(ctx context.Context, in *TicketRetaggingReq, opts ...grpc.CallOption) (*Empty, error)
	// 工单池 -- 公共标签
	TicketPublicTag(ctx context.Context, in *TicketPublicTagReq, opts ...grpc.CallOption) (*TicketPublicTagResp, error)
	// 工单池 -- 添加备注
	TicketAddRemark(ctx context.Context, in *TicketRemarkReq, opts ...grpc.CallOption) (*Empty, error)
	// 工单池 -- 添加备注
	TicketBatchRemark(ctx context.Context, in *TicketBatchRemarkReq, opts ...grpc.CallOption) (*Empty, error)
	// 工单池 - 工单升级
	TicketUpgrade(ctx context.Context, in *TicketUpgradeReq, opts ...grpc.CallOption) (*Empty, error)
	// 工单 - 工单流转
	TicketTurn(ctx context.Context, in *AssignmentReq, opts ...grpc.CallOption) (*Empty, error)
	// 工单 - 工单状态变更：工单回复|工单关单|工单拒单
	TicketReply(ctx context.Context, in *TicketTransferReq, opts ...grpc.CallOption) (*Empty, error)
	// 客服是否在线
	TicketIsAcceptor(ctx context.Context, in *GroupUserReq, opts ...grpc.CallOption) (*TicketIsUserResp, error)
}

type ticketApiClient struct {
	cc grpc.ClientConnInterface
}

func NewTicketApiClient(cc grpc.ClientConnInterface) TicketApiClient {
	return &ticketApiClient{cc}
}

func (c *ticketApiClient) TicketWorkPool(ctx context.Context, in *TicketPoolNewListReq, opts ...grpc.CallOption) (*TicketPoolNewListResp, error) {
	out := new(TicketPoolNewListResp)
	err := c.cc.Invoke(ctx, TicketApi_TicketWorkPool_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ticketApiClient) TicketCount(ctx context.Context, in *TicketCountReq, opts ...grpc.CallOption) (*TicketCountResp, error) {
	out := new(TicketCountResp)
	err := c.cc.Invoke(ctx, TicketApi_TicketCount_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ticketApiClient) TicketWorkPoolExport(ctx context.Context, in *TicketPoolNewListReq, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, TicketApi_TicketWorkPoolExport_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ticketApiClient) Reassign(ctx context.Context, in *AssignmentReq, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, TicketApi_Reassign_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ticketApiClient) TicketInfo(ctx context.Context, in *TicketIdReq, opts ...grpc.CallOption) (*TicketResponse, error) {
	out := new(TicketResponse)
	err := c.cc.Invoke(ctx, TicketApi_TicketInfo_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ticketApiClient) TicketReTags(ctx context.Context, in *TicketIdReq, opts ...grpc.CallOption) (*TicketTagRes, error) {
	out := new(TicketTagRes)
	err := c.cc.Invoke(ctx, TicketApi_TicketReTags_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ticketApiClient) TicketReTagging(ctx context.Context, in *TicketRetaggingReq, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, TicketApi_TicketReTagging_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ticketApiClient) TicketPublicTag(ctx context.Context, in *TicketPublicTagReq, opts ...grpc.CallOption) (*TicketPublicTagResp, error) {
	out := new(TicketPublicTagResp)
	err := c.cc.Invoke(ctx, TicketApi_TicketPublicTag_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ticketApiClient) TicketAddRemark(ctx context.Context, in *TicketRemarkReq, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, TicketApi_TicketAddRemark_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ticketApiClient) TicketBatchRemark(ctx context.Context, in *TicketBatchRemarkReq, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, TicketApi_TicketBatchRemark_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ticketApiClient) TicketUpgrade(ctx context.Context, in *TicketUpgradeReq, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, TicketApi_TicketUpgrade_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ticketApiClient) TicketTurn(ctx context.Context, in *AssignmentReq, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, TicketApi_TicketTurn_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ticketApiClient) TicketReply(ctx context.Context, in *TicketTransferReq, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, TicketApi_TicketReply_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ticketApiClient) TicketIsAcceptor(ctx context.Context, in *GroupUserReq, opts ...grpc.CallOption) (*TicketIsUserResp, error) {
	out := new(TicketIsUserResp)
	err := c.cc.Invoke(ctx, TicketApi_TicketIsAcceptor_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// TicketApiServer is the server API for TicketApi service.
// All implementations must embed UnimplementedTicketApiServer
// for forward compatibility
type TicketApiServer interface {
	// 工单池 - 搜索接口
	TicketWorkPool(context.Context, *TicketPoolNewListReq) (*TicketPoolNewListResp, error)
	// 工单池 - 工单池数据概览
	TicketCount(context.Context, *TicketCountReq) (*TicketCountResp, error)
	// 工单池 - 工单导出接口
	TicketWorkPoolExport(context.Context, *TicketPoolNewListReq) (*Empty, error)
	// 工单池 - 指派接口
	Reassign(context.Context, *AssignmentReq) (*Empty, error)
	// 工单池 - 所有基础数据
	TicketInfo(context.Context, *TicketIdReq) (*TicketResponse, error)
	// 工单池 -- 工单已绑定标签
	TicketReTags(context.Context, *TicketIdReq) (*TicketTagRes, error)
	// 工单池 -- 添加标签
	TicketReTagging(context.Context, *TicketRetaggingReq) (*Empty, error)
	// 工单池 -- 公共标签
	TicketPublicTag(context.Context, *TicketPublicTagReq) (*TicketPublicTagResp, error)
	// 工单池 -- 添加备注
	TicketAddRemark(context.Context, *TicketRemarkReq) (*Empty, error)
	// 工单池 -- 添加备注
	TicketBatchRemark(context.Context, *TicketBatchRemarkReq) (*Empty, error)
	// 工单池 - 工单升级
	TicketUpgrade(context.Context, *TicketUpgradeReq) (*Empty, error)
	// 工单 - 工单流转
	TicketTurn(context.Context, *AssignmentReq) (*Empty, error)
	// 工单 - 工单状态变更：工单回复|工单关单|工单拒单
	TicketReply(context.Context, *TicketTransferReq) (*Empty, error)
	// 客服是否在线
	TicketIsAcceptor(context.Context, *GroupUserReq) (*TicketIsUserResp, error)
	mustEmbedUnimplementedTicketApiServer()
}

// UnimplementedTicketApiServer must be embedded to have forward compatible implementations.
type UnimplementedTicketApiServer struct {
}

func (UnimplementedTicketApiServer) TicketWorkPool(context.Context, *TicketPoolNewListReq) (*TicketPoolNewListResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TicketWorkPool not implemented")
}
func (UnimplementedTicketApiServer) TicketCount(context.Context, *TicketCountReq) (*TicketCountResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TicketCount not implemented")
}
func (UnimplementedTicketApiServer) TicketWorkPoolExport(context.Context, *TicketPoolNewListReq) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TicketWorkPoolExport not implemented")
}
func (UnimplementedTicketApiServer) Reassign(context.Context, *AssignmentReq) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Reassign not implemented")
}
func (UnimplementedTicketApiServer) TicketInfo(context.Context, *TicketIdReq) (*TicketResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TicketInfo not implemented")
}
func (UnimplementedTicketApiServer) TicketReTags(context.Context, *TicketIdReq) (*TicketTagRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TicketReTags not implemented")
}
func (UnimplementedTicketApiServer) TicketReTagging(context.Context, *TicketRetaggingReq) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TicketReTagging not implemented")
}
func (UnimplementedTicketApiServer) TicketPublicTag(context.Context, *TicketPublicTagReq) (*TicketPublicTagResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TicketPublicTag not implemented")
}
func (UnimplementedTicketApiServer) TicketAddRemark(context.Context, *TicketRemarkReq) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TicketAddRemark not implemented")
}
func (UnimplementedTicketApiServer) TicketBatchRemark(context.Context, *TicketBatchRemarkReq) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TicketBatchRemark not implemented")
}
func (UnimplementedTicketApiServer) TicketUpgrade(context.Context, *TicketUpgradeReq) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TicketUpgrade not implemented")
}
func (UnimplementedTicketApiServer) TicketTurn(context.Context, *AssignmentReq) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TicketTurn not implemented")
}
func (UnimplementedTicketApiServer) TicketReply(context.Context, *TicketTransferReq) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TicketReply not implemented")
}
func (UnimplementedTicketApiServer) TicketIsAcceptor(context.Context, *GroupUserReq) (*TicketIsUserResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TicketIsAcceptor not implemented")
}
func (UnimplementedTicketApiServer) mustEmbedUnimplementedTicketApiServer() {}

// UnsafeTicketApiServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to TicketApiServer will
// result in compilation errors.
type UnsafeTicketApiServer interface {
	mustEmbedUnimplementedTicketApiServer()
}

func RegisterTicketApiServer(s grpc.ServiceRegistrar, srv TicketApiServer) {
	s.RegisterService(&TicketApi_ServiceDesc, srv)
}

func _TicketApi_TicketWorkPool_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TicketPoolNewListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TicketApiServer).TicketWorkPool(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TicketApi_TicketWorkPool_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TicketApiServer).TicketWorkPool(ctx, req.(*TicketPoolNewListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _TicketApi_TicketCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TicketCountReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TicketApiServer).TicketCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TicketApi_TicketCount_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TicketApiServer).TicketCount(ctx, req.(*TicketCountReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _TicketApi_TicketWorkPoolExport_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TicketPoolNewListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TicketApiServer).TicketWorkPoolExport(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TicketApi_TicketWorkPoolExport_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TicketApiServer).TicketWorkPoolExport(ctx, req.(*TicketPoolNewListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _TicketApi_Reassign_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AssignmentReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TicketApiServer).Reassign(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TicketApi_Reassign_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TicketApiServer).Reassign(ctx, req.(*AssignmentReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _TicketApi_TicketInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TicketIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TicketApiServer).TicketInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TicketApi_TicketInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TicketApiServer).TicketInfo(ctx, req.(*TicketIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _TicketApi_TicketReTags_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TicketIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TicketApiServer).TicketReTags(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TicketApi_TicketReTags_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TicketApiServer).TicketReTags(ctx, req.(*TicketIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _TicketApi_TicketReTagging_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TicketRetaggingReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TicketApiServer).TicketReTagging(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TicketApi_TicketReTagging_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TicketApiServer).TicketReTagging(ctx, req.(*TicketRetaggingReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _TicketApi_TicketPublicTag_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TicketPublicTagReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TicketApiServer).TicketPublicTag(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TicketApi_TicketPublicTag_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TicketApiServer).TicketPublicTag(ctx, req.(*TicketPublicTagReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _TicketApi_TicketAddRemark_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TicketRemarkReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TicketApiServer).TicketAddRemark(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TicketApi_TicketAddRemark_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TicketApiServer).TicketAddRemark(ctx, req.(*TicketRemarkReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _TicketApi_TicketBatchRemark_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TicketBatchRemarkReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TicketApiServer).TicketBatchRemark(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TicketApi_TicketBatchRemark_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TicketApiServer).TicketBatchRemark(ctx, req.(*TicketBatchRemarkReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _TicketApi_TicketUpgrade_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TicketUpgradeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TicketApiServer).TicketUpgrade(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TicketApi_TicketUpgrade_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TicketApiServer).TicketUpgrade(ctx, req.(*TicketUpgradeReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _TicketApi_TicketTurn_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AssignmentReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TicketApiServer).TicketTurn(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TicketApi_TicketTurn_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TicketApiServer).TicketTurn(ctx, req.(*AssignmentReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _TicketApi_TicketReply_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TicketTransferReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TicketApiServer).TicketReply(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TicketApi_TicketReply_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TicketApiServer).TicketReply(ctx, req.(*TicketTransferReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _TicketApi_TicketIsAcceptor_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GroupUserReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TicketApiServer).TicketIsAcceptor(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TicketApi_TicketIsAcceptor_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TicketApiServer).TicketIsAcceptor(ctx, req.(*GroupUserReq))
	}
	return interceptor(ctx, in, info, handler)
}

// TicketApi_ServiceDesc is the grpc.ServiceDesc for TicketApi service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var TicketApi_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "pb.TicketApi",
	HandlerType: (*TicketApiServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "TicketWorkPool",
			Handler:    _TicketApi_TicketWorkPool_Handler,
		},
		{
			MethodName: "TicketCount",
			Handler:    _TicketApi_TicketCount_Handler,
		},
		{
			MethodName: "TicketWorkPoolExport",
			Handler:    _TicketApi_TicketWorkPoolExport_Handler,
		},
		{
			MethodName: "Reassign",
			Handler:    _TicketApi_Reassign_Handler,
		},
		{
			MethodName: "TicketInfo",
			Handler:    _TicketApi_TicketInfo_Handler,
		},
		{
			MethodName: "TicketReTags",
			Handler:    _TicketApi_TicketReTags_Handler,
		},
		{
			MethodName: "TicketReTagging",
			Handler:    _TicketApi_TicketReTagging_Handler,
		},
		{
			MethodName: "TicketPublicTag",
			Handler:    _TicketApi_TicketPublicTag_Handler,
		},
		{
			MethodName: "TicketAddRemark",
			Handler:    _TicketApi_TicketAddRemark_Handler,
		},
		{
			MethodName: "TicketBatchRemark",
			Handler:    _TicketApi_TicketBatchRemark_Handler,
		},
		{
			MethodName: "TicketUpgrade",
			Handler:    _TicketApi_TicketUpgrade_Handler,
		},
		{
			MethodName: "TicketTurn",
			Handler:    _TicketApi_TicketTurn_Handler,
		},
		{
			MethodName: "TicketReply",
			Handler:    _TicketApi_TicketReply_Handler,
		},
		{
			MethodName: "TicketIsAcceptor",
			Handler:    _TicketApi_TicketIsAcceptor_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "service.proto",
}

const (
	DataPlatApi_DataPlatGameItemSave_FullMethodName  = "/pb.DataPlatApi/DataPlatGameItemSave"
	DataPlatApi_DataPlatGameItemList_FullMethodName  = "/pb.DataPlatApi/DataPlatGameItemList"
	DataPlatApi_DataPlatGameItemOpts_FullMethodName  = "/pb.DataPlatApi/DataPlatGameItemOpts"
	DataPlatApi_DataPlatUserGoldInfo_FullMethodName  = "/pb.DataPlatApi/DataPlatUserGoldInfo"
	DataPlatApi_DataPlatUserItemInfo_FullMethodName  = "/pb.DataPlatApi/DataPlatUserItemInfo"
	DataPlatApi_DataPlatUserPayInfo_FullMethodName   = "/pb.DataPlatApi/DataPlatUserPayInfo"
	DataPlatApi_DataPlatUserLoginInfo_FullMethodName = "/pb.DataPlatApi/DataPlatUserLoginInfo"
)

// DataPlatApiClient is the client API for DataPlatApi service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type DataPlatApiClient interface {
	// 游戏 item - 道具ID保存
	DataPlatGameItemSave(ctx context.Context, in *DataPlatGameItemBatchSaveReq, opts ...grpc.CallOption) (*Empty, error)
	// 游戏 item - 道具ID列表
	DataPlatGameItemList(ctx context.Context, in *DataPlatGameItemListReq, opts ...grpc.CallOption) (*DataPlatGameItemListResp, error)
	// 游戏 item - 道具ID列表
	DataPlatGameItemOpts(ctx context.Context, in *DataPlatGameItemOptsReq, opts ...grpc.CallOption) (*DataPlatGameItemOptsResp, error)
	// 精分玩家数据 - 金币查询
	DataPlatUserGoldInfo(ctx context.Context, in *DataPlatUserGoldInfoReq, opts ...grpc.CallOption) (*DataPlatUserGoldInfoResp, error)
	// 精分玩家数据 - 道具查询
	DataPlatUserItemInfo(ctx context.Context, in *DataPlatUserItemInfoReq, opts ...grpc.CallOption) (*DataPlatUserItemInfoResp, error)
	// 精分玩家数据 - 支付查询
	DataPlatUserPayInfo(ctx context.Context, in *DataPlatUserPayInfoReq, opts ...grpc.CallOption) (*DataPlatUserPayInfoResp, error)
	// 精分玩家数据 - 登录查询
	DataPlatUserLoginInfo(ctx context.Context, in *DataPlatUserLoginInfoReq, opts ...grpc.CallOption) (*DataPlatUserLoginInfoResp, error)
}

type dataPlatApiClient struct {
	cc grpc.ClientConnInterface
}

func NewDataPlatApiClient(cc grpc.ClientConnInterface) DataPlatApiClient {
	return &dataPlatApiClient{cc}
}

func (c *dataPlatApiClient) DataPlatGameItemSave(ctx context.Context, in *DataPlatGameItemBatchSaveReq, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, DataPlatApi_DataPlatGameItemSave_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dataPlatApiClient) DataPlatGameItemList(ctx context.Context, in *DataPlatGameItemListReq, opts ...grpc.CallOption) (*DataPlatGameItemListResp, error) {
	out := new(DataPlatGameItemListResp)
	err := c.cc.Invoke(ctx, DataPlatApi_DataPlatGameItemList_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dataPlatApiClient) DataPlatGameItemOpts(ctx context.Context, in *DataPlatGameItemOptsReq, opts ...grpc.CallOption) (*DataPlatGameItemOptsResp, error) {
	out := new(DataPlatGameItemOptsResp)
	err := c.cc.Invoke(ctx, DataPlatApi_DataPlatGameItemOpts_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dataPlatApiClient) DataPlatUserGoldInfo(ctx context.Context, in *DataPlatUserGoldInfoReq, opts ...grpc.CallOption) (*DataPlatUserGoldInfoResp, error) {
	out := new(DataPlatUserGoldInfoResp)
	err := c.cc.Invoke(ctx, DataPlatApi_DataPlatUserGoldInfo_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dataPlatApiClient) DataPlatUserItemInfo(ctx context.Context, in *DataPlatUserItemInfoReq, opts ...grpc.CallOption) (*DataPlatUserItemInfoResp, error) {
	out := new(DataPlatUserItemInfoResp)
	err := c.cc.Invoke(ctx, DataPlatApi_DataPlatUserItemInfo_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dataPlatApiClient) DataPlatUserPayInfo(ctx context.Context, in *DataPlatUserPayInfoReq, opts ...grpc.CallOption) (*DataPlatUserPayInfoResp, error) {
	out := new(DataPlatUserPayInfoResp)
	err := c.cc.Invoke(ctx, DataPlatApi_DataPlatUserPayInfo_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dataPlatApiClient) DataPlatUserLoginInfo(ctx context.Context, in *DataPlatUserLoginInfoReq, opts ...grpc.CallOption) (*DataPlatUserLoginInfoResp, error) {
	out := new(DataPlatUserLoginInfoResp)
	err := c.cc.Invoke(ctx, DataPlatApi_DataPlatUserLoginInfo_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// DataPlatApiServer is the server API for DataPlatApi service.
// All implementations must embed UnimplementedDataPlatApiServer
// for forward compatibility
type DataPlatApiServer interface {
	// 游戏 item - 道具ID保存
	DataPlatGameItemSave(context.Context, *DataPlatGameItemBatchSaveReq) (*Empty, error)
	// 游戏 item - 道具ID列表
	DataPlatGameItemList(context.Context, *DataPlatGameItemListReq) (*DataPlatGameItemListResp, error)
	// 游戏 item - 道具ID列表
	DataPlatGameItemOpts(context.Context, *DataPlatGameItemOptsReq) (*DataPlatGameItemOptsResp, error)
	// 精分玩家数据 - 金币查询
	DataPlatUserGoldInfo(context.Context, *DataPlatUserGoldInfoReq) (*DataPlatUserGoldInfoResp, error)
	// 精分玩家数据 - 道具查询
	DataPlatUserItemInfo(context.Context, *DataPlatUserItemInfoReq) (*DataPlatUserItemInfoResp, error)
	// 精分玩家数据 - 支付查询
	DataPlatUserPayInfo(context.Context, *DataPlatUserPayInfoReq) (*DataPlatUserPayInfoResp, error)
	// 精分玩家数据 - 登录查询
	DataPlatUserLoginInfo(context.Context, *DataPlatUserLoginInfoReq) (*DataPlatUserLoginInfoResp, error)
	mustEmbedUnimplementedDataPlatApiServer()
}

// UnimplementedDataPlatApiServer must be embedded to have forward compatible implementations.
type UnimplementedDataPlatApiServer struct {
}

func (UnimplementedDataPlatApiServer) DataPlatGameItemSave(context.Context, *DataPlatGameItemBatchSaveReq) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DataPlatGameItemSave not implemented")
}
func (UnimplementedDataPlatApiServer) DataPlatGameItemList(context.Context, *DataPlatGameItemListReq) (*DataPlatGameItemListResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DataPlatGameItemList not implemented")
}
func (UnimplementedDataPlatApiServer) DataPlatGameItemOpts(context.Context, *DataPlatGameItemOptsReq) (*DataPlatGameItemOptsResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DataPlatGameItemOpts not implemented")
}
func (UnimplementedDataPlatApiServer) DataPlatUserGoldInfo(context.Context, *DataPlatUserGoldInfoReq) (*DataPlatUserGoldInfoResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DataPlatUserGoldInfo not implemented")
}
func (UnimplementedDataPlatApiServer) DataPlatUserItemInfo(context.Context, *DataPlatUserItemInfoReq) (*DataPlatUserItemInfoResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DataPlatUserItemInfo not implemented")
}
func (UnimplementedDataPlatApiServer) DataPlatUserPayInfo(context.Context, *DataPlatUserPayInfoReq) (*DataPlatUserPayInfoResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DataPlatUserPayInfo not implemented")
}
func (UnimplementedDataPlatApiServer) DataPlatUserLoginInfo(context.Context, *DataPlatUserLoginInfoReq) (*DataPlatUserLoginInfoResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DataPlatUserLoginInfo not implemented")
}
func (UnimplementedDataPlatApiServer) mustEmbedUnimplementedDataPlatApiServer() {}

// UnsafeDataPlatApiServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to DataPlatApiServer will
// result in compilation errors.
type UnsafeDataPlatApiServer interface {
	mustEmbedUnimplementedDataPlatApiServer()
}

func RegisterDataPlatApiServer(s grpc.ServiceRegistrar, srv DataPlatApiServer) {
	s.RegisterService(&DataPlatApi_ServiceDesc, srv)
}

func _DataPlatApi_DataPlatGameItemSave_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DataPlatGameItemBatchSaveReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DataPlatApiServer).DataPlatGameItemSave(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DataPlatApi_DataPlatGameItemSave_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DataPlatApiServer).DataPlatGameItemSave(ctx, req.(*DataPlatGameItemBatchSaveReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _DataPlatApi_DataPlatGameItemList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DataPlatGameItemListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DataPlatApiServer).DataPlatGameItemList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DataPlatApi_DataPlatGameItemList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DataPlatApiServer).DataPlatGameItemList(ctx, req.(*DataPlatGameItemListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _DataPlatApi_DataPlatGameItemOpts_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DataPlatGameItemOptsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DataPlatApiServer).DataPlatGameItemOpts(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DataPlatApi_DataPlatGameItemOpts_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DataPlatApiServer).DataPlatGameItemOpts(ctx, req.(*DataPlatGameItemOptsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _DataPlatApi_DataPlatUserGoldInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DataPlatUserGoldInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DataPlatApiServer).DataPlatUserGoldInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DataPlatApi_DataPlatUserGoldInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DataPlatApiServer).DataPlatUserGoldInfo(ctx, req.(*DataPlatUserGoldInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _DataPlatApi_DataPlatUserItemInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DataPlatUserItemInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DataPlatApiServer).DataPlatUserItemInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DataPlatApi_DataPlatUserItemInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DataPlatApiServer).DataPlatUserItemInfo(ctx, req.(*DataPlatUserItemInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _DataPlatApi_DataPlatUserPayInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DataPlatUserPayInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DataPlatApiServer).DataPlatUserPayInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DataPlatApi_DataPlatUserPayInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DataPlatApiServer).DataPlatUserPayInfo(ctx, req.(*DataPlatUserPayInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _DataPlatApi_DataPlatUserLoginInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DataPlatUserLoginInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DataPlatApiServer).DataPlatUserLoginInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DataPlatApi_DataPlatUserLoginInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DataPlatApiServer).DataPlatUserLoginInfo(ctx, req.(*DataPlatUserLoginInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

// DataPlatApi_ServiceDesc is the grpc.ServiceDesc for DataPlatApi service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var DataPlatApi_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "pb.DataPlatApi",
	HandlerType: (*DataPlatApiServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "DataPlatGameItemSave",
			Handler:    _DataPlatApi_DataPlatGameItemSave_Handler,
		},
		{
			MethodName: "DataPlatGameItemList",
			Handler:    _DataPlatApi_DataPlatGameItemList_Handler,
		},
		{
			MethodName: "DataPlatGameItemOpts",
			Handler:    _DataPlatApi_DataPlatGameItemOpts_Handler,
		},
		{
			MethodName: "DataPlatUserGoldInfo",
			Handler:    _DataPlatApi_DataPlatUserGoldInfo_Handler,
		},
		{
			MethodName: "DataPlatUserItemInfo",
			Handler:    _DataPlatApi_DataPlatUserItemInfo_Handler,
		},
		{
			MethodName: "DataPlatUserPayInfo",
			Handler:    _DataPlatApi_DataPlatUserPayInfo_Handler,
		},
		{
			MethodName: "DataPlatUserLoginInfo",
			Handler:    _DataPlatApi_DataPlatUserLoginInfo_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "service.proto",
}

const (
	PunlicApi_AddonsEnum_FullMethodName                 = "/pb.PunlicApi/AddonsEnum"
	PunlicApi_AddonsLang_FullMethodName                 = "/pb.PunlicApi/AddonsLang"
	PunlicApi_AddonsGameList_FullMethodName             = "/pb.PunlicApi/AddonsGameList"
	PunlicApi_AddonsUserist_FullMethodName              = "/pb.PunlicApi/AddonsUserist"
	PunlicApi_AddonsDscBotList_FullMethodName           = "/pb.PunlicApi/AddonsDscBotList"
	PunlicApi_AddonsChannelList_FullMethodName          = "/pb.PunlicApi/AddonsChannelList"
	PunlicApi_AddonsChannelTreeList_FullMethodName      = "/pb.PunlicApi/AddonsChannelTreeList"
	PunlicApi_AddonsChannelPackageIDList_FullMethodName = "/pb.PunlicApi/AddonsChannelPackageIDList"
)

// PunlicApiClient is the client API for PunlicApi service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type PunlicApiClient interface {
	// 基础接口 - 枚举
	AddonsEnum(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*Empty, error)
	// 语言列表
	AddonsLang(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*Empty, error)
	// 游戏列表
	AddonsGameList(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*Empty, error)
	// 客服列表
	AddonsUserist(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*UserListResp, error)
	// discord机器人列表
	AddonsDscBotList(ctx context.Context, in *Projects, opts ...grpc.CallOption) (*Empty, error)
	// 渠道列表
	AddonsChannelList(ctx context.Context, in *Project, opts ...grpc.CallOption) (*Empty, error)
	// 渠道+子渠道列表
	AddonsChannelTreeList(ctx context.Context, in *Project, opts ...grpc.CallOption) (*Empty, error)
	// 渠道+渠道号列表
	AddonsChannelPackageIDList(ctx context.Context, in *Project, opts ...grpc.CallOption) (*Empty, error)
}

type punlicApiClient struct {
	cc grpc.ClientConnInterface
}

func NewPunlicApiClient(cc grpc.ClientConnInterface) PunlicApiClient {
	return &punlicApiClient{cc}
}

func (c *punlicApiClient) AddonsEnum(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, PunlicApi_AddonsEnum_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *punlicApiClient) AddonsLang(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, PunlicApi_AddonsLang_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *punlicApiClient) AddonsGameList(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, PunlicApi_AddonsGameList_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *punlicApiClient) AddonsUserist(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*UserListResp, error) {
	out := new(UserListResp)
	err := c.cc.Invoke(ctx, PunlicApi_AddonsUserist_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *punlicApiClient) AddonsDscBotList(ctx context.Context, in *Projects, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, PunlicApi_AddonsDscBotList_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *punlicApiClient) AddonsChannelList(ctx context.Context, in *Project, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, PunlicApi_AddonsChannelList_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *punlicApiClient) AddonsChannelTreeList(ctx context.Context, in *Project, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, PunlicApi_AddonsChannelTreeList_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *punlicApiClient) AddonsChannelPackageIDList(ctx context.Context, in *Project, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, PunlicApi_AddonsChannelPackageIDList_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PunlicApiServer is the server API for PunlicApi service.
// All implementations must embed UnimplementedPunlicApiServer
// for forward compatibility
type PunlicApiServer interface {
	// 基础接口 - 枚举
	AddonsEnum(context.Context, *Empty) (*Empty, error)
	// 语言列表
	AddonsLang(context.Context, *Empty) (*Empty, error)
	// 游戏列表
	AddonsGameList(context.Context, *Empty) (*Empty, error)
	// 客服列表
	AddonsUserist(context.Context, *Empty) (*UserListResp, error)
	// discord机器人列表
	AddonsDscBotList(context.Context, *Projects) (*Empty, error)
	// 渠道列表
	AddonsChannelList(context.Context, *Project) (*Empty, error)
	// 渠道+子渠道列表
	AddonsChannelTreeList(context.Context, *Project) (*Empty, error)
	// 渠道+渠道号列表
	AddonsChannelPackageIDList(context.Context, *Project) (*Empty, error)
	mustEmbedUnimplementedPunlicApiServer()
}

// UnimplementedPunlicApiServer must be embedded to have forward compatible implementations.
type UnimplementedPunlicApiServer struct {
}

func (UnimplementedPunlicApiServer) AddonsEnum(context.Context, *Empty) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddonsEnum not implemented")
}
func (UnimplementedPunlicApiServer) AddonsLang(context.Context, *Empty) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddonsLang not implemented")
}
func (UnimplementedPunlicApiServer) AddonsGameList(context.Context, *Empty) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddonsGameList not implemented")
}
func (UnimplementedPunlicApiServer) AddonsUserist(context.Context, *Empty) (*UserListResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddonsUserist not implemented")
}
func (UnimplementedPunlicApiServer) AddonsDscBotList(context.Context, *Projects) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddonsDscBotList not implemented")
}
func (UnimplementedPunlicApiServer) AddonsChannelList(context.Context, *Project) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddonsChannelList not implemented")
}
func (UnimplementedPunlicApiServer) AddonsChannelTreeList(context.Context, *Project) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddonsChannelTreeList not implemented")
}
func (UnimplementedPunlicApiServer) AddonsChannelPackageIDList(context.Context, *Project) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddonsChannelPackageIDList not implemented")
}
func (UnimplementedPunlicApiServer) mustEmbedUnimplementedPunlicApiServer() {}

// UnsafePunlicApiServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to PunlicApiServer will
// result in compilation errors.
type UnsafePunlicApiServer interface {
	mustEmbedUnimplementedPunlicApiServer()
}

func RegisterPunlicApiServer(s grpc.ServiceRegistrar, srv PunlicApiServer) {
	s.RegisterService(&PunlicApi_ServiceDesc, srv)
}

func _PunlicApi_AddonsEnum_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PunlicApiServer).AddonsEnum(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PunlicApi_AddonsEnum_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PunlicApiServer).AddonsEnum(ctx, req.(*Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _PunlicApi_AddonsLang_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PunlicApiServer).AddonsLang(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PunlicApi_AddonsLang_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PunlicApiServer).AddonsLang(ctx, req.(*Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _PunlicApi_AddonsGameList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PunlicApiServer).AddonsGameList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PunlicApi_AddonsGameList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PunlicApiServer).AddonsGameList(ctx, req.(*Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _PunlicApi_AddonsUserist_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PunlicApiServer).AddonsUserist(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PunlicApi_AddonsUserist_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PunlicApiServer).AddonsUserist(ctx, req.(*Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _PunlicApi_AddonsDscBotList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Projects)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PunlicApiServer).AddonsDscBotList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PunlicApi_AddonsDscBotList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PunlicApiServer).AddonsDscBotList(ctx, req.(*Projects))
	}
	return interceptor(ctx, in, info, handler)
}

func _PunlicApi_AddonsChannelList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Project)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PunlicApiServer).AddonsChannelList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PunlicApi_AddonsChannelList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PunlicApiServer).AddonsChannelList(ctx, req.(*Project))
	}
	return interceptor(ctx, in, info, handler)
}

func _PunlicApi_AddonsChannelTreeList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Project)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PunlicApiServer).AddonsChannelTreeList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PunlicApi_AddonsChannelTreeList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PunlicApiServer).AddonsChannelTreeList(ctx, req.(*Project))
	}
	return interceptor(ctx, in, info, handler)
}

func _PunlicApi_AddonsChannelPackageIDList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Project)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PunlicApiServer).AddonsChannelPackageIDList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PunlicApi_AddonsChannelPackageIDList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PunlicApiServer).AddonsChannelPackageIDList(ctx, req.(*Project))
	}
	return interceptor(ctx, in, info, handler)
}

// PunlicApi_ServiceDesc is the grpc.ServiceDesc for PunlicApi service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var PunlicApi_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "pb.PunlicApi",
	HandlerType: (*PunlicApiServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "AddonsEnum",
			Handler:    _PunlicApi_AddonsEnum_Handler,
		},
		{
			MethodName: "AddonsLang",
			Handler:    _PunlicApi_AddonsLang_Handler,
		},
		{
			MethodName: "AddonsGameList",
			Handler:    _PunlicApi_AddonsGameList_Handler,
		},
		{
			MethodName: "AddonsUserist",
			Handler:    _PunlicApi_AddonsUserist_Handler,
		},
		{
			MethodName: "AddonsDscBotList",
			Handler:    _PunlicApi_AddonsDscBotList_Handler,
		},
		{
			MethodName: "AddonsChannelList",
			Handler:    _PunlicApi_AddonsChannelList_Handler,
		},
		{
			MethodName: "AddonsChannelTreeList",
			Handler:    _PunlicApi_AddonsChannelTreeList_Handler,
		},
		{
			MethodName: "AddonsChannelPackageIDList",
			Handler:    _PunlicApi_AddonsChannelPackageIDList_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "service.proto",
}

const (
	TagLibApi_TagOpts_FullMethodName      = "/pb.TagLibApi/TagOpts"
	TagLibApi_TagLibOpts_FullMethodName   = "/pb.TagLibApi/TagLibOpts"
	TagLibApi_TagLibList_FullMethodName   = "/pb.TagLibApi/TagLibList"
	TagLibApi_TagLibInfo_FullMethodName   = "/pb.TagLibApi/TagLibInfo"
	TagLibApi_TagLibSave_FullMethodName   = "/pb.TagLibApi/TagLibSave"
	TagLibApi_TagLibEnable_FullMethodName = "/pb.TagLibApi/TagLibEnable"
)

// TagLibApiClient is the client API for TagLibApi service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type TagLibApiClient interface {
	// 标签列表 - 筛选
	TagOpts(ctx context.Context, in *TagOptsReq, opts ...grpc.CallOption) (*TagOptsResp, error)
	// 标签库 - 筛选列表接口
	TagLibOpts(ctx context.Context, in *TagOptsReq, opts ...grpc.CallOption) (*TagLibOptsResp, error)
	// 标签库配置 - 列表接口
	TagLibList(ctx context.Context, in *TagLibListReq, opts ...grpc.CallOption) (*TagLibListResp, error)
	// 标签库配置 - 详情
	TagLibInfo(ctx context.Context, in *TagLibID, opts ...grpc.CallOption) (*TagLibInfoResp, error)
	// 标签库配置 -  新增&修改
	TagLibSave(ctx context.Context, in *TagLibSaveReq, opts ...grpc.CallOption) (*TagLibID, error)
	// 标签库配置 - 修改禁用启用
	TagLibEnable(ctx context.Context, in *EnableReq, opts ...grpc.CallOption) (*Empty, error)
}

type tagLibApiClient struct {
	cc grpc.ClientConnInterface
}

func NewTagLibApiClient(cc grpc.ClientConnInterface) TagLibApiClient {
	return &tagLibApiClient{cc}
}

func (c *tagLibApiClient) TagOpts(ctx context.Context, in *TagOptsReq, opts ...grpc.CallOption) (*TagOptsResp, error) {
	out := new(TagOptsResp)
	err := c.cc.Invoke(ctx, TagLibApi_TagOpts_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tagLibApiClient) TagLibOpts(ctx context.Context, in *TagOptsReq, opts ...grpc.CallOption) (*TagLibOptsResp, error) {
	out := new(TagLibOptsResp)
	err := c.cc.Invoke(ctx, TagLibApi_TagLibOpts_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tagLibApiClient) TagLibList(ctx context.Context, in *TagLibListReq, opts ...grpc.CallOption) (*TagLibListResp, error) {
	out := new(TagLibListResp)
	err := c.cc.Invoke(ctx, TagLibApi_TagLibList_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tagLibApiClient) TagLibInfo(ctx context.Context, in *TagLibID, opts ...grpc.CallOption) (*TagLibInfoResp, error) {
	out := new(TagLibInfoResp)
	err := c.cc.Invoke(ctx, TagLibApi_TagLibInfo_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tagLibApiClient) TagLibSave(ctx context.Context, in *TagLibSaveReq, opts ...grpc.CallOption) (*TagLibID, error) {
	out := new(TagLibID)
	err := c.cc.Invoke(ctx, TagLibApi_TagLibSave_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tagLibApiClient) TagLibEnable(ctx context.Context, in *EnableReq, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, TagLibApi_TagLibEnable_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// TagLibApiServer is the server API for TagLibApi service.
// All implementations must embed UnimplementedTagLibApiServer
// for forward compatibility
type TagLibApiServer interface {
	// 标签列表 - 筛选
	TagOpts(context.Context, *TagOptsReq) (*TagOptsResp, error)
	// 标签库 - 筛选列表接口
	TagLibOpts(context.Context, *TagOptsReq) (*TagLibOptsResp, error)
	// 标签库配置 - 列表接口
	TagLibList(context.Context, *TagLibListReq) (*TagLibListResp, error)
	// 标签库配置 - 详情
	TagLibInfo(context.Context, *TagLibID) (*TagLibInfoResp, error)
	// 标签库配置 -  新增&修改
	TagLibSave(context.Context, *TagLibSaveReq) (*TagLibID, error)
	// 标签库配置 - 修改禁用启用
	TagLibEnable(context.Context, *EnableReq) (*Empty, error)
	mustEmbedUnimplementedTagLibApiServer()
}

// UnimplementedTagLibApiServer must be embedded to have forward compatible implementations.
type UnimplementedTagLibApiServer struct {
}

func (UnimplementedTagLibApiServer) TagOpts(context.Context, *TagOptsReq) (*TagOptsResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TagOpts not implemented")
}
func (UnimplementedTagLibApiServer) TagLibOpts(context.Context, *TagOptsReq) (*TagLibOptsResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TagLibOpts not implemented")
}
func (UnimplementedTagLibApiServer) TagLibList(context.Context, *TagLibListReq) (*TagLibListResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TagLibList not implemented")
}
func (UnimplementedTagLibApiServer) TagLibInfo(context.Context, *TagLibID) (*TagLibInfoResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TagLibInfo not implemented")
}
func (UnimplementedTagLibApiServer) TagLibSave(context.Context, *TagLibSaveReq) (*TagLibID, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TagLibSave not implemented")
}
func (UnimplementedTagLibApiServer) TagLibEnable(context.Context, *EnableReq) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TagLibEnable not implemented")
}
func (UnimplementedTagLibApiServer) mustEmbedUnimplementedTagLibApiServer() {}

// UnsafeTagLibApiServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to TagLibApiServer will
// result in compilation errors.
type UnsafeTagLibApiServer interface {
	mustEmbedUnimplementedTagLibApiServer()
}

func RegisterTagLibApiServer(s grpc.ServiceRegistrar, srv TagLibApiServer) {
	s.RegisterService(&TagLibApi_ServiceDesc, srv)
}

func _TagLibApi_TagOpts_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TagOptsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TagLibApiServer).TagOpts(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TagLibApi_TagOpts_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TagLibApiServer).TagOpts(ctx, req.(*TagOptsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _TagLibApi_TagLibOpts_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TagOptsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TagLibApiServer).TagLibOpts(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TagLibApi_TagLibOpts_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TagLibApiServer).TagLibOpts(ctx, req.(*TagOptsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _TagLibApi_TagLibList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TagLibListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TagLibApiServer).TagLibList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TagLibApi_TagLibList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TagLibApiServer).TagLibList(ctx, req.(*TagLibListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _TagLibApi_TagLibInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TagLibID)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TagLibApiServer).TagLibInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TagLibApi_TagLibInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TagLibApiServer).TagLibInfo(ctx, req.(*TagLibID))
	}
	return interceptor(ctx, in, info, handler)
}

func _TagLibApi_TagLibSave_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TagLibSaveReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TagLibApiServer).TagLibSave(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TagLibApi_TagLibSave_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TagLibApiServer).TagLibSave(ctx, req.(*TagLibSaveReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _TagLibApi_TagLibEnable_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(EnableReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TagLibApiServer).TagLibEnable(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TagLibApi_TagLibEnable_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TagLibApiServer).TagLibEnable(ctx, req.(*EnableReq))
	}
	return interceptor(ctx, in, info, handler)
}

// TagLibApi_ServiceDesc is the grpc.ServiceDesc for TagLibApi service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var TagLibApi_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "pb.TagLibApi",
	HandlerType: (*TagLibApiServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "TagOpts",
			Handler:    _TagLibApi_TagOpts_Handler,
		},
		{
			MethodName: "TagLibOpts",
			Handler:    _TagLibApi_TagLibOpts_Handler,
		},
		{
			MethodName: "TagLibList",
			Handler:    _TagLibApi_TagLibList_Handler,
		},
		{
			MethodName: "TagLibInfo",
			Handler:    _TagLibApi_TagLibInfo_Handler,
		},
		{
			MethodName: "TagLibSave",
			Handler:    _TagLibApi_TagLibSave_Handler,
		},
		{
			MethodName: "TagLibEnable",
			Handler:    _TagLibApi_TagLibEnable_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "service.proto",
}

const (
	AllTagLibApi_AllTagOpts_FullMethodName    = "/pb.AllTagLibApi/AllTagOpts"
	AllTagLibApi_AllTagLibList_FullMethodName = "/pb.AllTagLibApi/AllTagLibList"
	AllTagLibApi_AllTagLibInfo_FullMethodName = "/pb.AllTagLibApi/AllTagLibInfo"
	AllTagLibApi_TagLibSave_FullMethodName    = "/pb.AllTagLibApi/TagLibSave"
	AllTagLibApi_TagLibEnable_FullMethodName  = "/pb.AllTagLibApi/TagLibEnable"
)

// AllTagLibApiClient is the client API for AllTagLibApi service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type AllTagLibApiClient interface {
	// 标签列表 - 筛选
	AllTagOpts(ctx context.Context, in *TagOptsReq, opts ...grpc.CallOption) (*TagOptsResp, error)
	// 标签库配置 - 列表接口
	AllTagLibList(ctx context.Context, in *TagLibListReq, opts ...grpc.CallOption) (*TagLibListResp, error)
	// 标签库配置 - 详情
	AllTagLibInfo(ctx context.Context, in *TagLibID, opts ...grpc.CallOption) (*TagLibInfoResp, error)
	// 标签库配置 -  新增&修改
	TagLibSave(ctx context.Context, in *AllTagLibSaveReq, opts ...grpc.CallOption) (*TagLibID, error)
	// 标签库配置 - 修改禁用启用
	TagLibEnable(ctx context.Context, in *EnableReq, opts ...grpc.CallOption) (*Empty, error)
}

type allTagLibApiClient struct {
	cc grpc.ClientConnInterface
}

func NewAllTagLibApiClient(cc grpc.ClientConnInterface) AllTagLibApiClient {
	return &allTagLibApiClient{cc}
}

func (c *allTagLibApiClient) AllTagOpts(ctx context.Context, in *TagOptsReq, opts ...grpc.CallOption) (*TagOptsResp, error) {
	out := new(TagOptsResp)
	err := c.cc.Invoke(ctx, AllTagLibApi_AllTagOpts_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *allTagLibApiClient) AllTagLibList(ctx context.Context, in *TagLibListReq, opts ...grpc.CallOption) (*TagLibListResp, error) {
	out := new(TagLibListResp)
	err := c.cc.Invoke(ctx, AllTagLibApi_AllTagLibList_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *allTagLibApiClient) AllTagLibInfo(ctx context.Context, in *TagLibID, opts ...grpc.CallOption) (*TagLibInfoResp, error) {
	out := new(TagLibInfoResp)
	err := c.cc.Invoke(ctx, AllTagLibApi_AllTagLibInfo_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *allTagLibApiClient) TagLibSave(ctx context.Context, in *AllTagLibSaveReq, opts ...grpc.CallOption) (*TagLibID, error) {
	out := new(TagLibID)
	err := c.cc.Invoke(ctx, AllTagLibApi_TagLibSave_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *allTagLibApiClient) TagLibEnable(ctx context.Context, in *EnableReq, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, AllTagLibApi_TagLibEnable_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AllTagLibApiServer is the server API for AllTagLibApi service.
// All implementations must embed UnimplementedAllTagLibApiServer
// for forward compatibility
type AllTagLibApiServer interface {
	// 标签列表 - 筛选
	AllTagOpts(context.Context, *TagOptsReq) (*TagOptsResp, error)
	// 标签库配置 - 列表接口
	AllTagLibList(context.Context, *TagLibListReq) (*TagLibListResp, error)
	// 标签库配置 - 详情
	AllTagLibInfo(context.Context, *TagLibID) (*TagLibInfoResp, error)
	// 标签库配置 -  新增&修改
	TagLibSave(context.Context, *AllTagLibSaveReq) (*TagLibID, error)
	// 标签库配置 - 修改禁用启用
	TagLibEnable(context.Context, *EnableReq) (*Empty, error)
	mustEmbedUnimplementedAllTagLibApiServer()
}

// UnimplementedAllTagLibApiServer must be embedded to have forward compatible implementations.
type UnimplementedAllTagLibApiServer struct {
}

func (UnimplementedAllTagLibApiServer) AllTagOpts(context.Context, *TagOptsReq) (*TagOptsResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AllTagOpts not implemented")
}
func (UnimplementedAllTagLibApiServer) AllTagLibList(context.Context, *TagLibListReq) (*TagLibListResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AllTagLibList not implemented")
}
func (UnimplementedAllTagLibApiServer) AllTagLibInfo(context.Context, *TagLibID) (*TagLibInfoResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AllTagLibInfo not implemented")
}
func (UnimplementedAllTagLibApiServer) TagLibSave(context.Context, *AllTagLibSaveReq) (*TagLibID, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TagLibSave not implemented")
}
func (UnimplementedAllTagLibApiServer) TagLibEnable(context.Context, *EnableReq) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TagLibEnable not implemented")
}
func (UnimplementedAllTagLibApiServer) mustEmbedUnimplementedAllTagLibApiServer() {}

// UnsafeAllTagLibApiServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to AllTagLibApiServer will
// result in compilation errors.
type UnsafeAllTagLibApiServer interface {
	mustEmbedUnimplementedAllTagLibApiServer()
}

func RegisterAllTagLibApiServer(s grpc.ServiceRegistrar, srv AllTagLibApiServer) {
	s.RegisterService(&AllTagLibApi_ServiceDesc, srv)
}

func _AllTagLibApi_AllTagOpts_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TagOptsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AllTagLibApiServer).AllTagOpts(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AllTagLibApi_AllTagOpts_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AllTagLibApiServer).AllTagOpts(ctx, req.(*TagOptsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AllTagLibApi_AllTagLibList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TagLibListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AllTagLibApiServer).AllTagLibList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AllTagLibApi_AllTagLibList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AllTagLibApiServer).AllTagLibList(ctx, req.(*TagLibListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AllTagLibApi_AllTagLibInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TagLibID)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AllTagLibApiServer).AllTagLibInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AllTagLibApi_AllTagLibInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AllTagLibApiServer).AllTagLibInfo(ctx, req.(*TagLibID))
	}
	return interceptor(ctx, in, info, handler)
}

func _AllTagLibApi_TagLibSave_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AllTagLibSaveReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AllTagLibApiServer).TagLibSave(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AllTagLibApi_TagLibSave_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AllTagLibApiServer).TagLibSave(ctx, req.(*AllTagLibSaveReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _AllTagLibApi_TagLibEnable_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(EnableReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AllTagLibApiServer).TagLibEnable(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AllTagLibApi_TagLibEnable_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AllTagLibApiServer).TagLibEnable(ctx, req.(*EnableReq))
	}
	return interceptor(ctx, in, info, handler)
}

// AllTagLibApi_ServiceDesc is the grpc.ServiceDesc for AllTagLibApi service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var AllTagLibApi_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "pb.AllTagLibApi",
	HandlerType: (*AllTagLibApiServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "AllTagOpts",
			Handler:    _AllTagLibApi_AllTagOpts_Handler,
		},
		{
			MethodName: "AllTagLibList",
			Handler:    _AllTagLibApi_AllTagLibList_Handler,
		},
		{
			MethodName: "AllTagLibInfo",
			Handler:    _AllTagLibApi_AllTagLibInfo_Handler,
		},
		{
			MethodName: "TagLibSave",
			Handler:    _AllTagLibApi_TagLibSave_Handler,
		},
		{
			MethodName: "TagLibEnable",
			Handler:    _AllTagLibApi_TagLibEnable_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "service.proto",
}

const (
	UserAssignTicketApi_UserAssignTicketAdd_FullMethodName = "/pb.UserAssignTicketApi/UserAssignTicketAdd"
	UserAssignTicketApi_GroupAdd_FullMethodName            = "/pb.UserAssignTicketApi/GroupAdd"
	UserAssignTicketApi_GroupInfo_FullMethodName           = "/pb.UserAssignTicketApi/GroupInfo"
	UserAssignTicketApi_GroupOpts_FullMethodName           = "/pb.UserAssignTicketApi/GroupOpts"
	UserAssignTicketApi_AllocGroupEnable_FullMethodName    = "/pb.UserAssignTicketApi/AllocGroupEnable"
)

// UserAssignTicketApiClient is the client API for UserAssignTicketApi service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type UserAssignTicketApiClient interface {
	// 分单配置 - 添加
	UserAssignTicketAdd(ctx context.Context, in *UserAssignTicketAddReq, opts ...grpc.CallOption) (*Empty, error)
	// 分单配置 - 列表
	GroupAdd(ctx context.Context, in *UserAssignTicketListReq, opts ...grpc.CallOption) (*UserAssignTicketListResp, error)
	// 分单配置 - 修改
	GroupInfo(ctx context.Context, in *UserAssignTicketEditReq, opts ...grpc.CallOption) (*Empty, error)
	// 分单配置 - 详情
	GroupOpts(ctx context.Context, in *UserAssignTicketInfoReq, opts ...grpc.CallOption) (*UserAssignTicketListResp_Detail, error)
	// 分单配置 - 分单逻辑
	AllocGroupEnable(ctx context.Context, in *UserAssignTicketDelReq, opts ...grpc.CallOption) (*Empty, error)
}

type userAssignTicketApiClient struct {
	cc grpc.ClientConnInterface
}

func NewUserAssignTicketApiClient(cc grpc.ClientConnInterface) UserAssignTicketApiClient {
	return &userAssignTicketApiClient{cc}
}

func (c *userAssignTicketApiClient) UserAssignTicketAdd(ctx context.Context, in *UserAssignTicketAddReq, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, UserAssignTicketApi_UserAssignTicketAdd_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userAssignTicketApiClient) GroupAdd(ctx context.Context, in *UserAssignTicketListReq, opts ...grpc.CallOption) (*UserAssignTicketListResp, error) {
	out := new(UserAssignTicketListResp)
	err := c.cc.Invoke(ctx, UserAssignTicketApi_GroupAdd_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userAssignTicketApiClient) GroupInfo(ctx context.Context, in *UserAssignTicketEditReq, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, UserAssignTicketApi_GroupInfo_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userAssignTicketApiClient) GroupOpts(ctx context.Context, in *UserAssignTicketInfoReq, opts ...grpc.CallOption) (*UserAssignTicketListResp_Detail, error) {
	out := new(UserAssignTicketListResp_Detail)
	err := c.cc.Invoke(ctx, UserAssignTicketApi_GroupOpts_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userAssignTicketApiClient) AllocGroupEnable(ctx context.Context, in *UserAssignTicketDelReq, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, UserAssignTicketApi_AllocGroupEnable_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// UserAssignTicketApiServer is the server API for UserAssignTicketApi service.
// All implementations must embed UnimplementedUserAssignTicketApiServer
// for forward compatibility
type UserAssignTicketApiServer interface {
	// 分单配置 - 添加
	UserAssignTicketAdd(context.Context, *UserAssignTicketAddReq) (*Empty, error)
	// 分单配置 - 列表
	GroupAdd(context.Context, *UserAssignTicketListReq) (*UserAssignTicketListResp, error)
	// 分单配置 - 修改
	GroupInfo(context.Context, *UserAssignTicketEditReq) (*Empty, error)
	// 分单配置 - 详情
	GroupOpts(context.Context, *UserAssignTicketInfoReq) (*UserAssignTicketListResp_Detail, error)
	// 分单配置 - 分单逻辑
	AllocGroupEnable(context.Context, *UserAssignTicketDelReq) (*Empty, error)
	mustEmbedUnimplementedUserAssignTicketApiServer()
}

// UnimplementedUserAssignTicketApiServer must be embedded to have forward compatible implementations.
type UnimplementedUserAssignTicketApiServer struct {
}

func (UnimplementedUserAssignTicketApiServer) UserAssignTicketAdd(context.Context, *UserAssignTicketAddReq) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UserAssignTicketAdd not implemented")
}
func (UnimplementedUserAssignTicketApiServer) GroupAdd(context.Context, *UserAssignTicketListReq) (*UserAssignTicketListResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GroupAdd not implemented")
}
func (UnimplementedUserAssignTicketApiServer) GroupInfo(context.Context, *UserAssignTicketEditReq) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GroupInfo not implemented")
}
func (UnimplementedUserAssignTicketApiServer) GroupOpts(context.Context, *UserAssignTicketInfoReq) (*UserAssignTicketListResp_Detail, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GroupOpts not implemented")
}
func (UnimplementedUserAssignTicketApiServer) AllocGroupEnable(context.Context, *UserAssignTicketDelReq) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AllocGroupEnable not implemented")
}
func (UnimplementedUserAssignTicketApiServer) mustEmbedUnimplementedUserAssignTicketApiServer() {}

// UnsafeUserAssignTicketApiServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to UserAssignTicketApiServer will
// result in compilation errors.
type UnsafeUserAssignTicketApiServer interface {
	mustEmbedUnimplementedUserAssignTicketApiServer()
}

func RegisterUserAssignTicketApiServer(s grpc.ServiceRegistrar, srv UserAssignTicketApiServer) {
	s.RegisterService(&UserAssignTicketApi_ServiceDesc, srv)
}

func _UserAssignTicketApi_UserAssignTicketAdd_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserAssignTicketAddReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserAssignTicketApiServer).UserAssignTicketAdd(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserAssignTicketApi_UserAssignTicketAdd_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserAssignTicketApiServer).UserAssignTicketAdd(ctx, req.(*UserAssignTicketAddReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserAssignTicketApi_GroupAdd_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserAssignTicketListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserAssignTicketApiServer).GroupAdd(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserAssignTicketApi_GroupAdd_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserAssignTicketApiServer).GroupAdd(ctx, req.(*UserAssignTicketListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserAssignTicketApi_GroupInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserAssignTicketEditReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserAssignTicketApiServer).GroupInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserAssignTicketApi_GroupInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserAssignTicketApiServer).GroupInfo(ctx, req.(*UserAssignTicketEditReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserAssignTicketApi_GroupOpts_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserAssignTicketInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserAssignTicketApiServer).GroupOpts(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserAssignTicketApi_GroupOpts_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserAssignTicketApiServer).GroupOpts(ctx, req.(*UserAssignTicketInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserAssignTicketApi_AllocGroupEnable_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserAssignTicketDelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserAssignTicketApiServer).AllocGroupEnable(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserAssignTicketApi_AllocGroupEnable_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserAssignTicketApiServer).AllocGroupEnable(ctx, req.(*UserAssignTicketDelReq))
	}
	return interceptor(ctx, in, info, handler)
}

// UserAssignTicketApi_ServiceDesc is the grpc.ServiceDesc for UserAssignTicketApi service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var UserAssignTicketApi_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "pb.UserAssignTicketApi",
	HandlerType: (*UserAssignTicketApiServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "UserAssignTicketAdd",
			Handler:    _UserAssignTicketApi_UserAssignTicketAdd_Handler,
		},
		{
			MethodName: "GroupAdd",
			Handler:    _UserAssignTicketApi_GroupAdd_Handler,
		},
		{
			MethodName: "GroupInfo",
			Handler:    _UserAssignTicketApi_GroupInfo_Handler,
		},
		{
			MethodName: "GroupOpts",
			Handler:    _UserAssignTicketApi_GroupOpts_Handler,
		},
		{
			MethodName: "AllocGroupEnable",
			Handler:    _UserAssignTicketApi_AllocGroupEnable_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "service.proto",
}

const (
	ChannelCatApi_ChannelCatTree_FullMethodName    = "/pb.ChannelCatApi/ChannelCatTree"
	ChannelCatApi_ChannelCatAdd_FullMethodName     = "/pb.ChannelCatApi/ChannelCatAdd"
	ChannelCatApi_DscChannelCatSave_FullMethodName = "/pb.ChannelCatApi/DscChannelCatSave"
	ChannelCatApi_DscChannelCatDel_FullMethodName  = "/pb.ChannelCatApi/DscChannelCatDel"
)

// ChannelCatApiClient is the client API for ChannelCatApi service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ChannelCatApiClient interface {
	// discord 分类 - 获取分类树
	ChannelCatTree(ctx context.Context, in *CatProjectReq, opts ...grpc.CallOption) (*ChannelCatTreeResp, error)
	// discord 分类 - 添加分类
	ChannelCatAdd(ctx context.Context, in *ChannelCatAddReq, opts ...grpc.CallOption) (*Empty, error)
	// discord 分类 - 修改分类
	DscChannelCatSave(ctx context.Context, in *ChannelCatSaveReq, opts ...grpc.CallOption) (*Empty, error)
	// discord 分类 - 删除分类
	DscChannelCatDel(ctx context.Context, in *ChannelCatIdReq, opts ...grpc.CallOption) (*Empty, error)
}

type channelCatApiClient struct {
	cc grpc.ClientConnInterface
}

func NewChannelCatApiClient(cc grpc.ClientConnInterface) ChannelCatApiClient {
	return &channelCatApiClient{cc}
}

func (c *channelCatApiClient) ChannelCatTree(ctx context.Context, in *CatProjectReq, opts ...grpc.CallOption) (*ChannelCatTreeResp, error) {
	out := new(ChannelCatTreeResp)
	err := c.cc.Invoke(ctx, ChannelCatApi_ChannelCatTree_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelCatApiClient) ChannelCatAdd(ctx context.Context, in *ChannelCatAddReq, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, ChannelCatApi_ChannelCatAdd_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelCatApiClient) DscChannelCatSave(ctx context.Context, in *ChannelCatSaveReq, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, ChannelCatApi_DscChannelCatSave_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *channelCatApiClient) DscChannelCatDel(ctx context.Context, in *ChannelCatIdReq, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, ChannelCatApi_DscChannelCatDel_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ChannelCatApiServer is the server API for ChannelCatApi service.
// All implementations must embed UnimplementedChannelCatApiServer
// for forward compatibility
type ChannelCatApiServer interface {
	// discord 分类 - 获取分类树
	ChannelCatTree(context.Context, *CatProjectReq) (*ChannelCatTreeResp, error)
	// discord 分类 - 添加分类
	ChannelCatAdd(context.Context, *ChannelCatAddReq) (*Empty, error)
	// discord 分类 - 修改分类
	DscChannelCatSave(context.Context, *ChannelCatSaveReq) (*Empty, error)
	// discord 分类 - 删除分类
	DscChannelCatDel(context.Context, *ChannelCatIdReq) (*Empty, error)
	mustEmbedUnimplementedChannelCatApiServer()
}

// UnimplementedChannelCatApiServer must be embedded to have forward compatible implementations.
type UnimplementedChannelCatApiServer struct {
}

func (UnimplementedChannelCatApiServer) ChannelCatTree(context.Context, *CatProjectReq) (*ChannelCatTreeResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ChannelCatTree not implemented")
}
func (UnimplementedChannelCatApiServer) ChannelCatAdd(context.Context, *ChannelCatAddReq) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ChannelCatAdd not implemented")
}
func (UnimplementedChannelCatApiServer) DscChannelCatSave(context.Context, *ChannelCatSaveReq) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DscChannelCatSave not implemented")
}
func (UnimplementedChannelCatApiServer) DscChannelCatDel(context.Context, *ChannelCatIdReq) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DscChannelCatDel not implemented")
}
func (UnimplementedChannelCatApiServer) mustEmbedUnimplementedChannelCatApiServer() {}

// UnsafeChannelCatApiServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ChannelCatApiServer will
// result in compilation errors.
type UnsafeChannelCatApiServer interface {
	mustEmbedUnimplementedChannelCatApiServer()
}

func RegisterChannelCatApiServer(s grpc.ServiceRegistrar, srv ChannelCatApiServer) {
	s.RegisterService(&ChannelCatApi_ServiceDesc, srv)
}

func _ChannelCatApi_ChannelCatTree_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CatProjectReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelCatApiServer).ChannelCatTree(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ChannelCatApi_ChannelCatTree_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelCatApiServer).ChannelCatTree(ctx, req.(*CatProjectReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelCatApi_ChannelCatAdd_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ChannelCatAddReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelCatApiServer).ChannelCatAdd(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ChannelCatApi_ChannelCatAdd_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelCatApiServer).ChannelCatAdd(ctx, req.(*ChannelCatAddReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelCatApi_DscChannelCatSave_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ChannelCatSaveReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelCatApiServer).DscChannelCatSave(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ChannelCatApi_DscChannelCatSave_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelCatApiServer).DscChannelCatSave(ctx, req.(*ChannelCatSaveReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ChannelCatApi_DscChannelCatDel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ChannelCatIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChannelCatApiServer).DscChannelCatDel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ChannelCatApi_DscChannelCatDel_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChannelCatApiServer).DscChannelCatDel(ctx, req.(*ChannelCatIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

// ChannelCatApi_ServiceDesc is the grpc.ServiceDesc for ChannelCatApi service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ChannelCatApi_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "pb.ChannelCatApi",
	HandlerType: (*ChannelCatApiServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ChannelCatTree",
			Handler:    _ChannelCatApi_ChannelCatTree_Handler,
		},
		{
			MethodName: "ChannelCatAdd",
			Handler:    _ChannelCatApi_ChannelCatAdd_Handler,
		},
		{
			MethodName: "DscChannelCatSave",
			Handler:    _ChannelCatApi_DscChannelCatSave_Handler,
		},
		{
			MethodName: "DscChannelCatDel",
			Handler:    _ChannelCatApi_DscChannelCatDel_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "service.proto",
}

const (
	OverTimeApi_OvertimeTplList_FullMethodName   = "/pb.OverTimeApi/OvertimeTplList"
	OverTimeApi_OvertimeTplAdd_FullMethodName    = "/pb.OverTimeApi/OvertimeTplAdd"
	OverTimeApi_OvertimeTplEdit_FullMethodName   = "/pb.OverTimeApi/OvertimeTplEdit"
	OverTimeApi_OvertimeTplDel_FullMethodName    = "/pb.OverTimeApi/OvertimeTplDel"
	OverTimeApi_OvertimeTplEnable_FullMethodName = "/pb.OverTimeApi/OvertimeTplEnable"
	OverTimeApi_OvertimeTplOpts_FullMethodName   = "/pb.OverTimeApi/OvertimeTplOpts"
)

// OverTimeApiClient is the client API for OverTimeApi service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type OverTimeApiClient interface {
	// 超时模版 - 列表
	OvertimeTplList(ctx context.Context, in *OverTimeTplListReq, opts ...grpc.CallOption) (*OverTimeTplListResp, error)
	// 超时模版 - 添加
	OvertimeTplAdd(ctx context.Context, in *OverTimeTplAddReq, opts ...grpc.CallOption) (*Empty, error)
	// 超时模版 - 编辑
	OvertimeTplEdit(ctx context.Context, in *OverTimeTplEditReq, opts ...grpc.CallOption) (*Empty, error)
	// 超时模版 - 删除
	OvertimeTplDel(ctx context.Context, in *OverTimeTplDelReq, opts ...grpc.CallOption) (*Empty, error)
	// 超时模版 - 启用/禁用
	OvertimeTplEnable(ctx context.Context, in *OverTimeTplEnableReq, opts ...grpc.CallOption) (*Empty, error)
	// 超时模版 - 编辑
	OvertimeTplOpts(ctx context.Context, in *OverTimeTplOptsReq, opts ...grpc.CallOption) (*OverTimeTplOptsRes, error)
}

type overTimeApiClient struct {
	cc grpc.ClientConnInterface
}

func NewOverTimeApiClient(cc grpc.ClientConnInterface) OverTimeApiClient {
	return &overTimeApiClient{cc}
}

func (c *overTimeApiClient) OvertimeTplList(ctx context.Context, in *OverTimeTplListReq, opts ...grpc.CallOption) (*OverTimeTplListResp, error) {
	out := new(OverTimeTplListResp)
	err := c.cc.Invoke(ctx, OverTimeApi_OvertimeTplList_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *overTimeApiClient) OvertimeTplAdd(ctx context.Context, in *OverTimeTplAddReq, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, OverTimeApi_OvertimeTplAdd_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *overTimeApiClient) OvertimeTplEdit(ctx context.Context, in *OverTimeTplEditReq, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, OverTimeApi_OvertimeTplEdit_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *overTimeApiClient) OvertimeTplDel(ctx context.Context, in *OverTimeTplDelReq, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, OverTimeApi_OvertimeTplDel_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *overTimeApiClient) OvertimeTplEnable(ctx context.Context, in *OverTimeTplEnableReq, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, OverTimeApi_OvertimeTplEnable_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *overTimeApiClient) OvertimeTplOpts(ctx context.Context, in *OverTimeTplOptsReq, opts ...grpc.CallOption) (*OverTimeTplOptsRes, error) {
	out := new(OverTimeTplOptsRes)
	err := c.cc.Invoke(ctx, OverTimeApi_OvertimeTplOpts_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// OverTimeApiServer is the server API for OverTimeApi service.
// All implementations must embed UnimplementedOverTimeApiServer
// for forward compatibility
type OverTimeApiServer interface {
	// 超时模版 - 列表
	OvertimeTplList(context.Context, *OverTimeTplListReq) (*OverTimeTplListResp, error)
	// 超时模版 - 添加
	OvertimeTplAdd(context.Context, *OverTimeTplAddReq) (*Empty, error)
	// 超时模版 - 编辑
	OvertimeTplEdit(context.Context, *OverTimeTplEditReq) (*Empty, error)
	// 超时模版 - 删除
	OvertimeTplDel(context.Context, *OverTimeTplDelReq) (*Empty, error)
	// 超时模版 - 启用/禁用
	OvertimeTplEnable(context.Context, *OverTimeTplEnableReq) (*Empty, error)
	// 超时模版 - 编辑
	OvertimeTplOpts(context.Context, *OverTimeTplOptsReq) (*OverTimeTplOptsRes, error)
	mustEmbedUnimplementedOverTimeApiServer()
}

// UnimplementedOverTimeApiServer must be embedded to have forward compatible implementations.
type UnimplementedOverTimeApiServer struct {
}

func (UnimplementedOverTimeApiServer) OvertimeTplList(context.Context, *OverTimeTplListReq) (*OverTimeTplListResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method OvertimeTplList not implemented")
}
func (UnimplementedOverTimeApiServer) OvertimeTplAdd(context.Context, *OverTimeTplAddReq) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method OvertimeTplAdd not implemented")
}
func (UnimplementedOverTimeApiServer) OvertimeTplEdit(context.Context, *OverTimeTplEditReq) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method OvertimeTplEdit not implemented")
}
func (UnimplementedOverTimeApiServer) OvertimeTplDel(context.Context, *OverTimeTplDelReq) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method OvertimeTplDel not implemented")
}
func (UnimplementedOverTimeApiServer) OvertimeTplEnable(context.Context, *OverTimeTplEnableReq) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method OvertimeTplEnable not implemented")
}
func (UnimplementedOverTimeApiServer) OvertimeTplOpts(context.Context, *OverTimeTplOptsReq) (*OverTimeTplOptsRes, error) {
	return nil, status.Errorf(codes.Unimplemented, "method OvertimeTplOpts not implemented")
}
func (UnimplementedOverTimeApiServer) mustEmbedUnimplementedOverTimeApiServer() {}

// UnsafeOverTimeApiServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to OverTimeApiServer will
// result in compilation errors.
type UnsafeOverTimeApiServer interface {
	mustEmbedUnimplementedOverTimeApiServer()
}

func RegisterOverTimeApiServer(s grpc.ServiceRegistrar, srv OverTimeApiServer) {
	s.RegisterService(&OverTimeApi_ServiceDesc, srv)
}

func _OverTimeApi_OvertimeTplList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(OverTimeTplListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OverTimeApiServer).OvertimeTplList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OverTimeApi_OvertimeTplList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OverTimeApiServer).OvertimeTplList(ctx, req.(*OverTimeTplListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _OverTimeApi_OvertimeTplAdd_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(OverTimeTplAddReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OverTimeApiServer).OvertimeTplAdd(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OverTimeApi_OvertimeTplAdd_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OverTimeApiServer).OvertimeTplAdd(ctx, req.(*OverTimeTplAddReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _OverTimeApi_OvertimeTplEdit_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(OverTimeTplEditReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OverTimeApiServer).OvertimeTplEdit(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OverTimeApi_OvertimeTplEdit_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OverTimeApiServer).OvertimeTplEdit(ctx, req.(*OverTimeTplEditReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _OverTimeApi_OvertimeTplDel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(OverTimeTplDelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OverTimeApiServer).OvertimeTplDel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OverTimeApi_OvertimeTplDel_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OverTimeApiServer).OvertimeTplDel(ctx, req.(*OverTimeTplDelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _OverTimeApi_OvertimeTplEnable_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(OverTimeTplEnableReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OverTimeApiServer).OvertimeTplEnable(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OverTimeApi_OvertimeTplEnable_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OverTimeApiServer).OvertimeTplEnable(ctx, req.(*OverTimeTplEnableReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _OverTimeApi_OvertimeTplOpts_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(OverTimeTplOptsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(OverTimeApiServer).OvertimeTplOpts(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: OverTimeApi_OvertimeTplOpts_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(OverTimeApiServer).OvertimeTplOpts(ctx, req.(*OverTimeTplOptsReq))
	}
	return interceptor(ctx, in, info, handler)
}

// OverTimeApi_ServiceDesc is the grpc.ServiceDesc for OverTimeApi service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var OverTimeApi_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "pb.OverTimeApi",
	HandlerType: (*OverTimeApiServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "OvertimeTplList",
			Handler:    _OverTimeApi_OvertimeTplList_Handler,
		},
		{
			MethodName: "OvertimeTplAdd",
			Handler:    _OverTimeApi_OvertimeTplAdd_Handler,
		},
		{
			MethodName: "OvertimeTplEdit",
			Handler:    _OverTimeApi_OvertimeTplEdit_Handler,
		},
		{
			MethodName: "OvertimeTplDel",
			Handler:    _OverTimeApi_OvertimeTplDel_Handler,
		},
		{
			MethodName: "OvertimeTplEnable",
			Handler:    _OverTimeApi_OvertimeTplEnable_Handler,
		},
		{
			MethodName: "OvertimeTplOpts",
			Handler:    _OverTimeApi_OvertimeTplOpts_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "service.proto",
}

const (
	ModuleCatApi_ModuleCatAdd_FullMethodName  = "/pb.ModuleCatApi/ModuleCatAdd"
	ModuleCatApi_ModuleCatSave_FullMethodName = "/pb.ModuleCatApi/ModuleCatSave"
	ModuleCatApi_ModuleCatDel_FullMethodName  = "/pb.ModuleCatApi/ModuleCatDel"
	ModuleCatApi_ModuleCatTree_FullMethodName = "/pb.ModuleCatApi/ModuleCatTree"
)

// ModuleCatApiClient is the client API for ModuleCatApi service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ModuleCatApiClient interface {
	// 模版分类 - 添加分类
	ModuleCatAdd(ctx context.Context, in *ModuleCatAddReq, opts ...grpc.CallOption) (*Empty, error)
	// 模版分类 - 修改分类
	ModuleCatSave(ctx context.Context, in *ModuleCatSaveReq, opts ...grpc.CallOption) (*Empty, error)
	// 模版分类 - 删除分类
	ModuleCatDel(ctx context.Context, in *CatIdReq, opts ...grpc.CallOption) (*Empty, error)
	// 模版分类 - 返回分类树
	ModuleCatTree(ctx context.Context, in *ModuleCatTreeReq, opts ...grpc.CallOption) (*Empty, error)
}

type moduleCatApiClient struct {
	cc grpc.ClientConnInterface
}

func NewModuleCatApiClient(cc grpc.ClientConnInterface) ModuleCatApiClient {
	return &moduleCatApiClient{cc}
}

func (c *moduleCatApiClient) ModuleCatAdd(ctx context.Context, in *ModuleCatAddReq, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, ModuleCatApi_ModuleCatAdd_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *moduleCatApiClient) ModuleCatSave(ctx context.Context, in *ModuleCatSaveReq, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, ModuleCatApi_ModuleCatSave_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *moduleCatApiClient) ModuleCatDel(ctx context.Context, in *CatIdReq, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, ModuleCatApi_ModuleCatDel_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *moduleCatApiClient) ModuleCatTree(ctx context.Context, in *ModuleCatTreeReq, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, ModuleCatApi_ModuleCatTree_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ModuleCatApiServer is the server API for ModuleCatApi service.
// All implementations must embed UnimplementedModuleCatApiServer
// for forward compatibility
type ModuleCatApiServer interface {
	// 模版分类 - 添加分类
	ModuleCatAdd(context.Context, *ModuleCatAddReq) (*Empty, error)
	// 模版分类 - 修改分类
	ModuleCatSave(context.Context, *ModuleCatSaveReq) (*Empty, error)
	// 模版分类 - 删除分类
	ModuleCatDel(context.Context, *CatIdReq) (*Empty, error)
	// 模版分类 - 返回分类树
	ModuleCatTree(context.Context, *ModuleCatTreeReq) (*Empty, error)
	mustEmbedUnimplementedModuleCatApiServer()
}

// UnimplementedModuleCatApiServer must be embedded to have forward compatible implementations.
type UnimplementedModuleCatApiServer struct {
}

func (UnimplementedModuleCatApiServer) ModuleCatAdd(context.Context, *ModuleCatAddReq) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ModuleCatAdd not implemented")
}
func (UnimplementedModuleCatApiServer) ModuleCatSave(context.Context, *ModuleCatSaveReq) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ModuleCatSave not implemented")
}
func (UnimplementedModuleCatApiServer) ModuleCatDel(context.Context, *CatIdReq) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ModuleCatDel not implemented")
}
func (UnimplementedModuleCatApiServer) ModuleCatTree(context.Context, *ModuleCatTreeReq) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ModuleCatTree not implemented")
}
func (UnimplementedModuleCatApiServer) mustEmbedUnimplementedModuleCatApiServer() {}

// UnsafeModuleCatApiServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ModuleCatApiServer will
// result in compilation errors.
type UnsafeModuleCatApiServer interface {
	mustEmbedUnimplementedModuleCatApiServer()
}

func RegisterModuleCatApiServer(s grpc.ServiceRegistrar, srv ModuleCatApiServer) {
	s.RegisterService(&ModuleCatApi_ServiceDesc, srv)
}

func _ModuleCatApi_ModuleCatAdd_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ModuleCatAddReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ModuleCatApiServer).ModuleCatAdd(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ModuleCatApi_ModuleCatAdd_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ModuleCatApiServer).ModuleCatAdd(ctx, req.(*ModuleCatAddReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ModuleCatApi_ModuleCatSave_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ModuleCatSaveReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ModuleCatApiServer).ModuleCatSave(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ModuleCatApi_ModuleCatSave_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ModuleCatApiServer).ModuleCatSave(ctx, req.(*ModuleCatSaveReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ModuleCatApi_ModuleCatDel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CatIdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ModuleCatApiServer).ModuleCatDel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ModuleCatApi_ModuleCatDel_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ModuleCatApiServer).ModuleCatDel(ctx, req.(*CatIdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ModuleCatApi_ModuleCatTree_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ModuleCatTreeReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ModuleCatApiServer).ModuleCatTree(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ModuleCatApi_ModuleCatTree_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ModuleCatApiServer).ModuleCatTree(ctx, req.(*ModuleCatTreeReq))
	}
	return interceptor(ctx, in, info, handler)
}

// ModuleCatApi_ServiceDesc is the grpc.ServiceDesc for ModuleCatApi service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ModuleCatApi_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "pb.ModuleCatApi",
	HandlerType: (*ModuleCatApiServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ModuleCatAdd",
			Handler:    _ModuleCatApi_ModuleCatAdd_Handler,
		},
		{
			MethodName: "ModuleCatSave",
			Handler:    _ModuleCatApi_ModuleCatSave_Handler,
		},
		{
			MethodName: "ModuleCatDel",
			Handler:    _ModuleCatApi_ModuleCatDel_Handler,
		},
		{
			MethodName: "ModuleCatTree",
			Handler:    _ModuleCatApi_ModuleCatTree_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "service.proto",
}

const (
	TeamConfigApi_TeamConfigList_FullMethodName = "/pb.TeamConfigApi/TeamConfigList"
)

// TeamConfigApiClient is the client API for TeamConfigApi service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type TeamConfigApiClient interface {
	// 团队配置list
	TeamConfigList(ctx context.Context, in *TeamConfigListReq, opts ...grpc.CallOption) (*TeamConfigListResp, error)
}

type teamConfigApiClient struct {
	cc grpc.ClientConnInterface
}

func NewTeamConfigApiClient(cc grpc.ClientConnInterface) TeamConfigApiClient {
	return &teamConfigApiClient{cc}
}

func (c *teamConfigApiClient) TeamConfigList(ctx context.Context, in *TeamConfigListReq, opts ...grpc.CallOption) (*TeamConfigListResp, error) {
	out := new(TeamConfigListResp)
	err := c.cc.Invoke(ctx, TeamConfigApi_TeamConfigList_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// TeamConfigApiServer is the server API for TeamConfigApi service.
// All implementations must embed UnimplementedTeamConfigApiServer
// for forward compatibility
type TeamConfigApiServer interface {
	// 团队配置list
	TeamConfigList(context.Context, *TeamConfigListReq) (*TeamConfigListResp, error)
	mustEmbedUnimplementedTeamConfigApiServer()
}

// UnimplementedTeamConfigApiServer must be embedded to have forward compatible implementations.
type UnimplementedTeamConfigApiServer struct {
}

func (UnimplementedTeamConfigApiServer) TeamConfigList(context.Context, *TeamConfigListReq) (*TeamConfigListResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TeamConfigList not implemented")
}
func (UnimplementedTeamConfigApiServer) mustEmbedUnimplementedTeamConfigApiServer() {}

// UnsafeTeamConfigApiServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to TeamConfigApiServer will
// result in compilation errors.
type UnsafeTeamConfigApiServer interface {
	mustEmbedUnimplementedTeamConfigApiServer()
}

func RegisterTeamConfigApiServer(s grpc.ServiceRegistrar, srv TeamConfigApiServer) {
	s.RegisterService(&TeamConfigApi_ServiceDesc, srv)
}

func _TeamConfigApi_TeamConfigList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TeamConfigListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TeamConfigApiServer).TeamConfigList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TeamConfigApi_TeamConfigList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TeamConfigApiServer).TeamConfigList(ctx, req.(*TeamConfigListReq))
	}
	return interceptor(ctx, in, info, handler)
}

// TeamConfigApi_ServiceDesc is the grpc.ServiceDesc for TeamConfigApi service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var TeamConfigApi_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "pb.TeamConfigApi",
	HandlerType: (*TeamConfigApiServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "TeamConfigList",
			Handler:    _TeamConfigApi_TeamConfigList_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "service.proto",
}

const (
	TicketModuleApi_TicketModuleSave_FullMethodName = "/pb.TicketModuleApi/TicketModuleSave"
	TicketModuleApi_TicketModuleEdit_FullMethodName = "/pb.TicketModuleApi/TicketModuleEdit"
	TicketModuleApi_TicketModuleDel_FullMethodName  = "/pb.TicketModuleApi/TicketModuleDel"
	TicketModuleApi_TicketModuleList_FullMethodName = "/pb.TicketModuleApi/TicketModuleList"
)

// TicketModuleApiClient is the client API for TicketModuleApi service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type TicketModuleApiClient interface {
	// 工单模版 - 添加模版
	TicketModuleSave(ctx context.Context, in *ModuleSaveReq, opts ...grpc.CallOption) (*Empty, error)
	// 工单模版 - 修改模版
	TicketModuleEdit(ctx context.Context, in *ModuleEditReq, opts ...grpc.CallOption) (*Empty, error)
	// 工单模版 - 删除模版
	TicketModuleDel(ctx context.Context, in *ModuleDelReq, opts ...grpc.CallOption) (*Empty, error)
	// 工单模版 - 返回列表
	TicketModuleList(ctx context.Context, in *ModuleListReq, opts ...grpc.CallOption) (*ModuleListResp, error)
}

type ticketModuleApiClient struct {
	cc grpc.ClientConnInterface
}

func NewTicketModuleApiClient(cc grpc.ClientConnInterface) TicketModuleApiClient {
	return &ticketModuleApiClient{cc}
}

func (c *ticketModuleApiClient) TicketModuleSave(ctx context.Context, in *ModuleSaveReq, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, TicketModuleApi_TicketModuleSave_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ticketModuleApiClient) TicketModuleEdit(ctx context.Context, in *ModuleEditReq, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, TicketModuleApi_TicketModuleEdit_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ticketModuleApiClient) TicketModuleDel(ctx context.Context, in *ModuleDelReq, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, TicketModuleApi_TicketModuleDel_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ticketModuleApiClient) TicketModuleList(ctx context.Context, in *ModuleListReq, opts ...grpc.CallOption) (*ModuleListResp, error) {
	out := new(ModuleListResp)
	err := c.cc.Invoke(ctx, TicketModuleApi_TicketModuleList_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// TicketModuleApiServer is the server API for TicketModuleApi service.
// All implementations must embed UnimplementedTicketModuleApiServer
// for forward compatibility
type TicketModuleApiServer interface {
	// 工单模版 - 添加模版
	TicketModuleSave(context.Context, *ModuleSaveReq) (*Empty, error)
	// 工单模版 - 修改模版
	TicketModuleEdit(context.Context, *ModuleEditReq) (*Empty, error)
	// 工单模版 - 删除模版
	TicketModuleDel(context.Context, *ModuleDelReq) (*Empty, error)
	// 工单模版 - 返回列表
	TicketModuleList(context.Context, *ModuleListReq) (*ModuleListResp, error)
	mustEmbedUnimplementedTicketModuleApiServer()
}

// UnimplementedTicketModuleApiServer must be embedded to have forward compatible implementations.
type UnimplementedTicketModuleApiServer struct {
}

func (UnimplementedTicketModuleApiServer) TicketModuleSave(context.Context, *ModuleSaveReq) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TicketModuleSave not implemented")
}
func (UnimplementedTicketModuleApiServer) TicketModuleEdit(context.Context, *ModuleEditReq) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TicketModuleEdit not implemented")
}
func (UnimplementedTicketModuleApiServer) TicketModuleDel(context.Context, *ModuleDelReq) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TicketModuleDel not implemented")
}
func (UnimplementedTicketModuleApiServer) TicketModuleList(context.Context, *ModuleListReq) (*ModuleListResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TicketModuleList not implemented")
}
func (UnimplementedTicketModuleApiServer) mustEmbedUnimplementedTicketModuleApiServer() {}

// UnsafeTicketModuleApiServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to TicketModuleApiServer will
// result in compilation errors.
type UnsafeTicketModuleApiServer interface {
	mustEmbedUnimplementedTicketModuleApiServer()
}

func RegisterTicketModuleApiServer(s grpc.ServiceRegistrar, srv TicketModuleApiServer) {
	s.RegisterService(&TicketModuleApi_ServiceDesc, srv)
}

func _TicketModuleApi_TicketModuleSave_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ModuleSaveReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TicketModuleApiServer).TicketModuleSave(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TicketModuleApi_TicketModuleSave_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TicketModuleApiServer).TicketModuleSave(ctx, req.(*ModuleSaveReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _TicketModuleApi_TicketModuleEdit_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ModuleEditReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TicketModuleApiServer).TicketModuleEdit(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TicketModuleApi_TicketModuleEdit_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TicketModuleApiServer).TicketModuleEdit(ctx, req.(*ModuleEditReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _TicketModuleApi_TicketModuleDel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ModuleDelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TicketModuleApiServer).TicketModuleDel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TicketModuleApi_TicketModuleDel_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TicketModuleApiServer).TicketModuleDel(ctx, req.(*ModuleDelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _TicketModuleApi_TicketModuleList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ModuleListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TicketModuleApiServer).TicketModuleList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TicketModuleApi_TicketModuleList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TicketModuleApiServer).TicketModuleList(ctx, req.(*ModuleListReq))
	}
	return interceptor(ctx, in, info, handler)
}

// TicketModuleApi_ServiceDesc is the grpc.ServiceDesc for TicketModuleApi service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var TicketModuleApi_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "pb.TicketModuleApi",
	HandlerType: (*TicketModuleApiServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "TicketModuleSave",
			Handler:    _TicketModuleApi_TicketModuleSave_Handler,
		},
		{
			MethodName: "TicketModuleEdit",
			Handler:    _TicketModuleApi_TicketModuleEdit_Handler,
		},
		{
			MethodName: "TicketModuleDel",
			Handler:    _TicketModuleApi_TicketModuleDel_Handler,
		},
		{
			MethodName: "TicketModuleList",
			Handler:    _TicketModuleApi_TicketModuleList_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "service.proto",
}

const (
	TicketTabApi_TicketTabEdit_FullMethodName = "/pb.TicketTabApi/TicketTabEdit"
)

// TicketTabApiClient is the client API for TicketTabApi service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type TicketTabApiClient interface {
	// 工单tab编辑
	TicketTabEdit(ctx context.Context, in *TicketTabEditReq, opts ...grpc.CallOption) (*Empty, error)
}

type ticketTabApiClient struct {
	cc grpc.ClientConnInterface
}

func NewTicketTabApiClient(cc grpc.ClientConnInterface) TicketTabApiClient {
	return &ticketTabApiClient{cc}
}

func (c *ticketTabApiClient) TicketTabEdit(ctx context.Context, in *TicketTabEditReq, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, TicketTabApi_TicketTabEdit_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// TicketTabApiServer is the server API for TicketTabApi service.
// All implementations must embed UnimplementedTicketTabApiServer
// for forward compatibility
type TicketTabApiServer interface {
	// 工单tab编辑
	TicketTabEdit(context.Context, *TicketTabEditReq) (*Empty, error)
	mustEmbedUnimplementedTicketTabApiServer()
}

// UnimplementedTicketTabApiServer must be embedded to have forward compatible implementations.
type UnimplementedTicketTabApiServer struct {
}

func (UnimplementedTicketTabApiServer) TicketTabEdit(context.Context, *TicketTabEditReq) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TicketTabEdit not implemented")
}
func (UnimplementedTicketTabApiServer) mustEmbedUnimplementedTicketTabApiServer() {}

// UnsafeTicketTabApiServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to TicketTabApiServer will
// result in compilation errors.
type UnsafeTicketTabApiServer interface {
	mustEmbedUnimplementedTicketTabApiServer()
}

func RegisterTicketTabApiServer(s grpc.ServiceRegistrar, srv TicketTabApiServer) {
	s.RegisterService(&TicketTabApi_ServiceDesc, srv)
}

func _TicketTabApi_TicketTabEdit_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TicketTabEditReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TicketTabApiServer).TicketTabEdit(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TicketTabApi_TicketTabEdit_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TicketTabApiServer).TicketTabEdit(ctx, req.(*TicketTabEditReq))
	}
	return interceptor(ctx, in, info, handler)
}

// TicketTabApi_ServiceDesc is the grpc.ServiceDesc for TicketTabApi service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var TicketTabApi_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "pb.TicketTabApi",
	HandlerType: (*TicketTabApiServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "TicketTabEdit",
			Handler:    _TicketTabApi_TicketTabEdit_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "service.proto",
}

const (
	QuestionApi_QuestionSave_FullMethodName        = "/pb.QuestionApi/QuestionSave"
	QuestionApi_QuestionList_FullMethodName        = "/pb.QuestionApi/QuestionList"
	QuestionApi_QuestionExport_FullMethodName      = "/pb.QuestionApi/QuestionExport"
	QuestionApi_QuestionDel_FullMethodName         = "/pb.QuestionApi/QuestionDel"
	QuestionApi_QuestionBatchImport_FullMethodName = "/pb.QuestionApi/QuestionBatchImport"
	QuestionApi_QuestionTraining_FullMethodName    = "/pb.QuestionApi/QuestionTraining"
	QuestionApi_QuestionTrainLog_FullMethodName    = "/pb.QuestionApi/QuestionTrainLog"
)

// QuestionApiClient is the client API for QuestionApi service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type QuestionApiClient interface {
	// 工单知识库保存
	QuestionSave(ctx context.Context, in *QuestionSaveReq, opts ...grpc.CallOption) (*Empty, error)
	// 工单知识库列表
	QuestionList(ctx context.Context, in *QuestionListReq, opts ...grpc.CallOption) (*QuestionListResp, error)
	// 工单知识库下载
	QuestionExport(ctx context.Context, in *QuestionListReq, opts ...grpc.CallOption) (*Empty, error)
	// 工单知识库删除
	QuestionDel(ctx context.Context, in *QuestionDelReq, opts ...grpc.CallOption) (*Empty, error)
	// 工单知识库批量导入
	QuestionBatchImport(ctx context.Context, in *QuestionBatchImportReq, opts ...grpc.CallOption) (*Empty, error)
	// 工单知识库训练
	QuestionTraining(ctx context.Context, in *QuestionTrainingReq, opts ...grpc.CallOption) (*Empty, error)
	// 工单知识库训练结果页面
	QuestionTrainLog(ctx context.Context, in *QuestionTrainLogReq, opts ...grpc.CallOption) (*QuestionTrainLogResp, error)
}

type questionApiClient struct {
	cc grpc.ClientConnInterface
}

func NewQuestionApiClient(cc grpc.ClientConnInterface) QuestionApiClient {
	return &questionApiClient{cc}
}

func (c *questionApiClient) QuestionSave(ctx context.Context, in *QuestionSaveReq, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, QuestionApi_QuestionSave_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *questionApiClient) QuestionList(ctx context.Context, in *QuestionListReq, opts ...grpc.CallOption) (*QuestionListResp, error) {
	out := new(QuestionListResp)
	err := c.cc.Invoke(ctx, QuestionApi_QuestionList_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *questionApiClient) QuestionExport(ctx context.Context, in *QuestionListReq, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, QuestionApi_QuestionExport_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *questionApiClient) QuestionDel(ctx context.Context, in *QuestionDelReq, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, QuestionApi_QuestionDel_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *questionApiClient) QuestionBatchImport(ctx context.Context, in *QuestionBatchImportReq, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, QuestionApi_QuestionBatchImport_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *questionApiClient) QuestionTraining(ctx context.Context, in *QuestionTrainingReq, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, QuestionApi_QuestionTraining_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *questionApiClient) QuestionTrainLog(ctx context.Context, in *QuestionTrainLogReq, opts ...grpc.CallOption) (*QuestionTrainLogResp, error) {
	out := new(QuestionTrainLogResp)
	err := c.cc.Invoke(ctx, QuestionApi_QuestionTrainLog_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// QuestionApiServer is the server API for QuestionApi service.
// All implementations must embed UnimplementedQuestionApiServer
// for forward compatibility
type QuestionApiServer interface {
	// 工单知识库保存
	QuestionSave(context.Context, *QuestionSaveReq) (*Empty, error)
	// 工单知识库列表
	QuestionList(context.Context, *QuestionListReq) (*QuestionListResp, error)
	// 工单知识库下载
	QuestionExport(context.Context, *QuestionListReq) (*Empty, error)
	// 工单知识库删除
	QuestionDel(context.Context, *QuestionDelReq) (*Empty, error)
	// 工单知识库批量导入
	QuestionBatchImport(context.Context, *QuestionBatchImportReq) (*Empty, error)
	// 工单知识库训练
	QuestionTraining(context.Context, *QuestionTrainingReq) (*Empty, error)
	// 工单知识库训练结果页面
	QuestionTrainLog(context.Context, *QuestionTrainLogReq) (*QuestionTrainLogResp, error)
	mustEmbedUnimplementedQuestionApiServer()
}

// UnimplementedQuestionApiServer must be embedded to have forward compatible implementations.
type UnimplementedQuestionApiServer struct {
}

func (UnimplementedQuestionApiServer) QuestionSave(context.Context, *QuestionSaveReq) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QuestionSave not implemented")
}
func (UnimplementedQuestionApiServer) QuestionList(context.Context, *QuestionListReq) (*QuestionListResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QuestionList not implemented")
}
func (UnimplementedQuestionApiServer) QuestionExport(context.Context, *QuestionListReq) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QuestionExport not implemented")
}
func (UnimplementedQuestionApiServer) QuestionDel(context.Context, *QuestionDelReq) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QuestionDel not implemented")
}
func (UnimplementedQuestionApiServer) QuestionBatchImport(context.Context, *QuestionBatchImportReq) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QuestionBatchImport not implemented")
}
func (UnimplementedQuestionApiServer) QuestionTraining(context.Context, *QuestionTrainingReq) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QuestionTraining not implemented")
}
func (UnimplementedQuestionApiServer) QuestionTrainLog(context.Context, *QuestionTrainLogReq) (*QuestionTrainLogResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method QuestionTrainLog not implemented")
}
func (UnimplementedQuestionApiServer) mustEmbedUnimplementedQuestionApiServer() {}

// UnsafeQuestionApiServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to QuestionApiServer will
// result in compilation errors.
type UnsafeQuestionApiServer interface {
	mustEmbedUnimplementedQuestionApiServer()
}

func RegisterQuestionApiServer(s grpc.ServiceRegistrar, srv QuestionApiServer) {
	s.RegisterService(&QuestionApi_ServiceDesc, srv)
}

func _QuestionApi_QuestionSave_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QuestionSaveReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(QuestionApiServer).QuestionSave(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: QuestionApi_QuestionSave_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(QuestionApiServer).QuestionSave(ctx, req.(*QuestionSaveReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _QuestionApi_QuestionList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QuestionListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(QuestionApiServer).QuestionList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: QuestionApi_QuestionList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(QuestionApiServer).QuestionList(ctx, req.(*QuestionListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _QuestionApi_QuestionExport_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QuestionListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(QuestionApiServer).QuestionExport(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: QuestionApi_QuestionExport_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(QuestionApiServer).QuestionExport(ctx, req.(*QuestionListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _QuestionApi_QuestionDel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QuestionDelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(QuestionApiServer).QuestionDel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: QuestionApi_QuestionDel_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(QuestionApiServer).QuestionDel(ctx, req.(*QuestionDelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _QuestionApi_QuestionBatchImport_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QuestionBatchImportReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(QuestionApiServer).QuestionBatchImport(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: QuestionApi_QuestionBatchImport_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(QuestionApiServer).QuestionBatchImport(ctx, req.(*QuestionBatchImportReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _QuestionApi_QuestionTraining_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QuestionTrainingReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(QuestionApiServer).QuestionTraining(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: QuestionApi_QuestionTraining_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(QuestionApiServer).QuestionTraining(ctx, req.(*QuestionTrainingReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _QuestionApi_QuestionTrainLog_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(QuestionTrainLogReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(QuestionApiServer).QuestionTrainLog(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: QuestionApi_QuestionTrainLog_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(QuestionApiServer).QuestionTrainLog(ctx, req.(*QuestionTrainLogReq))
	}
	return interceptor(ctx, in, info, handler)
}

// QuestionApi_ServiceDesc is the grpc.ServiceDesc for QuestionApi service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var QuestionApi_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "pb.QuestionApi",
	HandlerType: (*QuestionApiServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "QuestionSave",
			Handler:    _QuestionApi_QuestionSave_Handler,
		},
		{
			MethodName: "QuestionList",
			Handler:    _QuestionApi_QuestionList_Handler,
		},
		{
			MethodName: "QuestionExport",
			Handler:    _QuestionApi_QuestionExport_Handler,
		},
		{
			MethodName: "QuestionDel",
			Handler:    _QuestionApi_QuestionDel_Handler,
		},
		{
			MethodName: "QuestionBatchImport",
			Handler:    _QuestionApi_QuestionBatchImport_Handler,
		},
		{
			MethodName: "QuestionTraining",
			Handler:    _QuestionApi_QuestionTraining_Handler,
		},
		{
			MethodName: "QuestionTrainLog",
			Handler:    _QuestionApi_QuestionTrainLog_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "service.proto",
}

const (
	TicketStrategyApi_TicketStrategySave_FullMethodName   = "/pb.TicketStrategyApi/TicketStrategySave"
	TicketStrategyApi_TicketStrategyDel_FullMethodName    = "/pb.TicketStrategyApi/TicketStrategyDel"
	TicketStrategyApi_TicketStrategyEnable_FullMethodName = "/pb.TicketStrategyApi/TicketStrategyEnable"
	TicketStrategyApi_TicketStrategyList_FullMethodName   = "/pb.TicketStrategyApi/TicketStrategyList"
)

// TicketStrategyApiClient is the client API for TicketStrategyApi service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type TicketStrategyApiClient interface {
	// 工单策略保存
	TicketStrategySave(ctx context.Context, in *StrategyAddReq, opts ...grpc.CallOption) (*Empty, error)
	// 工单策略删除
	TicketStrategyDel(ctx context.Context, in *StrategyDelReq, opts ...grpc.CallOption) (*Empty, error)
	// 工单策略禁/启用
	TicketStrategyEnable(ctx context.Context, in *StrategyEnabelReq, opts ...grpc.CallOption) (*Empty, error)
	// 工单策略列表
	TicketStrategyList(ctx context.Context, in *StrategyListReq, opts ...grpc.CallOption) (*StrategyListResp, error)
}

type ticketStrategyApiClient struct {
	cc grpc.ClientConnInterface
}

func NewTicketStrategyApiClient(cc grpc.ClientConnInterface) TicketStrategyApiClient {
	return &ticketStrategyApiClient{cc}
}

func (c *ticketStrategyApiClient) TicketStrategySave(ctx context.Context, in *StrategyAddReq, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, TicketStrategyApi_TicketStrategySave_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ticketStrategyApiClient) TicketStrategyDel(ctx context.Context, in *StrategyDelReq, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, TicketStrategyApi_TicketStrategyDel_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ticketStrategyApiClient) TicketStrategyEnable(ctx context.Context, in *StrategyEnabelReq, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, TicketStrategyApi_TicketStrategyEnable_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *ticketStrategyApiClient) TicketStrategyList(ctx context.Context, in *StrategyListReq, opts ...grpc.CallOption) (*StrategyListResp, error) {
	out := new(StrategyListResp)
	err := c.cc.Invoke(ctx, TicketStrategyApi_TicketStrategyList_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// TicketStrategyApiServer is the server API for TicketStrategyApi service.
// All implementations must embed UnimplementedTicketStrategyApiServer
// for forward compatibility
type TicketStrategyApiServer interface {
	// 工单策略保存
	TicketStrategySave(context.Context, *StrategyAddReq) (*Empty, error)
	// 工单策略删除
	TicketStrategyDel(context.Context, *StrategyDelReq) (*Empty, error)
	// 工单策略禁/启用
	TicketStrategyEnable(context.Context, *StrategyEnabelReq) (*Empty, error)
	// 工单策略列表
	TicketStrategyList(context.Context, *StrategyListReq) (*StrategyListResp, error)
	mustEmbedUnimplementedTicketStrategyApiServer()
}

// UnimplementedTicketStrategyApiServer must be embedded to have forward compatible implementations.
type UnimplementedTicketStrategyApiServer struct {
}

func (UnimplementedTicketStrategyApiServer) TicketStrategySave(context.Context, *StrategyAddReq) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TicketStrategySave not implemented")
}
func (UnimplementedTicketStrategyApiServer) TicketStrategyDel(context.Context, *StrategyDelReq) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TicketStrategyDel not implemented")
}
func (UnimplementedTicketStrategyApiServer) TicketStrategyEnable(context.Context, *StrategyEnabelReq) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TicketStrategyEnable not implemented")
}
func (UnimplementedTicketStrategyApiServer) TicketStrategyList(context.Context, *StrategyListReq) (*StrategyListResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TicketStrategyList not implemented")
}
func (UnimplementedTicketStrategyApiServer) mustEmbedUnimplementedTicketStrategyApiServer() {}

// UnsafeTicketStrategyApiServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to TicketStrategyApiServer will
// result in compilation errors.
type UnsafeTicketStrategyApiServer interface {
	mustEmbedUnimplementedTicketStrategyApiServer()
}

func RegisterTicketStrategyApiServer(s grpc.ServiceRegistrar, srv TicketStrategyApiServer) {
	s.RegisterService(&TicketStrategyApi_ServiceDesc, srv)
}

func _TicketStrategyApi_TicketStrategySave_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StrategyAddReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TicketStrategyApiServer).TicketStrategySave(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TicketStrategyApi_TicketStrategySave_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TicketStrategyApiServer).TicketStrategySave(ctx, req.(*StrategyAddReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _TicketStrategyApi_TicketStrategyDel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StrategyDelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TicketStrategyApiServer).TicketStrategyDel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TicketStrategyApi_TicketStrategyDel_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TicketStrategyApiServer).TicketStrategyDel(ctx, req.(*StrategyDelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _TicketStrategyApi_TicketStrategyEnable_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StrategyEnabelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TicketStrategyApiServer).TicketStrategyEnable(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TicketStrategyApi_TicketStrategyEnable_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TicketStrategyApiServer).TicketStrategyEnable(ctx, req.(*StrategyEnabelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _TicketStrategyApi_TicketStrategyList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StrategyListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TicketStrategyApiServer).TicketStrategyList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TicketStrategyApi_TicketStrategyList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TicketStrategyApiServer).TicketStrategyList(ctx, req.(*StrategyListReq))
	}
	return interceptor(ctx, in, info, handler)
}

// TicketStrategyApi_ServiceDesc is the grpc.ServiceDesc for TicketStrategyApi service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var TicketStrategyApi_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "pb.TicketStrategyApi",
	HandlerType: (*TicketStrategyApiServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "TicketStrategySave",
			Handler:    _TicketStrategyApi_TicketStrategySave_Handler,
		},
		{
			MethodName: "TicketStrategyDel",
			Handler:    _TicketStrategyApi_TicketStrategyDel_Handler,
		},
		{
			MethodName: "TicketStrategyEnable",
			Handler:    _TicketStrategyApi_TicketStrategyEnable_Handler,
		},
		{
			MethodName: "TicketStrategyList",
			Handler:    _TicketStrategyApi_TicketStrategyList_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "service.proto",
}
