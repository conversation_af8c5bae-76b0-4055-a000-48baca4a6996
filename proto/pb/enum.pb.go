// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.30.0
// 	protoc        v3.21.9
// source: enum.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Bind int32

const (
	Bind_UnBind Bind = 0
	Bind_Bound  Bind = 1
	Bind_Update Bind = 2
)

// Enum value maps for Bind.
var (
	Bind_name = map[int32]string{
		0: "UnBind",
		1: "Bound",
		2: "Update",
	}
	Bind_value = map[string]int32{
		"UnBind": 0,
		"Bound":  1,
		"Update": 2,
	}
)

func (x Bind) Enum() *Bind {
	p := new(Bind)
	*p = x
	return p
}

func (x Bind) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Bind) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[0].Descriptor()
}

func (Bind) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[0]
}

func (x Bind) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Bind.Descriptor instead.
func (Bind) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{0}
}

// 场景
type SceneType int32

const (
	// 通用场景 - 废弃: 定制版游戏强制转换成3
	SceneType_General SceneType = 0
	// 游戏外 - 加载入口
	SceneType_Loading SceneType = 1
	// 游戏外 - 封号入口
	SceneType_AccountBan SceneType = 2
	// 游戏内 - 游戏内入口
	SceneType_InGame SceneType = 3
)

// Enum value maps for SceneType.
var (
	SceneType_name = map[int32]string{
		0: "General",
		1: "Loading",
		2: "AccountBan",
		3: "InGame",
	}
	SceneType_value = map[string]int32{
		"General":    0,
		"Loading":    1,
		"AccountBan": 2,
		"InGame":     3,
	}
)

func (x SceneType) Enum() *SceneType {
	p := new(SceneType)
	*p = x
	return p
}

func (x SceneType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SceneType) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[1].Descriptor()
}

func (SceneType) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[1]
}

func (x SceneType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SceneType.Descriptor instead.
func (SceneType) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{1}
}

// 工单来源
type Origin int32

const (
	Origin_Nobody Origin = 0
	// 玩家端提交 - 目前只有此来源
	Origin_Player Origin = 1
)

// Enum value maps for Origin.
var (
	Origin_name = map[int32]string{
		0: "Nobody",
		1: "Player",
	}
	Origin_value = map[string]int32{
		"Nobody": 0,
		"Player": 1,
	}
)

func (x Origin) Enum() *Origin {
	p := new(Origin)
	*p = x
	return p
}

func (x Origin) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Origin) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[2].Descriptor()
}

func (Origin) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[2]
}

func (x Origin) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Origin.Descriptor instead.
func (Origin) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{2}
}

// 工单状态
type Status int32

const (
	// 待接入工单
	Status_Untreated Status = 0
	// 处理中工单
	Status_Processing Status = 1
	// 已完成
	Status_Done Status = 2
)

// Enum value maps for Status.
var (
	Status_name = map[int32]string{
		0: "Untreated",
		1: "Processing",
		2: "Done",
	}
	Status_value = map[string]int32{
		"Untreated":  0,
		"Processing": 1,
		"Done":       2,
	}
)

func (x Status) Enum() *Status {
	p := new(Status)
	*p = x
	return p
}

func (x Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Status) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[3].Descriptor()
}

func (Status) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[3]
}

func (x Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Status.Descriptor instead.
func (Status) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{3}
}

// CsiLevel 工单评星
type CsiLevel int32

const (
	CsiLevel_ZeroStar CsiLevel = 0
	// 一星
	CsiLevel_OneStar CsiLevel = 1
	// 二星
	CsiLevel_SecondStar CsiLevel = 2
	// 三星
	CsiLevel_ThreeStar CsiLevel = 3
	// 四星
	CsiLevel_FourStar CsiLevel = 4
	// 五星
	CsiLevel_FiveStar CsiLevel = 5
)

// Enum value maps for CsiLevel.
var (
	CsiLevel_name = map[int32]string{
		0: "ZeroStar",
		1: "OneStar",
		2: "SecondStar",
		3: "ThreeStar",
		4: "FourStar",
		5: "FiveStar",
	}
	CsiLevel_value = map[string]int32{
		"ZeroStar":   0,
		"OneStar":    1,
		"SecondStar": 2,
		"ThreeStar":  3,
		"FourStar":   4,
		"FiveStar":   5,
	}
)

func (x CsiLevel) Enum() *CsiLevel {
	p := new(CsiLevel)
	*p = x
	return p
}

func (x CsiLevel) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CsiLevel) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[4].Descriptor()
}

func (CsiLevel) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[4]
}

func (x CsiLevel) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CsiLevel.Descriptor instead.
func (CsiLevel) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{4}
}

// NpsLevel NPS 评分
type NpsLevel int32

const (
	NpsLevel_NpsScoreZero NpsLevel = 0
	// 1分
	NpsLevel_NpsOneScore NpsLevel = 1
	// 2分
	NpsLevel_NpsTwoScore NpsLevel = 2
	// 3分
	NpsLevel_NpsThreeScore NpsLevel = 3
	// 4分
	NpsLevel_NpsFourScore NpsLevel = 4
	// 5分
	NpsLevel_NpsFiveScore NpsLevel = 5
)

// Enum value maps for NpsLevel.
var (
	NpsLevel_name = map[int32]string{
		0: "NpsScoreZero",
		1: "NpsOneScore",
		2: "NpsTwoScore",
		3: "NpsThreeScore",
		4: "NpsFourScore",
		5: "NpsFiveScore",
	}
	NpsLevel_value = map[string]int32{
		"NpsScoreZero":  0,
		"NpsOneScore":   1,
		"NpsTwoScore":   2,
		"NpsThreeScore": 3,
		"NpsFourScore":  4,
		"NpsFiveScore":  5,
	}
)

func (x NpsLevel) Enum() *NpsLevel {
	p := new(NpsLevel)
	*p = x
	return p
}

func (x NpsLevel) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (NpsLevel) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[5].Descriptor()
}

func (NpsLevel) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[5]
}

func (x NpsLevel) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use NpsLevel.Descriptor instead.
func (NpsLevel) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{5}
}

type CsiLabel int32

const (
	CsiLabel_NullCsiLb           CsiLabel = 0
	CsiLabel_TheService          CsiLabel = 2
	CsiLabel_TheOutcome          CsiLabel = 3
	CsiLabel_ProcessingSpeed     CsiLabel = 4
	CsiLabel_FastProcessingSpeed CsiLabel = 5
	CsiLabel_SatisfiedResult     CsiLabel = 6
	CsiLabel_SatisfiedService    CsiLabel = 7
)

// Enum value maps for CsiLabel.
var (
	CsiLabel_name = map[int32]string{
		0: "NullCsiLb",
		2: "TheService",
		3: "TheOutcome",
		4: "ProcessingSpeed",
		5: "FastProcessingSpeed",
		6: "SatisfiedResult",
		7: "SatisfiedService",
	}
	CsiLabel_value = map[string]int32{
		"NullCsiLb":           0,
		"TheService":          2,
		"TheOutcome":          3,
		"ProcessingSpeed":     4,
		"FastProcessingSpeed": 5,
		"SatisfiedResult":     6,
		"SatisfiedService":    7,
	}
)

func (x CsiLabel) Enum() *CsiLabel {
	p := new(CsiLabel)
	*p = x
	return p
}

func (x CsiLabel) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CsiLabel) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[6].Descriptor()
}

func (CsiLabel) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[6]
}

func (x CsiLabel) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CsiLabel.Descriptor instead.
func (CsiLabel) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{6}
}

// FilterTagEnum 标签过滤枚举
type FilterTagEnum int32

const (
	// 全部, 只是用来占位, 无意义
	FilterTagEnum_All FilterTagEnum = 0
	// 空白
	FilterTagEnum_IsNull FilterTagEnum = 1
	// 包含
	FilterTagEnum_IsIn FilterTagEnum = 2
	// 不包含
	FilterTagEnum_IsNotIn FilterTagEnum = 3
	// 全部有标签的
	FilterTagEnum_IsAll FilterTagEnum = 4
)

// Enum value maps for FilterTagEnum.
var (
	FilterTagEnum_name = map[int32]string{
		0: "All",
		1: "IsNull",
		2: "IsIn",
		3: "IsNotIn",
		4: "IsAll",
	}
	FilterTagEnum_value = map[string]int32{
		"All":     0,
		"IsNull":  1,
		"IsIn":    2,
		"IsNotIn": 3,
		"IsAll":   4,
	}
)

func (x FilterTagEnum) Enum() *FilterTagEnum {
	p := new(FilterTagEnum)
	*p = x
	return p
}

func (x FilterTagEnum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (FilterTagEnum) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[7].Descriptor()
}

func (FilterTagEnum) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[7]
}

func (x FilterTagEnum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use FilterTagEnum.Descriptor instead.
func (FilterTagEnum) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{7}
}

// FilterEnum 基础过滤枚举
type FilterEnum int32

const (
	// 全部
	FilterEnum_FilterAll FilterEnum = 0
	// 空白
	FilterEnum_FilterIsNull FilterEnum = 1
	// 包含
	FilterEnum_FilterIsIn FilterEnum = 2
	// 不包含
	FilterEnum_FilterIsNotIn FilterEnum = 3
	// 系统
	FilterEnum_FilterSystem FilterEnum = 4
)

// Enum value maps for FilterEnum.
var (
	FilterEnum_name = map[int32]string{
		0: "FilterAll",
		1: "FilterIsNull",
		2: "FilterIsIn",
		3: "FilterIsNotIn",
		4: "FilterSystem",
	}
	FilterEnum_value = map[string]int32{
		"FilterAll":     0,
		"FilterIsNull":  1,
		"FilterIsIn":    2,
		"FilterIsNotIn": 3,
		"FilterSystem":  4,
	}
)

func (x FilterEnum) Enum() *FilterEnum {
	p := new(FilterEnum)
	*p = x
	return p
}

func (x FilterEnum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (FilterEnum) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[8].Descriptor()
}

func (FilterEnum) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[8]
}

func (x FilterEnum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use FilterEnum.Descriptor instead.
func (FilterEnum) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{8}
}

// CreatorType
type CreatorType int32

const (
	CreatorType_No       CreatorType = 0
	CreatorType_UID      CreatorType = 1
	CreatorType_Fpid     CreatorType = 2
	CreatorType_Nickname CreatorType = 3
)

// Enum value maps for CreatorType.
var (
	CreatorType_name = map[int32]string{
		0: "No",
		1: "UID",
		2: "Fpid",
		3: "Nickname",
	}
	CreatorType_value = map[string]int32{
		"No":       0,
		"UID":      1,
		"Fpid":     2,
		"Nickname": 3,
	}
)

func (x CreatorType) Enum() *CreatorType {
	p := new(CreatorType)
	*p = x
	return p
}

func (x CreatorType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CreatorType) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[9].Descriptor()
}

func (CreatorType) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[9]
}

func (x CreatorType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CreatorType.Descriptor instead.
func (CreatorType) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{9}
}

// Workbench 工作台
type Workbench int32

const (
	// 总览
	Workbench_OverviewWorkStation Workbench = 0
	// 一线工作台
	Workbench_FirstTierWorkStation Workbench = 1
	// 二线工作台
	Workbench_SecondTierWorkStation Workbench = 2
	// vip工作台
	Workbench_VipWorkStation Workbench = 3
	// 三线工作台
	Workbench_ThirdTierWorkStation Workbench = 4
	// 一线管理工作台
	Workbench_FirstTierAdminWorkStation Workbench = 5
)

// Enum value maps for Workbench.
var (
	Workbench_name = map[int32]string{
		0: "OverviewWorkStation",
		1: "FirstTierWorkStation",
		2: "SecondTierWorkStation",
		3: "VipWorkStation",
		4: "ThirdTierWorkStation",
		5: "FirstTierAdminWorkStation",
	}
	Workbench_value = map[string]int32{
		"OverviewWorkStation":       0,
		"FirstTierWorkStation":      1,
		"SecondTierWorkStation":     2,
		"VipWorkStation":            3,
		"ThirdTierWorkStation":      4,
		"FirstTierAdminWorkStation": 5,
	}
)

func (x Workbench) Enum() *Workbench {
	p := new(Workbench)
	*p = x
	return p
}

func (x Workbench) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Workbench) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[10].Descriptor()
}

func (Workbench) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[10]
}

func (x Workbench) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Workbench.Descriptor instead.
func (Workbench) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{10}
}

// TicketSys 使用哪个工单系统
type TicketSys int32

const (
	// 默认老版本工单
	TicketSys_TicketSysOld TicketSys = 0
	// 新工单系统
	TicketSys_ticketSysNew TicketSys = 1
)

// Enum value maps for TicketSys.
var (
	TicketSys_name = map[int32]string{
		0: "TicketSysOld",
		1: "ticketSysNew",
	}
	TicketSys_value = map[string]int32{
		"TicketSysOld": 0,
		"ticketSysNew": 1,
	}
)

func (x TicketSys) Enum() *TicketSys {
	p := new(TicketSys)
	*p = x
	return p
}

func (x TicketSys) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TicketSys) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[11].Descriptor()
}

func (TicketSys) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[11]
}

func (x TicketSys) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TicketSys.Descriptor instead.
func (TicketSys) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{11}
}

// RelateType 分类类型
type RelateType int32

const (
	// 默认为 tpl
	RelateType_RelateTypeDefault RelateType = 0
	// tpl 模版
	RelateType_RelateTypeTpl RelateType = 1
	// 自动化流程
	RelateType_RelateTypeProcess RelateType = 2
)

// Enum value maps for RelateType.
var (
	RelateType_name = map[int32]string{
		0: "RelateTypeDefault",
		1: "RelateTypeTpl",
		2: "RelateTypeProcess",
	}
	RelateType_value = map[string]int32{
		"RelateTypeDefault": 0,
		"RelateTypeTpl":     1,
		"RelateTypeProcess": 2,
	}
)

func (x RelateType) Enum() *RelateType {
	p := new(RelateType)
	*p = x
	return p
}

func (x RelateType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RelateType) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[12].Descriptor()
}

func (RelateType) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[12]
}

func (x RelateType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RelateType.Descriptor instead.
func (RelateType) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{12}
}

// 工单事件
type TicketEvent int32

const (
	// 工单创建
	TicketEvent_Create TicketEvent = 0
	// 一线接单自动分配
	TicketEvent_Allocation TicketEvent = 1
	// 一线接单
	TicketEvent_Receipt TicketEvent = 2
	// 工单流转
	TicketEvent_Transfered TicketEvent = 3
	// 工单结案 有满意度评价
	TicketEvent_DoneCase TicketEvent = 4
	// 工单指派
	TicketEvent_Assign TicketEvent = 5
	// 调整紧急度
	TicketEvent_Emergency TicketEvent = 6
	// 工单备注
	TicketEvent_Remark TicketEvent = 7
	// 工单回复
	TicketEvent_Reply TicketEvent = 8
	// 工单关闭 无满意度评价
	TicketEvent_Closed TicketEvent = 9
	// 标签操作
	TicketEvent_Tag TicketEvent = 10
	// 推送
	TicketEvent_Relate TicketEvent = 11
	// 撤回
	TicketEvent_Revoke TicketEvent = 12
	// 重开
	TicketEvent_Reopen TicketEvent = 13
	// 修正问题分类
	TicketEvent_RectifyCat TicketEvent = 14
)

// Enum value maps for TicketEvent.
var (
	TicketEvent_name = map[int32]string{
		0:  "Create",
		1:  "Allocation",
		2:  "Receipt",
		3:  "Transfered",
		4:  "DoneCase",
		5:  "Assign",
		6:  "Emergency",
		7:  "Remark",
		8:  "Reply",
		9:  "Closed",
		10: "Tag",
		11: "Relate",
		12: "Revoke",
		13: "Reopen",
		14: "RectifyCat",
	}
	TicketEvent_value = map[string]int32{
		"Create":     0,
		"Allocation": 1,
		"Receipt":    2,
		"Transfered": 3,
		"DoneCase":   4,
		"Assign":     5,
		"Emergency":  6,
		"Remark":     7,
		"Reply":      8,
		"Closed":     9,
		"Tag":        10,
		"Relate":     11,
		"Revoke":     12,
		"Reopen":     13,
		"RectifyCat": 14,
	}
)

func (x TicketEvent) Enum() *TicketEvent {
	p := new(TicketEvent)
	*p = x
	return p
}

func (x TicketEvent) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TicketEvent) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[13].Descriptor()
}

func (TicketEvent) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[13]
}

func (x TicketEvent) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TicketEvent.Descriptor instead.
func (TicketEvent) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{13}
}

// 工单事件
type TkEvent int32

const (
	TkEvent_TkEventUnknown TkEvent = 0
	// 工单创建
	TkEvent_TkEventCreate TkEvent = 1
	// 玩家补填；
	TkEvent_TkEventPlayerRefill TkEvent = 2
	// 工单 - 自动分配
	TkEvent_TkEventAutoAlloc TkEvent = 3
	// 工单结案 有满意度评价
	TkEvent_TkEventDoneCase TkEvent = 4
	// 工单指派
	TkEvent_TkEventAssign TkEvent = 5
	// 工单流转给某人
	TkEvent_TkEventTurn TkEvent = 6
	// 玩家端回复
	TkEvent_TkEventUserCommu TkEvent = 7
	// 客服 - 回复
	TkEvent_TkEventCommu TkEvent = 8
	// 客服 - 回复&关单
	TkEvent_TkEventCommuClose TkEvent = 9
	// 客服 - 拒单
	TkEvent_TkEventRefused TkEvent = 10
	// 客服 - 工单升级/降级
	TkEvent_TkEventUpgrade TkEvent = 11
	// 客服 - 增加备注
	TkEvent_TkEventRemark TkEvent = 12
	// 重开
	TkEvent_TkEventReopen TkEvent = 13
	// 玩家3天未回复 - 设置超时关闭
	TkEvent_TkEventUserNoAnsTimout TkEvent = 14
	// 标签 - 新增标签
	TkEvent_TkEventTagAdd TkEvent = 15
	// 标签 - 删除
	TkEvent_TkEventTagDel TkEvent = 16
	// 超时 - 分配处理人 2h 未处理
	TkEvent_TkEventNoOpTimeout TkEvent = 17
	// 退回工单池
	TkEvent_TkEventReturnPool TkEvent = 18
)

// Enum value maps for TkEvent.
var (
	TkEvent_name = map[int32]string{
		0:  "TkEventUnknown",
		1:  "TkEventCreate",
		2:  "TkEventPlayerRefill",
		3:  "TkEventAutoAlloc",
		4:  "TkEventDoneCase",
		5:  "TkEventAssign",
		6:  "TkEventTurn",
		7:  "TkEventUserCommu",
		8:  "TkEventCommu",
		9:  "TkEventCommuClose",
		10: "TkEventRefused",
		11: "TkEventUpgrade",
		12: "TkEventRemark",
		13: "TkEventReopen",
		14: "TkEventUserNoAnsTimout",
		15: "TkEventTagAdd",
		16: "TkEventTagDel",
		17: "TkEventNoOpTimeout",
		18: "TkEventReturnPool",
	}
	TkEvent_value = map[string]int32{
		"TkEventUnknown":         0,
		"TkEventCreate":          1,
		"TkEventPlayerRefill":    2,
		"TkEventAutoAlloc":       3,
		"TkEventDoneCase":        4,
		"TkEventAssign":          5,
		"TkEventTurn":            6,
		"TkEventUserCommu":       7,
		"TkEventCommu":           8,
		"TkEventCommuClose":      9,
		"TkEventRefused":         10,
		"TkEventUpgrade":         11,
		"TkEventRemark":          12,
		"TkEventReopen":          13,
		"TkEventUserNoAnsTimout": 14,
		"TkEventTagAdd":          15,
		"TkEventTagDel":          16,
		"TkEventNoOpTimeout":     17,
		"TkEventReturnPool":      18,
	}
)

func (x TkEvent) Enum() *TkEvent {
	p := new(TkEvent)
	*p = x
	return p
}

func (x TkEvent) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TkEvent) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[14].Descriptor()
}

func (TkEvent) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[14]
}

func (x TkEvent) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TkEvent.Descriptor instead.
func (TkEvent) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{14}
}

// UserRole 角色
type UserRole int32

const (
	UserRole_NobodyRole UserRole = 0
	// 玩家端
	UserRole_PlayerRole UserRole = 1
	// 服务端
	UserRole_ServiceRole UserRole = 2
	// 系统操作
	UserRole_SystemRole     UserRole = 3
	UserRole_VIPServiceRole UserRole = 4
)

// Enum value maps for UserRole.
var (
	UserRole_name = map[int32]string{
		0: "NobodyRole",
		1: "PlayerRole",
		2: "ServiceRole",
		3: "SystemRole",
		4: "VIPServiceRole",
	}
	UserRole_value = map[string]int32{
		"NobodyRole":     0,
		"PlayerRole":     1,
		"ServiceRole":    2,
		"SystemRole":     3,
		"VIPServiceRole": 4,
	}
)

func (x UserRole) Enum() *UserRole {
	p := new(UserRole)
	*p = x
	return p
}

func (x UserRole) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (UserRole) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[15].Descriptor()
}

func (UserRole) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[15]
}

func (x UserRole) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use UserRole.Descriptor instead.
func (UserRole) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{15}
}

// TkStatus 工单状态定义
type TkStatus int32

const (
	// 未知状态
	TkStatus_TkStatusUnknown TkStatus = 0
	// 待接入工单
	TkStatus_TkStatusUntreated TkStatus = 1
	// 处理中工单
	TkStatus_TkStatusProcessing TkStatus = 2
	// 已完成
	TkStatus_TkStatusDone TkStatus = 3
)

// Enum value maps for TkStatus.
var (
	TkStatus_name = map[int32]string{
		0: "TkStatusUnknown",
		1: "TkStatusUntreated",
		2: "TkStatusProcessing",
		3: "TkStatusDone",
	}
	TkStatus_value = map[string]int32{
		"TkStatusUnknown":    0,
		"TkStatusUntreated":  1,
		"TkStatusProcessing": 2,
		"TkStatusDone":       3,
	}
)

func (x TkStatus) Enum() *TkStatus {
	p := new(TkStatus)
	*p = x
	return p
}

func (x TkStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TkStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[16].Descriptor()
}

func (TkStatus) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[16]
}

func (x TkStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TkStatus.Descriptor instead.
func (TkStatus) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{16}
}

// TkStage 节点流转
type TkStage int32

const (
	TkStage_TkStageUnknown TkStage = 0
	// 工单状态 - New 待接单 - 玩家提交工单&未分配
	TkStage_TkStageNew TkStage = 1
	// 工单状态 - New For Agent 待处理 - 已分配/指派客服
	TkStage_TkStageNewForAgent TkStage = 2
	// 工单状态 - Agent Replied 处理中 - 待玩家回复 - 客服已回复待玩家回复
	TkStage_TkStageAgentReplied TkStage = 3
	// 工单状态 - Waiting For Agent 处理中 - 玩家已回复 待客服再次回复
	TkStage_TkStageWaitingForAgent TkStage = 4
	// 工单状态 - Agent Resolved 超时关闭 - 玩家3天内未回复，超时关闭
	TkStage_TkStageAgentResolved TkStage = 5
	// 工单状态 - Reopen 重开 - 玩家重新打开工单
	TkStage_TkStageAgentReopen TkStage = 6
	// 工单状态 - Rejected 拒单关闭 - 客服点击"拒单"，结束会话
	TkStage_TkStageAgentRejected TkStage = 7
	// 工单状态 - Completed 已完成 - 客服回复&关单
	TkStage_TkStageAgentCompleted TkStage = 8
)

// Enum value maps for TkStage.
var (
	TkStage_name = map[int32]string{
		0: "TkStageUnknown",
		1: "TkStageNew",
		2: "TkStageNewForAgent",
		3: "TkStageAgentReplied",
		4: "TkStageWaitingForAgent",
		5: "TkStageAgentResolved",
		6: "TkStageAgentReopen",
		7: "TkStageAgentRejected",
		8: "TkStageAgentCompleted",
	}
	TkStage_value = map[string]int32{
		"TkStageUnknown":         0,
		"TkStageNew":             1,
		"TkStageNewForAgent":     2,
		"TkStageAgentReplied":    3,
		"TkStageWaitingForAgent": 4,
		"TkStageAgentResolved":   5,
		"TkStageAgentReopen":     6,
		"TkStageAgentRejected":   7,
		"TkStageAgentCompleted":  8,
	}
)

func (x TkStage) Enum() *TkStage {
	p := new(TkStage)
	*p = x
	return p
}

func (x TkStage) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TkStage) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[17].Descriptor()
}

func (TkStage) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[17]
}

func (x TkStage) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TkStage.Descriptor instead.
func (TkStage) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{17}
}

// 工单流转节点部分
type TicketStage int32

const (
	// 待处理
	TicketStage_Pending TicketStage = 0
	// 一线处理中
	TicketStage_FirstTierProcessing TicketStage = 1
	// 一线补填中
	TicketStage_FirstTierRefill TicketStage = 2
	// 二线处理中
	TicketStage_SecondTierProcessing TicketStage = 3
	// 二线审核区
	TicketStage_SecondTierAudit TicketStage = 4
	// VIP补填中
	TicketStage_VipAgentRefill TicketStage = 5
	// 处理完成 玩家有评价
	TicketStage_TicketResolved TicketStage = 6
	// 玩家补填
	TicketStage_TicketRefill TicketStage = 7
	// 关闭工单 玩家无评价
	TicketStage_TicketClosed TicketStage = 8
	// 暂时回复玩家
	TicketStage_TemporaryReply TicketStage = 9
	// vip处理
	TicketStage_VipAgentProcessing TicketStage = 10
	// 二线补填中
	TicketStage_SecondTierRefill TicketStage = 11
	// 三线处理
	TicketStage_ThirdTierProcessing TicketStage = 12
	// 三线审核区
	TicketStage_ThirdTierAudit TicketStage = 13
	// 一线管理处理中
	TicketStage_FirstTierAdminProcessing TicketStage = 14
	// 一线管理补填中
	TicketStage_FirstTierAdminRefill TicketStage = 15
	// VIP处理完成
	TicketStage_VipAgentResolved TicketStage = 16
)

// Enum value maps for TicketStage.
var (
	TicketStage_name = map[int32]string{
		0:  "Pending",
		1:  "FirstTierProcessing",
		2:  "FirstTierRefill",
		3:  "SecondTierProcessing",
		4:  "SecondTierAudit",
		5:  "VipAgentRefill",
		6:  "TicketResolved",
		7:  "TicketRefill",
		8:  "TicketClosed",
		9:  "TemporaryReply",
		10: "VipAgentProcessing",
		11: "SecondTierRefill",
		12: "ThirdTierProcessing",
		13: "ThirdTierAudit",
		14: "FirstTierAdminProcessing",
		15: "FirstTierAdminRefill",
		16: "VipAgentResolved",
	}
	TicketStage_value = map[string]int32{
		"Pending":                  0,
		"FirstTierProcessing":      1,
		"FirstTierRefill":          2,
		"SecondTierProcessing":     3,
		"SecondTierAudit":          4,
		"VipAgentRefill":           5,
		"TicketResolved":           6,
		"TicketRefill":             7,
		"TicketClosed":             8,
		"TemporaryReply":           9,
		"VipAgentProcessing":       10,
		"SecondTierRefill":         11,
		"ThirdTierProcessing":      12,
		"ThirdTierAudit":           13,
		"FirstTierAdminProcessing": 14,
		"FirstTierAdminRefill":     15,
		"VipAgentResolved":         16,
	}
)

func (x TicketStage) Enum() *TicketStage {
	p := new(TicketStage)
	*p = x
	return p
}

func (x TicketStage) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TicketStage) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[18].Descriptor()
}

func (TicketStage) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[18]
}

func (x TicketStage) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TicketStage.Descriptor instead.
func (TicketStage) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{18}
}

// TkProgress 当前状态
type TkProgress int32

const (
	TkProgress_TkProgressUnknown TkProgress = 0
	// 1：已完成
	TkProgress_TkProgressDone TkProgress = 1
	// 2：待补充
	TkProgress_TkProgressUserRefill TkProgress = 2
	// 3：处理中
	TkProgress_TkProgressUserDoing TkProgress = 3
	// 4：超时关闭
	TkProgress_TkProgressUserRefillOverTime TkProgress = 4
	// 5: 重新打开
	TkProgress_TkProgressReopen TkProgress = 5
	// 6: 拒单关闭
	TkProgress_TkProgressReject TkProgress = 6
)

// Enum value maps for TkProgress.
var (
	TkProgress_name = map[int32]string{
		0: "TkProgressUnknown",
		1: "TkProgressDone",
		2: "TkProgressUserRefill",
		3: "TkProgressUserDoing",
		4: "TkProgressUserRefillOverTime",
		5: "TkProgressReopen",
		6: "TkProgressReject",
	}
	TkProgress_value = map[string]int32{
		"TkProgressUnknown":            0,
		"TkProgressDone":               1,
		"TkProgressUserRefill":         2,
		"TkProgressUserDoing":          3,
		"TkProgressUserRefillOverTime": 4,
		"TkProgressReopen":             5,
		"TkProgressReject":             6,
	}
)

func (x TkProgress) Enum() *TkProgress {
	p := new(TkProgress)
	*p = x
	return p
}

func (x TkProgress) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TkProgress) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[19].Descriptor()
}

func (TkProgress) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[19]
}

func (x TkProgress) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TkProgress.Descriptor instead.
func (TkProgress) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{19}
}

// 关单角色
type TkClosedRole int32

const (
	TkClosedRole_TkClosedNone          TkClosedRole = 0
	TkClosedRole_TkPlayerClosed        TkClosedRole = 1
	TkClosedRole_TkServiceClosed       TkClosedRole = 2
	TkClosedRole_TkSystemTimeoutClosed TkClosedRole = 3
	TkClosedRole_TkSystemReplyClosed   TkClosedRole = 4
)

// Enum value maps for TkClosedRole.
var (
	TkClosedRole_name = map[int32]string{
		0: "TkClosedNone",
		1: "TkPlayerClosed",
		2: "TkServiceClosed",
		3: "TkSystemTimeoutClosed",
		4: "TkSystemReplyClosed",
	}
	TkClosedRole_value = map[string]int32{
		"TkClosedNone":          0,
		"TkPlayerClosed":        1,
		"TkServiceClosed":       2,
		"TkSystemTimeoutClosed": 3,
		"TkSystemReplyClosed":   4,
	}
)

func (x TkClosedRole) Enum() *TkClosedRole {
	p := new(TkClosedRole)
	*p = x
	return p
}

func (x TkClosedRole) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TkClosedRole) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[20].Descriptor()
}

func (TkClosedRole) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[20]
}

func (x TkClosedRole) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TkClosedRole.Descriptor instead.
func (TkClosedRole) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{20}
}

// 客服人员是否在线状态定义
type UserLoginStatus int32

const (
	UserLoginStatus_UserLoginStatusUnknown UserLoginStatus = 0
	// 在线
	UserLoginStatus_UserLoginStatusYes UserLoginStatus = 1
	// 离线
	UserLoginStatus_UserLoginStatusNo UserLoginStatus = 2
)

// Enum value maps for UserLoginStatus.
var (
	UserLoginStatus_name = map[int32]string{
		0: "UserLoginStatusUnknown",
		1: "UserLoginStatusYes",
		2: "UserLoginStatusNo",
	}
	UserLoginStatus_value = map[string]int32{
		"UserLoginStatusUnknown": 0,
		"UserLoginStatusYes":     1,
		"UserLoginStatusNo":      2,
	}
)

func (x UserLoginStatus) Enum() *UserLoginStatus {
	p := new(UserLoginStatus)
	*p = x
	return p
}

func (x UserLoginStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (UserLoginStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[21].Descriptor()
}

func (UserLoginStatus) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[21]
}

func (x UserLoginStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use UserLoginStatus.Descriptor instead.
func (UserLoginStatus) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{21}
}

// 会话数据分类
type CommuType int32

const (
	CommuType_CommuTypeUnknown CommuType = 0
	// 正常会话
	CommuType_CommuTypeDialogue CommuType = 1
	// 备注消息
	CommuType_CommuTypeRemark CommuType = 2
)

// Enum value maps for CommuType.
var (
	CommuType_name = map[int32]string{
		0: "CommuTypeUnknown",
		1: "CommuTypeDialogue",
		2: "CommuTypeRemark",
	}
	CommuType_value = map[string]int32{
		"CommuTypeUnknown":  0,
		"CommuTypeDialogue": 1,
		"CommuTypeRemark":   2,
	}
)

func (x CommuType) Enum() *CommuType {
	p := new(CommuType)
	*p = x
	return p
}

func (x CommuType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CommuType) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[22].Descriptor()
}

func (CommuType) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[22]
}

func (x CommuType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CommuType.Descriptor instead.
func (CommuType) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{22}
}

// 客服回复状态
type DiscordServiceReplyStatus int32

const (
	DiscordServiceReplyStatus_StatusUnknown     DiscordServiceReplyStatus = 0
	DiscordServiceReplyStatus_WaitServiceReply  DiscordServiceReplyStatus = 1
	DiscordServiceReplyStatus_ServiceHasReplied DiscordServiceReplyStatus = 2
)

// Enum value maps for DiscordServiceReplyStatus.
var (
	DiscordServiceReplyStatus_name = map[int32]string{
		0: "StatusUnknown",
		1: "WaitServiceReply",
		2: "ServiceHasReplied",
	}
	DiscordServiceReplyStatus_value = map[string]int32{
		"StatusUnknown":     0,
		"WaitServiceReply":  1,
		"ServiceHasReplied": 2,
	}
)

func (x DiscordServiceReplyStatus) Enum() *DiscordServiceReplyStatus {
	p := new(DiscordServiceReplyStatus)
	*p = x
	return p
}

func (x DiscordServiceReplyStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DiscordServiceReplyStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[23].Descriptor()
}

func (DiscordServiceReplyStatus) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[23]
}

func (x DiscordServiceReplyStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DiscordServiceReplyStatus.Descriptor instead.
func (DiscordServiceReplyStatus) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{23}
}

// 玩家性别
type PlayerGender int32

const (
	PlayerGender_GenderUnknown PlayerGender = 0
	PlayerGender_Male          PlayerGender = 1
	PlayerGender_Female        PlayerGender = 2
)

// Enum value maps for PlayerGender.
var (
	PlayerGender_name = map[int32]string{
		0: "GenderUnknown",
		1: "Male",
		2: "Female",
	}
	PlayerGender_value = map[string]int32{
		"GenderUnknown": 0,
		"Male":          1,
		"Female":        2,
	}
)

func (x PlayerGender) Enum() *PlayerGender {
	p := new(PlayerGender)
	*p = x
	return p
}

func (x PlayerGender) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PlayerGender) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[24].Descriptor()
}

func (PlayerGender) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[24]
}

func (x PlayerGender) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PlayerGender.Descriptor instead.
func (PlayerGender) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{24}
}

// 玩家教育程度
type PlayerEducationLevel int32

const (
	PlayerEducationLevel_UnknownEducationLevel PlayerEducationLevel = 0
	PlayerEducationLevel_PrimarySchool         PlayerEducationLevel = 1
	PlayerEducationLevel_JuniorHighSchool      PlayerEducationLevel = 2
	PlayerEducationLevel_SeniorHighSchool      PlayerEducationLevel = 3
	PlayerEducationLevel_UnderGraduate         PlayerEducationLevel = 4
	PlayerEducationLevel_Graduate              PlayerEducationLevel = 5
	PlayerEducationLevel_Doctor                PlayerEducationLevel = 6
	PlayerEducationLevel_Postdoc               PlayerEducationLevel = 7
	PlayerEducationLevel_Others                PlayerEducationLevel = 8
)

// Enum value maps for PlayerEducationLevel.
var (
	PlayerEducationLevel_name = map[int32]string{
		0: "UnknownEducationLevel",
		1: "PrimarySchool",
		2: "JuniorHighSchool",
		3: "SeniorHighSchool",
		4: "UnderGraduate",
		5: "Graduate",
		6: "Doctor",
		7: "Postdoc",
		8: "Others",
	}
	PlayerEducationLevel_value = map[string]int32{
		"UnknownEducationLevel": 0,
		"PrimarySchool":         1,
		"JuniorHighSchool":      2,
		"SeniorHighSchool":      3,
		"UnderGraduate":         4,
		"Graduate":              5,
		"Doctor":                6,
		"Postdoc":               7,
		"Others":                8,
	}
)

func (x PlayerEducationLevel) Enum() *PlayerEducationLevel {
	p := new(PlayerEducationLevel)
	*p = x
	return p
}

func (x PlayerEducationLevel) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PlayerEducationLevel) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[25].Descriptor()
}

func (PlayerEducationLevel) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[25]
}

func (x PlayerEducationLevel) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PlayerEducationLevel.Descriptor instead.
func (PlayerEducationLevel) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{25}
}

// 玩家婚姻状况
type PlayerMarriageState int32

const (
	PlayerMarriageState_UnknownMarriageState PlayerMarriageState = 0
	PlayerMarriageState_Married              PlayerMarriageState = 1
	PlayerMarriageState_Single               PlayerMarriageState = 2
	PlayerMarriageState_Divorced             PlayerMarriageState = 3
	PlayerMarriageState_Widowed              PlayerMarriageState = 4
)

// Enum value maps for PlayerMarriageState.
var (
	PlayerMarriageState_name = map[int32]string{
		0: "UnknownMarriageState",
		1: "Married",
		2: "Single",
		3: "Divorced",
		4: "Widowed",
	}
	PlayerMarriageState_value = map[string]int32{
		"UnknownMarriageState": 0,
		"Married":              1,
		"Single":               2,
		"Divorced":             3,
		"Widowed":              4,
	}
)

func (x PlayerMarriageState) Enum() *PlayerMarriageState {
	p := new(PlayerMarriageState)
	*p = x
	return p
}

func (x PlayerMarriageState) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PlayerMarriageState) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[26].Descriptor()
}

func (PlayerMarriageState) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[26]
}

func (x PlayerMarriageState) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PlayerMarriageState.Descriptor instead.
func (PlayerMarriageState) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{26}
}

// 玩家生育状况
type PlayerFertilityState int32

const (
	PlayerFertilityState_UnknownFertilityState PlayerFertilityState = 0
	PlayerFertilityState_NoChild               PlayerFertilityState = 1
	PlayerFertilityState_OneChild              PlayerFertilityState = 2
	PlayerFertilityState_TwoKids               PlayerFertilityState = 3
	PlayerFertilityState_ThreeKids             PlayerFertilityState = 4
	PlayerFertilityState_MoreThanThreeKids     PlayerFertilityState = 5
)

// Enum value maps for PlayerFertilityState.
var (
	PlayerFertilityState_name = map[int32]string{
		0: "UnknownFertilityState",
		1: "NoChild",
		2: "OneChild",
		3: "TwoKids",
		4: "ThreeKids",
		5: "MoreThanThreeKids",
	}
	PlayerFertilityState_value = map[string]int32{
		"UnknownFertilityState": 0,
		"NoChild":               1,
		"OneChild":              2,
		"TwoKids":               3,
		"ThreeKids":             4,
		"MoreThanThreeKids":     5,
	}
)

func (x PlayerFertilityState) Enum() *PlayerFertilityState {
	p := new(PlayerFertilityState)
	*p = x
	return p
}

func (x PlayerFertilityState) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PlayerFertilityState) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[27].Descriptor()
}

func (PlayerFertilityState) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[27]
}

func (x PlayerFertilityState) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PlayerFertilityState.Descriptor instead.
func (PlayerFertilityState) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{27}
}

type PlayerVipState int32

const (
	PlayerVipState_UnknownVipState PlayerVipState = 0
	PlayerVipState_NonVip          PlayerVipState = 1
	PlayerVipState_Vip             PlayerVipState = 2
)

// Enum value maps for PlayerVipState.
var (
	PlayerVipState_name = map[int32]string{
		0: "UnknownVipState",
		1: "NonVip",
		2: "Vip",
	}
	PlayerVipState_value = map[string]int32{
		"UnknownVipState": 0,
		"NonVip":          1,
		"Vip":             2,
	}
)

func (x PlayerVipState) Enum() *PlayerVipState {
	p := new(PlayerVipState)
	*p = x
	return p
}

func (x PlayerVipState) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PlayerVipState) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[28].Descriptor()
}

func (PlayerVipState) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[28]
}

func (x PlayerVipState) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PlayerVipState.Descriptor instead.
func (PlayerVipState) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{28}
}

// discord 操作日志 - 分组
type OpGroup int32

const (
	OpGroup_OpGroupUnknown         OpGroup = 0
	OpGroup_OpGroupPortrait        OpGroup = 1  // 玩家画像
	OpGroup_OpGroupMaintainConfig  OpGroup = 2  // 玩家维护关系配置
	OpGroup_OpGroupSendTextMessage OpGroup = 3  // 发送文本消息
	OpGroup_OpGroupEditMessage     OpGroup = 4  // 编辑消息
	OpGroup_OpGroupSendFile        OpGroup = 5  // 发送文件
	OpGroup_OpGroupExamineTpl      OpGroup = 20 // 质检 - examineTpl配置
	OpGroup_OpGroupExamineDsc      OpGroup = 21 // 质检 - 质检单变动
	OpGroup_OpGroupSurveySend      OpGroup = 22 // 调查问卷 - 发放记录
	OpGroup_OpGroupSurveyBatchGen  OpGroup = 23 // 调查问卷 - 批量生成
)

// Enum value maps for OpGroup.
var (
	OpGroup_name = map[int32]string{
		0:  "OpGroupUnknown",
		1:  "OpGroupPortrait",
		2:  "OpGroupMaintainConfig",
		3:  "OpGroupSendTextMessage",
		4:  "OpGroupEditMessage",
		5:  "OpGroupSendFile",
		20: "OpGroupExamineTpl",
		21: "OpGroupExamineDsc",
		22: "OpGroupSurveySend",
		23: "OpGroupSurveyBatchGen",
	}
	OpGroup_value = map[string]int32{
		"OpGroupUnknown":         0,
		"OpGroupPortrait":        1,
		"OpGroupMaintainConfig":  2,
		"OpGroupSendTextMessage": 3,
		"OpGroupEditMessage":     4,
		"OpGroupSendFile":        5,
		"OpGroupExamineTpl":      20,
		"OpGroupExamineDsc":      21,
		"OpGroupSurveySend":      22,
		"OpGroupSurveyBatchGen":  23,
	}
)

func (x OpGroup) Enum() *OpGroup {
	p := new(OpGroup)
	*p = x
	return p
}

func (x OpGroup) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (OpGroup) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[29].Descriptor()
}

func (OpGroup) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[29]
}

func (x OpGroup) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use OpGroup.Descriptor instead.
func (OpGroup) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{29}
}

// discord 操作日志 - 操作类型
type OpAction int32

const (
	OpAction_OpActionUnknown OpAction = 0
	OpAction_OpActionAdd     OpAction = 1 // 新增
	OpAction_OpActionUpdate  OpAction = 2 // 修改
	OpAction_OpActionDelete  OpAction = 3 // 删除
	OpAction_OpActionStatus  OpAction = 4 // 修改状态
)

// Enum value maps for OpAction.
var (
	OpAction_name = map[int32]string{
		0: "OpActionUnknown",
		1: "OpActionAdd",
		2: "OpActionUpdate",
		3: "OpActionDelete",
		4: "OpActionStatus",
	}
	OpAction_value = map[string]int32{
		"OpActionUnknown": 0,
		"OpActionAdd":     1,
		"OpActionUpdate":  2,
		"OpActionDelete":  3,
		"OpActionStatus":  4,
	}
)

func (x OpAction) Enum() *OpAction {
	p := new(OpAction)
	*p = x
	return p
}

func (x OpAction) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (OpAction) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[30].Descriptor()
}

func (OpAction) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[30]
}

func (x OpAction) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use OpAction.Descriptor instead.
func (OpAction) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{30}
}

// discord 沟通问题类型
type QuestionType int32

const (
	QuestionType_UnknownQuestionType         QuestionType = 0
	QuestionType_GameConsultation            QuestionType = 1 // 游戏咨询
	QuestionType_GameSuggestion              QuestionType = 2 // 游戏建议
	QuestionType_GameException               QuestionType = 3 // 游戏异常
	QuestionType_ServerMatchAndServerMerge   QuestionType = 4 // 服务器匹配&合服
	QuestionType_ComplaintOrNegativeFeedback QuestionType = 5 // 抱怨/负面反馈
	QuestionType_OtherQuestion               QuestionType = 6 // 其他问题
)

// Enum value maps for QuestionType.
var (
	QuestionType_name = map[int32]string{
		0: "UnknownQuestionType",
		1: "GameConsultation",
		2: "GameSuggestion",
		3: "GameException",
		4: "ServerMatchAndServerMerge",
		5: "ComplaintOrNegativeFeedback",
		6: "OtherQuestion",
	}
	QuestionType_value = map[string]int32{
		"UnknownQuestionType":         0,
		"GameConsultation":            1,
		"GameSuggestion":              2,
		"GameException":               3,
		"ServerMatchAndServerMerge":   4,
		"ComplaintOrNegativeFeedback": 5,
		"OtherQuestion":               6,
	}
)

func (x QuestionType) Enum() *QuestionType {
	p := new(QuestionType)
	*p = x
	return p
}

func (x QuestionType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (QuestionType) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[31].Descriptor()
}

func (QuestionType) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[31]
}

func (x QuestionType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use QuestionType.Descriptor instead.
func (QuestionType) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{31}
}

// discord 沟通问题处理状态
type QuestionHandleStatus int32

const (
	QuestionHandleStatus_UnknownHandleStatus QuestionHandleStatus = 0
	QuestionHandleStatus_UnderProcess        QuestionHandleStatus = 1 // 处理中
	QuestionHandleStatus_Finished            QuestionHandleStatus = 2 // 已完成
)

// Enum value maps for QuestionHandleStatus.
var (
	QuestionHandleStatus_name = map[int32]string{
		0: "UnknownHandleStatus",
		1: "UnderProcess",
		2: "Finished",
	}
	QuestionHandleStatus_value = map[string]int32{
		"UnknownHandleStatus": 0,
		"UnderProcess":        1,
		"Finished":            2,
	}
)

func (x QuestionHandleStatus) Enum() *QuestionHandleStatus {
	p := new(QuestionHandleStatus)
	*p = x
	return p
}

func (x QuestionHandleStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (QuestionHandleStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[32].Descriptor()
}

func (QuestionHandleStatus) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[32]
}

func (x QuestionHandleStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use QuestionHandleStatus.Descriptor instead.
func (QuestionHandleStatus) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{32}
}

type TicketSystemTag int32

const (
	TicketSystemTag_UnknownTag       TicketSystemTag = 0
	TicketSystemTag_GreenChannelUser TicketSystemTag = 10
	TicketSystemTag_PrivateZoneUser  TicketSystemTag = 11
	TicketSystemTag_ElfinTypeUser    TicketSystemTag = 12
	TicketSystemTag_PrivateZoneCard  TicketSystemTag = 13
)

// Enum value maps for TicketSystemTag.
var (
	TicketSystemTag_name = map[int32]string{
		0:  "UnknownTag",
		10: "GreenChannelUser",
		11: "PrivateZoneUser",
		12: "ElfinTypeUser",
		13: "PrivateZoneCard",
	}
	TicketSystemTag_value = map[string]int32{
		"UnknownTag":       0,
		"GreenChannelUser": 10,
		"PrivateZoneUser":  11,
		"ElfinTypeUser":    12,
		"PrivateZoneCard":  13,
	}
)

func (x TicketSystemTag) Enum() *TicketSystemTag {
	p := new(TicketSystemTag)
	*p = x
	return p
}

func (x TicketSystemTag) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TicketSystemTag) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[33].Descriptor()
}

func (TicketSystemTag) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[33]
}

func (x TicketSystemTag) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TicketSystemTag.Descriptor instead.
func (TicketSystemTag) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{33}
}

// ai 润色标签
type AIPolishLabel int32

const (
	AIPolishLabel_UnknownPolishLabel   AIPolishLabel = 0
	AIPolishLabel_PolishFriendlyLabel  AIPolishLabel = 1
	AIPolishLabel_PolishConciseLabel   AIPolishLabel = 2
	AIPolishLabel_PolishFormalLabel    AIPolishLabel = 3
	AIPolishLabel_PolishPoliteLabel    AIPolishLabel = 4
	AIPolishLabel_PolishCustomizeLabel AIPolishLabel = 5
)

// Enum value maps for AIPolishLabel.
var (
	AIPolishLabel_name = map[int32]string{
		0: "UnknownPolishLabel",
		1: "PolishFriendlyLabel",
		2: "PolishConciseLabel",
		3: "PolishFormalLabel",
		4: "PolishPoliteLabel",
		5: "PolishCustomizeLabel",
	}
	AIPolishLabel_value = map[string]int32{
		"UnknownPolishLabel":   0,
		"PolishFriendlyLabel":  1,
		"PolishConciseLabel":   2,
		"PolishFormalLabel":    3,
		"PolishPoliteLabel":    4,
		"PolishCustomizeLabel": 5,
	}
)

func (x AIPolishLabel) Enum() *AIPolishLabel {
	p := new(AIPolishLabel)
	*p = x
	return p
}

func (x AIPolishLabel) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AIPolishLabel) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[34].Descriptor()
}

func (AIPolishLabel) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[34]
}

func (x AIPolishLabel) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AIPolishLabel.Descriptor instead.
func (AIPolishLabel) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{34}
}

// 标签类型
type TagConfigType int32

const (
	TagConfigType_UnknownConfigType TagConfigType = 0
	TagConfigType_TicketConfigType  TagConfigType = 1 // 工单标签类型
	TagConfigType_DiscordConfigType TagConfigType = 2 // Discord标签类型
	TagConfigType_LineConfigType    TagConfigType = 3 // Line标签类型
)

// Enum value maps for TagConfigType.
var (
	TagConfigType_name = map[int32]string{
		0: "UnknownConfigType",
		1: "TicketConfigType",
		2: "DiscordConfigType",
		3: "LineConfigType",
	}
	TagConfigType_value = map[string]int32{
		"UnknownConfigType": 0,
		"TicketConfigType":  1,
		"DiscordConfigType": 2,
		"LineConfigType":    3,
	}
)

func (x TagConfigType) Enum() *TagConfigType {
	p := new(TagConfigType)
	*p = x
	return p
}

func (x TagConfigType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TagConfigType) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[35].Descriptor()
}

func (TagConfigType) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[35]
}

func (x TagConfigType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TagConfigType.Descriptor instead.
func (TagConfigType) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{35}
}

// DC列表排序方式
type DcPoolSort int32

const (
	DcPoolSort_DcPoolSortWaitDefault DcPoolSort = 0
	// 等待时长 - 默认
	DcPoolSort_DcPoolSortWaitTm DcPoolSort = 1
	// 客服最后回复时间
	DcPoolSort_DcPoolSortLastReplyTm DcPoolSort = 2
	// 累付金额
	DcPoolSort_DcPoolSortpaidAmount DcPoolSort = 3
)

// Enum value maps for DcPoolSort.
var (
	DcPoolSort_name = map[int32]string{
		0: "DcPoolSortWaitDefault",
		1: "DcPoolSortWaitTm",
		2: "DcPoolSortLastReplyTm",
		3: "DcPoolSortpaidAmount",
	}
	DcPoolSort_value = map[string]int32{
		"DcPoolSortWaitDefault": 0,
		"DcPoolSortWaitTm":      1,
		"DcPoolSortLastReplyTm": 2,
		"DcPoolSortpaidAmount":  3,
	}
)

func (x DcPoolSort) Enum() *DcPoolSort {
	p := new(DcPoolSort)
	*p = x
	return p
}

func (x DcPoolSort) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DcPoolSort) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[36].Descriptor()
}

func (DcPoolSort) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[36]
}

func (x DcPoolSort) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DcPoolSort.Descriptor instead.
func (DcPoolSort) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{36}
}

// SVIP
type SVIP int32

const (
	SVIP_SVIPNone SVIP = 0
	SVIP_SVIPYes  SVIP = 1
	SVIP_SVIPNo   SVIP = 2
)

// Enum value maps for SVIP.
var (
	SVIP_name = map[int32]string{
		0: "SVIPNone",
		1: "SVIPYes",
		2: "SVIPNo",
	}
	SVIP_value = map[string]int32{
		"SVIPNone": 0,
		"SVIPYes":  1,
		"SVIPNo":   2,
	}
)

func (x SVIP) Enum() *SVIP {
	p := new(SVIP)
	*p = x
	return p
}

func (x SVIP) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SVIP) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[37].Descriptor()
}

func (SVIP) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[37]
}

func (x SVIP) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SVIP.Descriptor instead.
func (SVIP) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{37}
}

// 查询类型
type SearchTypeEnum int32

const (
	SearchTypeEnum_SearchTypeNone         SearchTypeEnum = 0
	SearchTypeEnum_SearchTypeSubmitUser   SearchTypeEnum = 1
	SearchTypeEnum_SearchTypeQuestionUser SearchTypeEnum = 2
)

// Enum value maps for SearchTypeEnum.
var (
	SearchTypeEnum_name = map[int32]string{
		0: "SearchTypeNone",
		1: "SearchTypeSubmitUser",
		2: "SearchTypeQuestionUser",
	}
	SearchTypeEnum_value = map[string]int32{
		"SearchTypeNone":         0,
		"SearchTypeSubmitUser":   1,
		"SearchTypeQuestionUser": 2,
	}
)

func (x SearchTypeEnum) Enum() *SearchTypeEnum {
	p := new(SearchTypeEnum)
	*p = x
	return p
}

func (x SearchTypeEnum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SearchTypeEnum) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[38].Descriptor()
}

func (SearchTypeEnum) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[38]
}

func (x SearchTypeEnum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SearchTypeEnum.Descriptor instead.
func (SearchTypeEnum) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{38}
}

// 用户类型
type UserTypeEnum int32

const (
	UserTypeEnum_UserTypeNone          UserTypeEnum = 0
	UserTypeEnum_UserTypeLongVipUser   UserTypeEnum = 1
	UserTypeEnum_UserTypeLimitTimeUser UserTypeEnum = 2
	UserTypeEnum_UserTypePaidUser      UserTypeEnum = 3
	UserTypeEnum_UserTypeRegularUser   UserTypeEnum = 4
)

// Enum value maps for UserTypeEnum.
var (
	UserTypeEnum_name = map[int32]string{
		0: "UserTypeNone",
		1: "UserTypeLongVipUser",
		2: "UserTypeLimitTimeUser",
		3: "UserTypePaidUser",
		4: "UserTypeRegularUser",
	}
	UserTypeEnum_value = map[string]int32{
		"UserTypeNone":          0,
		"UserTypeLongVipUser":   1,
		"UserTypeLimitTimeUser": 2,
		"UserTypePaidUser":      3,
		"UserTypeRegularUser":   4,
	}
)

func (x UserTypeEnum) Enum() *UserTypeEnum {
	p := new(UserTypeEnum)
	*p = x
	return p
}

func (x UserTypeEnum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (UserTypeEnum) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[39].Descriptor()
}

func (UserTypeEnum) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[39]
}

func (x UserTypeEnum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use UserTypeEnum.Descriptor instead.
func (UserTypeEnum) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{39}
}

// 红点类型
type RedPointTypeEnum int32

const (
	RedPointTypeEnum_RedPointTypeNone     RedPointTypeEnum = 0
	RedPointTypeEnum_RedPointTypeMessage  RedPointTypeEnum = 1
	RedPointTypeEnum_RedPointTypeOvertime RedPointTypeEnum = 2
)

// Enum value maps for RedPointTypeEnum.
var (
	RedPointTypeEnum_name = map[int32]string{
		0: "RedPointTypeNone",
		1: "RedPointTypeMessage",
		2: "RedPointTypeOvertime",
	}
	RedPointTypeEnum_value = map[string]int32{
		"RedPointTypeNone":     0,
		"RedPointTypeMessage":  1,
		"RedPointTypeOvertime": 2,
	}
)

func (x RedPointTypeEnum) Enum() *RedPointTypeEnum {
	p := new(RedPointTypeEnum)
	*p = x
	return p
}

func (x RedPointTypeEnum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RedPointTypeEnum) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[40].Descriptor()
}

func (RedPointTypeEnum) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[40]
}

func (x RedPointTypeEnum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RedPointTypeEnum.Descriptor instead.
func (RedPointTypeEnum) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{40}
}

type ChatOrFormType int32

const (
	ChatOrFormType_ChatOrFormTypeUnknown ChatOrFormType = 0
	ChatOrFormType_ChatOrFormTypeChat    ChatOrFormType = 1
	ChatOrFormType_ChatOrFormTypeForm    ChatOrFormType = 2
)

// Enum value maps for ChatOrFormType.
var (
	ChatOrFormType_name = map[int32]string{
		0: "ChatOrFormTypeUnknown",
		1: "ChatOrFormTypeChat",
		2: "ChatOrFormTypeForm",
	}
	ChatOrFormType_value = map[string]int32{
		"ChatOrFormTypeUnknown": 0,
		"ChatOrFormTypeChat":    1,
		"ChatOrFormTypeForm":    2,
	}
)

func (x ChatOrFormType) Enum() *ChatOrFormType {
	p := new(ChatOrFormType)
	*p = x
	return p
}

func (x ChatOrFormType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ChatOrFormType) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[41].Descriptor()
}

func (ChatOrFormType) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[41]
}

func (x ChatOrFormType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ChatOrFormType.Descriptor instead.
func (ChatOrFormType) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{41}
}

type TrainingTaskStatus int32

const (
	// 未开始
	TrainingTaskStatus_ProcessTicketStatusInit TrainingTaskStatus = 0
	// 处理中
	TrainingTaskStatus_ProcessTicketStatusDoing TrainingTaskStatus = 10
	// 处理失败
	TrainingTaskStatus_ProcessTicketStatusFail TrainingTaskStatus = 20
	// 处理成功
	TrainingTaskStatus_ProcessTicketStatusSuccess TrainingTaskStatus = 30
)

// Enum value maps for TrainingTaskStatus.
var (
	TrainingTaskStatus_name = map[int32]string{
		0:  "ProcessTicketStatusInit",
		10: "ProcessTicketStatusDoing",
		20: "ProcessTicketStatusFail",
		30: "ProcessTicketStatusSuccess",
	}
	TrainingTaskStatus_value = map[string]int32{
		"ProcessTicketStatusInit":    0,
		"ProcessTicketStatusDoing":   10,
		"ProcessTicketStatusFail":    20,
		"ProcessTicketStatusSuccess": 30,
	}
)

func (x TrainingTaskStatus) Enum() *TrainingTaskStatus {
	p := new(TrainingTaskStatus)
	*p = x
	return p
}

func (x TrainingTaskStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TrainingTaskStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[42].Descriptor()
}

func (TrainingTaskStatus) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[42]
}

func (x TrainingTaskStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TrainingTaskStatus.Descriptor instead.
func (TrainingTaskStatus) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{42}
}

type ConversationStatus int32

const (
	ConversationStatus_ConversationStatusUnknown ConversationStatus = 0
	ConversationStatus_ConversationStatusOn      ConversationStatus = 1
	ConversationStatus_ConversationStatusWait    ConversationStatus = 2
	ConversationStatus_ConversationStatusClose   ConversationStatus = 10
)

// Enum value maps for ConversationStatus.
var (
	ConversationStatus_name = map[int32]string{
		0:  "ConversationStatusUnknown",
		1:  "ConversationStatusOn",
		2:  "ConversationStatusWait",
		10: "ConversationStatusClose",
	}
	ConversationStatus_value = map[string]int32{
		"ConversationStatusUnknown": 0,
		"ConversationStatusOn":      1,
		"ConversationStatusWait":    2,
		"ConversationStatusClose":   10,
	}
)

func (x ConversationStatus) Enum() *ConversationStatus {
	p := new(ConversationStatus)
	*p = x
	return p
}

func (x ConversationStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ConversationStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[43].Descriptor()
}

func (ConversationStatus) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[43]
}

func (x ConversationStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ConversationStatus.Descriptor instead.
func (ConversationStatus) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{43}
}

type SolveType int32

const (
	SolveType_SolveTypeUnknowTicket    SolveType = 0
	SolveType_SolveTypeManualTicket    SolveType = 1
	SolveType_SolveTypeAutoReplyTicket SolveType = 2
	SolveType_SolveTypeInvalidTicket   SolveType = 3
	SolveType_SolveTypeStrategyTicket  SolveType = 4
)

// Enum value maps for SolveType.
var (
	SolveType_name = map[int32]string{
		0: "SolveTypeUnknowTicket",
		1: "SolveTypeManualTicket",
		2: "SolveTypeAutoReplyTicket",
		3: "SolveTypeInvalidTicket",
		4: "SolveTypeStrategyTicket",
	}
	SolveType_value = map[string]int32{
		"SolveTypeUnknowTicket":    0,
		"SolveTypeManualTicket":    1,
		"SolveTypeAutoReplyTicket": 2,
		"SolveTypeInvalidTicket":   3,
		"SolveTypeStrategyTicket":  4,
	}
)

func (x SolveType) Enum() *SolveType {
	p := new(SolveType)
	*p = x
	return p
}

func (x SolveType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SolveType) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[44].Descriptor()
}

func (SolveType) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[44]
}

func (x SolveType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SolveType.Descriptor instead.
func (SolveType) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{44}
}

type TicketType int32

const (
	TicketType_TicketTypeNormal TicketType = 0
	TicketType_TicketTypeNewAi  TicketType = 1
)

// Enum value maps for TicketType.
var (
	TicketType_name = map[int32]string{
		0: "TicketTypeNormal",
		1: "TicketTypeNewAi",
	}
	TicketType_value = map[string]int32{
		"TicketTypeNormal": 0,
		"TicketTypeNewAi":  1,
	}
)

func (x TicketType) Enum() *TicketType {
	p := new(TicketType)
	*p = x
	return p
}

func (x TicketType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TicketType) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[45].Descriptor()
}

func (TicketType) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[45]
}

func (x TicketType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TicketType.Descriptor instead.
func (TicketType) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{45}
}

type TicketTagType int32

const (
	TicketTagType_TicketTagTypeUnknown TicketTagType = 0
	TicketTagType_TicketTagTypeNormal  TicketTagType = 1
)

// Enum value maps for TicketTagType.
var (
	TicketTagType_name = map[int32]string{
		0: "TicketTagTypeUnknown",
		1: "TicketTagTypeNormal",
	}
	TicketTagType_value = map[string]int32{
		"TicketTagTypeUnknown": 0,
		"TicketTagTypeNormal":  1,
	}
)

func (x TicketTagType) Enum() *TicketTagType {
	p := new(TicketTagType)
	*p = x
	return p
}

func (x TicketTagType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TicketTagType) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[46].Descriptor()
}

func (TicketTagType) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[46]
}

func (x TicketTagType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TicketTagType.Descriptor instead.
func (TicketTagType) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{46}
}

type TicketAiTagType int32

const (
	TicketAiTagType_TicketAiTagTypeUnknown    TicketAiTagType = 0
	TicketAiTagType_TicketAiTagTypeAsk        TicketAiTagType = 1
	TicketAiTagType_TicketAiTagTypeComplaint  TicketAiTagType = 2
	TicketAiTagType_TicketAiTagTypeSuggestion TicketAiTagType = 3
	TicketAiTagType_TicketAiTagTypeElse       TicketAiTagType = 4
	TicketAiTagType_TicketAiTagNotTag         TicketAiTagType = 5
)

// Enum value maps for TicketAiTagType.
var (
	TicketAiTagType_name = map[int32]string{
		0: "TicketAiTagTypeUnknown",
		1: "TicketAiTagTypeAsk",
		2: "TicketAiTagTypeComplaint",
		3: "TicketAiTagTypeSuggestion",
		4: "TicketAiTagTypeElse",
		5: "TicketAiTagNotTag",
	}
	TicketAiTagType_value = map[string]int32{
		"TicketAiTagTypeUnknown":    0,
		"TicketAiTagTypeAsk":        1,
		"TicketAiTagTypeComplaint":  2,
		"TicketAiTagTypeSuggestion": 3,
		"TicketAiTagTypeElse":       4,
		"TicketAiTagNotTag":         5,
	}
)

func (x TicketAiTagType) Enum() *TicketAiTagType {
	p := new(TicketAiTagType)
	*p = x
	return p
}

func (x TicketAiTagType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TicketAiTagType) Descriptor() protoreflect.EnumDescriptor {
	return file_enum_proto_enumTypes[47].Descriptor()
}

func (TicketAiTagType) Type() protoreflect.EnumType {
	return &file_enum_proto_enumTypes[47]
}

func (x TicketAiTagType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TicketAiTagType.Descriptor instead.
func (TicketAiTagType) EnumDescriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{47}
}

// RetEnum 枚举
type RetEnum struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name  string `protobuf:"bytes,1,opt,name=name,proto3" json:"name"`    // 枚举名
	Value uint32 `protobuf:"varint,2,opt,name=value,proto3" json:"value"` // 枚举值
}

func (x *RetEnum) Reset() {
	*x = RetEnum{}
	if protoimpl.UnsafeEnabled {
		mi := &file_enum_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RetEnum) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RetEnum) ProtoMessage() {}

func (x *RetEnum) ProtoReflect() protoreflect.Message {
	mi := &file_enum_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RetEnum.ProtoReflect.Descriptor instead.
func (*RetEnum) Descriptor() ([]byte, []int) {
	return file_enum_proto_rawDescGZIP(), []int{0}
}

func (x *RetEnum) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *RetEnum) GetValue() uint32 {
	if x != nil {
		return x.Value
	}
	return 0
}

var File_enum_proto protoreflect.FileDescriptor

var file_enum_proto_rawDesc = []byte{
	0x0a, 0x0a, 0x65, 0x6e, 0x75, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x02, 0x70, 0x62,
	0x22, 0x33, 0x0a, 0x07, 0x52, 0x65, 0x74, 0x45, 0x6e, 0x75, 0x6d, 0x12, 0x12, 0x0a, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x2a, 0x29, 0x0a, 0x04, 0x42, 0x69, 0x6e, 0x64, 0x12, 0x0a, 0x0a,
	0x06, 0x55, 0x6e, 0x42, 0x69, 0x6e, 0x64, 0x10, 0x00, 0x12, 0x09, 0x0a, 0x05, 0x42, 0x6f, 0x75,
	0x6e, 0x64, 0x10, 0x01, 0x12, 0x0a, 0x0a, 0x06, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x10, 0x02,
	0x2a, 0x41, 0x0a, 0x09, 0x53, 0x63, 0x65, 0x6e, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0b, 0x0a,
	0x07, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x6c, 0x10, 0x00, 0x12, 0x0b, 0x0a, 0x07, 0x4c, 0x6f,
	0x61, 0x64, 0x69, 0x6e, 0x67, 0x10, 0x01, 0x12, 0x0e, 0x0a, 0x0a, 0x41, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x42, 0x61, 0x6e, 0x10, 0x02, 0x12, 0x0a, 0x0a, 0x06, 0x49, 0x6e, 0x47, 0x61, 0x6d,
	0x65, 0x10, 0x03, 0x2a, 0x20, 0x0a, 0x06, 0x4f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x12, 0x0a, 0x0a,
	0x06, 0x4e, 0x6f, 0x62, 0x6f, 0x64, 0x79, 0x10, 0x00, 0x12, 0x0a, 0x0a, 0x06, 0x50, 0x6c, 0x61,
	0x79, 0x65, 0x72, 0x10, 0x01, 0x2a, 0x31, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x0d, 0x0a, 0x09, 0x55, 0x6e, 0x74, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x10, 0x00, 0x12, 0x0e,
	0x0a, 0x0a, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x10, 0x01, 0x12, 0x08,
	0x0a, 0x04, 0x44, 0x6f, 0x6e, 0x65, 0x10, 0x02, 0x2a, 0x60, 0x0a, 0x08, 0x43, 0x73, 0x69, 0x4c,
	0x65, 0x76, 0x65, 0x6c, 0x12, 0x0c, 0x0a, 0x08, 0x5a, 0x65, 0x72, 0x6f, 0x53, 0x74, 0x61, 0x72,
	0x10, 0x00, 0x12, 0x0b, 0x0a, 0x07, 0x4f, 0x6e, 0x65, 0x53, 0x74, 0x61, 0x72, 0x10, 0x01, 0x12,
	0x0e, 0x0a, 0x0a, 0x53, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x53, 0x74, 0x61, 0x72, 0x10, 0x02, 0x12,
	0x0d, 0x0a, 0x09, 0x54, 0x68, 0x72, 0x65, 0x65, 0x53, 0x74, 0x61, 0x72, 0x10, 0x03, 0x12, 0x0c,
	0x0a, 0x08, 0x46, 0x6f, 0x75, 0x72, 0x53, 0x74, 0x61, 0x72, 0x10, 0x04, 0x12, 0x0c, 0x0a, 0x08,
	0x46, 0x69, 0x76, 0x65, 0x53, 0x74, 0x61, 0x72, 0x10, 0x05, 0x2a, 0x75, 0x0a, 0x08, 0x4e, 0x70,
	0x73, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x10, 0x0a, 0x0c, 0x4e, 0x70, 0x73, 0x53, 0x63, 0x6f,
	0x72, 0x65, 0x5a, 0x65, 0x72, 0x6f, 0x10, 0x00, 0x12, 0x0f, 0x0a, 0x0b, 0x4e, 0x70, 0x73, 0x4f,
	0x6e, 0x65, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x10, 0x01, 0x12, 0x0f, 0x0a, 0x0b, 0x4e, 0x70, 0x73,
	0x54, 0x77, 0x6f, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x10, 0x02, 0x12, 0x11, 0x0a, 0x0d, 0x4e, 0x70,
	0x73, 0x54, 0x68, 0x72, 0x65, 0x65, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x10, 0x03, 0x12, 0x10, 0x0a,
	0x0c, 0x4e, 0x70, 0x73, 0x46, 0x6f, 0x75, 0x72, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x10, 0x04, 0x12,
	0x10, 0x0a, 0x0c, 0x4e, 0x70, 0x73, 0x46, 0x69, 0x76, 0x65, 0x53, 0x63, 0x6f, 0x72, 0x65, 0x10,
	0x05, 0x2a, 0x92, 0x01, 0x0a, 0x08, 0x43, 0x73, 0x69, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x12, 0x0d,
	0x0a, 0x09, 0x4e, 0x75, 0x6c, 0x6c, 0x43, 0x73, 0x69, 0x4c, 0x62, 0x10, 0x00, 0x12, 0x0e, 0x0a,
	0x0a, 0x54, 0x68, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x10, 0x02, 0x12, 0x0e, 0x0a,
	0x0a, 0x54, 0x68, 0x65, 0x4f, 0x75, 0x74, 0x63, 0x6f, 0x6d, 0x65, 0x10, 0x03, 0x12, 0x13, 0x0a,
	0x0f, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x53, 0x70, 0x65, 0x65, 0x64,
	0x10, 0x04, 0x12, 0x17, 0x0a, 0x13, 0x46, 0x61, 0x73, 0x74, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73,
	0x73, 0x69, 0x6e, 0x67, 0x53, 0x70, 0x65, 0x65, 0x64, 0x10, 0x05, 0x12, 0x13, 0x0a, 0x0f, 0x53,
	0x61, 0x74, 0x69, 0x73, 0x66, 0x69, 0x65, 0x64, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x10, 0x06,
	0x12, 0x14, 0x0a, 0x10, 0x53, 0x61, 0x74, 0x69, 0x73, 0x66, 0x69, 0x65, 0x64, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x10, 0x07, 0x2a, 0x46, 0x0a, 0x0d, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72,
	0x54, 0x61, 0x67, 0x45, 0x6e, 0x75, 0x6d, 0x12, 0x07, 0x0a, 0x03, 0x41, 0x6c, 0x6c, 0x10, 0x00,
	0x12, 0x0a, 0x0a, 0x06, 0x49, 0x73, 0x4e, 0x75, 0x6c, 0x6c, 0x10, 0x01, 0x12, 0x08, 0x0a, 0x04,
	0x49, 0x73, 0x49, 0x6e, 0x10, 0x02, 0x12, 0x0b, 0x0a, 0x07, 0x49, 0x73, 0x4e, 0x6f, 0x74, 0x49,
	0x6e, 0x10, 0x03, 0x12, 0x09, 0x0a, 0x05, 0x49, 0x73, 0x41, 0x6c, 0x6c, 0x10, 0x04, 0x2a, 0x62,
	0x0a, 0x0a, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x45, 0x6e, 0x75, 0x6d, 0x12, 0x0d, 0x0a, 0x09,
	0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x41, 0x6c, 0x6c, 0x10, 0x00, 0x12, 0x10, 0x0a, 0x0c, 0x46,
	0x69, 0x6c, 0x74, 0x65, 0x72, 0x49, 0x73, 0x4e, 0x75, 0x6c, 0x6c, 0x10, 0x01, 0x12, 0x0e, 0x0a,
	0x0a, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x49, 0x73, 0x49, 0x6e, 0x10, 0x02, 0x12, 0x11, 0x0a,
	0x0d, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x49, 0x73, 0x4e, 0x6f, 0x74, 0x49, 0x6e, 0x10, 0x03,
	0x12, 0x10, 0x0a, 0x0c, 0x46, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d,
	0x10, 0x04, 0x2a, 0x36, 0x0a, 0x0b, 0x43, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x06, 0x0a, 0x02, 0x4e, 0x6f, 0x10, 0x00, 0x12, 0x07, 0x0a, 0x03, 0x55, 0x49, 0x44,
	0x10, 0x01, 0x12, 0x08, 0x0a, 0x04, 0x46, 0x70, 0x69, 0x64, 0x10, 0x02, 0x12, 0x0c, 0x0a, 0x08,
	0x4e, 0x69, 0x63, 0x6b, 0x6e, 0x61, 0x6d, 0x65, 0x10, 0x03, 0x2a, 0xa6, 0x01, 0x0a, 0x09, 0x57,
	0x6f, 0x72, 0x6b, 0x62, 0x65, 0x6e, 0x63, 0x68, 0x12, 0x17, 0x0a, 0x13, 0x4f, 0x76, 0x65, 0x72,
	0x76, 0x69, 0x65, 0x77, 0x57, 0x6f, 0x72, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x10,
	0x00, 0x12, 0x18, 0x0a, 0x14, 0x46, 0x69, 0x72, 0x73, 0x74, 0x54, 0x69, 0x65, 0x72, 0x57, 0x6f,
	0x72, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x10, 0x01, 0x12, 0x19, 0x0a, 0x15, 0x53,
	0x65, 0x63, 0x6f, 0x6e, 0x64, 0x54, 0x69, 0x65, 0x72, 0x57, 0x6f, 0x72, 0x6b, 0x53, 0x74, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x10, 0x02, 0x12, 0x12, 0x0a, 0x0e, 0x56, 0x69, 0x70, 0x57, 0x6f, 0x72,
	0x6b, 0x53, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x10, 0x03, 0x12, 0x18, 0x0a, 0x14, 0x54, 0x68,
	0x69, 0x72, 0x64, 0x54, 0x69, 0x65, 0x72, 0x57, 0x6f, 0x72, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x10, 0x04, 0x12, 0x1d, 0x0a, 0x19, 0x46, 0x69, 0x72, 0x73, 0x74, 0x54, 0x69, 0x65,
	0x72, 0x41, 0x64, 0x6d, 0x69, 0x6e, 0x57, 0x6f, 0x72, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x10, 0x05, 0x2a, 0x2f, 0x0a, 0x09, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x53, 0x79, 0x73,
	0x12, 0x10, 0x0a, 0x0c, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x53, 0x79, 0x73, 0x4f, 0x6c, 0x64,
	0x10, 0x00, 0x12, 0x10, 0x0a, 0x0c, 0x74, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x53, 0x79, 0x73, 0x4e,
	0x65, 0x77, 0x10, 0x01, 0x2a, 0x4d, 0x0a, 0x0a, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x15, 0x0a, 0x11, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x10, 0x00, 0x12, 0x11, 0x0a, 0x0d, 0x52, 0x65, 0x6c,
	0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x54, 0x70, 0x6c, 0x10, 0x01, 0x12, 0x15, 0x0a, 0x11,
	0x52, 0x65, 0x6c, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73,
	0x73, 0x10, 0x02, 0x2a, 0xcf, 0x01, 0x0a, 0x0b, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x45, 0x76,
	0x65, 0x6e, 0x74, 0x12, 0x0a, 0x0a, 0x06, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x10, 0x00, 0x12,
	0x0e, 0x0a, 0x0a, 0x41, 0x6c, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x10, 0x01, 0x12,
	0x0b, 0x0a, 0x07, 0x52, 0x65, 0x63, 0x65, 0x69, 0x70, 0x74, 0x10, 0x02, 0x12, 0x0e, 0x0a, 0x0a,
	0x54, 0x72, 0x61, 0x6e, 0x73, 0x66, 0x65, 0x72, 0x65, 0x64, 0x10, 0x03, 0x12, 0x0c, 0x0a, 0x08,
	0x44, 0x6f, 0x6e, 0x65, 0x43, 0x61, 0x73, 0x65, 0x10, 0x04, 0x12, 0x0a, 0x0a, 0x06, 0x41, 0x73,
	0x73, 0x69, 0x67, 0x6e, 0x10, 0x05, 0x12, 0x0d, 0x0a, 0x09, 0x45, 0x6d, 0x65, 0x72, 0x67, 0x65,
	0x6e, 0x63, 0x79, 0x10, 0x06, 0x12, 0x0a, 0x0a, 0x06, 0x52, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x10,
	0x07, 0x12, 0x09, 0x0a, 0x05, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x10, 0x08, 0x12, 0x0a, 0x0a, 0x06,
	0x43, 0x6c, 0x6f, 0x73, 0x65, 0x64, 0x10, 0x09, 0x12, 0x07, 0x0a, 0x03, 0x54, 0x61, 0x67, 0x10,
	0x0a, 0x12, 0x0a, 0x0a, 0x06, 0x52, 0x65, 0x6c, 0x61, 0x74, 0x65, 0x10, 0x0b, 0x12, 0x0a, 0x0a,
	0x06, 0x52, 0x65, 0x76, 0x6f, 0x6b, 0x65, 0x10, 0x0c, 0x12, 0x0a, 0x0a, 0x06, 0x52, 0x65, 0x6f,
	0x70, 0x65, 0x6e, 0x10, 0x0d, 0x12, 0x0e, 0x0a, 0x0a, 0x52, 0x65, 0x63, 0x74, 0x69, 0x66, 0x79,
	0x43, 0x61, 0x74, 0x10, 0x0e, 0x2a, 0x96, 0x03, 0x0a, 0x07, 0x54, 0x6b, 0x45, 0x76, 0x65, 0x6e,
	0x74, 0x12, 0x12, 0x0a, 0x0e, 0x54, 0x6b, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x55, 0x6e, 0x6b, 0x6e,
	0x6f, 0x77, 0x6e, 0x10, 0x00, 0x12, 0x11, 0x0a, 0x0d, 0x54, 0x6b, 0x45, 0x76, 0x65, 0x6e, 0x74,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x10, 0x01, 0x12, 0x17, 0x0a, 0x13, 0x54, 0x6b, 0x45, 0x76,
	0x65, 0x6e, 0x74, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x52, 0x65, 0x66, 0x69, 0x6c, 0x6c, 0x10,
	0x02, 0x12, 0x14, 0x0a, 0x10, 0x54, 0x6b, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x41, 0x75, 0x74, 0x6f,
	0x41, 0x6c, 0x6c, 0x6f, 0x63, 0x10, 0x03, 0x12, 0x13, 0x0a, 0x0f, 0x54, 0x6b, 0x45, 0x76, 0x65,
	0x6e, 0x74, 0x44, 0x6f, 0x6e, 0x65, 0x43, 0x61, 0x73, 0x65, 0x10, 0x04, 0x12, 0x11, 0x0a, 0x0d,
	0x54, 0x6b, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x41, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x10, 0x05, 0x12,
	0x0f, 0x0a, 0x0b, 0x54, 0x6b, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x54, 0x75, 0x72, 0x6e, 0x10, 0x06,
	0x12, 0x14, 0x0a, 0x10, 0x54, 0x6b, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x55, 0x73, 0x65, 0x72, 0x43,
	0x6f, 0x6d, 0x6d, 0x75, 0x10, 0x07, 0x12, 0x10, 0x0a, 0x0c, 0x54, 0x6b, 0x45, 0x76, 0x65, 0x6e,
	0x74, 0x43, 0x6f, 0x6d, 0x6d, 0x75, 0x10, 0x08, 0x12, 0x15, 0x0a, 0x11, 0x54, 0x6b, 0x45, 0x76,
	0x65, 0x6e, 0x74, 0x43, 0x6f, 0x6d, 0x6d, 0x75, 0x43, 0x6c, 0x6f, 0x73, 0x65, 0x10, 0x09, 0x12,
	0x12, 0x0a, 0x0e, 0x54, 0x6b, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x66, 0x75, 0x73, 0x65,
	0x64, 0x10, 0x0a, 0x12, 0x12, 0x0a, 0x0e, 0x54, 0x6b, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x55, 0x70,
	0x67, 0x72, 0x61, 0x64, 0x65, 0x10, 0x0b, 0x12, 0x11, 0x0a, 0x0d, 0x54, 0x6b, 0x45, 0x76, 0x65,
	0x6e, 0x74, 0x52, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x10, 0x0c, 0x12, 0x11, 0x0a, 0x0d, 0x54, 0x6b,
	0x45, 0x76, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x6f, 0x70, 0x65, 0x6e, 0x10, 0x0d, 0x12, 0x1a, 0x0a,
	0x16, 0x54, 0x6b, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x55, 0x73, 0x65, 0x72, 0x4e, 0x6f, 0x41, 0x6e,
	0x73, 0x54, 0x69, 0x6d, 0x6f, 0x75, 0x74, 0x10, 0x0e, 0x12, 0x11, 0x0a, 0x0d, 0x54, 0x6b, 0x45,
	0x76, 0x65, 0x6e, 0x74, 0x54, 0x61, 0x67, 0x41, 0x64, 0x64, 0x10, 0x0f, 0x12, 0x11, 0x0a, 0x0d,
	0x54, 0x6b, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x54, 0x61, 0x67, 0x44, 0x65, 0x6c, 0x10, 0x10, 0x12,
	0x16, 0x0a, 0x12, 0x54, 0x6b, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x4e, 0x6f, 0x4f, 0x70, 0x54, 0x69,
	0x6d, 0x65, 0x6f, 0x75, 0x74, 0x10, 0x11, 0x12, 0x15, 0x0a, 0x11, 0x54, 0x6b, 0x45, 0x76, 0x65,
	0x6e, 0x74, 0x52, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x50, 0x6f, 0x6f, 0x6c, 0x10, 0x12, 0x2a, 0x5f,
	0x0a, 0x08, 0x55, 0x73, 0x65, 0x72, 0x52, 0x6f, 0x6c, 0x65, 0x12, 0x0e, 0x0a, 0x0a, 0x4e, 0x6f,
	0x62, 0x6f, 0x64, 0x79, 0x52, 0x6f, 0x6c, 0x65, 0x10, 0x00, 0x12, 0x0e, 0x0a, 0x0a, 0x50, 0x6c,
	0x61, 0x79, 0x65, 0x72, 0x52, 0x6f, 0x6c, 0x65, 0x10, 0x01, 0x12, 0x0f, 0x0a, 0x0b, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x6f, 0x6c, 0x65, 0x10, 0x02, 0x12, 0x0e, 0x0a, 0x0a, 0x53,
	0x79, 0x73, 0x74, 0x65, 0x6d, 0x52, 0x6f, 0x6c, 0x65, 0x10, 0x03, 0x12, 0x12, 0x0a, 0x0e, 0x56,
	0x49, 0x50, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x6f, 0x6c, 0x65, 0x10, 0x04, 0x2a,
	0x60, 0x0a, 0x08, 0x54, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x13, 0x0a, 0x0f, 0x54,
	0x6b, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x10, 0x00,
	0x12, 0x15, 0x0a, 0x11, 0x54, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x55, 0x6e, 0x74, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x64, 0x10, 0x01, 0x12, 0x16, 0x0a, 0x12, 0x54, 0x6b, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x10, 0x02, 0x12,
	0x10, 0x0a, 0x0c, 0x54, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x44, 0x6f, 0x6e, 0x65, 0x10,
	0x03, 0x2a, 0xe1, 0x01, 0x0a, 0x07, 0x54, 0x6b, 0x53, 0x74, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a,
	0x0e, 0x54, 0x6b, 0x53, 0x74, 0x61, 0x67, 0x65, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x10,
	0x00, 0x12, 0x0e, 0x0a, 0x0a, 0x54, 0x6b, 0x53, 0x74, 0x61, 0x67, 0x65, 0x4e, 0x65, 0x77, 0x10,
	0x01, 0x12, 0x16, 0x0a, 0x12, 0x54, 0x6b, 0x53, 0x74, 0x61, 0x67, 0x65, 0x4e, 0x65, 0x77, 0x46,
	0x6f, 0x72, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x10, 0x02, 0x12, 0x17, 0x0a, 0x13, 0x54, 0x6b, 0x53,
	0x74, 0x61, 0x67, 0x65, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x69, 0x65, 0x64,
	0x10, 0x03, 0x12, 0x1a, 0x0a, 0x16, 0x54, 0x6b, 0x53, 0x74, 0x61, 0x67, 0x65, 0x57, 0x61, 0x69,
	0x74, 0x69, 0x6e, 0x67, 0x46, 0x6f, 0x72, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x10, 0x04, 0x12, 0x18,
	0x0a, 0x14, 0x54, 0x6b, 0x53, 0x74, 0x61, 0x67, 0x65, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x52, 0x65,
	0x73, 0x6f, 0x6c, 0x76, 0x65, 0x64, 0x10, 0x05, 0x12, 0x16, 0x0a, 0x12, 0x54, 0x6b, 0x53, 0x74,
	0x61, 0x67, 0x65, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x6f, 0x70, 0x65, 0x6e, 0x10, 0x06,
	0x12, 0x18, 0x0a, 0x14, 0x54, 0x6b, 0x53, 0x74, 0x61, 0x67, 0x65, 0x41, 0x67, 0x65, 0x6e, 0x74,
	0x52, 0x65, 0x6a, 0x65, 0x63, 0x74, 0x65, 0x64, 0x10, 0x07, 0x12, 0x19, 0x0a, 0x15, 0x54, 0x6b,
	0x53, 0x74, 0x61, 0x67, 0x65, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x65,
	0x74, 0x65, 0x64, 0x10, 0x08, 0x2a, 0x80, 0x03, 0x0a, 0x0b, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74,
	0x53, 0x74, 0x61, 0x67, 0x65, 0x12, 0x0b, 0x0a, 0x07, 0x50, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67,
	0x10, 0x00, 0x12, 0x17, 0x0a, 0x13, 0x46, 0x69, 0x72, 0x73, 0x74, 0x54, 0x69, 0x65, 0x72, 0x50,
	0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x10, 0x01, 0x12, 0x13, 0x0a, 0x0f, 0x46,
	0x69, 0x72, 0x73, 0x74, 0x54, 0x69, 0x65, 0x72, 0x52, 0x65, 0x66, 0x69, 0x6c, 0x6c, 0x10, 0x02,
	0x12, 0x18, 0x0a, 0x14, 0x53, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x54, 0x69, 0x65, 0x72, 0x50, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x10, 0x03, 0x12, 0x13, 0x0a, 0x0f, 0x53, 0x65,
	0x63, 0x6f, 0x6e, 0x64, 0x54, 0x69, 0x65, 0x72, 0x41, 0x75, 0x64, 0x69, 0x74, 0x10, 0x04, 0x12,
	0x12, 0x0a, 0x0e, 0x56, 0x69, 0x70, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x66, 0x69, 0x6c,
	0x6c, 0x10, 0x05, 0x12, 0x12, 0x0a, 0x0e, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x52, 0x65, 0x73,
	0x6f, 0x6c, 0x76, 0x65, 0x64, 0x10, 0x06, 0x12, 0x10, 0x0a, 0x0c, 0x54, 0x69, 0x63, 0x6b, 0x65,
	0x74, 0x52, 0x65, 0x66, 0x69, 0x6c, 0x6c, 0x10, 0x07, 0x12, 0x10, 0x0a, 0x0c, 0x54, 0x69, 0x63,
	0x6b, 0x65, 0x74, 0x43, 0x6c, 0x6f, 0x73, 0x65, 0x64, 0x10, 0x08, 0x12, 0x12, 0x0a, 0x0e, 0x54,
	0x65, 0x6d, 0x70, 0x6f, 0x72, 0x61, 0x72, 0x79, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x10, 0x09, 0x12,
	0x16, 0x0a, 0x12, 0x56, 0x69, 0x70, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x50, 0x72, 0x6f, 0x63, 0x65,
	0x73, 0x73, 0x69, 0x6e, 0x67, 0x10, 0x0a, 0x12, 0x14, 0x0a, 0x10, 0x53, 0x65, 0x63, 0x6f, 0x6e,
	0x64, 0x54, 0x69, 0x65, 0x72, 0x52, 0x65, 0x66, 0x69, 0x6c, 0x6c, 0x10, 0x0b, 0x12, 0x17, 0x0a,
	0x13, 0x54, 0x68, 0x69, 0x72, 0x64, 0x54, 0x69, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73,
	0x73, 0x69, 0x6e, 0x67, 0x10, 0x0c, 0x12, 0x12, 0x0a, 0x0e, 0x54, 0x68, 0x69, 0x72, 0x64, 0x54,
	0x69, 0x65, 0x72, 0x41, 0x75, 0x64, 0x69, 0x74, 0x10, 0x0d, 0x12, 0x1c, 0x0a, 0x18, 0x46, 0x69,
	0x72, 0x73, 0x74, 0x54, 0x69, 0x65, 0x72, 0x41, 0x64, 0x6d, 0x69, 0x6e, 0x50, 0x72, 0x6f, 0x63,
	0x65, 0x73, 0x73, 0x69, 0x6e, 0x67, 0x10, 0x0e, 0x12, 0x18, 0x0a, 0x14, 0x46, 0x69, 0x72, 0x73,
	0x74, 0x54, 0x69, 0x65, 0x72, 0x41, 0x64, 0x6d, 0x69, 0x6e, 0x52, 0x65, 0x66, 0x69, 0x6c, 0x6c,
	0x10, 0x0f, 0x12, 0x14, 0x0a, 0x10, 0x56, 0x69, 0x70, 0x41, 0x67, 0x65, 0x6e, 0x74, 0x52, 0x65,
	0x73, 0x6f, 0x6c, 0x76, 0x65, 0x64, 0x10, 0x10, 0x2a, 0xb8, 0x01, 0x0a, 0x0a, 0x54, 0x6b, 0x50,
	0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x12, 0x15, 0x0a, 0x11, 0x54, 0x6b, 0x50, 0x72, 0x6f,
	0x67, 0x72, 0x65, 0x73, 0x73, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x10, 0x00, 0x12, 0x12,
	0x0a, 0x0e, 0x54, 0x6b, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x44, 0x6f, 0x6e, 0x65,
	0x10, 0x01, 0x12, 0x18, 0x0a, 0x14, 0x54, 0x6b, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73,
	0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x66, 0x69, 0x6c, 0x6c, 0x10, 0x02, 0x12, 0x17, 0x0a, 0x13,
	0x54, 0x6b, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x55, 0x73, 0x65, 0x72, 0x44, 0x6f,
	0x69, 0x6e, 0x67, 0x10, 0x03, 0x12, 0x20, 0x0a, 0x1c, 0x54, 0x6b, 0x50, 0x72, 0x6f, 0x67, 0x72,
	0x65, 0x73, 0x73, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x66, 0x69, 0x6c, 0x6c, 0x4f, 0x76, 0x65,
	0x72, 0x54, 0x69, 0x6d, 0x65, 0x10, 0x04, 0x12, 0x14, 0x0a, 0x10, 0x54, 0x6b, 0x50, 0x72, 0x6f,
	0x67, 0x72, 0x65, 0x73, 0x73, 0x52, 0x65, 0x6f, 0x70, 0x65, 0x6e, 0x10, 0x05, 0x12, 0x14, 0x0a,
	0x10, 0x54, 0x6b, 0x50, 0x72, 0x6f, 0x67, 0x72, 0x65, 0x73, 0x73, 0x52, 0x65, 0x6a, 0x65, 0x63,
	0x74, 0x10, 0x06, 0x2a, 0x7d, 0x0a, 0x0c, 0x54, 0x6b, 0x43, 0x6c, 0x6f, 0x73, 0x65, 0x64, 0x52,
	0x6f, 0x6c, 0x65, 0x12, 0x10, 0x0a, 0x0c, 0x54, 0x6b, 0x43, 0x6c, 0x6f, 0x73, 0x65, 0x64, 0x4e,
	0x6f, 0x6e, 0x65, 0x10, 0x00, 0x12, 0x12, 0x0a, 0x0e, 0x54, 0x6b, 0x50, 0x6c, 0x61, 0x79, 0x65,
	0x72, 0x43, 0x6c, 0x6f, 0x73, 0x65, 0x64, 0x10, 0x01, 0x12, 0x13, 0x0a, 0x0f, 0x54, 0x6b, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x6c, 0x6f, 0x73, 0x65, 0x64, 0x10, 0x02, 0x12, 0x19,
	0x0a, 0x15, 0x54, 0x6b, 0x53, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x54, 0x69, 0x6d, 0x65, 0x6f, 0x75,
	0x74, 0x43, 0x6c, 0x6f, 0x73, 0x65, 0x64, 0x10, 0x03, 0x12, 0x17, 0x0a, 0x13, 0x54, 0x6b, 0x53,
	0x79, 0x73, 0x74, 0x65, 0x6d, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x43, 0x6c, 0x6f, 0x73, 0x65, 0x64,
	0x10, 0x04, 0x2a, 0x5c, 0x0a, 0x0f, 0x55, 0x73, 0x65, 0x72, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1a, 0x0a, 0x16, 0x55, 0x73, 0x65, 0x72, 0x4c, 0x6f, 0x67,
	0x69, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x10,
	0x00, 0x12, 0x16, 0x0a, 0x12, 0x55, 0x73, 0x65, 0x72, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x59, 0x65, 0x73, 0x10, 0x01, 0x12, 0x15, 0x0a, 0x11, 0x55, 0x73, 0x65,
	0x72, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x4e, 0x6f, 0x10, 0x02,
	0x2a, 0x4d, 0x0a, 0x09, 0x43, 0x6f, 0x6d, 0x6d, 0x75, 0x54, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a,
	0x10, 0x43, 0x6f, 0x6d, 0x6d, 0x75, 0x54, 0x79, 0x70, 0x65, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77,
	0x6e, 0x10, 0x00, 0x12, 0x15, 0x0a, 0x11, 0x43, 0x6f, 0x6d, 0x6d, 0x75, 0x54, 0x79, 0x70, 0x65,
	0x44, 0x69, 0x61, 0x6c, 0x6f, 0x67, 0x75, 0x65, 0x10, 0x01, 0x12, 0x13, 0x0a, 0x0f, 0x43, 0x6f,
	0x6d, 0x6d, 0x75, 0x54, 0x79, 0x70, 0x65, 0x52, 0x65, 0x6d, 0x61, 0x72, 0x6b, 0x10, 0x02, 0x2a,
	0x5b, 0x0a, 0x19, 0x44, 0x69, 0x73, 0x63, 0x6f, 0x72, 0x64, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x11, 0x0a, 0x0d,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x10, 0x00, 0x12,
	0x14, 0x0a, 0x10, 0x57, 0x61, 0x69, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65,
	0x70, 0x6c, 0x79, 0x10, 0x01, 0x12, 0x15, 0x0a, 0x11, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x48, 0x61, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x69, 0x65, 0x64, 0x10, 0x02, 0x2a, 0x37, 0x0a, 0x0c,
	0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x47, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x12, 0x11, 0x0a, 0x0d,
	0x47, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x10, 0x00, 0x12,
	0x08, 0x0a, 0x04, 0x4d, 0x61, 0x6c, 0x65, 0x10, 0x01, 0x12, 0x0a, 0x0a, 0x06, 0x46, 0x65, 0x6d,
	0x61, 0x6c, 0x65, 0x10, 0x02, 0x2a, 0xb6, 0x01, 0x0a, 0x14, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72,
	0x45, 0x64, 0x75, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x19,
	0x0a, 0x15, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x45, 0x64, 0x75, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x10, 0x00, 0x12, 0x11, 0x0a, 0x0d, 0x50, 0x72, 0x69,
	0x6d, 0x61, 0x72, 0x79, 0x53, 0x63, 0x68, 0x6f, 0x6f, 0x6c, 0x10, 0x01, 0x12, 0x14, 0x0a, 0x10,
	0x4a, 0x75, 0x6e, 0x69, 0x6f, 0x72, 0x48, 0x69, 0x67, 0x68, 0x53, 0x63, 0x68, 0x6f, 0x6f, 0x6c,
	0x10, 0x02, 0x12, 0x14, 0x0a, 0x10, 0x53, 0x65, 0x6e, 0x69, 0x6f, 0x72, 0x48, 0x69, 0x67, 0x68,
	0x53, 0x63, 0x68, 0x6f, 0x6f, 0x6c, 0x10, 0x03, 0x12, 0x11, 0x0a, 0x0d, 0x55, 0x6e, 0x64, 0x65,
	0x72, 0x47, 0x72, 0x61, 0x64, 0x75, 0x61, 0x74, 0x65, 0x10, 0x04, 0x12, 0x0c, 0x0a, 0x08, 0x47,
	0x72, 0x61, 0x64, 0x75, 0x61, 0x74, 0x65, 0x10, 0x05, 0x12, 0x0a, 0x0a, 0x06, 0x44, 0x6f, 0x63,
	0x74, 0x6f, 0x72, 0x10, 0x06, 0x12, 0x0b, 0x0a, 0x07, 0x50, 0x6f, 0x73, 0x74, 0x64, 0x6f, 0x63,
	0x10, 0x07, 0x12, 0x0a, 0x0a, 0x06, 0x4f, 0x74, 0x68, 0x65, 0x72, 0x73, 0x10, 0x08, 0x2a, 0x63,
	0x0a, 0x13, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x4d, 0x61, 0x72, 0x72, 0x69, 0x61, 0x67, 0x65,
	0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x18, 0x0a, 0x14, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e,
	0x4d, 0x61, 0x72, 0x72, 0x69, 0x61, 0x67, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x10, 0x00, 0x12,
	0x0b, 0x0a, 0x07, 0x4d, 0x61, 0x72, 0x72, 0x69, 0x65, 0x64, 0x10, 0x01, 0x12, 0x0a, 0x0a, 0x06,
	0x53, 0x69, 0x6e, 0x67, 0x6c, 0x65, 0x10, 0x02, 0x12, 0x0c, 0x0a, 0x08, 0x44, 0x69, 0x76, 0x6f,
	0x72, 0x63, 0x65, 0x64, 0x10, 0x03, 0x12, 0x0b, 0x0a, 0x07, 0x57, 0x69, 0x64, 0x6f, 0x77, 0x65,
	0x64, 0x10, 0x04, 0x2a, 0x7f, 0x0a, 0x14, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x46, 0x65, 0x72,
	0x74, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x19, 0x0a, 0x15, 0x55,
	0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x46, 0x65, 0x72, 0x74, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x53,
	0x74, 0x61, 0x74, 0x65, 0x10, 0x00, 0x12, 0x0b, 0x0a, 0x07, 0x4e, 0x6f, 0x43, 0x68, 0x69, 0x6c,
	0x64, 0x10, 0x01, 0x12, 0x0c, 0x0a, 0x08, 0x4f, 0x6e, 0x65, 0x43, 0x68, 0x69, 0x6c, 0x64, 0x10,
	0x02, 0x12, 0x0b, 0x0a, 0x07, 0x54, 0x77, 0x6f, 0x4b, 0x69, 0x64, 0x73, 0x10, 0x03, 0x12, 0x0d,
	0x0a, 0x09, 0x54, 0x68, 0x72, 0x65, 0x65, 0x4b, 0x69, 0x64, 0x73, 0x10, 0x04, 0x12, 0x15, 0x0a,
	0x11, 0x4d, 0x6f, 0x72, 0x65, 0x54, 0x68, 0x61, 0x6e, 0x54, 0x68, 0x72, 0x65, 0x65, 0x4b, 0x69,
	0x64, 0x73, 0x10, 0x05, 0x2a, 0x3a, 0x0a, 0x0e, 0x50, 0x6c, 0x61, 0x79, 0x65, 0x72, 0x56, 0x69,
	0x70, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x13, 0x0a, 0x0f, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77,
	0x6e, 0x56, 0x69, 0x70, 0x53, 0x74, 0x61, 0x74, 0x65, 0x10, 0x00, 0x12, 0x0a, 0x0a, 0x06, 0x4e,
	0x6f, 0x6e, 0x56, 0x69, 0x70, 0x10, 0x01, 0x12, 0x07, 0x0a, 0x03, 0x56, 0x69, 0x70, 0x10, 0x02,
	0x2a, 0xf6, 0x01, 0x0a, 0x07, 0x4f, 0x70, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x12, 0x0a, 0x0e,
	0x4f, 0x70, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x10, 0x00,
	0x12, 0x13, 0x0a, 0x0f, 0x4f, 0x70, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x50, 0x6f, 0x72, 0x74, 0x72,
	0x61, 0x69, 0x74, 0x10, 0x01, 0x12, 0x19, 0x0a, 0x15, 0x4f, 0x70, 0x47, 0x72, 0x6f, 0x75, 0x70,
	0x4d, 0x61, 0x69, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x10, 0x02,
	0x12, 0x1a, 0x0a, 0x16, 0x4f, 0x70, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x53, 0x65, 0x6e, 0x64, 0x54,
	0x65, 0x78, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x10, 0x03, 0x12, 0x16, 0x0a, 0x12,
	0x4f, 0x70, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x45, 0x64, 0x69, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x10, 0x04, 0x12, 0x13, 0x0a, 0x0f, 0x4f, 0x70, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x53,
	0x65, 0x6e, 0x64, 0x46, 0x69, 0x6c, 0x65, 0x10, 0x05, 0x12, 0x15, 0x0a, 0x11, 0x4f, 0x70, 0x47,
	0x72, 0x6f, 0x75, 0x70, 0x45, 0x78, 0x61, 0x6d, 0x69, 0x6e, 0x65, 0x54, 0x70, 0x6c, 0x10, 0x14,
	0x12, 0x15, 0x0a, 0x11, 0x4f, 0x70, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x45, 0x78, 0x61, 0x6d, 0x69,
	0x6e, 0x65, 0x44, 0x73, 0x63, 0x10, 0x15, 0x12, 0x15, 0x0a, 0x11, 0x4f, 0x70, 0x47, 0x72, 0x6f,
	0x75, 0x70, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x53, 0x65, 0x6e, 0x64, 0x10, 0x16, 0x12, 0x19,
	0x0a, 0x15, 0x4f, 0x70, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x53, 0x75, 0x72, 0x76, 0x65, 0x79, 0x42,
	0x61, 0x74, 0x63, 0x68, 0x47, 0x65, 0x6e, 0x10, 0x17, 0x2a, 0x6c, 0x0a, 0x08, 0x4f, 0x70, 0x41,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x13, 0x0a, 0x0f, 0x4f, 0x70, 0x41, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x10, 0x00, 0x12, 0x0f, 0x0a, 0x0b, 0x4f, 0x70,
	0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x41, 0x64, 0x64, 0x10, 0x01, 0x12, 0x12, 0x0a, 0x0e, 0x4f,
	0x70, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x10, 0x02, 0x12,
	0x12, 0x0a, 0x0e, 0x4f, 0x70, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x10, 0x03, 0x12, 0x12, 0x0a, 0x0e, 0x4f, 0x70, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x10, 0x04, 0x2a, 0xb7, 0x01, 0x0a, 0x0c, 0x51, 0x75, 0x65, 0x73,
	0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x17, 0x0a, 0x13, 0x55, 0x6e, 0x6b, 0x6e,
	0x6f, 0x77, 0x6e, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x10,
	0x00, 0x12, 0x14, 0x0a, 0x10, 0x47, 0x61, 0x6d, 0x65, 0x43, 0x6f, 0x6e, 0x73, 0x75, 0x6c, 0x74,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x10, 0x01, 0x12, 0x12, 0x0a, 0x0e, 0x47, 0x61, 0x6d, 0x65, 0x53,
	0x75, 0x67, 0x67, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x10, 0x02, 0x12, 0x11, 0x0a, 0x0d, 0x47,
	0x61, 0x6d, 0x65, 0x45, 0x78, 0x63, 0x65, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x10, 0x03, 0x12, 0x1d,
	0x0a, 0x19, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x41, 0x6e, 0x64,
	0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x4d, 0x65, 0x72, 0x67, 0x65, 0x10, 0x04, 0x12, 0x1f, 0x0a,
	0x1b, 0x43, 0x6f, 0x6d, 0x70, 0x6c, 0x61, 0x69, 0x6e, 0x74, 0x4f, 0x72, 0x4e, 0x65, 0x67, 0x61,
	0x74, 0x69, 0x76, 0x65, 0x46, 0x65, 0x65, 0x64, 0x62, 0x61, 0x63, 0x6b, 0x10, 0x05, 0x12, 0x11,
	0x0a, 0x0d, 0x4f, 0x74, 0x68, 0x65, 0x72, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x10,
	0x06, 0x2a, 0x4f, 0x0a, 0x14, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x61, 0x6e,
	0x64, 0x6c, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x17, 0x0a, 0x13, 0x55, 0x6e, 0x6b,
	0x6e, 0x6f, 0x77, 0x6e, 0x48, 0x61, 0x6e, 0x64, 0x6c, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x10, 0x00, 0x12, 0x10, 0x0a, 0x0c, 0x55, 0x6e, 0x64, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x63, 0x65,
	0x73, 0x73, 0x10, 0x01, 0x12, 0x0c, 0x0a, 0x08, 0x46, 0x69, 0x6e, 0x69, 0x73, 0x68, 0x65, 0x64,
	0x10, 0x02, 0x2a, 0x74, 0x0a, 0x0f, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x53, 0x79, 0x73, 0x74,
	0x65, 0x6d, 0x54, 0x61, 0x67, 0x12, 0x0e, 0x0a, 0x0a, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e,
	0x54, 0x61, 0x67, 0x10, 0x00, 0x12, 0x14, 0x0a, 0x10, 0x47, 0x72, 0x65, 0x65, 0x6e, 0x43, 0x68,
	0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x55, 0x73, 0x65, 0x72, 0x10, 0x0a, 0x12, 0x13, 0x0a, 0x0f, 0x50,
	0x72, 0x69, 0x76, 0x61, 0x74, 0x65, 0x5a, 0x6f, 0x6e, 0x65, 0x55, 0x73, 0x65, 0x72, 0x10, 0x0b,
	0x12, 0x11, 0x0a, 0x0d, 0x45, 0x6c, 0x66, 0x69, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x55, 0x73, 0x65,
	0x72, 0x10, 0x0c, 0x12, 0x13, 0x0a, 0x0f, 0x50, 0x72, 0x69, 0x76, 0x61, 0x74, 0x65, 0x5a, 0x6f,
	0x6e, 0x65, 0x43, 0x61, 0x72, 0x64, 0x10, 0x0d, 0x2a, 0xa0, 0x01, 0x0a, 0x0d, 0x41, 0x49, 0x50,
	0x6f, 0x6c, 0x69, 0x73, 0x68, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x12, 0x16, 0x0a, 0x12, 0x55, 0x6e,
	0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x50, 0x6f, 0x6c, 0x69, 0x73, 0x68, 0x4c, 0x61, 0x62, 0x65, 0x6c,
	0x10, 0x00, 0x12, 0x17, 0x0a, 0x13, 0x50, 0x6f, 0x6c, 0x69, 0x73, 0x68, 0x46, 0x72, 0x69, 0x65,
	0x6e, 0x64, 0x6c, 0x79, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x10, 0x01, 0x12, 0x16, 0x0a, 0x12, 0x50,
	0x6f, 0x6c, 0x69, 0x73, 0x68, 0x43, 0x6f, 0x6e, 0x63, 0x69, 0x73, 0x65, 0x4c, 0x61, 0x62, 0x65,
	0x6c, 0x10, 0x02, 0x12, 0x15, 0x0a, 0x11, 0x50, 0x6f, 0x6c, 0x69, 0x73, 0x68, 0x46, 0x6f, 0x72,
	0x6d, 0x61, 0x6c, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x10, 0x03, 0x12, 0x15, 0x0a, 0x11, 0x50, 0x6f,
	0x6c, 0x69, 0x73, 0x68, 0x50, 0x6f, 0x6c, 0x69, 0x74, 0x65, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x10,
	0x04, 0x12, 0x18, 0x0a, 0x14, 0x50, 0x6f, 0x6c, 0x69, 0x73, 0x68, 0x43, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x69, 0x7a, 0x65, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x10, 0x05, 0x2a, 0x67, 0x0a, 0x0d, 0x54,
	0x61, 0x67, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x54, 0x79, 0x70, 0x65, 0x12, 0x15, 0x0a, 0x11,
	0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x54, 0x79, 0x70,
	0x65, 0x10, 0x00, 0x12, 0x14, 0x0a, 0x10, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x54, 0x79, 0x70, 0x65, 0x10, 0x01, 0x12, 0x15, 0x0a, 0x11, 0x44, 0x69, 0x73,
	0x63, 0x6f, 0x72, 0x64, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x54, 0x79, 0x70, 0x65, 0x10, 0x02,
	0x12, 0x12, 0x0a, 0x0e, 0x4c, 0x69, 0x6e, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x54, 0x79,
	0x70, 0x65, 0x10, 0x03, 0x2a, 0x72, 0x0a, 0x0a, 0x44, 0x63, 0x50, 0x6f, 0x6f, 0x6c, 0x53, 0x6f,
	0x72, 0x74, 0x12, 0x19, 0x0a, 0x15, 0x44, 0x63, 0x50, 0x6f, 0x6f, 0x6c, 0x53, 0x6f, 0x72, 0x74,
	0x57, 0x61, 0x69, 0x74, 0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x10, 0x00, 0x12, 0x14, 0x0a,
	0x10, 0x44, 0x63, 0x50, 0x6f, 0x6f, 0x6c, 0x53, 0x6f, 0x72, 0x74, 0x57, 0x61, 0x69, 0x74, 0x54,
	0x6d, 0x10, 0x01, 0x12, 0x19, 0x0a, 0x15, 0x44, 0x63, 0x50, 0x6f, 0x6f, 0x6c, 0x53, 0x6f, 0x72,
	0x74, 0x4c, 0x61, 0x73, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x54, 0x6d, 0x10, 0x02, 0x12, 0x18,
	0x0a, 0x14, 0x44, 0x63, 0x50, 0x6f, 0x6f, 0x6c, 0x53, 0x6f, 0x72, 0x74, 0x70, 0x61, 0x69, 0x64,
	0x41, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x10, 0x03, 0x2a, 0x2d, 0x0a, 0x04, 0x53, 0x56, 0x49, 0x50,
	0x12, 0x0c, 0x0a, 0x08, 0x53, 0x56, 0x49, 0x50, 0x4e, 0x6f, 0x6e, 0x65, 0x10, 0x00, 0x12, 0x0b,
	0x0a, 0x07, 0x53, 0x56, 0x49, 0x50, 0x59, 0x65, 0x73, 0x10, 0x01, 0x12, 0x0a, 0x0a, 0x06, 0x53,
	0x56, 0x49, 0x50, 0x4e, 0x6f, 0x10, 0x02, 0x2a, 0x5a, 0x0a, 0x0e, 0x53, 0x65, 0x61, 0x72, 0x63,
	0x68, 0x54, 0x79, 0x70, 0x65, 0x45, 0x6e, 0x75, 0x6d, 0x12, 0x12, 0x0a, 0x0e, 0x53, 0x65, 0x61,
	0x72, 0x63, 0x68, 0x54, 0x79, 0x70, 0x65, 0x4e, 0x6f, 0x6e, 0x65, 0x10, 0x00, 0x12, 0x18, 0x0a,
	0x14, 0x53, 0x65, 0x61, 0x72, 0x63, 0x68, 0x54, 0x79, 0x70, 0x65, 0x53, 0x75, 0x62, 0x6d, 0x69,
	0x74, 0x55, 0x73, 0x65, 0x72, 0x10, 0x01, 0x12, 0x1a, 0x0a, 0x16, 0x53, 0x65, 0x61, 0x72, 0x63,
	0x68, 0x54, 0x79, 0x70, 0x65, 0x51, 0x75, 0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x55, 0x73, 0x65,
	0x72, 0x10, 0x02, 0x2a, 0x83, 0x01, 0x0a, 0x0c, 0x55, 0x73, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65,
	0x45, 0x6e, 0x75, 0x6d, 0x12, 0x10, 0x0a, 0x0c, 0x55, 0x73, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65,
	0x4e, 0x6f, 0x6e, 0x65, 0x10, 0x00, 0x12, 0x17, 0x0a, 0x13, 0x55, 0x73, 0x65, 0x72, 0x54, 0x79,
	0x70, 0x65, 0x4c, 0x6f, 0x6e, 0x67, 0x56, 0x69, 0x70, 0x55, 0x73, 0x65, 0x72, 0x10, 0x01, 0x12,
	0x19, 0x0a, 0x15, 0x55, 0x73, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74,
	0x54, 0x69, 0x6d, 0x65, 0x55, 0x73, 0x65, 0x72, 0x10, 0x02, 0x12, 0x14, 0x0a, 0x10, 0x55, 0x73,
	0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x50, 0x61, 0x69, 0x64, 0x55, 0x73, 0x65, 0x72, 0x10, 0x03,
	0x12, 0x17, 0x0a, 0x13, 0x55, 0x73, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x52, 0x65, 0x67, 0x75,
	0x6c, 0x61, 0x72, 0x55, 0x73, 0x65, 0x72, 0x10, 0x04, 0x2a, 0x5b, 0x0a, 0x10, 0x52, 0x65, 0x64,
	0x50, 0x6f, 0x69, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x45, 0x6e, 0x75, 0x6d, 0x12, 0x14, 0x0a,
	0x10, 0x52, 0x65, 0x64, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x4e, 0x6f, 0x6e,
	0x65, 0x10, 0x00, 0x12, 0x17, 0x0a, 0x13, 0x52, 0x65, 0x64, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x54,
	0x79, 0x70, 0x65, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x10, 0x01, 0x12, 0x18, 0x0a, 0x14,
	0x52, 0x65, 0x64, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x4f, 0x76, 0x65, 0x72,
	0x74, 0x69, 0x6d, 0x65, 0x10, 0x02, 0x2a, 0x5b, 0x0a, 0x0e, 0x43, 0x68, 0x61, 0x74, 0x4f, 0x72,
	0x46, 0x6f, 0x72, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x12, 0x19, 0x0a, 0x15, 0x43, 0x68, 0x61, 0x74,
	0x4f, 0x72, 0x46, 0x6f, 0x72, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77,
	0x6e, 0x10, 0x00, 0x12, 0x16, 0x0a, 0x12, 0x43, 0x68, 0x61, 0x74, 0x4f, 0x72, 0x46, 0x6f, 0x72,
	0x6d, 0x54, 0x79, 0x70, 0x65, 0x43, 0x68, 0x61, 0x74, 0x10, 0x01, 0x12, 0x16, 0x0a, 0x12, 0x43,
	0x68, 0x61, 0x74, 0x4f, 0x72, 0x46, 0x6f, 0x72, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x46, 0x6f, 0x72,
	0x6d, 0x10, 0x02, 0x2a, 0x8c, 0x01, 0x0a, 0x12, 0x54, 0x72, 0x61, 0x69, 0x6e, 0x69, 0x6e, 0x67,
	0x54, 0x61, 0x73, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1b, 0x0a, 0x17, 0x50, 0x72,
	0x6f, 0x63, 0x65, 0x73, 0x73, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x49, 0x6e, 0x69, 0x74, 0x10, 0x00, 0x12, 0x1c, 0x0a, 0x18, 0x50, 0x72, 0x6f, 0x63, 0x65,
	0x73, 0x73, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x44, 0x6f,
	0x69, 0x6e, 0x67, 0x10, 0x0a, 0x12, 0x1b, 0x0a, 0x17, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73,
	0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x46, 0x61, 0x69, 0x6c,
	0x10, 0x14, 0x12, 0x1e, 0x0a, 0x1a, 0x50, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x54, 0x69, 0x63,
	0x6b, 0x65, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x53, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73,
	0x10, 0x1e, 0x2a, 0x86, 0x01, 0x0a, 0x12, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1d, 0x0a, 0x19, 0x43, 0x6f, 0x6e,
	0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x55,
	0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x10, 0x00, 0x12, 0x18, 0x0a, 0x14, 0x43, 0x6f, 0x6e, 0x76,
	0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x4f, 0x6e,
	0x10, 0x01, 0x12, 0x1a, 0x0a, 0x16, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x57, 0x61, 0x69, 0x74, 0x10, 0x02, 0x12, 0x1b,
	0x0a, 0x17, 0x43, 0x6f, 0x6e, 0x76, 0x65, 0x72, 0x73, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x43, 0x6c, 0x6f, 0x73, 0x65, 0x10, 0x0a, 0x2a, 0x98, 0x01, 0x0a, 0x09,
	0x53, 0x6f, 0x6c, 0x76, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x19, 0x0a, 0x15, 0x53, 0x6f, 0x6c,
	0x76, 0x65, 0x54, 0x79, 0x70, 0x65, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x54, 0x69, 0x63, 0x6b,
	0x65, 0x74, 0x10, 0x00, 0x12, 0x19, 0x0a, 0x15, 0x53, 0x6f, 0x6c, 0x76, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x4d, 0x61, 0x6e, 0x75, 0x61, 0x6c, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x10, 0x01, 0x12,
	0x1c, 0x0a, 0x18, 0x53, 0x6f, 0x6c, 0x76, 0x65, 0x54, 0x79, 0x70, 0x65, 0x41, 0x75, 0x74, 0x6f,
	0x52, 0x65, 0x70, 0x6c, 0x79, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x10, 0x02, 0x12, 0x1a, 0x0a,
	0x16, 0x53, 0x6f, 0x6c, 0x76, 0x65, 0x54, 0x79, 0x70, 0x65, 0x49, 0x6e, 0x76, 0x61, 0x6c, 0x69,
	0x64, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x10, 0x03, 0x12, 0x1b, 0x0a, 0x17, 0x53, 0x6f, 0x6c,
	0x76, 0x65, 0x54, 0x79, 0x70, 0x65, 0x53, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x54, 0x69,
	0x63, 0x6b, 0x65, 0x74, 0x10, 0x04, 0x2a, 0x37, 0x0a, 0x0a, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x10, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x54, 0x79,
	0x70, 0x65, 0x4e, 0x6f, 0x72, 0x6d, 0x61, 0x6c, 0x10, 0x00, 0x12, 0x13, 0x0a, 0x0f, 0x54, 0x69,
	0x63, 0x6b, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x4e, 0x65, 0x77, 0x41, 0x69, 0x10, 0x01, 0x2a,
	0x42, 0x0a, 0x0d, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x54, 0x61, 0x67, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x18, 0x0a, 0x14, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x54, 0x61, 0x67, 0x54, 0x79, 0x70,
	0x65, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77, 0x6e, 0x10, 0x00, 0x12, 0x17, 0x0a, 0x13, 0x54, 0x69,
	0x63, 0x6b, 0x65, 0x74, 0x54, 0x61, 0x67, 0x54, 0x79, 0x70, 0x65, 0x4e, 0x6f, 0x72, 0x6d, 0x61,
	0x6c, 0x10, 0x01, 0x2a, 0xb2, 0x01, 0x0a, 0x0f, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x41, 0x69,
	0x54, 0x61, 0x67, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1a, 0x0a, 0x16, 0x54, 0x69, 0x63, 0x6b, 0x65,
	0x74, 0x41, 0x69, 0x54, 0x61, 0x67, 0x54, 0x79, 0x70, 0x65, 0x55, 0x6e, 0x6b, 0x6e, 0x6f, 0x77,
	0x6e, 0x10, 0x00, 0x12, 0x16, 0x0a, 0x12, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x41, 0x69, 0x54,
	0x61, 0x67, 0x54, 0x79, 0x70, 0x65, 0x41, 0x73, 0x6b, 0x10, 0x01, 0x12, 0x1c, 0x0a, 0x18, 0x54,
	0x69, 0x63, 0x6b, 0x65, 0x74, 0x41, 0x69, 0x54, 0x61, 0x67, 0x54, 0x79, 0x70, 0x65, 0x43, 0x6f,
	0x6d, 0x70, 0x6c, 0x61, 0x69, 0x6e, 0x74, 0x10, 0x02, 0x12, 0x1d, 0x0a, 0x19, 0x54, 0x69, 0x63,
	0x6b, 0x65, 0x74, 0x41, 0x69, 0x54, 0x61, 0x67, 0x54, 0x79, 0x70, 0x65, 0x53, 0x75, 0x67, 0x67,
	0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x10, 0x03, 0x12, 0x17, 0x0a, 0x13, 0x54, 0x69, 0x63, 0x6b,
	0x65, 0x74, 0x41, 0x69, 0x54, 0x61, 0x67, 0x54, 0x79, 0x70, 0x65, 0x45, 0x6c, 0x73, 0x65, 0x10,
	0x04, 0x12, 0x15, 0x0a, 0x11, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x41, 0x69, 0x54, 0x61, 0x67,
	0x4e, 0x6f, 0x74, 0x54, 0x61, 0x67, 0x10, 0x05, 0x42, 0x06, 0x5a, 0x04, 0x2e, 0x3b, 0x70, 0x62,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_enum_proto_rawDescOnce sync.Once
	file_enum_proto_rawDescData = file_enum_proto_rawDesc
)

func file_enum_proto_rawDescGZIP() []byte {
	file_enum_proto_rawDescOnce.Do(func() {
		file_enum_proto_rawDescData = protoimpl.X.CompressGZIP(file_enum_proto_rawDescData)
	})
	return file_enum_proto_rawDescData
}

var file_enum_proto_enumTypes = make([]protoimpl.EnumInfo, 48)
var file_enum_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_enum_proto_goTypes = []interface{}{
	(Bind)(0),                      // 0: pb.Bind
	(SceneType)(0),                 // 1: pb.SceneType
	(Origin)(0),                    // 2: pb.Origin
	(Status)(0),                    // 3: pb.Status
	(CsiLevel)(0),                  // 4: pb.CsiLevel
	(NpsLevel)(0),                  // 5: pb.NpsLevel
	(CsiLabel)(0),                  // 6: pb.CsiLabel
	(FilterTagEnum)(0),             // 7: pb.FilterTagEnum
	(FilterEnum)(0),                // 8: pb.FilterEnum
	(CreatorType)(0),               // 9: pb.CreatorType
	(Workbench)(0),                 // 10: pb.Workbench
	(TicketSys)(0),                 // 11: pb.TicketSys
	(RelateType)(0),                // 12: pb.RelateType
	(TicketEvent)(0),               // 13: pb.TicketEvent
	(TkEvent)(0),                   // 14: pb.TkEvent
	(UserRole)(0),                  // 15: pb.UserRole
	(TkStatus)(0),                  // 16: pb.TkStatus
	(TkStage)(0),                   // 17: pb.TkStage
	(TicketStage)(0),               // 18: pb.TicketStage
	(TkProgress)(0),                // 19: pb.TkProgress
	(TkClosedRole)(0),              // 20: pb.TkClosedRole
	(UserLoginStatus)(0),           // 21: pb.UserLoginStatus
	(CommuType)(0),                 // 22: pb.CommuType
	(DiscordServiceReplyStatus)(0), // 23: pb.DiscordServiceReplyStatus
	(PlayerGender)(0),              // 24: pb.PlayerGender
	(PlayerEducationLevel)(0),      // 25: pb.PlayerEducationLevel
	(PlayerMarriageState)(0),       // 26: pb.PlayerMarriageState
	(PlayerFertilityState)(0),      // 27: pb.PlayerFertilityState
	(PlayerVipState)(0),            // 28: pb.PlayerVipState
	(OpGroup)(0),                   // 29: pb.OpGroup
	(OpAction)(0),                  // 30: pb.OpAction
	(QuestionType)(0),              // 31: pb.QuestionType
	(QuestionHandleStatus)(0),      // 32: pb.QuestionHandleStatus
	(TicketSystemTag)(0),           // 33: pb.TicketSystemTag
	(AIPolishLabel)(0),             // 34: pb.AIPolishLabel
	(TagConfigType)(0),             // 35: pb.TagConfigType
	(DcPoolSort)(0),                // 36: pb.DcPoolSort
	(SVIP)(0),                      // 37: pb.SVIP
	(SearchTypeEnum)(0),            // 38: pb.SearchTypeEnum
	(UserTypeEnum)(0),              // 39: pb.UserTypeEnum
	(RedPointTypeEnum)(0),          // 40: pb.RedPointTypeEnum
	(ChatOrFormType)(0),            // 41: pb.ChatOrFormType
	(TrainingTaskStatus)(0),        // 42: pb.TrainingTaskStatus
	(ConversationStatus)(0),        // 43: pb.ConversationStatus
	(SolveType)(0),                 // 44: pb.SolveType
	(TicketType)(0),                // 45: pb.TicketType
	(TicketTagType)(0),             // 46: pb.TicketTagType
	(TicketAiTagType)(0),           // 47: pb.TicketAiTagType
	(*RetEnum)(nil),                // 48: pb.RetEnum
}
var file_enum_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_enum_proto_init() }
func file_enum_proto_init() {
	if File_enum_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_enum_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RetEnum); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_enum_proto_rawDesc,
			NumEnums:      48,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_enum_proto_goTypes,
		DependencyIndexes: file_enum_proto_depIdxs,
		EnumInfos:         file_enum_proto_enumTypes,
		MessageInfos:      file_enum_proto_msgTypes,
	}.Build()
	File_enum_proto = out.File
	file_enum_proto_rawDesc = nil
	file_enum_proto_goTypes = nil
	file_enum_proto_depIdxs = nil
}
