// Code generated by protoc-gen-go. DO NOT EDIT.
// source: egress.proto

package pb

import (
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

type CatSubRequest struct {
	// 项目标识 @gotags: validate:"required_without=FpxAppId"
	GameId uint32 `protobuf:"varint,1,opt,name=game_id,json=gameId,proto3" json:"game_id" validate:"required_without=FpxAppId"`
	// 项目标识 @gotags: validate:"required_without=GameId"
	FpxAppId string `protobuf:"bytes,2,opt,name=fpx_app_id,json=fpxAppId,proto3" json:"fpx_app_id" validate:"required_without=GameId"`
	// 语言标识 @gotags: validate:"required"
	Lang string `protobuf:"bytes,3,opt,name=lang,proto3" json:"lang" validate:"required"`
	// 类别ID @gotags: validate:"required"
	CatId uint32 `protobuf:"varint,4,opt,name=cat_id,json=catId,proto3" json:"cat_id" validate:"required"`
	// 国家code
	CountryCode string `protobuf:"bytes,5,opt,name=country_code,json=countryCode,proto3" json:"country_code"`
	// 渠道
	Channel string `protobuf:"bytes,6,opt,name=channel,proto3" json:"channel"`
	// 区服
	Sid string `protobuf:"bytes,7,opt,name=sid,proto3" json:"sid"`
	// json_data
	JsonData             string   `protobuf:"bytes,8,opt,name=json_data,json=jsonData,proto3" json:"json_data"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *CatSubRequest) Reset()         { *m = CatSubRequest{} }
func (m *CatSubRequest) String() string { return proto.CompactTextString(m) }
func (*CatSubRequest) ProtoMessage()    {}
func (*CatSubRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_e640ebe2e4e4631c, []int{0}
}

func (m *CatSubRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CatSubRequest.Unmarshal(m, b)
}
func (m *CatSubRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CatSubRequest.Marshal(b, m, deterministic)
}
func (m *CatSubRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CatSubRequest.Merge(m, src)
}
func (m *CatSubRequest) XXX_Size() int {
	return xxx_messageInfo_CatSubRequest.Size(m)
}
func (m *CatSubRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CatSubRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CatSubRequest proto.InternalMessageInfo

func (m *CatSubRequest) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *CatSubRequest) GetFpxAppId() string {
	if m != nil {
		return m.FpxAppId
	}
	return ""
}

func (m *CatSubRequest) GetLang() string {
	if m != nil {
		return m.Lang
	}
	return ""
}

func (m *CatSubRequest) GetCatId() uint32 {
	if m != nil {
		return m.CatId
	}
	return 0
}

func (m *CatSubRequest) GetCountryCode() string {
	if m != nil {
		return m.CountryCode
	}
	return ""
}

func (m *CatSubRequest) GetChannel() string {
	if m != nil {
		return m.Channel
	}
	return ""
}

func (m *CatSubRequest) GetSid() string {
	if m != nil {
		return m.Sid
	}
	return ""
}

func (m *CatSubRequest) GetJsonData() string {
	if m != nil {
		return m.JsonData
	}
	return ""
}

type EgressCatInfoResp struct {
	CatId        uint32            `protobuf:"varint,1,opt,name=cat_id,json=catId,proto3" json:"cat_id"`
	CatName      string            `protobuf:"bytes,2,opt,name=cat_name,json=catName,proto3" json:"cat_name"`
	RelateType   uint32            `protobuf:"varint,3,opt,name=relate_type,json=relateType,proto3" json:"relate_type"`
	ProcessId    uint32            `protobuf:"varint,4,opt,name=process_id,json=processId,proto3" json:"process_id"`
	TplId        uint32            `protobuf:"varint,5,opt,name=tpl_id,json=tplId,proto3" json:"tpl_id"`
	Tpl          string            `protobuf:"bytes,6,opt,name=tpl,proto3" json:"tpl"`
	Fields       string            `protobuf:"bytes,7,opt,name=fields,proto3" json:"fields"`
	Category     string            `protobuf:"bytes,8,opt,name=category,proto3" json:"category"`
	ReplyContent map[string]string `protobuf:"bytes,9,rep,name=reply_content,json=replyContent,proto3" json:"reply_content" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// 问题列表
	QuestionList []*QuestionInfo `protobuf:"bytes,10,rep,name=question_list,json=questionList,proto3" json:"question_list"`
	// chatOrForm 1: 聊天 2: 表单
	ChatOrForm           ChatOrFormType `protobuf:"varint,11,opt,name=chat_or_form,json=chatOrForm,proto3,enum=pb.ChatOrFormType" json:"chat_or_form"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-" gorm:"-"`
	XXX_unrecognized     []byte         `json:"-" gorm:"-"`
	XXX_sizecache        int32          `json:"-" gorm:"-"`
}

func (m *EgressCatInfoResp) Reset()         { *m = EgressCatInfoResp{} }
func (m *EgressCatInfoResp) String() string { return proto.CompactTextString(m) }
func (*EgressCatInfoResp) ProtoMessage()    {}
func (*EgressCatInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_e640ebe2e4e4631c, []int{1}
}

func (m *EgressCatInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EgressCatInfoResp.Unmarshal(m, b)
}
func (m *EgressCatInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EgressCatInfoResp.Marshal(b, m, deterministic)
}
func (m *EgressCatInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EgressCatInfoResp.Merge(m, src)
}
func (m *EgressCatInfoResp) XXX_Size() int {
	return xxx_messageInfo_EgressCatInfoResp.Size(m)
}
func (m *EgressCatInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_EgressCatInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_EgressCatInfoResp proto.InternalMessageInfo

func (m *EgressCatInfoResp) GetCatId() uint32 {
	if m != nil {
		return m.CatId
	}
	return 0
}

func (m *EgressCatInfoResp) GetCatName() string {
	if m != nil {
		return m.CatName
	}
	return ""
}

func (m *EgressCatInfoResp) GetRelateType() uint32 {
	if m != nil {
		return m.RelateType
	}
	return 0
}

func (m *EgressCatInfoResp) GetProcessId() uint32 {
	if m != nil {
		return m.ProcessId
	}
	return 0
}

func (m *EgressCatInfoResp) GetTplId() uint32 {
	if m != nil {
		return m.TplId
	}
	return 0
}

func (m *EgressCatInfoResp) GetTpl() string {
	if m != nil {
		return m.Tpl
	}
	return ""
}

func (m *EgressCatInfoResp) GetFields() string {
	if m != nil {
		return m.Fields
	}
	return ""
}

func (m *EgressCatInfoResp) GetCategory() string {
	if m != nil {
		return m.Category
	}
	return ""
}

func (m *EgressCatInfoResp) GetReplyContent() map[string]string {
	if m != nil {
		return m.ReplyContent
	}
	return nil
}

func (m *EgressCatInfoResp) GetQuestionList() []*QuestionInfo {
	if m != nil {
		return m.QuestionList
	}
	return nil
}

func (m *EgressCatInfoResp) GetChatOrForm() ChatOrFormType {
	if m != nil {
		return m.ChatOrForm
	}
	return ChatOrFormType_ChatOrFormTypeUnknown
}

type QuestionInfo struct {
	QuestionKey          string   `protobuf:"bytes,1,opt,name=question_key,json=questionKey,proto3" json:"question_key"`
	QuestionName         string   `protobuf:"bytes,2,opt,name=question_name,json=questionName,proto3" json:"question_name"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *QuestionInfo) Reset()         { *m = QuestionInfo{} }
func (m *QuestionInfo) String() string { return proto.CompactTextString(m) }
func (*QuestionInfo) ProtoMessage()    {}
func (*QuestionInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_e640ebe2e4e4631c, []int{2}
}

func (m *QuestionInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QuestionInfo.Unmarshal(m, b)
}
func (m *QuestionInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QuestionInfo.Marshal(b, m, deterministic)
}
func (m *QuestionInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QuestionInfo.Merge(m, src)
}
func (m *QuestionInfo) XXX_Size() int {
	return xxx_messageInfo_QuestionInfo.Size(m)
}
func (m *QuestionInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_QuestionInfo.DiscardUnknown(m)
}

var xxx_messageInfo_QuestionInfo proto.InternalMessageInfo

func (m *QuestionInfo) GetQuestionKey() string {
	if m != nil {
		return m.QuestionKey
	}
	return ""
}

func (m *QuestionInfo) GetQuestionName() string {
	if m != nil {
		return m.QuestionName
	}
	return ""
}

type CatOptRequest struct {
	// 项目标识 @gotags: validate:"required_without=FpxAppId"
	GameId uint32 `protobuf:"varint,1,opt,name=game_id,json=gameId,proto3" json:"game_id" validate:"required_without=FpxAppId"`
	// 项目标识 @gotags: validate:"required_without=GameId"
	FpxAppId string `protobuf:"bytes,2,opt,name=fpx_app_id,json=fpxAppId,proto3" json:"fpx_app_id" validate:"required_without=GameId"`
	// 语言标识 @gotags: validate:"required"
	Lang                 string   `protobuf:"bytes,3,opt,name=lang,proto3" json:"lang" validate:"required"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *CatOptRequest) Reset()         { *m = CatOptRequest{} }
func (m *CatOptRequest) String() string { return proto.CompactTextString(m) }
func (*CatOptRequest) ProtoMessage()    {}
func (*CatOptRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_e640ebe2e4e4631c, []int{3}
}

func (m *CatOptRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CatOptRequest.Unmarshal(m, b)
}
func (m *CatOptRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CatOptRequest.Marshal(b, m, deterministic)
}
func (m *CatOptRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CatOptRequest.Merge(m, src)
}
func (m *CatOptRequest) XXX_Size() int {
	return xxx_messageInfo_CatOptRequest.Size(m)
}
func (m *CatOptRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_CatOptRequest.DiscardUnknown(m)
}

var xxx_messageInfo_CatOptRequest proto.InternalMessageInfo

func (m *CatOptRequest) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *CatOptRequest) GetFpxAppId() string {
	if m != nil {
		return m.FpxAppId
	}
	return ""
}

func (m *CatOptRequest) GetLang() string {
	if m != nil {
		return m.Lang
	}
	return ""
}

func init() {
	proto.RegisterType((*CatSubRequest)(nil), "pb.CatSubRequest")
	proto.RegisterType((*EgressCatInfoResp)(nil), "pb.EgressCatInfoResp")
	proto.RegisterMapType((map[string]string)(nil), "pb.EgressCatInfoResp.ReplyContentEntry")
	proto.RegisterType((*QuestionInfo)(nil), "pb.QuestionInfo")
	proto.RegisterType((*CatOptRequest)(nil), "pb.CatOptRequest")
}

func init() {
	proto.RegisterFile("egress.proto", fileDescriptor_e640ebe2e4e4631c)
}

var fileDescriptor_e640ebe2e4e4631c = []byte{
	// 891 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xac, 0x95, 0xcd, 0x6e, 0xdb, 0x46,
	0x10, 0xc7, 0x21, 0x7f, 0xc8, 0xd6, 0x48, 0x72, 0xed, 0x75, 0xdc, 0xd0, 0x8a, 0xd3, 0xa8, 0x1b,
	0x04, 0x35, 0x52, 0x40, 0x02, 0xd4, 0x16, 0x28, 0xdc, 0x43, 0xe1, 0x2a, 0x49, 0x21, 0x24, 0xb5,
	0x1b, 0x46, 0x75, 0x81, 0x5c, 0x88, 0x15, 0x39, 0x92, 0x59, 0x92, 0xbb, 0x1b, 0x72, 0x15, 0x44,
	0xd7, 0xbe, 0x42, 0x5f, 0xac, 0x40, 0xcf, 0xed, 0xa9, 0x0f, 0x52, 0xcc, 0x92, 0x94, 0x68, 0xa5,
	0x6e, 0x2f, 0xb9, 0xed, 0xfc, 0x77, 0xe6, 0xb7, 0x33, 0xb3, 0xcb, 0x21, 0xb4, 0x70, 0x96, 0x62,
	0x96, 0xf5, 0x74, 0xaa, 0x8c, 0x62, 0x1b, 0x7a, 0xd2, 0x39, 0x99, 0x29, 0x35, 0x8b, 0xb1, 0x2f,
	0x74, 0xd8, 0x17, 0x52, 0x2a, 0x23, 0x4c, 0xa8, 0x64, 0xe1, 0xd1, 0x39, 0xcc, 0xfd, 0x3d, 0x13,
	0xfa, 0x11, 0x9a, 0x42, 0x6c, 0xf9, 0x2a, 0x49, 0x94, 0x2c, 0x2c, 0x40, 0x39, 0x4f, 0xf2, 0x35,
	0xff, 0xb3, 0x06, 0xed, 0xa1, 0x30, 0xaf, 0xe6, 0x13, 0x17, 0xdf, 0xcc, 0x31, 0x33, 0xec, 0x2e,
	0xec, 0xcc, 0x44, 0x82, 0x5e, 0x18, 0x38, 0xb5, 0x6e, 0xed, 0xb4, 0xed, 0xd6, 0xc9, 0x1c, 0x05,
	0xec, 0x04, 0x60, 0xaa, 0xdf, 0x79, 0x42, 0x6b, 0xda, 0xdb, 0xe8, 0xd6, 0x4e, 0x1b, 0xee, 0xee,
	0x54, 0xbf, 0x3b, 0xd7, 0x7a, 0x14, 0x30, 0x06, 0x5b, 0xb1, 0x90, 0x33, 0x67, 0xd3, 0xea, 0x76,
	0xcd, 0x8e, 0xa0, 0xee, 0x0b, 0x43, 0xde, 0x5b, 0x96, 0xb4, 0xed, 0x0b, 0x33, 0x0a, 0xd8, 0xa7,
	0xd0, 0xf2, 0xd5, 0x5c, 0x9a, 0x74, 0xe1, 0xf9, 0x2a, 0x40, 0x67, 0xdb, 0x86, 0x34, 0x0b, 0x6d,
	0xa8, 0x02, 0x64, 0x0e, 0xec, 0xf8, 0xd7, 0x42, 0x4a, 0x8c, 0x9d, 0xba, 0xdd, 0x2d, 0x4d, 0xb6,
	0x0f, 0x9b, 0x59, 0x18, 0x38, 0x3b, 0x56, 0xa5, 0x25, 0xbb, 0x07, 0x8d, 0x5f, 0x32, 0x25, 0xbd,
	0x40, 0x18, 0xe1, 0xec, 0xe6, 0x69, 0x91, 0xf0, 0x44, 0x18, 0xc1, 0xff, 0xda, 0x84, 0x83, 0xa7,
	0xb6, 0x23, 0x43, 0x61, 0x46, 0x72, 0xaa, 0x5c, 0xcc, 0x74, 0x25, 0xb1, 0x5a, 0x35, 0xb1, 0x63,
	0xd8, 0x25, 0x59, 0x8a, 0x04, 0x8b, 0xfa, 0x76, 0x7c, 0x61, 0x2e, 0x44, 0x82, 0xec, 0x01, 0x34,
	0x53, 0x8c, 0x85, 0x41, 0xcf, 0x2c, 0x34, 0xda, 0x2a, 0xdb, 0x2e, 0xe4, 0xd2, 0x78, 0xa1, 0x91,
	0xdd, 0x07, 0xd0, 0xa9, 0xf2, 0xa9, 0xf5, 0xcb, 0x7a, 0x1b, 0x85, 0x32, 0x0a, 0xe8, 0x44, 0xa3,
	0x63, 0xda, 0xda, 0xce, 0x4f, 0x34, 0x3a, 0x1e, 0x05, 0x54, 0x8d, 0xd1, 0x65, 0x8d, 0xb4, 0x64,
	0x1f, 0x43, 0x7d, 0x1a, 0x62, 0x1c, 0x64, 0x45, 0x89, 0x85, 0xc5, 0x3a, 0x36, 0x37, 0x9c, 0xa9,
	0x74, 0x51, 0x16, 0x59, 0xda, 0xec, 0x05, 0xb4, 0x53, 0xd4, 0x31, 0xb5, 0x53, 0x1a, 0x94, 0xc6,
	0x69, 0x74, 0x37, 0x4f, 0x9b, 0x83, 0xcf, 0x7a, 0x7a, 0xd2, 0x7b, 0xaf, 0xf8, 0x9e, 0x4b, 0xae,
	0xc3, 0xdc, 0xf3, 0x29, 0x75, 0xdc, 0x6d, 0xa5, 0x15, 0x89, 0x7d, 0x05, 0x6d, 0xfb, 0x12, 0x42,
	0x25, 0xbd, 0x38, 0xcc, 0x8c, 0x03, 0x96, 0xb6, 0x4f, 0xb4, 0x97, 0xc5, 0x86, 0x85, 0xb5, 0x4a,
	0xb7, 0x17, 0x61, 0x66, 0xd8, 0x97, 0xd0, 0xf2, 0xaf, 0x85, 0xf1, 0x54, 0xea, 0x4d, 0x55, 0x9a,
	0x38, 0xcd, 0x6e, 0xed, 0x74, 0x6f, 0xc0, 0x28, 0x6a, 0x78, 0x2d, 0xcc, 0x65, 0xfa, 0x4c, 0xa5,
	0x09, 0xb5, 0xca, 0x05, 0x7f, 0x69, 0x77, 0xbe, 0x85, 0x83, 0xf7, 0xf2, 0xa1, 0xae, 0x44, 0xb8,
	0xb0, 0x77, 0xd3, 0x70, 0x69, 0xc9, 0xee, 0xc0, 0xf6, 0x5b, 0x11, 0xcf, 0xcb, 0x6b, 0xc9, 0x8d,
	0xb3, 0x8d, 0xaf, 0x6b, 0xfc, 0x0a, 0x5a, 0xd5, 0xa4, 0xe8, 0x71, 0x2d, 0xb3, 0x5f, 0x41, 0x9a,
	0xa5, 0xf6, 0x1c, 0x17, 0xec, 0x61, 0xa5, 0xc0, 0xca, 0x5d, 0x2f, 0xe3, 0xe8, 0xc2, 0xf9, 0x6b,
	0xfb, 0x5d, 0x5c, 0x6a, 0xf3, 0xe1, 0xbf, 0x8b, 0xc1, 0xef, 0x75, 0x68, 0xe4, 0xf7, 0x72, 0xfe,
	0xe3, 0x88, 0x5d, 0x42, 0x7b, 0x1c, 0xbd, 0x0a, 0xa2, 0x0b, 0x65, 0x42, 0x1f, 0xaf, 0x06, 0xec,
	0x90, 0x7a, 0x96, 0x5b, 0xc5, 0xe1, 0x57, 0x83, 0xce, 0x5e, 0x55, 0xcc, 0x34, 0x3f, 0xfe, 0xf5,
	0x8f, 0xbf, 0x7f, 0xdb, 0x38, 0xe4, 0x7b, 0x76, 0x0e, 0xbc, 0x1d, 0xf4, 0xa5, 0xdd, 0x3b, 0xab,
	0x3d, 0x66, 0x97, 0xb0, 0x3b, 0x8e, 0x86, 0x29, 0x0a, 0x83, 0xec, 0x23, 0x0a, 0x2b, 0x2d, 0x17,
	0xdf, 0x74, 0xf6, 0x6f, 0x0a, 0x99, 0xe6, 0x5d, 0x4b, 0xea, 0xf0, 0xa3, 0x7e, 0x3e, 0x3a, 0xfa,
	0xf9, 0xe8, 0xe8, 0xfb, 0xd6, 0x85, 0x80, 0xdf, 0x43, 0x7d, 0x1c, 0xfd, 0x10, 0x4a, 0x64, 0xed,
	0x3c, 0x9a, 0xd6, 0x04, 0xdb, 0xab, 0x9a, 0x99, 0xe6, 0x9f, 0x58, 0x94, 0xc3, 0x0f, 0xd7, 0x50,
	0x49, 0x28, 0x57, 0x99, 0x3d, 0x41, 0x23, 0xc2, 0xb8, 0xcc, 0x2c, 0xb7, 0x2a, 0x99, 0x95, 0xc2,
	0x7f, 0x64, 0x16, 0x58, 0x17, 0x02, 0xbe, 0x84, 0xe6, 0x38, 0xa2, 0x07, 0x84, 0x32, 0xcc, 0xae,
	0x19, 0xcb, 0x11, 0x4b, 0x81, 0xb0, 0x0d, 0xfb, 0x15, 0x24, 0xda, 0x2c, 0xf8, 0x43, 0xcb, 0xbb,
	0xcf, 0x9d, 0x35, 0x5e, 0x5a, 0xfa, 0x13, 0xf2, 0x39, 0xe5, 0xe8, 0xa2, 0xd2, 0x28, 0xff, 0x8f,
	0x77, 0x5b, 0x7e, 0xa9, 0x8d, 0x26, 0xd8, 0x05, 0xc0, 0x38, 0x3a, 0xd7, 0x3a, 0x15, 0x61, 0x86,
	0xec, 0x20, 0xc7, 0x95, 0xf6, 0x1a, 0x8d, 0x5b, 0xda, 0x09, 0xbf, 0xbb, 0x46, 0x13, 0x85, 0x3b,
	0xf1, 0x66, 0xc0, 0x56, 0xf1, 0xcf, 0x10, 0x83, 0x89, 0xf0, 0x23, 0x76, 0x7c, 0x93, 0x5b, 0xea,
	0x6b, 0xfc, 0xcf, 0x2d, 0xff, 0x11, 0xef, 0xde, 0xc2, 0xef, 0x4f, 0x8b, 0x38, 0x3a, 0xe8, 0x67,
	0x7a, 0x94, 0x43, 0x95, 0x24, 0x73, 0x19, 0xd2, 0x9c, 0x61, 0x77, 0x8a, 0x77, 0xb3, 0x92, 0xd6,
	0xf0, 0x8f, 0x2c, 0xfe, 0x01, 0xef, 0xac, 0x3f, 0xa3, 0x55, 0x04, 0x81, 0x7f, 0x82, 0xd6, 0xb0,
	0x98, 0x5b, 0xf6, 0x7b, 0xb5, 0x3d, 0xb9, 0xf1, 0x07, 0xea, 0x1c, 0xfd, 0xeb, 0xdc, 0xe2, 0xf7,
	0xec, 0x01, 0x47, 0x7c, 0xbf, 0x3c, 0xc0, 0x17, 0xa6, 0x1f, 0xca, 0xa9, 0x3a, 0xab, 0x3d, 0xfe,
	0xae, 0xfe, 0x7a, 0xab, 0xf7, 0x8d, 0x9e, 0x4c, 0xea, 0xf6, 0xb7, 0xf6, 0xc5, 0x3f, 0x01, 0x00,
	0x00, 0xff, 0xff, 0xaa, 0x19, 0x45, 0x49, 0x37, 0x07, 0x00, 0x00,
}
