// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.14.0
// source: examine.proto

package pb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	ExamineApi_ExamineTplList_FullMethodName             = "/pb.ExamineApi/ExamineTplList"
	ExamineApi_ExamineTplDetail_FullMethodName           = "/pb.ExamineApi/ExamineTplDetail"
	ExamineApi_ExamineTplOpts_FullMethodName             = "/pb.ExamineApi/ExamineTplOpts"
	ExamineApi_ExamineTplSave_FullMethodName             = "/pb.ExamineApi/ExamineTplSave"
	ExamineApi_ExamineTplCopy_FullMethodName             = "/pb.ExamineApi/ExamineTplCopy"
	ExamineApi_ExamineTplEnable_FullMethodName           = "/pb.ExamineApi/ExamineTplEnable"
	ExamineApi_ExamineTplDel_FullMethodName              = "/pb.ExamineApi/ExamineTplDel"
	ExamineApi_ExamineTaskList_FullMethodName            = "/pb.ExamineApi/ExamineTaskList"
	ExamineApi_ExamineTaskDetail_FullMethodName          = "/pb.ExamineApi/ExamineTaskDetail"
	ExamineApi_ExamineTaskSave_FullMethodName            = "/pb.ExamineApi/ExamineTaskSave"
	ExamineApi_ExamineTaskDscFilterCount_FullMethodName  = "/pb.ExamineApi/ExamineTaskDscFilterCount"
	ExamineApi_ExamineDscOrderSave_FullMethodName        = "/pb.ExamineApi/ExamineDscOrderSave"
	ExamineApi_ExamineDscOrderDetail_FullMethodName      = "/pb.ExamineApi/ExamineDscOrderDetail"
	ExamineApi_ExamineDscOrderList_FullMethodName        = "/pb.ExamineApi/ExamineDscOrderList"
	ExamineApi_ExamineDscOrderListExport_FullMethodName  = "/pb.ExamineApi/ExamineDscOrderListExport"
	ExamineApi_ExamineDscOrderStats_FullMethodName       = "/pb.ExamineApi/ExamineDscOrderStats"
	ExamineApi_ExamineOrderNoticeAcceptor_FullMethodName = "/pb.ExamineApi/ExamineOrderNoticeAcceptor"
	ExamineApi_ExamineOrderNoticeList_FullMethodName     = "/pb.ExamineApi/ExamineOrderNoticeList"
)

// ExamineApiClient is the client API for ExamineApi service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ExamineApiClient interface {
	// examine 质检打分表配置 - 列表
	ExamineTplList(ctx context.Context, in *ExamineTplListReq, opts ...grpc.CallOption) (*ExamineTplListResp, error)
	// examine 质检打分表配置 - 详情
	ExamineTplDetail(ctx context.Context, in *ExamineTplDetailReq, opts ...grpc.CallOption) (*ExamineTplDetailResp, error)
	// examine 质检打分表配置 - 下来选项
	ExamineTplOpts(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*ExamineTplOptsResp, error)
	// examine 质检打分表配置 - 保存 add&save
	ExamineTplSave(ctx context.Context, in *ExamineTplSaveReq, opts ...grpc.CallOption) (*Empty, error)
	// examine 质检打分表配置 - 保存 add&save
	ExamineTplCopy(ctx context.Context, in *ExamineTplCopyReq, opts ...grpc.CallOption) (*Empty, error)
	// examine 质检打分表配置 - enable&disable
	ExamineTplEnable(ctx context.Context, in *EnableReq, opts ...grpc.CallOption) (*Empty, error)
	// examine 质检打分表配置 - 删除
	ExamineTplDel(ctx context.Context, in *ExamineTplDelReq, opts ...grpc.CallOption) (*Empty, error)
	// examine 任务配置 - 列表
	ExamineTaskList(ctx context.Context, in *ExamineTaskListReq, opts ...grpc.CallOption) (*ExamineTaskListResp, error)
	// examine 任务配置 - 详情
	ExamineTaskDetail(ctx context.Context, in *ExamineTaskDetailReq, opts ...grpc.CallOption) (*ExamineTaskDetailResp, error)
	// examine 任务配置 - 保存 add&save
	ExamineTaskSave(ctx context.Context, in *ExamineTaskSaveReq, opts ...grpc.CallOption) (*Empty, error)
	// examine 任务配置 - 过滤 全检标准 - 统计
	ExamineTaskDscFilterCount(ctx context.Context, in *ExamineTaskDscFilterCountReq, opts ...grpc.CallOption) (*ExamineTaskDscFilterCountResp, error)
	// examine 质检单数据 - 保存
	ExamineDscOrderSave(ctx context.Context, in *ExamineDscOrderSaveReq, opts ...grpc.CallOption) (*Empty, error)
	// examine 质检单数据 - 详情
	ExamineDscOrderDetail(ctx context.Context, in *ExamineDscOrderDetailReq, opts ...grpc.CallOption) (*ExamineDscOrderDetailResp, error)
	// examine 质检单数据 - 列表
	ExamineDscOrderList(ctx context.Context, in *ExamineDscOrderListReq, opts ...grpc.CallOption) (*ExamineDscOrderListResp, error)
	// examine 质检单数据 - 列表Export
	ExamineDscOrderListExport(ctx context.Context, in *ExamineDscOrderListReq, opts ...grpc.CallOption) (*Empty, error)
	// examine 质检单数据 - 统计数据
	ExamineDscOrderStats(ctx context.Context, in *ExamineDscOrderStatsReq, opts ...grpc.CallOption) (*ExamineDscOrderStatsResp, error)
	// examine 质检单红点通知 - 是否有红点
	ExamineOrderNoticeAcceptor(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*ExamineOrderNoticeAcceptorResp, error)
	// examine 质检单红点通知 - 列表
	ExamineOrderNoticeList(ctx context.Context, in *ExamineOrderNoticeListReq, opts ...grpc.CallOption) (*ExamineOrderNoticeListResp, error)
}

type examineApiClient struct {
	cc grpc.ClientConnInterface
}

func NewExamineApiClient(cc grpc.ClientConnInterface) ExamineApiClient {
	return &examineApiClient{cc}
}

func (c *examineApiClient) ExamineTplList(ctx context.Context, in *ExamineTplListReq, opts ...grpc.CallOption) (*ExamineTplListResp, error) {
	out := new(ExamineTplListResp)
	err := c.cc.Invoke(ctx, ExamineApi_ExamineTplList_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *examineApiClient) ExamineTplDetail(ctx context.Context, in *ExamineTplDetailReq, opts ...grpc.CallOption) (*ExamineTplDetailResp, error) {
	out := new(ExamineTplDetailResp)
	err := c.cc.Invoke(ctx, ExamineApi_ExamineTplDetail_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *examineApiClient) ExamineTplOpts(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*ExamineTplOptsResp, error) {
	out := new(ExamineTplOptsResp)
	err := c.cc.Invoke(ctx, ExamineApi_ExamineTplOpts_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *examineApiClient) ExamineTplSave(ctx context.Context, in *ExamineTplSaveReq, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, ExamineApi_ExamineTplSave_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *examineApiClient) ExamineTplCopy(ctx context.Context, in *ExamineTplCopyReq, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, ExamineApi_ExamineTplCopy_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *examineApiClient) ExamineTplEnable(ctx context.Context, in *EnableReq, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, ExamineApi_ExamineTplEnable_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *examineApiClient) ExamineTplDel(ctx context.Context, in *ExamineTplDelReq, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, ExamineApi_ExamineTplDel_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *examineApiClient) ExamineTaskList(ctx context.Context, in *ExamineTaskListReq, opts ...grpc.CallOption) (*ExamineTaskListResp, error) {
	out := new(ExamineTaskListResp)
	err := c.cc.Invoke(ctx, ExamineApi_ExamineTaskList_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *examineApiClient) ExamineTaskDetail(ctx context.Context, in *ExamineTaskDetailReq, opts ...grpc.CallOption) (*ExamineTaskDetailResp, error) {
	out := new(ExamineTaskDetailResp)
	err := c.cc.Invoke(ctx, ExamineApi_ExamineTaskDetail_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *examineApiClient) ExamineTaskSave(ctx context.Context, in *ExamineTaskSaveReq, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, ExamineApi_ExamineTaskSave_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *examineApiClient) ExamineTaskDscFilterCount(ctx context.Context, in *ExamineTaskDscFilterCountReq, opts ...grpc.CallOption) (*ExamineTaskDscFilterCountResp, error) {
	out := new(ExamineTaskDscFilterCountResp)
	err := c.cc.Invoke(ctx, ExamineApi_ExamineTaskDscFilterCount_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *examineApiClient) ExamineDscOrderSave(ctx context.Context, in *ExamineDscOrderSaveReq, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, ExamineApi_ExamineDscOrderSave_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *examineApiClient) ExamineDscOrderDetail(ctx context.Context, in *ExamineDscOrderDetailReq, opts ...grpc.CallOption) (*ExamineDscOrderDetailResp, error) {
	out := new(ExamineDscOrderDetailResp)
	err := c.cc.Invoke(ctx, ExamineApi_ExamineDscOrderDetail_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *examineApiClient) ExamineDscOrderList(ctx context.Context, in *ExamineDscOrderListReq, opts ...grpc.CallOption) (*ExamineDscOrderListResp, error) {
	out := new(ExamineDscOrderListResp)
	err := c.cc.Invoke(ctx, ExamineApi_ExamineDscOrderList_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *examineApiClient) ExamineDscOrderListExport(ctx context.Context, in *ExamineDscOrderListReq, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, ExamineApi_ExamineDscOrderListExport_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *examineApiClient) ExamineDscOrderStats(ctx context.Context, in *ExamineDscOrderStatsReq, opts ...grpc.CallOption) (*ExamineDscOrderStatsResp, error) {
	out := new(ExamineDscOrderStatsResp)
	err := c.cc.Invoke(ctx, ExamineApi_ExamineDscOrderStats_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *examineApiClient) ExamineOrderNoticeAcceptor(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*ExamineOrderNoticeAcceptorResp, error) {
	out := new(ExamineOrderNoticeAcceptorResp)
	err := c.cc.Invoke(ctx, ExamineApi_ExamineOrderNoticeAcceptor_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *examineApiClient) ExamineOrderNoticeList(ctx context.Context, in *ExamineOrderNoticeListReq, opts ...grpc.CallOption) (*ExamineOrderNoticeListResp, error) {
	out := new(ExamineOrderNoticeListResp)
	err := c.cc.Invoke(ctx, ExamineApi_ExamineOrderNoticeList_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ExamineApiServer is the server API for ExamineApi service.
// All implementations must embed UnimplementedExamineApiServer
// for forward compatibility
type ExamineApiServer interface {
	// examine 质检打分表配置 - 列表
	ExamineTplList(context.Context, *ExamineTplListReq) (*ExamineTplListResp, error)
	// examine 质检打分表配置 - 详情
	ExamineTplDetail(context.Context, *ExamineTplDetailReq) (*ExamineTplDetailResp, error)
	// examine 质检打分表配置 - 下来选项
	ExamineTplOpts(context.Context, *Empty) (*ExamineTplOptsResp, error)
	// examine 质检打分表配置 - 保存 add&save
	ExamineTplSave(context.Context, *ExamineTplSaveReq) (*Empty, error)
	// examine 质检打分表配置 - 保存 add&save
	ExamineTplCopy(context.Context, *ExamineTplCopyReq) (*Empty, error)
	// examine 质检打分表配置 - enable&disable
	ExamineTplEnable(context.Context, *EnableReq) (*Empty, error)
	// examine 质检打分表配置 - 删除
	ExamineTplDel(context.Context, *ExamineTplDelReq) (*Empty, error)
	// examine 任务配置 - 列表
	ExamineTaskList(context.Context, *ExamineTaskListReq) (*ExamineTaskListResp, error)
	// examine 任务配置 - 详情
	ExamineTaskDetail(context.Context, *ExamineTaskDetailReq) (*ExamineTaskDetailResp, error)
	// examine 任务配置 - 保存 add&save
	ExamineTaskSave(context.Context, *ExamineTaskSaveReq) (*Empty, error)
	// examine 任务配置 - 过滤 全检标准 - 统计
	ExamineTaskDscFilterCount(context.Context, *ExamineTaskDscFilterCountReq) (*ExamineTaskDscFilterCountResp, error)
	// examine 质检单数据 - 保存
	ExamineDscOrderSave(context.Context, *ExamineDscOrderSaveReq) (*Empty, error)
	// examine 质检单数据 - 详情
	ExamineDscOrderDetail(context.Context, *ExamineDscOrderDetailReq) (*ExamineDscOrderDetailResp, error)
	// examine 质检单数据 - 列表
	ExamineDscOrderList(context.Context, *ExamineDscOrderListReq) (*ExamineDscOrderListResp, error)
	// examine 质检单数据 - 列表Export
	ExamineDscOrderListExport(context.Context, *ExamineDscOrderListReq) (*Empty, error)
	// examine 质检单数据 - 统计数据
	ExamineDscOrderStats(context.Context, *ExamineDscOrderStatsReq) (*ExamineDscOrderStatsResp, error)
	// examine 质检单红点通知 - 是否有红点
	ExamineOrderNoticeAcceptor(context.Context, *Empty) (*ExamineOrderNoticeAcceptorResp, error)
	// examine 质检单红点通知 - 列表
	ExamineOrderNoticeList(context.Context, *ExamineOrderNoticeListReq) (*ExamineOrderNoticeListResp, error)
	mustEmbedUnimplementedExamineApiServer()
}

// UnimplementedExamineApiServer must be embedded to have forward compatible implementations.
type UnimplementedExamineApiServer struct {
}

func (UnimplementedExamineApiServer) ExamineTplList(context.Context, *ExamineTplListReq) (*ExamineTplListResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ExamineTplList not implemented")
}
func (UnimplementedExamineApiServer) ExamineTplDetail(context.Context, *ExamineTplDetailReq) (*ExamineTplDetailResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ExamineTplDetail not implemented")
}
func (UnimplementedExamineApiServer) ExamineTplOpts(context.Context, *Empty) (*ExamineTplOptsResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ExamineTplOpts not implemented")
}
func (UnimplementedExamineApiServer) ExamineTplSave(context.Context, *ExamineTplSaveReq) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ExamineTplSave not implemented")
}
func (UnimplementedExamineApiServer) ExamineTplCopy(context.Context, *ExamineTplCopyReq) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ExamineTplCopy not implemented")
}
func (UnimplementedExamineApiServer) ExamineTplEnable(context.Context, *EnableReq) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ExamineTplEnable not implemented")
}
func (UnimplementedExamineApiServer) ExamineTplDel(context.Context, *ExamineTplDelReq) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ExamineTplDel not implemented")
}
func (UnimplementedExamineApiServer) ExamineTaskList(context.Context, *ExamineTaskListReq) (*ExamineTaskListResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ExamineTaskList not implemented")
}
func (UnimplementedExamineApiServer) ExamineTaskDetail(context.Context, *ExamineTaskDetailReq) (*ExamineTaskDetailResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ExamineTaskDetail not implemented")
}
func (UnimplementedExamineApiServer) ExamineTaskSave(context.Context, *ExamineTaskSaveReq) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ExamineTaskSave not implemented")
}
func (UnimplementedExamineApiServer) ExamineTaskDscFilterCount(context.Context, *ExamineTaskDscFilterCountReq) (*ExamineTaskDscFilterCountResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ExamineTaskDscFilterCount not implemented")
}
func (UnimplementedExamineApiServer) ExamineDscOrderSave(context.Context, *ExamineDscOrderSaveReq) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ExamineDscOrderSave not implemented")
}
func (UnimplementedExamineApiServer) ExamineDscOrderDetail(context.Context, *ExamineDscOrderDetailReq) (*ExamineDscOrderDetailResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ExamineDscOrderDetail not implemented")
}
func (UnimplementedExamineApiServer) ExamineDscOrderList(context.Context, *ExamineDscOrderListReq) (*ExamineDscOrderListResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ExamineDscOrderList not implemented")
}
func (UnimplementedExamineApiServer) ExamineDscOrderListExport(context.Context, *ExamineDscOrderListReq) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ExamineDscOrderListExport not implemented")
}
func (UnimplementedExamineApiServer) ExamineDscOrderStats(context.Context, *ExamineDscOrderStatsReq) (*ExamineDscOrderStatsResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ExamineDscOrderStats not implemented")
}
func (UnimplementedExamineApiServer) ExamineOrderNoticeAcceptor(context.Context, *Empty) (*ExamineOrderNoticeAcceptorResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ExamineOrderNoticeAcceptor not implemented")
}
func (UnimplementedExamineApiServer) ExamineOrderNoticeList(context.Context, *ExamineOrderNoticeListReq) (*ExamineOrderNoticeListResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ExamineOrderNoticeList not implemented")
}
func (UnimplementedExamineApiServer) mustEmbedUnimplementedExamineApiServer() {}

// UnsafeExamineApiServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ExamineApiServer will
// result in compilation errors.
type UnsafeExamineApiServer interface {
	mustEmbedUnimplementedExamineApiServer()
}

func RegisterExamineApiServer(s grpc.ServiceRegistrar, srv ExamineApiServer) {
	s.RegisterService(&ExamineApi_ServiceDesc, srv)
}

func _ExamineApi_ExamineTplList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ExamineTplListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ExamineApiServer).ExamineTplList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ExamineApi_ExamineTplList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ExamineApiServer).ExamineTplList(ctx, req.(*ExamineTplListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ExamineApi_ExamineTplDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ExamineTplDetailReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ExamineApiServer).ExamineTplDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ExamineApi_ExamineTplDetail_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ExamineApiServer).ExamineTplDetail(ctx, req.(*ExamineTplDetailReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ExamineApi_ExamineTplOpts_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ExamineApiServer).ExamineTplOpts(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ExamineApi_ExamineTplOpts_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ExamineApiServer).ExamineTplOpts(ctx, req.(*Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _ExamineApi_ExamineTplSave_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ExamineTplSaveReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ExamineApiServer).ExamineTplSave(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ExamineApi_ExamineTplSave_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ExamineApiServer).ExamineTplSave(ctx, req.(*ExamineTplSaveReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ExamineApi_ExamineTplCopy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ExamineTplCopyReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ExamineApiServer).ExamineTplCopy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ExamineApi_ExamineTplCopy_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ExamineApiServer).ExamineTplCopy(ctx, req.(*ExamineTplCopyReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ExamineApi_ExamineTplEnable_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(EnableReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ExamineApiServer).ExamineTplEnable(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ExamineApi_ExamineTplEnable_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ExamineApiServer).ExamineTplEnable(ctx, req.(*EnableReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ExamineApi_ExamineTplDel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ExamineTplDelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ExamineApiServer).ExamineTplDel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ExamineApi_ExamineTplDel_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ExamineApiServer).ExamineTplDel(ctx, req.(*ExamineTplDelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ExamineApi_ExamineTaskList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ExamineTaskListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ExamineApiServer).ExamineTaskList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ExamineApi_ExamineTaskList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ExamineApiServer).ExamineTaskList(ctx, req.(*ExamineTaskListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ExamineApi_ExamineTaskDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ExamineTaskDetailReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ExamineApiServer).ExamineTaskDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ExamineApi_ExamineTaskDetail_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ExamineApiServer).ExamineTaskDetail(ctx, req.(*ExamineTaskDetailReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ExamineApi_ExamineTaskSave_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ExamineTaskSaveReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ExamineApiServer).ExamineTaskSave(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ExamineApi_ExamineTaskSave_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ExamineApiServer).ExamineTaskSave(ctx, req.(*ExamineTaskSaveReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ExamineApi_ExamineTaskDscFilterCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ExamineTaskDscFilterCountReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ExamineApiServer).ExamineTaskDscFilterCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ExamineApi_ExamineTaskDscFilterCount_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ExamineApiServer).ExamineTaskDscFilterCount(ctx, req.(*ExamineTaskDscFilterCountReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ExamineApi_ExamineDscOrderSave_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ExamineDscOrderSaveReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ExamineApiServer).ExamineDscOrderSave(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ExamineApi_ExamineDscOrderSave_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ExamineApiServer).ExamineDscOrderSave(ctx, req.(*ExamineDscOrderSaveReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ExamineApi_ExamineDscOrderDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ExamineDscOrderDetailReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ExamineApiServer).ExamineDscOrderDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ExamineApi_ExamineDscOrderDetail_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ExamineApiServer).ExamineDscOrderDetail(ctx, req.(*ExamineDscOrderDetailReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ExamineApi_ExamineDscOrderList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ExamineDscOrderListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ExamineApiServer).ExamineDscOrderList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ExamineApi_ExamineDscOrderList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ExamineApiServer).ExamineDscOrderList(ctx, req.(*ExamineDscOrderListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ExamineApi_ExamineDscOrderListExport_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ExamineDscOrderListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ExamineApiServer).ExamineDscOrderListExport(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ExamineApi_ExamineDscOrderListExport_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ExamineApiServer).ExamineDscOrderListExport(ctx, req.(*ExamineDscOrderListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ExamineApi_ExamineDscOrderStats_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ExamineDscOrderStatsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ExamineApiServer).ExamineDscOrderStats(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ExamineApi_ExamineDscOrderStats_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ExamineApiServer).ExamineDscOrderStats(ctx, req.(*ExamineDscOrderStatsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ExamineApi_ExamineOrderNoticeAcceptor_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ExamineApiServer).ExamineOrderNoticeAcceptor(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ExamineApi_ExamineOrderNoticeAcceptor_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ExamineApiServer).ExamineOrderNoticeAcceptor(ctx, req.(*Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _ExamineApi_ExamineOrderNoticeList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ExamineOrderNoticeListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ExamineApiServer).ExamineOrderNoticeList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ExamineApi_ExamineOrderNoticeList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ExamineApiServer).ExamineOrderNoticeList(ctx, req.(*ExamineOrderNoticeListReq))
	}
	return interceptor(ctx, in, info, handler)
}

// ExamineApi_ServiceDesc is the grpc.ServiceDesc for ExamineApi service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ExamineApi_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "pb.ExamineApi",
	HandlerType: (*ExamineApiServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ExamineTplList",
			Handler:    _ExamineApi_ExamineTplList_Handler,
		},
		{
			MethodName: "ExamineTplDetail",
			Handler:    _ExamineApi_ExamineTplDetail_Handler,
		},
		{
			MethodName: "ExamineTplOpts",
			Handler:    _ExamineApi_ExamineTplOpts_Handler,
		},
		{
			MethodName: "ExamineTplSave",
			Handler:    _ExamineApi_ExamineTplSave_Handler,
		},
		{
			MethodName: "ExamineTplCopy",
			Handler:    _ExamineApi_ExamineTplCopy_Handler,
		},
		{
			MethodName: "ExamineTplEnable",
			Handler:    _ExamineApi_ExamineTplEnable_Handler,
		},
		{
			MethodName: "ExamineTplDel",
			Handler:    _ExamineApi_ExamineTplDel_Handler,
		},
		{
			MethodName: "ExamineTaskList",
			Handler:    _ExamineApi_ExamineTaskList_Handler,
		},
		{
			MethodName: "ExamineTaskDetail",
			Handler:    _ExamineApi_ExamineTaskDetail_Handler,
		},
		{
			MethodName: "ExamineTaskSave",
			Handler:    _ExamineApi_ExamineTaskSave_Handler,
		},
		{
			MethodName: "ExamineTaskDscFilterCount",
			Handler:    _ExamineApi_ExamineTaskDscFilterCount_Handler,
		},
		{
			MethodName: "ExamineDscOrderSave",
			Handler:    _ExamineApi_ExamineDscOrderSave_Handler,
		},
		{
			MethodName: "ExamineDscOrderDetail",
			Handler:    _ExamineApi_ExamineDscOrderDetail_Handler,
		},
		{
			MethodName: "ExamineDscOrderList",
			Handler:    _ExamineApi_ExamineDscOrderList_Handler,
		},
		{
			MethodName: "ExamineDscOrderListExport",
			Handler:    _ExamineApi_ExamineDscOrderListExport_Handler,
		},
		{
			MethodName: "ExamineDscOrderStats",
			Handler:    _ExamineApi_ExamineDscOrderStats_Handler,
		},
		{
			MethodName: "ExamineOrderNoticeAcceptor",
			Handler:    _ExamineApi_ExamineOrderNoticeAcceptor_Handler,
		},
		{
			MethodName: "ExamineOrderNoticeList",
			Handler:    _ExamineApi_ExamineOrderNoticeList_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "examine.proto",
}
