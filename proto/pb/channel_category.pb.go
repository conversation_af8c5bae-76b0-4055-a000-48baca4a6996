// Code generated by protoc-gen-go. DO NOT EDIT.
// source: channel_category.proto

package pb

import (
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

type CatProjectReq struct {
	// 项目 @gotags: validate:"required"
	Project string `protobuf:"bytes,1,opt,name=project,proto3" json:"project" validate:"required"`
	// 类别:line or discord @gotags: validate:"required,oneof=1 2"
	CatType              uint32   `protobuf:"varint,2,opt,name=cat_type,json=catType,proto3" json:"cat_type" validate:"required,oneof=1 2"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *CatProjectReq) Reset()         { *m = CatProjectReq{} }
func (m *CatProjectReq) String() string { return proto.CompactTextString(m) }
func (*CatProjectReq) ProtoMessage()    {}
func (*CatProjectReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_a7ec8897bb9006f1, []int{0}
}

func (m *CatProjectReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CatProjectReq.Unmarshal(m, b)
}
func (m *CatProjectReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CatProjectReq.Marshal(b, m, deterministic)
}
func (m *CatProjectReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CatProjectReq.Merge(m, src)
}
func (m *CatProjectReq) XXX_Size() int {
	return xxx_messageInfo_CatProjectReq.Size(m)
}
func (m *CatProjectReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CatProjectReq.DiscardUnknown(m)
}

var xxx_messageInfo_CatProjectReq proto.InternalMessageInfo

func (m *CatProjectReq) GetProject() string {
	if m != nil {
		return m.Project
	}
	return ""
}

func (m *CatProjectReq) GetCatType() uint32 {
	if m != nil {
		return m.CatType
	}
	return 0
}

// CatOptsResp 问题分类配置(下拉级联多选)列表
type ChannelCatTreeResp struct {
	Data                 []*ChannelCatTreeResp_Cat `protobuf:"bytes,1,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-" gorm:"-"`
	XXX_unrecognized     []byte                    `json:"-" gorm:"-"`
	XXX_sizecache        int32                     `json:"-" gorm:"-"`
}

func (m *ChannelCatTreeResp) Reset()         { *m = ChannelCatTreeResp{} }
func (m *ChannelCatTreeResp) String() string { return proto.CompactTextString(m) }
func (*ChannelCatTreeResp) ProtoMessage()    {}
func (*ChannelCatTreeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_a7ec8897bb9006f1, []int{1}
}

func (m *ChannelCatTreeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelCatTreeResp.Unmarshal(m, b)
}
func (m *ChannelCatTreeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelCatTreeResp.Marshal(b, m, deterministic)
}
func (m *ChannelCatTreeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelCatTreeResp.Merge(m, src)
}
func (m *ChannelCatTreeResp) XXX_Size() int {
	return xxx_messageInfo_ChannelCatTreeResp.Size(m)
}
func (m *ChannelCatTreeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelCatTreeResp.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelCatTreeResp proto.InternalMessageInfo

func (m *ChannelCatTreeResp) GetData() []*ChannelCatTreeResp_Cat {
	if m != nil {
		return m.Data
	}
	return nil
}

type ChannelCatTreeResp_Cat struct {
	Id    uint32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	Label string `protobuf:"bytes,2,opt,name=label,proto3" json:"label"`
	Level uint32 `protobuf:"varint,3,opt,name=level,proto3" json:"level"`
	// 子结构 @gotags: json:"children,omitempty"
	Children             []*ChannelCatTreeResp_Cat `protobuf:"bytes,7,rep,name=children,proto3" json:"children,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                  `json:"-" gorm:"-"`
	XXX_unrecognized     []byte                    `json:"-" gorm:"-"`
	XXX_sizecache        int32                     `json:"-" gorm:"-"`
}

func (m *ChannelCatTreeResp_Cat) Reset()         { *m = ChannelCatTreeResp_Cat{} }
func (m *ChannelCatTreeResp_Cat) String() string { return proto.CompactTextString(m) }
func (*ChannelCatTreeResp_Cat) ProtoMessage()    {}
func (*ChannelCatTreeResp_Cat) Descriptor() ([]byte, []int) {
	return fileDescriptor_a7ec8897bb9006f1, []int{1, 0}
}

func (m *ChannelCatTreeResp_Cat) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelCatTreeResp_Cat.Unmarshal(m, b)
}
func (m *ChannelCatTreeResp_Cat) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelCatTreeResp_Cat.Marshal(b, m, deterministic)
}
func (m *ChannelCatTreeResp_Cat) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelCatTreeResp_Cat.Merge(m, src)
}
func (m *ChannelCatTreeResp_Cat) XXX_Size() int {
	return xxx_messageInfo_ChannelCatTreeResp_Cat.Size(m)
}
func (m *ChannelCatTreeResp_Cat) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelCatTreeResp_Cat.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelCatTreeResp_Cat proto.InternalMessageInfo

func (m *ChannelCatTreeResp_Cat) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *ChannelCatTreeResp_Cat) GetLabel() string {
	if m != nil {
		return m.Label
	}
	return ""
}

func (m *ChannelCatTreeResp_Cat) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *ChannelCatTreeResp_Cat) GetChildren() []*ChannelCatTreeResp_Cat {
	if m != nil {
		return m.Children
	}
	return nil
}

// CatItems cat item
type ChannelCatItems struct {
	CatId                uint32   `protobuf:"varint,1,opt,name=cat_id,json=catId,proto3" json:"cat_id"`
	OneLevel             uint32   `protobuf:"varint,2,opt,name=one_level,json=oneLevel,proto3" json:"one_level"`
	SecondLevel          uint32   `protobuf:"varint,3,opt,name=second_level,json=secondLevel,proto3" json:"second_level"`
	Level                uint32   `protobuf:"varint,4,opt,name=level,proto3" json:"level"`
	Category             string   `protobuf:"bytes,6,opt,name=category,proto3" json:"category"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *ChannelCatItems) Reset()         { *m = ChannelCatItems{} }
func (m *ChannelCatItems) String() string { return proto.CompactTextString(m) }
func (*ChannelCatItems) ProtoMessage()    {}
func (*ChannelCatItems) Descriptor() ([]byte, []int) {
	return fileDescriptor_a7ec8897bb9006f1, []int{2}
}

func (m *ChannelCatItems) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelCatItems.Unmarshal(m, b)
}
func (m *ChannelCatItems) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelCatItems.Marshal(b, m, deterministic)
}
func (m *ChannelCatItems) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelCatItems.Merge(m, src)
}
func (m *ChannelCatItems) XXX_Size() int {
	return xxx_messageInfo_ChannelCatItems.Size(m)
}
func (m *ChannelCatItems) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelCatItems.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelCatItems proto.InternalMessageInfo

func (m *ChannelCatItems) GetCatId() uint32 {
	if m != nil {
		return m.CatId
	}
	return 0
}

func (m *ChannelCatItems) GetOneLevel() uint32 {
	if m != nil {
		return m.OneLevel
	}
	return 0
}

func (m *ChannelCatItems) GetSecondLevel() uint32 {
	if m != nil {
		return m.SecondLevel
	}
	return 0
}

func (m *ChannelCatItems) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *ChannelCatItems) GetCategory() string {
	if m != nil {
		return m.Category
	}
	return ""
}

// CatProbAddReq 添加分类 请求参数
type ChannelCatAddReq struct {
	// 项目 @gotags: validate:"required"
	Project string `protobuf:"bytes,1,opt,name=project,proto3" json:"project" validate:"required"`
	// 分类的等级 @gotags: validate:"required,oneof=1 2 3"
	CatLevel uint32 `protobuf:"varint,2,opt,name=cat_level,json=catLevel,proto3" json:"cat_level" validate:"required,oneof=1 2 3"`
	// 分类名称 @gotags: validate:"required"
	Category string `protobuf:"bytes,3,opt,name=category,proto3" json:"category" validate:"required"`
	// 一级分类 @gotags: validate:"required_if=CatLevel 2"
	OneLevel uint32 `protobuf:"varint,4,opt,name=one_level,json=oneLevel,proto3" json:"one_level" validate:"required_if=CatLevel 2"`
	// 二级分类 @gotags: validate:"required_if=CatLevel 3"
	SecondLevel uint32 `protobuf:"varint,5,opt,name=second_level,json=secondLevel,proto3" json:"second_level" validate:"required_if=CatLevel 3"`
	// 类别:line or discord @gotags: validate:"required,oneof=1 2"
	CatType              uint32   `protobuf:"varint,6,opt,name=cat_type,json=catType,proto3" json:"cat_type" validate:"required,oneof=1 2"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *ChannelCatAddReq) Reset()         { *m = ChannelCatAddReq{} }
func (m *ChannelCatAddReq) String() string { return proto.CompactTextString(m) }
func (*ChannelCatAddReq) ProtoMessage()    {}
func (*ChannelCatAddReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_a7ec8897bb9006f1, []int{3}
}

func (m *ChannelCatAddReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelCatAddReq.Unmarshal(m, b)
}
func (m *ChannelCatAddReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelCatAddReq.Marshal(b, m, deterministic)
}
func (m *ChannelCatAddReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelCatAddReq.Merge(m, src)
}
func (m *ChannelCatAddReq) XXX_Size() int {
	return xxx_messageInfo_ChannelCatAddReq.Size(m)
}
func (m *ChannelCatAddReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelCatAddReq.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelCatAddReq proto.InternalMessageInfo

func (m *ChannelCatAddReq) GetProject() string {
	if m != nil {
		return m.Project
	}
	return ""
}

func (m *ChannelCatAddReq) GetCatLevel() uint32 {
	if m != nil {
		return m.CatLevel
	}
	return 0
}

func (m *ChannelCatAddReq) GetCategory() string {
	if m != nil {
		return m.Category
	}
	return ""
}

func (m *ChannelCatAddReq) GetOneLevel() uint32 {
	if m != nil {
		return m.OneLevel
	}
	return 0
}

func (m *ChannelCatAddReq) GetSecondLevel() uint32 {
	if m != nil {
		return m.SecondLevel
	}
	return 0
}

func (m *ChannelCatAddReq) GetCatType() uint32 {
	if m != nil {
		return m.CatType
	}
	return 0
}

// ChannelCatSaveReq 修改分类 请求参数
type ChannelCatSaveReq struct {
	// 分类ID @gotags: validate:"required"
	CatId uint32 `protobuf:"varint,1,opt,name=cat_id,json=catId,proto3" json:"cat_id" validate:"required"`
	// 分类名称 @gotags: validate:"required"
	Category string `protobuf:"bytes,2,opt,name=category,proto3" json:"category" validate:"required"`
	// 分类的等级 @gotags: validate:"required,oneof=1 2 3"
	CatLevel             uint32   `protobuf:"varint,7,opt,name=cat_level,json=catLevel,proto3" json:"cat_level" validate:"required,oneof=1 2 3"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *ChannelCatSaveReq) Reset()         { *m = ChannelCatSaveReq{} }
func (m *ChannelCatSaveReq) String() string { return proto.CompactTextString(m) }
func (*ChannelCatSaveReq) ProtoMessage()    {}
func (*ChannelCatSaveReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_a7ec8897bb9006f1, []int{4}
}

func (m *ChannelCatSaveReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelCatSaveReq.Unmarshal(m, b)
}
func (m *ChannelCatSaveReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelCatSaveReq.Marshal(b, m, deterministic)
}
func (m *ChannelCatSaveReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelCatSaveReq.Merge(m, src)
}
func (m *ChannelCatSaveReq) XXX_Size() int {
	return xxx_messageInfo_ChannelCatSaveReq.Size(m)
}
func (m *ChannelCatSaveReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelCatSaveReq.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelCatSaveReq proto.InternalMessageInfo

func (m *ChannelCatSaveReq) GetCatId() uint32 {
	if m != nil {
		return m.CatId
	}
	return 0
}

func (m *ChannelCatSaveReq) GetCategory() string {
	if m != nil {
		return m.Category
	}
	return ""
}

func (m *ChannelCatSaveReq) GetCatLevel() uint32 {
	if m != nil {
		return m.CatLevel
	}
	return 0
}

// CatIdReq 类别Id 请求参数
type ChannelCatIdReq struct {
	// 分类ID @gotags: validate:"required"
	CatId                uint32   `protobuf:"varint,1,opt,name=cat_id,json=catId,proto3" json:"cat_id" validate:"required"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *ChannelCatIdReq) Reset()         { *m = ChannelCatIdReq{} }
func (m *ChannelCatIdReq) String() string { return proto.CompactTextString(m) }
func (*ChannelCatIdReq) ProtoMessage()    {}
func (*ChannelCatIdReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_a7ec8897bb9006f1, []int{5}
}

func (m *ChannelCatIdReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChannelCatIdReq.Unmarshal(m, b)
}
func (m *ChannelCatIdReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChannelCatIdReq.Marshal(b, m, deterministic)
}
func (m *ChannelCatIdReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelCatIdReq.Merge(m, src)
}
func (m *ChannelCatIdReq) XXX_Size() int {
	return xxx_messageInfo_ChannelCatIdReq.Size(m)
}
func (m *ChannelCatIdReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelCatIdReq.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelCatIdReq proto.InternalMessageInfo

func (m *ChannelCatIdReq) GetCatId() uint32 {
	if m != nil {
		return m.CatId
	}
	return 0
}

func init() {
	proto.RegisterType((*CatProjectReq)(nil), "pb.CatProjectReq")
	proto.RegisterType((*ChannelCatTreeResp)(nil), "pb.ChannelCatTreeResp")
	proto.RegisterType((*ChannelCatTreeResp_Cat)(nil), "pb.ChannelCatTreeResp.Cat")
	proto.RegisterType((*ChannelCatItems)(nil), "pb.ChannelCatItems")
	proto.RegisterType((*ChannelCatAddReq)(nil), "pb.ChannelCatAddReq")
	proto.RegisterType((*ChannelCatSaveReq)(nil), "pb.ChannelCatSaveReq")
	proto.RegisterType((*ChannelCatIdReq)(nil), "pb.ChannelCatIdReq")
}

func init() {
	proto.RegisterFile("channel_category.proto", fileDescriptor_a7ec8897bb9006f1)
}

var fileDescriptor_a7ec8897bb9006f1 = []byte{
	// 373 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x84, 0x93, 0xbd, 0x4e, 0xe3, 0x40,
	0x14, 0x85, 0xe5, 0x9f, 0xd8, 0xf1, 0xcd, 0x66, 0x7f, 0x46, 0xbb, 0x2b, 0x93, 0x34, 0xc1, 0x95,
	0x2b, 0x17, 0x20, 0xd1, 0x50, 0x81, 0x69, 0x22, 0xa5, 0x40, 0x26, 0x15, 0x8d, 0x35, 0x9e, 0xb9,
	0x22, 0x41, 0xc6, 0x1e, 0x9c, 0x51, 0x24, 0xbf, 0x0a, 0x2f, 0x03, 0x8f, 0x86, 0x32, 0x23, 0xfc,
	0x23, 0x2b, 0xa4, 0x3c, 0xa3, 0x23, 0x9d, 0xf3, 0xdd, 0x7b, 0x07, 0xfe, 0xb3, 0x0d, 0x2d, 0x0a,
	0xcc, 0x53, 0x46, 0x25, 0x3e, 0x95, 0x55, 0x1d, 0x89, 0xaa, 0x94, 0x25, 0x31, 0x45, 0x16, 0xdc,
	0xc1, 0x34, 0xa6, 0xf2, 0xbe, 0x2a, 0x9f, 0x91, 0xc9, 0x04, 0x5f, 0x89, 0x0f, 0xae, 0xd0, 0xca,
	0x37, 0x16, 0x46, 0xe8, 0x25, 0x5f, 0x92, 0x9c, 0xc1, 0x98, 0x51, 0x99, 0xca, 0x5a, 0xa0, 0x6f,
	0x2e, 0x8c, 0x70, 0x9a, 0xb8, 0x8c, 0xca, 0x75, 0x2d, 0x30, 0x78, 0x37, 0x80, 0xc4, 0x3a, 0x24,
	0xa6, 0x72, 0x5d, 0x21, 0x26, 0xb8, 0x13, 0x24, 0x02, 0x9b, 0x53, 0x49, 0x7d, 0x63, 0x61, 0x85,
	0x93, 0x8b, 0x59, 0x24, 0xb2, 0x68, 0xe8, 0x8a, 0x62, 0x2a, 0x13, 0xe5, 0x9b, 0xd5, 0x60, 0xc5,
	0x54, 0x92, 0x9f, 0x60, 0x6e, 0xb9, 0x4a, 0x9f, 0x26, 0xe6, 0x96, 0x93, 0xbf, 0x30, 0xca, 0x69,
	0x86, 0xb9, 0x4a, 0xf5, 0x12, 0x2d, 0xd4, 0x2b, 0xee, 0x31, 0xf7, 0x2d, 0x65, 0xd4, 0x82, 0x5c,
	0xc1, 0x98, 0x6d, 0xb6, 0x39, 0xaf, 0xb0, 0xf0, 0xdd, 0x93, 0xb1, 0x8d, 0x37, 0x78, 0x33, 0xe0,
	0x57, 0x6b, 0x5a, 0x4a, 0x7c, 0xd9, 0x91, 0x7f, 0xe0, 0x1c, 0x80, 0x9b, 0x2e, 0x23, 0x46, 0xe5,
	0x92, 0x93, 0x39, 0x78, 0x65, 0x81, 0xa9, 0x0e, 0xd7, 0x83, 0x18, 0x97, 0x05, 0xae, 0x54, 0xfe,
	0x39, 0xfc, 0xd8, 0x21, 0x2b, 0x0b, 0x9e, 0x76, 0xcb, 0x4d, 0xf4, 0x9b, 0xb6, 0x34, 0xc5, 0xed,
	0x6e, 0xf1, 0x99, 0x9a, 0xae, 0x5a, 0x8f, 0xef, 0x28, 0xce, 0x46, 0x07, 0x1f, 0x06, 0xfc, 0x6e,
	0xcb, 0xdd, 0x70, 0xfe, 0xfd, 0xa2, 0xe6, 0xe0, 0x1d, 0x7a, 0xf7, 0x0a, 0x32, 0x2a, 0x57, 0x83,
	0x1c, 0xab, 0x9f, 0xd3, 0x27, 0xb3, 0x4f, 0x90, 0x8d, 0x86, 0x64, 0xdd, 0x0b, 0x71, 0xfa, 0x17,
	0xc2, 0xe0, 0x4f, 0x4b, 0xf0, 0x40, 0xf7, 0x78, 0x40, 0x38, 0x32, 0xe0, 0x6e, 0x45, 0x73, 0x58,
	0xb1, 0x65, 0x73, 0xfb, 0x6c, 0x41, 0xd8, 0xdb, 0x21, 0x3f, 0x1e, 0x71, 0xeb, 0x3c, 0xda, 0xd1,
	0xb5, 0xc8, 0x32, 0x47, 0xfd, 0x84, 0xcb, 0xcf, 0x00, 0x00, 0x00, 0xff, 0xff, 0xc2, 0x27, 0xa3,
	0x02, 0x23, 0x03, 0x00, 0x00,
}
