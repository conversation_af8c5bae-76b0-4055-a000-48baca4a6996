// Code generated by protoc-gen-go. DO NOT EDIT.
// source: indices.proto

package pb

import (
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

type IndicesListResp struct {
	// 列表
	Data                 []*IndicesListRespItem `protobuf:"bytes,1,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-" gorm:"-"`
	XXX_unrecognized     []byte                 `json:"-" gorm:"-"`
	XXX_sizecache        int32                  `json:"-" gorm:"-"`
}

func (m *IndicesListResp) Reset()         { *m = IndicesListResp{} }
func (m *IndicesListResp) String() string { return proto.CompactTextString(m) }
func (*IndicesListResp) ProtoMessage()    {}
func (*IndicesListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_9f6dfb7c0d08268b, []int{0}
}

func (m *IndicesListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IndicesListResp.Unmarshal(m, b)
}
func (m *IndicesListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IndicesListResp.Marshal(b, m, deterministic)
}
func (m *IndicesListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IndicesListResp.Merge(m, src)
}
func (m *IndicesListResp) XXX_Size() int {
	return xxx_messageInfo_IndicesListResp.Size(m)
}
func (m *IndicesListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_IndicesListResp.DiscardUnknown(m)
}

var xxx_messageInfo_IndicesListResp proto.InternalMessageInfo

func (m *IndicesListResp) GetData() []*IndicesListRespItem {
	if m != nil {
		return m.Data
	}
	return nil
}

type IndicesListRespItem struct {
	Id          uint64   `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	CatType     string   `protobuf:"bytes,2,opt,name=cat_type,json=catType,proto3" json:"cat_type"`
	Name        string   `protobuf:"bytes,3,opt,name=name,proto3" json:"name"`
	CatIds      []uint32 `protobuf:"varint,4,rep,packed,name=cat_ids,json=catIds,proto3" json:"cat_ids"`
	UpdatedAt   string   `protobuf:"bytes,5,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at"`
	UpdatedUser string   `protobuf:"bytes,6,opt,name=updated_user,json=updatedUser,proto3" json:"updated_user"`
	Project     string   `protobuf:"bytes,7,opt,name=project,proto3" json:"project"`
	// 启用true 禁用false
	Enable               bool     `protobuf:"varint,8,opt,name=enable,proto3" json:"enable"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *IndicesListRespItem) Reset()         { *m = IndicesListRespItem{} }
func (m *IndicesListRespItem) String() string { return proto.CompactTextString(m) }
func (*IndicesListRespItem) ProtoMessage()    {}
func (*IndicesListRespItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_9f6dfb7c0d08268b, []int{0, 0}
}

func (m *IndicesListRespItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IndicesListRespItem.Unmarshal(m, b)
}
func (m *IndicesListRespItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IndicesListRespItem.Marshal(b, m, deterministic)
}
func (m *IndicesListRespItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IndicesListRespItem.Merge(m, src)
}
func (m *IndicesListRespItem) XXX_Size() int {
	return xxx_messageInfo_IndicesListRespItem.Size(m)
}
func (m *IndicesListRespItem) XXX_DiscardUnknown() {
	xxx_messageInfo_IndicesListRespItem.DiscardUnknown(m)
}

var xxx_messageInfo_IndicesListRespItem proto.InternalMessageInfo

func (m *IndicesListRespItem) GetId() uint64 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *IndicesListRespItem) GetCatType() string {
	if m != nil {
		return m.CatType
	}
	return ""
}

func (m *IndicesListRespItem) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *IndicesListRespItem) GetCatIds() []uint32 {
	if m != nil {
		return m.CatIds
	}
	return nil
}

func (m *IndicesListRespItem) GetUpdatedAt() string {
	if m != nil {
		return m.UpdatedAt
	}
	return ""
}

func (m *IndicesListRespItem) GetUpdatedUser() string {
	if m != nil {
		return m.UpdatedUser
	}
	return ""
}

func (m *IndicesListRespItem) GetProject() string {
	if m != nil {
		return m.Project
	}
	return ""
}

func (m *IndicesListRespItem) GetEnable() bool {
	if m != nil {
		return m.Enable
	}
	return false
}

type IndicesSaveReq struct {
	// 保存接口必传，新增接口不传
	Id uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	// 配置内容类型 @gotags: validate:"required"
	CatType string `protobuf:"bytes,2,opt,name=cat_type,json=catType,proto3" json:"cat_type" validate:"required"`
	// 关联问题分类 @gotags: validate:"required"
	CatIds []uint32 `protobuf:"varint,3,rep,packed,name=cat_ids,json=catIds,proto3" json:"cat_ids" validate:"required"`
	// project 新增接口必传
	Project              string   `protobuf:"bytes,4,opt,name=project,proto3" json:"project"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *IndicesSaveReq) Reset()         { *m = IndicesSaveReq{} }
func (m *IndicesSaveReq) String() string { return proto.CompactTextString(m) }
func (*IndicesSaveReq) ProtoMessage()    {}
func (*IndicesSaveReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_9f6dfb7c0d08268b, []int{1}
}

func (m *IndicesSaveReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IndicesSaveReq.Unmarshal(m, b)
}
func (m *IndicesSaveReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IndicesSaveReq.Marshal(b, m, deterministic)
}
func (m *IndicesSaveReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IndicesSaveReq.Merge(m, src)
}
func (m *IndicesSaveReq) XXX_Size() int {
	return xxx_messageInfo_IndicesSaveReq.Size(m)
}
func (m *IndicesSaveReq) XXX_DiscardUnknown() {
	xxx_messageInfo_IndicesSaveReq.DiscardUnknown(m)
}

var xxx_messageInfo_IndicesSaveReq proto.InternalMessageInfo

func (m *IndicesSaveReq) GetId() uint64 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *IndicesSaveReq) GetCatType() string {
	if m != nil {
		return m.CatType
	}
	return ""
}

func (m *IndicesSaveReq) GetCatIds() []uint32 {
	if m != nil {
		return m.CatIds
	}
	return nil
}

func (m *IndicesSaveReq) GetProject() string {
	if m != nil {
		return m.Project
	}
	return ""
}

type IndicesEnableReq struct {
	// id @gotags: validate:"required"
	Id uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id" validate:"required"`
	// 启用true 禁用false
	Enable               bool     `protobuf:"varint,2,opt,name=enable,proto3" json:"enable"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *IndicesEnableReq) Reset()         { *m = IndicesEnableReq{} }
func (m *IndicesEnableReq) String() string { return proto.CompactTextString(m) }
func (*IndicesEnableReq) ProtoMessage()    {}
func (*IndicesEnableReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_9f6dfb7c0d08268b, []int{2}
}

func (m *IndicesEnableReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IndicesEnableReq.Unmarshal(m, b)
}
func (m *IndicesEnableReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IndicesEnableReq.Marshal(b, m, deterministic)
}
func (m *IndicesEnableReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IndicesEnableReq.Merge(m, src)
}
func (m *IndicesEnableReq) XXX_Size() int {
	return xxx_messageInfo_IndicesEnableReq.Size(m)
}
func (m *IndicesEnableReq) XXX_DiscardUnknown() {
	xxx_messageInfo_IndicesEnableReq.DiscardUnknown(m)
}

var xxx_messageInfo_IndicesEnableReq proto.InternalMessageInfo

func (m *IndicesEnableReq) GetId() uint64 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *IndicesEnableReq) GetEnable() bool {
	if m != nil {
		return m.Enable
	}
	return false
}

type IndicesDelReq struct {
	// id @gotags: validate:"required"
	Id                   uint64   `protobuf:"varint,1,opt,name=id,proto3" json:"id" validate:"required"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *IndicesDelReq) Reset()         { *m = IndicesDelReq{} }
func (m *IndicesDelReq) String() string { return proto.CompactTextString(m) }
func (*IndicesDelReq) ProtoMessage()    {}
func (*IndicesDelReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_9f6dfb7c0d08268b, []int{3}
}

func (m *IndicesDelReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IndicesDelReq.Unmarshal(m, b)
}
func (m *IndicesDelReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IndicesDelReq.Marshal(b, m, deterministic)
}
func (m *IndicesDelReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IndicesDelReq.Merge(m, src)
}
func (m *IndicesDelReq) XXX_Size() int {
	return xxx_messageInfo_IndicesDelReq.Size(m)
}
func (m *IndicesDelReq) XXX_DiscardUnknown() {
	xxx_messageInfo_IndicesDelReq.DiscardUnknown(m)
}

var xxx_messageInfo_IndicesDelReq proto.InternalMessageInfo

func (m *IndicesDelReq) GetId() uint64 {
	if m != nil {
		return m.Id
	}
	return 0
}

type IndicesCfgResp struct {
	// 内容配置json
	CatTypes             map[string]string `protobuf:"bytes,1,rep,name=cat_types,json=catTypes,proto3" json:"cat_types" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-" gorm:"-"`
	XXX_unrecognized     []byte            `json:"-" gorm:"-"`
	XXX_sizecache        int32             `json:"-" gorm:"-"`
}

func (m *IndicesCfgResp) Reset()         { *m = IndicesCfgResp{} }
func (m *IndicesCfgResp) String() string { return proto.CompactTextString(m) }
func (*IndicesCfgResp) ProtoMessage()    {}
func (*IndicesCfgResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_9f6dfb7c0d08268b, []int{4}
}

func (m *IndicesCfgResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_IndicesCfgResp.Unmarshal(m, b)
}
func (m *IndicesCfgResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_IndicesCfgResp.Marshal(b, m, deterministic)
}
func (m *IndicesCfgResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_IndicesCfgResp.Merge(m, src)
}
func (m *IndicesCfgResp) XXX_Size() int {
	return xxx_messageInfo_IndicesCfgResp.Size(m)
}
func (m *IndicesCfgResp) XXX_DiscardUnknown() {
	xxx_messageInfo_IndicesCfgResp.DiscardUnknown(m)
}

var xxx_messageInfo_IndicesCfgResp proto.InternalMessageInfo

func (m *IndicesCfgResp) GetCatTypes() map[string]string {
	if m != nil {
		return m.CatTypes
	}
	return nil
}

func init() {
	proto.RegisterType((*IndicesListResp)(nil), "pb.IndicesListResp")
	proto.RegisterType((*IndicesListRespItem)(nil), "pb.IndicesListResp.item")
	proto.RegisterType((*IndicesSaveReq)(nil), "pb.IndicesSaveReq")
	proto.RegisterType((*IndicesEnableReq)(nil), "pb.IndicesEnableReq")
	proto.RegisterType((*IndicesDelReq)(nil), "pb.IndicesDelReq")
	proto.RegisterType((*IndicesCfgResp)(nil), "pb.IndicesCfgResp")
	proto.RegisterMapType((map[string]string)(nil), "pb.IndicesCfgResp.CatTypesEntry")
}

func init() {
	proto.RegisterFile("indices.proto", fileDescriptor_9f6dfb7c0d08268b)
}

var fileDescriptor_9f6dfb7c0d08268b = []byte{
	// 580 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x94, 0x94, 0xcf, 0x6e, 0xd4, 0x3e,
	0x10, 0xc7, 0x95, 0x3f, 0xcd, 0xee, 0x4e, 0xbb, 0xfd, 0xe3, 0xf6, 0xd7, 0x9f, 0x1b, 0x0a, 0x0d,
	0x39, 0xad, 0x2a, 0x94, 0x4a, 0xe5, 0x82, 0xb6, 0xe2, 0x50, 0xb6, 0x45, 0x6a, 0xd5, 0x03, 0x0a,
	0x70, 0xe1, 0x52, 0x79, 0x93, 0x69, 0x64, 0xc8, 0x26, 0x26, 0x76, 0x2b, 0xed, 0x95, 0x33, 0x37,
	0x78, 0x21, 0x5e, 0x01, 0x5e, 0x81, 0x07, 0x41, 0x71, 0xdc, 0x92, 0xd0, 0xe5, 0xc0, 0x2d, 0x1e,
	0x8f, 0x3f, 0xf3, 0x9d, 0xf9, 0x8e, 0x02, 0x43, 0x5e, 0xa4, 0x3c, 0x41, 0x19, 0x89, 0xaa, 0x54,
	0x25, 0xb1, 0xc5, 0xd4, 0x5f, 0x49, 0xca, 0xd9, 0xac, 0x2c, 0x9a, 0x88, 0xbf, 0x9b, 0x95, 0x65,
	0x96, 0xe3, 0x01, 0x13, 0xfc, 0x80, 0x15, 0x45, 0xa9, 0x98, 0xe2, 0x65, 0x61, 0xf2, 0xc3, 0xaf,
	0x36, 0xac, 0x9d, 0x35, 0x84, 0x0b, 0x2e, 0x55, 0x8c, 0x52, 0x90, 0x27, 0xe0, 0xa6, 0x4c, 0x31,
	0x6a, 0x05, 0xce, 0x68, 0xf9, 0x90, 0x46, 0x62, 0x1a, 0xfd, 0x91, 0x12, 0x71, 0x85, 0xb3, 0x58,
	0x67, 0xf9, 0xdf, 0x2d, 0x70, 0xeb, 0x23, 0x59, 0x05, 0x9b, 0xa7, 0xd4, 0x0a, 0xac, 0x91, 0x1b,
	0xdb, 0x3c, 0x25, 0x3b, 0xd0, 0x4f, 0x98, 0xba, 0x54, 0x73, 0x81, 0xd4, 0x0e, 0xac, 0xd1, 0x20,
	0xee, 0x25, 0x4c, 0xbd, 0x99, 0x0b, 0x24, 0x04, 0xdc, 0x82, 0xcd, 0x90, 0x3a, 0x3a, 0xac, 0xbf,
	0xc9, 0xff, 0x50, 0x5f, 0x5f, 0xf2, 0x54, 0x52, 0x37, 0x70, 0x46, 0xc3, 0xd8, 0x4b, 0x98, 0x3a,
	0x4b, 0x25, 0x79, 0x08, 0x70, 0x2d, 0x52, 0xa6, 0x30, 0xbd, 0x64, 0x8a, 0x2e, 0xe9, 0x27, 0x03,
	0x13, 0x39, 0x56, 0xe4, 0x31, 0xac, 0xdc, 0x5e, 0x5f, 0x4b, 0xac, 0xa8, 0xa7, 0x13, 0x96, 0x4d,
	0xec, 0xad, 0xc4, 0x8a, 0x50, 0xe8, 0x89, 0xaa, 0x7c, 0x8f, 0x89, 0xa2, 0xbd, 0x46, 0x88, 0x39,
	0x92, 0x6d, 0xf0, 0xb0, 0x60, 0xd3, 0x1c, 0x69, 0x3f, 0xb0, 0x46, 0xfd, 0xd8, 0x9c, 0xc2, 0x02,
	0x56, 0x4d, 0xcb, 0xaf, 0xd9, 0x0d, 0xc6, 0xf8, 0xf1, 0x5f, 0xba, 0x6b, 0x75, 0xe2, 0x74, 0x3a,
	0x69, 0xe9, 0x70, 0x3b, 0x3a, 0xc2, 0x31, 0xac, 0x9b, 0x7a, 0xa7, 0x5a, 0xc0, 0xa2, 0x8a, 0xbf,
	0xb5, 0xda, 0x1d, 0xad, 0x7b, 0x30, 0x34, 0x6f, 0x4f, 0x30, 0x5f, 0xf0, 0x30, 0xfc, 0x6c, 0xdd,
	0x75, 0x33, 0xb9, 0xca, 0xb4, 0xc5, 0xcf, 0x61, 0x70, 0xab, 0x5e, 0x1a, 0x9f, 0x83, 0x96, 0xcf,
	0x26, 0x2d, 0x9a, 0x34, 0x1d, 0xc9, 0xd3, 0x42, 0x55, 0xf3, 0xb8, 0x6f, 0x1a, 0x94, 0xfe, 0x11,
	0x0c, 0x3b, 0x57, 0x64, 0x1d, 0x9c, 0x0f, 0x38, 0xd7, 0x35, 0x07, 0x71, 0xfd, 0x49, 0xb6, 0x60,
	0xe9, 0x86, 0xe5, 0xd7, 0xb7, 0xc3, 0x69, 0x0e, 0x63, 0xfb, 0x99, 0x75, 0xf8, 0xcd, 0x01, 0x30,
	0x75, 0x8e, 0x05, 0x27, 0xe7, 0xe0, 0x4d, 0xca, 0xe2, 0x8a, 0x67, 0x64, 0xad, 0x56, 0xf0, 0xaa,
	0x99, 0xc8, 0x05, 0x2b, 0x32, 0x9f, 0xdc, 0x97, 0x14, 0x3e, 0xf8, 0xf4, 0xe3, 0xe7, 0x17, 0xfb,
	0xbf, 0x70, 0x5d, 0x2f, 0xb4, 0x59, 0xfe, 0x83, 0xe4, 0x2a, 0x1b, 0x5b, 0xfb, 0xe4, 0x1c, 0xdc,
	0x7a, 0x45, 0xef, 0x93, 0x36, 0x17, 0x2c, 0x71, 0xb8, 0xab, 0x51, 0xdb, 0xe1, 0x46, 0x07, 0x95,
	0x73, 0xa9, 0x6a, 0xd6, 0x04, 0xdc, 0xda, 0x7b, 0xd2, 0x16, 0x61, 0x96, 0xc1, 0x1f, 0xd4, 0xb1,
	0xd3, 0x99, 0x50, 0xf3, 0xbf, 0x40, 0x24, 0xbb, 0xc1, 0x1a, 0x72, 0x06, 0x5e, 0x63, 0x28, 0xd9,
	0x6a, 0x61, 0xee, 0x3c, 0x6e, 0x83, 0x1e, 0x69, 0x10, 0x0d, 0x37, 0x3b, 0xa0, 0xc6, 0xe3, 0x46,
	0x8f, 0x77, 0x82, 0x39, 0x2a, 0x24, 0x1b, 0x2d, 0x54, 0x63, 0x79, 0x9b, 0xb3, 0x78, 0x40, 0x29,
	0xe6, 0x35, 0xe4, 0x25, 0xf4, 0x62, 0xcc, 0x91, 0x49, 0xbc, 0x3f, 0xa3, 0x16, 0x63, 0x4f, 0x33,
	0x76, 0xc2, 0xad, 0x0e, 0xa3, 0x6a, 0x5e, 0x8e, 0xad, 0xfd, 0x17, 0xde, 0x3b, 0x37, 0x3a, 0x12,
	0xd3, 0xa9, 0xa7, 0xff, 0x22, 0x4f, 0x7f, 0x05, 0x00, 0x00, 0xff, 0xff, 0xd0, 0x43, 0x62, 0x03,
	0x86, 0x04, 0x00, 0x00,
}
