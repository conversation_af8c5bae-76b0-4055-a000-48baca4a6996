// Code generated by protoc-gen-go. DO NOT EDIT.
// source: module_category.proto

package pb

import (
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

// ModuleCatAddReq 添加分类 请求参数
type ModuleCatAddReq struct {
	// 项目 @gotags: validate:"required"
	Project string `protobuf:"bytes,1,opt,name=project,proto3" json:"project" validate:"required"`
	// 分类的等级 @gotags: validate:"required,oneof=1 2 3"
	CatLevel uint32 `protobuf:"varint,2,opt,name=cat_level,json=catLevel,proto3" json:"cat_level" validate:"required,oneof=1 2 3"`
	// 分类名称 @gotags: validate:"required"
	Category string `protobuf:"bytes,3,opt,name=category,proto3" json:"category" validate:"required"`
	// 父分类ID
	ParentId             uint32   `protobuf:"varint,4,opt,name=parent_id,json=parentId,proto3" json:"parent_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *ModuleCatAddReq) Reset()         { *m = ModuleCatAddReq{} }
func (m *ModuleCatAddReq) String() string { return proto.CompactTextString(m) }
func (*ModuleCatAddReq) ProtoMessage()    {}
func (*ModuleCatAddReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_546f6c13beca1721, []int{0}
}

func (m *ModuleCatAddReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ModuleCatAddReq.Unmarshal(m, b)
}
func (m *ModuleCatAddReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ModuleCatAddReq.Marshal(b, m, deterministic)
}
func (m *ModuleCatAddReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ModuleCatAddReq.Merge(m, src)
}
func (m *ModuleCatAddReq) XXX_Size() int {
	return xxx_messageInfo_ModuleCatAddReq.Size(m)
}
func (m *ModuleCatAddReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ModuleCatAddReq.DiscardUnknown(m)
}

var xxx_messageInfo_ModuleCatAddReq proto.InternalMessageInfo

func (m *ModuleCatAddReq) GetProject() string {
	if m != nil {
		return m.Project
	}
	return ""
}

func (m *ModuleCatAddReq) GetCatLevel() uint32 {
	if m != nil {
		return m.CatLevel
	}
	return 0
}

func (m *ModuleCatAddReq) GetCategory() string {
	if m != nil {
		return m.Category
	}
	return ""
}

func (m *ModuleCatAddReq) GetParentId() uint32 {
	if m != nil {
		return m.ParentId
	}
	return 0
}

// ModuleCatSaveReq 修改分类 请求参数
type ModuleCatSaveReq struct {
	// 分类ID @gotags: validate:"required"
	CatId uint32 `protobuf:"varint,1,opt,name=cat_id,json=catId,proto3" json:"cat_id" validate:"required"`
	// 分类名称 @gotags: validate:"required"
	Category string `protobuf:"bytes,2,opt,name=category,proto3" json:"category" validate:"required"`
	// 分类的等级 @gotags: validate:"required,oneof=1 2 3"
	CatLevel             uint32   `protobuf:"varint,7,opt,name=cat_level,json=catLevel,proto3" json:"cat_level" validate:"required,oneof=1 2 3"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *ModuleCatSaveReq) Reset()         { *m = ModuleCatSaveReq{} }
func (m *ModuleCatSaveReq) String() string { return proto.CompactTextString(m) }
func (*ModuleCatSaveReq) ProtoMessage()    {}
func (*ModuleCatSaveReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_546f6c13beca1721, []int{1}
}

func (m *ModuleCatSaveReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ModuleCatSaveReq.Unmarshal(m, b)
}
func (m *ModuleCatSaveReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ModuleCatSaveReq.Marshal(b, m, deterministic)
}
func (m *ModuleCatSaveReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ModuleCatSaveReq.Merge(m, src)
}
func (m *ModuleCatSaveReq) XXX_Size() int {
	return xxx_messageInfo_ModuleCatSaveReq.Size(m)
}
func (m *ModuleCatSaveReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ModuleCatSaveReq.DiscardUnknown(m)
}

var xxx_messageInfo_ModuleCatSaveReq proto.InternalMessageInfo

func (m *ModuleCatSaveReq) GetCatId() uint32 {
	if m != nil {
		return m.CatId
	}
	return 0
}

func (m *ModuleCatSaveReq) GetCategory() string {
	if m != nil {
		return m.Category
	}
	return ""
}

func (m *ModuleCatSaveReq) GetCatLevel() uint32 {
	if m != nil {
		return m.CatLevel
	}
	return 0
}

type ModuleCatTreeReq struct {
	// 项目 @gotags: validate:"required"
	Project              string   `protobuf:"bytes,1,opt,name=project,proto3" json:"project" validate:"required"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *ModuleCatTreeReq) Reset()         { *m = ModuleCatTreeReq{} }
func (m *ModuleCatTreeReq) String() string { return proto.CompactTextString(m) }
func (*ModuleCatTreeReq) ProtoMessage()    {}
func (*ModuleCatTreeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_546f6c13beca1721, []int{2}
}

func (m *ModuleCatTreeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ModuleCatTreeReq.Unmarshal(m, b)
}
func (m *ModuleCatTreeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ModuleCatTreeReq.Marshal(b, m, deterministic)
}
func (m *ModuleCatTreeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ModuleCatTreeReq.Merge(m, src)
}
func (m *ModuleCatTreeReq) XXX_Size() int {
	return xxx_messageInfo_ModuleCatTreeReq.Size(m)
}
func (m *ModuleCatTreeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ModuleCatTreeReq.DiscardUnknown(m)
}

var xxx_messageInfo_ModuleCatTreeReq proto.InternalMessageInfo

func (m *ModuleCatTreeReq) GetProject() string {
	if m != nil {
		return m.Project
	}
	return ""
}

// ModuleCatTreeResp 问题分类配置(下拉级联多选)列表
type ModuleCatTreeResp struct {
	Data                 []*ModuleCatTreeResp_Cat `protobuf:"bytes,1,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-" gorm:"-"`
	XXX_unrecognized     []byte                   `json:"-" gorm:"-"`
	XXX_sizecache        int32                    `json:"-" gorm:"-"`
}

func (m *ModuleCatTreeResp) Reset()         { *m = ModuleCatTreeResp{} }
func (m *ModuleCatTreeResp) String() string { return proto.CompactTextString(m) }
func (*ModuleCatTreeResp) ProtoMessage()    {}
func (*ModuleCatTreeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_546f6c13beca1721, []int{3}
}

func (m *ModuleCatTreeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ModuleCatTreeResp.Unmarshal(m, b)
}
func (m *ModuleCatTreeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ModuleCatTreeResp.Marshal(b, m, deterministic)
}
func (m *ModuleCatTreeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ModuleCatTreeResp.Merge(m, src)
}
func (m *ModuleCatTreeResp) XXX_Size() int {
	return xxx_messageInfo_ModuleCatTreeResp.Size(m)
}
func (m *ModuleCatTreeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ModuleCatTreeResp.DiscardUnknown(m)
}

var xxx_messageInfo_ModuleCatTreeResp proto.InternalMessageInfo

func (m *ModuleCatTreeResp) GetData() []*ModuleCatTreeResp_Cat {
	if m != nil {
		return m.Data
	}
	return nil
}

type ModuleCatTreeResp_Cat struct {
	CatId    uint32 `protobuf:"varint,1,opt,name=cat_id,json=catId,proto3" json:"cat_id"`
	Category string `protobuf:"bytes,2,opt,name=category,proto3" json:"category"`
	Level    uint32 `protobuf:"varint,3,opt,name=level,proto3" json:"level"`
	// 子结构 @gotags: json:"children,omitempty"
	Children             []*ModuleCatTreeResp_Cat `protobuf:"bytes,7,rep,name=children,proto3" json:"children,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-" gorm:"-"`
	XXX_unrecognized     []byte                   `json:"-" gorm:"-"`
	XXX_sizecache        int32                    `json:"-" gorm:"-"`
}

func (m *ModuleCatTreeResp_Cat) Reset()         { *m = ModuleCatTreeResp_Cat{} }
func (m *ModuleCatTreeResp_Cat) String() string { return proto.CompactTextString(m) }
func (*ModuleCatTreeResp_Cat) ProtoMessage()    {}
func (*ModuleCatTreeResp_Cat) Descriptor() ([]byte, []int) {
	return fileDescriptor_546f6c13beca1721, []int{3, 0}
}

func (m *ModuleCatTreeResp_Cat) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ModuleCatTreeResp_Cat.Unmarshal(m, b)
}
func (m *ModuleCatTreeResp_Cat) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ModuleCatTreeResp_Cat.Marshal(b, m, deterministic)
}
func (m *ModuleCatTreeResp_Cat) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ModuleCatTreeResp_Cat.Merge(m, src)
}
func (m *ModuleCatTreeResp_Cat) XXX_Size() int {
	return xxx_messageInfo_ModuleCatTreeResp_Cat.Size(m)
}
func (m *ModuleCatTreeResp_Cat) XXX_DiscardUnknown() {
	xxx_messageInfo_ModuleCatTreeResp_Cat.DiscardUnknown(m)
}

var xxx_messageInfo_ModuleCatTreeResp_Cat proto.InternalMessageInfo

func (m *ModuleCatTreeResp_Cat) GetCatId() uint32 {
	if m != nil {
		return m.CatId
	}
	return 0
}

func (m *ModuleCatTreeResp_Cat) GetCategory() string {
	if m != nil {
		return m.Category
	}
	return ""
}

func (m *ModuleCatTreeResp_Cat) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *ModuleCatTreeResp_Cat) GetChildren() []*ModuleCatTreeResp_Cat {
	if m != nil {
		return m.Children
	}
	return nil
}

// ModuleCatItems
type ModuleCatItems struct {
	CatId                uint32   `protobuf:"varint,1,opt,name=cat_id,json=catId,proto3" json:"cat_id"`
	ParentId             uint32   `protobuf:"varint,2,opt,name=parent_id,json=parentId,proto3" json:"parent_id"`
	Level                uint32   `protobuf:"varint,4,opt,name=level,proto3" json:"level"`
	Category             string   `protobuf:"bytes,6,opt,name=category,proto3" json:"category"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *ModuleCatItems) Reset()         { *m = ModuleCatItems{} }
func (m *ModuleCatItems) String() string { return proto.CompactTextString(m) }
func (*ModuleCatItems) ProtoMessage()    {}
func (*ModuleCatItems) Descriptor() ([]byte, []int) {
	return fileDescriptor_546f6c13beca1721, []int{4}
}

func (m *ModuleCatItems) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ModuleCatItems.Unmarshal(m, b)
}
func (m *ModuleCatItems) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ModuleCatItems.Marshal(b, m, deterministic)
}
func (m *ModuleCatItems) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ModuleCatItems.Merge(m, src)
}
func (m *ModuleCatItems) XXX_Size() int {
	return xxx_messageInfo_ModuleCatItems.Size(m)
}
func (m *ModuleCatItems) XXX_DiscardUnknown() {
	xxx_messageInfo_ModuleCatItems.DiscardUnknown(m)
}

var xxx_messageInfo_ModuleCatItems proto.InternalMessageInfo

func (m *ModuleCatItems) GetCatId() uint32 {
	if m != nil {
		return m.CatId
	}
	return 0
}

func (m *ModuleCatItems) GetParentId() uint32 {
	if m != nil {
		return m.ParentId
	}
	return 0
}

func (m *ModuleCatItems) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *ModuleCatItems) GetCategory() string {
	if m != nil {
		return m.Category
	}
	return ""
}

func init() {
	proto.RegisterType((*ModuleCatAddReq)(nil), "pb.ModuleCatAddReq")
	proto.RegisterType((*ModuleCatSaveReq)(nil), "pb.ModuleCatSaveReq")
	proto.RegisterType((*ModuleCatTreeReq)(nil), "pb.ModuleCatTreeReq")
	proto.RegisterType((*ModuleCatTreeResp)(nil), "pb.ModuleCatTreeResp")
	proto.RegisterType((*ModuleCatTreeResp_Cat)(nil), "pb.ModuleCatTreeResp.Cat")
	proto.RegisterType((*ModuleCatItems)(nil), "pb.ModuleCatItems")
}

func init() {
	proto.RegisterFile("module_category.proto", fileDescriptor_546f6c13beca1721)
}

var fileDescriptor_546f6c13beca1721 = []byte{
	// 311 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x9c, 0x92, 0x3f, 0x4f, 0xc3, 0x30,
	0x14, 0xc4, 0xe5, 0x24, 0x4d, 0xdb, 0x87, 0xca, 0x1f, 0x8b, 0x4a, 0xa6, 0x2c, 0x55, 0xa6, 0x0e,
	0x90, 0x01, 0xc4, 0xc4, 0x04, 0x9d, 0x2a, 0xc1, 0x12, 0x98, 0x58, 0x2a, 0xc7, 0x7e, 0x82, 0xa2,
	0xb4, 0x31, 0xee, 0xa3, 0x12, 0x23, 0x03, 0x5f, 0x8e, 0x4f, 0x85, 0xe2, 0xa8, 0x69, 0x42, 0x05,
	0x48, 0x8c, 0x97, 0xdc, 0xe9, 0x7e, 0xa7, 0x67, 0xe8, 0xcf, 0x73, 0xfd, 0x9a, 0xe1, 0x54, 0x49,
	0xc2, 0xc7, 0xdc, 0xbe, 0xc5, 0xc6, 0xe6, 0x94, 0x73, 0xcf, 0xa4, 0xd1, 0x3b, 0x83, 0xbd, 0x5b,
	0xf7, 0x77, 0x2c, 0xe9, 0x4a, 0xeb, 0x04, 0x5f, 0xb8, 0x80, 0xb6, 0xb1, 0xf9, 0x33, 0x2a, 0x12,
	0x6c, 0xc8, 0x46, 0xdd, 0x64, 0x2d, 0xf9, 0x31, 0x74, 0x95, 0xa4, 0x69, 0x86, 0x2b, 0xcc, 0x84,
	0x37, 0x64, 0xa3, 0x5e, 0xd2, 0x51, 0x92, 0x6e, 0x0a, 0xcd, 0x07, 0xd0, 0x59, 0x17, 0x08, 0xdf,
	0xe5, 0x2a, 0x5d, 0x04, 0x8d, 0xb4, 0xb8, 0xa0, 0xe9, 0x4c, 0x8b, 0xa0, 0x0c, 0x96, 0x1f, 0x26,
	0x3a, 0x4a, 0x61, 0xbf, 0x42, 0xb8, 0x93, 0x2b, 0x2c, 0x18, 0xfa, 0x10, 0x16, 0x4d, 0x33, 0xed,
	0x10, 0x7a, 0x49, 0x4b, 0x49, 0x9a, 0xe8, 0x46, 0x87, 0xb7, 0xdd, 0xb1, 0x81, 0x6b, 0x37, 0xe1,
	0xa2, 0x93, 0x5a, 0xc7, 0xbd, 0x45, 0xfc, 0x75, 0x67, 0xf4, 0xc9, 0xe0, 0xe0, 0x9b, 0x7d, 0x69,
	0xf8, 0x29, 0x04, 0x5a, 0x92, 0x14, 0x6c, 0xe8, 0x8f, 0x76, 0xce, 0x8e, 0x62, 0x93, 0xc6, 0x5b,
	0xa6, 0x78, 0x2c, 0x29, 0x71, 0xb6, 0xc1, 0x07, 0x03, 0x7f, 0x2c, 0xe9, 0x3f, 0x53, 0x0e, 0xa1,
	0x55, 0xce, 0xf0, 0xcb, 0x84, 0x13, 0xfc, 0x02, 0x3a, 0xea, 0x69, 0x96, 0x69, 0x8b, 0x0b, 0xd1,
	0xfe, 0x8b, 0xa1, 0xb2, 0x46, 0x2b, 0xd8, 0xad, 0x2c, 0x13, 0xc2, 0xf9, 0xf2, 0x27, 0xa2, 0xc6,
	0x91, 0xbc, 0xe6, 0x91, 0x36, 0x48, 0x41, 0x1d, 0xa9, 0x3e, 0x22, 0x6c, 0x8e, 0xb8, 0x0e, 0x1f,
	0x82, 0xf8, 0xd2, 0xa4, 0x69, 0xe8, 0x5e, 0xdb, 0xf9, 0x57, 0x00, 0x00, 0x00, 0xff, 0xff, 0x9c,
	0xc5, 0x5d, 0xe9, 0x86, 0x02, 0x00, 0x00,
}
