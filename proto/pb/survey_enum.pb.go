// Code generated by protoc-gen-go. DO NOT EDIT.
// source: survey_enum.proto

package pb

import (
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

// 问卷有效期
type SurveyEffective int32

const (
	SurveyEffective_UnknownEffectiveDay SurveyEffective = 0
	// 三天
	SurveyEffective_EffectiveDay3 SurveyEffective = 3
	// 五天
	SurveyEffective_EffectiveDay5 SurveyEffective = 5
	// 七天
	SurveyEffective_EffectiveDay7 SurveyEffective = 7
)

var SurveyEffective_name = map[int32]string{
	0: "UnknownEffectiveDay",
	3: "EffectiveDay3",
	5: "EffectiveDay5",
	7: "EffectiveDay7",
}

var SurveyEffective_value = map[string]int32{
	"UnknownEffectiveDay": 0,
	"EffectiveDay3":       3,
	"EffectiveDay5":       5,
	"EffectiveDay7":       7,
}

func (x SurveyEffective) String() string {
	return proto.EnumName(SurveyEffective_name, int32(x))
}

func (SurveyEffective) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_1e343313b2457f4f, []int{0}
}

// 问卷推送周期
type SurveyPushCycle int32

const (
	SurveyPushCycle_UnknownSurveyPushCycle SurveyPushCycle = 0
	/// 每周推送一次
	SurveyPushCycle_SurveyPushCycleEveryWeek SurveyPushCycle = 1
	// 每两周推送一次
	SurveyPushCycle_SurveyPushCycleEveryTwoWeeks SurveyPushCycle = 2
	// 每月推送一次
	SurveyPushCycle_SurveyPushCycleEveryMonth SurveyPushCycle = 3
)

var SurveyPushCycle_name = map[int32]string{
	0: "UnknownSurveyPushCycle",
	1: "SurveyPushCycleEveryWeek",
	2: "SurveyPushCycleEveryTwoWeeks",
	3: "SurveyPushCycleEveryMonth",
}

var SurveyPushCycle_value = map[string]int32{
	"UnknownSurveyPushCycle":       0,
	"SurveyPushCycleEveryWeek":     1,
	"SurveyPushCycleEveryTwoWeeks": 2,
	"SurveyPushCycleEveryMonth":    3,
}

func (x SurveyPushCycle) String() string {
	return proto.EnumName(SurveyPushCycle_name, int32(x))
}

func (SurveyPushCycle) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_1e343313b2457f4f, []int{1}
}

type SurveyQstType int32

const (
	SurveyQstType_UnknownSurveyQstType SurveyQstType = 0
	// 产品题
	SurveyQstType_SurveyQstProduct SurveyQstType = 1
	// 服务题
	SurveyQstType_SurveyQstService SurveyQstType = 2
)

var SurveyQstType_name = map[int32]string{
	0: "UnknownSurveyQstType",
	1: "SurveyQstProduct",
	2: "SurveyQstService",
}

var SurveyQstType_value = map[string]int32{
	"UnknownSurveyQstType": 0,
	"SurveyQstProduct":     1,
	"SurveyQstService":     2,
}

func (x SurveyQstType) String() string {
	return proto.EnumName(SurveyQstType_name, int32(x))
}

func (SurveyQstType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_1e343313b2457f4f, []int{2}
}

// 报表维度
type SurveyStatType int32

const (
	SurveyStatType_UnknownSurveyStatType SurveyStatType = 0
	// 日期
	SurveyStatType_SurveyStatDay SurveyStatType = 1
	// 游戏
	SurveyStatType_SurveyStatGame SurveyStatType = 2
	// 客服
	SurveyStatType_SurveyStatAccount SurveyStatType = 3
)

var SurveyStatType_name = map[int32]string{
	0: "UnknownSurveyStatType",
	1: "SurveyStatDay",
	2: "SurveyStatGame",
	3: "SurveyStatAccount",
}

var SurveyStatType_value = map[string]int32{
	"UnknownSurveyStatType": 0,
	"SurveyStatDay":         1,
	"SurveyStatGame":        2,
	"SurveyStatAccount":     3,
}

func (x SurveyStatType) String() string {
	return proto.EnumName(SurveyStatType_name, int32(x))
}

func (SurveyStatType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_1e343313b2457f4f, []int{3}
}

func init() {
	proto.RegisterEnum("pb.SurveyEffective", SurveyEffective_name, SurveyEffective_value)
	proto.RegisterEnum("pb.SurveyPushCycle", SurveyPushCycle_name, SurveyPushCycle_value)
	proto.RegisterEnum("pb.SurveyQstType", SurveyQstType_name, SurveyQstType_value)
	proto.RegisterEnum("pb.SurveyStatType", SurveyStatType_name, SurveyStatType_value)
}

func init() {
	proto.RegisterFile("survey_enum.proto", fileDescriptor_1e343313b2457f4f)
}

var fileDescriptor_1e343313b2457f4f = []byte{
	// 273 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x6c, 0xd1, 0x4b, 0x4b, 0xc3, 0x40,
	0x10, 0x07, 0xf0, 0x3c, 0xb4, 0xc2, 0x40, 0x75, 0x32, 0xb6, 0xda, 0x4a, 0x05, 0xcf, 0x39, 0xf4,
	0x52, 0xc4, 0x83, 0x27, 0x1f, 0xc5, 0x93, 0x50, 0x49, 0x8b, 0xe0, 0x45, 0x92, 0x75, 0x4a, 0x43,
	0xed, 0x6e, 0x48, 0x36, 0x29, 0xfb, 0x1d, 0xfc, 0xd0, 0xd2, 0xa6, 0xaf, 0x84, 0x5e, 0x7f, 0xff,
	0xd9, 0x99, 0x65, 0x06, 0xbc, 0x2c, 0x4f, 0x0b, 0x36, 0xdf, 0x2c, 0xf3, 0x45, 0x3f, 0x49, 0x95,
	0x56, 0xe4, 0x24, 0x91, 0x2f, 0xe0, 0x22, 0x58, 0x07, 0xc3, 0xe9, 0x94, 0x85, 0x8e, 0x0b, 0xa6,
	0x6b, 0xb8, 0x9c, 0xc8, 0xb9, 0x54, 0x4b, 0xb9, 0xb3, 0xd7, 0xd0, 0xa0, 0x45, 0x1e, 0x34, 0x0f,
	0x65, 0x80, 0x6e, 0x9d, 0xee, 0xf1, 0xb4, 0x4e, 0x0f, 0x78, 0xe6, 0xff, 0xd9, 0xdb, 0x29, 0xa3,
	0x3c, 0x9b, 0xbd, 0x18, 0xf1, 0xcb, 0x74, 0x03, 0x57, 0x9b, 0x29, 0xb5, 0x04, 0x2d, 0xea, 0x41,
	0xa7, 0x86, 0xc3, 0x82, 0x53, 0xf3, 0xc9, 0x3c, 0x47, 0x9b, 0xee, 0xa0, 0x77, 0x2c, 0x1d, 0x2f,
	0xd5, 0xaa, 0x20, 0x43, 0x87, 0x6e, 0xa1, 0x7b, 0xac, 0xe2, 0x5d, 0x49, 0x3d, 0x43, 0xd7, 0x9f,
	0x40, 0xb3, 0x8c, 0x3f, 0x32, 0x3d, 0x36, 0x09, 0x53, 0x07, 0x5a, 0x95, 0xbf, 0x6c, 0x1c, 0x2d,
	0x6a, 0x01, 0xee, 0x68, 0x94, 0xaa, 0x9f, 0x5c, 0x68, 0xb4, 0x2b, 0x1a, 0x70, 0x5a, 0xc4, 0x82,
	0xd1, 0xf1, 0x63, 0x38, 0x2f, 0x35, 0xd0, 0x61, 0xd9, 0xb7, 0x0b, 0xed, 0x4a, 0xdf, 0x6d, 0x50,
	0xee, 0x72, 0x6f, 0xab, 0xf5, 0xda, 0x44, 0x87, 0xef, 0xdf, 0xc2, 0x05, 0xa3, 0x43, 0x6d, 0xf0,
	0xf6, 0xf6, 0x24, 0x84, 0xca, 0xa5, 0x46, 0xf7, 0xb9, 0xf1, 0x75, 0xd2, 0x7f, 0x4c, 0xa2, 0xa8,
	0xb1, 0x3e, 0xe4, 0xe0, 0x3f, 0x00, 0x00, 0xff, 0xff, 0x84, 0xcf, 0x21, 0x7e, 0xdd, 0x01, 0x00,
	0x00,
}
