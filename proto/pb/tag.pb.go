// Code generated by protoc-gen-go. DO NOT EDIT.
// source: tag.proto

package pb

import (
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

type TagId struct {
	// 标签ID @gotags: validate:"required"
	TagId                uint32   `protobuf:"varint,1,opt,name=tag_id,json=tagId,proto3" json:"tag_id" validate:"required"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *TagId) Reset()         { *m = TagId{} }
func (m *TagId) String() string { return proto.CompactTextString(m) }
func (*TagId) ProtoMessage()    {}
func (*TagId) Descriptor() ([]byte, []int) {
	return fileDescriptor_27f545bcde37ecb5, []int{0}
}

func (m *TagId) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TagId.Unmarshal(m, b)
}
func (m *TagId) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TagId.Marshal(b, m, deterministic)
}
func (m *TagId) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TagId.Merge(m, src)
}
func (m *TagId) XXX_Size() int {
	return xxx_messageInfo_TagId.Size(m)
}
func (m *TagId) XXX_DiscardUnknown() {
	xxx_messageInfo_TagId.DiscardUnknown(m)
}

var xxx_messageInfo_TagId proto.InternalMessageInfo

func (m *TagId) GetTagId() uint32 {
	if m != nil {
		return m.TagId
	}
	return 0
}

// TagReq 添加标签请求参数
type TagAddReq struct {
	// 标签组ID @gotags: validate:"required"
	LibId uint32 `protobuf:"varint,1,opt,name=lib_id,json=libId,proto3" json:"lib_id" validate:"required"`
	// 级别
	Level uint32 `protobuf:"varint,2,opt,name=level,proto3" json:"level"`
	// 父级ID
	Pid uint32 `protobuf:"varint,3,opt,name=pid,proto3" json:"pid"`
	// 标签名称 @gotags: validate:"required"
	TagName              string   `protobuf:"bytes,4,opt,name=tag_name,json=tagName,proto3" json:"tag_name" validate:"required"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *TagAddReq) Reset()         { *m = TagAddReq{} }
func (m *TagAddReq) String() string { return proto.CompactTextString(m) }
func (*TagAddReq) ProtoMessage()    {}
func (*TagAddReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_27f545bcde37ecb5, []int{1}
}

func (m *TagAddReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TagAddReq.Unmarshal(m, b)
}
func (m *TagAddReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TagAddReq.Marshal(b, m, deterministic)
}
func (m *TagAddReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TagAddReq.Merge(m, src)
}
func (m *TagAddReq) XXX_Size() int {
	return xxx_messageInfo_TagAddReq.Size(m)
}
func (m *TagAddReq) XXX_DiscardUnknown() {
	xxx_messageInfo_TagAddReq.DiscardUnknown(m)
}

var xxx_messageInfo_TagAddReq proto.InternalMessageInfo

func (m *TagAddReq) GetLibId() uint32 {
	if m != nil {
		return m.LibId
	}
	return 0
}

func (m *TagAddReq) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *TagAddReq) GetPid() uint32 {
	if m != nil {
		return m.Pid
	}
	return 0
}

func (m *TagAddReq) GetTagName() string {
	if m != nil {
		return m.TagName
	}
	return ""
}

type TagNode struct {
	Name                 string     `protobuf:"bytes,1,opt,name=name,proto3" json:"name"`
	Children             []*TagNode `protobuf:"bytes,2,rep,name=children,proto3" json:"children"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-" gorm:"-"`
	XXX_unrecognized     []byte     `json:"-" gorm:"-"`
	XXX_sizecache        int32      `json:"-" gorm:"-"`
}

func (m *TagNode) Reset()         { *m = TagNode{} }
func (m *TagNode) String() string { return proto.CompactTextString(m) }
func (*TagNode) ProtoMessage()    {}
func (*TagNode) Descriptor() ([]byte, []int) {
	return fileDescriptor_27f545bcde37ecb5, []int{2}
}

func (m *TagNode) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TagNode.Unmarshal(m, b)
}
func (m *TagNode) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TagNode.Marshal(b, m, deterministic)
}
func (m *TagNode) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TagNode.Merge(m, src)
}
func (m *TagNode) XXX_Size() int {
	return xxx_messageInfo_TagNode.Size(m)
}
func (m *TagNode) XXX_DiscardUnknown() {
	xxx_messageInfo_TagNode.DiscardUnknown(m)
}

var xxx_messageInfo_TagNode proto.InternalMessageInfo

func (m *TagNode) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *TagNode) GetChildren() []*TagNode {
	if m != nil {
		return m.Children
	}
	return nil
}

type TagGroups struct {
	Tags                 []*TagGroup `protobuf:"bytes,1,rep,name=tags,proto3" json:"tags"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-" gorm:"-"`
	XXX_unrecognized     []byte      `json:"-" gorm:"-"`
	XXX_sizecache        int32       `json:"-" gorm:"-"`
}

func (m *TagGroups) Reset()         { *m = TagGroups{} }
func (m *TagGroups) String() string { return proto.CompactTextString(m) }
func (*TagGroups) ProtoMessage()    {}
func (*TagGroups) Descriptor() ([]byte, []int) {
	return fileDescriptor_27f545bcde37ecb5, []int{3}
}

func (m *TagGroups) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TagGroups.Unmarshal(m, b)
}
func (m *TagGroups) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TagGroups.Marshal(b, m, deterministic)
}
func (m *TagGroups) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TagGroups.Merge(m, src)
}
func (m *TagGroups) XXX_Size() int {
	return xxx_messageInfo_TagGroups.Size(m)
}
func (m *TagGroups) XXX_DiscardUnknown() {
	xxx_messageInfo_TagGroups.DiscardUnknown(m)
}

var xxx_messageInfo_TagGroups proto.InternalMessageInfo

func (m *TagGroups) GetTags() []*TagGroup {
	if m != nil {
		return m.Tags
	}
	return nil
}

type TagGroup struct {
	LevelOne             string   `protobuf:"bytes,1,opt,name=level_one,json=levelOne,proto3" json:"level_one"`
	LevelTwo             string   `protobuf:"bytes,2,opt,name=level_two,json=levelTwo,proto3" json:"level_two"`
	LevelThree           string   `protobuf:"bytes,3,opt,name=level_three,json=levelThree,proto3" json:"level_three"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *TagGroup) Reset()         { *m = TagGroup{} }
func (m *TagGroup) String() string { return proto.CompactTextString(m) }
func (*TagGroup) ProtoMessage()    {}
func (*TagGroup) Descriptor() ([]byte, []int) {
	return fileDescriptor_27f545bcde37ecb5, []int{4}
}

func (m *TagGroup) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TagGroup.Unmarshal(m, b)
}
func (m *TagGroup) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TagGroup.Marshal(b, m, deterministic)
}
func (m *TagGroup) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TagGroup.Merge(m, src)
}
func (m *TagGroup) XXX_Size() int {
	return xxx_messageInfo_TagGroup.Size(m)
}
func (m *TagGroup) XXX_DiscardUnknown() {
	xxx_messageInfo_TagGroup.DiscardUnknown(m)
}

var xxx_messageInfo_TagGroup proto.InternalMessageInfo

func (m *TagGroup) GetLevelOne() string {
	if m != nil {
		return m.LevelOne
	}
	return ""
}

func (m *TagGroup) GetLevelTwo() string {
	if m != nil {
		return m.LevelTwo
	}
	return ""
}

func (m *TagGroup) GetLevelThree() string {
	if m != nil {
		return m.LevelThree
	}
	return ""
}

func init() {
	proto.RegisterType((*TagId)(nil), "pb.TagId")
	proto.RegisterType((*TagAddReq)(nil), "pb.TagAddReq")
	proto.RegisterType((*TagNode)(nil), "pb.TagNode")
	proto.RegisterType((*TagGroups)(nil), "pb.TagGroups")
	proto.RegisterType((*TagGroup)(nil), "pb.TagGroup")
}

func init() {
	proto.RegisterFile("tag.proto", fileDescriptor_27f545bcde37ecb5)
}

var fileDescriptor_27f545bcde37ecb5 = []byte{
	// 275 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x4c, 0x90, 0xc1, 0x4b, 0xec, 0x30,
	0x10, 0x87, 0xe9, 0x6e, 0xdb, 0x6d, 0xa7, 0xef, 0x81, 0x04, 0x85, 0x88, 0xa0, 0xa5, 0x17, 0x7b,
	0xb1, 0x07, 0x3d, 0x7a, 0xd2, 0x83, 0xb2, 0x97, 0x15, 0x42, 0x4f, 0x5e, 0x96, 0xc4, 0x0c, 0xd9,
	0x42, 0xb7, 0x89, 0xdd, 0xe8, 0xfe, 0xfb, 0x92, 0xb1, 0x55, 0x6f, 0x93, 0xef, 0xfb, 0x0d, 0x33,
	0x13, 0xc8, 0xbd, 0x34, 0x8d, 0x1b, 0xad, 0xb7, 0x6c, 0xe1, 0x54, 0x75, 0x09, 0x49, 0x2b, 0xcd,
	0x5a, 0xb3, 0x33, 0x48, 0xbd, 0x34, 0xdb, 0x4e, 0xf3, 0xa8, 0x8c, 0xea, 0xff, 0x22, 0xf1, 0x01,
	0x57, 0x08, 0x79, 0x2b, 0xcd, 0x83, 0xd6, 0x02, 0xdf, 0x43, 0xa6, 0xef, 0xd4, 0x9f, 0x4c, 0xdf,
	0xa9, 0xb5, 0x66, 0xa7, 0x90, 0xf4, 0xf8, 0x89, 0x3d, 0x5f, 0x4c, 0x34, 0x3c, 0xd8, 0x09, 0x2c,
	0x5d, 0xa7, 0xf9, 0x92, 0x58, 0x28, 0xd9, 0x39, 0x64, 0x61, 0xc4, 0x20, 0xf7, 0xc8, 0xe3, 0x32,
	0xaa, 0x73, 0xb1, 0xf2, 0xd2, 0x6c, 0xe4, 0x1e, 0xab, 0x27, 0x58, 0xb5, 0xd2, 0x6c, 0xac, 0x46,
	0xc6, 0x20, 0xa6, 0x44, 0x44, 0x09, 0xaa, 0xd9, 0x35, 0x64, 0x6f, 0xbb, 0xae, 0xd7, 0x23, 0x0e,
	0x7c, 0x51, 0x2e, 0xeb, 0xe2, 0xb6, 0x68, 0x9c, 0x6a, 0xa6, 0x16, 0xf1, 0x23, 0xab, 0x1b, 0x5a,
	0xf7, 0x79, 0xb4, 0x1f, 0xee, 0xc0, 0x4a, 0x88, 0xbd, 0x34, 0x07, 0x1e, 0x51, 0xc7, 0xbf, 0xa9,
	0x83, 0xa4, 0x20, 0x53, 0x21, 0x64, 0x33, 0x61, 0x17, 0x90, 0xd3, 0xe2, 0x5b, 0x3b, 0xcc, 0xc3,
	0x33, 0x02, 0x2f, 0x03, 0xfe, 0x4a, 0x7f, 0xb4, 0x74, 0xe6, 0x2c, 0xdb, 0xa3, 0x65, 0x57, 0x50,
	0x4c, 0x72, 0x37, 0x22, 0xd2, 0xc5, 0xb9, 0x80, 0x6f, 0x1d, 0xc8, 0x63, 0xfa, 0x1a, 0x37, 0xf7,
	0x4e, 0xa9, 0x94, 0xfe, 0xfd, 0xee, 0x2b, 0x00, 0x00, 0xff, 0xff, 0x9b, 0x44, 0xee, 0x42, 0x84,
	0x01, 0x00, 0x00,
}
