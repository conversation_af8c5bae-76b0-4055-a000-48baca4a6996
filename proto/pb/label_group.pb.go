// Code generated by protoc-gen-go. DO NOT EDIT.
// source: label_group.proto

package pb

import (
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

// TagLibOptsResp 标签组列表
type TagLibOptsResp struct {
	Data                 []*TagLibOptsResp_TagLib `protobuf:"bytes,1,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-" gorm:"-"`
	XXX_unrecognized     []byte                   `json:"-" gorm:"-"`
	XXX_sizecache        int32                    `json:"-" gorm:"-"`
}

func (m *TagLibOptsResp) Reset()         { *m = TagLibOptsResp{} }
func (m *TagLibOptsResp) String() string { return proto.CompactTextString(m) }
func (*TagLibOptsResp) ProtoMessage()    {}
func (*TagLibOptsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_debfb026132a9ef5, []int{0}
}

func (m *TagLibOptsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TagLibOptsResp.Unmarshal(m, b)
}
func (m *TagLibOptsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TagLibOptsResp.Marshal(b, m, deterministic)
}
func (m *TagLibOptsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TagLibOptsResp.Merge(m, src)
}
func (m *TagLibOptsResp) XXX_Size() int {
	return xxx_messageInfo_TagLibOptsResp.Size(m)
}
func (m *TagLibOptsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_TagLibOptsResp.DiscardUnknown(m)
}

var xxx_messageInfo_TagLibOptsResp proto.InternalMessageInfo

func (m *TagLibOptsResp) GetData() []*TagLibOptsResp_TagLib {
	if m != nil {
		return m.Data
	}
	return nil
}

type TagLibOptsResp_TagLib struct {
	// 标签ID
	LibId uint32 `protobuf:"varint,1,opt,name=lib_id,json=libId,proto3" json:"lib_id"`
	// 标签名称
	LibName              string   `protobuf:"bytes,2,opt,name=lib_name,json=libName,proto3" json:"lib_name"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *TagLibOptsResp_TagLib) Reset()         { *m = TagLibOptsResp_TagLib{} }
func (m *TagLibOptsResp_TagLib) String() string { return proto.CompactTextString(m) }
func (*TagLibOptsResp_TagLib) ProtoMessage()    {}
func (*TagLibOptsResp_TagLib) Descriptor() ([]byte, []int) {
	return fileDescriptor_debfb026132a9ef5, []int{0, 0}
}

func (m *TagLibOptsResp_TagLib) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TagLibOptsResp_TagLib.Unmarshal(m, b)
}
func (m *TagLibOptsResp_TagLib) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TagLibOptsResp_TagLib.Marshal(b, m, deterministic)
}
func (m *TagLibOptsResp_TagLib) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TagLibOptsResp_TagLib.Merge(m, src)
}
func (m *TagLibOptsResp_TagLib) XXX_Size() int {
	return xxx_messageInfo_TagLibOptsResp_TagLib.Size(m)
}
func (m *TagLibOptsResp_TagLib) XXX_DiscardUnknown() {
	xxx_messageInfo_TagLibOptsResp_TagLib.DiscardUnknown(m)
}

var xxx_messageInfo_TagLibOptsResp_TagLib proto.InternalMessageInfo

func (m *TagLibOptsResp_TagLib) GetLibId() uint32 {
	if m != nil {
		return m.LibId
	}
	return 0
}

func (m *TagLibOptsResp_TagLib) GetLibName() string {
	if m != nil {
		return m.LibName
	}
	return ""
}

// TagLibListReq 标签库列表搜索
type TagLibListReq struct {
	// 标签库名称
	LibName string `protobuf:"bytes,1,opt,name=lib_name,json=libName,proto3" json:"lib_name"`
	// 标签类型 @gotags: validate:"required,oneof=1 2 3"
	LibType              uint32   `protobuf:"varint,5,opt,name=lib_type,json=libType,proto3" json:"lib_type" validate:"required,oneof=1 2 3"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *TagLibListReq) Reset()         { *m = TagLibListReq{} }
func (m *TagLibListReq) String() string { return proto.CompactTextString(m) }
func (*TagLibListReq) ProtoMessage()    {}
func (*TagLibListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_debfb026132a9ef5, []int{1}
}

func (m *TagLibListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TagLibListReq.Unmarshal(m, b)
}
func (m *TagLibListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TagLibListReq.Marshal(b, m, deterministic)
}
func (m *TagLibListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TagLibListReq.Merge(m, src)
}
func (m *TagLibListReq) XXX_Size() int {
	return xxx_messageInfo_TagLibListReq.Size(m)
}
func (m *TagLibListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_TagLibListReq.DiscardUnknown(m)
}

var xxx_messageInfo_TagLibListReq proto.InternalMessageInfo

func (m *TagLibListReq) GetLibName() string {
	if m != nil {
		return m.LibName
	}
	return ""
}

func (m *TagLibListReq) GetLibType() uint32 {
	if m != nil {
		return m.LibType
	}
	return 0
}

// TagLibIDReq 标签库ID请求参数
type TagLibID struct {
	// 标签库ID @gotags: validate:"required"
	LibId                uint32   `protobuf:"varint,1,opt,name=lib_id,json=libId,proto3" json:"lib_id" validate:"required"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *TagLibID) Reset()         { *m = TagLibID{} }
func (m *TagLibID) String() string { return proto.CompactTextString(m) }
func (*TagLibID) ProtoMessage()    {}
func (*TagLibID) Descriptor() ([]byte, []int) {
	return fileDescriptor_debfb026132a9ef5, []int{2}
}

func (m *TagLibID) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TagLibID.Unmarshal(m, b)
}
func (m *TagLibID) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TagLibID.Marshal(b, m, deterministic)
}
func (m *TagLibID) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TagLibID.Merge(m, src)
}
func (m *TagLibID) XXX_Size() int {
	return xxx_messageInfo_TagLibID.Size(m)
}
func (m *TagLibID) XXX_DiscardUnknown() {
	xxx_messageInfo_TagLibID.DiscardUnknown(m)
}

var xxx_messageInfo_TagLibID proto.InternalMessageInfo

func (m *TagLibID) GetLibId() uint32 {
	if m != nil {
		return m.LibId
	}
	return 0
}

// TagLibListResp 标签库列表响应结果
type TagLibListResp struct {
	Data                 []*TagLibListResp_TagLib `protobuf:"bytes,1,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-" gorm:"-"`
	XXX_unrecognized     []byte                   `json:"-" gorm:"-"`
	XXX_sizecache        int32                    `json:"-" gorm:"-"`
}

func (m *TagLibListResp) Reset()         { *m = TagLibListResp{} }
func (m *TagLibListResp) String() string { return proto.CompactTextString(m) }
func (*TagLibListResp) ProtoMessage()    {}
func (*TagLibListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_debfb026132a9ef5, []int{3}
}

func (m *TagLibListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TagLibListResp.Unmarshal(m, b)
}
func (m *TagLibListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TagLibListResp.Marshal(b, m, deterministic)
}
func (m *TagLibListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TagLibListResp.Merge(m, src)
}
func (m *TagLibListResp) XXX_Size() int {
	return xxx_messageInfo_TagLibListResp.Size(m)
}
func (m *TagLibListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_TagLibListResp.DiscardUnknown(m)
}

var xxx_messageInfo_TagLibListResp proto.InternalMessageInfo

func (m *TagLibListResp) GetData() []*TagLibListResp_TagLib {
	if m != nil {
		return m.Data
	}
	return nil
}

type TagLibListResp_TagLib struct {
	// 标签库ID
	LibId uint32 `protobuf:"varint,1,opt,name=lib_id,json=libId,proto3" json:"lib_id"`
	// 标签库名称
	LibName string `protobuf:"bytes,2,opt,name=lib_name,json=libName,proto3" json:"lib_name"`
	// 标签数据 - 原始文件地址
	TagUploadFile string `protobuf:"bytes,3,opt,name=tag_upload_file,json=tagUploadFile,proto3" json:"tag_upload_file"`
	// 更新时间
	UpdatedAt string `protobuf:"bytes,5,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at"`
	// 更新用户
	Op string `protobuf:"bytes,6,opt,name=op,proto3" json:"op"`
	// 状态
	Enable bool `protobuf:"varint,7,opt,name=enable,proto3" json:"enable"`
	// 项目列表 @gotags: gorm:"-"
	Projects             []string `protobuf:"bytes,9,rep,name=projects,proto3" json:"projects" gorm:"-"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *TagLibListResp_TagLib) Reset()         { *m = TagLibListResp_TagLib{} }
func (m *TagLibListResp_TagLib) String() string { return proto.CompactTextString(m) }
func (*TagLibListResp_TagLib) ProtoMessage()    {}
func (*TagLibListResp_TagLib) Descriptor() ([]byte, []int) {
	return fileDescriptor_debfb026132a9ef5, []int{3, 0}
}

func (m *TagLibListResp_TagLib) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TagLibListResp_TagLib.Unmarshal(m, b)
}
func (m *TagLibListResp_TagLib) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TagLibListResp_TagLib.Marshal(b, m, deterministic)
}
func (m *TagLibListResp_TagLib) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TagLibListResp_TagLib.Merge(m, src)
}
func (m *TagLibListResp_TagLib) XXX_Size() int {
	return xxx_messageInfo_TagLibListResp_TagLib.Size(m)
}
func (m *TagLibListResp_TagLib) XXX_DiscardUnknown() {
	xxx_messageInfo_TagLibListResp_TagLib.DiscardUnknown(m)
}

var xxx_messageInfo_TagLibListResp_TagLib proto.InternalMessageInfo

func (m *TagLibListResp_TagLib) GetLibId() uint32 {
	if m != nil {
		return m.LibId
	}
	return 0
}

func (m *TagLibListResp_TagLib) GetLibName() string {
	if m != nil {
		return m.LibName
	}
	return ""
}

func (m *TagLibListResp_TagLib) GetTagUploadFile() string {
	if m != nil {
		return m.TagUploadFile
	}
	return ""
}

func (m *TagLibListResp_TagLib) GetUpdatedAt() string {
	if m != nil {
		return m.UpdatedAt
	}
	return ""
}

func (m *TagLibListResp_TagLib) GetOp() string {
	if m != nil {
		return m.Op
	}
	return ""
}

func (m *TagLibListResp_TagLib) GetEnable() bool {
	if m != nil {
		return m.Enable
	}
	return false
}

func (m *TagLibListResp_TagLib) GetProjects() []string {
	if m != nil {
		return m.Projects
	}
	return nil
}

// TagLibInfoResp 标签库信息
type TagLibInfoResp struct {
	// 标签库ID
	LibId uint32 `protobuf:"varint,1,opt,name=lib_id,json=libId,proto3" json:"lib_id"`
	// 标签库名称
	LibName string `protobuf:"bytes,2,opt,name=lib_name,json=libName,proto3" json:"lib_name"`
	// 标签库描述
	LibFileUrl string `protobuf:"bytes,3,opt,name=lib_file_url,json=libFileUrl,proto3" json:"lib_file_url"`
	// 标签数据 - 原始文件地址
	Projects []string `protobuf:"bytes,4,rep,name=projects,proto3" json:"projects"`
	// @gotags: json:"project"
	Project              []string `protobuf:"bytes,5,rep,name=project,proto3" json:"project"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *TagLibInfoResp) Reset()         { *m = TagLibInfoResp{} }
func (m *TagLibInfoResp) String() string { return proto.CompactTextString(m) }
func (*TagLibInfoResp) ProtoMessage()    {}
func (*TagLibInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_debfb026132a9ef5, []int{4}
}

func (m *TagLibInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TagLibInfoResp.Unmarshal(m, b)
}
func (m *TagLibInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TagLibInfoResp.Marshal(b, m, deterministic)
}
func (m *TagLibInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TagLibInfoResp.Merge(m, src)
}
func (m *TagLibInfoResp) XXX_Size() int {
	return xxx_messageInfo_TagLibInfoResp.Size(m)
}
func (m *TagLibInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_TagLibInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_TagLibInfoResp proto.InternalMessageInfo

func (m *TagLibInfoResp) GetLibId() uint32 {
	if m != nil {
		return m.LibId
	}
	return 0
}

func (m *TagLibInfoResp) GetLibName() string {
	if m != nil {
		return m.LibName
	}
	return ""
}

func (m *TagLibInfoResp) GetLibFileUrl() string {
	if m != nil {
		return m.LibFileUrl
	}
	return ""
}

func (m *TagLibInfoResp) GetProjects() []string {
	if m != nil {
		return m.Projects
	}
	return nil
}

func (m *TagLibInfoResp) GetProject() []string {
	if m != nil {
		return m.Project
	}
	return nil
}

// GroupAddReq 添加标签库请求参数
type TagLibAddReq struct {
	// 标签库名称 @gotags: validate:"required"
	LibName string `protobuf:"bytes,1,opt,name=lib_name,json=libName,proto3" json:"lib_name" validate:"required"`
	// 标签数据 - 原始文件地址 @gotags: validate:"required"
	LibFileUrl string `protobuf:"bytes,2,opt,name=lib_file_url,json=libFileUrl,proto3" json:"lib_file_url" validate:"required"`
	// 关联游戏 @gotags: validate:"required,gt=0"
	Projects             []string `protobuf:"bytes,3,rep,name=projects,proto3" json:"projects" validate:"required,gt=0"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *TagLibAddReq) Reset()         { *m = TagLibAddReq{} }
func (m *TagLibAddReq) String() string { return proto.CompactTextString(m) }
func (*TagLibAddReq) ProtoMessage()    {}
func (*TagLibAddReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_debfb026132a9ef5, []int{5}
}

func (m *TagLibAddReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TagLibAddReq.Unmarshal(m, b)
}
func (m *TagLibAddReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TagLibAddReq.Marshal(b, m, deterministic)
}
func (m *TagLibAddReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TagLibAddReq.Merge(m, src)
}
func (m *TagLibAddReq) XXX_Size() int {
	return xxx_messageInfo_TagLibAddReq.Size(m)
}
func (m *TagLibAddReq) XXX_DiscardUnknown() {
	xxx_messageInfo_TagLibAddReq.DiscardUnknown(m)
}

var xxx_messageInfo_TagLibAddReq proto.InternalMessageInfo

func (m *TagLibAddReq) GetLibName() string {
	if m != nil {
		return m.LibName
	}
	return ""
}

func (m *TagLibAddReq) GetLibFileUrl() string {
	if m != nil {
		return m.LibFileUrl
	}
	return ""
}

func (m *TagLibAddReq) GetProjects() []string {
	if m != nil {
		return m.Projects
	}
	return nil
}

// TagLibSaveReq 更新标签库请求参数
type TagLibSaveReq struct {
	// 标签库ID
	LibId uint32 `protobuf:"varint,1,opt,name=lib_id,json=libId,proto3" json:"lib_id"`
	// 标签库名称 @gotags: validate:"required"
	LibName string `protobuf:"bytes,2,opt,name=lib_name,json=libName,proto3" json:"lib_name" validate:"required"`
	// 标签数据-原始文件地址 @gotags: validate:"required"
	LibFileUrl string `protobuf:"bytes,3,opt,name=lib_file_url,json=libFileUrl,proto3" json:"lib_file_url" validate:"required"`
	// 关联游戏 @gotags: validate:"required,gt=0"
	Projects             []string `protobuf:"bytes,4,rep,name=projects,proto3" json:"projects" validate:"required,gt=0"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *TagLibSaveReq) Reset()         { *m = TagLibSaveReq{} }
func (m *TagLibSaveReq) String() string { return proto.CompactTextString(m) }
func (*TagLibSaveReq) ProtoMessage()    {}
func (*TagLibSaveReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_debfb026132a9ef5, []int{6}
}

func (m *TagLibSaveReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TagLibSaveReq.Unmarshal(m, b)
}
func (m *TagLibSaveReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TagLibSaveReq.Marshal(b, m, deterministic)
}
func (m *TagLibSaveReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TagLibSaveReq.Merge(m, src)
}
func (m *TagLibSaveReq) XXX_Size() int {
	return xxx_messageInfo_TagLibSaveReq.Size(m)
}
func (m *TagLibSaveReq) XXX_DiscardUnknown() {
	xxx_messageInfo_TagLibSaveReq.DiscardUnknown(m)
}

var xxx_messageInfo_TagLibSaveReq proto.InternalMessageInfo

func (m *TagLibSaveReq) GetLibId() uint32 {
	if m != nil {
		return m.LibId
	}
	return 0
}

func (m *TagLibSaveReq) GetLibName() string {
	if m != nil {
		return m.LibName
	}
	return ""
}

func (m *TagLibSaveReq) GetLibFileUrl() string {
	if m != nil {
		return m.LibFileUrl
	}
	return ""
}

func (m *TagLibSaveReq) GetProjects() []string {
	if m != nil {
		return m.Projects
	}
	return nil
}

// TagLibSaveReq 更新标签库请求参数
type AllTagLibSaveReq struct {
	// 标签库ID
	LibId uint32 `protobuf:"varint,1,opt,name=lib_id,json=libId,proto3" json:"lib_id"`
	// 标签库名称 @gotags: validate:"required"
	LibName string `protobuf:"bytes,2,opt,name=lib_name,json=libName,proto3" json:"lib_name" validate:"required"`
	// 标签数据-原始文件地址 @gotags: validate:"required"
	LibFileUrl string `protobuf:"bytes,3,opt,name=lib_file_url,json=libFileUrl,proto3" json:"lib_file_url" validate:"required"`
	// 关联游戏 @gotags: validate:"required,gt=0"
	Projects []string `protobuf:"bytes,4,rep,name=projects,proto3" json:"projects" validate:"required,gt=0"`
	// 标签类型 @gotags: validate:"required,oneof=1 2 3"
	LibType              uint32   `protobuf:"varint,5,opt,name=lib_type,json=libType,proto3" json:"lib_type" validate:"required,oneof=1 2 3"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *AllTagLibSaveReq) Reset()         { *m = AllTagLibSaveReq{} }
func (m *AllTagLibSaveReq) String() string { return proto.CompactTextString(m) }
func (*AllTagLibSaveReq) ProtoMessage()    {}
func (*AllTagLibSaveReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_debfb026132a9ef5, []int{7}
}

func (m *AllTagLibSaveReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AllTagLibSaveReq.Unmarshal(m, b)
}
func (m *AllTagLibSaveReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AllTagLibSaveReq.Marshal(b, m, deterministic)
}
func (m *AllTagLibSaveReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AllTagLibSaveReq.Merge(m, src)
}
func (m *AllTagLibSaveReq) XXX_Size() int {
	return xxx_messageInfo_AllTagLibSaveReq.Size(m)
}
func (m *AllTagLibSaveReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AllTagLibSaveReq.DiscardUnknown(m)
}

var xxx_messageInfo_AllTagLibSaveReq proto.InternalMessageInfo

func (m *AllTagLibSaveReq) GetLibId() uint32 {
	if m != nil {
		return m.LibId
	}
	return 0
}

func (m *AllTagLibSaveReq) GetLibName() string {
	if m != nil {
		return m.LibName
	}
	return ""
}

func (m *AllTagLibSaveReq) GetLibFileUrl() string {
	if m != nil {
		return m.LibFileUrl
	}
	return ""
}

func (m *AllTagLibSaveReq) GetProjects() []string {
	if m != nil {
		return m.Projects
	}
	return nil
}

func (m *AllTagLibSaveReq) GetLibType() uint32 {
	if m != nil {
		return m.LibType
	}
	return 0
}

type TagOptsReq struct {
	// 项目名称-根据项目名称进行搜索 @gotags: validate:"required"
	ProjectName string `protobuf:"bytes,1,opt,name=project_name,json=projectName,proto3" json:"project_name" validate:"required"`
	// 标签库ID - 根据标签库ID进行搜索
	LibId uint32 `protobuf:"varint,2,opt,name=lib_id,json=libId,proto3" json:"lib_id"`
	// 标签类型 @gotags: validate:"required,oneof=1 2 3"
	LibType              uint32   `protobuf:"varint,5,opt,name=lib_type,json=libType,proto3" json:"lib_type" validate:"required,oneof=1 2 3"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *TagOptsReq) Reset()         { *m = TagOptsReq{} }
func (m *TagOptsReq) String() string { return proto.CompactTextString(m) }
func (*TagOptsReq) ProtoMessage()    {}
func (*TagOptsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_debfb026132a9ef5, []int{8}
}

func (m *TagOptsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TagOptsReq.Unmarshal(m, b)
}
func (m *TagOptsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TagOptsReq.Marshal(b, m, deterministic)
}
func (m *TagOptsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TagOptsReq.Merge(m, src)
}
func (m *TagOptsReq) XXX_Size() int {
	return xxx_messageInfo_TagOptsReq.Size(m)
}
func (m *TagOptsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_TagOptsReq.DiscardUnknown(m)
}

var xxx_messageInfo_TagOptsReq proto.InternalMessageInfo

func (m *TagOptsReq) GetProjectName() string {
	if m != nil {
		return m.ProjectName
	}
	return ""
}

func (m *TagOptsReq) GetLibId() uint32 {
	if m != nil {
		return m.LibId
	}
	return 0
}

func (m *TagOptsReq) GetLibType() uint32 {
	if m != nil {
		return m.LibType
	}
	return 0
}

// TagOptsResp 标签列表
type TagOptsResp struct {
	Data                 []*TagOptsResp_Tag `protobuf:"bytes,1,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-" gorm:"-"`
	XXX_unrecognized     []byte             `json:"-" gorm:"-"`
	XXX_sizecache        int32              `json:"-" gorm:"-"`
}

func (m *TagOptsResp) Reset()         { *m = TagOptsResp{} }
func (m *TagOptsResp) String() string { return proto.CompactTextString(m) }
func (*TagOptsResp) ProtoMessage()    {}
func (*TagOptsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_debfb026132a9ef5, []int{9}
}

func (m *TagOptsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TagOptsResp.Unmarshal(m, b)
}
func (m *TagOptsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TagOptsResp.Marshal(b, m, deterministic)
}
func (m *TagOptsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TagOptsResp.Merge(m, src)
}
func (m *TagOptsResp) XXX_Size() int {
	return xxx_messageInfo_TagOptsResp.Size(m)
}
func (m *TagOptsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_TagOptsResp.DiscardUnknown(m)
}

var xxx_messageInfo_TagOptsResp proto.InternalMessageInfo

func (m *TagOptsResp) GetData() []*TagOptsResp_Tag {
	if m != nil {
		return m.Data
	}
	return nil
}

type TagOptsResp_Tag struct {
	// 标签ID
	TagId uint32 `protobuf:"varint,1,opt,name=tag_id,json=tagId,proto3" json:"tag_id"`
	// 标签名称
	TagName string `protobuf:"bytes,2,opt,name=tag_name,json=tagName,proto3" json:"tag_name"`
	// 级别
	Level uint32 `protobuf:"varint,3,opt,name=level,proto3" json:"level"`
	// 启用
	Enable  bool `protobuf:"varint,4,opt,name=enable,proto3" json:"enable"`
	Disable bool `protobuf:"varint,5,opt,name=disable,proto3" json:"disable"`
	// 子结构 @gotags: json:"children,omitempty"
	Children             []*TagOptsResp_Tag `protobuf:"bytes,6,rep,name=children,proto3" json:"children,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-" gorm:"-"`
	XXX_unrecognized     []byte             `json:"-" gorm:"-"`
	XXX_sizecache        int32              `json:"-" gorm:"-"`
}

func (m *TagOptsResp_Tag) Reset()         { *m = TagOptsResp_Tag{} }
func (m *TagOptsResp_Tag) String() string { return proto.CompactTextString(m) }
func (*TagOptsResp_Tag) ProtoMessage()    {}
func (*TagOptsResp_Tag) Descriptor() ([]byte, []int) {
	return fileDescriptor_debfb026132a9ef5, []int{9, 0}
}

func (m *TagOptsResp_Tag) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TagOptsResp_Tag.Unmarshal(m, b)
}
func (m *TagOptsResp_Tag) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TagOptsResp_Tag.Marshal(b, m, deterministic)
}
func (m *TagOptsResp_Tag) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TagOptsResp_Tag.Merge(m, src)
}
func (m *TagOptsResp_Tag) XXX_Size() int {
	return xxx_messageInfo_TagOptsResp_Tag.Size(m)
}
func (m *TagOptsResp_Tag) XXX_DiscardUnknown() {
	xxx_messageInfo_TagOptsResp_Tag.DiscardUnknown(m)
}

var xxx_messageInfo_TagOptsResp_Tag proto.InternalMessageInfo

func (m *TagOptsResp_Tag) GetTagId() uint32 {
	if m != nil {
		return m.TagId
	}
	return 0
}

func (m *TagOptsResp_Tag) GetTagName() string {
	if m != nil {
		return m.TagName
	}
	return ""
}

func (m *TagOptsResp_Tag) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *TagOptsResp_Tag) GetEnable() bool {
	if m != nil {
		return m.Enable
	}
	return false
}

func (m *TagOptsResp_Tag) GetDisable() bool {
	if m != nil {
		return m.Disable
	}
	return false
}

func (m *TagOptsResp_Tag) GetChildren() []*TagOptsResp_Tag {
	if m != nil {
		return m.Children
	}
	return nil
}

// GroupListReq 团队分单列表请求参数
type GroupListReq struct {
	// 团队名称
	GroupDesc string `protobuf:"bytes,2,opt,name=group_desc,json=groupDesc,proto3" json:"group_desc"`
	// user
	User string `protobuf:"bytes,3,opt,name=user,proto3" json:"user"`
	// 页码
	Page uint32 `protobuf:"varint,4,opt,name=page,proto3" json:"page"`
	// 页大小
	PageSize             uint32   `protobuf:"varint,5,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *GroupListReq) Reset()         { *m = GroupListReq{} }
func (m *GroupListReq) String() string { return proto.CompactTextString(m) }
func (*GroupListReq) ProtoMessage()    {}
func (*GroupListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_debfb026132a9ef5, []int{10}
}

func (m *GroupListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GroupListReq.Unmarshal(m, b)
}
func (m *GroupListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GroupListReq.Marshal(b, m, deterministic)
}
func (m *GroupListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GroupListReq.Merge(m, src)
}
func (m *GroupListReq) XXX_Size() int {
	return xxx_messageInfo_GroupListReq.Size(m)
}
func (m *GroupListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GroupListReq.DiscardUnknown(m)
}

var xxx_messageInfo_GroupListReq proto.InternalMessageInfo

func (m *GroupListReq) GetGroupDesc() string {
	if m != nil {
		return m.GroupDesc
	}
	return ""
}

func (m *GroupListReq) GetUser() string {
	if m != nil {
		return m.User
	}
	return ""
}

func (m *GroupListReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *GroupListReq) GetPageSize() uint32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

// GroupListResp 技能组列表】响应结果
type GroupListResp struct {
	// 当前页
	CurrentPage uint32 `protobuf:"varint,1,opt,name=current_page,json=currentPage,proto3" json:"current_page"`
	// 页大小
	PerPage uint32 `protobuf:"varint,2,opt,name=per_page,json=perPage,proto3" json:"per_page"`
	// 总数
	Total uint32 `protobuf:"varint,3,opt,name=total,proto3" json:"total"`
	// 数据列表
	Data                 []*GroupListResp_Group `protobuf:"bytes,4,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-" gorm:"-"`
	XXX_unrecognized     []byte                 `json:"-" gorm:"-"`
	XXX_sizecache        int32                  `json:"-" gorm:"-"`
}

func (m *GroupListResp) Reset()         { *m = GroupListResp{} }
func (m *GroupListResp) String() string { return proto.CompactTextString(m) }
func (*GroupListResp) ProtoMessage()    {}
func (*GroupListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_debfb026132a9ef5, []int{11}
}

func (m *GroupListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GroupListResp.Unmarshal(m, b)
}
func (m *GroupListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GroupListResp.Marshal(b, m, deterministic)
}
func (m *GroupListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GroupListResp.Merge(m, src)
}
func (m *GroupListResp) XXX_Size() int {
	return xxx_messageInfo_GroupListResp.Size(m)
}
func (m *GroupListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GroupListResp.DiscardUnknown(m)
}

var xxx_messageInfo_GroupListResp proto.InternalMessageInfo

func (m *GroupListResp) GetCurrentPage() uint32 {
	if m != nil {
		return m.CurrentPage
	}
	return 0
}

func (m *GroupListResp) GetPerPage() uint32 {
	if m != nil {
		return m.PerPage
	}
	return 0
}

func (m *GroupListResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *GroupListResp) GetData() []*GroupListResp_Group {
	if m != nil {
		return m.Data
	}
	return nil
}

type GroupListResp_Group struct {
	// 团队 user的id
	Id uint32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	// 团队id
	GroupId uint32 `protobuf:"varint,2,opt,name=group_id,json=groupId,proto3" json:"group_id"`
	// 团队名称
	GroupDesc string `protobuf:"bytes,3,opt,name=group_desc,json=groupDesc,proto3" json:"group_desc"`
	// 人员列表
	//        repeated string user = 4;
	User string `protobuf:"bytes,4,opt,name=user,proto3" json:"user"`
	// 游戏列表
	Game []string `protobuf:"bytes,5,rep,name=game,proto3" json:"game"`
	// 更新时间
	UpdatedAt string `protobuf:"bytes,6,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at"`
	// 更新用户
	Operator string `protobuf:"bytes,7,opt,name=operator,proto3" json:"operator"`
	// 状态
	Enable bool `protobuf:"varint,8,opt,name=enable,proto3" json:"enable"`
	// 语言
	Language []string `protobuf:"bytes,9,rep,name=language,proto3" json:"language"`
	// 技能组接单上限
	UpperLimit           uint32   `protobuf:"varint,10,opt,name=upper_limit,json=upperLimit,proto3" json:"upper_limit"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *GroupListResp_Group) Reset()         { *m = GroupListResp_Group{} }
func (m *GroupListResp_Group) String() string { return proto.CompactTextString(m) }
func (*GroupListResp_Group) ProtoMessage()    {}
func (*GroupListResp_Group) Descriptor() ([]byte, []int) {
	return fileDescriptor_debfb026132a9ef5, []int{11, 0}
}

func (m *GroupListResp_Group) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GroupListResp_Group.Unmarshal(m, b)
}
func (m *GroupListResp_Group) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GroupListResp_Group.Marshal(b, m, deterministic)
}
func (m *GroupListResp_Group) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GroupListResp_Group.Merge(m, src)
}
func (m *GroupListResp_Group) XXX_Size() int {
	return xxx_messageInfo_GroupListResp_Group.Size(m)
}
func (m *GroupListResp_Group) XXX_DiscardUnknown() {
	xxx_messageInfo_GroupListResp_Group.DiscardUnknown(m)
}

var xxx_messageInfo_GroupListResp_Group proto.InternalMessageInfo

func (m *GroupListResp_Group) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *GroupListResp_Group) GetGroupId() uint32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

func (m *GroupListResp_Group) GetGroupDesc() string {
	if m != nil {
		return m.GroupDesc
	}
	return ""
}

func (m *GroupListResp_Group) GetUser() string {
	if m != nil {
		return m.User
	}
	return ""
}

func (m *GroupListResp_Group) GetGame() []string {
	if m != nil {
		return m.Game
	}
	return nil
}

func (m *GroupListResp_Group) GetUpdatedAt() string {
	if m != nil {
		return m.UpdatedAt
	}
	return ""
}

func (m *GroupListResp_Group) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

func (m *GroupListResp_Group) GetEnable() bool {
	if m != nil {
		return m.Enable
	}
	return false
}

func (m *GroupListResp_Group) GetLanguage() []string {
	if m != nil {
		return m.Language
	}
	return nil
}

func (m *GroupListResp_Group) GetUpperLimit() uint32 {
	if m != nil {
		return m.UpperLimit
	}
	return 0
}

// GroupsIdReq 技能组ID 请求参数
type GroupIdReq struct {
	// 技能组ID @gotags: validate:"required"
	GroupId              uint32   `protobuf:"varint,1,opt,name=group_id,json=groupId,proto3" json:"group_id" validate:"required"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *GroupIdReq) Reset()         { *m = GroupIdReq{} }
func (m *GroupIdReq) String() string { return proto.CompactTextString(m) }
func (*GroupIdReq) ProtoMessage()    {}
func (*GroupIdReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_debfb026132a9ef5, []int{12}
}

func (m *GroupIdReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GroupIdReq.Unmarshal(m, b)
}
func (m *GroupIdReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GroupIdReq.Marshal(b, m, deterministic)
}
func (m *GroupIdReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GroupIdReq.Merge(m, src)
}
func (m *GroupIdReq) XXX_Size() int {
	return xxx_messageInfo_GroupIdReq.Size(m)
}
func (m *GroupIdReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GroupIdReq.DiscardUnknown(m)
}

var xxx_messageInfo_GroupIdReq proto.InternalMessageInfo

func (m *GroupIdReq) GetGroupId() uint32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

// GroupEditReq 技能组信息 响应结果
type GroupInfoResp struct {
	// 技能组ID
	GroupId uint32 `protobuf:"varint,1,opt,name=group_id,json=groupId,proto3" json:"group_id"`
	// 团队名称
	GroupDesc string `protobuf:"bytes,2,opt,name=group_desc,json=groupDesc,proto3" json:"group_desc"`
	// 人员列表
	User []string `protobuf:"bytes,3,rep,name=user,proto3" json:"user"`
	//    string user = 3;
	// 游戏列表
	Game []string `protobuf:"bytes,4,rep,name=game,proto3" json:"game"`
	//    string game = 4;
	// 语言
	Language []string `protobuf:"bytes,5,rep,name=language,proto3" json:"language"`
	// 问题分类
	Categories []string `protobuf:"bytes,6,rep,name=categories,proto3" json:"categories"`
	// 技能组接单上限
	UpperLimit           int32    `protobuf:"varint,7,opt,name=upper_limit,json=upperLimit,proto3" json:"upper_limit"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *GroupInfoResp) Reset()         { *m = GroupInfoResp{} }
func (m *GroupInfoResp) String() string { return proto.CompactTextString(m) }
func (*GroupInfoResp) ProtoMessage()    {}
func (*GroupInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_debfb026132a9ef5, []int{13}
}

func (m *GroupInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GroupInfoResp.Unmarshal(m, b)
}
func (m *GroupInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GroupInfoResp.Marshal(b, m, deterministic)
}
func (m *GroupInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GroupInfoResp.Merge(m, src)
}
func (m *GroupInfoResp) XXX_Size() int {
	return xxx_messageInfo_GroupInfoResp.Size(m)
}
func (m *GroupInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GroupInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_GroupInfoResp proto.InternalMessageInfo

func (m *GroupInfoResp) GetGroupId() uint32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

func (m *GroupInfoResp) GetGroupDesc() string {
	if m != nil {
		return m.GroupDesc
	}
	return ""
}

func (m *GroupInfoResp) GetUser() []string {
	if m != nil {
		return m.User
	}
	return nil
}

func (m *GroupInfoResp) GetGame() []string {
	if m != nil {
		return m.Game
	}
	return nil
}

func (m *GroupInfoResp) GetLanguage() []string {
	if m != nil {
		return m.Language
	}
	return nil
}

func (m *GroupInfoResp) GetCategories() []string {
	if m != nil {
		return m.Categories
	}
	return nil
}

func (m *GroupInfoResp) GetUpperLimit() int32 {
	if m != nil {
		return m.UpperLimit
	}
	return 0
}

// 客服在线状态请求参数
type GroupUserReq struct {
	Account              string   `protobuf:"bytes,1,opt,name=account,proto3" json:"account"`
	Status               uint32   `protobuf:"varint,2,opt,name=status,proto3" json:"status"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *GroupUserReq) Reset()         { *m = GroupUserReq{} }
func (m *GroupUserReq) String() string { return proto.CompactTextString(m) }
func (*GroupUserReq) ProtoMessage()    {}
func (*GroupUserReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_debfb026132a9ef5, []int{14}
}

func (m *GroupUserReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GroupUserReq.Unmarshal(m, b)
}
func (m *GroupUserReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GroupUserReq.Marshal(b, m, deterministic)
}
func (m *GroupUserReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GroupUserReq.Merge(m, src)
}
func (m *GroupUserReq) XXX_Size() int {
	return xxx_messageInfo_GroupUserReq.Size(m)
}
func (m *GroupUserReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GroupUserReq.DiscardUnknown(m)
}

var xxx_messageInfo_GroupUserReq proto.InternalMessageInfo

func (m *GroupUserReq) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *GroupUserReq) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

// 客服在线状态响应参数
type GroupUserResp struct {
	Status               string   `protobuf:"bytes,1,opt,name=status,proto3" json:"status"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *GroupUserResp) Reset()         { *m = GroupUserResp{} }
func (m *GroupUserResp) String() string { return proto.CompactTextString(m) }
func (*GroupUserResp) ProtoMessage()    {}
func (*GroupUserResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_debfb026132a9ef5, []int{15}
}

func (m *GroupUserResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GroupUserResp.Unmarshal(m, b)
}
func (m *GroupUserResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GroupUserResp.Marshal(b, m, deterministic)
}
func (m *GroupUserResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GroupUserResp.Merge(m, src)
}
func (m *GroupUserResp) XXX_Size() int {
	return xxx_messageInfo_GroupUserResp.Size(m)
}
func (m *GroupUserResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GroupUserResp.DiscardUnknown(m)
}

var xxx_messageInfo_GroupUserResp proto.InternalMessageInfo

func (m *GroupUserResp) GetStatus() string {
	if m != nil {
		return m.Status
	}
	return ""
}

// 删除分单定义
type GroupDelReq struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	User                 string   `protobuf:"bytes,2,opt,name=user,proto3" json:"user"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *GroupDelReq) Reset()         { *m = GroupDelReq{} }
func (m *GroupDelReq) String() string { return proto.CompactTextString(m) }
func (*GroupDelReq) ProtoMessage()    {}
func (*GroupDelReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_debfb026132a9ef5, []int{16}
}

func (m *GroupDelReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GroupDelReq.Unmarshal(m, b)
}
func (m *GroupDelReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GroupDelReq.Marshal(b, m, deterministic)
}
func (m *GroupDelReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GroupDelReq.Merge(m, src)
}
func (m *GroupDelReq) XXX_Size() int {
	return xxx_messageInfo_GroupDelReq.Size(m)
}
func (m *GroupDelReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GroupDelReq.DiscardUnknown(m)
}

var xxx_messageInfo_GroupDelReq proto.InternalMessageInfo

func (m *GroupDelReq) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *GroupDelReq) GetUser() string {
	if m != nil {
		return m.User
	}
	return ""
}

// 更新分单定义
type GroupSaveReq struct {
	GroupId              uint32   `protobuf:"varint,1,opt,name=group_id,json=groupId,proto3" json:"group_id"`
	GroupDesc            string   `protobuf:"bytes,2,opt,name=group_desc,json=groupDesc,proto3" json:"group_desc"`
	UpperLimit           int32    `protobuf:"varint,3,opt,name=upper_limit,json=upperLimit,proto3" json:"upper_limit"`
	Id                   int32    `protobuf:"varint,4,opt,name=id,proto3" json:"id"`
	Game                 []string `protobuf:"bytes,5,rep,name=game,proto3" json:"game"`
	Language             []string `protobuf:"bytes,6,rep,name=language,proto3" json:"language"`
	User                 []string `protobuf:"bytes,7,rep,name=user,proto3" json:"user"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *GroupSaveReq) Reset()         { *m = GroupSaveReq{} }
func (m *GroupSaveReq) String() string { return proto.CompactTextString(m) }
func (*GroupSaveReq) ProtoMessage()    {}
func (*GroupSaveReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_debfb026132a9ef5, []int{17}
}

func (m *GroupSaveReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GroupSaveReq.Unmarshal(m, b)
}
func (m *GroupSaveReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GroupSaveReq.Marshal(b, m, deterministic)
}
func (m *GroupSaveReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GroupSaveReq.Merge(m, src)
}
func (m *GroupSaveReq) XXX_Size() int {
	return xxx_messageInfo_GroupSaveReq.Size(m)
}
func (m *GroupSaveReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GroupSaveReq.DiscardUnknown(m)
}

var xxx_messageInfo_GroupSaveReq proto.InternalMessageInfo

func (m *GroupSaveReq) GetGroupId() uint32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

func (m *GroupSaveReq) GetGroupDesc() string {
	if m != nil {
		return m.GroupDesc
	}
	return ""
}

func (m *GroupSaveReq) GetUpperLimit() int32 {
	if m != nil {
		return m.UpperLimit
	}
	return 0
}

func (m *GroupSaveReq) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *GroupSaveReq) GetGame() []string {
	if m != nil {
		return m.Game
	}
	return nil
}

func (m *GroupSaveReq) GetLanguage() []string {
	if m != nil {
		return m.Language
	}
	return nil
}

func (m *GroupSaveReq) GetUser() []string {
	if m != nil {
		return m.User
	}
	return nil
}

// TagConfigReq 标签配置请求参数
type TagConfigAddReq struct {
	// 标签库ID @gotags: validate:"required"
	LibId uint32 `protobuf:"varint,1,opt,name=lib_id,json=libId,proto3" json:"lib_id" validate:"required"`
	// 级别 @gotags: validate:"required"
	Level uint32 `protobuf:"varint,2,opt,name=level,proto3" json:"level" validate:"required"`
	// 父级ID
	Pid uint32 `protobuf:"varint,3,opt,name=pid,proto3" json:"pid"`
	// 标签名称 @gotags: validate:"required"
	TagName              string   `protobuf:"bytes,4,opt,name=tag_name,json=tagName,proto3" json:"tag_name" validate:"required"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *TagConfigAddReq) Reset()         { *m = TagConfigAddReq{} }
func (m *TagConfigAddReq) String() string { return proto.CompactTextString(m) }
func (*TagConfigAddReq) ProtoMessage()    {}
func (*TagConfigAddReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_debfb026132a9ef5, []int{18}
}

func (m *TagConfigAddReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TagConfigAddReq.Unmarshal(m, b)
}
func (m *TagConfigAddReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TagConfigAddReq.Marshal(b, m, deterministic)
}
func (m *TagConfigAddReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TagConfigAddReq.Merge(m, src)
}
func (m *TagConfigAddReq) XXX_Size() int {
	return xxx_messageInfo_TagConfigAddReq.Size(m)
}
func (m *TagConfigAddReq) XXX_DiscardUnknown() {
	xxx_messageInfo_TagConfigAddReq.DiscardUnknown(m)
}

var xxx_messageInfo_TagConfigAddReq proto.InternalMessageInfo

func (m *TagConfigAddReq) GetLibId() uint32 {
	if m != nil {
		return m.LibId
	}
	return 0
}

func (m *TagConfigAddReq) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *TagConfigAddReq) GetPid() uint32 {
	if m != nil {
		return m.Pid
	}
	return 0
}

func (m *TagConfigAddReq) GetTagName() string {
	if m != nil {
		return m.TagName
	}
	return ""
}

// TagInfoReq 标签信息响应参数
type TagConfigInfoResp struct {
	// 标签ID
	TagId uint32 `protobuf:"varint,1,opt,name=tag_id,json=tagId,proto3" json:"tag_id"`
	// 标签名称
	TagName string `protobuf:"bytes,2,opt,name=tag_name,json=tagName,proto3" json:"tag_name"`
	// 级别
	Level                uint32   `protobuf:"varint,3,opt,name=level,proto3" json:"level"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *TagConfigInfoResp) Reset()         { *m = TagConfigInfoResp{} }
func (m *TagConfigInfoResp) String() string { return proto.CompactTextString(m) }
func (*TagConfigInfoResp) ProtoMessage()    {}
func (*TagConfigInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_debfb026132a9ef5, []int{19}
}

func (m *TagConfigInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TagConfigInfoResp.Unmarshal(m, b)
}
func (m *TagConfigInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TagConfigInfoResp.Marshal(b, m, deterministic)
}
func (m *TagConfigInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TagConfigInfoResp.Merge(m, src)
}
func (m *TagConfigInfoResp) XXX_Size() int {
	return xxx_messageInfo_TagConfigInfoResp.Size(m)
}
func (m *TagConfigInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_TagConfigInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_TagConfigInfoResp proto.InternalMessageInfo

func (m *TagConfigInfoResp) GetTagId() uint32 {
	if m != nil {
		return m.TagId
	}
	return 0
}

func (m *TagConfigInfoResp) GetTagName() string {
	if m != nil {
		return m.TagName
	}
	return ""
}

func (m *TagConfigInfoResp) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

// TagConfigEditReq 标签配置修改
type TagConfigEditReq struct {
	// @gotags: validate:"required"
	TagId uint32 `protobuf:"varint,1,opt,name=tag_id,json=tagId,proto3" json:"tag_id" validate:"required"`
	// @gotags: validate:"required"
	TagName              string   `protobuf:"bytes,2,opt,name=tag_name,json=tagName,proto3" json:"tag_name" validate:"required"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *TagConfigEditReq) Reset()         { *m = TagConfigEditReq{} }
func (m *TagConfigEditReq) String() string { return proto.CompactTextString(m) }
func (*TagConfigEditReq) ProtoMessage()    {}
func (*TagConfigEditReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_debfb026132a9ef5, []int{20}
}

func (m *TagConfigEditReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TagConfigEditReq.Unmarshal(m, b)
}
func (m *TagConfigEditReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TagConfigEditReq.Marshal(b, m, deterministic)
}
func (m *TagConfigEditReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TagConfigEditReq.Merge(m, src)
}
func (m *TagConfigEditReq) XXX_Size() int {
	return xxx_messageInfo_TagConfigEditReq.Size(m)
}
func (m *TagConfigEditReq) XXX_DiscardUnknown() {
	xxx_messageInfo_TagConfigEditReq.DiscardUnknown(m)
}

var xxx_messageInfo_TagConfigEditReq proto.InternalMessageInfo

func (m *TagConfigEditReq) GetTagId() uint32 {
	if m != nil {
		return m.TagId
	}
	return 0
}

func (m *TagConfigEditReq) GetTagName() string {
	if m != nil {
		return m.TagName
	}
	return ""
}

func init() {
	proto.RegisterType((*TagLibOptsResp)(nil), "pb.TagLibOptsResp")
	proto.RegisterType((*TagLibOptsResp_TagLib)(nil), "pb.TagLibOptsResp.TagLib")
	proto.RegisterType((*TagLibListReq)(nil), "pb.TagLibListReq")
	proto.RegisterType((*TagLibID)(nil), "pb.TagLibID")
	proto.RegisterType((*TagLibListResp)(nil), "pb.TagLibListResp")
	proto.RegisterType((*TagLibListResp_TagLib)(nil), "pb.TagLibListResp.TagLib")
	proto.RegisterType((*TagLibInfoResp)(nil), "pb.TagLibInfoResp")
	proto.RegisterType((*TagLibAddReq)(nil), "pb.TagLibAddReq")
	proto.RegisterType((*TagLibSaveReq)(nil), "pb.TagLibSaveReq")
	proto.RegisterType((*AllTagLibSaveReq)(nil), "pb.AllTagLibSaveReq")
	proto.RegisterType((*TagOptsReq)(nil), "pb.TagOptsReq")
	proto.RegisterType((*TagOptsResp)(nil), "pb.TagOptsResp")
	proto.RegisterType((*TagOptsResp_Tag)(nil), "pb.TagOptsResp.Tag")
	proto.RegisterType((*GroupListReq)(nil), "pb.GroupListReq")
	proto.RegisterType((*GroupListResp)(nil), "pb.GroupListResp")
	proto.RegisterType((*GroupListResp_Group)(nil), "pb.GroupListResp.Group")
	proto.RegisterType((*GroupIdReq)(nil), "pb.GroupIdReq")
	proto.RegisterType((*GroupInfoResp)(nil), "pb.GroupInfoResp")
	proto.RegisterType((*GroupUserReq)(nil), "pb.GroupUserReq")
	proto.RegisterType((*GroupUserResp)(nil), "pb.GroupUserResp")
	proto.RegisterType((*GroupDelReq)(nil), "pb.GroupDelReq")
	proto.RegisterType((*GroupSaveReq)(nil), "pb.GroupSaveReq")
	proto.RegisterType((*TagConfigAddReq)(nil), "pb.TagConfigAddReq")
	proto.RegisterType((*TagConfigInfoResp)(nil), "pb.TagConfigInfoResp")
	proto.RegisterType((*TagConfigEditReq)(nil), "pb.TagConfigEditReq")
}

func init() {
	proto.RegisterFile("label_group.proto", fileDescriptor_debfb026132a9ef5)
}

var fileDescriptor_debfb026132a9ef5 = []byte{
	// 957 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xc4, 0x56, 0xcd, 0x6e, 0xe4, 0x44,
	0x10, 0x96, 0x7f, 0xc7, 0xae, 0xc9, 0xec, 0x66, 0x9b, 0x05, 0x9c, 0xa0, 0x85, 0x59, 0x1f, 0xc8,
	0x48, 0x88, 0x20, 0xe0, 0x06, 0x17, 0x02, 0x59, 0xa2, 0x48, 0x11, 0x20, 0xef, 0xe4, 0x02, 0x07,
	0xab, 0x3d, 0xee, 0x98, 0x86, 0x8e, 0xdd, 0x6b, 0xb7, 0x57, 0xda, 0xe5, 0xc0, 0x11, 0xf1, 0x0a,
	0x08, 0xde, 0x81, 0x17, 0xe0, 0xc8, 0x95, 0xf7, 0xe0, 0x2d, 0x50, 0xff, 0xd8, 0xb1, 0xbd, 0x3b,
	0x81, 0x8d, 0x10, 0x9c, 0xa6, 0xab, 0xba, 0xa6, 0xab, 0xbe, 0xaf, 0xaa, 0xbe, 0x19, 0xb8, 0xc3,
	0x70, 0x46, 0x58, 0x5a, 0xd4, 0x55, 0xcb, 0x0f, 0x79, 0x5d, 0x89, 0x0a, 0xd9, 0x3c, 0x8b, 0xbf,
	0x83, 0x5b, 0x6b, 0x5c, 0x9c, 0xd1, 0xec, 0x73, 0x2e, 0x9a, 0x84, 0x34, 0x1c, 0xbd, 0x0d, 0x6e,
	0x8e, 0x05, 0x8e, 0xac, 0xa5, 0xb3, 0x9a, 0xbf, 0xb7, 0x77, 0xc8, 0xb3, 0xc3, 0x71, 0x84, 0x31,
	0x13, 0x15, 0xb6, 0xff, 0x01, 0xf8, 0xda, 0x46, 0x2f, 0x83, 0xcf, 0x68, 0x96, 0xd2, 0x3c, 0xb2,
	0x96, 0xd6, 0x6a, 0x91, 0x78, 0x8c, 0x66, 0xa7, 0x39, 0xda, 0x83, 0x40, 0xba, 0x4b, 0x7c, 0x49,
	0x22, 0x7b, 0x69, 0xad, 0xc2, 0x64, 0xc6, 0x68, 0xf6, 0x19, 0xbe, 0x24, 0xf1, 0x03, 0x58, 0xe8,
	0xef, 0x9e, 0xd1, 0x46, 0x24, 0xe4, 0xd1, 0x28, 0xd6, 0x1a, 0xc5, 0x76, 0x57, 0xe2, 0x09, 0x27,
	0x91, 0xa7, 0xde, 0x97, 0x57, 0xeb, 0x27, 0x9c, 0xc4, 0xf7, 0x21, 0xd0, 0xcf, 0x9c, 0x1e, 0x6f,
	0x29, 0x22, 0xfe, 0xc1, 0xee, 0x70, 0xea, 0x54, 0xd7, 0xe1, 0xec, 0x22, 0xc6, 0x38, 0x7f, 0xb7,
	0x6e, 0x0e, 0x14, 0xbd, 0x09, 0xb7, 0x05, 0x2e, 0xd2, 0x96, 0xb3, 0x0a, 0xe7, 0xe9, 0x05, 0x65,
	0x24, 0x72, 0x54, 0xc4, 0x42, 0xe0, 0xe2, 0x5c, 0x79, 0x3f, 0xa5, 0x8c, 0xa0, 0x7b, 0x00, 0x2d,
	0xcf, 0xb1, 0x20, 0x79, 0x8a, 0x85, 0x82, 0x19, 0x26, 0xa1, 0xf1, 0x1c, 0x09, 0x74, 0x0b, 0xec,
	0x8a, 0x47, 0xbe, 0x72, 0xdb, 0x15, 0x47, 0xaf, 0x80, 0x4f, 0x4a, 0x9c, 0x31, 0x12, 0xcd, 0x96,
	0xd6, 0x2a, 0x48, 0x8c, 0x85, 0xf6, 0x21, 0xe0, 0x75, 0xf5, 0x0d, 0xd9, 0x88, 0x26, 0x0a, 0x97,
	0xce, 0x2a, 0x4c, 0x7a, 0x3b, 0xfe, 0xc9, 0xea, 0x98, 0x38, 0x2d, 0x2f, 0x2a, 0xc5, 0xc4, 0x8b,
	0xe3, 0x59, 0xc2, 0x8e, 0xbc, 0x92, 0x40, 0xd2, 0xb6, 0x66, 0x06, 0x0c, 0x30, 0x9a, 0x49, 0x18,
	0xe7, 0x35, 0x1b, 0x95, 0xe0, 0x8e, 0x4b, 0x40, 0x11, 0xcc, 0xcc, 0x39, 0xf2, 0xd4, 0x55, 0x67,
	0xc6, 0x05, 0xec, 0xe8, 0xda, 0x8e, 0xf2, 0xfc, 0x6f, 0xe6, 0x61, 0x5a, 0x82, 0x7d, 0x6d, 0x09,
	0xce, 0x84, 0x85, 0xef, 0xbb, 0xc9, 0x7b, 0x88, 0x1f, 0x13, 0x99, 0xe9, 0x3f, 0xe6, 0x20, 0xfe,
	0xc5, 0x82, 0xdd, 0x23, 0xc6, 0xfe, 0xd7, 0x22, 0xae, 0xdb, 0x29, 0x0c, 0xb0, 0xc6, 0x85, 0x5e,
	0xf9, 0x47, 0xe8, 0x3e, 0xec, 0x98, 0x2f, 0x0d, 0x7b, 0x31, 0x37, 0x3e, 0x55, 0xc9, 0x55, 0xed,
	0xf6, 0x73, 0x6a, 0x7f, 0x5e, 0x8a, 0x3f, 0x2d, 0x98, 0xf7, 0x39, 0x1a, 0x8e, 0x0e, 0x46, 0x0b,
	0xf9, 0x92, 0x59, 0xc8, 0xa1, 0xea, 0x98, 0x55, 0xfc, 0xd5, 0x02, 0x67, 0x8d, 0x0b, 0x99, 0x52,
	0x6e, 0xd5, 0x15, 0x5d, 0x02, 0x17, 0x3a, 0xa5, 0x74, 0x0f, 0xe9, 0x12, 0xb8, 0x50, 0x45, 0xde,
	0x05, 0x8f, 0x91, 0xc7, 0x44, 0xf3, 0x24, 0x6b, 0x94, 0xc6, 0x60, 0x8d, 0xdc, 0xd1, 0x1a, 0x45,
	0x30, 0xcb, 0x69, 0xa3, 0x2e, 0x3c, 0x75, 0xd1, 0x99, 0xe8, 0x1d, 0x08, 0x36, 0x5f, 0x53, 0x96,
	0xd7, 0xa4, 0x8c, 0xfc, 0xed, 0xe5, 0xf6, 0x41, 0x71, 0x0d, 0x3b, 0x27, 0x52, 0x79, 0x3b, 0xa1,
	0xbb, 0x07, 0xa0, 0x94, 0x38, 0xcd, 0x49, 0xb3, 0x31, 0x55, 0x86, 0xca, 0x73, 0x4c, 0x9a, 0x0d,
	0x42, 0xe0, 0xb6, 0x0d, 0xa9, 0x4d, 0x3b, 0xd5, 0x59, 0xfa, 0x38, 0x2e, 0x74, 0x8d, 0x8b, 0x44,
	0x9d, 0xd1, 0x6b, 0x10, 0xca, 0xcf, 0xb4, 0xa1, 0x4f, 0x3b, 0x7a, 0x03, 0xe9, 0x78, 0x48, 0x9f,
	0x92, 0xf8, 0x67, 0x07, 0x16, 0x83, 0xa4, 0x0d, 0x97, 0x6d, 0xdc, 0xb4, 0x75, 0x4d, 0x4a, 0x91,
	0xaa, 0xa7, 0x34, 0x6d, 0x73, 0xe3, 0xfb, 0x42, 0xbe, 0xb8, 0x07, 0x01, 0x27, 0xb5, 0xbe, 0xd6,
	0x8d, 0x9c, 0x71, 0x52, 0xab, 0xab, 0xbb, 0xe0, 0x89, 0x4a, 0xe0, 0x9e, 0x3c, 0x65, 0xa0, 0xb7,
	0x4c, 0xd7, 0x5c, 0x45, 0xc3, 0xab, 0x92, 0x86, 0x51, 0x52, 0x6d, 0x99, 0xce, 0xfd, 0x68, 0x83,
	0xa7, 0x6c, 0x29, 0x65, 0x7d, 0xdf, 0x6c, 0xaa, 0x9a, 0xa6, 0x09, 0xe9, 0x07, 0x68, 0xa6, 0xec,
	0xd3, 0x7c, 0xc2, 0x95, 0xb3, 0x8d, 0x2b, 0x77, 0xcc, 0x55, 0x21, 0xdb, 0xaf, 0xe5, 0x45, 0x9d,
	0x27, 0xda, 0xea, 0x4f, 0xb5, 0x75, 0x1f, 0x82, 0x8a, 0x93, 0x1a, 0x8b, 0xaa, 0x56, 0x6a, 0x1a,
	0x26, 0xbd, 0x3d, 0x18, 0x90, 0x60, 0xaa, 0xb3, 0x0c, 0x97, 0x45, 0x2b, 0xc9, 0x32, 0x3a, 0xdb,
	0xd9, 0xe8, 0x0d, 0x98, 0xb7, 0x5c, 0x52, 0xc9, 0xe8, 0x25, 0x15, 0x11, 0x28, 0x4c, 0xa0, 0x5c,
	0x67, 0xd2, 0x13, 0x1f, 0x00, 0x9c, 0x68, 0x84, 0x46, 0xe9, 0x7a, 0xfc, 0xd6, 0x08, 0x7f, 0xfc,
	0x87, 0x65, 0xfa, 0xd8, 0x0b, 0xf6, 0xf6, 0xe0, 0x7f, 0x3e, 0x58, 0xce, 0x33, 0x64, 0xb9, 0x03,
	0xb2, 0x86, 0xc8, 0xbc, 0x09, 0xb2, 0xd7, 0x01, 0x36, 0x58, 0x90, 0xa2, 0xaa, 0x29, 0x69, 0xd4,
	0xf8, 0x87, 0xc9, 0xc0, 0x33, 0x45, 0x2e, 0xc9, 0xf4, 0x46, 0xc8, 0x3f, 0x32, 0xcb, 0x70, 0xde,
	0x90, 0x5a, 0x62, 0x8f, 0x60, 0x86, 0x37, 0x9b, 0xaa, 0x2d, 0x45, 0x27, 0xf2, 0xc6, 0x94, 0xc4,
	0x37, 0x02, 0x8b, 0xb6, 0x31, 0x33, 0x61, 0xac, 0xf8, 0xc0, 0x30, 0xa2, 0x5f, 0x68, 0xf8, 0x20,
	0x50, 0xbf, 0xd0, 0x05, 0xbe, 0x0b, 0xf3, 0x13, 0x0d, 0x9e, 0xc9, 0x4c, 0xd3, 0xa9, 0xeb, 0xe8,
	0xb0, 0xaf, 0x66, 0x27, 0xfe, 0xcd, 0x32, 0xe5, 0x75, 0xaa, 0x7c, 0x73, 0xb6, 0x27, 0x4c, 0x38,
	0x53, 0x26, 0x4c, 0x3d, 0xae, 0xf2, 0x9b, 0x7a, 0x9e, 0x99, 0xdb, 0x61, 0x2b, 0xfc, 0x49, 0x2b,
	0xba, 0xfa, 0x67, 0x57, 0xed, 0x8c, 0xbf, 0x85, 0xdb, 0x6b, 0x5c, 0x7c, 0x52, 0x95, 0x17, 0xb4,
	0x30, 0x3f, 0xa3, 0x5b, 0x7e, 0x57, 0x7a, 0x35, 0xb4, 0x87, 0x6a, 0xb8, 0x0b, 0x0e, 0xa7, 0xb9,
	0x59, 0x72, 0x79, 0x1c, 0x09, 0xaa, 0x3b, 0x12, 0xd4, 0xf8, 0x2b, 0xb8, 0xd3, 0x27, 0x1b, 0xfe,
	0x9f, 0xf8, 0x37, 0x74, 0x39, 0x3e, 0x86, 0xdd, 0xfe, 0xf1, 0x07, 0x39, 0x15, 0x06, 0xca, 0x8b,
	0xbd, 0xfd, 0xb1, 0xff, 0xa5, 0x7b, 0xf8, 0x21, 0xcf, 0x32, 0x5f, 0xfd, 0xe9, 0x7d, 0xff, 0xaf,
	0x00, 0x00, 0x00, 0xff, 0xff, 0xe7, 0xe6, 0x00, 0x07, 0x09, 0x0b, 0x00, 0x00,
}
