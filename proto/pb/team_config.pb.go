// Code generated by protoc-gen-go. DO NOT EDIT.
// source: team_config.proto

package pb

import (
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

// TeamConfigAddReq 新增团队配置
type TeamConfigAddReq struct {
	// 团队名称 @gotags: validate:"required"
	TeamName string `protobuf:"bytes,1,opt,name=team_name,json=teamName,proto3" json:"team_name" validate:"required"`
	// 团队成员 @gotags: validate:"required"
	TeamMember           string   `protobuf:"bytes,2,opt,name=team_member,json=teamMember,proto3" json:"team_member" validate:"required"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *TeamConfigAddReq) Reset()         { *m = TeamConfigAddReq{} }
func (m *TeamConfigAddReq) String() string { return proto.CompactTextString(m) }
func (*TeamConfigAddReq) ProtoMessage()    {}
func (*TeamConfigAddReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_98ba588592b2e483, []int{0}
}

func (m *TeamConfigAddReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TeamConfigAddReq.Unmarshal(m, b)
}
func (m *TeamConfigAddReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TeamConfigAddReq.Marshal(b, m, deterministic)
}
func (m *TeamConfigAddReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TeamConfigAddReq.Merge(m, src)
}
func (m *TeamConfigAddReq) XXX_Size() int {
	return xxx_messageInfo_TeamConfigAddReq.Size(m)
}
func (m *TeamConfigAddReq) XXX_DiscardUnknown() {
	xxx_messageInfo_TeamConfigAddReq.DiscardUnknown(m)
}

var xxx_messageInfo_TeamConfigAddReq proto.InternalMessageInfo

func (m *TeamConfigAddReq) GetTeamName() string {
	if m != nil {
		return m.TeamName
	}
	return ""
}

func (m *TeamConfigAddReq) GetTeamMember() string {
	if m != nil {
		return m.TeamMember
	}
	return ""
}

// TeamConfigEditReq 编辑修改团队配置信息
type TeamConfigEditReq struct {
	// 团队Id @gotags: validate:"required"
	TeamId uint32 `protobuf:"varint,1,opt,name=team_id,json=teamId,proto3" json:"team_id" validate:"required"`
	// 团队名称
	TeamName string `protobuf:"bytes,2,opt,name=team_name,json=teamName,proto3" json:"team_name"`
	// 团队成员
	TeamMember           string   `protobuf:"bytes,3,opt,name=team_member,json=teamMember,proto3" json:"team_member"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *TeamConfigEditReq) Reset()         { *m = TeamConfigEditReq{} }
func (m *TeamConfigEditReq) String() string { return proto.CompactTextString(m) }
func (*TeamConfigEditReq) ProtoMessage()    {}
func (*TeamConfigEditReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_98ba588592b2e483, []int{1}
}

func (m *TeamConfigEditReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TeamConfigEditReq.Unmarshal(m, b)
}
func (m *TeamConfigEditReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TeamConfigEditReq.Marshal(b, m, deterministic)
}
func (m *TeamConfigEditReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TeamConfigEditReq.Merge(m, src)
}
func (m *TeamConfigEditReq) XXX_Size() int {
	return xxx_messageInfo_TeamConfigEditReq.Size(m)
}
func (m *TeamConfigEditReq) XXX_DiscardUnknown() {
	xxx_messageInfo_TeamConfigEditReq.DiscardUnknown(m)
}

var xxx_messageInfo_TeamConfigEditReq proto.InternalMessageInfo

func (m *TeamConfigEditReq) GetTeamId() uint32 {
	if m != nil {
		return m.TeamId
	}
	return 0
}

func (m *TeamConfigEditReq) GetTeamName() string {
	if m != nil {
		return m.TeamName
	}
	return ""
}

func (m *TeamConfigEditReq) GetTeamMember() string {
	if m != nil {
		return m.TeamMember
	}
	return ""
}

type TeamConfigDelReq struct {
	// 团队Id @gotags: validate:"required"
	TeamId               uint32   `protobuf:"varint,1,opt,name=team_id,json=teamId,proto3" json:"team_id" validate:"required"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *TeamConfigDelReq) Reset()         { *m = TeamConfigDelReq{} }
func (m *TeamConfigDelReq) String() string { return proto.CompactTextString(m) }
func (*TeamConfigDelReq) ProtoMessage()    {}
func (*TeamConfigDelReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_98ba588592b2e483, []int{2}
}

func (m *TeamConfigDelReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TeamConfigDelReq.Unmarshal(m, b)
}
func (m *TeamConfigDelReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TeamConfigDelReq.Marshal(b, m, deterministic)
}
func (m *TeamConfigDelReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TeamConfigDelReq.Merge(m, src)
}
func (m *TeamConfigDelReq) XXX_Size() int {
	return xxx_messageInfo_TeamConfigDelReq.Size(m)
}
func (m *TeamConfigDelReq) XXX_DiscardUnknown() {
	xxx_messageInfo_TeamConfigDelReq.DiscardUnknown(m)
}

var xxx_messageInfo_TeamConfigDelReq proto.InternalMessageInfo

func (m *TeamConfigDelReq) GetTeamId() uint32 {
	if m != nil {
		return m.TeamId
	}
	return 0
}

type TeamConfigListReq struct {
	TeamName             string   `protobuf:"bytes,1,opt,name=team_name,json=teamName,proto3" json:"team_name"`
	TeamMember           string   `protobuf:"bytes,2,opt,name=team_member,json=teamMember,proto3" json:"team_member"`
	Page                 uint32   `protobuf:"varint,3,opt,name=page,proto3" json:"page"`
	PageSize             uint32   `protobuf:"varint,4,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	IsAll                bool     `protobuf:"varint,5,opt,name=isAll,proto3" json:"isAll"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *TeamConfigListReq) Reset()         { *m = TeamConfigListReq{} }
func (m *TeamConfigListReq) String() string { return proto.CompactTextString(m) }
func (*TeamConfigListReq) ProtoMessage()    {}
func (*TeamConfigListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_98ba588592b2e483, []int{3}
}

func (m *TeamConfigListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TeamConfigListReq.Unmarshal(m, b)
}
func (m *TeamConfigListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TeamConfigListReq.Marshal(b, m, deterministic)
}
func (m *TeamConfigListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TeamConfigListReq.Merge(m, src)
}
func (m *TeamConfigListReq) XXX_Size() int {
	return xxx_messageInfo_TeamConfigListReq.Size(m)
}
func (m *TeamConfigListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_TeamConfigListReq.DiscardUnknown(m)
}

var xxx_messageInfo_TeamConfigListReq proto.InternalMessageInfo

func (m *TeamConfigListReq) GetTeamName() string {
	if m != nil {
		return m.TeamName
	}
	return ""
}

func (m *TeamConfigListReq) GetTeamMember() string {
	if m != nil {
		return m.TeamMember
	}
	return ""
}

func (m *TeamConfigListReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *TeamConfigListReq) GetPageSize() uint32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *TeamConfigListReq) GetIsAll() bool {
	if m != nil {
		return m.IsAll
	}
	return false
}

type TeamConfigListResp struct {
	CurrentPage          uint32                       `protobuf:"varint,1,opt,name=current_page,json=currentPage,proto3" json:"current_page"`
	PerPage              uint32                       `protobuf:"varint,2,opt,name=per_page,json=perPage,proto3" json:"per_page"`
	Total                uint32                       `protobuf:"varint,3,opt,name=total,proto3" json:"total"`
	Data                 []*TeamConfigListResp_Detail `protobuf:"bytes,4,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}                     `json:"-" gorm:"-"`
	XXX_unrecognized     []byte                       `json:"-" gorm:"-"`
	XXX_sizecache        int32                        `json:"-" gorm:"-"`
}

func (m *TeamConfigListResp) Reset()         { *m = TeamConfigListResp{} }
func (m *TeamConfigListResp) String() string { return proto.CompactTextString(m) }
func (*TeamConfigListResp) ProtoMessage()    {}
func (*TeamConfigListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_98ba588592b2e483, []int{4}
}

func (m *TeamConfigListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TeamConfigListResp.Unmarshal(m, b)
}
func (m *TeamConfigListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TeamConfigListResp.Marshal(b, m, deterministic)
}
func (m *TeamConfigListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TeamConfigListResp.Merge(m, src)
}
func (m *TeamConfigListResp) XXX_Size() int {
	return xxx_messageInfo_TeamConfigListResp.Size(m)
}
func (m *TeamConfigListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_TeamConfigListResp.DiscardUnknown(m)
}

var xxx_messageInfo_TeamConfigListResp proto.InternalMessageInfo

func (m *TeamConfigListResp) GetCurrentPage() uint32 {
	if m != nil {
		return m.CurrentPage
	}
	return 0
}

func (m *TeamConfigListResp) GetPerPage() uint32 {
	if m != nil {
		return m.PerPage
	}
	return 0
}

func (m *TeamConfigListResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *TeamConfigListResp) GetData() []*TeamConfigListResp_Detail {
	if m != nil {
		return m.Data
	}
	return nil
}

type TeamConfigListResp_Detail struct {
	TeamId               int64    `protobuf:"varint,1,opt,name=team_id,json=teamId,proto3" json:"team_id"`
	TeamName             string   `protobuf:"bytes,2,opt,name=team_name,json=teamName,proto3" json:"team_name"`
	TeamMember           string   `protobuf:"bytes,3,opt,name=team_member,json=teamMember,proto3" json:"team_member"`
	UpdateTime           string   `protobuf:"bytes,4,opt,name=update_time,json=updateTime,proto3" json:"update_time"`
	Updater              string   `protobuf:"bytes,5,opt,name=updater,proto3" json:"updater"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *TeamConfigListResp_Detail) Reset()         { *m = TeamConfigListResp_Detail{} }
func (m *TeamConfigListResp_Detail) String() string { return proto.CompactTextString(m) }
func (*TeamConfigListResp_Detail) ProtoMessage()    {}
func (*TeamConfigListResp_Detail) Descriptor() ([]byte, []int) {
	return fileDescriptor_98ba588592b2e483, []int{4, 0}
}

func (m *TeamConfigListResp_Detail) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TeamConfigListResp_Detail.Unmarshal(m, b)
}
func (m *TeamConfigListResp_Detail) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TeamConfigListResp_Detail.Marshal(b, m, deterministic)
}
func (m *TeamConfigListResp_Detail) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TeamConfigListResp_Detail.Merge(m, src)
}
func (m *TeamConfigListResp_Detail) XXX_Size() int {
	return xxx_messageInfo_TeamConfigListResp_Detail.Size(m)
}
func (m *TeamConfigListResp_Detail) XXX_DiscardUnknown() {
	xxx_messageInfo_TeamConfigListResp_Detail.DiscardUnknown(m)
}

var xxx_messageInfo_TeamConfigListResp_Detail proto.InternalMessageInfo

func (m *TeamConfigListResp_Detail) GetTeamId() int64 {
	if m != nil {
		return m.TeamId
	}
	return 0
}

func (m *TeamConfigListResp_Detail) GetTeamName() string {
	if m != nil {
		return m.TeamName
	}
	return ""
}

func (m *TeamConfigListResp_Detail) GetTeamMember() string {
	if m != nil {
		return m.TeamMember
	}
	return ""
}

func (m *TeamConfigListResp_Detail) GetUpdateTime() string {
	if m != nil {
		return m.UpdateTime
	}
	return ""
}

func (m *TeamConfigListResp_Detail) GetUpdater() string {
	if m != nil {
		return m.Updater
	}
	return ""
}

func init() {
	proto.RegisterType((*TeamConfigAddReq)(nil), "pb.TeamConfigAddReq")
	proto.RegisterType((*TeamConfigEditReq)(nil), "pb.TeamConfigEditReq")
	proto.RegisterType((*TeamConfigDelReq)(nil), "pb.TeamConfigDelReq")
	proto.RegisterType((*TeamConfigListReq)(nil), "pb.TeamConfigListReq")
	proto.RegisterType((*TeamConfigListResp)(nil), "pb.TeamConfigListResp")
	proto.RegisterType((*TeamConfigListResp_Detail)(nil), "pb.TeamConfigListResp.Detail")
}

func init() {
	proto.RegisterFile("team_config.proto", fileDescriptor_98ba588592b2e483)
}

var fileDescriptor_98ba588592b2e483 = []byte{
	// 358 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xac, 0x52, 0xc1, 0x6a, 0xdb, 0x40,
	0x14, 0x44, 0xb2, 0x2c, 0x4b, 0xcf, 0x35, 0xd4, 0x8b, 0xa1, 0x6a, 0x4b, 0xa9, 0xab, 0x93, 0xa1,
	0x20, 0x68, 0x7b, 0xec, 0xc9, 0xad, 0x7b, 0x28, 0x24, 0xc1, 0x6c, 0x7c, 0xca, 0x45, 0xac, 0xac,
	0x17, 0xb3, 0x41, 0x6b, 0x6d, 0xa4, 0xf5, 0xc5, 0x5f, 0x12, 0xf2, 0x25, 0xf9, 0xbc, 0xb0, 0x6f,
	0x1d, 0xe2, 0xd8, 0xe0, 0x4b, 0x72, 0x92, 0x66, 0xe6, 0x69, 0x66, 0xf6, 0x69, 0x61, 0x68, 0x50,
	0xa8, 0x7c, 0x59, 0xaf, 0xaf, 0xe5, 0x2a, 0xd3, 0x4d, 0x6d, 0x6a, 0xe6, 0xeb, 0x22, 0x9d, 0xc3,
	0xfb, 0x05, 0x0a, 0xf5, 0x97, 0xf8, 0x69, 0x59, 0x72, 0xbc, 0x65, 0x9f, 0x21, 0xa6, 0xe1, 0xb5,
	0x50, 0x98, 0x78, 0x63, 0x6f, 0x12, 0xf3, 0xc8, 0x12, 0x17, 0x42, 0x21, 0xfb, 0x0a, 0x7d, 0x12,
	0x15, 0xaa, 0x02, 0x9b, 0xc4, 0x27, 0x19, 0x2c, 0x75, 0x4e, 0x4c, 0x7a, 0x03, 0xc3, 0x67, 0xc7,
	0x7f, 0xa5, 0x34, 0xd6, 0xf2, 0x03, 0xf4, 0xe8, 0x2b, 0x59, 0x92, 0xe1, 0x80, 0x87, 0x16, 0xfe,
	0x2f, 0x5f, 0x66, 0xf9, 0xa7, 0xb3, 0x3a, 0x47, 0x59, 0xdf, 0xf7, 0xdb, 0xcf, 0xb0, 0x3a, 0x15,
	0x95, 0xde, 0x79, 0xfb, 0xcd, 0xce, 0x64, 0x6b, 0x5e, 0x7d, 0x58, 0xc6, 0x20, 0xd0, 0x62, 0x85,
	0x54, 0x6d, 0xc0, 0xe9, 0xdd, 0x3a, 0xda, 0x67, 0xde, 0xca, 0x2d, 0x26, 0x01, 0x09, 0x91, 0x25,
	0x2e, 0xe5, 0x16, 0xd9, 0x08, 0xba, 0xb2, 0x9d, 0x56, 0x55, 0xd2, 0x1d, 0x7b, 0x93, 0x88, 0x3b,
	0x90, 0x3e, 0xf8, 0xc0, 0x0e, 0xab, 0xb5, 0x9a, 0x7d, 0x83, 0x77, 0xcb, 0x4d, 0xd3, 0xe0, 0xda,
	0xe4, 0x94, 0xe2, 0xce, 0xd3, 0xdf, 0x71, 0x73, 0x1b, 0xf6, 0x11, 0x22, 0x8d, 0x8d, 0x93, 0x7d,
	0x92, 0x7b, 0x1a, 0x1b, 0x92, 0x46, 0xd0, 0x35, 0xb5, 0x11, 0xd5, 0xae, 0x9c, 0x03, 0xec, 0x07,
	0x04, 0xa5, 0x30, 0x22, 0x09, 0xc6, 0x9d, 0x49, 0xff, 0xe7, 0x97, 0x4c, 0x17, 0xd9, 0x71, 0x72,
	0x36, 0x43, 0x23, 0x64, 0xc5, 0x69, 0xf4, 0xd3, 0xbd, 0x07, 0xa1, 0x23, 0x0e, 0x97, 0xdb, 0x79,
	0x9b, 0xff, 0x68, 0x07, 0x36, 0xba, 0x14, 0x06, 0x73, 0x23, 0x95, 0x5b, 0x5a, 0xcc, 0xc1, 0x51,
	0x0b, 0xa9, 0x90, 0x25, 0xd0, 0x73, 0xa8, 0xa1, 0xc5, 0xc5, 0xfc, 0x09, 0xfe, 0x09, 0xaf, 0x82,
	0xec, 0xb7, 0x2e, 0x8a, 0x90, 0xee, 0xf4, 0xaf, 0xc7, 0x00, 0x00, 0x00, 0xff, 0xff, 0xfe, 0x97,
	0x55, 0xa7, 0xe8, 0x02, 0x00, 0x00,
}
