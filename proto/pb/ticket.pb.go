// Code generated by protoc-gen-go. DO NOT EDIT.
// source: ticket.proto

package pb

import (
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

// WorkbenchPart 我的工单
type WorkbenchPart int32

const (
	WorkbenchPart_AllPart WorkbenchPart = 0
	// 1:待处理工单部分
	WorkbenchPart_PendingPart WorkbenchPart = 1
	// 2:已处理工单部分
	WorkbenchPart_DonePart WorkbenchPart = 2
	// 3:待补填工单部分
	WorkbenchPart_FillUpPart WorkbenchPart = 3
)

var WorkbenchPart_name = map[int32]string{
	0: "AllPart",
	1: "PendingPart",
	2: "DonePart",
	3: "FillUpPart",
}

var WorkbenchPart_value = map[string]int32{
	"AllPart":     0,
	"PendingPart": 1,
	"DonePart":    2,
	"FillUpPart":  3,
}

func (x WorkbenchPart) String() string {
	return proto.EnumName(WorkbenchPart_name, int32(x))
}

func (WorkbenchPart) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_98a6c21780e82d22, []int{0}
}

// 补填状态
type FillStatus int32

const (
	FillStatus_FillNone FillStatus = 0
	// 玩家已补填单
	FillStatus_PlayerRefillSt FillStatus = 1
	// VIP已补填单
	FillStatus_VipRefillSt FillStatus = 2
)

var FillStatus_name = map[int32]string{
	0: "FillNone",
	1: "PlayerRefillSt",
	2: "VipRefillSt",
}

var FillStatus_value = map[string]int32{
	"FillNone":       0,
	"PlayerRefillSt": 1,
	"VipRefillSt":    2,
}

func (x FillStatus) String() string {
	return proto.EnumName(FillStatus_name, int32(x))
}

func (FillStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_98a6c21780e82d22, []int{1}
}

// 工单列表排序方式
type TkPoolSort int32

const (
	TkPoolSort_TkPoolSortWaitDefault TkPoolSort = 0
	// 等待时长 - 默认
	TkPoolSort_TkPoolSortWaitTm TkPoolSort = 1
	// 创建时间
	TkPoolSort_TkPoolSortCreateTm TkPoolSort = 2
	// 充值金额
	TkPoolSort_TkPoolSortRecharge TkPoolSort = 3
)

var TkPoolSort_name = map[int32]string{
	0: "TkPoolSortWaitDefault",
	1: "TkPoolSortWaitTm",
	2: "TkPoolSortCreateTm",
	3: "TkPoolSortRecharge",
}

var TkPoolSort_value = map[string]int32{
	"TkPoolSortWaitDefault": 0,
	"TkPoolSortWaitTm":      1,
	"TkPoolSortCreateTm":    2,
	"TkPoolSortRecharge":    3,
}

func (x TkPoolSort) String() string {
	return proto.EnumName(TkPoolSort_name, int32(x))
}

func (TkPoolSort) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_98a6c21780e82d22, []int{2}
}

type TkTransferOpType int32

const (
	TkTransferOpType_TkTransferOpTypeUnknown TkTransferOpType = 0
	// 客服回复
	TkTransferOpType_TkTransferOpTypeCommu TkTransferOpType = 1
	// 回复&关单
	TkTransferOpType_TkTransferOpTypeCommuClose TkTransferOpType = 2
	// 拒单
	TkTransferOpType_TkTransferOpTypeRejected TkTransferOpType = 3
)

var TkTransferOpType_name = map[int32]string{
	0: "TkTransferOpTypeUnknown",
	1: "TkTransferOpTypeCommu",
	2: "TkTransferOpTypeCommuClose",
	3: "TkTransferOpTypeRejected",
}

var TkTransferOpType_value = map[string]int32{
	"TkTransferOpTypeUnknown":    0,
	"TkTransferOpTypeCommu":      1,
	"TkTransferOpTypeCommuClose": 2,
	"TkTransferOpTypeRejected":   3,
}

func (x TkTransferOpType) String() string {
	return proto.EnumName(TkTransferOpType_name, int32(x))
}

func (TkTransferOpType) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_98a6c21780e82d22, []int{3}
}

// TicketPoolListReq 工单池列表响应结果
type TicketPoolListResp struct {
	CurrentPage          uint32        `protobuf:"varint,1,opt,name=current_page,json=currentPage,proto3" json:"current_page"`
	PerPage              uint32        `protobuf:"varint,2,opt,name=per_page,json=perPage,proto3" json:"per_page"`
	Total                uint32        `protobuf:"varint,3,opt,name=total,proto3" json:"total"`
	Data                 []*TicketInfo `protobuf:"bytes,4,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-" gorm:"-"`
	XXX_unrecognized     []byte        `json:"-" gorm:"-"`
	XXX_sizecache        int32         `json:"-" gorm:"-"`
}

func (m *TicketPoolListResp) Reset()         { *m = TicketPoolListResp{} }
func (m *TicketPoolListResp) String() string { return proto.CompactTextString(m) }
func (*TicketPoolListResp) ProtoMessage()    {}
func (*TicketPoolListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_98a6c21780e82d22, []int{0}
}

func (m *TicketPoolListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TicketPoolListResp.Unmarshal(m, b)
}
func (m *TicketPoolListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TicketPoolListResp.Marshal(b, m, deterministic)
}
func (m *TicketPoolListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TicketPoolListResp.Merge(m, src)
}
func (m *TicketPoolListResp) XXX_Size() int {
	return xxx_messageInfo_TicketPoolListResp.Size(m)
}
func (m *TicketPoolListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_TicketPoolListResp.DiscardUnknown(m)
}

var xxx_messageInfo_TicketPoolListResp proto.InternalMessageInfo

func (m *TicketPoolListResp) GetCurrentPage() uint32 {
	if m != nil {
		return m.CurrentPage
	}
	return 0
}

func (m *TicketPoolListResp) GetPerPage() uint32 {
	if m != nil {
		return m.PerPage
	}
	return 0
}

func (m *TicketPoolListResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *TicketPoolListResp) GetData() []*TicketInfo {
	if m != nil {
		return m.Data
	}
	return nil
}

type TicketInfo struct {
	// 工单ID
	TicketId uint64 `protobuf:"varint,1,opt,name=ticket_id,json=ticketId,proto3" json:"ticket_id"`
	// 所属项目
	Project string `protobuf:"bytes,2,opt,name=project,proto3" json:"project"`
	// 问题分类
	//    string category = 3;
	// 工单来源
	Origin uint32 `protobuf:"varint,4,opt,name=origin,proto3" json:"origin"`
	// 工单节点
	ConversionNode uint32 `protobuf:"varint,5,opt,name=conversion_node,json=conversionNode,proto3" json:"conversion_node"`
	// 状态
	Status uint32 `protobuf:"varint,6,opt,name=status,proto3" json:"status"`
	// 创建时间
	CreatedAt string `protobuf:"bytes,7,opt,name=created_at,json=createdAt,proto3" json:"created_at"`
	// 结案时间
	ClosedAt string `protobuf:"bytes,8,opt,name=closed_at,json=closedAt,proto3" json:"closed_at"`
	// 创建人
	Creator string `protobuf:"bytes,9,opt,name=creator,proto3" json:"creator"`
	// 受理员
	Acceptor string `protobuf:"bytes,10,opt,name=acceptor,proto3" json:"acceptor"`
	// 语言
	Lang string `protobuf:"bytes,11,opt,name=lang,proto3" json:"lang"`
	// vip等级
	Vip uint32 `protobuf:"varint,12,opt,name=vip,proto3" json:"vip"`
	// 紧急度
	Priority uint32 `protobuf:"varint,13,opt,name=priority,proto3" json:"priority"`
	// account_id
	AccountId string `protobuf:"bytes,15,opt,name=account_id,json=accountId,proto3" json:"account_id"`
	// csi
	Csi uint32 `protobuf:"varint,16,opt,name=csi,proto3" json:"csi"`
	// 评语
	Comments             string   `protobuf:"bytes,17,opt,name=comments,proto3" json:"comments"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *TicketInfo) Reset()         { *m = TicketInfo{} }
func (m *TicketInfo) String() string { return proto.CompactTextString(m) }
func (*TicketInfo) ProtoMessage()    {}
func (*TicketInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_98a6c21780e82d22, []int{1}
}

func (m *TicketInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TicketInfo.Unmarshal(m, b)
}
func (m *TicketInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TicketInfo.Marshal(b, m, deterministic)
}
func (m *TicketInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TicketInfo.Merge(m, src)
}
func (m *TicketInfo) XXX_Size() int {
	return xxx_messageInfo_TicketInfo.Size(m)
}
func (m *TicketInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_TicketInfo.DiscardUnknown(m)
}

var xxx_messageInfo_TicketInfo proto.InternalMessageInfo

func (m *TicketInfo) GetTicketId() uint64 {
	if m != nil {
		return m.TicketId
	}
	return 0
}

func (m *TicketInfo) GetProject() string {
	if m != nil {
		return m.Project
	}
	return ""
}

func (m *TicketInfo) GetOrigin() uint32 {
	if m != nil {
		return m.Origin
	}
	return 0
}

func (m *TicketInfo) GetConversionNode() uint32 {
	if m != nil {
		return m.ConversionNode
	}
	return 0
}

func (m *TicketInfo) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *TicketInfo) GetCreatedAt() string {
	if m != nil {
		return m.CreatedAt
	}
	return ""
}

func (m *TicketInfo) GetClosedAt() string {
	if m != nil {
		return m.ClosedAt
	}
	return ""
}

func (m *TicketInfo) GetCreator() string {
	if m != nil {
		return m.Creator
	}
	return ""
}

func (m *TicketInfo) GetAcceptor() string {
	if m != nil {
		return m.Acceptor
	}
	return ""
}

func (m *TicketInfo) GetLang() string {
	if m != nil {
		return m.Lang
	}
	return ""
}

func (m *TicketInfo) GetVip() uint32 {
	if m != nil {
		return m.Vip
	}
	return 0
}

func (m *TicketInfo) GetPriority() uint32 {
	if m != nil {
		return m.Priority
	}
	return 0
}

func (m *TicketInfo) GetAccountId() string {
	if m != nil {
		return m.AccountId
	}
	return ""
}

func (m *TicketInfo) GetCsi() uint32 {
	if m != nil {
		return m.Csi
	}
	return 0
}

func (m *TicketInfo) GetComments() string {
	if m != nil {
		return m.Comments
	}
	return ""
}

//工单总量请求参数
type TicketCountReq struct {
	// 游戏权限
	Project []string `protobuf:"bytes,1,rep,name=project,proto3" json:"project"`
	// 工单状态
	Stage []uint32 `protobuf:"varint,2,rep,packed,name=stage,proto3" json:"stage"`
	// 受理人
	Acceptor []string `protobuf:"bytes,3,rep,name=acceptor,proto3" json:"acceptor"`
	// 语言
	Lang []string `protobuf:"bytes,4,rep,name=lang,proto3" json:"lang"`
	// VIP
	Vip []uint32 `protobuf:"varint,5,rep,packed,name=vip,proto3" json:"vip"`
	// 升级区
	Priority             uint32   `protobuf:"varint,6,opt,name=priority,proto3" json:"priority"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *TicketCountReq) Reset()         { *m = TicketCountReq{} }
func (m *TicketCountReq) String() string { return proto.CompactTextString(m) }
func (*TicketCountReq) ProtoMessage()    {}
func (*TicketCountReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_98a6c21780e82d22, []int{2}
}

func (m *TicketCountReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TicketCountReq.Unmarshal(m, b)
}
func (m *TicketCountReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TicketCountReq.Marshal(b, m, deterministic)
}
func (m *TicketCountReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TicketCountReq.Merge(m, src)
}
func (m *TicketCountReq) XXX_Size() int {
	return xxx_messageInfo_TicketCountReq.Size(m)
}
func (m *TicketCountReq) XXX_DiscardUnknown() {
	xxx_messageInfo_TicketCountReq.DiscardUnknown(m)
}

var xxx_messageInfo_TicketCountReq proto.InternalMessageInfo

func (m *TicketCountReq) GetProject() []string {
	if m != nil {
		return m.Project
	}
	return nil
}

func (m *TicketCountReq) GetStage() []uint32 {
	if m != nil {
		return m.Stage
	}
	return nil
}

func (m *TicketCountReq) GetAcceptor() []string {
	if m != nil {
		return m.Acceptor
	}
	return nil
}

func (m *TicketCountReq) GetLang() []string {
	if m != nil {
		return m.Lang
	}
	return nil
}

func (m *TicketCountReq) GetVip() []uint32 {
	if m != nil {
		return m.Vip
	}
	return nil
}

func (m *TicketCountReq) GetPriority() uint32 {
	if m != nil {
		return m.Priority
	}
	return 0
}

//工单总量响应值
type TicketCountResp struct {
	// 待处理工单条数
	PendingCount uint64 `protobuf:"varint,1,opt,name=pending_count,json=pendingCount,proto3" json:"pending_count"`
	// 当前用户待处理工单量
	UserPendingCount uint64 `protobuf:"varint,2,opt,name=user_pending_count,json=userPendingCount,proto3" json:"user_pending_count"`
	// 当前用户已完成工单量
	UserCompletedCount uint64 `protobuf:"varint,3,opt,name=user_completed_count,json=userCompletedCount,proto3" json:"user_completed_count"`
	// 待处理中文工单条数
	PendingCnCount uint64 `protobuf:"varint,4,opt,name=pending_cn_count,json=pendingCnCount,proto3" json:"pending_cn_count"`
	// 待处理vip工单条数
	PendingVipCount uint64 `protobuf:"varint,5,opt,name=pending_vip_count,json=pendingVipCount,proto3" json:"pending_vip_count"`
	// 升级区工单条数
	PriorityCount        uint64   `protobuf:"varint,6,opt,name=priority_count,json=priorityCount,proto3" json:"priority_count"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *TicketCountResp) Reset()         { *m = TicketCountResp{} }
func (m *TicketCountResp) String() string { return proto.CompactTextString(m) }
func (*TicketCountResp) ProtoMessage()    {}
func (*TicketCountResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_98a6c21780e82d22, []int{3}
}

func (m *TicketCountResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TicketCountResp.Unmarshal(m, b)
}
func (m *TicketCountResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TicketCountResp.Marshal(b, m, deterministic)
}
func (m *TicketCountResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TicketCountResp.Merge(m, src)
}
func (m *TicketCountResp) XXX_Size() int {
	return xxx_messageInfo_TicketCountResp.Size(m)
}
func (m *TicketCountResp) XXX_DiscardUnknown() {
	xxx_messageInfo_TicketCountResp.DiscardUnknown(m)
}

var xxx_messageInfo_TicketCountResp proto.InternalMessageInfo

func (m *TicketCountResp) GetPendingCount() uint64 {
	if m != nil {
		return m.PendingCount
	}
	return 0
}

func (m *TicketCountResp) GetUserPendingCount() uint64 {
	if m != nil {
		return m.UserPendingCount
	}
	return 0
}

func (m *TicketCountResp) GetUserCompletedCount() uint64 {
	if m != nil {
		return m.UserCompletedCount
	}
	return 0
}

func (m *TicketCountResp) GetPendingCnCount() uint64 {
	if m != nil {
		return m.PendingCnCount
	}
	return 0
}

func (m *TicketCountResp) GetPendingVipCount() uint64 {
	if m != nil {
		return m.PendingVipCount
	}
	return 0
}

func (m *TicketCountResp) GetPriorityCount() uint64 {
	if m != nil {
		return m.PriorityCount
	}
	return 0
}

// TicketPoolNewListReq 新工单池请求参数
type TicketPoolNewListReq struct {
	// 游戏
	Project []string `protobuf:"bytes,1,rep,name=project,proto3" json:"project"`
	// 创建时间
	CreatedAt []string `protobuf:"bytes,2,rep,name=created_at,json=createdAt,proto3" json:"created_at"`
	// 结单时间
	ClosedAt []string `protobuf:"bytes,3,rep,name=closed_at,json=closedAt,proto3" json:"closed_at"`
	// 评价时间
	EvaluateAt []string `protobuf:"bytes,4,rep,name=evaluate_at,json=evaluateAt,proto3" json:"evaluate_at"`
	// 工单状态  - 工单池
	Status []uint32 `protobuf:"varint,5,rep,packed,name=status,proto3" json:"status"`
	// 工单来源
	Scene []uint32 `protobuf:"varint,6,rep,packed,name=scene,proto3" json:"scene"`
	// 渠道包
	Channel []string `protobuf:"bytes,7,rep,name=channel,proto3" json:"channel"`
	// 标签
	Label []uint32 `protobuf:"varint,8,rep,packed,name=label,proto3" json:"label"`
	// NPS
	Nps []uint32 `protobuf:"varint,9,rep,packed,name=nps,proto3" json:"nps"`
	// 工单评星
	Csi []uint32 `protobuf:"varint,10,rep,packed,name=csi,proto3" json:"csi"`
	// 工单语言
	Language []string `protobuf:"bytes,11,rep,name=language,proto3" json:"language"`
	// 服务器
	Sid string `protobuf:"bytes,12,opt,name=sid,proto3" json:"sid"`
	// 工单ID
	TicketId uint64 `protobuf:"varint,13,opt,name=ticket_id,json=ticketId,proto3" json:"ticket_id"`
	// 处理人类型 1:空白 2:包含
	AcceptorType FilterEnum `protobuf:"varint,14,opt,name=acceptor_type,json=acceptorType,proto3,enum=pb.FilterEnum" json:"acceptor_type"`
	// 处理人 - 工单池
	Acceptor string `protobuf:"bytes,15,opt,name=acceptor,proto3" json:"acceptor"`
	// 备注
	Remark string `protobuf:"bytes,16,opt,name=remark,proto3" json:"remark"`
	// 提交人查询类型 1:玩家fpid 2：玩家姓名 3:客服账号，4：account_id 5:uid
	CreatorType CreatorType `protobuf:"varint,17,opt,name=creator_type,json=creatorType,proto3,enum=pb.CreatorType" json:"creator_type"`
	// 提交人查询值
	Creator string `protobuf:"bytes,18,opt,name=creator,proto3" json:"creator"`
	// 页码
	Page uint32 `protobuf:"varint,19,opt,name=page,proto3" json:"page"`
	// 页大小
	PageSize uint32 `protobuf:"varint,20,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	// 问题分类
	CatId       []uint32 `protobuf:"varint,21,rep,packed,name=cat_id,json=catId,proto3" json:"cat_id"`
	SystemLabel []uint32 `protobuf:"varint,22,rep,packed,name=system_label,json=systemLabel,proto3" json:"system_label"`
	// 表单信息
	Field string `protobuf:"bytes,23,opt,name=field,proto3" json:"field"`
	// 是否是 VIP: true：表示是 VIP
	IsVip bool `protobuf:"varint,24,opt,name=is_vip,json=isVip,proto3" json:"is_vip"`
	// 是否是 升级单： true: 表示是 升级单
	IsUpgrade bool `protobuf:"varint,25,opt,name=is_upgrade,json=isUpgrade,proto3" json:"is_upgrade"`
	// 标签类型
	TagType FilterTagEnum `protobuf:"varint,26,opt,name=tag_type,json=tagType,proto3,enum=pb.FilterTagEnum" json:"tag_type"`
	// 数据排序方式： 1：等待时长； 2：创建时间；3：充值金额
	SortBy TkPoolSort `protobuf:"varint,27,opt,name=sort_by,json=sortBy,proto3,enum=pb.TkPoolSort" json:"sort_by"`
	// 排序顺序，升序asc 逆序desc 默认asc
	Order string `protobuf:"bytes,28,opt,name=order,proto3" json:"order"`
	// 查询类型
	SearchType uint32 `protobuf:"varint,29,opt,name=search_type,json=searchType,proto3" json:"search_type"`
	// 用户类型
	UserType []uint32 `protobuf:"varint,30,rep,packed,name=user_type,json=userType,proto3" json:"user_type"`
	// 0默认全部，1是，2不是
	Svip string `protobuf:"bytes,31,opt,name=svip,proto3" json:"svip"`
	// // 0默认全部，1是，2不是
	VipCrm string `protobuf:"bytes,32,opt,name=vip_crm,json=vipCrm,proto3" json:"vip_crm"`
	// 工单号s
	TicketIds string `protobuf:"bytes,33,opt,name=ticket_ids,json=ticketIds,proto3" json:"ticket_ids"`
	// 客服s
	Acceptors []string `protobuf:"bytes,34,rep,name=acceptors,proto3" json:"acceptors"`
	// 团队
	TeamIds []int64 `protobuf:"varint,35,rep,packed,name=team_ids,json=teamIds,proto3" json:"team_ids"`
	// 重开次数
	ReopenNum string `protobuf:"bytes,36,opt,name=reopen_num,json=reopenNum,proto3" json:"reopen_num"`
	// 升级次数
	UpgradeNum string `protobuf:"bytes,37,opt,name=upgrade_num,json=upgradeNum,proto3" json:"upgrade_num"`
	// 渠道号
	PackageId []string `protobuf:"bytes,38,rep,name=packageId,proto3" json:"packageId"`
	// 游戏版本
	GameVersion []string `protobuf:"bytes,39,rep,name=game_version,json=gameVersion,proto3" json:"game_version"`
	// 工单处理类型
	SolveType []uint32 `protobuf:"varint,40,rep,packed,name=solve_type,json=solveType,proto3" json:"solve_type"`
	// 私域R级参数
	ZoneVipLevel []string `protobuf:"bytes,41,rep,name=zone_vip_level,json=zoneVipLevel,proto3" json:"zone_vip_level"`
	// 玩家累付金额
	PayAll               []int64  `protobuf:"varint,42,rep,packed,name=pay_all,json=payAll,proto3" json:"pay_all"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *TicketPoolNewListReq) Reset()         { *m = TicketPoolNewListReq{} }
func (m *TicketPoolNewListReq) String() string { return proto.CompactTextString(m) }
func (*TicketPoolNewListReq) ProtoMessage()    {}
func (*TicketPoolNewListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_98a6c21780e82d22, []int{4}
}

func (m *TicketPoolNewListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TicketPoolNewListReq.Unmarshal(m, b)
}
func (m *TicketPoolNewListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TicketPoolNewListReq.Marshal(b, m, deterministic)
}
func (m *TicketPoolNewListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TicketPoolNewListReq.Merge(m, src)
}
func (m *TicketPoolNewListReq) XXX_Size() int {
	return xxx_messageInfo_TicketPoolNewListReq.Size(m)
}
func (m *TicketPoolNewListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_TicketPoolNewListReq.DiscardUnknown(m)
}

var xxx_messageInfo_TicketPoolNewListReq proto.InternalMessageInfo

func (m *TicketPoolNewListReq) GetProject() []string {
	if m != nil {
		return m.Project
	}
	return nil
}

func (m *TicketPoolNewListReq) GetCreatedAt() []string {
	if m != nil {
		return m.CreatedAt
	}
	return nil
}

func (m *TicketPoolNewListReq) GetClosedAt() []string {
	if m != nil {
		return m.ClosedAt
	}
	return nil
}

func (m *TicketPoolNewListReq) GetEvaluateAt() []string {
	if m != nil {
		return m.EvaluateAt
	}
	return nil
}

func (m *TicketPoolNewListReq) GetStatus() []uint32 {
	if m != nil {
		return m.Status
	}
	return nil
}

func (m *TicketPoolNewListReq) GetScene() []uint32 {
	if m != nil {
		return m.Scene
	}
	return nil
}

func (m *TicketPoolNewListReq) GetChannel() []string {
	if m != nil {
		return m.Channel
	}
	return nil
}

func (m *TicketPoolNewListReq) GetLabel() []uint32 {
	if m != nil {
		return m.Label
	}
	return nil
}

func (m *TicketPoolNewListReq) GetNps() []uint32 {
	if m != nil {
		return m.Nps
	}
	return nil
}

func (m *TicketPoolNewListReq) GetCsi() []uint32 {
	if m != nil {
		return m.Csi
	}
	return nil
}

func (m *TicketPoolNewListReq) GetLanguage() []string {
	if m != nil {
		return m.Language
	}
	return nil
}

func (m *TicketPoolNewListReq) GetSid() string {
	if m != nil {
		return m.Sid
	}
	return ""
}

func (m *TicketPoolNewListReq) GetTicketId() uint64 {
	if m != nil {
		return m.TicketId
	}
	return 0
}

func (m *TicketPoolNewListReq) GetAcceptorType() FilterEnum {
	if m != nil {
		return m.AcceptorType
	}
	return FilterEnum_FilterAll
}

func (m *TicketPoolNewListReq) GetAcceptor() string {
	if m != nil {
		return m.Acceptor
	}
	return ""
}

func (m *TicketPoolNewListReq) GetRemark() string {
	if m != nil {
		return m.Remark
	}
	return ""
}

func (m *TicketPoolNewListReq) GetCreatorType() CreatorType {
	if m != nil {
		return m.CreatorType
	}
	return CreatorType_No
}

func (m *TicketPoolNewListReq) GetCreator() string {
	if m != nil {
		return m.Creator
	}
	return ""
}

func (m *TicketPoolNewListReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *TicketPoolNewListReq) GetPageSize() uint32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *TicketPoolNewListReq) GetCatId() []uint32 {
	if m != nil {
		return m.CatId
	}
	return nil
}

func (m *TicketPoolNewListReq) GetSystemLabel() []uint32 {
	if m != nil {
		return m.SystemLabel
	}
	return nil
}

func (m *TicketPoolNewListReq) GetField() string {
	if m != nil {
		return m.Field
	}
	return ""
}

func (m *TicketPoolNewListReq) GetIsVip() bool {
	if m != nil {
		return m.IsVip
	}
	return false
}

func (m *TicketPoolNewListReq) GetIsUpgrade() bool {
	if m != nil {
		return m.IsUpgrade
	}
	return false
}

func (m *TicketPoolNewListReq) GetTagType() FilterTagEnum {
	if m != nil {
		return m.TagType
	}
	return FilterTagEnum_All
}

func (m *TicketPoolNewListReq) GetSortBy() TkPoolSort {
	if m != nil {
		return m.SortBy
	}
	return TkPoolSort_TkPoolSortWaitDefault
}

func (m *TicketPoolNewListReq) GetOrder() string {
	if m != nil {
		return m.Order
	}
	return ""
}

func (m *TicketPoolNewListReq) GetSearchType() uint32 {
	if m != nil {
		return m.SearchType
	}
	return 0
}

func (m *TicketPoolNewListReq) GetUserType() []uint32 {
	if m != nil {
		return m.UserType
	}
	return nil
}

func (m *TicketPoolNewListReq) GetSvip() string {
	if m != nil {
		return m.Svip
	}
	return ""
}

func (m *TicketPoolNewListReq) GetVipCrm() string {
	if m != nil {
		return m.VipCrm
	}
	return ""
}

func (m *TicketPoolNewListReq) GetTicketIds() string {
	if m != nil {
		return m.TicketIds
	}
	return ""
}

func (m *TicketPoolNewListReq) GetAcceptors() []string {
	if m != nil {
		return m.Acceptors
	}
	return nil
}

func (m *TicketPoolNewListReq) GetTeamIds() []int64 {
	if m != nil {
		return m.TeamIds
	}
	return nil
}

func (m *TicketPoolNewListReq) GetReopenNum() string {
	if m != nil {
		return m.ReopenNum
	}
	return ""
}

func (m *TicketPoolNewListReq) GetUpgradeNum() string {
	if m != nil {
		return m.UpgradeNum
	}
	return ""
}

func (m *TicketPoolNewListReq) GetPackageId() []string {
	if m != nil {
		return m.PackageId
	}
	return nil
}

func (m *TicketPoolNewListReq) GetGameVersion() []string {
	if m != nil {
		return m.GameVersion
	}
	return nil
}

func (m *TicketPoolNewListReq) GetSolveType() []uint32 {
	if m != nil {
		return m.SolveType
	}
	return nil
}

func (m *TicketPoolNewListReq) GetZoneVipLevel() []string {
	if m != nil {
		return m.ZoneVipLevel
	}
	return nil
}

func (m *TicketPoolNewListReq) GetPayAll() []int64 {
	if m != nil {
		return m.PayAll
	}
	return nil
}

// TicketPoolHistoryListReq 历史工单池请求参数
type TicketPoolHistoryListReq struct {
	// 工单ID
	TicketId uint64 `protobuf:"varint,1,opt,name=ticket_id,json=ticketId,proto3" json:"ticket_id"`
	// 页码
	Page uint32 `protobuf:"varint,2,opt,name=page,proto3" json:"page"`
	// 页大小
	PageSize             uint32   `protobuf:"varint,3,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *TicketPoolHistoryListReq) Reset()         { *m = TicketPoolHistoryListReq{} }
func (m *TicketPoolHistoryListReq) String() string { return proto.CompactTextString(m) }
func (*TicketPoolHistoryListReq) ProtoMessage()    {}
func (*TicketPoolHistoryListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_98a6c21780e82d22, []int{5}
}

func (m *TicketPoolHistoryListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TicketPoolHistoryListReq.Unmarshal(m, b)
}
func (m *TicketPoolHistoryListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TicketPoolHistoryListReq.Marshal(b, m, deterministic)
}
func (m *TicketPoolHistoryListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TicketPoolHistoryListReq.Merge(m, src)
}
func (m *TicketPoolHistoryListReq) XXX_Size() int {
	return xxx_messageInfo_TicketPoolHistoryListReq.Size(m)
}
func (m *TicketPoolHistoryListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_TicketPoolHistoryListReq.DiscardUnknown(m)
}

var xxx_messageInfo_TicketPoolHistoryListReq proto.InternalMessageInfo

func (m *TicketPoolHistoryListReq) GetTicketId() uint64 {
	if m != nil {
		return m.TicketId
	}
	return 0
}

func (m *TicketPoolHistoryListReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *TicketPoolHistoryListReq) GetPageSize() uint32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

// TicketPoolNewListResp 新工单池响应参数
type TicketPoolNewListResp struct {
	CurrentPage          uint32                                  `protobuf:"varint,1,opt,name=current_page,json=currentPage,proto3" json:"current_page"`
	PerPage              uint32                                  `protobuf:"varint,2,opt,name=per_page,json=perPage,proto3" json:"per_page"`
	Total                uint32                                  `protobuf:"varint,3,opt,name=total,proto3" json:"total"`
	Data                 []*TicketPoolNewListResp_TicketPoolInfo `protobuf:"bytes,4,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}                                `json:"-" gorm:"-"`
	XXX_unrecognized     []byte                                  `json:"-" gorm:"-"`
	XXX_sizecache        int32                                   `json:"-" gorm:"-"`
}

func (m *TicketPoolNewListResp) Reset()         { *m = TicketPoolNewListResp{} }
func (m *TicketPoolNewListResp) String() string { return proto.CompactTextString(m) }
func (*TicketPoolNewListResp) ProtoMessage()    {}
func (*TicketPoolNewListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_98a6c21780e82d22, []int{6}
}

func (m *TicketPoolNewListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TicketPoolNewListResp.Unmarshal(m, b)
}
func (m *TicketPoolNewListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TicketPoolNewListResp.Marshal(b, m, deterministic)
}
func (m *TicketPoolNewListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TicketPoolNewListResp.Merge(m, src)
}
func (m *TicketPoolNewListResp) XXX_Size() int {
	return xxx_messageInfo_TicketPoolNewListResp.Size(m)
}
func (m *TicketPoolNewListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_TicketPoolNewListResp.DiscardUnknown(m)
}

var xxx_messageInfo_TicketPoolNewListResp proto.InternalMessageInfo

func (m *TicketPoolNewListResp) GetCurrentPage() uint32 {
	if m != nil {
		return m.CurrentPage
	}
	return 0
}

func (m *TicketPoolNewListResp) GetPerPage() uint32 {
	if m != nil {
		return m.PerPage
	}
	return 0
}

func (m *TicketPoolNewListResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *TicketPoolNewListResp) GetData() []*TicketPoolNewListResp_TicketPoolInfo {
	if m != nil {
		return m.Data
	}
	return nil
}

type TicketPoolNewListResp_TicketPoolInfo struct {
	// 游戏
	Project string `protobuf:"bytes,1,opt,name=project,proto3" json:"project"`
	// 工单ID
	TicketId uint64 `protobuf:"varint,2,opt,name=ticket_id,json=ticketId,proto3" json:"ticket_id"`
	// 玩家输入第一句话
	Detail string `protobuf:"bytes,3,opt,name=detail,proto3" json:"detail"`
	// 累计金额
	Recharge float64 `protobuf:"fixed64,4,opt,name=recharge,proto3" json:"recharge"`
	// 工单状态
	Status uint32 `protobuf:"varint,5,opt,name=status,proto3" json:"status"`
	// 等待时长
	WaitingTime string `protobuf:"bytes,6,opt,name=waiting_time,json=waitingTime,proto3" json:"waiting_time"`
	// 服务评分
	Csi uint32 `protobuf:"varint,7,opt,name=csi,proto3" json:"csi"`
	// 当前处理人
	Acceptor string `protobuf:"bytes,8,opt,name=acceptor,proto3" json:"acceptor"`
	// ss vip绿色通道单
	CrmVipUserFlag bool `protobuf:"varint,9,opt,name=crm_vip_user_flag,json=crmVipUserFlag,proto3" json:"crm_vip_user_flag"`
	Checked        bool `protobuf:"varint,10,opt,name=checked,proto3" json:"checked"`
	// 是否7日内在dc端发起过对话
	DcFlag bool `protobuf:"varint,11,opt,name=dc_flag,json=dcFlag,proto3" json:"dc_flag"`
	// DC对话创建时间
	DcCreateTime string `protobuf:"bytes,12,opt,name=dc_create_time,json=dcCreateTime,proto3" json:"dc_create_time"`
	// 用户AccountID
	AccountId string `protobuf:"bytes,13,opt,name=account_id,json=accountId,proto3" json:"account_id"`
	//系统标签
	SystemLabel          []uint32 `protobuf:"varint,22,rep,packed,name=system_label,json=systemLabel,proto3" json:"system_label"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *TicketPoolNewListResp_TicketPoolInfo) Reset()         { *m = TicketPoolNewListResp_TicketPoolInfo{} }
func (m *TicketPoolNewListResp_TicketPoolInfo) String() string { return proto.CompactTextString(m) }
func (*TicketPoolNewListResp_TicketPoolInfo) ProtoMessage()    {}
func (*TicketPoolNewListResp_TicketPoolInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_98a6c21780e82d22, []int{6, 0}
}

func (m *TicketPoolNewListResp_TicketPoolInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TicketPoolNewListResp_TicketPoolInfo.Unmarshal(m, b)
}
func (m *TicketPoolNewListResp_TicketPoolInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TicketPoolNewListResp_TicketPoolInfo.Marshal(b, m, deterministic)
}
func (m *TicketPoolNewListResp_TicketPoolInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TicketPoolNewListResp_TicketPoolInfo.Merge(m, src)
}
func (m *TicketPoolNewListResp_TicketPoolInfo) XXX_Size() int {
	return xxx_messageInfo_TicketPoolNewListResp_TicketPoolInfo.Size(m)
}
func (m *TicketPoolNewListResp_TicketPoolInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_TicketPoolNewListResp_TicketPoolInfo.DiscardUnknown(m)
}

var xxx_messageInfo_TicketPoolNewListResp_TicketPoolInfo proto.InternalMessageInfo

func (m *TicketPoolNewListResp_TicketPoolInfo) GetProject() string {
	if m != nil {
		return m.Project
	}
	return ""
}

func (m *TicketPoolNewListResp_TicketPoolInfo) GetTicketId() uint64 {
	if m != nil {
		return m.TicketId
	}
	return 0
}

func (m *TicketPoolNewListResp_TicketPoolInfo) GetDetail() string {
	if m != nil {
		return m.Detail
	}
	return ""
}

func (m *TicketPoolNewListResp_TicketPoolInfo) GetRecharge() float64 {
	if m != nil {
		return m.Recharge
	}
	return 0
}

func (m *TicketPoolNewListResp_TicketPoolInfo) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *TicketPoolNewListResp_TicketPoolInfo) GetWaitingTime() string {
	if m != nil {
		return m.WaitingTime
	}
	return ""
}

func (m *TicketPoolNewListResp_TicketPoolInfo) GetCsi() uint32 {
	if m != nil {
		return m.Csi
	}
	return 0
}

func (m *TicketPoolNewListResp_TicketPoolInfo) GetAcceptor() string {
	if m != nil {
		return m.Acceptor
	}
	return ""
}

func (m *TicketPoolNewListResp_TicketPoolInfo) GetCrmVipUserFlag() bool {
	if m != nil {
		return m.CrmVipUserFlag
	}
	return false
}

func (m *TicketPoolNewListResp_TicketPoolInfo) GetChecked() bool {
	if m != nil {
		return m.Checked
	}
	return false
}

func (m *TicketPoolNewListResp_TicketPoolInfo) GetDcFlag() bool {
	if m != nil {
		return m.DcFlag
	}
	return false
}

func (m *TicketPoolNewListResp_TicketPoolInfo) GetDcCreateTime() string {
	if m != nil {
		return m.DcCreateTime
	}
	return ""
}

func (m *TicketPoolNewListResp_TicketPoolInfo) GetAccountId() string {
	if m != nil {
		return m.AccountId
	}
	return ""
}

func (m *TicketPoolNewListResp_TicketPoolInfo) GetSystemLabel() []uint32 {
	if m != nil {
		return m.SystemLabel
	}
	return nil
}

// TicketPoolNewTopResp 顶部信息
type TicketPoolTopResp struct {
	// 工单ID
	TicketId uint64 `protobuf:"varint,1,opt,name=ticket_id,json=ticketId,proto3" json:"ticket_id"`
	// - 处理人（当没有处理人时，此处显示：待接单） todo 代码处理
	Acceptor string `protobuf:"bytes,2,opt,name=acceptor,proto3" json:"acceptor"`
	// 玩家昵称
	Nickname string `protobuf:"bytes,3,opt,name=nickname,proto3" json:"nickname"`
	// Fpid
	AccountId string `protobuf:"bytes,4,opt,name=account_id,json=accountId,proto3" json:"account_id"`
	// Uid
	Uid uint64 `protobuf:"varint,5,opt,name=uid,proto3" json:"uid"`
	// 游戏版本
	AppVersion string `protobuf:"bytes,6,opt,name=app_version,json=appVersion,proto3" json:"app_version"`
	// 服务器
	Sid uint32 `protobuf:"varint,7,opt,name=sid,proto3" json:"sid"`
	// 游戏
	Project string `protobuf:"bytes,8,opt,name=project,proto3" json:"project"`
	// 升级单： 1：正常单；2：升级单
	Priority uint32 `protobuf:"varint,9,opt,name=priority,proto3" json:"priority"`
	// 当前工单流转状态
	Status               TkStage  `protobuf:"varint,10,opt,name=status,proto3,enum=pb.TkStage" json:"status"`
	Csi                  uint32   `protobuf:"varint,11,opt,name=csi,proto3" json:"csi"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *TicketPoolTopResp) Reset()         { *m = TicketPoolTopResp{} }
func (m *TicketPoolTopResp) String() string { return proto.CompactTextString(m) }
func (*TicketPoolTopResp) ProtoMessage()    {}
func (*TicketPoolTopResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_98a6c21780e82d22, []int{7}
}

func (m *TicketPoolTopResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TicketPoolTopResp.Unmarshal(m, b)
}
func (m *TicketPoolTopResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TicketPoolTopResp.Marshal(b, m, deterministic)
}
func (m *TicketPoolTopResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TicketPoolTopResp.Merge(m, src)
}
func (m *TicketPoolTopResp) XXX_Size() int {
	return xxx_messageInfo_TicketPoolTopResp.Size(m)
}
func (m *TicketPoolTopResp) XXX_DiscardUnknown() {
	xxx_messageInfo_TicketPoolTopResp.DiscardUnknown(m)
}

var xxx_messageInfo_TicketPoolTopResp proto.InternalMessageInfo

func (m *TicketPoolTopResp) GetTicketId() uint64 {
	if m != nil {
		return m.TicketId
	}
	return 0
}

func (m *TicketPoolTopResp) GetAcceptor() string {
	if m != nil {
		return m.Acceptor
	}
	return ""
}

func (m *TicketPoolTopResp) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *TicketPoolTopResp) GetAccountId() string {
	if m != nil {
		return m.AccountId
	}
	return ""
}

func (m *TicketPoolTopResp) GetUid() uint64 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *TicketPoolTopResp) GetAppVersion() string {
	if m != nil {
		return m.AppVersion
	}
	return ""
}

func (m *TicketPoolTopResp) GetSid() uint32 {
	if m != nil {
		return m.Sid
	}
	return 0
}

func (m *TicketPoolTopResp) GetProject() string {
	if m != nil {
		return m.Project
	}
	return ""
}

func (m *TicketPoolTopResp) GetPriority() uint32 {
	if m != nil {
		return m.Priority
	}
	return 0
}

func (m *TicketPoolTopResp) GetStatus() TkStage {
	if m != nil {
		return m.Status
	}
	return TkStage_TkStageUnknown
}

func (m *TicketPoolTopResp) GetCsi() uint32 {
	if m != nil {
		return m.Csi
	}
	return 0
}

// TicketPoolNewTopResp 顶部信息
type TicketPoolNewTopResp struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *TicketPoolNewTopResp) Reset()         { *m = TicketPoolNewTopResp{} }
func (m *TicketPoolNewTopResp) String() string { return proto.CompactTextString(m) }
func (*TicketPoolNewTopResp) ProtoMessage()    {}
func (*TicketPoolNewTopResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_98a6c21780e82d22, []int{8}
}

func (m *TicketPoolNewTopResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TicketPoolNewTopResp.Unmarshal(m, b)
}
func (m *TicketPoolNewTopResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TicketPoolNewTopResp.Marshal(b, m, deterministic)
}
func (m *TicketPoolNewTopResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TicketPoolNewTopResp.Merge(m, src)
}
func (m *TicketPoolNewTopResp) XXX_Size() int {
	return xxx_messageInfo_TicketPoolNewTopResp.Size(m)
}
func (m *TicketPoolNewTopResp) XXX_DiscardUnknown() {
	xxx_messageInfo_TicketPoolNewTopResp.DiscardUnknown(m)
}

var xxx_messageInfo_TicketPoolNewTopResp proto.InternalMessageInfo

type TicketPoolNewTopRespTop struct {
	// 工单ID
	TicketId []uint64 `protobuf:"varint,1,rep,packed,name=ticket_id,json=ticketId,proto3" json:"ticket_id"`
	// - 处理人（当没有处理人时，此处显示：待接单） todo 代码处理
	Acceptor string `protobuf:"bytes,2,opt,name=acceptor,proto3" json:"acceptor"`
	// 玩家昵称
	Nickname []uint64 `protobuf:"varint,3,rep,packed,name=nickname,proto3" json:"nickname"`
	// Fpid
	Fpid []uint32 `protobuf:"varint,4,rep,packed,name=fpid,proto3" json:"fpid"`
	// Uid
	Uid []uint32 `protobuf:"varint,5,rep,packed,name=uid,proto3" json:"uid"`
	// 游戏版本
	GameVersion []string `protobuf:"bytes,6,rep,name=game_version,json=gameVersion,proto3" json:"game_version"`
	// 服务器
	Sid                  []uint32 `protobuf:"varint,7,rep,packed,name=sid,proto3" json:"sid"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *TicketPoolNewTopRespTop) Reset()         { *m = TicketPoolNewTopRespTop{} }
func (m *TicketPoolNewTopRespTop) String() string { return proto.CompactTextString(m) }
func (*TicketPoolNewTopRespTop) ProtoMessage()    {}
func (*TicketPoolNewTopRespTop) Descriptor() ([]byte, []int) {
	return fileDescriptor_98a6c21780e82d22, []int{8, 0}
}

func (m *TicketPoolNewTopRespTop) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TicketPoolNewTopRespTop.Unmarshal(m, b)
}
func (m *TicketPoolNewTopRespTop) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TicketPoolNewTopRespTop.Marshal(b, m, deterministic)
}
func (m *TicketPoolNewTopRespTop) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TicketPoolNewTopRespTop.Merge(m, src)
}
func (m *TicketPoolNewTopRespTop) XXX_Size() int {
	return xxx_messageInfo_TicketPoolNewTopRespTop.Size(m)
}
func (m *TicketPoolNewTopRespTop) XXX_DiscardUnknown() {
	xxx_messageInfo_TicketPoolNewTopRespTop.DiscardUnknown(m)
}

var xxx_messageInfo_TicketPoolNewTopRespTop proto.InternalMessageInfo

func (m *TicketPoolNewTopRespTop) GetTicketId() []uint64 {
	if m != nil {
		return m.TicketId
	}
	return nil
}

func (m *TicketPoolNewTopRespTop) GetAcceptor() string {
	if m != nil {
		return m.Acceptor
	}
	return ""
}

func (m *TicketPoolNewTopRespTop) GetNickname() []uint64 {
	if m != nil {
		return m.Nickname
	}
	return nil
}

func (m *TicketPoolNewTopRespTop) GetFpid() []uint32 {
	if m != nil {
		return m.Fpid
	}
	return nil
}

func (m *TicketPoolNewTopRespTop) GetUid() []uint32 {
	if m != nil {
		return m.Uid
	}
	return nil
}

func (m *TicketPoolNewTopRespTop) GetGameVersion() []string {
	if m != nil {
		return m.GameVersion
	}
	return nil
}

func (m *TicketPoolNewTopRespTop) GetSid() []uint32 {
	if m != nil {
		return m.Sid
	}
	return nil
}

type TicketPoolNewTopResp_UserInfoResp struct {
	// 标签
	Label []int32 `protobuf:"varint,1,rep,packed,name=label,proto3" json:"label"`
	// 问题类型
	Category string `protobuf:"bytes,2,opt,name=category,proto3" json:"category"`
	// 创建时间
	CreatedAt string `protobuf:"bytes,3,opt,name=created_at,json=createdAt,proto3" json:"created_at"`
	// 渠道包
	Channel string `protobuf:"bytes,4,opt,name=channel,proto3" json:"channel"`
	// 设备型号
	DeviceType string `protobuf:"bytes,5,opt,name=device_type,json=deviceType,proto3" json:"device_type"`
	// 存储总量
	RomGb float64 `protobuf:"fixed64,6,opt,name=rom_gb,json=romGb,proto3" json:"rom_gb"`
	// 存储剩余总量
	RemainRom float64 `protobuf:"fixed64,7,opt,name=remain_rom,json=remainRom,proto3" json:"remain_rom"`
	// 充值金额
	Recharge string `protobuf:"bytes,8,opt,name=recharge,proto3" json:"recharge"`
	// 玩家语言
	Language []string `protobuf:"bytes,9,rep,name=language,proto3" json:"language"`
	// 国家
	Country []string `protobuf:"bytes,10,rep,name=country,proto3" json:"country"`
	// 系统版本
	OsVersion            string   `protobuf:"bytes,11,opt,name=os_version,json=osVersion,proto3" json:"os_version"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *TicketPoolNewTopResp_UserInfoResp) Reset()         { *m = TicketPoolNewTopResp_UserInfoResp{} }
func (m *TicketPoolNewTopResp_UserInfoResp) String() string { return proto.CompactTextString(m) }
func (*TicketPoolNewTopResp_UserInfoResp) ProtoMessage()    {}
func (*TicketPoolNewTopResp_UserInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_98a6c21780e82d22, []int{8, 1}
}

func (m *TicketPoolNewTopResp_UserInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TicketPoolNewTopResp_UserInfoResp.Unmarshal(m, b)
}
func (m *TicketPoolNewTopResp_UserInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TicketPoolNewTopResp_UserInfoResp.Marshal(b, m, deterministic)
}
func (m *TicketPoolNewTopResp_UserInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TicketPoolNewTopResp_UserInfoResp.Merge(m, src)
}
func (m *TicketPoolNewTopResp_UserInfoResp) XXX_Size() int {
	return xxx_messageInfo_TicketPoolNewTopResp_UserInfoResp.Size(m)
}
func (m *TicketPoolNewTopResp_UserInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_TicketPoolNewTopResp_UserInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_TicketPoolNewTopResp_UserInfoResp proto.InternalMessageInfo

func (m *TicketPoolNewTopResp_UserInfoResp) GetLabel() []int32 {
	if m != nil {
		return m.Label
	}
	return nil
}

func (m *TicketPoolNewTopResp_UserInfoResp) GetCategory() string {
	if m != nil {
		return m.Category
	}
	return ""
}

func (m *TicketPoolNewTopResp_UserInfoResp) GetCreatedAt() string {
	if m != nil {
		return m.CreatedAt
	}
	return ""
}

func (m *TicketPoolNewTopResp_UserInfoResp) GetChannel() string {
	if m != nil {
		return m.Channel
	}
	return ""
}

func (m *TicketPoolNewTopResp_UserInfoResp) GetDeviceType() string {
	if m != nil {
		return m.DeviceType
	}
	return ""
}

func (m *TicketPoolNewTopResp_UserInfoResp) GetRomGb() float64 {
	if m != nil {
		return m.RomGb
	}
	return 0
}

func (m *TicketPoolNewTopResp_UserInfoResp) GetRemainRom() float64 {
	if m != nil {
		return m.RemainRom
	}
	return 0
}

func (m *TicketPoolNewTopResp_UserInfoResp) GetRecharge() string {
	if m != nil {
		return m.Recharge
	}
	return ""
}

func (m *TicketPoolNewTopResp_UserInfoResp) GetLanguage() []string {
	if m != nil {
		return m.Language
	}
	return nil
}

func (m *TicketPoolNewTopResp_UserInfoResp) GetCountry() []string {
	if m != nil {
		return m.Country
	}
	return nil
}

func (m *TicketPoolNewTopResp_UserInfoResp) GetOsVersion() string {
	if m != nil {
		return m.OsVersion
	}
	return ""
}

// TicketDialogueInfoResp 详情对话 todo  带时间戳
type TicketDialogueInfoResp struct {
	// 对话信息
	DialogueInformation []string `protobuf:"bytes,1,rep,name=dialogue_information,json=dialogueInformation,proto3" json:"dialogue_information"`
	// 客服备注消息
	Remarks []string `protobuf:"bytes,2,rep,name=remarks,proto3" json:"remarks"`
	// 标签
	Label                []int32  `protobuf:"varint,3,rep,packed,name=label,proto3" json:"label"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *TicketDialogueInfoResp) Reset()         { *m = TicketDialogueInfoResp{} }
func (m *TicketDialogueInfoResp) String() string { return proto.CompactTextString(m) }
func (*TicketDialogueInfoResp) ProtoMessage()    {}
func (*TicketDialogueInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_98a6c21780e82d22, []int{9}
}

func (m *TicketDialogueInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TicketDialogueInfoResp.Unmarshal(m, b)
}
func (m *TicketDialogueInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TicketDialogueInfoResp.Marshal(b, m, deterministic)
}
func (m *TicketDialogueInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TicketDialogueInfoResp.Merge(m, src)
}
func (m *TicketDialogueInfoResp) XXX_Size() int {
	return xxx_messageInfo_TicketDialogueInfoResp.Size(m)
}
func (m *TicketDialogueInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_TicketDialogueInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_TicketDialogueInfoResp proto.InternalMessageInfo

func (m *TicketDialogueInfoResp) GetDialogueInformation() []string {
	if m != nil {
		return m.DialogueInformation
	}
	return nil
}

func (m *TicketDialogueInfoResp) GetRemarks() []string {
	if m != nil {
		return m.Remarks
	}
	return nil
}

func (m *TicketDialogueInfoResp) GetLabel() []int32 {
	if m != nil {
		return m.Label
	}
	return nil
}

// TicketRecordResp 历史工单记录
type TicketRecordResp struct {
	CurrentPage          uint32                     `protobuf:"varint,1,opt,name=current_page,json=currentPage,proto3" json:"current_page"`
	PerPage              uint32                     `protobuf:"varint,2,opt,name=per_page,json=perPage,proto3" json:"per_page"`
	Total                uint32                     `protobuf:"varint,3,opt,name=total,proto3" json:"total"`
	Data                 []*TicketRecordResp_Record `protobuf:"bytes,4,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}                   `json:"-" gorm:"-"`
	XXX_unrecognized     []byte                     `json:"-" gorm:"-"`
	XXX_sizecache        int32                      `json:"-" gorm:"-"`
}

func (m *TicketRecordResp) Reset()         { *m = TicketRecordResp{} }
func (m *TicketRecordResp) String() string { return proto.CompactTextString(m) }
func (*TicketRecordResp) ProtoMessage()    {}
func (*TicketRecordResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_98a6c21780e82d22, []int{10}
}

func (m *TicketRecordResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TicketRecordResp.Unmarshal(m, b)
}
func (m *TicketRecordResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TicketRecordResp.Marshal(b, m, deterministic)
}
func (m *TicketRecordResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TicketRecordResp.Merge(m, src)
}
func (m *TicketRecordResp) XXX_Size() int {
	return xxx_messageInfo_TicketRecordResp.Size(m)
}
func (m *TicketRecordResp) XXX_DiscardUnknown() {
	xxx_messageInfo_TicketRecordResp.DiscardUnknown(m)
}

var xxx_messageInfo_TicketRecordResp proto.InternalMessageInfo

func (m *TicketRecordResp) GetCurrentPage() uint32 {
	if m != nil {
		return m.CurrentPage
	}
	return 0
}

func (m *TicketRecordResp) GetPerPage() uint32 {
	if m != nil {
		return m.PerPage
	}
	return 0
}

func (m *TicketRecordResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *TicketRecordResp) GetData() []*TicketRecordResp_Record {
	if m != nil {
		return m.Data
	}
	return nil
}

type TicketRecordResp_Record struct {
	// 游戏
	Project string `protobuf:"bytes,1,opt,name=project,proto3" json:"project"`
	// 工单ID
	TicketId uint64 `protobuf:"varint,2,opt,name=ticket_id,json=ticketId,proto3" json:"ticket_id"`
	// 玩家输入第一句话
	Detail string `protobuf:"bytes,3,opt,name=detail,proto3" json:"detail"`
	// 累计金额
	Recharge float64 `protobuf:"fixed64,4,opt,name=recharge,proto3" json:"recharge"`
	// 工单状态
	Status uint32 `protobuf:"varint,5,opt,name=status,proto3" json:"status"`
	// 等待时长
	WaitingTime          string   `protobuf:"bytes,6,opt,name=waiting_time,json=waitingTime,proto3" json:"waiting_time"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *TicketRecordResp_Record) Reset()         { *m = TicketRecordResp_Record{} }
func (m *TicketRecordResp_Record) String() string { return proto.CompactTextString(m) }
func (*TicketRecordResp_Record) ProtoMessage()    {}
func (*TicketRecordResp_Record) Descriptor() ([]byte, []int) {
	return fileDescriptor_98a6c21780e82d22, []int{10, 0}
}

func (m *TicketRecordResp_Record) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TicketRecordResp_Record.Unmarshal(m, b)
}
func (m *TicketRecordResp_Record) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TicketRecordResp_Record.Marshal(b, m, deterministic)
}
func (m *TicketRecordResp_Record) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TicketRecordResp_Record.Merge(m, src)
}
func (m *TicketRecordResp_Record) XXX_Size() int {
	return xxx_messageInfo_TicketRecordResp_Record.Size(m)
}
func (m *TicketRecordResp_Record) XXX_DiscardUnknown() {
	xxx_messageInfo_TicketRecordResp_Record.DiscardUnknown(m)
}

var xxx_messageInfo_TicketRecordResp_Record proto.InternalMessageInfo

func (m *TicketRecordResp_Record) GetProject() string {
	if m != nil {
		return m.Project
	}
	return ""
}

func (m *TicketRecordResp_Record) GetTicketId() uint64 {
	if m != nil {
		return m.TicketId
	}
	return 0
}

func (m *TicketRecordResp_Record) GetDetail() string {
	if m != nil {
		return m.Detail
	}
	return ""
}

func (m *TicketRecordResp_Record) GetRecharge() float64 {
	if m != nil {
		return m.Recharge
	}
	return 0
}

func (m *TicketRecordResp_Record) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *TicketRecordResp_Record) GetWaitingTime() string {
	if m != nil {
		return m.WaitingTime
	}
	return ""
}

// UserInfoResp 基础信息
type UserInfoResp struct {
	// 标签
	TagName []string `protobuf:"bytes,1,rep,name=tag_name,json=tagName,proto3" json:"tag_name"`
	// 问题类型
	Category string `protobuf:"bytes,2,opt,name=category,proto3" json:"category"`
	// 创建时间
	CreatedAt string `protobuf:"bytes,3,opt,name=created_at,json=createdAt,proto3" json:"created_at"`
	// 渠道包
	Channel string `protobuf:"bytes,4,opt,name=channel,proto3" json:"channel"`
	// 设备型号
	DeviceType string `protobuf:"bytes,5,opt,name=device_type,json=deviceType,proto3" json:"device_type"`
	// 存储总量
	RomGb float64 `protobuf:"fixed64,6,opt,name=rom_gb,json=romGb,proto3" json:"rom_gb"`
	// 存储剩余总量
	RemainRom float64 `protobuf:"fixed64,7,opt,name=remain_rom,json=remainRom,proto3" json:"remain_rom"`
	// 充值金额
	Recharge float64 `protobuf:"fixed64,8,opt,name=recharge,proto3" json:"recharge"`
	// 玩家语言
	Lang string `protobuf:"bytes,9,opt,name=lang,proto3" json:"lang"`
	// 国家
	Country string `protobuf:"bytes,10,opt,name=country,proto3" json:"country"`
	// 系统版本
	OsVersion string `protobuf:"bytes,11,opt,name=os_version,json=osVersion,proto3" json:"os_version"`
	// IP地址
	Ip string `protobuf:"bytes,12,opt,name=ip,proto3" json:"ip"`
	// 渠道号
	PackageId string `protobuf:"bytes,13,opt,name=packageId,proto3" json:"packageId"`
	// 私域R级
	ZoneVipLevel         string   `protobuf:"bytes,14,opt,name=zone_vip_level,json=zoneVipLevel,proto3" json:"zone_vip_level"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *UserInfoResp) Reset()         { *m = UserInfoResp{} }
func (m *UserInfoResp) String() string { return proto.CompactTextString(m) }
func (*UserInfoResp) ProtoMessage()    {}
func (*UserInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_98a6c21780e82d22, []int{11}
}

func (m *UserInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserInfoResp.Unmarshal(m, b)
}
func (m *UserInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserInfoResp.Marshal(b, m, deterministic)
}
func (m *UserInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserInfoResp.Merge(m, src)
}
func (m *UserInfoResp) XXX_Size() int {
	return xxx_messageInfo_UserInfoResp.Size(m)
}
func (m *UserInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UserInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_UserInfoResp proto.InternalMessageInfo

func (m *UserInfoResp) GetTagName() []string {
	if m != nil {
		return m.TagName
	}
	return nil
}

func (m *UserInfoResp) GetCategory() string {
	if m != nil {
		return m.Category
	}
	return ""
}

func (m *UserInfoResp) GetCreatedAt() string {
	if m != nil {
		return m.CreatedAt
	}
	return ""
}

func (m *UserInfoResp) GetChannel() string {
	if m != nil {
		return m.Channel
	}
	return ""
}

func (m *UserInfoResp) GetDeviceType() string {
	if m != nil {
		return m.DeviceType
	}
	return ""
}

func (m *UserInfoResp) GetRomGb() float64 {
	if m != nil {
		return m.RomGb
	}
	return 0
}

func (m *UserInfoResp) GetRemainRom() float64 {
	if m != nil {
		return m.RemainRom
	}
	return 0
}

func (m *UserInfoResp) GetRecharge() float64 {
	if m != nil {
		return m.Recharge
	}
	return 0
}

func (m *UserInfoResp) GetLang() string {
	if m != nil {
		return m.Lang
	}
	return ""
}

func (m *UserInfoResp) GetCountry() string {
	if m != nil {
		return m.Country
	}
	return ""
}

func (m *UserInfoResp) GetOsVersion() string {
	if m != nil {
		return m.OsVersion
	}
	return ""
}

func (m *UserInfoResp) GetIp() string {
	if m != nil {
		return m.Ip
	}
	return ""
}

func (m *UserInfoResp) GetPackageId() string {
	if m != nil {
		return m.PackageId
	}
	return ""
}

func (m *UserInfoResp) GetZoneVipLevel() string {
	if m != nil {
		return m.ZoneVipLevel
	}
	return ""
}

// TicketAppraiseResp 工单评价信息
type TicketAppraiseResp struct {
	CreatedAt            string   `protobuf:"bytes,1,opt,name=created_at,json=createdAt,proto3" json:"created_at"`
	Csi                  uint32   `protobuf:"varint,2,opt,name=csi,proto3" json:"csi"`
	RecommendationLevel  uint32   `protobuf:"varint,3,opt,name=recommendation_level,json=recommendationLevel,proto3" json:"recommendation_level"`
	Remark               string   `protobuf:"bytes,4,opt,name=remark,proto3" json:"remark"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *TicketAppraiseResp) Reset()         { *m = TicketAppraiseResp{} }
func (m *TicketAppraiseResp) String() string { return proto.CompactTextString(m) }
func (*TicketAppraiseResp) ProtoMessage()    {}
func (*TicketAppraiseResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_98a6c21780e82d22, []int{12}
}

func (m *TicketAppraiseResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TicketAppraiseResp.Unmarshal(m, b)
}
func (m *TicketAppraiseResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TicketAppraiseResp.Marshal(b, m, deterministic)
}
func (m *TicketAppraiseResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TicketAppraiseResp.Merge(m, src)
}
func (m *TicketAppraiseResp) XXX_Size() int {
	return xxx_messageInfo_TicketAppraiseResp.Size(m)
}
func (m *TicketAppraiseResp) XXX_DiscardUnknown() {
	xxx_messageInfo_TicketAppraiseResp.DiscardUnknown(m)
}

var xxx_messageInfo_TicketAppraiseResp proto.InternalMessageInfo

func (m *TicketAppraiseResp) GetCreatedAt() string {
	if m != nil {
		return m.CreatedAt
	}
	return ""
}

func (m *TicketAppraiseResp) GetCsi() uint32 {
	if m != nil {
		return m.Csi
	}
	return 0
}

func (m *TicketAppraiseResp) GetRecommendationLevel() uint32 {
	if m != nil {
		return m.RecommendationLevel
	}
	return 0
}

func (m *TicketAppraiseResp) GetRemark() string {
	if m != nil {
		return m.Remark
	}
	return ""
}

// 工单详情
type TicketResponse struct {
	// 工单详情 - 顶部信息
	TopInfo *TicketPoolTopResp `protobuf:"bytes,1,opt,name=top_info,json=topInfo,proto3" json:"top_info"`
	// 工单详情 - 基本信息
	UserInfo *UserInfoResp `protobuf:"bytes,2,opt,name=user_info,json=userInfo,proto3" json:"user_info"`
	// 工单详情 - 历史工单
	RecordInfo *TicketRecordResp `protobuf:"bytes,3,opt,name=record_info,json=recordInfo,proto3" json:"record_info"`
	// 工单详情 - 对话
	CommuInfo []*TicketDialogueResp `protobuf:"bytes,4,rep,name=commu_info,json=commuInfo,proto3" json:"commu_info"`
	// 工单详情 - 分单日志
	History              []*TicketHistResp   `protobuf:"bytes,5,rep,name=history,proto3" json:"history"`
	TicketAppraise       *TicketAppraiseResp `protobuf:"bytes,6,opt,name=ticket_appraise,json=ticketAppraise,proto3" json:"ticket_appraise"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-" gorm:"-"`
	XXX_unrecognized     []byte              `json:"-" gorm:"-"`
	XXX_sizecache        int32               `json:"-" gorm:"-"`
}

func (m *TicketResponse) Reset()         { *m = TicketResponse{} }
func (m *TicketResponse) String() string { return proto.CompactTextString(m) }
func (*TicketResponse) ProtoMessage()    {}
func (*TicketResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_98a6c21780e82d22, []int{13}
}

func (m *TicketResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TicketResponse.Unmarshal(m, b)
}
func (m *TicketResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TicketResponse.Marshal(b, m, deterministic)
}
func (m *TicketResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TicketResponse.Merge(m, src)
}
func (m *TicketResponse) XXX_Size() int {
	return xxx_messageInfo_TicketResponse.Size(m)
}
func (m *TicketResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_TicketResponse.DiscardUnknown(m)
}

var xxx_messageInfo_TicketResponse proto.InternalMessageInfo

func (m *TicketResponse) GetTopInfo() *TicketPoolTopResp {
	if m != nil {
		return m.TopInfo
	}
	return nil
}

func (m *TicketResponse) GetUserInfo() *UserInfoResp {
	if m != nil {
		return m.UserInfo
	}
	return nil
}

func (m *TicketResponse) GetRecordInfo() *TicketRecordResp {
	if m != nil {
		return m.RecordInfo
	}
	return nil
}

func (m *TicketResponse) GetCommuInfo() []*TicketDialogueResp {
	if m != nil {
		return m.CommuInfo
	}
	return nil
}

func (m *TicketResponse) GetHistory() []*TicketHistResp {
	if m != nil {
		return m.History
	}
	return nil
}

func (m *TicketResponse) GetTicketAppraise() *TicketAppraiseResp {
	if m != nil {
		return m.TicketAppraise
	}
	return nil
}

// AssignmentReq 指派请求参数
type AssignmentReq struct {
	// 工单ID @gotags: validate:"required"
	TicketId uint64 `protobuf:"varint,1,opt,name=ticket_id,json=ticketId,proto3" json:"ticket_id" validate:"required"`
	// 其他客服 @gotags: validate:"required"
	Acceptor             string   `protobuf:"bytes,2,opt,name=acceptor,proto3" json:"acceptor" validate:"required"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *AssignmentReq) Reset()         { *m = AssignmentReq{} }
func (m *AssignmentReq) String() string { return proto.CompactTextString(m) }
func (*AssignmentReq) ProtoMessage()    {}
func (*AssignmentReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_98a6c21780e82d22, []int{14}
}

func (m *AssignmentReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AssignmentReq.Unmarshal(m, b)
}
func (m *AssignmentReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AssignmentReq.Marshal(b, m, deterministic)
}
func (m *AssignmentReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AssignmentReq.Merge(m, src)
}
func (m *AssignmentReq) XXX_Size() int {
	return xxx_messageInfo_AssignmentReq.Size(m)
}
func (m *AssignmentReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AssignmentReq.DiscardUnknown(m)
}

var xxx_messageInfo_AssignmentReq proto.InternalMessageInfo

func (m *AssignmentReq) GetTicketId() uint64 {
	if m != nil {
		return m.TicketId
	}
	return 0
}

func (m *AssignmentReq) GetAcceptor() string {
	if m != nil {
		return m.Acceptor
	}
	return ""
}

// AssignmentReq 批量指派请求参数
type BatchAssignmentReq struct {
	// 工单ID @gotags: validate:"required"
	TicketId []uint64 `protobuf:"varint,1,rep,packed,name=ticket_id,json=ticketId,proto3" json:"ticket_id" validate:"required"`
	// 其他客服 @gotags: validate:"required"
	Acceptor             string   `protobuf:"bytes,2,opt,name=acceptor,proto3" json:"acceptor" validate:"required"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *BatchAssignmentReq) Reset()         { *m = BatchAssignmentReq{} }
func (m *BatchAssignmentReq) String() string { return proto.CompactTextString(m) }
func (*BatchAssignmentReq) ProtoMessage()    {}
func (*BatchAssignmentReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_98a6c21780e82d22, []int{15}
}

func (m *BatchAssignmentReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BatchAssignmentReq.Unmarshal(m, b)
}
func (m *BatchAssignmentReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BatchAssignmentReq.Marshal(b, m, deterministic)
}
func (m *BatchAssignmentReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BatchAssignmentReq.Merge(m, src)
}
func (m *BatchAssignmentReq) XXX_Size() int {
	return xxx_messageInfo_BatchAssignmentReq.Size(m)
}
func (m *BatchAssignmentReq) XXX_DiscardUnknown() {
	xxx_messageInfo_BatchAssignmentReq.DiscardUnknown(m)
}

var xxx_messageInfo_BatchAssignmentReq proto.InternalMessageInfo

func (m *BatchAssignmentReq) GetTicketId() []uint64 {
	if m != nil {
		return m.TicketId
	}
	return nil
}

func (m *BatchAssignmentReq) GetAcceptor() string {
	if m != nil {
		return m.Acceptor
	}
	return ""
}

// TicketRemarkReq 添加工单备注
type TicketRemarkReq struct {
	// 工单ID @gotags: validate:"required,gte=1"
	TicketId uint64 `protobuf:"varint,1,opt,name=ticket_id,json=ticketId,proto3" json:"ticket_id" validate:"required,gte=1"`
	// 备注 @gotags: validate:"required"
	Content              string   `protobuf:"bytes,2,opt,name=content,proto3" json:"content" validate:"required"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *TicketRemarkReq) Reset()         { *m = TicketRemarkReq{} }
func (m *TicketRemarkReq) String() string { return proto.CompactTextString(m) }
func (*TicketRemarkReq) ProtoMessage()    {}
func (*TicketRemarkReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_98a6c21780e82d22, []int{16}
}

func (m *TicketRemarkReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TicketRemarkReq.Unmarshal(m, b)
}
func (m *TicketRemarkReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TicketRemarkReq.Marshal(b, m, deterministic)
}
func (m *TicketRemarkReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TicketRemarkReq.Merge(m, src)
}
func (m *TicketRemarkReq) XXX_Size() int {
	return xxx_messageInfo_TicketRemarkReq.Size(m)
}
func (m *TicketRemarkReq) XXX_DiscardUnknown() {
	xxx_messageInfo_TicketRemarkReq.DiscardUnknown(m)
}

var xxx_messageInfo_TicketRemarkReq proto.InternalMessageInfo

func (m *TicketRemarkReq) GetTicketId() uint64 {
	if m != nil {
		return m.TicketId
	}
	return 0
}

func (m *TicketRemarkReq) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

// TicketBatchRemarkReq 批量
type TicketBatchRemarkReq struct {
	// 工单ID @gotags: validate:"required"
	TicketIds []uint64 `protobuf:"varint,1,rep,packed,name=ticket_ids,json=ticketIds,proto3" json:"ticket_ids" validate:"required"`
	// 备注 @gotags: validate:"required"
	Content              string   `protobuf:"bytes,2,opt,name=content,proto3" json:"content" validate:"required"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *TicketBatchRemarkReq) Reset()         { *m = TicketBatchRemarkReq{} }
func (m *TicketBatchRemarkReq) String() string { return proto.CompactTextString(m) }
func (*TicketBatchRemarkReq) ProtoMessage()    {}
func (*TicketBatchRemarkReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_98a6c21780e82d22, []int{17}
}

func (m *TicketBatchRemarkReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TicketBatchRemarkReq.Unmarshal(m, b)
}
func (m *TicketBatchRemarkReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TicketBatchRemarkReq.Marshal(b, m, deterministic)
}
func (m *TicketBatchRemarkReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TicketBatchRemarkReq.Merge(m, src)
}
func (m *TicketBatchRemarkReq) XXX_Size() int {
	return xxx_messageInfo_TicketBatchRemarkReq.Size(m)
}
func (m *TicketBatchRemarkReq) XXX_DiscardUnknown() {
	xxx_messageInfo_TicketBatchRemarkReq.DiscardUnknown(m)
}

var xxx_messageInfo_TicketBatchRemarkReq proto.InternalMessageInfo

func (m *TicketBatchRemarkReq) GetTicketIds() []uint64 {
	if m != nil {
		return m.TicketIds
	}
	return nil
}

func (m *TicketBatchRemarkReq) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

// TicketTagReq 重新给现有的工单打标签
type TicketRetaggingReq struct {
	// 工单ID @gotags: validate:"required"
	TicketId uint64 `protobuf:"varint,1,opt,name=ticket_id,json=ticketId,proto3" json:"ticket_id" validate:"required"`
	// 更新后的标签ID
	LabelId              []uint32 `protobuf:"varint,2,rep,packed,name=label_id,json=labelId,proto3" json:"label_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *TicketRetaggingReq) Reset()         { *m = TicketRetaggingReq{} }
func (m *TicketRetaggingReq) String() string { return proto.CompactTextString(m) }
func (*TicketRetaggingReq) ProtoMessage()    {}
func (*TicketRetaggingReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_98a6c21780e82d22, []int{18}
}

func (m *TicketRetaggingReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TicketRetaggingReq.Unmarshal(m, b)
}
func (m *TicketRetaggingReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TicketRetaggingReq.Marshal(b, m, deterministic)
}
func (m *TicketRetaggingReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TicketRetaggingReq.Merge(m, src)
}
func (m *TicketRetaggingReq) XXX_Size() int {
	return xxx_messageInfo_TicketRetaggingReq.Size(m)
}
func (m *TicketRetaggingReq) XXX_DiscardUnknown() {
	xxx_messageInfo_TicketRetaggingReq.DiscardUnknown(m)
}

var xxx_messageInfo_TicketRetaggingReq proto.InternalMessageInfo

func (m *TicketRetaggingReq) GetTicketId() uint64 {
	if m != nil {
		return m.TicketId
	}
	return 0
}

func (m *TicketRetaggingReq) GetLabelId() []uint32 {
	if m != nil {
		return m.LabelId
	}
	return nil
}

type TicketTagRes struct {
	TicketId             uint64   `protobuf:"varint,1,opt,name=ticket_id,json=ticketId,proto3" json:"ticket_id"`
	LabelId              []uint32 `protobuf:"varint,2,rep,packed,name=label_id,json=labelId,proto3" json:"label_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *TicketTagRes) Reset()         { *m = TicketTagRes{} }
func (m *TicketTagRes) String() string { return proto.CompactTextString(m) }
func (*TicketTagRes) ProtoMessage()    {}
func (*TicketTagRes) Descriptor() ([]byte, []int) {
	return fileDescriptor_98a6c21780e82d22, []int{19}
}

func (m *TicketTagRes) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TicketTagRes.Unmarshal(m, b)
}
func (m *TicketTagRes) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TicketTagRes.Marshal(b, m, deterministic)
}
func (m *TicketTagRes) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TicketTagRes.Merge(m, src)
}
func (m *TicketTagRes) XXX_Size() int {
	return xxx_messageInfo_TicketTagRes.Size(m)
}
func (m *TicketTagRes) XXX_DiscardUnknown() {
	xxx_messageInfo_TicketTagRes.DiscardUnknown(m)
}

var xxx_messageInfo_TicketTagRes proto.InternalMessageInfo

func (m *TicketTagRes) GetTicketId() uint64 {
	if m != nil {
		return m.TicketId
	}
	return 0
}

func (m *TicketTagRes) GetLabelId() []uint32 {
	if m != nil {
		return m.LabelId
	}
	return nil
}

type TicketLabel struct {
	// 工单ID @gotags: validate:"required"
	TicketId uint64 `protobuf:"varint,1,opt,name=ticket_id,json=ticketId,proto3" json:"ticket_id" validate:"required"`
	// 删除的标签ID
	LabelId              []uint32 `protobuf:"varint,2,rep,packed,name=label_id,json=labelId,proto3" json:"label_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *TicketLabel) Reset()         { *m = TicketLabel{} }
func (m *TicketLabel) String() string { return proto.CompactTextString(m) }
func (*TicketLabel) ProtoMessage()    {}
func (*TicketLabel) Descriptor() ([]byte, []int) {
	return fileDescriptor_98a6c21780e82d22, []int{20}
}

func (m *TicketLabel) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TicketLabel.Unmarshal(m, b)
}
func (m *TicketLabel) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TicketLabel.Marshal(b, m, deterministic)
}
func (m *TicketLabel) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TicketLabel.Merge(m, src)
}
func (m *TicketLabel) XXX_Size() int {
	return xxx_messageInfo_TicketLabel.Size(m)
}
func (m *TicketLabel) XXX_DiscardUnknown() {
	xxx_messageInfo_TicketLabel.DiscardUnknown(m)
}

var xxx_messageInfo_TicketLabel proto.InternalMessageInfo

func (m *TicketLabel) GetTicketId() uint64 {
	if m != nil {
		return m.TicketId
	}
	return 0
}

func (m *TicketLabel) GetLabelId() []uint32 {
	if m != nil {
		return m.LabelId
	}
	return nil
}

// 获取多个工单绑定的 label
type TicketTagsReq struct {
	// 工单ID @gotags: validate:"required,gt=0"
	TicketId             []uint64 `protobuf:"varint,1,rep,packed,name=ticket_id,json=ticketId,proto3" json:"ticket_id" validate:"required,gt=0"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *TicketTagsReq) Reset()         { *m = TicketTagsReq{} }
func (m *TicketTagsReq) String() string { return proto.CompactTextString(m) }
func (*TicketTagsReq) ProtoMessage()    {}
func (*TicketTagsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_98a6c21780e82d22, []int{21}
}

func (m *TicketTagsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TicketTagsReq.Unmarshal(m, b)
}
func (m *TicketTagsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TicketTagsReq.Marshal(b, m, deterministic)
}
func (m *TicketTagsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TicketTagsReq.Merge(m, src)
}
func (m *TicketTagsReq) XXX_Size() int {
	return xxx_messageInfo_TicketTagsReq.Size(m)
}
func (m *TicketTagsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_TicketTagsReq.DiscardUnknown(m)
}

var xxx_messageInfo_TicketTagsReq proto.InternalMessageInfo

func (m *TicketTagsReq) GetTicketId() []uint64 {
	if m != nil {
		return m.TicketId
	}
	return nil
}

// 获取多个工单绑定的 label 相应详情
type TicketTagsResp struct {
	// 工单绑定关系
	Details              []*TicketLabel `protobuf:"bytes,1,rep,name=details,proto3" json:"details"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-" gorm:"-"`
	XXX_unrecognized     []byte         `json:"-" gorm:"-"`
	XXX_sizecache        int32          `json:"-" gorm:"-"`
}

func (m *TicketTagsResp) Reset()         { *m = TicketTagsResp{} }
func (m *TicketTagsResp) String() string { return proto.CompactTextString(m) }
func (*TicketTagsResp) ProtoMessage()    {}
func (*TicketTagsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_98a6c21780e82d22, []int{22}
}

func (m *TicketTagsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TicketTagsResp.Unmarshal(m, b)
}
func (m *TicketTagsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TicketTagsResp.Marshal(b, m, deterministic)
}
func (m *TicketTagsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TicketTagsResp.Merge(m, src)
}
func (m *TicketTagsResp) XXX_Size() int {
	return xxx_messageInfo_TicketTagsResp.Size(m)
}
func (m *TicketTagsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_TicketTagsResp.DiscardUnknown(m)
}

var xxx_messageInfo_TicketTagsResp proto.InternalMessageInfo

func (m *TicketTagsResp) GetDetails() []*TicketLabel {
	if m != nil {
		return m.Details
	}
	return nil
}

// TicketHistoryReq 工单日志请求
type TicketHistoriesReq struct {
	// 工单ID @gotags: validate:"required"
	TicketId uint64 `protobuf:"varint,1,opt,name=ticket_id,json=ticketId,proto3" json:"ticket_id" validate:"required"`
	// 关联类型  1:工单 2:流程
	RelateType           uint32   `protobuf:"varint,2,opt,name=relate_type,json=relateType,proto3" json:"relate_type"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *TicketHistoriesReq) Reset()         { *m = TicketHistoriesReq{} }
func (m *TicketHistoriesReq) String() string { return proto.CompactTextString(m) }
func (*TicketHistoriesReq) ProtoMessage()    {}
func (*TicketHistoriesReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_98a6c21780e82d22, []int{23}
}

func (m *TicketHistoriesReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TicketHistoriesReq.Unmarshal(m, b)
}
func (m *TicketHistoriesReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TicketHistoriesReq.Marshal(b, m, deterministic)
}
func (m *TicketHistoriesReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TicketHistoriesReq.Merge(m, src)
}
func (m *TicketHistoriesReq) XXX_Size() int {
	return xxx_messageInfo_TicketHistoriesReq.Size(m)
}
func (m *TicketHistoriesReq) XXX_DiscardUnknown() {
	xxx_messageInfo_TicketHistoriesReq.DiscardUnknown(m)
}

var xxx_messageInfo_TicketHistoriesReq proto.InternalMessageInfo

func (m *TicketHistoriesReq) GetTicketId() uint64 {
	if m != nil {
		return m.TicketId
	}
	return 0
}

func (m *TicketHistoriesReq) GetRelateType() uint32 {
	if m != nil {
		return m.RelateType
	}
	return 0
}

// TicketIsUserRsq 客服在线状态
type TicketIsUserReq struct {
	// 客服工号
	Empno                uint32   `protobuf:"varint,1,opt,name=empno,proto3" json:"empno"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *TicketIsUserReq) Reset()         { *m = TicketIsUserReq{} }
func (m *TicketIsUserReq) String() string { return proto.CompactTextString(m) }
func (*TicketIsUserReq) ProtoMessage()    {}
func (*TicketIsUserReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_98a6c21780e82d22, []int{24}
}

func (m *TicketIsUserReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TicketIsUserReq.Unmarshal(m, b)
}
func (m *TicketIsUserReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TicketIsUserReq.Marshal(b, m, deterministic)
}
func (m *TicketIsUserReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TicketIsUserReq.Merge(m, src)
}
func (m *TicketIsUserReq) XXX_Size() int {
	return xxx_messageInfo_TicketIsUserReq.Size(m)
}
func (m *TicketIsUserReq) XXX_DiscardUnknown() {
	xxx_messageInfo_TicketIsUserReq.DiscardUnknown(m)
}

var xxx_messageInfo_TicketIsUserReq proto.InternalMessageInfo

func (m *TicketIsUserReq) GetEmpno() uint32 {
	if m != nil {
		return m.Empno
	}
	return 0
}

// TicketIsUserResp 客服在线状态
type TicketIsUserResp struct {
	// 客服在线状态  0:不在线 1:在线
	Status               uint32   `protobuf:"varint,1,opt,name=status,proto3" json:"status"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *TicketIsUserResp) Reset()         { *m = TicketIsUserResp{} }
func (m *TicketIsUserResp) String() string { return proto.CompactTextString(m) }
func (*TicketIsUserResp) ProtoMessage()    {}
func (*TicketIsUserResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_98a6c21780e82d22, []int{25}
}

func (m *TicketIsUserResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TicketIsUserResp.Unmarshal(m, b)
}
func (m *TicketIsUserResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TicketIsUserResp.Marshal(b, m, deterministic)
}
func (m *TicketIsUserResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TicketIsUserResp.Merge(m, src)
}
func (m *TicketIsUserResp) XXX_Size() int {
	return xxx_messageInfo_TicketIsUserResp.Size(m)
}
func (m *TicketIsUserResp) XXX_DiscardUnknown() {
	xxx_messageInfo_TicketIsUserResp.DiscardUnknown(m)
}

var xxx_messageInfo_TicketIsUserResp proto.InternalMessageInfo

func (m *TicketIsUserResp) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

// TicketHistoriesResp 工单日志列表
type TicketHistoriesResp struct {
	// 操作日志
	Data                 []*TicketHistoryInfo `protobuf:"bytes,1,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-" gorm:"-"`
	XXX_unrecognized     []byte               `json:"-" gorm:"-"`
	XXX_sizecache        int32                `json:"-" gorm:"-"`
}

func (m *TicketHistoriesResp) Reset()         { *m = TicketHistoriesResp{} }
func (m *TicketHistoriesResp) String() string { return proto.CompactTextString(m) }
func (*TicketHistoriesResp) ProtoMessage()    {}
func (*TicketHistoriesResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_98a6c21780e82d22, []int{26}
}

func (m *TicketHistoriesResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TicketHistoriesResp.Unmarshal(m, b)
}
func (m *TicketHistoriesResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TicketHistoriesResp.Marshal(b, m, deterministic)
}
func (m *TicketHistoriesResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TicketHistoriesResp.Merge(m, src)
}
func (m *TicketHistoriesResp) XXX_Size() int {
	return xxx_messageInfo_TicketHistoriesResp.Size(m)
}
func (m *TicketHistoriesResp) XXX_DiscardUnknown() {
	xxx_messageInfo_TicketHistoriesResp.DiscardUnknown(m)
}

var xxx_messageInfo_TicketHistoriesResp proto.InternalMessageInfo

func (m *TicketHistoriesResp) GetData() []*TicketHistoryInfo {
	if m != nil {
		return m.Data
	}
	return nil
}

// TicketHistoryInfo 工单日志信息
type TicketHistoryInfo struct {
	// 工单ID
	TicketId uint64 `protobuf:"varint,1,opt,name=ticket_id,json=ticketId,proto3" json:"ticket_id"`
	// 操作
	Operate string `protobuf:"bytes,2,opt,name=operate,proto3" json:"operate"`
	// 子操作
	OpDetail string `protobuf:"bytes,3,opt,name=op_detail,json=opDetail,proto3" json:"op_detail"`
	// 备注
	Remark string `protobuf:"bytes,4,opt,name=remark,proto3" json:"remark"`
	// 创建时间
	CreatedAt string `protobuf:"bytes,5,opt,name=created_at,json=createdAt,proto3" json:"created_at"`
	// 旧工单ID @gotags: json:"from_ticket_id,omitempty"
	FromTicketId         uint64   `protobuf:"varint,6,opt,name=from_ticket_id,json=fromTicketId,proto3" json:"from_ticket_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *TicketHistoryInfo) Reset()         { *m = TicketHistoryInfo{} }
func (m *TicketHistoryInfo) String() string { return proto.CompactTextString(m) }
func (*TicketHistoryInfo) ProtoMessage()    {}
func (*TicketHistoryInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_98a6c21780e82d22, []int{27}
}

func (m *TicketHistoryInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TicketHistoryInfo.Unmarshal(m, b)
}
func (m *TicketHistoryInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TicketHistoryInfo.Marshal(b, m, deterministic)
}
func (m *TicketHistoryInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TicketHistoryInfo.Merge(m, src)
}
func (m *TicketHistoryInfo) XXX_Size() int {
	return xxx_messageInfo_TicketHistoryInfo.Size(m)
}
func (m *TicketHistoryInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_TicketHistoryInfo.DiscardUnknown(m)
}

var xxx_messageInfo_TicketHistoryInfo proto.InternalMessageInfo

func (m *TicketHistoryInfo) GetTicketId() uint64 {
	if m != nil {
		return m.TicketId
	}
	return 0
}

func (m *TicketHistoryInfo) GetOperate() string {
	if m != nil {
		return m.Operate
	}
	return ""
}

func (m *TicketHistoryInfo) GetOpDetail() string {
	if m != nil {
		return m.OpDetail
	}
	return ""
}

func (m *TicketHistoryInfo) GetRemark() string {
	if m != nil {
		return m.Remark
	}
	return ""
}

func (m *TicketHistoryInfo) GetCreatedAt() string {
	if m != nil {
		return m.CreatedAt
	}
	return ""
}

func (m *TicketHistoryInfo) GetFromTicketId() uint64 {
	if m != nil {
		return m.FromTicketId
	}
	return 0
}

// 工单表单 对话信息
type TicketDialogueResp struct {
	// 对话内容 、工单 form
	Detail    string `protobuf:"bytes,1,opt,name=detail,proto3" json:"detail"`
	CreatedAt string `protobuf:"bytes,2,opt,name=created_at,json=createdAt,proto3" json:"created_at"`
	// 操作员
	Operator string `protobuf:"bytes,3,opt,name=operator,proto3" json:"operator"`
	//回复角色类型  1:玩家 2:客服 3:系统',
	FromRole uint32 `protobuf:"varint,4,opt,name=from_role,json=fromRole,proto3" json:"from_role"`
	// '消息类型： CommuTypeDialogue：对话消息；CommuTypeRemark：增加备注',
	CommuType string `protobuf:"bytes,5,opt,name=commu_type,json=commuType,proto3" json:"commu_type"`
	// 工单详情 1:是工单form详情; 2: 重开提交信息
	IsTicket uint32 `protobuf:"varint,6,opt,name=is_ticket,json=isTicket,proto3" json:"is_ticket"`
	// 重开单 - 图片/视频资源
	Files string `protobuf:"bytes,7,opt,name=files,proto3" json:"files"`
	// 时间 - 时间戳
	CreatedTime uint64 `protobuf:"varint,8,opt,name=created_time,json=createdTime,proto3" json:"created_time"`
	// 玩家上传的图片
	Picture string `protobuf:"bytes,9,opt,name=picture,proto3" json:"picture"`
	// 玩家上传的视频
	Video                string   `protobuf:"bytes,10,opt,name=video,proto3" json:"video"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *TicketDialogueResp) Reset()         { *m = TicketDialogueResp{} }
func (m *TicketDialogueResp) String() string { return proto.CompactTextString(m) }
func (*TicketDialogueResp) ProtoMessage()    {}
func (*TicketDialogueResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_98a6c21780e82d22, []int{28}
}

func (m *TicketDialogueResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TicketDialogueResp.Unmarshal(m, b)
}
func (m *TicketDialogueResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TicketDialogueResp.Marshal(b, m, deterministic)
}
func (m *TicketDialogueResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TicketDialogueResp.Merge(m, src)
}
func (m *TicketDialogueResp) XXX_Size() int {
	return xxx_messageInfo_TicketDialogueResp.Size(m)
}
func (m *TicketDialogueResp) XXX_DiscardUnknown() {
	xxx_messageInfo_TicketDialogueResp.DiscardUnknown(m)
}

var xxx_messageInfo_TicketDialogueResp proto.InternalMessageInfo

func (m *TicketDialogueResp) GetDetail() string {
	if m != nil {
		return m.Detail
	}
	return ""
}

func (m *TicketDialogueResp) GetCreatedAt() string {
	if m != nil {
		return m.CreatedAt
	}
	return ""
}

func (m *TicketDialogueResp) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

func (m *TicketDialogueResp) GetFromRole() uint32 {
	if m != nil {
		return m.FromRole
	}
	return 0
}

func (m *TicketDialogueResp) GetCommuType() string {
	if m != nil {
		return m.CommuType
	}
	return ""
}

func (m *TicketDialogueResp) GetIsTicket() uint32 {
	if m != nil {
		return m.IsTicket
	}
	return 0
}

func (m *TicketDialogueResp) GetFiles() string {
	if m != nil {
		return m.Files
	}
	return ""
}

func (m *TicketDialogueResp) GetCreatedTime() uint64 {
	if m != nil {
		return m.CreatedTime
	}
	return 0
}

func (m *TicketDialogueResp) GetPicture() string {
	if m != nil {
		return m.Picture
	}
	return ""
}

func (m *TicketDialogueResp) GetVideo() string {
	if m != nil {
		return m.Video
	}
	return ""
}

// 工单 - 日志记录详情
type TicketHistResp struct {
	// 工单id
	TicketId uint64 `protobuf:"varint,1,opt,name=ticket_id,json=ticketId,proto3" json:"ticket_id"`
	// 分组描述
	Type string `protobuf:"bytes,2,opt,name=type,proto3" json:"type"`
	// 详情
	Remark string `protobuf:"bytes,3,opt,name=remark,proto3" json:"remark"`
	// 时间
	CreatedAt            string   `protobuf:"bytes,4,opt,name=created_at,json=createdAt,proto3" json:"created_at"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *TicketHistResp) Reset()         { *m = TicketHistResp{} }
func (m *TicketHistResp) String() string { return proto.CompactTextString(m) }
func (*TicketHistResp) ProtoMessage()    {}
func (*TicketHistResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_98a6c21780e82d22, []int{29}
}

func (m *TicketHistResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TicketHistResp.Unmarshal(m, b)
}
func (m *TicketHistResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TicketHistResp.Marshal(b, m, deterministic)
}
func (m *TicketHistResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TicketHistResp.Merge(m, src)
}
func (m *TicketHistResp) XXX_Size() int {
	return xxx_messageInfo_TicketHistResp.Size(m)
}
func (m *TicketHistResp) XXX_DiscardUnknown() {
	xxx_messageInfo_TicketHistResp.DiscardUnknown(m)
}

var xxx_messageInfo_TicketHistResp proto.InternalMessageInfo

func (m *TicketHistResp) GetTicketId() uint64 {
	if m != nil {
		return m.TicketId
	}
	return 0
}

func (m *TicketHistResp) GetType() string {
	if m != nil {
		return m.Type
	}
	return ""
}

func (m *TicketHistResp) GetRemark() string {
	if m != nil {
		return m.Remark
	}
	return ""
}

func (m *TicketHistResp) GetCreatedAt() string {
	if m != nil {
		return m.CreatedAt
	}
	return ""
}

// TicketHistoryInfo 分单日志信息
type TicketGroupInfoResp struct {
	// 工单ID
	TicketId uint64 `protobuf:"varint,1,opt,name=ticket_id,json=ticketId,proto3" json:"ticket_id"`
	// 操作
	Operate string `protobuf:"bytes,2,opt,name=operate,proto3" json:"operate"`
	// 子操作
	OpDetail string `protobuf:"bytes,3,opt,name=op_detail,json=opDetail,proto3" json:"op_detail"`
	// 备注
	Remark string `protobuf:"bytes,4,opt,name=remark,proto3" json:"remark"`
	// 创建时间
	CreatedAt string `protobuf:"bytes,5,opt,name=created_at,json=createdAt,proto3" json:"created_at"`
	// 旧工单ID @gotags: json:"from_ticket_id,omitempty"
	FromTicketId         uint64   `protobuf:"varint,6,opt,name=from_ticket_id,json=fromTicketId,proto3" json:"from_ticket_id,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *TicketGroupInfoResp) Reset()         { *m = TicketGroupInfoResp{} }
func (m *TicketGroupInfoResp) String() string { return proto.CompactTextString(m) }
func (*TicketGroupInfoResp) ProtoMessage()    {}
func (*TicketGroupInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_98a6c21780e82d22, []int{30}
}

func (m *TicketGroupInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TicketGroupInfoResp.Unmarshal(m, b)
}
func (m *TicketGroupInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TicketGroupInfoResp.Marshal(b, m, deterministic)
}
func (m *TicketGroupInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TicketGroupInfoResp.Merge(m, src)
}
func (m *TicketGroupInfoResp) XXX_Size() int {
	return xxx_messageInfo_TicketGroupInfoResp.Size(m)
}
func (m *TicketGroupInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_TicketGroupInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_TicketGroupInfoResp proto.InternalMessageInfo

func (m *TicketGroupInfoResp) GetTicketId() uint64 {
	if m != nil {
		return m.TicketId
	}
	return 0
}

func (m *TicketGroupInfoResp) GetOperate() string {
	if m != nil {
		return m.Operate
	}
	return ""
}

func (m *TicketGroupInfoResp) GetOpDetail() string {
	if m != nil {
		return m.OpDetail
	}
	return ""
}

func (m *TicketGroupInfoResp) GetRemark() string {
	if m != nil {
		return m.Remark
	}
	return ""
}

func (m *TicketGroupInfoResp) GetCreatedAt() string {
	if m != nil {
		return m.CreatedAt
	}
	return ""
}

func (m *TicketGroupInfoResp) GetFromTicketId() uint64 {
	if m != nil {
		return m.FromTicketId
	}
	return 0
}

// TicketStatusReq 工单状态
type TicketStatusResp struct {
	// 工单状态 1,待接单  2,待处理 3,处理中-待玩家回复 4,处理中-玩家已回复 5,超时关闭 6,重开 7,拒单关闭 8,已完成
	Status               uint32   `protobuf:"varint,1,opt,name=status,proto3" json:"status"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *TicketStatusResp) Reset()         { *m = TicketStatusResp{} }
func (m *TicketStatusResp) String() string { return proto.CompactTextString(m) }
func (*TicketStatusResp) ProtoMessage()    {}
func (*TicketStatusResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_98a6c21780e82d22, []int{31}
}

func (m *TicketStatusResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TicketStatusResp.Unmarshal(m, b)
}
func (m *TicketStatusResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TicketStatusResp.Marshal(b, m, deterministic)
}
func (m *TicketStatusResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TicketStatusResp.Merge(m, src)
}
func (m *TicketStatusResp) XXX_Size() int {
	return xxx_messageInfo_TicketStatusResp.Size(m)
}
func (m *TicketStatusResp) XXX_DiscardUnknown() {
	xxx_messageInfo_TicketStatusResp.DiscardUnknown(m)
}

var xxx_messageInfo_TicketStatusResp proto.InternalMessageInfo

func (m *TicketStatusResp) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

// TicketUserListReq 客服昵称列表
type TicketUserListReq struct {
	// 客服昵称
	Nickname             uint32   `protobuf:"varint,1,opt,name=nickname,proto3" json:"nickname"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *TicketUserListReq) Reset()         { *m = TicketUserListReq{} }
func (m *TicketUserListReq) String() string { return proto.CompactTextString(m) }
func (*TicketUserListReq) ProtoMessage()    {}
func (*TicketUserListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_98a6c21780e82d22, []int{32}
}

func (m *TicketUserListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TicketUserListReq.Unmarshal(m, b)
}
func (m *TicketUserListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TicketUserListReq.Marshal(b, m, deterministic)
}
func (m *TicketUserListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TicketUserListReq.Merge(m, src)
}
func (m *TicketUserListReq) XXX_Size() int {
	return xxx_messageInfo_TicketUserListReq.Size(m)
}
func (m *TicketUserListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_TicketUserListReq.DiscardUnknown(m)
}

var xxx_messageInfo_TicketUserListReq proto.InternalMessageInfo

func (m *TicketUserListReq) GetNickname() uint32 {
	if m != nil {
		return m.Nickname
	}
	return 0
}

// TicketId 工单ID
type TicketIdReq struct {
	// 工单ID @gotags: validate:"required"
	TicketId uint64 `protobuf:"varint,1,opt,name=ticket_id,json=ticketId,proto3" json:"ticket_id" validate:"required"`
	// 使用哪个工单系统
	TicketSysType TicketSys `protobuf:"varint,2,opt,name=ticket_sys_type,json=ticketSysType,proto3,enum=pb.TicketSys" json:"ticket_sys_type"`
	// 页码
	Page uint32 `protobuf:"varint,3,opt,name=page,proto3" json:"page"`
	// 页大小
	PageSize             uint32   `protobuf:"varint,4,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *TicketIdReq) Reset()         { *m = TicketIdReq{} }
func (m *TicketIdReq) String() string { return proto.CompactTextString(m) }
func (*TicketIdReq) ProtoMessage()    {}
func (*TicketIdReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_98a6c21780e82d22, []int{33}
}

func (m *TicketIdReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TicketIdReq.Unmarshal(m, b)
}
func (m *TicketIdReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TicketIdReq.Marshal(b, m, deterministic)
}
func (m *TicketIdReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TicketIdReq.Merge(m, src)
}
func (m *TicketIdReq) XXX_Size() int {
	return xxx_messageInfo_TicketIdReq.Size(m)
}
func (m *TicketIdReq) XXX_DiscardUnknown() {
	xxx_messageInfo_TicketIdReq.DiscardUnknown(m)
}

var xxx_messageInfo_TicketIdReq proto.InternalMessageInfo

func (m *TicketIdReq) GetTicketId() uint64 {
	if m != nil {
		return m.TicketId
	}
	return 0
}

func (m *TicketIdReq) GetTicketSysType() TicketSys {
	if m != nil {
		return m.TicketSysType
	}
	return TicketSys_TicketSysOld
}

func (m *TicketIdReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *TicketIdReq) GetPageSize() uint32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

type CustomerService struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	OrderCount           int32    `protobuf:"varint,2,opt,name=order_count,json=orderCount,proto3" json:"order_count"`
	WaitTime             int32    `protobuf:"varint,3,opt,name=wait_time,json=waitTime,proto3" json:"wait_time"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *CustomerService) Reset()         { *m = CustomerService{} }
func (m *CustomerService) String() string { return proto.CompactTextString(m) }
func (*CustomerService) ProtoMessage()    {}
func (*CustomerService) Descriptor() ([]byte, []int) {
	return fileDescriptor_98a6c21780e82d22, []int{34}
}

func (m *CustomerService) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CustomerService.Unmarshal(m, b)
}
func (m *CustomerService) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CustomerService.Marshal(b, m, deterministic)
}
func (m *CustomerService) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CustomerService.Merge(m, src)
}
func (m *CustomerService) XXX_Size() int {
	return xxx_messageInfo_CustomerService.Size(m)
}
func (m *CustomerService) XXX_DiscardUnknown() {
	xxx_messageInfo_CustomerService.DiscardUnknown(m)
}

var xxx_messageInfo_CustomerService proto.InternalMessageInfo

func (m *CustomerService) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *CustomerService) GetOrderCount() int32 {
	if m != nil {
		return m.OrderCount
	}
	return 0
}

func (m *CustomerService) GetWaitTime() int32 {
	if m != nil {
		return m.WaitTime
	}
	return 0
}

type TicketTransferReq struct {
	// 工单ID @gotags: validate:"required"
	TicketId uint64 `protobuf:"varint,1,opt,name=ticket_id,json=ticketId,proto3" json:"ticket_id" validate:"required"`
	// 操作分类：1:客服回复;2:回复&关单;3:拒单 @gotags: validate:"required,gt=0"
	OpType TkTransferOpType `protobuf:"varint,2,opt,name=op_type,json=opType,proto3,enum=pb.TkTransferOpType" json:"op_type" validate:"required,gt=0"`
	// 回复内容
	Content string `protobuf:"bytes,3,opt,name=content,proto3" json:"content"`
	// 是否同步到ai精灵
	IsSyncAiElfin bool `protobuf:"varint,4,opt,name=is_sync_ai_elfin,json=isSyncAiElfin,proto3" json:"is_sync_ai_elfin"`
	// 问题
	Question string `protobuf:"bytes,5,opt,name=question,proto3" json:"question"`
	// 答案
	Answer               string   `protobuf:"bytes,6,opt,name=answer,proto3" json:"answer"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *TicketTransferReq) Reset()         { *m = TicketTransferReq{} }
func (m *TicketTransferReq) String() string { return proto.CompactTextString(m) }
func (*TicketTransferReq) ProtoMessage()    {}
func (*TicketTransferReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_98a6c21780e82d22, []int{35}
}

func (m *TicketTransferReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TicketTransferReq.Unmarshal(m, b)
}
func (m *TicketTransferReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TicketTransferReq.Marshal(b, m, deterministic)
}
func (m *TicketTransferReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TicketTransferReq.Merge(m, src)
}
func (m *TicketTransferReq) XXX_Size() int {
	return xxx_messageInfo_TicketTransferReq.Size(m)
}
func (m *TicketTransferReq) XXX_DiscardUnknown() {
	xxx_messageInfo_TicketTransferReq.DiscardUnknown(m)
}

var xxx_messageInfo_TicketTransferReq proto.InternalMessageInfo

func (m *TicketTransferReq) GetTicketId() uint64 {
	if m != nil {
		return m.TicketId
	}
	return 0
}

func (m *TicketTransferReq) GetOpType() TkTransferOpType {
	if m != nil {
		return m.OpType
	}
	return TkTransferOpType_TkTransferOpTypeUnknown
}

func (m *TicketTransferReq) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *TicketTransferReq) GetIsSyncAiElfin() bool {
	if m != nil {
		return m.IsSyncAiElfin
	}
	return false
}

func (m *TicketTransferReq) GetQuestion() string {
	if m != nil {
		return m.Question
	}
	return ""
}

func (m *TicketTransferReq) GetAnswer() string {
	if m != nil {
		return m.Answer
	}
	return ""
}

type TicketUpgradeReq struct {
	// 工单ID @gotags: validate:"required,gt=0"
	TicketId uint64 `protobuf:"varint,1,opt,name=ticket_id,json=ticketId,proto3" json:"ticket_id" validate:"required,gt=0"`
	// 工单升级/降级：1:降级；2:升级 @gotags: validate:"required,oneof=1 2"
	Upgrade              uint32   `protobuf:"varint,2,opt,name=upgrade,proto3" json:"upgrade" validate:"required,oneof=1 2"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *TicketUpgradeReq) Reset()         { *m = TicketUpgradeReq{} }
func (m *TicketUpgradeReq) String() string { return proto.CompactTextString(m) }
func (*TicketUpgradeReq) ProtoMessage()    {}
func (*TicketUpgradeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_98a6c21780e82d22, []int{36}
}

func (m *TicketUpgradeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TicketUpgradeReq.Unmarshal(m, b)
}
func (m *TicketUpgradeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TicketUpgradeReq.Marshal(b, m, deterministic)
}
func (m *TicketUpgradeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TicketUpgradeReq.Merge(m, src)
}
func (m *TicketUpgradeReq) XXX_Size() int {
	return xxx_messageInfo_TicketUpgradeReq.Size(m)
}
func (m *TicketUpgradeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_TicketUpgradeReq.DiscardUnknown(m)
}

var xxx_messageInfo_TicketUpgradeReq proto.InternalMessageInfo

func (m *TicketUpgradeReq) GetTicketId() uint64 {
	if m != nil {
		return m.TicketId
	}
	return 0
}

func (m *TicketUpgradeReq) GetUpgrade() uint32 {
	if m != nil {
		return m.Upgrade
	}
	return 0
}

//ai精灵报表统计过滤后工单量
type TicketCountStatisReq struct {
	Filter               *Filter  `protobuf:"bytes,1,opt,name=filter,proto3" json:"filter"`
	CatIds               []int64  `protobuf:"varint,2,rep,packed,name=cat_ids,json=catIds,proto3" json:"cat_ids"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *TicketCountStatisReq) Reset()         { *m = TicketCountStatisReq{} }
func (m *TicketCountStatisReq) String() string { return proto.CompactTextString(m) }
func (*TicketCountStatisReq) ProtoMessage()    {}
func (*TicketCountStatisReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_98a6c21780e82d22, []int{37}
}

func (m *TicketCountStatisReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TicketCountStatisReq.Unmarshal(m, b)
}
func (m *TicketCountStatisReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TicketCountStatisReq.Marshal(b, m, deterministic)
}
func (m *TicketCountStatisReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TicketCountStatisReq.Merge(m, src)
}
func (m *TicketCountStatisReq) XXX_Size() int {
	return xxx_messageInfo_TicketCountStatisReq.Size(m)
}
func (m *TicketCountStatisReq) XXX_DiscardUnknown() {
	xxx_messageInfo_TicketCountStatisReq.DiscardUnknown(m)
}

var xxx_messageInfo_TicketCountStatisReq proto.InternalMessageInfo

func (m *TicketCountStatisReq) GetFilter() *Filter {
	if m != nil {
		return m.Filter
	}
	return nil
}

func (m *TicketCountStatisReq) GetCatIds() []int64 {
	if m != nil {
		return m.CatIds
	}
	return nil
}

//用户过滤条件
type Filter struct {
	// 游戏标识 @gotags: validate:"required"
	GameProject string `protobuf:"bytes,1,opt,name=game_project,json=gameProject,proto3" json:"game_project" validate:"required"`
	// 开始时间：
	StartTime string `protobuf:"bytes,2,opt,name=start_time,json=startTime,proto3" json:"start_time"`
	// 结束时间：
	EndTime string `protobuf:"bytes,3,opt,name=end_time,json=endTime,proto3" json:"end_time"`
	// 渠道
	Channel []string `protobuf:"bytes,4,rep,name=channel,proto3" json:"channel"`
	// 搜索语种
	Lang []string `protobuf:"bytes,5,rep,name=lang,proto3" json:"lang"`
	// 多个区服id用逗号分隔，如："1,2,3"
	ServerIds string `protobuf:"bytes,6,opt,name=server_ids,json=serverIds,proto3" json:"server_ids"`
	// 区服数组，如: [[1,100],[101,200]]
	ServerArr            []*ServerBtw `protobuf:"bytes,7,rep,name=server_arr,json=serverArr,proto3" json:"server_arr"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-" gorm:"-"`
	XXX_unrecognized     []byte       `json:"-" gorm:"-"`
	XXX_sizecache        int32        `json:"-" gorm:"-"`
}

func (m *Filter) Reset()         { *m = Filter{} }
func (m *Filter) String() string { return proto.CompactTextString(m) }
func (*Filter) ProtoMessage()    {}
func (*Filter) Descriptor() ([]byte, []int) {
	return fileDescriptor_98a6c21780e82d22, []int{38}
}

func (m *Filter) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Filter.Unmarshal(m, b)
}
func (m *Filter) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Filter.Marshal(b, m, deterministic)
}
func (m *Filter) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Filter.Merge(m, src)
}
func (m *Filter) XXX_Size() int {
	return xxx_messageInfo_Filter.Size(m)
}
func (m *Filter) XXX_DiscardUnknown() {
	xxx_messageInfo_Filter.DiscardUnknown(m)
}

var xxx_messageInfo_Filter proto.InternalMessageInfo

func (m *Filter) GetGameProject() string {
	if m != nil {
		return m.GameProject
	}
	return ""
}

func (m *Filter) GetStartTime() string {
	if m != nil {
		return m.StartTime
	}
	return ""
}

func (m *Filter) GetEndTime() string {
	if m != nil {
		return m.EndTime
	}
	return ""
}

func (m *Filter) GetChannel() []string {
	if m != nil {
		return m.Channel
	}
	return nil
}

func (m *Filter) GetLang() []string {
	if m != nil {
		return m.Lang
	}
	return nil
}

func (m *Filter) GetServerIds() string {
	if m != nil {
		return m.ServerIds
	}
	return ""
}

func (m *Filter) GetServerArr() []*ServerBtw {
	if m != nil {
		return m.ServerArr
	}
	return nil
}

type ServerBtw struct {
	Gte                  int64    `protobuf:"varint,1,opt,name=gte,proto3" json:"gte"`
	Lte                  int64    `protobuf:"varint,2,opt,name=lte,proto3" json:"lte"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *ServerBtw) Reset()         { *m = ServerBtw{} }
func (m *ServerBtw) String() string { return proto.CompactTextString(m) }
func (*ServerBtw) ProtoMessage()    {}
func (*ServerBtw) Descriptor() ([]byte, []int) {
	return fileDescriptor_98a6c21780e82d22, []int{39}
}

func (m *ServerBtw) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ServerBtw.Unmarshal(m, b)
}
func (m *ServerBtw) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ServerBtw.Marshal(b, m, deterministic)
}
func (m *ServerBtw) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ServerBtw.Merge(m, src)
}
func (m *ServerBtw) XXX_Size() int {
	return xxx_messageInfo_ServerBtw.Size(m)
}
func (m *ServerBtw) XXX_DiscardUnknown() {
	xxx_messageInfo_ServerBtw.DiscardUnknown(m)
}

var xxx_messageInfo_ServerBtw proto.InternalMessageInfo

func (m *ServerBtw) GetGte() int64 {
	if m != nil {
		return m.Gte
	}
	return 0
}

func (m *ServerBtw) GetLte() int64 {
	if m != nil {
		return m.Lte
	}
	return 0
}

// 退回工单池
type TicketReturnPoolReq struct {
	// 工单ID @gotags: validate:"required,gt=0"
	TicketId             uint64   `protobuf:"varint,1,opt,name=ticket_id,json=ticketId,proto3" json:"ticket_id" validate:"required,gt=0"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *TicketReturnPoolReq) Reset()         { *m = TicketReturnPoolReq{} }
func (m *TicketReturnPoolReq) String() string { return proto.CompactTextString(m) }
func (*TicketReturnPoolReq) ProtoMessage()    {}
func (*TicketReturnPoolReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_98a6c21780e82d22, []int{40}
}

func (m *TicketReturnPoolReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TicketReturnPoolReq.Unmarshal(m, b)
}
func (m *TicketReturnPoolReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TicketReturnPoolReq.Marshal(b, m, deterministic)
}
func (m *TicketReturnPoolReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TicketReturnPoolReq.Merge(m, src)
}
func (m *TicketReturnPoolReq) XXX_Size() int {
	return xxx_messageInfo_TicketReturnPoolReq.Size(m)
}
func (m *TicketReturnPoolReq) XXX_DiscardUnknown() {
	xxx_messageInfo_TicketReturnPoolReq.DiscardUnknown(m)
}

var xxx_messageInfo_TicketReturnPoolReq proto.InternalMessageInfo

func (m *TicketReturnPoolReq) GetTicketId() uint64 {
	if m != nil {
		return m.TicketId
	}
	return 0
}

// 精分数据 - 游戏item - 道具 ID 保存
type DataPlatGameItemBatchSaveReq struct {
	// 游戏标识 @gotags: validate:"required"
	Project string `protobuf:"bytes,1,opt,name=project,proto3" json:"project" validate:"required"`
	// 导入文件地址 @gotags: validate:"required"
	FileName             string   `protobuf:"bytes,2,opt,name=file_name,json=fileName,proto3" json:"file_name" validate:"required"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *DataPlatGameItemBatchSaveReq) Reset()         { *m = DataPlatGameItemBatchSaveReq{} }
func (m *DataPlatGameItemBatchSaveReq) String() string { return proto.CompactTextString(m) }
func (*DataPlatGameItemBatchSaveReq) ProtoMessage()    {}
func (*DataPlatGameItemBatchSaveReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_98a6c21780e82d22, []int{41}
}

func (m *DataPlatGameItemBatchSaveReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DataPlatGameItemBatchSaveReq.Unmarshal(m, b)
}
func (m *DataPlatGameItemBatchSaveReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DataPlatGameItemBatchSaveReq.Marshal(b, m, deterministic)
}
func (m *DataPlatGameItemBatchSaveReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DataPlatGameItemBatchSaveReq.Merge(m, src)
}
func (m *DataPlatGameItemBatchSaveReq) XXX_Size() int {
	return xxx_messageInfo_DataPlatGameItemBatchSaveReq.Size(m)
}
func (m *DataPlatGameItemBatchSaveReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DataPlatGameItemBatchSaveReq.DiscardUnknown(m)
}

var xxx_messageInfo_DataPlatGameItemBatchSaveReq proto.InternalMessageInfo

func (m *DataPlatGameItemBatchSaveReq) GetProject() string {
	if m != nil {
		return m.Project
	}
	return ""
}

func (m *DataPlatGameItemBatchSaveReq) GetFileName() string {
	if m != nil {
		return m.FileName
	}
	return ""
}

// 精分数据 - 游戏item - 道具 ID list
type DataPlatGameItemListReq struct {
	// 游戏标识
	Project string `protobuf:"bytes,1,opt,name=project,proto3" json:"project"`
	// 页码
	Page uint32 `protobuf:"varint,19,opt,name=page,proto3" json:"page"`
	// 页大小
	PageSize             uint32   `protobuf:"varint,20,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *DataPlatGameItemListReq) Reset()         { *m = DataPlatGameItemListReq{} }
func (m *DataPlatGameItemListReq) String() string { return proto.CompactTextString(m) }
func (*DataPlatGameItemListReq) ProtoMessage()    {}
func (*DataPlatGameItemListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_98a6c21780e82d22, []int{42}
}

func (m *DataPlatGameItemListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DataPlatGameItemListReq.Unmarshal(m, b)
}
func (m *DataPlatGameItemListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DataPlatGameItemListReq.Marshal(b, m, deterministic)
}
func (m *DataPlatGameItemListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DataPlatGameItemListReq.Merge(m, src)
}
func (m *DataPlatGameItemListReq) XXX_Size() int {
	return xxx_messageInfo_DataPlatGameItemListReq.Size(m)
}
func (m *DataPlatGameItemListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DataPlatGameItemListReq.DiscardUnknown(m)
}

var xxx_messageInfo_DataPlatGameItemListReq proto.InternalMessageInfo

func (m *DataPlatGameItemListReq) GetProject() string {
	if m != nil {
		return m.Project
	}
	return ""
}

func (m *DataPlatGameItemListReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *DataPlatGameItemListReq) GetPageSize() uint32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

type DataPlatGameItemOptsReq struct {
	// 游戏标识 @gotags: validate:"required"
	Project              string   `protobuf:"bytes,1,opt,name=project,proto3" json:"project" validate:"required"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *DataPlatGameItemOptsReq) Reset()         { *m = DataPlatGameItemOptsReq{} }
func (m *DataPlatGameItemOptsReq) String() string { return proto.CompactTextString(m) }
func (*DataPlatGameItemOptsReq) ProtoMessage()    {}
func (*DataPlatGameItemOptsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_98a6c21780e82d22, []int{43}
}

func (m *DataPlatGameItemOptsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DataPlatGameItemOptsReq.Unmarshal(m, b)
}
func (m *DataPlatGameItemOptsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DataPlatGameItemOptsReq.Marshal(b, m, deterministic)
}
func (m *DataPlatGameItemOptsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DataPlatGameItemOptsReq.Merge(m, src)
}
func (m *DataPlatGameItemOptsReq) XXX_Size() int {
	return xxx_messageInfo_DataPlatGameItemOptsReq.Size(m)
}
func (m *DataPlatGameItemOptsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DataPlatGameItemOptsReq.DiscardUnknown(m)
}

var xxx_messageInfo_DataPlatGameItemOptsReq proto.InternalMessageInfo

func (m *DataPlatGameItemOptsReq) GetProject() string {
	if m != nil {
		return m.Project
	}
	return ""
}

type DataPlatGameItemOptsResp struct {
	Data                 []*DataPlatGameItemOptsResp_Item `protobuf:"bytes,1,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}                         `json:"-" gorm:"-"`
	XXX_unrecognized     []byte                           `json:"-" gorm:"-"`
	XXX_sizecache        int32                            `json:"-" gorm:"-"`
}

func (m *DataPlatGameItemOptsResp) Reset()         { *m = DataPlatGameItemOptsResp{} }
func (m *DataPlatGameItemOptsResp) String() string { return proto.CompactTextString(m) }
func (*DataPlatGameItemOptsResp) ProtoMessage()    {}
func (*DataPlatGameItemOptsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_98a6c21780e82d22, []int{44}
}

func (m *DataPlatGameItemOptsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DataPlatGameItemOptsResp.Unmarshal(m, b)
}
func (m *DataPlatGameItemOptsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DataPlatGameItemOptsResp.Marshal(b, m, deterministic)
}
func (m *DataPlatGameItemOptsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DataPlatGameItemOptsResp.Merge(m, src)
}
func (m *DataPlatGameItemOptsResp) XXX_Size() int {
	return xxx_messageInfo_DataPlatGameItemOptsResp.Size(m)
}
func (m *DataPlatGameItemOptsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DataPlatGameItemOptsResp.DiscardUnknown(m)
}

var xxx_messageInfo_DataPlatGameItemOptsResp proto.InternalMessageInfo

func (m *DataPlatGameItemOptsResp) GetData() []*DataPlatGameItemOptsResp_Item {
	if m != nil {
		return m.Data
	}
	return nil
}

type DataPlatGameItemOptsResp_Item struct {
	// 道具 id
	Value string `protobuf:"bytes,1,opt,name=value,proto3" json:"value"`
	// 道具名称
	Label                string   `protobuf:"bytes,2,opt,name=label,proto3" json:"label"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *DataPlatGameItemOptsResp_Item) Reset()         { *m = DataPlatGameItemOptsResp_Item{} }
func (m *DataPlatGameItemOptsResp_Item) String() string { return proto.CompactTextString(m) }
func (*DataPlatGameItemOptsResp_Item) ProtoMessage()    {}
func (*DataPlatGameItemOptsResp_Item) Descriptor() ([]byte, []int) {
	return fileDescriptor_98a6c21780e82d22, []int{44, 0}
}

func (m *DataPlatGameItemOptsResp_Item) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DataPlatGameItemOptsResp_Item.Unmarshal(m, b)
}
func (m *DataPlatGameItemOptsResp_Item) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DataPlatGameItemOptsResp_Item.Marshal(b, m, deterministic)
}
func (m *DataPlatGameItemOptsResp_Item) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DataPlatGameItemOptsResp_Item.Merge(m, src)
}
func (m *DataPlatGameItemOptsResp_Item) XXX_Size() int {
	return xxx_messageInfo_DataPlatGameItemOptsResp_Item.Size(m)
}
func (m *DataPlatGameItemOptsResp_Item) XXX_DiscardUnknown() {
	xxx_messageInfo_DataPlatGameItemOptsResp_Item.DiscardUnknown(m)
}

var xxx_messageInfo_DataPlatGameItemOptsResp_Item proto.InternalMessageInfo

func (m *DataPlatGameItemOptsResp_Item) GetValue() string {
	if m != nil {
		return m.Value
	}
	return ""
}

func (m *DataPlatGameItemOptsResp_Item) GetLabel() string {
	if m != nil {
		return m.Label
	}
	return ""
}

// 精分数据 - 游戏item - 道具 ID list resp
type DataPlatGameItemListResp struct {
	CurrentPage          uint32                                 `protobuf:"varint,1,opt,name=current_page,json=currentPage,proto3" json:"current_page"`
	PerPage              uint32                                 `protobuf:"varint,2,opt,name=per_page,json=perPage,proto3" json:"per_page"`
	Total                uint32                                 `protobuf:"varint,3,opt,name=total,proto3" json:"total"`
	Data                 []*DataPlatGameItemListResp_ItemDetail `protobuf:"bytes,4,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}                               `json:"-" gorm:"-"`
	XXX_unrecognized     []byte                                 `json:"-" gorm:"-"`
	XXX_sizecache        int32                                  `json:"-" gorm:"-"`
}

func (m *DataPlatGameItemListResp) Reset()         { *m = DataPlatGameItemListResp{} }
func (m *DataPlatGameItemListResp) String() string { return proto.CompactTextString(m) }
func (*DataPlatGameItemListResp) ProtoMessage()    {}
func (*DataPlatGameItemListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_98a6c21780e82d22, []int{45}
}

func (m *DataPlatGameItemListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DataPlatGameItemListResp.Unmarshal(m, b)
}
func (m *DataPlatGameItemListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DataPlatGameItemListResp.Marshal(b, m, deterministic)
}
func (m *DataPlatGameItemListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DataPlatGameItemListResp.Merge(m, src)
}
func (m *DataPlatGameItemListResp) XXX_Size() int {
	return xxx_messageInfo_DataPlatGameItemListResp.Size(m)
}
func (m *DataPlatGameItemListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DataPlatGameItemListResp.DiscardUnknown(m)
}

var xxx_messageInfo_DataPlatGameItemListResp proto.InternalMessageInfo

func (m *DataPlatGameItemListResp) GetCurrentPage() uint32 {
	if m != nil {
		return m.CurrentPage
	}
	return 0
}

func (m *DataPlatGameItemListResp) GetPerPage() uint32 {
	if m != nil {
		return m.PerPage
	}
	return 0
}

func (m *DataPlatGameItemListResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *DataPlatGameItemListResp) GetData() []*DataPlatGameItemListResp_ItemDetail {
	if m != nil {
		return m.Data
	}
	return nil
}

type DataPlatGameItemListResp_ItemDetail struct {
	// id
	Id uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	// item_id
	ItemId string `protobuf:"bytes,2,opt,name=item_id,json=itemId,proto3" json:"item_id"`
	// 道具名称
	ItemName string `protobuf:"bytes,3,opt,name=item_name,json=itemName,proto3" json:"item_name"`
	// 更新时间
	UpdatedAt string `protobuf:"bytes,4,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at"`
	// 变更人
	Operator             string   `protobuf:"bytes,5,opt,name=operator,proto3" json:"operator"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *DataPlatGameItemListResp_ItemDetail) Reset()         { *m = DataPlatGameItemListResp_ItemDetail{} }
func (m *DataPlatGameItemListResp_ItemDetail) String() string { return proto.CompactTextString(m) }
func (*DataPlatGameItemListResp_ItemDetail) ProtoMessage()    {}
func (*DataPlatGameItemListResp_ItemDetail) Descriptor() ([]byte, []int) {
	return fileDescriptor_98a6c21780e82d22, []int{45, 0}
}

func (m *DataPlatGameItemListResp_ItemDetail) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DataPlatGameItemListResp_ItemDetail.Unmarshal(m, b)
}
func (m *DataPlatGameItemListResp_ItemDetail) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DataPlatGameItemListResp_ItemDetail.Marshal(b, m, deterministic)
}
func (m *DataPlatGameItemListResp_ItemDetail) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DataPlatGameItemListResp_ItemDetail.Merge(m, src)
}
func (m *DataPlatGameItemListResp_ItemDetail) XXX_Size() int {
	return xxx_messageInfo_DataPlatGameItemListResp_ItemDetail.Size(m)
}
func (m *DataPlatGameItemListResp_ItemDetail) XXX_DiscardUnknown() {
	xxx_messageInfo_DataPlatGameItemListResp_ItemDetail.DiscardUnknown(m)
}

var xxx_messageInfo_DataPlatGameItemListResp_ItemDetail proto.InternalMessageInfo

func (m *DataPlatGameItemListResp_ItemDetail) GetId() uint64 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *DataPlatGameItemListResp_ItemDetail) GetItemId() string {
	if m != nil {
		return m.ItemId
	}
	return ""
}

func (m *DataPlatGameItemListResp_ItemDetail) GetItemName() string {
	if m != nil {
		return m.ItemName
	}
	return ""
}

func (m *DataPlatGameItemListResp_ItemDetail) GetUpdatedAt() string {
	if m != nil {
		return m.UpdatedAt
	}
	return ""
}

func (m *DataPlatGameItemListResp_ItemDetail) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

// 精分数据 - 金币查询 req
type DataPlatUserGoldInfoReq struct {
	// 游戏标识
	Project string `protobuf:"bytes,1,opt,name=project,proto3" json:"project"`
	// uid
	Uid uint64 `protobuf:"varint,2,opt,name=uid,proto3" json:"uid"`
	// 查询时间段 1天数据
	CreatedAt            []string `protobuf:"bytes,3,rep,name=created_at,json=createdAt,proto3" json:"created_at"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *DataPlatUserGoldInfoReq) Reset()         { *m = DataPlatUserGoldInfoReq{} }
func (m *DataPlatUserGoldInfoReq) String() string { return proto.CompactTextString(m) }
func (*DataPlatUserGoldInfoReq) ProtoMessage()    {}
func (*DataPlatUserGoldInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_98a6c21780e82d22, []int{46}
}

func (m *DataPlatUserGoldInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DataPlatUserGoldInfoReq.Unmarshal(m, b)
}
func (m *DataPlatUserGoldInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DataPlatUserGoldInfoReq.Marshal(b, m, deterministic)
}
func (m *DataPlatUserGoldInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DataPlatUserGoldInfoReq.Merge(m, src)
}
func (m *DataPlatUserGoldInfoReq) XXX_Size() int {
	return xxx_messageInfo_DataPlatUserGoldInfoReq.Size(m)
}
func (m *DataPlatUserGoldInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DataPlatUserGoldInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_DataPlatUserGoldInfoReq proto.InternalMessageInfo

func (m *DataPlatUserGoldInfoReq) GetProject() string {
	if m != nil {
		return m.Project
	}
	return ""
}

func (m *DataPlatUserGoldInfoReq) GetUid() uint64 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *DataPlatUserGoldInfoReq) GetCreatedAt() []string {
	if m != nil {
		return m.CreatedAt
	}
	return nil
}

// 精分数据 - 金币查询 resp
type DataPlatUserGoldInfoResp struct {
	// 游戏标识
	Project string `protobuf:"bytes,1,opt,name=project,proto3" json:"project"`
	// 变化量
	Change string `protobuf:"bytes,2,opt,name=change,proto3" json:"change"`
	// 变化前
	Before string `protobuf:"bytes,3,opt,name=before,proto3" json:"before"`
	// 变化后
	After string `protobuf:"bytes,4,opt,name=after,proto3" json:"after"`
	// 时间
	ChangeTime string `protobuf:"bytes,5,opt,name=change_time,json=changeTime,proto3" json:"change_time"`
	// 原因
	Reason               string   `protobuf:"bytes,6,opt,name=reason,proto3" json:"reason"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *DataPlatUserGoldInfoResp) Reset()         { *m = DataPlatUserGoldInfoResp{} }
func (m *DataPlatUserGoldInfoResp) String() string { return proto.CompactTextString(m) }
func (*DataPlatUserGoldInfoResp) ProtoMessage()    {}
func (*DataPlatUserGoldInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_98a6c21780e82d22, []int{47}
}

func (m *DataPlatUserGoldInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DataPlatUserGoldInfoResp.Unmarshal(m, b)
}
func (m *DataPlatUserGoldInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DataPlatUserGoldInfoResp.Marshal(b, m, deterministic)
}
func (m *DataPlatUserGoldInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DataPlatUserGoldInfoResp.Merge(m, src)
}
func (m *DataPlatUserGoldInfoResp) XXX_Size() int {
	return xxx_messageInfo_DataPlatUserGoldInfoResp.Size(m)
}
func (m *DataPlatUserGoldInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DataPlatUserGoldInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_DataPlatUserGoldInfoResp proto.InternalMessageInfo

func (m *DataPlatUserGoldInfoResp) GetProject() string {
	if m != nil {
		return m.Project
	}
	return ""
}

func (m *DataPlatUserGoldInfoResp) GetChange() string {
	if m != nil {
		return m.Change
	}
	return ""
}

func (m *DataPlatUserGoldInfoResp) GetBefore() string {
	if m != nil {
		return m.Before
	}
	return ""
}

func (m *DataPlatUserGoldInfoResp) GetAfter() string {
	if m != nil {
		return m.After
	}
	return ""
}

func (m *DataPlatUserGoldInfoResp) GetChangeTime() string {
	if m != nil {
		return m.ChangeTime
	}
	return ""
}

func (m *DataPlatUserGoldInfoResp) GetReason() string {
	if m != nil {
		return m.Reason
	}
	return ""
}

// 精分数据 - 物品(道具)查询 req
type DataPlatUserItemInfoReq struct {
	// 游戏标识
	Project string `protobuf:"bytes,1,opt,name=project,proto3" json:"project"`
	// uid
	Uid uint64 `protobuf:"varint,2,opt,name=uid,proto3" json:"uid"`
	// 道具 id
	ItemId string `protobuf:"bytes,3,opt,name=item_id,json=itemId,proto3" json:"item_id"`
	// 查询时间段
	CreatedAt            []string `protobuf:"bytes,4,rep,name=created_at,json=createdAt,proto3" json:"created_at"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *DataPlatUserItemInfoReq) Reset()         { *m = DataPlatUserItemInfoReq{} }
func (m *DataPlatUserItemInfoReq) String() string { return proto.CompactTextString(m) }
func (*DataPlatUserItemInfoReq) ProtoMessage()    {}
func (*DataPlatUserItemInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_98a6c21780e82d22, []int{48}
}

func (m *DataPlatUserItemInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DataPlatUserItemInfoReq.Unmarshal(m, b)
}
func (m *DataPlatUserItemInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DataPlatUserItemInfoReq.Marshal(b, m, deterministic)
}
func (m *DataPlatUserItemInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DataPlatUserItemInfoReq.Merge(m, src)
}
func (m *DataPlatUserItemInfoReq) XXX_Size() int {
	return xxx_messageInfo_DataPlatUserItemInfoReq.Size(m)
}
func (m *DataPlatUserItemInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DataPlatUserItemInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_DataPlatUserItemInfoReq proto.InternalMessageInfo

func (m *DataPlatUserItemInfoReq) GetProject() string {
	if m != nil {
		return m.Project
	}
	return ""
}

func (m *DataPlatUserItemInfoReq) GetUid() uint64 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *DataPlatUserItemInfoReq) GetItemId() string {
	if m != nil {
		return m.ItemId
	}
	return ""
}

func (m *DataPlatUserItemInfoReq) GetCreatedAt() []string {
	if m != nil {
		return m.CreatedAt
	}
	return nil
}

// 精分数据 - 物品(道具)查询 resp
type DataPlatUserItemInfoResp struct {
	// 游戏标识
	Project string `protobuf:"bytes,1,opt,name=project,proto3" json:"project"`
	// 物品 id
	ItemId string `protobuf:"bytes,2,opt,name=item_id,json=itemId,proto3" json:"item_id"`
	// 物品名称
	ItemName string `protobuf:"bytes,3,opt,name=item_name,json=itemName,proto3" json:"item_name"`
	// 变化量
	Change string `protobuf:"bytes,4,opt,name=change,proto3" json:"change"`
	// 变化前
	Before string `protobuf:"bytes,5,opt,name=before,proto3" json:"before"`
	// 变化后
	After string `protobuf:"bytes,6,opt,name=after,proto3" json:"after"`
	// 时间
	ChangeTime string `protobuf:"bytes,7,opt,name=change_time,json=changeTime,proto3" json:"change_time"`
	// 原因
	Reason               string   `protobuf:"bytes,8,opt,name=reason,proto3" json:"reason"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *DataPlatUserItemInfoResp) Reset()         { *m = DataPlatUserItemInfoResp{} }
func (m *DataPlatUserItemInfoResp) String() string { return proto.CompactTextString(m) }
func (*DataPlatUserItemInfoResp) ProtoMessage()    {}
func (*DataPlatUserItemInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_98a6c21780e82d22, []int{49}
}

func (m *DataPlatUserItemInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DataPlatUserItemInfoResp.Unmarshal(m, b)
}
func (m *DataPlatUserItemInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DataPlatUserItemInfoResp.Marshal(b, m, deterministic)
}
func (m *DataPlatUserItemInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DataPlatUserItemInfoResp.Merge(m, src)
}
func (m *DataPlatUserItemInfoResp) XXX_Size() int {
	return xxx_messageInfo_DataPlatUserItemInfoResp.Size(m)
}
func (m *DataPlatUserItemInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DataPlatUserItemInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_DataPlatUserItemInfoResp proto.InternalMessageInfo

func (m *DataPlatUserItemInfoResp) GetProject() string {
	if m != nil {
		return m.Project
	}
	return ""
}

func (m *DataPlatUserItemInfoResp) GetItemId() string {
	if m != nil {
		return m.ItemId
	}
	return ""
}

func (m *DataPlatUserItemInfoResp) GetItemName() string {
	if m != nil {
		return m.ItemName
	}
	return ""
}

func (m *DataPlatUserItemInfoResp) GetChange() string {
	if m != nil {
		return m.Change
	}
	return ""
}

func (m *DataPlatUserItemInfoResp) GetBefore() string {
	if m != nil {
		return m.Before
	}
	return ""
}

func (m *DataPlatUserItemInfoResp) GetAfter() string {
	if m != nil {
		return m.After
	}
	return ""
}

func (m *DataPlatUserItemInfoResp) GetChangeTime() string {
	if m != nil {
		return m.ChangeTime
	}
	return ""
}

func (m *DataPlatUserItemInfoResp) GetReason() string {
	if m != nil {
		return m.Reason
	}
	return ""
}

// 精分数据 - 支付查询 req
type DataPlatUserPayInfoReq struct {
	// 游戏标识
	Project string `protobuf:"bytes,1,opt,name=project,proto3" json:"project"`
	// uid
	Uid uint64 `protobuf:"varint,2,opt,name=uid,proto3" json:"uid"`
	// 查询时间段
	CreatedAt            []string `protobuf:"bytes,3,rep,name=created_at,json=createdAt,proto3" json:"created_at"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *DataPlatUserPayInfoReq) Reset()         { *m = DataPlatUserPayInfoReq{} }
func (m *DataPlatUserPayInfoReq) String() string { return proto.CompactTextString(m) }
func (*DataPlatUserPayInfoReq) ProtoMessage()    {}
func (*DataPlatUserPayInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_98a6c21780e82d22, []int{50}
}

func (m *DataPlatUserPayInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DataPlatUserPayInfoReq.Unmarshal(m, b)
}
func (m *DataPlatUserPayInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DataPlatUserPayInfoReq.Marshal(b, m, deterministic)
}
func (m *DataPlatUserPayInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DataPlatUserPayInfoReq.Merge(m, src)
}
func (m *DataPlatUserPayInfoReq) XXX_Size() int {
	return xxx_messageInfo_DataPlatUserPayInfoReq.Size(m)
}
func (m *DataPlatUserPayInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DataPlatUserPayInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_DataPlatUserPayInfoReq proto.InternalMessageInfo

func (m *DataPlatUserPayInfoReq) GetProject() string {
	if m != nil {
		return m.Project
	}
	return ""
}

func (m *DataPlatUserPayInfoReq) GetUid() uint64 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *DataPlatUserPayInfoReq) GetCreatedAt() []string {
	if m != nil {
		return m.CreatedAt
	}
	return nil
}

// 精分数据 - 支付查询 resp
type DataPlatUserPayInfoResp struct {
	// 游戏标识
	Project string `protobuf:"bytes,1,opt,name=project,proto3" json:"project"`
	// 商品名称 - iap_product_name
	ProductName string `protobuf:"bytes,2,opt,name=product_name,json=productName,proto3" json:"product_name"`
	// 支付渠道 - payment_processor
	PayChannel string `protobuf:"bytes,3,opt,name=pay_channel,json=payChannel,proto3" json:"pay_channel"`
	// 价格 - price
	Price string `protobuf:"bytes,4,opt,name=price,proto3" json:"price"`
	// 基础价格 - base_price
	BasicPrice string `protobuf:"bytes,5,opt,name=basic_price,json=basicPrice,proto3" json:"basic_price"`
	// 币种 - currency
	Currency string `protobuf:"bytes,6,opt,name=currency,proto3" json:"currency"`
	// 支付结果 - result
	Status string `protobuf:"bytes,7,opt,name=status,proto3" json:"status"`
	// 支付时间 - finish_time
	PaidAt               string   `protobuf:"bytes,8,opt,name=paid_at,json=paidAt,proto3" json:"paid_at"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *DataPlatUserPayInfoResp) Reset()         { *m = DataPlatUserPayInfoResp{} }
func (m *DataPlatUserPayInfoResp) String() string { return proto.CompactTextString(m) }
func (*DataPlatUserPayInfoResp) ProtoMessage()    {}
func (*DataPlatUserPayInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_98a6c21780e82d22, []int{51}
}

func (m *DataPlatUserPayInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DataPlatUserPayInfoResp.Unmarshal(m, b)
}
func (m *DataPlatUserPayInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DataPlatUserPayInfoResp.Marshal(b, m, deterministic)
}
func (m *DataPlatUserPayInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DataPlatUserPayInfoResp.Merge(m, src)
}
func (m *DataPlatUserPayInfoResp) XXX_Size() int {
	return xxx_messageInfo_DataPlatUserPayInfoResp.Size(m)
}
func (m *DataPlatUserPayInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DataPlatUserPayInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_DataPlatUserPayInfoResp proto.InternalMessageInfo

func (m *DataPlatUserPayInfoResp) GetProject() string {
	if m != nil {
		return m.Project
	}
	return ""
}

func (m *DataPlatUserPayInfoResp) GetProductName() string {
	if m != nil {
		return m.ProductName
	}
	return ""
}

func (m *DataPlatUserPayInfoResp) GetPayChannel() string {
	if m != nil {
		return m.PayChannel
	}
	return ""
}

func (m *DataPlatUserPayInfoResp) GetPrice() string {
	if m != nil {
		return m.Price
	}
	return ""
}

func (m *DataPlatUserPayInfoResp) GetBasicPrice() string {
	if m != nil {
		return m.BasicPrice
	}
	return ""
}

func (m *DataPlatUserPayInfoResp) GetCurrency() string {
	if m != nil {
		return m.Currency
	}
	return ""
}

func (m *DataPlatUserPayInfoResp) GetStatus() string {
	if m != nil {
		return m.Status
	}
	return ""
}

func (m *DataPlatUserPayInfoResp) GetPaidAt() string {
	if m != nil {
		return m.PaidAt
	}
	return ""
}

// 精分数据 - 登录查询 req
type DataPlatUserLoginInfoReq struct {
	// 游戏标识
	Project string `protobuf:"bytes,1,opt,name=project,proto3" json:"project"`
	// uid
	Uid uint64 `protobuf:"varint,2,opt,name=uid,proto3" json:"uid"`
	// 查询时间段
	CreatedAt            []string `protobuf:"bytes,3,rep,name=created_at,json=createdAt,proto3" json:"created_at"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *DataPlatUserLoginInfoReq) Reset()         { *m = DataPlatUserLoginInfoReq{} }
func (m *DataPlatUserLoginInfoReq) String() string { return proto.CompactTextString(m) }
func (*DataPlatUserLoginInfoReq) ProtoMessage()    {}
func (*DataPlatUserLoginInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_98a6c21780e82d22, []int{52}
}

func (m *DataPlatUserLoginInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DataPlatUserLoginInfoReq.Unmarshal(m, b)
}
func (m *DataPlatUserLoginInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DataPlatUserLoginInfoReq.Marshal(b, m, deterministic)
}
func (m *DataPlatUserLoginInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DataPlatUserLoginInfoReq.Merge(m, src)
}
func (m *DataPlatUserLoginInfoReq) XXX_Size() int {
	return xxx_messageInfo_DataPlatUserLoginInfoReq.Size(m)
}
func (m *DataPlatUserLoginInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DataPlatUserLoginInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_DataPlatUserLoginInfoReq proto.InternalMessageInfo

func (m *DataPlatUserLoginInfoReq) GetProject() string {
	if m != nil {
		return m.Project
	}
	return ""
}

func (m *DataPlatUserLoginInfoReq) GetUid() uint64 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *DataPlatUserLoginInfoReq) GetCreatedAt() []string {
	if m != nil {
		return m.CreatedAt
	}
	return nil
}

// 精分数据 - 登录查询 resp
type DataPlatUserLoginInfoResp struct {
	// 游戏标识
	Project string `protobuf:"bytes,1,opt,name=project,proto3" json:"project"`
	// 登录设备
	FpDeviceId string `protobuf:"bytes,2,opt,name=fp_device_id,json=fpDeviceId,proto3" json:"fp_device_id"`
	// 设备型号
	DeviceType string `protobuf:"bytes,3,opt,name=device_type,json=deviceType,proto3" json:"device_type"`
	// 登录 IP
	Ip string `protobuf:"bytes,4,opt,name=ip,proto3" json:"ip"`
	// IP 位置
	IpLoc string `protobuf:"bytes,5,opt,name=ip_loc,json=ipLoc,proto3" json:"ip_loc"`
	// 登录时间
	LoginAt              string   `protobuf:"bytes,8,opt,name=login_at,json=loginAt,proto3" json:"login_at"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *DataPlatUserLoginInfoResp) Reset()         { *m = DataPlatUserLoginInfoResp{} }
func (m *DataPlatUserLoginInfoResp) String() string { return proto.CompactTextString(m) }
func (*DataPlatUserLoginInfoResp) ProtoMessage()    {}
func (*DataPlatUserLoginInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_98a6c21780e82d22, []int{53}
}

func (m *DataPlatUserLoginInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DataPlatUserLoginInfoResp.Unmarshal(m, b)
}
func (m *DataPlatUserLoginInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DataPlatUserLoginInfoResp.Marshal(b, m, deterministic)
}
func (m *DataPlatUserLoginInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DataPlatUserLoginInfoResp.Merge(m, src)
}
func (m *DataPlatUserLoginInfoResp) XXX_Size() int {
	return xxx_messageInfo_DataPlatUserLoginInfoResp.Size(m)
}
func (m *DataPlatUserLoginInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DataPlatUserLoginInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_DataPlatUserLoginInfoResp proto.InternalMessageInfo

func (m *DataPlatUserLoginInfoResp) GetProject() string {
	if m != nil {
		return m.Project
	}
	return ""
}

func (m *DataPlatUserLoginInfoResp) GetFpDeviceId() string {
	if m != nil {
		return m.FpDeviceId
	}
	return ""
}

func (m *DataPlatUserLoginInfoResp) GetDeviceType() string {
	if m != nil {
		return m.DeviceType
	}
	return ""
}

func (m *DataPlatUserLoginInfoResp) GetIp() string {
	if m != nil {
		return m.Ip
	}
	return ""
}

func (m *DataPlatUserLoginInfoResp) GetIpLoc() string {
	if m != nil {
		return m.IpLoc
	}
	return ""
}

func (m *DataPlatUserLoginInfoResp) GetLoginAt() string {
	if m != nil {
		return m.LoginAt
	}
	return ""
}

// 工单备注支持保存草稿
type TicketDraftSaveReq struct {
	// 草稿id
	Id uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	// 工单id @gotags: validate:"required"
	TicketId uint64 `protobuf:"varint,2,opt,name=ticket_id,json=ticketId,proto3" json:"ticket_id" validate:"required"`
	// 操作内容json @gotags: validate:"required"
	Content              string   `protobuf:"bytes,3,opt,name=content,proto3" json:"content" validate:"required"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *TicketDraftSaveReq) Reset()         { *m = TicketDraftSaveReq{} }
func (m *TicketDraftSaveReq) String() string { return proto.CompactTextString(m) }
func (*TicketDraftSaveReq) ProtoMessage()    {}
func (*TicketDraftSaveReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_98a6c21780e82d22, []int{54}
}

func (m *TicketDraftSaveReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TicketDraftSaveReq.Unmarshal(m, b)
}
func (m *TicketDraftSaveReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TicketDraftSaveReq.Marshal(b, m, deterministic)
}
func (m *TicketDraftSaveReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TicketDraftSaveReq.Merge(m, src)
}
func (m *TicketDraftSaveReq) XXX_Size() int {
	return xxx_messageInfo_TicketDraftSaveReq.Size(m)
}
func (m *TicketDraftSaveReq) XXX_DiscardUnknown() {
	xxx_messageInfo_TicketDraftSaveReq.DiscardUnknown(m)
}

var xxx_messageInfo_TicketDraftSaveReq proto.InternalMessageInfo

func (m *TicketDraftSaveReq) GetId() uint64 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *TicketDraftSaveReq) GetTicketId() uint64 {
	if m != nil {
		return m.TicketId
	}
	return 0
}

func (m *TicketDraftSaveReq) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

type TicketDraftInfoReq struct {
	// 工单id @gotags: validate:"required"
	TicketId             uint64   `protobuf:"varint,1,opt,name=ticket_id,json=ticketId,proto3" json:"ticket_id" validate:"required"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *TicketDraftInfoReq) Reset()         { *m = TicketDraftInfoReq{} }
func (m *TicketDraftInfoReq) String() string { return proto.CompactTextString(m) }
func (*TicketDraftInfoReq) ProtoMessage()    {}
func (*TicketDraftInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_98a6c21780e82d22, []int{55}
}

func (m *TicketDraftInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TicketDraftInfoReq.Unmarshal(m, b)
}
func (m *TicketDraftInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TicketDraftInfoReq.Marshal(b, m, deterministic)
}
func (m *TicketDraftInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TicketDraftInfoReq.Merge(m, src)
}
func (m *TicketDraftInfoReq) XXX_Size() int {
	return xxx_messageInfo_TicketDraftInfoReq.Size(m)
}
func (m *TicketDraftInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_TicketDraftInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_TicketDraftInfoReq proto.InternalMessageInfo

func (m *TicketDraftInfoReq) GetTicketId() uint64 {
	if m != nil {
		return m.TicketId
	}
	return 0
}

type TicketDraftInfoResp struct {
	// 草稿id
	Id uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	// 工单id
	TicketId uint64 `protobuf:"varint,2,opt,name=ticket_id,json=ticketId,proto3" json:"ticket_id"`
	// 操作内容json
	Content string `protobuf:"bytes,3,opt,name=content,proto3" json:"content"`
	// 上次保存时间
	UpdateTime           string   `protobuf:"bytes,4,opt,name=update_time,json=updateTime,proto3" json:"update_time"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *TicketDraftInfoResp) Reset()         { *m = TicketDraftInfoResp{} }
func (m *TicketDraftInfoResp) String() string { return proto.CompactTextString(m) }
func (*TicketDraftInfoResp) ProtoMessage()    {}
func (*TicketDraftInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_98a6c21780e82d22, []int{56}
}

func (m *TicketDraftInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TicketDraftInfoResp.Unmarshal(m, b)
}
func (m *TicketDraftInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TicketDraftInfoResp.Marshal(b, m, deterministic)
}
func (m *TicketDraftInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TicketDraftInfoResp.Merge(m, src)
}
func (m *TicketDraftInfoResp) XXX_Size() int {
	return xxx_messageInfo_TicketDraftInfoResp.Size(m)
}
func (m *TicketDraftInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_TicketDraftInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_TicketDraftInfoResp proto.InternalMessageInfo

func (m *TicketDraftInfoResp) GetId() uint64 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *TicketDraftInfoResp) GetTicketId() uint64 {
	if m != nil {
		return m.TicketId
	}
	return 0
}

func (m *TicketDraftInfoResp) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *TicketDraftInfoResp) GetUpdateTime() string {
	if m != nil {
		return m.UpdateTime
	}
	return ""
}

type TicketTabAddReq struct {
	// @gotags: validate:"required"
	TabName string `protobuf:"bytes,1,opt,name=tab_name,json=tabName,proto3" json:"tab_name" validate:"required"`
	// @gotags: validate:"required,oneof=1 2"
	Public int32 `protobuf:"varint,2,opt,name=public,proto3" json:"public" validate:"required,oneof=1 2"`
	// 搜索条件组合 @gotags: validate:"required"
	Detail               *TicketPoolNewListReq `protobuf:"bytes,3,opt,name=detail,proto3" json:"detail" validate:"required"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-" gorm:"-"`
	XXX_unrecognized     []byte                `json:"-" gorm:"-"`
	XXX_sizecache        int32                 `json:"-" gorm:"-"`
}

func (m *TicketTabAddReq) Reset()         { *m = TicketTabAddReq{} }
func (m *TicketTabAddReq) String() string { return proto.CompactTextString(m) }
func (*TicketTabAddReq) ProtoMessage()    {}
func (*TicketTabAddReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_98a6c21780e82d22, []int{57}
}

func (m *TicketTabAddReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TicketTabAddReq.Unmarshal(m, b)
}
func (m *TicketTabAddReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TicketTabAddReq.Marshal(b, m, deterministic)
}
func (m *TicketTabAddReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TicketTabAddReq.Merge(m, src)
}
func (m *TicketTabAddReq) XXX_Size() int {
	return xxx_messageInfo_TicketTabAddReq.Size(m)
}
func (m *TicketTabAddReq) XXX_DiscardUnknown() {
	xxx_messageInfo_TicketTabAddReq.DiscardUnknown(m)
}

var xxx_messageInfo_TicketTabAddReq proto.InternalMessageInfo

func (m *TicketTabAddReq) GetTabName() string {
	if m != nil {
		return m.TabName
	}
	return ""
}

func (m *TicketTabAddReq) GetPublic() int32 {
	if m != nil {
		return m.Public
	}
	return 0
}

func (m *TicketTabAddReq) GetDetail() *TicketPoolNewListReq {
	if m != nil {
		return m.Detail
	}
	return nil
}

type TicketTabEditReq struct {
	// @gotags: validate:"required"
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id" validate:"required"`
	// @gotags: validate:"required"
	TabName string `protobuf:"bytes,2,opt,name=tab_name,json=tabName,proto3" json:"tab_name" validate:"required"`
	// @gotags: validate:"required,oneof=1 2"
	Public               int32    `protobuf:"varint,3,opt,name=public,proto3" json:"public" validate:"required,oneof=1 2"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *TicketTabEditReq) Reset()         { *m = TicketTabEditReq{} }
func (m *TicketTabEditReq) String() string { return proto.CompactTextString(m) }
func (*TicketTabEditReq) ProtoMessage()    {}
func (*TicketTabEditReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_98a6c21780e82d22, []int{58}
}

func (m *TicketTabEditReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TicketTabEditReq.Unmarshal(m, b)
}
func (m *TicketTabEditReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TicketTabEditReq.Marshal(b, m, deterministic)
}
func (m *TicketTabEditReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TicketTabEditReq.Merge(m, src)
}
func (m *TicketTabEditReq) XXX_Size() int {
	return xxx_messageInfo_TicketTabEditReq.Size(m)
}
func (m *TicketTabEditReq) XXX_DiscardUnknown() {
	xxx_messageInfo_TicketTabEditReq.DiscardUnknown(m)
}

var xxx_messageInfo_TicketTabEditReq proto.InternalMessageInfo

func (m *TicketTabEditReq) GetId() int64 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *TicketTabEditReq) GetTabName() string {
	if m != nil {
		return m.TabName
	}
	return ""
}

func (m *TicketTabEditReq) GetPublic() int32 {
	if m != nil {
		return m.Public
	}
	return 0
}

type TicketTabDelReq struct {
	// @gotags: validate:"required"
	Id                   int64    `protobuf:"varint,1,opt,name=id,proto3" json:"id" validate:"required"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *TicketTabDelReq) Reset()         { *m = TicketTabDelReq{} }
func (m *TicketTabDelReq) String() string { return proto.CompactTextString(m) }
func (*TicketTabDelReq) ProtoMessage()    {}
func (*TicketTabDelReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_98a6c21780e82d22, []int{59}
}

func (m *TicketTabDelReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TicketTabDelReq.Unmarshal(m, b)
}
func (m *TicketTabDelReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TicketTabDelReq.Marshal(b, m, deterministic)
}
func (m *TicketTabDelReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TicketTabDelReq.Merge(m, src)
}
func (m *TicketTabDelReq) XXX_Size() int {
	return xxx_messageInfo_TicketTabDelReq.Size(m)
}
func (m *TicketTabDelReq) XXX_DiscardUnknown() {
	xxx_messageInfo_TicketTabDelReq.DiscardUnknown(m)
}

var xxx_messageInfo_TicketTabDelReq proto.InternalMessageInfo

func (m *TicketTabDelReq) GetId() int64 {
	if m != nil {
		return m.Id
	}
	return 0
}

type TicketTabListResp struct {
	Data                 []*TicketTabListResp_TicketTabDetail `protobuf:"bytes,1,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}                             `json:"-" gorm:"-"`
	XXX_unrecognized     []byte                               `json:"-" gorm:"-"`
	XXX_sizecache        int32                                `json:"-" gorm:"-"`
}

func (m *TicketTabListResp) Reset()         { *m = TicketTabListResp{} }
func (m *TicketTabListResp) String() string { return proto.CompactTextString(m) }
func (*TicketTabListResp) ProtoMessage()    {}
func (*TicketTabListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_98a6c21780e82d22, []int{60}
}

func (m *TicketTabListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TicketTabListResp.Unmarshal(m, b)
}
func (m *TicketTabListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TicketTabListResp.Marshal(b, m, deterministic)
}
func (m *TicketTabListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TicketTabListResp.Merge(m, src)
}
func (m *TicketTabListResp) XXX_Size() int {
	return xxx_messageInfo_TicketTabListResp.Size(m)
}
func (m *TicketTabListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_TicketTabListResp.DiscardUnknown(m)
}

var xxx_messageInfo_TicketTabListResp proto.InternalMessageInfo

func (m *TicketTabListResp) GetData() []*TicketTabListResp_TicketTabDetail {
	if m != nil {
		return m.Data
	}
	return nil
}

type TicketTabListResp_TicketTabDetail struct {
	Tab                  []*TicketTabListResp_TabInfo `protobuf:"bytes,1,rep,name=tab,proto3" json:"tab"`
	Project              string                       `protobuf:"bytes,2,opt,name=project,proto3" json:"project"`
	XXX_NoUnkeyedLiteral struct{}                     `json:"-" gorm:"-"`
	XXX_unrecognized     []byte                       `json:"-" gorm:"-"`
	XXX_sizecache        int32                        `json:"-" gorm:"-"`
}

func (m *TicketTabListResp_TicketTabDetail) Reset()         { *m = TicketTabListResp_TicketTabDetail{} }
func (m *TicketTabListResp_TicketTabDetail) String() string { return proto.CompactTextString(m) }
func (*TicketTabListResp_TicketTabDetail) ProtoMessage()    {}
func (*TicketTabListResp_TicketTabDetail) Descriptor() ([]byte, []int) {
	return fileDescriptor_98a6c21780e82d22, []int{60, 0}
}

func (m *TicketTabListResp_TicketTabDetail) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TicketTabListResp_TicketTabDetail.Unmarshal(m, b)
}
func (m *TicketTabListResp_TicketTabDetail) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TicketTabListResp_TicketTabDetail.Marshal(b, m, deterministic)
}
func (m *TicketTabListResp_TicketTabDetail) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TicketTabListResp_TicketTabDetail.Merge(m, src)
}
func (m *TicketTabListResp_TicketTabDetail) XXX_Size() int {
	return xxx_messageInfo_TicketTabListResp_TicketTabDetail.Size(m)
}
func (m *TicketTabListResp_TicketTabDetail) XXX_DiscardUnknown() {
	xxx_messageInfo_TicketTabListResp_TicketTabDetail.DiscardUnknown(m)
}

var xxx_messageInfo_TicketTabListResp_TicketTabDetail proto.InternalMessageInfo

func (m *TicketTabListResp_TicketTabDetail) GetTab() []*TicketTabListResp_TabInfo {
	if m != nil {
		return m.Tab
	}
	return nil
}

func (m *TicketTabListResp_TicketTabDetail) GetProject() string {
	if m != nil {
		return m.Project
	}
	return ""
}

type TicketTabListResp_TabInfo struct {
	Id                   int64                 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	TabName              string                `protobuf:"bytes,2,opt,name=tab_name,json=tabName,proto3" json:"tab_name"`
	Detail               *TicketPoolNewListReq `protobuf:"bytes,3,opt,name=detail,proto3" json:"detail"`
	Public               int32                 `protobuf:"varint,4,opt,name=public,proto3" json:"public"`
	Operator             string                `protobuf:"bytes,5,opt,name=operator,proto3" json:"operator"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-" gorm:"-"`
	XXX_unrecognized     []byte                `json:"-" gorm:"-"`
	XXX_sizecache        int32                 `json:"-" gorm:"-"`
}

func (m *TicketTabListResp_TabInfo) Reset()         { *m = TicketTabListResp_TabInfo{} }
func (m *TicketTabListResp_TabInfo) String() string { return proto.CompactTextString(m) }
func (*TicketTabListResp_TabInfo) ProtoMessage()    {}
func (*TicketTabListResp_TabInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_98a6c21780e82d22, []int{60, 1}
}

func (m *TicketTabListResp_TabInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TicketTabListResp_TabInfo.Unmarshal(m, b)
}
func (m *TicketTabListResp_TabInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TicketTabListResp_TabInfo.Marshal(b, m, deterministic)
}
func (m *TicketTabListResp_TabInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TicketTabListResp_TabInfo.Merge(m, src)
}
func (m *TicketTabListResp_TabInfo) XXX_Size() int {
	return xxx_messageInfo_TicketTabListResp_TabInfo.Size(m)
}
func (m *TicketTabListResp_TabInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_TicketTabListResp_TabInfo.DiscardUnknown(m)
}

var xxx_messageInfo_TicketTabListResp_TabInfo proto.InternalMessageInfo

func (m *TicketTabListResp_TabInfo) GetId() int64 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *TicketTabListResp_TabInfo) GetTabName() string {
	if m != nil {
		return m.TabName
	}
	return ""
}

func (m *TicketTabListResp_TabInfo) GetDetail() *TicketPoolNewListReq {
	if m != nil {
		return m.Detail
	}
	return nil
}

func (m *TicketTabListResp_TabInfo) GetPublic() int32 {
	if m != nil {
		return m.Public
	}
	return 0
}

func (m *TicketTabListResp_TabInfo) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

type TicketTabCountResp struct {
	Detail               []*TicketTabCountResp_TicketTabCount `protobuf:"bytes,1,rep,name=detail,proto3" json:"detail"`
	XXX_NoUnkeyedLiteral struct{}                             `json:"-" gorm:"-"`
	XXX_unrecognized     []byte                               `json:"-" gorm:"-"`
	XXX_sizecache        int32                                `json:"-" gorm:"-"`
}

func (m *TicketTabCountResp) Reset()         { *m = TicketTabCountResp{} }
func (m *TicketTabCountResp) String() string { return proto.CompactTextString(m) }
func (*TicketTabCountResp) ProtoMessage()    {}
func (*TicketTabCountResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_98a6c21780e82d22, []int{61}
}

func (m *TicketTabCountResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TicketTabCountResp.Unmarshal(m, b)
}
func (m *TicketTabCountResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TicketTabCountResp.Marshal(b, m, deterministic)
}
func (m *TicketTabCountResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TicketTabCountResp.Merge(m, src)
}
func (m *TicketTabCountResp) XXX_Size() int {
	return xxx_messageInfo_TicketTabCountResp.Size(m)
}
func (m *TicketTabCountResp) XXX_DiscardUnknown() {
	xxx_messageInfo_TicketTabCountResp.DiscardUnknown(m)
}

var xxx_messageInfo_TicketTabCountResp proto.InternalMessageInfo

func (m *TicketTabCountResp) GetDetail() []*TicketTabCountResp_TicketTabCount {
	if m != nil {
		return m.Detail
	}
	return nil
}

type TicketTabCountResp_TicketTabCount struct {
	Tab                  []*TicketTabCountResp_TabCountDetail `protobuf:"bytes,1,rep,name=tab,proto3" json:"tab"`
	Project              string                               `protobuf:"bytes,2,opt,name=project,proto3" json:"project"`
	XXX_NoUnkeyedLiteral struct{}                             `json:"-" gorm:"-"`
	XXX_unrecognized     []byte                               `json:"-" gorm:"-"`
	XXX_sizecache        int32                                `json:"-" gorm:"-"`
}

func (m *TicketTabCountResp_TicketTabCount) Reset()         { *m = TicketTabCountResp_TicketTabCount{} }
func (m *TicketTabCountResp_TicketTabCount) String() string { return proto.CompactTextString(m) }
func (*TicketTabCountResp_TicketTabCount) ProtoMessage()    {}
func (*TicketTabCountResp_TicketTabCount) Descriptor() ([]byte, []int) {
	return fileDescriptor_98a6c21780e82d22, []int{61, 0}
}

func (m *TicketTabCountResp_TicketTabCount) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TicketTabCountResp_TicketTabCount.Unmarshal(m, b)
}
func (m *TicketTabCountResp_TicketTabCount) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TicketTabCountResp_TicketTabCount.Marshal(b, m, deterministic)
}
func (m *TicketTabCountResp_TicketTabCount) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TicketTabCountResp_TicketTabCount.Merge(m, src)
}
func (m *TicketTabCountResp_TicketTabCount) XXX_Size() int {
	return xxx_messageInfo_TicketTabCountResp_TicketTabCount.Size(m)
}
func (m *TicketTabCountResp_TicketTabCount) XXX_DiscardUnknown() {
	xxx_messageInfo_TicketTabCountResp_TicketTabCount.DiscardUnknown(m)
}

var xxx_messageInfo_TicketTabCountResp_TicketTabCount proto.InternalMessageInfo

func (m *TicketTabCountResp_TicketTabCount) GetTab() []*TicketTabCountResp_TabCountDetail {
	if m != nil {
		return m.Tab
	}
	return nil
}

func (m *TicketTabCountResp_TicketTabCount) GetProject() string {
	if m != nil {
		return m.Project
	}
	return ""
}

type TicketTabCountResp_TabCountDetail struct {
	TabName              string   `protobuf:"bytes,2,opt,name=tab_name,json=tabName,proto3" json:"tab_name"`
	Count                uint64   `protobuf:"varint,5,opt,name=count,proto3" json:"count"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *TicketTabCountResp_TabCountDetail) Reset()         { *m = TicketTabCountResp_TabCountDetail{} }
func (m *TicketTabCountResp_TabCountDetail) String() string { return proto.CompactTextString(m) }
func (*TicketTabCountResp_TabCountDetail) ProtoMessage()    {}
func (*TicketTabCountResp_TabCountDetail) Descriptor() ([]byte, []int) {
	return fileDescriptor_98a6c21780e82d22, []int{61, 1}
}

func (m *TicketTabCountResp_TabCountDetail) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TicketTabCountResp_TabCountDetail.Unmarshal(m, b)
}
func (m *TicketTabCountResp_TabCountDetail) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TicketTabCountResp_TabCountDetail.Marshal(b, m, deterministic)
}
func (m *TicketTabCountResp_TabCountDetail) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TicketTabCountResp_TabCountDetail.Merge(m, src)
}
func (m *TicketTabCountResp_TabCountDetail) XXX_Size() int {
	return xxx_messageInfo_TicketTabCountResp_TabCountDetail.Size(m)
}
func (m *TicketTabCountResp_TabCountDetail) XXX_DiscardUnknown() {
	xxx_messageInfo_TicketTabCountResp_TabCountDetail.DiscardUnknown(m)
}

var xxx_messageInfo_TicketTabCountResp_TabCountDetail proto.InternalMessageInfo

func (m *TicketTabCountResp_TabCountDetail) GetTabName() string {
	if m != nil {
		return m.TabName
	}
	return ""
}

func (m *TicketTabCountResp_TabCountDetail) GetCount() uint64 {
	if m != nil {
		return m.Count
	}
	return 0
}

type TicketTabUpdateSortReq struct {
	// @gotags: validate:"required"
	SortSetting          string   `protobuf:"bytes,1,opt,name=sort_setting,json=sortSetting,proto3" json:"sort_setting" validate:"required"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *TicketTabUpdateSortReq) Reset()         { *m = TicketTabUpdateSortReq{} }
func (m *TicketTabUpdateSortReq) String() string { return proto.CompactTextString(m) }
func (*TicketTabUpdateSortReq) ProtoMessage()    {}
func (*TicketTabUpdateSortReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_98a6c21780e82d22, []int{62}
}

func (m *TicketTabUpdateSortReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TicketTabUpdateSortReq.Unmarshal(m, b)
}
func (m *TicketTabUpdateSortReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TicketTabUpdateSortReq.Marshal(b, m, deterministic)
}
func (m *TicketTabUpdateSortReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TicketTabUpdateSortReq.Merge(m, src)
}
func (m *TicketTabUpdateSortReq) XXX_Size() int {
	return xxx_messageInfo_TicketTabUpdateSortReq.Size(m)
}
func (m *TicketTabUpdateSortReq) XXX_DiscardUnknown() {
	xxx_messageInfo_TicketTabUpdateSortReq.DiscardUnknown(m)
}

var xxx_messageInfo_TicketTabUpdateSortReq proto.InternalMessageInfo

func (m *TicketTabUpdateSortReq) GetSortSetting() string {
	if m != nil {
		return m.SortSetting
	}
	return ""
}

func init() {
	proto.RegisterEnum("pb.WorkbenchPart", WorkbenchPart_name, WorkbenchPart_value)
	proto.RegisterEnum("pb.FillStatus", FillStatus_name, FillStatus_value)
	proto.RegisterEnum("pb.TkPoolSort", TkPoolSort_name, TkPoolSort_value)
	proto.RegisterEnum("pb.TkTransferOpType", TkTransferOpType_name, TkTransferOpType_value)
	proto.RegisterType((*TicketPoolListResp)(nil), "pb.TicketPoolListResp")
	proto.RegisterType((*TicketInfo)(nil), "pb.TicketInfo")
	proto.RegisterType((*TicketCountReq)(nil), "pb.TicketCountReq")
	proto.RegisterType((*TicketCountResp)(nil), "pb.TicketCountResp")
	proto.RegisterType((*TicketPoolNewListReq)(nil), "pb.TicketPoolNewListReq")
	proto.RegisterType((*TicketPoolHistoryListReq)(nil), "pb.TicketPoolHistoryListReq")
	proto.RegisterType((*TicketPoolNewListResp)(nil), "pb.TicketPoolNewListResp")
	proto.RegisterType((*TicketPoolNewListResp_TicketPoolInfo)(nil), "pb.TicketPoolNewListResp.TicketPoolInfo")
	proto.RegisterType((*TicketPoolTopResp)(nil), "pb.TicketPoolTopResp")
	proto.RegisterType((*TicketPoolNewTopResp)(nil), "pb.TicketPoolNewTopResp")
	proto.RegisterType((*TicketPoolNewTopRespTop)(nil), "pb.TicketPoolNewTopResp.top")
	proto.RegisterType((*TicketPoolNewTopResp_UserInfoResp)(nil), "pb.TicketPoolNewTopResp.UserInfoResp")
	proto.RegisterType((*TicketDialogueInfoResp)(nil), "pb.TicketDialogueInfoResp")
	proto.RegisterType((*TicketRecordResp)(nil), "pb.TicketRecordResp")
	proto.RegisterType((*TicketRecordResp_Record)(nil), "pb.TicketRecordResp.Record")
	proto.RegisterType((*UserInfoResp)(nil), "pb.UserInfoResp")
	proto.RegisterType((*TicketAppraiseResp)(nil), "pb.TicketAppraiseResp")
	proto.RegisterType((*TicketResponse)(nil), "pb.TicketResponse")
	proto.RegisterType((*AssignmentReq)(nil), "pb.AssignmentReq")
	proto.RegisterType((*BatchAssignmentReq)(nil), "pb.BatchAssignmentReq")
	proto.RegisterType((*TicketRemarkReq)(nil), "pb.TicketRemarkReq")
	proto.RegisterType((*TicketBatchRemarkReq)(nil), "pb.TicketBatchRemarkReq")
	proto.RegisterType((*TicketRetaggingReq)(nil), "pb.TicketRetaggingReq")
	proto.RegisterType((*TicketTagRes)(nil), "pb.TicketTagRes")
	proto.RegisterType((*TicketLabel)(nil), "pb.TicketLabel")
	proto.RegisterType((*TicketTagsReq)(nil), "pb.TicketTagsReq")
	proto.RegisterType((*TicketTagsResp)(nil), "pb.TicketTagsResp")
	proto.RegisterType((*TicketHistoriesReq)(nil), "pb.TicketHistoriesReq")
	proto.RegisterType((*TicketIsUserReq)(nil), "pb.TicketIsUserReq")
	proto.RegisterType((*TicketIsUserResp)(nil), "pb.TicketIsUserResp")
	proto.RegisterType((*TicketHistoriesResp)(nil), "pb.TicketHistoriesResp")
	proto.RegisterType((*TicketHistoryInfo)(nil), "pb.TicketHistoryInfo")
	proto.RegisterType((*TicketDialogueResp)(nil), "pb.TicketDialogueResp")
	proto.RegisterType((*TicketHistResp)(nil), "pb.TicketHistResp")
	proto.RegisterType((*TicketGroupInfoResp)(nil), "pb.TicketGroupInfoResp")
	proto.RegisterType((*TicketStatusResp)(nil), "pb.TicketStatusResp")
	proto.RegisterType((*TicketUserListReq)(nil), "pb.TicketUserListReq")
	proto.RegisterType((*TicketIdReq)(nil), "pb.TicketIdReq")
	proto.RegisterType((*CustomerService)(nil), "pb.CustomerService")
	proto.RegisterType((*TicketTransferReq)(nil), "pb.TicketTransferReq")
	proto.RegisterType((*TicketUpgradeReq)(nil), "pb.TicketUpgradeReq")
	proto.RegisterType((*TicketCountStatisReq)(nil), "pb.TicketCountStatisReq")
	proto.RegisterType((*Filter)(nil), "pb.Filter")
	proto.RegisterType((*ServerBtw)(nil), "pb.ServerBtw")
	proto.RegisterType((*TicketReturnPoolReq)(nil), "pb.TicketReturnPoolReq")
	proto.RegisterType((*DataPlatGameItemBatchSaveReq)(nil), "pb.DataPlatGameItemBatchSaveReq")
	proto.RegisterType((*DataPlatGameItemListReq)(nil), "pb.DataPlatGameItemListReq")
	proto.RegisterType((*DataPlatGameItemOptsReq)(nil), "pb.DataPlatGameItemOptsReq")
	proto.RegisterType((*DataPlatGameItemOptsResp)(nil), "pb.DataPlatGameItemOptsResp")
	proto.RegisterType((*DataPlatGameItemOptsResp_Item)(nil), "pb.DataPlatGameItemOptsResp.Item")
	proto.RegisterType((*DataPlatGameItemListResp)(nil), "pb.DataPlatGameItemListResp")
	proto.RegisterType((*DataPlatGameItemListResp_ItemDetail)(nil), "pb.DataPlatGameItemListResp.ItemDetail")
	proto.RegisterType((*DataPlatUserGoldInfoReq)(nil), "pb.DataPlatUserGoldInfoReq")
	proto.RegisterType((*DataPlatUserGoldInfoResp)(nil), "pb.DataPlatUserGoldInfoResp")
	proto.RegisterType((*DataPlatUserItemInfoReq)(nil), "pb.DataPlatUserItemInfoReq")
	proto.RegisterType((*DataPlatUserItemInfoResp)(nil), "pb.DataPlatUserItemInfoResp")
	proto.RegisterType((*DataPlatUserPayInfoReq)(nil), "pb.DataPlatUserPayInfoReq")
	proto.RegisterType((*DataPlatUserPayInfoResp)(nil), "pb.DataPlatUserPayInfoResp")
	proto.RegisterType((*DataPlatUserLoginInfoReq)(nil), "pb.DataPlatUserLoginInfoReq")
	proto.RegisterType((*DataPlatUserLoginInfoResp)(nil), "pb.DataPlatUserLoginInfoResp")
	proto.RegisterType((*TicketDraftSaveReq)(nil), "pb.TicketDraftSaveReq")
	proto.RegisterType((*TicketDraftInfoReq)(nil), "pb.TicketDraftInfoReq")
	proto.RegisterType((*TicketDraftInfoResp)(nil), "pb.TicketDraftInfoResp")
	proto.RegisterType((*TicketTabAddReq)(nil), "pb.TicketTabAddReq")
	proto.RegisterType((*TicketTabEditReq)(nil), "pb.TicketTabEditReq")
	proto.RegisterType((*TicketTabDelReq)(nil), "pb.TicketTabDelReq")
	proto.RegisterType((*TicketTabListResp)(nil), "pb.TicketTabListResp")
	proto.RegisterType((*TicketTabListResp_TicketTabDetail)(nil), "pb.TicketTabListResp.TicketTabDetail")
	proto.RegisterType((*TicketTabListResp_TabInfo)(nil), "pb.TicketTabListResp.TabInfo")
	proto.RegisterType((*TicketTabCountResp)(nil), "pb.TicketTabCountResp")
	proto.RegisterType((*TicketTabCountResp_TicketTabCount)(nil), "pb.TicketTabCountResp.TicketTabCount")
	proto.RegisterType((*TicketTabCountResp_TabCountDetail)(nil), "pb.TicketTabCountResp.TabCountDetail")
	proto.RegisterType((*TicketTabUpdateSortReq)(nil), "pb.TicketTabUpdateSortReq")
}

func init() {
	proto.RegisterFile("ticket.proto", fileDescriptor_98a6c21780e82d22)
}

var fileDescriptor_98a6c21780e82d22 = []byte{
	// 3713 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xd4, 0x3a, 0x4d, 0x8f, 0x1c, 0xd7,
	0x56, 0xe9, 0xae, 0xfe, 0x3c, 0x3d, 0xd3, 0xd3, 0xae, 0x8c, 0xc7, 0xe5, 0x71, 0xf2, 0x6c, 0x57,
	0x12, 0xec, 0x58, 0x7e, 0x4e, 0xe2, 0x28, 0x42, 0x28, 0xa0, 0xc7, 0xc4, 0xce, 0xc7, 0x08, 0xe3,
	0x8c, 0x6a, 0xc6, 0x7e, 0x12, 0x20, 0xb5, 0x6e, 0x57, 0xdd, 0x69, 0x17, 0x53, 0x5f, 0xa9, 0x5b,
	0x3d, 0xa6, 0x83, 0xde, 0x12, 0x89, 0x05, 0x48, 0x28, 0xec, 0x90, 0x60, 0x0f, 0x0b, 0x36, 0x88,
	0x05, 0xb0, 0x42, 0x42, 0x08, 0x36, 0x2c, 0x1f, 0x5b, 0xb6, 0xfc, 0x86, 0xb7, 0x41, 0xe7, 0x9c,
	0x7b, 0xeb, 0xa3, 0x67, 0xa6, 0x27, 0xb1, 0xf0, 0x22, 0xab, 0xae, 0x73, 0xee, 0xb9, 0xf7, 0x9e,
	0x7b, 0xbe, 0xef, 0xb9, 0x0d, 0x1b, 0x45, 0xe8, 0x9f, 0xc8, 0xe2, 0x41, 0x96, 0xa7, 0x45, 0x6a,
	0xb7, 0xb3, 0xd9, 0x2e, 0xc8, 0x64, 0x11, 0x33, 0xec, 0xfe, 0x59, 0x0b, 0xec, 0x23, 0x22, 0x38,
	0x48, 0xd3, 0xe8, 0x49, 0xa8, 0x0a, 0x4f, 0xaa, 0xcc, 0xbe, 0x0d, 0x1b, 0xfe, 0x22, 0xcf, 0x65,
	0x52, 0x4c, 0x33, 0x31, 0x97, 0x4e, 0xeb, 0x56, 0xeb, 0xee, 0xa6, 0x37, 0xd2, 0xb8, 0x03, 0x31,
	0x97, 0xf6, 0x75, 0x18, 0x64, 0x32, 0xe7, 0xe1, 0x36, 0x0d, 0xf7, 0x33, 0x99, 0xd3, 0xd0, 0x36,
	0x74, 0x8b, 0xb4, 0x10, 0x91, 0x63, 0x11, 0x9e, 0x01, 0xdb, 0x85, 0x4e, 0x20, 0x0a, 0xe1, 0x74,
	0x6e, 0x59, 0x77, 0x47, 0x0f, 0xc7, 0x0f, 0xb2, 0xd9, 0x03, 0xde, 0x79, 0x3f, 0x39, 0x4e, 0x3d,
	0x1a, 0x73, 0xff, 0xda, 0x02, 0xa8, 0x90, 0xf6, 0x0d, 0x18, 0x32, 0xf7, 0xd3, 0x30, 0x20, 0x1e,
	0x3a, 0xde, 0x80, 0x11, 0xfb, 0x81, 0xed, 0x40, 0x3f, 0xcb, 0xd3, 0x3f, 0x94, 0x7e, 0x41, 0xfb,
	0x0f, 0x3d, 0x03, 0xda, 0x3b, 0xd0, 0x4b, 0xf3, 0x70, 0x1e, 0x26, 0x4e, 0x87, 0x18, 0xd0, 0x90,
	0x7d, 0x07, 0xb6, 0xfc, 0x34, 0x39, 0x95, 0xb9, 0x0a, 0xd3, 0x64, 0x9a, 0xa4, 0x81, 0x74, 0xba,
	0x44, 0x30, 0xae, 0xd0, 0x4f, 0xd3, 0x40, 0xe2, 0x02, 0xaa, 0x10, 0xc5, 0x42, 0x39, 0x3d, 0x5e,
	0x80, 0x21, 0xfb, 0x6d, 0x00, 0x3f, 0x97, 0xa2, 0x90, 0xc1, 0x54, 0x14, 0x4e, 0x9f, 0x76, 0x1d,
	0x6a, 0xcc, 0x5e, 0x81, 0xec, 0xfa, 0x51, 0xaa, 0x78, 0x74, 0x40, 0xa3, 0x03, 0x46, 0xec, 0x15,
	0xc8, 0x2e, 0x51, 0xa6, 0xb9, 0x33, 0x64, 0x76, 0x35, 0x68, 0xef, 0xc2, 0x40, 0xf8, 0xbe, 0xcc,
	0x70, 0x08, 0x78, 0x96, 0x81, 0x6d, 0x1b, 0x3a, 0x91, 0x48, 0xe6, 0xce, 0x88, 0xf0, 0xf4, 0x6d,
	0x4f, 0xc0, 0x3a, 0x0d, 0x33, 0x67, 0x83, 0x58, 0xc3, 0x4f, 0x5c, 0x21, 0xcb, 0xc3, 0x34, 0x0f,
	0x8b, 0xa5, 0xb3, 0x49, 0xe8, 0x12, 0x46, 0x9e, 0x85, 0xef, 0xa7, 0x8b, 0x84, 0x84, 0xb8, 0xc5,
	0x3c, 0x6b, 0xcc, 0x7e, 0x80, 0x8b, 0xf9, 0x2a, 0x74, 0x26, 0xbc, 0x98, 0xaf, 0x42, 0x5c, 0xcc,
	0x4f, 0xe3, 0x58, 0x26, 0x85, 0x72, 0xae, 0xe8, 0x43, 0x68, 0xd8, 0xfd, 0x9b, 0x16, 0x8c, 0x59,
	0x3f, 0x8f, 0x70, 0xbe, 0x27, 0xbf, 0xa9, 0xab, 0xa1, 0x75, 0xcb, 0xaa, 0xab, 0x61, 0x1b, 0xba,
	0xaa, 0x60, 0xf3, 0xb0, 0xd0, 0x0c, 0x08, 0x68, 0x9c, 0xd6, 0xa2, 0x09, 0x67, 0x4f, 0xdb, 0x21,
	0x7c, 0xe3, 0xb4, 0x5d, 0x5a, 0xe3, 0xcc, 0x69, 0x7b, 0xcd, 0xd3, 0xba, 0x7f, 0xd9, 0x86, 0xad,
	0x06, 0x83, 0x2a, 0xb3, 0xdf, 0x81, 0xcd, 0x4c, 0x26, 0x41, 0x98, 0xcc, 0xa7, 0x74, 0x6a, 0x6d,
	0x49, 0x1b, 0x1a, 0x49, 0x84, 0xf6, 0x7d, 0xb0, 0x17, 0x0a, 0xed, 0xb9, 0x41, 0xd9, 0x26, 0xca,
	0x09, 0x8e, 0x1c, 0xd4, 0xa9, 0x3f, 0x84, 0x6d, 0xa2, 0xf6, 0xd3, 0x38, 0x8b, 0x24, 0xda, 0x03,
	0xd3, 0x5b, 0x44, 0x4f, 0x2b, 0x3d, 0x32, 0x43, 0x3c, 0xe3, 0x2e, 0x4c, 0xca, 0xa5, 0x13, 0x4d,
	0xdd, 0x21, 0xea, 0xb1, 0xe1, 0x23, 0x61, 0xca, 0x7b, 0x70, 0xc5, 0x50, 0x9e, 0x86, 0x99, 0x26,
	0xed, 0x12, 0xe9, 0x96, 0x1e, 0x78, 0x1e, 0x66, 0x4c, 0xfb, 0x1e, 0x8c, 0xcd, 0xd1, 0x35, 0x61,
	0x8f, 0x08, 0x37, 0x0d, 0x96, 0xc8, 0xdc, 0xff, 0x1e, 0xc2, 0x76, 0xe5, 0xe5, 0x4f, 0xe5, 0x4b,
	0x76, 0xf4, 0x75, 0xca, 0x6b, 0x9a, 0x7a, 0x9b, 0x06, 0x2f, 0x32, 0x75, 0xad, 0xc6, 0xd2, 0xd4,
	0x6f, 0xc2, 0x48, 0x9e, 0x8a, 0x68, 0x21, 0x0a, 0x89, 0xc3, 0xac, 0x4d, 0x30, 0xa8, 0xbd, 0xa2,
	0xe6, 0x5f, 0xac, 0x56, 0xe3, 0x5f, 0x68, 0x31, 0xbe, 0x4c, 0xa4, 0xd3, 0xd3, 0x16, 0x83, 0x00,
	0x79, 0xce, 0x0b, 0x91, 0x24, 0x32, 0x72, 0xfa, 0xcc, 0xa4, 0x06, 0x91, 0x3e, 0x12, 0x33, 0x19,
	0x39, 0x03, 0xa6, 0x27, 0x00, 0x2d, 0x26, 0xc9, 0x94, 0x33, 0x64, 0x8b, 0x49, 0x32, 0x65, 0x8c,
	0x1c, 0x18, 0xa3, 0x8d, 0x1c, 0xad, 0x6b, 0x81, 0xe6, 0x39, 0x62, 0xf6, 0x0d, 0x8c, 0xd4, 0x2a,
	0x0c, 0xc8, 0xbf, 0x86, 0x1e, 0x7e, 0x36, 0xe3, 0xd0, 0xe6, 0x4a, 0x1c, 0xfa, 0x18, 0x36, 0x8d,
	0x01, 0x4f, 0x8b, 0x65, 0x26, 0x9d, 0xf1, 0xad, 0xd6, 0xdd, 0x31, 0x07, 0xb8, 0x2f, 0xc2, 0xa8,
	0x90, 0xf9, 0xe7, 0xc9, 0x22, 0xf6, 0x36, 0x0c, 0xd1, 0xd1, 0x32, 0x6b, 0x7a, 0xc1, 0xd6, 0x8a,
	0xcf, 0xef, 0x40, 0x2f, 0x97, 0xb1, 0xc8, 0x4f, 0xc8, 0x2b, 0x87, 0x9e, 0x86, 0xec, 0x87, 0xb0,
	0xa1, 0x43, 0x06, 0xef, 0x73, 0x85, 0xf6, 0xd9, 0xc2, 0x7d, 0x1e, 0x31, 0x1e, 0x97, 0xf6, 0x46,
	0x7e, 0x05, 0xd4, 0xa3, 0x8e, 0xdd, 0x8c, 0x3a, 0x36, 0x74, 0x28, 0x76, 0xbf, 0x49, 0x1e, 0x44,
	0xdf, 0x78, 0x4e, 0xfc, 0x9d, 0xaa, 0xf0, 0x5b, 0xe9, 0x6c, 0x6b, 0xd7, 0x12, 0x73, 0x79, 0x18,
	0x7e, 0x2b, 0xed, 0xab, 0xd0, 0xf3, 0x05, 0x49, 0xe0, 0x2a, 0x4b, 0xdb, 0x17, 0x78, 0xfc, 0xdb,
	0xb0, 0xa1, 0x96, 0xaa, 0x90, 0xf1, 0x94, 0x55, 0xb1, 0x43, 0x83, 0x23, 0xc6, 0x3d, 0x21, 0x85,
	0x6c, 0x43, 0xf7, 0x38, 0x94, 0x51, 0xe0, 0x5c, 0x23, 0x16, 0x18, 0xc0, 0xf5, 0x42, 0x85, 0x26,
	0xee, 0x38, 0xb7, 0x5a, 0x77, 0x07, 0x5e, 0x37, 0x54, 0xcf, 0xc3, 0x0c, 0x0d, 0x2f, 0x54, 0xd3,
	0x45, 0x36, 0xcf, 0x45, 0x20, 0x9d, 0xeb, 0x34, 0x34, 0x0c, 0xd5, 0x33, 0x46, 0xd8, 0xf7, 0x61,
	0x50, 0x88, 0x39, 0x0b, 0x60, 0x97, 0x04, 0x70, 0xa5, 0x12, 0xf4, 0x91, 0x98, 0x93, 0xac, 0xfb,
	0x85, 0x98, 0xd3, 0xf1, 0xef, 0x40, 0x5f, 0xa5, 0x79, 0x31, 0x9d, 0x2d, 0x9d, 0x1b, 0x95, 0x56,
	0x8e, 0x4e, 0xd0, 0x0d, 0x0e, 0xd3, 0xbc, 0xf0, 0x7a, 0x38, 0xfc, 0xd9, 0x12, 0x59, 0x4c, 0xf3,
	0x40, 0xe6, 0xce, 0x5b, 0xcc, 0x22, 0x01, 0x68, 0xc8, 0x4a, 0x8a, 0xdc, 0x7f, 0xc1, 0xfb, 0xbd,
	0x4d, 0x12, 0x01, 0x46, 0xd1, 0xfa, 0x37, 0x60, 0x48, 0x71, 0x80, 0x86, 0x7f, 0x42, 0x27, 0x1f,
	0x20, 0x82, 0x06, 0x6d, 0xe8, 0x28, 0x3c, 0xde, 0x4d, 0x8e, 0xdd, 0xf8, 0x6d, 0x5f, 0x83, 0x3e,
	0x39, 0x75, 0x1e, 0x3b, 0xb7, 0x58, 0xb9, 0xa7, 0x61, 0xf6, 0x28, 0x8f, 0xf1, 0xd8, 0xa5, 0x89,
	0x29, 0xe7, 0x36, 0x87, 0x69, 0x63, 0x63, 0xca, 0x7e, 0x0b, 0x86, 0xc6, 0x3e, 0x94, 0xe3, 0xb2,
	0x37, 0x96, 0x08, 0xcc, 0xc5, 0x85, 0x14, 0x31, 0x4d, 0x7d, 0xe7, 0x96, 0x75, 0xd7, 0xf2, 0xfa,
	0x08, 0xe3, 0xc4, 0xb7, 0x01, 0x72, 0x99, 0x66, 0x32, 0x99, 0x26, 0x8b, 0xd8, 0x79, 0x97, 0xd7,
	0x65, 0xcc, 0xd3, 0x45, 0x8c, 0x27, 0xd4, 0xa2, 0xa6, 0xf1, 0xf7, 0x68, 0x1c, 0x34, 0x0a, 0x09,
	0xde, 0x42, 0x93, 0xf0, 0x4f, 0xc4, 0x5c, 0xee, 0x07, 0xce, 0xaf, 0xf1, 0xc6, 0x25, 0x02, 0x95,
	0x3f, 0x17, 0xb1, 0x9c, 0xea, 0xe4, 0xe9, 0xdc, 0x21, 0x82, 0x11, 0xe2, 0x9e, 0x33, 0x0a, 0x19,
	0x50, 0x69, 0x74, 0x2a, 0x59, 0x46, 0x77, 0x49, 0x46, 0x43, 0xc2, 0x90, 0x90, 0xde, 0x85, 0xf1,
	0xb7, 0x69, 0x22, 0x29, 0xd4, 0x45, 0xf2, 0x54, 0x46, 0xce, 0xfb, 0xb4, 0xc6, 0x06, 0x62, 0x9f,
	0x87, 0xd9, 0x13, 0xc4, 0xa1, 0xd8, 0x32, 0xb1, 0x9c, 0x8a, 0x28, 0x72, 0xee, 0xd1, 0xf9, 0x7a,
	0x99, 0x58, 0xee, 0x45, 0x91, 0xfb, 0x02, 0x9c, 0x2a, 0xb0, 0x7d, 0x15, 0xaa, 0x22, 0xcd, 0x97,
	0x26, 0xb8, 0xad, 0xad, 0x1e, 0x8c, 0xf9, 0xb7, 0x2f, 0x32, 0x7f, 0xab, 0x69, 0xfe, 0xee, 0x2f,
	0x3b, 0x70, 0xf5, 0x9c, 0x18, 0xfa, 0x9a, 0x8a, 0xa5, 0xdf, 0x6c, 0x14, 0x4b, 0x77, 0xab, 0x62,
	0x69, 0x65, 0xf3, 0x1a, 0xb6, 0x2a, 0xa3, 0x76, 0xff, 0xce, 0x32, 0x69, 0xda, 0x0c, 0x34, 0x23,
	0x7d, 0xa3, 0x5a, 0x6a, 0x88, 0xa9, 0xbd, 0x22, 0xa6, 0x1d, 0xe8, 0x05, 0xb2, 0x10, 0x21, 0xb3,
	0x37, 0xf4, 0x34, 0x84, 0xf1, 0x2b, 0x97, 0xfe, 0x0b, 0x91, 0xcf, 0x25, 0xa5, 0xb1, 0x96, 0x57,
	0xc2, 0x8d, 0xe8, 0x5e, 0xaf, 0x9e, 0x6e, 0xc3, 0xc6, 0x4b, 0x11, 0x16, 0x98, 0xd8, 0x8a, 0x30,
	0x96, 0x94, 0xaa, 0x86, 0xde, 0x48, 0xe3, 0x8e, 0xc2, 0x58, 0x9a, 0x40, 0xdd, 0x6f, 0x54, 0x23,
	0x65, 0xa0, 0x1c, 0xac, 0x04, 0xca, 0xf7, 0xe1, 0x8a, 0x9f, 0xc7, 0x64, 0x3a, 0xe4, 0x85, 0xc7,
	0x91, 0x98, 0x53, 0x71, 0x35, 0xf0, 0xc6, 0x7e, 0x1e, 0x3f, 0x0f, 0xb3, 0x67, 0x4a, 0xe6, 0x5f,
	0x44, 0x62, 0xce, 0x39, 0x44, 0xfa, 0x27, 0x32, 0xa0, 0x12, 0x6b, 0xe0, 0x19, 0x10, 0x4d, 0x2b,
	0xf0, 0x79, 0xea, 0x88, 0x46, 0x7a, 0x81, 0x4f, 0x53, 0xde, 0x85, 0x71, 0xe0, 0x4f, 0x39, 0xe5,
	0x31, 0xc3, 0x9c, 0x11, 0x36, 0x02, 0x9f, 0xc2, 0xad, 0x24, 0x8e, 0x9b, 0xe5, 0xd5, 0xe6, 0x6a,
	0x79, 0x75, 0x79, 0x74, 0x74, 0xff, 0xa9, 0x0d, 0x57, 0x2a, 0x65, 0x1d, 0xa5, 0x19, 0x19, 0xd5,
	0x5a, 0xe3, 0xad, 0x0b, 0xa5, 0xbd, 0x22, 0x94, 0x5d, 0x18, 0x24, 0xa1, 0x7f, 0x92, 0x88, 0x58,
	0x6a, 0x9d, 0x95, 0xf0, 0x0a, 0xb3, 0x9d, 0x73, 0x6a, 0xc1, 0x45, 0x18, 0xe8, 0x5a, 0x03, 0x3f,
	0x31, 0x3c, 0x88, 0x2c, 0x2b, 0xdd, 0x9b, 0x35, 0x06, 0x22, 0xcb, 0x8c, 0x77, 0xeb, 0x5c, 0xa9,
	0x15, 0x86, 0xb9, 0xb2, 0x66, 0x68, 0x83, 0xa6, 0xa1, 0xd5, 0xeb, 0xb6, 0xe1, 0x4a, 0x95, 0xfa,
	0x4e, 0x69, 0x33, 0x40, 0x71, 0x7a, 0xc4, 0x71, 0xfa, 0x10, 0x4b, 0xc6, 0xd2, 0x80, 0xb4, 0x75,
	0x8c, 0x4a, 0xeb, 0x70, 0xbf, 0xeb, 0xac, 0x14, 0x36, 0x5a, 0x7c, 0xbb, 0xff, 0xd8, 0x02, 0xab,
	0x48, 0xcf, 0x88, 0xd1, 0x7a, 0x45, 0x31, 0xd2, 0xbc, 0x52, 0x8c, 0x36, 0x74, 0x8e, 0x33, 0x12,
	0x20, 0x2a, 0x93, 0xbe, 0x2b, 0xd9, 0x51, 0x89, 0x81, 0xb2, 0x5b, 0x8d, 0x8d, 0xbd, 0xb3, 0xb1,
	0xb1, 0x94, 0x9e, 0xa5, 0xa5, 0xb7, 0xfb, 0xaf, 0x6d, 0xd8, 0x40, 0xa3, 0x25, 0x67, 0x46, 0x3b,
	0x28, 0x4b, 0x1c, 0x64, 0xbe, 0x6b, 0x4a, 0x1c, 0xac, 0xd1, 0x45, 0x21, 0xe7, 0x69, 0xbe, 0x34,
	0x9c, 0x1b, 0x78, 0xa5, 0x72, 0xb3, 0x56, 0x2f, 0x29, 0xb5, 0x6a, 0xaa, 0xa3, 0x2b, 0x02, 0x5d,
	0x4d, 0xdd, 0x84, 0x51, 0x20, 0x4f, 0x43, 0x5f, 0x87, 0xea, 0x2e, 0x2b, 0x9b, 0x51, 0x14, 0xab,
	0xaf, 0x42, 0x2f, 0x4f, 0xe3, 0xe9, 0x7c, 0x46, 0x86, 0xd0, 0xf2, 0xba, 0x79, 0x1a, 0x7f, 0x39,
	0xe3, 0x14, 0x13, 0x8b, 0x30, 0x99, 0xe6, 0x69, 0x4c, 0xa6, 0xd0, 0xc2, 0x14, 0x83, 0x18, 0x2f,
	0x8d, 0x1b, 0xa1, 0x42, 0x7b, 0x70, 0x19, 0x2a, 0xea, 0x65, 0xd8, 0x70, 0xa5, 0x0c, 0x43, 0x46,
	0xd1, 0x30, 0xf3, 0x25, 0x15, 0x6e, 0xc8, 0x28, 0x83, 0xb8, 0x61, 0xaa, 0x4a, 0xb9, 0xf2, 0xd5,
	0x68, 0x98, 0x2a, 0x2d, 0x55, 0xf7, 0x8f, 0x61, 0x87, 0x6d, 0xe2, 0x71, 0x28, 0xa2, 0x74, 0xbe,
	0x90, 0xa5, 0x30, 0x3f, 0x82, 0xed, 0x40, 0xe3, 0xa6, 0x61, 0x72, 0x9c, 0xe6, 0xb1, 0x28, 0x70,
	0x09, 0xae, 0x7d, 0xdf, 0x0c, 0x6a, 0xf4, 0x7a, 0x08, 0xb9, 0xe0, 0xf2, 0x4b, 0xe9, 0x22, 0xd8,
	0x80, 0x95, 0x66, 0xac, 0x9a, 0x66, 0xdc, 0xff, 0x68, 0xc3, 0x84, 0x77, 0xf7, 0xa4, 0x9f, 0xe6,
	0xc1, 0x6b, 0xcb, 0x10, 0x1f, 0x34, 0x32, 0xc4, 0x8d, 0x2a, 0x43, 0x54, 0xfb, 0x3e, 0xd0, 0x9f,
	0x9c, 0x14, 0xfe, 0xbe, 0x05, 0x3d, 0x46, 0xfc, 0x38, 0x92, 0x81, 0xfb, 0x9d, 0xb5, 0xe2, 0x0b,
	0xd7, 0xb9, 0xf6, 0x23, 0x9f, 0xd4, 0xd7, 0x95, 0x42, 0xcc, 0x9f, 0xa2, 0x4b, 0xfe, 0x98, 0x1d,
	0xa2, 0x2e, 0x2e, 0x73, 0x03, 0x1e, 0xd6, 0xee, 0xfb, 0x0d, 0x47, 0x68, 0x7d, 0x7f, 0x47, 0xb0,
	0xc7, 0xd0, 0xd6, 0x7d, 0x82, 0xa1, 0xd7, 0x0e, 0xb3, 0x66, 0x2d, 0xa7, 0x53, 0x55, 0x55, 0xcb,
	0x9d, 0xad, 0xc4, 0xc6, 0x9c, 0xef, 0xea, 0x95, 0x98, 0xfb, 0x17, 0x65, 0xc3, 0x68, 0x2f, 0xcb,
	0x72, 0x11, 0x2a, 0x49, 0xaa, 0x69, 0xca, 0xb8, 0xb5, 0x2a, 0x63, 0x1d, 0xb9, 0xdb, 0x55, 0x5e,
	0xff, 0x08, 0xb6, 0x73, 0xc9, 0x7d, 0x85, 0x80, 0x3c, 0x4d, 0xef, 0xc9, 0x36, 0xfe, 0x66, 0x73,
	0x8c, 0x8b, 0xc0, 0xea, 0x5e, 0xd4, 0xa9, 0xdf, 0x8b, 0xdc, 0xff, 0x6a, 0x9b, 0x6a, 0x07, 0x59,
	0x49, 0x13, 0x25, 0xed, 0x0f, 0x61, 0x50, 0xa4, 0x19, 0xf9, 0x38, 0x31, 0x33, 0x7a, 0x78, 0xb5,
	0x59, 0x42, 0xe9, 0x3c, 0xe1, 0xf5, 0x8b, 0x34, 0xa3, 0xfa, 0xe8, 0xa7, 0xba, 0x92, 0xa7, 0x29,
	0x6d, 0x9a, 0x32, 0xc1, 0x29, 0x75, 0x03, 0xe4, 0xda, 0x9e, 0xc8, 0x3f, 0x81, 0x51, 0x4e, 0xbe,
	0xc4, 0x13, 0x2c, 0x9a, 0xb0, 0x7d, 0x9e, 0x13, 0x7a, 0xc0, 0x84, 0x7a, 0x1a, 0xe0, 0xb9, 0x16,
	0x3c, 0x8b, 0x5d, 0x77, 0xa7, 0x9a, 0x65, 0x02, 0x16, 0xcd, 0x1b, 0x12, 0x25, 0x4d, 0xbb, 0x0f,
	0xfd, 0x17, 0x5c, 0xdb, 0x52, 0x82, 0x19, 0x3d, 0xb4, 0xab, 0x39, 0x5f, 0xe9, 0x4a, 0xd0, 0x33,
	0x24, 0xf6, 0xcf, 0x60, 0x4b, 0xfb, 0xb0, 0xd0, 0x2a, 0x22, 0xf3, 0x6c, 0xec, 0x54, 0x57, 0x9e,
	0x37, 0x2e, 0x1a, 0x38, 0xf7, 0x2b, 0xd8, 0xdc, 0x53, 0x2a, 0x9c, 0x27, 0xb1, 0x4c, 0x2e, 0xaf,
	0xa4, 0xd7, 0x64, 0x51, 0xf7, 0x77, 0xc1, 0xfe, 0x4c, 0x14, 0xfe, 0x8b, 0xb5, 0xcb, 0x7d, 0xef,
	0xa4, 0xec, 0x7e, 0x65, 0x9a, 0x3b, 0x1e, 0x69, 0xfe, 0x52, 0xd6, 0xc8, 0x73, 0x92, 0x42, 0x26,
	0x65, 0x8b, 0x50, 0x83, 0xee, 0xd7, 0xa6, 0x6e, 0x20, 0xf6, 0xaa, 0xe5, 0x9a, 0xd7, 0x30, 0xe6,
	0xad, 0x76, 0x0d, 0xbb, 0x78, 0xc1, 0x27, 0xc6, 0x2d, 0x3c, 0x59, 0x88, 0xf9, 0x3c, 0x4c, 0xe6,
	0x97, 0x72, 0x77, 0x1d, 0x93, 0xdf, 0x4c, 0x46, 0x1c, 0x6a, 0xb1, 0x04, 0xe8, 0x13, 0xbc, 0x1f,
	0xb8, 0x5f, 0xc0, 0x06, 0xaf, 0x76, 0x24, 0xe6, 0x9e, 0x54, 0xaf, 0xbc, 0xce, 0xe7, 0x30, 0xe2,
	0x75, 0xf8, 0x22, 0xfe, 0xaa, 0xcb, 0xdc, 0x87, 0xcd, 0x92, 0x1d, 0x75, 0x99, 0x06, 0xdd, 0x4f,
	0x8d, 0x3b, 0x32, 0xb5, 0xca, 0xec, 0xf7, 0xa1, 0xcf, 0xa9, 0x82, 0x45, 0x3a, 0xe2, 0xa6, 0x45,
	0x8d, 0x33, 0xcf, 0x8c, 0xbb, 0x9e, 0x91, 0x23, 0x5f, 0xe6, 0x42, 0xa9, 0x2e, 0x95, 0xe3, 0x4d,
	0xf4, 0xc5, 0x88, 0xaa, 0x74, 0x0c, 0xd3, 0x1c, 0x64, 0x80, 0x51, 0x18, 0xa6, 0xdd, 0x3b, 0xc6,
	0x6c, 0xf6, 0x15, 0xba, 0x33, 0x2e, 0xb8, 0x0d, 0x5d, 0x19, 0x67, 0x49, 0xaa, 0x53, 0x31, 0x03,
	0xee, 0x3d, 0x93, 0xbb, 0x0d, 0xa1, 0xca, 0x6a, 0x09, 0xac, 0x55, 0x4f, 0x60, 0xee, 0x6f, 0xc3,
	0x9b, 0x67, 0x18, 0xa5, 0xa3, 0x72, 0x5a, 0xe6, 0x73, 0x5e, 0x6d, 0xfa, 0x69, 0x9a, 0x2f, 0x6b,
	0xcd, 0xee, 0x7f, 0x6b, 0x99, 0xc2, 0xbf, 0x36, 0x76, 0xa9, 0x41, 0xa7, 0x99, 0xcc, 0x45, 0x21,
	0x8d, 0xfd, 0x69, 0x10, 0xa7, 0xa5, 0xd9, 0xb4, 0x91, 0x9e, 0x07, 0x69, 0xf6, 0x98, 0x13, 0xf4,
	0x05, 0x91, 0x73, 0x25, 0x6a, 0x77, 0x57, 0xa3, 0xf6, 0xbb, 0x30, 0x3e, 0xc6, 0xfc, 0x56, 0xf1,
	0xc3, 0xdd, 0xc5, 0x0d, 0xc4, 0x1e, 0x19, 0x75, 0xff, 0x6d, 0xdb, 0xa8, 0xac, 0x1e, 0xbe, 0x6a,
	0xc5, 0x42, 0xab, 0x51, 0x2c, 0xac, 0x36, 0x16, 0x57, 0xf6, 0xdc, 0x85, 0x01, 0x1f, 0x89, 0xda,
	0xc3, 0xfa, 0x18, 0x0c, 0xe3, 0x19, 0x89, 0x9f, 0x3c, 0x8d, 0xa4, 0x6e, 0xed, 0x0f, 0x10, 0xe1,
	0xa5, 0x11, 0xdd, 0x6d, 0x38, 0xb4, 0xd6, 0x72, 0x35, 0x87, 0x50, 0xd3, 0xa9, 0x09, 0x95, 0x3e,
	0x89, 0xe9, 0x1a, 0x87, 0x8a, 0xf9, 0xe6, 0x06, 0x55, 0x24, 0x95, 0x6e, 0xe9, 0x33, 0x40, 0x55,
	0x9b, 0xe6, 0x94, 0x4a, 0x94, 0x01, 0x1d, 0x7e, 0xa4, 0x71, 0x74, 0xfb, 0xc3, 0x42, 0x2a, 0xf4,
	0x8b, 0x45, 0x2e, 0x4d, 0x53, 0x5f, 0x83, 0xb8, 0xe4, 0x69, 0x18, 0xc8, 0x54, 0xa7, 0x6c, 0x06,
	0xdc, 0x3f, 0x32, 0xae, 0x61, 0xa2, 0xf6, 0xa5, 0x4d, 0x8a, 0xd2, 0xa4, 0x87, 0x1e, 0x7d, 0xd7,
	0x74, 0x69, 0xad, 0xd1, 0x65, 0x67, 0x45, 0xae, 0xee, 0xbf, 0xb7, 0x8c, 0xbd, 0x7e, 0x99, 0xa7,
	0x8b, 0xac, 0xac, 0xa9, 0x7e, 0x74, 0xe6, 0x56, 0xfa, 0xe8, 0x21, 0xf9, 0xe1, 0x5a, 0x1f, 0xfd,
	0xc0, 0x38, 0x18, 0x7a, 0xb3, 0x69, 0x0b, 0xd5, 0x6f, 0x76, 0x4c, 0x5e, 0xc2, 0xee, 0x77, 0x2d,
	0x13, 0x30, 0xf7, 0x83, 0x4b, 0xe3, 0xce, 0x27, 0x65, 0x9e, 0x55, 0x4b, 0x55, 0xc5, 0x9e, 0xf1,
	0xc3, 0xcd, 0xca, 0xeb, 0x0f, 0x97, 0xca, 0xdb, 0x2c, 0xcc, 0xa7, 0x69, 0x0b, 0x52, 0x95, 0x6f,
	0x5d, 0xd4, 0x79, 0xea, 0xac, 0x74, 0x9e, 0xa6, 0xb0, 0xf5, 0x68, 0xa1, 0x8a, 0x34, 0x96, 0xf9,
	0xa1, 0xcc, 0xb1, 0xf8, 0xa4, 0xca, 0x2e, 0xd0, 0xdc, 0xb7, 0xf9, 0x9e, 0x4e, 0x1d, 0xcb, 0xda,
	0xb3, 0x45, 0xd7, 0x03, 0x42, 0xf1, 0x43, 0xc1, 0x0d, 0x18, 0x62, 0x69, 0xcd, 0x86, 0x6c, 0xd1,
	0xf0, 0x00, 0x11, 0x54, 0x68, 0xff, 0xb2, 0x0c, 0x44, 0x47, 0xb9, 0x48, 0xd4, 0x31, 0x87, 0xc8,
	0xb5, 0x67, 0xff, 0x29, 0x5a, 0x46, 0xfd, 0xcc, 0x5c, 0xfb, 0x9c, 0x98, 0x05, 0xbe, 0xce, 0xa8,
	0x17, 0xdd, 0x4b, 0xb3, 0xb2, 0x0d, 0xad, 0xf3, 0xa6, 0xd5, 0xc8, 0x9b, 0xf6, 0x1d, 0x98, 0x84,
	0x6a, 0xaa, 0x96, 0x89, 0x3f, 0x15, 0xe1, 0x54, 0x46, 0xc7, 0xfa, 0xd5, 0x6e, 0xe0, 0x6d, 0x86,
	0xea, 0x70, 0x99, 0xf8, 0x7b, 0xe1, 0xe7, 0x88, 0x44, 0xb5, 0x7d, 0xb3, 0x90, 0x8a, 0xee, 0x6b,
	0x6c, 0x3a, 0x25, 0x8c, 0xfa, 0x17, 0x89, 0x7a, 0x29, 0x73, 0x7d, 0x8d, 0xd0, 0x90, 0xbb, 0x6f,
	0x6c, 0x45, 0x77, 0x8f, 0xbf, 0x4f, 0xc1, 0x60, 0x3a, 0xcf, 0xfa, 0x12, 0xa6, 0x41, 0xf7, 0xd0,
	0x14, 0x0c, 0x24, 0x4f, 0xb4, 0xbd, 0x90, 0x32, 0x93, 0x0b, 0xbd, 0x63, 0xea, 0x3d, 0xeb, 0x3a,
	0x13, 0xaa, 0x6e, 0xb4, 0xa7, 0x47, 0xec, 0x6b, 0xd0, 0xe7, 0xce, 0x39, 0xdf, 0x21, 0x2d, 0xaf,
	0x47, 0xad, 0x73, 0xe5, 0xfe, 0x4f, 0x0b, 0x7a, 0x4c, 0x5b, 0x76, 0x0b, 0x9a, 0xf7, 0x32, 0xea,
	0x16, 0x1c, 0x54, 0x4f, 0x32, 0xaa, 0x10, 0xb9, 0x56, 0xa2, 0x8e, 0x9c, 0x84, 0xa1, 0x58, 0x74,
	0x1d, 0x06, 0x32, 0x09, 0x2a, 0x0d, 0x0f, 0xbd, 0xbe, 0x4c, 0xca, 0x30, 0x55, 0x5d, 0x71, 0x1a,
	0x2f, 0x28, 0xe6, 0xbe, 0xd1, 0xad, 0xbd, 0xb8, 0xe1, 0x3e, 0x32, 0x3f, 0xc5, 0x62, 0x38, 0x50,
	0x5a, 0xa2, 0x43, 0xc6, 0x60, 0x0d, 0x74, 0xbf, 0x1c, 0x16, 0x79, 0x4e, 0xbd, 0x8b, 0x11, 0x5b,
	0xfc, 0x21, 0x61, 0x3f, 0x2b, 0x5e, 0x1a, 0xea, 0xbd, 0x3c, 0x77, 0x3f, 0x80, 0x61, 0x89, 0xc7,
	0x6b, 0xc0, 0xbc, 0x60, 0xaf, 0xb3, 0x3c, 0xfc, 0x44, 0x4c, 0xa4, 0xa3, 0x8b, 0xe5, 0xe1, 0xa7,
	0xfb, 0xd0, 0xc4, 0x29, 0x4f, 0x16, 0x8b, 0x3c, 0xc1, 0x62, 0xfd, 0x32, 0xb5, 0xb9, 0xcf, 0xe0,
	0xad, 0xc7, 0xa2, 0x10, 0x07, 0x91, 0x28, 0xbe, 0x14, 0xb1, 0xdc, 0x2f, 0x64, 0x4c, 0x75, 0xdd,
	0xa1, 0x38, 0x95, 0x67, 0x9e, 0xb9, 0x56, 0xef, 0xbb, 0x18, 0xec, 0xf9, 0x4e, 0xa9, 0xcb, 0x4d,
	0x44, 0xe0, 0xa5, 0xd2, 0x0d, 0xe0, 0xda, 0xea, 0xb2, 0xe7, 0x3e, 0x9c, 0x35, 0x56, 0xfc, 0xa1,
	0xef, 0x2a, 0xee, 0xc7, 0x67, 0x77, 0xf9, 0x3a, 0x2b, 0xd4, 0xda, 0x5d, 0xdc, 0x3f, 0x69, 0x81,
	0x73, 0xfe, 0x2c, 0x95, 0xd9, 0x9f, 0x34, 0x6a, 0x90, 0xdb, 0xa8, 0x9b, 0x8b, 0x68, 0x1f, 0x20,
	0xa0, 0x1b, 0x04, 0x0f, 0xa1, 0x83, 0x10, 0xa5, 0x2e, 0x11, 0x2d, 0xa4, 0xde, 0x93, 0x81, 0xaa,
	0xdd, 0xc1, 0x52, 0xd2, 0xed, 0x8e, 0x7f, 0x69, 0x9f, 0xe5, 0xe3, 0xb5, 0x36, 0xc6, 0x3f, 0x6d,
	0xb4, 0x3d, 0xee, 0x9c, 0x77, 0xb6, 0xb2, 0x37, 0x8e, 0x00, 0xa7, 0x24, 0x7d, 0xc2, 0x3f, 0x6f,
	0x01, 0x54, 0xc8, 0x5a, 0x14, 0xed, 0x50, 0x14, 0xbd, 0x06, 0xfd, 0xb0, 0x90, 0xb1, 0x69, 0x7d,
	0x0c, 0xbd, 0x1e, 0x82, 0xfb, 0xf4, 0xfe, 0x47, 0x03, 0xf5, 0xa6, 0x2a, 0x22, 0x9e, 0xea, 0xa6,
	0xea, 0x22, 0x0b, 0x56, 0x12, 0xaf, 0xc6, 0xac, 0x14, 0x34, 0xdd, 0x66, 0x41, 0x53, 0x37, 0x30,
	0xcc, 0x50, 0x5f, 0xa6, 0x51, 0xc0, 0x89, 0x79, 0x9d, 0x81, 0xe9, 0x4e, 0x63, 0xbb, 0xea, 0xd2,
	0xae, 0x36, 0x38, 0x9a, 0x6f, 0xb5, 0xee, 0x3f, 0xd4, 0x6c, 0xa5, 0xb9, 0x8d, 0xca, 0xd6, 0xec,
	0xb3, 0x03, 0x3d, 0x8c, 0x12, 0x73, 0xe3, 0x17, 0x1a, 0x42, 0xfc, 0x4c, 0x1e, 0xa7, 0xb9, 0x91,
	0x84, 0x86, 0x50, 0x5f, 0xe2, 0x18, 0x03, 0x21, 0x8b, 0x80, 0x01, 0xcc, 0x4c, 0x3c, 0x8f, 0x03,
	0x93, 0xee, 0xa1, 0x30, 0x8a, 0x62, 0x13, 0x15, 0x0b, 0x42, 0x95, 0xdd, 0x65, 0x0d, 0xb9, 0xbf,
	0x68, 0xca, 0x06, 0xd5, 0xf6, 0x2a, 0xb2, 0xa9, 0xe9, 0xd4, 0x6a, 0xe8, 0x74, 0xb5, 0x5e, 0x5a,
	0x11, 0xda, 0xff, 0xae, 0x08, 0xad, 0xda, 0x7f, 0xad, 0xd0, 0x5e, 0xcd, 0x84, 0x2a, 0x51, 0x77,
	0x2e, 0x10, 0x75, 0xf7, 0x7c, 0x51, 0xf7, 0xd6, 0x88, 0xba, 0xbf, 0x46, 0xd4, 0x83, 0x86, 0xa8,
	0x7d, 0xd8, 0xa9, 0x1f, 0xf5, 0x40, 0x2c, 0x5f, 0x83, 0x15, 0xfe, 0xaa, 0xd5, 0x54, 0x68, 0xb9,
	0xcb, 0x5a, 0x79, 0xde, 0x86, 0x8d, 0x2c, 0x4f, 0x83, 0x85, 0x5f, 0xd4, 0x43, 0xf4, 0x48, 0xe3,
	0x48, 0x78, 0x37, 0x61, 0x94, 0x89, 0xe5, 0xd4, 0x24, 0x38, 0x96, 0x2d, 0x64, 0x62, 0xf9, 0xa8,
	0xfa, 0x97, 0x40, 0x96, 0x87, 0xbe, 0x11, 0x2e, 0x03, 0x38, 0x6d, 0x26, 0x54, 0xe8, 0x4f, 0x79,
	0x4c, 0x1b, 0x26, 0xa1, 0x0e, 0x88, 0x60, 0x17, 0x06, 0x1c, 0xa9, 0xfc, 0xa5, 0x96, 0x73, 0x09,
	0xd7, 0x0a, 0x4e, 0x96, 0xb2, 0xe9, 0x6a, 0xd2, 0x3b, 0x65, 0x58, 0xfb, 0xff, 0x4f, 0x0f, 0xc1,
	0xbd, 0xc2, 0x95, 0x4d, 0x6b, 0x7a, 0x92, 0xce, 0xc3, 0xe4, 0x35, 0x08, 0xf9, 0x9f, 0x5b, 0x70,
	0xfd, 0x82, 0x7d, 0xd6, 0x8a, 0xf9, 0x16, 0x6c, 0x1c, 0x63, 0x39, 0x4f, 0xcd, 0xce, 0xd2, 0x76,
	0xe1, 0x38, 0x7b, 0x4c, 0x28, 0xbe, 0x64, 0xd7, 0x7b, 0xa1, 0xd6, 0x99, 0x5e, 0x28, 0x37, 0x1b,
	0x3b, 0x65, 0xb3, 0xf1, 0x2a, 0xf4, 0xc2, 0x6c, 0x1a, 0xa5, 0xbe, 0x16, 0x6d, 0x37, 0xcc, 0x9e,
	0xa4, 0x3e, 0x75, 0x19, 0x90, 0xa9, 0x4a, 0x44, 0x7d, 0x82, 0xf7, 0x0a, 0xf7, 0xf7, 0xcb, 0x7b,
	0x64, 0x2e, 0x8e, 0x0b, 0x93, 0xbb, 0x57, 0x83, 0xf4, 0xda, 0x0e, 0xf5, 0x85, 0x75, 0xa6, 0xfb,
	0x51, 0x63, 0x71, 0x23, 0xfa, 0xb5, 0x55, 0xc5, 0x2f, 0x4c, 0x25, 0x52, 0x9b, 0xa2, 0xb2, 0xff,
	0x27, 0x86, 0xf8, 0xe5, 0x3d, 0x28, 0xdf, 0x16, 0x3b, 0xe6, 0xe5, 0x3d, 0xd0, 0x2f, 0x8b, 0xee,
	0xa9, 0xe9, 0x5a, 0x1c, 0x89, 0xd9, 0x5e, 0x40, 0xd7, 0x11, 0x6a, 0x80, 0xcf, 0xa6, 0xe5, 0xd5,
	0x85, 0x1a, 0xe0, 0x33, 0x13, 0x42, 0xb2, 0xc5, 0x2c, 0x0a, 0x7d, 0x5d, 0xfc, 0x6b, 0xc8, 0xfe,
	0xb0, 0xd1, 0xb3, 0x1f, 0x3d, 0x74, 0x2e, 0x78, 0x4a, 0xfe, 0xc6, 0x5c, 0xd0, 0xdd, 0x67, 0xa6,
	0x68, 0x3e, 0x12, 0xb3, 0xcf, 0x83, 0xb0, 0x68, 0x2a, 0xc1, 0xa2, 0x33, 0xd7, 0x19, 0x69, 0x5f,
	0xc4, 0x88, 0x55, 0x67, 0xc4, 0xbd, 0x5d, 0x3b, 0xce, 0x63, 0x19, 0x9d, 0xb3, 0xaa, 0xfb, 0x9f,
	0xe5, 0x4b, 0xe8, 0x91, 0x98, 0x95, 0x55, 0xc4, 0x6f, 0x34, 0xaa, 0x99, 0xf7, 0x2a, 0xfe, 0x6b,
	0x44, 0x0f, 0x6a, 0x4b, 0xd7, 0xf2, 0xfd, 0x1f, 0x34, 0xf6, 0xa4, 0x9c, 0xff, 0x01, 0x58, 0x85,
	0x98, 0xe9, 0xc5, 0xde, 0xbe, 0x60, 0x31, 0x31, 0x23, 0x6d, 0x23, 0xe5, 0xc5, 0x7f, 0x33, 0xdc,
	0xfd, 0xab, 0x16, 0xf4, 0x35, 0xe9, 0x0f, 0x11, 0xd0, 0x0f, 0xd6, 0x48, 0x4d, 0xa4, 0x9d, 0x86,
	0x6e, 0xd7, 0x95, 0x16, 0xbf, 0x2a, 0xfb, 0xf4, 0x47, 0x62, 0x56, 0xfd, 0x17, 0xee, 0xb7, 0x6a,
	0x5d, 0x99, 0xb3, 0xe2, 0x2c, 0xe9, 0x56, 0x51, 0x7a, 0xd2, 0xae, 0x5f, 0xb5, 0xf6, 0x78, 0xc4,
	0xfe, 0xf5, 0xba, 0x3c, 0x2f, 0x5c, 0x4d, 0x03, 0x5a, 0x39, 0x97, 0xc8, 0x75, 0x0f, 0xc6, 0xcd,
	0x09, 0xeb, 0xa4, 0xb9, 0x0d, 0xdd, 0xfa, 0x3f, 0xe4, 0x18, 0x70, 0x3f, 0x35, 0x4f, 0x80, 0x47,
	0x62, 0xf6, 0x8c, 0x5c, 0x8a, 0xfe, 0xed, 0x23, 0xbf, 0xa1, 0x07, 0xf9, 0x34, 0x2f, 0xa6, 0x4a,
	0x16, 0x45, 0x98, 0xcc, 0xcd, 0x3d, 0x0b, 0x71, 0x87, 0x8c, 0xba, 0xf7, 0x3b, 0xb0, 0xf9, 0xf3,
	0x34, 0x3f, 0x99, 0xc9, 0xc4, 0x7f, 0x71, 0x20, 0xf2, 0xc2, 0x1e, 0x41, 0x7f, 0x2f, 0x8a, 0xf0,
	0x73, 0xf2, 0x86, 0xbd, 0x05, 0x23, 0xfd, 0x57, 0x40, 0x42, 0xb4, 0xec, 0x0d, 0x18, 0x3c, 0x4e,
	0x13, 0x49, 0x50, 0xdb, 0x1e, 0x03, 0x7c, 0x11, 0x46, 0xd1, 0xb3, 0x8c, 0x60, 0xeb, 0xde, 0xcf,
	0x18, 0xe6, 0x66, 0x05, 0xd2, 0x22, 0xf4, 0x34, 0x4d, 0xe4, 0xe4, 0x0d, 0xdb, 0x86, 0xf1, 0x41,
	0x24, 0x96, 0x78, 0xdd, 0x3e, 0x26, 0x9a, 0x49, 0x0b, 0x97, 0x7f, 0x1e, 0x66, 0x25, 0xa2, 0x7d,
	0x2f, 0x06, 0xa8, 0xfe, 0xaf, 0x64, 0x5f, 0x87, 0xab, 0x15, 0xf4, 0x73, 0x11, 0x16, 0x8f, 0xe5,
	0xb1, 0x58, 0x44, 0xc8, 0xd8, 0x36, 0x4c, 0x9a, 0x43, 0x47, 0xf1, 0xa4, 0x65, 0xef, 0x80, 0x5d,
	0x61, 0xf5, 0xff, 0x16, 0xe2, 0x49, 0xbb, 0x89, 0xf7, 0xf4, 0xf3, 0xd3, 0xc4, 0xba, 0xf7, 0xa7,
	0x2d, 0x5c, 0xa6, 0x79, 0x8d, 0xb7, 0x6f, 0xc0, 0xb5, 0x55, 0xdc, 0xb3, 0xe4, 0x24, 0x49, 0x5f,
	0x26, 0x93, 0x37, 0x98, 0xa5, 0xe6, 0xe0, 0xa3, 0x34, 0x8e, 0x17, 0x93, 0x96, 0xfd, 0x13, 0xd8,
	0x3d, 0x77, 0xe8, 0x51, 0x94, 0x2a, 0x39, 0x69, 0xdb, 0x6f, 0x81, 0x73, 0xa6, 0x65, 0x20, 0xd1,
	0x06, 0x64, 0x30, 0xb1, 0x3e, 0xeb, 0xfd, 0x5e, 0xe7, 0xc1, 0xa7, 0xd9, 0x6c, 0xd6, 0xa3, 0xbf,
	0x2a, 0x7f, 0xfc, 0x7f, 0x01, 0x00, 0x00, 0xff, 0xff, 0x94, 0xfc, 0xc6, 0xd7, 0xca, 0x2c, 0x00,
	0x00,
}
