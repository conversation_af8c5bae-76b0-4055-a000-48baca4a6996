// Code generated by protoc-gen-go. DO NOT EDIT.
// source: user_assign_ticket.proto

package pb

import (
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

// 游戏、问题分类和系统标签
type GameCategory struct {
	// @gotags: validate:"required"
	Game string `protobuf:"bytes,1,opt,name=game,proto3" json:"game" validate:"required"`
	// @gotags: validate:"required"
	Categories []string `protobuf:"bytes,2,rep,name=categories,proto3" json:"categories" validate:"required"`
	// @gotags: validate:"required"
	SystemTag            []TicketSystemTag `protobuf:"varint,3,rep,packed,name=system_tag,json=systemTag,proto3,enum=pb.TicketSystemTag" json:"system_tag" validate:"required"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-" gorm:"-"`
	XXX_unrecognized     []byte            `json:"-" gorm:"-"`
	XXX_sizecache        int32             `json:"-" gorm:"-"`
}

func (m *GameCategory) Reset()         { *m = GameCategory{} }
func (m *GameCategory) String() string { return proto.CompactTextString(m) }
func (*GameCategory) ProtoMessage()    {}
func (*GameCategory) Descriptor() ([]byte, []int) {
	return fileDescriptor_113762360b927be3, []int{0}
}

func (m *GameCategory) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GameCategory.Unmarshal(m, b)
}
func (m *GameCategory) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GameCategory.Marshal(b, m, deterministic)
}
func (m *GameCategory) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GameCategory.Merge(m, src)
}
func (m *GameCategory) XXX_Size() int {
	return xxx_messageInfo_GameCategory.Size(m)
}
func (m *GameCategory) XXX_DiscardUnknown() {
	xxx_messageInfo_GameCategory.DiscardUnknown(m)
}

var xxx_messageInfo_GameCategory proto.InternalMessageInfo

func (m *GameCategory) GetGame() string {
	if m != nil {
		return m.Game
	}
	return ""
}

func (m *GameCategory) GetCategories() []string {
	if m != nil {
		return m.Categories
	}
	return nil
}

func (m *GameCategory) GetSystemTag() []TicketSystemTag {
	if m != nil {
		return m.SystemTag
	}
	return nil
}

// 新增用户分单配置
type UserAssignTicketAddReq struct {
	UpperLimit uint32 `protobuf:"varint,1,opt,name=upper_limit,json=upperLimit,proto3" json:"upper_limit"`
	// @gotags: validate:"required"
	Lang []string `protobuf:"bytes,3,rep,name=lang,proto3" json:"lang" validate:"required"`
	// @gotags: validate:"required"
	User                 []string        `protobuf:"bytes,4,rep,name=user,proto3" json:"user" validate:"required"`
	Detail               []*GameCategory `protobuf:"bytes,5,rep,name=detail,proto3" json:"detail"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-" gorm:"-"`
	XXX_unrecognized     []byte          `json:"-" gorm:"-"`
	XXX_sizecache        int32           `json:"-" gorm:"-"`
}

func (m *UserAssignTicketAddReq) Reset()         { *m = UserAssignTicketAddReq{} }
func (m *UserAssignTicketAddReq) String() string { return proto.CompactTextString(m) }
func (*UserAssignTicketAddReq) ProtoMessage()    {}
func (*UserAssignTicketAddReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_113762360b927be3, []int{1}
}

func (m *UserAssignTicketAddReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserAssignTicketAddReq.Unmarshal(m, b)
}
func (m *UserAssignTicketAddReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserAssignTicketAddReq.Marshal(b, m, deterministic)
}
func (m *UserAssignTicketAddReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserAssignTicketAddReq.Merge(m, src)
}
func (m *UserAssignTicketAddReq) XXX_Size() int {
	return xxx_messageInfo_UserAssignTicketAddReq.Size(m)
}
func (m *UserAssignTicketAddReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UserAssignTicketAddReq.DiscardUnknown(m)
}

var xxx_messageInfo_UserAssignTicketAddReq proto.InternalMessageInfo

func (m *UserAssignTicketAddReq) GetUpperLimit() uint32 {
	if m != nil {
		return m.UpperLimit
	}
	return 0
}

func (m *UserAssignTicketAddReq) GetLang() []string {
	if m != nil {
		return m.Lang
	}
	return nil
}

func (m *UserAssignTicketAddReq) GetUser() []string {
	if m != nil {
		return m.User
	}
	return nil
}

func (m *UserAssignTicketAddReq) GetDetail() []*GameCategory {
	if m != nil {
		return m.Detail
	}
	return nil
}

// 修改用户分单配置
type UserAssignTicketEditReq struct {
	// @gotags: validate:"required"
	Id                   uint32          `protobuf:"varint,1,opt,name=id,proto3" json:"id" validate:"required"`
	UpperLimit           uint32          `protobuf:"varint,2,opt,name=upper_limit,json=upperLimit,proto3" json:"upper_limit"`
	Detail               []*GameCategory `protobuf:"bytes,3,rep,name=detail,proto3" json:"detail"`
	Lang                 []string        `protobuf:"bytes,4,rep,name=lang,proto3" json:"lang"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-" gorm:"-"`
	XXX_unrecognized     []byte          `json:"-" gorm:"-"`
	XXX_sizecache        int32           `json:"-" gorm:"-"`
}

func (m *UserAssignTicketEditReq) Reset()         { *m = UserAssignTicketEditReq{} }
func (m *UserAssignTicketEditReq) String() string { return proto.CompactTextString(m) }
func (*UserAssignTicketEditReq) ProtoMessage()    {}
func (*UserAssignTicketEditReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_113762360b927be3, []int{2}
}

func (m *UserAssignTicketEditReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserAssignTicketEditReq.Unmarshal(m, b)
}
func (m *UserAssignTicketEditReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserAssignTicketEditReq.Marshal(b, m, deterministic)
}
func (m *UserAssignTicketEditReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserAssignTicketEditReq.Merge(m, src)
}
func (m *UserAssignTicketEditReq) XXX_Size() int {
	return xxx_messageInfo_UserAssignTicketEditReq.Size(m)
}
func (m *UserAssignTicketEditReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UserAssignTicketEditReq.DiscardUnknown(m)
}

var xxx_messageInfo_UserAssignTicketEditReq proto.InternalMessageInfo

func (m *UserAssignTicketEditReq) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *UserAssignTicketEditReq) GetUpperLimit() uint32 {
	if m != nil {
		return m.UpperLimit
	}
	return 0
}

func (m *UserAssignTicketEditReq) GetDetail() []*GameCategory {
	if m != nil {
		return m.Detail
	}
	return nil
}

func (m *UserAssignTicketEditReq) GetLang() []string {
	if m != nil {
		return m.Lang
	}
	return nil
}

// 删除用户分单配置
type UserAssignTicketDelReq struct {
	// @gotags: validate:"required"
	Id uint32 `protobuf:"varint,1,opt,name=id,proto3" json:"id" validate:"required"`
	// @gotags: validate:"required"
	User                 string   `protobuf:"bytes,2,opt,name=user,proto3" json:"user" validate:"required"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *UserAssignTicketDelReq) Reset()         { *m = UserAssignTicketDelReq{} }
func (m *UserAssignTicketDelReq) String() string { return proto.CompactTextString(m) }
func (*UserAssignTicketDelReq) ProtoMessage()    {}
func (*UserAssignTicketDelReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_113762360b927be3, []int{3}
}

func (m *UserAssignTicketDelReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserAssignTicketDelReq.Unmarshal(m, b)
}
func (m *UserAssignTicketDelReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserAssignTicketDelReq.Marshal(b, m, deterministic)
}
func (m *UserAssignTicketDelReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserAssignTicketDelReq.Merge(m, src)
}
func (m *UserAssignTicketDelReq) XXX_Size() int {
	return xxx_messageInfo_UserAssignTicketDelReq.Size(m)
}
func (m *UserAssignTicketDelReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UserAssignTicketDelReq.DiscardUnknown(m)
}

var xxx_messageInfo_UserAssignTicketDelReq proto.InternalMessageInfo

func (m *UserAssignTicketDelReq) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *UserAssignTicketDelReq) GetUser() string {
	if m != nil {
		return m.User
	}
	return ""
}

// 用户分单配置详情
type UserAssignTicketInfoReq struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *UserAssignTicketInfoReq) Reset()         { *m = UserAssignTicketInfoReq{} }
func (m *UserAssignTicketInfoReq) String() string { return proto.CompactTextString(m) }
func (*UserAssignTicketInfoReq) ProtoMessage()    {}
func (*UserAssignTicketInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_113762360b927be3, []int{4}
}

func (m *UserAssignTicketInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserAssignTicketInfoReq.Unmarshal(m, b)
}
func (m *UserAssignTicketInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserAssignTicketInfoReq.Marshal(b, m, deterministic)
}
func (m *UserAssignTicketInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserAssignTicketInfoReq.Merge(m, src)
}
func (m *UserAssignTicketInfoReq) XXX_Size() int {
	return xxx_messageInfo_UserAssignTicketInfoReq.Size(m)
}
func (m *UserAssignTicketInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UserAssignTicketInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_UserAssignTicketInfoReq proto.InternalMessageInfo

func (m *UserAssignTicketInfoReq) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

// UserAssignTicketListReq 用户分单列表请求参数
type UserAssignTicketListReq struct {
	// 用户账号
	Account string `protobuf:"bytes,1,opt,name=account,proto3" json:"account"`
	// 页码
	Page uint32 `protobuf:"varint,2,opt,name=page,proto3" json:"page"`
	// 页大小
	PageSize             uint32   `protobuf:"varint,3,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *UserAssignTicketListReq) Reset()         { *m = UserAssignTicketListReq{} }
func (m *UserAssignTicketListReq) String() string { return proto.CompactTextString(m) }
func (*UserAssignTicketListReq) ProtoMessage()    {}
func (*UserAssignTicketListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_113762360b927be3, []int{5}
}

func (m *UserAssignTicketListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserAssignTicketListReq.Unmarshal(m, b)
}
func (m *UserAssignTicketListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserAssignTicketListReq.Marshal(b, m, deterministic)
}
func (m *UserAssignTicketListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserAssignTicketListReq.Merge(m, src)
}
func (m *UserAssignTicketListReq) XXX_Size() int {
	return xxx_messageInfo_UserAssignTicketListReq.Size(m)
}
func (m *UserAssignTicketListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UserAssignTicketListReq.DiscardUnknown(m)
}

var xxx_messageInfo_UserAssignTicketListReq proto.InternalMessageInfo

func (m *UserAssignTicketListReq) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *UserAssignTicketListReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *UserAssignTicketListReq) GetPageSize() uint32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

// UserAssignTicketListResp 查询列表响应结果
type UserAssignTicketListResp struct {
	// 当前页
	CurrentPage uint32 `protobuf:"varint,1,opt,name=current_page,json=currentPage,proto3" json:"current_page"`
	// 页大小
	PerPage uint32 `protobuf:"varint,2,opt,name=per_page,json=perPage,proto3" json:"per_page"`
	// 总数
	Total uint32 `protobuf:"varint,3,opt,name=total,proto3" json:"total"`
	// 数据列表
	Data                 []*UserAssignTicketListResp_Detail `protobuf:"bytes,4,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}                           `json:"-" gorm:"-"`
	XXX_unrecognized     []byte                             `json:"-" gorm:"-"`
	XXX_sizecache        int32                              `json:"-" gorm:"-"`
}

func (m *UserAssignTicketListResp) Reset()         { *m = UserAssignTicketListResp{} }
func (m *UserAssignTicketListResp) String() string { return proto.CompactTextString(m) }
func (*UserAssignTicketListResp) ProtoMessage()    {}
func (*UserAssignTicketListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_113762360b927be3, []int{6}
}

func (m *UserAssignTicketListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserAssignTicketListResp.Unmarshal(m, b)
}
func (m *UserAssignTicketListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserAssignTicketListResp.Marshal(b, m, deterministic)
}
func (m *UserAssignTicketListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserAssignTicketListResp.Merge(m, src)
}
func (m *UserAssignTicketListResp) XXX_Size() int {
	return xxx_messageInfo_UserAssignTicketListResp.Size(m)
}
func (m *UserAssignTicketListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UserAssignTicketListResp.DiscardUnknown(m)
}

var xxx_messageInfo_UserAssignTicketListResp proto.InternalMessageInfo

func (m *UserAssignTicketListResp) GetCurrentPage() uint32 {
	if m != nil {
		return m.CurrentPage
	}
	return 0
}

func (m *UserAssignTicketListResp) GetPerPage() uint32 {
	if m != nil {
		return m.PerPage
	}
	return 0
}

func (m *UserAssignTicketListResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *UserAssignTicketListResp) GetData() []*UserAssignTicketListResp_Detail {
	if m != nil {
		return m.Data
	}
	return nil
}

type UserAssignTicketListResp_Detail struct {
	// 用户的id
	Id uint32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	// 用户账号
	Account string `protobuf:"bytes,2,opt,name=account,proto3" json:"account"`
	// 游戏列表
	Game []string `protobuf:"bytes,3,rep,name=game,proto3" json:"game"`
	// 操作人
	Operator string `protobuf:"bytes,4,opt,name=operator,proto3" json:"operator"`
	// 语言
	Lang []string `protobuf:"bytes,5,rep,name=lang,proto3" json:"lang"`
	// 用户接单上限
	UpperLimit uint32 `protobuf:"varint,6,opt,name=upper_limit,json=upperLimit,proto3" json:"upper_limit"`
	// 更新时间
	UpdatedAt string `protobuf:"bytes,7,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at"`
	// 游戏、问题分类和系统标签
	Detail               []*GameCategory `protobuf:"bytes,8,rep,name=detail,proto3" json:"detail"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-" gorm:"-"`
	XXX_unrecognized     []byte          `json:"-" gorm:"-"`
	XXX_sizecache        int32           `json:"-" gorm:"-"`
}

func (m *UserAssignTicketListResp_Detail) Reset()         { *m = UserAssignTicketListResp_Detail{} }
func (m *UserAssignTicketListResp_Detail) String() string { return proto.CompactTextString(m) }
func (*UserAssignTicketListResp_Detail) ProtoMessage()    {}
func (*UserAssignTicketListResp_Detail) Descriptor() ([]byte, []int) {
	return fileDescriptor_113762360b927be3, []int{6, 0}
}

func (m *UserAssignTicketListResp_Detail) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserAssignTicketListResp_Detail.Unmarshal(m, b)
}
func (m *UserAssignTicketListResp_Detail) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserAssignTicketListResp_Detail.Marshal(b, m, deterministic)
}
func (m *UserAssignTicketListResp_Detail) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserAssignTicketListResp_Detail.Merge(m, src)
}
func (m *UserAssignTicketListResp_Detail) XXX_Size() int {
	return xxx_messageInfo_UserAssignTicketListResp_Detail.Size(m)
}
func (m *UserAssignTicketListResp_Detail) XXX_DiscardUnknown() {
	xxx_messageInfo_UserAssignTicketListResp_Detail.DiscardUnknown(m)
}

var xxx_messageInfo_UserAssignTicketListResp_Detail proto.InternalMessageInfo

func (m *UserAssignTicketListResp_Detail) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *UserAssignTicketListResp_Detail) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *UserAssignTicketListResp_Detail) GetGame() []string {
	if m != nil {
		return m.Game
	}
	return nil
}

func (m *UserAssignTicketListResp_Detail) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

func (m *UserAssignTicketListResp_Detail) GetLang() []string {
	if m != nil {
		return m.Lang
	}
	return nil
}

func (m *UserAssignTicketListResp_Detail) GetUpperLimit() uint32 {
	if m != nil {
		return m.UpperLimit
	}
	return 0
}

func (m *UserAssignTicketListResp_Detail) GetUpdatedAt() string {
	if m != nil {
		return m.UpdatedAt
	}
	return ""
}

func (m *UserAssignTicketListResp_Detail) GetDetail() []*GameCategory {
	if m != nil {
		return m.Detail
	}
	return nil
}

type UserStateDf struct {
	UpperLimit           int64    `protobuf:"varint,1,opt,name=UpperLimit,proto3" json:"UpperLimit"`
	Game                 string   `protobuf:"bytes,2,opt,name=Game,proto3" json:"Game"`
	Lang                 string   `protobuf:"bytes,3,opt,name=Lang,proto3" json:"Lang"`
	IsLogin              int32    `protobuf:"varint,4,opt,name=IsLogin,proto3" json:"IsLogin"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *UserStateDf) Reset()         { *m = UserStateDf{} }
func (m *UserStateDf) String() string { return proto.CompactTextString(m) }
func (*UserStateDf) ProtoMessage()    {}
func (*UserStateDf) Descriptor() ([]byte, []int) {
	return fileDescriptor_113762360b927be3, []int{7}
}

func (m *UserStateDf) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserStateDf.Unmarshal(m, b)
}
func (m *UserStateDf) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserStateDf.Marshal(b, m, deterministic)
}
func (m *UserStateDf) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserStateDf.Merge(m, src)
}
func (m *UserStateDf) XXX_Size() int {
	return xxx_messageInfo_UserStateDf.Size(m)
}
func (m *UserStateDf) XXX_DiscardUnknown() {
	xxx_messageInfo_UserStateDf.DiscardUnknown(m)
}

var xxx_messageInfo_UserStateDf proto.InternalMessageInfo

func (m *UserStateDf) GetUpperLimit() int64 {
	if m != nil {
		return m.UpperLimit
	}
	return 0
}

func (m *UserStateDf) GetGame() string {
	if m != nil {
		return m.Game
	}
	return ""
}

func (m *UserStateDf) GetLang() string {
	if m != nil {
		return m.Lang
	}
	return ""
}

func (m *UserStateDf) GetIsLogin() int32 {
	if m != nil {
		return m.IsLogin
	}
	return 0
}

func init() {
	proto.RegisterType((*GameCategory)(nil), "pb.GameCategory")
	proto.RegisterType((*UserAssignTicketAddReq)(nil), "pb.UserAssignTicketAddReq")
	proto.RegisterType((*UserAssignTicketEditReq)(nil), "pb.UserAssignTicketEditReq")
	proto.RegisterType((*UserAssignTicketDelReq)(nil), "pb.UserAssignTicketDelReq")
	proto.RegisterType((*UserAssignTicketInfoReq)(nil), "pb.UserAssignTicketInfoReq")
	proto.RegisterType((*UserAssignTicketListReq)(nil), "pb.UserAssignTicketListReq")
	proto.RegisterType((*UserAssignTicketListResp)(nil), "pb.UserAssignTicketListResp")
	proto.RegisterType((*UserAssignTicketListResp_Detail)(nil), "pb.UserAssignTicketListResp.Detail")
	proto.RegisterType((*UserStateDf)(nil), "pb.UserStateDf")
}

func init() {
	proto.RegisterFile("user_assign_ticket.proto", fileDescriptor_113762360b927be3)
}

var fileDescriptor_113762360b927be3 = []byte{
	// 547 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x84, 0x54, 0x4f, 0x8f, 0xd3, 0x3e,
	0x10, 0x55, 0x93, 0xfe, 0xcb, 0x74, 0x7f, 0xab, 0x9f, 0x02, 0x82, 0x50, 0x04, 0x94, 0x70, 0x29,
	0x97, 0x1e, 0xca, 0x81, 0x03, 0x5c, 0x0a, 0x45, 0x68, 0xa5, 0x1e, 0x50, 0xba, 0x7b, 0xe1, 0x12,
	0xb9, 0xcd, 0x6c, 0x64, 0xd1, 0x26, 0x26, 0x76, 0x90, 0x76, 0x3f, 0x01, 0x82, 0x2f, 0xc9, 0x47,
	0x41, 0x33, 0x4e, 0xba, 0x51, 0x53, 0xe0, 0xd4, 0xf1, 0x1b, 0xc7, 0xef, 0xbd, 0xf1, 0x73, 0x21,
	0x28, 0x35, 0x16, 0xb1, 0xd0, 0x5a, 0xa6, 0x59, 0x6c, 0xe4, 0xf6, 0x0b, 0x9a, 0x99, 0x2a, 0x72,
	0x93, 0xfb, 0x8e, 0xda, 0x8c, 0x01, 0xb3, 0x72, 0x6f, 0xd7, 0xe1, 0x37, 0x38, 0xfb, 0x28, 0xf6,
	0xf8, 0x5e, 0x18, 0x4c, 0xf3, 0xe2, 0xc6, 0xf7, 0xa1, 0x9b, 0x8a, 0x3d, 0x06, 0x9d, 0x49, 0x67,
	0xea, 0x45, 0x5c, 0xfb, 0x4f, 0x01, 0xb6, 0xb6, 0x2f, 0x51, 0x07, 0xce, 0xc4, 0x9d, 0x7a, 0x51,
	0x03, 0xf1, 0xe7, 0x00, 0xfa, 0x46, 0x1b, 0xdc, 0xc7, 0x46, 0xa4, 0x81, 0x3b, 0x71, 0xa7, 0xe7,
	0xf3, 0x7b, 0x33, 0xb5, 0x99, 0x5d, 0x32, 0xf3, 0x9a, 0x7b, 0x97, 0x22, 0x8d, 0x3c, 0x5d, 0x97,
	0xe1, 0xcf, 0x0e, 0x3c, 0xb8, 0xd2, 0x58, 0x2c, 0x58, 0xa3, 0xdd, 0xb8, 0x48, 0x92, 0x08, 0xbf,
	0xfa, 0xcf, 0x60, 0x54, 0x2a, 0x85, 0x45, 0xbc, 0x93, 0x7b, 0x69, 0x58, 0xc9, 0x7f, 0x11, 0x30,
	0xb4, 0x22, 0x84, 0x34, 0xee, 0x44, 0x66, 0x99, 0xbc, 0x88, 0x6b, 0xc2, 0xc8, 0x73, 0xd0, 0xb5,
	0x18, 0xd5, 0xfe, 0x14, 0xfa, 0x09, 0x1a, 0x21, 0x77, 0x41, 0x6f, 0xe2, 0x4e, 0x47, 0xf3, 0xff,
	0x49, 0x53, 0xd3, 0x6d, 0x54, 0xf5, 0xc3, 0xef, 0x1d, 0x78, 0x78, 0xac, 0xe6, 0x43, 0x22, 0x0d,
	0xc9, 0x39, 0x07, 0x47, 0x26, 0x95, 0x0a, 0x47, 0x26, 0xc7, 0xf2, 0x9c, 0x96, 0xbc, 0x3b, 0x5a,
	0xf7, 0xef, 0xb4, 0x07, 0x23, 0xdd, 0x3b, 0x23, 0xe1, 0xdb, 0xf6, 0x5c, 0x96, 0xb8, 0x3b, 0x25,
	0xa4, 0xb6, 0xec, 0xd8, 0xab, 0xa2, 0x3a, 0x7c, 0xd9, 0xf6, 0x71, 0x91, 0x5d, 0xe7, 0x27, 0x3e,
	0x0f, 0x93, 0xf6, 0xd6, 0x95, 0xd4, 0x6c, 0x39, 0x80, 0x81, 0xd8, 0x6e, 0xf3, 0x32, 0x33, 0x55,
	0x0e, 0xea, 0x25, 0x71, 0x2a, 0x91, 0x62, 0xe5, 0x9a, 0x6b, 0xff, 0x31, 0x78, 0xf4, 0x1b, 0x6b,
	0x79, 0x8b, 0x81, 0xcb, 0x8d, 0x21, 0x01, 0x6b, 0x79, 0x8b, 0xe1, 0x0f, 0x17, 0x82, 0xd3, 0x34,
	0x5a, 0xf9, 0xcf, 0xe1, 0x6c, 0x5b, 0x16, 0x05, 0x66, 0x26, 0xe6, 0x53, 0xad, 0xb8, 0x51, 0x85,
	0x7d, 0xa2, 0xc3, 0x1f, 0xc1, 0x90, 0x66, 0xdd, 0x20, 0x1d, 0x28, 0x2c, 0xb8, 0x75, 0x1f, 0x7a,
	0x26, 0x37, 0x62, 0x57, 0x71, 0xda, 0x85, 0xff, 0x1a, 0xba, 0x89, 0x30, 0x82, 0x67, 0x3a, 0x9a,
	0xbf, 0xa0, 0xd9, 0xff, 0x89, 0x7f, 0xb6, 0xe4, 0x6b, 0x88, 0xf8, 0x83, 0xf1, 0xaf, 0x0e, 0xf4,
	0x2d, 0xd0, 0x9a, 0x74, 0x63, 0x1e, 0x4e, 0x6b, 0x1e, 0xfc, 0x5c, 0xaa, 0x28, 0xf2, 0x73, 0x19,
	0xc3, 0x30, 0x57, 0x58, 0x08, 0x93, 0x53, 0x1c, 0x69, 0xfb, 0x61, 0x7d, 0xb8, 0xf1, 0x5e, 0x23,
	0xba, 0x47, 0x81, 0xea, 0xb7, 0x02, 0xf5, 0x04, 0xa0, 0x54, 0x89, 0x30, 0x98, 0xc4, 0xc2, 0x04,
	0x03, 0x3e, 0xd2, 0xab, 0x90, 0x45, 0x33, 0x6f, 0xc3, 0x7f, 0xc4, 0x3c, 0x87, 0x11, 0xcd, 0x62,
	0x6d, 0x84, 0xc1, 0xe5, 0x35, 0xbd, 0xeb, 0xab, 0x03, 0x0b, 0xdb, 0x75, 0xa3, 0x06, 0x42, 0x62,
	0xe9, 0x98, 0x3a, 0x60, 0x54, 0x13, 0xb6, 0xb2, 0x6f, 0x8f, 0x31, 0xaa, 0x69, 0x3c, 0x17, 0x7a,
	0x95, 0xa7, 0x32, 0x63, 0xbf, 0xbd, 0xa8, 0x5e, 0xbe, 0xeb, 0x7f, 0xee, 0xce, 0xde, 0xa8, 0xcd,
	0xa6, 0xcf, 0x7f, 0x36, 0xaf, 0x7e, 0x07, 0x00, 0x00, 0xff, 0xff, 0xc8, 0xa4, 0x52, 0x92, 0x98,
	0x04, 0x00, 0x00,
}
