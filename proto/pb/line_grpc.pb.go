// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.14.0
// source: line.proto

package pb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	LineApi_LineTabSave_FullMethodName  = "/pb.LineApi/LineTabSave"
	LineApi_LineTabDel_FullMethodName   = "/pb.LineApi/LineTabDel"
	LineApi_LineTabList_FullMethodName  = "/pb.LineApi/LineTabList"
	LineApi_LineTabCount_FullMethodName = "/pb.LineApi/LineTabCount"
	LineApi_LineTabEdit_FullMethodName  = "/pb.LineApi/LineTabEdit"
)

// LineApiClient is the client API for LineApi service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type LineApiClient interface {
	LineTabSave(ctx context.Context, in *LineTabAddReq, opts ...grpc.CallOption) (*Empty, error)
	LineTabDel(ctx context.Context, in *LineTabDelReq, opts ...grpc.CallOption) (*Empty, error)
	LineTabList(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*LineTabListResp, error)
	LineTabCount(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*LineTabCountResp, error)
	LineTabEdit(ctx context.Context, in *LineTabEditReq, opts ...grpc.CallOption) (*Empty, error)
}

type lineApiClient struct {
	cc grpc.ClientConnInterface
}

func NewLineApiClient(cc grpc.ClientConnInterface) LineApiClient {
	return &lineApiClient{cc}
}

func (c *lineApiClient) LineTabSave(ctx context.Context, in *LineTabAddReq, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, LineApi_LineTabSave_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lineApiClient) LineTabDel(ctx context.Context, in *LineTabDelReq, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, LineApi_LineTabDel_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lineApiClient) LineTabList(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*LineTabListResp, error) {
	out := new(LineTabListResp)
	err := c.cc.Invoke(ctx, LineApi_LineTabList_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lineApiClient) LineTabCount(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*LineTabCountResp, error) {
	out := new(LineTabCountResp)
	err := c.cc.Invoke(ctx, LineApi_LineTabCount_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *lineApiClient) LineTabEdit(ctx context.Context, in *LineTabEditReq, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, LineApi_LineTabEdit_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// LineApiServer is the server API for LineApi service.
// All implementations must embed UnimplementedLineApiServer
// for forward compatibility
type LineApiServer interface {
	LineTabSave(context.Context, *LineTabAddReq) (*Empty, error)
	LineTabDel(context.Context, *LineTabDelReq) (*Empty, error)
	LineTabList(context.Context, *Empty) (*LineTabListResp, error)
	LineTabCount(context.Context, *Empty) (*LineTabCountResp, error)
	LineTabEdit(context.Context, *LineTabEditReq) (*Empty, error)
	mustEmbedUnimplementedLineApiServer()
}

// UnimplementedLineApiServer must be embedded to have forward compatible implementations.
type UnimplementedLineApiServer struct {
}

func (UnimplementedLineApiServer) LineTabSave(context.Context, *LineTabAddReq) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method LineTabSave not implemented")
}
func (UnimplementedLineApiServer) LineTabDel(context.Context, *LineTabDelReq) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method LineTabDel not implemented")
}
func (UnimplementedLineApiServer) LineTabList(context.Context, *Empty) (*LineTabListResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method LineTabList not implemented")
}
func (UnimplementedLineApiServer) LineTabCount(context.Context, *Empty) (*LineTabCountResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method LineTabCount not implemented")
}
func (UnimplementedLineApiServer) LineTabEdit(context.Context, *LineTabEditReq) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method LineTabEdit not implemented")
}
func (UnimplementedLineApiServer) mustEmbedUnimplementedLineApiServer() {}

// UnsafeLineApiServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to LineApiServer will
// result in compilation errors.
type UnsafeLineApiServer interface {
	mustEmbedUnimplementedLineApiServer()
}

func RegisterLineApiServer(s grpc.ServiceRegistrar, srv LineApiServer) {
	s.RegisterService(&LineApi_ServiceDesc, srv)
}

func _LineApi_LineTabSave_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LineTabAddReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LineApiServer).LineTabSave(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: LineApi_LineTabSave_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LineApiServer).LineTabSave(ctx, req.(*LineTabAddReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _LineApi_LineTabDel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LineTabDelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LineApiServer).LineTabDel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: LineApi_LineTabDel_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LineApiServer).LineTabDel(ctx, req.(*LineTabDelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _LineApi_LineTabList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LineApiServer).LineTabList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: LineApi_LineTabList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LineApiServer).LineTabList(ctx, req.(*Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _LineApi_LineTabCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LineApiServer).LineTabCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: LineApi_LineTabCount_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LineApiServer).LineTabCount(ctx, req.(*Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _LineApi_LineTabEdit_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LineTabEditReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LineApiServer).LineTabEdit(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: LineApi_LineTabEdit_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LineApiServer).LineTabEdit(ctx, req.(*LineTabEditReq))
	}
	return interceptor(ctx, in, info, handler)
}

// LineApi_ServiceDesc is the grpc.ServiceDesc for LineApi service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var LineApi_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "pb.LineApi",
	HandlerType: (*LineApiServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "LineTabSave",
			Handler:    _LineApi_LineTabSave_Handler,
		},
		{
			MethodName: "LineTabDel",
			Handler:    _LineApi_LineTabDel_Handler,
		},
		{
			MethodName: "LineTabList",
			Handler:    _LineApi_LineTabList_Handler,
		},
		{
			MethodName: "LineTabCount",
			Handler:    _LineApi_LineTabCount_Handler,
		},
		{
			MethodName: "LineTabEdit",
			Handler:    _LineApi_LineTabEdit_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "line.proto",
}
