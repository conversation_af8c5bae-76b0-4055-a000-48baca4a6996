// Code generated by protoc-gen-go. DO NOT EDIT.
// source: egress_ticket.proto

package pb

import (
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

// TkCreateReq 创建工单
type TkCreateReq struct {
	// 工单来源 1:玩家 2:VIP客服 3:普通客服 @gotags: validate:"required"
	Origin uint32 `protobuf:"varint,1,opt,name=origin,proto3" json:"origin" validate:"required"`
	// 场景枚举
	Scene uint32 `protobuf:"varint,2,opt,name=scene,proto3" json:"scene"`
	// Deprecated: 废弃，后续整合到fpx_app_id中，统一使用`fpx_app_id`
	// 游戏ID @gotags: validate:"required_without=FpxAppId"
	GameId uint32 `protobuf:"varint,3,opt,name=game_id,json=gameId,proto3" json:"game_id" validate:"required_without=FpxAppId"`
	// 项目标识 @gotags: validate:"required_without=GameId"
	FpxAppId string `protobuf:"bytes,4,opt,name=fpx_app_id,json=fpxAppId,proto3" json:"fpx_app_id" validate:"required_without=GameId"`
	// lang 语言 @gotags: validate:"required"
	Lang string `protobuf:"bytes,5,opt,name=lang,proto3" json:"lang" validate:"required"`
	// uuid 设备ID
	Uuid string `protobuf:"bytes,6,opt,name=uuid,proto3" json:"uuid"`
	// Deprecated: 废弃，fpid 后续整合到 account_id中，统一使用`account_id`
	Fpid uint64 `protobuf:"varint,7,opt,name=fpid,proto3" json:"fpid"`
	// uid 用户ID
	Uid uint64 `protobuf:"varint,8,opt,name=uid,proto3" json:"uid"`
	// account_id
	AccountId string `protobuf:"bytes,9,opt,name=account_id,json=accountId,proto3" json:"account_id"`
	// role_id 角色ID
	RoleId string `protobuf:"bytes,10,opt,name=role_id,json=roleId,proto3" json:"role_id"`
	// sid 区服
	Sid string `protobuf:"bytes,11,opt,name=sid,proto3" json:"sid"`
	// channel 渠道
	Channel string `protobuf:"bytes,12,opt,name=channel,proto3" json:"channel"`
	// nickname 昵称
	Nickname string `protobuf:"bytes,13,opt,name=nickname,proto3" json:"nickname"`
	// cat_id 问题类别ID @gotags: validate:"required"
	CatId uint32 `protobuf:"varint,14,opt,name=cat_id,json=catId,proto3" json:"cat_id" validate:"required"`
	// fields 自定义字段  @gotags: validate:"required"
	Fields      string `protobuf:"bytes,15,opt,name=fields,proto3" json:"fields" validate:"required"`
	DeviceType  string `protobuf:"bytes,16,opt,name=device_type,json=deviceType,proto3" json:"device_type"`
	Os          string `protobuf:"bytes,17,opt,name=os,proto3" json:"os"`
	OsVersion   string `protobuf:"bytes,18,opt,name=os_version,json=osVersion,proto3" json:"os_version"`
	AppVersion  string `protobuf:"bytes,19,opt,name=app_version,json=appVersion,proto3" json:"app_version"`
	RomGb       string `protobuf:"bytes,20,opt,name=rom_gb,json=romGb,proto3" json:"rom_gb"`
	RemainRom   string `protobuf:"bytes,21,opt,name=remain_rom,json=remainRom,proto3" json:"remain_rom"`
	RamMb       string `protobuf:"bytes,22,opt,name=ram_mb,json=ramMb,proto3" json:"ram_mb"`
	NetworkInfo string `protobuf:"bytes,23,opt,name=network_info,json=networkInfo,proto3" json:"network_info"`
	SdkVersion  string `protobuf:"bytes,24,opt,name=sdk_version,json=sdkVersion,proto3" json:"sdk_version"`
	// 国家
	Country string `protobuf:"bytes,25,opt,name=country,proto3" json:"country"`
	// 历史充值总额
	TotalPay float64  `protobuf:"fixed64,26,opt,name=total_pay,json=totalPay,proto3" json:"total_pay"`
	LabelId  []uint32 `protobuf:"varint,27,rep,packed,name=label_id,json=labelId,proto3" json:"label_id"`
	Extend   string   `protobuf:"bytes,28,opt,name=extend,proto3" json:"extend"`
	// Deprecated: 废弃，后续使用 total_pay，统一使用`total_pay`
	PayAmount float64 `protobuf:"fixed64,29,opt,name=pay_amount,json=payAmount,proto3" json:"pay_amount"`
	// Deprecated: 废弃，后续 使用country，统一使用`country`
	CountryCode string `protobuf:"bytes,30,opt,name=country_code,json=countryCode,proto3" json:"country_code"`
	// 重提工单ID
	RelatedCat   uint32 `protobuf:"varint,31,opt,name=related_cat,json=relatedCat,proto3" json:"related_cat"`
	FromTicketId uint64 `protobuf:"varint,32,opt,name=from_ticket_id,json=fromTicketId,proto3" json:"from_ticket_id"`
	// subchannel 子渠道
	Subchannel string `protobuf:"bytes,33,opt,name=subchannel,proto3" json:"subchannel"`
	// 微信内小游戏 - openid
	Openid string `protobuf:"bytes,34,opt,name=openid,proto3" json:"openid"`
	// 流程会话
	ProcessSession string `protobuf:"bytes,35,opt,name=process_session,json=processSession,proto3" json:"process_session"`
	// 流程ID
	ProcessId int64 `protobuf:"varint,36,opt,name=process_id,json=processId,proto3" json:"process_id"`
	// ss vip绿色通道标识
	Qfrom string `protobuf:"bytes,40,opt,name=qfrom,proto3" json:"qfrom"`
	// 私域来源请求标识
	ZoneFrom string `protobuf:"bytes,41,opt,name=zone_from,json=zoneFrom,proto3" json:"zone_from"`
	// 问题用户 account_id(前端不传)
	TroubleAccountId string `protobuf:"bytes,43,opt,name=trouble_account_id,json=troubleAccountId,proto3" json:"trouble_account_id"`
	// 问题用户 uid
	TroubleUid uint64 `protobuf:"varint,44,opt,name=trouble_uid,json=troubleUid,proto3" json:"trouble_uid"`
	// 渠道号
	PackageId string `protobuf:"bytes,45,opt,name=packageId,proto3" json:"packageId"`
	// 私域 zone_token
	ZoneToken string `protobuf:"bytes,46,opt,name=zone_token,json=zoneToken,proto3" json:"zone_token"`
	// 对话ID
	ConversationId string `protobuf:"bytes,47,opt,name=conversation_id,json=conversationId,proto3" json:"conversation_id"`
	// 是否为新版ai客服单
	TicketType bool `protobuf:"varint,48,opt,name=ticket_type,json=ticketType,proto3" json:"ticket_type"`
	// 新版客服无效单判断逻辑 (仅为描述与建议这两个key，且结果为暂未收集到有效信息)
	IsInvalid uint32 `protobuf:"varint,49,opt,name=is_invalid,json=isInvalid,proto3" json:"is_invalid"`
	// 私域 R级参数
	ZoneVipLevel uint64 `protobuf:"varint,50,opt,name=zone_vip_level,json=zoneVipLevel,proto3" json:"zone_vip_level"`
	// 私域权益使用
	PrivRightsUse        bool     `protobuf:"varint,51,opt,name=priv_rights_use,json=privRightsUse,proto3" json:"priv_rights_use"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *TkCreateReq) Reset()         { *m = TkCreateReq{} }
func (m *TkCreateReq) String() string { return proto.CompactTextString(m) }
func (*TkCreateReq) ProtoMessage()    {}
func (*TkCreateReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_c529971104d8ed1e, []int{0}
}

func (m *TkCreateReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TkCreateReq.Unmarshal(m, b)
}
func (m *TkCreateReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TkCreateReq.Marshal(b, m, deterministic)
}
func (m *TkCreateReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TkCreateReq.Merge(m, src)
}
func (m *TkCreateReq) XXX_Size() int {
	return xxx_messageInfo_TkCreateReq.Size(m)
}
func (m *TkCreateReq) XXX_DiscardUnknown() {
	xxx_messageInfo_TkCreateReq.DiscardUnknown(m)
}

var xxx_messageInfo_TkCreateReq proto.InternalMessageInfo

func (m *TkCreateReq) GetOrigin() uint32 {
	if m != nil {
		return m.Origin
	}
	return 0
}

func (m *TkCreateReq) GetScene() uint32 {
	if m != nil {
		return m.Scene
	}
	return 0
}

func (m *TkCreateReq) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *TkCreateReq) GetFpxAppId() string {
	if m != nil {
		return m.FpxAppId
	}
	return ""
}

func (m *TkCreateReq) GetLang() string {
	if m != nil {
		return m.Lang
	}
	return ""
}

func (m *TkCreateReq) GetUuid() string {
	if m != nil {
		return m.Uuid
	}
	return ""
}

func (m *TkCreateReq) GetFpid() uint64 {
	if m != nil {
		return m.Fpid
	}
	return 0
}

func (m *TkCreateReq) GetUid() uint64 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *TkCreateReq) GetAccountId() string {
	if m != nil {
		return m.AccountId
	}
	return ""
}

func (m *TkCreateReq) GetRoleId() string {
	if m != nil {
		return m.RoleId
	}
	return ""
}

func (m *TkCreateReq) GetSid() string {
	if m != nil {
		return m.Sid
	}
	return ""
}

func (m *TkCreateReq) GetChannel() string {
	if m != nil {
		return m.Channel
	}
	return ""
}

func (m *TkCreateReq) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *TkCreateReq) GetCatId() uint32 {
	if m != nil {
		return m.CatId
	}
	return 0
}

func (m *TkCreateReq) GetFields() string {
	if m != nil {
		return m.Fields
	}
	return ""
}

func (m *TkCreateReq) GetDeviceType() string {
	if m != nil {
		return m.DeviceType
	}
	return ""
}

func (m *TkCreateReq) GetOs() string {
	if m != nil {
		return m.Os
	}
	return ""
}

func (m *TkCreateReq) GetOsVersion() string {
	if m != nil {
		return m.OsVersion
	}
	return ""
}

func (m *TkCreateReq) GetAppVersion() string {
	if m != nil {
		return m.AppVersion
	}
	return ""
}

func (m *TkCreateReq) GetRomGb() string {
	if m != nil {
		return m.RomGb
	}
	return ""
}

func (m *TkCreateReq) GetRemainRom() string {
	if m != nil {
		return m.RemainRom
	}
	return ""
}

func (m *TkCreateReq) GetRamMb() string {
	if m != nil {
		return m.RamMb
	}
	return ""
}

func (m *TkCreateReq) GetNetworkInfo() string {
	if m != nil {
		return m.NetworkInfo
	}
	return ""
}

func (m *TkCreateReq) GetSdkVersion() string {
	if m != nil {
		return m.SdkVersion
	}
	return ""
}

func (m *TkCreateReq) GetCountry() string {
	if m != nil {
		return m.Country
	}
	return ""
}

func (m *TkCreateReq) GetTotalPay() float64 {
	if m != nil {
		return m.TotalPay
	}
	return 0
}

func (m *TkCreateReq) GetLabelId() []uint32 {
	if m != nil {
		return m.LabelId
	}
	return nil
}

func (m *TkCreateReq) GetExtend() string {
	if m != nil {
		return m.Extend
	}
	return ""
}

func (m *TkCreateReq) GetPayAmount() float64 {
	if m != nil {
		return m.PayAmount
	}
	return 0
}

func (m *TkCreateReq) GetCountryCode() string {
	if m != nil {
		return m.CountryCode
	}
	return ""
}

func (m *TkCreateReq) GetRelatedCat() uint32 {
	if m != nil {
		return m.RelatedCat
	}
	return 0
}

func (m *TkCreateReq) GetFromTicketId() uint64 {
	if m != nil {
		return m.FromTicketId
	}
	return 0
}

func (m *TkCreateReq) GetSubchannel() string {
	if m != nil {
		return m.Subchannel
	}
	return ""
}

func (m *TkCreateReq) GetOpenid() string {
	if m != nil {
		return m.Openid
	}
	return ""
}

func (m *TkCreateReq) GetProcessSession() string {
	if m != nil {
		return m.ProcessSession
	}
	return ""
}

func (m *TkCreateReq) GetProcessId() int64 {
	if m != nil {
		return m.ProcessId
	}
	return 0
}

func (m *TkCreateReq) GetQfrom() string {
	if m != nil {
		return m.Qfrom
	}
	return ""
}

func (m *TkCreateReq) GetZoneFrom() string {
	if m != nil {
		return m.ZoneFrom
	}
	return ""
}

func (m *TkCreateReq) GetTroubleAccountId() string {
	if m != nil {
		return m.TroubleAccountId
	}
	return ""
}

func (m *TkCreateReq) GetTroubleUid() uint64 {
	if m != nil {
		return m.TroubleUid
	}
	return 0
}

func (m *TkCreateReq) GetPackageId() string {
	if m != nil {
		return m.PackageId
	}
	return ""
}

func (m *TkCreateReq) GetZoneToken() string {
	if m != nil {
		return m.ZoneToken
	}
	return ""
}

func (m *TkCreateReq) GetConversationId() string {
	if m != nil {
		return m.ConversationId
	}
	return ""
}

func (m *TkCreateReq) GetTicketType() bool {
	if m != nil {
		return m.TicketType
	}
	return false
}

func (m *TkCreateReq) GetIsInvalid() uint32 {
	if m != nil {
		return m.IsInvalid
	}
	return 0
}

func (m *TkCreateReq) GetZoneVipLevel() uint64 {
	if m != nil {
		return m.ZoneVipLevel
	}
	return 0
}

func (m *TkCreateReq) GetPrivRightsUse() bool {
	if m != nil {
		return m.PrivRightsUse
	}
	return false
}

// TkCreateResp 工单ID
type TkCreateResp struct {
	// 工单ID @gotags: validate:"required"
	TicketId uint64 `protobuf:"varint,1,opt,name=ticket_id,json=ticketId,proto3" json:"ticket_id" validate:"required"`
	// 使用哪个工单系统
	TicketSysType        TicketSys `protobuf:"varint,2,opt,name=ticket_sys_type,json=ticketSysType,proto3,enum=pb.TicketSys" json:"ticket_sys_type"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-" gorm:"-"`
	XXX_unrecognized     []byte    `json:"-" gorm:"-"`
	XXX_sizecache        int32     `json:"-" gorm:"-"`
}

func (m *TkCreateResp) Reset()         { *m = TkCreateResp{} }
func (m *TkCreateResp) String() string { return proto.CompactTextString(m) }
func (*TkCreateResp) ProtoMessage()    {}
func (*TkCreateResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_c529971104d8ed1e, []int{1}
}

func (m *TkCreateResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TkCreateResp.Unmarshal(m, b)
}
func (m *TkCreateResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TkCreateResp.Marshal(b, m, deterministic)
}
func (m *TkCreateResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TkCreateResp.Merge(m, src)
}
func (m *TkCreateResp) XXX_Size() int {
	return xxx_messageInfo_TkCreateResp.Size(m)
}
func (m *TkCreateResp) XXX_DiscardUnknown() {
	xxx_messageInfo_TkCreateResp.DiscardUnknown(m)
}

var xxx_messageInfo_TkCreateResp proto.InternalMessageInfo

func (m *TkCreateResp) GetTicketId() uint64 {
	if m != nil {
		return m.TicketId
	}
	return 0
}

func (m *TkCreateResp) GetTicketSysType() TicketSys {
	if m != nil {
		return m.TicketSysType
	}
	return TicketSys_TicketSysOld
}

// TkUpResp 常规操作返回
type TkUpResp struct {
	// 工单ID
	TicketId uint64 `protobuf:"varint,1,opt,name=ticket_id,json=ticketId,proto3" json:"ticket_id"`
	// 使用哪个工单系统
	TicketSysType        TicketSys `protobuf:"varint,2,opt,name=ticket_sys_type,json=ticketSysType,proto3,enum=pb.TicketSys" json:"ticket_sys_type"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-" gorm:"-"`
	XXX_unrecognized     []byte    `json:"-" gorm:"-"`
	XXX_sizecache        int32     `json:"-" gorm:"-"`
}

func (m *TkUpResp) Reset()         { *m = TkUpResp{} }
func (m *TkUpResp) String() string { return proto.CompactTextString(m) }
func (*TkUpResp) ProtoMessage()    {}
func (*TkUpResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_c529971104d8ed1e, []int{2}
}

func (m *TkUpResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TkUpResp.Unmarshal(m, b)
}
func (m *TkUpResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TkUpResp.Marshal(b, m, deterministic)
}
func (m *TkUpResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TkUpResp.Merge(m, src)
}
func (m *TkUpResp) XXX_Size() int {
	return xxx_messageInfo_TkUpResp.Size(m)
}
func (m *TkUpResp) XXX_DiscardUnknown() {
	xxx_messageInfo_TkUpResp.DiscardUnknown(m)
}

var xxx_messageInfo_TkUpResp proto.InternalMessageInfo

func (m *TkUpResp) GetTicketId() uint64 {
	if m != nil {
		return m.TicketId
	}
	return 0
}

func (m *TkUpResp) GetTicketSysType() TicketSys {
	if m != nil {
		return m.TicketSysType
	}
	return TicketSys_TicketSysOld
}

// TkMineReq 我的工单列表
type TkMineReq struct {
	// 场景枚举
	Scene uint32 `protobuf:"varint,1,opt,name=scene,proto3" json:"scene"`
	// Deprecated: 废弃，后续整合到fpx_app_id中
	// 游戏ID @gotags: validate:"required_without=FpxAppId"
	GameId uint32 `protobuf:"varint,3,opt,name=game_id,json=gameId,proto3" json:"game_id" validate:"required_without=FpxAppId"`
	// 项目标识 @gotags: validate:"required_without=GameId"
	FpxAppId string `protobuf:"bytes,4,opt,name=fpx_app_id,json=fpxAppId,proto3" json:"fpx_app_id" validate:"required_without=GameId"`
	// lang 语言 @gotags: validate:"required"
	Lang string `protobuf:"bytes,5,opt,name=lang,proto3" json:"lang" validate:"required"`
	// uuid 设备ID
	Uuid string `protobuf:"bytes,6,opt,name=uuid,proto3" json:"uuid"`
	// Deprecated: 废弃，fpid 后续整合到 account_id中
	Fpid uint64 `protobuf:"varint,7,opt,name=fpid,proto3" json:"fpid"`
	// uid 用户ID
	Uid uint64 `protobuf:"varint,8,opt,name=uid,proto3" json:"uid"`
	// account_id
	AccountId string `protobuf:"bytes,9,opt,name=account_id,json=accountId,proto3" json:"account_id"`
	// role_id 角色ID
	RoleId string `protobuf:"bytes,10,opt,name=role_id,json=roleId,proto3" json:"role_id"`
	// sid 区服
	Sid string `protobuf:"bytes,11,opt,name=sid,proto3" json:"sid"`
	// channel 渠道
	Channel              string   `protobuf:"bytes,12,opt,name=channel,proto3" json:"channel"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *TkMineReq) Reset()         { *m = TkMineReq{} }
func (m *TkMineReq) String() string { return proto.CompactTextString(m) }
func (*TkMineReq) ProtoMessage()    {}
func (*TkMineReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_c529971104d8ed1e, []int{3}
}

func (m *TkMineReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TkMineReq.Unmarshal(m, b)
}
func (m *TkMineReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TkMineReq.Marshal(b, m, deterministic)
}
func (m *TkMineReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TkMineReq.Merge(m, src)
}
func (m *TkMineReq) XXX_Size() int {
	return xxx_messageInfo_TkMineReq.Size(m)
}
func (m *TkMineReq) XXX_DiscardUnknown() {
	xxx_messageInfo_TkMineReq.DiscardUnknown(m)
}

var xxx_messageInfo_TkMineReq proto.InternalMessageInfo

func (m *TkMineReq) GetScene() uint32 {
	if m != nil {
		return m.Scene
	}
	return 0
}

func (m *TkMineReq) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *TkMineReq) GetFpxAppId() string {
	if m != nil {
		return m.FpxAppId
	}
	return ""
}

func (m *TkMineReq) GetLang() string {
	if m != nil {
		return m.Lang
	}
	return ""
}

func (m *TkMineReq) GetUuid() string {
	if m != nil {
		return m.Uuid
	}
	return ""
}

func (m *TkMineReq) GetFpid() uint64 {
	if m != nil {
		return m.Fpid
	}
	return 0
}

func (m *TkMineReq) GetUid() uint64 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *TkMineReq) GetAccountId() string {
	if m != nil {
		return m.AccountId
	}
	return ""
}

func (m *TkMineReq) GetRoleId() string {
	if m != nil {
		return m.RoleId
	}
	return ""
}

func (m *TkMineReq) GetSid() string {
	if m != nil {
		return m.Sid
	}
	return ""
}

func (m *TkMineReq) GetChannel() string {
	if m != nil {
		return m.Channel
	}
	return ""
}

// TkMineResp 工单历史 - 老工单字段及定义
type TkMineResp struct {
	Data                 []*TkMineResp_TkList `protobuf:"bytes,1,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-" gorm:"-"`
	XXX_unrecognized     []byte               `json:"-" gorm:"-"`
	XXX_sizecache        int32                `json:"-" gorm:"-"`
}

func (m *TkMineResp) Reset()         { *m = TkMineResp{} }
func (m *TkMineResp) String() string { return proto.CompactTextString(m) }
func (*TkMineResp) ProtoMessage()    {}
func (*TkMineResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_c529971104d8ed1e, []int{4}
}

func (m *TkMineResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TkMineResp.Unmarshal(m, b)
}
func (m *TkMineResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TkMineResp.Marshal(b, m, deterministic)
}
func (m *TkMineResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TkMineResp.Merge(m, src)
}
func (m *TkMineResp) XXX_Size() int {
	return xxx_messageInfo_TkMineResp.Size(m)
}
func (m *TkMineResp) XXX_DiscardUnknown() {
	xxx_messageInfo_TkMineResp.DiscardUnknown(m)
}

var xxx_messageInfo_TkMineResp proto.InternalMessageInfo

func (m *TkMineResp) GetData() []*TkMineResp_TkList {
	if m != nil {
		return m.Data
	}
	return nil
}

type TkMineResp_TkList struct {
	// 工单ID
	TicketId uint64 `protobuf:"varint,1,opt,name=ticket_id,json=ticketId,proto3" json:"ticket_id"`
	// 创建时间
	CreatedAt string `protobuf:"bytes,2,opt,name=created_at,json=createdAt,proto3" json:"created_at"`
	// 类别
	Category string `protobuf:"bytes,3,opt,name=category,proto3" json:"category"`
	// 当前状态 1：已完成 2：待补充 3：处理中 4：补充超时 5：重新打开
	Progress TkProgress `protobuf:"varint,4,opt,name=progress,proto3,enum=pb.TkProgress" json:"progress"`
	// 当前数据状态： 前端忽略
	Status TkStatus `protobuf:"varint,5,opt,name=status,proto3,enum=pb.TkStatus" json:"status"`
	// 消息已读状态 1:已读 0:未读
	Read uint32 `protobuf:"varint,6,opt,name=read,proto3" json:"read"`
	// 使用哪个工单系统
	TicketSysType        TicketSys `protobuf:"varint,7,opt,name=ticket_sys_type,json=ticketSysType,proto3,enum=pb.TicketSys" json:"ticket_sys_type"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-" gorm:"-"`
	XXX_unrecognized     []byte    `json:"-" gorm:"-"`
	XXX_sizecache        int32     `json:"-" gorm:"-"`
}

func (m *TkMineResp_TkList) Reset()         { *m = TkMineResp_TkList{} }
func (m *TkMineResp_TkList) String() string { return proto.CompactTextString(m) }
func (*TkMineResp_TkList) ProtoMessage()    {}
func (*TkMineResp_TkList) Descriptor() ([]byte, []int) {
	return fileDescriptor_c529971104d8ed1e, []int{4, 0}
}

func (m *TkMineResp_TkList) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TkMineResp_TkList.Unmarshal(m, b)
}
func (m *TkMineResp_TkList) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TkMineResp_TkList.Marshal(b, m, deterministic)
}
func (m *TkMineResp_TkList) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TkMineResp_TkList.Merge(m, src)
}
func (m *TkMineResp_TkList) XXX_Size() int {
	return xxx_messageInfo_TkMineResp_TkList.Size(m)
}
func (m *TkMineResp_TkList) XXX_DiscardUnknown() {
	xxx_messageInfo_TkMineResp_TkList.DiscardUnknown(m)
}

var xxx_messageInfo_TkMineResp_TkList proto.InternalMessageInfo

func (m *TkMineResp_TkList) GetTicketId() uint64 {
	if m != nil {
		return m.TicketId
	}
	return 0
}

func (m *TkMineResp_TkList) GetCreatedAt() string {
	if m != nil {
		return m.CreatedAt
	}
	return ""
}

func (m *TkMineResp_TkList) GetCategory() string {
	if m != nil {
		return m.Category
	}
	return ""
}

func (m *TkMineResp_TkList) GetProgress() TkProgress {
	if m != nil {
		return m.Progress
	}
	return TkProgress_TkProgressUnknown
}

func (m *TkMineResp_TkList) GetStatus() TkStatus {
	if m != nil {
		return m.Status
	}
	return TkStatus_TkStatusUnknown
}

func (m *TkMineResp_TkList) GetRead() uint32 {
	if m != nil {
		return m.Read
	}
	return 0
}

func (m *TkMineResp_TkList) GetTicketSysType() TicketSys {
	if m != nil {
		return m.TicketSysType
	}
	return TicketSys_TicketSysOld
}

// TkDetailReq 工单详情
type TkDetailReq struct {
	// 项目标识 @gotags: validate:"required"
	TicketId uint64 `protobuf:"varint,1,opt,name=ticket_id,json=ticketId,proto3" json:"ticket_id" validate:"required"`
	// Deprecated: 废弃，后续整合到fpx_app_id中
	// 游戏ID @gotags: validate:"required_without=FpxAppId"
	GameId uint32 `protobuf:"varint,3,opt,name=game_id,json=gameId,proto3" json:"game_id" validate:"required_without=FpxAppId"`
	// 项目标识 @gotags: validate:"required_without=GameId"
	FpxAppId string `protobuf:"bytes,4,opt,name=fpx_app_id,json=fpxAppId,proto3" json:"fpx_app_id" validate:"required_without=GameId"`
	// lang 语言 @gotags: validate:"required"
	Lang string `protobuf:"bytes,5,opt,name=lang,proto3" json:"lang" validate:"required"`
	Uuid string `protobuf:"bytes,6,opt,name=uuid,proto3" json:"uuid"`
	// Deprecated: 废弃，fpid 后续整合到 account_id中
	Fpid uint64 `protobuf:"varint,7,opt,name=fpid,proto3" json:"fpid"`
	// uid 用户ID
	Uid uint64 `protobuf:"varint,8,opt,name=uid,proto3" json:"uid"`
	// account_id
	AccountId string `protobuf:"bytes,9,opt,name=account_id,json=accountId,proto3" json:"account_id"`
	// role_id 角色ID
	RoleId string `protobuf:"bytes,10,opt,name=role_id,json=roleId,proto3" json:"role_id"`
	// sid 区服
	Sid string `protobuf:"bytes,11,opt,name=sid,proto3" json:"sid"`
	// channel 渠道
	Channel              string   `protobuf:"bytes,12,opt,name=channel,proto3" json:"channel"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *TkDetailReq) Reset()         { *m = TkDetailReq{} }
func (m *TkDetailReq) String() string { return proto.CompactTextString(m) }
func (*TkDetailReq) ProtoMessage()    {}
func (*TkDetailReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_c529971104d8ed1e, []int{5}
}

func (m *TkDetailReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TkDetailReq.Unmarshal(m, b)
}
func (m *TkDetailReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TkDetailReq.Marshal(b, m, deterministic)
}
func (m *TkDetailReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TkDetailReq.Merge(m, src)
}
func (m *TkDetailReq) XXX_Size() int {
	return xxx_messageInfo_TkDetailReq.Size(m)
}
func (m *TkDetailReq) XXX_DiscardUnknown() {
	xxx_messageInfo_TkDetailReq.DiscardUnknown(m)
}

var xxx_messageInfo_TkDetailReq proto.InternalMessageInfo

func (m *TkDetailReq) GetTicketId() uint64 {
	if m != nil {
		return m.TicketId
	}
	return 0
}

func (m *TkDetailReq) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *TkDetailReq) GetFpxAppId() string {
	if m != nil {
		return m.FpxAppId
	}
	return ""
}

func (m *TkDetailReq) GetLang() string {
	if m != nil {
		return m.Lang
	}
	return ""
}

func (m *TkDetailReq) GetUuid() string {
	if m != nil {
		return m.Uuid
	}
	return ""
}

func (m *TkDetailReq) GetFpid() uint64 {
	if m != nil {
		return m.Fpid
	}
	return 0
}

func (m *TkDetailReq) GetUid() uint64 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *TkDetailReq) GetAccountId() string {
	if m != nil {
		return m.AccountId
	}
	return ""
}

func (m *TkDetailReq) GetRoleId() string {
	if m != nil {
		return m.RoleId
	}
	return ""
}

func (m *TkDetailReq) GetSid() string {
	if m != nil {
		return m.Sid
	}
	return ""
}

func (m *TkDetailReq) GetChannel() string {
	if m != nil {
		return m.Channel
	}
	return ""
}

// TicketDetailResp 工单详细信息
type TkDetailResp struct {
	// 工单ID
	TicketId uint64 `protobuf:"varint,1,opt,name=ticket_id,json=ticketId,proto3" json:"ticket_id"`
	// 问题分类
	Category string `protobuf:"bytes,2,opt,name=category,proto3" json:"category"`
	// 表单信息
	Filed string `protobuf:"bytes,3,opt,name=filed,proto3" json:"filed"`
	// 流转
	Transfer uint32 `protobuf:"varint,4,opt,name=transfer,proto3" json:"transfer"`
	// 解决方案
	Solution string `protobuf:"bytes,5,opt,name=solution,proto3" json:"solution"`
	// 状态 true:已结单
	Done bool `protobuf:"varint,6,opt,name=done,proto3" json:"done"`
	// 关闭 0:未关闭 1:结单 2:关闭 3:超时关闭
	Closed uint32 `protobuf:"varint,7,opt,name=closed,proto3" json:"closed"`
	// 补充举证 true:需举证 - 去补填按钮
	Evidence bool `protobuf:"varint,8,opt,name=evidence,proto3" json:"evidence"`
	// 评价 true：需评价
	Appraise bool `protobuf:"varint,9,opt,name=appraise,proto3" json:"appraise"`
	// 补充列表 -- 存在 空着 - 不需要
	Replenish []*TkDetailResp_Replenish `protobuf:"bytes,10,rep,name=replenish,proto3" json:"replenish"`
	// 沟通记录
	Commu []*TkDetailResp_Commu `protobuf:"bytes,11,rep,name=commu,proto3" json:"commu"`
	// 评分
	Csi uint32 `protobuf:"varint,12,opt,name=csi,proto3" json:"csi"`
	// 评价标签
	Label []uint32 `protobuf:"varint,13,rep,packed,name=label,proto3" json:"label"`
	// 关联类型,1:表单 2:自动化流程
	RelateType uint32 `protobuf:"varint,16,opt,name=relate_type,json=relateType,proto3" json:"relate_type"`
	// 重开信息
	Reopen []*TkDetailResp_Reopen `protobuf:"bytes,18,rep,name=reopen,proto3" json:"reopen"`
	// 超过重开次数+不支持重开：true
	ReopenDisabled bool `protobuf:"varint,19,opt,name=reopen_disabled,json=reopenDisabled,proto3" json:"reopen_disabled"`
	// 问题类别ID
	CatId uint32 `protobuf:"varint,20,opt,name=cat_id,json=catId,proto3" json:"cat_id"`
	// 使用哪个工单系统
	TicketSysType TicketSys `protobuf:"varint,21,opt,name=ticket_sys_type,json=ticketSysType,proto3,enum=pb.TicketSys" json:"ticket_sys_type"`
	// 超时回复文案
	OvertimeReply string `protobuf:"bytes,22,opt,name=overtime_reply,json=overtimeReply,proto3" json:"overtime_reply"`
	// 超时时间
	Overtime string `protobuf:"bytes,23,opt,name=overtime,proto3" json:"overtime"`
	// 是否为新版ai客服 - 0为不是，1为是
	IsChatTicket         uint32   `protobuf:"varint,24,opt,name=is_chat_ticket,json=isChatTicket,proto3" json:"is_chat_ticket"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *TkDetailResp) Reset()         { *m = TkDetailResp{} }
func (m *TkDetailResp) String() string { return proto.CompactTextString(m) }
func (*TkDetailResp) ProtoMessage()    {}
func (*TkDetailResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_c529971104d8ed1e, []int{6}
}

func (m *TkDetailResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TkDetailResp.Unmarshal(m, b)
}
func (m *TkDetailResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TkDetailResp.Marshal(b, m, deterministic)
}
func (m *TkDetailResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TkDetailResp.Merge(m, src)
}
func (m *TkDetailResp) XXX_Size() int {
	return xxx_messageInfo_TkDetailResp.Size(m)
}
func (m *TkDetailResp) XXX_DiscardUnknown() {
	xxx_messageInfo_TkDetailResp.DiscardUnknown(m)
}

var xxx_messageInfo_TkDetailResp proto.InternalMessageInfo

func (m *TkDetailResp) GetTicketId() uint64 {
	if m != nil {
		return m.TicketId
	}
	return 0
}

func (m *TkDetailResp) GetCategory() string {
	if m != nil {
		return m.Category
	}
	return ""
}

func (m *TkDetailResp) GetFiled() string {
	if m != nil {
		return m.Filed
	}
	return ""
}

func (m *TkDetailResp) GetTransfer() uint32 {
	if m != nil {
		return m.Transfer
	}
	return 0
}

func (m *TkDetailResp) GetSolution() string {
	if m != nil {
		return m.Solution
	}
	return ""
}

func (m *TkDetailResp) GetDone() bool {
	if m != nil {
		return m.Done
	}
	return false
}

func (m *TkDetailResp) GetClosed() uint32 {
	if m != nil {
		return m.Closed
	}
	return 0
}

func (m *TkDetailResp) GetEvidence() bool {
	if m != nil {
		return m.Evidence
	}
	return false
}

func (m *TkDetailResp) GetAppraise() bool {
	if m != nil {
		return m.Appraise
	}
	return false
}

func (m *TkDetailResp) GetReplenish() []*TkDetailResp_Replenish {
	if m != nil {
		return m.Replenish
	}
	return nil
}

func (m *TkDetailResp) GetCommu() []*TkDetailResp_Commu {
	if m != nil {
		return m.Commu
	}
	return nil
}

func (m *TkDetailResp) GetCsi() uint32 {
	if m != nil {
		return m.Csi
	}
	return 0
}

func (m *TkDetailResp) GetLabel() []uint32 {
	if m != nil {
		return m.Label
	}
	return nil
}

func (m *TkDetailResp) GetRelateType() uint32 {
	if m != nil {
		return m.RelateType
	}
	return 0
}

func (m *TkDetailResp) GetReopen() []*TkDetailResp_Reopen {
	if m != nil {
		return m.Reopen
	}
	return nil
}

func (m *TkDetailResp) GetReopenDisabled() bool {
	if m != nil {
		return m.ReopenDisabled
	}
	return false
}

func (m *TkDetailResp) GetCatId() uint32 {
	if m != nil {
		return m.CatId
	}
	return 0
}

func (m *TkDetailResp) GetTicketSysType() TicketSys {
	if m != nil {
		return m.TicketSysType
	}
	return TicketSys_TicketSysOld
}

func (m *TkDetailResp) GetOvertimeReply() string {
	if m != nil {
		return m.OvertimeReply
	}
	return ""
}

func (m *TkDetailResp) GetOvertime() string {
	if m != nil {
		return m.Overtime
	}
	return ""
}

func (m *TkDetailResp) GetIsChatTicket() uint32 {
	if m != nil {
		return m.IsChatTicket
	}
	return 0
}

type TkDetailResp_Replenish struct {
	ReplenishId uint64 `protobuf:"varint,1,opt,name=replenish_id,json=replenishId,proto3" json:"replenish_id"`
	// 工单ID
	TicketId uint64 `protobuf:"varint,2,opt,name=ticket_id,json=ticketId,proto3" json:"ticket_id"`
	// 备注信息
	Remark string `protobuf:"bytes,3,opt,name=remark,proto3" json:"remark"`
	// 补填内容
	FillContent string `protobuf:"bytes,4,opt,name=fill_content,json=fillContent,proto3" json:"fill_content"`
	// 文件
	Files string `protobuf:"bytes,5,opt,name=files,proto3" json:"files"`
	// 操作 9:回复信息 7:打回补填 8:关闭工单 6:处理完成
	Op uint32 `protobuf:"varint,6,opt,name=op,proto3" json:"op"`
	// 创建时间
	CreatedAt string `protobuf:"bytes,7,opt,name=created_at,json=createdAt,proto3" json:"created_at"`
	// 创建时间ts
	CreatedTs            uint64   `protobuf:"varint,8,opt,name=created_ts,json=createdTs,proto3" json:"created_ts"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *TkDetailResp_Replenish) Reset()         { *m = TkDetailResp_Replenish{} }
func (m *TkDetailResp_Replenish) String() string { return proto.CompactTextString(m) }
func (*TkDetailResp_Replenish) ProtoMessage()    {}
func (*TkDetailResp_Replenish) Descriptor() ([]byte, []int) {
	return fileDescriptor_c529971104d8ed1e, []int{6, 0}
}

func (m *TkDetailResp_Replenish) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TkDetailResp_Replenish.Unmarshal(m, b)
}
func (m *TkDetailResp_Replenish) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TkDetailResp_Replenish.Marshal(b, m, deterministic)
}
func (m *TkDetailResp_Replenish) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TkDetailResp_Replenish.Merge(m, src)
}
func (m *TkDetailResp_Replenish) XXX_Size() int {
	return xxx_messageInfo_TkDetailResp_Replenish.Size(m)
}
func (m *TkDetailResp_Replenish) XXX_DiscardUnknown() {
	xxx_messageInfo_TkDetailResp_Replenish.DiscardUnknown(m)
}

var xxx_messageInfo_TkDetailResp_Replenish proto.InternalMessageInfo

func (m *TkDetailResp_Replenish) GetReplenishId() uint64 {
	if m != nil {
		return m.ReplenishId
	}
	return 0
}

func (m *TkDetailResp_Replenish) GetTicketId() uint64 {
	if m != nil {
		return m.TicketId
	}
	return 0
}

func (m *TkDetailResp_Replenish) GetRemark() string {
	if m != nil {
		return m.Remark
	}
	return ""
}

func (m *TkDetailResp_Replenish) GetFillContent() string {
	if m != nil {
		return m.FillContent
	}
	return ""
}

func (m *TkDetailResp_Replenish) GetFiles() string {
	if m != nil {
		return m.Files
	}
	return ""
}

func (m *TkDetailResp_Replenish) GetOp() uint32 {
	if m != nil {
		return m.Op
	}
	return 0
}

func (m *TkDetailResp_Replenish) GetCreatedAt() string {
	if m != nil {
		return m.CreatedAt
	}
	return ""
}

func (m *TkDetailResp_Replenish) GetCreatedTs() uint64 {
	if m != nil {
		return m.CreatedTs
	}
	return 0
}

type TkDetailResp_Reopen struct {
	// 重开id
	ReopenId uint64 `protobuf:"varint,1,opt,name=reopen_id,json=reopenId,proto3" json:"reopen_id"`
	// 重开提交内容
	FillContent string `protobuf:"bytes,2,opt,name=fill_content,json=fillContent,proto3" json:"fill_content"`
	// 重开附件
	Files string `protobuf:"bytes,3,opt,name=files,proto3" json:"files"`
	// 补填+回复信息
	Replenish []*TkDetailResp_Replenish `protobuf:"bytes,4,rep,name=replenish,proto3" json:"replenish"`
	// 创建时间
	CreatedAt string `protobuf:"bytes,5,opt,name=created_at,json=createdAt,proto3" json:"created_at"`
	// 回复id - 执行重开 - commu
	ReplyId uint64 `protobuf:"varint,6,opt,name=reply_id,json=replyId,proto3" json:"reply_id"`
	// 重开单 - 客服回复&关单 的id - commu
	ResponseReplyId      uint64   `protobuf:"varint,7,opt,name=response_reply_id,json=responseReplyId,proto3" json:"response_reply_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *TkDetailResp_Reopen) Reset()         { *m = TkDetailResp_Reopen{} }
func (m *TkDetailResp_Reopen) String() string { return proto.CompactTextString(m) }
func (*TkDetailResp_Reopen) ProtoMessage()    {}
func (*TkDetailResp_Reopen) Descriptor() ([]byte, []int) {
	return fileDescriptor_c529971104d8ed1e, []int{6, 1}
}

func (m *TkDetailResp_Reopen) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TkDetailResp_Reopen.Unmarshal(m, b)
}
func (m *TkDetailResp_Reopen) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TkDetailResp_Reopen.Marshal(b, m, deterministic)
}
func (m *TkDetailResp_Reopen) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TkDetailResp_Reopen.Merge(m, src)
}
func (m *TkDetailResp_Reopen) XXX_Size() int {
	return xxx_messageInfo_TkDetailResp_Reopen.Size(m)
}
func (m *TkDetailResp_Reopen) XXX_DiscardUnknown() {
	xxx_messageInfo_TkDetailResp_Reopen.DiscardUnknown(m)
}

var xxx_messageInfo_TkDetailResp_Reopen proto.InternalMessageInfo

func (m *TkDetailResp_Reopen) GetReopenId() uint64 {
	if m != nil {
		return m.ReopenId
	}
	return 0
}

func (m *TkDetailResp_Reopen) GetFillContent() string {
	if m != nil {
		return m.FillContent
	}
	return ""
}

func (m *TkDetailResp_Reopen) GetFiles() string {
	if m != nil {
		return m.Files
	}
	return ""
}

func (m *TkDetailResp_Reopen) GetReplenish() []*TkDetailResp_Replenish {
	if m != nil {
		return m.Replenish
	}
	return nil
}

func (m *TkDetailResp_Reopen) GetCreatedAt() string {
	if m != nil {
		return m.CreatedAt
	}
	return ""
}

func (m *TkDetailResp_Reopen) GetReplyId() uint64 {
	if m != nil {
		return m.ReplyId
	}
	return 0
}

func (m *TkDetailResp_Reopen) GetResponseReplyId() uint64 {
	if m != nil {
		return m.ResponseReplyId
	}
	return 0
}

// 对话记录
type TkDetailResp_Commu struct {
	// 消息内容
	Content string `protobuf:"bytes,1,opt,name=content,proto3" json:"content"`
	// 消息来源: 1:玩家端； 2客服回复
	Role UserRole `protobuf:"varint,2,opt,name=role,proto3,enum=pb.UserRole" json:"role"`
	// 客服回复人员名称
	CustomNickName string `protobuf:"bytes,3,opt,name=custom_nick_name,json=customNickName,proto3" json:"custom_nick_name"`
	// 消息时间
	CreatedAt string `protobuf:"bytes,4,opt,name=created_at,json=createdAt,proto3" json:"created_at"`
	Picture   string `protobuf:"bytes,5,opt,name=picture,proto3" json:"picture"`
	Video     string `protobuf:"bytes,6,opt,name=video,proto3" json:"video"`
	// 操作类型
	OpType               uint32   `protobuf:"varint,7,opt,name=op_type,json=opType,proto3" json:"op_type"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *TkDetailResp_Commu) Reset()         { *m = TkDetailResp_Commu{} }
func (m *TkDetailResp_Commu) String() string { return proto.CompactTextString(m) }
func (*TkDetailResp_Commu) ProtoMessage()    {}
func (*TkDetailResp_Commu) Descriptor() ([]byte, []int) {
	return fileDescriptor_c529971104d8ed1e, []int{6, 2}
}

func (m *TkDetailResp_Commu) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TkDetailResp_Commu.Unmarshal(m, b)
}
func (m *TkDetailResp_Commu) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TkDetailResp_Commu.Marshal(b, m, deterministic)
}
func (m *TkDetailResp_Commu) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TkDetailResp_Commu.Merge(m, src)
}
func (m *TkDetailResp_Commu) XXX_Size() int {
	return xxx_messageInfo_TkDetailResp_Commu.Size(m)
}
func (m *TkDetailResp_Commu) XXX_DiscardUnknown() {
	xxx_messageInfo_TkDetailResp_Commu.DiscardUnknown(m)
}

var xxx_messageInfo_TkDetailResp_Commu proto.InternalMessageInfo

func (m *TkDetailResp_Commu) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *TkDetailResp_Commu) GetRole() UserRole {
	if m != nil {
		return m.Role
	}
	return UserRole_NobodyRole
}

func (m *TkDetailResp_Commu) GetCustomNickName() string {
	if m != nil {
		return m.CustomNickName
	}
	return ""
}

func (m *TkDetailResp_Commu) GetCreatedAt() string {
	if m != nil {
		return m.CreatedAt
	}
	return ""
}

func (m *TkDetailResp_Commu) GetPicture() string {
	if m != nil {
		return m.Picture
	}
	return ""
}

func (m *TkDetailResp_Commu) GetVideo() string {
	if m != nil {
		return m.Video
	}
	return ""
}

func (m *TkDetailResp_Commu) GetOpType() uint32 {
	if m != nil {
		return m.OpType
	}
	return 0
}

// TicketDetailModel
type TicketDetailModel struct {
	TicketId   uint64 `protobuf:"varint,1,opt,name=ticket_id,json=ticketId,proto3" json:"ticket_id"`
	Uuid       string `protobuf:"bytes,2,opt,name=uuid,proto3" json:"uuid"`
	Origin     uint32 `protobuf:"varint,3,opt,name=origin,proto3" json:"origin"`
	CatId      uint32 `protobuf:"varint,5,opt,name=cat_id,json=catId,proto3" json:"cat_id"`
	Evaluation uint32 `protobuf:"varint,6,opt,name=evaluation,proto3" json:"evaluation"`
	Csi        uint32 `protobuf:"varint,7,opt,name=csi,proto3" json:"csi"`
	Proof      uint32 `protobuf:"varint,8,opt,name=proof,proto3" json:"proof"`
	Stage      uint32 `protobuf:"varint,9,opt,name=stage,proto3" json:"stage"`
	Status     uint32 `protobuf:"varint,10,opt,name=status,proto3" json:"status"`
	Closed     uint32 `protobuf:"varint,11,opt,name=closed,proto3" json:"closed"`
	ClosedAt   uint64 `protobuf:"varint,12,opt,name=closed_at,json=closedAt,proto3" json:"closed_at"`
	Field      string `protobuf:"bytes,13,opt,name=field,proto3" json:"field"`
	// account_id
	AccountId string `protobuf:"bytes,16,opt,name=account_id,json=accountId,proto3" json:"account_id"`
	// project
	Project string `protobuf:"bytes,17,opt,name=project,proto3" json:"project"`
	// game_id
	GmId string `protobuf:"bytes,18,opt,name=gm_id,json=gmId,proto3" json:"gm_id"`
	// project
	Scene uint32 `protobuf:"varint,20,opt,name=scene,proto3" json:"scene"`
	// 重开次数
	ReopenNum uint32 `protobuf:"varint,21,opt,name=reopen_num,json=reopenNum,proto3" json:"reopen_num"`
	// 自动回复模版id
	AutoReplyId uint32 `protobuf:"varint,22,opt,name=auto_reply_id,json=autoReplyId,proto3" json:"auto_reply_id"`
	// 是否升级单
	Priority             uint32   `protobuf:"varint,23,opt,name=priority,proto3" json:"priority"`
	TicketType           uint32   `protobuf:"varint,24,opt,name=ticket_type,json=ticketType,proto3" json:"ticket_type"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *TicketDetailModel) Reset()         { *m = TicketDetailModel{} }
func (m *TicketDetailModel) String() string { return proto.CompactTextString(m) }
func (*TicketDetailModel) ProtoMessage()    {}
func (*TicketDetailModel) Descriptor() ([]byte, []int) {
	return fileDescriptor_c529971104d8ed1e, []int{7}
}

func (m *TicketDetailModel) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TicketDetailModel.Unmarshal(m, b)
}
func (m *TicketDetailModel) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TicketDetailModel.Marshal(b, m, deterministic)
}
func (m *TicketDetailModel) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TicketDetailModel.Merge(m, src)
}
func (m *TicketDetailModel) XXX_Size() int {
	return xxx_messageInfo_TicketDetailModel.Size(m)
}
func (m *TicketDetailModel) XXX_DiscardUnknown() {
	xxx_messageInfo_TicketDetailModel.DiscardUnknown(m)
}

var xxx_messageInfo_TicketDetailModel proto.InternalMessageInfo

func (m *TicketDetailModel) GetTicketId() uint64 {
	if m != nil {
		return m.TicketId
	}
	return 0
}

func (m *TicketDetailModel) GetUuid() string {
	if m != nil {
		return m.Uuid
	}
	return ""
}

func (m *TicketDetailModel) GetOrigin() uint32 {
	if m != nil {
		return m.Origin
	}
	return 0
}

func (m *TicketDetailModel) GetCatId() uint32 {
	if m != nil {
		return m.CatId
	}
	return 0
}

func (m *TicketDetailModel) GetEvaluation() uint32 {
	if m != nil {
		return m.Evaluation
	}
	return 0
}

func (m *TicketDetailModel) GetCsi() uint32 {
	if m != nil {
		return m.Csi
	}
	return 0
}

func (m *TicketDetailModel) GetProof() uint32 {
	if m != nil {
		return m.Proof
	}
	return 0
}

func (m *TicketDetailModel) GetStage() uint32 {
	if m != nil {
		return m.Stage
	}
	return 0
}

func (m *TicketDetailModel) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *TicketDetailModel) GetClosed() uint32 {
	if m != nil {
		return m.Closed
	}
	return 0
}

func (m *TicketDetailModel) GetClosedAt() uint64 {
	if m != nil {
		return m.ClosedAt
	}
	return 0
}

func (m *TicketDetailModel) GetField() string {
	if m != nil {
		return m.Field
	}
	return ""
}

func (m *TicketDetailModel) GetAccountId() string {
	if m != nil {
		return m.AccountId
	}
	return ""
}

func (m *TicketDetailModel) GetProject() string {
	if m != nil {
		return m.Project
	}
	return ""
}

func (m *TicketDetailModel) GetGmId() string {
	if m != nil {
		return m.GmId
	}
	return ""
}

func (m *TicketDetailModel) GetScene() uint32 {
	if m != nil {
		return m.Scene
	}
	return 0
}

func (m *TicketDetailModel) GetReopenNum() uint32 {
	if m != nil {
		return m.ReopenNum
	}
	return 0
}

func (m *TicketDetailModel) GetAutoReplyId() uint32 {
	if m != nil {
		return m.AutoReplyId
	}
	return 0
}

func (m *TicketDetailModel) GetPriority() uint32 {
	if m != nil {
		return m.Priority
	}
	return 0
}

func (m *TicketDetailModel) GetTicketType() uint32 {
	if m != nil {
		return m.TicketType
	}
	return 0
}

type NoticeResp struct {
	// 消息ID @gotags: json:"notice_id,omitempty"
	NoticeId uint64 `protobuf:"varint,1,opt,name=notice_id,json=noticeId,proto3" json:"notice_id,omitempty"`
	// 来源 @gotags: json:"from,omitempty"
	From uint32 `protobuf:"varint,2,opt,name=from,proto3" json:"from,omitempty"`
	// 对象ID @gotags: json:"object_id,omitempty"
	ObjectId uint64 `protobuf:"varint,3,opt,name=object_id,json=objectId,proto3" json:"object_id,omitempty"`
	// 场景
	Scene uint32 `protobuf:"varint,4,opt,name=scene,proto3" json:"scene"`
	// 使用哪个工单系统
	TicketSysType TicketSys `protobuf:"varint,5,opt,name=ticket_sys_type,json=ticketSysType,proto3,enum=pb.TicketSys" json:"ticket_sys_type"`
	// 消息未读数量 @gotags: json:"notice_count"
	NoticeCount          uint64   `protobuf:"varint,6,opt,name=notice_count,json=noticeCount,proto3" json:"notice_count"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *NoticeResp) Reset()         { *m = NoticeResp{} }
func (m *NoticeResp) String() string { return proto.CompactTextString(m) }
func (*NoticeResp) ProtoMessage()    {}
func (*NoticeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_c529971104d8ed1e, []int{8}
}

func (m *NoticeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NoticeResp.Unmarshal(m, b)
}
func (m *NoticeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NoticeResp.Marshal(b, m, deterministic)
}
func (m *NoticeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NoticeResp.Merge(m, src)
}
func (m *NoticeResp) XXX_Size() int {
	return xxx_messageInfo_NoticeResp.Size(m)
}
func (m *NoticeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_NoticeResp.DiscardUnknown(m)
}

var xxx_messageInfo_NoticeResp proto.InternalMessageInfo

func (m *NoticeResp) GetNoticeId() uint64 {
	if m != nil {
		return m.NoticeId
	}
	return 0
}

func (m *NoticeResp) GetFrom() uint32 {
	if m != nil {
		return m.From
	}
	return 0
}

func (m *NoticeResp) GetObjectId() uint64 {
	if m != nil {
		return m.ObjectId
	}
	return 0
}

func (m *NoticeResp) GetScene() uint32 {
	if m != nil {
		return m.Scene
	}
	return 0
}

func (m *NoticeResp) GetTicketSysType() TicketSys {
	if m != nil {
		return m.TicketSysType
	}
	return TicketSys_TicketSysOld
}

func (m *NoticeResp) GetNoticeCount() uint64 {
	if m != nil {
		return m.NoticeCount
	}
	return 0
}

// TkReplenishReq 补填信息
type TkReplenishReq struct {
	// 工单ID @gotags: validate:"required"
	TicketId uint64 `protobuf:"varint,1,opt,name=ticket_id,json=ticketId,proto3" json:"ticket_id" validate:"required"`
	// 工单内容 @gotags: validate:"required"
	Content string `protobuf:"bytes,2,opt,name=content,proto3" json:"content" validate:"required"`
	// 工单附件 @gotags: validate:"required"
	Files string `protobuf:"bytes,3,opt,name=files,proto3" json:"files" validate:"required"`
	// Deprecated: 废弃，后续整合到fpx_app_id中
	// 游戏ID @gotags: validate:"required_without=FpxAppId"
	GameId uint32 `protobuf:"varint,4,opt,name=game_id,json=gameId,proto3" json:"game_id" validate:"required_without=FpxAppId"`
	// 项目标识 @gotags: validate:"required_without=GameId"
	FpxAppId string `protobuf:"bytes,5,opt,name=fpx_app_id,json=fpxAppId,proto3" json:"fpx_app_id" validate:"required_without=GameId"`
	// lang 语言 @gotags: validate:"required"
	Lang string `protobuf:"bytes,6,opt,name=lang,proto3" json:"lang" validate:"required"`
	// uuid 设备ID
	Uuid string `protobuf:"bytes,7,opt,name=uuid,proto3" json:"uuid"`
	// Deprecated: 废弃，fpid 后续整合到 account_id中
	Fpid uint64 `protobuf:"varint,8,opt,name=fpid,proto3" json:"fpid"`
	// uid 用户ID
	Uid uint64 `protobuf:"varint,9,opt,name=uid,proto3" json:"uid"`
	// account_id
	AccountId string `protobuf:"bytes,10,opt,name=account_id,json=accountId,proto3" json:"account_id"`
	// role_id 角色ID
	RoleId string `protobuf:"bytes,11,opt,name=role_id,json=roleId,proto3" json:"role_id"`
	// sid 区服
	Sid string `protobuf:"bytes,12,opt,name=sid,proto3" json:"sid"`
	// channel 渠道
	Channel string `protobuf:"bytes,13,opt,name=channel,proto3" json:"channel"`
	// nickname 昵称
	Nickname string `protobuf:"bytes,14,opt,name=nickname,proto3" json:"nickname"`
	// subchannel 子渠道
	Subchannel           string   `protobuf:"bytes,34,opt,name=subchannel,proto3" json:"subchannel"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *TkReplenishReq) Reset()         { *m = TkReplenishReq{} }
func (m *TkReplenishReq) String() string { return proto.CompactTextString(m) }
func (*TkReplenishReq) ProtoMessage()    {}
func (*TkReplenishReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_c529971104d8ed1e, []int{9}
}

func (m *TkReplenishReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TkReplenishReq.Unmarshal(m, b)
}
func (m *TkReplenishReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TkReplenishReq.Marshal(b, m, deterministic)
}
func (m *TkReplenishReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TkReplenishReq.Merge(m, src)
}
func (m *TkReplenishReq) XXX_Size() int {
	return xxx_messageInfo_TkReplenishReq.Size(m)
}
func (m *TkReplenishReq) XXX_DiscardUnknown() {
	xxx_messageInfo_TkReplenishReq.DiscardUnknown(m)
}

var xxx_messageInfo_TkReplenishReq proto.InternalMessageInfo

func (m *TkReplenishReq) GetTicketId() uint64 {
	if m != nil {
		return m.TicketId
	}
	return 0
}

func (m *TkReplenishReq) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *TkReplenishReq) GetFiles() string {
	if m != nil {
		return m.Files
	}
	return ""
}

func (m *TkReplenishReq) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *TkReplenishReq) GetFpxAppId() string {
	if m != nil {
		return m.FpxAppId
	}
	return ""
}

func (m *TkReplenishReq) GetLang() string {
	if m != nil {
		return m.Lang
	}
	return ""
}

func (m *TkReplenishReq) GetUuid() string {
	if m != nil {
		return m.Uuid
	}
	return ""
}

func (m *TkReplenishReq) GetFpid() uint64 {
	if m != nil {
		return m.Fpid
	}
	return 0
}

func (m *TkReplenishReq) GetUid() uint64 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *TkReplenishReq) GetAccountId() string {
	if m != nil {
		return m.AccountId
	}
	return ""
}

func (m *TkReplenishReq) GetRoleId() string {
	if m != nil {
		return m.RoleId
	}
	return ""
}

func (m *TkReplenishReq) GetSid() string {
	if m != nil {
		return m.Sid
	}
	return ""
}

func (m *TkReplenishReq) GetChannel() string {
	if m != nil {
		return m.Channel
	}
	return ""
}

func (m *TkReplenishReq) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *TkReplenishReq) GetSubchannel() string {
	if m != nil {
		return m.Subchannel
	}
	return ""
}

// TkAppraiseReq 评价
type TkAppraiseReq struct {
	// 工单ID @gotags: validate:"required"
	TicketId uint64 `protobuf:"varint,1,opt,name=ticket_id,json=ticketId,proto3" json:"ticket_id" validate:"required"`
	// Deprecated: 废弃，后续整合到fpx_app_id中
	// 游戏ID @gotags: validate:"required_without=FpxAppId"
	GameId uint32 `protobuf:"varint,4,opt,name=game_id,json=gameId,proto3" json:"game_id" validate:"required_without=FpxAppId"`
	// 项目标识 @gotags: validate:"required_without=GameId"
	FpxAppId string `protobuf:"bytes,5,opt,name=fpx_app_id,json=fpxAppId,proto3" json:"fpx_app_id" validate:"required_without=GameId"`
	// lang 语言 @gotags: validate:"required"
	Lang string `protobuf:"bytes,6,opt,name=lang,proto3" json:"lang" validate:"required"`
	// Deprecated: 废弃，fpid 后续整合到 account_id中
	Fpid uint64 `protobuf:"varint,8,opt,name=fpid,proto3" json:"fpid"`
	// uid 用户ID
	Uid uint64 `protobuf:"varint,9,opt,name=uid,proto3" json:"uid"`
	// account_id
	AccountId string `protobuf:"bytes,10,opt,name=account_id,json=accountId,proto3" json:"account_id"`
	// role_id 角色ID
	RoleId string `protobuf:"bytes,11,opt,name=role_id,json=roleId,proto3" json:"role_id"`
	// sid 区服
	Sid string `protobuf:"bytes,12,opt,name=sid,proto3" json:"sid"`
	// channel 渠道
	Channel string `protobuf:"bytes,13,opt,name=channel,proto3" json:"channel"`
	// 评价等级 @gotags: validate:"required"
	Appraise uint32 `protobuf:"varint,14,opt,name=appraise,proto3" json:"appraise" validate:"required"`
	// 服务态度评分 - 老接口字段 - 废弃
	ServiceRating uint32 `protobuf:"varint,15,opt,name=service_rating,json=serviceRating,proto3" json:"service_rating"`
	// 处理速度评分 - 老接口字段 - 废弃
	ServiceTimeRating uint32 `protobuf:"varint,16,opt,name=service_time_rating,json=serviceTimeRating,proto3" json:"service_time_rating"`
	// 处理方案评分 - 老接口字段 - 废弃
	ServiceSolutionRating uint32 `protobuf:"varint,17,opt,name=service_solution_rating,json=serviceSolutionRating,proto3" json:"service_solution_rating"`
	// 推荐给别人的意愿程度 @gotags: validate:"required_if=ServiceRating 0"
	RecommendationLevel uint32 `protobuf:"varint,18,opt,name=recommendation_level,json=recommendationLevel,proto3" json:"recommendation_level" validate:"required_if=ServiceRating 0"`
	// 评价内容
	Remark               string   `protobuf:"bytes,19,opt,name=remark,proto3" json:"remark"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *TkAppraiseReq) Reset()         { *m = TkAppraiseReq{} }
func (m *TkAppraiseReq) String() string { return proto.CompactTextString(m) }
func (*TkAppraiseReq) ProtoMessage()    {}
func (*TkAppraiseReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_c529971104d8ed1e, []int{10}
}

func (m *TkAppraiseReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TkAppraiseReq.Unmarshal(m, b)
}
func (m *TkAppraiseReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TkAppraiseReq.Marshal(b, m, deterministic)
}
func (m *TkAppraiseReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TkAppraiseReq.Merge(m, src)
}
func (m *TkAppraiseReq) XXX_Size() int {
	return xxx_messageInfo_TkAppraiseReq.Size(m)
}
func (m *TkAppraiseReq) XXX_DiscardUnknown() {
	xxx_messageInfo_TkAppraiseReq.DiscardUnknown(m)
}

var xxx_messageInfo_TkAppraiseReq proto.InternalMessageInfo

func (m *TkAppraiseReq) GetTicketId() uint64 {
	if m != nil {
		return m.TicketId
	}
	return 0
}

func (m *TkAppraiseReq) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *TkAppraiseReq) GetFpxAppId() string {
	if m != nil {
		return m.FpxAppId
	}
	return ""
}

func (m *TkAppraiseReq) GetLang() string {
	if m != nil {
		return m.Lang
	}
	return ""
}

func (m *TkAppraiseReq) GetFpid() uint64 {
	if m != nil {
		return m.Fpid
	}
	return 0
}

func (m *TkAppraiseReq) GetUid() uint64 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *TkAppraiseReq) GetAccountId() string {
	if m != nil {
		return m.AccountId
	}
	return ""
}

func (m *TkAppraiseReq) GetRoleId() string {
	if m != nil {
		return m.RoleId
	}
	return ""
}

func (m *TkAppraiseReq) GetSid() string {
	if m != nil {
		return m.Sid
	}
	return ""
}

func (m *TkAppraiseReq) GetChannel() string {
	if m != nil {
		return m.Channel
	}
	return ""
}

func (m *TkAppraiseReq) GetAppraise() uint32 {
	if m != nil {
		return m.Appraise
	}
	return 0
}

func (m *TkAppraiseReq) GetServiceRating() uint32 {
	if m != nil {
		return m.ServiceRating
	}
	return 0
}

func (m *TkAppraiseReq) GetServiceTimeRating() uint32 {
	if m != nil {
		return m.ServiceTimeRating
	}
	return 0
}

func (m *TkAppraiseReq) GetServiceSolutionRating() uint32 {
	if m != nil {
		return m.ServiceSolutionRating
	}
	return 0
}

func (m *TkAppraiseReq) GetRecommendationLevel() uint32 {
	if m != nil {
		return m.RecommendationLevel
	}
	return 0
}

func (m *TkAppraiseReq) GetRemark() string {
	if m != nil {
		return m.Remark
	}
	return ""
}

// TkAppraiseReq 工单评价
type TkAppraiseFeedbackReq struct {
	// 工单ID @gotags: validate:"required"
	TicketId uint64 `protobuf:"varint,1,opt,name=ticket_id,json=ticketId,proto3" json:"ticket_id" validate:"required"`
	// Deprecated: 废弃，后续整合到fpx_app_id中
	// 游戏ID @gotags: validate:"required_without=FpxAppId"
	GameId uint32 `protobuf:"varint,4,opt,name=game_id,json=gameId,proto3" json:"game_id" validate:"required_without=FpxAppId"`
	// 项目标识 @gotags: validate:"required_without=GameId"
	FpxAppId string `protobuf:"bytes,5,opt,name=fpx_app_id,json=fpxAppId,proto3" json:"fpx_app_id" validate:"required_without=GameId"`
	// lang 语言 @gotags: validate:"required"
	Lang string `protobuf:"bytes,6,opt,name=lang,proto3" json:"lang" validate:"required"`
	// Deprecated: 废弃，fpid 后续整合到 account_id中
	Fpid uint64 `protobuf:"varint,8,opt,name=fpid,proto3" json:"fpid"`
	// uid 用户ID
	Uid uint64 `protobuf:"varint,9,opt,name=uid,proto3" json:"uid"`
	// account_id
	AccountId string `protobuf:"bytes,10,opt,name=account_id,json=accountId,proto3" json:"account_id"`
	// role_id 角色ID
	RoleId string `protobuf:"bytes,11,opt,name=role_id,json=roleId,proto3" json:"role_id"`
	// sid 区服
	Sid string `protobuf:"bytes,12,opt,name=sid,proto3" json:"sid"`
	// channel 渠道
	Channel string `protobuf:"bytes,13,opt,name=channel,proto3" json:"channel"`
	// 是否解决  1：解决 2：未解决
	Resolved             uint32   `protobuf:"varint,14,opt,name=resolved,proto3" json:"resolved"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *TkAppraiseFeedbackReq) Reset()         { *m = TkAppraiseFeedbackReq{} }
func (m *TkAppraiseFeedbackReq) String() string { return proto.CompactTextString(m) }
func (*TkAppraiseFeedbackReq) ProtoMessage()    {}
func (*TkAppraiseFeedbackReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_c529971104d8ed1e, []int{11}
}

func (m *TkAppraiseFeedbackReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TkAppraiseFeedbackReq.Unmarshal(m, b)
}
func (m *TkAppraiseFeedbackReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TkAppraiseFeedbackReq.Marshal(b, m, deterministic)
}
func (m *TkAppraiseFeedbackReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TkAppraiseFeedbackReq.Merge(m, src)
}
func (m *TkAppraiseFeedbackReq) XXX_Size() int {
	return xxx_messageInfo_TkAppraiseFeedbackReq.Size(m)
}
func (m *TkAppraiseFeedbackReq) XXX_DiscardUnknown() {
	xxx_messageInfo_TkAppraiseFeedbackReq.DiscardUnknown(m)
}

var xxx_messageInfo_TkAppraiseFeedbackReq proto.InternalMessageInfo

func (m *TkAppraiseFeedbackReq) GetTicketId() uint64 {
	if m != nil {
		return m.TicketId
	}
	return 0
}

func (m *TkAppraiseFeedbackReq) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *TkAppraiseFeedbackReq) GetFpxAppId() string {
	if m != nil {
		return m.FpxAppId
	}
	return ""
}

func (m *TkAppraiseFeedbackReq) GetLang() string {
	if m != nil {
		return m.Lang
	}
	return ""
}

func (m *TkAppraiseFeedbackReq) GetFpid() uint64 {
	if m != nil {
		return m.Fpid
	}
	return 0
}

func (m *TkAppraiseFeedbackReq) GetUid() uint64 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *TkAppraiseFeedbackReq) GetAccountId() string {
	if m != nil {
		return m.AccountId
	}
	return ""
}

func (m *TkAppraiseFeedbackReq) GetRoleId() string {
	if m != nil {
		return m.RoleId
	}
	return ""
}

func (m *TkAppraiseFeedbackReq) GetSid() string {
	if m != nil {
		return m.Sid
	}
	return ""
}

func (m *TkAppraiseFeedbackReq) GetChannel() string {
	if m != nil {
		return m.Channel
	}
	return ""
}

func (m *TkAppraiseFeedbackReq) GetResolved() uint32 {
	if m != nil {
		return m.Resolved
	}
	return 0
}

// TkCommunicateReq 沟通发送消息
type TkCommunicateReq struct {
	// 工单ID @gotags: validate:"required"
	TicketId uint64 `protobuf:"varint,1,opt,name=ticket_id,json=ticketId,proto3" json:"ticket_id" validate:"required"`
	// Deprecated: 废弃，后续整合到fpx_app_id中
	// 游戏ID @gotags: validate:"required_without=FpxAppId"
	GameId uint32 `protobuf:"varint,4,opt,name=game_id,json=gameId,proto3" json:"game_id" validate:"required_without=FpxAppId"`
	// 项目标识 @gotags: validate:"required_without=GameId"
	FpxAppId string `protobuf:"bytes,5,opt,name=fpx_app_id,json=fpxAppId,proto3" json:"fpx_app_id" validate:"required_without=GameId"`
	// lang 语言 @gotags: validate:"required"
	Lang string `protobuf:"bytes,6,opt,name=lang,proto3" json:"lang" validate:"required"`
	// Deprecated: 废弃，fpid 后续整合到 account_id中
	Fpid uint64 `protobuf:"varint,8,opt,name=fpid,proto3" json:"fpid"`
	// uid 用户ID
	Uid uint64 `protobuf:"varint,9,opt,name=uid,proto3" json:"uid"`
	// account_id
	AccountId string `protobuf:"bytes,10,opt,name=account_id,json=accountId,proto3" json:"account_id"`
	// role_id 角色ID
	RoleId string `protobuf:"bytes,11,opt,name=role_id,json=roleId,proto3" json:"role_id"`
	// sid 区服
	Sid string `protobuf:"bytes,12,opt,name=sid,proto3" json:"sid"`
	// channel 渠道
	Channel string `protobuf:"bytes,13,opt,name=channel,proto3" json:"channel"`
	// 发送消息内容
	Content string `protobuf:"bytes,14,opt,name=content,proto3" json:"content"`
	// 玩家上传的图片url链接
	Picture string `protobuf:"bytes,15,opt,name=picture,proto3" json:"picture"`
	// 玩家上传的视频url链接
	Video string `protobuf:"bytes,16,opt,name=video,proto3" json:"video"`
	// 场景
	Scene uint32 `protobuf:"varint,17,opt,name=scene,proto3" json:"scene"`
	// uuid 设备ID
	Uuid                 string   `protobuf:"bytes,18,opt,name=uuid,proto3" json:"uuid"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *TkCommunicateReq) Reset()         { *m = TkCommunicateReq{} }
func (m *TkCommunicateReq) String() string { return proto.CompactTextString(m) }
func (*TkCommunicateReq) ProtoMessage()    {}
func (*TkCommunicateReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_c529971104d8ed1e, []int{12}
}

func (m *TkCommunicateReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TkCommunicateReq.Unmarshal(m, b)
}
func (m *TkCommunicateReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TkCommunicateReq.Marshal(b, m, deterministic)
}
func (m *TkCommunicateReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TkCommunicateReq.Merge(m, src)
}
func (m *TkCommunicateReq) XXX_Size() int {
	return xxx_messageInfo_TkCommunicateReq.Size(m)
}
func (m *TkCommunicateReq) XXX_DiscardUnknown() {
	xxx_messageInfo_TkCommunicateReq.DiscardUnknown(m)
}

var xxx_messageInfo_TkCommunicateReq proto.InternalMessageInfo

func (m *TkCommunicateReq) GetTicketId() uint64 {
	if m != nil {
		return m.TicketId
	}
	return 0
}

func (m *TkCommunicateReq) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *TkCommunicateReq) GetFpxAppId() string {
	if m != nil {
		return m.FpxAppId
	}
	return ""
}

func (m *TkCommunicateReq) GetLang() string {
	if m != nil {
		return m.Lang
	}
	return ""
}

func (m *TkCommunicateReq) GetFpid() uint64 {
	if m != nil {
		return m.Fpid
	}
	return 0
}

func (m *TkCommunicateReq) GetUid() uint64 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *TkCommunicateReq) GetAccountId() string {
	if m != nil {
		return m.AccountId
	}
	return ""
}

func (m *TkCommunicateReq) GetRoleId() string {
	if m != nil {
		return m.RoleId
	}
	return ""
}

func (m *TkCommunicateReq) GetSid() string {
	if m != nil {
		return m.Sid
	}
	return ""
}

func (m *TkCommunicateReq) GetChannel() string {
	if m != nil {
		return m.Channel
	}
	return ""
}

func (m *TkCommunicateReq) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *TkCommunicateReq) GetPicture() string {
	if m != nil {
		return m.Picture
	}
	return ""
}

func (m *TkCommunicateReq) GetVideo() string {
	if m != nil {
		return m.Video
	}
	return ""
}

func (m *TkCommunicateReq) GetScene() uint32 {
	if m != nil {
		return m.Scene
	}
	return 0
}

func (m *TkCommunicateReq) GetUuid() string {
	if m != nil {
		return m.Uuid
	}
	return ""
}

type NoticeRequestV2 struct {
	// 项目标识 @gotags: validate:"required"
	AppId string `protobuf:"bytes,1,opt,name=appId,proto3" json:"appId" validate:"required"`
	// 项目标识 @gotags: validate:"required"
	GameId uint32 `protobuf:"varint,2,opt,name=gameId,proto3" json:"gameId" validate:"required"`
	// 唯一标识 @gotags: validate:"required_without=Fpid"
	DeviceId string `protobuf:"bytes,3,opt,name=device_id,json=deviceId,proto3" json:"device_id" validate:"required_without=Fpid"`
	// fpid @gotags: validate:"required_without=DeviceId"
	Fpid uint64 `protobuf:"varint,4,opt,name=fpid,proto3" json:"fpid" validate:"required_without=DeviceId"`
	// uid
	Uid uint64 `protobuf:"varint,5,opt,name=uid,proto3" json:"uid"`
	// 区服标识
	Sid string `protobuf:"bytes,6,opt,name=sid,proto3" json:"sid"`
	// 渠道标识
	Channel string `protobuf:"bytes,7,opt,name=channel,proto3" json:"channel"`
	// 语言标识
	Lang string `protobuf:"bytes,8,opt,name=lang,proto3" json:"lang"`
	// Sdk版本
	SdkVersion string `protobuf:"bytes,9,opt,name=sdk_version,json=sdkVersion,proto3" json:"sdk_version"`
	// 系统版本
	Os string `protobuf:"bytes,10,opt,name=os,proto3" json:"os"`
	// 时间戳
	Ts uint64 `protobuf:"varint,11,opt,name=ts,proto3" json:"ts"`
	// 客户端版
	Auth string `protobuf:"bytes,12,opt,name=auth,proto3" json:"auth"`
	// 场景
	Scene                uint32   `protobuf:"varint,13,opt,name=scene,proto3" json:"scene"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *NoticeRequestV2) Reset()         { *m = NoticeRequestV2{} }
func (m *NoticeRequestV2) String() string { return proto.CompactTextString(m) }
func (*NoticeRequestV2) ProtoMessage()    {}
func (*NoticeRequestV2) Descriptor() ([]byte, []int) {
	return fileDescriptor_c529971104d8ed1e, []int{13}
}

func (m *NoticeRequestV2) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NoticeRequestV2.Unmarshal(m, b)
}
func (m *NoticeRequestV2) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NoticeRequestV2.Marshal(b, m, deterministic)
}
func (m *NoticeRequestV2) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NoticeRequestV2.Merge(m, src)
}
func (m *NoticeRequestV2) XXX_Size() int {
	return xxx_messageInfo_NoticeRequestV2.Size(m)
}
func (m *NoticeRequestV2) XXX_DiscardUnknown() {
	xxx_messageInfo_NoticeRequestV2.DiscardUnknown(m)
}

var xxx_messageInfo_NoticeRequestV2 proto.InternalMessageInfo

func (m *NoticeRequestV2) GetAppId() string {
	if m != nil {
		return m.AppId
	}
	return ""
}

func (m *NoticeRequestV2) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *NoticeRequestV2) GetDeviceId() string {
	if m != nil {
		return m.DeviceId
	}
	return ""
}

func (m *NoticeRequestV2) GetFpid() uint64 {
	if m != nil {
		return m.Fpid
	}
	return 0
}

func (m *NoticeRequestV2) GetUid() uint64 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *NoticeRequestV2) GetSid() string {
	if m != nil {
		return m.Sid
	}
	return ""
}

func (m *NoticeRequestV2) GetChannel() string {
	if m != nil {
		return m.Channel
	}
	return ""
}

func (m *NoticeRequestV2) GetLang() string {
	if m != nil {
		return m.Lang
	}
	return ""
}

func (m *NoticeRequestV2) GetSdkVersion() string {
	if m != nil {
		return m.SdkVersion
	}
	return ""
}

func (m *NoticeRequestV2) GetOs() string {
	if m != nil {
		return m.Os
	}
	return ""
}

func (m *NoticeRequestV2) GetTs() uint64 {
	if m != nil {
		return m.Ts
	}
	return 0
}

func (m *NoticeRequestV2) GetAuth() string {
	if m != nil {
		return m.Auth
	}
	return ""
}

func (m *NoticeRequestV2) GetScene() uint32 {
	if m != nil {
		return m.Scene
	}
	return 0
}

// NoticeFpxRequest fpx 消息通知
type NoticeFpxRequestV3 struct {
	// 项目标识 @gotags: validate:"required"
	AppId string `protobuf:"bytes,1,opt,name=appId,proto3" json:"appId" validate:"required"`
	// 项目标识:对应kg:game_id/fpx:fpx_app_id @gotags: validate:"required"
	FpxAppId string `protobuf:"bytes,2,opt,name=fpx_app_id,json=fpxAppId,proto3" json:"fpx_app_id" validate:"required"`
	// 唯一标识：游戏外场景为uuid @gotags: validate:"required"
	DeviceId string `protobuf:"bytes,4,opt,name=device_id,json=deviceId,proto3" json:"device_id" validate:"required"`
	// account_id - 对应kg:fpid/fpx:account_id
	AccountId string `protobuf:"bytes,5,opt,name=account_id,json=accountId,proto3" json:"account_id"`
	// sid 区服ID
	Sid string `protobuf:"bytes,6,opt,name=sid,proto3" json:"sid"`
	// role_id 角色ID
	RoleId string `protobuf:"bytes,7,opt,name=role_id,json=roleId,proto3" json:"role_id"`
	// 时间戳
	Ts uint64 `protobuf:"varint,8,opt,name=ts,proto3" json:"ts"`
	// 客户端版
	Auth string `protobuf:"bytes,9,opt,name=auth,proto3" json:"auth"`
	// 场景
	Scene                uint32   `protobuf:"varint,10,opt,name=scene,proto3" json:"scene"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *NoticeFpxRequestV3) Reset()         { *m = NoticeFpxRequestV3{} }
func (m *NoticeFpxRequestV3) String() string { return proto.CompactTextString(m) }
func (*NoticeFpxRequestV3) ProtoMessage()    {}
func (*NoticeFpxRequestV3) Descriptor() ([]byte, []int) {
	return fileDescriptor_c529971104d8ed1e, []int{14}
}

func (m *NoticeFpxRequestV3) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_NoticeFpxRequestV3.Unmarshal(m, b)
}
func (m *NoticeFpxRequestV3) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_NoticeFpxRequestV3.Marshal(b, m, deterministic)
}
func (m *NoticeFpxRequestV3) XXX_Merge(src proto.Message) {
	xxx_messageInfo_NoticeFpxRequestV3.Merge(m, src)
}
func (m *NoticeFpxRequestV3) XXX_Size() int {
	return xxx_messageInfo_NoticeFpxRequestV3.Size(m)
}
func (m *NoticeFpxRequestV3) XXX_DiscardUnknown() {
	xxx_messageInfo_NoticeFpxRequestV3.DiscardUnknown(m)
}

var xxx_messageInfo_NoticeFpxRequestV3 proto.InternalMessageInfo

func (m *NoticeFpxRequestV3) GetAppId() string {
	if m != nil {
		return m.AppId
	}
	return ""
}

func (m *NoticeFpxRequestV3) GetFpxAppId() string {
	if m != nil {
		return m.FpxAppId
	}
	return ""
}

func (m *NoticeFpxRequestV3) GetDeviceId() string {
	if m != nil {
		return m.DeviceId
	}
	return ""
}

func (m *NoticeFpxRequestV3) GetAccountId() string {
	if m != nil {
		return m.AccountId
	}
	return ""
}

func (m *NoticeFpxRequestV3) GetSid() string {
	if m != nil {
		return m.Sid
	}
	return ""
}

func (m *NoticeFpxRequestV3) GetRoleId() string {
	if m != nil {
		return m.RoleId
	}
	return ""
}

func (m *NoticeFpxRequestV3) GetTs() uint64 {
	if m != nil {
		return m.Ts
	}
	return 0
}

func (m *NoticeFpxRequestV3) GetAuth() string {
	if m != nil {
		return m.Auth
	}
	return ""
}

func (m *NoticeFpxRequestV3) GetScene() uint32 {
	if m != nil {
		return m.Scene
	}
	return 0
}

// 私域服务端请求查询工单未读消息 - 请求参数
type InnerNoticeRequest struct {
	// 请求来源标识 @gotags: validate:"required"
	SourceAppId string `protobuf:"bytes,1,opt,name=source_app_id,json=sourceAppId,proto3" json:"source_app_id" validate:"required"`
	// 项目标识：kg:game_id/fpx:fpx_app_id @gotags: validate:"required"
	FpxAppId string `protobuf:"bytes,2,opt,name=fpx_app_id,json=fpxAppId,proto3" json:"fpx_app_id" validate:"required"`
	// 唯一标识：设备id @gotags: validate:"required_without=AccountId"
	DeviceId string `protobuf:"bytes,3,opt,name=device_id,json=deviceId,proto3" json:"device_id" validate:"required_without=AccountId"`
	// 对应用户标识：kg:fpid/fpx:account_id @gotags: validate:"required_without=DeviceId"
	AccountId string `protobuf:"bytes,4,opt,name=account_id,json=accountId,proto3" json:"account_id" validate:"required_without=DeviceId"`
	// 游戏内用户uid：kg:uid/fpx:uid @gotags: validate:"required_without=DeviceId"
	Uid string `protobuf:"bytes,5,opt,name=uid,proto3" json:"uid" validate:"required_without=DeviceId"`
	// 场景来源： 3： 游戏内场景
	Scene                uint32   `protobuf:"varint,6,opt,name=scene,proto3" json:"scene"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *InnerNoticeRequest) Reset()         { *m = InnerNoticeRequest{} }
func (m *InnerNoticeRequest) String() string { return proto.CompactTextString(m) }
func (*InnerNoticeRequest) ProtoMessage()    {}
func (*InnerNoticeRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_c529971104d8ed1e, []int{15}
}

func (m *InnerNoticeRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_InnerNoticeRequest.Unmarshal(m, b)
}
func (m *InnerNoticeRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_InnerNoticeRequest.Marshal(b, m, deterministic)
}
func (m *InnerNoticeRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_InnerNoticeRequest.Merge(m, src)
}
func (m *InnerNoticeRequest) XXX_Size() int {
	return xxx_messageInfo_InnerNoticeRequest.Size(m)
}
func (m *InnerNoticeRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_InnerNoticeRequest.DiscardUnknown(m)
}

var xxx_messageInfo_InnerNoticeRequest proto.InternalMessageInfo

func (m *InnerNoticeRequest) GetSourceAppId() string {
	if m != nil {
		return m.SourceAppId
	}
	return ""
}

func (m *InnerNoticeRequest) GetFpxAppId() string {
	if m != nil {
		return m.FpxAppId
	}
	return ""
}

func (m *InnerNoticeRequest) GetDeviceId() string {
	if m != nil {
		return m.DeviceId
	}
	return ""
}

func (m *InnerNoticeRequest) GetAccountId() string {
	if m != nil {
		return m.AccountId
	}
	return ""
}

func (m *InnerNoticeRequest) GetUid() string {
	if m != nil {
		return m.Uid
	}
	return ""
}

func (m *InnerNoticeRequest) GetScene() uint32 {
	if m != nil {
		return m.Scene
	}
	return 0
}

// 私域服务端请求查询工单未读消息 - 响应数据
type InnerNoticeResp struct {
	// 是否有未读消息
	HasUnread bool `protobuf:"varint,1,opt,name=has_unread,json=hasUnread,proto3" json:"has_unread"`
	// 最新一条消息ID @gotags: json:"notice_id"
	NoticeId uint64 `protobuf:"varint,2,opt,name=notice_id,json=noticeId,proto3" json:"notice_id"`
	// 最新一条对象ID @gotags: json:"object_id"
	ObjectId uint64 `protobuf:"varint,3,opt,name=object_id,json=objectId,proto3" json:"object_id"`
	// 场景
	Scene                uint32   `protobuf:"varint,4,opt,name=scene,proto3" json:"scene"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *InnerNoticeResp) Reset()         { *m = InnerNoticeResp{} }
func (m *InnerNoticeResp) String() string { return proto.CompactTextString(m) }
func (*InnerNoticeResp) ProtoMessage()    {}
func (*InnerNoticeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_c529971104d8ed1e, []int{16}
}

func (m *InnerNoticeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_InnerNoticeResp.Unmarshal(m, b)
}
func (m *InnerNoticeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_InnerNoticeResp.Marshal(b, m, deterministic)
}
func (m *InnerNoticeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_InnerNoticeResp.Merge(m, src)
}
func (m *InnerNoticeResp) XXX_Size() int {
	return xxx_messageInfo_InnerNoticeResp.Size(m)
}
func (m *InnerNoticeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_InnerNoticeResp.DiscardUnknown(m)
}

var xxx_messageInfo_InnerNoticeResp proto.InternalMessageInfo

func (m *InnerNoticeResp) GetHasUnread() bool {
	if m != nil {
		return m.HasUnread
	}
	return false
}

func (m *InnerNoticeResp) GetNoticeId() uint64 {
	if m != nil {
		return m.NoticeId
	}
	return 0
}

func (m *InnerNoticeResp) GetObjectId() uint64 {
	if m != nil {
		return m.ObjectId
	}
	return 0
}

func (m *InnerNoticeResp) GetScene() uint32 {
	if m != nil {
		return m.Scene
	}
	return 0
}

// 自动回复模板 - 结构体定义
type TkCatAutoReplyTplPrefer struct {
	// swift回复模板id
	SwiftReplyTplId uint32 `protobuf:"varint,1,opt,name=swift_reply_tpl_id,json=swiftReplyTplId,proto3" json:"swift_reply_tpl_id"`
	// 生效规则： false:全部生效 true:部分生效: 需过滤条件
	ReplyFlagForFilter bool `protobuf:"varint,2,opt,name=reply_flag_for_filter,json=replyFlagForFilter,proto3" json:"reply_flag_for_filter"`
	// 过滤条件： 累付金额 小于等于xx美金 自动回复, 两位小数
	TotalPay             float64  `protobuf:"fixed64,3,opt,name=total_pay,json=totalPay,proto3" json:"total_pay"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *TkCatAutoReplyTplPrefer) Reset()         { *m = TkCatAutoReplyTplPrefer{} }
func (m *TkCatAutoReplyTplPrefer) String() string { return proto.CompactTextString(m) }
func (*TkCatAutoReplyTplPrefer) ProtoMessage()    {}
func (*TkCatAutoReplyTplPrefer) Descriptor() ([]byte, []int) {
	return fileDescriptor_c529971104d8ed1e, []int{17}
}

func (m *TkCatAutoReplyTplPrefer) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TkCatAutoReplyTplPrefer.Unmarshal(m, b)
}
func (m *TkCatAutoReplyTplPrefer) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TkCatAutoReplyTplPrefer.Marshal(b, m, deterministic)
}
func (m *TkCatAutoReplyTplPrefer) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TkCatAutoReplyTplPrefer.Merge(m, src)
}
func (m *TkCatAutoReplyTplPrefer) XXX_Size() int {
	return xxx_messageInfo_TkCatAutoReplyTplPrefer.Size(m)
}
func (m *TkCatAutoReplyTplPrefer) XXX_DiscardUnknown() {
	xxx_messageInfo_TkCatAutoReplyTplPrefer.DiscardUnknown(m)
}

var xxx_messageInfo_TkCatAutoReplyTplPrefer proto.InternalMessageInfo

func (m *TkCatAutoReplyTplPrefer) GetSwiftReplyTplId() uint32 {
	if m != nil {
		return m.SwiftReplyTplId
	}
	return 0
}

func (m *TkCatAutoReplyTplPrefer) GetReplyFlagForFilter() bool {
	if m != nil {
		return m.ReplyFlagForFilter
	}
	return false
}

func (m *TkCatAutoReplyTplPrefer) GetTotalPay() float64 {
	if m != nil {
		return m.TotalPay
	}
	return 0
}

// 过滤:自动回复模板 - 请求参数
type TkCatAutoReplyParamDf struct {
	// 充值总额 - 美金*rate
	TotalPay             uint64   `protobuf:"varint,1,opt,name=total_pay,json=totalPay,proto3" json:"total_pay"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *TkCatAutoReplyParamDf) Reset()         { *m = TkCatAutoReplyParamDf{} }
func (m *TkCatAutoReplyParamDf) String() string { return proto.CompactTextString(m) }
func (*TkCatAutoReplyParamDf) ProtoMessage()    {}
func (*TkCatAutoReplyParamDf) Descriptor() ([]byte, []int) {
	return fileDescriptor_c529971104d8ed1e, []int{18}
}

func (m *TkCatAutoReplyParamDf) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TkCatAutoReplyParamDf.Unmarshal(m, b)
}
func (m *TkCatAutoReplyParamDf) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TkCatAutoReplyParamDf.Marshal(b, m, deterministic)
}
func (m *TkCatAutoReplyParamDf) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TkCatAutoReplyParamDf.Merge(m, src)
}
func (m *TkCatAutoReplyParamDf) XXX_Size() int {
	return xxx_messageInfo_TkCatAutoReplyParamDf.Size(m)
}
func (m *TkCatAutoReplyParamDf) XXX_DiscardUnknown() {
	xxx_messageInfo_TkCatAutoReplyParamDf.DiscardUnknown(m)
}

var xxx_messageInfo_TkCatAutoReplyParamDf proto.InternalMessageInfo

func (m *TkCatAutoReplyParamDf) GetTotalPay() uint64 {
	if m != nil {
		return m.TotalPay
	}
	return 0
}

func init() {
	proto.RegisterType((*TkCreateReq)(nil), "pb.TkCreateReq")
	proto.RegisterType((*TkCreateResp)(nil), "pb.TkCreateResp")
	proto.RegisterType((*TkUpResp)(nil), "pb.TkUpResp")
	proto.RegisterType((*TkMineReq)(nil), "pb.TkMineReq")
	proto.RegisterType((*TkMineResp)(nil), "pb.TkMineResp")
	proto.RegisterType((*TkMineResp_TkList)(nil), "pb.TkMineResp.TkList")
	proto.RegisterType((*TkDetailReq)(nil), "pb.TkDetailReq")
	proto.RegisterType((*TkDetailResp)(nil), "pb.TkDetailResp")
	proto.RegisterType((*TkDetailResp_Replenish)(nil), "pb.TkDetailResp.Replenish")
	proto.RegisterType((*TkDetailResp_Reopen)(nil), "pb.TkDetailResp.Reopen")
	proto.RegisterType((*TkDetailResp_Commu)(nil), "pb.TkDetailResp.Commu")
	proto.RegisterType((*TicketDetailModel)(nil), "pb.TicketDetailModel")
	proto.RegisterType((*NoticeResp)(nil), "pb.NoticeResp")
	proto.RegisterType((*TkReplenishReq)(nil), "pb.TkReplenishReq")
	proto.RegisterType((*TkAppraiseReq)(nil), "pb.TkAppraiseReq")
	proto.RegisterType((*TkAppraiseFeedbackReq)(nil), "pb.TkAppraiseFeedbackReq")
	proto.RegisterType((*TkCommunicateReq)(nil), "pb.TkCommunicateReq")
	proto.RegisterType((*NoticeRequestV2)(nil), "pb.NoticeRequestV2")
	proto.RegisterType((*NoticeFpxRequestV3)(nil), "pb.NoticeFpxRequestV3")
	proto.RegisterType((*InnerNoticeRequest)(nil), "pb.InnerNoticeRequest")
	proto.RegisterType((*InnerNoticeResp)(nil), "pb.InnerNoticeResp")
	proto.RegisterType((*TkCatAutoReplyTplPrefer)(nil), "pb.TkCatAutoReplyTplPrefer")
	proto.RegisterType((*TkCatAutoReplyParamDf)(nil), "pb.TkCatAutoReplyParamDf")
}

func init() {
	proto.RegisterFile("egress_ticket.proto", fileDescriptor_c529971104d8ed1e)
}

var fileDescriptor_c529971104d8ed1e = []byte{
	// 2402 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xe4, 0x59, 0x4f, 0x8f, 0x1b, 0x49,
	0x15, 0x97, 0xff, 0xdb, 0xe5, 0x69, 0xcf, 0x4c, 0xcf, 0x9f, 0xf4, 0x4e, 0x36, 0xbb, 0xb3, 0x66,
	0x17, 0x66, 0x77, 0xc3, 0x2c, 0x99, 0x00, 0x42, 0xe2, 0x34, 0x4c, 0x34, 0xc8, 0xd2, 0x26, 0x8a,
	0x3a, 0x4e, 0x0e, 0x1c, 0x68, 0x95, 0xbb, 0xcb, 0x9e, 0xc2, 0xed, 0xae, 0x4e, 0x57, 0x79, 0x36,
	0xe6, 0x04, 0x1c, 0xb9, 0x22, 0x21, 0xbe, 0x00, 0x47, 0xce, 0x48, 0x7c, 0x04, 0xee, 0x5c, 0xf8,
	0x06, 0xf0, 0x05, 0xe0, 0x02, 0x42, 0xef, 0xbd, 0xea, 0x76, 0xdb, 0xe3, 0x4c, 0x36, 0x12, 0x48,
	0x48, 0xb9, 0xf5, 0xfb, 0xbd, 0xea, 0x76, 0xbd, 0xdf, 0x7b, 0xf5, 0xea, 0x57, 0x65, 0xb6, 0x27,
	0x26, 0x99, 0xd0, 0x3a, 0x30, 0x32, 0x9c, 0x0a, 0x73, 0x9a, 0x66, 0xca, 0x28, 0xb7, 0x9a, 0x8e,
	0x8e, 0x98, 0x48, 0xe6, 0x33, 0xb2, 0xfb, 0x7f, 0x65, 0xac, 0x3b, 0x9c, 0x5e, 0x64, 0x82, 0x1b,
	0xe1, 0x8b, 0x97, 0xee, 0x21, 0x6b, 0xaa, 0x4c, 0x4e, 0x64, 0xe2, 0x55, 0x8e, 0x2b, 0x27, 0x8e,
	0x6f, 0x2d, 0x77, 0x9f, 0x35, 0x74, 0x28, 0x12, 0xe1, 0x55, 0x11, 0x26, 0xc3, 0xbd, 0xc3, 0x5a,
	0x13, 0x3e, 0x13, 0x81, 0x8c, 0xbc, 0x1a, 0x0d, 0x07, 0x73, 0x10, 0xb9, 0xef, 0x33, 0x36, 0x4e,
	0x5f, 0x05, 0x3c, 0x4d, 0xc1, 0x57, 0x3f, 0xae, 0x9c, 0x74, 0xfc, 0xf6, 0x38, 0x7d, 0x75, 0x9e,
	0xa6, 0x83, 0xc8, 0x75, 0x59, 0x3d, 0xe6, 0xc9, 0xc4, 0x6b, 0x20, 0x8e, 0xcf, 0x80, 0xcd, 0xe7,
	0x32, 0xf2, 0x9a, 0x84, 0xc1, 0x33, 0x60, 0xe3, 0x54, 0x46, 0x5e, 0xeb, 0xb8, 0x72, 0x52, 0xf7,
	0xf1, 0xd9, 0xdd, 0x61, 0x35, 0x18, 0xd6, 0x46, 0x08, 0x1e, 0xdd, 0x7b, 0x8c, 0xf1, 0x30, 0x54,
	0xf3, 0xc4, 0xc0, 0x6f, 0x75, 0xf0, 0xfd, 0x8e, 0x45, 0x06, 0x11, 0xcc, 0x31, 0x53, 0x31, 0xce,
	0x91, 0xa1, 0xaf, 0x09, 0xe6, 0x00, 0xbf, 0xa4, 0x65, 0xe4, 0x75, 0x11, 0x84, 0x47, 0xd7, 0x63,
	0xad, 0xf0, 0x8a, 0x27, 0x89, 0x88, 0xbd, 0x2d, 0x44, 0x73, 0xd3, 0x3d, 0x62, 0xed, 0x44, 0x86,
	0xd3, 0x84, 0xcf, 0x84, 0xe7, 0x50, 0x34, 0xb9, 0xed, 0x1e, 0xb0, 0x66, 0xc8, 0xf1, 0xb7, 0x7b,
	0xc4, 0x4d, 0xc8, 0xe1, 0x77, 0x0f, 0x59, 0x73, 0x2c, 0x45, 0x1c, 0x69, 0x6f, 0x9b, 0x7e, 0x96,
	0x2c, 0xf7, 0x43, 0xd6, 0x8d, 0xc4, 0xb5, 0x0c, 0x45, 0x60, 0x16, 0xa9, 0xf0, 0x76, 0xd0, 0xc9,
	0x08, 0x1a, 0x2e, 0x52, 0xe1, 0xf6, 0x58, 0x55, 0x69, 0x6f, 0x17, 0xf1, 0xaa, 0xd2, 0x10, 0x9f,
	0xd2, 0xc1, 0xb5, 0xc8, 0xb4, 0x54, 0x89, 0xe7, 0x52, 0x7c, 0x4a, 0xbf, 0x20, 0x00, 0xbe, 0x07,
	0x34, 0xe7, 0xfe, 0x3d, 0xfa, 0x1e, 0x4f, 0xd3, 0x7c, 0xc0, 0x01, 0x6b, 0x66, 0x6a, 0x16, 0x4c,
	0x46, 0xde, 0x3e, 0xfa, 0x1a, 0x99, 0x9a, 0xfd, 0x78, 0x04, 0x9f, 0xcd, 0xc4, 0x8c, 0xcb, 0x24,
	0xc8, 0xd4, 0xcc, 0x3b, 0xa0, 0xcf, 0x12, 0xe2, 0xab, 0x19, 0xbe, 0xc5, 0x67, 0xc1, 0x6c, 0xe4,
	0x1d, 0xda, 0xb7, 0xf8, 0xec, 0xf1, 0xc8, 0xfd, 0x88, 0x6d, 0x25, 0xc2, 0x7c, 0xa5, 0xb2, 0x69,
	0x20, 0x93, 0xb1, 0xf2, 0xee, 0xa0, 0xb3, 0x6b, 0xb1, 0x41, 0x32, 0x56, 0x30, 0x21, 0x1d, 0x4d,
	0x8b, 0x09, 0x79, 0x34, 0x21, 0x1d, 0x4d, 0xf3, 0x09, 0x01, 0xcd, 0x90, 0x9c, 0x6c, 0xe1, 0xbd,
	0x67, 0x69, 0x26, 0xd3, 0xbd, 0xcb, 0x3a, 0x46, 0x19, 0x1e, 0x07, 0x29, 0x5f, 0x78, 0x47, 0xc7,
	0x95, 0x93, 0x8a, 0xdf, 0x46, 0xe0, 0x29, 0x5f, 0xb8, 0xef, 0xb1, 0x76, 0xcc, 0x47, 0x22, 0x06,
	0xa6, 0xef, 0x1e, 0xd7, 0x4e, 0x1c, 0xbf, 0x85, 0x36, 0x71, 0x2d, 0x5e, 0x19, 0x91, 0x44, 0xde,
	0xfb, 0xc4, 0x35, 0x59, 0x10, 0x63, 0xca, 0x17, 0x01, 0x9f, 0xc1, 0xf7, 0xbd, 0x7b, 0xf8, 0xc1,
	0x4e, 0xca, 0x17, 0xe7, 0x08, 0x40, 0x30, 0xf6, 0x97, 0x83, 0x50, 0x45, 0xc2, 0xfb, 0x80, 0x82,
	0xb1, 0xd8, 0x85, 0x8a, 0x04, 0x04, 0x93, 0x89, 0x98, 0x1b, 0x11, 0x05, 0x21, 0x37, 0xde, 0x87,
	0x98, 0x61, 0x66, 0xa1, 0x0b, 0x6e, 0xdc, 0x8f, 0x59, 0x6f, 0x0c, 0xf4, 0xd2, 0x2a, 0x83, 0xb9,
	0x1d, 0x63, 0x69, 0x6e, 0x01, 0x3a, 0x44, 0x70, 0x10, 0xb9, 0x1f, 0x30, 0xa6, 0xe7, 0xa3, 0xbc,
	0xb8, 0x3e, 0xb2, 0x94, 0x14, 0x08, 0x2e, 0xbb, 0x54, 0x24, 0x32, 0xf2, 0xfa, 0x14, 0x00, 0x59,
	0xee, 0xb7, 0xd8, 0x76, 0x9a, 0xa9, 0x10, 0x96, 0xb1, 0x16, 0x1a, 0xf9, 0xfc, 0x06, 0x0e, 0xe8,
	0x59, 0xf8, 0x19, 0xa1, 0x18, 0xa9, 0x1d, 0x28, 0x23, 0xef, 0xe3, 0xe3, 0xca, 0x49, 0xcd, 0xef,
	0x58, 0x64, 0x10, 0xc1, 0xf2, 0x7d, 0x09, 0x13, 0xf2, 0x4e, 0x28, 0x99, 0x68, 0x00, 0xdd, 0x3f,
	0x57, 0x89, 0x08, 0xd0, 0xf3, 0x29, 0x95, 0x35, 0x00, 0x97, 0xe0, 0xbc, 0xcf, 0x5c, 0x93, 0xa9,
	0xf9, 0x28, 0x16, 0x41, 0x69, 0x79, 0x7d, 0x8e, 0xa3, 0x76, 0xac, 0xe7, 0xbc, 0x58, 0x65, 0x1f,
	0xb2, 0x6e, 0x3e, 0x1a, 0x96, 0xe7, 0x7d, 0xe4, 0x80, 0x59, 0xe8, 0xb9, 0x84, 0x8e, 0xd0, 0x49,
	0x79, 0x38, 0xe5, 0x13, 0x31, 0x88, 0xbc, 0x6f, 0x53, 0xb5, 0x15, 0x00, 0x4c, 0x1f, 0x67, 0x62,
	0xd4, 0x54, 0x24, 0xde, 0x29, 0xb9, 0x01, 0x19, 0x02, 0x00, 0x34, 0x84, 0x2a, 0x81, 0x8a, 0xe2,
	0x46, 0xaa, 0x04, 0x26, 0xf2, 0x05, 0xd1, 0x50, 0x86, 0xed, 0x34, 0x28, 0x11, 0xb8, 0xb8, 0xbe,
	0x73, 0x5c, 0x39, 0x69, 0xfb, 0x8c, 0x20, 0x5c, 0x5c, 0xf7, 0x18, 0x93, 0x3a, 0x90, 0xc9, 0x35,
	0x8f, 0x65, 0xe4, 0x3d, 0xc0, 0x74, 0x76, 0xa4, 0x1e, 0x10, 0x00, 0xd9, 0xc4, 0x79, 0x5c, 0xcb,
	0x34, 0x88, 0xc5, 0xb5, 0x88, 0xbd, 0x33, 0xca, 0x26, 0xa0, 0x2f, 0x64, 0xfa, 0x25, 0x60, 0xee,
	0x37, 0x21, 0x2b, 0xf2, 0x3a, 0xc8, 0xe4, 0xe4, 0xca, 0xe8, 0x60, 0xae, 0x85, 0xf7, 0x10, 0x7f,
	0xc9, 0x01, 0xd8, 0x47, 0xf4, 0xb9, 0x16, 0xfd, 0x11, 0xdb, 0x5a, 0xf6, 0x56, 0x9d, 0x62, 0x79,
	0x17, 0x65, 0x52, 0xc1, 0x0f, 0xb7, 0x4d, 0x5e, 0x22, 0xdf, 0x63, 0xdb, 0xd6, 0xa9, 0x17, 0x9a,
	0xa6, 0x0f, 0xbd, 0xb6, 0x77, 0xe6, 0x9c, 0xa6, 0xa3, 0x53, 0xaa, 0xa4, 0x67, 0x0b, 0xed, 0x3b,
	0x26, 0x7f, 0x84, 0x80, 0xfa, 0x3f, 0x65, 0xed, 0xe1, 0xf4, 0x79, 0xfa, 0x3f, 0xfb, 0xfe, 0xaf,
	0xab, 0xac, 0x33, 0x9c, 0x3e, 0x96, 0x09, 0x6e, 0x0f, 0xc5, 0x36, 0x50, 0x79, 0x17, 0xb7, 0x81,
	0xfe, 0x1f, 0xab, 0x8c, 0xe5, 0x64, 0xe8, 0xd4, 0xfd, 0x94, 0xd5, 0x23, 0x6e, 0xb8, 0x57, 0x39,
	0xae, 0x9d, 0x74, 0xcf, 0x0e, 0x90, 0xc7, 0xc2, 0x7b, 0x3a, 0x9c, 0x7e, 0x29, 0xb5, 0xf1, 0x71,
	0xc8, 0xd1, 0xbf, 0x2a, 0xac, 0x49, 0xc0, 0xed, 0x59, 0xba, 0xc7, 0x58, 0x88, 0x05, 0x13, 0x05,
	0xdc, 0x60, 0x82, 0x3a, 0x7e, 0xc7, 0x22, 0xe7, 0x06, 0xf6, 0xa1, 0x90, 0x1b, 0x31, 0x51, 0xd9,
	0x02, 0xa9, 0xee, 0xf8, 0x85, 0xed, 0x7e, 0xc6, 0xda, 0x69, 0xa6, 0x70, 0xcf, 0x47, 0xaa, 0x7b,
	0x67, 0x3d, 0x9a, 0xd1, 0x53, 0x8b, 0xfa, 0x85, 0xdf, 0xfd, 0x98, 0x35, 0xb5, 0xe1, 0x66, 0xae,
	0x91, 0xfc, 0xde, 0xd9, 0x16, 0x8d, 0x7c, 0x86, 0x98, 0x6f, 0x7d, 0x40, 0x7c, 0x26, 0x38, 0x25,
	0xc3, 0xf1, 0xf1, 0x79, 0x53, 0x19, 0xb5, 0xbe, 0x46, 0x19, 0xfd, 0xa6, 0x0a, 0x3a, 0xe3, 0x91,
	0x30, 0x5c, 0xc6, 0x50, 0x48, 0xb7, 0x92, 0xf0, 0xce, 0xd4, 0xd3, 0x2f, 0xbb, 0xd0, 0x21, 0x72,
	0x56, 0xde, 0xb4, 0x82, 0xcb, 0xc9, 0xaf, 0xae, 0x25, 0x7f, 0x9f, 0x35, 0xc6, 0x32, 0x16, 0x91,
	0xad, 0x0a, 0x32, 0xe0, 0x0d, 0x93, 0xf1, 0x44, 0x8f, 0x45, 0x86, 0x6c, 0x39, 0x7e, 0x61, 0x83,
	0x4f, 0xab, 0x78, 0x0e, 0x8d, 0xd3, 0x32, 0x56, 0xd8, 0xc0, 0x50, 0xa4, 0x12, 0x81, 0xac, 0xb5,
	0x7d, 0x7c, 0x86, 0x2d, 0x2a, 0x8c, 0x95, 0x16, 0xc4, 0x9b, 0xe3, 0x5b, 0x0b, 0xbe, 0x23, 0xae,
	0x65, 0x24, 0x92, 0x50, 0x20, 0x7d, 0x6d, 0xbf, 0xb0, 0xc1, 0xc7, 0xd3, 0x34, 0xe3, 0x52, 0x0b,
	0x64, 0xb0, 0xed, 0x17, 0xb6, 0xfb, 0x03, 0xd6, 0xc9, 0x44, 0x1a, 0x8b, 0x44, 0xea, 0x2b, 0x8f,
	0xe1, 0x0a, 0x3a, 0xa2, 0x2a, 0x5c, 0xf2, 0x71, 0xea, 0xe7, 0x23, 0xfc, 0xe5, 0x60, 0xf7, 0x3e,
	0x6b, 0x84, 0x6a, 0x36, 0x9b, 0x7b, 0x5d, 0x7c, 0xeb, 0xf0, 0xc6, 0x5b, 0x17, 0xe0, 0xf5, 0x69,
	0x10, 0xe4, 0x23, 0xd4, 0x12, 0x99, 0x77, 0x7c, 0x78, 0x04, 0xae, 0x50, 0x38, 0x78, 0x0e, 0xaa,
	0x08, 0x32, 0x96, 0x3b, 0xfd, 0x52, 0x97, 0x15, 0x3b, 0x3d, 0x6e, 0x1d, 0x5f, 0xb0, 0x66, 0x26,
	0x60, 0x5f, 0xf6, 0x5c, 0xfc, 0xdd, 0x3b, 0x1b, 0x66, 0x0b, 0x6e, 0xdf, 0x0e, 0x83, 0x5d, 0x8b,
	0x9e, 0x82, 0x48, 0x6a, 0x3e, 0x82, 0xec, 0xec, 0x21, 0x09, 0x3d, 0x82, 0x1f, 0x59, 0xb4, 0xa4,
	0x20, 0xf7, 0xcb, 0x0a, 0x72, 0xc3, 0x52, 0x3b, 0x78, 0xf3, 0x52, 0x73, 0x3f, 0x61, 0x3d, 0x75,
	0x2d, 0x32, 0x23, 0x67, 0x22, 0x00, 0xd2, 0x16, 0x56, 0xc1, 0x39, 0x39, 0x0a, 0xa4, 0x2e, 0x20,
	0x37, 0x39, 0x60, 0x55, 0x5c, 0x61, 0xc3, 0x36, 0x28, 0x75, 0x10, 0x5e, 0x71, 0x63, 0x75, 0x0d,
	0xaa, 0x38, 0xc7, 0xdf, 0x92, 0xfa, 0xe2, 0x8a, 0x1b, 0xfa, 0xe9, 0xa3, 0xbf, 0x57, 0x58, 0xa7,
	0x48, 0x10, 0x88, 0xa9, 0x22, 0x45, 0xcb, 0xea, 0xed, 0x16, 0xd8, 0x20, 0x5a, 0xad, 0xee, 0xea,
	0x5a, 0x75, 0x1f, 0x02, 0xbd, 0x33, 0x9e, 0x4d, 0x6d, 0x09, 0x5b, 0x0b, 0xbe, 0x3b, 0x96, 0x71,
	0x1c, 0x84, 0x2a, 0x31, 0x22, 0x31, 0x76, 0xd5, 0x77, 0x01, 0xbb, 0x20, 0x28, 0x2f, 0x7e, 0x6d,
	0xeb, 0x98, 0x0c, 0xd4, 0xd1, 0xa9, 0xed, 0x5d, 0x55, 0x95, 0xae, 0xb5, 0xd6, 0xd6, 0x7a, 0x6b,
	0x2d, 0xb9, 0x8d, 0xb6, 0x8d, 0x20, 0x77, 0x0f, 0xf5, 0xd1, 0xbf, 0x2b, 0xac, 0x49, 0xf9, 0x85,
	0x30, 0x6c, 0x5e, 0x97, 0x8b, 0x94, 0x80, 0x41, 0x74, 0x63, 0xba, 0xd5, 0x5b, 0xa6, 0x5b, 0x2b,
	0x4f, 0x77, 0x65, 0x3d, 0xd4, 0xdf, 0x66, 0x3d, 0xac, 0x06, 0xd6, 0x58, 0x0f, 0xec, 0x3d, 0xd6,
	0xc6, 0x32, 0x08, 0x6c, 0x1b, 0xac, 0xfb, 0x2d, 0xb4, 0x07, 0x91, 0xfb, 0x19, 0xdb, 0xcd, 0x84,
	0x4e, 0x55, 0xa2, 0x6d, 0xa9, 0x04, 0x45, 0x5b, 0xdc, 0xce, 0x1d, 0x3e, 0x8d, 0x3d, 0xfa, 0x4b,
	0x85, 0x35, 0x70, 0x61, 0x91, 0x7e, 0xa7, 0xe8, 0x2a, 0xb9, 0x7e, 0xa7, 0xc8, 0x8e, 0x59, 0x1d,
	0xba, 0xa0, 0x15, 0x16, 0xb8, 0xa9, 0x3c, 0xd7, 0x22, 0xf3, 0x55, 0x2c, 0x7c, 0xf4, 0xb8, 0x27,
	0x6c, 0x27, 0x9c, 0x6b, 0xa3, 0x66, 0x01, 0x9c, 0x9f, 0x02, 0x3c, 0x50, 0xd5, 0xac, 0x94, 0x43,
	0xfc, 0x89, 0x0c, 0xa7, 0x4f, 0xe0, 0x58, 0xb5, 0x1a, 0x55, 0x7d, 0x3d, 0x2a, 0x8f, 0xb5, 0x52,
	0x19, 0x9a, 0x79, 0x26, 0x6c, 0xc4, 0xb9, 0x09, 0xf4, 0x42, 0xff, 0x51, 0xb6, 0xe7, 0x93, 0x01,
	0xfd, 0x5a, 0xa5, 0xcb, 0xfd, 0x0a, 0x4e, 0xb6, 0x29, 0xee, 0x4c, 0xbf, 0xab, 0xb3, 0x5d, 0x2a,
	0x68, 0xe2, 0xf9, 0xb1, 0x8a, 0x44, 0x7c, 0x7b, 0x23, 0xce, 0x37, 0x95, 0x6a, 0x69, 0x53, 0x59,
	0x1e, 0x9c, 0x6b, 0x2b, 0x07, 0xe7, 0xe5, 0xda, 0x6e, 0x94, 0xd7, 0xf6, 0x07, 0x8c, 0x89, 0x6b,
	0x1e, 0xcf, 0x51, 0xb8, 0xda, 0x22, 0x2d, 0x21, 0x79, 0xd7, 0x6a, 0xad, 0x74, 0xad, 0x34, 0x53,
	0x6a, 0x8c, 0xa5, 0xe9, 0xf8, 0x64, 0xa0, 0x20, 0x33, 0x7c, 0x42, 0xed, 0x15, 0x04, 0x19, 0x18,
	0x30, 0x19, 0xbb, 0xbd, 0x33, 0x9a, 0x8c, 0xdd, 0xd0, 0x97, 0x3d, 0xbc, 0xbb, 0xd2, 0xc3, 0xef,
	0xb2, 0x0e, 0x3d, 0x01, 0xd5, 0x5b, 0x14, 0x2d, 0x01, 0xe7, 0xb6, 0x5c, 0x45, 0x1c, 0xd9, 0x83,
	0x2f, 0x19, 0x6b, 0xdb, 0xe3, 0xce, 0xfa, 0xf6, 0x08, 0xe9, 0xc9, 0xd4, 0xcf, 0x44, 0x68, 0xec,
	0x49, 0x36, 0x37, 0xdd, 0x3d, 0xd6, 0x98, 0xcc, 0xe0, 0x1d, 0x3a, 0xc9, 0xd6, 0x27, 0x33, 0x3a,
	0x9f, 0x90, 0xae, 0xdc, 0x2f, 0xeb, 0x4a, 0x3c, 0xa2, 0xe2, 0x42, 0x4b, 0xe6, 0x74, 0x44, 0x75,
	0x7c, 0xbb, 0xf4, 0x9e, 0xcc, 0x67, 0x6e, 0x9f, 0x39, 0x7c, 0x6e, 0xd4, 0xb2, 0x72, 0x0f, 0x71,
	0x44, 0x17, 0x40, 0x5b, 0xb5, 0xd0, 0xe5, 0xd2, 0x4c, 0xaa, 0x4c, 0x9a, 0x05, 0x76, 0x39, 0xc7,
	0x2f, 0xec, 0xf5, 0xc3, 0x02, 0xb5, 0xb8, 0xd2, 0x61, 0xa1, 0xff, 0xe7, 0x0a, 0x63, 0x4f, 0x94,
	0x91, 0x61, 0x21, 0xdf, 0x13, 0xb4, 0x4a, 0x35, 0x41, 0x00, 0xd5, 0x04, 0x1e, 0xa3, 0xe8, 0x7e,
	0xa4, 0x9e, 0x9f, 0xaf, 0xd4, 0x08, 0x82, 0xce, 0x95, 0x4c, 0xdd, 0x6f, 0x13, 0x50, 0x0e, 0xb9,
	0x5e, 0x0e, 0x79, 0x43, 0xcf, 0x6f, 0x7c, 0x8d, 0x9e, 0x0f, 0xc7, 0x72, 0x9a, 0x1a, 0x26, 0xc0,
	0xae, 0xf3, 0x2e, 0x61, 0x17, 0x00, 0xf5, 0x7f, 0x55, 0x63, 0xbd, 0xe1, 0x74, 0xd9, 0x40, 0xde,
	0x24, 0xc2, 0x4a, 0xab, 0xbc, 0xba, 0xba, 0xca, 0x37, 0xf7, 0xaf, 0x92, 0x68, 0xab, 0xdf, 0x22,
	0xda, 0x1a, 0xaf, 0x11, 0x6d, 0xcd, 0x0d, 0xa2, 0xad, 0xb5, 0x41, 0xb4, 0xb5, 0x6f, 0x8a, 0xb6,
	0xce, 0xeb, 0x44, 0x1b, 0xbb, 0x45, 0xb4, 0x75, 0x37, 0x89, 0xb6, 0xad, 0x8d, 0xa2, 0xcd, 0x79,
	0xfd, 0x5d, 0x50, 0x6f, 0xed, 0x2e, 0x68, 0xf5, 0x9c, 0xdf, 0x5f, 0x3f, 0xe7, 0xf7, 0xff, 0x51,
	0x63, 0xce, 0x70, 0x7a, 0x6e, 0x35, 0xd0, 0xdb, 0x08, 0xe1, 0xff, 0x02, 0xa7, 0xff, 0x3f, 0xfc,
	0x15, 0xa2, 0x90, 0x6e, 0xcc, 0x96, 0xa2, 0xf0, 0x13, 0xd6, 0xd3, 0x22, 0xc3, 0xdb, 0xb1, 0x8c,
	0x1b, 0x99, 0x4c, 0xf0, 0xf2, 0xcc, 0xf1, 0x1d, 0x8b, 0xfa, 0x08, 0xba, 0xa7, 0x6c, 0x2f, 0x1f,
	0x46, 0x32, 0x87, 0xc6, 0x92, 0x66, 0xdb, 0xb5, 0xae, 0x21, 0x48, 0x1d, 0x1a, 0xff, 0x7d, 0x76,
	0x27, 0x1f, 0x9f, 0x6b, 0xdc, 0xfc, 0x9d, 0x5d, 0x7c, 0xe7, 0xc0, 0xba, 0x9f, 0x59, 0xaf, 0x7d,
	0xef, 0x01, 0xdb, 0xcf, 0x04, 0xc8, 0x48, 0x91, 0x44, 0x74, 0xf3, 0x40, 0x97, 0x02, 0x2e, 0xbe,
	0xb4, 0xb7, 0xea, 0xa3, 0xbb, 0x81, 0xa5, 0x8c, 0xd9, 0x2b, 0xcb, 0x98, 0xfe, 0xef, 0xab, 0xec,
	0x60, 0x99, 0xf9, 0x4b, 0x21, 0xa2, 0x11, 0x0f, 0xa7, 0xef, 0x6c, 0x05, 0x64, 0x42, 0xab, 0xf8,
	0x5a, 0xe4, 0x77, 0xa6, 0x85, 0xdd, 0xff, 0x67, 0x95, 0xed, 0x0c, 0xa7, 0x28, 0x34, 0x12, 0x19,
	0xda, 0x5b, 0xe9, 0x77, 0x8f, 0xa2, 0x52, 0xf7, 0xed, 0xad, 0x76, 0xdf, 0x92, 0xf0, 0xd9, 0x7e,
	0x8d, 0xf0, 0xd9, 0x29, 0x0b, 0x9f, 0x62, 0x9f, 0xd9, 0x2d, 0xef, 0x33, 0x79, 0x8b, 0x75, 0x97,
	0x2d, 0xb6, 0xff, 0x87, 0x2a, 0xdb, 0xce, 0xb7, 0xbb, 0x97, 0x73, 0xa1, 0xcd, 0x8b, 0x33, 0x78,
	0x9b, 0x03, 0x5d, 0x56, 0xe9, 0x91, 0x01, 0x45, 0x4e, 0x1c, 0xdb, 0xed, 0x2e, 0x67, 0xfc, 0x2e,
	0xeb, 0xd8, 0xbb, 0x6d, 0x99, 0x9f, 0x44, 0xdb, 0x04, 0x0c, 0x96, 0x1d, 0xbc, 0x7e, 0x93, 0xdc,
	0xc6, 0x92, 0x5c, 0x4b, 0x52, 0x73, 0x23, 0x49, 0xad, 0x55, 0x92, 0xf2, 0x14, 0xb6, 0x4b, 0x29,
	0x5c, 0xbb, 0x7d, 0xee, 0xdc, 0xb8, 0x7d, 0xa6, 0xeb, 0x75, 0x56, 0x5c, 0xaf, 0xf7, 0x58, 0xd5,
	0x68, 0xcc, 0x54, 0xdd, 0xaf, 0x1a, 0xbc, 0xf4, 0xe0, 0x73, 0x73, 0x65, 0xd3, 0x84, 0xcf, 0x4b,
	0x0e, 0x9d, 0x12, 0x87, 0xfd, 0xbf, 0x55, 0x98, 0x4b, 0x7c, 0x5d, 0xa6, 0xaf, 0x72, 0xca, 0x1e,
	0xbe, 0x86, 0xb2, 0xd5, 0x62, 0xac, 0xae, 0x15, 0xe3, 0x0a, 0x71, 0xf5, 0x35, 0xe2, 0x56, 0xeb,
	0xad, 0xb1, 0x5e, 0x6f, 0x37, 0x19, 0x2b, 0x55, 0x60, 0x6b, 0xa5, 0x02, 0x29, 0xd6, 0xf6, 0x8d,
	0x58, 0x3b, 0x9b, 0x62, 0x65, 0xe5, 0x58, 0xff, 0x54, 0x61, 0xee, 0x20, 0x49, 0x44, 0xb6, 0x52,
	0x20, 0x20, 0xc1, 0xb4, 0x9a, 0x67, 0xa1, 0xc8, 0x03, 0xa3, 0x98, 0xbb, 0x04, 0x9e, 0xbf, 0x6d,
	0xe4, 0xb5, 0x5b, 0x23, 0xaf, 0x6f, 0x88, 0x7c, 0x5e, 0x30, 0x82, 0xd5, 0x53, 0x4c, 0xbe, 0x59,
	0x9e, 0xfc, 0x2f, 0x2a, 0x6c, 0x7b, 0x65, 0xf2, 0x1a, 0x4f, 0x83, 0x57, 0x5c, 0x07, 0xf3, 0x04,
	0x6f, 0xb8, 0x2a, 0x78, 0x2e, 0xef, 0x5c, 0x71, 0xfd, 0x1c, 0x81, 0x55, 0xad, 0x57, 0x5d, 0xd3,
	0x7a, 0x6f, 0xaf, 0xeb, 0xfa, 0xbf, 0xad, 0xb0, 0x3b, 0xc3, 0xe9, 0x05, 0x37, 0xe7, 0xb9, 0x38,
	0x1d, 0xa6, 0xf1, 0xd3, 0x4c, 0x8c, 0x45, 0xe6, 0x7e, 0xce, 0x5c, 0xfd, 0x95, 0x1c, 0x1b, 0x2b,
	0x64, 0x4d, 0x1a, 0xe7, 0x4c, 0x3a, 0xfe, 0x36, 0x7a, 0xf2, 0x17, 0x06, 0x91, 0xfb, 0x80, 0x1d,
	0xd0, 0xb0, 0x71, 0xcc, 0x27, 0xc1, 0x58, 0x65, 0xc1, 0x58, 0xc6, 0x46, 0x64, 0x38, 0xc9, 0xb6,
	0xef, 0xa2, 0xf3, 0x32, 0xe6, 0x93, 0x4b, 0x95, 0x5d, 0xa2, 0x67, 0xf5, 0x5f, 0x95, 0xda, 0xea,
	0xbf, 0x2a, 0xfd, 0xef, 0xc2, 0xb6, 0x54, 0x9e, 0xd7, 0x53, 0x9e, 0xf1, 0xd9, 0xa3, 0xf1, 0xea,
	0x5b, 0x79, 0xcf, 0xb5, 0x6f, 0xfd, 0xa8, 0xf9, 0x93, 0xfa, 0xe9, 0x0f, 0xd3, 0xd1, 0xa8, 0x89,
	0xff, 0x22, 0x3e, 0xfc, 0x4f, 0x00, 0x00, 0x00, 0xff, 0xff, 0x00, 0xf5, 0x04, 0x63, 0x6c, 0x1c,
	0x00, 0x00,
}
