// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.14.0
// source: category_inner.proto

package pb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	CategoryInnerAPI_GetCatTitles_FullMethodName = "/pb.CategoryInnerAPI/GetCatTitles"
)

// CategoryInnerAPIClient is the client API for CategoryInnerAPI service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type CategoryInnerAPIClient interface {
	// 分类获取title
	GetCatTitles(ctx context.Context, in *GetCatTitlesReq, opts ...grpc.CallOption) (*Empty, error)
}

type categoryInnerAPIClient struct {
	cc grpc.ClientConnInterface
}

func NewCategoryInnerAPIClient(cc grpc.ClientConnInterface) CategoryInnerAPIClient {
	return &categoryInnerAPIClient{cc}
}

func (c *categoryInnerAPIClient) GetCatTitles(ctx context.Context, in *GetCatTitlesReq, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, CategoryInnerAPI_GetCatTitles_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// CategoryInnerAPIServer is the server API for CategoryInnerAPI service.
// All implementations must embed UnimplementedCategoryInnerAPIServer
// for forward compatibility
type CategoryInnerAPIServer interface {
	// 分类获取title
	GetCatTitles(context.Context, *GetCatTitlesReq) (*Empty, error)
	mustEmbedUnimplementedCategoryInnerAPIServer()
}

// UnimplementedCategoryInnerAPIServer must be embedded to have forward compatible implementations.
type UnimplementedCategoryInnerAPIServer struct {
}

func (UnimplementedCategoryInnerAPIServer) GetCatTitles(context.Context, *GetCatTitlesReq) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCatTitles not implemented")
}
func (UnimplementedCategoryInnerAPIServer) mustEmbedUnimplementedCategoryInnerAPIServer() {}

// UnsafeCategoryInnerAPIServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to CategoryInnerAPIServer will
// result in compilation errors.
type UnsafeCategoryInnerAPIServer interface {
	mustEmbedUnimplementedCategoryInnerAPIServer()
}

func RegisterCategoryInnerAPIServer(s grpc.ServiceRegistrar, srv CategoryInnerAPIServer) {
	s.RegisterService(&CategoryInnerAPI_ServiceDesc, srv)
}

func _CategoryInnerAPI_GetCatTitles_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCatTitlesReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CategoryInnerAPIServer).GetCatTitles(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CategoryInnerAPI_GetCatTitles_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CategoryInnerAPIServer).GetCatTitles(ctx, req.(*GetCatTitlesReq))
	}
	return interceptor(ctx, in, info, handler)
}

// CategoryInnerAPI_ServiceDesc is the grpc.ServiceDesc for CategoryInnerAPI service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var CategoryInnerAPI_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "pb.CategoryInnerAPI",
	HandlerType: (*CategoryInnerAPIServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetCatTitles",
			Handler:    _CategoryInnerAPI_GetCatTitles_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "category_inner.proto",
}
