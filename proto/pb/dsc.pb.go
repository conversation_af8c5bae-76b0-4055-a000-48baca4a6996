// Code generated by protoc-gen-go. DO NOT EDIT.
// source: dsc.proto

package pb

import (
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

// PortraitInfoReq 获取玩家画像信息
type PortraitInfoReq struct {
	// @gotags: validate:"required"
	DscUserId string `protobuf:"bytes,1,opt,name=dsc_user_id,json=dscUserId,proto3" json:"dsc_user_id" validate:"required"`
	// @gotags: validate:"required"
	GameProject          string   `protobuf:"bytes,2,opt,name=game_project,json=gameProject,proto3" json:"game_project" validate:"required"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *PortraitInfoReq) Reset()         { *m = PortraitInfoReq{} }
func (m *PortraitInfoReq) String() string { return proto.CompactTextString(m) }
func (*PortraitInfoReq) ProtoMessage()    {}
func (*PortraitInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_2f04d4c6ef946e57, []int{0}
}

func (m *PortraitInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PortraitInfoReq.Unmarshal(m, b)
}
func (m *PortraitInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PortraitInfoReq.Marshal(b, m, deterministic)
}
func (m *PortraitInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PortraitInfoReq.Merge(m, src)
}
func (m *PortraitInfoReq) XXX_Size() int {
	return xxx_messageInfo_PortraitInfoReq.Size(m)
}
func (m *PortraitInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_PortraitInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_PortraitInfoReq proto.InternalMessageInfo

func (m *PortraitInfoReq) GetDscUserId() string {
	if m != nil {
		return m.DscUserId
	}
	return ""
}

func (m *PortraitInfoReq) GetGameProject() string {
	if m != nil {
		return m.GameProject
	}
	return ""
}

type PortraitInfoResp struct {
	Id                   uint64                             `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	Label                []*PortraitInfoResp_LabelDetail    `protobuf:"bytes,2,rep,name=label,proto3" json:"label"`
	Gender               uint32                             `protobuf:"varint,3,opt,name=gender,proto3" json:"gender"`
	Birthday             string                             `protobuf:"bytes,4,opt,name=birthday,proto3" json:"birthday"`
	Career               string                             `protobuf:"bytes,5,opt,name=career,proto3" json:"career"`
	EducationLevel       uint32                             `protobuf:"varint,6,opt,name=education_level,json=educationLevel,proto3" json:"education_level"`
	MarriedState         uint32                             `protobuf:"varint,7,opt,name=married_state,json=marriedState,proto3" json:"married_state"`
	FertilityState       uint32                             `protobuf:"varint,8,opt,name=fertility_state,json=fertilityState,proto3" json:"fertility_state"`
	Remark               string                             `protobuf:"bytes,9,opt,name=remark,proto3" json:"remark"`
	DscUserId            string                             `protobuf:"bytes,10,opt,name=dsc_user_id,json=dscUserId,proto3" json:"dsc_user_id"`
	GameProject          string                             `protobuf:"bytes,11,opt,name=game_project,json=gameProject,proto3" json:"game_project"`
	NewLabel             []*PortraitInfoResp_NewLabelDetail `protobuf:"bytes,12,rep,name=new_label,json=newLabel,proto3" json:"new_label"`
	TicketInfo           []*PortraitInfoResp_TicketInfo     `protobuf:"bytes,13,rep,name=ticket_info,json=ticketInfo,proto3" json:"ticket_info"`
	XXX_NoUnkeyedLiteral struct{}                           `json:"-" gorm:"-"`
	XXX_unrecognized     []byte                             `json:"-" gorm:"-"`
	XXX_sizecache        int32                              `json:"-" gorm:"-"`
}

func (m *PortraitInfoResp) Reset()         { *m = PortraitInfoResp{} }
func (m *PortraitInfoResp) String() string { return proto.CompactTextString(m) }
func (*PortraitInfoResp) ProtoMessage()    {}
func (*PortraitInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_2f04d4c6ef946e57, []int{1}
}

func (m *PortraitInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PortraitInfoResp.Unmarshal(m, b)
}
func (m *PortraitInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PortraitInfoResp.Marshal(b, m, deterministic)
}
func (m *PortraitInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PortraitInfoResp.Merge(m, src)
}
func (m *PortraitInfoResp) XXX_Size() int {
	return xxx_messageInfo_PortraitInfoResp.Size(m)
}
func (m *PortraitInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_PortraitInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_PortraitInfoResp proto.InternalMessageInfo

func (m *PortraitInfoResp) GetId() uint64 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *PortraitInfoResp) GetLabel() []*PortraitInfoResp_LabelDetail {
	if m != nil {
		return m.Label
	}
	return nil
}

func (m *PortraitInfoResp) GetGender() uint32 {
	if m != nil {
		return m.Gender
	}
	return 0
}

func (m *PortraitInfoResp) GetBirthday() string {
	if m != nil {
		return m.Birthday
	}
	return ""
}

func (m *PortraitInfoResp) GetCareer() string {
	if m != nil {
		return m.Career
	}
	return ""
}

func (m *PortraitInfoResp) GetEducationLevel() uint32 {
	if m != nil {
		return m.EducationLevel
	}
	return 0
}

func (m *PortraitInfoResp) GetMarriedState() uint32 {
	if m != nil {
		return m.MarriedState
	}
	return 0
}

func (m *PortraitInfoResp) GetFertilityState() uint32 {
	if m != nil {
		return m.FertilityState
	}
	return 0
}

func (m *PortraitInfoResp) GetRemark() string {
	if m != nil {
		return m.Remark
	}
	return ""
}

func (m *PortraitInfoResp) GetDscUserId() string {
	if m != nil {
		return m.DscUserId
	}
	return ""
}

func (m *PortraitInfoResp) GetGameProject() string {
	if m != nil {
		return m.GameProject
	}
	return ""
}

func (m *PortraitInfoResp) GetNewLabel() []*PortraitInfoResp_NewLabelDetail {
	if m != nil {
		return m.NewLabel
	}
	return nil
}

func (m *PortraitInfoResp) GetTicketInfo() []*PortraitInfoResp_TicketInfo {
	if m != nil {
		return m.TicketInfo
	}
	return nil
}

type PortraitInfoResp_LabelDetail struct {
	TagId                uint64   `protobuf:"varint,1,opt,name=tag_id,json=tagId,proto3" json:"tag_id"`
	TagName              string   `protobuf:"bytes,2,opt,name=tag_name,json=tagName,proto3" json:"tag_name"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *PortraitInfoResp_LabelDetail) Reset()         { *m = PortraitInfoResp_LabelDetail{} }
func (m *PortraitInfoResp_LabelDetail) String() string { return proto.CompactTextString(m) }
func (*PortraitInfoResp_LabelDetail) ProtoMessage()    {}
func (*PortraitInfoResp_LabelDetail) Descriptor() ([]byte, []int) {
	return fileDescriptor_2f04d4c6ef946e57, []int{1, 0}
}

func (m *PortraitInfoResp_LabelDetail) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PortraitInfoResp_LabelDetail.Unmarshal(m, b)
}
func (m *PortraitInfoResp_LabelDetail) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PortraitInfoResp_LabelDetail.Marshal(b, m, deterministic)
}
func (m *PortraitInfoResp_LabelDetail) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PortraitInfoResp_LabelDetail.Merge(m, src)
}
func (m *PortraitInfoResp_LabelDetail) XXX_Size() int {
	return xxx_messageInfo_PortraitInfoResp_LabelDetail.Size(m)
}
func (m *PortraitInfoResp_LabelDetail) XXX_DiscardUnknown() {
	xxx_messageInfo_PortraitInfoResp_LabelDetail.DiscardUnknown(m)
}

var xxx_messageInfo_PortraitInfoResp_LabelDetail proto.InternalMessageInfo

func (m *PortraitInfoResp_LabelDetail) GetTagId() uint64 {
	if m != nil {
		return m.TagId
	}
	return 0
}

func (m *PortraitInfoResp_LabelDetail) GetTagName() string {
	if m != nil {
		return m.TagName
	}
	return ""
}

type PortraitInfoResp_NewLabelDetail struct {
	TagId                uint64   `protobuf:"varint,1,opt,name=tag_id,json=tagId,proto3" json:"tag_id"`
	TagDesc              string   `protobuf:"bytes,2,opt,name=tag_desc,json=tagDesc,proto3" json:"tag_desc"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *PortraitInfoResp_NewLabelDetail) Reset()         { *m = PortraitInfoResp_NewLabelDetail{} }
func (m *PortraitInfoResp_NewLabelDetail) String() string { return proto.CompactTextString(m) }
func (*PortraitInfoResp_NewLabelDetail) ProtoMessage()    {}
func (*PortraitInfoResp_NewLabelDetail) Descriptor() ([]byte, []int) {
	return fileDescriptor_2f04d4c6ef946e57, []int{1, 1}
}

func (m *PortraitInfoResp_NewLabelDetail) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PortraitInfoResp_NewLabelDetail.Unmarshal(m, b)
}
func (m *PortraitInfoResp_NewLabelDetail) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PortraitInfoResp_NewLabelDetail.Marshal(b, m, deterministic)
}
func (m *PortraitInfoResp_NewLabelDetail) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PortraitInfoResp_NewLabelDetail.Merge(m, src)
}
func (m *PortraitInfoResp_NewLabelDetail) XXX_Size() int {
	return xxx_messageInfo_PortraitInfoResp_NewLabelDetail.Size(m)
}
func (m *PortraitInfoResp_NewLabelDetail) XXX_DiscardUnknown() {
	xxx_messageInfo_PortraitInfoResp_NewLabelDetail.DiscardUnknown(m)
}

var xxx_messageInfo_PortraitInfoResp_NewLabelDetail proto.InternalMessageInfo

func (m *PortraitInfoResp_NewLabelDetail) GetTagId() uint64 {
	if m != nil {
		return m.TagId
	}
	return 0
}

func (m *PortraitInfoResp_NewLabelDetail) GetTagDesc() string {
	if m != nil {
		return m.TagDesc
	}
	return ""
}

type PortraitInfoResp_TicketInfo struct {
	// 游戏
	Project string `protobuf:"bytes,1,opt,name=project,proto3" json:"project"`
	// 工单IDz
	TicketId uint64 `protobuf:"varint,2,opt,name=ticket_id,json=ticketId,proto3" json:"ticket_id"`
	// 玩家输入第一句话
	Detail string `protobuf:"bytes,3,opt,name=detail,proto3" json:"detail"`
	// 累计金额
	Recharge float64 `protobuf:"fixed64,4,opt,name=recharge,proto3" json:"recharge"`
	// 工单状态
	Status uint32 `protobuf:"varint,5,opt,name=status,proto3" json:"status"`
	// 等待时长
	WaitingTime          string   `protobuf:"bytes,6,opt,name=waiting_time,json=waitingTime,proto3" json:"waiting_time"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *PortraitInfoResp_TicketInfo) Reset()         { *m = PortraitInfoResp_TicketInfo{} }
func (m *PortraitInfoResp_TicketInfo) String() string { return proto.CompactTextString(m) }
func (*PortraitInfoResp_TicketInfo) ProtoMessage()    {}
func (*PortraitInfoResp_TicketInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_2f04d4c6ef946e57, []int{1, 2}
}

func (m *PortraitInfoResp_TicketInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PortraitInfoResp_TicketInfo.Unmarshal(m, b)
}
func (m *PortraitInfoResp_TicketInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PortraitInfoResp_TicketInfo.Marshal(b, m, deterministic)
}
func (m *PortraitInfoResp_TicketInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PortraitInfoResp_TicketInfo.Merge(m, src)
}
func (m *PortraitInfoResp_TicketInfo) XXX_Size() int {
	return xxx_messageInfo_PortraitInfoResp_TicketInfo.Size(m)
}
func (m *PortraitInfoResp_TicketInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_PortraitInfoResp_TicketInfo.DiscardUnknown(m)
}

var xxx_messageInfo_PortraitInfoResp_TicketInfo proto.InternalMessageInfo

func (m *PortraitInfoResp_TicketInfo) GetProject() string {
	if m != nil {
		return m.Project
	}
	return ""
}

func (m *PortraitInfoResp_TicketInfo) GetTicketId() uint64 {
	if m != nil {
		return m.TicketId
	}
	return 0
}

func (m *PortraitInfoResp_TicketInfo) GetDetail() string {
	if m != nil {
		return m.Detail
	}
	return ""
}

func (m *PortraitInfoResp_TicketInfo) GetRecharge() float64 {
	if m != nil {
		return m.Recharge
	}
	return 0
}

func (m *PortraitInfoResp_TicketInfo) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *PortraitInfoResp_TicketInfo) GetWaitingTime() string {
	if m != nil {
		return m.WaitingTime
	}
	return ""
}

// PortraitEditReq 添加/编辑 画像信息
type PortraitEditReq struct {
	Id             uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	GameProject    string `protobuf:"bytes,2,opt,name=game_project,json=gameProject,proto3" json:"game_project"`
	Label          string `protobuf:"bytes,3,opt,name=label,proto3" json:"label"`
	Gender         uint32 `protobuf:"varint,4,opt,name=gender,proto3" json:"gender"`
	Birthday       string `protobuf:"bytes,5,opt,name=birthday,proto3" json:"birthday"`
	Career         string `protobuf:"bytes,6,opt,name=career,proto3" json:"career"`
	EducationLevel uint32 `protobuf:"varint,7,opt,name=education_level,json=educationLevel,proto3" json:"education_level"`
	MarriedState   uint32 `protobuf:"varint,8,opt,name=married_state,json=marriedState,proto3" json:"married_state"`
	FertilityState uint32 `protobuf:"varint,9,opt,name=fertility_state,json=fertilityState,proto3" json:"fertility_state"`
	Remark         string `protobuf:"bytes,10,opt,name=remark,proto3" json:"remark"`
	// @gotags: validate:"required_without=Id"
	DscUserId            string   `protobuf:"bytes,11,opt,name=dsc_user_id,json=dscUserId,proto3" json:"dsc_user_id" validate:"required_without=Id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *PortraitEditReq) Reset()         { *m = PortraitEditReq{} }
func (m *PortraitEditReq) String() string { return proto.CompactTextString(m) }
func (*PortraitEditReq) ProtoMessage()    {}
func (*PortraitEditReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_2f04d4c6ef946e57, []int{2}
}

func (m *PortraitEditReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_PortraitEditReq.Unmarshal(m, b)
}
func (m *PortraitEditReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_PortraitEditReq.Marshal(b, m, deterministic)
}
func (m *PortraitEditReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_PortraitEditReq.Merge(m, src)
}
func (m *PortraitEditReq) XXX_Size() int {
	return xxx_messageInfo_PortraitEditReq.Size(m)
}
func (m *PortraitEditReq) XXX_DiscardUnknown() {
	xxx_messageInfo_PortraitEditReq.DiscardUnknown(m)
}

var xxx_messageInfo_PortraitEditReq proto.InternalMessageInfo

func (m *PortraitEditReq) GetId() uint64 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *PortraitEditReq) GetGameProject() string {
	if m != nil {
		return m.GameProject
	}
	return ""
}

func (m *PortraitEditReq) GetLabel() string {
	if m != nil {
		return m.Label
	}
	return ""
}

func (m *PortraitEditReq) GetGender() uint32 {
	if m != nil {
		return m.Gender
	}
	return 0
}

func (m *PortraitEditReq) GetBirthday() string {
	if m != nil {
		return m.Birthday
	}
	return ""
}

func (m *PortraitEditReq) GetCareer() string {
	if m != nil {
		return m.Career
	}
	return ""
}

func (m *PortraitEditReq) GetEducationLevel() uint32 {
	if m != nil {
		return m.EducationLevel
	}
	return 0
}

func (m *PortraitEditReq) GetMarriedState() uint32 {
	if m != nil {
		return m.MarriedState
	}
	return 0
}

func (m *PortraitEditReq) GetFertilityState() uint32 {
	if m != nil {
		return m.FertilityState
	}
	return 0
}

func (m *PortraitEditReq) GetRemark() string {
	if m != nil {
		return m.Remark
	}
	return ""
}

func (m *PortraitEditReq) GetDscUserId() string {
	if m != nil {
		return m.DscUserId
	}
	return ""
}

// MaintainConfigDelReq 删除玩家维护配置
type MaintainConfigDelReq struct {
	// id @gotags: validate:"required"
	Id uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id" validate:"required"`
	// @gotags: validate:"required"
	DscUserId string `protobuf:"bytes,2,opt,name=dsc_user_id,json=dscUserId,proto3" json:"dsc_user_id" validate:"required"`
	//  @gotags: validate:"required"
	GameProject          string   `protobuf:"bytes,3,opt,name=game_project,json=gameProject,proto3" json:"game_project" validate:"required"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *MaintainConfigDelReq) Reset()         { *m = MaintainConfigDelReq{} }
func (m *MaintainConfigDelReq) String() string { return proto.CompactTextString(m) }
func (*MaintainConfigDelReq) ProtoMessage()    {}
func (*MaintainConfigDelReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_2f04d4c6ef946e57, []int{3}
}

func (m *MaintainConfigDelReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MaintainConfigDelReq.Unmarshal(m, b)
}
func (m *MaintainConfigDelReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MaintainConfigDelReq.Marshal(b, m, deterministic)
}
func (m *MaintainConfigDelReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MaintainConfigDelReq.Merge(m, src)
}
func (m *MaintainConfigDelReq) XXX_Size() int {
	return xxx_messageInfo_MaintainConfigDelReq.Size(m)
}
func (m *MaintainConfigDelReq) XXX_DiscardUnknown() {
	xxx_messageInfo_MaintainConfigDelReq.DiscardUnknown(m)
}

var xxx_messageInfo_MaintainConfigDelReq proto.InternalMessageInfo

func (m *MaintainConfigDelReq) GetId() uint64 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *MaintainConfigDelReq) GetDscUserId() string {
	if m != nil {
		return m.DscUserId
	}
	return ""
}

func (m *MaintainConfigDelReq) GetGameProject() string {
	if m != nil {
		return m.GameProject
	}
	return ""
}

// MaintainConfigListReq 玩家维护配置查询请求参数
type MaintainConfigListReq struct {
	// 游戏, 支持多选
	GameProject []string `protobuf:"bytes,1,rep,name=game_project,json=gameProject,proto3" json:"game_project"`
	// 维护专员, 支持多选
	Maintainer []string `protobuf:"bytes,2,rep,name=maintainer,proto3" json:"maintainer"`
	// 昵称, 支持多选
	NickName []string `protobuf:"bytes,3,rep,name=nick_name,json=nickName,proto3" json:"nick_name"`
	// 玩家账号,单选
	DscUserId            string   `protobuf:"bytes,4,opt,name=dsc_user_id,json=dscUserId,proto3" json:"dsc_user_id"`
	VipState             uint32   `protobuf:"varint,5,opt,name=vip_state,json=vipState,proto3" json:"vip_state"`
	Page                 uint32   `protobuf:"varint,6,opt,name=page,proto3" json:"page"`
	PageSize             uint32   `protobuf:"varint,7,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *MaintainConfigListReq) Reset()         { *m = MaintainConfigListReq{} }
func (m *MaintainConfigListReq) String() string { return proto.CompactTextString(m) }
func (*MaintainConfigListReq) ProtoMessage()    {}
func (*MaintainConfigListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_2f04d4c6ef946e57, []int{4}
}

func (m *MaintainConfigListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MaintainConfigListReq.Unmarshal(m, b)
}
func (m *MaintainConfigListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MaintainConfigListReq.Marshal(b, m, deterministic)
}
func (m *MaintainConfigListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MaintainConfigListReq.Merge(m, src)
}
func (m *MaintainConfigListReq) XXX_Size() int {
	return xxx_messageInfo_MaintainConfigListReq.Size(m)
}
func (m *MaintainConfigListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_MaintainConfigListReq.DiscardUnknown(m)
}

var xxx_messageInfo_MaintainConfigListReq proto.InternalMessageInfo

func (m *MaintainConfigListReq) GetGameProject() []string {
	if m != nil {
		return m.GameProject
	}
	return nil
}

func (m *MaintainConfigListReq) GetMaintainer() []string {
	if m != nil {
		return m.Maintainer
	}
	return nil
}

func (m *MaintainConfigListReq) GetNickName() []string {
	if m != nil {
		return m.NickName
	}
	return nil
}

func (m *MaintainConfigListReq) GetDscUserId() string {
	if m != nil {
		return m.DscUserId
	}
	return ""
}

func (m *MaintainConfigListReq) GetVipState() uint32 {
	if m != nil {
		return m.VipState
	}
	return 0
}

func (m *MaintainConfigListReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *MaintainConfigListReq) GetPageSize() uint32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

// MaintainConfigListResp 玩家维护配置查询响应
type MaintainConfigListResp struct {
	CurrentPage          uint32                                       `protobuf:"varint,1,opt,name=current_page,json=currentPage,proto3" json:"current_page"`
	PerPage              uint32                                       `protobuf:"varint,2,opt,name=per_page,json=perPage,proto3" json:"per_page"`
	Total                uint32                                       `protobuf:"varint,3,opt,name=total,proto3" json:"total"`
	Data                 []*MaintainConfigListResp_MaintainConfigInfo `protobuf:"bytes,4,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}                                     `json:"-" gorm:"-"`
	XXX_unrecognized     []byte                                       `json:"-" gorm:"-"`
	XXX_sizecache        int32                                        `json:"-" gorm:"-"`
}

func (m *MaintainConfigListResp) Reset()         { *m = MaintainConfigListResp{} }
func (m *MaintainConfigListResp) String() string { return proto.CompactTextString(m) }
func (*MaintainConfigListResp) ProtoMessage()    {}
func (*MaintainConfigListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_2f04d4c6ef946e57, []int{5}
}

func (m *MaintainConfigListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MaintainConfigListResp.Unmarshal(m, b)
}
func (m *MaintainConfigListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MaintainConfigListResp.Marshal(b, m, deterministic)
}
func (m *MaintainConfigListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MaintainConfigListResp.Merge(m, src)
}
func (m *MaintainConfigListResp) XXX_Size() int {
	return xxx_messageInfo_MaintainConfigListResp.Size(m)
}
func (m *MaintainConfigListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_MaintainConfigListResp.DiscardUnknown(m)
}

var xxx_messageInfo_MaintainConfigListResp proto.InternalMessageInfo

func (m *MaintainConfigListResp) GetCurrentPage() uint32 {
	if m != nil {
		return m.CurrentPage
	}
	return 0
}

func (m *MaintainConfigListResp) GetPerPage() uint32 {
	if m != nil {
		return m.PerPage
	}
	return 0
}

func (m *MaintainConfigListResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *MaintainConfigListResp) GetData() []*MaintainConfigListResp_MaintainConfigInfo {
	if m != nil {
		return m.Data
	}
	return nil
}

type MaintainConfigListResp_MaintainConfigInfo struct {
	Id uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	// 玩家discord_id
	DscUserId string `protobuf:"bytes,2,opt,name=dsc_user_id,json=dscUserId,proto3" json:"dsc_user_id"`
	// 玩家discord 昵称
	NickName string `protobuf:"bytes,3,opt,name=nick_name,json=nickName,proto3" json:"nick_name"`
	// 玩家fpid
	Fpid string `protobuf:"bytes,4,opt,name=fpid,proto3" json:"fpid"`
	Uid  uint64 `protobuf:"varint,5,opt,name=uid,proto3" json:"uid"`
	// 服务器
	Sid string `protobuf:"bytes,6,opt,name=sid,proto3" json:"sid"`
	// 维护专员
	Maintainer string `protobuf:"bytes,7,opt,name=maintainer,proto3" json:"maintainer"`
	// 游戏
	GameProject string `protobuf:"bytes,8,opt,name=game_project,json=gameProject,proto3" json:"game_project"`
	// 操作人
	Operator string `protobuf:"bytes,9,opt,name=operator,proto3" json:"operator"`
	VipState uint32 `protobuf:"varint,10,opt,name=vip_state,json=vipState,proto3" json:"vip_state"`
	// 操作时间
	UpdateTime           string   `protobuf:"bytes,11,opt,name=update_time,json=updateTime,proto3" json:"update_time"`
	Lang                 string   `protobuf:"bytes,12,opt,name=lang,proto3" json:"lang"`
	Birthday             string   `protobuf:"bytes,13,opt,name=birthday,proto3" json:"birthday"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *MaintainConfigListResp_MaintainConfigInfo) Reset() {
	*m = MaintainConfigListResp_MaintainConfigInfo{}
}
func (m *MaintainConfigListResp_MaintainConfigInfo) String() string {
	return proto.CompactTextString(m)
}
func (*MaintainConfigListResp_MaintainConfigInfo) ProtoMessage() {}
func (*MaintainConfigListResp_MaintainConfigInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_2f04d4c6ef946e57, []int{5, 0}
}

func (m *MaintainConfigListResp_MaintainConfigInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MaintainConfigListResp_MaintainConfigInfo.Unmarshal(m, b)
}
func (m *MaintainConfigListResp_MaintainConfigInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MaintainConfigListResp_MaintainConfigInfo.Marshal(b, m, deterministic)
}
func (m *MaintainConfigListResp_MaintainConfigInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MaintainConfigListResp_MaintainConfigInfo.Merge(m, src)
}
func (m *MaintainConfigListResp_MaintainConfigInfo) XXX_Size() int {
	return xxx_messageInfo_MaintainConfigListResp_MaintainConfigInfo.Size(m)
}
func (m *MaintainConfigListResp_MaintainConfigInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_MaintainConfigListResp_MaintainConfigInfo.DiscardUnknown(m)
}

var xxx_messageInfo_MaintainConfigListResp_MaintainConfigInfo proto.InternalMessageInfo

func (m *MaintainConfigListResp_MaintainConfigInfo) GetId() uint64 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *MaintainConfigListResp_MaintainConfigInfo) GetDscUserId() string {
	if m != nil {
		return m.DscUserId
	}
	return ""
}

func (m *MaintainConfigListResp_MaintainConfigInfo) GetNickName() string {
	if m != nil {
		return m.NickName
	}
	return ""
}

func (m *MaintainConfigListResp_MaintainConfigInfo) GetFpid() string {
	if m != nil {
		return m.Fpid
	}
	return ""
}

func (m *MaintainConfigListResp_MaintainConfigInfo) GetUid() uint64 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *MaintainConfigListResp_MaintainConfigInfo) GetSid() string {
	if m != nil {
		return m.Sid
	}
	return ""
}

func (m *MaintainConfigListResp_MaintainConfigInfo) GetMaintainer() string {
	if m != nil {
		return m.Maintainer
	}
	return ""
}

func (m *MaintainConfigListResp_MaintainConfigInfo) GetGameProject() string {
	if m != nil {
		return m.GameProject
	}
	return ""
}

func (m *MaintainConfigListResp_MaintainConfigInfo) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

func (m *MaintainConfigListResp_MaintainConfigInfo) GetVipState() uint32 {
	if m != nil {
		return m.VipState
	}
	return 0
}

func (m *MaintainConfigListResp_MaintainConfigInfo) GetUpdateTime() string {
	if m != nil {
		return m.UpdateTime
	}
	return ""
}

func (m *MaintainConfigListResp_MaintainConfigInfo) GetLang() string {
	if m != nil {
		return m.Lang
	}
	return ""
}

func (m *MaintainConfigListResp_MaintainConfigInfo) GetBirthday() string {
	if m != nil {
		return m.Birthday
	}
	return ""
}

type MaintainConfigNewReq struct {
	// 玩家discord_id @gotags: validate:"required"
	DscUserId string `protobuf:"bytes,1,opt,name=dsc_user_id,json=dscUserId,proto3" json:"dsc_user_id" validate:"required"`
	// 玩家discord 昵称
	NickName string `protobuf:"bytes,2,opt,name=nick_name,json=nickName,proto3" json:"nick_name"`
	// 玩家fpid @gotags: validate:"required"
	Fpid string `protobuf:"bytes,3,opt,name=fpid,proto3" json:"fpid" validate:"required"`
	// 维护专员 @gotags: validate:"required"
	Maintainer string `protobuf:"bytes,4,opt,name=maintainer,proto3" json:"maintainer" validate:"required"`
	// 游戏 @gotags: validate:"required"
	GameProject          string   `protobuf:"bytes,5,opt,name=game_project,json=gameProject,proto3" json:"game_project" validate:"required"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *MaintainConfigNewReq) Reset()         { *m = MaintainConfigNewReq{} }
func (m *MaintainConfigNewReq) String() string { return proto.CompactTextString(m) }
func (*MaintainConfigNewReq) ProtoMessage()    {}
func (*MaintainConfigNewReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_2f04d4c6ef946e57, []int{6}
}

func (m *MaintainConfigNewReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MaintainConfigNewReq.Unmarshal(m, b)
}
func (m *MaintainConfigNewReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MaintainConfigNewReq.Marshal(b, m, deterministic)
}
func (m *MaintainConfigNewReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MaintainConfigNewReq.Merge(m, src)
}
func (m *MaintainConfigNewReq) XXX_Size() int {
	return xxx_messageInfo_MaintainConfigNewReq.Size(m)
}
func (m *MaintainConfigNewReq) XXX_DiscardUnknown() {
	xxx_messageInfo_MaintainConfigNewReq.DiscardUnknown(m)
}

var xxx_messageInfo_MaintainConfigNewReq proto.InternalMessageInfo

func (m *MaintainConfigNewReq) GetDscUserId() string {
	if m != nil {
		return m.DscUserId
	}
	return ""
}

func (m *MaintainConfigNewReq) GetNickName() string {
	if m != nil {
		return m.NickName
	}
	return ""
}

func (m *MaintainConfigNewReq) GetFpid() string {
	if m != nil {
		return m.Fpid
	}
	return ""
}

func (m *MaintainConfigNewReq) GetMaintainer() string {
	if m != nil {
		return m.Maintainer
	}
	return ""
}

func (m *MaintainConfigNewReq) GetGameProject() string {
	if m != nil {
		return m.GameProject
	}
	return ""
}

type MaintainConfigEditReq struct {
	// @gotags: validate:"required"
	Id uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id" validate:"required"`
	// 玩家fpid
	Fpid string `protobuf:"bytes,2,opt,name=fpid,proto3" json:"fpid"`
	// 维护专员
	Maintainer string `protobuf:"bytes,3,opt,name=maintainer,proto3" json:"maintainer"`
	Uid        uint64 `protobuf:"varint,4,opt,name=uid,proto3" json:"uid"`
	VipState   uint32 `protobuf:"varint,5,opt,name=vip_state,json=vipState,proto3" json:"vip_state"`
	// @gotags: validate:"required"
	GameProject          string   `protobuf:"bytes,6,opt,name=game_project,json=gameProject,proto3" json:"game_project" validate:"required"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *MaintainConfigEditReq) Reset()         { *m = MaintainConfigEditReq{} }
func (m *MaintainConfigEditReq) String() string { return proto.CompactTextString(m) }
func (*MaintainConfigEditReq) ProtoMessage()    {}
func (*MaintainConfigEditReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_2f04d4c6ef946e57, []int{7}
}

func (m *MaintainConfigEditReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_MaintainConfigEditReq.Unmarshal(m, b)
}
func (m *MaintainConfigEditReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_MaintainConfigEditReq.Marshal(b, m, deterministic)
}
func (m *MaintainConfigEditReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_MaintainConfigEditReq.Merge(m, src)
}
func (m *MaintainConfigEditReq) XXX_Size() int {
	return xxx_messageInfo_MaintainConfigEditReq.Size(m)
}
func (m *MaintainConfigEditReq) XXX_DiscardUnknown() {
	xxx_messageInfo_MaintainConfigEditReq.DiscardUnknown(m)
}

var xxx_messageInfo_MaintainConfigEditReq proto.InternalMessageInfo

func (m *MaintainConfigEditReq) GetId() uint64 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *MaintainConfigEditReq) GetFpid() string {
	if m != nil {
		return m.Fpid
	}
	return ""
}

func (m *MaintainConfigEditReq) GetMaintainer() string {
	if m != nil {
		return m.Maintainer
	}
	return ""
}

func (m *MaintainConfigEditReq) GetUid() uint64 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *MaintainConfigEditReq) GetVipState() uint32 {
	if m != nil {
		return m.VipState
	}
	return 0
}

func (m *MaintainConfigEditReq) GetGameProject() string {
	if m != nil {
		return m.GameProject
	}
	return ""
}

type DiscordPlayerAccountsReq struct {
	// @gotags: validate:"required"
	GameProject string `protobuf:"bytes,1,opt,name=game_project,json=gameProject,proto3" json:"game_project" validate:"required"`
	// 搜索值
	DscUserId            string   `protobuf:"bytes,2,opt,name=dsc_user_id,json=dscUserId,proto3" json:"dsc_user_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *DiscordPlayerAccountsReq) Reset()         { *m = DiscordPlayerAccountsReq{} }
func (m *DiscordPlayerAccountsReq) String() string { return proto.CompactTextString(m) }
func (*DiscordPlayerAccountsReq) ProtoMessage()    {}
func (*DiscordPlayerAccountsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_2f04d4c6ef946e57, []int{8}
}

func (m *DiscordPlayerAccountsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DiscordPlayerAccountsReq.Unmarshal(m, b)
}
func (m *DiscordPlayerAccountsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DiscordPlayerAccountsReq.Marshal(b, m, deterministic)
}
func (m *DiscordPlayerAccountsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DiscordPlayerAccountsReq.Merge(m, src)
}
func (m *DiscordPlayerAccountsReq) XXX_Size() int {
	return xxx_messageInfo_DiscordPlayerAccountsReq.Size(m)
}
func (m *DiscordPlayerAccountsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DiscordPlayerAccountsReq.DiscardUnknown(m)
}

var xxx_messageInfo_DiscordPlayerAccountsReq proto.InternalMessageInfo

func (m *DiscordPlayerAccountsReq) GetGameProject() string {
	if m != nil {
		return m.GameProject
	}
	return ""
}

func (m *DiscordPlayerAccountsReq) GetDscUserId() string {
	if m != nil {
		return m.DscUserId
	}
	return ""
}

type DiscordPlayerAccountsResp struct {
	DscUserIds           []string `protobuf:"bytes,1,rep,name=dsc_user_ids,json=dscUserIds,proto3" json:"dsc_user_ids"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *DiscordPlayerAccountsResp) Reset()         { *m = DiscordPlayerAccountsResp{} }
func (m *DiscordPlayerAccountsResp) String() string { return proto.CompactTextString(m) }
func (*DiscordPlayerAccountsResp) ProtoMessage()    {}
func (*DiscordPlayerAccountsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_2f04d4c6ef946e57, []int{9}
}

func (m *DiscordPlayerAccountsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DiscordPlayerAccountsResp.Unmarshal(m, b)
}
func (m *DiscordPlayerAccountsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DiscordPlayerAccountsResp.Marshal(b, m, deterministic)
}
func (m *DiscordPlayerAccountsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DiscordPlayerAccountsResp.Merge(m, src)
}
func (m *DiscordPlayerAccountsResp) XXX_Size() int {
	return xxx_messageInfo_DiscordPlayerAccountsResp.Size(m)
}
func (m *DiscordPlayerAccountsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DiscordPlayerAccountsResp.DiscardUnknown(m)
}

var xxx_messageInfo_DiscordPlayerAccountsResp proto.InternalMessageInfo

func (m *DiscordPlayerAccountsResp) GetDscUserIds() []string {
	if m != nil {
		return m.DscUserIds
	}
	return nil
}

type DiscordCommuRecordAddReq struct {
	// 沟通日期 @gotags: validate:"required"
	CommuDate string `protobuf:"bytes,1,opt,name=commu_date,json=commuDate,proto3" json:"commu_date" validate:"required"`
	// 游戏 @gotags: validate:"required"
	Project string `protobuf:"bytes,2,opt,name=project,proto3" json:"project" validate:"required"`
	// 沟通问题 @gotags: validate:"required"
	Question string `protobuf:"bytes,3,opt,name=question,proto3" json:"question" validate:"required"`
	// 问题类型 @gotags: validate:"required"
	QuestionType int32 `protobuf:"varint,4,opt,name=question_type,json=questionType,proto3" json:"question_type" validate:"required"`
	// 处理状态 @gotags: validate:"required"
	HandleStatus int32 `protobuf:"varint,5,opt,name=handle_status,json=handleStatus,proto3" json:"handle_status" validate:"required"`
	// 备注
	Remark string `protobuf:"bytes,6,opt,name=remark,proto3" json:"remark"`
	// 涉及对话信息 @gotags: validate:"required"
	MsgIds string `protobuf:"bytes,7,opt,name=msg_ids,json=msgIds,proto3" json:"msg_ids" validate:"required"`
	// 玩家dsc_user_id @gotags: validate:"required"
	DscUserId string `protobuf:"bytes,8,opt,name=dsc_user_id,json=dscUserId,proto3" json:"dsc_user_id" validate:"required"`
	// 玩家uid
	Uid int64 `protobuf:"varint,9,opt,name=uid,proto3" json:"uid"`
	// 玩家所在的服务器
	Sid string `protobuf:"bytes,10,opt,name=sid,proto3" json:"sid"`
	// 玩家昵称 @gotags: validate:"required"
	NickName string `protobuf:"bytes,11,opt,name=nick_name,json=nickName,proto3" json:"nick_name" validate:"required"`
	// 玩家累付金额
	PayAll               float64  `protobuf:"fixed64,12,opt,name=pay_all,json=payAll,proto3" json:"pay_all"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *DiscordCommuRecordAddReq) Reset()         { *m = DiscordCommuRecordAddReq{} }
func (m *DiscordCommuRecordAddReq) String() string { return proto.CompactTextString(m) }
func (*DiscordCommuRecordAddReq) ProtoMessage()    {}
func (*DiscordCommuRecordAddReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_2f04d4c6ef946e57, []int{10}
}

func (m *DiscordCommuRecordAddReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DiscordCommuRecordAddReq.Unmarshal(m, b)
}
func (m *DiscordCommuRecordAddReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DiscordCommuRecordAddReq.Marshal(b, m, deterministic)
}
func (m *DiscordCommuRecordAddReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DiscordCommuRecordAddReq.Merge(m, src)
}
func (m *DiscordCommuRecordAddReq) XXX_Size() int {
	return xxx_messageInfo_DiscordCommuRecordAddReq.Size(m)
}
func (m *DiscordCommuRecordAddReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DiscordCommuRecordAddReq.DiscardUnknown(m)
}

var xxx_messageInfo_DiscordCommuRecordAddReq proto.InternalMessageInfo

func (m *DiscordCommuRecordAddReq) GetCommuDate() string {
	if m != nil {
		return m.CommuDate
	}
	return ""
}

func (m *DiscordCommuRecordAddReq) GetProject() string {
	if m != nil {
		return m.Project
	}
	return ""
}

func (m *DiscordCommuRecordAddReq) GetQuestion() string {
	if m != nil {
		return m.Question
	}
	return ""
}

func (m *DiscordCommuRecordAddReq) GetQuestionType() int32 {
	if m != nil {
		return m.QuestionType
	}
	return 0
}

func (m *DiscordCommuRecordAddReq) GetHandleStatus() int32 {
	if m != nil {
		return m.HandleStatus
	}
	return 0
}

func (m *DiscordCommuRecordAddReq) GetRemark() string {
	if m != nil {
		return m.Remark
	}
	return ""
}

func (m *DiscordCommuRecordAddReq) GetMsgIds() string {
	if m != nil {
		return m.MsgIds
	}
	return ""
}

func (m *DiscordCommuRecordAddReq) GetDscUserId() string {
	if m != nil {
		return m.DscUserId
	}
	return ""
}

func (m *DiscordCommuRecordAddReq) GetUid() int64 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *DiscordCommuRecordAddReq) GetSid() string {
	if m != nil {
		return m.Sid
	}
	return ""
}

func (m *DiscordCommuRecordAddReq) GetNickName() string {
	if m != nil {
		return m.NickName
	}
	return ""
}

func (m *DiscordCommuRecordAddReq) GetPayAll() float64 {
	if m != nil {
		return m.PayAll
	}
	return 0
}

type DiscordCommuRecordListReq struct {
	Project   []string `protobuf:"bytes,1,rep,name=project,proto3" json:"project"`
	CommuDate []string `protobuf:"bytes,2,rep,name=commu_date,json=commuDate,proto3" json:"commu_date"`
	Operator  []string `protobuf:"bytes,3,rep,name=operator,proto3" json:"operator"`
	Uid       int64    `protobuf:"varint,4,opt,name=uid,proto3" json:"uid"`
	// @gotags: validate:"required"
	Page uint32 `protobuf:"varint,5,opt,name=page,proto3" json:"page" validate:"required"`
	// @gotags: validate:"required"
	PageSize             uint32   `protobuf:"varint,6,opt,name=page_size,json=pageSize,proto3" json:"page_size" validate:"required"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *DiscordCommuRecordListReq) Reset()         { *m = DiscordCommuRecordListReq{} }
func (m *DiscordCommuRecordListReq) String() string { return proto.CompactTextString(m) }
func (*DiscordCommuRecordListReq) ProtoMessage()    {}
func (*DiscordCommuRecordListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_2f04d4c6ef946e57, []int{11}
}

func (m *DiscordCommuRecordListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DiscordCommuRecordListReq.Unmarshal(m, b)
}
func (m *DiscordCommuRecordListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DiscordCommuRecordListReq.Marshal(b, m, deterministic)
}
func (m *DiscordCommuRecordListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DiscordCommuRecordListReq.Merge(m, src)
}
func (m *DiscordCommuRecordListReq) XXX_Size() int {
	return xxx_messageInfo_DiscordCommuRecordListReq.Size(m)
}
func (m *DiscordCommuRecordListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DiscordCommuRecordListReq.DiscardUnknown(m)
}

var xxx_messageInfo_DiscordCommuRecordListReq proto.InternalMessageInfo

func (m *DiscordCommuRecordListReq) GetProject() []string {
	if m != nil {
		return m.Project
	}
	return nil
}

func (m *DiscordCommuRecordListReq) GetCommuDate() []string {
	if m != nil {
		return m.CommuDate
	}
	return nil
}

func (m *DiscordCommuRecordListReq) GetOperator() []string {
	if m != nil {
		return m.Operator
	}
	return nil
}

func (m *DiscordCommuRecordListReq) GetUid() int64 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *DiscordCommuRecordListReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *DiscordCommuRecordListReq) GetPageSize() uint32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

type DiscordCommuRecordListResp struct {
	CurrentPage          uint32                                           `protobuf:"varint,1,opt,name=current_page,json=currentPage,proto3" json:"current_page"`
	PerPage              uint32                                           `protobuf:"varint,2,opt,name=per_page,json=perPage,proto3" json:"per_page"`
	Total                uint32                                           `protobuf:"varint,3,opt,name=total,proto3" json:"total"`
	Data                 []*DiscordCommuRecordListResp_DiscordCommuRecord `protobuf:"bytes,4,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}                                         `json:"-" gorm:"-"`
	XXX_unrecognized     []byte                                           `json:"-" gorm:"-"`
	XXX_sizecache        int32                                            `json:"-" gorm:"-"`
}

func (m *DiscordCommuRecordListResp) Reset()         { *m = DiscordCommuRecordListResp{} }
func (m *DiscordCommuRecordListResp) String() string { return proto.CompactTextString(m) }
func (*DiscordCommuRecordListResp) ProtoMessage()    {}
func (*DiscordCommuRecordListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_2f04d4c6ef946e57, []int{12}
}

func (m *DiscordCommuRecordListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DiscordCommuRecordListResp.Unmarshal(m, b)
}
func (m *DiscordCommuRecordListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DiscordCommuRecordListResp.Marshal(b, m, deterministic)
}
func (m *DiscordCommuRecordListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DiscordCommuRecordListResp.Merge(m, src)
}
func (m *DiscordCommuRecordListResp) XXX_Size() int {
	return xxx_messageInfo_DiscordCommuRecordListResp.Size(m)
}
func (m *DiscordCommuRecordListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DiscordCommuRecordListResp.DiscardUnknown(m)
}

var xxx_messageInfo_DiscordCommuRecordListResp proto.InternalMessageInfo

func (m *DiscordCommuRecordListResp) GetCurrentPage() uint32 {
	if m != nil {
		return m.CurrentPage
	}
	return 0
}

func (m *DiscordCommuRecordListResp) GetPerPage() uint32 {
	if m != nil {
		return m.PerPage
	}
	return 0
}

func (m *DiscordCommuRecordListResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *DiscordCommuRecordListResp) GetData() []*DiscordCommuRecordListResp_DiscordCommuRecord {
	if m != nil {
		return m.Data
	}
	return nil
}

type DiscordCommuRecordListResp_DialogueItem struct {
	Role                 string   `protobuf:"bytes,1,opt,name=role,proto3" json:"role"`
	Content              string   `protobuf:"bytes,2,opt,name=content,proto3" json:"content"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *DiscordCommuRecordListResp_DialogueItem) Reset() {
	*m = DiscordCommuRecordListResp_DialogueItem{}
}
func (m *DiscordCommuRecordListResp_DialogueItem) String() string { return proto.CompactTextString(m) }
func (*DiscordCommuRecordListResp_DialogueItem) ProtoMessage()    {}
func (*DiscordCommuRecordListResp_DialogueItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_2f04d4c6ef946e57, []int{12, 0}
}

func (m *DiscordCommuRecordListResp_DialogueItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DiscordCommuRecordListResp_DialogueItem.Unmarshal(m, b)
}
func (m *DiscordCommuRecordListResp_DialogueItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DiscordCommuRecordListResp_DialogueItem.Marshal(b, m, deterministic)
}
func (m *DiscordCommuRecordListResp_DialogueItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DiscordCommuRecordListResp_DialogueItem.Merge(m, src)
}
func (m *DiscordCommuRecordListResp_DialogueItem) XXX_Size() int {
	return xxx_messageInfo_DiscordCommuRecordListResp_DialogueItem.Size(m)
}
func (m *DiscordCommuRecordListResp_DialogueItem) XXX_DiscardUnknown() {
	xxx_messageInfo_DiscordCommuRecordListResp_DialogueItem.DiscardUnknown(m)
}

var xxx_messageInfo_DiscordCommuRecordListResp_DialogueItem proto.InternalMessageInfo

func (m *DiscordCommuRecordListResp_DialogueItem) GetRole() string {
	if m != nil {
		return m.Role
	}
	return ""
}

func (m *DiscordCommuRecordListResp_DialogueItem) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

type DiscordCommuRecordListResp_DiscordCommuRecord struct {
	CommuDate            string                                     `protobuf:"bytes,1,opt,name=commu_date,json=commuDate,proto3" json:"commu_date"`
	Project              string                                     `protobuf:"bytes,2,opt,name=project,proto3" json:"project"`
	Uid                  int64                                      `protobuf:"varint,3,opt,name=uid,proto3" json:"uid"`
	Sid                  string                                     `protobuf:"bytes,4,opt,name=sid,proto3" json:"sid"`
	NickName             string                                     `protobuf:"bytes,5,opt,name=nick_name,json=nickName,proto3" json:"nick_name"`
	PayAll               float64                                    `protobuf:"fixed64,6,opt,name=pay_all,json=payAll,proto3" json:"pay_all"`
	Question             string                                     `protobuf:"bytes,7,opt,name=question,proto3" json:"question"`
	QuestionType         int32                                      `protobuf:"varint,8,opt,name=question_type,json=questionType,proto3" json:"question_type"`
	HandleStatus         int32                                      `protobuf:"varint,9,opt,name=handle_status,json=handleStatus,proto3" json:"handle_status"`
	Remark               string                                     `protobuf:"bytes,10,opt,name=remark,proto3" json:"remark"`
	Dialogue             []*DiscordCommuRecordListResp_DialogueItem `protobuf:"bytes,11,rep,name=dialogue,proto3" json:"dialogue"`
	Operator             string                                     `protobuf:"bytes,12,opt,name=operator,proto3" json:"operator"`
	Id                   int64                                      `protobuf:"varint,13,opt,name=id,proto3" json:"id"`
	Maintainer           string                                     `protobuf:"bytes,14,opt,name=maintainer,proto3" json:"maintainer"`
	XXX_NoUnkeyedLiteral struct{}                                   `json:"-" gorm:"-"`
	XXX_unrecognized     []byte                                     `json:"-" gorm:"-"`
	XXX_sizecache        int32                                      `json:"-" gorm:"-"`
}

func (m *DiscordCommuRecordListResp_DiscordCommuRecord) Reset() {
	*m = DiscordCommuRecordListResp_DiscordCommuRecord{}
}
func (m *DiscordCommuRecordListResp_DiscordCommuRecord) String() string {
	return proto.CompactTextString(m)
}
func (*DiscordCommuRecordListResp_DiscordCommuRecord) ProtoMessage() {}
func (*DiscordCommuRecordListResp_DiscordCommuRecord) Descriptor() ([]byte, []int) {
	return fileDescriptor_2f04d4c6ef946e57, []int{12, 1}
}

func (m *DiscordCommuRecordListResp_DiscordCommuRecord) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DiscordCommuRecordListResp_DiscordCommuRecord.Unmarshal(m, b)
}
func (m *DiscordCommuRecordListResp_DiscordCommuRecord) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DiscordCommuRecordListResp_DiscordCommuRecord.Marshal(b, m, deterministic)
}
func (m *DiscordCommuRecordListResp_DiscordCommuRecord) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DiscordCommuRecordListResp_DiscordCommuRecord.Merge(m, src)
}
func (m *DiscordCommuRecordListResp_DiscordCommuRecord) XXX_Size() int {
	return xxx_messageInfo_DiscordCommuRecordListResp_DiscordCommuRecord.Size(m)
}
func (m *DiscordCommuRecordListResp_DiscordCommuRecord) XXX_DiscardUnknown() {
	xxx_messageInfo_DiscordCommuRecordListResp_DiscordCommuRecord.DiscardUnknown(m)
}

var xxx_messageInfo_DiscordCommuRecordListResp_DiscordCommuRecord proto.InternalMessageInfo

func (m *DiscordCommuRecordListResp_DiscordCommuRecord) GetCommuDate() string {
	if m != nil {
		return m.CommuDate
	}
	return ""
}

func (m *DiscordCommuRecordListResp_DiscordCommuRecord) GetProject() string {
	if m != nil {
		return m.Project
	}
	return ""
}

func (m *DiscordCommuRecordListResp_DiscordCommuRecord) GetUid() int64 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *DiscordCommuRecordListResp_DiscordCommuRecord) GetSid() string {
	if m != nil {
		return m.Sid
	}
	return ""
}

func (m *DiscordCommuRecordListResp_DiscordCommuRecord) GetNickName() string {
	if m != nil {
		return m.NickName
	}
	return ""
}

func (m *DiscordCommuRecordListResp_DiscordCommuRecord) GetPayAll() float64 {
	if m != nil {
		return m.PayAll
	}
	return 0
}

func (m *DiscordCommuRecordListResp_DiscordCommuRecord) GetQuestion() string {
	if m != nil {
		return m.Question
	}
	return ""
}

func (m *DiscordCommuRecordListResp_DiscordCommuRecord) GetQuestionType() int32 {
	if m != nil {
		return m.QuestionType
	}
	return 0
}

func (m *DiscordCommuRecordListResp_DiscordCommuRecord) GetHandleStatus() int32 {
	if m != nil {
		return m.HandleStatus
	}
	return 0
}

func (m *DiscordCommuRecordListResp_DiscordCommuRecord) GetRemark() string {
	if m != nil {
		return m.Remark
	}
	return ""
}

func (m *DiscordCommuRecordListResp_DiscordCommuRecord) GetDialogue() []*DiscordCommuRecordListResp_DialogueItem {
	if m != nil {
		return m.Dialogue
	}
	return nil
}

func (m *DiscordCommuRecordListResp_DiscordCommuRecord) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

func (m *DiscordCommuRecordListResp_DiscordCommuRecord) GetId() int64 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *DiscordCommuRecordListResp_DiscordCommuRecord) GetMaintainer() string {
	if m != nil {
		return m.Maintainer
	}
	return ""
}

type DiscordCommuRecordEditReq struct {
	// @gotags: validate:"required"
	Id                   int64    `protobuf:"varint,1,opt,name=id,proto3" json:"id" validate:"required"`
	CommuDate            string   `protobuf:"bytes,2,opt,name=commu_date,json=commuDate,proto3" json:"commu_date"`
	Question             string   `protobuf:"bytes,3,opt,name=question,proto3" json:"question"`
	QuestionType         int32    `protobuf:"varint,4,opt,name=question_type,json=questionType,proto3" json:"question_type"`
	HandleStatus         int32    `protobuf:"varint,5,opt,name=handle_status,json=handleStatus,proto3" json:"handle_status"`
	Remark               string   `protobuf:"bytes,6,opt,name=remark,proto3" json:"remark"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *DiscordCommuRecordEditReq) Reset()         { *m = DiscordCommuRecordEditReq{} }
func (m *DiscordCommuRecordEditReq) String() string { return proto.CompactTextString(m) }
func (*DiscordCommuRecordEditReq) ProtoMessage()    {}
func (*DiscordCommuRecordEditReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_2f04d4c6ef946e57, []int{13}
}

func (m *DiscordCommuRecordEditReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DiscordCommuRecordEditReq.Unmarshal(m, b)
}
func (m *DiscordCommuRecordEditReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DiscordCommuRecordEditReq.Marshal(b, m, deterministic)
}
func (m *DiscordCommuRecordEditReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DiscordCommuRecordEditReq.Merge(m, src)
}
func (m *DiscordCommuRecordEditReq) XXX_Size() int {
	return xxx_messageInfo_DiscordCommuRecordEditReq.Size(m)
}
func (m *DiscordCommuRecordEditReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DiscordCommuRecordEditReq.DiscardUnknown(m)
}

var xxx_messageInfo_DiscordCommuRecordEditReq proto.InternalMessageInfo

func (m *DiscordCommuRecordEditReq) GetId() int64 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *DiscordCommuRecordEditReq) GetCommuDate() string {
	if m != nil {
		return m.CommuDate
	}
	return ""
}

func (m *DiscordCommuRecordEditReq) GetQuestion() string {
	if m != nil {
		return m.Question
	}
	return ""
}

func (m *DiscordCommuRecordEditReq) GetQuestionType() int32 {
	if m != nil {
		return m.QuestionType
	}
	return 0
}

func (m *DiscordCommuRecordEditReq) GetHandleStatus() int32 {
	if m != nil {
		return m.HandleStatus
	}
	return 0
}

func (m *DiscordCommuRecordEditReq) GetRemark() string {
	if m != nil {
		return m.Remark
	}
	return ""
}

type DiscordTagAddReq struct {
	// @gotags: validate:"required"
	TagName string `protobuf:"bytes,1,opt,name=tag_name,json=tagName,proto3" json:"tag_name" validate:"required"`
	TagDesc string `protobuf:"bytes,2,opt,name=tag_desc,json=tagDesc,proto3" json:"tag_desc"`
	// @gotags: validate:"required,oneof=1 2"
	Status               int32    `protobuf:"varint,3,opt,name=status,proto3" json:"status" validate:"required,oneof=1 2"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *DiscordTagAddReq) Reset()         { *m = DiscordTagAddReq{} }
func (m *DiscordTagAddReq) String() string { return proto.CompactTextString(m) }
func (*DiscordTagAddReq) ProtoMessage()    {}
func (*DiscordTagAddReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_2f04d4c6ef946e57, []int{14}
}

func (m *DiscordTagAddReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DiscordTagAddReq.Unmarshal(m, b)
}
func (m *DiscordTagAddReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DiscordTagAddReq.Marshal(b, m, deterministic)
}
func (m *DiscordTagAddReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DiscordTagAddReq.Merge(m, src)
}
func (m *DiscordTagAddReq) XXX_Size() int {
	return xxx_messageInfo_DiscordTagAddReq.Size(m)
}
func (m *DiscordTagAddReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DiscordTagAddReq.DiscardUnknown(m)
}

var xxx_messageInfo_DiscordTagAddReq proto.InternalMessageInfo

func (m *DiscordTagAddReq) GetTagName() string {
	if m != nil {
		return m.TagName
	}
	return ""
}

func (m *DiscordTagAddReq) GetTagDesc() string {
	if m != nil {
		return m.TagDesc
	}
	return ""
}

func (m *DiscordTagAddReq) GetStatus() int32 {
	if m != nil {
		return m.Status
	}
	return 0
}

type DiscordTagEditReq struct {
	// @gotags: validate:"required"
	Id                   int32    `protobuf:"varint,1,opt,name=id,proto3" json:"id" validate:"required"`
	TagName              string   `protobuf:"bytes,2,opt,name=tag_name,json=tagName,proto3" json:"tag_name"`
	TagDesc              string   `protobuf:"bytes,3,opt,name=tag_desc,json=tagDesc,proto3" json:"tag_desc"`
	Status               int32    `protobuf:"varint,4,opt,name=status,proto3" json:"status"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *DiscordTagEditReq) Reset()         { *m = DiscordTagEditReq{} }
func (m *DiscordTagEditReq) String() string { return proto.CompactTextString(m) }
func (*DiscordTagEditReq) ProtoMessage()    {}
func (*DiscordTagEditReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_2f04d4c6ef946e57, []int{15}
}

func (m *DiscordTagEditReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DiscordTagEditReq.Unmarshal(m, b)
}
func (m *DiscordTagEditReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DiscordTagEditReq.Marshal(b, m, deterministic)
}
func (m *DiscordTagEditReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DiscordTagEditReq.Merge(m, src)
}
func (m *DiscordTagEditReq) XXX_Size() int {
	return xxx_messageInfo_DiscordTagEditReq.Size(m)
}
func (m *DiscordTagEditReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DiscordTagEditReq.DiscardUnknown(m)
}

var xxx_messageInfo_DiscordTagEditReq proto.InternalMessageInfo

func (m *DiscordTagEditReq) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *DiscordTagEditReq) GetTagName() string {
	if m != nil {
		return m.TagName
	}
	return ""
}

func (m *DiscordTagEditReq) GetTagDesc() string {
	if m != nil {
		return m.TagDesc
	}
	return ""
}

func (m *DiscordTagEditReq) GetStatus() int32 {
	if m != nil {
		return m.Status
	}
	return 0
}

type DiscordTagListReq struct {
	TagName              string   `protobuf:"bytes,1,opt,name=tag_name,json=tagName,proto3" json:"tag_name"`
	Operator             string   `protobuf:"bytes,2,opt,name=operator,proto3" json:"operator"`
	Status               int32    `protobuf:"varint,3,opt,name=status,proto3" json:"status"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *DiscordTagListReq) Reset()         { *m = DiscordTagListReq{} }
func (m *DiscordTagListReq) String() string { return proto.CompactTextString(m) }
func (*DiscordTagListReq) ProtoMessage()    {}
func (*DiscordTagListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_2f04d4c6ef946e57, []int{16}
}

func (m *DiscordTagListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DiscordTagListReq.Unmarshal(m, b)
}
func (m *DiscordTagListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DiscordTagListReq.Marshal(b, m, deterministic)
}
func (m *DiscordTagListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DiscordTagListReq.Merge(m, src)
}
func (m *DiscordTagListReq) XXX_Size() int {
	return xxx_messageInfo_DiscordTagListReq.Size(m)
}
func (m *DiscordTagListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DiscordTagListReq.DiscardUnknown(m)
}

var xxx_messageInfo_DiscordTagListReq proto.InternalMessageInfo

func (m *DiscordTagListReq) GetTagName() string {
	if m != nil {
		return m.TagName
	}
	return ""
}

func (m *DiscordTagListReq) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

func (m *DiscordTagListReq) GetStatus() int32 {
	if m != nil {
		return m.Status
	}
	return 0
}

type DiscordTagListResp struct {
	Data                 []*DiscordTagListResp_TagDetail `protobuf:"bytes,1,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}                        `json:"-" gorm:"-"`
	XXX_unrecognized     []byte                          `json:"-" gorm:"-"`
	XXX_sizecache        int32                           `json:"-" gorm:"-"`
}

func (m *DiscordTagListResp) Reset()         { *m = DiscordTagListResp{} }
func (m *DiscordTagListResp) String() string { return proto.CompactTextString(m) }
func (*DiscordTagListResp) ProtoMessage()    {}
func (*DiscordTagListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_2f04d4c6ef946e57, []int{17}
}

func (m *DiscordTagListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DiscordTagListResp.Unmarshal(m, b)
}
func (m *DiscordTagListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DiscordTagListResp.Marshal(b, m, deterministic)
}
func (m *DiscordTagListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DiscordTagListResp.Merge(m, src)
}
func (m *DiscordTagListResp) XXX_Size() int {
	return xxx_messageInfo_DiscordTagListResp.Size(m)
}
func (m *DiscordTagListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DiscordTagListResp.DiscardUnknown(m)
}

var xxx_messageInfo_DiscordTagListResp proto.InternalMessageInfo

func (m *DiscordTagListResp) GetData() []*DiscordTagListResp_TagDetail {
	if m != nil {
		return m.Data
	}
	return nil
}

type DiscordTagListResp_TagDetail struct {
	Id                   int32    `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	TagName              string   `protobuf:"bytes,2,opt,name=tag_name,json=tagName,proto3" json:"tag_name"`
	TagDesc              string   `protobuf:"bytes,3,opt,name=tag_desc,json=tagDesc,proto3" json:"tag_desc"`
	UpdateTime           string   `protobuf:"bytes,4,opt,name=update_time,json=updateTime,proto3" json:"update_time"`
	Operator             string   `protobuf:"bytes,5,opt,name=operator,proto3" json:"operator"`
	Status               int32    `protobuf:"varint,6,opt,name=status,proto3" json:"status"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *DiscordTagListResp_TagDetail) Reset()         { *m = DiscordTagListResp_TagDetail{} }
func (m *DiscordTagListResp_TagDetail) String() string { return proto.CompactTextString(m) }
func (*DiscordTagListResp_TagDetail) ProtoMessage()    {}
func (*DiscordTagListResp_TagDetail) Descriptor() ([]byte, []int) {
	return fileDescriptor_2f04d4c6ef946e57, []int{17, 0}
}

func (m *DiscordTagListResp_TagDetail) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DiscordTagListResp_TagDetail.Unmarshal(m, b)
}
func (m *DiscordTagListResp_TagDetail) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DiscordTagListResp_TagDetail.Marshal(b, m, deterministic)
}
func (m *DiscordTagListResp_TagDetail) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DiscordTagListResp_TagDetail.Merge(m, src)
}
func (m *DiscordTagListResp_TagDetail) XXX_Size() int {
	return xxx_messageInfo_DiscordTagListResp_TagDetail.Size(m)
}
func (m *DiscordTagListResp_TagDetail) XXX_DiscardUnknown() {
	xxx_messageInfo_DiscordTagListResp_TagDetail.DiscardUnknown(m)
}

var xxx_messageInfo_DiscordTagListResp_TagDetail proto.InternalMessageInfo

func (m *DiscordTagListResp_TagDetail) GetId() int32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *DiscordTagListResp_TagDetail) GetTagName() string {
	if m != nil {
		return m.TagName
	}
	return ""
}

func (m *DiscordTagListResp_TagDetail) GetTagDesc() string {
	if m != nil {
		return m.TagDesc
	}
	return ""
}

func (m *DiscordTagListResp_TagDetail) GetUpdateTime() string {
	if m != nil {
		return m.UpdateTime
	}
	return ""
}

func (m *DiscordTagListResp_TagDetail) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

func (m *DiscordTagListResp_TagDetail) GetStatus() int32 {
	if m != nil {
		return m.Status
	}
	return 0
}

type DiscordBatchTagReq struct {
	// @gotags: validate:"required"
	DscUserIdList []string `protobuf:"bytes,1,rep,name=dsc_user_id_list,json=dscUserIdList,proto3" json:"dsc_user_id_list" validate:"required"`
	// @gotags: validate:"required"
	Tag string `protobuf:"bytes,2,opt,name=tag,proto3" json:"tag" validate:"required"`
	// @gotags: validate:"required"
	Project              string   `protobuf:"bytes,3,opt,name=project,proto3" json:"project" validate:"required"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *DiscordBatchTagReq) Reset()         { *m = DiscordBatchTagReq{} }
func (m *DiscordBatchTagReq) String() string { return proto.CompactTextString(m) }
func (*DiscordBatchTagReq) ProtoMessage()    {}
func (*DiscordBatchTagReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_2f04d4c6ef946e57, []int{18}
}

func (m *DiscordBatchTagReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DiscordBatchTagReq.Unmarshal(m, b)
}
func (m *DiscordBatchTagReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DiscordBatchTagReq.Marshal(b, m, deterministic)
}
func (m *DiscordBatchTagReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DiscordBatchTagReq.Merge(m, src)
}
func (m *DiscordBatchTagReq) XXX_Size() int {
	return xxx_messageInfo_DiscordBatchTagReq.Size(m)
}
func (m *DiscordBatchTagReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DiscordBatchTagReq.DiscardUnknown(m)
}

var xxx_messageInfo_DiscordBatchTagReq proto.InternalMessageInfo

func (m *DiscordBatchTagReq) GetDscUserIdList() []string {
	if m != nil {
		return m.DscUserIdList
	}
	return nil
}

func (m *DiscordBatchTagReq) GetTag() string {
	if m != nil {
		return m.Tag
	}
	return ""
}

func (m *DiscordBatchTagReq) GetProject() string {
	if m != nil {
		return m.Project
	}
	return ""
}

type DiscordMessageTaskListReq struct {
	Project  []string `protobuf:"bytes,1,rep,name=project,proto3" json:"project"`
	Date     []string `protobuf:"bytes,2,rep,name=date,proto3" json:"date"`
	Operator []string `protobuf:"bytes,3,rep,name=operator,proto3" json:"operator"`
	// @gotags: validate:"required"
	Page uint32 `protobuf:"varint,5,opt,name=page,proto3" json:"page" validate:"required"`
	// @gotags: validate:"required"
	PageSize             uint32   `protobuf:"varint,6,opt,name=page_size,json=pageSize,proto3" json:"page_size" validate:"required"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *DiscordMessageTaskListReq) Reset()         { *m = DiscordMessageTaskListReq{} }
func (m *DiscordMessageTaskListReq) String() string { return proto.CompactTextString(m) }
func (*DiscordMessageTaskListReq) ProtoMessage()    {}
func (*DiscordMessageTaskListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_2f04d4c6ef946e57, []int{19}
}

func (m *DiscordMessageTaskListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DiscordMessageTaskListReq.Unmarshal(m, b)
}
func (m *DiscordMessageTaskListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DiscordMessageTaskListReq.Marshal(b, m, deterministic)
}
func (m *DiscordMessageTaskListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DiscordMessageTaskListReq.Merge(m, src)
}
func (m *DiscordMessageTaskListReq) XXX_Size() int {
	return xxx_messageInfo_DiscordMessageTaskListReq.Size(m)
}
func (m *DiscordMessageTaskListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DiscordMessageTaskListReq.DiscardUnknown(m)
}

var xxx_messageInfo_DiscordMessageTaskListReq proto.InternalMessageInfo

func (m *DiscordMessageTaskListReq) GetProject() []string {
	if m != nil {
		return m.Project
	}
	return nil
}

func (m *DiscordMessageTaskListReq) GetDate() []string {
	if m != nil {
		return m.Date
	}
	return nil
}

func (m *DiscordMessageTaskListReq) GetOperator() []string {
	if m != nil {
		return m.Operator
	}
	return nil
}

func (m *DiscordMessageTaskListReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *DiscordMessageTaskListReq) GetPageSize() uint32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

type DiscordMessageTaskListResp struct {
	CurrentPage          uint32                                             `protobuf:"varint,1,opt,name=current_page,json=currentPage,proto3" json:"current_page"`
	PerPage              uint32                                             `protobuf:"varint,2,opt,name=per_page,json=perPage,proto3" json:"per_page"`
	Total                uint32                                             `protobuf:"varint,3,opt,name=total,proto3" json:"total"`
	Data                 []*DiscordMessageTaskListResp_DiscordMessageRecord `protobuf:"bytes,4,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}                                           `json:"-" gorm:"-"`
	XXX_unrecognized     []byte                                             `json:"-" gorm:"-"`
	XXX_sizecache        int32                                              `json:"-" gorm:"-"`
}

func (m *DiscordMessageTaskListResp) Reset()         { *m = DiscordMessageTaskListResp{} }
func (m *DiscordMessageTaskListResp) String() string { return proto.CompactTextString(m) }
func (*DiscordMessageTaskListResp) ProtoMessage()    {}
func (*DiscordMessageTaskListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_2f04d4c6ef946e57, []int{20}
}

func (m *DiscordMessageTaskListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DiscordMessageTaskListResp.Unmarshal(m, b)
}
func (m *DiscordMessageTaskListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DiscordMessageTaskListResp.Marshal(b, m, deterministic)
}
func (m *DiscordMessageTaskListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DiscordMessageTaskListResp.Merge(m, src)
}
func (m *DiscordMessageTaskListResp) XXX_Size() int {
	return xxx_messageInfo_DiscordMessageTaskListResp.Size(m)
}
func (m *DiscordMessageTaskListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DiscordMessageTaskListResp.DiscardUnknown(m)
}

var xxx_messageInfo_DiscordMessageTaskListResp proto.InternalMessageInfo

func (m *DiscordMessageTaskListResp) GetCurrentPage() uint32 {
	if m != nil {
		return m.CurrentPage
	}
	return 0
}

func (m *DiscordMessageTaskListResp) GetPerPage() uint32 {
	if m != nil {
		return m.PerPage
	}
	return 0
}

func (m *DiscordMessageTaskListResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *DiscordMessageTaskListResp) GetData() []*DiscordMessageTaskListResp_DiscordMessageRecord {
	if m != nil {
		return m.Data
	}
	return nil
}

type DiscordMessageTaskListResp_ReplyContent struct {
	Content              string   `protobuf:"bytes,3,opt,name=content,proto3" json:"content"`
	FileUrl              string   `protobuf:"bytes,4,opt,name=file_url,json=fileUrl,proto3" json:"file_url"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *DiscordMessageTaskListResp_ReplyContent) Reset() {
	*m = DiscordMessageTaskListResp_ReplyContent{}
}
func (m *DiscordMessageTaskListResp_ReplyContent) String() string { return proto.CompactTextString(m) }
func (*DiscordMessageTaskListResp_ReplyContent) ProtoMessage()    {}
func (*DiscordMessageTaskListResp_ReplyContent) Descriptor() ([]byte, []int) {
	return fileDescriptor_2f04d4c6ef946e57, []int{20, 0}
}

func (m *DiscordMessageTaskListResp_ReplyContent) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DiscordMessageTaskListResp_ReplyContent.Unmarshal(m, b)
}
func (m *DiscordMessageTaskListResp_ReplyContent) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DiscordMessageTaskListResp_ReplyContent.Marshal(b, m, deterministic)
}
func (m *DiscordMessageTaskListResp_ReplyContent) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DiscordMessageTaskListResp_ReplyContent.Merge(m, src)
}
func (m *DiscordMessageTaskListResp_ReplyContent) XXX_Size() int {
	return xxx_messageInfo_DiscordMessageTaskListResp_ReplyContent.Size(m)
}
func (m *DiscordMessageTaskListResp_ReplyContent) XXX_DiscardUnknown() {
	xxx_messageInfo_DiscordMessageTaskListResp_ReplyContent.DiscardUnknown(m)
}

var xxx_messageInfo_DiscordMessageTaskListResp_ReplyContent proto.InternalMessageInfo

func (m *DiscordMessageTaskListResp_ReplyContent) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *DiscordMessageTaskListResp_ReplyContent) GetFileUrl() string {
	if m != nil {
		return m.FileUrl
	}
	return ""
}

type DiscordMessageTaskListResp_Count struct {
	SuccessCount         int32    `protobuf:"varint,7,opt,name=success_count,json=successCount,proto3" json:"success_count"`
	FailedCount          int32    `protobuf:"varint,8,opt,name=failed_count,json=failedCount,proto3" json:"failed_count"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *DiscordMessageTaskListResp_Count) Reset()         { *m = DiscordMessageTaskListResp_Count{} }
func (m *DiscordMessageTaskListResp_Count) String() string { return proto.CompactTextString(m) }
func (*DiscordMessageTaskListResp_Count) ProtoMessage()    {}
func (*DiscordMessageTaskListResp_Count) Descriptor() ([]byte, []int) {
	return fileDescriptor_2f04d4c6ef946e57, []int{20, 1}
}

func (m *DiscordMessageTaskListResp_Count) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DiscordMessageTaskListResp_Count.Unmarshal(m, b)
}
func (m *DiscordMessageTaskListResp_Count) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DiscordMessageTaskListResp_Count.Marshal(b, m, deterministic)
}
func (m *DiscordMessageTaskListResp_Count) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DiscordMessageTaskListResp_Count.Merge(m, src)
}
func (m *DiscordMessageTaskListResp_Count) XXX_Size() int {
	return xxx_messageInfo_DiscordMessageTaskListResp_Count.Size(m)
}
func (m *DiscordMessageTaskListResp_Count) XXX_DiscardUnknown() {
	xxx_messageInfo_DiscordMessageTaskListResp_Count.DiscardUnknown(m)
}

var xxx_messageInfo_DiscordMessageTaskListResp_Count proto.InternalMessageInfo

func (m *DiscordMessageTaskListResp_Count) GetSuccessCount() int32 {
	if m != nil {
		return m.SuccessCount
	}
	return 0
}

func (m *DiscordMessageTaskListResp_Count) GetFailedCount() int32 {
	if m != nil {
		return m.FailedCount
	}
	return 0
}

type DiscordMessageTaskListResp_DiscordMessageRecord struct {
	TaskId               uint64                                   `protobuf:"varint,1,opt,name=task_id,json=taskId,proto3" json:"task_id"`
	Project              string                                   `protobuf:"bytes,2,opt,name=project,proto3" json:"project"`
	ReplyContent         *DiscordMessageTaskListResp_ReplyContent `protobuf:"bytes,3,opt,name=reply_content,json=replyContent,proto3" json:"reply_content"`
	Status               int32                                    `protobuf:"varint,5,opt,name=status,proto3" json:"status"`
	Total                int32                                    `protobuf:"varint,6,opt,name=total,proto3" json:"total"`
	Count                *DiscordMessageTaskListResp_Count        `protobuf:"bytes,7,opt,name=count,proto3" json:"count"`
	Operator             string                                   `protobuf:"bytes,9,opt,name=operator,proto3" json:"operator"`
	CreateAt             string                                   `protobuf:"bytes,10,opt,name=create_at,json=createAt,proto3" json:"create_at"`
	FinishedAt           string                                   `protobuf:"bytes,11,opt,name=finished_at,json=finishedAt,proto3" json:"finished_at"`
	FailIds              []int64                                  `protobuf:"varint,12,rep,packed,name=fail_ids,json=failIds,proto3" json:"fail_ids"`
	XXX_NoUnkeyedLiteral struct{}                                 `json:"-" gorm:"-"`
	XXX_unrecognized     []byte                                   `json:"-" gorm:"-"`
	XXX_sizecache        int32                                    `json:"-" gorm:"-"`
}

func (m *DiscordMessageTaskListResp_DiscordMessageRecord) Reset() {
	*m = DiscordMessageTaskListResp_DiscordMessageRecord{}
}
func (m *DiscordMessageTaskListResp_DiscordMessageRecord) String() string {
	return proto.CompactTextString(m)
}
func (*DiscordMessageTaskListResp_DiscordMessageRecord) ProtoMessage() {}
func (*DiscordMessageTaskListResp_DiscordMessageRecord) Descriptor() ([]byte, []int) {
	return fileDescriptor_2f04d4c6ef946e57, []int{20, 2}
}

func (m *DiscordMessageTaskListResp_DiscordMessageRecord) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DiscordMessageTaskListResp_DiscordMessageRecord.Unmarshal(m, b)
}
func (m *DiscordMessageTaskListResp_DiscordMessageRecord) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DiscordMessageTaskListResp_DiscordMessageRecord.Marshal(b, m, deterministic)
}
func (m *DiscordMessageTaskListResp_DiscordMessageRecord) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DiscordMessageTaskListResp_DiscordMessageRecord.Merge(m, src)
}
func (m *DiscordMessageTaskListResp_DiscordMessageRecord) XXX_Size() int {
	return xxx_messageInfo_DiscordMessageTaskListResp_DiscordMessageRecord.Size(m)
}
func (m *DiscordMessageTaskListResp_DiscordMessageRecord) XXX_DiscardUnknown() {
	xxx_messageInfo_DiscordMessageTaskListResp_DiscordMessageRecord.DiscardUnknown(m)
}

var xxx_messageInfo_DiscordMessageTaskListResp_DiscordMessageRecord proto.InternalMessageInfo

func (m *DiscordMessageTaskListResp_DiscordMessageRecord) GetTaskId() uint64 {
	if m != nil {
		return m.TaskId
	}
	return 0
}

func (m *DiscordMessageTaskListResp_DiscordMessageRecord) GetProject() string {
	if m != nil {
		return m.Project
	}
	return ""
}

func (m *DiscordMessageTaskListResp_DiscordMessageRecord) GetReplyContent() *DiscordMessageTaskListResp_ReplyContent {
	if m != nil {
		return m.ReplyContent
	}
	return nil
}

func (m *DiscordMessageTaskListResp_DiscordMessageRecord) GetStatus() int32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *DiscordMessageTaskListResp_DiscordMessageRecord) GetTotal() int32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *DiscordMessageTaskListResp_DiscordMessageRecord) GetCount() *DiscordMessageTaskListResp_Count {
	if m != nil {
		return m.Count
	}
	return nil
}

func (m *DiscordMessageTaskListResp_DiscordMessageRecord) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

func (m *DiscordMessageTaskListResp_DiscordMessageRecord) GetCreateAt() string {
	if m != nil {
		return m.CreateAt
	}
	return ""
}

func (m *DiscordMessageTaskListResp_DiscordMessageRecord) GetFinishedAt() string {
	if m != nil {
		return m.FinishedAt
	}
	return ""
}

func (m *DiscordMessageTaskListResp_DiscordMessageRecord) GetFailIds() []int64 {
	if m != nil {
		return m.FailIds
	}
	return nil
}

type DiscordMessageTaskDetailExportReq struct {
	// @gotags: validate:"required"
	TaskId               uint64   `protobuf:"varint,1,opt,name=task_id,json=taskId,proto3" json:"task_id" validate:"required"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *DiscordMessageTaskDetailExportReq) Reset()         { *m = DiscordMessageTaskDetailExportReq{} }
func (m *DiscordMessageTaskDetailExportReq) String() string { return proto.CompactTextString(m) }
func (*DiscordMessageTaskDetailExportReq) ProtoMessage()    {}
func (*DiscordMessageTaskDetailExportReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_2f04d4c6ef946e57, []int{21}
}

func (m *DiscordMessageTaskDetailExportReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DiscordMessageTaskDetailExportReq.Unmarshal(m, b)
}
func (m *DiscordMessageTaskDetailExportReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DiscordMessageTaskDetailExportReq.Marshal(b, m, deterministic)
}
func (m *DiscordMessageTaskDetailExportReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DiscordMessageTaskDetailExportReq.Merge(m, src)
}
func (m *DiscordMessageTaskDetailExportReq) XXX_Size() int {
	return xxx_messageInfo_DiscordMessageTaskDetailExportReq.Size(m)
}
func (m *DiscordMessageTaskDetailExportReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DiscordMessageTaskDetailExportReq.DiscardUnknown(m)
}

var xxx_messageInfo_DiscordMessageTaskDetailExportReq proto.InternalMessageInfo

func (m *DiscordMessageTaskDetailExportReq) GetTaskId() uint64 {
	if m != nil {
		return m.TaskId
	}
	return 0
}

type DiscordNewCommuRecordAddReq struct {
	// 沟通日期 @gotags: validate:"required"
	CommuDate string `protobuf:"bytes,1,opt,name=commu_date,json=commuDate,proto3" json:"commu_date" validate:"required"`
	// 游戏 @gotags: validate:"required"
	Project string `protobuf:"bytes,2,opt,name=project,proto3" json:"project" validate:"required"`
	// 沟通问题 @gotags: validate:"required"
	Question string `protobuf:"bytes,3,opt,name=question,proto3" json:"question" validate:"required"`
	// 问题类型Id @gotags: validate:"required"
	CatId uint32 `protobuf:"varint,4,opt,name=cat_id,json=catId,proto3" json:"cat_id" validate:"required"`
	// 处理状态 @gotags: validate:"required"
	HandleStatus int32 `protobuf:"varint,5,opt,name=handle_status,json=handleStatus,proto3" json:"handle_status" validate:"required"`
	// 备注
	Remark string `protobuf:"bytes,6,opt,name=remark,proto3" json:"remark"`
	// 涉及对话信息 @gotags: validate:"required"
	MsgIds string `protobuf:"bytes,7,opt,name=msg_ids,json=msgIds,proto3" json:"msg_ids" validate:"required"`
	// 玩家dsc_user_id @gotags: validate:"required"
	DscUserId string `protobuf:"bytes,8,opt,name=dsc_user_id,json=dscUserId,proto3" json:"dsc_user_id" validate:"required"`
	// 玩家uid
	Uid int64 `protobuf:"varint,9,opt,name=uid,proto3" json:"uid"`
	// 玩家所在的服务器
	Sid string `protobuf:"bytes,10,opt,name=sid,proto3" json:"sid"`
	// 玩家昵称 @gotags: validate:"required"
	NickName string `protobuf:"bytes,11,opt,name=nick_name,json=nickName,proto3" json:"nick_name" validate:"required"`
	// 玩家累付金额
	PayAll float64 `protobuf:"fixed64,12,opt,name=pay_all,json=payAll,proto3" json:"pay_all"`
	// 类别:line or discord @gotags: validate:"required,oneof=1 2"
	CatType              uint32   `protobuf:"varint,13,opt,name=cat_type,json=catType,proto3" json:"cat_type" validate:"required,oneof=1 2"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *DiscordNewCommuRecordAddReq) Reset()         { *m = DiscordNewCommuRecordAddReq{} }
func (m *DiscordNewCommuRecordAddReq) String() string { return proto.CompactTextString(m) }
func (*DiscordNewCommuRecordAddReq) ProtoMessage()    {}
func (*DiscordNewCommuRecordAddReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_2f04d4c6ef946e57, []int{22}
}

func (m *DiscordNewCommuRecordAddReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DiscordNewCommuRecordAddReq.Unmarshal(m, b)
}
func (m *DiscordNewCommuRecordAddReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DiscordNewCommuRecordAddReq.Marshal(b, m, deterministic)
}
func (m *DiscordNewCommuRecordAddReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DiscordNewCommuRecordAddReq.Merge(m, src)
}
func (m *DiscordNewCommuRecordAddReq) XXX_Size() int {
	return xxx_messageInfo_DiscordNewCommuRecordAddReq.Size(m)
}
func (m *DiscordNewCommuRecordAddReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DiscordNewCommuRecordAddReq.DiscardUnknown(m)
}

var xxx_messageInfo_DiscordNewCommuRecordAddReq proto.InternalMessageInfo

func (m *DiscordNewCommuRecordAddReq) GetCommuDate() string {
	if m != nil {
		return m.CommuDate
	}
	return ""
}

func (m *DiscordNewCommuRecordAddReq) GetProject() string {
	if m != nil {
		return m.Project
	}
	return ""
}

func (m *DiscordNewCommuRecordAddReq) GetQuestion() string {
	if m != nil {
		return m.Question
	}
	return ""
}

func (m *DiscordNewCommuRecordAddReq) GetCatId() uint32 {
	if m != nil {
		return m.CatId
	}
	return 0
}

func (m *DiscordNewCommuRecordAddReq) GetHandleStatus() int32 {
	if m != nil {
		return m.HandleStatus
	}
	return 0
}

func (m *DiscordNewCommuRecordAddReq) GetRemark() string {
	if m != nil {
		return m.Remark
	}
	return ""
}

func (m *DiscordNewCommuRecordAddReq) GetMsgIds() string {
	if m != nil {
		return m.MsgIds
	}
	return ""
}

func (m *DiscordNewCommuRecordAddReq) GetDscUserId() string {
	if m != nil {
		return m.DscUserId
	}
	return ""
}

func (m *DiscordNewCommuRecordAddReq) GetUid() int64 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *DiscordNewCommuRecordAddReq) GetSid() string {
	if m != nil {
		return m.Sid
	}
	return ""
}

func (m *DiscordNewCommuRecordAddReq) GetNickName() string {
	if m != nil {
		return m.NickName
	}
	return ""
}

func (m *DiscordNewCommuRecordAddReq) GetPayAll() float64 {
	if m != nil {
		return m.PayAll
	}
	return 0
}

func (m *DiscordNewCommuRecordAddReq) GetCatType() uint32 {
	if m != nil {
		return m.CatType
	}
	return 0
}

type DiscordNewCommuRecordEditReq struct {
	// @gotags: validate:"required"
	Id                   int64    `protobuf:"varint,1,opt,name=id,proto3" json:"id" validate:"required"`
	CommuDate            string   `protobuf:"bytes,2,opt,name=commu_date,json=commuDate,proto3" json:"commu_date"`
	Question             string   `protobuf:"bytes,3,opt,name=question,proto3" json:"question"`
	CatId                uint32   `protobuf:"varint,4,opt,name=cat_id,json=catId,proto3" json:"cat_id"`
	HandleStatus         int32    `protobuf:"varint,5,opt,name=handle_status,json=handleStatus,proto3" json:"handle_status"`
	Remark               string   `protobuf:"bytes,6,opt,name=remark,proto3" json:"remark"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *DiscordNewCommuRecordEditReq) Reset()         { *m = DiscordNewCommuRecordEditReq{} }
func (m *DiscordNewCommuRecordEditReq) String() string { return proto.CompactTextString(m) }
func (*DiscordNewCommuRecordEditReq) ProtoMessage()    {}
func (*DiscordNewCommuRecordEditReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_2f04d4c6ef946e57, []int{23}
}

func (m *DiscordNewCommuRecordEditReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DiscordNewCommuRecordEditReq.Unmarshal(m, b)
}
func (m *DiscordNewCommuRecordEditReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DiscordNewCommuRecordEditReq.Marshal(b, m, deterministic)
}
func (m *DiscordNewCommuRecordEditReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DiscordNewCommuRecordEditReq.Merge(m, src)
}
func (m *DiscordNewCommuRecordEditReq) XXX_Size() int {
	return xxx_messageInfo_DiscordNewCommuRecordEditReq.Size(m)
}
func (m *DiscordNewCommuRecordEditReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DiscordNewCommuRecordEditReq.DiscardUnknown(m)
}

var xxx_messageInfo_DiscordNewCommuRecordEditReq proto.InternalMessageInfo

func (m *DiscordNewCommuRecordEditReq) GetId() int64 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *DiscordNewCommuRecordEditReq) GetCommuDate() string {
	if m != nil {
		return m.CommuDate
	}
	return ""
}

func (m *DiscordNewCommuRecordEditReq) GetQuestion() string {
	if m != nil {
		return m.Question
	}
	return ""
}

func (m *DiscordNewCommuRecordEditReq) GetCatId() uint32 {
	if m != nil {
		return m.CatId
	}
	return 0
}

func (m *DiscordNewCommuRecordEditReq) GetHandleStatus() int32 {
	if m != nil {
		return m.HandleStatus
	}
	return 0
}

func (m *DiscordNewCommuRecordEditReq) GetRemark() string {
	if m != nil {
		return m.Remark
	}
	return ""
}

type DiscordNewCommuRecordListReq struct {
	Project   string   `protobuf:"bytes,1,opt,name=project,proto3" json:"project"`
	CommuDate []string `protobuf:"bytes,2,rep,name=commu_date,json=commuDate,proto3" json:"commu_date"`
	Operator  []string `protobuf:"bytes,3,rep,name=operator,proto3" json:"operator"`
	Uid       int64    `protobuf:"varint,4,opt,name=uid,proto3" json:"uid"`
	// @gotags: validate:"required"
	Page uint32 `protobuf:"varint,5,opt,name=page,proto3" json:"page" validate:"required"`
	// @gotags: validate:"required"
	PageSize     uint32  `protobuf:"varint,6,opt,name=page_size,json=pageSize,proto3" json:"page_size" validate:"required"`
	CatId        uint32  `protobuf:"varint,7,opt,name=cat_id,json=catId,proto3" json:"cat_id"`
	HandleStatus []int32 `protobuf:"varint,8,rep,packed,name=handle_status,json=handleStatus,proto3" json:"handle_status"`
	// 类别:line or discord @gotags: validate:"required,oneof=1 2"
	CatType              uint32   `protobuf:"varint,13,opt,name=cat_type,json=catType,proto3" json:"cat_type" validate:"required,oneof=1 2"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *DiscordNewCommuRecordListReq) Reset()         { *m = DiscordNewCommuRecordListReq{} }
func (m *DiscordNewCommuRecordListReq) String() string { return proto.CompactTextString(m) }
func (*DiscordNewCommuRecordListReq) ProtoMessage()    {}
func (*DiscordNewCommuRecordListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_2f04d4c6ef946e57, []int{24}
}

func (m *DiscordNewCommuRecordListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DiscordNewCommuRecordListReq.Unmarshal(m, b)
}
func (m *DiscordNewCommuRecordListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DiscordNewCommuRecordListReq.Marshal(b, m, deterministic)
}
func (m *DiscordNewCommuRecordListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DiscordNewCommuRecordListReq.Merge(m, src)
}
func (m *DiscordNewCommuRecordListReq) XXX_Size() int {
	return xxx_messageInfo_DiscordNewCommuRecordListReq.Size(m)
}
func (m *DiscordNewCommuRecordListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DiscordNewCommuRecordListReq.DiscardUnknown(m)
}

var xxx_messageInfo_DiscordNewCommuRecordListReq proto.InternalMessageInfo

func (m *DiscordNewCommuRecordListReq) GetProject() string {
	if m != nil {
		return m.Project
	}
	return ""
}

func (m *DiscordNewCommuRecordListReq) GetCommuDate() []string {
	if m != nil {
		return m.CommuDate
	}
	return nil
}

func (m *DiscordNewCommuRecordListReq) GetOperator() []string {
	if m != nil {
		return m.Operator
	}
	return nil
}

func (m *DiscordNewCommuRecordListReq) GetUid() int64 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *DiscordNewCommuRecordListReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *DiscordNewCommuRecordListReq) GetPageSize() uint32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *DiscordNewCommuRecordListReq) GetCatId() uint32 {
	if m != nil {
		return m.CatId
	}
	return 0
}

func (m *DiscordNewCommuRecordListReq) GetHandleStatus() []int32 {
	if m != nil {
		return m.HandleStatus
	}
	return nil
}

func (m *DiscordNewCommuRecordListReq) GetCatType() uint32 {
	if m != nil {
		return m.CatType
	}
	return 0
}

type DiscordNewCommuRecordListResp struct {
	CurrentPage          uint32                                              `protobuf:"varint,1,opt,name=current_page,json=currentPage,proto3" json:"current_page"`
	PerPage              uint32                                              `protobuf:"varint,2,opt,name=per_page,json=perPage,proto3" json:"per_page"`
	Total                uint32                                              `protobuf:"varint,3,opt,name=total,proto3" json:"total"`
	Data                 []*DiscordNewCommuRecordListResp_DiscordCommuRecord `protobuf:"bytes,4,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}                                            `json:"-" gorm:"-"`
	XXX_unrecognized     []byte                                              `json:"-" gorm:"-"`
	XXX_sizecache        int32                                               `json:"-" gorm:"-"`
}

func (m *DiscordNewCommuRecordListResp) Reset()         { *m = DiscordNewCommuRecordListResp{} }
func (m *DiscordNewCommuRecordListResp) String() string { return proto.CompactTextString(m) }
func (*DiscordNewCommuRecordListResp) ProtoMessage()    {}
func (*DiscordNewCommuRecordListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_2f04d4c6ef946e57, []int{25}
}

func (m *DiscordNewCommuRecordListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DiscordNewCommuRecordListResp.Unmarshal(m, b)
}
func (m *DiscordNewCommuRecordListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DiscordNewCommuRecordListResp.Marshal(b, m, deterministic)
}
func (m *DiscordNewCommuRecordListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DiscordNewCommuRecordListResp.Merge(m, src)
}
func (m *DiscordNewCommuRecordListResp) XXX_Size() int {
	return xxx_messageInfo_DiscordNewCommuRecordListResp.Size(m)
}
func (m *DiscordNewCommuRecordListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DiscordNewCommuRecordListResp.DiscardUnknown(m)
}

var xxx_messageInfo_DiscordNewCommuRecordListResp proto.InternalMessageInfo

func (m *DiscordNewCommuRecordListResp) GetCurrentPage() uint32 {
	if m != nil {
		return m.CurrentPage
	}
	return 0
}

func (m *DiscordNewCommuRecordListResp) GetPerPage() uint32 {
	if m != nil {
		return m.PerPage
	}
	return 0
}

func (m *DiscordNewCommuRecordListResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *DiscordNewCommuRecordListResp) GetData() []*DiscordNewCommuRecordListResp_DiscordCommuRecord {
	if m != nil {
		return m.Data
	}
	return nil
}

type DiscordNewCommuRecordListResp_DialogueItem struct {
	Role                 string   `protobuf:"bytes,1,opt,name=role,proto3" json:"role"`
	Content              string   `protobuf:"bytes,2,opt,name=content,proto3" json:"content"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *DiscordNewCommuRecordListResp_DialogueItem) Reset() {
	*m = DiscordNewCommuRecordListResp_DialogueItem{}
}
func (m *DiscordNewCommuRecordListResp_DialogueItem) String() string {
	return proto.CompactTextString(m)
}
func (*DiscordNewCommuRecordListResp_DialogueItem) ProtoMessage() {}
func (*DiscordNewCommuRecordListResp_DialogueItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_2f04d4c6ef946e57, []int{25, 0}
}

func (m *DiscordNewCommuRecordListResp_DialogueItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DiscordNewCommuRecordListResp_DialogueItem.Unmarshal(m, b)
}
func (m *DiscordNewCommuRecordListResp_DialogueItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DiscordNewCommuRecordListResp_DialogueItem.Marshal(b, m, deterministic)
}
func (m *DiscordNewCommuRecordListResp_DialogueItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DiscordNewCommuRecordListResp_DialogueItem.Merge(m, src)
}
func (m *DiscordNewCommuRecordListResp_DialogueItem) XXX_Size() int {
	return xxx_messageInfo_DiscordNewCommuRecordListResp_DialogueItem.Size(m)
}
func (m *DiscordNewCommuRecordListResp_DialogueItem) XXX_DiscardUnknown() {
	xxx_messageInfo_DiscordNewCommuRecordListResp_DialogueItem.DiscardUnknown(m)
}

var xxx_messageInfo_DiscordNewCommuRecordListResp_DialogueItem proto.InternalMessageInfo

func (m *DiscordNewCommuRecordListResp_DialogueItem) GetRole() string {
	if m != nil {
		return m.Role
	}
	return ""
}

func (m *DiscordNewCommuRecordListResp_DialogueItem) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

type DiscordNewCommuRecordListResp_DiscordCommuRecord struct {
	CommuDate            string                                        `protobuf:"bytes,1,opt,name=commu_date,json=commuDate,proto3" json:"commu_date"`
	Project              string                                        `protobuf:"bytes,2,opt,name=project,proto3" json:"project"`
	Uid                  int64                                         `protobuf:"varint,3,opt,name=uid,proto3" json:"uid"`
	Sid                  string                                        `protobuf:"bytes,4,opt,name=sid,proto3" json:"sid"`
	NickName             string                                        `protobuf:"bytes,5,opt,name=nick_name,json=nickName,proto3" json:"nick_name"`
	PayAll               float64                                       `protobuf:"fixed64,6,opt,name=pay_all,json=payAll,proto3" json:"pay_all"`
	Question             string                                        `protobuf:"bytes,7,opt,name=question,proto3" json:"question"`
	QuestionType         int32                                         `protobuf:"varint,8,opt,name=question_type,json=questionType,proto3" json:"question_type"`
	HandleStatus         int32                                         `protobuf:"varint,9,opt,name=handle_status,json=handleStatus,proto3" json:"handle_status"`
	Remark               string                                        `protobuf:"bytes,10,opt,name=remark,proto3" json:"remark"`
	Dialogue             []*DiscordNewCommuRecordListResp_DialogueItem `protobuf:"bytes,11,rep,name=dialogue,proto3" json:"dialogue"`
	Operator             string                                        `protobuf:"bytes,12,opt,name=operator,proto3" json:"operator"`
	Id                   int64                                         `protobuf:"varint,13,opt,name=id,proto3" json:"id"`
	Maintainer           string                                        `protobuf:"bytes,14,opt,name=maintainer,proto3" json:"maintainer"`
	CatId                int32                                         `protobuf:"varint,15,opt,name=cat_id,json=catId,proto3" json:"cat_id"`
	Category             string                                        `protobuf:"bytes,16,opt,name=category,proto3" json:"category"`
	XXX_NoUnkeyedLiteral struct{}                                      `json:"-" gorm:"-"`
	XXX_unrecognized     []byte                                        `json:"-" gorm:"-"`
	XXX_sizecache        int32                                         `json:"-" gorm:"-"`
}

func (m *DiscordNewCommuRecordListResp_DiscordCommuRecord) Reset() {
	*m = DiscordNewCommuRecordListResp_DiscordCommuRecord{}
}
func (m *DiscordNewCommuRecordListResp_DiscordCommuRecord) String() string {
	return proto.CompactTextString(m)
}
func (*DiscordNewCommuRecordListResp_DiscordCommuRecord) ProtoMessage() {}
func (*DiscordNewCommuRecordListResp_DiscordCommuRecord) Descriptor() ([]byte, []int) {
	return fileDescriptor_2f04d4c6ef946e57, []int{25, 1}
}

func (m *DiscordNewCommuRecordListResp_DiscordCommuRecord) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DiscordNewCommuRecordListResp_DiscordCommuRecord.Unmarshal(m, b)
}
func (m *DiscordNewCommuRecordListResp_DiscordCommuRecord) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DiscordNewCommuRecordListResp_DiscordCommuRecord.Marshal(b, m, deterministic)
}
func (m *DiscordNewCommuRecordListResp_DiscordCommuRecord) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DiscordNewCommuRecordListResp_DiscordCommuRecord.Merge(m, src)
}
func (m *DiscordNewCommuRecordListResp_DiscordCommuRecord) XXX_Size() int {
	return xxx_messageInfo_DiscordNewCommuRecordListResp_DiscordCommuRecord.Size(m)
}
func (m *DiscordNewCommuRecordListResp_DiscordCommuRecord) XXX_DiscardUnknown() {
	xxx_messageInfo_DiscordNewCommuRecordListResp_DiscordCommuRecord.DiscardUnknown(m)
}

var xxx_messageInfo_DiscordNewCommuRecordListResp_DiscordCommuRecord proto.InternalMessageInfo

func (m *DiscordNewCommuRecordListResp_DiscordCommuRecord) GetCommuDate() string {
	if m != nil {
		return m.CommuDate
	}
	return ""
}

func (m *DiscordNewCommuRecordListResp_DiscordCommuRecord) GetProject() string {
	if m != nil {
		return m.Project
	}
	return ""
}

func (m *DiscordNewCommuRecordListResp_DiscordCommuRecord) GetUid() int64 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *DiscordNewCommuRecordListResp_DiscordCommuRecord) GetSid() string {
	if m != nil {
		return m.Sid
	}
	return ""
}

func (m *DiscordNewCommuRecordListResp_DiscordCommuRecord) GetNickName() string {
	if m != nil {
		return m.NickName
	}
	return ""
}

func (m *DiscordNewCommuRecordListResp_DiscordCommuRecord) GetPayAll() float64 {
	if m != nil {
		return m.PayAll
	}
	return 0
}

func (m *DiscordNewCommuRecordListResp_DiscordCommuRecord) GetQuestion() string {
	if m != nil {
		return m.Question
	}
	return ""
}

func (m *DiscordNewCommuRecordListResp_DiscordCommuRecord) GetQuestionType() int32 {
	if m != nil {
		return m.QuestionType
	}
	return 0
}

func (m *DiscordNewCommuRecordListResp_DiscordCommuRecord) GetHandleStatus() int32 {
	if m != nil {
		return m.HandleStatus
	}
	return 0
}

func (m *DiscordNewCommuRecordListResp_DiscordCommuRecord) GetRemark() string {
	if m != nil {
		return m.Remark
	}
	return ""
}

func (m *DiscordNewCommuRecordListResp_DiscordCommuRecord) GetDialogue() []*DiscordNewCommuRecordListResp_DialogueItem {
	if m != nil {
		return m.Dialogue
	}
	return nil
}

func (m *DiscordNewCommuRecordListResp_DiscordCommuRecord) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

func (m *DiscordNewCommuRecordListResp_DiscordCommuRecord) GetId() int64 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *DiscordNewCommuRecordListResp_DiscordCommuRecord) GetMaintainer() string {
	if m != nil {
		return m.Maintainer
	}
	return ""
}

func (m *DiscordNewCommuRecordListResp_DiscordCommuRecord) GetCatId() int32 {
	if m != nil {
		return m.CatId
	}
	return 0
}

func (m *DiscordNewCommuRecordListResp_DiscordCommuRecord) GetCategory() string {
	if m != nil {
		return m.Category
	}
	return ""
}

type DiscordPublicTagReq struct {
	// @gotags: validate:"required"
	DscUserIdList        []string `protobuf:"bytes,1,rep,name=dsc_user_id_list,json=dscUserIdList,proto3" json:"dsc_user_id_list" validate:"required"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *DiscordPublicTagReq) Reset()         { *m = DiscordPublicTagReq{} }
func (m *DiscordPublicTagReq) String() string { return proto.CompactTextString(m) }
func (*DiscordPublicTagReq) ProtoMessage()    {}
func (*DiscordPublicTagReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_2f04d4c6ef946e57, []int{26}
}

func (m *DiscordPublicTagReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DiscordPublicTagReq.Unmarshal(m, b)
}
func (m *DiscordPublicTagReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DiscordPublicTagReq.Marshal(b, m, deterministic)
}
func (m *DiscordPublicTagReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DiscordPublicTagReq.Merge(m, src)
}
func (m *DiscordPublicTagReq) XXX_Size() int {
	return xxx_messageInfo_DiscordPublicTagReq.Size(m)
}
func (m *DiscordPublicTagReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DiscordPublicTagReq.DiscardUnknown(m)
}

var xxx_messageInfo_DiscordPublicTagReq proto.InternalMessageInfo

func (m *DiscordPublicTagReq) GetDscUserIdList() []string {
	if m != nil {
		return m.DscUserIdList
	}
	return nil
}

type DiscordPublicTagResp struct {
	Tags                 []*DiscordPublicTagResp_TagInfo `protobuf:"bytes,1,rep,name=tags,proto3" json:"tags"`
	XXX_NoUnkeyedLiteral struct{}                        `json:"-" gorm:"-"`
	XXX_unrecognized     []byte                          `json:"-" gorm:"-"`
	XXX_sizecache        int32                           `json:"-" gorm:"-"`
}

func (m *DiscordPublicTagResp) Reset()         { *m = DiscordPublicTagResp{} }
func (m *DiscordPublicTagResp) String() string { return proto.CompactTextString(m) }
func (*DiscordPublicTagResp) ProtoMessage()    {}
func (*DiscordPublicTagResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_2f04d4c6ef946e57, []int{27}
}

func (m *DiscordPublicTagResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DiscordPublicTagResp.Unmarshal(m, b)
}
func (m *DiscordPublicTagResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DiscordPublicTagResp.Marshal(b, m, deterministic)
}
func (m *DiscordPublicTagResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DiscordPublicTagResp.Merge(m, src)
}
func (m *DiscordPublicTagResp) XXX_Size() int {
	return xxx_messageInfo_DiscordPublicTagResp.Size(m)
}
func (m *DiscordPublicTagResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DiscordPublicTagResp.DiscardUnknown(m)
}

var xxx_messageInfo_DiscordPublicTagResp proto.InternalMessageInfo

func (m *DiscordPublicTagResp) GetTags() []*DiscordPublicTagResp_TagInfo {
	if m != nil {
		return m.Tags
	}
	return nil
}

type DiscordPublicTagResp_TagInfo struct {
	// 标签ID
	TagId uint32 `protobuf:"varint,1,opt,name=tag_id,json=tagId,proto3" json:"tag_id"`
	// 标签名称
	TagName              string   `protobuf:"bytes,2,opt,name=tag_name,json=tagName,proto3" json:"tag_name"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *DiscordPublicTagResp_TagInfo) Reset()         { *m = DiscordPublicTagResp_TagInfo{} }
func (m *DiscordPublicTagResp_TagInfo) String() string { return proto.CompactTextString(m) }
func (*DiscordPublicTagResp_TagInfo) ProtoMessage()    {}
func (*DiscordPublicTagResp_TagInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_2f04d4c6ef946e57, []int{27, 0}
}

func (m *DiscordPublicTagResp_TagInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DiscordPublicTagResp_TagInfo.Unmarshal(m, b)
}
func (m *DiscordPublicTagResp_TagInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DiscordPublicTagResp_TagInfo.Marshal(b, m, deterministic)
}
func (m *DiscordPublicTagResp_TagInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DiscordPublicTagResp_TagInfo.Merge(m, src)
}
func (m *DiscordPublicTagResp_TagInfo) XXX_Size() int {
	return xxx_messageInfo_DiscordPublicTagResp_TagInfo.Size(m)
}
func (m *DiscordPublicTagResp_TagInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_DiscordPublicTagResp_TagInfo.DiscardUnknown(m)
}

var xxx_messageInfo_DiscordPublicTagResp_TagInfo proto.InternalMessageInfo

func (m *DiscordPublicTagResp_TagInfo) GetTagId() uint32 {
	if m != nil {
		return m.TagId
	}
	return 0
}

func (m *DiscordPublicTagResp_TagInfo) GetTagName() string {
	if m != nil {
		return m.TagName
	}
	return ""
}

// 批量删除DC标签
type DiscordTagBatchDelete struct {
	// 待删除dscuserID @gotags: validate:"required"
	DscUserIdList []string `protobuf:"bytes,1,rep,name=dsc_user_id_list,json=dscUserIdList,proto3" json:"dsc_user_id_list" validate:"required"`
	// 待删除标签ID @gotags: validate:"required"
	TagIds []uint32 `protobuf:"varint,2,rep,packed,name=tag_ids,json=tagIds,proto3" json:"tag_ids" validate:"required"`
	// 游戏 @gotags: validate:"required"
	Project              string   `protobuf:"bytes,3,opt,name=project,proto3" json:"project" validate:"required"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *DiscordTagBatchDelete) Reset()         { *m = DiscordTagBatchDelete{} }
func (m *DiscordTagBatchDelete) String() string { return proto.CompactTextString(m) }
func (*DiscordTagBatchDelete) ProtoMessage()    {}
func (*DiscordTagBatchDelete) Descriptor() ([]byte, []int) {
	return fileDescriptor_2f04d4c6ef946e57, []int{28}
}

func (m *DiscordTagBatchDelete) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DiscordTagBatchDelete.Unmarshal(m, b)
}
func (m *DiscordTagBatchDelete) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DiscordTagBatchDelete.Marshal(b, m, deterministic)
}
func (m *DiscordTagBatchDelete) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DiscordTagBatchDelete.Merge(m, src)
}
func (m *DiscordTagBatchDelete) XXX_Size() int {
	return xxx_messageInfo_DiscordTagBatchDelete.Size(m)
}
func (m *DiscordTagBatchDelete) XXX_DiscardUnknown() {
	xxx_messageInfo_DiscordTagBatchDelete.DiscardUnknown(m)
}

var xxx_messageInfo_DiscordTagBatchDelete proto.InternalMessageInfo

func (m *DiscordTagBatchDelete) GetDscUserIdList() []string {
	if m != nil {
		return m.DscUserIdList
	}
	return nil
}

func (m *DiscordTagBatchDelete) GetTagIds() []uint32 {
	if m != nil {
		return m.TagIds
	}
	return nil
}

func (m *DiscordTagBatchDelete) GetProject() string {
	if m != nil {
		return m.Project
	}
	return ""
}

func init() {
	proto.RegisterType((*PortraitInfoReq)(nil), "pb.PortraitInfoReq")
	proto.RegisterType((*PortraitInfoResp)(nil), "pb.PortraitInfoResp")
	proto.RegisterType((*PortraitInfoResp_LabelDetail)(nil), "pb.PortraitInfoResp.LabelDetail")
	proto.RegisterType((*PortraitInfoResp_NewLabelDetail)(nil), "pb.PortraitInfoResp.NewLabelDetail")
	proto.RegisterType((*PortraitInfoResp_TicketInfo)(nil), "pb.PortraitInfoResp.TicketInfo")
	proto.RegisterType((*PortraitEditReq)(nil), "pb.PortraitEditReq")
	proto.RegisterType((*MaintainConfigDelReq)(nil), "pb.MaintainConfigDelReq")
	proto.RegisterType((*MaintainConfigListReq)(nil), "pb.MaintainConfigListReq")
	proto.RegisterType((*MaintainConfigListResp)(nil), "pb.MaintainConfigListResp")
	proto.RegisterType((*MaintainConfigListResp_MaintainConfigInfo)(nil), "pb.MaintainConfigListResp.MaintainConfigInfo")
	proto.RegisterType((*MaintainConfigNewReq)(nil), "pb.MaintainConfigNewReq")
	proto.RegisterType((*MaintainConfigEditReq)(nil), "pb.MaintainConfigEditReq")
	proto.RegisterType((*DiscordPlayerAccountsReq)(nil), "pb.DiscordPlayerAccountsReq")
	proto.RegisterType((*DiscordPlayerAccountsResp)(nil), "pb.DiscordPlayerAccountsResp")
	proto.RegisterType((*DiscordCommuRecordAddReq)(nil), "pb.DiscordCommuRecordAddReq")
	proto.RegisterType((*DiscordCommuRecordListReq)(nil), "pb.DiscordCommuRecordListReq")
	proto.RegisterType((*DiscordCommuRecordListResp)(nil), "pb.DiscordCommuRecordListResp")
	proto.RegisterType((*DiscordCommuRecordListResp_DialogueItem)(nil), "pb.DiscordCommuRecordListResp.DialogueItem")
	proto.RegisterType((*DiscordCommuRecordListResp_DiscordCommuRecord)(nil), "pb.DiscordCommuRecordListResp.DiscordCommuRecord")
	proto.RegisterType((*DiscordCommuRecordEditReq)(nil), "pb.DiscordCommuRecordEditReq")
	proto.RegisterType((*DiscordTagAddReq)(nil), "pb.DiscordTagAddReq")
	proto.RegisterType((*DiscordTagEditReq)(nil), "pb.DiscordTagEditReq")
	proto.RegisterType((*DiscordTagListReq)(nil), "pb.DiscordTagListReq")
	proto.RegisterType((*DiscordTagListResp)(nil), "pb.DiscordTagListResp")
	proto.RegisterType((*DiscordTagListResp_TagDetail)(nil), "pb.DiscordTagListResp.TagDetail")
	proto.RegisterType((*DiscordBatchTagReq)(nil), "pb.DiscordBatchTagReq")
	proto.RegisterType((*DiscordMessageTaskListReq)(nil), "pb.DiscordMessageTaskListReq")
	proto.RegisterType((*DiscordMessageTaskListResp)(nil), "pb.DiscordMessageTaskListResp")
	proto.RegisterType((*DiscordMessageTaskListResp_ReplyContent)(nil), "pb.DiscordMessageTaskListResp.ReplyContent")
	proto.RegisterType((*DiscordMessageTaskListResp_Count)(nil), "pb.DiscordMessageTaskListResp.Count")
	proto.RegisterType((*DiscordMessageTaskListResp_DiscordMessageRecord)(nil), "pb.DiscordMessageTaskListResp.DiscordMessageRecord")
	proto.RegisterType((*DiscordMessageTaskDetailExportReq)(nil), "pb.DiscordMessageTaskDetailExportReq")
	proto.RegisterType((*DiscordNewCommuRecordAddReq)(nil), "pb.DiscordNewCommuRecordAddReq")
	proto.RegisterType((*DiscordNewCommuRecordEditReq)(nil), "pb.DiscordNewCommuRecordEditReq")
	proto.RegisterType((*DiscordNewCommuRecordListReq)(nil), "pb.DiscordNewCommuRecordListReq")
	proto.RegisterType((*DiscordNewCommuRecordListResp)(nil), "pb.DiscordNewCommuRecordListResp")
	proto.RegisterType((*DiscordNewCommuRecordListResp_DialogueItem)(nil), "pb.DiscordNewCommuRecordListResp.DialogueItem")
	proto.RegisterType((*DiscordNewCommuRecordListResp_DiscordCommuRecord)(nil), "pb.DiscordNewCommuRecordListResp.DiscordCommuRecord")
	proto.RegisterType((*DiscordPublicTagReq)(nil), "pb.DiscordPublicTagReq")
	proto.RegisterType((*DiscordPublicTagResp)(nil), "pb.DiscordPublicTagResp")
	proto.RegisterType((*DiscordPublicTagResp_TagInfo)(nil), "pb.DiscordPublicTagResp.TagInfo")
	proto.RegisterType((*DiscordTagBatchDelete)(nil), "pb.DiscordTagBatchDelete")
}

func init() {
	proto.RegisterFile("dsc.proto", fileDescriptor_2f04d4c6ef946e57)
}

var fileDescriptor_2f04d4c6ef946e57 = []byte{
	// 1943 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xec, 0x59, 0x4f, 0x8f, 0xe4, 0x46,
	0x15, 0x97, 0xed, 0xb6, 0xdb, 0xfd, 0xba, 0x7b, 0x77, 0x30, 0xbb, 0x9b, 0xde, 0x5e, 0x92, 0xcc,
	0x3a, 0x48, 0x59, 0x09, 0x31, 0x12, 0x49, 0xc4, 0x81, 0x04, 0xc8, 0xec, 0xcc, 0x2a, 0x0c, 0xda,
	0x2c, 0x23, 0xef, 0xe4, 0x82, 0x84, 0x4c, 0x8d, 0x5d, 0xe3, 0x29, 0xc6, 0x6d, 0x7b, 0xab, 0xaa,
	0x77, 0x98, 0x7c, 0x03, 0x0e, 0x5c, 0x11, 0x9f, 0x80, 0x03, 0x12, 0x97, 0x20, 0x71, 0x40, 0xe2,
	0xce, 0x81, 0x2b, 0xdf, 0x00, 0x38, 0x22, 0x24, 0x38, 0x71, 0x42, 0xf5, 0xc7, 0x6e, 0xff, 0x69,
	0x77, 0xcf, 0x22, 0x06, 0x10, 0xe2, 0xd4, 0x55, 0xcf, 0x55, 0xe5, 0xf7, 0xef, 0xf7, 0x7e, 0xaf,
	0xdc, 0x30, 0x8a, 0x59, 0xb4, 0x57, 0xd0, 0x9c, 0xe7, 0x9e, 0x59, 0x9c, 0xfa, 0x27, 0x70, 0xfb,
	0x38, 0xa7, 0x9c, 0x22, 0xc2, 0x8f, 0xb2, 0xb3, 0x3c, 0xc0, 0x2f, 0xbc, 0x37, 0x60, 0x1c, 0xb3,
	0x28, 0x5c, 0x32, 0x4c, 0x43, 0x12, 0xcf, 0x8c, 0x5d, 0xe3, 0xd1, 0x28, 0x10, 0xdb, 0x3e, 0x61,
	0x98, 0x1e, 0xc5, 0xde, 0x43, 0x98, 0x24, 0x68, 0x81, 0xc3, 0x82, 0xe6, 0x3f, 0xc0, 0x11, 0x9f,
	0x99, 0x72, 0xc1, 0x58, 0xc8, 0x8e, 0x95, 0xc8, 0xff, 0x95, 0x03, 0x3b, 0xcd, 0x63, 0x59, 0xe1,
	0xdd, 0x02, 0x53, 0x1f, 0x37, 0x08, 0x4c, 0x12, 0x7b, 0x5f, 0x05, 0x3b, 0x45, 0xa7, 0x38, 0x9d,
	0x99, 0xbb, 0xd6, 0xa3, 0xf1, 0x3b, 0xbb, 0x7b, 0xc5, 0xe9, 0x5e, 0x7b, 0xd3, 0xde, 0x53, 0xb1,
	0xe2, 0x10, 0x73, 0x44, 0xd2, 0x40, 0x2d, 0xf7, 0xee, 0x81, 0x93, 0xe0, 0x2c, 0xc6, 0x74, 0x66,
	0xed, 0x1a, 0x8f, 0xa6, 0x81, 0x9e, 0x79, 0x73, 0x70, 0x4f, 0x09, 0xe5, 0xe7, 0x31, 0xba, 0x9a,
	0x0d, 0xa4, 0x4e, 0xd5, 0x5c, 0xec, 0x89, 0x10, 0xc5, 0x98, 0xce, 0x6c, 0xf9, 0x44, 0xcf, 0xbc,
	0xb7, 0xe1, 0x36, 0x8e, 0x97, 0x11, 0xe2, 0x24, 0xcf, 0xc2, 0x14, 0xbf, 0xc4, 0xe9, 0xcc, 0x91,
	0x87, 0xde, 0xaa, 0xc4, 0x4f, 0x85, 0xd4, 0x7b, 0x0b, 0xa6, 0x0b, 0x44, 0x29, 0xc1, 0x71, 0xc8,
	0x38, 0xe2, 0x78, 0x36, 0x94, 0xcb, 0x26, 0x5a, 0xf8, 0x5c, 0xc8, 0xc4, 0x69, 0x67, 0x98, 0x72,
	0x92, 0x12, 0x7e, 0xa5, 0x97, 0xb9, 0xea, 0xb4, 0x4a, 0xac, 0x16, 0xde, 0x03, 0x87, 0xe2, 0x05,
	0xa2, 0x17, 0xb3, 0x91, 0x52, 0x47, 0xcd, 0xda, 0xae, 0x87, 0x6d, 0xae, 0x1f, 0x77, 0x5c, 0xef,
	0x7d, 0x08, 0xa3, 0x0c, 0x5f, 0x86, 0xca, 0xb3, 0x13, 0xe9, 0xd9, 0xb7, 0xd6, 0x7a, 0xf6, 0x19,
	0xbe, 0xac, 0x3b, 0xd7, 0xcd, 0xf4, 0xdc, 0xfb, 0x10, 0xc6, 0x9c, 0x44, 0x17, 0x98, 0x87, 0x24,
	0x3b, 0xcb, 0x67, 0x53, 0x79, 0xc6, 0x9b, 0x6b, 0xcf, 0x38, 0x91, 0xeb, 0xe4, 0x14, 0x78, 0x35,
	0x9e, 0x7f, 0x13, 0xc6, 0xb5, 0xa3, 0xbd, 0xbb, 0xe0, 0x70, 0x94, 0x84, 0x55, 0xf0, 0x6d, 0x8e,
	0x92, 0xa3, 0xd8, 0xbb, 0x0f, 0xae, 0x10, 0x67, 0x68, 0x81, 0x75, 0x0e, 0x0d, 0x39, 0x4a, 0x9e,
	0xa1, 0x05, 0x9e, 0x3f, 0x86, 0x5b, 0x4d, 0xf5, 0xb6, 0x9c, 0x11, 0x63, 0x16, 0xd5, 0xce, 0x38,
	0xc4, 0x2c, 0x9a, 0x7f, 0x66, 0x00, 0xac, 0xf4, 0xf3, 0x66, 0x30, 0x2c, 0xbd, 0xa6, 0x32, 0xba,
	0x9c, 0x7a, 0x0f, 0x60, 0x54, 0xda, 0x1b, 0xcb, 0x43, 0x06, 0x81, 0xab, 0x8d, 0x89, 0x45, 0xa4,
	0x62, 0xa9, 0x81, 0x4c, 0xb6, 0x51, 0xa0, 0x67, 0x22, 0xd9, 0x28, 0x8e, 0xce, 0x11, 0x4d, 0xb0,
	0x4c, 0x36, 0x23, 0xa8, 0xe6, 0x62, 0x8f, 0x08, 0xfe, 0x92, 0xc9, 0x64, 0x9b, 0x06, 0x7a, 0x26,
	0xa2, 0x77, 0x89, 0x08, 0x27, 0x59, 0x12, 0x72, 0xb2, 0xc0, 0x32, 0xd3, 0x46, 0xc1, 0x58, 0xcb,
	0x4e, 0xc8, 0x02, 0xfb, 0xbf, 0x37, 0x57, 0x78, 0x7c, 0x12, 0x13, 0x2e, 0xf0, 0xd8, 0xc6, 0xcd,
	0x76, 0xfc, 0x79, 0x77, 0x4a, 0x68, 0x29, 0xa5, 0x3b, 0xc0, 0x19, 0xf4, 0x02, 0xc7, 0xee, 0x05,
	0x8e, 0xb3, 0x0d, 0x38, 0xc3, 0xeb, 0x01, 0xc7, 0xbd, 0x1e, 0x70, 0x46, 0x5b, 0x80, 0x03, 0x9b,
	0x80, 0x33, 0x6e, 0x01, 0xc7, 0x27, 0x70, 0xe7, 0x63, 0x44, 0x32, 0x8e, 0x48, 0x76, 0x90, 0x67,
	0x67, 0x24, 0x39, 0xc4, 0xe9, 0x3a, 0xdf, 0xb6, 0xce, 0x31, 0xb7, 0x01, 0xd0, 0xea, 0xd6, 0xbe,
	0x3f, 0x1a, 0x70, 0xb7, 0xf9, 0xae, 0xa7, 0x84, 0xc9, 0x40, 0xb6, 0x37, 0x1b, 0xbb, 0x56, 0x3b,
	0x70, 0x6f, 0x00, 0x2c, 0xf4, 0x5e, 0x4c, 0x65, 0x61, 0x1c, 0x05, 0x35, 0x89, 0xc8, 0xd5, 0x8c,
	0x44, 0x17, 0x0a, 0x34, 0x96, 0x7c, 0xec, 0x0a, 0x81, 0x40, 0x4d, 0x5b, 0xf9, 0x41, 0x5b, 0xf9,
	0x07, 0x30, 0x7a, 0x49, 0x0a, 0xed, 0x5f, 0x95, 0x9a, 0xee, 0x4b, 0x52, 0x28, 0xcf, 0x7a, 0x30,
	0x28, 0x50, 0x82, 0x75, 0xf9, 0x93, 0x63, 0xb1, 0x41, 0xfc, 0x86, 0x8c, 0x7c, 0x5a, 0x16, 0x3c,
	0x57, 0x08, 0x9e, 0x93, 0x4f, 0xb1, 0xff, 0xd3, 0x01, 0xdc, 0x5b, 0x67, 0x27, 0x2b, 0x84, 0xa1,
	0xd1, 0x92, 0x52, 0x9c, 0xf1, 0x50, 0x9e, 0x69, 0xc8, 0xad, 0x63, 0x2d, 0x3b, 0x16, 0x47, 0xdf,
	0x07, 0xb7, 0xc0, 0x54, 0x3d, 0x36, 0xe5, 0xe3, 0x61, 0x81, 0xa9, 0x7c, 0x74, 0x07, 0x6c, 0x9e,
	0x73, 0x94, 0xea, 0xf2, 0xae, 0x26, 0xde, 0x3e, 0x0c, 0x62, 0xc4, 0xd1, 0x6c, 0x20, 0xcb, 0xd1,
	0x97, 0x45, 0x39, 0x5a, 0xff, 0xf6, 0x96, 0x58, 0x16, 0x27, 0xb9, 0x75, 0xfe, 0x27, 0x13, 0xbc,
	0xee, 0xc3, 0x57, 0xce, 0x81, 0x56, 0x0c, 0x8c, 0x46, 0x0c, 0x3c, 0x18, 0x9c, 0x15, 0x95, 0xf3,
	0xe5, 0xd8, 0xdb, 0x01, 0x6b, 0x49, 0x62, 0xe9, 0xf1, 0x41, 0x20, 0x86, 0x42, 0xc2, 0x48, 0xac,
	0x21, 0x25, 0x86, 0xad, 0xc0, 0x0f, 0xe5, 0x83, 0x7a, 0xe0, 0xdb, 0xb9, 0xe3, 0x76, 0x41, 0x3f,
	0x07, 0x37, 0x2f, 0x30, 0x45, 0x3c, 0xa7, 0x9a, 0x56, 0xaa, 0x79, 0x33, 0xf4, 0xd0, 0x0a, 0xfd,
	0x9b, 0x30, 0x5e, 0x16, 0x31, 0xe2, 0x58, 0x95, 0x25, 0x05, 0x1e, 0x50, 0x22, 0x51, 0x95, 0x84,
	0x51, 0x29, 0xca, 0x92, 0xd9, 0x44, 0x19, 0x25, 0xc6, 0x8d, 0xa2, 0x31, 0x6d, 0x16, 0x0d, 0xff,
	0xe7, 0x46, 0x1b, 0x6e, 0xcf, 0xf0, 0xe5, 0x75, 0x5a, 0x8b, 0x86, 0x6b, 0xcd, 0x1e, 0xd7, 0x5a,
	0x35, 0xd7, 0x36, 0xdd, 0x36, 0xd8, 0xea, 0x36, 0xbb, 0x8b, 0xd7, 0x5f, 0x74, 0xf0, 0xda, 0x57,
	0x78, 0x4b, 0x05, 0xcc, 0x5e, 0x05, 0xac, 0x8e, 0x02, 0x3a, 0xf6, 0x83, 0x55, 0xec, 0x37, 0xa2,
	0xb0, 0xad, 0xaf, 0xd3, 0xd5, 0xf7, 0x7b, 0x30, 0x3b, 0x24, 0x2c, 0xca, 0x69, 0x7c, 0x9c, 0xa2,
	0x2b, 0x4c, 0xf7, 0xa3, 0x28, 0x5f, 0x66, 0x9c, 0xad, 0xaf, 0x30, 0x46, 0xb7, 0xc2, 0x6c, 0xcc,
	0x6e, 0xff, 0xeb, 0x70, 0xbf, 0xe7, 0x78, 0x56, 0x78, 0xbb, 0x30, 0xa9, 0x6d, 0x66, 0xba, 0x82,
	0x41, 0xb5, 0x9b, 0xf9, 0x7f, 0x30, 0x2b, 0xf5, 0x0e, 0xf2, 0xc5, 0x62, 0x19, 0x60, 0x31, 0xdc,
	0x8f, 0x63, 0xa1, 0xde, 0xeb, 0x00, 0x91, 0x10, 0x86, 0x22, 0xb3, 0xca, 0xe8, 0x4b, 0xc9, 0xa1,
	0x30, 0xbe, 0x46, 0xd1, 0x66, 0x93, 0xa2, 0xe7, 0xe0, 0xbe, 0x58, 0x62, 0x26, 0x58, 0xa5, 0x44,
	0x5c, 0x39, 0x17, 0x04, 0x53, 0x8e, 0x43, 0x7e, 0x55, 0x28, 0x3a, 0xb6, 0x83, 0x49, 0x29, 0x3c,
	0xb9, 0x2a, 0xb0, 0x58, 0x74, 0x8e, 0xb2, 0x38, 0xc5, 0x61, 0x8d, 0x99, 0xed, 0x60, 0xa2, 0x84,
	0xcf, 0x15, 0x3f, 0xaf, 0xc8, 0xc5, 0x69, 0x90, 0xcb, 0x6b, 0x30, 0x5c, 0xb0, 0x44, 0x1a, 0xac,
	0x80, 0xe9, 0x2c, 0x58, 0x72, 0x14, 0xb3, 0xb6, 0x2f, 0xdd, 0x76, 0x3a, 0xeb, 0xe0, 0x0b, 0x30,
	0x5a, 0x0d, 0xe0, 0xc3, 0x0a, 0xf8, 0x8d, 0x94, 0x1f, 0xb7, 0x52, 0xfe, 0x35, 0x18, 0x16, 0xe8,
	0x2a, 0x44, 0x69, 0x2a, 0xb1, 0x67, 0x04, 0x4e, 0x81, 0xae, 0xf6, 0xd3, 0xd4, 0xff, 0xa5, 0x51,
	0x85, 0xa9, 0xe6, 0xe6, 0x92, 0x68, 0x1a, 0xbd, 0x8e, 0x55, 0x77, 0x64, 0x33, 0x02, 0x8a, 0x5f,
	0x6a, 0x11, 0xa8, 0x97, 0x10, 0xcd, 0x2e, 0x55, 0x09, 0xa9, 0x65, 0xb2, 0x36, 0xa6, 0xa4, 0x0c,
	0xbb, 0x8f, 0x32, 0x9c, 0x16, 0x65, 0x7c, 0x66, 0xc3, 0xbc, 0x4f, 0xeb, 0x1b, 0xa2, 0x8d, 0x27,
	0x0d, 0xda, 0xf8, 0x8a, 0xa0, 0x8d, 0x7e, 0x0d, 0xd6, 0x3c, 0xd2, 0xd4, 0xf1, 0x01, 0x4c, 0x0e,
	0x09, 0x4a, 0xf3, 0x64, 0x89, 0x8f, 0x38, 0x5e, 0x08, 0xd3, 0x69, 0x9e, 0x96, 0x39, 0x2c, 0xc7,
	0xc2, 0xeb, 0x51, 0x9e, 0x71, 0x9c, 0x55, 0xe9, 0xab, 0xa7, 0xf3, 0x5f, 0x5b, 0xe0, 0x75, 0x8f,
	0xfe, 0xe7, 0xe1, 0xa0, 0x43, 0x61, 0x75, 0xf2, 0x6a, 0xd0, 0x93, 0x57, 0x76, 0x7f, 0x5e, 0x39,
	0xf5, 0xbc, 0x6a, 0x00, 0x6d, 0xb8, 0x0d, 0x68, 0xee, 0x75, 0x80, 0x36, 0xda, 0x08, 0xb4, 0x66,
	0x17, 0xf7, 0x11, 0xb8, 0xb1, 0xf6, 0xf2, 0x6c, 0x2c, 0x03, 0xf6, 0xa5, 0xad, 0x01, 0x5b, 0x05,
	0x25, 0xa8, 0x36, 0x37, 0xf2, 0x78, 0xd2, 0xa2, 0x42, 0x55, 0xd5, 0xa7, 0xd2, 0x77, 0x66, 0xa7,
	0x82, 0xdf, 0x6a, 0x57, 0x70, 0xff, 0xb7, 0x6b, 0xa1, 0xd6, 0xe5, 0x08, 0x75, 0x5a, 0x1b, 0x60,
	0x46, 0x07, 0x60, 0xff, 0xb9, 0x42, 0xe6, 0x7f, 0x1f, 0x76, 0xb4, 0x25, 0x27, 0x28, 0xd1, 0x35,
	0xb9, 0x7e, 0x0b, 0x33, 0x1a, 0xb7, 0xb0, 0x0d, 0x97, 0xab, 0xda, 0x15, 0xc7, 0x92, 0xef, 0xd7,
	0x33, 0xff, 0x05, 0x7c, 0x6e, 0xf5, 0x86, 0xae, 0x8f, 0x6c, 0xe9, 0xa3, 0xfe, 0x8b, 0x5f, 0xe3,
	0x95, 0x56, 0xdf, 0x2b, 0x07, 0x8d, 0x57, 0x9e, 0xd6, 0x5f, 0x59, 0x56, 0xc0, 0x0d, 0x56, 0xd5,
	0x73, 0xc3, 0x6c, 0xe5, 0x46, 0x9f, 0x59, 0x7f, 0x35, 0x2a, 0x00, 0x57, 0x2f, 0x61, 0x85, 0xf7,
	0x9e, 0x2e, 0x2e, 0xc6, 0xea, 0x03, 0x46, 0x77, 0xd5, 0xde, 0x89, 0x30, 0x40, 0xde, 0xb1, 0x55,
	0x2d, 0xf9, 0x99, 0x01, 0xa3, 0x4a, 0xf6, 0x2f, 0x72, 0x4e, 0xab, 0x85, 0x1b, 0x74, 0x5a, 0xb8,
	0xba, 0xd5, 0x76, 0xaf, 0xd5, 0x4e, 0xc3, 0x6a, 0x52, 0x19, 0xfd, 0x18, 0xf1, 0xe8, 0xfc, 0x04,
	0x25, 0xc2, 0xb5, 0x6f, 0xc3, 0x4e, 0x8d, 0xf4, 0xc2, 0x94, 0xb0, 0x92, 0x65, 0xa6, 0x15, 0xf3,
	0x09, 0xdb, 0x45, 0x4d, 0xe2, 0x28, 0xd1, 0x46, 0x88, 0x61, 0xbd, 0xa2, 0x59, 0x8d, 0x8a, 0xe6,
	0xff, 0x64, 0x05, 0xb2, 0x8f, 0x31, 0x63, 0x28, 0xc1, 0x27, 0x88, 0x5d, 0x6c, 0xe7, 0x33, 0x4f,
	0x46, 0xa0, 0x64, 0x32, 0x39, 0xde, 0x48, 0x62, 0xaf, 0x4c, 0x59, 0xbf, 0x5b, 0x51, 0x56, 0x47,
	0xb1, 0x1b, 0xa2, 0xac, 0x8f, 0x1a, 0x94, 0xf5, 0x6e, 0x2d, 0xab, 0xd6, 0x68, 0xd0, 0x7a, 0xd4,
	0x20, 0xad, 0x03, 0x98, 0x04, 0xb8, 0x48, 0xaf, 0x0e, 0x14, 0x0d, 0xd5, 0x09, 0xca, 0x6a, 0x10,
	0x94, 0xd0, 0xf1, 0x8c, 0xa4, 0x38, 0x5c, 0xd2, 0x54, 0xe7, 0xce, 0x50, 0xcc, 0x3f, 0xa1, 0xe9,
	0xfc, 0x3b, 0x60, 0x1f, 0x88, 0x06, 0x50, 0x54, 0x1e, 0xb6, 0x8c, 0x22, 0xcc, 0x58, 0x28, 0x3b,
	0x42, 0xc9, 0x0f, 0x76, 0x30, 0xd1, 0x42, 0xb5, 0xe8, 0x21, 0x4c, 0xce, 0x10, 0x49, 0x71, 0xac,
	0xd7, 0x28, 0x8a, 0x18, 0x2b, 0x99, 0x5c, 0x32, 0xff, 0xbb, 0x09, 0x77, 0xd6, 0x29, 0x2d, 0x48,
	0x89, 0x23, 0x76, 0xb1, 0xfa, 0xc6, 0xe3, 0x88, 0xe9, 0x51, 0xbc, 0x81, 0x08, 0x8f, 0x61, 0x4a,
	0x85, 0x85, 0x61, 0xdd, 0xae, 0x26, 0x6b, 0xac, 0xf3, 0x59, 0xdd, 0x2b, 0xc1, 0x84, 0xd6, 0x7d,
	0xd4, 0xfc, 0x76, 0x53, 0x61, 0x61, 0x15, 0x2a, 0x05, 0x11, 0x1d, 0xaa, 0xaf, 0x81, 0xbd, 0xf2,
	0xc5, 0xf8, 0x9d, 0x2f, 0x6e, 0x79, 0xaf, 0x74, 0x40, 0xa0, 0xb6, 0x6c, 0xbb, 0xae, 0x45, 0x14,
	0x0b, 0x38, 0x23, 0xae, 0x39, 0xd2, 0x55, 0x82, 0x7d, 0x2e, 0xb0, 0x7e, 0x46, 0x32, 0xc2, 0xce,
	0x71, 0x2c, 0x1e, 0xeb, 0xeb, 0x5a, 0x29, 0xda, 0x57, 0xd1, 0x44, 0x24, 0x95, 0x0d, 0xeb, 0x64,
	0xd7, 0x7a, 0x64, 0x05, 0x43, 0x31, 0x17, 0xed, 0xf9, 0x07, 0xf0, 0xb0, 0xab, 0x9f, 0xaa, 0x44,
	0x4f, 0x7e, 0x58, 0xe4, 0x54, 0xc2, 0xad, 0x2f, 0x10, 0xfe, 0xdf, 0x4c, 0x78, 0xa0, 0xb7, 0x3f,
	0xc3, 0x97, 0xff, 0xa6, 0xfe, 0xfe, 0x2e, 0x38, 0x11, 0xe2, 0xe5, 0x07, 0x8d, 0x69, 0x60, 0x47,
	0x88, 0x1f, 0xc5, 0xff, 0x6b, 0x1d, 0xbd, 0x08, 0x9a, 0x30, 0x53, 0x12, 0xff, 0x54, 0x95, 0x89,
	0x08, 0x71, 0xc1, 0xf9, 0xfe, 0x6f, 0x0c, 0xf8, 0xc2, 0x5a, 0xb7, 0xdf, 0x40, 0x13, 0x72, 0x03,
	0xde, 0xf6, 0x7f, 0x6c, 0xf6, 0xe8, 0xbf, 0xb6, 0xbe, 0x1b, 0xff, 0x35, 0xf7, 0x95, 0x9a, 0x33,
	0x86, 0x1b, 0x9d, 0xe1, 0xee, 0x5a, 0x1d, 0x67, 0x6c, 0x88, 0xe7, 0x9f, 0x6d, 0x78, 0x7d, 0x83,
	0x3f, 0x6e, 0x88, 0x56, 0xbe, 0xd5, 0xa0, 0x95, 0xf7, 0x6a, 0xa5, 0x6a, 0xbd, 0x12, 0x37, 0x75,
	0x19, 0xfa, 0xcb, 0xff, 0x2f, 0x43, 0xd7, 0xba, 0x0c, 0x7d, 0xbb, 0x73, 0x19, 0xda, 0xbb, 0x4e,
	0xcc, 0x6e, 0xfa, 0x3e, 0x54, 0x03, 0xc5, 0x6d, 0x45, 0x85, 0x0a, 0x14, 0x73, 0x99, 0xef, 0x38,
	0xc9, 0xe9, 0xd5, 0x6c, 0x47, 0x33, 0x96, 0x9e, 0xfb, 0xdf, 0x80, 0xcf, 0x97, 0xdf, 0x94, 0x96,
	0xa7, 0x29, 0x89, 0x5e, 0xb1, 0x93, 0xf4, 0x7f, 0x64, 0x54, 0x2d, 0x43, 0xed, 0x00, 0xd5, 0x80,
	0x73, 0x94, 0xb0, 0x35, 0x0d, 0x78, 0x63, 0x9d, 0x68, 0xc1, 0xd5, 0x77, 0x60, 0xb1, 0x7a, 0xfe,
	0x3e, 0x0c, 0xb5, 0xa0, 0xf5, 0xb7, 0xd2, 0x74, 0xfb, 0x5f, 0x53, 0x3e, 0x83, 0xbb, 0xab, 0x1e,
	0x5f, 0xf6, 0xc5, 0x87, 0x38, 0xc5, 0xf2, 0x3f, 0x8c, 0x6b, 0xf6, 0xc5, 0x92, 0x5e, 0x15, 0xf9,
	0x88, 0x82, 0x36, 0x15, 0xf4, 0x2a, 0xc9, 0xa7, 0xb7, 0x3d, 0x7e, 0xec, 0x7c, 0x77, 0xb0, 0xf7,
	0x7e, 0x71, 0x7a, 0xea, 0xc8, 0x3f, 0x6e, 0xdf, 0xfd, 0x47, 0x00, 0x00, 0x00, 0xff, 0xff, 0xe9,
	0xbe, 0xa3, 0xc4, 0xc5, 0x1d, 0x00, 0x00,
}
