// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.14.0
// source: discord.proto

package pb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	DscApi_DscUserList_FullMethodName           = "/pb.DscApi/DscUserList"
	DscApi_DscUserDetail_FullMethodName         = "/pb.DscApi/DscUserDetail"
	DscApi_DscChannelDialog_FullMethodName      = "/pb.DscApi/DscChannelDialog"
	DscApi_DscChannelDialogFresh_FullMethodName = "/pb.DscApi/DscChannelDialogFresh"
	DscApi_DscReplyMessage_FullMethodName       = "/pb.DscApi/DscReplyMessage"
	DscApi_DscEditMessage_FullMethodName        = "/pb.DscApi/DscEditMessage"
	DscApi_DscSendFile_FullMethodName           = "/pb.DscApi/DscSendFile"
	DscApi_DscPortraitInfo_FullMethodName       = "/pb.DscApi/DscPortraitInfo"
	DscApi_DscPortraitEdit_FullMethodName       = "/pb.DscApi/DscPortraitEdit"
	DscApi_DscTabCount_FullMethodName           = "/pb.DscApi/DscTabCount"
	DscApi_DscNewCommuSave_FullMethodName       = "/pb.DscApi/DscNewCommuSave"
	DscApi_DscNewCommuEdit_FullMethodName       = "/pb.DscApi/DscNewCommuEdit"
	DscApi_DscNewCommuList_FullMethodName       = "/pb.DscApi/DscNewCommuList"
	DscApi_DscTabEdit_FullMethodName            = "/pb.DscApi/DscTabEdit"
	DscApi_DscPublicTag_FullMethodName          = "/pb.DscApi/DscPublicTag"
	DscApi_DscTagBatchDelete_FullMethodName     = "/pb.DscApi/DscTagBatchDelete"
)

// DscApiClient is the client API for DscApi service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type DscApiClient interface {
	// discord 绑定玩家 - 列表查询
	DscUserList(ctx context.Context, in *DscUserListReq, opts ...grpc.CallOption) (*DscUserListResp, error)
	// discord 绑定玩家 - 详情查询 undo
	DscUserDetail(ctx context.Context, in *DscUserDetailReq, opts ...grpc.CallOption) (*DscUserDetailResp, error)
	// discord 玩家对话 - 会话历史
	DscChannelDialog(ctx context.Context, in *DscChannelDialogReq, opts ...grpc.CallOption) (*DscDialogDetailResp, error)
	// discord 玩家对话 - 新增会话+事件
	DscChannelDialogFresh(ctx context.Context, in *DscChannelDialogFreshReq, opts ...grpc.CallOption) (*DscChannelDialogFreshResp, error)
	// discord 消息 - 发送/回复消息
	DscReplyMessage(ctx context.Context, in *DscChannelMsgCreateReq, opts ...grpc.CallOption) (*DscChannelMsgCreateResp, error)
	// discord 消息 - 修改消息
	DscEditMessage(ctx context.Context, in *DscChannelMsgEditReq, opts ...grpc.CallOption) (*DscChannelMsgCreateResp, error)
	DscSendFile(ctx context.Context, in *DscChannelFileCreateReq, opts ...grpc.CallOption) (*DscDialogDetail, error)
	DscPortraitInfo(ctx context.Context, in *PortraitInfoReq, opts ...grpc.CallOption) (*PortraitInfoResp, error)
	DscPortraitEdit(ctx context.Context, in *PortraitEditReq, opts ...grpc.CallOption) (*Empty, error)
	DscTabCount(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*DiscordTabCountResp, error)
	DscNewCommuSave(ctx context.Context, in *DiscordNewCommuRecordAddReq, opts ...grpc.CallOption) (*Empty, error)
	DscNewCommuEdit(ctx context.Context, in *DiscordNewCommuRecordEditReq, opts ...grpc.CallOption) (*Empty, error)
	DscNewCommuList(ctx context.Context, in *DiscordNewCommuRecordListReq, opts ...grpc.CallOption) (*DiscordNewCommuRecordListResp, error)
	DscTabEdit(ctx context.Context, in *DiscordTabEditReq, opts ...grpc.CallOption) (*Empty, error)
	// Discord -- 公共标签
	DscPublicTag(ctx context.Context, in *DiscordPublicTagReq, opts ...grpc.CallOption) (*DiscordPublicTagResp, error)
	// Discord -- 批量删除标签
	DscTagBatchDelete(ctx context.Context, in *DiscordTagBatchDelete, opts ...grpc.CallOption) (*Empty, error)
}

type dscApiClient struct {
	cc grpc.ClientConnInterface
}

func NewDscApiClient(cc grpc.ClientConnInterface) DscApiClient {
	return &dscApiClient{cc}
}

func (c *dscApiClient) DscUserList(ctx context.Context, in *DscUserListReq, opts ...grpc.CallOption) (*DscUserListResp, error) {
	out := new(DscUserListResp)
	err := c.cc.Invoke(ctx, DscApi_DscUserList_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dscApiClient) DscUserDetail(ctx context.Context, in *DscUserDetailReq, opts ...grpc.CallOption) (*DscUserDetailResp, error) {
	out := new(DscUserDetailResp)
	err := c.cc.Invoke(ctx, DscApi_DscUserDetail_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dscApiClient) DscChannelDialog(ctx context.Context, in *DscChannelDialogReq, opts ...grpc.CallOption) (*DscDialogDetailResp, error) {
	out := new(DscDialogDetailResp)
	err := c.cc.Invoke(ctx, DscApi_DscChannelDialog_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dscApiClient) DscChannelDialogFresh(ctx context.Context, in *DscChannelDialogFreshReq, opts ...grpc.CallOption) (*DscChannelDialogFreshResp, error) {
	out := new(DscChannelDialogFreshResp)
	err := c.cc.Invoke(ctx, DscApi_DscChannelDialogFresh_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dscApiClient) DscReplyMessage(ctx context.Context, in *DscChannelMsgCreateReq, opts ...grpc.CallOption) (*DscChannelMsgCreateResp, error) {
	out := new(DscChannelMsgCreateResp)
	err := c.cc.Invoke(ctx, DscApi_DscReplyMessage_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dscApiClient) DscEditMessage(ctx context.Context, in *DscChannelMsgEditReq, opts ...grpc.CallOption) (*DscChannelMsgCreateResp, error) {
	out := new(DscChannelMsgCreateResp)
	err := c.cc.Invoke(ctx, DscApi_DscEditMessage_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dscApiClient) DscSendFile(ctx context.Context, in *DscChannelFileCreateReq, opts ...grpc.CallOption) (*DscDialogDetail, error) {
	out := new(DscDialogDetail)
	err := c.cc.Invoke(ctx, DscApi_DscSendFile_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dscApiClient) DscPortraitInfo(ctx context.Context, in *PortraitInfoReq, opts ...grpc.CallOption) (*PortraitInfoResp, error) {
	out := new(PortraitInfoResp)
	err := c.cc.Invoke(ctx, DscApi_DscPortraitInfo_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dscApiClient) DscPortraitEdit(ctx context.Context, in *PortraitEditReq, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, DscApi_DscPortraitEdit_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dscApiClient) DscTabCount(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*DiscordTabCountResp, error) {
	out := new(DiscordTabCountResp)
	err := c.cc.Invoke(ctx, DscApi_DscTabCount_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dscApiClient) DscNewCommuSave(ctx context.Context, in *DiscordNewCommuRecordAddReq, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, DscApi_DscNewCommuSave_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dscApiClient) DscNewCommuEdit(ctx context.Context, in *DiscordNewCommuRecordEditReq, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, DscApi_DscNewCommuEdit_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dscApiClient) DscNewCommuList(ctx context.Context, in *DiscordNewCommuRecordListReq, opts ...grpc.CallOption) (*DiscordNewCommuRecordListResp, error) {
	out := new(DiscordNewCommuRecordListResp)
	err := c.cc.Invoke(ctx, DscApi_DscNewCommuList_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dscApiClient) DscTabEdit(ctx context.Context, in *DiscordTabEditReq, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, DscApi_DscTabEdit_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dscApiClient) DscPublicTag(ctx context.Context, in *DiscordPublicTagReq, opts ...grpc.CallOption) (*DiscordPublicTagResp, error) {
	out := new(DiscordPublicTagResp)
	err := c.cc.Invoke(ctx, DscApi_DscPublicTag_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dscApiClient) DscTagBatchDelete(ctx context.Context, in *DiscordTagBatchDelete, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, DscApi_DscTagBatchDelete_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// DscApiServer is the server API for DscApi service.
// All implementations must embed UnimplementedDscApiServer
// for forward compatibility
type DscApiServer interface {
	// discord 绑定玩家 - 列表查询
	DscUserList(context.Context, *DscUserListReq) (*DscUserListResp, error)
	// discord 绑定玩家 - 详情查询 undo
	DscUserDetail(context.Context, *DscUserDetailReq) (*DscUserDetailResp, error)
	// discord 玩家对话 - 会话历史
	DscChannelDialog(context.Context, *DscChannelDialogReq) (*DscDialogDetailResp, error)
	// discord 玩家对话 - 新增会话+事件
	DscChannelDialogFresh(context.Context, *DscChannelDialogFreshReq) (*DscChannelDialogFreshResp, error)
	// discord 消息 - 发送/回复消息
	DscReplyMessage(context.Context, *DscChannelMsgCreateReq) (*DscChannelMsgCreateResp, error)
	// discord 消息 - 修改消息
	DscEditMessage(context.Context, *DscChannelMsgEditReq) (*DscChannelMsgCreateResp, error)
	DscSendFile(context.Context, *DscChannelFileCreateReq) (*DscDialogDetail, error)
	DscPortraitInfo(context.Context, *PortraitInfoReq) (*PortraitInfoResp, error)
	DscPortraitEdit(context.Context, *PortraitEditReq) (*Empty, error)
	DscTabCount(context.Context, *Empty) (*DiscordTabCountResp, error)
	DscNewCommuSave(context.Context, *DiscordNewCommuRecordAddReq) (*Empty, error)
	DscNewCommuEdit(context.Context, *DiscordNewCommuRecordEditReq) (*Empty, error)
	DscNewCommuList(context.Context, *DiscordNewCommuRecordListReq) (*DiscordNewCommuRecordListResp, error)
	DscTabEdit(context.Context, *DiscordTabEditReq) (*Empty, error)
	// Discord -- 公共标签
	DscPublicTag(context.Context, *DiscordPublicTagReq) (*DiscordPublicTagResp, error)
	// Discord -- 批量删除标签
	DscTagBatchDelete(context.Context, *DiscordTagBatchDelete) (*Empty, error)
	mustEmbedUnimplementedDscApiServer()
}

// UnimplementedDscApiServer must be embedded to have forward compatible implementations.
type UnimplementedDscApiServer struct {
}

func (UnimplementedDscApiServer) DscUserList(context.Context, *DscUserListReq) (*DscUserListResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DscUserList not implemented")
}
func (UnimplementedDscApiServer) DscUserDetail(context.Context, *DscUserDetailReq) (*DscUserDetailResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DscUserDetail not implemented")
}
func (UnimplementedDscApiServer) DscChannelDialog(context.Context, *DscChannelDialogReq) (*DscDialogDetailResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DscChannelDialog not implemented")
}
func (UnimplementedDscApiServer) DscChannelDialogFresh(context.Context, *DscChannelDialogFreshReq) (*DscChannelDialogFreshResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DscChannelDialogFresh not implemented")
}
func (UnimplementedDscApiServer) DscReplyMessage(context.Context, *DscChannelMsgCreateReq) (*DscChannelMsgCreateResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DscReplyMessage not implemented")
}
func (UnimplementedDscApiServer) DscEditMessage(context.Context, *DscChannelMsgEditReq) (*DscChannelMsgCreateResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DscEditMessage not implemented")
}
func (UnimplementedDscApiServer) DscSendFile(context.Context, *DscChannelFileCreateReq) (*DscDialogDetail, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DscSendFile not implemented")
}
func (UnimplementedDscApiServer) DscPortraitInfo(context.Context, *PortraitInfoReq) (*PortraitInfoResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DscPortraitInfo not implemented")
}
func (UnimplementedDscApiServer) DscPortraitEdit(context.Context, *PortraitEditReq) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DscPortraitEdit not implemented")
}
func (UnimplementedDscApiServer) DscTabCount(context.Context, *Empty) (*DiscordTabCountResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DscTabCount not implemented")
}
func (UnimplementedDscApiServer) DscNewCommuSave(context.Context, *DiscordNewCommuRecordAddReq) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DscNewCommuSave not implemented")
}
func (UnimplementedDscApiServer) DscNewCommuEdit(context.Context, *DiscordNewCommuRecordEditReq) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DscNewCommuEdit not implemented")
}
func (UnimplementedDscApiServer) DscNewCommuList(context.Context, *DiscordNewCommuRecordListReq) (*DiscordNewCommuRecordListResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DscNewCommuList not implemented")
}
func (UnimplementedDscApiServer) DscTabEdit(context.Context, *DiscordTabEditReq) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DscTabEdit not implemented")
}
func (UnimplementedDscApiServer) DscPublicTag(context.Context, *DiscordPublicTagReq) (*DiscordPublicTagResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DscPublicTag not implemented")
}
func (UnimplementedDscApiServer) DscTagBatchDelete(context.Context, *DiscordTagBatchDelete) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DscTagBatchDelete not implemented")
}
func (UnimplementedDscApiServer) mustEmbedUnimplementedDscApiServer() {}

// UnsafeDscApiServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to DscApiServer will
// result in compilation errors.
type UnsafeDscApiServer interface {
	mustEmbedUnimplementedDscApiServer()
}

func RegisterDscApiServer(s grpc.ServiceRegistrar, srv DscApiServer) {
	s.RegisterService(&DscApi_ServiceDesc, srv)
}

func _DscApi_DscUserList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DscUserListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DscApiServer).DscUserList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DscApi_DscUserList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DscApiServer).DscUserList(ctx, req.(*DscUserListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _DscApi_DscUserDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DscUserDetailReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DscApiServer).DscUserDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DscApi_DscUserDetail_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DscApiServer).DscUserDetail(ctx, req.(*DscUserDetailReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _DscApi_DscChannelDialog_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DscChannelDialogReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DscApiServer).DscChannelDialog(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DscApi_DscChannelDialog_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DscApiServer).DscChannelDialog(ctx, req.(*DscChannelDialogReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _DscApi_DscChannelDialogFresh_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DscChannelDialogFreshReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DscApiServer).DscChannelDialogFresh(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DscApi_DscChannelDialogFresh_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DscApiServer).DscChannelDialogFresh(ctx, req.(*DscChannelDialogFreshReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _DscApi_DscReplyMessage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DscChannelMsgCreateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DscApiServer).DscReplyMessage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DscApi_DscReplyMessage_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DscApiServer).DscReplyMessage(ctx, req.(*DscChannelMsgCreateReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _DscApi_DscEditMessage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DscChannelMsgEditReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DscApiServer).DscEditMessage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DscApi_DscEditMessage_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DscApiServer).DscEditMessage(ctx, req.(*DscChannelMsgEditReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _DscApi_DscSendFile_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DscChannelFileCreateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DscApiServer).DscSendFile(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DscApi_DscSendFile_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DscApiServer).DscSendFile(ctx, req.(*DscChannelFileCreateReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _DscApi_DscPortraitInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PortraitInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DscApiServer).DscPortraitInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DscApi_DscPortraitInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DscApiServer).DscPortraitInfo(ctx, req.(*PortraitInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _DscApi_DscPortraitEdit_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PortraitEditReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DscApiServer).DscPortraitEdit(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DscApi_DscPortraitEdit_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DscApiServer).DscPortraitEdit(ctx, req.(*PortraitEditReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _DscApi_DscTabCount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DscApiServer).DscTabCount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DscApi_DscTabCount_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DscApiServer).DscTabCount(ctx, req.(*Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _DscApi_DscNewCommuSave_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DiscordNewCommuRecordAddReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DscApiServer).DscNewCommuSave(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DscApi_DscNewCommuSave_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DscApiServer).DscNewCommuSave(ctx, req.(*DiscordNewCommuRecordAddReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _DscApi_DscNewCommuEdit_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DiscordNewCommuRecordEditReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DscApiServer).DscNewCommuEdit(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DscApi_DscNewCommuEdit_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DscApiServer).DscNewCommuEdit(ctx, req.(*DiscordNewCommuRecordEditReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _DscApi_DscNewCommuList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DiscordNewCommuRecordListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DscApiServer).DscNewCommuList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DscApi_DscNewCommuList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DscApiServer).DscNewCommuList(ctx, req.(*DiscordNewCommuRecordListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _DscApi_DscTabEdit_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DiscordTabEditReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DscApiServer).DscTabEdit(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DscApi_DscTabEdit_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DscApiServer).DscTabEdit(ctx, req.(*DiscordTabEditReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _DscApi_DscPublicTag_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DiscordPublicTagReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DscApiServer).DscPublicTag(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DscApi_DscPublicTag_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DscApiServer).DscPublicTag(ctx, req.(*DiscordPublicTagReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _DscApi_DscTagBatchDelete_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DiscordTagBatchDelete)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DscApiServer).DscTagBatchDelete(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DscApi_DscTagBatchDelete_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DscApiServer).DscTagBatchDelete(ctx, req.(*DiscordTagBatchDelete))
	}
	return interceptor(ctx, in, info, handler)
}

// DscApi_ServiceDesc is the grpc.ServiceDesc for DscApi service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var DscApi_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "pb.DscApi",
	HandlerType: (*DscApiServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "DscUserList",
			Handler:    _DscApi_DscUserList_Handler,
		},
		{
			MethodName: "DscUserDetail",
			Handler:    _DscApi_DscUserDetail_Handler,
		},
		{
			MethodName: "DscChannelDialog",
			Handler:    _DscApi_DscChannelDialog_Handler,
		},
		{
			MethodName: "DscChannelDialogFresh",
			Handler:    _DscApi_DscChannelDialogFresh_Handler,
		},
		{
			MethodName: "DscReplyMessage",
			Handler:    _DscApi_DscReplyMessage_Handler,
		},
		{
			MethodName: "DscEditMessage",
			Handler:    _DscApi_DscEditMessage_Handler,
		},
		{
			MethodName: "DscSendFile",
			Handler:    _DscApi_DscSendFile_Handler,
		},
		{
			MethodName: "DscPortraitInfo",
			Handler:    _DscApi_DscPortraitInfo_Handler,
		},
		{
			MethodName: "DscPortraitEdit",
			Handler:    _DscApi_DscPortraitEdit_Handler,
		},
		{
			MethodName: "DscTabCount",
			Handler:    _DscApi_DscTabCount_Handler,
		},
		{
			MethodName: "DscNewCommuSave",
			Handler:    _DscApi_DscNewCommuSave_Handler,
		},
		{
			MethodName: "DscNewCommuEdit",
			Handler:    _DscApi_DscNewCommuEdit_Handler,
		},
		{
			MethodName: "DscNewCommuList",
			Handler:    _DscApi_DscNewCommuList_Handler,
		},
		{
			MethodName: "DscTabEdit",
			Handler:    _DscApi_DscTabEdit_Handler,
		},
		{
			MethodName: "DscPublicTag",
			Handler:    _DscApi_DscPublicTag_Handler,
		},
		{
			MethodName: "DscTagBatchDelete",
			Handler:    _DscApi_DscTagBatchDelete_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "discord.proto",
}
