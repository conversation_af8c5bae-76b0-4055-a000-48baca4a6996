// Code generated by protoc-gen-go. DO NOT EDIT.
// source: dict.proto

package pb

import (
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

type DictId struct {
	// 字段Id @gotags: validate:"required"
	DictId               uint32   `protobuf:"varint,1,opt,name=dict_id,json=dictId,proto3" json:"dict_id" validate:"required"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *DictId) Reset()         { *m = DictId{} }
func (m *DictId) String() string { return proto.CompactTextString(m) }
func (*DictId) ProtoMessage()    {}
func (*DictId) Descriptor() ([]byte, []int) {
	return fileDescriptor_67812e90854f6714, []int{0}
}

func (m *DictId) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DictId.Unmarshal(m, b)
}
func (m *DictId) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DictId.Marshal(b, m, deterministic)
}
func (m *DictId) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DictId.Merge(m, src)
}
func (m *DictId) XXX_Size() int {
	return xxx_messageInfo_DictId.Size(m)
}
func (m *DictId) XXX_DiscardUnknown() {
	xxx_messageInfo_DictId.DiscardUnknown(m)
}

var xxx_messageInfo_DictId proto.InternalMessageInfo

func (m *DictId) GetDictId() uint32 {
	if m != nil {
		return m.DictId
	}
	return 0
}

type DictOptsResp struct {
	List                 []*DictOptsResp_Opts `protobuf:"bytes,2,rep,name=list,proto3" json:"list"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-" gorm:"-"`
	XXX_unrecognized     []byte               `json:"-" gorm:"-"`
	XXX_sizecache        int32                `json:"-" gorm:"-"`
}

func (m *DictOptsResp) Reset()         { *m = DictOptsResp{} }
func (m *DictOptsResp) String() string { return proto.CompactTextString(m) }
func (*DictOptsResp) ProtoMessage()    {}
func (*DictOptsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_67812e90854f6714, []int{1}
}

func (m *DictOptsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DictOptsResp.Unmarshal(m, b)
}
func (m *DictOptsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DictOptsResp.Marshal(b, m, deterministic)
}
func (m *DictOptsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DictOptsResp.Merge(m, src)
}
func (m *DictOptsResp) XXX_Size() int {
	return xxx_messageInfo_DictOptsResp.Size(m)
}
func (m *DictOptsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DictOptsResp.DiscardUnknown(m)
}

var xxx_messageInfo_DictOptsResp proto.InternalMessageInfo

func (m *DictOptsResp) GetList() []*DictOptsResp_Opts {
	if m != nil {
		return m.List
	}
	return nil
}

type DictOptsResp_Opts struct {
	// 字段ID
	DictId uint32 `protobuf:"varint,1,opt,name=dict_id,json=dictId,proto3" json:"dict_id"`
	// 字段名称
	DictName string `protobuf:"bytes,2,opt,name=dict_name,json=dictName,proto3" json:"dict_name"`
	// 字段Key
	DictKey              string   `protobuf:"bytes,3,opt,name=dict_key,json=dictKey,proto3" json:"dict_key"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *DictOptsResp_Opts) Reset()         { *m = DictOptsResp_Opts{} }
func (m *DictOptsResp_Opts) String() string { return proto.CompactTextString(m) }
func (*DictOptsResp_Opts) ProtoMessage()    {}
func (*DictOptsResp_Opts) Descriptor() ([]byte, []int) {
	return fileDescriptor_67812e90854f6714, []int{1, 0}
}

func (m *DictOptsResp_Opts) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DictOptsResp_Opts.Unmarshal(m, b)
}
func (m *DictOptsResp_Opts) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DictOptsResp_Opts.Marshal(b, m, deterministic)
}
func (m *DictOptsResp_Opts) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DictOptsResp_Opts.Merge(m, src)
}
func (m *DictOptsResp_Opts) XXX_Size() int {
	return xxx_messageInfo_DictOptsResp_Opts.Size(m)
}
func (m *DictOptsResp_Opts) XXX_DiscardUnknown() {
	xxx_messageInfo_DictOptsResp_Opts.DiscardUnknown(m)
}

var xxx_messageInfo_DictOptsResp_Opts proto.InternalMessageInfo

func (m *DictOptsResp_Opts) GetDictId() uint32 {
	if m != nil {
		return m.DictId
	}
	return 0
}

func (m *DictOptsResp_Opts) GetDictName() string {
	if m != nil {
		return m.DictName
	}
	return ""
}

func (m *DictOptsResp_Opts) GetDictKey() string {
	if m != nil {
		return m.DictKey
	}
	return ""
}

type DictListResp struct {
	List                 []*DictListResp_Dict `protobuf:"bytes,1,rep,name=list,proto3" json:"list"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-" gorm:"-"`
	XXX_unrecognized     []byte               `json:"-" gorm:"-"`
	XXX_sizecache        int32                `json:"-" gorm:"-"`
}

func (m *DictListResp) Reset()         { *m = DictListResp{} }
func (m *DictListResp) String() string { return proto.CompactTextString(m) }
func (*DictListResp) ProtoMessage()    {}
func (*DictListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_67812e90854f6714, []int{2}
}

func (m *DictListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DictListResp.Unmarshal(m, b)
}
func (m *DictListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DictListResp.Marshal(b, m, deterministic)
}
func (m *DictListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DictListResp.Merge(m, src)
}
func (m *DictListResp) XXX_Size() int {
	return xxx_messageInfo_DictListResp.Size(m)
}
func (m *DictListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DictListResp.DiscardUnknown(m)
}

var xxx_messageInfo_DictListResp proto.InternalMessageInfo

func (m *DictListResp) GetList() []*DictListResp_Dict {
	if m != nil {
		return m.List
	}
	return nil
}

type DictListResp_Dict struct {
	// 团队ID
	DictId uint32 `protobuf:"varint,1,opt,name=dict_id,json=dictId,proto3" json:"dict_id"`
	// 入口名称
	DictName string `protobuf:"bytes,2,opt,name=dict_name,json=dictName,proto3" json:"dict_name"`
	// 入口描述
	DictKey string `protobuf:"bytes,3,opt,name=dict_key,json=dictKey,proto3" json:"dict_key"`
	// 更新时间
	UpdatedAt string `protobuf:"bytes,4,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at"`
	// 操作人员
	Operator string `protobuf:"bytes,5,opt,name=operator,proto3" json:"operator"`
	// 状态
	Enable               bool     `protobuf:"varint,6,opt,name=enable,proto3" json:"enable"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *DictListResp_Dict) Reset()         { *m = DictListResp_Dict{} }
func (m *DictListResp_Dict) String() string { return proto.CompactTextString(m) }
func (*DictListResp_Dict) ProtoMessage()    {}
func (*DictListResp_Dict) Descriptor() ([]byte, []int) {
	return fileDescriptor_67812e90854f6714, []int{2, 0}
}

func (m *DictListResp_Dict) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DictListResp_Dict.Unmarshal(m, b)
}
func (m *DictListResp_Dict) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DictListResp_Dict.Marshal(b, m, deterministic)
}
func (m *DictListResp_Dict) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DictListResp_Dict.Merge(m, src)
}
func (m *DictListResp_Dict) XXX_Size() int {
	return xxx_messageInfo_DictListResp_Dict.Size(m)
}
func (m *DictListResp_Dict) XXX_DiscardUnknown() {
	xxx_messageInfo_DictListResp_Dict.DiscardUnknown(m)
}

var xxx_messageInfo_DictListResp_Dict proto.InternalMessageInfo

func (m *DictListResp_Dict) GetDictId() uint32 {
	if m != nil {
		return m.DictId
	}
	return 0
}

func (m *DictListResp_Dict) GetDictName() string {
	if m != nil {
		return m.DictName
	}
	return ""
}

func (m *DictListResp_Dict) GetDictKey() string {
	if m != nil {
		return m.DictKey
	}
	return ""
}

func (m *DictListResp_Dict) GetUpdatedAt() string {
	if m != nil {
		return m.UpdatedAt
	}
	return ""
}

func (m *DictListResp_Dict) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

func (m *DictListResp_Dict) GetEnable() bool {
	if m != nil {
		return m.Enable
	}
	return false
}

type DictAddReq struct {
	// 字段名称 @gotags: validate:"required"
	DictName string `protobuf:"bytes,1,opt,name=dict_name,json=dictName,proto3" json:"dict_name" validate:"required"`
	// 字段Key @gotags: validate:"required"
	DictKey              string   `protobuf:"bytes,2,opt,name=dict_key,json=dictKey,proto3" json:"dict_key" validate:"required"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *DictAddReq) Reset()         { *m = DictAddReq{} }
func (m *DictAddReq) String() string { return proto.CompactTextString(m) }
func (*DictAddReq) ProtoMessage()    {}
func (*DictAddReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_67812e90854f6714, []int{3}
}

func (m *DictAddReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DictAddReq.Unmarshal(m, b)
}
func (m *DictAddReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DictAddReq.Marshal(b, m, deterministic)
}
func (m *DictAddReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DictAddReq.Merge(m, src)
}
func (m *DictAddReq) XXX_Size() int {
	return xxx_messageInfo_DictAddReq.Size(m)
}
func (m *DictAddReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DictAddReq.DiscardUnknown(m)
}

var xxx_messageInfo_DictAddReq proto.InternalMessageInfo

func (m *DictAddReq) GetDictName() string {
	if m != nil {
		return m.DictName
	}
	return ""
}

func (m *DictAddReq) GetDictKey() string {
	if m != nil {
		return m.DictKey
	}
	return ""
}

type DictInfoResp struct {
	// 字段Id @gotags: validate:"required"
	DictId uint32 `protobuf:"varint,1,opt,name=dict_id,json=dictId,proto3" json:"dict_id" validate:"required"`
	// 字段名称 @gotags: validate:"required"
	DictName string `protobuf:"bytes,2,opt,name=dict_name,json=dictName,proto3" json:"dict_name" validate:"required"`
	// 字段Key @gotags: validate:"required"
	DictKey              string   `protobuf:"bytes,3,opt,name=dict_key,json=dictKey,proto3" json:"dict_key" validate:"required"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *DictInfoResp) Reset()         { *m = DictInfoResp{} }
func (m *DictInfoResp) String() string { return proto.CompactTextString(m) }
func (*DictInfoResp) ProtoMessage()    {}
func (*DictInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_67812e90854f6714, []int{4}
}

func (m *DictInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DictInfoResp.Unmarshal(m, b)
}
func (m *DictInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DictInfoResp.Marshal(b, m, deterministic)
}
func (m *DictInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DictInfoResp.Merge(m, src)
}
func (m *DictInfoResp) XXX_Size() int {
	return xxx_messageInfo_DictInfoResp.Size(m)
}
func (m *DictInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DictInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_DictInfoResp proto.InternalMessageInfo

func (m *DictInfoResp) GetDictId() uint32 {
	if m != nil {
		return m.DictId
	}
	return 0
}

func (m *DictInfoResp) GetDictName() string {
	if m != nil {
		return m.DictName
	}
	return ""
}

func (m *DictInfoResp) GetDictKey() string {
	if m != nil {
		return m.DictKey
	}
	return ""
}

func init() {
	proto.RegisterType((*DictId)(nil), "pb.DictId")
	proto.RegisterType((*DictOptsResp)(nil), "pb.DictOptsResp")
	proto.RegisterType((*DictOptsResp_Opts)(nil), "pb.DictOptsResp.Opts")
	proto.RegisterType((*DictListResp)(nil), "pb.DictListResp")
	proto.RegisterType((*DictListResp_Dict)(nil), "pb.DictListResp.Dict")
	proto.RegisterType((*DictAddReq)(nil), "pb.DictAddReq")
	proto.RegisterType((*DictInfoResp)(nil), "pb.DictInfoResp")
}

func init() {
	proto.RegisterFile("dict.proto", fileDescriptor_67812e90854f6714)
}

var fileDescriptor_67812e90854f6714 = []byte{
	// 289 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xac, 0x92, 0xb1, 0x4a, 0xc4, 0x40,
	0x10, 0x86, 0xd9, 0x5c, 0x5c, 0x2f, 0xa3, 0x36, 0x0b, 0xea, 0x7a, 0x22, 0xc4, 0x54, 0xb1, 0x49,
	0xa1, 0xa5, 0xd5, 0xc9, 0x35, 0x87, 0xa2, 0x90, 0x46, 0xb0, 0x09, 0x9b, 0xdb, 0x11, 0x82, 0x77,
	0xc9, 0x9a, 0x8c, 0xc5, 0xbd, 0x86, 0x8f, 0xe1, 0x83, 0xf9, 0x1c, 0xb2, 0x73, 0x39, 0x30, 0x85,
	0x56, 0xd7, 0xcd, 0x3f, 0xdf, 0x30, 0x7c, 0x3b, 0x2c, 0x80, 0xad, 0x16, 0x94, 0xb9, 0xb6, 0xa1,
	0x46, 0x05, 0xae, 0x4c, 0x2e, 0x41, 0xce, 0xaa, 0x05, 0xcd, 0xad, 0x3a, 0x85, 0x7d, 0xcf, 0x8a,
	0xca, 0x6a, 0x11, 0x8b, 0xf4, 0x28, 0x97, 0x96, 0x41, 0xf2, 0x29, 0xe0, 0xd0, 0xcf, 0x3c, 0x39,
	0xea, 0x72, 0xec, 0x9c, 0xba, 0x82, 0x70, 0x59, 0x75, 0xa4, 0x83, 0x78, 0x94, 0x1e, 0x5c, 0x1f,
	0x67, 0xae, 0xcc, 0x7e, 0xf3, 0x8c, 0x0b, 0x1e, 0x99, 0x3c, 0x43, 0xe8, 0xd3, 0x9f, 0xcb, 0xd5,
	0x39, 0x44, 0x0c, 0x6a, 0xb3, 0x42, 0x1d, 0xc4, 0x22, 0x8d, 0xf2, 0xb1, 0x6f, 0x3c, 0x9a, 0x15,
	0xaa, 0x33, 0xe0, 0xba, 0x78, 0xc3, 0xb5, 0x1e, 0x31, 0xe3, 0x2d, 0xf7, 0xb8, 0x4e, 0xbe, 0x7b,
	0xa9, 0x87, 0xaa, 0xa3, 0x81, 0x94, 0x18, 0x4a, 0x6d, 0x39, 0x87, 0x5e, 0xea, 0x4b, 0x40, 0xe8,
	0xe3, 0xce, 0xad, 0xd4, 0x05, 0xc0, 0x87, 0xb3, 0x86, 0xd0, 0x16, 0x86, 0x74, 0xc8, 0x30, 0xea,
	0x3b, 0x53, 0x52, 0x13, 0x18, 0x37, 0x0e, 0x5b, 0x43, 0x4d, 0xab, 0xf7, 0x36, 0x5b, 0xb7, 0x59,
	0x9d, 0x80, 0xc4, 0xda, 0x94, 0x4b, 0xd4, 0x32, 0x16, 0xe9, 0x38, 0xef, 0x53, 0x32, 0x03, 0xf0,
	0xae, 0x53, 0x6b, 0x73, 0x7c, 0x1f, 0x8a, 0x89, 0x7f, 0xc4, 0x82, 0xe1, 0xb9, 0x8a, 0xcd, 0xb5,
	0xe6, 0xf5, 0x6b, 0xc3, 0xd7, 0xda, 0xf5, 0xcb, 0xef, 0xe4, 0x4b, 0x98, 0xdd, 0xba, 0xb2, 0x94,
	0xfc, 0xb5, 0x6e, 0x7e, 0x02, 0x00, 0x00, 0xff, 0xff, 0xc2, 0x16, 0xf4, 0x41, 0x68, 0x02, 0x00,
	0x00,
}
