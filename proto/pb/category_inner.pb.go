// Code generated by protoc-gen-go. DO NOT EDIT.
// source: category_inner.proto

package pb

import (
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

type GetCatTitlesReq struct {
	CatIds               []int32  `protobuf:"varint,1,rep,packed,name=cat_ids,json=catIds,proto3" json:"cat_ids"`
	Lang                 string   `protobuf:"bytes,2,opt,name=lang,proto3" json:"lang"`
	GameProject          string   `protobuf:"bytes,3,opt,name=game_project,json=gameProject,proto3" json:"game_project"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *GetCatTitlesReq) Reset()         { *m = GetCatTitlesReq{} }
func (m *GetCatTitlesReq) String() string { return proto.CompactTextString(m) }
func (*GetCatTitlesReq) ProtoMessage()    {}
func (*GetCatTitlesReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_bb432e6fe46d91d3, []int{0}
}

func (m *GetCatTitlesReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCatTitlesReq.Unmarshal(m, b)
}
func (m *GetCatTitlesReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCatTitlesReq.Marshal(b, m, deterministic)
}
func (m *GetCatTitlesReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCatTitlesReq.Merge(m, src)
}
func (m *GetCatTitlesReq) XXX_Size() int {
	return xxx_messageInfo_GetCatTitlesReq.Size(m)
}
func (m *GetCatTitlesReq) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCatTitlesReq.DiscardUnknown(m)
}

var xxx_messageInfo_GetCatTitlesReq proto.InternalMessageInfo

func (m *GetCatTitlesReq) GetCatIds() []int32 {
	if m != nil {
		return m.CatIds
	}
	return nil
}

func (m *GetCatTitlesReq) GetLang() string {
	if m != nil {
		return m.Lang
	}
	return ""
}

func (m *GetCatTitlesReq) GetGameProject() string {
	if m != nil {
		return m.GameProject
	}
	return ""
}

type GetCatTitlesResp struct {
	CatTitles            []*CatTitle `protobuf:"bytes,1,rep,name=cat_titles,json=catTitles,proto3" json:"cat_titles"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-" gorm:"-"`
	XXX_unrecognized     []byte      `json:"-" gorm:"-"`
	XXX_sizecache        int32       `json:"-" gorm:"-"`
}

func (m *GetCatTitlesResp) Reset()         { *m = GetCatTitlesResp{} }
func (m *GetCatTitlesResp) String() string { return proto.CompactTextString(m) }
func (*GetCatTitlesResp) ProtoMessage()    {}
func (*GetCatTitlesResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_bb432e6fe46d91d3, []int{1}
}

func (m *GetCatTitlesResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetCatTitlesResp.Unmarshal(m, b)
}
func (m *GetCatTitlesResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetCatTitlesResp.Marshal(b, m, deterministic)
}
func (m *GetCatTitlesResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetCatTitlesResp.Merge(m, src)
}
func (m *GetCatTitlesResp) XXX_Size() int {
	return xxx_messageInfo_GetCatTitlesResp.Size(m)
}
func (m *GetCatTitlesResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GetCatTitlesResp.DiscardUnknown(m)
}

var xxx_messageInfo_GetCatTitlesResp proto.InternalMessageInfo

func (m *GetCatTitlesResp) GetCatTitles() []*CatTitle {
	if m != nil {
		return m.CatTitles
	}
	return nil
}

type CatTitle struct {
	CatId                int32    `protobuf:"varint,1,opt,name=cat_id,json=catId,proto3" json:"cat_id"`
	CatName              string   `protobuf:"bytes,2,opt,name=cat_name,json=catName,proto3" json:"cat_name"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *CatTitle) Reset()         { *m = CatTitle{} }
func (m *CatTitle) String() string { return proto.CompactTextString(m) }
func (*CatTitle) ProtoMessage()    {}
func (*CatTitle) Descriptor() ([]byte, []int) {
	return fileDescriptor_bb432e6fe46d91d3, []int{2}
}

func (m *CatTitle) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CatTitle.Unmarshal(m, b)
}
func (m *CatTitle) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CatTitle.Marshal(b, m, deterministic)
}
func (m *CatTitle) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CatTitle.Merge(m, src)
}
func (m *CatTitle) XXX_Size() int {
	return xxx_messageInfo_CatTitle.Size(m)
}
func (m *CatTitle) XXX_DiscardUnknown() {
	xxx_messageInfo_CatTitle.DiscardUnknown(m)
}

var xxx_messageInfo_CatTitle proto.InternalMessageInfo

func (m *CatTitle) GetCatId() int32 {
	if m != nil {
		return m.CatId
	}
	return 0
}

func (m *CatTitle) GetCatName() string {
	if m != nil {
		return m.CatName
	}
	return ""
}

func init() {
	proto.RegisterType((*GetCatTitlesReq)(nil), "pb.GetCatTitlesReq")
	proto.RegisterType((*GetCatTitlesResp)(nil), "pb.GetCatTitlesResp")
	proto.RegisterType((*CatTitle)(nil), "pb.CatTitle")
}

func init() {
	proto.RegisterFile("category_inner.proto", fileDescriptor_bb432e6fe46d91d3)
}

var fileDescriptor_bb432e6fe46d91d3 = []byte{
	// 307 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x5c, 0x90, 0xcb, 0x4a, 0xc3, 0x40,
	0x14, 0x86, 0x49, 0x2f, 0xb1, 0x3d, 0x0d, 0x58, 0x46, 0x8b, 0x31, 0xb8, 0x88, 0x59, 0x15, 0x85,
	0x04, 0xea, 0x4e, 0x05, 0xd1, 0x22, 0xd2, 0x8d, 0x94, 0xe0, 0xca, 0x4d, 0x39, 0x99, 0x0e, 0x21,
	0xd2, 0xb9, 0x98, 0x9c, 0x4d, 0xb7, 0xbe, 0x82, 0x8f, 0xe6, 0x2b, 0xf8, 0x20, 0x32, 0x93, 0x16,
	0xd4, 0xdd, 0xcc, 0x37, 0x67, 0xf8, 0xff, 0xf3, 0xc1, 0x31, 0x47, 0x12, 0xa5, 0xae, 0xb7, 0xab,
	0x4a, 0x29, 0x51, 0xa7, 0xa6, 0xd6, 0xa4, 0x59, 0xc7, 0x14, 0xd1, 0x59, 0xa9, 0x75, 0xb9, 0x11,
	0x19, 0x9a, 0x2a, 0x43, 0xa5, 0x34, 0x21, 0x55, 0x5a, 0x35, 0xed, 0x44, 0x14, 0x70, 0x2d, 0xa5,
	0x56, 0xed, 0x2d, 0x41, 0x38, 0x7c, 0x12, 0x34, 0x47, 0x7a, 0xa9, 0x68, 0x23, 0x9a, 0x5c, 0xbc,
	0xb3, 0x13, 0x38, 0xe0, 0x48, 0xab, 0x6a, 0xdd, 0x84, 0x5e, 0xdc, 0x9d, 0xf6, 0x73, 0x9f, 0x23,
	0x2d, 0xd6, 0x0d, 0x63, 0xd0, 0xdb, 0xa0, 0x2a, 0xc3, 0x4e, 0xec, 0x4d, 0x87, 0xb9, 0x3b, 0xb3,
	0x73, 0x08, 0x4a, 0x94, 0x62, 0x65, 0x6a, 0xfd, 0x26, 0x38, 0x85, 0x5d, 0xf7, 0x36, 0xb2, 0x6c,
	0xd9, 0xa2, 0xe4, 0x0e, 0xc6, 0x7f, 0x23, 0x1a, 0xc3, 0x2e, 0x01, 0x6c, 0x06, 0x39, 0xe2, 0x62,
	0x46, 0xb3, 0x20, 0x35, 0x45, 0xba, 0x1f, 0xcb, 0x87, 0x7c, 0xff, 0x21, 0xb9, 0x85, 0xc1, 0x1e,
	0xb3, 0x09, 0xf8, 0x6d, 0xb9, 0xd0, 0x8b, 0xbd, 0x69, 0x3f, 0xef, 0xbb, 0x6e, 0xec, 0x14, 0x06,
	0x16, 0x2b, 0x94, 0x62, 0x57, 0xcf, 0xee, 0xf0, 0x8c, 0x52, 0xcc, 0xd6, 0x30, 0x9e, 0xef, 0x4c,
	0x2d, 0xac, 0xa8, 0xfb, 0xe5, 0x82, 0x2d, 0x21, 0xf8, 0x5d, 0x89, 0x1d, 0xd9, 0xe8, 0x7f, 0x1e,
	0xa2, 0xa1, 0x85, 0x8f, 0xd2, 0xd0, 0x36, 0x89, 0x3f, 0xbe, 0xbe, 0x3f, 0x3b, 0x51, 0x32, 0xc9,
	0x9c, 0x6b, 0x67, 0x96, 0x23, 0x65, 0x6d, 0xff, 0x6b, 0xef, 0xe2, 0xc1, 0x7f, 0xed, 0xa5, 0x37,
	0xa6, 0x28, 0x7c, 0xa7, 0xf5, 0xea, 0x27, 0x00, 0x00, 0xff, 0xff, 0x59, 0x90, 0xc4, 0xef, 0x9e,
	0x01, 0x00, 0x00,
}
