// Code generated by protoc-gen-go. DO NOT EDIT.
// source: examine_enum.proto

package pb

import (
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

// 质检打分表-字段值类型定义
type ExamineFieldTpDf int32

const (
	// 不需要定义
	ExamineFieldTpDf_ExamineFieldTpDfUnknown ExamineFieldTpDf = 0
	// 下拉单选
	ExamineFieldTpDf_ExamineFieldTpSel ExamineFieldTpDf = 1
	// 下拉多选
	ExamineFieldTpDf_ExamineFieldTpMulSel ExamineFieldTpDf = 2
	// 单行文本
	ExamineFieldTpDf_ExamineFieldTpText ExamineFieldTpDf = 3
	// 多行文档 - 富文本textarea
	ExamineFieldTpDf_ExamineFieldTpTextarea ExamineFieldTpDf = 4
)

var ExamineFieldTpDf_name = map[int32]string{
	0: "ExamineFieldTpDfUnknown",
	1: "ExamineFieldTpSel",
	2: "ExamineFieldTpMulSel",
	3: "ExamineFieldTpText",
	4: "ExamineFieldTpTextarea",
}

var ExamineFieldTpDf_value = map[string]int32{
	"ExamineFieldTpDfUnknown": 0,
	"ExamineFieldTpSel":       1,
	"ExamineFieldTpMulSel":    2,
	"ExamineFieldTpText":      3,
	"ExamineFieldTpTextarea":  4,
}

func (x ExamineFieldTpDf) String() string {
	return proto.EnumName(ExamineFieldTpDf_name, int32(x))
}

func (ExamineFieldTpDf) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_93676526e7c05870, []int{0}
}

// 质检任务-状态定义
type ExamineTaskStateDf int32

const (
	ExamineTaskStateDf_ExamineTaskStateDfUnknown ExamineTaskStateDf = 0
	// 未开始
	ExamineTaskStateDf_ExamineTaskStateDfInit ExamineTaskStateDf = 1
	// 进行中
	ExamineTaskStateDf_ExamineTaskStateDfDoing ExamineTaskStateDf = 10
	// 成功
	ExamineTaskStateDf_ExamineTaskStateDfSuccess ExamineTaskStateDf = 100
	// 失败
	ExamineTaskStateDf_ExamineTaskStateDfFail ExamineTaskStateDf = 200
)

var ExamineTaskStateDf_name = map[int32]string{
	0:   "ExamineTaskStateDfUnknown",
	1:   "ExamineTaskStateDfInit",
	10:  "ExamineTaskStateDfDoing",
	100: "ExamineTaskStateDfSuccess",
	200: "ExamineTaskStateDfFail",
}

var ExamineTaskStateDf_value = map[string]int32{
	"ExamineTaskStateDfUnknown": 0,
	"ExamineTaskStateDfInit":    1,
	"ExamineTaskStateDfDoing":   10,
	"ExamineTaskStateDfSuccess": 100,
	"ExamineTaskStateDfFail":    200,
}

func (x ExamineTaskStateDf) String() string {
	return proto.EnumName(ExamineTaskStateDf_name, int32(x))
}

func (ExamineTaskStateDf) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_93676526e7c05870, []int{1}
}

// 质检任务-任务类型
type ExamineTaskGroupDf int32

const (
	ExamineTaskGroupDf_ExamineTaskGroupDfUnknown ExamineTaskGroupDf = 0
	// 工单
	ExamineTaskGroupDf_ExamineTaskGroupDfTicket ExamineTaskGroupDf = 1
	// discord
	ExamineTaskGroupDf_ExamineTaskGroupDfDiscord ExamineTaskGroupDf = 2
)

var ExamineTaskGroupDf_name = map[int32]string{
	0: "ExamineTaskGroupDfUnknown",
	1: "ExamineTaskGroupDfTicket",
	2: "ExamineTaskGroupDfDiscord",
}

var ExamineTaskGroupDf_value = map[string]int32{
	"ExamineTaskGroupDfUnknown": 0,
	"ExamineTaskGroupDfTicket":  1,
	"ExamineTaskGroupDfDiscord": 2,
}

func (x ExamineTaskGroupDf) String() string {
	return proto.EnumName(ExamineTaskGroupDf_name, int32(x))
}

func (ExamineTaskGroupDf) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_93676526e7c05870, []int{2}
}

// ExamineStateDf 质检单 当前状态
type ExamineStateDf int32

const (
	ExamineStateDf_ExamineStateDfUnknown ExamineStateDf = 0
	// 未开始
	ExamineStateDf_ExamineStateDfInit ExamineStateDf = 1
	// 进行中
	ExamineStateDf_ExamineStateDfDoing ExamineStateDf = 10
	// 已完成
	ExamineStateDf_ExamineStateDfSuccess ExamineStateDf = 100
)

var ExamineStateDf_name = map[int32]string{
	0:   "ExamineStateDfUnknown",
	1:   "ExamineStateDfInit",
	10:  "ExamineStateDfDoing",
	100: "ExamineStateDfSuccess",
}

var ExamineStateDf_value = map[string]int32{
	"ExamineStateDfUnknown": 0,
	"ExamineStateDfInit":    1,
	"ExamineStateDfDoing":   10,
	"ExamineStateDfSuccess": 100,
}

func (x ExamineStateDf) String() string {
	return proto.EnumName(ExamineStateDf_name, int32(x))
}

func (ExamineStateDf) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_93676526e7c05870, []int{3}
}

// ExamineFinalResultDf 质检单 最终结果
type ExamineFinalResultDf int32

const (
	ExamineFinalResultDf_ExamineFinalResultDfUnknown ExamineFinalResultDf = 0
	// 通过
	ExamineFinalResultDf_ExamineFinalResultDfPass ExamineFinalResultDf = 1
	// 不通过
	ExamineFinalResultDf_ExamineFinalResultDfFail ExamineFinalResultDf = 2
)

var ExamineFinalResultDf_name = map[int32]string{
	0: "ExamineFinalResultDfUnknown",
	1: "ExamineFinalResultDfPass",
	2: "ExamineFinalResultDfFail",
}

var ExamineFinalResultDf_value = map[string]int32{
	"ExamineFinalResultDfUnknown": 0,
	"ExamineFinalResultDfPass":    1,
	"ExamineFinalResultDfFail":    2,
}

func (x ExamineFinalResultDf) String() string {
	return proto.EnumName(ExamineFinalResultDf_name, int32(x))
}

func (ExamineFinalResultDf) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_93676526e7c05870, []int{4}
}

// ExamineNoticeMsgGroupDf 消息分类
type ExamineNoticeMsgGroupDf int32

const (
	ExamineNoticeMsgGroupDf_ExamineNoticeMsgGroupDfUnknown ExamineNoticeMsgGroupDf = 0
	// 质检结果通知
	ExamineNoticeMsgGroupDf_ExamineNoticeMsgGroupDfResult ExamineNoticeMsgGroupDf = 1
)

var ExamineNoticeMsgGroupDf_name = map[int32]string{
	0: "ExamineNoticeMsgGroupDfUnknown",
	1: "ExamineNoticeMsgGroupDfResult",
}

var ExamineNoticeMsgGroupDf_value = map[string]int32{
	"ExamineNoticeMsgGroupDfUnknown": 0,
	"ExamineNoticeMsgGroupDfResult":  1,
}

func (x ExamineNoticeMsgGroupDf) String() string {
	return proto.EnumName(ExamineNoticeMsgGroupDf_name, int32(x))
}

func (ExamineNoticeMsgGroupDf) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_93676526e7c05870, []int{5}
}

// ExamineNoticeStateDf 消息状态
type ExamineNoticeStateDf int32

const (
	ExamineNoticeStateDf_ExamineNoticeStateDfUnknown ExamineNoticeStateDf = 0
	// 未读
	ExamineNoticeStateDf_ExamineNoticeStateDfUnread ExamineNoticeStateDf = 1
	// 已读
	ExamineNoticeStateDf_ExamineNoticeStateDfRead ExamineNoticeStateDf = 2
)

var ExamineNoticeStateDf_name = map[int32]string{
	0: "ExamineNoticeStateDfUnknown",
	1: "ExamineNoticeStateDfUnread",
	2: "ExamineNoticeStateDfRead",
}

var ExamineNoticeStateDf_value = map[string]int32{
	"ExamineNoticeStateDfUnknown": 0,
	"ExamineNoticeStateDfUnread":  1,
	"ExamineNoticeStateDfRead":    2,
}

func (x ExamineNoticeStateDf) String() string {
	return proto.EnumName(ExamineNoticeStateDf_name, int32(x))
}

func (ExamineNoticeStateDf) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_93676526e7c05870, []int{6}
}

// 质检任务-抽检规则
type ExamineTaskRulesDf int32

const (
	ExamineTaskRulesDf_ExamineTaskRulesDfUnknown ExamineTaskRulesDf = 0
	// 无差别随机抽检
	ExamineTaskRulesDf_ExamineTaskRulesDfRandom ExamineTaskRulesDf = 1
	// 定向等量抽检
	ExamineTaskRulesDf_ExamineTaskRulesDfAvg ExamineTaskRulesDf = 2
)

var ExamineTaskRulesDf_name = map[int32]string{
	0: "ExamineTaskRulesDfUnknown",
	1: "ExamineTaskRulesDfRandom",
	2: "ExamineTaskRulesDfAvg",
}

var ExamineTaskRulesDf_value = map[string]int32{
	"ExamineTaskRulesDfUnknown": 0,
	"ExamineTaskRulesDfRandom":  1,
	"ExamineTaskRulesDfAvg":     2,
}

func (x ExamineTaskRulesDf) String() string {
	return proto.EnumName(ExamineTaskRulesDf_name, int32(x))
}

func (ExamineTaskRulesDf) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_93676526e7c05870, []int{7}
}

func init() {
	proto.RegisterEnum("pb.ExamineFieldTpDf", ExamineFieldTpDf_name, ExamineFieldTpDf_value)
	proto.RegisterEnum("pb.ExamineTaskStateDf", ExamineTaskStateDf_name, ExamineTaskStateDf_value)
	proto.RegisterEnum("pb.ExamineTaskGroupDf", ExamineTaskGroupDf_name, ExamineTaskGroupDf_value)
	proto.RegisterEnum("pb.ExamineStateDf", ExamineStateDf_name, ExamineStateDf_value)
	proto.RegisterEnum("pb.ExamineFinalResultDf", ExamineFinalResultDf_name, ExamineFinalResultDf_value)
	proto.RegisterEnum("pb.ExamineNoticeMsgGroupDf", ExamineNoticeMsgGroupDf_name, ExamineNoticeMsgGroupDf_value)
	proto.RegisterEnum("pb.ExamineNoticeStateDf", ExamineNoticeStateDf_name, ExamineNoticeStateDf_value)
	proto.RegisterEnum("pb.ExamineTaskRulesDf", ExamineTaskRulesDf_name, ExamineTaskRulesDf_value)
}

func init() {
	proto.RegisterFile("examine_enum.proto", fileDescriptor_93676526e7c05870)
}

var fileDescriptor_93676526e7c05870 = []byte{
	// 394 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x74, 0x93, 0xbd, 0x92, 0xd3, 0x30,
	0x10, 0xc7, 0x91, 0xc9, 0xa4, 0xd8, 0x82, 0x11, 0x82, 0x7c, 0x93, 0x30, 0x50, 0xaa, 0x48, 0x43,
	0x49, 0x05, 0x63, 0xc2, 0x50, 0x84, 0x61, 0x1c, 0xd3, 0xd0, 0x70, 0x8a, 0xad, 0x64, 0x34, 0x51,
	0x24, 0x8f, 0x65, 0x5f, 0xf2, 0x20, 0xf7, 0x10, 0xf7, 0x28, 0xf7, 0x58, 0x37, 0x49, 0x6c, 0xcb,
	0x3a, 0xdb, 0xed, 0xff, 0xb7, 0xda, 0x8f, 0xff, 0xae, 0x80, 0xf0, 0x33, 0x3b, 0x0a, 0xc5, 0xff,
	0x73, 0x95, 0x1f, 0x97, 0x49, 0xaa, 0x33, 0x4d, 0xbc, 0x64, 0x4b, 0x1f, 0x10, 0xe0, 0x1f, 0x37,
	0xb4, 0x12, 0x5c, 0xc6, 0x61, 0xe2, 0xef, 0xc8, 0x0c, 0x46, 0x2f, 0xb5, 0xbf, 0xea, 0xa0, 0xf4,
	0x49, 0xe1, 0x57, 0x64, 0x00, 0x6f, 0x5d, 0xb8, 0xe1, 0x12, 0x23, 0x32, 0x86, 0xf7, 0xae, 0xbc,
	0xce, 0xe5, 0x85, 0x78, 0x64, 0x08, 0xc4, 0x25, 0x21, 0x3f, 0x67, 0xf8, 0x35, 0x99, 0xc2, 0xb0,
	0xa9, 0xb3, 0x94, 0x33, 0xdc, 0xa3, 0x8f, 0xa8, 0x7a, 0x14, 0x32, 0x73, 0xd8, 0x64, 0x2c, 0xe3,
	0xfe, 0x8e, 0xcc, 0x61, 0xd2, 0x54, 0x6d, 0x6b, 0x36, 0x63, 0x0d, 0xff, 0x52, 0x22, 0xc3, 0xa8,
	0x36, 0x53, 0x8d, 0xf9, 0x5a, 0xa8, 0x3d, 0x86, 0xf6, 0xbc, 0x9b, 0x3c, 0x8a, 0xb8, 0x31, 0x38,
	0x26, 0xb3, 0xb6, 0xbc, 0x2b, 0x26, 0x24, 0x7e, 0x42, 0x34, 0x71, 0x3a, 0xfd, 0x99, 0xea, 0x3c,
	0x69, 0x74, 0x5a, 0xa8, 0xb6, 0xd3, 0x0f, 0x30, 0x6e, 0xe2, 0x50, 0x44, 0x07, 0x7e, 0xe9, 0xb5,
	0xf5, 0xb1, 0x2f, 0x4c, 0xa4, 0xd3, 0x18, 0x7b, 0xf4, 0x04, 0x6f, 0x0a, 0x5c, 0xfa, 0x32, 0x81,
	0x81, 0xab, 0xd8, 0x4a, 0xd6, 0x7d, 0xd7, 0x8f, 0x11, 0xbc, 0x73, 0xf5, 0xd2, 0x8b, 0x46, 0xae,
	0xca, 0x07, 0x6a, 0x6a, 0x3b, 0x56, 0x4c, 0x06, 0xdc, 0xe4, 0x32, 0xf3, 0x77, 0xe4, 0x23, 0xcc,
	0xda, 0xf4, 0xb6, 0x71, 0x9d, 0x80, 0x3f, 0xcc, 0x18, 0x8c, 0xba, 0xe8, 0xd5, 0x60, 0x8f, 0xde,
	0x55, 0x8b, 0xfb, 0xad, 0x33, 0x11, 0xf1, 0xb5, 0xd9, 0x97, 0x26, 0x7f, 0x86, 0x45, 0x07, 0xb2,
	0xa5, 0x3f, 0xc1, 0xbc, 0x23, 0xe6, 0x56, 0x07, 0x23, 0x9a, 0x57, 0x63, 0xdd, 0x42, 0x4a, 0x57,
	0xed, 0x58, 0x8e, 0x6e, 0x73, 0x2f, 0x60, 0xda, 0x1e, 0x90, 0x72, 0x16, 0x3b, 0x83, 0x39, 0x3c,
	0xb8, 0x50, 0x8f, 0x4a, 0xe7, 0x70, 0x82, 0x5c, 0x72, 0xd3, 0x38, 0x9c, 0x42, 0xed, 0x3a, 0x9c,
	0x02, 0x07, 0x4c, 0xc5, 0xfa, 0x88, 0x51, 0x6d, 0x77, 0x35, 0xfa, 0xed, 0x7e, 0x8f, 0xbd, 0xef,
	0xfd, 0x7f, 0xbd, 0xe5, 0xd7, 0x64, 0xbb, 0xed, 0x5f, 0xff, 0xfe, 0x97, 0xe7, 0x00, 0x00, 0x00,
	0xff, 0xff, 0xea, 0x70, 0x03, 0x7b, 0x11, 0x04, 0x00, 0x00,
}
