// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.14.0
// source: indices.proto

package pb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	IndicesApi_Config_FullMethodName  = "/pb.IndicesApi/Config"
	IndicesApi_List_FullMethodName    = "/pb.IndicesApi/List"
	IndicesApi_Save_FullMethodName    = "/pb.IndicesApi/Save"
	IndicesApi_Enable_FullMethodName  = "/pb.IndicesApi/Enable"
	IndicesApi_Delete_FullMethodName  = "/pb.IndicesApi/Delete"
	IndicesApi_Release_FullMethodName = "/pb.IndicesApi/Release"
)

// IndicesApiClient is the client API for IndicesApi service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type IndicesApiClient interface {
	// 顶部数据-配置内容列表接口
	Config(ctx context.Context, in *ProjectLang, opts ...grpc.CallOption) (*IndicesCfgResp, error)
	// 顶部数据-列表接口
	List(ctx context.Context, in *ProjectLang, opts ...grpc.CallOption) (*IndicesListResp, error)
	// 顶部数据-新增/保存接口
	Save(ctx context.Context, in *IndicesSaveReq, opts ...grpc.CallOption) (*Empty, error)
	// 顶部数据-启用/禁用接口
	Enable(ctx context.Context, in *IndicesEnableReq, opts ...grpc.CallOption) (*Empty, error)
	// 顶部数据-删除接口
	Delete(ctx context.Context, in *IndicesDelReq, opts ...grpc.CallOption) (*Empty, error)
	// 顶部数据-发布接口
	Release(ctx context.Context, in *ProjectLang, opts ...grpc.CallOption) (*Empty, error)
}

type indicesApiClient struct {
	cc grpc.ClientConnInterface
}

func NewIndicesApiClient(cc grpc.ClientConnInterface) IndicesApiClient {
	return &indicesApiClient{cc}
}

func (c *indicesApiClient) Config(ctx context.Context, in *ProjectLang, opts ...grpc.CallOption) (*IndicesCfgResp, error) {
	out := new(IndicesCfgResp)
	err := c.cc.Invoke(ctx, IndicesApi_Config_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *indicesApiClient) List(ctx context.Context, in *ProjectLang, opts ...grpc.CallOption) (*IndicesListResp, error) {
	out := new(IndicesListResp)
	err := c.cc.Invoke(ctx, IndicesApi_List_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *indicesApiClient) Save(ctx context.Context, in *IndicesSaveReq, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, IndicesApi_Save_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *indicesApiClient) Enable(ctx context.Context, in *IndicesEnableReq, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, IndicesApi_Enable_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *indicesApiClient) Delete(ctx context.Context, in *IndicesDelReq, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, IndicesApi_Delete_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *indicesApiClient) Release(ctx context.Context, in *ProjectLang, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, IndicesApi_Release_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// IndicesApiServer is the server API for IndicesApi service.
// All implementations must embed UnimplementedIndicesApiServer
// for forward compatibility
type IndicesApiServer interface {
	// 顶部数据-配置内容列表接口
	Config(context.Context, *ProjectLang) (*IndicesCfgResp, error)
	// 顶部数据-列表接口
	List(context.Context, *ProjectLang) (*IndicesListResp, error)
	// 顶部数据-新增/保存接口
	Save(context.Context, *IndicesSaveReq) (*Empty, error)
	// 顶部数据-启用/禁用接口
	Enable(context.Context, *IndicesEnableReq) (*Empty, error)
	// 顶部数据-删除接口
	Delete(context.Context, *IndicesDelReq) (*Empty, error)
	// 顶部数据-发布接口
	Release(context.Context, *ProjectLang) (*Empty, error)
	mustEmbedUnimplementedIndicesApiServer()
}

// UnimplementedIndicesApiServer must be embedded to have forward compatible implementations.
type UnimplementedIndicesApiServer struct {
}

func (UnimplementedIndicesApiServer) Config(context.Context, *ProjectLang) (*IndicesCfgResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Config not implemented")
}
func (UnimplementedIndicesApiServer) List(context.Context, *ProjectLang) (*IndicesListResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method List not implemented")
}
func (UnimplementedIndicesApiServer) Save(context.Context, *IndicesSaveReq) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Save not implemented")
}
func (UnimplementedIndicesApiServer) Enable(context.Context, *IndicesEnableReq) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Enable not implemented")
}
func (UnimplementedIndicesApiServer) Delete(context.Context, *IndicesDelReq) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Delete not implemented")
}
func (UnimplementedIndicesApiServer) Release(context.Context, *ProjectLang) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Release not implemented")
}
func (UnimplementedIndicesApiServer) mustEmbedUnimplementedIndicesApiServer() {}

// UnsafeIndicesApiServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to IndicesApiServer will
// result in compilation errors.
type UnsafeIndicesApiServer interface {
	mustEmbedUnimplementedIndicesApiServer()
}

func RegisterIndicesApiServer(s grpc.ServiceRegistrar, srv IndicesApiServer) {
	s.RegisterService(&IndicesApi_ServiceDesc, srv)
}

func _IndicesApi_Config_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ProjectLang)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(IndicesApiServer).Config(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: IndicesApi_Config_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(IndicesApiServer).Config(ctx, req.(*ProjectLang))
	}
	return interceptor(ctx, in, info, handler)
}

func _IndicesApi_List_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ProjectLang)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(IndicesApiServer).List(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: IndicesApi_List_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(IndicesApiServer).List(ctx, req.(*ProjectLang))
	}
	return interceptor(ctx, in, info, handler)
}

func _IndicesApi_Save_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IndicesSaveReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(IndicesApiServer).Save(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: IndicesApi_Save_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(IndicesApiServer).Save(ctx, req.(*IndicesSaveReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _IndicesApi_Enable_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IndicesEnableReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(IndicesApiServer).Enable(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: IndicesApi_Enable_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(IndicesApiServer).Enable(ctx, req.(*IndicesEnableReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _IndicesApi_Delete_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IndicesDelReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(IndicesApiServer).Delete(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: IndicesApi_Delete_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(IndicesApiServer).Delete(ctx, req.(*IndicesDelReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _IndicesApi_Release_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ProjectLang)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(IndicesApiServer).Release(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: IndicesApi_Release_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(IndicesApiServer).Release(ctx, req.(*ProjectLang))
	}
	return interceptor(ctx, in, info, handler)
}

// IndicesApi_ServiceDesc is the grpc.ServiceDesc for IndicesApi service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var IndicesApi_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "pb.IndicesApi",
	HandlerType: (*IndicesApiServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Config",
			Handler:    _IndicesApi_Config_Handler,
		},
		{
			MethodName: "List",
			Handler:    _IndicesApi_List_Handler,
		},
		{
			MethodName: "Save",
			Handler:    _IndicesApi_Save_Handler,
		},
		{
			MethodName: "Enable",
			Handler:    _IndicesApi_Enable_Handler,
		},
		{
			MethodName: "Delete",
			Handler:    _IndicesApi_Delete_Handler,
		},
		{
			MethodName: "Release",
			Handler:    _IndicesApi_Release_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "indices.proto",
}
