// Code generated by protoc-gen-go. DO NOT EDIT.
// source: batch_ticket.proto

package pb

import (
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

// TicketBatchTagReq 工单批量打标签
type TicketRetaggingBatchReq struct {
	// 工单ID @gotags: validate:"required"
	TicketIds []uint64 `protobuf:"varint,1,rep,packed,name=ticket_ids,json=ticketIds,proto3" json:"ticket_ids" validate:"required"`
	// 要打的标签ID集合 @gotags: validate:"required"
	LabelId              []uint32 `protobuf:"varint,2,rep,packed,name=label_id,json=labelId,proto3" json:"label_id" validate:"required"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *TicketRetaggingBatchReq) Reset()         { *m = TicketRetaggingBatchReq{} }
func (m *TicketRetaggingBatchReq) String() string { return proto.CompactTextString(m) }
func (*TicketRetaggingBatchReq) ProtoMessage()    {}
func (*TicketRetaggingBatchReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_caa130931b48b631, []int{0}
}

func (m *TicketRetaggingBatchReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TicketRetaggingBatchReq.Unmarshal(m, b)
}
func (m *TicketRetaggingBatchReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TicketRetaggingBatchReq.Marshal(b, m, deterministic)
}
func (m *TicketRetaggingBatchReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TicketRetaggingBatchReq.Merge(m, src)
}
func (m *TicketRetaggingBatchReq) XXX_Size() int {
	return xxx_messageInfo_TicketRetaggingBatchReq.Size(m)
}
func (m *TicketRetaggingBatchReq) XXX_DiscardUnknown() {
	xxx_messageInfo_TicketRetaggingBatchReq.DiscardUnknown(m)
}

var xxx_messageInfo_TicketRetaggingBatchReq proto.InternalMessageInfo

func (m *TicketRetaggingBatchReq) GetTicketIds() []uint64 {
	if m != nil {
		return m.TicketIds
	}
	return nil
}

func (m *TicketRetaggingBatchReq) GetLabelId() []uint32 {
	if m != nil {
		return m.LabelId
	}
	return nil
}

type TicketBatchTransferReq struct {
	// 工单ID @gotags: validate:"required"
	TicketIds []uint64 `protobuf:"varint,1,rep,packed,name=ticket_ids,json=ticketIds,proto3" json:"ticket_ids" validate:"required"`
	// 操作分类：1:客服回复;2:回复&关单;3:拒单 @gotags: validate:"required,gt=0"
	OpType TkTransferOpType `protobuf:"varint,2,opt,name=op_type,json=opType,proto3,enum=pb.TkTransferOpType" json:"op_type" validate:"required,gt=0"`
	// 回复内容
	Content string `protobuf:"bytes,3,opt,name=content,proto3" json:"content"`
	// 是否同步到ai精灵
	IsSyncAiElfin bool `protobuf:"varint,4,opt,name=is_sync_ai_elfin,json=isSyncAiElfin,proto3" json:"is_sync_ai_elfin"`
	// 问题
	Question string `protobuf:"bytes,5,opt,name=question,proto3" json:"question"`
	// 答案
	Answer               string   `protobuf:"bytes,6,opt,name=answer,proto3" json:"answer"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *TicketBatchTransferReq) Reset()         { *m = TicketBatchTransferReq{} }
func (m *TicketBatchTransferReq) String() string { return proto.CompactTextString(m) }
func (*TicketBatchTransferReq) ProtoMessage()    {}
func (*TicketBatchTransferReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_caa130931b48b631, []int{1}
}

func (m *TicketBatchTransferReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TicketBatchTransferReq.Unmarshal(m, b)
}
func (m *TicketBatchTransferReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TicketBatchTransferReq.Marshal(b, m, deterministic)
}
func (m *TicketBatchTransferReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TicketBatchTransferReq.Merge(m, src)
}
func (m *TicketBatchTransferReq) XXX_Size() int {
	return xxx_messageInfo_TicketBatchTransferReq.Size(m)
}
func (m *TicketBatchTransferReq) XXX_DiscardUnknown() {
	xxx_messageInfo_TicketBatchTransferReq.DiscardUnknown(m)
}

var xxx_messageInfo_TicketBatchTransferReq proto.InternalMessageInfo

func (m *TicketBatchTransferReq) GetTicketIds() []uint64 {
	if m != nil {
		return m.TicketIds
	}
	return nil
}

func (m *TicketBatchTransferReq) GetOpType() TkTransferOpType {
	if m != nil {
		return m.OpType
	}
	return TkTransferOpType_TkTransferOpTypeUnknown
}

func (m *TicketBatchTransferReq) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *TicketBatchTransferReq) GetIsSyncAiElfin() bool {
	if m != nil {
		return m.IsSyncAiElfin
	}
	return false
}

func (m *TicketBatchTransferReq) GetQuestion() string {
	if m != nil {
		return m.Question
	}
	return ""
}

func (m *TicketBatchTransferReq) GetAnswer() string {
	if m != nil {
		return m.Answer
	}
	return ""
}

// TicketPublicTagReq 工单公共标签
type TicketPublicTagReq struct {
	// 工单ID @gotags: validate:"required"
	TicketIds            []uint64 `protobuf:"varint,1,rep,packed,name=ticket_ids,json=ticketIds,proto3" json:"ticket_ids" validate:"required"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *TicketPublicTagReq) Reset()         { *m = TicketPublicTagReq{} }
func (m *TicketPublicTagReq) String() string { return proto.CompactTextString(m) }
func (*TicketPublicTagReq) ProtoMessage()    {}
func (*TicketPublicTagReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_caa130931b48b631, []int{2}
}

func (m *TicketPublicTagReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TicketPublicTagReq.Unmarshal(m, b)
}
func (m *TicketPublicTagReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TicketPublicTagReq.Marshal(b, m, deterministic)
}
func (m *TicketPublicTagReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TicketPublicTagReq.Merge(m, src)
}
func (m *TicketPublicTagReq) XXX_Size() int {
	return xxx_messageInfo_TicketPublicTagReq.Size(m)
}
func (m *TicketPublicTagReq) XXX_DiscardUnknown() {
	xxx_messageInfo_TicketPublicTagReq.DiscardUnknown(m)
}

var xxx_messageInfo_TicketPublicTagReq proto.InternalMessageInfo

func (m *TicketPublicTagReq) GetTicketIds() []uint64 {
	if m != nil {
		return m.TicketIds
	}
	return nil
}

type TicketPublicTagResp struct {
	Tags                 []*TicketPublicTagResp_TagInfo `protobuf:"bytes,1,rep,name=tags,proto3" json:"tags"`
	XXX_NoUnkeyedLiteral struct{}                       `json:"-" gorm:"-"`
	XXX_unrecognized     []byte                         `json:"-" gorm:"-"`
	XXX_sizecache        int32                          `json:"-" gorm:"-"`
}

func (m *TicketPublicTagResp) Reset()         { *m = TicketPublicTagResp{} }
func (m *TicketPublicTagResp) String() string { return proto.CompactTextString(m) }
func (*TicketPublicTagResp) ProtoMessage()    {}
func (*TicketPublicTagResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_caa130931b48b631, []int{3}
}

func (m *TicketPublicTagResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TicketPublicTagResp.Unmarshal(m, b)
}
func (m *TicketPublicTagResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TicketPublicTagResp.Marshal(b, m, deterministic)
}
func (m *TicketPublicTagResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TicketPublicTagResp.Merge(m, src)
}
func (m *TicketPublicTagResp) XXX_Size() int {
	return xxx_messageInfo_TicketPublicTagResp.Size(m)
}
func (m *TicketPublicTagResp) XXX_DiscardUnknown() {
	xxx_messageInfo_TicketPublicTagResp.DiscardUnknown(m)
}

var xxx_messageInfo_TicketPublicTagResp proto.InternalMessageInfo

func (m *TicketPublicTagResp) GetTags() []*TicketPublicTagResp_TagInfo {
	if m != nil {
		return m.Tags
	}
	return nil
}

type TicketPublicTagResp_TagInfo struct {
	// 标签ID
	TagId uint32 `protobuf:"varint,1,opt,name=tag_id,json=tagId,proto3" json:"tag_id"`
	// 标签名称
	TagName              string   `protobuf:"bytes,2,opt,name=tag_name,json=tagName,proto3" json:"tag_name"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *TicketPublicTagResp_TagInfo) Reset()         { *m = TicketPublicTagResp_TagInfo{} }
func (m *TicketPublicTagResp_TagInfo) String() string { return proto.CompactTextString(m) }
func (*TicketPublicTagResp_TagInfo) ProtoMessage()    {}
func (*TicketPublicTagResp_TagInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_caa130931b48b631, []int{3, 0}
}

func (m *TicketPublicTagResp_TagInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TicketPublicTagResp_TagInfo.Unmarshal(m, b)
}
func (m *TicketPublicTagResp_TagInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TicketPublicTagResp_TagInfo.Marshal(b, m, deterministic)
}
func (m *TicketPublicTagResp_TagInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TicketPublicTagResp_TagInfo.Merge(m, src)
}
func (m *TicketPublicTagResp_TagInfo) XXX_Size() int {
	return xxx_messageInfo_TicketPublicTagResp_TagInfo.Size(m)
}
func (m *TicketPublicTagResp_TagInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_TicketPublicTagResp_TagInfo.DiscardUnknown(m)
}

var xxx_messageInfo_TicketPublicTagResp_TagInfo proto.InternalMessageInfo

func (m *TicketPublicTagResp_TagInfo) GetTagId() uint32 {
	if m != nil {
		return m.TagId
	}
	return 0
}

func (m *TicketPublicTagResp_TagInfo) GetTagName() string {
	if m != nil {
		return m.TagName
	}
	return ""
}

type TicketTagBatchDelete struct {
	// 待删除工单ID @gotags: validate:"required"
	TicketIds []uint64 `protobuf:"varint,1,rep,packed,name=ticket_ids,json=ticketIds,proto3" json:"ticket_ids" validate:"required"`
	// 待删除标签ID @gotags: validate:"required"
	TagIds               []uint32 `protobuf:"varint,2,rep,packed,name=tag_ids,json=tagIds,proto3" json:"tag_ids" validate:"required"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *TicketTagBatchDelete) Reset()         { *m = TicketTagBatchDelete{} }
func (m *TicketTagBatchDelete) String() string { return proto.CompactTextString(m) }
func (*TicketTagBatchDelete) ProtoMessage()    {}
func (*TicketTagBatchDelete) Descriptor() ([]byte, []int) {
	return fileDescriptor_caa130931b48b631, []int{4}
}

func (m *TicketTagBatchDelete) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TicketTagBatchDelete.Unmarshal(m, b)
}
func (m *TicketTagBatchDelete) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TicketTagBatchDelete.Marshal(b, m, deterministic)
}
func (m *TicketTagBatchDelete) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TicketTagBatchDelete.Merge(m, src)
}
func (m *TicketTagBatchDelete) XXX_Size() int {
	return xxx_messageInfo_TicketTagBatchDelete.Size(m)
}
func (m *TicketTagBatchDelete) XXX_DiscardUnknown() {
	xxx_messageInfo_TicketTagBatchDelete.DiscardUnknown(m)
}

var xxx_messageInfo_TicketTagBatchDelete proto.InternalMessageInfo

func (m *TicketTagBatchDelete) GetTicketIds() []uint64 {
	if m != nil {
		return m.TicketIds
	}
	return nil
}

func (m *TicketTagBatchDelete) GetTagIds() []uint32 {
	if m != nil {
		return m.TagIds
	}
	return nil
}

func init() {
	proto.RegisterType((*TicketRetaggingBatchReq)(nil), "pb.TicketRetaggingBatchReq")
	proto.RegisterType((*TicketBatchTransferReq)(nil), "pb.TicketBatchTransferReq")
	proto.RegisterType((*TicketPublicTagReq)(nil), "pb.TicketPublicTagReq")
	proto.RegisterType((*TicketPublicTagResp)(nil), "pb.TicketPublicTagResp")
	proto.RegisterType((*TicketPublicTagResp_TagInfo)(nil), "pb.TicketPublicTagResp.TagInfo")
	proto.RegisterType((*TicketTagBatchDelete)(nil), "pb.TicketTagBatchDelete")
}

func init() {
	proto.RegisterFile("batch_ticket.proto", fileDescriptor_caa130931b48b631)
}

var fileDescriptor_caa130931b48b631 = []byte{
	// 383 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x8c, 0x52, 0xc1, 0x8e, 0xd3, 0x30,
	0x14, 0x94, 0xdb, 0x6c, 0xd2, 0x3e, 0x28, 0x42, 0x66, 0xd9, 0x35, 0x95, 0x10, 0x51, 0x2e, 0xe4,
	0x42, 0x0e, 0xdb, 0xe3, 0x9e, 0x58, 0xc1, 0x21, 0x97, 0x82, 0xdc, 0x9c, 0xb8, 0x44, 0x4e, 0xe2,
	0x1a, 0xab, 0xa9, 0xe3, 0xc6, 0xae, 0x50, 0xbe, 0x80, 0xaf, 0xe4, 0x5f, 0x50, 0xec, 0x16, 0x21,
	0xf5, 0xd0, 0x3d, 0x59, 0x33, 0x7e, 0x6f, 0x46, 0xe3, 0x31, 0xe0, 0x8a, 0xd9, 0xfa, 0x67, 0x69,
	0x65, 0xbd, 0xe3, 0x36, 0xd3, 0x7d, 0x67, 0x3b, 0x3c, 0xd1, 0xd5, 0xf2, 0xe5, 0xff, 0x4c, 0xb2,
	0x81, 0xfb, 0xc2, 0x61, 0xca, 0x2d, 0x13, 0x42, 0x2a, 0xf1, 0x34, 0xae, 0x51, 0x7e, 0xc0, 0xef,
	0x01, 0xfc, 0x68, 0x29, 0x1b, 0x43, 0x50, 0x3c, 0x4d, 0x03, 0x3a, 0xf7, 0x4c, 0xde, 0x18, 0xfc,
	0x0e, 0x66, 0x2d, 0xab, 0x78, 0x5b, 0xca, 0x86, 0x4c, 0xe2, 0x69, 0xba, 0xa0, 0x91, 0xc3, 0x79,
	0x93, 0xfc, 0x41, 0x70, 0xe7, 0x55, 0x9d, 0x58, 0xd1, 0x33, 0x65, 0xb6, 0xbc, 0x7f, 0x86, 0xe8,
	0x27, 0x88, 0x3a, 0x5d, 0xda, 0x41, 0x73, 0x32, 0x89, 0x51, 0xfa, 0xea, 0xe1, 0x36, 0xd3, 0x55,
	0x56, 0xec, 0xce, 0x12, 0xdf, 0x74, 0x31, 0x68, 0x4e, 0xc3, 0xce, 0x9d, 0x98, 0x40, 0x54, 0x77,
	0xca, 0x72, 0x65, 0xc9, 0x34, 0x46, 0xe9, 0x9c, 0x9e, 0x21, 0xfe, 0x08, 0xaf, 0xa5, 0x29, 0xcd,
	0xa0, 0xea, 0x92, 0xc9, 0x92, 0xb7, 0x5b, 0xa9, 0x48, 0x10, 0xa3, 0x74, 0x46, 0x17, 0xd2, 0x6c,
	0x06, 0x55, 0x7f, 0x96, 0x5f, 0x47, 0x12, 0x2f, 0x61, 0x76, 0x38, 0x72, 0x63, 0x65, 0xa7, 0xc8,
	0x8d, 0xd3, 0xf8, 0x87, 0xf1, 0x1d, 0x84, 0x4c, 0x99, 0x5f, 0xbc, 0x27, 0xa1, 0xbb, 0x39, 0xa1,
	0x64, 0x05, 0xd8, 0xc7, 0xfb, 0x7e, 0xac, 0x5a, 0x59, 0x17, 0x4c, 0x5c, 0x8f, 0x96, 0xfc, 0x46,
	0xf0, 0xe6, 0x62, 0xcb, 0x68, 0xbc, 0x82, 0xc0, 0x32, 0xe1, 0x17, 0x5e, 0x3c, 0x7c, 0x70, 0x79,
	0x2f, 0xc7, 0xb2, 0x82, 0x89, 0x5c, 0x6d, 0x3b, 0xea, 0x86, 0x97, 0x8f, 0x10, 0x9d, 0x08, 0xfc,
	0x16, 0x42, 0xcb, 0xc4, 0xd8, 0x02, 0x8a, 0x51, 0xba, 0xa0, 0x37, 0x96, 0x89, 0xbc, 0x19, 0xeb,
	0x19, 0x69, 0xc5, 0xf6, 0xfe, 0x29, 0xe7, 0x34, 0xb2, 0x4c, 0xac, 0xd9, 0x9e, 0x27, 0x6b, 0xb8,
	0xf5, 0x0e, 0x05, 0xf3, 0x6d, 0x7f, 0xe1, 0x2d, 0xb7, 0xfc, 0x5a, 0x37, 0xf7, 0x10, 0x79, 0x23,
	0x73, 0xea, 0x3b, 0x74, 0x4e, 0xe6, 0x29, 0xfc, 0x11, 0x64, 0x8f, 0xba, 0xaa, 0x42, 0xf7, 0xa5,
	0x56, 0x7f, 0x03, 0x00, 0x00, 0xff, 0xff, 0x2a, 0x39, 0x13, 0x7b, 0x7a, 0x02, 0x00, 0x00,
}
