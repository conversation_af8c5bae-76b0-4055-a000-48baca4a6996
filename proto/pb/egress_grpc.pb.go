// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.14.0
// source: egress.proto

package pb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	EgressAPI_TkSdkNoticeV2_FullMethodName      = "/pb.EgressAPI/TkSdkNoticeV2"
	EgressAPI_TkCreate_FullMethodName           = "/pb.EgressAPI/TkCreate"
	EgressAPI_TkMine_FullMethodName             = "/pb.EgressAPI/TkMine"
	EgressAPI_TkDetail_FullMethodName           = "/pb.EgressAPI/TkDetail"
	EgressAPI_TkReplenish_FullMethodName        = "/pb.EgressAPI/TkReplenish"
	EgressAPI_TkReopen_FullMethodName           = "/pb.EgressAPI/TkReopen"
	EgressAPI_TkAppraise_FullMethodName         = "/pb.EgressAPI/TkAppraise"
	EgressAPI_TkAppraiseFeedback_FullMethodName = "/pb.EgressAPI/TkAppraiseFeedback"
	EgressAPI_TkCommunicate_FullMethodName      = "/pb.EgressAPI/TkCommunicate"
	EgressAPI_CategoryInfo_FullMethodName       = "/pb.EgressAPI/CategoryInfo"
)

// EgressAPIClient is the client API for EgressAPI service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type EgressAPIClient interface {
	// 工单 - sdk - 红点v2
	TkSdkNoticeV2(ctx context.Context, in *NoticeRequestV2, opts ...grpc.CallOption) (*NoticeResp, error)
	// 工单 - 创建工单接口
	TkCreate(ctx context.Context, in *TkCreateReq, opts ...grpc.CallOption) (*TkCreateResp, error)
	// 工单 - 创建工单接口
	TkMine(ctx context.Context, in *TkMineReq, opts ...grpc.CallOption) (*TkMineResp, error)
	// 工单 - 工单详情接口
	TkDetail(ctx context.Context, in *TkDetailReq, opts ...grpc.CallOption) (*TkDetailResp, error)
	// 工单 - 工单补填接口
	TkReplenish(ctx context.Context, in *TkReplenishReq, opts ...grpc.CallOption) (*Empty, error)
	// 工单 - 工单重开接口
	TkReopen(ctx context.Context, in *TkReplenishReq, opts ...grpc.CallOption) (*Empty, error)
	// 工单 - 工单评价接口
	TkAppraise(ctx context.Context, in *TkAppraiseReq, opts ...grpc.CallOption) (*Empty, error)
	// 工单 - 工单评价反馈接口
	TkAppraiseFeedback(ctx context.Context, in *TkAppraiseFeedbackReq, opts ...grpc.CallOption) (*Empty, error)
	// 工单 - 给客服发消息
	TkCommunicate(ctx context.Context, in *TkCommunicateReq, opts ...grpc.CallOption) (*Empty, error)
	// 三级分类信息
	CategoryInfo(ctx context.Context, in *CatSubRequest, opts ...grpc.CallOption) (*EgressCatInfoResp, error)
}

type egressAPIClient struct {
	cc grpc.ClientConnInterface
}

func NewEgressAPIClient(cc grpc.ClientConnInterface) EgressAPIClient {
	return &egressAPIClient{cc}
}

func (c *egressAPIClient) TkSdkNoticeV2(ctx context.Context, in *NoticeRequestV2, opts ...grpc.CallOption) (*NoticeResp, error) {
	out := new(NoticeResp)
	err := c.cc.Invoke(ctx, EgressAPI_TkSdkNoticeV2_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *egressAPIClient) TkCreate(ctx context.Context, in *TkCreateReq, opts ...grpc.CallOption) (*TkCreateResp, error) {
	out := new(TkCreateResp)
	err := c.cc.Invoke(ctx, EgressAPI_TkCreate_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *egressAPIClient) TkMine(ctx context.Context, in *TkMineReq, opts ...grpc.CallOption) (*TkMineResp, error) {
	out := new(TkMineResp)
	err := c.cc.Invoke(ctx, EgressAPI_TkMine_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *egressAPIClient) TkDetail(ctx context.Context, in *TkDetailReq, opts ...grpc.CallOption) (*TkDetailResp, error) {
	out := new(TkDetailResp)
	err := c.cc.Invoke(ctx, EgressAPI_TkDetail_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *egressAPIClient) TkReplenish(ctx context.Context, in *TkReplenishReq, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, EgressAPI_TkReplenish_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *egressAPIClient) TkReopen(ctx context.Context, in *TkReplenishReq, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, EgressAPI_TkReopen_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *egressAPIClient) TkAppraise(ctx context.Context, in *TkAppraiseReq, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, EgressAPI_TkAppraise_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *egressAPIClient) TkAppraiseFeedback(ctx context.Context, in *TkAppraiseFeedbackReq, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, EgressAPI_TkAppraiseFeedback_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *egressAPIClient) TkCommunicate(ctx context.Context, in *TkCommunicateReq, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, EgressAPI_TkCommunicate_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *egressAPIClient) CategoryInfo(ctx context.Context, in *CatSubRequest, opts ...grpc.CallOption) (*EgressCatInfoResp, error) {
	out := new(EgressCatInfoResp)
	err := c.cc.Invoke(ctx, EgressAPI_CategoryInfo_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// EgressAPIServer is the server API for EgressAPI service.
// All implementations must embed UnimplementedEgressAPIServer
// for forward compatibility
type EgressAPIServer interface {
	// 工单 - sdk - 红点v2
	TkSdkNoticeV2(context.Context, *NoticeRequestV2) (*NoticeResp, error)
	// 工单 - 创建工单接口
	TkCreate(context.Context, *TkCreateReq) (*TkCreateResp, error)
	// 工单 - 创建工单接口
	TkMine(context.Context, *TkMineReq) (*TkMineResp, error)
	// 工单 - 工单详情接口
	TkDetail(context.Context, *TkDetailReq) (*TkDetailResp, error)
	// 工单 - 工单补填接口
	TkReplenish(context.Context, *TkReplenishReq) (*Empty, error)
	// 工单 - 工单重开接口
	TkReopen(context.Context, *TkReplenishReq) (*Empty, error)
	// 工单 - 工单评价接口
	TkAppraise(context.Context, *TkAppraiseReq) (*Empty, error)
	// 工单 - 工单评价反馈接口
	TkAppraiseFeedback(context.Context, *TkAppraiseFeedbackReq) (*Empty, error)
	// 工单 - 给客服发消息
	TkCommunicate(context.Context, *TkCommunicateReq) (*Empty, error)
	// 三级分类信息
	CategoryInfo(context.Context, *CatSubRequest) (*EgressCatInfoResp, error)
	mustEmbedUnimplementedEgressAPIServer()
}

// UnimplementedEgressAPIServer must be embedded to have forward compatible implementations.
type UnimplementedEgressAPIServer struct {
}

func (UnimplementedEgressAPIServer) TkSdkNoticeV2(context.Context, *NoticeRequestV2) (*NoticeResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TkSdkNoticeV2 not implemented")
}
func (UnimplementedEgressAPIServer) TkCreate(context.Context, *TkCreateReq) (*TkCreateResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TkCreate not implemented")
}
func (UnimplementedEgressAPIServer) TkMine(context.Context, *TkMineReq) (*TkMineResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TkMine not implemented")
}
func (UnimplementedEgressAPIServer) TkDetail(context.Context, *TkDetailReq) (*TkDetailResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TkDetail not implemented")
}
func (UnimplementedEgressAPIServer) TkReplenish(context.Context, *TkReplenishReq) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TkReplenish not implemented")
}
func (UnimplementedEgressAPIServer) TkReopen(context.Context, *TkReplenishReq) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TkReopen not implemented")
}
func (UnimplementedEgressAPIServer) TkAppraise(context.Context, *TkAppraiseReq) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TkAppraise not implemented")
}
func (UnimplementedEgressAPIServer) TkAppraiseFeedback(context.Context, *TkAppraiseFeedbackReq) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TkAppraiseFeedback not implemented")
}
func (UnimplementedEgressAPIServer) TkCommunicate(context.Context, *TkCommunicateReq) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TkCommunicate not implemented")
}
func (UnimplementedEgressAPIServer) CategoryInfo(context.Context, *CatSubRequest) (*EgressCatInfoResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CategoryInfo not implemented")
}
func (UnimplementedEgressAPIServer) mustEmbedUnimplementedEgressAPIServer() {}

// UnsafeEgressAPIServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to EgressAPIServer will
// result in compilation errors.
type UnsafeEgressAPIServer interface {
	mustEmbedUnimplementedEgressAPIServer()
}

func RegisterEgressAPIServer(s grpc.ServiceRegistrar, srv EgressAPIServer) {
	s.RegisterService(&EgressAPI_ServiceDesc, srv)
}

func _EgressAPI_TkSdkNoticeV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NoticeRequestV2)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EgressAPIServer).TkSdkNoticeV2(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EgressAPI_TkSdkNoticeV2_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EgressAPIServer).TkSdkNoticeV2(ctx, req.(*NoticeRequestV2))
	}
	return interceptor(ctx, in, info, handler)
}

func _EgressAPI_TkCreate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TkCreateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EgressAPIServer).TkCreate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EgressAPI_TkCreate_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EgressAPIServer).TkCreate(ctx, req.(*TkCreateReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _EgressAPI_TkMine_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TkMineReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EgressAPIServer).TkMine(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EgressAPI_TkMine_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EgressAPIServer).TkMine(ctx, req.(*TkMineReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _EgressAPI_TkDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TkDetailReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EgressAPIServer).TkDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EgressAPI_TkDetail_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EgressAPIServer).TkDetail(ctx, req.(*TkDetailReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _EgressAPI_TkReplenish_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TkReplenishReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EgressAPIServer).TkReplenish(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EgressAPI_TkReplenish_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EgressAPIServer).TkReplenish(ctx, req.(*TkReplenishReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _EgressAPI_TkReopen_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TkReplenishReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EgressAPIServer).TkReopen(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EgressAPI_TkReopen_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EgressAPIServer).TkReopen(ctx, req.(*TkReplenishReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _EgressAPI_TkAppraise_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TkAppraiseReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EgressAPIServer).TkAppraise(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EgressAPI_TkAppraise_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EgressAPIServer).TkAppraise(ctx, req.(*TkAppraiseReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _EgressAPI_TkAppraiseFeedback_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TkAppraiseFeedbackReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EgressAPIServer).TkAppraiseFeedback(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EgressAPI_TkAppraiseFeedback_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EgressAPIServer).TkAppraiseFeedback(ctx, req.(*TkAppraiseFeedbackReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _EgressAPI_TkCommunicate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TkCommunicateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EgressAPIServer).TkCommunicate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EgressAPI_TkCommunicate_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EgressAPIServer).TkCommunicate(ctx, req.(*TkCommunicateReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _EgressAPI_CategoryInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CatSubRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EgressAPIServer).CategoryInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EgressAPI_CategoryInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EgressAPIServer).CategoryInfo(ctx, req.(*CatSubRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// EgressAPI_ServiceDesc is the grpc.ServiceDesc for EgressAPI service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var EgressAPI_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "pb.EgressAPI",
	HandlerType: (*EgressAPIServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "TkSdkNoticeV2",
			Handler:    _EgressAPI_TkSdkNoticeV2_Handler,
		},
		{
			MethodName: "TkCreate",
			Handler:    _EgressAPI_TkCreate_Handler,
		},
		{
			MethodName: "TkMine",
			Handler:    _EgressAPI_TkMine_Handler,
		},
		{
			MethodName: "TkDetail",
			Handler:    _EgressAPI_TkDetail_Handler,
		},
		{
			MethodName: "TkReplenish",
			Handler:    _EgressAPI_TkReplenish_Handler,
		},
		{
			MethodName: "TkReopen",
			Handler:    _EgressAPI_TkReopen_Handler,
		},
		{
			MethodName: "TkAppraise",
			Handler:    _EgressAPI_TkAppraise_Handler,
		},
		{
			MethodName: "TkAppraiseFeedback",
			Handler:    _EgressAPI_TkAppraiseFeedback_Handler,
		},
		{
			MethodName: "TkCommunicate",
			Handler:    _EgressAPI_TkCommunicate_Handler,
		},
		{
			MethodName: "CategoryInfo",
			Handler:    _EgressAPI_CategoryInfo_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "egress.proto",
}
