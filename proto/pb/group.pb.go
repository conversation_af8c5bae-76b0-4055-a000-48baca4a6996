// Code generated by protoc-gen-go. DO NOT EDIT.
// source: group.proto

package pb

import (
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

// GroupOptsResp 技能组筛选列表 响应结果
type GroupOptsResp struct {
	List                 []*GroupOptsResp_Opts `protobuf:"bytes,1,rep,name=list,proto3" json:"list"`
	XXX_NoUnkeyedLiteral struct{}              `json:"-" gorm:"-"`
	XXX_unrecognized     []byte                `json:"-" gorm:"-"`
	XXX_sizecache        int32                 `json:"-" gorm:"-"`
}

func (m *GroupOptsResp) Reset()         { *m = GroupOptsResp{} }
func (m *GroupOptsResp) String() string { return proto.CompactTextString(m) }
func (*GroupOptsResp) ProtoMessage()    {}
func (*GroupOptsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_e10f4c9b19ad8eee, []int{0}
}

func (m *GroupOptsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GroupOptsResp.Unmarshal(m, b)
}
func (m *GroupOptsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GroupOptsResp.Marshal(b, m, deterministic)
}
func (m *GroupOptsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GroupOptsResp.Merge(m, src)
}
func (m *GroupOptsResp) XXX_Size() int {
	return xxx_messageInfo_GroupOptsResp.Size(m)
}
func (m *GroupOptsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_GroupOptsResp.DiscardUnknown(m)
}

var xxx_messageInfo_GroupOptsResp proto.InternalMessageInfo

func (m *GroupOptsResp) GetList() []*GroupOptsResp_Opts {
	if m != nil {
		return m.List
	}
	return nil
}

type GroupOptsResp_Opts struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	GroupDesc            string   `protobuf:"bytes,2,opt,name=group_desc,json=groupDesc,proto3" json:"group_desc"`
	Game                 string   `protobuf:"bytes,3,opt,name=game,proto3" json:"game"`
	Language             string   `protobuf:"bytes,4,opt,name=language,proto3" json:"language"`
	User                 string   `protobuf:"bytes,5,opt,name=user,proto3" json:"user"`
	UpdateTime           string   `protobuf:"bytes,6,opt,name=update_time,json=updateTime,proto3" json:"update_time"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *GroupOptsResp_Opts) Reset()         { *m = GroupOptsResp_Opts{} }
func (m *GroupOptsResp_Opts) String() string { return proto.CompactTextString(m) }
func (*GroupOptsResp_Opts) ProtoMessage()    {}
func (*GroupOptsResp_Opts) Descriptor() ([]byte, []int) {
	return fileDescriptor_e10f4c9b19ad8eee, []int{0, 0}
}

func (m *GroupOptsResp_Opts) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GroupOptsResp_Opts.Unmarshal(m, b)
}
func (m *GroupOptsResp_Opts) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GroupOptsResp_Opts.Marshal(b, m, deterministic)
}
func (m *GroupOptsResp_Opts) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GroupOptsResp_Opts.Merge(m, src)
}
func (m *GroupOptsResp_Opts) XXX_Size() int {
	return xxx_messageInfo_GroupOptsResp_Opts.Size(m)
}
func (m *GroupOptsResp_Opts) XXX_DiscardUnknown() {
	xxx_messageInfo_GroupOptsResp_Opts.DiscardUnknown(m)
}

var xxx_messageInfo_GroupOptsResp_Opts proto.InternalMessageInfo

func (m *GroupOptsResp_Opts) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *GroupOptsResp_Opts) GetGroupDesc() string {
	if m != nil {
		return m.GroupDesc
	}
	return ""
}

func (m *GroupOptsResp_Opts) GetGame() string {
	if m != nil {
		return m.Game
	}
	return ""
}

func (m *GroupOptsResp_Opts) GetLanguage() string {
	if m != nil {
		return m.Language
	}
	return ""
}

func (m *GroupOptsResp_Opts) GetUser() string {
	if m != nil {
		return m.User
	}
	return ""
}

func (m *GroupOptsResp_Opts) GetUpdateTime() string {
	if m != nil {
		return m.UpdateTime
	}
	return ""
}

type GroupCat struct {
	Id uint32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	// 游戏项目 @gotags: validate:"required"
	Game string `protobuf:"bytes,2,opt,name=game,proto3" json:"game" validate:"required"`
	// 语言 @gotags: validate:"required"
	Language string `protobuf:"bytes,3,opt,name=language,proto3" json:"language" validate:"required"`
	// 问题分类 @gotags: validate:"required"
	Categories           string   `protobuf:"bytes,4,opt,name=categories,proto3" json:"categories" validate:"required"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *GroupCat) Reset()         { *m = GroupCat{} }
func (m *GroupCat) String() string { return proto.CompactTextString(m) }
func (*GroupCat) ProtoMessage()    {}
func (*GroupCat) Descriptor() ([]byte, []int) {
	return fileDescriptor_e10f4c9b19ad8eee, []int{1}
}

func (m *GroupCat) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GroupCat.Unmarshal(m, b)
}
func (m *GroupCat) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GroupCat.Marshal(b, m, deterministic)
}
func (m *GroupCat) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GroupCat.Merge(m, src)
}
func (m *GroupCat) XXX_Size() int {
	return xxx_messageInfo_GroupCat.Size(m)
}
func (m *GroupCat) XXX_DiscardUnknown() {
	xxx_messageInfo_GroupCat.DiscardUnknown(m)
}

var xxx_messageInfo_GroupCat proto.InternalMessageInfo

func (m *GroupCat) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *GroupCat) GetGame() string {
	if m != nil {
		return m.Game
	}
	return ""
}

func (m *GroupCat) GetLanguage() string {
	if m != nil {
		return m.Language
	}
	return ""
}

func (m *GroupCat) GetCategories() string {
	if m != nil {
		return m.Categories
	}
	return ""
}

type GroupUser struct {
	// 人员
	User                 uint32   `protobuf:"varint,1,opt,name=user,proto3" json:"user"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *GroupUser) Reset()         { *m = GroupUser{} }
func (m *GroupUser) String() string { return proto.CompactTextString(m) }
func (*GroupUser) ProtoMessage()    {}
func (*GroupUser) Descriptor() ([]byte, []int) {
	return fileDescriptor_e10f4c9b19ad8eee, []int{2}
}

func (m *GroupUser) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GroupUser.Unmarshal(m, b)
}
func (m *GroupUser) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GroupUser.Marshal(b, m, deterministic)
}
func (m *GroupUser) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GroupUser.Merge(m, src)
}
func (m *GroupUser) XXX_Size() int {
	return xxx_messageInfo_GroupUser.Size(m)
}
func (m *GroupUser) XXX_DiscardUnknown() {
	xxx_messageInfo_GroupUser.DiscardUnknown(m)
}

var xxx_messageInfo_GroupUser proto.InternalMessageInfo

func (m *GroupUser) GetUser() uint32 {
	if m != nil {
		return m.User
	}
	return 0
}

type LoginStatus struct {
	IsLogin              int32    `protobuf:"varint,1,opt,name=is_login,json=isLogin,proto3" json:"is_login"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *LoginStatus) Reset()         { *m = LoginStatus{} }
func (m *LoginStatus) String() string { return proto.CompactTextString(m) }
func (*LoginStatus) ProtoMessage()    {}
func (*LoginStatus) Descriptor() ([]byte, []int) {
	return fileDescriptor_e10f4c9b19ad8eee, []int{3}
}

func (m *LoginStatus) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LoginStatus.Unmarshal(m, b)
}
func (m *LoginStatus) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LoginStatus.Marshal(b, m, deterministic)
}
func (m *LoginStatus) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LoginStatus.Merge(m, src)
}
func (m *LoginStatus) XXX_Size() int {
	return xxx_messageInfo_LoginStatus.Size(m)
}
func (m *LoginStatus) XXX_DiscardUnknown() {
	xxx_messageInfo_LoginStatus.DiscardUnknown(m)
}

var xxx_messageInfo_LoginStatus proto.InternalMessageInfo

func (m *LoginStatus) GetIsLogin() int32 {
	if m != nil {
		return m.IsLogin
	}
	return 0
}

type GroupUserStateDf struct {
	UpperLimit           int64    `protobuf:"varint,1,opt,name=UpperLimit,proto3" json:"UpperLimit"`
	Game                 string   `protobuf:"bytes,2,opt,name=Game,proto3" json:"Game"`
	Language             string   `protobuf:"bytes,3,opt,name=Language,proto3" json:"Language"`
	IsLogin              int32    `protobuf:"varint,4,opt,name=IsLogin,proto3" json:"IsLogin"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *GroupUserStateDf) Reset()         { *m = GroupUserStateDf{} }
func (m *GroupUserStateDf) String() string { return proto.CompactTextString(m) }
func (*GroupUserStateDf) ProtoMessage()    {}
func (*GroupUserStateDf) Descriptor() ([]byte, []int) {
	return fileDescriptor_e10f4c9b19ad8eee, []int{4}
}

func (m *GroupUserStateDf) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GroupUserStateDf.Unmarshal(m, b)
}
func (m *GroupUserStateDf) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GroupUserStateDf.Marshal(b, m, deterministic)
}
func (m *GroupUserStateDf) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GroupUserStateDf.Merge(m, src)
}
func (m *GroupUserStateDf) XXX_Size() int {
	return xxx_messageInfo_GroupUserStateDf.Size(m)
}
func (m *GroupUserStateDf) XXX_DiscardUnknown() {
	xxx_messageInfo_GroupUserStateDf.DiscardUnknown(m)
}

var xxx_messageInfo_GroupUserStateDf proto.InternalMessageInfo

func (m *GroupUserStateDf) GetUpperLimit() int64 {
	if m != nil {
		return m.UpperLimit
	}
	return 0
}

func (m *GroupUserStateDf) GetGame() string {
	if m != nil {
		return m.Game
	}
	return ""
}

func (m *GroupUserStateDf) GetLanguage() string {
	if m != nil {
		return m.Language
	}
	return ""
}

func (m *GroupUserStateDf) GetIsLogin() int32 {
	if m != nil {
		return m.IsLogin
	}
	return 0
}

func init() {
	proto.RegisterType((*GroupOptsResp)(nil), "pb.GroupOptsResp")
	proto.RegisterType((*GroupOptsResp_Opts)(nil), "pb.GroupOptsResp.Opts")
	proto.RegisterType((*GroupCat)(nil), "pb.GroupCat")
	proto.RegisterType((*GroupUser)(nil), "pb.GroupUser")
	proto.RegisterType((*LoginStatus)(nil), "pb.LoginStatus")
	proto.RegisterType((*GroupUserStateDf)(nil), "pb.GroupUserStateDf")
}

func init() {
	proto.RegisterFile("group.proto", fileDescriptor_e10f4c9b19ad8eee)
}

var fileDescriptor_e10f4c9b19ad8eee = []byte{
	// 333 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x74, 0x92, 0x4f, 0x4b, 0xc3, 0x40,
	0x10, 0xc5, 0xc9, 0x9f, 0xfe, 0x9b, 0x50, 0x91, 0x3d, 0xc8, 0x5a, 0xd0, 0x96, 0x9c, 0x82, 0x87,
	0x1c, 0xf4, 0xe8, 0x4d, 0x0b, 0x45, 0x28, 0x08, 0xd1, 0x5e, 0xbc, 0x94, 0x4d, 0x33, 0x2e, 0x2b,
	0x4d, 0xb3, 0x64, 0x37, 0x37, 0x3f, 0x89, 0x9f, 0xcc, 0x8f, 0x23, 0x3b, 0x69, 0x4a, 0xad, 0x78,
	0x9b, 0x79, 0xf3, 0x92, 0xf7, 0x7b, 0xb0, 0x10, 0xc9, 0xba, 0x6a, 0x74, 0xaa, 0xeb, 0xca, 0x56,
	0xcc, 0xd7, 0x79, 0xfc, 0xed, 0xc1, 0x78, 0xe1, 0xb4, 0x67, 0x6d, 0x4d, 0x86, 0x46, 0xb3, 0x1b,
	0x08, 0xb7, 0xca, 0x58, 0xee, 0xcd, 0x82, 0x24, 0xba, 0xbd, 0x48, 0x75, 0x9e, 0xfe, 0x32, 0xa4,
	0x34, 0x90, 0x67, 0xf2, 0xe5, 0x41, 0xe8, 0x56, 0x76, 0x06, 0xbe, 0x2a, 0xb8, 0x37, 0xf3, 0x92,
	0x71, 0xe6, 0xab, 0x82, 0x5d, 0x01, 0x50, 0xd2, 0xba, 0x40, 0xb3, 0xe1, 0xfe, 0xcc, 0x4b, 0x46,
	0xd9, 0x88, 0x94, 0x39, 0x9a, 0x0d, 0x63, 0x10, 0x4a, 0x51, 0x22, 0x0f, 0xe8, 0x40, 0x33, 0x9b,
	0xc0, 0x70, 0x2b, 0x76, 0xb2, 0x11, 0x12, 0x79, 0x48, 0xfa, 0x61, 0x77, 0xfe, 0xc6, 0x60, 0xcd,
	0x7b, 0xad, 0xdf, 0xcd, 0x6c, 0x0a, 0x51, 0xa3, 0x0b, 0x61, 0x71, 0x6d, 0x55, 0x89, 0xbc, 0x4f,
	0x27, 0x68, 0xa5, 0x57, 0x55, 0x62, 0xfc, 0x01, 0x43, 0x02, 0x7f, 0x14, 0xf6, 0x0f, 0x5f, 0x07,
	0xe0, 0xff, 0x03, 0x10, 0x9c, 0x00, 0x5c, 0x03, 0x6c, 0x84, 0x45, 0x59, 0xd5, 0x0a, 0xcd, 0x1e,
	0xef, 0x48, 0x89, 0xa7, 0x30, 0xa2, 0xac, 0x95, 0x23, 0xeb, 0x68, 0xdb, 0x38, 0x9a, 0xe3, 0x04,
	0xa2, 0x65, 0x25, 0xd5, 0xee, 0xc5, 0x0a, 0xdb, 0x18, 0x76, 0x09, 0x43, 0x65, 0xd6, 0x5b, 0xa7,
	0x90, 0xad, 0x97, 0x0d, 0x94, 0x21, 0x43, 0xfc, 0x09, 0xe7, 0x87, 0x5f, 0x39, 0x37, 0xce, 0xdf,
	0x5d, 0xfc, 0x4a, 0x6b, 0xac, 0x97, 0xaa, 0x54, 0x96, 0x3e, 0x08, 0xb2, 0x23, 0xc5, 0x25, 0x2e,
	0x8e, 0xea, 0x2c, 0xf6, 0x75, 0x96, 0x27, 0x75, 0xba, 0x9d, 0x71, 0x18, 0x3c, 0xb5, 0x71, 0xd4,
	0xa5, 0x97, 0x75, 0xeb, 0x43, 0xff, 0x2d, 0x4c, 0xef, 0x75, 0x9e, 0xf7, 0xe9, 0x89, 0xdc, 0xfd,
	0x04, 0x00, 0x00, 0xff, 0xff, 0x56, 0x2c, 0xeb, 0x8d, 0x31, 0x02, 0x00, 0x00,
}
