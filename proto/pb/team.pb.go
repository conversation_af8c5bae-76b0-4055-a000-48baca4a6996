// Code generated by protoc-gen-go. DO NOT EDIT.
// source: team.proto

package pb

import (
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

type TeamOptsResp struct {
	List                 []*TeamOptsResp_Opts `protobuf:"bytes,2,rep,name=list,proto3" json:"list"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-" gorm:"-"`
	XXX_unrecognized     []byte               `json:"-" gorm:"-"`
	XXX_sizecache        int32                `json:"-" gorm:"-"`
}

func (m *TeamOptsResp) Reset()         { *m = TeamOptsResp{} }
func (m *TeamOptsResp) String() string { return proto.CompactTextString(m) }
func (*TeamOptsResp) ProtoMessage()    {}
func (*TeamOptsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_8b4e9e93d7b2c6bb, []int{0}
}

func (m *TeamOptsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TeamOptsResp.Unmarshal(m, b)
}
func (m *TeamOptsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TeamOptsResp.Marshal(b, m, deterministic)
}
func (m *TeamOptsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TeamOptsResp.Merge(m, src)
}
func (m *TeamOptsResp) XXX_Size() int {
	return xxx_messageInfo_TeamOptsResp.Size(m)
}
func (m *TeamOptsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_TeamOptsResp.DiscardUnknown(m)
}

var xxx_messageInfo_TeamOptsResp proto.InternalMessageInfo

func (m *TeamOptsResp) GetList() []*TeamOptsResp_Opts {
	if m != nil {
		return m.List
	}
	return nil
}

type TeamOptsResp_Opts struct {
	// 团队ID
	TeamId uint32 `protobuf:"varint,1,opt,name=team_id,json=teamId,proto3" json:"team_id"`
	// 团队名称
	TeamName             string   `protobuf:"bytes,2,opt,name=team_name,json=teamName,proto3" json:"team_name"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *TeamOptsResp_Opts) Reset()         { *m = TeamOptsResp_Opts{} }
func (m *TeamOptsResp_Opts) String() string { return proto.CompactTextString(m) }
func (*TeamOptsResp_Opts) ProtoMessage()    {}
func (*TeamOptsResp_Opts) Descriptor() ([]byte, []int) {
	return fileDescriptor_8b4e9e93d7b2c6bb, []int{0, 0}
}

func (m *TeamOptsResp_Opts) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TeamOptsResp_Opts.Unmarshal(m, b)
}
func (m *TeamOptsResp_Opts) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TeamOptsResp_Opts.Marshal(b, m, deterministic)
}
func (m *TeamOptsResp_Opts) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TeamOptsResp_Opts.Merge(m, src)
}
func (m *TeamOptsResp_Opts) XXX_Size() int {
	return xxx_messageInfo_TeamOptsResp_Opts.Size(m)
}
func (m *TeamOptsResp_Opts) XXX_DiscardUnknown() {
	xxx_messageInfo_TeamOptsResp_Opts.DiscardUnknown(m)
}

var xxx_messageInfo_TeamOptsResp_Opts proto.InternalMessageInfo

func (m *TeamOptsResp_Opts) GetTeamId() uint32 {
	if m != nil {
		return m.TeamId
	}
	return 0
}

func (m *TeamOptsResp_Opts) GetTeamName() string {
	if m != nil {
		return m.TeamName
	}
	return ""
}

func init() {
	proto.RegisterType((*TeamOptsResp)(nil), "pb.TeamOptsResp")
	proto.RegisterType((*TeamOptsResp_Opts)(nil), "pb.TeamOptsResp.Opts")
}

func init() {
	proto.RegisterFile("team.proto", fileDescriptor_8b4e9e93d7b2c6bb)
}

var fileDescriptor_8b4e9e93d7b2c6bb = []byte{
	// 146 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xe2, 0xe2, 0x2a, 0x49, 0x4d, 0xcc,
	0xd5, 0x2b, 0x28, 0xca, 0x2f, 0xc9, 0x17, 0x62, 0x2a, 0x48, 0x52, 0x2a, 0xe7, 0xe2, 0x09, 0x49,
	0x4d, 0xcc, 0xf5, 0x2f, 0x28, 0x29, 0x0e, 0x4a, 0x2d, 0x2e, 0x10, 0xd2, 0xe4, 0x62, 0xc9, 0xc9,
	0x2c, 0x2e, 0x91, 0x60, 0x52, 0x60, 0xd6, 0xe0, 0x36, 0x12, 0xd5, 0x2b, 0x48, 0xd2, 0x43, 0x96,
	0xd7, 0x03, 0x33, 0xc0, 0x4a, 0xa4, 0x6c, 0xb8, 0x58, 0x40, 0x3c, 0x21, 0x71, 0x2e, 0x76, 0x90,
	0xa1, 0xf1, 0x99, 0x29, 0x12, 0x8c, 0x0a, 0x8c, 0x1a, 0xbc, 0x41, 0x6c, 0x20, 0xae, 0x67, 0x8a,
	0x90, 0x34, 0x17, 0x27, 0x58, 0x22, 0x2f, 0x31, 0x37, 0x55, 0x82, 0x49, 0x81, 0x51, 0x83, 0x33,
	0x88, 0x03, 0x24, 0xe0, 0x97, 0x98, 0x9b, 0xea, 0xc4, 0x16, 0xc5, 0xa2, 0x67, 0x5d, 0x90, 0x94,
	0xc4, 0x06, 0x76, 0x8b, 0x31, 0x20, 0x00, 0x00, 0xff, 0xff, 0xaf, 0x68, 0xeb, 0xaa, 0x99, 0x00,
	0x00, 0x00,
}
