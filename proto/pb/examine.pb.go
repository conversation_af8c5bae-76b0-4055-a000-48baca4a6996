// Code generated by protoc-gen-go. DO NOT EDIT.
// source: examine.proto

package pb

import (
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	anypb "google.golang.org/protobuf/types/known/anypb"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

// 打分表配置 - 列表 - req
type ExamineTplListReq struct {
	// 标题
	TplDesc string `protobuf:"bytes,1,opt,name=tpl_desc,json=tplDesc,proto3" json:"tpl_desc"`
	// 页码
	Page uint32 `protobuf:"varint,2,opt,name=page,proto3" json:"page"`
	// 页大小
	PageSize             uint32   `protobuf:"varint,3,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *ExamineTplListReq) Reset()         { *m = ExamineTplListReq{} }
func (m *ExamineTplListReq) String() string { return proto.CompactTextString(m) }
func (*ExamineTplListReq) ProtoMessage()    {}
func (*ExamineTplListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_9b15753b7a0c29a9, []int{0}
}

func (m *ExamineTplListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ExamineTplListReq.Unmarshal(m, b)
}
func (m *ExamineTplListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ExamineTplListReq.Marshal(b, m, deterministic)
}
func (m *ExamineTplListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExamineTplListReq.Merge(m, src)
}
func (m *ExamineTplListReq) XXX_Size() int {
	return xxx_messageInfo_ExamineTplListReq.Size(m)
}
func (m *ExamineTplListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ExamineTplListReq.DiscardUnknown(m)
}

var xxx_messageInfo_ExamineTplListReq proto.InternalMessageInfo

func (m *ExamineTplListReq) GetTplDesc() string {
	if m != nil {
		return m.TplDesc
	}
	return ""
}

func (m *ExamineTplListReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *ExamineTplListReq) GetPageSize() uint32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

// 打分表配置 - 列表 - resp
type ExamineTplListResp struct {
	CurrentPage          uint32                           `protobuf:"varint,1,opt,name=current_page,json=currentPage,proto3" json:"current_page"`
	PerPage              uint32                           `protobuf:"varint,2,opt,name=per_page,json=perPage,proto3" json:"per_page"`
	Total                int64                            `protobuf:"varint,3,opt,name=total,proto3" json:"total"`
	Data                 []*ExamineTplListResp_ExamineTpl `protobuf:"bytes,4,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}                         `json:"-" gorm:"-"`
	XXX_unrecognized     []byte                           `json:"-" gorm:"-"`
	XXX_sizecache        int32                            `json:"-" gorm:"-"`
}

func (m *ExamineTplListResp) Reset()         { *m = ExamineTplListResp{} }
func (m *ExamineTplListResp) String() string { return proto.CompactTextString(m) }
func (*ExamineTplListResp) ProtoMessage()    {}
func (*ExamineTplListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_9b15753b7a0c29a9, []int{1}
}

func (m *ExamineTplListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ExamineTplListResp.Unmarshal(m, b)
}
func (m *ExamineTplListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ExamineTplListResp.Marshal(b, m, deterministic)
}
func (m *ExamineTplListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExamineTplListResp.Merge(m, src)
}
func (m *ExamineTplListResp) XXX_Size() int {
	return xxx_messageInfo_ExamineTplListResp.Size(m)
}
func (m *ExamineTplListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ExamineTplListResp.DiscardUnknown(m)
}

var xxx_messageInfo_ExamineTplListResp proto.InternalMessageInfo

func (m *ExamineTplListResp) GetCurrentPage() uint32 {
	if m != nil {
		return m.CurrentPage
	}
	return 0
}

func (m *ExamineTplListResp) GetPerPage() uint32 {
	if m != nil {
		return m.PerPage
	}
	return 0
}

func (m *ExamineTplListResp) GetTotal() int64 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *ExamineTplListResp) GetData() []*ExamineTplListResp_ExamineTpl {
	if m != nil {
		return m.Data
	}
	return nil
}

type ExamineTplListResp_ExamineTpl struct {
	// id
	Id uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	// 打分表名字
	TplDesc string `protobuf:"bytes,2,opt,name=tpl_desc,json=tplDesc,proto3" json:"tpl_desc"`
	// 操作人
	Operator string `protobuf:"bytes,3,opt,name=operator,proto3" json:"operator"`
	// 操作时间
	UpdatedAt string `protobuf:"bytes,4,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at"`
	// 当前状态: true/false
	Enable               bool     `protobuf:"varint,5,opt,name=enable,proto3" json:"enable"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *ExamineTplListResp_ExamineTpl) Reset()         { *m = ExamineTplListResp_ExamineTpl{} }
func (m *ExamineTplListResp_ExamineTpl) String() string { return proto.CompactTextString(m) }
func (*ExamineTplListResp_ExamineTpl) ProtoMessage()    {}
func (*ExamineTplListResp_ExamineTpl) Descriptor() ([]byte, []int) {
	return fileDescriptor_9b15753b7a0c29a9, []int{1, 0}
}

func (m *ExamineTplListResp_ExamineTpl) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ExamineTplListResp_ExamineTpl.Unmarshal(m, b)
}
func (m *ExamineTplListResp_ExamineTpl) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ExamineTplListResp_ExamineTpl.Marshal(b, m, deterministic)
}
func (m *ExamineTplListResp_ExamineTpl) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExamineTplListResp_ExamineTpl.Merge(m, src)
}
func (m *ExamineTplListResp_ExamineTpl) XXX_Size() int {
	return xxx_messageInfo_ExamineTplListResp_ExamineTpl.Size(m)
}
func (m *ExamineTplListResp_ExamineTpl) XXX_DiscardUnknown() {
	xxx_messageInfo_ExamineTplListResp_ExamineTpl.DiscardUnknown(m)
}

var xxx_messageInfo_ExamineTplListResp_ExamineTpl proto.InternalMessageInfo

func (m *ExamineTplListResp_ExamineTpl) GetId() uint64 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *ExamineTplListResp_ExamineTpl) GetTplDesc() string {
	if m != nil {
		return m.TplDesc
	}
	return ""
}

func (m *ExamineTplListResp_ExamineTpl) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

func (m *ExamineTplListResp_ExamineTpl) GetUpdatedAt() string {
	if m != nil {
		return m.UpdatedAt
	}
	return ""
}

func (m *ExamineTplListResp_ExamineTpl) GetEnable() bool {
	if m != nil {
		return m.Enable
	}
	return false
}

// 打分表配置 - 详情 - req
type ExamineTplDetailReq struct {
	// 打分表 id
	TplId                uint64   `protobuf:"varint,1,opt,name=tpl_id,json=tplId,proto3" json:"tpl_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *ExamineTplDetailReq) Reset()         { *m = ExamineTplDetailReq{} }
func (m *ExamineTplDetailReq) String() string { return proto.CompactTextString(m) }
func (*ExamineTplDetailReq) ProtoMessage()    {}
func (*ExamineTplDetailReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_9b15753b7a0c29a9, []int{2}
}

func (m *ExamineTplDetailReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ExamineTplDetailReq.Unmarshal(m, b)
}
func (m *ExamineTplDetailReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ExamineTplDetailReq.Marshal(b, m, deterministic)
}
func (m *ExamineTplDetailReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExamineTplDetailReq.Merge(m, src)
}
func (m *ExamineTplDetailReq) XXX_Size() int {
	return xxx_messageInfo_ExamineTplDetailReq.Size(m)
}
func (m *ExamineTplDetailReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ExamineTplDetailReq.DiscardUnknown(m)
}

var xxx_messageInfo_ExamineTplDetailReq proto.InternalMessageInfo

func (m *ExamineTplDetailReq) GetTplId() uint64 {
	if m != nil {
		return m.TplId
	}
	return 0
}

// 打分表配置 - 详情 - 模块 字段定义
type ExamineFieldDt struct {
	// 字段名
	FieldName string `protobuf:"bytes,1,opt,name=field_name,json=fieldName,proto3" json:"field_name"`
	// 字段类型
	FieldType ExamineFieldTpDf `protobuf:"varint,3,opt,name=field_type,json=fieldType,proto3,enum=pb.ExamineFieldTpDf" json:"field_type"`
	// 字段值选项
	FieldOpts            []string `protobuf:"bytes,4,rep,name=field_opts,json=fieldOpts,proto3" json:"field_opts"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *ExamineFieldDt) Reset()         { *m = ExamineFieldDt{} }
func (m *ExamineFieldDt) String() string { return proto.CompactTextString(m) }
func (*ExamineFieldDt) ProtoMessage()    {}
func (*ExamineFieldDt) Descriptor() ([]byte, []int) {
	return fileDescriptor_9b15753b7a0c29a9, []int{3}
}

func (m *ExamineFieldDt) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ExamineFieldDt.Unmarshal(m, b)
}
func (m *ExamineFieldDt) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ExamineFieldDt.Marshal(b, m, deterministic)
}
func (m *ExamineFieldDt) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExamineFieldDt.Merge(m, src)
}
func (m *ExamineFieldDt) XXX_Size() int {
	return xxx_messageInfo_ExamineFieldDt.Size(m)
}
func (m *ExamineFieldDt) XXX_DiscardUnknown() {
	xxx_messageInfo_ExamineFieldDt.DiscardUnknown(m)
}

var xxx_messageInfo_ExamineFieldDt proto.InternalMessageInfo

func (m *ExamineFieldDt) GetFieldName() string {
	if m != nil {
		return m.FieldName
	}
	return ""
}

func (m *ExamineFieldDt) GetFieldType() ExamineFieldTpDf {
	if m != nil {
		return m.FieldType
	}
	return ExamineFieldTpDf_ExamineFieldTpDfUnknown
}

func (m *ExamineFieldDt) GetFieldOpts() []string {
	if m != nil {
		return m.FieldOpts
	}
	return nil
}

// 打分表配置 - 详情 - 模块定义
type ExamineModuleDt struct {
	// 模块名称 gotags: validate:"required"
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name"`
	// 考核项 gotags: validate:"required"
	FieldDetail          []*ExamineFieldDt `protobuf:"bytes,2,rep,name=field_detail,json=fieldDetail,proto3" json:"field_detail"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-" gorm:"-"`
	XXX_unrecognized     []byte            `json:"-" gorm:"-"`
	XXX_sizecache        int32             `json:"-" gorm:"-"`
}

func (m *ExamineModuleDt) Reset()         { *m = ExamineModuleDt{} }
func (m *ExamineModuleDt) String() string { return proto.CompactTextString(m) }
func (*ExamineModuleDt) ProtoMessage()    {}
func (*ExamineModuleDt) Descriptor() ([]byte, []int) {
	return fileDescriptor_9b15753b7a0c29a9, []int{4}
}

func (m *ExamineModuleDt) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ExamineModuleDt.Unmarshal(m, b)
}
func (m *ExamineModuleDt) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ExamineModuleDt.Marshal(b, m, deterministic)
}
func (m *ExamineModuleDt) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExamineModuleDt.Merge(m, src)
}
func (m *ExamineModuleDt) XXX_Size() int {
	return xxx_messageInfo_ExamineModuleDt.Size(m)
}
func (m *ExamineModuleDt) XXX_DiscardUnknown() {
	xxx_messageInfo_ExamineModuleDt.DiscardUnknown(m)
}

var xxx_messageInfo_ExamineModuleDt proto.InternalMessageInfo

func (m *ExamineModuleDt) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *ExamineModuleDt) GetFieldDetail() []*ExamineFieldDt {
	if m != nil {
		return m.FieldDetail
	}
	return nil
}

// 打分表配置 - 详情 - resp
type ExamineTplDetailResp struct {
	// 打分表名字：
	TplDesc string `protobuf:"bytes,1,opt,name=tpl_desc,json=tplDesc,proto3" json:"tpl_desc"`
	// 模块定义
	ModuleDetail         []*ExamineModuleDt `protobuf:"bytes,2,rep,name=module_detail,json=moduleDetail,proto3" json:"module_detail"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-" gorm:"-"`
	XXX_unrecognized     []byte             `json:"-" gorm:"-"`
	XXX_sizecache        int32              `json:"-" gorm:"-"`
}

func (m *ExamineTplDetailResp) Reset()         { *m = ExamineTplDetailResp{} }
func (m *ExamineTplDetailResp) String() string { return proto.CompactTextString(m) }
func (*ExamineTplDetailResp) ProtoMessage()    {}
func (*ExamineTplDetailResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_9b15753b7a0c29a9, []int{5}
}

func (m *ExamineTplDetailResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ExamineTplDetailResp.Unmarshal(m, b)
}
func (m *ExamineTplDetailResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ExamineTplDetailResp.Marshal(b, m, deterministic)
}
func (m *ExamineTplDetailResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExamineTplDetailResp.Merge(m, src)
}
func (m *ExamineTplDetailResp) XXX_Size() int {
	return xxx_messageInfo_ExamineTplDetailResp.Size(m)
}
func (m *ExamineTplDetailResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ExamineTplDetailResp.DiscardUnknown(m)
}

var xxx_messageInfo_ExamineTplDetailResp proto.InternalMessageInfo

func (m *ExamineTplDetailResp) GetTplDesc() string {
	if m != nil {
		return m.TplDesc
	}
	return ""
}

func (m *ExamineTplDetailResp) GetModuleDetail() []*ExamineModuleDt {
	if m != nil {
		return m.ModuleDetail
	}
	return nil
}

type ExamineTplOptsResp struct {
	// 打分表选项
	List                 []*ExamineTplOptsResp_ExamineTplOpts `protobuf:"bytes,1,rep,name=list,proto3" json:"list"`
	XXX_NoUnkeyedLiteral struct{}                             `json:"-" gorm:"-"`
	XXX_unrecognized     []byte                               `json:"-" gorm:"-"`
	XXX_sizecache        int32                                `json:"-" gorm:"-"`
}

func (m *ExamineTplOptsResp) Reset()         { *m = ExamineTplOptsResp{} }
func (m *ExamineTplOptsResp) String() string { return proto.CompactTextString(m) }
func (*ExamineTplOptsResp) ProtoMessage()    {}
func (*ExamineTplOptsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_9b15753b7a0c29a9, []int{6}
}

func (m *ExamineTplOptsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ExamineTplOptsResp.Unmarshal(m, b)
}
func (m *ExamineTplOptsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ExamineTplOptsResp.Marshal(b, m, deterministic)
}
func (m *ExamineTplOptsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExamineTplOptsResp.Merge(m, src)
}
func (m *ExamineTplOptsResp) XXX_Size() int {
	return xxx_messageInfo_ExamineTplOptsResp.Size(m)
}
func (m *ExamineTplOptsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ExamineTplOptsResp.DiscardUnknown(m)
}

var xxx_messageInfo_ExamineTplOptsResp proto.InternalMessageInfo

func (m *ExamineTplOptsResp) GetList() []*ExamineTplOptsResp_ExamineTplOpts {
	if m != nil {
		return m.List
	}
	return nil
}

type ExamineTplOptsResp_ExamineTplOpts struct {
	// 打分表 id
	TplId uint64 `protobuf:"varint,1,opt,name=tpl_id,json=tplId,proto3" json:"tpl_id"`
	// 打分表名字
	TplDsc               string   `protobuf:"bytes,2,opt,name=tpl_dsc,json=tplDsc,proto3" json:"tpl_dsc"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *ExamineTplOptsResp_ExamineTplOpts) Reset()         { *m = ExamineTplOptsResp_ExamineTplOpts{} }
func (m *ExamineTplOptsResp_ExamineTplOpts) String() string { return proto.CompactTextString(m) }
func (*ExamineTplOptsResp_ExamineTplOpts) ProtoMessage()    {}
func (*ExamineTplOptsResp_ExamineTplOpts) Descriptor() ([]byte, []int) {
	return fileDescriptor_9b15753b7a0c29a9, []int{6, 0}
}

func (m *ExamineTplOptsResp_ExamineTplOpts) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ExamineTplOptsResp_ExamineTplOpts.Unmarshal(m, b)
}
func (m *ExamineTplOptsResp_ExamineTplOpts) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ExamineTplOptsResp_ExamineTplOpts.Marshal(b, m, deterministic)
}
func (m *ExamineTplOptsResp_ExamineTplOpts) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExamineTplOptsResp_ExamineTplOpts.Merge(m, src)
}
func (m *ExamineTplOptsResp_ExamineTplOpts) XXX_Size() int {
	return xxx_messageInfo_ExamineTplOptsResp_ExamineTplOpts.Size(m)
}
func (m *ExamineTplOptsResp_ExamineTplOpts) XXX_DiscardUnknown() {
	xxx_messageInfo_ExamineTplOptsResp_ExamineTplOpts.DiscardUnknown(m)
}

var xxx_messageInfo_ExamineTplOptsResp_ExamineTplOpts proto.InternalMessageInfo

func (m *ExamineTplOptsResp_ExamineTplOpts) GetTplId() uint64 {
	if m != nil {
		return m.TplId
	}
	return 0
}

func (m *ExamineTplOptsResp_ExamineTplOpts) GetTplDsc() string {
	if m != nil {
		return m.TplDsc
	}
	return ""
}

// 打分表配置 - 保存 - req
type ExamineTplSaveReq struct {
	// id
	Id uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	// 打分表名字 @gotags: validate:"required"
	TplDesc string `protobuf:"bytes,2,opt,name=tpl_desc,json=tplDesc,proto3" json:"tpl_desc" validate:"required"`
	// 模块定义 @gotags: validate:"required"
	ModuleDetail         []*ExamineModuleDt `protobuf:"bytes,3,rep,name=module_detail,json=moduleDetail,proto3" json:"module_detail" validate:"required"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-" gorm:"-"`
	XXX_unrecognized     []byte             `json:"-" gorm:"-"`
	XXX_sizecache        int32              `json:"-" gorm:"-"`
}

func (m *ExamineTplSaveReq) Reset()         { *m = ExamineTplSaveReq{} }
func (m *ExamineTplSaveReq) String() string { return proto.CompactTextString(m) }
func (*ExamineTplSaveReq) ProtoMessage()    {}
func (*ExamineTplSaveReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_9b15753b7a0c29a9, []int{7}
}

func (m *ExamineTplSaveReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ExamineTplSaveReq.Unmarshal(m, b)
}
func (m *ExamineTplSaveReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ExamineTplSaveReq.Marshal(b, m, deterministic)
}
func (m *ExamineTplSaveReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExamineTplSaveReq.Merge(m, src)
}
func (m *ExamineTplSaveReq) XXX_Size() int {
	return xxx_messageInfo_ExamineTplSaveReq.Size(m)
}
func (m *ExamineTplSaveReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ExamineTplSaveReq.DiscardUnknown(m)
}

var xxx_messageInfo_ExamineTplSaveReq proto.InternalMessageInfo

func (m *ExamineTplSaveReq) GetId() uint64 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *ExamineTplSaveReq) GetTplDesc() string {
	if m != nil {
		return m.TplDesc
	}
	return ""
}

func (m *ExamineTplSaveReq) GetModuleDetail() []*ExamineModuleDt {
	if m != nil {
		return m.ModuleDetail
	}
	return nil
}

// 打分表配置 - 复制 - req
type ExamineTplCopyReq struct {
	// 打分表 id @gotags: validate:"required"
	FromTplId uint64 `protobuf:"varint,1,opt,name=from_tpl_id,json=fromTplId,proto3" json:"from_tpl_id" validate:"required"`
	// 复制打分表字段信息： @gotags: validate:"required"
	TplDesc              string   `protobuf:"bytes,2,opt,name=tpl_desc,json=tplDesc,proto3" json:"tpl_desc" validate:"required"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *ExamineTplCopyReq) Reset()         { *m = ExamineTplCopyReq{} }
func (m *ExamineTplCopyReq) String() string { return proto.CompactTextString(m) }
func (*ExamineTplCopyReq) ProtoMessage()    {}
func (*ExamineTplCopyReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_9b15753b7a0c29a9, []int{8}
}

func (m *ExamineTplCopyReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ExamineTplCopyReq.Unmarshal(m, b)
}
func (m *ExamineTplCopyReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ExamineTplCopyReq.Marshal(b, m, deterministic)
}
func (m *ExamineTplCopyReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExamineTplCopyReq.Merge(m, src)
}
func (m *ExamineTplCopyReq) XXX_Size() int {
	return xxx_messageInfo_ExamineTplCopyReq.Size(m)
}
func (m *ExamineTplCopyReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ExamineTplCopyReq.DiscardUnknown(m)
}

var xxx_messageInfo_ExamineTplCopyReq proto.InternalMessageInfo

func (m *ExamineTplCopyReq) GetFromTplId() uint64 {
	if m != nil {
		return m.FromTplId
	}
	return 0
}

func (m *ExamineTplCopyReq) GetTplDesc() string {
	if m != nil {
		return m.TplDesc
	}
	return ""
}

// 打分表配置 - 保存 - resp
type ExamineTplSaveResp struct {
	TplId                uint64   `protobuf:"varint,1,opt,name=tpl_id,json=tplId,proto3" json:"tpl_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *ExamineTplSaveResp) Reset()         { *m = ExamineTplSaveResp{} }
func (m *ExamineTplSaveResp) String() string { return proto.CompactTextString(m) }
func (*ExamineTplSaveResp) ProtoMessage()    {}
func (*ExamineTplSaveResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_9b15753b7a0c29a9, []int{9}
}

func (m *ExamineTplSaveResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ExamineTplSaveResp.Unmarshal(m, b)
}
func (m *ExamineTplSaveResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ExamineTplSaveResp.Marshal(b, m, deterministic)
}
func (m *ExamineTplSaveResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExamineTplSaveResp.Merge(m, src)
}
func (m *ExamineTplSaveResp) XXX_Size() int {
	return xxx_messageInfo_ExamineTplSaveResp.Size(m)
}
func (m *ExamineTplSaveResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ExamineTplSaveResp.DiscardUnknown(m)
}

var xxx_messageInfo_ExamineTplSaveResp proto.InternalMessageInfo

func (m *ExamineTplSaveResp) GetTplId() uint64 {
	if m != nil {
		return m.TplId
	}
	return 0
}

// 打分表配置 - del - req
type ExamineTplDelReq struct {
	// 打分表 id
	TplId                uint64   `protobuf:"varint,1,opt,name=tpl_id,json=tplId,proto3" json:"tpl_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *ExamineTplDelReq) Reset()         { *m = ExamineTplDelReq{} }
func (m *ExamineTplDelReq) String() string { return proto.CompactTextString(m) }
func (*ExamineTplDelReq) ProtoMessage()    {}
func (*ExamineTplDelReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_9b15753b7a0c29a9, []int{10}
}

func (m *ExamineTplDelReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ExamineTplDelReq.Unmarshal(m, b)
}
func (m *ExamineTplDelReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ExamineTplDelReq.Marshal(b, m, deterministic)
}
func (m *ExamineTplDelReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExamineTplDelReq.Merge(m, src)
}
func (m *ExamineTplDelReq) XXX_Size() int {
	return xxx_messageInfo_ExamineTplDelReq.Size(m)
}
func (m *ExamineTplDelReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ExamineTplDelReq.DiscardUnknown(m)
}

var xxx_messageInfo_ExamineTplDelReq proto.InternalMessageInfo

func (m *ExamineTplDelReq) GetTplId() uint64 {
	if m != nil {
		return m.TplId
	}
	return 0
}

// 任务配置 - 列表 - req
type ExamineTaskListReq struct {
	// 质检任务名称
	TaskName string `protobuf:"bytes,1,opt,name=task_name,json=taskName,proto3" json:"task_name"`
	// 页码
	Page uint32 `protobuf:"varint,2,opt,name=page,proto3" json:"page"`
	// 页大小
	PageSize             uint32   `protobuf:"varint,3,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *ExamineTaskListReq) Reset()         { *m = ExamineTaskListReq{} }
func (m *ExamineTaskListReq) String() string { return proto.CompactTextString(m) }
func (*ExamineTaskListReq) ProtoMessage()    {}
func (*ExamineTaskListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_9b15753b7a0c29a9, []int{11}
}

func (m *ExamineTaskListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ExamineTaskListReq.Unmarshal(m, b)
}
func (m *ExamineTaskListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ExamineTaskListReq.Marshal(b, m, deterministic)
}
func (m *ExamineTaskListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExamineTaskListReq.Merge(m, src)
}
func (m *ExamineTaskListReq) XXX_Size() int {
	return xxx_messageInfo_ExamineTaskListReq.Size(m)
}
func (m *ExamineTaskListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ExamineTaskListReq.DiscardUnknown(m)
}

var xxx_messageInfo_ExamineTaskListReq proto.InternalMessageInfo

func (m *ExamineTaskListReq) GetTaskName() string {
	if m != nil {
		return m.TaskName
	}
	return ""
}

func (m *ExamineTaskListReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *ExamineTaskListReq) GetPageSize() uint32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

// 任务配置 - 列表 - resp
type ExamineTaskListResp struct {
	CurrentPage          uint32                             `protobuf:"varint,1,opt,name=current_page,json=currentPage,proto3" json:"current_page"`
	PerPage              uint32                             `protobuf:"varint,2,opt,name=per_page,json=perPage,proto3" json:"per_page"`
	Total                int64                              `protobuf:"varint,3,opt,name=total,proto3" json:"total"`
	Data                 []*ExamineTaskListResp_ExamineTask `protobuf:"bytes,4,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}                           `json:"-" gorm:"-"`
	XXX_unrecognized     []byte                             `json:"-" gorm:"-"`
	XXX_sizecache        int32                              `json:"-" gorm:"-"`
}

func (m *ExamineTaskListResp) Reset()         { *m = ExamineTaskListResp{} }
func (m *ExamineTaskListResp) String() string { return proto.CompactTextString(m) }
func (*ExamineTaskListResp) ProtoMessage()    {}
func (*ExamineTaskListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_9b15753b7a0c29a9, []int{12}
}

func (m *ExamineTaskListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ExamineTaskListResp.Unmarshal(m, b)
}
func (m *ExamineTaskListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ExamineTaskListResp.Marshal(b, m, deterministic)
}
func (m *ExamineTaskListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExamineTaskListResp.Merge(m, src)
}
func (m *ExamineTaskListResp) XXX_Size() int {
	return xxx_messageInfo_ExamineTaskListResp.Size(m)
}
func (m *ExamineTaskListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ExamineTaskListResp.DiscardUnknown(m)
}

var xxx_messageInfo_ExamineTaskListResp proto.InternalMessageInfo

func (m *ExamineTaskListResp) GetCurrentPage() uint32 {
	if m != nil {
		return m.CurrentPage
	}
	return 0
}

func (m *ExamineTaskListResp) GetPerPage() uint32 {
	if m != nil {
		return m.PerPage
	}
	return 0
}

func (m *ExamineTaskListResp) GetTotal() int64 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *ExamineTaskListResp) GetData() []*ExamineTaskListResp_ExamineTask {
	if m != nil {
		return m.Data
	}
	return nil
}

type ExamineTaskListResp_ExamineTask struct {
	// id
	Id uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	// 任务名称
	TaskName string `protobuf:"bytes,2,opt,name=task_name,json=taskName,proto3" json:"task_name"`
	// 任务状态
	Status ExamineTaskStateDf `protobuf:"varint,3,opt,name=status,proto3,enum=pb.ExamineTaskStateDf" json:"status"`
	// 操作人
	Operator string `protobuf:"bytes,4,opt,name=operator,proto3" json:"operator"`
	// 操作时间
	UpdatedAt string `protobuf:"bytes,5,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at"`
	// 快照详情
	Link                 string   `protobuf:"bytes,6,opt,name=link,proto3" json:"link"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *ExamineTaskListResp_ExamineTask) Reset()         { *m = ExamineTaskListResp_ExamineTask{} }
func (m *ExamineTaskListResp_ExamineTask) String() string { return proto.CompactTextString(m) }
func (*ExamineTaskListResp_ExamineTask) ProtoMessage()    {}
func (*ExamineTaskListResp_ExamineTask) Descriptor() ([]byte, []int) {
	return fileDescriptor_9b15753b7a0c29a9, []int{12, 0}
}

func (m *ExamineTaskListResp_ExamineTask) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ExamineTaskListResp_ExamineTask.Unmarshal(m, b)
}
func (m *ExamineTaskListResp_ExamineTask) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ExamineTaskListResp_ExamineTask.Marshal(b, m, deterministic)
}
func (m *ExamineTaskListResp_ExamineTask) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExamineTaskListResp_ExamineTask.Merge(m, src)
}
func (m *ExamineTaskListResp_ExamineTask) XXX_Size() int {
	return xxx_messageInfo_ExamineTaskListResp_ExamineTask.Size(m)
}
func (m *ExamineTaskListResp_ExamineTask) XXX_DiscardUnknown() {
	xxx_messageInfo_ExamineTaskListResp_ExamineTask.DiscardUnknown(m)
}

var xxx_messageInfo_ExamineTaskListResp_ExamineTask proto.InternalMessageInfo

func (m *ExamineTaskListResp_ExamineTask) GetId() uint64 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *ExamineTaskListResp_ExamineTask) GetTaskName() string {
	if m != nil {
		return m.TaskName
	}
	return ""
}

func (m *ExamineTaskListResp_ExamineTask) GetStatus() ExamineTaskStateDf {
	if m != nil {
		return m.Status
	}
	return ExamineTaskStateDf_ExamineTaskStateDfUnknown
}

func (m *ExamineTaskListResp_ExamineTask) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

func (m *ExamineTaskListResp_ExamineTask) GetUpdatedAt() string {
	if m != nil {
		return m.UpdatedAt
	}
	return ""
}

func (m *ExamineTaskListResp_ExamineTask) GetLink() string {
	if m != nil {
		return m.Link
	}
	return ""
}

// 任务配置 - 详情 - req
type ExamineTaskDetailReq struct {
	Id                   uint64   `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *ExamineTaskDetailReq) Reset()         { *m = ExamineTaskDetailReq{} }
func (m *ExamineTaskDetailReq) String() string { return proto.CompactTextString(m) }
func (*ExamineTaskDetailReq) ProtoMessage()    {}
func (*ExamineTaskDetailReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_9b15753b7a0c29a9, []int{13}
}

func (m *ExamineTaskDetailReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ExamineTaskDetailReq.Unmarshal(m, b)
}
func (m *ExamineTaskDetailReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ExamineTaskDetailReq.Marshal(b, m, deterministic)
}
func (m *ExamineTaskDetailReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExamineTaskDetailReq.Merge(m, src)
}
func (m *ExamineTaskDetailReq) XXX_Size() int {
	return xxx_messageInfo_ExamineTaskDetailReq.Size(m)
}
func (m *ExamineTaskDetailReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ExamineTaskDetailReq.DiscardUnknown(m)
}

var xxx_messageInfo_ExamineTaskDetailReq proto.InternalMessageInfo

func (m *ExamineTaskDetailReq) GetId() uint64 {
	if m != nil {
		return m.Id
	}
	return 0
}

// 任务配置 - 详情 - resp
type ExamineTaskDetailResp struct {
	// 任务id
	Id uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	// 任务名称
	TaskName string `protobuf:"bytes,2,opt,name=task_name,json=taskName,proto3" json:"task_name"`
	// 游戏
	Project string `protobuf:"bytes,3,opt,name=project,proto3" json:"project"`
	// 关联打分表
	TplId uint64 `protobuf:"varint,4,opt,name=tpl_id,json=tplId,proto3" json:"tpl_id"`
	// 抽检库分组
	TaskGroup ExamineTaskGroupDf `protobuf:"varint,5,opt,name=task_group,json=taskGroup,proto3,enum=pb.ExamineTaskGroupDf" json:"task_group"`
	// discord 抽查规则 - 回复时间
	FilterDscRepliedAt []string `protobuf:"bytes,20,rep,name=filter_dsc_replied_at,json=filterDscRepliedAt,proto3" json:"filter_dsc_replied_at"`
	// discord 抽查规则 - 全检标准：充值金额
	FilterDscAllTotalPayGte int64 `protobuf:"varint,21,opt,name=filter_dsc_all_total_pay_gte,json=filterDscAllTotalPayGte,proto3" json:"filter_dsc_all_total_pay_gte"`
	// discord 抽查规则 - 剩下抽检范围 - 是否全部： true 全部；false 部分
	FilterDscOtherAll bool `protobuf:"varint,22,opt,name=filter_dsc_other_all,json=filterDscOtherAll,proto3" json:"filter_dsc_other_all"`
	//    // discord 抽查规则 - 剩下抽检范围-部分-圈选规则-标签
	//    repeated string filter_dsc_other_label = 23;
	// discord 抽查规则 - 剩下抽检范围-部分-圈选规则-vip 状态
	FilterDscOtherVipState []uint32 `protobuf:"varint,24,rep,packed,name=filter_dsc_other_vip_state,json=filterDscOtherVipState,proto3" json:"filter_dsc_other_vip_state"`
	// discord 抽查规则 - 剩下抽检范围-部分-圈选规则-维护人
	FilterDscOtherMaintainer []string `protobuf:"bytes,25,rep,name=filter_dsc_other_maintainer,json=filterDscOtherMaintainer,proto3" json:"filter_dsc_other_maintainer"`
	// discord 抽查规则 - 剩下抽检范围-部分-圈选规则-抽检数量
	FilterDscOtherNum int32 `protobuf:"varint,26,opt,name=filter_dsc_other_num,json=filterDscOtherNum,proto3" json:"filter_dsc_other_num"`
	// discord 抽查规则 - 剩下抽检范围-部分-圈选规则-分配人员
	FilterDscAssignAccount []string `protobuf:"bytes,27,rep,name=filter_dsc_assign_account,json=filterDscAssignAccount,proto3" json:"filter_dsc_assign_account"`
	XXX_NoUnkeyedLiteral   struct{} `json:"-" gorm:"-"`
	XXX_unrecognized       []byte   `json:"-" gorm:"-"`
	XXX_sizecache          int32    `json:"-" gorm:"-"`
}

func (m *ExamineTaskDetailResp) Reset()         { *m = ExamineTaskDetailResp{} }
func (m *ExamineTaskDetailResp) String() string { return proto.CompactTextString(m) }
func (*ExamineTaskDetailResp) ProtoMessage()    {}
func (*ExamineTaskDetailResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_9b15753b7a0c29a9, []int{14}
}

func (m *ExamineTaskDetailResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ExamineTaskDetailResp.Unmarshal(m, b)
}
func (m *ExamineTaskDetailResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ExamineTaskDetailResp.Marshal(b, m, deterministic)
}
func (m *ExamineTaskDetailResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExamineTaskDetailResp.Merge(m, src)
}
func (m *ExamineTaskDetailResp) XXX_Size() int {
	return xxx_messageInfo_ExamineTaskDetailResp.Size(m)
}
func (m *ExamineTaskDetailResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ExamineTaskDetailResp.DiscardUnknown(m)
}

var xxx_messageInfo_ExamineTaskDetailResp proto.InternalMessageInfo

func (m *ExamineTaskDetailResp) GetId() uint64 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *ExamineTaskDetailResp) GetTaskName() string {
	if m != nil {
		return m.TaskName
	}
	return ""
}

func (m *ExamineTaskDetailResp) GetProject() string {
	if m != nil {
		return m.Project
	}
	return ""
}

func (m *ExamineTaskDetailResp) GetTplId() uint64 {
	if m != nil {
		return m.TplId
	}
	return 0
}

func (m *ExamineTaskDetailResp) GetTaskGroup() ExamineTaskGroupDf {
	if m != nil {
		return m.TaskGroup
	}
	return ExamineTaskGroupDf_ExamineTaskGroupDfUnknown
}

func (m *ExamineTaskDetailResp) GetFilterDscRepliedAt() []string {
	if m != nil {
		return m.FilterDscRepliedAt
	}
	return nil
}

func (m *ExamineTaskDetailResp) GetFilterDscAllTotalPayGte() int64 {
	if m != nil {
		return m.FilterDscAllTotalPayGte
	}
	return 0
}

func (m *ExamineTaskDetailResp) GetFilterDscOtherAll() bool {
	if m != nil {
		return m.FilterDscOtherAll
	}
	return false
}

func (m *ExamineTaskDetailResp) GetFilterDscOtherVipState() []uint32 {
	if m != nil {
		return m.FilterDscOtherVipState
	}
	return nil
}

func (m *ExamineTaskDetailResp) GetFilterDscOtherMaintainer() []string {
	if m != nil {
		return m.FilterDscOtherMaintainer
	}
	return nil
}

func (m *ExamineTaskDetailResp) GetFilterDscOtherNum() int32 {
	if m != nil {
		return m.FilterDscOtherNum
	}
	return 0
}

func (m *ExamineTaskDetailResp) GetFilterDscAssignAccount() []string {
	if m != nil {
		return m.FilterDscAssignAccount
	}
	return nil
}

// 任务配置 - 保存 - req
type ExamineTaskSaveReq struct {
	// 任务名称 @gotags: validate:"required"
	TaskName string `protobuf:"bytes,1,opt,name=task_name,json=taskName,proto3" json:"task_name" validate:"required"`
	// 游戏 @gotags: validate:"required"
	Project string `protobuf:"bytes,2,opt,name=project,proto3" json:"project" validate:"required"`
	// 关联打分表 @gotags: validate:"required"
	TplId uint64 `protobuf:"varint,3,opt,name=tpl_id,json=tplId,proto3" json:"tpl_id" validate:"required"`
	// 抽检库分组 @gotags: validate:"required,gte=1"
	TaskGroup ExamineTaskGroupDf `protobuf:"varint,4,opt,name=task_group,json=taskGroup,proto3,enum=pb.ExamineTaskGroupDf" json:"task_group" validate:"required,gte=1"`
	// discord 抽查规则 - 抽检方式 @gotags: validate:"required"
	TaskRule ExamineTaskRulesDf `protobuf:"varint,5,opt,name=task_rule,json=taskRule,proto3,enum=pb.ExamineTaskRulesDf" json:"task_rule" validate:"required"`
	// discord抽查规则-回复时间 @gotags: validate:"required_if=TaskGroup 2"
	FilterDscRepliedAt []string `protobuf:"bytes,20,rep,name=filter_dsc_replied_at,json=filterDscRepliedAt,proto3" json:"filter_dsc_replied_at" validate:"required_if=TaskGroup 2"`
	// discord抽查规则-全检标准:充值金额 @gotags: validate:"required_if=TaskGroup 2"
	FilterDscAllTotalPayGte int64 `protobuf:"varint,21,opt,name=filter_dsc_all_total_pay_gte,json=filterDscAllTotalPayGte,proto3" json:"filter_dsc_all_total_pay_gte" validate:"required_if=TaskGroup 2"`
	// discord抽查规则-剩下抽检范围-是否全部：true:全部；false:部分
	FilterDscOtherAll bool `protobuf:"varint,22,opt,name=filter_dsc_other_all,json=filterDscOtherAll,proto3" json:"filter_dsc_other_all"`
	//    // discord抽查规则-剩下抽检范围-部分-圈选规则-标签
	//    repeated string filter_dsc_other_tag = 23;
	// discord抽查规则-剩下抽检范围-部分-圈选规则-维护人
	FilterDscOtherMaintainer []string `protobuf:"bytes,24,rep,name=filter_dsc_other_maintainer,json=filterDscOtherMaintainer,proto3" json:"filter_dsc_other_maintainer"`
	// discord抽查规则-剩下抽检范围-部分-圈选规则-vip状态
	FilterDscOtherVipState []uint32 `protobuf:"varint,25,rep,packed,name=filter_dsc_other_vip_state,json=filterDscOtherVipState,proto3" json:"filter_dsc_other_vip_state"`
	// discord抽查规则-剩下抽检范围-部分-圈选规则-抽检数量"
	FilterDscOtherNum uint32 `protobuf:"varint,26,opt,name=filter_dsc_other_num,json=filterDscOtherNum,proto3" json:"filter_dsc_other_num"`
	// discord抽查规则-剩下抽检范围-部分-圈选规则-分配人员" @gotags: validate:"required_if=TaskGroup 2"
	FilterDscAssignAccount []string `protobuf:"bytes,27,rep,name=filter_dsc_assign_account,json=filterDscAssignAccount,proto3" json:"filter_dsc_assign_account" validate:"required_if=TaskGroup 2"`
	// 详情快照图地址
	Link                 string   `protobuf:"bytes,28,opt,name=link,proto3" json:"link"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *ExamineTaskSaveReq) Reset()         { *m = ExamineTaskSaveReq{} }
func (m *ExamineTaskSaveReq) String() string { return proto.CompactTextString(m) }
func (*ExamineTaskSaveReq) ProtoMessage()    {}
func (*ExamineTaskSaveReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_9b15753b7a0c29a9, []int{15}
}

func (m *ExamineTaskSaveReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ExamineTaskSaveReq.Unmarshal(m, b)
}
func (m *ExamineTaskSaveReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ExamineTaskSaveReq.Marshal(b, m, deterministic)
}
func (m *ExamineTaskSaveReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExamineTaskSaveReq.Merge(m, src)
}
func (m *ExamineTaskSaveReq) XXX_Size() int {
	return xxx_messageInfo_ExamineTaskSaveReq.Size(m)
}
func (m *ExamineTaskSaveReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ExamineTaskSaveReq.DiscardUnknown(m)
}

var xxx_messageInfo_ExamineTaskSaveReq proto.InternalMessageInfo

func (m *ExamineTaskSaveReq) GetTaskName() string {
	if m != nil {
		return m.TaskName
	}
	return ""
}

func (m *ExamineTaskSaveReq) GetProject() string {
	if m != nil {
		return m.Project
	}
	return ""
}

func (m *ExamineTaskSaveReq) GetTplId() uint64 {
	if m != nil {
		return m.TplId
	}
	return 0
}

func (m *ExamineTaskSaveReq) GetTaskGroup() ExamineTaskGroupDf {
	if m != nil {
		return m.TaskGroup
	}
	return ExamineTaskGroupDf_ExamineTaskGroupDfUnknown
}

func (m *ExamineTaskSaveReq) GetTaskRule() ExamineTaskRulesDf {
	if m != nil {
		return m.TaskRule
	}
	return ExamineTaskRulesDf_ExamineTaskRulesDfUnknown
}

func (m *ExamineTaskSaveReq) GetFilterDscRepliedAt() []string {
	if m != nil {
		return m.FilterDscRepliedAt
	}
	return nil
}

func (m *ExamineTaskSaveReq) GetFilterDscAllTotalPayGte() int64 {
	if m != nil {
		return m.FilterDscAllTotalPayGte
	}
	return 0
}

func (m *ExamineTaskSaveReq) GetFilterDscOtherAll() bool {
	if m != nil {
		return m.FilterDscOtherAll
	}
	return false
}

func (m *ExamineTaskSaveReq) GetFilterDscOtherMaintainer() []string {
	if m != nil {
		return m.FilterDscOtherMaintainer
	}
	return nil
}

func (m *ExamineTaskSaveReq) GetFilterDscOtherVipState() []uint32 {
	if m != nil {
		return m.FilterDscOtherVipState
	}
	return nil
}

func (m *ExamineTaskSaveReq) GetFilterDscOtherNum() uint32 {
	if m != nil {
		return m.FilterDscOtherNum
	}
	return 0
}

func (m *ExamineTaskSaveReq) GetFilterDscAssignAccount() []string {
	if m != nil {
		return m.FilterDscAssignAccount
	}
	return nil
}

func (m *ExamineTaskSaveReq) GetLink() string {
	if m != nil {
		return m.Link
	}
	return ""
}

type ExamineTaskDscFilterCountReq struct {
	// 游戏 @gotags: validate:"required"
	Project string `protobuf:"bytes,1,opt,name=project,proto3" json:"project" validate:"required"`
	// 回复时间 @gotags: validate:"required"
	RepliedAt []string `protobuf:"bytes,2,rep,name=replied_at,json=repliedAt,proto3" json:"replied_at" validate:"required"`
	// 充值金额 @gotags: validate:"required,gte=1"
	TotalPay             int64    `protobuf:"varint,3,opt,name=total_pay,json=totalPay,proto3" json:"total_pay" validate:"required,gte=1"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *ExamineTaskDscFilterCountReq) Reset()         { *m = ExamineTaskDscFilterCountReq{} }
func (m *ExamineTaskDscFilterCountReq) String() string { return proto.CompactTextString(m) }
func (*ExamineTaskDscFilterCountReq) ProtoMessage()    {}
func (*ExamineTaskDscFilterCountReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_9b15753b7a0c29a9, []int{16}
}

func (m *ExamineTaskDscFilterCountReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ExamineTaskDscFilterCountReq.Unmarshal(m, b)
}
func (m *ExamineTaskDscFilterCountReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ExamineTaskDscFilterCountReq.Marshal(b, m, deterministic)
}
func (m *ExamineTaskDscFilterCountReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExamineTaskDscFilterCountReq.Merge(m, src)
}
func (m *ExamineTaskDscFilterCountReq) XXX_Size() int {
	return xxx_messageInfo_ExamineTaskDscFilterCountReq.Size(m)
}
func (m *ExamineTaskDscFilterCountReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ExamineTaskDscFilterCountReq.DiscardUnknown(m)
}

var xxx_messageInfo_ExamineTaskDscFilterCountReq proto.InternalMessageInfo

func (m *ExamineTaskDscFilterCountReq) GetProject() string {
	if m != nil {
		return m.Project
	}
	return ""
}

func (m *ExamineTaskDscFilterCountReq) GetRepliedAt() []string {
	if m != nil {
		return m.RepliedAt
	}
	return nil
}

func (m *ExamineTaskDscFilterCountReq) GetTotalPay() int64 {
	if m != nil {
		return m.TotalPay
	}
	return 0
}

// 抽检数量
type ExamineTaskDscPartFilterCountReq struct {
	// 游戏 @gotags: validate:"required"
	Project string `protobuf:"bytes,1,opt,name=project,proto3" json:"project" validate:"required"`
	// 回复时间 @gotags: validate:"required"
	RepliedAt []string `protobuf:"bytes,2,rep,name=replied_at,json=repliedAt,proto3" json:"replied_at" validate:"required"`
	// 充值金额 @gotags: validate:"required,gte=1"
	TotalPay int64 `protobuf:"varint,3,opt,name=total_pay,json=totalPay,proto3" json:"total_pay" validate:"required,gte=1"`
	// 客服
	Maintainer []string `protobuf:"bytes,4,rep,name=maintainer,proto3" json:"maintainer"`
	// vip状态
	VipState []uint32 `protobuf:"varint,5,rep,packed,name=vip_state,json=vipState,proto3" json:"vip_state"`
	// 是否抽检
	IsPart               bool     `protobuf:"varint,6,opt,name=is_part,json=isPart,proto3" json:"is_part"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *ExamineTaskDscPartFilterCountReq) Reset()         { *m = ExamineTaskDscPartFilterCountReq{} }
func (m *ExamineTaskDscPartFilterCountReq) String() string { return proto.CompactTextString(m) }
func (*ExamineTaskDscPartFilterCountReq) ProtoMessage()    {}
func (*ExamineTaskDscPartFilterCountReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_9b15753b7a0c29a9, []int{17}
}

func (m *ExamineTaskDscPartFilterCountReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ExamineTaskDscPartFilterCountReq.Unmarshal(m, b)
}
func (m *ExamineTaskDscPartFilterCountReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ExamineTaskDscPartFilterCountReq.Marshal(b, m, deterministic)
}
func (m *ExamineTaskDscPartFilterCountReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExamineTaskDscPartFilterCountReq.Merge(m, src)
}
func (m *ExamineTaskDscPartFilterCountReq) XXX_Size() int {
	return xxx_messageInfo_ExamineTaskDscPartFilterCountReq.Size(m)
}
func (m *ExamineTaskDscPartFilterCountReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ExamineTaskDscPartFilterCountReq.DiscardUnknown(m)
}

var xxx_messageInfo_ExamineTaskDscPartFilterCountReq proto.InternalMessageInfo

func (m *ExamineTaskDscPartFilterCountReq) GetProject() string {
	if m != nil {
		return m.Project
	}
	return ""
}

func (m *ExamineTaskDscPartFilterCountReq) GetRepliedAt() []string {
	if m != nil {
		return m.RepliedAt
	}
	return nil
}

func (m *ExamineTaskDscPartFilterCountReq) GetTotalPay() int64 {
	if m != nil {
		return m.TotalPay
	}
	return 0
}

func (m *ExamineTaskDscPartFilterCountReq) GetMaintainer() []string {
	if m != nil {
		return m.Maintainer
	}
	return nil
}

func (m *ExamineTaskDscPartFilterCountReq) GetVipState() []uint32 {
	if m != nil {
		return m.VipState
	}
	return nil
}

func (m *ExamineTaskDscPartFilterCountReq) GetIsPart() bool {
	if m != nil {
		return m.IsPart
	}
	return false
}

type ExamineTaskDscFilterCountResp struct {
	// 数量
	Num                  int64    `protobuf:"varint,1,opt,name=num,proto3" json:"num"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *ExamineTaskDscFilterCountResp) Reset()         { *m = ExamineTaskDscFilterCountResp{} }
func (m *ExamineTaskDscFilterCountResp) String() string { return proto.CompactTextString(m) }
func (*ExamineTaskDscFilterCountResp) ProtoMessage()    {}
func (*ExamineTaskDscFilterCountResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_9b15753b7a0c29a9, []int{18}
}

func (m *ExamineTaskDscFilterCountResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ExamineTaskDscFilterCountResp.Unmarshal(m, b)
}
func (m *ExamineTaskDscFilterCountResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ExamineTaskDscFilterCountResp.Marshal(b, m, deterministic)
}
func (m *ExamineTaskDscFilterCountResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExamineTaskDscFilterCountResp.Merge(m, src)
}
func (m *ExamineTaskDscFilterCountResp) XXX_Size() int {
	return xxx_messageInfo_ExamineTaskDscFilterCountResp.Size(m)
}
func (m *ExamineTaskDscFilterCountResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ExamineTaskDscFilterCountResp.DiscardUnknown(m)
}

var xxx_messageInfo_ExamineTaskDscFilterCountResp proto.InternalMessageInfo

func (m *ExamineTaskDscFilterCountResp) GetNum() int64 {
	if m != nil {
		return m.Num
	}
	return 0
}

// 质检单 - 搜索 - req
type ExamineDscOrderListReq struct {
	// 游戏
	Project []string `protobuf:"bytes,1,rep,name=project,proto3" json:"project"`
	// 质检单 ID
	DscExamineId uint64 `protobuf:"varint,2,opt,name=dsc_examine_id,json=dscExamineId,proto3" json:"dsc_examine_id"`
	// 质检状态
	Status []ExamineStateDf `protobuf:"varint,3,rep,packed,name=status,proto3,enum=pb.ExamineStateDf" json:"status"`
	// 质检结果
	FinalResult []ExamineFinalResultDf `protobuf:"varint,4,rep,packed,name=final_result,json=finalResult,proto3,enum=pb.ExamineFinalResultDf" json:"final_result"`
	// 被检员：质检打分时关联的员工
	RelatedAccount []string `protobuf:"bytes,5,rep,name=related_account,json=relatedAccount,proto3" json:"related_account"`
	// 质检员：进行质检打分的人员
	Inspector []string `protobuf:"bytes,6,rep,name=inspector,proto3" json:"inspector"`
	// 创建时间：质检任务的创建时间
	CreatedAt []string `protobuf:"bytes,7,rep,name=created_at,json=createdAt,proto3" json:"created_at"`
	// 结案时间：质检任务的完成时间
	FinishedAt []string `protobuf:"bytes,8,rep,name=finished_at,json=finishedAt,proto3" json:"finished_at"`
	// 玩家VIP状态
	VipState []uint32 `protobuf:"varint,12,rep,packed,name=vip_state,json=vipState,proto3" json:"vip_state"`
	// 页码
	Page uint32 `protobuf:"varint,9,opt,name=page,proto3" json:"page"`
	// 页大小
	PageSize             uint32   `protobuf:"varint,10,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *ExamineDscOrderListReq) Reset()         { *m = ExamineDscOrderListReq{} }
func (m *ExamineDscOrderListReq) String() string { return proto.CompactTextString(m) }
func (*ExamineDscOrderListReq) ProtoMessage()    {}
func (*ExamineDscOrderListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_9b15753b7a0c29a9, []int{19}
}

func (m *ExamineDscOrderListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ExamineDscOrderListReq.Unmarshal(m, b)
}
func (m *ExamineDscOrderListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ExamineDscOrderListReq.Marshal(b, m, deterministic)
}
func (m *ExamineDscOrderListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExamineDscOrderListReq.Merge(m, src)
}
func (m *ExamineDscOrderListReq) XXX_Size() int {
	return xxx_messageInfo_ExamineDscOrderListReq.Size(m)
}
func (m *ExamineDscOrderListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ExamineDscOrderListReq.DiscardUnknown(m)
}

var xxx_messageInfo_ExamineDscOrderListReq proto.InternalMessageInfo

func (m *ExamineDscOrderListReq) GetProject() []string {
	if m != nil {
		return m.Project
	}
	return nil
}

func (m *ExamineDscOrderListReq) GetDscExamineId() uint64 {
	if m != nil {
		return m.DscExamineId
	}
	return 0
}

func (m *ExamineDscOrderListReq) GetStatus() []ExamineStateDf {
	if m != nil {
		return m.Status
	}
	return nil
}

func (m *ExamineDscOrderListReq) GetFinalResult() []ExamineFinalResultDf {
	if m != nil {
		return m.FinalResult
	}
	return nil
}

func (m *ExamineDscOrderListReq) GetRelatedAccount() []string {
	if m != nil {
		return m.RelatedAccount
	}
	return nil
}

func (m *ExamineDscOrderListReq) GetInspector() []string {
	if m != nil {
		return m.Inspector
	}
	return nil
}

func (m *ExamineDscOrderListReq) GetCreatedAt() []string {
	if m != nil {
		return m.CreatedAt
	}
	return nil
}

func (m *ExamineDscOrderListReq) GetFinishedAt() []string {
	if m != nil {
		return m.FinishedAt
	}
	return nil
}

func (m *ExamineDscOrderListReq) GetVipState() []uint32 {
	if m != nil {
		return m.VipState
	}
	return nil
}

func (m *ExamineDscOrderListReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *ExamineDscOrderListReq) GetPageSize() uint32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

// 质检单 - 搜索 - resp
type ExamineDscOrderListResp struct {
	CurrentPage          uint32                                     `protobuf:"varint,1,opt,name=current_page,json=currentPage,proto3" json:"current_page"`
	PerPage              uint32                                     `protobuf:"varint,2,opt,name=per_page,json=perPage,proto3" json:"per_page"`
	Total                int64                                      `protobuf:"varint,3,opt,name=total,proto3" json:"total"`
	Data                 []*ExamineDscOrderListResp_ExamineDscOrder `protobuf:"bytes,4,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}                                   `json:"-" gorm:"-"`
	XXX_unrecognized     []byte                                     `json:"-" gorm:"-"`
	XXX_sizecache        int32                                      `json:"-" gorm:"-"`
}

func (m *ExamineDscOrderListResp) Reset()         { *m = ExamineDscOrderListResp{} }
func (m *ExamineDscOrderListResp) String() string { return proto.CompactTextString(m) }
func (*ExamineDscOrderListResp) ProtoMessage()    {}
func (*ExamineDscOrderListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_9b15753b7a0c29a9, []int{20}
}

func (m *ExamineDscOrderListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ExamineDscOrderListResp.Unmarshal(m, b)
}
func (m *ExamineDscOrderListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ExamineDscOrderListResp.Marshal(b, m, deterministic)
}
func (m *ExamineDscOrderListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExamineDscOrderListResp.Merge(m, src)
}
func (m *ExamineDscOrderListResp) XXX_Size() int {
	return xxx_messageInfo_ExamineDscOrderListResp.Size(m)
}
func (m *ExamineDscOrderListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ExamineDscOrderListResp.DiscardUnknown(m)
}

var xxx_messageInfo_ExamineDscOrderListResp proto.InternalMessageInfo

func (m *ExamineDscOrderListResp) GetCurrentPage() uint32 {
	if m != nil {
		return m.CurrentPage
	}
	return 0
}

func (m *ExamineDscOrderListResp) GetPerPage() uint32 {
	if m != nil {
		return m.PerPage
	}
	return 0
}

func (m *ExamineDscOrderListResp) GetTotal() int64 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *ExamineDscOrderListResp) GetData() []*ExamineDscOrderListResp_ExamineDscOrder {
	if m != nil {
		return m.Data
	}
	return nil
}

type ExamineDscOrderListResp_ExamineDscOrder struct {
	// 质检单 ID
	DscExamineId uint64 `protobuf:"varint,1,opt,name=dsc_examine_id,json=dscExamineId,proto3" json:"dsc_examine_id"`
	// 任务id
	TaskId uint64 `protobuf:"varint,6,opt,name=task_id,json=taskId,proto3" json:"task_id"`
	// 模板id
	TplId uint64 `protobuf:"varint,7,opt,name=tpl_id,json=tplId,proto3" json:"tpl_id"`
	// 游戏
	Project string `protobuf:"bytes,2,opt,name=project,proto3" json:"project"`
	// 玩家 DC ID
	DscUserId string `protobuf:"bytes,3,opt,name=dsc_user_id,json=dscUserId,proto3" json:"dsc_user_id"`
	// 玩家名称 - 前端展示使用此字段
	UserName string `protobuf:"bytes,4,opt,name=user_name,json=userName,proto3" json:"user_name"`
	// dm channel
	DmChannel string `protobuf:"bytes,5,opt,name=dm_channel,json=dmChannel,proto3" json:"dm_channel"`
	// 累付金额
	TotalPay float64 `protobuf:"fixed64,8,opt,name=total_pay,json=totalPay,proto3" json:"total_pay"`
	// 最近30天付费金额
	PayLastThirtyDays float64 `protobuf:"fixed64,9,opt,name=pay_last_thirty_days,json=payLastThirtyDays,proto3" json:"pay_last_thirty_days"`
	// 最近登录时间
	LastLogin string `protobuf:"bytes,10,opt,name=last_login,json=lastLogin,proto3" json:"last_login"`
	// 玩家信息回复状态
	RepliedStatus uint32 `protobuf:"varint,11,opt,name=replied_status,json=repliedStatus,proto3" json:"replied_status"`
	// 玩家VIP状态
	VipState uint32 `protobuf:"varint,12,opt,name=vip_state,json=vipState,proto3" json:"vip_state"`
	Fpid     string `protobuf:"bytes,13,opt,name=fpid,proto3" json:"fpid"`
	Uid      uint64 `protobuf:"varint,14,opt,name=uid,proto3" json:"uid"`
	Sid      string `protobuf:"bytes,15,opt,name=sid,proto3" json:"sid"`
	// 机器人 user_id
	BotId string `protobuf:"bytes,16,opt,name=bot_id,json=botId,proto3" json:"bot_id"`
	// 机器人昵称
	BotShow string `protobuf:"bytes,19,opt,name=bot_show,json=botShow,proto3" json:"bot_show"`
	// 质检状态
	Status ExamineStateDf `protobuf:"varint,20,opt,name=status,proto3,enum=pb.ExamineStateDf" json:"status"`
	// 质检结果
	FinalResult ExamineFinalResultDf `protobuf:"varint,21,opt,name=final_result,json=finalResult,proto3,enum=pb.ExamineFinalResultDf" json:"final_result"`
	// 创建时间：质检任务的创建时间
	CreatedAt string `protobuf:"bytes,22,opt,name=created_at,json=createdAt,proto3" json:"created_at"`
	// 结案时间：质检任务的完成时间
	FinishedAt string `protobuf:"bytes,23,opt,name=finished_at,json=finishedAt,proto3" json:"finished_at"`
	// 质检员
	Inspector            string   `protobuf:"bytes,24,opt,name=inspector,proto3" json:"inspector"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *ExamineDscOrderListResp_ExamineDscOrder) Reset() {
	*m = ExamineDscOrderListResp_ExamineDscOrder{}
}
func (m *ExamineDscOrderListResp_ExamineDscOrder) String() string { return proto.CompactTextString(m) }
func (*ExamineDscOrderListResp_ExamineDscOrder) ProtoMessage()    {}
func (*ExamineDscOrderListResp_ExamineDscOrder) Descriptor() ([]byte, []int) {
	return fileDescriptor_9b15753b7a0c29a9, []int{20, 0}
}

func (m *ExamineDscOrderListResp_ExamineDscOrder) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ExamineDscOrderListResp_ExamineDscOrder.Unmarshal(m, b)
}
func (m *ExamineDscOrderListResp_ExamineDscOrder) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ExamineDscOrderListResp_ExamineDscOrder.Marshal(b, m, deterministic)
}
func (m *ExamineDscOrderListResp_ExamineDscOrder) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExamineDscOrderListResp_ExamineDscOrder.Merge(m, src)
}
func (m *ExamineDscOrderListResp_ExamineDscOrder) XXX_Size() int {
	return xxx_messageInfo_ExamineDscOrderListResp_ExamineDscOrder.Size(m)
}
func (m *ExamineDscOrderListResp_ExamineDscOrder) XXX_DiscardUnknown() {
	xxx_messageInfo_ExamineDscOrderListResp_ExamineDscOrder.DiscardUnknown(m)
}

var xxx_messageInfo_ExamineDscOrderListResp_ExamineDscOrder proto.InternalMessageInfo

func (m *ExamineDscOrderListResp_ExamineDscOrder) GetDscExamineId() uint64 {
	if m != nil {
		return m.DscExamineId
	}
	return 0
}

func (m *ExamineDscOrderListResp_ExamineDscOrder) GetTaskId() uint64 {
	if m != nil {
		return m.TaskId
	}
	return 0
}

func (m *ExamineDscOrderListResp_ExamineDscOrder) GetTplId() uint64 {
	if m != nil {
		return m.TplId
	}
	return 0
}

func (m *ExamineDscOrderListResp_ExamineDscOrder) GetProject() string {
	if m != nil {
		return m.Project
	}
	return ""
}

func (m *ExamineDscOrderListResp_ExamineDscOrder) GetDscUserId() string {
	if m != nil {
		return m.DscUserId
	}
	return ""
}

func (m *ExamineDscOrderListResp_ExamineDscOrder) GetUserName() string {
	if m != nil {
		return m.UserName
	}
	return ""
}

func (m *ExamineDscOrderListResp_ExamineDscOrder) GetDmChannel() string {
	if m != nil {
		return m.DmChannel
	}
	return ""
}

func (m *ExamineDscOrderListResp_ExamineDscOrder) GetTotalPay() float64 {
	if m != nil {
		return m.TotalPay
	}
	return 0
}

func (m *ExamineDscOrderListResp_ExamineDscOrder) GetPayLastThirtyDays() float64 {
	if m != nil {
		return m.PayLastThirtyDays
	}
	return 0
}

func (m *ExamineDscOrderListResp_ExamineDscOrder) GetLastLogin() string {
	if m != nil {
		return m.LastLogin
	}
	return ""
}

func (m *ExamineDscOrderListResp_ExamineDscOrder) GetRepliedStatus() uint32 {
	if m != nil {
		return m.RepliedStatus
	}
	return 0
}

func (m *ExamineDscOrderListResp_ExamineDscOrder) GetVipState() uint32 {
	if m != nil {
		return m.VipState
	}
	return 0
}

func (m *ExamineDscOrderListResp_ExamineDscOrder) GetFpid() string {
	if m != nil {
		return m.Fpid
	}
	return ""
}

func (m *ExamineDscOrderListResp_ExamineDscOrder) GetUid() uint64 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *ExamineDscOrderListResp_ExamineDscOrder) GetSid() string {
	if m != nil {
		return m.Sid
	}
	return ""
}

func (m *ExamineDscOrderListResp_ExamineDscOrder) GetBotId() string {
	if m != nil {
		return m.BotId
	}
	return ""
}

func (m *ExamineDscOrderListResp_ExamineDscOrder) GetBotShow() string {
	if m != nil {
		return m.BotShow
	}
	return ""
}

func (m *ExamineDscOrderListResp_ExamineDscOrder) GetStatus() ExamineStateDf {
	if m != nil {
		return m.Status
	}
	return ExamineStateDf_ExamineStateDfUnknown
}

func (m *ExamineDscOrderListResp_ExamineDscOrder) GetFinalResult() ExamineFinalResultDf {
	if m != nil {
		return m.FinalResult
	}
	return ExamineFinalResultDf_ExamineFinalResultDfUnknown
}

func (m *ExamineDscOrderListResp_ExamineDscOrder) GetCreatedAt() string {
	if m != nil {
		return m.CreatedAt
	}
	return ""
}

func (m *ExamineDscOrderListResp_ExamineDscOrder) GetFinishedAt() string {
	if m != nil {
		return m.FinishedAt
	}
	return ""
}

func (m *ExamineDscOrderListResp_ExamineDscOrder) GetInspector() string {
	if m != nil {
		return m.Inspector
	}
	return ""
}

// 质检单 - 详情 - req
type ExamineDscOrderDetailReq struct {
	// 质检单 ID
	DscExamineId         uint64   `protobuf:"varint,1,opt,name=dsc_examine_id,json=dscExamineId,proto3" json:"dsc_examine_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *ExamineDscOrderDetailReq) Reset()         { *m = ExamineDscOrderDetailReq{} }
func (m *ExamineDscOrderDetailReq) String() string { return proto.CompactTextString(m) }
func (*ExamineDscOrderDetailReq) ProtoMessage()    {}
func (*ExamineDscOrderDetailReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_9b15753b7a0c29a9, []int{21}
}

func (m *ExamineDscOrderDetailReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ExamineDscOrderDetailReq.Unmarshal(m, b)
}
func (m *ExamineDscOrderDetailReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ExamineDscOrderDetailReq.Marshal(b, m, deterministic)
}
func (m *ExamineDscOrderDetailReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExamineDscOrderDetailReq.Merge(m, src)
}
func (m *ExamineDscOrderDetailReq) XXX_Size() int {
	return xxx_messageInfo_ExamineDscOrderDetailReq.Size(m)
}
func (m *ExamineDscOrderDetailReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ExamineDscOrderDetailReq.DiscardUnknown(m)
}

var xxx_messageInfo_ExamineDscOrderDetailReq proto.InternalMessageInfo

func (m *ExamineDscOrderDetailReq) GetDscExamineId() uint64 {
	if m != nil {
		return m.DscExamineId
	}
	return 0
}

// 质检单 - 详情 - resp
type ExamineDscOrderDetailResp struct {
	// 质检单 - id
	DscExamineId uint64 `protobuf:"varint,1,opt,name=dsc_examine_id,json=dscExamineId,proto3" json:"dsc_examine_id"`
	// 任务id
	TaskId uint64 `protobuf:"varint,2,opt,name=task_id,json=taskId,proto3" json:"task_id"`
	// 模板id
	TplId uint64 `protobuf:"varint,3,opt,name=tpl_id,json=tplId,proto3" json:"tpl_id"`
	// 项目
	Project string `protobuf:"bytes,4,opt,name=project,proto3" json:"project"`
	// 玩家 DC ID
	DscUserId string `protobuf:"bytes,5,opt,name=dsc_user_id,json=dscUserId,proto3" json:"dsc_user_id"`
	// 机器人 id
	DscBotId string `protobuf:"bytes,6,opt,name=dsc_bot_id,json=dscBotId,proto3" json:"dsc_bot_id"`
	// 渠道id
	DscChannelId string `protobuf:"bytes,7,opt,name=dsc_channel_id,json=dscChannelId,proto3" json:"dsc_channel_id"`
	// 质检员
	Inspector string `protobuf:"bytes,8,opt,name=inspector,proto3" json:"inspector"`
	// 质检状态
	Status ExamineStateDf `protobuf:"varint,9,opt,name=status,proto3,enum=pb.ExamineStateDf" json:"status"`
	// 创建时间
	CreatedAt string `protobuf:"bytes,10,opt,name=created_at,json=createdAt,proto3" json:"created_at"`
	// 结案时间
	FinishedAt string `protobuf:"bytes,11,opt,name=finished_at,json=finishedAt,proto3" json:"finished_at"`
	// 是否可以编辑：true:可编辑； false:不可编辑
	CanEdited bool `protobuf:"varint,12,opt,name=can_edited,json=canEdited,proto3" json:"can_edited"`
	// 自定义表单数据
	DefineField *anypb.Any `protobuf:"bytes,20,opt,name=define_field,json=defineField,proto3" json:"define_field"`
	// 通用结果字段
	CommonField          *ExamineDscOrderDetailResp_Common `protobuf:"bytes,21,opt,name=common_field,json=commonField,proto3" json:"common_field"`
	XXX_NoUnkeyedLiteral struct{}                          `json:"-" gorm:"-"`
	XXX_unrecognized     []byte                            `json:"-" gorm:"-"`
	XXX_sizecache        int32                             `json:"-" gorm:"-"`
}

func (m *ExamineDscOrderDetailResp) Reset()         { *m = ExamineDscOrderDetailResp{} }
func (m *ExamineDscOrderDetailResp) String() string { return proto.CompactTextString(m) }
func (*ExamineDscOrderDetailResp) ProtoMessage()    {}
func (*ExamineDscOrderDetailResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_9b15753b7a0c29a9, []int{22}
}

func (m *ExamineDscOrderDetailResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ExamineDscOrderDetailResp.Unmarshal(m, b)
}
func (m *ExamineDscOrderDetailResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ExamineDscOrderDetailResp.Marshal(b, m, deterministic)
}
func (m *ExamineDscOrderDetailResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExamineDscOrderDetailResp.Merge(m, src)
}
func (m *ExamineDscOrderDetailResp) XXX_Size() int {
	return xxx_messageInfo_ExamineDscOrderDetailResp.Size(m)
}
func (m *ExamineDscOrderDetailResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ExamineDscOrderDetailResp.DiscardUnknown(m)
}

var xxx_messageInfo_ExamineDscOrderDetailResp proto.InternalMessageInfo

func (m *ExamineDscOrderDetailResp) GetDscExamineId() uint64 {
	if m != nil {
		return m.DscExamineId
	}
	return 0
}

func (m *ExamineDscOrderDetailResp) GetTaskId() uint64 {
	if m != nil {
		return m.TaskId
	}
	return 0
}

func (m *ExamineDscOrderDetailResp) GetTplId() uint64 {
	if m != nil {
		return m.TplId
	}
	return 0
}

func (m *ExamineDscOrderDetailResp) GetProject() string {
	if m != nil {
		return m.Project
	}
	return ""
}

func (m *ExamineDscOrderDetailResp) GetDscUserId() string {
	if m != nil {
		return m.DscUserId
	}
	return ""
}

func (m *ExamineDscOrderDetailResp) GetDscBotId() string {
	if m != nil {
		return m.DscBotId
	}
	return ""
}

func (m *ExamineDscOrderDetailResp) GetDscChannelId() string {
	if m != nil {
		return m.DscChannelId
	}
	return ""
}

func (m *ExamineDscOrderDetailResp) GetInspector() string {
	if m != nil {
		return m.Inspector
	}
	return ""
}

func (m *ExamineDscOrderDetailResp) GetStatus() ExamineStateDf {
	if m != nil {
		return m.Status
	}
	return ExamineStateDf_ExamineStateDfUnknown
}

func (m *ExamineDscOrderDetailResp) GetCreatedAt() string {
	if m != nil {
		return m.CreatedAt
	}
	return ""
}

func (m *ExamineDscOrderDetailResp) GetFinishedAt() string {
	if m != nil {
		return m.FinishedAt
	}
	return ""
}

func (m *ExamineDscOrderDetailResp) GetCanEdited() bool {
	if m != nil {
		return m.CanEdited
	}
	return false
}

func (m *ExamineDscOrderDetailResp) GetDefineField() *anypb.Any {
	if m != nil {
		return m.DefineField
	}
	return nil
}

func (m *ExamineDscOrderDetailResp) GetCommonField() *ExamineDscOrderDetailResp_Common {
	if m != nil {
		return m.CommonField
	}
	return nil
}

type ExamineDscOrderDetailResp_Common struct {
	// 质检结果 @gotags: validate:"required,gt=0"
	FinalResult ExamineFinalResultDf `protobuf:"varint,1,opt,name=final_result,json=finalResult,proto3,enum=pb.ExamineFinalResultDf" json:"final_result" validate:"required,gt=0"`
	// 质检分数
	FinalScore int32 `protobuf:"varint,2,opt,name=final_score,json=finalScore,proto3" json:"final_score"`
	// 错误根源
	FinalReason string `protobuf:"bytes,3,opt,name=final_reason,json=finalReason,proto3" json:"final_reason"`
	// 质检结果关联员工：下拉多选，支持搜索
	RelatedAccount []string `protobuf:"bytes,4,rep,name=related_account,json=relatedAccount,proto3" json:"related_account"`
	// 同步质检结果：
	NoticeAccount []string `protobuf:"bytes,5,rep,name=notice_account,json=noticeAccount,proto3" json:"notice_account"`
	// 质检问题描述
	FinalDesc string `protobuf:"bytes,6,opt,name=final_desc,json=finalDesc,proto3" json:"final_desc"`
	// 修改原因备注
	FinalResultModifyComment string   `protobuf:"bytes,7,opt,name=final_result_modify_comment,json=finalResultModifyComment,proto3" json:"final_result_modify_comment"`
	XXX_NoUnkeyedLiteral     struct{} `json:"-" gorm:"-"`
	XXX_unrecognized         []byte   `json:"-" gorm:"-"`
	XXX_sizecache            int32    `json:"-" gorm:"-"`
}

func (m *ExamineDscOrderDetailResp_Common) Reset()         { *m = ExamineDscOrderDetailResp_Common{} }
func (m *ExamineDscOrderDetailResp_Common) String() string { return proto.CompactTextString(m) }
func (*ExamineDscOrderDetailResp_Common) ProtoMessage()    {}
func (*ExamineDscOrderDetailResp_Common) Descriptor() ([]byte, []int) {
	return fileDescriptor_9b15753b7a0c29a9, []int{22, 0}
}

func (m *ExamineDscOrderDetailResp_Common) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ExamineDscOrderDetailResp_Common.Unmarshal(m, b)
}
func (m *ExamineDscOrderDetailResp_Common) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ExamineDscOrderDetailResp_Common.Marshal(b, m, deterministic)
}
func (m *ExamineDscOrderDetailResp_Common) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExamineDscOrderDetailResp_Common.Merge(m, src)
}
func (m *ExamineDscOrderDetailResp_Common) XXX_Size() int {
	return xxx_messageInfo_ExamineDscOrderDetailResp_Common.Size(m)
}
func (m *ExamineDscOrderDetailResp_Common) XXX_DiscardUnknown() {
	xxx_messageInfo_ExamineDscOrderDetailResp_Common.DiscardUnknown(m)
}

var xxx_messageInfo_ExamineDscOrderDetailResp_Common proto.InternalMessageInfo

func (m *ExamineDscOrderDetailResp_Common) GetFinalResult() ExamineFinalResultDf {
	if m != nil {
		return m.FinalResult
	}
	return ExamineFinalResultDf_ExamineFinalResultDfUnknown
}

func (m *ExamineDscOrderDetailResp_Common) GetFinalScore() int32 {
	if m != nil {
		return m.FinalScore
	}
	return 0
}

func (m *ExamineDscOrderDetailResp_Common) GetFinalReason() string {
	if m != nil {
		return m.FinalReason
	}
	return ""
}

func (m *ExamineDscOrderDetailResp_Common) GetRelatedAccount() []string {
	if m != nil {
		return m.RelatedAccount
	}
	return nil
}

func (m *ExamineDscOrderDetailResp_Common) GetNoticeAccount() []string {
	if m != nil {
		return m.NoticeAccount
	}
	return nil
}

func (m *ExamineDscOrderDetailResp_Common) GetFinalDesc() string {
	if m != nil {
		return m.FinalDesc
	}
	return ""
}

func (m *ExamineDscOrderDetailResp_Common) GetFinalResultModifyComment() string {
	if m != nil {
		return m.FinalResultModifyComment
	}
	return ""
}

// 质检单 - 保存 - req
type ExamineDscOrderSaveReq struct {
	// 质检单-id
	DscExamineId uint64 `protobuf:"varint,1,opt,name=dsc_examine_id,json=dscExamineId,proto3" json:"dsc_examine_id"`
	// 自定义表单数据 eg: {"操作1": ["xxxx","xx"],"操作2": "xxxx"}
	DefineField *anypb.Any `protobuf:"bytes,20,opt,name=define_field,json=defineField,proto3" json:"define_field"`
	// 通用结果字段
	CommonField          *ExamineDscOrderSaveReq_Common `protobuf:"bytes,21,opt,name=common_field,json=commonField,proto3" json:"common_field"`
	XXX_NoUnkeyedLiteral struct{}                       `json:"-" gorm:"-"`
	XXX_unrecognized     []byte                         `json:"-" gorm:"-"`
	XXX_sizecache        int32                          `json:"-" gorm:"-"`
}

func (m *ExamineDscOrderSaveReq) Reset()         { *m = ExamineDscOrderSaveReq{} }
func (m *ExamineDscOrderSaveReq) String() string { return proto.CompactTextString(m) }
func (*ExamineDscOrderSaveReq) ProtoMessage()    {}
func (*ExamineDscOrderSaveReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_9b15753b7a0c29a9, []int{23}
}

func (m *ExamineDscOrderSaveReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ExamineDscOrderSaveReq.Unmarshal(m, b)
}
func (m *ExamineDscOrderSaveReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ExamineDscOrderSaveReq.Marshal(b, m, deterministic)
}
func (m *ExamineDscOrderSaveReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExamineDscOrderSaveReq.Merge(m, src)
}
func (m *ExamineDscOrderSaveReq) XXX_Size() int {
	return xxx_messageInfo_ExamineDscOrderSaveReq.Size(m)
}
func (m *ExamineDscOrderSaveReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ExamineDscOrderSaveReq.DiscardUnknown(m)
}

var xxx_messageInfo_ExamineDscOrderSaveReq proto.InternalMessageInfo

func (m *ExamineDscOrderSaveReq) GetDscExamineId() uint64 {
	if m != nil {
		return m.DscExamineId
	}
	return 0
}

func (m *ExamineDscOrderSaveReq) GetDefineField() *anypb.Any {
	if m != nil {
		return m.DefineField
	}
	return nil
}

func (m *ExamineDscOrderSaveReq) GetCommonField() *ExamineDscOrderSaveReq_Common {
	if m != nil {
		return m.CommonField
	}
	return nil
}

type ExamineDscOrderSaveReq_Common struct {
	// 质检结果 @gotags: validate:"required,gt=0"
	FinalResult ExamineFinalResultDf `protobuf:"varint,1,opt,name=final_result,json=finalResult,proto3,enum=pb.ExamineFinalResultDf" json:"final_result" validate:"required,gt=0"`
	// 质检分数
	FinalScore int32 `protobuf:"varint,2,opt,name=final_score,json=finalScore,proto3" json:"final_score"`
	// 错误根源
	FinalReason string `protobuf:"bytes,3,opt,name=final_reason,json=finalReason,proto3" json:"final_reason"`
	// 质检结果关联员工：下拉多选，支持搜索 @gotags: validate:"required"
	RelatedAccount []string `protobuf:"bytes,4,rep,name=related_account,json=relatedAccount,proto3" json:"related_account" validate:"required"`
	// 同步质检结果：
	NoticeAccount []string `protobuf:"bytes,5,rep,name=notice_account,json=noticeAccount,proto3" json:"notice_account"`
	// 质检问题描述
	FinalDesc string `protobuf:"bytes,6,opt,name=final_desc,json=finalDesc,proto3" json:"final_desc"`
	// 修改原因备注
	FinalResultModifyComment string   `protobuf:"bytes,7,opt,name=final_result_modify_comment,json=finalResultModifyComment,proto3" json:"final_result_modify_comment"`
	XXX_NoUnkeyedLiteral     struct{} `json:"-" gorm:"-"`
	XXX_unrecognized         []byte   `json:"-" gorm:"-"`
	XXX_sizecache            int32    `json:"-" gorm:"-"`
}

func (m *ExamineDscOrderSaveReq_Common) Reset()         { *m = ExamineDscOrderSaveReq_Common{} }
func (m *ExamineDscOrderSaveReq_Common) String() string { return proto.CompactTextString(m) }
func (*ExamineDscOrderSaveReq_Common) ProtoMessage()    {}
func (*ExamineDscOrderSaveReq_Common) Descriptor() ([]byte, []int) {
	return fileDescriptor_9b15753b7a0c29a9, []int{23, 0}
}

func (m *ExamineDscOrderSaveReq_Common) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ExamineDscOrderSaveReq_Common.Unmarshal(m, b)
}
func (m *ExamineDscOrderSaveReq_Common) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ExamineDscOrderSaveReq_Common.Marshal(b, m, deterministic)
}
func (m *ExamineDscOrderSaveReq_Common) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExamineDscOrderSaveReq_Common.Merge(m, src)
}
func (m *ExamineDscOrderSaveReq_Common) XXX_Size() int {
	return xxx_messageInfo_ExamineDscOrderSaveReq_Common.Size(m)
}
func (m *ExamineDscOrderSaveReq_Common) XXX_DiscardUnknown() {
	xxx_messageInfo_ExamineDscOrderSaveReq_Common.DiscardUnknown(m)
}

var xxx_messageInfo_ExamineDscOrderSaveReq_Common proto.InternalMessageInfo

func (m *ExamineDscOrderSaveReq_Common) GetFinalResult() ExamineFinalResultDf {
	if m != nil {
		return m.FinalResult
	}
	return ExamineFinalResultDf_ExamineFinalResultDfUnknown
}

func (m *ExamineDscOrderSaveReq_Common) GetFinalScore() int32 {
	if m != nil {
		return m.FinalScore
	}
	return 0
}

func (m *ExamineDscOrderSaveReq_Common) GetFinalReason() string {
	if m != nil {
		return m.FinalReason
	}
	return ""
}

func (m *ExamineDscOrderSaveReq_Common) GetRelatedAccount() []string {
	if m != nil {
		return m.RelatedAccount
	}
	return nil
}

func (m *ExamineDscOrderSaveReq_Common) GetNoticeAccount() []string {
	if m != nil {
		return m.NoticeAccount
	}
	return nil
}

func (m *ExamineDscOrderSaveReq_Common) GetFinalDesc() string {
	if m != nil {
		return m.FinalDesc
	}
	return ""
}

func (m *ExamineDscOrderSaveReq_Common) GetFinalResultModifyComment() string {
	if m != nil {
		return m.FinalResultModifyComment
	}
	return ""
}

// discord 质检单统计数据 - req
type ExamineDscOrderStatsReq struct {
	Project              []string `protobuf:"bytes,1,rep,name=project,proto3" json:"project"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *ExamineDscOrderStatsReq) Reset()         { *m = ExamineDscOrderStatsReq{} }
func (m *ExamineDscOrderStatsReq) String() string { return proto.CompactTextString(m) }
func (*ExamineDscOrderStatsReq) ProtoMessage()    {}
func (*ExamineDscOrderStatsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_9b15753b7a0c29a9, []int{24}
}

func (m *ExamineDscOrderStatsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ExamineDscOrderStatsReq.Unmarshal(m, b)
}
func (m *ExamineDscOrderStatsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ExamineDscOrderStatsReq.Marshal(b, m, deterministic)
}
func (m *ExamineDscOrderStatsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExamineDscOrderStatsReq.Merge(m, src)
}
func (m *ExamineDscOrderStatsReq) XXX_Size() int {
	return xxx_messageInfo_ExamineDscOrderStatsReq.Size(m)
}
func (m *ExamineDscOrderStatsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ExamineDscOrderStatsReq.DiscardUnknown(m)
}

var xxx_messageInfo_ExamineDscOrderStatsReq proto.InternalMessageInfo

func (m *ExamineDscOrderStatsReq) GetProject() []string {
	if m != nil {
		return m.Project
	}
	return nil
}

// discord 质检单统计数据 - resp
type ExamineDscOrderStatsResp struct {
	// 全部数据量
	ExamineDscCount int64 `protobuf:"varint,1,opt,name=examine_dsc_count,json=examineDscCount,proto3" json:"examine_dsc_count"`
	// 待处理质检任务
	ExamineDscWaitCount int64 `protobuf:"varint,2,opt,name=examine_dsc_wait_count,json=examineDscWaitCount,proto3" json:"examine_dsc_wait_count"`
	// 我的待处理质检任务
	ExamineDscMineWaitCount int64 `protobuf:"varint,3,opt,name=examine_dsc_mine_wait_count,json=examineDscMineWaitCount,proto3" json:"examine_dsc_mine_wait_count"`
	// 我的已完成质检任务
	ExamineDscMineDoneCount int64 `protobuf:"varint,4,opt,name=examine_dsc_mine_done_count,json=examineDscMineDoneCount,proto3" json:"examine_dsc_mine_done_count"`
	// 质检不合格任务
	ExamineDscUnqualifiedCount int64 `protobuf:"varint,5,opt,name=examine_dsc_unqualified_count,json=examineDscUnqualifiedCount,proto3" json:"examine_dsc_unqualified_count"`
	// 我的质检不合格任务
	ExamineDscMineUnqualifiedCount int64    `protobuf:"varint,6,opt,name=examine_dsc_mine_unqualified_count,json=examineDscMineUnqualifiedCount,proto3" json:"examine_dsc_mine_unqualified_count"`
	XXX_NoUnkeyedLiteral           struct{} `json:"-" gorm:"-"`
	XXX_unrecognized               []byte   `json:"-" gorm:"-"`
	XXX_sizecache                  int32    `json:"-" gorm:"-"`
}

func (m *ExamineDscOrderStatsResp) Reset()         { *m = ExamineDscOrderStatsResp{} }
func (m *ExamineDscOrderStatsResp) String() string { return proto.CompactTextString(m) }
func (*ExamineDscOrderStatsResp) ProtoMessage()    {}
func (*ExamineDscOrderStatsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_9b15753b7a0c29a9, []int{25}
}

func (m *ExamineDscOrderStatsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ExamineDscOrderStatsResp.Unmarshal(m, b)
}
func (m *ExamineDscOrderStatsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ExamineDscOrderStatsResp.Marshal(b, m, deterministic)
}
func (m *ExamineDscOrderStatsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExamineDscOrderStatsResp.Merge(m, src)
}
func (m *ExamineDscOrderStatsResp) XXX_Size() int {
	return xxx_messageInfo_ExamineDscOrderStatsResp.Size(m)
}
func (m *ExamineDscOrderStatsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ExamineDscOrderStatsResp.DiscardUnknown(m)
}

var xxx_messageInfo_ExamineDscOrderStatsResp proto.InternalMessageInfo

func (m *ExamineDscOrderStatsResp) GetExamineDscCount() int64 {
	if m != nil {
		return m.ExamineDscCount
	}
	return 0
}

func (m *ExamineDscOrderStatsResp) GetExamineDscWaitCount() int64 {
	if m != nil {
		return m.ExamineDscWaitCount
	}
	return 0
}

func (m *ExamineDscOrderStatsResp) GetExamineDscMineWaitCount() int64 {
	if m != nil {
		return m.ExamineDscMineWaitCount
	}
	return 0
}

func (m *ExamineDscOrderStatsResp) GetExamineDscMineDoneCount() int64 {
	if m != nil {
		return m.ExamineDscMineDoneCount
	}
	return 0
}

func (m *ExamineDscOrderStatsResp) GetExamineDscUnqualifiedCount() int64 {
	if m != nil {
		return m.ExamineDscUnqualifiedCount
	}
	return 0
}

func (m *ExamineDscOrderStatsResp) GetExamineDscMineUnqualifiedCount() int64 {
	if m != nil {
		return m.ExamineDscMineUnqualifiedCount
	}
	return 0
}

// 质检 - 红点
type ExamineOrderNoticeAcceptorResp struct {
	// 是否有 未读消息
	NewNotice            bool     `protobuf:"varint,1,opt,name=new_notice,json=newNotice,proto3" json:"new_notice"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *ExamineOrderNoticeAcceptorResp) Reset()         { *m = ExamineOrderNoticeAcceptorResp{} }
func (m *ExamineOrderNoticeAcceptorResp) String() string { return proto.CompactTextString(m) }
func (*ExamineOrderNoticeAcceptorResp) ProtoMessage()    {}
func (*ExamineOrderNoticeAcceptorResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_9b15753b7a0c29a9, []int{26}
}

func (m *ExamineOrderNoticeAcceptorResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ExamineOrderNoticeAcceptorResp.Unmarshal(m, b)
}
func (m *ExamineOrderNoticeAcceptorResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ExamineOrderNoticeAcceptorResp.Marshal(b, m, deterministic)
}
func (m *ExamineOrderNoticeAcceptorResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExamineOrderNoticeAcceptorResp.Merge(m, src)
}
func (m *ExamineOrderNoticeAcceptorResp) XXX_Size() int {
	return xxx_messageInfo_ExamineOrderNoticeAcceptorResp.Size(m)
}
func (m *ExamineOrderNoticeAcceptorResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ExamineOrderNoticeAcceptorResp.DiscardUnknown(m)
}

var xxx_messageInfo_ExamineOrderNoticeAcceptorResp proto.InternalMessageInfo

func (m *ExamineOrderNoticeAcceptorResp) GetNewNotice() bool {
	if m != nil {
		return m.NewNotice
	}
	return false
}

type ExamineOrderNoticeListReq struct {
	// 页码
	Page uint32 `protobuf:"varint,2,opt,name=page,proto3" json:"page"`
	// 页大小
	PageSize             uint32   `protobuf:"varint,3,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *ExamineOrderNoticeListReq) Reset()         { *m = ExamineOrderNoticeListReq{} }
func (m *ExamineOrderNoticeListReq) String() string { return proto.CompactTextString(m) }
func (*ExamineOrderNoticeListReq) ProtoMessage()    {}
func (*ExamineOrderNoticeListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_9b15753b7a0c29a9, []int{27}
}

func (m *ExamineOrderNoticeListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ExamineOrderNoticeListReq.Unmarshal(m, b)
}
func (m *ExamineOrderNoticeListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ExamineOrderNoticeListReq.Marshal(b, m, deterministic)
}
func (m *ExamineOrderNoticeListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExamineOrderNoticeListReq.Merge(m, src)
}
func (m *ExamineOrderNoticeListReq) XXX_Size() int {
	return xxx_messageInfo_ExamineOrderNoticeListReq.Size(m)
}
func (m *ExamineOrderNoticeListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ExamineOrderNoticeListReq.DiscardUnknown(m)
}

var xxx_messageInfo_ExamineOrderNoticeListReq proto.InternalMessageInfo

func (m *ExamineOrderNoticeListReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *ExamineOrderNoticeListReq) GetPageSize() uint32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

// 打分表配置 - 列表 - resp
type ExamineOrderNoticeListResp struct {
	CurrentPage          uint32                                            `protobuf:"varint,1,opt,name=current_page,json=currentPage,proto3" json:"current_page"`
	PerPage              uint32                                            `protobuf:"varint,2,opt,name=per_page,json=perPage,proto3" json:"per_page"`
	Total                int64                                             `protobuf:"varint,3,opt,name=total,proto3" json:"total"`
	Data                 []*ExamineOrderNoticeListResp_ExamineNoticeDetail `protobuf:"bytes,4,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}                                          `json:"-" gorm:"-"`
	XXX_unrecognized     []byte                                            `json:"-" gorm:"-"`
	XXX_sizecache        int32                                             `json:"-" gorm:"-"`
}

func (m *ExamineOrderNoticeListResp) Reset()         { *m = ExamineOrderNoticeListResp{} }
func (m *ExamineOrderNoticeListResp) String() string { return proto.CompactTextString(m) }
func (*ExamineOrderNoticeListResp) ProtoMessage()    {}
func (*ExamineOrderNoticeListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_9b15753b7a0c29a9, []int{28}
}

func (m *ExamineOrderNoticeListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ExamineOrderNoticeListResp.Unmarshal(m, b)
}
func (m *ExamineOrderNoticeListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ExamineOrderNoticeListResp.Marshal(b, m, deterministic)
}
func (m *ExamineOrderNoticeListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExamineOrderNoticeListResp.Merge(m, src)
}
func (m *ExamineOrderNoticeListResp) XXX_Size() int {
	return xxx_messageInfo_ExamineOrderNoticeListResp.Size(m)
}
func (m *ExamineOrderNoticeListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ExamineOrderNoticeListResp.DiscardUnknown(m)
}

var xxx_messageInfo_ExamineOrderNoticeListResp proto.InternalMessageInfo

func (m *ExamineOrderNoticeListResp) GetCurrentPage() uint32 {
	if m != nil {
		return m.CurrentPage
	}
	return 0
}

func (m *ExamineOrderNoticeListResp) GetPerPage() uint32 {
	if m != nil {
		return m.PerPage
	}
	return 0
}

func (m *ExamineOrderNoticeListResp) GetTotal() int64 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *ExamineOrderNoticeListResp) GetData() []*ExamineOrderNoticeListResp_ExamineNoticeDetail {
	if m != nil {
		return m.Data
	}
	return nil
}

type ExamineOrderNoticeListResp_ExamineNoticeDetail struct {
	// id
	Id uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	// 消息类型
	MsgGroup string `protobuf:"bytes,2,opt,name=msg_group,json=msgGroup,proto3" json:"msg_group"`
	// 抽检库分组
	TaskGroup ExamineTaskGroupDf `protobuf:"varint,3,opt,name=task_group,json=taskGroup,proto3,enum=pb.ExamineTaskGroupDf" json:"task_group"`
	// 抽检单id
	DetailId uint64 `protobuf:"varint,4,opt,name=detail_id,json=detailId,proto3" json:"detail_id"`
	// 通知人
	ToUser string `protobuf:"bytes,5,opt,name=to_user,json=toUser,proto3" json:"to_user"`
	// 创建时间
	CreatedAt string `protobuf:"bytes,6,opt,name=created_at,json=createdAt,proto3" json:"created_at"`
	// 当前状态-是否已读: true/false
	HasRead              bool     `protobuf:"varint,7,opt,name=has_read,json=hasRead,proto3" json:"has_read"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *ExamineOrderNoticeListResp_ExamineNoticeDetail) Reset() {
	*m = ExamineOrderNoticeListResp_ExamineNoticeDetail{}
}
func (m *ExamineOrderNoticeListResp_ExamineNoticeDetail) String() string {
	return proto.CompactTextString(m)
}
func (*ExamineOrderNoticeListResp_ExamineNoticeDetail) ProtoMessage() {}
func (*ExamineOrderNoticeListResp_ExamineNoticeDetail) Descriptor() ([]byte, []int) {
	return fileDescriptor_9b15753b7a0c29a9, []int{28, 0}
}

func (m *ExamineOrderNoticeListResp_ExamineNoticeDetail) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ExamineOrderNoticeListResp_ExamineNoticeDetail.Unmarshal(m, b)
}
func (m *ExamineOrderNoticeListResp_ExamineNoticeDetail) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ExamineOrderNoticeListResp_ExamineNoticeDetail.Marshal(b, m, deterministic)
}
func (m *ExamineOrderNoticeListResp_ExamineNoticeDetail) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ExamineOrderNoticeListResp_ExamineNoticeDetail.Merge(m, src)
}
func (m *ExamineOrderNoticeListResp_ExamineNoticeDetail) XXX_Size() int {
	return xxx_messageInfo_ExamineOrderNoticeListResp_ExamineNoticeDetail.Size(m)
}
func (m *ExamineOrderNoticeListResp_ExamineNoticeDetail) XXX_DiscardUnknown() {
	xxx_messageInfo_ExamineOrderNoticeListResp_ExamineNoticeDetail.DiscardUnknown(m)
}

var xxx_messageInfo_ExamineOrderNoticeListResp_ExamineNoticeDetail proto.InternalMessageInfo

func (m *ExamineOrderNoticeListResp_ExamineNoticeDetail) GetId() uint64 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *ExamineOrderNoticeListResp_ExamineNoticeDetail) GetMsgGroup() string {
	if m != nil {
		return m.MsgGroup
	}
	return ""
}

func (m *ExamineOrderNoticeListResp_ExamineNoticeDetail) GetTaskGroup() ExamineTaskGroupDf {
	if m != nil {
		return m.TaskGroup
	}
	return ExamineTaskGroupDf_ExamineTaskGroupDfUnknown
}

func (m *ExamineOrderNoticeListResp_ExamineNoticeDetail) GetDetailId() uint64 {
	if m != nil {
		return m.DetailId
	}
	return 0
}

func (m *ExamineOrderNoticeListResp_ExamineNoticeDetail) GetToUser() string {
	if m != nil {
		return m.ToUser
	}
	return ""
}

func (m *ExamineOrderNoticeListResp_ExamineNoticeDetail) GetCreatedAt() string {
	if m != nil {
		return m.CreatedAt
	}
	return ""
}

func (m *ExamineOrderNoticeListResp_ExamineNoticeDetail) GetHasRead() bool {
	if m != nil {
		return m.HasRead
	}
	return false
}

func init() {
	proto.RegisterType((*ExamineTplListReq)(nil), "pb.ExamineTplListReq")
	proto.RegisterType((*ExamineTplListResp)(nil), "pb.ExamineTplListResp")
	proto.RegisterType((*ExamineTplListResp_ExamineTpl)(nil), "pb.ExamineTplListResp.ExamineTpl")
	proto.RegisterType((*ExamineTplDetailReq)(nil), "pb.ExamineTplDetailReq")
	proto.RegisterType((*ExamineFieldDt)(nil), "pb.ExamineFieldDt")
	proto.RegisterType((*ExamineModuleDt)(nil), "pb.ExamineModuleDt")
	proto.RegisterType((*ExamineTplDetailResp)(nil), "pb.ExamineTplDetailResp")
	proto.RegisterType((*ExamineTplOptsResp)(nil), "pb.ExamineTplOptsResp")
	proto.RegisterType((*ExamineTplOptsResp_ExamineTplOpts)(nil), "pb.ExamineTplOptsResp.ExamineTplOpts")
	proto.RegisterType((*ExamineTplSaveReq)(nil), "pb.ExamineTplSaveReq")
	proto.RegisterType((*ExamineTplCopyReq)(nil), "pb.ExamineTplCopyReq")
	proto.RegisterType((*ExamineTplSaveResp)(nil), "pb.ExamineTplSaveResp")
	proto.RegisterType((*ExamineTplDelReq)(nil), "pb.ExamineTplDelReq")
	proto.RegisterType((*ExamineTaskListReq)(nil), "pb.ExamineTaskListReq")
	proto.RegisterType((*ExamineTaskListResp)(nil), "pb.ExamineTaskListResp")
	proto.RegisterType((*ExamineTaskListResp_ExamineTask)(nil), "pb.ExamineTaskListResp.ExamineTask")
	proto.RegisterType((*ExamineTaskDetailReq)(nil), "pb.ExamineTaskDetailReq")
	proto.RegisterType((*ExamineTaskDetailResp)(nil), "pb.ExamineTaskDetailResp")
	proto.RegisterType((*ExamineTaskSaveReq)(nil), "pb.ExamineTaskSaveReq")
	proto.RegisterType((*ExamineTaskDscFilterCountReq)(nil), "pb.ExamineTaskDscFilterCountReq")
	proto.RegisterType((*ExamineTaskDscPartFilterCountReq)(nil), "pb.ExamineTaskDscPartFilterCountReq")
	proto.RegisterType((*ExamineTaskDscFilterCountResp)(nil), "pb.ExamineTaskDscFilterCountResp")
	proto.RegisterType((*ExamineDscOrderListReq)(nil), "pb.ExamineDscOrderListReq")
	proto.RegisterType((*ExamineDscOrderListResp)(nil), "pb.ExamineDscOrderListResp")
	proto.RegisterType((*ExamineDscOrderListResp_ExamineDscOrder)(nil), "pb.ExamineDscOrderListResp.ExamineDscOrder")
	proto.RegisterType((*ExamineDscOrderDetailReq)(nil), "pb.ExamineDscOrderDetailReq")
	proto.RegisterType((*ExamineDscOrderDetailResp)(nil), "pb.ExamineDscOrderDetailResp")
	proto.RegisterType((*ExamineDscOrderDetailResp_Common)(nil), "pb.ExamineDscOrderDetailResp.Common")
	proto.RegisterType((*ExamineDscOrderSaveReq)(nil), "pb.ExamineDscOrderSaveReq")
	proto.RegisterType((*ExamineDscOrderSaveReq_Common)(nil), "pb.ExamineDscOrderSaveReq.Common")
	proto.RegisterType((*ExamineDscOrderStatsReq)(nil), "pb.ExamineDscOrderStatsReq")
	proto.RegisterType((*ExamineDscOrderStatsResp)(nil), "pb.ExamineDscOrderStatsResp")
	proto.RegisterType((*ExamineOrderNoticeAcceptorResp)(nil), "pb.ExamineOrderNoticeAcceptorResp")
	proto.RegisterType((*ExamineOrderNoticeListReq)(nil), "pb.ExamineOrderNoticeListReq")
	proto.RegisterType((*ExamineOrderNoticeListResp)(nil), "pb.ExamineOrderNoticeListResp")
	proto.RegisterType((*ExamineOrderNoticeListResp_ExamineNoticeDetail)(nil), "pb.ExamineOrderNoticeListResp.ExamineNoticeDetail")
}

func init() {
	proto.RegisterFile("examine.proto", fileDescriptor_9b15753b7a0c29a9)
}

var fileDescriptor_9b15753b7a0c29a9 = []byte{
	// 2615 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xec, 0x39, 0xcb, 0x6f, 0x1b, 0xc7,
	0xf9, 0x58, 0x92, 0xa2, 0xc8, 0xd1, 0xc3, 0xf1, 0x58, 0x8f, 0x15, 0xf5, 0x88, 0xb4, 0x8e, 0x63,
	0xc5, 0xf9, 0xfd, 0x24, 0x44, 0x86, 0x91, 0x26, 0x69, 0x90, 0x28, 0xa2, 0x6d, 0xb8, 0xb0, 0x1d,
	0x77, 0x25, 0x37, 0x40, 0x51, 0x60, 0x31, 0xe2, 0x0e, 0xa5, 0xad, 0x96, 0xbb, 0xa3, 0x9d, 0xa1,
	0x6d, 0xfa, 0xd0, 0x43, 0x0a, 0xf4, 0xd0, 0x4b, 0x11, 0x14, 0xe8, 0x5f, 0xd0, 0x63, 0xcf, 0x2d,
	0x7a, 0xea, 0x7f, 0xd0, 0x4b, 0x7b, 0xee, 0xa9, 0x97, 0x02, 0x3d, 0xf4, 0xd6, 0x6b, 0x31, 0xdf,
	0xcc, 0x2e, 0x67, 0x77, 0x49, 0x4a, 0x2a, 0xe2, 0x02, 0x05, 0x7a, 0x22, 0xf7, 0x9b, 0xf9, 0xde,
	0xcf, 0x99, 0x41, 0x73, 0xf4, 0x15, 0xe9, 0x05, 0x11, 0xdd, 0x61, 0x49, 0x2c, 0x62, 0x5c, 0x61,
	0xc7, 0xad, 0xb5, 0x93, 0x38, 0x3e, 0x09, 0xe9, 0x2e, 0x61, 0xc1, 0x2e, 0x89, 0xa2, 0x58, 0x10,
	0x11, 0xc4, 0x11, 0x57, 0x3b, 0x5a, 0x58, 0x23, 0x78, 0x34, 0xea, 0xf7, 0x34, 0x6c, 0xb6, 0x13,
	0xf7, 0x7a, 0x71, 0xa4, 0xbf, 0x56, 0x34, 0x3e, 0x7c, 0x1d, 0xf7, 0xbb, 0xbb, 0x24, 0x1a, 0xa8,
	0x25, 0xc7, 0x43, 0xd7, 0xef, 0x2b, 0xf4, 0x23, 0x16, 0x3e, 0x0e, 0xb8, 0x70, 0xe9, 0x39, 0x5e,
	0x41, 0x0d, 0xc1, 0x42, 0xcf, 0xa7, 0xbc, 0x63, 0x5b, 0x9b, 0xd6, 0x76, 0xd3, 0x9d, 0x16, 0x2c,
	0x6c, 0x53, 0xde, 0xc1, 0x18, 0xd5, 0x18, 0x39, 0xa1, 0x76, 0x65, 0xd3, 0xda, 0x9e, 0x73, 0xe1,
	0x3f, 0x5e, 0x45, 0x4d, 0xf9, 0xeb, 0xf1, 0xe0, 0x35, 0xb5, 0xab, 0xb0, 0xd0, 0x90, 0x80, 0xc3,
	0xe0, 0x35, 0x75, 0x7e, 0x53, 0x41, 0xb8, 0xc8, 0x81, 0x33, 0xbc, 0x85, 0x66, 0x3b, 0xfd, 0x24,
	0xa1, 0x91, 0xf0, 0x80, 0x9e, 0x05, 0x68, 0x33, 0x1a, 0xf6, 0x4c, 0x92, 0x5d, 0x41, 0x0d, 0x46,
	0x13, 0xcf, 0x60, 0x37, 0xcd, 0x68, 0x02, 0x4b, 0x0b, 0x68, 0x4a, 0xc4, 0x82, 0x84, 0xc0, 0xad,
	0xea, 0xaa, 0x0f, 0x7c, 0x0f, 0xd5, 0x7c, 0x22, 0x88, 0x5d, 0xdb, 0xac, 0x6e, 0xcf, 0xec, 0x6d,
	0xed, 0xb0, 0xe3, 0x9d, 0x32, 0x67, 0x03, 0xe4, 0xc2, 0xf6, 0xd6, 0xcf, 0x2d, 0x84, 0x86, 0x40,
	0x3c, 0x8f, 0x2a, 0x81, 0x0f, 0xf2, 0xd4, 0xdc, 0x4a, 0xe0, 0xe7, 0x8c, 0x51, 0xc9, 0x1b, 0xa3,
	0x85, 0x1a, 0x31, 0xa3, 0x09, 0x11, 0x71, 0x02, 0x92, 0x34, 0xdd, 0xec, 0x1b, 0xaf, 0x23, 0xd4,
	0x67, 0x3e, 0x11, 0xd4, 0xf7, 0x88, 0xb0, 0x6b, 0xb0, 0xda, 0xd4, 0x90, 0x7d, 0x81, 0x97, 0x50,
	0x9d, 0x46, 0xe4, 0x38, 0xa4, 0xf6, 0xd4, 0xa6, 0xb5, 0xdd, 0x70, 0xf5, 0x97, 0xf3, 0x7f, 0xe8,
	0xc6, 0x50, 0x96, 0x36, 0x15, 0x24, 0x08, 0xa5, 0x47, 0x16, 0x51, 0x5d, 0x0a, 0x91, 0x09, 0x36,
	0x25, 0x58, 0xf8, 0xc8, 0x77, 0x7e, 0x6a, 0xa1, 0x79, 0xbd, 0xfd, 0x41, 0x40, 0x43, 0xbf, 0x2d,
	0x24, 0xdf, 0xae, 0xfc, 0xeb, 0x45, 0xa4, 0x47, 0xb5, 0xf7, 0x9a, 0x00, 0x79, 0x4a, 0x7a, 0x14,
	0xdf, 0x4d, 0x97, 0xc5, 0x80, 0x29, 0x67, 0xcd, 0xef, 0x2d, 0x18, 0x96, 0x02, 0x32, 0x47, 0xac,
	0xdd, 0xd5, 0x48, 0x47, 0x03, 0x46, 0x87, 0x34, 0x63, 0x26, 0x38, 0x98, 0x37, 0xa5, 0xf9, 0x25,
	0x13, 0xdc, 0xf9, 0x11, 0xba, 0xa6, 0xb1, 0x9f, 0xc4, 0x7e, 0x3f, 0xa4, 0x6d, 0x21, 0xc3, 0xc4,
	0xe0, 0x0f, 0xff, 0xf1, 0x3d, 0x34, 0xab, 0xa8, 0xf8, 0xa0, 0x96, 0x5d, 0x01, 0x37, 0xe1, 0x22,
	0xf3, 0xb6, 0x70, 0x67, 0x60, 0x9f, 0xd2, 0xde, 0x39, 0x43, 0x0b, 0x65, 0x8b, 0x70, 0x36, 0x29,
	0x48, 0xbf, 0x83, 0xe6, 0x7a, 0x20, 0x49, 0x9e, 0xd5, 0x0d, 0x83, 0x55, 0x2a, 0xa9, 0x3b, 0xab,
	0x76, 0x6a, 0x66, 0xdf, 0x58, 0x66, 0xb4, 0x4a, 0xed, 0x80, 0xd7, 0x47, 0xa8, 0x16, 0x06, 0x5c,
	0xd8, 0x16, 0xd0, 0xb9, 0x95, 0x8f, 0xac, 0x74, 0x57, 0x11, 0x04, 0x28, 0xad, 0xcf, 0x33, 0x0f,
	0x69, 0xf8, 0x18, 0x5f, 0xe2, 0x65, 0x34, 0x0d, 0xfa, 0x64, 0x61, 0x26, 0x77, 0xb5, 0x79, 0xc7,
	0x79, 0x65, 0xa6, 0xe8, 0x21, 0x79, 0x41, 0x65, 0x40, 0x5c, 0x21, 0x4a, 0x4b, 0xd6, 0xa8, 0x5e,
	0xd6, 0x1a, 0x4f, 0x4d, 0xce, 0x07, 0x31, 0x1b, 0x48, 0xce, 0x1b, 0x68, 0xa6, 0x9b, 0xc4, 0x3d,
	0x2f, 0xa7, 0x43, 0x53, 0x82, 0x8e, 0x40, 0x8f, 0xf1, 0x92, 0x38, 0xef, 0x9b, 0xc6, 0x55, 0x9a,
	0x70, 0x36, 0x2e, 0xb6, 0xdf, 0x43, 0x6f, 0x99, 0x7e, 0x9f, 0x94, 0x06, 0xc7, 0x43, 0xba, 0x84,
	0x9f, 0xa5, 0x55, 0x6c, 0x15, 0x35, 0x05, 0xe1, 0x67, 0x66, 0x22, 0x34, 0x24, 0x00, 0xf2, 0xe0,
	0xca, 0x75, 0xec, 0x2f, 0x95, 0x61, 0x66, 0x66, 0x4c, 0xde, 0x50, 0x21, 0xfb, 0x30, 0x57, 0xc8,
	0x6e, 0x9a, 0xe1, 0x66, 0xb0, 0x36, 0x61, 0xba, 0x94, 0xfd, 0xde, 0x42, 0x33, 0x06, 0xb4, 0x14,
	0x25, 0x39, 0x93, 0x54, 0x0a, 0x26, 0xd9, 0x41, 0x75, 0x2e, 0x88, 0xe8, 0x73, 0x5d, 0x16, 0x96,
	0x0a, 0x7c, 0x0f, 0x05, 0x11, 0xb4, 0xdd, 0x75, 0xf5, 0xae, 0x5c, 0xf5, 0xab, 0x4d, 0xac, 0x7e,
	0x53, 0xc5, 0xea, 0x87, 0x65, 0x3e, 0x45, 0x67, 0x76, 0x5d, 0x95, 0x07, 0xf9, 0xdf, 0x79, 0x77,
	0x98, 0xe7, 0x84, 0x9f, 0x0d, 0x4b, 0x5f, 0x41, 0x07, 0xe7, 0xb7, 0x35, 0xb4, 0x38, 0x62, 0x23,
	0x67, 0x57, 0xd3, 0xd6, 0x46, 0xd3, 0x2c, 0x89, 0x7f, 0x4c, 0x3b, 0x42, 0x97, 0xee, 0xf4, 0xd3,
	0x08, 0xb2, 0x9a, 0x99, 0x9f, 0xf7, 0x10, 0x02, 0x6a, 0x27, 0x49, 0xdc, 0x67, 0xa0, 0x52, 0xd9,
	0x44, 0x0f, 0xe5, 0x9a, 0xac, 0x9d, 0x22, 0xfd, 0xc0, 0x1f, 0xa0, 0xc5, 0x6e, 0x10, 0x0a, 0x9a,
	0xc8, 0xcc, 0xf6, 0x12, 0xca, 0xc2, 0x40, 0x19, 0x65, 0x01, 0xca, 0x28, 0x56, 0x8b, 0x6d, 0xde,
	0x71, 0xd5, 0xd2, 0xbe, 0xc0, 0x9f, 0xa2, 0x35, 0x03, 0x85, 0x84, 0xa1, 0x07, 0x61, 0xe1, 0x31,
	0x32, 0xf0, 0x4e, 0x04, 0xb5, 0x17, 0x21, 0x56, 0x96, 0x33, 0xcc, 0xfd, 0x30, 0x3c, 0x92, 0x1b,
	0x9e, 0x91, 0xc1, 0x43, 0x41, 0xf1, 0x2e, 0x5a, 0x30, 0xd0, 0x63, 0x71, 0x4a, 0x13, 0x49, 0xc4,
	0x5e, 0x82, 0x46, 0x73, 0x3d, 0x43, 0xfb, 0x52, 0xae, 0xec, 0x87, 0x21, 0xfe, 0x18, 0xb5, 0x4a,
	0x08, 0x2f, 0x02, 0xe6, 0x49, 0x3f, 0x53, 0xdb, 0xde, 0xac, 0x6e, 0xcf, 0xb9, 0x4b, 0x79, 0xb4,
	0x1f, 0x04, 0x0c, 0xc2, 0x01, 0x7f, 0x8a, 0x56, 0x4b, 0xb8, 0x3d, 0x12, 0x44, 0x82, 0x04, 0x11,
	0x4d, 0xec, 0x15, 0x50, 0xd2, 0xce, 0x23, 0x3f, 0xc9, 0xd6, 0x47, 0xca, 0x1a, 0xf5, 0x7b, 0x76,
	0x6b, 0xd3, 0xda, 0x9e, 0x2a, 0xca, 0xfa, 0xb4, 0xdf, 0xc3, 0x1f, 0xa1, 0x15, 0xd3, 0x36, 0x9c,
	0x07, 0x27, 0x91, 0x47, 0x3a, 0x9d, 0xb8, 0x1f, 0x09, 0x7b, 0x15, 0xb8, 0x0d, 0x45, 0xdd, 0x87,
	0xe5, 0x7d, 0xb5, 0xea, 0xfc, 0xbd, 0x96, 0x2b, 0x13, 0x69, 0x25, 0x9d, 0x58, 0x26, 0x8c, 0x28,
	0xa9, 0x8c, 0x8b, 0x92, 0xea, 0xf8, 0x28, 0xa9, 0x5d, 0x36, 0x4a, 0xee, 0x6a, 0x21, 0x92, 0xbe,
	0x9e, 0x08, 0xca, 0x58, 0x6e, 0x3f, 0xa4, 0xbc, 0xdd, 0x55, 0xc2, 0xc9, 0x8f, 0xff, 0x86, 0xd0,
	0xba, 0x20, 0x3c, 0xec, 0x0b, 0xc2, 0x63, 0x72, 0x64, 0xae, 0x4c, 0x8c, 0xcc, 0x49, 0xa1, 0x35,
	0xf7, 0xed, 0x86, 0x56, 0x56, 0xcf, 0xd6, 0x8c, 0x7a, 0x26, 0xd0, 0x9a, 0x59, 0xa6, 0x78, 0xe7,
	0x01, 0xe0, 0x1e, 0x48, 0x04, 0x19, 0x77, 0x46, 0x68, 0x59, 0xf9, 0xd0, 0x5a, 0x47, 0xc8, 0x70,
	0x66, 0x45, 0x8d, 0x5b, 0x49, 0xe6, 0x43, 0x19, 0xb0, 0xa9, 0xd3, 0x74, 0xdf, 0x68, 0x08, 0xed,
	0x24, 0xe7, 0x8f, 0x16, 0xda, 0xcc, 0xb3, 0x7d, 0x46, 0x12, 0xf1, 0x9f, 0x60, 0x8d, 0x37, 0x10,
	0x32, 0x5c, 0xab, 0xa6, 0x44, 0x03, 0x22, 0x91, 0x87, 0xbe, 0x9b, 0x02, 0xdf, 0x35, 0x5e, 0xa4,
	0xde, 0x5a, 0x46, 0xd3, 0x01, 0xf7, 0x18, 0x49, 0x04, 0x34, 0x85, 0x86, 0x5b, 0x0f, 0xb8, 0x94,
	0xdc, 0xf9, 0x00, 0xad, 0x4f, 0x30, 0x23, 0x67, 0xf8, 0x2d, 0x54, 0x95, 0x6e, 0xb5, 0x40, 0x1a,
	0xf9, 0xd7, 0xf9, 0x55, 0x15, 0x2d, 0x69, 0x1c, 0xe9, 0xdf, 0xc4, 0xa7, 0x49, 0x3a, 0x13, 0xe4,
	0x34, 0xaf, 0x9a, 0x9a, 0xbf, 0x83, 0xe6, 0xa5, 0xdb, 0xd3, 0xb3, 0x54, 0xe0, 0x43, 0xc2, 0xd7,
	0xdc, 0x59, 0x9f, 0x77, 0x34, 0xb1, 0x47, 0x3e, 0xbe, 0x63, 0xf4, 0xc8, 0xea, 0xf6, 0x7c, 0x6e,
	0x7a, 0x2d, 0xf6, 0xc7, 0x4f, 0xe4, 0xbc, 0x1b, 0x91, 0xd0, 0x4b, 0x28, 0xef, 0x87, 0x02, 0x2c,
	0x32, 0xbf, 0x67, 0xe7, 0xe6, 0xdd, 0x88, 0xc8, 0xd6, 0xd5, 0x0f, 0x45, 0xbb, 0x2b, 0xa7, 0xde,
	0xec, 0x13, 0xdf, 0x46, 0xd7, 0x12, 0x1a, 0xaa, 0x06, 0xaa, 0x43, 0x70, 0x0a, 0x04, 0x9e, 0xd7,
	0xe0, 0x34, 0xf4, 0xd6, 0x50, 0x33, 0x88, 0x38, 0xa3, 0x1d, 0xd9, 0x86, 0xeb, 0xca, 0x61, 0x19,
	0x40, 0xfa, 0xb3, 0x93, 0xd0, 0xb4, 0x0f, 0x4f, 0xab, 0x65, 0x0d, 0xd9, 0x17, 0xf8, 0x6d, 0x24,
	0x99, 0x06, 0xfc, 0x54, 0xad, 0x37, 0x94, 0xcf, 0x52, 0x90, 0x72, 0xf8, 0xd0, 0x67, 0xb3, 0x05,
	0x9f, 0xa5, 0x33, 0x54, 0x73, 0xdc, 0x0c, 0x85, 0x0a, 0x33, 0xd4, 0x3f, 0xea, 0x68, 0x79, 0xa4,
	0x63, 0xde, 0xd0, 0x1c, 0xf5, 0x59, 0x6e, 0x8e, 0x7a, 0xdf, 0xb0, 0x7c, 0x91, 0x7d, 0x11, 0xae,
	0xe7, 0xa9, 0x3f, 0x4c, 0x65, 0x47, 0x9b, 0x74, 0x65, 0x44, 0xa0, 0x58, 0x23, 0x02, 0x45, 0x4e,
	0xf3, 0xb2, 0xa0, 0x07, 0x3e, 0xc4, 0x73, 0xcd, 0xad, 0xcb, 0xcf, 0x47, 0xbe, 0xd1, 0x37, 0xa6,
	0xcd, 0xbe, 0x31, 0xbe, 0xd1, 0x6c, 0xa0, 0x19, 0xc9, 0xaf, 0xcf, 0x69, 0x92, 0x76, 0x9b, 0xa6,
	0xdb, 0xf4, 0x79, 0xe7, 0x39, 0xa7, 0xc9, 0x23, 0x98, 0x72, 0x60, 0x0d, 0xfa, 0x97, 0x9e, 0xc3,
	0x24, 0x00, 0xfa, 0xd7, 0x3a, 0x42, 0x7e, 0xcf, 0xeb, 0x9c, 0x92, 0x28, 0xa2, 0x61, 0x3a, 0x87,
	0xf9, 0xbd, 0x03, 0x05, 0xc8, 0xe7, 0x73, 0x63, 0xd3, 0xda, 0xb6, 0x8c, 0x7c, 0xde, 0x45, 0x0b,
	0xb2, 0x2d, 0x84, 0x84, 0x0b, 0x4f, 0x9c, 0x06, 0x89, 0x18, 0x78, 0x3e, 0x19, 0x70, 0x70, 0xb7,
	0xe5, 0x5e, 0x67, 0x64, 0xf0, 0x98, 0x70, 0x71, 0x04, 0x2b, 0x6d, 0x32, 0xe0, 0x92, 0x19, 0x6c,
	0x0e, 0xe3, 0x93, 0x20, 0x02, 0xe7, 0x37, 0xdd, 0xa6, 0x84, 0x3c, 0x96, 0x00, 0x7c, 0x0b, 0xcd,
	0xa7, 0xb5, 0x45, 0xe7, 0xd0, 0x0c, 0x38, 0x71, 0x4e, 0x43, 0x0f, 0x55, 0xda, 0x14, 0x42, 0xce,
	0x2a, 0x86, 0x5c, 0x97, 0x05, 0xbe, 0x3d, 0xa7, 0x0a, 0xad, 0xfc, 0x2f, 0x0b, 0x40, 0x3f, 0xf0,
	0xed, 0x79, 0x30, 0xa7, 0xfc, 0x2b, 0x21, 0x3c, 0xf0, 0xed, 0x6b, 0xb0, 0x49, 0xfe, 0x95, 0x56,
	0x3f, 0x8e, 0x85, 0xb4, 0xdf, 0x5b, 0x00, 0x9c, 0x3a, 0x8e, 0x85, 0x3a, 0xab, 0x48, 0x30, 0x3f,
	0x8d, 0x5f, 0xda, 0x37, 0x94, 0xd9, 0x8f, 0x63, 0x71, 0x78, 0x1a, 0xbf, 0x34, 0x32, 0x7d, 0x01,
	0xda, 0xf1, 0x55, 0x32, 0x7d, 0x11, 0x30, 0x2e, 0x99, 0xe9, 0xf9, 0x14, 0x5d, 0x52, 0x56, 0x1b,
	0x9b, 0xa2, 0xcb, 0xb0, 0x6e, 0xa6, 0x68, 0xae, 0x00, 0xd8, 0x0a, 0x3d, 0x03, 0x38, 0x9f, 0x23,
	0xbb, 0x10, 0xc0, 0xc3, 0xc9, 0xfa, 0x52, 0x91, 0xec, 0xfc, 0xb9, 0x8e, 0x56, 0xc6, 0x90, 0xe0,
	0xec, 0xea, 0xd9, 0x50, 0x19, 0x93, 0x0d, 0xd5, 0x31, 0xd9, 0x50, 0x9b, 0x98, 0x0d, 0x53, 0xc5,
	0x6c, 0x58, 0x43, 0x48, 0xae, 0x6b, 0x67, 0xab, 0xf3, 0x45, 0xc3, 0xe7, 0x9d, 0x2f, 0xc0, 0xdf,
	0x5a, 0x5a, 0x9d, 0x0f, 0x69, 0x12, 0x36, 0x41, 0x5a, 0x9d, 0x13, 0x40, 0xc3, 0xb0, 0x68, 0xa3,
	0x60, 0x51, 0x23, 0x30, 0x9a, 0x17, 0x06, 0x46, 0xde, 0xb7, 0xe8, 0x02, 0xdf, 0xce, 0x94, 0x7c,
	0x2b, 0xf1, 0x49, 0xe4, 0x51, 0x3f, 0x10, 0xd4, 0x87, 0x64, 0x68, 0xb8, 0xcd, 0x0e, 0x89, 0xee,
	0x03, 0x00, 0x7f, 0x88, 0x66, 0x7d, 0xda, 0x95, 0x76, 0x87, 0x0b, 0x13, 0x88, 0xd4, 0x99, 0xbd,
	0x85, 0x1d, 0x75, 0xdd, 0xb7, 0x93, 0x5e, 0xf7, 0xed, 0xec, 0x47, 0x03, 0x77, 0x46, 0xed, 0x84,
	0x2b, 0x16, 0xfc, 0x10, 0xe9, 0x0b, 0x42, 0x8d, 0xb8, 0x08, 0x88, 0xef, 0x8c, 0x28, 0x90, 0x43,
	0x57, 0xef, 0x1c, 0x00, 0x86, 0x3b, 0xa3, 0x30, 0x81, 0x50, 0xeb, 0x77, 0x15, 0x54, 0x57, 0xf0,
	0x52, 0x12, 0x58, 0x57, 0x49, 0x02, 0x65, 0x09, 0x12, 0x7a, 0xbc, 0x13, 0x27, 0xaa, 0xba, 0x4f,
	0x81, 0x25, 0x48, 0x78, 0x28, 0x21, 0xb2, 0x3d, 0xa4, 0xd4, 0x09, 0x8f, 0x23, 0x5d, 0x06, 0x53,
	0x1a, 0x12, 0x34, 0xaa, 0x65, 0xd6, 0x46, 0xb6, 0xcc, 0x5b, 0x68, 0x3e, 0x8a, 0x45, 0xd0, 0xa1,
	0x85, 0xd6, 0x3a, 0xa7, 0xa0, 0xe9, 0x36, 0xb8, 0xf5, 0x92, 0x2c, 0xe1, 0x2a, 0xa3, 0x9e, 0xde,
	0xa4, 0x45, 0x44, 0x5d, 0xab, 0xc0, 0x68, 0x3b, 0xd4, 0xd7, 0xeb, 0xc5, 0x7e, 0xd0, 0x1d, 0x78,
	0xd2, 0x3a, 0x34, 0x12, 0x3a, 0xb0, 0x6c, 0x43, 0xc9, 0x27, 0xb0, 0xe1, 0x40, 0xad, 0x3b, 0xff,
	0x2c, 0x0f, 0x29, 0xe9, 0x89, 0xe4, 0x72, 0x39, 0xf5, 0x6f, 0x3b, 0xbf, 0x3d, 0xd2, 0xf9, 0x5b,
	0x23, 0x9c, 0xaf, 0x05, 0xfa, 0x9f, 0xe7, 0xbf, 0x55, 0xcf, 0xdf, 0x2d, 0x0d, 0x41, 0xb2, 0x6c,
	0xf0, 0x89, 0xe3, 0xa9, 0xf3, 0xb3, 0x6a, 0xa9, 0x90, 0x6b, 0x2c, 0xce, 0xf0, 0x1d, 0x74, 0x3d,
	0x0d, 0x16, 0x28, 0x6f, 0xa0, 0x99, 0x1a, 0x88, 0xaf, 0xd1, 0x0c, 0x09, 0x46, 0x66, 0x7c, 0x17,
	0x2d, 0x99, 0x7b, 0x5f, 0x92, 0x40, 0x68, 0x84, 0x0a, 0x20, 0xdc, 0x18, 0x22, 0x7c, 0x45, 0x02,
	0xa1, 0x90, 0xbe, 0x8b, 0x56, 0x4d, 0x24, 0xf8, 0x63, 0x60, 0xaa, 0xa1, 0x6b, 0x79, 0x88, 0xf9,
	0x24, 0x88, 0xe8, 0x64, 0x6c, 0x3f, 0x8e, 0xa8, 0x97, 0xba, 0x6a, 0x04, 0x76, 0x3b, 0x8e, 0xa8,
	0xc2, 0xde, 0x47, 0xeb, 0x26, 0x76, 0x3f, 0x3a, 0xef, 0x93, 0x30, 0xe8, 0xca, 0x31, 0x22, 0x75,
	0xa1, 0xc4, 0x6f, 0x0d, 0xf1, 0x9f, 0x0f, 0xb7, 0x28, 0x12, 0xdf, 0x43, 0x4e, 0x49, 0x80, 0x32,
	0x9d, 0x3a, 0xd0, 0xd9, 0xc8, 0xcb, 0x51, 0xa4, 0xe5, 0x7c, 0x86, 0x36, 0xb4, 0x1f, 0xc0, 0x09,
	0x4f, 0xd3, 0xc0, 0xa1, 0x4c, 0xc4, 0x09, 0x78, 0x63, 0x1d, 0xa1, 0x88, 0xbe, 0xf4, 0x54, 0x48,
	0x81, 0x1b, 0x1a, 0x6e, 0x33, 0xa2, 0x2f, 0xd5, 0x56, 0xe7, 0x71, 0xd6, 0x4e, 0x0d, 0x02, 0xe9,
	0xf9, 0xe4, 0xca, 0xd7, 0x92, 0xbf, 0xae, 0xa2, 0xd6, 0x38, 0x72, 0x6f, 0x68, 0xaa, 0x7e, 0x90,
	0x9b, 0xaa, 0xf7, 0x8c, 0x34, 0x1f, 0x21, 0x41, 0xba, 0xa4, 0xa0, 0xba, 0x9b, 0xa8, 0xe1, 0xfa,
	0x6f, 0x56, 0x76, 0xa3, 0x6a, 0xae, 0x8e, 0xba, 0xc6, 0xeb, 0xf1, 0x13, 0x7d, 0xa3, 0xa2, 0xaf,
	0xf1, 0x7a, 0xfc, 0x44, 0x5d, 0x9c, 0xe4, 0xef, 0x5b, 0xaa, 0x97, 0xbd, 0x6f, 0x59, 0x45, 0x4d,
	0x75, 0x19, 0x3e, 0xbc, 0xe6, 0x6b, 0x28, 0x80, 0x9e, 0x56, 0x62, 0x18, 0x31, 0xf4, 0x7c, 0x51,
	0x17, 0xb1, 0x1c, 0x2f, 0x0a, 0xed, 0xbc, 0x5e, 0x6c, 0xe7, 0x2b, 0xa8, 0x71, 0x4a, 0xb8, 0xac,
	0x53, 0x6a, 0xae, 0x68, 0xb8, 0xd3, 0xa7, 0x84, 0xbb, 0x94, 0xf8, 0x7b, 0xdf, 0x5c, 0xcb, 0xde,
	0x98, 0xf6, 0x59, 0x80, 0x89, 0xf9, 0x28, 0x20, 0x4d, 0x85, 0x17, 0x47, 0xbd, 0x56, 0x9d, 0xb7,
	0x96, 0x46, 0x3f, 0x62, 0x39, 0x9b, 0x5f, 0xff, 0xe9, 0xaf, 0xbf, 0xac, 0xb4, 0x9c, 0x45, 0x78,
	0x13, 0xd4, 0x21, 0xbb, 0x2b, 0x58, 0xb8, 0x1b, 0x06, 0x5c, 0x7c, 0x6c, 0xdd, 0xc1, 0x41, 0xfe,
	0xfa, 0x1c, 0x2c, 0xbb, 0x9c, 0xa7, 0x96, 0x4d, 0x82, 0x2d, 0x7b, 0xf4, 0x02, 0x67, 0x8e, 0x03,
	0x8c, 0xd6, 0x9c, 0xe5, 0x12, 0x23, 0x65, 0x2e, 0xc9, 0xea, 0x79, 0xe9, 0x89, 0xa3, 0x09, 0xf4,
	0x7a, 0x4c, 0x0c, 0x8a, 0x1a, 0xa4, 0x8f, 0x25, 0x13, 0x34, 0x88, 0x99, 0xe0, 0x92, 0xec, 0x91,
	0x49, 0x56, 0xb6, 0xa2, 0xa2, 0x91, 0x74, 0x7b, 0x6a, 0x0d, 0xb9, 0x4d, 0xa0, 0xca, 0xc9, 0x0b,
	0x5a, 0xa2, 0x7a, 0x10, 0xb3, 0x41, 0x91, 0xaa, 0x7e, 0xe7, 0xb8, 0x1c, 0xd5, 0x4e, 0xcc, 0x06,
	0x92, 0xea, 0x33, 0xd3, 0xda, 0xf7, 0xe1, 0x29, 0x0f, 0xcf, 0x01, 0x01, 0xf8, 0x5f, 0xa0, 0x37,
	0xde, 0xa8, 0xea, 0x15, 0x50, 0x52, 0xfc, 0x3e, 0x9a, 0xcb, 0x3d, 0x7f, 0xe0, 0x85, 0xa2, 0x8f,
	0xc2, 0x02, 0xd5, 0xb7, 0x81, 0xea, 0x8a, 0xb3, 0x30, 0xc2, 0x55, 0xe0, 0x27, 0x9a, 0x1d, 0x66,
	0xd3, 0x67, 0x04, 0xbc, 0x34, 0xf2, 0x6d, 0xe1, 0xbc, 0xb5, 0x3c, 0xe6, 0xcd, 0xc1, 0xd9, 0x02,
	0x26, 0xab, 0xce, 0x52, 0x9e, 0x09, 0xe1, 0x67, 0x59, 0xe4, 0xf5, 0x86, 0xaf, 0x46, 0xd9, 0xfd,
	0x3c, 0xb6, 0x0b, 0x04, 0x87, 0xb1, 0xb7, 0x32, 0x66, 0x85, 0x33, 0xe7, 0x26, 0x30, 0x5b, 0x77,
	0xec, 0x32, 0xb3, 0x61, 0xf4, 0x7d, 0x95, 0xd3, 0x0a, 0xe2, 0xa4, 0xf4, 0x72, 0x51, 0x0e, 0x94,
	0x09, 0x7a, 0xa4, 0x91, 0xf2, 0x0b, 0x2b, 0xab, 0xd4, 0xe5, 0xab, 0x27, 0xbc, 0x59, 0x14, 0xbb,
	0x78, 0xc1, 0xd7, 0xda, 0xba, 0x60, 0x07, 0x67, 0xce, 0xff, 0x83, 0x14, 0xb7, 0x1d, 0x67, 0x84,
	0x82, 0xbc, 0xb3, 0xab, 0xaf, 0x23, 0xa1, 0x3d, 0x49, 0x89, 0xfc, 0xac, 0x60, 0x9a, 0x13, 0x1a,
	0x6e, 0x8d, 0x1f, 0xdd, 0x4c, 0x95, 0xdf, 0x05, 0x66, 0x9b, 0xce, 0x6a, 0x8e, 0x59, 0x2c, 0x77,
	0x03, 0xb7, 0x54, 0xef, 0x9f, 0x64, 0xef, 0x2b, 0xf9, 0x43, 0x00, 0x5e, 0x9b, 0x70, 0x3e, 0x38,
	0x6f, 0xad, 0x4f, 0x3c, 0x3d, 0x38, 0xdb, 0xc0, 0xdd, 0x71, 0xd6, 0xc7, 0x70, 0x1f, 0x3a, 0xf4,
	0x45, 0x49, 0x4b, 0x08, 0xd5, 0xd6, 0xd8, 0xeb, 0x9b, 0xf3, 0xd6, 0xea, 0x84, 0xab, 0x9d, 0x0b,
	0xf5, 0x4e, 0xe3, 0xf6, 0xbc, 0x74, 0xce, 0x95, 0x24, 0xee, 0xbf, 0x62, 0x71, 0x32, 0x99, 0xbb,
	0x61, 0xe3, 0xd1, 0x0e, 0xcd, 0xf3, 0xf2, 0x28, 0x90, 0x94, 0x2c, 0x5f, 0x67, 0x6f, 0x5e, 0xb9,
	0xa1, 0x0e, 0x8f, 0xd2, 0x27, 0x1d, 0x12, 0x5b, 0x6b, 0xe3, 0x17, 0x39, 0x73, 0x6e, 0x83, 0x04,
	0x5b, 0xce, 0xda, 0x38, 0x2f, 0xcb, 0x9d, 0x8a, 0x77, 0x6b, 0xfc, 0x20, 0x63, 0x56, 0x70, 0x67,
	0x74, 0x87, 0x37, 0x67, 0x9e, 0x89, 0x7a, 0xab, 0x41, 0x68, 0x97, 0xe8, 0xfd, 0x92, 0xf7, 0xd7,
	0x56, 0x76, 0xf8, 0x29, 0xcc, 0x0c, 0x78, 0x7d, 0xd2, 0x3c, 0x71, 0xde, 0xda, 0x98, 0x3c, 0x6e,
	0x38, 0xef, 0x81, 0x20, 0x37, 0x9d, 0x8d, 0xf1, 0x82, 0x68, 0x7f, 0x7f, 0x51, 0xff, 0x61, 0x6d,
	0xe7, 0x13, 0x76, 0x7c, 0x5c, 0x87, 0xa3, 0xd2, 0xdd, 0x7f, 0x05, 0x00, 0x00, 0xff, 0xff, 0x92,
	0x44, 0x0a, 0x10, 0x79, 0x23, 0x00, 0x00,
}
