// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.14.0
// source: conversation.proto

package pb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	ConversationService_UpdateConversation_FullMethodName     = "/pb.ConversationService/UpdateConversation"
	ConversationService_GetConversationHistory_FullMethodName = "/pb.ConversationService/GetConversationHistory"
	ConversationService_HandleChat_FullMethodName             = "/pb.ConversationService/HandleChat"
	ConversationService_ContinueChat_FullMethodName           = "/pb.ConversationService/ContinueChat"
	ConversationService_CloseChat_FullMethodName              = "/pb.ConversationService/CloseChat"
)

// ConversationServiceClient is the client API for ConversationService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ConversationServiceClient interface {
	// 更新对话
	UpdateConversation(ctx context.Context, in *UpdateConversationRequest, opts ...grpc.CallOption) (*UpdateConversationResponse, error)
	// 获取对话历史
	GetConversationHistory(ctx context.Context, in *GetHistoryRequest, opts ...grpc.CallOption) (*GetHistoryResponse, error)
	// 对话交互
	HandleChat(ctx context.Context, in *ChatRequest, opts ...grpc.CallOption) (*ChatResponse, error)
	// egress/conversation/continue
	ContinueChat(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*ContinueChatResponse, error)
	// egress/conversation/close
	CloseChat(ctx context.Context, in *CloseChatReq, opts ...grpc.CallOption) (*Empty, error)
}

type conversationServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewConversationServiceClient(cc grpc.ClientConnInterface) ConversationServiceClient {
	return &conversationServiceClient{cc}
}

func (c *conversationServiceClient) UpdateConversation(ctx context.Context, in *UpdateConversationRequest, opts ...grpc.CallOption) (*UpdateConversationResponse, error) {
	out := new(UpdateConversationResponse)
	err := c.cc.Invoke(ctx, ConversationService_UpdateConversation_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *conversationServiceClient) GetConversationHistory(ctx context.Context, in *GetHistoryRequest, opts ...grpc.CallOption) (*GetHistoryResponse, error) {
	out := new(GetHistoryResponse)
	err := c.cc.Invoke(ctx, ConversationService_GetConversationHistory_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *conversationServiceClient) HandleChat(ctx context.Context, in *ChatRequest, opts ...grpc.CallOption) (*ChatResponse, error) {
	out := new(ChatResponse)
	err := c.cc.Invoke(ctx, ConversationService_HandleChat_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *conversationServiceClient) ContinueChat(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*ContinueChatResponse, error) {
	out := new(ContinueChatResponse)
	err := c.cc.Invoke(ctx, ConversationService_ContinueChat_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *conversationServiceClient) CloseChat(ctx context.Context, in *CloseChatReq, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, ConversationService_CloseChat_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ConversationServiceServer is the server API for ConversationService service.
// All implementations must embed UnimplementedConversationServiceServer
// for forward compatibility
type ConversationServiceServer interface {
	// 更新对话
	UpdateConversation(context.Context, *UpdateConversationRequest) (*UpdateConversationResponse, error)
	// 获取对话历史
	GetConversationHistory(context.Context, *GetHistoryRequest) (*GetHistoryResponse, error)
	// 对话交互
	HandleChat(context.Context, *ChatRequest) (*ChatResponse, error)
	// egress/conversation/continue
	ContinueChat(context.Context, *Empty) (*ContinueChatResponse, error)
	// egress/conversation/close
	CloseChat(context.Context, *CloseChatReq) (*Empty, error)
	mustEmbedUnimplementedConversationServiceServer()
}

// UnimplementedConversationServiceServer must be embedded to have forward compatible implementations.
type UnimplementedConversationServiceServer struct {
}

func (UnimplementedConversationServiceServer) UpdateConversation(context.Context, *UpdateConversationRequest) (*UpdateConversationResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateConversation not implemented")
}
func (UnimplementedConversationServiceServer) GetConversationHistory(context.Context, *GetHistoryRequest) (*GetHistoryResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetConversationHistory not implemented")
}
func (UnimplementedConversationServiceServer) HandleChat(context.Context, *ChatRequest) (*ChatResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method HandleChat not implemented")
}
func (UnimplementedConversationServiceServer) ContinueChat(context.Context, *Empty) (*ContinueChatResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ContinueChat not implemented")
}
func (UnimplementedConversationServiceServer) CloseChat(context.Context, *CloseChatReq) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CloseChat not implemented")
}
func (UnimplementedConversationServiceServer) mustEmbedUnimplementedConversationServiceServer() {}

// UnsafeConversationServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ConversationServiceServer will
// result in compilation errors.
type UnsafeConversationServiceServer interface {
	mustEmbedUnimplementedConversationServiceServer()
}

func RegisterConversationServiceServer(s grpc.ServiceRegistrar, srv ConversationServiceServer) {
	s.RegisterService(&ConversationService_ServiceDesc, srv)
}

func _ConversationService_UpdateConversation_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateConversationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConversationServiceServer).UpdateConversation(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ConversationService_UpdateConversation_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConversationServiceServer).UpdateConversation(ctx, req.(*UpdateConversationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ConversationService_GetConversationHistory_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetHistoryRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConversationServiceServer).GetConversationHistory(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ConversationService_GetConversationHistory_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConversationServiceServer).GetConversationHistory(ctx, req.(*GetHistoryRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ConversationService_HandleChat_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ChatRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConversationServiceServer).HandleChat(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ConversationService_HandleChat_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConversationServiceServer).HandleChat(ctx, req.(*ChatRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ConversationService_ContinueChat_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConversationServiceServer).ContinueChat(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ConversationService_ContinueChat_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConversationServiceServer).ContinueChat(ctx, req.(*Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _ConversationService_CloseChat_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CloseChatReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConversationServiceServer).CloseChat(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ConversationService_CloseChat_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConversationServiceServer).CloseChat(ctx, req.(*CloseChatReq))
	}
	return interceptor(ctx, in, info, handler)
}

// ConversationService_ServiceDesc is the grpc.ServiceDesc for ConversationService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ConversationService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "pb.ConversationService",
	HandlerType: (*ConversationServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "UpdateConversation",
			Handler:    _ConversationService_UpdateConversation_Handler,
		},
		{
			MethodName: "GetConversationHistory",
			Handler:    _ConversationService_GetConversationHistory_Handler,
		},
		{
			MethodName: "HandleChat",
			Handler:    _ConversationService_HandleChat_Handler,
		},
		{
			MethodName: "ContinueChat",
			Handler:    _ConversationService_ContinueChat_Handler,
		},
		{
			MethodName: "CloseChat",
			Handler:    _ConversationService_CloseChat_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "conversation.proto",
}
