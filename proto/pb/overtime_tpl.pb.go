// Code generated by protoc-gen-go. DO NOT EDIT.
// source: overtime_tpl.proto

package pb

import (
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

// OverTimeTplAddReq 新增模板
type OverTimeTplAddReq struct {
	// @gotags: validate:"required"
	TplName string `protobuf:"bytes,1,opt,name=tpl_name,json=tplName,proto3" json:"tpl_name" validate:"required"`
	// @gotags: validate:"min=1"
	GameProject []string `protobuf:"bytes,2,rep,name=game_project,json=gameProject,proto3" json:"game_project" validate:"min=1"`
	// 预提醒时间
	RemindTime uint32 `protobuf:"varint,3,opt,name=remind_time,json=remindTime,proto3" json:"remind_time"`
	// @gotags: validate:"required"
	Content              map[string]string `protobuf:"bytes,4,rep,name=content,proto3" json:"content" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3" validate:"required"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-" gorm:"-"`
	XXX_unrecognized     []byte            `json:"-" gorm:"-"`
	XXX_sizecache        int32             `json:"-" gorm:"-"`
}

func (m *OverTimeTplAddReq) Reset()         { *m = OverTimeTplAddReq{} }
func (m *OverTimeTplAddReq) String() string { return proto.CompactTextString(m) }
func (*OverTimeTplAddReq) ProtoMessage()    {}
func (*OverTimeTplAddReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_5ceb164813729599, []int{0}
}

func (m *OverTimeTplAddReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OverTimeTplAddReq.Unmarshal(m, b)
}
func (m *OverTimeTplAddReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OverTimeTplAddReq.Marshal(b, m, deterministic)
}
func (m *OverTimeTplAddReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OverTimeTplAddReq.Merge(m, src)
}
func (m *OverTimeTplAddReq) XXX_Size() int {
	return xxx_messageInfo_OverTimeTplAddReq.Size(m)
}
func (m *OverTimeTplAddReq) XXX_DiscardUnknown() {
	xxx_messageInfo_OverTimeTplAddReq.DiscardUnknown(m)
}

var xxx_messageInfo_OverTimeTplAddReq proto.InternalMessageInfo

func (m *OverTimeTplAddReq) GetTplName() string {
	if m != nil {
		return m.TplName
	}
	return ""
}

func (m *OverTimeTplAddReq) GetGameProject() []string {
	if m != nil {
		return m.GameProject
	}
	return nil
}

func (m *OverTimeTplAddReq) GetRemindTime() uint32 {
	if m != nil {
		return m.RemindTime
	}
	return 0
}

func (m *OverTimeTplAddReq) GetContent() map[string]string {
	if m != nil {
		return m.Content
	}
	return nil
}

// OverTimeTplListReq 模板查询请求参数
type OverTimeTplListReq struct {
	// 模板名称,支持模糊搜索
	TplName string `protobuf:"bytes,1,opt,name=tpl_name,json=tplName,proto3" json:"tpl_name"`
	// 游戏, 支持多选
	GameProject          []string `protobuf:"bytes,2,rep,name=game_project,json=gameProject,proto3" json:"game_project"`
	Page                 uint32   `protobuf:"varint,3,opt,name=page,proto3" json:"page"`
	PageSize             uint32   `protobuf:"varint,4,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *OverTimeTplListReq) Reset()         { *m = OverTimeTplListReq{} }
func (m *OverTimeTplListReq) String() string { return proto.CompactTextString(m) }
func (*OverTimeTplListReq) ProtoMessage()    {}
func (*OverTimeTplListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_5ceb164813729599, []int{1}
}

func (m *OverTimeTplListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OverTimeTplListReq.Unmarshal(m, b)
}
func (m *OverTimeTplListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OverTimeTplListReq.Marshal(b, m, deterministic)
}
func (m *OverTimeTplListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OverTimeTplListReq.Merge(m, src)
}
func (m *OverTimeTplListReq) XXX_Size() int {
	return xxx_messageInfo_OverTimeTplListReq.Size(m)
}
func (m *OverTimeTplListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_OverTimeTplListReq.DiscardUnknown(m)
}

var xxx_messageInfo_OverTimeTplListReq proto.InternalMessageInfo

func (m *OverTimeTplListReq) GetTplName() string {
	if m != nil {
		return m.TplName
	}
	return ""
}

func (m *OverTimeTplListReq) GetGameProject() []string {
	if m != nil {
		return m.GameProject
	}
	return nil
}

func (m *OverTimeTplListReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *OverTimeTplListReq) GetPageSize() uint32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

// OverTimeTplListResp 模版列表响应
type OverTimeTplListResp struct {
	CurrentPage          uint32                         `protobuf:"varint,1,opt,name=current_page,json=currentPage,proto3" json:"current_page"`
	PerPage              uint32                         `protobuf:"varint,2,opt,name=per_page,json=perPage,proto3" json:"per_page"`
	Total                uint32                         `protobuf:"varint,3,opt,name=total,proto3" json:"total"`
	Data                 []*OverTimeTplListResp_TplInfo `protobuf:"bytes,4,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}                       `json:"-" gorm:"-"`
	XXX_unrecognized     []byte                         `json:"-" gorm:"-"`
	XXX_sizecache        int32                          `json:"-" gorm:"-"`
}

func (m *OverTimeTplListResp) Reset()         { *m = OverTimeTplListResp{} }
func (m *OverTimeTplListResp) String() string { return proto.CompactTextString(m) }
func (*OverTimeTplListResp) ProtoMessage()    {}
func (*OverTimeTplListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_5ceb164813729599, []int{2}
}

func (m *OverTimeTplListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OverTimeTplListResp.Unmarshal(m, b)
}
func (m *OverTimeTplListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OverTimeTplListResp.Marshal(b, m, deterministic)
}
func (m *OverTimeTplListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OverTimeTplListResp.Merge(m, src)
}
func (m *OverTimeTplListResp) XXX_Size() int {
	return xxx_messageInfo_OverTimeTplListResp.Size(m)
}
func (m *OverTimeTplListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_OverTimeTplListResp.DiscardUnknown(m)
}

var xxx_messageInfo_OverTimeTplListResp proto.InternalMessageInfo

func (m *OverTimeTplListResp) GetCurrentPage() uint32 {
	if m != nil {
		return m.CurrentPage
	}
	return 0
}

func (m *OverTimeTplListResp) GetPerPage() uint32 {
	if m != nil {
		return m.PerPage
	}
	return 0
}

func (m *OverTimeTplListResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *OverTimeTplListResp) GetData() []*OverTimeTplListResp_TplInfo {
	if m != nil {
		return m.Data
	}
	return nil
}

type OverTimeTplListResp_TplInfo struct {
	// 模板id
	Id uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	// 模板名称
	TplName string `protobuf:"bytes,2,opt,name=tpl_name,json=tplName,proto3" json:"tpl_name"`
	// 关联游戏
	GameProject []string `protobuf:"bytes,4,rep,name=game_project,json=gameProject,proto3" json:"game_project"`
	// 操作人
	Operator string `protobuf:"bytes,5,opt,name=operator,proto3" json:"operator"`
	// 状态 1启用 0禁用
	Enable uint32 `protobuf:"varint,6,opt,name=enable,proto3" json:"enable"`
	// 操作时间
	UpdateTime string `protobuf:"bytes,7,opt,name=update_time,json=updateTime,proto3" json:"update_time"`
	// 超时时间
	RemindTime uint32 `protobuf:"varint,8,opt,name=remind_time,json=remindTime,proto3" json:"remind_time"`
	// 模版文案
	TplContent           map[string]string `protobuf:"bytes,9,rep,name=tpl_content,json=tplContent,proto3" json:"tpl_content" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-" gorm:"-"`
	XXX_unrecognized     []byte            `json:"-" gorm:"-"`
	XXX_sizecache        int32             `json:"-" gorm:"-"`
}

func (m *OverTimeTplListResp_TplInfo) Reset()         { *m = OverTimeTplListResp_TplInfo{} }
func (m *OverTimeTplListResp_TplInfo) String() string { return proto.CompactTextString(m) }
func (*OverTimeTplListResp_TplInfo) ProtoMessage()    {}
func (*OverTimeTplListResp_TplInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_5ceb164813729599, []int{2, 0}
}

func (m *OverTimeTplListResp_TplInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OverTimeTplListResp_TplInfo.Unmarshal(m, b)
}
func (m *OverTimeTplListResp_TplInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OverTimeTplListResp_TplInfo.Marshal(b, m, deterministic)
}
func (m *OverTimeTplListResp_TplInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OverTimeTplListResp_TplInfo.Merge(m, src)
}
func (m *OverTimeTplListResp_TplInfo) XXX_Size() int {
	return xxx_messageInfo_OverTimeTplListResp_TplInfo.Size(m)
}
func (m *OverTimeTplListResp_TplInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_OverTimeTplListResp_TplInfo.DiscardUnknown(m)
}

var xxx_messageInfo_OverTimeTplListResp_TplInfo proto.InternalMessageInfo

func (m *OverTimeTplListResp_TplInfo) GetId() uint64 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *OverTimeTplListResp_TplInfo) GetTplName() string {
	if m != nil {
		return m.TplName
	}
	return ""
}

func (m *OverTimeTplListResp_TplInfo) GetGameProject() []string {
	if m != nil {
		return m.GameProject
	}
	return nil
}

func (m *OverTimeTplListResp_TplInfo) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

func (m *OverTimeTplListResp_TplInfo) GetEnable() uint32 {
	if m != nil {
		return m.Enable
	}
	return 0
}

func (m *OverTimeTplListResp_TplInfo) GetUpdateTime() string {
	if m != nil {
		return m.UpdateTime
	}
	return ""
}

func (m *OverTimeTplListResp_TplInfo) GetRemindTime() uint32 {
	if m != nil {
		return m.RemindTime
	}
	return 0
}

func (m *OverTimeTplListResp_TplInfo) GetTplContent() map[string]string {
	if m != nil {
		return m.TplContent
	}
	return nil
}

// OverTimeTplEditReq 编辑模板
type OverTimeTplEditReq struct {
	// 模板id @gotags: validate:"required"
	TplId uint64 `protobuf:"varint,1,opt,name=tpl_id,json=tplId,proto3" json:"tpl_id" validate:"required"`
	// @gotags: validate:"required"
	TplName string `protobuf:"bytes,2,opt,name=tpl_name,json=tplName,proto3" json:"tpl_name" validate:"required"`
	// @gotags: validate:"min=1"
	GameProject []string `protobuf:"bytes,3,rep,name=game_project,json=gameProject,proto3" json:"game_project" validate:"min=1"`
	// 预提醒时间
	RemindTime uint32 `protobuf:"varint,4,opt,name=remind_time,json=remindTime,proto3" json:"remind_time"`
	// @gotags: validate:"required"
	Content              map[string]string `protobuf:"bytes,5,rep,name=content,proto3" json:"content" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3" validate:"required"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-" gorm:"-"`
	XXX_unrecognized     []byte            `json:"-" gorm:"-"`
	XXX_sizecache        int32             `json:"-" gorm:"-"`
}

func (m *OverTimeTplEditReq) Reset()         { *m = OverTimeTplEditReq{} }
func (m *OverTimeTplEditReq) String() string { return proto.CompactTextString(m) }
func (*OverTimeTplEditReq) ProtoMessage()    {}
func (*OverTimeTplEditReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_5ceb164813729599, []int{3}
}

func (m *OverTimeTplEditReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OverTimeTplEditReq.Unmarshal(m, b)
}
func (m *OverTimeTplEditReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OverTimeTplEditReq.Marshal(b, m, deterministic)
}
func (m *OverTimeTplEditReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OverTimeTplEditReq.Merge(m, src)
}
func (m *OverTimeTplEditReq) XXX_Size() int {
	return xxx_messageInfo_OverTimeTplEditReq.Size(m)
}
func (m *OverTimeTplEditReq) XXX_DiscardUnknown() {
	xxx_messageInfo_OverTimeTplEditReq.DiscardUnknown(m)
}

var xxx_messageInfo_OverTimeTplEditReq proto.InternalMessageInfo

func (m *OverTimeTplEditReq) GetTplId() uint64 {
	if m != nil {
		return m.TplId
	}
	return 0
}

func (m *OverTimeTplEditReq) GetTplName() string {
	if m != nil {
		return m.TplName
	}
	return ""
}

func (m *OverTimeTplEditReq) GetGameProject() []string {
	if m != nil {
		return m.GameProject
	}
	return nil
}

func (m *OverTimeTplEditReq) GetRemindTime() uint32 {
	if m != nil {
		return m.RemindTime
	}
	return 0
}

func (m *OverTimeTplEditReq) GetContent() map[string]string {
	if m != nil {
		return m.Content
	}
	return nil
}

// OverTimeTplDelReq 删除模版
type OverTimeTplDelReq struct {
	// 模板id @gotags: validate:"required"
	Id                   uint64   `protobuf:"varint,1,opt,name=id,proto3" json:"id" validate:"required"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *OverTimeTplDelReq) Reset()         { *m = OverTimeTplDelReq{} }
func (m *OverTimeTplDelReq) String() string { return proto.CompactTextString(m) }
func (*OverTimeTplDelReq) ProtoMessage()    {}
func (*OverTimeTplDelReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_5ceb164813729599, []int{4}
}

func (m *OverTimeTplDelReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OverTimeTplDelReq.Unmarshal(m, b)
}
func (m *OverTimeTplDelReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OverTimeTplDelReq.Marshal(b, m, deterministic)
}
func (m *OverTimeTplDelReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OverTimeTplDelReq.Merge(m, src)
}
func (m *OverTimeTplDelReq) XXX_Size() int {
	return xxx_messageInfo_OverTimeTplDelReq.Size(m)
}
func (m *OverTimeTplDelReq) XXX_DiscardUnknown() {
	xxx_messageInfo_OverTimeTplDelReq.DiscardUnknown(m)
}

var xxx_messageInfo_OverTimeTplDelReq proto.InternalMessageInfo

func (m *OverTimeTplDelReq) GetId() uint64 {
	if m != nil {
		return m.Id
	}
	return 0
}

// OverTimeTplOptsReq 模板配置筛选列表-请求
type OverTimeTplOptsReq struct {
	GameProject          string   `protobuf:"bytes,1,opt,name=game_project,json=gameProject,proto3" json:"game_project"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *OverTimeTplOptsReq) Reset()         { *m = OverTimeTplOptsReq{} }
func (m *OverTimeTplOptsReq) String() string { return proto.CompactTextString(m) }
func (*OverTimeTplOptsReq) ProtoMessage()    {}
func (*OverTimeTplOptsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_5ceb164813729599, []int{5}
}

func (m *OverTimeTplOptsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OverTimeTplOptsReq.Unmarshal(m, b)
}
func (m *OverTimeTplOptsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OverTimeTplOptsReq.Marshal(b, m, deterministic)
}
func (m *OverTimeTplOptsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OverTimeTplOptsReq.Merge(m, src)
}
func (m *OverTimeTplOptsReq) XXX_Size() int {
	return xxx_messageInfo_OverTimeTplOptsReq.Size(m)
}
func (m *OverTimeTplOptsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_OverTimeTplOptsReq.DiscardUnknown(m)
}

var xxx_messageInfo_OverTimeTplOptsReq proto.InternalMessageInfo

func (m *OverTimeTplOptsReq) GetGameProject() string {
	if m != nil {
		return m.GameProject
	}
	return ""
}

// OverTimeTplOptsReq 模板配置筛选列表-响应结果
type OverTimeTplOptsRes struct {
	List                 []*OverTimeTplOptsRes_Opts `protobuf:"bytes,2,rep,name=list,proto3" json:"list"`
	XXX_NoUnkeyedLiteral struct{}                   `json:"-" gorm:"-"`
	XXX_unrecognized     []byte                     `json:"-" gorm:"-"`
	XXX_sizecache        int32                      `json:"-" gorm:"-"`
}

func (m *OverTimeTplOptsRes) Reset()         { *m = OverTimeTplOptsRes{} }
func (m *OverTimeTplOptsRes) String() string { return proto.CompactTextString(m) }
func (*OverTimeTplOptsRes) ProtoMessage()    {}
func (*OverTimeTplOptsRes) Descriptor() ([]byte, []int) {
	return fileDescriptor_5ceb164813729599, []int{6}
}

func (m *OverTimeTplOptsRes) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OverTimeTplOptsRes.Unmarshal(m, b)
}
func (m *OverTimeTplOptsRes) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OverTimeTplOptsRes.Marshal(b, m, deterministic)
}
func (m *OverTimeTplOptsRes) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OverTimeTplOptsRes.Merge(m, src)
}
func (m *OverTimeTplOptsRes) XXX_Size() int {
	return xxx_messageInfo_OverTimeTplOptsRes.Size(m)
}
func (m *OverTimeTplOptsRes) XXX_DiscardUnknown() {
	xxx_messageInfo_OverTimeTplOptsRes.DiscardUnknown(m)
}

var xxx_messageInfo_OverTimeTplOptsRes proto.InternalMessageInfo

func (m *OverTimeTplOptsRes) GetList() []*OverTimeTplOptsRes_Opts {
	if m != nil {
		return m.List
	}
	return nil
}

type OverTimeTplOptsRes_Opts struct {
	TplId                uint32   `protobuf:"varint,1,opt,name=tpl_id,json=tplId,proto3" json:"tpl_id"`
	TplName              string   `protobuf:"bytes,2,opt,name=tpl_name,json=tplName,proto3" json:"tpl_name"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *OverTimeTplOptsRes_Opts) Reset()         { *m = OverTimeTplOptsRes_Opts{} }
func (m *OverTimeTplOptsRes_Opts) String() string { return proto.CompactTextString(m) }
func (*OverTimeTplOptsRes_Opts) ProtoMessage()    {}
func (*OverTimeTplOptsRes_Opts) Descriptor() ([]byte, []int) {
	return fileDescriptor_5ceb164813729599, []int{6, 0}
}

func (m *OverTimeTplOptsRes_Opts) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OverTimeTplOptsRes_Opts.Unmarshal(m, b)
}
func (m *OverTimeTplOptsRes_Opts) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OverTimeTplOptsRes_Opts.Marshal(b, m, deterministic)
}
func (m *OverTimeTplOptsRes_Opts) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OverTimeTplOptsRes_Opts.Merge(m, src)
}
func (m *OverTimeTplOptsRes_Opts) XXX_Size() int {
	return xxx_messageInfo_OverTimeTplOptsRes_Opts.Size(m)
}
func (m *OverTimeTplOptsRes_Opts) XXX_DiscardUnknown() {
	xxx_messageInfo_OverTimeTplOptsRes_Opts.DiscardUnknown(m)
}

var xxx_messageInfo_OverTimeTplOptsRes_Opts proto.InternalMessageInfo

func (m *OverTimeTplOptsRes_Opts) GetTplId() uint32 {
	if m != nil {
		return m.TplId
	}
	return 0
}

func (m *OverTimeTplOptsRes_Opts) GetTplName() string {
	if m != nil {
		return m.TplName
	}
	return ""
}

// OverTimeTplEnableReq 启用/禁用模版
type OverTimeTplEnableReq struct {
	// 对象ID @gotags: validate:"required"
	ObjectId uint32 `protobuf:"varint,1,opt,name=object_id,json=objectId,proto3" json:"object_id" validate:"required"`
	// 启用1禁用0
	Enable               uint32   `protobuf:"varint,2,opt,name=enable,proto3" json:"enable"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *OverTimeTplEnableReq) Reset()         { *m = OverTimeTplEnableReq{} }
func (m *OverTimeTplEnableReq) String() string { return proto.CompactTextString(m) }
func (*OverTimeTplEnableReq) ProtoMessage()    {}
func (*OverTimeTplEnableReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_5ceb164813729599, []int{7}
}

func (m *OverTimeTplEnableReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_OverTimeTplEnableReq.Unmarshal(m, b)
}
func (m *OverTimeTplEnableReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_OverTimeTplEnableReq.Marshal(b, m, deterministic)
}
func (m *OverTimeTplEnableReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OverTimeTplEnableReq.Merge(m, src)
}
func (m *OverTimeTplEnableReq) XXX_Size() int {
	return xxx_messageInfo_OverTimeTplEnableReq.Size(m)
}
func (m *OverTimeTplEnableReq) XXX_DiscardUnknown() {
	xxx_messageInfo_OverTimeTplEnableReq.DiscardUnknown(m)
}

var xxx_messageInfo_OverTimeTplEnableReq proto.InternalMessageInfo

func (m *OverTimeTplEnableReq) GetObjectId() uint32 {
	if m != nil {
		return m.ObjectId
	}
	return 0
}

func (m *OverTimeTplEnableReq) GetEnable() uint32 {
	if m != nil {
		return m.Enable
	}
	return 0
}

func init() {
	proto.RegisterType((*OverTimeTplAddReq)(nil), "pb.OverTimeTplAddReq")
	proto.RegisterMapType((map[string]string)(nil), "pb.OverTimeTplAddReq.ContentEntry")
	proto.RegisterType((*OverTimeTplListReq)(nil), "pb.OverTimeTplListReq")
	proto.RegisterType((*OverTimeTplListResp)(nil), "pb.OverTimeTplListResp")
	proto.RegisterType((*OverTimeTplListResp_TplInfo)(nil), "pb.OverTimeTplListResp.TplInfo")
	proto.RegisterMapType((map[string]string)(nil), "pb.OverTimeTplListResp.TplInfo.TplContentEntry")
	proto.RegisterType((*OverTimeTplEditReq)(nil), "pb.OverTimeTplEditReq")
	proto.RegisterMapType((map[string]string)(nil), "pb.OverTimeTplEditReq.ContentEntry")
	proto.RegisterType((*OverTimeTplDelReq)(nil), "pb.OverTimeTplDelReq")
	proto.RegisterType((*OverTimeTplOptsReq)(nil), "pb.OverTimeTplOptsReq")
	proto.RegisterType((*OverTimeTplOptsRes)(nil), "pb.OverTimeTplOptsRes")
	proto.RegisterType((*OverTimeTplOptsRes_Opts)(nil), "pb.OverTimeTplOptsRes.Opts")
	proto.RegisterType((*OverTimeTplEnableReq)(nil), "pb.OverTimeTplEnableReq")
}

func init() {
	proto.RegisterFile("overtime_tpl.proto", fileDescriptor_5ceb164813729599)
}

var fileDescriptor_5ceb164813729599 = []byte{
	// 588 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xb4, 0x54, 0x4b, 0x6f, 0xd3, 0x40,
	0x10, 0x96, 0x1f, 0x49, 0x9c, 0x71, 0xcb, 0x63, 0x09, 0xc8, 0x4d, 0x0e, 0x0d, 0xee, 0x25, 0x27,
	0x57, 0x6a, 0x0f, 0x54, 0x85, 0x1e, 0x78, 0xf4, 0x10, 0x81, 0x68, 0x64, 0x72, 0xe2, 0x62, 0xd9,
	0xf1, 0x50, 0x19, 0xfc, 0x58, 0xd6, 0x9b, 0x48, 0xed, 0x85, 0x03, 0xe2, 0x6f, 0xf2, 0x0b, 0xf8,
	0x0f, 0xa0, 0xdd, 0x75, 0x52, 0xc7, 0x41, 0x44, 0x15, 0xea, 0xc9, 0xde, 0x6f, 0x66, 0x76, 0xbe,
	0xf9, 0xfc, 0x8d, 0x81, 0x14, 0x0b, 0x64, 0x3c, 0xc9, 0x30, 0xe0, 0x34, 0xf5, 0x28, 0x2b, 0x78,
	0x41, 0x74, 0x1a, 0xb9, 0xbf, 0x34, 0x78, 0x78, 0xb1, 0x40, 0x36, 0x4d, 0x32, 0x9c, 0xd2, 0xf4,
	0x65, 0x1c, 0xfb, 0xf8, 0x95, 0xec, 0x81, 0xc5, 0x69, 0x1a, 0xe4, 0x61, 0x86, 0x8e, 0x36, 0xd4,
	0x46, 0x5d, 0xbf, 0xc3, 0x69, 0xfa, 0x3e, 0xcc, 0x90, 0x3c, 0x85, 0x9d, 0xcb, 0x30, 0xc3, 0x80,
	0xb2, 0xe2, 0x33, 0xce, 0xb8, 0xa3, 0x0f, 0x8d, 0x51, 0xd7, 0xb7, 0x05, 0x36, 0x51, 0x10, 0xd9,
	0x07, 0x9b, 0x61, 0x96, 0xe4, 0x71, 0x20, 0x1a, 0x3a, 0xc6, 0x50, 0x1b, 0xed, 0xfa, 0xa0, 0x20,
	0xd1, 0x87, 0xbc, 0x80, 0xce, 0xac, 0xc8, 0x39, 0xe6, 0xdc, 0x31, 0x87, 0xc6, 0xc8, 0x3e, 0x72,
	0x3d, 0x1a, 0x79, 0x1b, 0x34, 0xbc, 0xd7, 0x2a, 0xe9, 0x3c, 0xe7, 0xec, 0xca, 0x5f, 0x96, 0xf4,
	0x4f, 0x61, 0xa7, 0x1e, 0x20, 0x0f, 0xc0, 0xf8, 0x82, 0x57, 0x15, 0x4f, 0xf1, 0x4a, 0x7a, 0xd0,
	0x5a, 0x84, 0xe9, 0x1c, 0x1d, 0x5d, 0x62, 0xea, 0x70, 0xaa, 0x9f, 0x68, 0xee, 0x77, 0x0d, 0x48,
	0xad, 0xcf, 0xbb, 0xa4, 0xe4, 0xff, 0x3f, 0x2f, 0x01, 0x93, 0x86, 0x97, 0xcb, 0x41, 0xe5, 0x3b,
	0x19, 0x40, 0x57, 0x3c, 0x83, 0x32, 0xb9, 0x46, 0xc7, 0x94, 0x01, 0x4b, 0x00, 0x1f, 0x92, 0x6b,
	0x74, 0x7f, 0x1b, 0xf0, 0x68, 0x83, 0x45, 0x49, 0x45, 0xaf, 0xd9, 0x9c, 0x31, 0xcc, 0x79, 0x20,
	0x2f, 0xd4, 0x64, 0x9d, 0x5d, 0x61, 0x13, 0x71, 0xef, 0x1e, 0x58, 0x14, 0x99, 0x0a, 0xeb, 0x32,
	0xdc, 0xa1, 0xc8, 0x64, 0xa8, 0x07, 0x2d, 0x5e, 0xf0, 0x30, 0xad, 0x78, 0xa8, 0x03, 0x39, 0x06,
	0x33, 0x0e, 0x79, 0x58, 0x09, 0xbd, 0xdf, 0x10, 0x7a, 0xd9, 0xda, 0x9b, 0xd2, 0x74, 0x9c, 0x7f,
	0x2a, 0x7c, 0x99, 0xdc, 0xff, 0xa9, 0x43, 0xa7, 0x42, 0xc8, 0x3d, 0xd0, 0x93, 0x58, 0x52, 0x31,
	0x7d, 0x3d, 0x89, 0xd7, 0xb4, 0xd2, 0xff, 0xad, 0x95, 0xb9, 0xa9, 0x55, 0x1f, 0xac, 0x82, 0x22,
	0x0b, 0x79, 0xc1, 0x9c, 0x96, 0xac, 0x5e, 0x9d, 0xc9, 0x13, 0x68, 0x63, 0x1e, 0x46, 0x29, 0x3a,
	0x6d, 0x39, 0x41, 0x75, 0x12, 0x7e, 0x9a, 0xd3, 0x38, 0xe4, 0xa8, 0xfc, 0xd4, 0x91, 0x65, 0xa0,
	0x20, 0xe9, 0xa7, 0x86, 0xe1, 0xac, 0x0d, 0xc3, 0x4d, 0xc0, 0x16, 0x9c, 0x97, 0xa6, 0xeb, 0x4a,
	0x2d, 0x0e, 0xb7, 0x68, 0x21, 0x9e, 0x6b, 0x0e, 0x04, 0xbe, 0x02, 0xfa, 0x67, 0x70, 0xbf, 0x11,
	0xbe, 0x95, 0x0f, 0x7f, 0xe8, 0x6b, 0x3e, 0x3c, 0x8f, 0x13, 0xe9, 0xc3, 0xc7, 0xd0, 0x16, 0x3c,
	0x57, 0x7a, 0xb7, 0x38, 0x4d, 0xc7, 0xb7, 0x92, 0xdc, 0xd8, 0xba, 0x8e, 0xe6, 0x86, 0x3a, 0x67,
	0x37, 0xeb, 0xd8, 0x92, 0xca, 0x1c, 0x34, 0x94, 0xa9, 0xe8, 0xdd, 0xc1, 0x3e, 0x1e, 0xac, 0xfd,
	0x7d, 0xde, 0x60, 0x2a, 0x54, 0x68, 0x38, 0xce, 0x7d, 0xb6, 0xa6, 0xd5, 0x05, 0xe5, 0xa5, 0xc8,
	0x6a, 0x4e, 0xae, 0xfa, 0xd5, 0x27, 0x77, 0xbf, 0xfd, 0xa5, 0xb0, 0x24, 0x87, 0x60, 0xa6, 0x49,
	0xa9, 0x36, 0xd9, 0x3e, 0x1a, 0x34, 0x66, 0xad, 0xb2, 0x3c, 0xf9, 0x94, 0x89, 0xfd, 0x13, 0x30,
	0xc5, 0xa9, 0xf1, 0x75, 0x76, 0xb7, 0x7f, 0x1d, 0xf7, 0x2d, 0xf4, 0xea, 0x32, 0x4a, 0x3b, 0x0b,
	0xee, 0x03, 0xe8, 0x16, 0x91, 0xa0, 0x78, 0x73, 0x99, 0xa5, 0x80, 0x71, 0x5c, 0x5b, 0x03, 0xbd,
	0xbe, 0x06, 0xaf, 0xda, 0x1f, 0x4d, 0xef, 0x39, 0x8d, 0xa2, 0xb6, 0xfc, 0x7b, 0x1f, 0xff, 0x09,
	0x00, 0x00, 0xff, 0xff, 0x54, 0x75, 0x7c, 0x05, 0xd3, 0x05, 0x00, 0x00,
}
