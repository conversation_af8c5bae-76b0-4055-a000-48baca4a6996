// Code generated by protoc-gen-go. DO NOT EDIT.
// source: survey.proto

package pb

import (
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

// SurveyListReq 问卷调查配置列表-请求参数
type SurveyListReq struct {
	// 页码
	Page uint32 `protobuf:"varint,1,opt,name=page,proto3" json:"page"`
	// 页大小
	PageSize             uint32   `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *SurveyListReq) Reset()         { *m = SurveyListReq{} }
func (m *SurveyListReq) String() string { return proto.CompactTextString(m) }
func (*SurveyListReq) ProtoMessage()    {}
func (*SurveyListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_a40f94eaa8e6ca46, []int{0}
}

func (m *SurveyListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SurveyListReq.Unmarshal(m, b)
}
func (m *SurveyListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SurveyListReq.Marshal(b, m, deterministic)
}
func (m *SurveyListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SurveyListReq.Merge(m, src)
}
func (m *SurveyListReq) XXX_Size() int {
	return xxx_messageInfo_SurveyListReq.Size(m)
}
func (m *SurveyListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SurveyListReq.DiscardUnknown(m)
}

var xxx_messageInfo_SurveyListReq proto.InternalMessageInfo

func (m *SurveyListReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *SurveyListReq) GetPageSize() uint32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

// SurveyListResp 问卷调查配置列表-响应结果
type SurveyListResp struct {
	CurrentPage          uint32                       `protobuf:"varint,1,opt,name=current_page,json=currentPage,proto3" json:"current_page"`
	PerPage              uint32                       `protobuf:"varint,2,opt,name=per_page,json=perPage,proto3" json:"per_page"`
	Total                uint32                       `protobuf:"varint,3,opt,name=total,proto3" json:"total"`
	Data                 []*SurveyListResp_SurveyInfo `protobuf:"bytes,4,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}                     `json:"-" gorm:"-"`
	XXX_unrecognized     []byte                       `json:"-" gorm:"-"`
	XXX_sizecache        int32                        `json:"-" gorm:"-"`
}

func (m *SurveyListResp) Reset()         { *m = SurveyListResp{} }
func (m *SurveyListResp) String() string { return proto.CompactTextString(m) }
func (*SurveyListResp) ProtoMessage()    {}
func (*SurveyListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_a40f94eaa8e6ca46, []int{1}
}

func (m *SurveyListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SurveyListResp.Unmarshal(m, b)
}
func (m *SurveyListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SurveyListResp.Marshal(b, m, deterministic)
}
func (m *SurveyListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SurveyListResp.Merge(m, src)
}
func (m *SurveyListResp) XXX_Size() int {
	return xxx_messageInfo_SurveyListResp.Size(m)
}
func (m *SurveyListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SurveyListResp.DiscardUnknown(m)
}

var xxx_messageInfo_SurveyListResp proto.InternalMessageInfo

func (m *SurveyListResp) GetCurrentPage() uint32 {
	if m != nil {
		return m.CurrentPage
	}
	return 0
}

func (m *SurveyListResp) GetPerPage() uint32 {
	if m != nil {
		return m.PerPage
	}
	return 0
}

func (m *SurveyListResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *SurveyListResp) GetData() []*SurveyListResp_SurveyInfo {
	if m != nil {
		return m.Data
	}
	return nil
}

type SurveyListResp_SurveyInfo struct {
	Id                   uint64   `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	Project              string   `protobuf:"bytes,2,opt,name=project,proto3" json:"project"`
	UpdatedAt            string   `protobuf:"bytes,3,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at"`
	Op                   string   `protobuf:"bytes,4,opt,name=op,proto3" json:"op"`
	Enable               bool     `protobuf:"varint,5,opt,name=enable,proto3" json:"enable"`
	CanGenLink           bool     `protobuf:"varint,6,opt,name=can_gen_link,json=canGenLink,proto3" json:"can_gen_link"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *SurveyListResp_SurveyInfo) Reset()         { *m = SurveyListResp_SurveyInfo{} }
func (m *SurveyListResp_SurveyInfo) String() string { return proto.CompactTextString(m) }
func (*SurveyListResp_SurveyInfo) ProtoMessage()    {}
func (*SurveyListResp_SurveyInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_a40f94eaa8e6ca46, []int{1, 0}
}

func (m *SurveyListResp_SurveyInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SurveyListResp_SurveyInfo.Unmarshal(m, b)
}
func (m *SurveyListResp_SurveyInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SurveyListResp_SurveyInfo.Marshal(b, m, deterministic)
}
func (m *SurveyListResp_SurveyInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SurveyListResp_SurveyInfo.Merge(m, src)
}
func (m *SurveyListResp_SurveyInfo) XXX_Size() int {
	return xxx_messageInfo_SurveyListResp_SurveyInfo.Size(m)
}
func (m *SurveyListResp_SurveyInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_SurveyListResp_SurveyInfo.DiscardUnknown(m)
}

var xxx_messageInfo_SurveyListResp_SurveyInfo proto.InternalMessageInfo

func (m *SurveyListResp_SurveyInfo) GetId() uint64 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *SurveyListResp_SurveyInfo) GetProject() string {
	if m != nil {
		return m.Project
	}
	return ""
}

func (m *SurveyListResp_SurveyInfo) GetUpdatedAt() string {
	if m != nil {
		return m.UpdatedAt
	}
	return ""
}

func (m *SurveyListResp_SurveyInfo) GetOp() string {
	if m != nil {
		return m.Op
	}
	return ""
}

func (m *SurveyListResp_SurveyInfo) GetEnable() bool {
	if m != nil {
		return m.Enable
	}
	return false
}

func (m *SurveyListResp_SurveyInfo) GetCanGenLink() bool {
	if m != nil {
		return m.CanGenLink
	}
	return false
}

// SurveyInfoReq 调查问卷配置详情-请求参数
type SurveyInfoReq struct {
	// 问卷id @gotags: validate:"required"
	Id                   uint64   `protobuf:"varint,1,opt,name=id,proto3" json:"id" validate:"required"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *SurveyInfoReq) Reset()         { *m = SurveyInfoReq{} }
func (m *SurveyInfoReq) String() string { return proto.CompactTextString(m) }
func (*SurveyInfoReq) ProtoMessage()    {}
func (*SurveyInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_a40f94eaa8e6ca46, []int{2}
}

func (m *SurveyInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SurveyInfoReq.Unmarshal(m, b)
}
func (m *SurveyInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SurveyInfoReq.Marshal(b, m, deterministic)
}
func (m *SurveyInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SurveyInfoReq.Merge(m, src)
}
func (m *SurveyInfoReq) XXX_Size() int {
	return xxx_messageInfo_SurveyInfoReq.Size(m)
}
func (m *SurveyInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SurveyInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_SurveyInfoReq proto.InternalMessageInfo

func (m *SurveyInfoReq) GetId() uint64 {
	if m != nil {
		return m.Id
	}
	return 0
}

// SurveyInfoResp 调查问卷配置详情-响应参数
type SurveyInfoResp struct {
	Data                 *SurveyEditReq `protobuf:"bytes,1,opt,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-" gorm:"-"`
	XXX_unrecognized     []byte         `json:"-" gorm:"-"`
	XXX_sizecache        int32          `json:"-" gorm:"-"`
}

func (m *SurveyInfoResp) Reset()         { *m = SurveyInfoResp{} }
func (m *SurveyInfoResp) String() string { return proto.CompactTextString(m) }
func (*SurveyInfoResp) ProtoMessage()    {}
func (*SurveyInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_a40f94eaa8e6ca46, []int{3}
}

func (m *SurveyInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SurveyInfoResp.Unmarshal(m, b)
}
func (m *SurveyInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SurveyInfoResp.Marshal(b, m, deterministic)
}
func (m *SurveyInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SurveyInfoResp.Merge(m, src)
}
func (m *SurveyInfoResp) XXX_Size() int {
	return xxx_messageInfo_SurveyInfoResp.Size(m)
}
func (m *SurveyInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SurveyInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_SurveyInfoResp proto.InternalMessageInfo

func (m *SurveyInfoResp) GetData() *SurveyEditReq {
	if m != nil {
		return m.Data
	}
	return nil
}

type DscSurveyPlayerDetail struct {
	// dsc priv channel id
	PrivChannelId string `protobuf:"bytes,1,opt,name=priv_channel_id,json=privChannelId,proto3" json:"priv_channel_id"`
	// app id
	AppId string `protobuf:"bytes,2,opt,name=app_id,json=appId,proto3" json:"app_id"`
	// dsc user id
	DscUserId string `protobuf:"bytes,3,opt,name=dsc_user_id,json=dscUserId,proto3" json:"dsc_user_id"`
	// dsc nick name
	DscNickName string `protobuf:"bytes,4,opt,name=dsc_nick_name,json=dscNickName,proto3" json:"dsc_nick_name"`
	// uid
	Uid uint64 `protobuf:"varint,6,opt,name=uid,proto3" json:"uid"`
	// account id
	AccountId string `protobuf:"bytes,7,opt,name=account_id,json=accountId,proto3" json:"account_id"`
	// lang
	Lang string `protobuf:"bytes,8,opt,name=lang,proto3" json:"lang"`
	// maintainer - 维护人
	Maintainer string `protobuf:"bytes,9,opt,name=maintainer,proto3" json:"maintainer"`
	// 经办人
	Processors []string `protobuf:"bytes,10,rep,name=processors,proto3" json:"processors"`
	// 处理人
	LastReplyService     string   `protobuf:"bytes,11,opt,name=last_reply_service,json=lastReplyService,proto3" json:"last_reply_service"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *DscSurveyPlayerDetail) Reset()         { *m = DscSurveyPlayerDetail{} }
func (m *DscSurveyPlayerDetail) String() string { return proto.CompactTextString(m) }
func (*DscSurveyPlayerDetail) ProtoMessage()    {}
func (*DscSurveyPlayerDetail) Descriptor() ([]byte, []int) {
	return fileDescriptor_a40f94eaa8e6ca46, []int{4}
}

func (m *DscSurveyPlayerDetail) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DscSurveyPlayerDetail.Unmarshal(m, b)
}
func (m *DscSurveyPlayerDetail) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DscSurveyPlayerDetail.Marshal(b, m, deterministic)
}
func (m *DscSurveyPlayerDetail) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DscSurveyPlayerDetail.Merge(m, src)
}
func (m *DscSurveyPlayerDetail) XXX_Size() int {
	return xxx_messageInfo_DscSurveyPlayerDetail.Size(m)
}
func (m *DscSurveyPlayerDetail) XXX_DiscardUnknown() {
	xxx_messageInfo_DscSurveyPlayerDetail.DiscardUnknown(m)
}

var xxx_messageInfo_DscSurveyPlayerDetail proto.InternalMessageInfo

func (m *DscSurveyPlayerDetail) GetPrivChannelId() string {
	if m != nil {
		return m.PrivChannelId
	}
	return ""
}

func (m *DscSurveyPlayerDetail) GetAppId() string {
	if m != nil {
		return m.AppId
	}
	return ""
}

func (m *DscSurveyPlayerDetail) GetDscUserId() string {
	if m != nil {
		return m.DscUserId
	}
	return ""
}

func (m *DscSurveyPlayerDetail) GetDscNickName() string {
	if m != nil {
		return m.DscNickName
	}
	return ""
}

func (m *DscSurveyPlayerDetail) GetUid() uint64 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *DscSurveyPlayerDetail) GetAccountId() string {
	if m != nil {
		return m.AccountId
	}
	return ""
}

func (m *DscSurveyPlayerDetail) GetLang() string {
	if m != nil {
		return m.Lang
	}
	return ""
}

func (m *DscSurveyPlayerDetail) GetMaintainer() string {
	if m != nil {
		return m.Maintainer
	}
	return ""
}

func (m *DscSurveyPlayerDetail) GetProcessors() []string {
	if m != nil {
		return m.Processors
	}
	return nil
}

func (m *DscSurveyPlayerDetail) GetLastReplyService() string {
	if m != nil {
		return m.LastReplyService
	}
	return ""
}

// SurveyLinkParamDf 问卷调查链接参数
type SurveyLinkParamDf struct {
	SurveyId uint64 `protobuf:"varint,1,opt,name=survey_id,json=surveyId,proto3" json:"survey_id"`
	Project  string `protobuf:"bytes,2,opt,name=project,proto3" json:"project"`
	Lang     string `protobuf:"bytes,3,opt,name=lang,proto3" json:"lang"`
	Uid      int64  `protobuf:"varint,4,opt,name=uid,proto3" json:"uid"`
	Account  string `protobuf:"bytes,6,opt,name=account,proto3" json:"account"`
	// 当前此刻的锁定信息
	MomentAttrs          *SurveyLinkParamDf_Attrs `protobuf:"bytes,7,opt,name=moment_attrs,json=momentAttrs,proto3" json:"moment_attrs"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-" gorm:"-"`
	XXX_unrecognized     []byte                   `json:"-" gorm:"-"`
	XXX_sizecache        int32                    `json:"-" gorm:"-"`
}

func (m *SurveyLinkParamDf) Reset()         { *m = SurveyLinkParamDf{} }
func (m *SurveyLinkParamDf) String() string { return proto.CompactTextString(m) }
func (*SurveyLinkParamDf) ProtoMessage()    {}
func (*SurveyLinkParamDf) Descriptor() ([]byte, []int) {
	return fileDescriptor_a40f94eaa8e6ca46, []int{5}
}

func (m *SurveyLinkParamDf) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SurveyLinkParamDf.Unmarshal(m, b)
}
func (m *SurveyLinkParamDf) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SurveyLinkParamDf.Marshal(b, m, deterministic)
}
func (m *SurveyLinkParamDf) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SurveyLinkParamDf.Merge(m, src)
}
func (m *SurveyLinkParamDf) XXX_Size() int {
	return xxx_messageInfo_SurveyLinkParamDf.Size(m)
}
func (m *SurveyLinkParamDf) XXX_DiscardUnknown() {
	xxx_messageInfo_SurveyLinkParamDf.DiscardUnknown(m)
}

var xxx_messageInfo_SurveyLinkParamDf proto.InternalMessageInfo

func (m *SurveyLinkParamDf) GetSurveyId() uint64 {
	if m != nil {
		return m.SurveyId
	}
	return 0
}

func (m *SurveyLinkParamDf) GetProject() string {
	if m != nil {
		return m.Project
	}
	return ""
}

func (m *SurveyLinkParamDf) GetLang() string {
	if m != nil {
		return m.Lang
	}
	return ""
}

func (m *SurveyLinkParamDf) GetUid() int64 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SurveyLinkParamDf) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func (m *SurveyLinkParamDf) GetMomentAttrs() *SurveyLinkParamDf_Attrs {
	if m != nil {
		return m.MomentAttrs
	}
	return nil
}

type SurveyLinkParamDf_Attrs struct {
	// 生成问卷时 - 维护人
	Maintainer string `protobuf:"bytes,2,opt,name=maintainer,proto3" json:"maintainer"`
	// 生成问卷时 - 最近处理人
	LastReplyService string `protobuf:"bytes,3,opt,name=last_reply_service,json=lastReplyService,proto3" json:"last_reply_service"`
	// 生成问卷时 - 经办人
	Processors []string `protobuf:"bytes,4,rep,name=processors,proto3" json:"processors"`
	// discord 账号 id
	DscUserId string `protobuf:"bytes,5,opt,name=dsc_user_id,json=dscUserId,proto3" json:"dsc_user_id"`
	// discord 账号昵称
	DscUserName string `protobuf:"bytes,6,opt,name=dsc_user_name,json=dscUserName,proto3" json:"dsc_user_name"`
	// uid
	Uid uint64 `protobuf:"varint,7,opt,name=uid,proto3" json:"uid"`
	// fpid
	AccountId string `protobuf:"bytes,8,opt,name=account_id,json=accountId,proto3" json:"account_id"`
	// priv channel id
	DscChannelId string `protobuf:"bytes,9,opt,name=dsc_channel_id,json=dscChannelId,proto3" json:"dsc_channel_id"`
	// dsc_bot_id
	DscBotId             string   `protobuf:"bytes,10,opt,name=dsc_bot_id,json=dscBotId,proto3" json:"dsc_bot_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *SurveyLinkParamDf_Attrs) Reset()         { *m = SurveyLinkParamDf_Attrs{} }
func (m *SurveyLinkParamDf_Attrs) String() string { return proto.CompactTextString(m) }
func (*SurveyLinkParamDf_Attrs) ProtoMessage()    {}
func (*SurveyLinkParamDf_Attrs) Descriptor() ([]byte, []int) {
	return fileDescriptor_a40f94eaa8e6ca46, []int{5, 0}
}

func (m *SurveyLinkParamDf_Attrs) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SurveyLinkParamDf_Attrs.Unmarshal(m, b)
}
func (m *SurveyLinkParamDf_Attrs) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SurveyLinkParamDf_Attrs.Marshal(b, m, deterministic)
}
func (m *SurveyLinkParamDf_Attrs) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SurveyLinkParamDf_Attrs.Merge(m, src)
}
func (m *SurveyLinkParamDf_Attrs) XXX_Size() int {
	return xxx_messageInfo_SurveyLinkParamDf_Attrs.Size(m)
}
func (m *SurveyLinkParamDf_Attrs) XXX_DiscardUnknown() {
	xxx_messageInfo_SurveyLinkParamDf_Attrs.DiscardUnknown(m)
}

var xxx_messageInfo_SurveyLinkParamDf_Attrs proto.InternalMessageInfo

func (m *SurveyLinkParamDf_Attrs) GetMaintainer() string {
	if m != nil {
		return m.Maintainer
	}
	return ""
}

func (m *SurveyLinkParamDf_Attrs) GetLastReplyService() string {
	if m != nil {
		return m.LastReplyService
	}
	return ""
}

func (m *SurveyLinkParamDf_Attrs) GetProcessors() []string {
	if m != nil {
		return m.Processors
	}
	return nil
}

func (m *SurveyLinkParamDf_Attrs) GetDscUserId() string {
	if m != nil {
		return m.DscUserId
	}
	return ""
}

func (m *SurveyLinkParamDf_Attrs) GetDscUserName() string {
	if m != nil {
		return m.DscUserName
	}
	return ""
}

func (m *SurveyLinkParamDf_Attrs) GetUid() uint64 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SurveyLinkParamDf_Attrs) GetAccountId() string {
	if m != nil {
		return m.AccountId
	}
	return ""
}

func (m *SurveyLinkParamDf_Attrs) GetDscChannelId() string {
	if m != nil {
		return m.DscChannelId
	}
	return ""
}

func (m *SurveyLinkParamDf_Attrs) GetDscBotId() string {
	if m != nil {
		return m.DscBotId
	}
	return ""
}

// SurveyEditResp 调查问卷配置修改-请求参数
type SurveyEditReq struct {
	// 问卷id @gotags: validate:"required,gt=0"
	Id uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id" validate:"required,gt=0"`
	// 游戏 @gotags: validate:"required"
	Project string `protobuf:"bytes,2,opt,name=project,proto3" json:"project" validate:"required"`
	// 推送周期 @gotags: validate:"required,gt=0"
	PushCycle SurveyPushCycle `protobuf:"varint,3,opt,name=push_cycle,json=pushCycle,proto3,enum=pb.SurveyPushCycle" json:"push_cycle" validate:"required,gt=0"`
	// 推送时间-星期 @gotags: validate:"required"
	PushWeek uint64 `protobuf:"varint,4,opt,name=push_week,json=pushWeek,proto3" json:"push_week" validate:"required"`
	// 推送时间-小时
	PushTime uint64 `protobuf:"varint,5,opt,name=push_time,json=pushTime,proto3" json:"push_time"`
	// 推送时间-前端传递 @gotags: validate:"required"
	PushTimeWeb string `protobuf:"bytes,13,opt,name=push_time_web,json=pushTimeWeb,proto3" json:"push_time_web" validate:"required"`
	// 生效时间 @gotags: validate:"required"
	EffectiveTime string `protobuf:"bytes,6,opt,name=effective_time,json=effectiveTime,proto3" json:"effective_time" validate:"required"`
	// 有效期 @gotags: validate:"required,gt=0"
	ExpireTime SurveyEffective `protobuf:"varint,7,opt,name=expire_time,json=expireTime,proto3,enum=pb.SurveyEffective" json:"expire_time" validate:"required,gt=0"`
	// 问卷标题 多语言
	SurveyTitles map[string]string `protobuf:"bytes,8,rep,name=survey_titles,json=surveyTitles,proto3" json:"survey_titles" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// 推送文案 多语言
	PushContents map[string]string `protobuf:"bytes,9,rep,name=push_contents,json=pushContents,proto3" json:"push_contents" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// 产品题 多语言
	ProductQuestions map[string]string `protobuf:"bytes,10,rep,name=product_questions,json=productQuestions,proto3" json:"product_questions" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// 服务题 多语言
	ServiceQuestions map[string]string `protobuf:"bytes,11,rep,name=service_questions,json=serviceQuestions,proto3" json:"service_questions" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// 填写理由 多语言
	Reasons              map[string]string `protobuf:"bytes,12,rep,name=reasons,proto3" json:"reasons" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-" gorm:"-"`
	XXX_unrecognized     []byte            `json:"-" gorm:"-"`
	XXX_sizecache        int32             `json:"-" gorm:"-"`
}

func (m *SurveyEditReq) Reset()         { *m = SurveyEditReq{} }
func (m *SurveyEditReq) String() string { return proto.CompactTextString(m) }
func (*SurveyEditReq) ProtoMessage()    {}
func (*SurveyEditReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_a40f94eaa8e6ca46, []int{6}
}

func (m *SurveyEditReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SurveyEditReq.Unmarshal(m, b)
}
func (m *SurveyEditReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SurveyEditReq.Marshal(b, m, deterministic)
}
func (m *SurveyEditReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SurveyEditReq.Merge(m, src)
}
func (m *SurveyEditReq) XXX_Size() int {
	return xxx_messageInfo_SurveyEditReq.Size(m)
}
func (m *SurveyEditReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SurveyEditReq.DiscardUnknown(m)
}

var xxx_messageInfo_SurveyEditReq proto.InternalMessageInfo

func (m *SurveyEditReq) GetId() uint64 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *SurveyEditReq) GetProject() string {
	if m != nil {
		return m.Project
	}
	return ""
}

func (m *SurveyEditReq) GetPushCycle() SurveyPushCycle {
	if m != nil {
		return m.PushCycle
	}
	return SurveyPushCycle_UnknownSurveyPushCycle
}

func (m *SurveyEditReq) GetPushWeek() uint64 {
	if m != nil {
		return m.PushWeek
	}
	return 0
}

func (m *SurveyEditReq) GetPushTime() uint64 {
	if m != nil {
		return m.PushTime
	}
	return 0
}

func (m *SurveyEditReq) GetPushTimeWeb() string {
	if m != nil {
		return m.PushTimeWeb
	}
	return ""
}

func (m *SurveyEditReq) GetEffectiveTime() string {
	if m != nil {
		return m.EffectiveTime
	}
	return ""
}

func (m *SurveyEditReq) GetExpireTime() SurveyEffective {
	if m != nil {
		return m.ExpireTime
	}
	return SurveyEffective_UnknownEffectiveDay
}

func (m *SurveyEditReq) GetSurveyTitles() map[string]string {
	if m != nil {
		return m.SurveyTitles
	}
	return nil
}

func (m *SurveyEditReq) GetPushContents() map[string]string {
	if m != nil {
		return m.PushContents
	}
	return nil
}

func (m *SurveyEditReq) GetProductQuestions() map[string]string {
	if m != nil {
		return m.ProductQuestions
	}
	return nil
}

func (m *SurveyEditReq) GetServiceQuestions() map[string]string {
	if m != nil {
		return m.ServiceQuestions
	}
	return nil
}

func (m *SurveyEditReq) GetReasons() map[string]string {
	if m != nil {
		return m.Reasons
	}
	return nil
}

// SurveyEgressConfigResp 问卷调查配置-响应参数
type SurveyEgressConfigResp struct {
	Langs                []*SurveyEgressConfigResp_EnumLang `protobuf:"bytes,1,rep,name=langs,proto3" json:"langs"`
	XXX_NoUnkeyedLiteral struct{}                           `json:"-" gorm:"-"`
	XXX_unrecognized     []byte                             `json:"-" gorm:"-"`
	XXX_sizecache        int32                              `json:"-" gorm:"-"`
}

func (m *SurveyEgressConfigResp) Reset()         { *m = SurveyEgressConfigResp{} }
func (m *SurveyEgressConfigResp) String() string { return proto.CompactTextString(m) }
func (*SurveyEgressConfigResp) ProtoMessage()    {}
func (*SurveyEgressConfigResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_a40f94eaa8e6ca46, []int{7}
}

func (m *SurveyEgressConfigResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SurveyEgressConfigResp.Unmarshal(m, b)
}
func (m *SurveyEgressConfigResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SurveyEgressConfigResp.Marshal(b, m, deterministic)
}
func (m *SurveyEgressConfigResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SurveyEgressConfigResp.Merge(m, src)
}
func (m *SurveyEgressConfigResp) XXX_Size() int {
	return xxx_messageInfo_SurveyEgressConfigResp.Size(m)
}
func (m *SurveyEgressConfigResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SurveyEgressConfigResp.DiscardUnknown(m)
}

var xxx_messageInfo_SurveyEgressConfigResp proto.InternalMessageInfo

func (m *SurveyEgressConfigResp) GetLangs() []*SurveyEgressConfigResp_EnumLang {
	if m != nil {
		return m.Langs
	}
	return nil
}

type SurveyEgressConfigResp_EnumLang struct {
	Code                 string   `protobuf:"bytes,1,opt,name=code,proto3" json:"code"`
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *SurveyEgressConfigResp_EnumLang) Reset()         { *m = SurveyEgressConfigResp_EnumLang{} }
func (m *SurveyEgressConfigResp_EnumLang) String() string { return proto.CompactTextString(m) }
func (*SurveyEgressConfigResp_EnumLang) ProtoMessage()    {}
func (*SurveyEgressConfigResp_EnumLang) Descriptor() ([]byte, []int) {
	return fileDescriptor_a40f94eaa8e6ca46, []int{7, 0}
}

func (m *SurveyEgressConfigResp_EnumLang) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SurveyEgressConfigResp_EnumLang.Unmarshal(m, b)
}
func (m *SurveyEgressConfigResp_EnumLang) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SurveyEgressConfigResp_EnumLang.Marshal(b, m, deterministic)
}
func (m *SurveyEgressConfigResp_EnumLang) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SurveyEgressConfigResp_EnumLang.Merge(m, src)
}
func (m *SurveyEgressConfigResp_EnumLang) XXX_Size() int {
	return xxx_messageInfo_SurveyEgressConfigResp_EnumLang.Size(m)
}
func (m *SurveyEgressConfigResp_EnumLang) XXX_DiscardUnknown() {
	xxx_messageInfo_SurveyEgressConfigResp_EnumLang.DiscardUnknown(m)
}

var xxx_messageInfo_SurveyEgressConfigResp_EnumLang proto.InternalMessageInfo

func (m *SurveyEgressConfigResp_EnumLang) GetCode() string {
	if m != nil {
		return m.Code
	}
	return ""
}

func (m *SurveyEgressConfigResp_EnumLang) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

// SurveySubmitReq 调查问卷配置修改-请求参数
type SurveySubmitReq struct {
	// 唯一标识 token @gotags: validate:"required"
	SurveyToken string `protobuf:"bytes,1,opt,name=survey_token,json=surveyToken,proto3" json:"survey_token" validate:"required"`
	// 当前时间戳 毫秒 ts_rank
	TsRank int64 `protobuf:"varint,2,opt,name=ts_rank,json=tsRank,proto3" json:"ts_rank"`
	// 语言
	Language string `protobuf:"bytes,3,opt,name=language,proto3" json:"language"`
	// uid - 选填
	Uid int64 `protobuf:"varint,6,opt,name=uid,proto3" json:"uid"`
	// 产品题-评星
	ProductRating int32 `protobuf:"varint,7,opt,name=product_rating,json=productRating,proto3" json:"product_rating"`
	// 产品题-回复
	ProductAnswer string `protobuf:"bytes,8,opt,name=product_answer,json=productAnswer,proto3" json:"product_answer"`
	// 服务题-评星
	ServiceRating int32 `protobuf:"varint,9,opt,name=service_rating,json=serviceRating,proto3" json:"service_rating"`
	// 服务题-回复
	ServiceAnswer        string   `protobuf:"bytes,10,opt,name=service_answer,json=serviceAnswer,proto3" json:"service_answer"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *SurveySubmitReq) Reset()         { *m = SurveySubmitReq{} }
func (m *SurveySubmitReq) String() string { return proto.CompactTextString(m) }
func (*SurveySubmitReq) ProtoMessage()    {}
func (*SurveySubmitReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_a40f94eaa8e6ca46, []int{8}
}

func (m *SurveySubmitReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SurveySubmitReq.Unmarshal(m, b)
}
func (m *SurveySubmitReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SurveySubmitReq.Marshal(b, m, deterministic)
}
func (m *SurveySubmitReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SurveySubmitReq.Merge(m, src)
}
func (m *SurveySubmitReq) XXX_Size() int {
	return xxx_messageInfo_SurveySubmitReq.Size(m)
}
func (m *SurveySubmitReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SurveySubmitReq.DiscardUnknown(m)
}

var xxx_messageInfo_SurveySubmitReq proto.InternalMessageInfo

func (m *SurveySubmitReq) GetSurveyToken() string {
	if m != nil {
		return m.SurveyToken
	}
	return ""
}

func (m *SurveySubmitReq) GetTsRank() int64 {
	if m != nil {
		return m.TsRank
	}
	return 0
}

func (m *SurveySubmitReq) GetLanguage() string {
	if m != nil {
		return m.Language
	}
	return ""
}

func (m *SurveySubmitReq) GetUid() int64 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SurveySubmitReq) GetProductRating() int32 {
	if m != nil {
		return m.ProductRating
	}
	return 0
}

func (m *SurveySubmitReq) GetProductAnswer() string {
	if m != nil {
		return m.ProductAnswer
	}
	return ""
}

func (m *SurveySubmitReq) GetServiceRating() int32 {
	if m != nil {
		return m.ServiceRating
	}
	return 0
}

func (m *SurveySubmitReq) GetServiceAnswer() string {
	if m != nil {
		return m.ServiceAnswer
	}
	return ""
}

// SurveyDetailReq 调查问卷模版-请求参数
type SurveyTemplateReq struct {
	// 问卷Token @gotags: validate:"required"
	SurveyToken string `protobuf:"bytes,1,opt,name=survey_token,json=surveyToken,proto3" json:"survey_token" validate:"required"`
	// 语言
	Lang string `protobuf:"bytes,4,opt,name=lang,proto3" json:"lang"`
	// 当前时间戳 毫秒 ts_rank
	TsRank               int64    `protobuf:"varint,5,opt,name=ts_rank,json=tsRank,proto3" json:"ts_rank"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *SurveyTemplateReq) Reset()         { *m = SurveyTemplateReq{} }
func (m *SurveyTemplateReq) String() string { return proto.CompactTextString(m) }
func (*SurveyTemplateReq) ProtoMessage()    {}
func (*SurveyTemplateReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_a40f94eaa8e6ca46, []int{9}
}

func (m *SurveyTemplateReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SurveyTemplateReq.Unmarshal(m, b)
}
func (m *SurveyTemplateReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SurveyTemplateReq.Marshal(b, m, deterministic)
}
func (m *SurveyTemplateReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SurveyTemplateReq.Merge(m, src)
}
func (m *SurveyTemplateReq) XXX_Size() int {
	return xxx_messageInfo_SurveyTemplateReq.Size(m)
}
func (m *SurveyTemplateReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SurveyTemplateReq.DiscardUnknown(m)
}

var xxx_messageInfo_SurveyTemplateReq proto.InternalMessageInfo

func (m *SurveyTemplateReq) GetSurveyToken() string {
	if m != nil {
		return m.SurveyToken
	}
	return ""
}

func (m *SurveyTemplateReq) GetLang() string {
	if m != nil {
		return m.Lang
	}
	return ""
}

func (m *SurveyTemplateReq) GetTsRank() int64 {
	if m != nil {
		return m.TsRank
	}
	return 0
}

// SurveyTemplateResp 调查问卷模版-响应参数
type SurveyTemplateResp struct {
	// 问卷配置id @gotags: validate:"required"
	SurveyId uint64 `protobuf:"varint,1,opt,name=survey_id,json=surveyId,proto3" json:"survey_id" validate:"required"`
	// 问卷 token 标识 ：
	SurveyToken string `protobuf:"bytes,2,opt,name=survey_token,json=surveyToken,proto3" json:"survey_token"`
	// 游戏
	Project string `protobuf:"bytes,3,opt,name=project,proto3" json:"project"`
	// 语言-为空时展示语言下拉框
	Lang string `protobuf:"bytes,4,opt,name=lang,proto3" json:"lang"`
	// 是否展示产品题
	IsShowProduct bool `protobuf:"varint,5,opt,name=is_show_product,json=isShowProduct,proto3" json:"is_show_product"`
	// 是否展示服务题
	IsShowService bool `protobuf:"varint,6,opt,name=is_show_service,json=isShowService,proto3" json:"is_show_service"`
	// 问卷标题 多语言
	SurveyTitles string `protobuf:"bytes,7,opt,name=survey_titles,json=surveyTitles,proto3" json:"survey_titles"`
	// 推送文案 多语言
	PushContents string `protobuf:"bytes,8,opt,name=push_contents,json=pushContents,proto3" json:"push_contents"`
	// 产品题 多语言
	ProductQuestions string `protobuf:"bytes,9,opt,name=product_questions,json=productQuestions,proto3" json:"product_questions"`
	// 服务题 多语言
	ServiceQuestions string `protobuf:"bytes,10,opt,name=service_questions,json=serviceQuestions,proto3" json:"service_questions"`
	// 填写理由 多语言
	Reasons string `protobuf:"bytes,11,opt,name=reasons,proto3" json:"reasons"`
	// 是否可以填写
	CanSubmit bool `protobuf:"varint,12,opt,name=can_submit,json=canSubmit,proto3" json:"can_submit"`
	// 是否需要填写 uid
	NeedUid              bool     `protobuf:"varint,13,opt,name=need_uid,json=needUid,proto3" json:"need_uid"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *SurveyTemplateResp) Reset()         { *m = SurveyTemplateResp{} }
func (m *SurveyTemplateResp) String() string { return proto.CompactTextString(m) }
func (*SurveyTemplateResp) ProtoMessage()    {}
func (*SurveyTemplateResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_a40f94eaa8e6ca46, []int{10}
}

func (m *SurveyTemplateResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SurveyTemplateResp.Unmarshal(m, b)
}
func (m *SurveyTemplateResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SurveyTemplateResp.Marshal(b, m, deterministic)
}
func (m *SurveyTemplateResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SurveyTemplateResp.Merge(m, src)
}
func (m *SurveyTemplateResp) XXX_Size() int {
	return xxx_messageInfo_SurveyTemplateResp.Size(m)
}
func (m *SurveyTemplateResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SurveyTemplateResp.DiscardUnknown(m)
}

var xxx_messageInfo_SurveyTemplateResp proto.InternalMessageInfo

func (m *SurveyTemplateResp) GetSurveyId() uint64 {
	if m != nil {
		return m.SurveyId
	}
	return 0
}

func (m *SurveyTemplateResp) GetSurveyToken() string {
	if m != nil {
		return m.SurveyToken
	}
	return ""
}

func (m *SurveyTemplateResp) GetProject() string {
	if m != nil {
		return m.Project
	}
	return ""
}

func (m *SurveyTemplateResp) GetLang() string {
	if m != nil {
		return m.Lang
	}
	return ""
}

func (m *SurveyTemplateResp) GetIsShowProduct() bool {
	if m != nil {
		return m.IsShowProduct
	}
	return false
}

func (m *SurveyTemplateResp) GetIsShowService() bool {
	if m != nil {
		return m.IsShowService
	}
	return false
}

func (m *SurveyTemplateResp) GetSurveyTitles() string {
	if m != nil {
		return m.SurveyTitles
	}
	return ""
}

func (m *SurveyTemplateResp) GetPushContents() string {
	if m != nil {
		return m.PushContents
	}
	return ""
}

func (m *SurveyTemplateResp) GetProductQuestions() string {
	if m != nil {
		return m.ProductQuestions
	}
	return ""
}

func (m *SurveyTemplateResp) GetServiceQuestions() string {
	if m != nil {
		return m.ServiceQuestions
	}
	return ""
}

func (m *SurveyTemplateResp) GetReasons() string {
	if m != nil {
		return m.Reasons
	}
	return ""
}

func (m *SurveyTemplateResp) GetCanSubmit() bool {
	if m != nil {
		return m.CanSubmit
	}
	return false
}

func (m *SurveyTemplateResp) GetNeedUid() bool {
	if m != nil {
		return m.NeedUid
	}
	return false
}

// SurveyGenLinkReq 问卷调查生成链接-请求参数
type SurveyGenLinkReq struct {
	// 项目名称 @gotags: validate:"required"
	Project string `protobuf:"bytes,1,opt,name=project,proto3" json:"project" validate:"required"`
	Uid     int64  `protobuf:"varint,2,opt,name=uid,proto3" json:"uid"`
	Lang    string `protobuf:"bytes,3,opt,name=lang,proto3" json:"lang"`
	// 问卷ID @gotags: validate:"required,gt=0"
	SurveyId int64 `protobuf:"varint,4,opt,name=survey_id,json=surveyId,proto3" json:"survey_id" validate:"required,gt=0"`
	// 是否公开 浏览器打开 为 true
	IsPublic bool `protobuf:"varint,5,opt,name=is_public,json=isPublic,proto3" json:"is_public"`
	// 对应 dsc_user_id
	DscUserId string `protobuf:"bytes,6,opt,name=dsc_user_id,json=dscUserId,proto3" json:"dsc_user_id"`
	// 有效的截止日期
	ExpireAt int64 `protobuf:"varint,7,opt,name=expire_at,json=expireAt,proto3" json:"expire_at"`
	// 客服账号 - 只有对外公开需要携带客服账号是才需要
	AccountName string `protobuf:"bytes,8,opt,name=account_name,json=accountName,proto3" json:"account_name"`
	// 生成批次 ID
	BatchId              uint64   `protobuf:"varint,9,opt,name=batch_id,json=batchId,proto3" json:"batch_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *SurveyGenLinkReq) Reset()         { *m = SurveyGenLinkReq{} }
func (m *SurveyGenLinkReq) String() string { return proto.CompactTextString(m) }
func (*SurveyGenLinkReq) ProtoMessage()    {}
func (*SurveyGenLinkReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_a40f94eaa8e6ca46, []int{11}
}

func (m *SurveyGenLinkReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SurveyGenLinkReq.Unmarshal(m, b)
}
func (m *SurveyGenLinkReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SurveyGenLinkReq.Marshal(b, m, deterministic)
}
func (m *SurveyGenLinkReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SurveyGenLinkReq.Merge(m, src)
}
func (m *SurveyGenLinkReq) XXX_Size() int {
	return xxx_messageInfo_SurveyGenLinkReq.Size(m)
}
func (m *SurveyGenLinkReq) XXX_DiscardUnknown() {
	xxx_messageInfo_SurveyGenLinkReq.DiscardUnknown(m)
}

var xxx_messageInfo_SurveyGenLinkReq proto.InternalMessageInfo

func (m *SurveyGenLinkReq) GetProject() string {
	if m != nil {
		return m.Project
	}
	return ""
}

func (m *SurveyGenLinkReq) GetUid() int64 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *SurveyGenLinkReq) GetLang() string {
	if m != nil {
		return m.Lang
	}
	return ""
}

func (m *SurveyGenLinkReq) GetSurveyId() int64 {
	if m != nil {
		return m.SurveyId
	}
	return 0
}

func (m *SurveyGenLinkReq) GetIsPublic() bool {
	if m != nil {
		return m.IsPublic
	}
	return false
}

func (m *SurveyGenLinkReq) GetDscUserId() string {
	if m != nil {
		return m.DscUserId
	}
	return ""
}

func (m *SurveyGenLinkReq) GetExpireAt() int64 {
	if m != nil {
		return m.ExpireAt
	}
	return 0
}

func (m *SurveyGenLinkReq) GetAccountName() string {
	if m != nil {
		return m.AccountName
	}
	return ""
}

func (m *SurveyGenLinkReq) GetBatchId() uint64 {
	if m != nil {
		return m.BatchId
	}
	return 0
}

// SurveyGenLinkResp 问卷调查生成链接-响应参数
type SurveyGenLinkResp struct {
	Link                 string   `protobuf:"bytes,1,opt,name=link,proto3" json:"link"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *SurveyGenLinkResp) Reset()         { *m = SurveyGenLinkResp{} }
func (m *SurveyGenLinkResp) String() string { return proto.CompactTextString(m) }
func (*SurveyGenLinkResp) ProtoMessage()    {}
func (*SurveyGenLinkResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_a40f94eaa8e6ca46, []int{12}
}

func (m *SurveyGenLinkResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_SurveyGenLinkResp.Unmarshal(m, b)
}
func (m *SurveyGenLinkResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_SurveyGenLinkResp.Marshal(b, m, deterministic)
}
func (m *SurveyGenLinkResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_SurveyGenLinkResp.Merge(m, src)
}
func (m *SurveyGenLinkResp) XXX_Size() int {
	return xxx_messageInfo_SurveyGenLinkResp.Size(m)
}
func (m *SurveyGenLinkResp) XXX_DiscardUnknown() {
	xxx_messageInfo_SurveyGenLinkResp.DiscardUnknown(m)
}

var xxx_messageInfo_SurveyGenLinkResp proto.InternalMessageInfo

func (m *SurveyGenLinkResp) GetLink() string {
	if m != nil {
		return m.Link
	}
	return ""
}

type DiscordPlayerSatisfactionStatsReq struct {
	// 游戏项目
	Project []string `protobuf:"bytes,1,rep,name=project,proto3" json:"project"`
	// 日期
	Date []string `protobuf:"bytes,2,rep,name=date,proto3" json:"date"`
	// 评价对象
	EvaluationTarget []SurveyQstType `protobuf:"varint,3,rep,packed,name=evaluation_target,json=evaluationTarget,proto3,enum=pb.SurveyQstType" json:"evaluation_target"`
	// 处理人
	Operator []string `protobuf:"bytes,4,rep,name=operator,proto3" json:"operator"`
	// 维护人
	Maintainer []string `protobuf:"bytes,5,rep,name=maintainer,proto3" json:"maintainer"`
	// 报表维度 @gotags: validate:"required,oneof=1 2 3"
	StatType             SurveyStatType `protobuf:"varint,6,opt,name=stat_type,json=statType,proto3,enum=pb.SurveyStatType" json:"stat_type" validate:"required,oneof=1 2 3"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-" gorm:"-"`
	XXX_unrecognized     []byte         `json:"-" gorm:"-"`
	XXX_sizecache        int32          `json:"-" gorm:"-"`
}

func (m *DiscordPlayerSatisfactionStatsReq) Reset()         { *m = DiscordPlayerSatisfactionStatsReq{} }
func (m *DiscordPlayerSatisfactionStatsReq) String() string { return proto.CompactTextString(m) }
func (*DiscordPlayerSatisfactionStatsReq) ProtoMessage()    {}
func (*DiscordPlayerSatisfactionStatsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_a40f94eaa8e6ca46, []int{13}
}

func (m *DiscordPlayerSatisfactionStatsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DiscordPlayerSatisfactionStatsReq.Unmarshal(m, b)
}
func (m *DiscordPlayerSatisfactionStatsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DiscordPlayerSatisfactionStatsReq.Marshal(b, m, deterministic)
}
func (m *DiscordPlayerSatisfactionStatsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DiscordPlayerSatisfactionStatsReq.Merge(m, src)
}
func (m *DiscordPlayerSatisfactionStatsReq) XXX_Size() int {
	return xxx_messageInfo_DiscordPlayerSatisfactionStatsReq.Size(m)
}
func (m *DiscordPlayerSatisfactionStatsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DiscordPlayerSatisfactionStatsReq.DiscardUnknown(m)
}

var xxx_messageInfo_DiscordPlayerSatisfactionStatsReq proto.InternalMessageInfo

func (m *DiscordPlayerSatisfactionStatsReq) GetProject() []string {
	if m != nil {
		return m.Project
	}
	return nil
}

func (m *DiscordPlayerSatisfactionStatsReq) GetDate() []string {
	if m != nil {
		return m.Date
	}
	return nil
}

func (m *DiscordPlayerSatisfactionStatsReq) GetEvaluationTarget() []SurveyQstType {
	if m != nil {
		return m.EvaluationTarget
	}
	return nil
}

func (m *DiscordPlayerSatisfactionStatsReq) GetOperator() []string {
	if m != nil {
		return m.Operator
	}
	return nil
}

func (m *DiscordPlayerSatisfactionStatsReq) GetMaintainer() []string {
	if m != nil {
		return m.Maintainer
	}
	return nil
}

func (m *DiscordPlayerSatisfactionStatsReq) GetStatType() SurveyStatType {
	if m != nil {
		return m.StatType
	}
	return SurveyStatType_UnknownSurveyStatType
}

type DiscordPlayerSatisfactionStatsResp struct {
	Data                 []*DiscordPlayerSatisfactionStatsResp_Stats `protobuf:"bytes,1,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}                                    `json:"-" gorm:"-"`
	XXX_unrecognized     []byte                                      `json:"-" gorm:"-"`
	XXX_sizecache        int32                                       `json:"-" gorm:"-"`
}

func (m *DiscordPlayerSatisfactionStatsResp) Reset()         { *m = DiscordPlayerSatisfactionStatsResp{} }
func (m *DiscordPlayerSatisfactionStatsResp) String() string { return proto.CompactTextString(m) }
func (*DiscordPlayerSatisfactionStatsResp) ProtoMessage()    {}
func (*DiscordPlayerSatisfactionStatsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_a40f94eaa8e6ca46, []int{14}
}

func (m *DiscordPlayerSatisfactionStatsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DiscordPlayerSatisfactionStatsResp.Unmarshal(m, b)
}
func (m *DiscordPlayerSatisfactionStatsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DiscordPlayerSatisfactionStatsResp.Marshal(b, m, deterministic)
}
func (m *DiscordPlayerSatisfactionStatsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DiscordPlayerSatisfactionStatsResp.Merge(m, src)
}
func (m *DiscordPlayerSatisfactionStatsResp) XXX_Size() int {
	return xxx_messageInfo_DiscordPlayerSatisfactionStatsResp.Size(m)
}
func (m *DiscordPlayerSatisfactionStatsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DiscordPlayerSatisfactionStatsResp.DiscardUnknown(m)
}

var xxx_messageInfo_DiscordPlayerSatisfactionStatsResp proto.InternalMessageInfo

func (m *DiscordPlayerSatisfactionStatsResp) GetData() []*DiscordPlayerSatisfactionStatsResp_Stats {
	if m != nil {
		return m.Data
	}
	return nil
}

type DiscordPlayerSatisfactionStatsResp_Stats struct {
	// 日期类型：demo:
	Aa                   string   `protobuf:"bytes,1,opt,name=aa,proto3" json:"aa"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *DiscordPlayerSatisfactionStatsResp_Stats) Reset() {
	*m = DiscordPlayerSatisfactionStatsResp_Stats{}
}
func (m *DiscordPlayerSatisfactionStatsResp_Stats) String() string { return proto.CompactTextString(m) }
func (*DiscordPlayerSatisfactionStatsResp_Stats) ProtoMessage()    {}
func (*DiscordPlayerSatisfactionStatsResp_Stats) Descriptor() ([]byte, []int) {
	return fileDescriptor_a40f94eaa8e6ca46, []int{14, 0}
}

func (m *DiscordPlayerSatisfactionStatsResp_Stats) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DiscordPlayerSatisfactionStatsResp_Stats.Unmarshal(m, b)
}
func (m *DiscordPlayerSatisfactionStatsResp_Stats) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DiscordPlayerSatisfactionStatsResp_Stats.Marshal(b, m, deterministic)
}
func (m *DiscordPlayerSatisfactionStatsResp_Stats) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DiscordPlayerSatisfactionStatsResp_Stats.Merge(m, src)
}
func (m *DiscordPlayerSatisfactionStatsResp_Stats) XXX_Size() int {
	return xxx_messageInfo_DiscordPlayerSatisfactionStatsResp_Stats.Size(m)
}
func (m *DiscordPlayerSatisfactionStatsResp_Stats) XXX_DiscardUnknown() {
	xxx_messageInfo_DiscordPlayerSatisfactionStatsResp_Stats.DiscardUnknown(m)
}

var xxx_messageInfo_DiscordPlayerSatisfactionStatsResp_Stats proto.InternalMessageInfo

func (m *DiscordPlayerSatisfactionStatsResp_Stats) GetAa() string {
	if m != nil {
		return m.Aa
	}
	return ""
}

func init() {
	proto.RegisterType((*SurveyListReq)(nil), "pb.SurveyListReq")
	proto.RegisterType((*SurveyListResp)(nil), "pb.SurveyListResp")
	proto.RegisterType((*SurveyListResp_SurveyInfo)(nil), "pb.SurveyListResp.SurveyInfo")
	proto.RegisterType((*SurveyInfoReq)(nil), "pb.SurveyInfoReq")
	proto.RegisterType((*SurveyInfoResp)(nil), "pb.SurveyInfoResp")
	proto.RegisterType((*DscSurveyPlayerDetail)(nil), "pb.DscSurveyPlayerDetail")
	proto.RegisterType((*SurveyLinkParamDf)(nil), "pb.SurveyLinkParamDf")
	proto.RegisterType((*SurveyLinkParamDf_Attrs)(nil), "pb.SurveyLinkParamDf.Attrs")
	proto.RegisterType((*SurveyEditReq)(nil), "pb.SurveyEditReq")
	proto.RegisterMapType((map[string]string)(nil), "pb.SurveyEditReq.ProductQuestionsEntry")
	proto.RegisterMapType((map[string]string)(nil), "pb.SurveyEditReq.PushContentsEntry")
	proto.RegisterMapType((map[string]string)(nil), "pb.SurveyEditReq.ReasonsEntry")
	proto.RegisterMapType((map[string]string)(nil), "pb.SurveyEditReq.ServiceQuestionsEntry")
	proto.RegisterMapType((map[string]string)(nil), "pb.SurveyEditReq.SurveyTitlesEntry")
	proto.RegisterType((*SurveyEgressConfigResp)(nil), "pb.SurveyEgressConfigResp")
	proto.RegisterType((*SurveyEgressConfigResp_EnumLang)(nil), "pb.SurveyEgressConfigResp.EnumLang")
	proto.RegisterType((*SurveySubmitReq)(nil), "pb.SurveySubmitReq")
	proto.RegisterType((*SurveyTemplateReq)(nil), "pb.SurveyTemplateReq")
	proto.RegisterType((*SurveyTemplateResp)(nil), "pb.SurveyTemplateResp")
	proto.RegisterType((*SurveyGenLinkReq)(nil), "pb.SurveyGenLinkReq")
	proto.RegisterType((*SurveyGenLinkResp)(nil), "pb.SurveyGenLinkResp")
	proto.RegisterType((*DiscordPlayerSatisfactionStatsReq)(nil), "pb.DiscordPlayerSatisfactionStatsReq")
	proto.RegisterType((*DiscordPlayerSatisfactionStatsResp)(nil), "pb.DiscordPlayerSatisfactionStatsResp")
	proto.RegisterType((*DiscordPlayerSatisfactionStatsResp_Stats)(nil), "pb.DiscordPlayerSatisfactionStatsResp.Stats")
}

func init() {
	proto.RegisterFile("survey.proto", fileDescriptor_a40f94eaa8e6ca46)
}

var fileDescriptor_a40f94eaa8e6ca46 = []byte{
	// 1892 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x9c, 0x58, 0xdd, 0x6e, 0x23, 0xb7,
	0x15, 0x86, 0xfe, 0x2c, 0x89, 0x92, 0xbc, 0x6b, 0x36, 0xbb, 0x3b, 0xab, 0xfd, 0x89, 0x77, 0x1c,
	0x67, 0x8d, 0x4d, 0x60, 0xa1, 0x6e, 0x8b, 0xa6, 0x5b, 0x20, 0x8d, 0xb3, 0x6b, 0xb4, 0x06, 0x16,
	0x81, 0x33, 0x76, 0x10, 0xa0, 0x40, 0x31, 0xa0, 0x66, 0x68, 0x99, 0x95, 0xc4, 0xe1, 0x0e, 0x29,
	0x3b, 0x0a, 0x0a, 0x14, 0xe8, 0x4d, 0x7a, 0xdf, 0x8b, 0x5e, 0xf6, 0x0d, 0x7a, 0xdf, 0x57, 0x28,
	0xd0, 0xab, 0xbe, 0x42, 0xaf, 0xfa, 0x0a, 0x05, 0x8a, 0xe2, 0x1c, 0x92, 0xd2, 0x78, 0xa4, 0xdd,
	0xac, 0x7b, 0x25, 0xf2, 0xf0, 0xf0, 0xe3, 0x99, 0x8f, 0xe7, 0x8f, 0x22, 0x5d, 0x3d, 0xcb, 0x2f,
	0xf9, 0x7c, 0x5f, 0xe5, 0x99, 0xc9, 0x68, 0x55, 0x0d, 0xfb, 0x0f, 0x47, 0x59, 0x36, 0x9a, 0xf0,
	0x01, 0x53, 0x62, 0xc0, 0xa4, 0xcc, 0x0c, 0x33, 0x22, 0x93, 0xda, 0x6a, 0xf4, 0xbb, 0x49, 0x36,
	0x9d, 0x66, 0xd2, 0xcd, 0xb6, 0xec, 0xee, 0x98, 0xcb, 0xd9, 0xd4, 0x8a, 0xc2, 0xcf, 0x48, 0xef,
	0x14, 0x85, 0xaf, 0x84, 0x36, 0x11, 0x7f, 0x4d, 0x29, 0xa9, 0x2b, 0x36, 0xe2, 0x41, 0x65, 0xbb,
	0xb2, 0xd7, 0x8b, 0x70, 0x4c, 0x1f, 0x90, 0x36, 0xfc, 0xc6, 0x5a, 0x7c, 0xcb, 0x83, 0x2a, 0x2e,
	0xb4, 0x40, 0x70, 0x2a, 0xbe, 0xe5, 0xe1, 0xdf, 0xaa, 0x64, 0xb3, 0x08, 0xa1, 0x15, 0x7d, 0x42,
	0xba, 0xc9, 0x2c, 0xcf, 0xb9, 0x34, 0x71, 0x01, 0xab, 0xe3, 0x64, 0x27, 0x00, 0x79, 0x9f, 0xb4,
	0x14, 0xcf, 0xed, 0xb2, 0x45, 0x6c, 0x2a, 0x9e, 0xe3, 0xd2, 0x7b, 0xa4, 0x61, 0x32, 0xc3, 0x26,
	0x41, 0x0d, 0xe5, 0x76, 0x42, 0x7f, 0x48, 0xea, 0x29, 0x33, 0x2c, 0xa8, 0x6f, 0xd7, 0xf6, 0x3a,
	0x07, 0x8f, 0xf6, 0xd5, 0x70, 0xff, 0xfa, 0xa9, 0x6e, 0x7a, 0x2c, 0xcf, 0xb3, 0x08, 0x55, 0xfb,
	0x7f, 0xa9, 0x10, 0xb2, 0x14, 0xd2, 0x4d, 0x52, 0x15, 0x29, 0xda, 0x52, 0x8f, 0xaa, 0x22, 0xa5,
	0x01, 0x69, 0xaa, 0x3c, 0xfb, 0x2d, 0x4f, 0x0c, 0x5a, 0xd0, 0x8e, 0xfc, 0x94, 0x3e, 0x22, 0x64,
	0xa6, 0x52, 0x66, 0x78, 0x1a, 0x33, 0x83, 0x66, 0xb4, 0xa3, 0xb6, 0x93, 0x1c, 0x1a, 0x00, 0xca,
	0x54, 0x50, 0x47, 0x71, 0x35, 0x53, 0xf4, 0x2e, 0xd9, 0xe0, 0x92, 0x0d, 0x27, 0x3c, 0x68, 0x6c,
	0x57, 0xf6, 0x5a, 0x91, 0x9b, 0xd1, 0x6d, 0xd2, 0x4d, 0x98, 0x8c, 0x47, 0x5c, 0xc6, 0x13, 0x21,
	0xc7, 0xc1, 0x06, 0xae, 0x92, 0x84, 0xc9, 0x5f, 0x72, 0xf9, 0x4a, 0xc8, 0x71, 0xf8, 0xbe, 0x67,
	0x1f, 0xad, 0xe6, 0xaf, 0xcb, 0x36, 0x86, 0x3f, 0xf5, 0xdc, 0x5a, 0x05, 0xad, 0xe8, 0xae, 0xe3,
	0x01, 0x74, 0x3a, 0x07, 0x5b, 0x4b, 0x1e, 0x8e, 0x52, 0x01, 0x17, 0x68, 0xbf, 0x3d, 0xfc, 0x47,
	0x95, 0xdc, 0x79, 0xa9, 0x13, 0xbb, 0x74, 0x32, 0x61, 0x73, 0x9e, 0xbf, 0xe4, 0x86, 0x89, 0x09,
	0xfd, 0x90, 0xdc, 0x52, 0xb9, 0xb8, 0x8c, 0x93, 0x0b, 0x26, 0x25, 0x9f, 0xc4, 0xee, 0xbc, 0x76,
	0xd4, 0x03, 0xf1, 0x0b, 0x2b, 0x3d, 0x4e, 0xe9, 0x1d, 0xb2, 0xc1, 0x94, 0x82, 0x65, 0xcb, 0x4e,
	0x83, 0x29, 0x75, 0x9c, 0xd2, 0xc7, 0xa4, 0x93, 0xea, 0x24, 0x9e, 0x69, 0x9e, 0xc3, 0x9a, 0x23,
	0x27, 0xd5, 0xc9, 0x57, 0x9a, 0xe7, 0xc7, 0x29, 0x0d, 0x49, 0x0f, 0xd6, 0xa5, 0x48, 0xc6, 0xb1,
	0x64, 0x53, 0xee, 0x78, 0x82, 0x4d, 0x5f, 0x88, 0x64, 0xfc, 0x05, 0x9b, 0x72, 0x7a, 0x9b, 0xd4,
	0x66, 0x22, 0x45, 0x3e, 0xea, 0x11, 0x0c, 0x81, 0x71, 0x96, 0x24, 0xd9, 0x4c, 0x1a, 0x00, 0x6d,
	0x5a, 0x50, 0x27, 0x39, 0x4e, 0xc1, 0x29, 0x27, 0x4c, 0x8e, 0x82, 0x16, 0x2e, 0xe0, 0x98, 0x3e,
	0x26, 0x64, 0xca, 0x84, 0x34, 0x4c, 0x48, 0x9e, 0x07, 0x6d, 0x5c, 0x29, 0x48, 0x60, 0x5d, 0xe5,
	0x59, 0xc2, 0xb5, 0xce, 0x72, 0x1d, 0x90, 0xed, 0x1a, 0xac, 0x2f, 0x25, 0xf4, 0x63, 0x42, 0x27,
	0x4c, 0x9b, 0x38, 0xe7, 0x6a, 0x32, 0x8f, 0x35, 0xcf, 0x2f, 0x45, 0xc2, 0x83, 0x0e, 0xe2, 0xdc,
	0x86, 0x95, 0x08, 0x16, 0x4e, 0xad, 0x3c, 0xfc, 0x4f, 0x8d, 0x6c, 0x79, 0x7f, 0x93, 0xe3, 0x13,
	0x96, 0xb3, 0xe9, 0xcb, 0x73, 0x08, 0x0c, 0x17, 0x52, 0x8b, 0x5b, 0x6b, 0x59, 0xc1, 0xf1, 0xdb,
	0xfc, 0xcb, 0x7f, 0x4e, 0xad, 0xf0, 0x39, 0x8e, 0x13, 0x60, 0xab, 0x66, 0x39, 0x09, 0x48, 0xd3,
	0x31, 0x80, 0x4c, 0xb5, 0x23, 0x3f, 0xa5, 0x9f, 0x92, 0xee, 0x34, 0x9b, 0x42, 0x78, 0x31, 0x63,
	0x72, 0x8d, 0x7c, 0x75, 0x0e, 0x1e, 0x14, 0x63, 0x62, 0x61, 0xe3, 0xfe, 0x21, 0xa8, 0x44, 0x1d,
	0xbb, 0x01, 0x27, 0xfd, 0xbf, 0x56, 0x49, 0x03, 0x47, 0x25, 0x12, 0xab, 0x2b, 0x24, 0xae, 0x27,
	0xa9, 0xb6, 0x9e, 0xa4, 0x12, 0xe5, 0xf5, 0x15, 0xca, 0x4b, 0xbe, 0xd3, 0x78, 0x83, 0xef, 0xe0,
	0x3a, 0xfa, 0xce, 0xc6, 0xc2, 0x77, 0x40, 0xa3, 0xe8, 0x3b, 0xcd, 0x37, 0xf9, 0x4e, 0xab, 0xec,
	0x3b, 0x1f, 0x90, 0x4d, 0x00, 0x2d, 0xb8, 0xbb, 0xf5, 0x95, 0x6e, 0xaa, 0x93, 0xa5, 0xb7, 0x3f,
	0x24, 0x04, 0xb4, 0x86, 0x19, 0x82, 0x10, 0xd4, 0x68, 0xa5, 0x3a, 0xf9, 0x3c, 0x33, 0xc7, 0x69,
	0xf8, 0xef, 0xa6, 0x0f, 0x54, 0x17, 0x65, 0x37, 0x48, 0x26, 0x07, 0x84, 0xa8, 0x99, 0xbe, 0x88,
	0x93, 0x79, 0x32, 0xb1, 0xd4, 0x6d, 0x1e, 0xfc, 0x60, 0x79, 0x55, 0x27, 0x33, 0x7d, 0xf1, 0x02,
	0x96, 0xa2, 0xb6, 0xf2, 0x43, 0x4c, 0xb8, 0xb0, 0xe7, 0x8a, 0xf3, 0x31, 0xba, 0x44, 0x3d, 0x6a,
	0x81, 0xe0, 0x6b, 0xce, 0xc7, 0x8b, 0x45, 0x23, 0xa6, 0x36, 0xe3, 0xb8, 0xc5, 0x33, 0x31, 0xe5,
	0x40, 0xe1, 0x62, 0x31, 0xbe, 0xe2, 0xc3, 0xa0, 0x67, 0x29, 0xf4, 0x0a, 0x5f, 0xf3, 0x21, 0xdd,
	0x25, 0x9b, 0xfc, 0xfc, 0x9c, 0x27, 0x46, 0x5c, 0x72, 0x8b, 0x62, 0x79, 0xee, 0x2d, 0xa4, 0x08,
	0xf5, 0x63, 0xd2, 0xe1, 0xdf, 0x28, 0x91, 0x3b, 0x9d, 0x66, 0xd9, 0xf2, 0x23, 0xaf, 0x1d, 0x11,
	0xab, 0x87, 0xbb, 0x7e, 0x45, 0x7a, 0x2e, 0x24, 0x8c, 0x30, 0x13, 0xae, 0x83, 0x16, 0x26, 0xec,
	0x9d, 0x95, 0x44, 0xe5, 0x66, 0x67, 0xa8, 0x75, 0x24, 0x4d, 0x3e, 0x8f, 0x5c, 0x75, 0xb3, 0x22,
	0x40, 0xb2, 0xc4, 0x65, 0xd2, 0x70, 0x69, 0x74, 0xd0, 0x7e, 0x13, 0x12, 0x72, 0xe8, 0xb4, 0x1c,
	0x92, 0x2a, 0x88, 0xe8, 0x19, 0xd9, 0x52, 0x79, 0x96, 0xce, 0x12, 0x13, 0xbf, 0x9e, 0x71, 0x8d,
	0x05, 0x12, 0x33, 0x42, 0xe7, 0xe0, 0xe9, 0x1a, 0x34, 0xab, 0xfa, 0xa5, 0xd7, 0xb4, 0x88, 0xb7,
	0x55, 0x49, 0x0c, 0xa8, 0x2e, 0x20, 0x0a, 0xa8, 0x9d, 0x37, 0xa1, 0xba, 0x18, 0x29, 0xa3, 0xea,
	0x92, 0x98, 0x7e, 0x42, 0x9a, 0x39, 0x67, 0x1a, 0xb0, 0xba, 0x88, 0xf5, 0x78, 0x15, 0x2b, 0xb2,
	0x0a, 0x16, 0xc2, 0xab, 0xf7, 0x7f, 0xe1, 0x33, 0x54, 0x81, 0x52, 0x08, 0x97, 0x31, 0x9f, 0xbb,
	0x0c, 0x0f, 0x43, 0x28, 0xaf, 0x97, 0x6c, 0x32, 0xe3, 0x3e, 0xad, 0xe3, 0xe4, 0x79, 0xf5, 0x93,
	0x0a, 0x00, 0xac, 0x30, 0x79, 0x23, 0x80, 0x17, 0xe4, 0xce, 0x5a, 0xf2, 0x6e, 0x0a, 0xb2, 0x96,
	0xab, 0x1b, 0x81, 0x3c, 0x27, 0xdd, 0x22, 0x49, 0x37, 0xd9, 0x1b, 0x7e, 0x57, 0x21, 0x77, 0x1d,
	0xdf, 0xa3, 0x9c, 0x6b, 0xfd, 0x22, 0x93, 0xe7, 0x62, 0x84, 0xc5, 0xf7, 0x67, 0xa4, 0x01, 0xc9,
	0x5a, 0x07, 0x95, 0x15, 0x57, 0x2c, 0xa9, 0xee, 0x1f, 0xc9, 0xd9, 0xf4, 0x15, 0x93, 0xa3, 0xc8,
	0xee, 0xe8, 0x1f, 0x90, 0x96, 0x17, 0x41, 0xfe, 0x4f, 0xb2, 0x94, 0x3b, 0x73, 0x70, 0x0c, 0x32,
	0x4c, 0x79, 0xd6, 0x1c, 0x1c, 0x87, 0x7f, 0xae, 0x92, 0x5b, 0x16, 0xfe, 0x74, 0x36, 0x9c, 0xda,
	0xc4, 0xf3, 0xc4, 0xf7, 0x80, 0xb1, 0xc9, 0xc6, 0x5c, 0x3a, 0x8c, 0x8e, 0x8b, 0x1c, 0x10, 0xd1,
	0x7b, 0xa4, 0x69, 0x74, 0x9c, 0x33, 0x39, 0x46, 0xb4, 0x5a, 0xb4, 0x61, 0x74, 0xc4, 0xe4, 0x98,
	0xf6, 0x49, 0x0b, 0x8c, 0x99, 0x41, 0xd3, 0x65, 0x73, 0xf8, 0x62, 0x5e, 0xac, 0xc9, 0xae, 0xfe,
	0xec, 0x92, 0x4d, 0x1f, 0x35, 0x39, 0x33, 0x42, 0x8e, 0x30, 0x05, 0x34, 0xa0, 0x4f, 0x40, 0x69,
	0x84, 0xc2, 0xa2, 0x1a, 0x93, 0xfa, 0x8a, 0xe7, 0x2e, 0x05, 0x7b, 0xb5, 0x43, 0x14, 0x82, 0x9a,
	0x8f, 0x16, 0x87, 0xd6, 0xb6, 0x68, 0x4e, 0xba, 0x44, 0xf3, 0x6a, 0x0e, 0xcd, 0xe6, 0x62, 0xaf,
	0x66, 0xd1, 0xc2, 0x64, 0xe1, 0xeb, 0x7c, 0xaa, 0x26, 0xcc, 0xf0, 0x77, 0xa4, 0xc6, 0x57, 0xde,
	0x7a, 0xa1, 0xf2, 0x16, 0xe8, 0x6a, 0x14, 0xe9, 0x0a, 0xff, 0x5e, 0x23, 0xb4, 0x7c, 0x8a, 0x56,
	0x6f, 0x2f, 0xfa, 0x65, 0x1b, 0xaa, 0xab, 0x36, 0x14, 0x4a, 0x45, 0x6d, 0x7d, 0x5f, 0x50, 0xb4,
	0xee, 0x43, 0x72, 0x4b, 0xe8, 0x58, 0x5f, 0x64, 0x57, 0xb1, 0x23, 0xd4, 0x75, 0x99, 0x3d, 0xa1,
	0x4f, 0x2f, 0xb2, 0x2b, 0x17, 0x70, 0x45, 0x3d, 0x5f, 0xa6, 0x37, 0x8a, 0x7a, 0xbe, 0x46, 0xef,
	0x94, 0xf3, 0xb3, 0x6d, 0xb6, 0xae, 0xa7, 0xde, 0x9d, 0x72, 0xea, 0xb5, 0x57, 0x7a, 0x3d, 0xab,
	0x7e, 0xb4, 0x2e, 0xab, 0xda, 0xda, 0xba, 0x9a, 0x2c, 0x3f, 0x5a, 0x97, 0x2c, 0xed, 0xd5, 0xae,
	0xe6, 0xc0, 0x60, 0x99, 0x03, 0x6d, 0x3f, 0xe6, 0xa7, 0x50, 0xeb, 0xa1, 0xa5, 0xd6, 0x18, 0x0e,
	0x41, 0x17, 0x3f, 0xb0, 0x9d, 0x30, 0x69, 0xe3, 0x03, 0x5e, 0x15, 0x92, 0xf3, 0x34, 0x06, 0x4f,
	0xee, 0xe1, 0x62, 0x13, 0xe6, 0x5f, 0x89, 0x34, 0xfc, 0xae, 0x4a, 0x6e, 0xdb, 0xcb, 0x74, 0xcd,
	0x37, 0x78, 0x4c, 0xe1, 0x2a, 0x2a, 0xd7, 0xaf, 0xc2, 0x85, 0x43, 0x75, 0x19, 0x0e, 0xeb, 0x9a,
	0xb6, 0x6b, 0xae, 0x60, 0x5b, 0xb7, 0xa5, 0x2b, 0x3c, 0x20, 0x6d, 0xa1, 0x63, 0x35, 0x1b, 0x4e,
	0x44, 0xe2, 0xee, 0xac, 0x25, 0xf4, 0x09, 0xce, 0xcb, 0xad, 0xd0, 0x46, 0xb9, 0x15, 0x7a, 0x40,
	0xda, 0xae, 0xf8, 0x32, 0x83, 0x57, 0x54, 0x8b, 0x5a, 0x56, 0x70, 0x68, 0xc0, 0xc9, 0x7c, 0xc7,
	0x83, 0x39, 0xc3, 0xde, 0x4e, 0xc7, 0xc9, 0xb0, 0x4d, 0xba, 0x4f, 0x5a, 0x43, 0x66, 0x92, 0x0b,
	0xdf, 0xef, 0xd4, 0xa3, 0x26, 0xce, 0x8f, 0xd3, 0xf0, 0xa9, 0x8f, 0x9d, 0x05, 0x11, 0x5a, 0xe1,
	0xd7, 0xc1, 0x1b, 0xc5, 0xa5, 0x24, 0x18, 0x87, 0xff, 0xad, 0x90, 0x27, 0x2f, 0x85, 0x4e, 0xb2,
	0x3c, 0xb5, 0x2f, 0x88, 0x53, 0x66, 0x84, 0x3e, 0x67, 0x09, 0xdc, 0xd2, 0xa9, 0x61, 0x46, 0xaf,
	0x70, 0x58, 0x2b, 0xb9, 0x33, 0x3c, 0x99, 0x82, 0x2a, 0x8a, 0x71, 0x4c, 0x3f, 0x25, 0x5b, 0x1c,
	0x52, 0x2d, 0xbe, 0x52, 0x63, 0xc3, 0xf2, 0x11, 0x87, 0x30, 0xa8, 0xed, 0x6d, 0x16, 0xdf, 0x32,
	0x5f, 0x6a, 0x73, 0x36, 0x57, 0x3c, 0xba, 0xbd, 0xd4, 0x3d, 0x43, 0x55, 0x48, 0x61, 0x99, 0xe2,
	0x39, 0x33, 0x59, 0xee, 0x1a, 0xcc, 0xc5, 0xbc, 0xd4, 0xcc, 0x36, 0x6c, 0xfb, 0x59, 0x68, 0x66,
	0x07, 0xa4, 0xad, 0x0d, 0x33, 0xb1, 0x99, 0x2b, 0x1b, 0x1c, 0x9b, 0x07, 0x74, 0x79, 0x26, 0x7c,
	0x10, 0x1e, 0xda, 0xd2, 0x6e, 0x14, 0xfe, 0x9e, 0x84, 0xdf, 0xf7, 0xfd, 0x5a, 0xd1, 0xcf, 0x16,
	0x2f, 0x32, 0xa8, 0x09, 0x1f, 0x03, 0xe2, 0xf7, 0xef, 0xda, 0xb7, 0x23, 0xfb, 0x50, 0xbd, 0x47,
	0x1a, 0x38, 0x85, 0xae, 0x92, 0x31, 0x77, 0x07, 0x55, 0xc6, 0x0e, 0xfe, 0xd8, 0x24, 0x6d, 0x6b,
	0xdd, 0xa1, 0x12, 0xf4, 0x37, 0xde, 0x83, 0x6d, 0x99, 0x81, 0x87, 0x2f, 0xdd, 0x2a, 0x3f, 0x84,
	0x5f, 0xf7, 0xe9, 0xea, 0xdb, 0x38, 0x0c, 0xff, 0xf0, 0xcf, 0x7f, 0xfd, 0xa9, 0xfa, 0x30, 0xbc,
	0x87, 0xff, 0x13, 0x38, 0x9f, 0x4d, 0x10, 0x66, 0x30, 0x11, 0xda, 0x3c, 0xaf, 0x3c, 0xa3, 0x67,
	0xd7, 0xe1, 0xa1, 0xd9, 0xa0, 0xab, 0xef, 0xcb, 0x7e, 0x1b, 0x44, 0x47, 0x53, 0x65, 0xe6, 0x6f,
	0x45, 0xe5, 0xa9, 0x40, 0xd4, 0x92, 0xd1, 0xf8, 0x12, 0x2f, 0xa0, 0xba, 0x87, 0x6f, 0xd1, 0x68,
	0xff, 0xd4, 0x7d, 0x2b, 0xbc, 0x90, 0xe7, 0x99, 0x35, 0x9a, 0x5e, 0x33, 0xda, 0xbe, 0xbc, 0x7b,
	0x68, 0x23, 0x8e, 0x4b, 0x26, 0x7f, 0x80, 0x98, 0x8f, 0xc3, 0xfb, 0xeb, 0x4c, 0xc6, 0x0d, 0x80,
	0xca, 0x7d, 0xbb, 0xef, 0x42, 0x84, 0xbe, 0xb7, 0x34, 0x6f, 0x99, 0x3e, 0xfa, 0x77, 0xd6, 0x48,
	0xb5, 0x0a, 0x9f, 0xe2, 0x19, 0x4f, 0xc2, 0x87, 0x6b, 0xce, 0xf0, 0x7f, 0x06, 0x68, 0x38, 0x26,
	0x23, 0x9d, 0xa5, 0xef, 0x69, 0xba, 0xfb, 0x2e, 0xae, 0x73, 0xed, 0x6b, 0xf6, 0xf1, 0xa4, 0xbd,
	0x70, 0xa7, 0x70, 0xd2, 0x40, 0x17, 0x36, 0x0d, 0x20, 0xe8, 0x06, 0xe0, 0xd0, 0x78, 0xe0, 0xef,
	0x7c, 0xe8, 0x23, 0xd8, 0xd1, 0x37, 0x2a, 0xcb, 0xcd, 0xff, 0x71, 0xec, 0x4f, 0xf0, 0xd8, 0x41,
	0xf8, 0xec, 0x1d, 0x8e, 0x8d, 0x39, 0x9e, 0x02, 0xa7, 0x9f, 0xfb, 0x3f, 0x33, 0x7c, 0x39, 0xa5,
	0x05, 0x02, 0x0b, 0x85, 0xbc, 0x7f, 0x77, 0x9d, 0x58, 0xab, 0x70, 0x17, 0xcf, 0x7d, 0x3f, 0xec,
	0x0f, 0x38, 0xb6, 0x5b, 0xb1, 0x3b, 0x3a, 0x4d, 0x06, 0xc6, 0xe9, 0xc1, 0x39, 0xa7, 0xa4, 0x5b,
	0xec, 0x9a, 0x68, 0xe1, 0xcd, 0xb2, 0xe8, 0xa3, 0x8a, 0x9f, 0xb3, 0x83, 0xb0, 0x8f, 0xc2, 0x60,
	0x15, 0xd6, 0x16, 0x9a, 0xe7, 0x95, 0x67, 0x9f, 0x6f, 0xfc, 0xba, 0xbe, 0xff, 0x73, 0x35, 0x1c,
	0x6e, 0xe0, 0xff, 0x66, 0x3f, 0xfa, 0x5f, 0x00, 0x00, 0x00, 0xff, 0xff, 0xf5, 0xad, 0x4e, 0x14,
	0x8a, 0x13, 0x00, 0x00,
}
