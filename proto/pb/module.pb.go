// Code generated by protoc-gen-go. DO NOT EDIT.
// source: module.proto

package pb

import (
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

// ModuleSaveReq 新增模板
type ModuleSaveReq struct {
	// @gotags: validate:"required"
	ModuleName string `protobuf:"bytes,1,opt,name=module_name,json=moduleName,proto3" json:"module_name" validate:"required"`
	// @gotags: validate:"required"
	GameProject string `protobuf:"bytes,2,opt,name=game_project,json=gameProject,proto3" json:"game_project" validate:"required"`
	// @gotags: validate:"required"
	Content string `protobuf:"bytes,3,opt,name=content,proto3" json:"content" validate:"required"`
	// 模板分类id @gotags: validate:"required"
	CatId                uint32   `protobuf:"varint,4,opt,name=cat_id,json=catId,proto3" json:"cat_id" validate:"required"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *ModuleSaveReq) Reset()         { *m = ModuleSaveReq{} }
func (m *ModuleSaveReq) String() string { return proto.CompactTextString(m) }
func (*ModuleSaveReq) ProtoMessage()    {}
func (*ModuleSaveReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_ae7704718fb7daeb, []int{0}
}

func (m *ModuleSaveReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ModuleSaveReq.Unmarshal(m, b)
}
func (m *ModuleSaveReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ModuleSaveReq.Marshal(b, m, deterministic)
}
func (m *ModuleSaveReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ModuleSaveReq.Merge(m, src)
}
func (m *ModuleSaveReq) XXX_Size() int {
	return xxx_messageInfo_ModuleSaveReq.Size(m)
}
func (m *ModuleSaveReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ModuleSaveReq.DiscardUnknown(m)
}

var xxx_messageInfo_ModuleSaveReq proto.InternalMessageInfo

func (m *ModuleSaveReq) GetModuleName() string {
	if m != nil {
		return m.ModuleName
	}
	return ""
}

func (m *ModuleSaveReq) GetGameProject() string {
	if m != nil {
		return m.GameProject
	}
	return ""
}

func (m *ModuleSaveReq) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *ModuleSaveReq) GetCatId() uint32 {
	if m != nil {
		return m.CatId
	}
	return 0
}

// ModuleEditReq 编辑模板
type ModuleEditReq struct {
	// 模板id @gotags: validate:"required"
	Id          uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id" validate:"required"`
	ModuleName  string `protobuf:"bytes,2,opt,name=module_name,json=moduleName,proto3" json:"module_name"`
	GameProject string `protobuf:"bytes,3,opt,name=game_project,json=gameProject,proto3" json:"game_project"`
	Content     string `protobuf:"bytes,4,opt,name=content,proto3" json:"content"`
	// 启用或禁用模板
	Enable uint32 `protobuf:"varint,5,opt,name=enable,proto3" json:"enable"`
	// 模板分类id
	CatId                uint32   `protobuf:"varint,6,opt,name=cat_id,json=catId,proto3" json:"cat_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *ModuleEditReq) Reset()         { *m = ModuleEditReq{} }
func (m *ModuleEditReq) String() string { return proto.CompactTextString(m) }
func (*ModuleEditReq) ProtoMessage()    {}
func (*ModuleEditReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_ae7704718fb7daeb, []int{1}
}

func (m *ModuleEditReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ModuleEditReq.Unmarshal(m, b)
}
func (m *ModuleEditReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ModuleEditReq.Marshal(b, m, deterministic)
}
func (m *ModuleEditReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ModuleEditReq.Merge(m, src)
}
func (m *ModuleEditReq) XXX_Size() int {
	return xxx_messageInfo_ModuleEditReq.Size(m)
}
func (m *ModuleEditReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ModuleEditReq.DiscardUnknown(m)
}

var xxx_messageInfo_ModuleEditReq proto.InternalMessageInfo

func (m *ModuleEditReq) GetId() uint64 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *ModuleEditReq) GetModuleName() string {
	if m != nil {
		return m.ModuleName
	}
	return ""
}

func (m *ModuleEditReq) GetGameProject() string {
	if m != nil {
		return m.GameProject
	}
	return ""
}

func (m *ModuleEditReq) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *ModuleEditReq) GetEnable() uint32 {
	if m != nil {
		return m.Enable
	}
	return 0
}

func (m *ModuleEditReq) GetCatId() uint32 {
	if m != nil {
		return m.CatId
	}
	return 0
}

// ModuleEditReq 删除模版
type ModuleDelReq struct {
	// 模板id @gotags: validate:"required"
	Id                   uint64   `protobuf:"varint,1,opt,name=id,proto3" json:"id" validate:"required"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *ModuleDelReq) Reset()         { *m = ModuleDelReq{} }
func (m *ModuleDelReq) String() string { return proto.CompactTextString(m) }
func (*ModuleDelReq) ProtoMessage()    {}
func (*ModuleDelReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_ae7704718fb7daeb, []int{2}
}

func (m *ModuleDelReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ModuleDelReq.Unmarshal(m, b)
}
func (m *ModuleDelReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ModuleDelReq.Marshal(b, m, deterministic)
}
func (m *ModuleDelReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ModuleDelReq.Merge(m, src)
}
func (m *ModuleDelReq) XXX_Size() int {
	return xxx_messageInfo_ModuleDelReq.Size(m)
}
func (m *ModuleDelReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ModuleDelReq.DiscardUnknown(m)
}

var xxx_messageInfo_ModuleDelReq proto.InternalMessageInfo

func (m *ModuleDelReq) GetId() uint64 {
	if m != nil {
		return m.Id
	}
	return 0
}

// ModuleListReq 模板查询请求参数
type ModuleListReq struct {
	// 模板名称,支持模糊搜索
	ModuleName string `protobuf:"bytes,1,opt,name=module_name,json=moduleName,proto3" json:"module_name"`
	// 模板内容,支持模糊搜索
	Content string `protobuf:"bytes,2,opt,name=content,proto3" json:"content"`
	// 游戏, 支持多选
	GameProject string `protobuf:"bytes,3,opt,name=game_project,json=gameProject,proto3" json:"game_project"`
	Enable      uint32 `protobuf:"varint,4,opt,name=enable,proto3" json:"enable"`
	Page        uint32 `protobuf:"varint,5,opt,name=page,proto3" json:"page"`
	PageSize    uint32 `protobuf:"varint,6,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	// 模版分类
	CatId                []uint32 `protobuf:"varint,7,rep,packed,name=cat_id,json=catId,proto3" json:"cat_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *ModuleListReq) Reset()         { *m = ModuleListReq{} }
func (m *ModuleListReq) String() string { return proto.CompactTextString(m) }
func (*ModuleListReq) ProtoMessage()    {}
func (*ModuleListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_ae7704718fb7daeb, []int{3}
}

func (m *ModuleListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ModuleListReq.Unmarshal(m, b)
}
func (m *ModuleListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ModuleListReq.Marshal(b, m, deterministic)
}
func (m *ModuleListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ModuleListReq.Merge(m, src)
}
func (m *ModuleListReq) XXX_Size() int {
	return xxx_messageInfo_ModuleListReq.Size(m)
}
func (m *ModuleListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ModuleListReq.DiscardUnknown(m)
}

var xxx_messageInfo_ModuleListReq proto.InternalMessageInfo

func (m *ModuleListReq) GetModuleName() string {
	if m != nil {
		return m.ModuleName
	}
	return ""
}

func (m *ModuleListReq) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *ModuleListReq) GetGameProject() string {
	if m != nil {
		return m.GameProject
	}
	return ""
}

func (m *ModuleListReq) GetEnable() uint32 {
	if m != nil {
		return m.Enable
	}
	return 0
}

func (m *ModuleListReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *ModuleListReq) GetPageSize() uint32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *ModuleListReq) GetCatId() []uint32 {
	if m != nil {
		return m.CatId
	}
	return nil
}

// ModuleListResp 新工单池响应参数
type ModuleListResp struct {
	CurrentPage          uint32                       `protobuf:"varint,1,opt,name=current_page,json=currentPage,proto3" json:"current_page"`
	PerPage              uint32                       `protobuf:"varint,2,opt,name=per_page,json=perPage,proto3" json:"per_page"`
	Total                uint32                       `protobuf:"varint,3,opt,name=total,proto3" json:"total"`
	Data                 []*ModuleListResp_ModuleInfo `protobuf:"bytes,4,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}                     `json:"-" gorm:"-"`
	XXX_unrecognized     []byte                       `json:"-" gorm:"-"`
	XXX_sizecache        int32                        `json:"-" gorm:"-"`
}

func (m *ModuleListResp) Reset()         { *m = ModuleListResp{} }
func (m *ModuleListResp) String() string { return proto.CompactTextString(m) }
func (*ModuleListResp) ProtoMessage()    {}
func (*ModuleListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_ae7704718fb7daeb, []int{4}
}

func (m *ModuleListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ModuleListResp.Unmarshal(m, b)
}
func (m *ModuleListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ModuleListResp.Marshal(b, m, deterministic)
}
func (m *ModuleListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ModuleListResp.Merge(m, src)
}
func (m *ModuleListResp) XXX_Size() int {
	return xxx_messageInfo_ModuleListResp.Size(m)
}
func (m *ModuleListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ModuleListResp.DiscardUnknown(m)
}

var xxx_messageInfo_ModuleListResp proto.InternalMessageInfo

func (m *ModuleListResp) GetCurrentPage() uint32 {
	if m != nil {
		return m.CurrentPage
	}
	return 0
}

func (m *ModuleListResp) GetPerPage() uint32 {
	if m != nil {
		return m.PerPage
	}
	return 0
}

func (m *ModuleListResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *ModuleListResp) GetData() []*ModuleListResp_ModuleInfo {
	if m != nil {
		return m.Data
	}
	return nil
}

type ModuleListResp_ModuleInfo struct {
	// 模板id
	Id uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	// 模板名称
	ModuleName string `protobuf:"bytes,2,opt,name=module_name,json=moduleName,proto3" json:"module_name"`
	// 模板内容
	Content string `protobuf:"bytes,3,opt,name=content,proto3" json:"content"`
	// 关联游戏
	GameProject string `protobuf:"bytes,4,opt,name=game_project,json=gameProject,proto3" json:"game_project"`
	// 操作人
	Operator string `protobuf:"bytes,5,opt,name=operator,proto3" json:"operator"`
	// 状态 1启用 2禁用
	Enable uint32 `protobuf:"varint,6,opt,name=enable,proto3" json:"enable"`
	// 操作时间
	UpdateTime string `protobuf:"bytes,7,opt,name=update_time,json=updateTime,proto3" json:"update_time"`
	// 模板分类名称
	Category string `protobuf:"bytes,8,opt,name=category,proto3" json:"category"`
	// 模版分类id
	CatId                uint32   `protobuf:"varint,9,opt,name=cat_id,json=catId,proto3" json:"cat_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *ModuleListResp_ModuleInfo) Reset()         { *m = ModuleListResp_ModuleInfo{} }
func (m *ModuleListResp_ModuleInfo) String() string { return proto.CompactTextString(m) }
func (*ModuleListResp_ModuleInfo) ProtoMessage()    {}
func (*ModuleListResp_ModuleInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_ae7704718fb7daeb, []int{4, 0}
}

func (m *ModuleListResp_ModuleInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ModuleListResp_ModuleInfo.Unmarshal(m, b)
}
func (m *ModuleListResp_ModuleInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ModuleListResp_ModuleInfo.Marshal(b, m, deterministic)
}
func (m *ModuleListResp_ModuleInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ModuleListResp_ModuleInfo.Merge(m, src)
}
func (m *ModuleListResp_ModuleInfo) XXX_Size() int {
	return xxx_messageInfo_ModuleListResp_ModuleInfo.Size(m)
}
func (m *ModuleListResp_ModuleInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_ModuleListResp_ModuleInfo.DiscardUnknown(m)
}

var xxx_messageInfo_ModuleListResp_ModuleInfo proto.InternalMessageInfo

func (m *ModuleListResp_ModuleInfo) GetId() uint64 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *ModuleListResp_ModuleInfo) GetModuleName() string {
	if m != nil {
		return m.ModuleName
	}
	return ""
}

func (m *ModuleListResp_ModuleInfo) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *ModuleListResp_ModuleInfo) GetGameProject() string {
	if m != nil {
		return m.GameProject
	}
	return ""
}

func (m *ModuleListResp_ModuleInfo) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

func (m *ModuleListResp_ModuleInfo) GetEnable() uint32 {
	if m != nil {
		return m.Enable
	}
	return 0
}

func (m *ModuleListResp_ModuleInfo) GetUpdateTime() string {
	if m != nil {
		return m.UpdateTime
	}
	return ""
}

func (m *ModuleListResp_ModuleInfo) GetCategory() string {
	if m != nil {
		return m.Category
	}
	return ""
}

func (m *ModuleListResp_ModuleInfo) GetCatId() uint32 {
	if m != nil {
		return m.CatId
	}
	return 0
}

type ModuleOptsReq struct {
	// 项目 @gotags: validate:"required"
	Project              string   `protobuf:"bytes,1,opt,name=project,proto3" json:"project" validate:"required"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *ModuleOptsReq) Reset()         { *m = ModuleOptsReq{} }
func (m *ModuleOptsReq) String() string { return proto.CompactTextString(m) }
func (*ModuleOptsReq) ProtoMessage()    {}
func (*ModuleOptsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_ae7704718fb7daeb, []int{5}
}

func (m *ModuleOptsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ModuleOptsReq.Unmarshal(m, b)
}
func (m *ModuleOptsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ModuleOptsReq.Marshal(b, m, deterministic)
}
func (m *ModuleOptsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ModuleOptsReq.Merge(m, src)
}
func (m *ModuleOptsReq) XXX_Size() int {
	return xxx_messageInfo_ModuleOptsReq.Size(m)
}
func (m *ModuleOptsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ModuleOptsReq.DiscardUnknown(m)
}

var xxx_messageInfo_ModuleOptsReq proto.InternalMessageInfo

func (m *ModuleOptsReq) GetProject() string {
	if m != nil {
		return m.Project
	}
	return ""
}

type ModuleOptsResp struct {
	// 模版名称
	Label string `protobuf:"bytes,1,opt,name=label,proto3" json:"label"`
	// 模版id
	Value                string   `protobuf:"bytes,2,opt,name=value,proto3" json:"value"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *ModuleOptsResp) Reset()         { *m = ModuleOptsResp{} }
func (m *ModuleOptsResp) String() string { return proto.CompactTextString(m) }
func (*ModuleOptsResp) ProtoMessage()    {}
func (*ModuleOptsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_ae7704718fb7daeb, []int{6}
}

func (m *ModuleOptsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ModuleOptsResp.Unmarshal(m, b)
}
func (m *ModuleOptsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ModuleOptsResp.Marshal(b, m, deterministic)
}
func (m *ModuleOptsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ModuleOptsResp.Merge(m, src)
}
func (m *ModuleOptsResp) XXX_Size() int {
	return xxx_messageInfo_ModuleOptsResp.Size(m)
}
func (m *ModuleOptsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_ModuleOptsResp.DiscardUnknown(m)
}

var xxx_messageInfo_ModuleOptsResp proto.InternalMessageInfo

func (m *ModuleOptsResp) GetLabel() string {
	if m != nil {
		return m.Label
	}
	return ""
}

func (m *ModuleOptsResp) GetValue() string {
	if m != nil {
		return m.Value
	}
	return ""
}

func init() {
	proto.RegisterType((*ModuleSaveReq)(nil), "pb.ModuleSaveReq")
	proto.RegisterType((*ModuleEditReq)(nil), "pb.ModuleEditReq")
	proto.RegisterType((*ModuleDelReq)(nil), "pb.ModuleDelReq")
	proto.RegisterType((*ModuleListReq)(nil), "pb.ModuleListReq")
	proto.RegisterType((*ModuleListResp)(nil), "pb.ModuleListResp")
	proto.RegisterType((*ModuleListResp_ModuleInfo)(nil), "pb.ModuleListResp.ModuleInfo")
	proto.RegisterType((*ModuleOptsReq)(nil), "pb.ModuleOptsReq")
	proto.RegisterType((*ModuleOptsResp)(nil), "pb.ModuleOptsResp")
}

func init() {
	proto.RegisterFile("module.proto", fileDescriptor_ae7704718fb7daeb)
}

var fileDescriptor_ae7704718fb7daeb = []byte{
	// 480 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x94, 0x94, 0xcf, 0x8e, 0xd3, 0x30,
	0x10, 0xc6, 0x95, 0xc4, 0x4d, 0xda, 0x49, 0xbb, 0x87, 0x08, 0x90, 0x59, 0x04, 0x5b, 0x7a, 0x2a,
	0x97, 0x4a, 0xc0, 0x11, 0x4e, 0x08, 0x0e, 0x2b, 0xf1, 0x67, 0x95, 0xe5, 0xc4, 0x25, 0x72, 0x92,
	0xa1, 0x32, 0x4a, 0x62, 0xe3, 0xb8, 0x2b, 0xb1, 0x47, 0xc4, 0x63, 0xf0, 0x08, 0xbc, 0x0a, 0xef,
	0x84, 0x6c, 0x27, 0x6d, 0xb2, 0xfc, 0xdb, 0x3d, 0xb5, 0xdf, 0xe7, 0x91, 0xfd, 0xfb, 0x66, 0x46,
	0x81, 0x79, 0x2d, 0xca, 0x5d, 0x85, 0x1b, 0xa9, 0x84, 0x16, 0x89, 0x2f, 0xf3, 0xd5, 0x37, 0x0f,
	0x16, 0x6f, 0xac, 0x79, 0xce, 0x2e, 0x30, 0xc5, 0xcf, 0xc9, 0x09, 0xc4, 0xae, 0x2a, 0x6b, 0x58,
	0x8d, 0xd4, 0x5b, 0x7a, 0xeb, 0x59, 0x0a, 0xce, 0x7a, 0xcb, 0x6a, 0x4c, 0x1e, 0xc2, 0x7c, 0xcb,
	0x6a, 0xcc, 0xa4, 0x12, 0x9f, 0xb0, 0xd0, 0xd4, 0xb7, 0x15, 0xb1, 0xf1, 0xce, 0x9c, 0x95, 0x50,
	0x88, 0x0a, 0xd1, 0x68, 0x6c, 0x34, 0x0d, 0xec, 0x69, 0x2f, 0x93, 0xdb, 0x10, 0x16, 0x4c, 0x67,
	0xbc, 0xa4, 0x64, 0xe9, 0xad, 0x17, 0xe9, 0xa4, 0x60, 0xfa, 0xb4, 0x5c, 0xfd, 0xd8, 0x63, 0xbc,
	0x2a, 0xb9, 0x36, 0x18, 0x47, 0xe0, 0xf3, 0xd2, 0xbe, 0x4e, 0x52, 0x9f, 0x97, 0x57, 0xb1, 0xfc,
	0xff, 0x62, 0x05, 0xff, 0xc4, 0x22, 0x63, 0xac, 0x3b, 0x10, 0x62, 0xc3, 0xf2, 0x0a, 0xe9, 0xc4,
	0x62, 0x75, 0x6a, 0x80, 0x1b, 0x0e, 0x71, 0x1f, 0xc0, 0xdc, 0xd1, 0xbe, 0xc4, 0xea, 0x0f, 0xb0,
	0xab, 0x9f, 0xfb, 0x38, 0xaf, 0x79, 0xab, 0xaf, 0xd5, 0xd5, 0x01, 0x9b, 0x3f, 0x66, 0xbb, 0x46,
	0xb0, 0x03, 0x3e, 0x19, 0xe1, 0x27, 0x40, 0x24, 0xdb, 0xf6, 0xa1, 0xec, 0xff, 0xe4, 0x1e, 0xcc,
	0xcc, 0x6f, 0xd6, 0xf2, 0x4b, 0xec, 0x52, 0x4d, 0x8d, 0x71, 0xce, 0x2f, 0x87, 0x79, 0xa3, 0x65,
	0x70, 0xc8, 0xfb, 0x3d, 0x80, 0xa3, 0x61, 0x9e, 0x56, 0x1a, 0xaa, 0x62, 0xa7, 0x14, 0x36, 0x3a,
	0xb3, 0x4f, 0x78, 0xf6, 0xa6, 0xb8, 0xf3, 0xce, 0xcc, 0x4b, 0x77, 0x61, 0x2a, 0x51, 0xb9, 0x63,
	0xdf, 0x1e, 0x47, 0x12, 0x95, 0x3d, 0xba, 0x05, 0x13, 0x2d, 0x34, 0xab, 0x6c, 0x98, 0x45, 0xea,
	0x44, 0xf2, 0x18, 0x48, 0xc9, 0x34, 0xa3, 0x64, 0x19, 0xac, 0xe3, 0x27, 0xf7, 0x37, 0x32, 0xdf,
	0x8c, 0x5f, 0xed, 0xe4, 0x69, 0xf3, 0x51, 0xa4, 0xb6, 0xf4, 0xf8, 0xab, 0x0f, 0x70, 0x30, 0x6f,
	0xbe, 0x35, 0x7f, 0xdf, 0xd4, 0xab, 0x6d, 0x27, 0xbf, 0xb7, 0xfd, 0x18, 0xa6, 0x42, 0xa2, 0x62,
	0x5a, 0x28, 0xdb, 0xe2, 0x59, 0xba, 0xd7, 0x83, 0x91, 0x84, 0xa3, 0x91, 0x9c, 0x40, 0xbc, 0x93,
	0x25, 0xd3, 0x98, 0x69, 0x5e, 0x23, 0x8d, 0x1c, 0x91, 0xb3, 0xde, 0xf3, 0x1a, 0xcd, 0xa5, 0x05,
	0xd3, 0xb8, 0x15, 0xea, 0x0b, 0x9d, 0xba, 0x4b, 0x7b, 0x3d, 0x18, 0xcf, 0x6c, 0xb8, 0x8e, 0x8f,
	0xfa, 0x6d, 0x7b, 0x27, 0x75, 0x6b, 0xb6, 0x8d, 0x42, 0xd4, 0x63, 0xbb, 0x4d, 0xeb, 0xe5, 0xea,
	0x79, 0x3f, 0x48, 0x57, 0xda, 0x4a, 0x33, 0x8a, 0x8a, 0xe5, 0x58, 0x75, 0x95, 0x4e, 0x18, 0xf7,
	0x82, 0x55, 0xbb, 0xbe, 0x65, 0x4e, 0xbc, 0x08, 0x3f, 0x90, 0xcd, 0x33, 0x99, 0xe7, 0xa1, 0xfd,
	0x80, 0x3c, 0xfd, 0x15, 0x00, 0x00, 0xff, 0xff, 0x69, 0x0a, 0x14, 0xaf, 0x50, 0x04, 0x00, 0x00,
}
