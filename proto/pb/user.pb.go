// Code generated by protoc-gen-go. DO NOT EDIT.
// source: user.proto

package pb

import (
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

type UserListResp struct {
	// 团队ID
	Account              string   `protobuf:"bytes,1,opt,name=account,proto3" json:"account"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *UserListResp) Reset()         { *m = UserListResp{} }
func (m *UserListResp) String() string { return proto.CompactTextString(m) }
func (*UserListResp) ProtoMessage()    {}
func (*UserListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_116e343673f7ffaf, []int{0}
}

func (m *UserListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserListResp.Unmarshal(m, b)
}
func (m *UserListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserListResp.Marshal(b, m, deterministic)
}
func (m *UserListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserListResp.Merge(m, src)
}
func (m *UserListResp) XXX_Size() int {
	return xxx_messageInfo_UserListResp.Size(m)
}
func (m *UserListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_UserListResp.DiscardUnknown(m)
}

var xxx_messageInfo_UserListResp proto.InternalMessageInfo

func (m *UserListResp) GetAccount() string {
	if m != nil {
		return m.Account
	}
	return ""
}

func init() {
	proto.RegisterType((*UserListResp)(nil), "pb.UserListResp")
}

func init() {
	proto.RegisterFile("user.proto", fileDescriptor_116e343673f7ffaf)
}

var fileDescriptor_116e343673f7ffaf = []byte{
	// 89 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xe2, 0xe2, 0x2a, 0x2d, 0x4e, 0x2d,
	0xd2, 0x2b, 0x28, 0xca, 0x2f, 0xc9, 0x17, 0x62, 0x2a, 0x48, 0x52, 0xd2, 0xe0, 0xe2, 0x09, 0x2d,
	0x4e, 0x2d, 0xf2, 0xc9, 0x2c, 0x2e, 0x09, 0x4a, 0x2d, 0x2e, 0x10, 0x92, 0xe0, 0x62, 0x4f, 0x4c,
	0x4e, 0xce, 0x2f, 0xcd, 0x2b, 0x91, 0x60, 0x54, 0x60, 0xd4, 0xe0, 0x0c, 0x82, 0x71, 0x9d, 0xd8,
	0xa2, 0x58, 0xf4, 0xac, 0x0b, 0x92, 0x92, 0xd8, 0xc0, 0x9a, 0x8d, 0x01, 0x01, 0x00, 0x00, 0xff,
	0xff, 0x10, 0xcb, 0x4a, 0x5e, 0x4a, 0x00, 0x00, 0x00,
}
