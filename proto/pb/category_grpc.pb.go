// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.14.0
// source: category.proto

package pb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	CategoryBackendAPI_CategoryRelease_FullMethodName = "/pb.CategoryBackendAPI/CategoryRelease"
)

// CategoryBackendAPIClient is the client API for CategoryBackendAPI service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type CategoryBackendAPIClient interface {
	// 模版发布- 清除缓存
	CategoryRelease(ctx context.Context, in *CatProjectLang, opts ...grpc.CallOption) (*Empty, error)
}

type categoryBackendAPIClient struct {
	cc grpc.ClientConnInterface
}

func NewCategoryBackendAPIClient(cc grpc.ClientConnInterface) CategoryBackendAPIClient {
	return &categoryBackendAPIClient{cc}
}

func (c *categoryBackendAPIClient) CategoryRelease(ctx context.Context, in *CatProjectLang, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, CategoryBackendAPI_CategoryRelease_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// CategoryBackendAPIServer is the server API for CategoryBackendAPI service.
// All implementations must embed UnimplementedCategoryBackendAPIServer
// for forward compatibility
type CategoryBackendAPIServer interface {
	// 模版发布- 清除缓存
	CategoryRelease(context.Context, *CatProjectLang) (*Empty, error)
	mustEmbedUnimplementedCategoryBackendAPIServer()
}

// UnimplementedCategoryBackendAPIServer must be embedded to have forward compatible implementations.
type UnimplementedCategoryBackendAPIServer struct {
}

func (UnimplementedCategoryBackendAPIServer) CategoryRelease(context.Context, *CatProjectLang) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CategoryRelease not implemented")
}
func (UnimplementedCategoryBackendAPIServer) mustEmbedUnimplementedCategoryBackendAPIServer() {}

// UnsafeCategoryBackendAPIServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to CategoryBackendAPIServer will
// result in compilation errors.
type UnsafeCategoryBackendAPIServer interface {
	mustEmbedUnimplementedCategoryBackendAPIServer()
}

func RegisterCategoryBackendAPIServer(s grpc.ServiceRegistrar, srv CategoryBackendAPIServer) {
	s.RegisterService(&CategoryBackendAPI_ServiceDesc, srv)
}

func _CategoryBackendAPI_CategoryRelease_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CatProjectLang)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CategoryBackendAPIServer).CategoryRelease(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CategoryBackendAPI_CategoryRelease_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CategoryBackendAPIServer).CategoryRelease(ctx, req.(*CatProjectLang))
	}
	return interceptor(ctx, in, info, handler)
}

// CategoryBackendAPI_ServiceDesc is the grpc.ServiceDesc for CategoryBackendAPI service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var CategoryBackendAPI_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "pb.CategoryBackendAPI",
	HandlerType: (*CategoryBackendAPIServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CategoryRelease",
			Handler:    _CategoryBackendAPI_CategoryRelease_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "category.proto",
}
