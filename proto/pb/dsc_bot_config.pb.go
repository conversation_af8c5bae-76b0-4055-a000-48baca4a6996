// Code generated by protoc-gen-go. DO NOT EDIT.
// source: dsc_bot_config.proto

package pb

import (
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

// DCSBotConfigAddReq 新增DC机器人配置
type DCSBotConfigAddReq struct {
	// 项目名称 @gotags: validate:"required"
	Project string `protobuf:"bytes,1,opt,name=project,proto3" json:"project" validate:"required"`
	// DC账号
	DscName string `protobuf:"bytes,2,opt,name=dsc_name,json=dscName,proto3" json:"dsc_name"`
	// 机器人描述
	BotDesc string `protobuf:"bytes,3,opt,name=bot_desc,json=botDesc,proto3" json:"bot_desc"`
	// Application ID
	AppId string `protobuf:"bytes,4,opt,name=app_id,json=appId,proto3" json:"app_id"`
	// 机器人配置信息 @gotags: validate:"required"
	BotConfig *DscBotConfig `protobuf:"bytes,5,opt,name=bot_config,json=botConfig,proto3" json:"bot_config" validate:"required"`
	// 用户ID
	UserId string `protobuf:"bytes,6,opt,name=user_id,json=userId,proto3" json:"user_id"`
	// 用户名称
	Username string `protobuf:"bytes,7,opt,name=username,proto3" json:"username"`
	// 标识
	Discriminator        string   `protobuf:"bytes,8,opt,name=discriminator,proto3" json:"discriminator"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *DCSBotConfigAddReq) Reset()         { *m = DCSBotConfigAddReq{} }
func (m *DCSBotConfigAddReq) String() string { return proto.CompactTextString(m) }
func (*DCSBotConfigAddReq) ProtoMessage()    {}
func (*DCSBotConfigAddReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_5eb59fa7f4b732bc, []int{0}
}

func (m *DCSBotConfigAddReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DCSBotConfigAddReq.Unmarshal(m, b)
}
func (m *DCSBotConfigAddReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DCSBotConfigAddReq.Marshal(b, m, deterministic)
}
func (m *DCSBotConfigAddReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DCSBotConfigAddReq.Merge(m, src)
}
func (m *DCSBotConfigAddReq) XXX_Size() int {
	return xxx_messageInfo_DCSBotConfigAddReq.Size(m)
}
func (m *DCSBotConfigAddReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DCSBotConfigAddReq.DiscardUnknown(m)
}

var xxx_messageInfo_DCSBotConfigAddReq proto.InternalMessageInfo

func (m *DCSBotConfigAddReq) GetProject() string {
	if m != nil {
		return m.Project
	}
	return ""
}

func (m *DCSBotConfigAddReq) GetDscName() string {
	if m != nil {
		return m.DscName
	}
	return ""
}

func (m *DCSBotConfigAddReq) GetBotDesc() string {
	if m != nil {
		return m.BotDesc
	}
	return ""
}

func (m *DCSBotConfigAddReq) GetAppId() string {
	if m != nil {
		return m.AppId
	}
	return ""
}

func (m *DCSBotConfigAddReq) GetBotConfig() *DscBotConfig {
	if m != nil {
		return m.BotConfig
	}
	return nil
}

func (m *DCSBotConfigAddReq) GetUserId() string {
	if m != nil {
		return m.UserId
	}
	return ""
}

func (m *DCSBotConfigAddReq) GetUsername() string {
	if m != nil {
		return m.Username
	}
	return ""
}

func (m *DCSBotConfigAddReq) GetDiscriminator() string {
	if m != nil {
		return m.Discriminator
	}
	return ""
}

// DCSBotConfigListResp DC机器人配置列表响应
type DCSBotConfigListResp struct {
	Data                 []*DscBotDetail `protobuf:"bytes,1,rep,name=data,proto3" json:"data"`
	Total                uint32          `protobuf:"varint,2,opt,name=total,proto3" json:"total"`
	CurrentPage          uint32          `protobuf:"varint,3,opt,name=current_page,json=currentPage,proto3" json:"current_page"`
	PerPage              uint32          `protobuf:"varint,4,opt,name=per_page,json=perPage,proto3" json:"per_page"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-" gorm:"-"`
	XXX_unrecognized     []byte          `json:"-" gorm:"-"`
	XXX_sizecache        int32           `json:"-" gorm:"-"`
}

func (m *DCSBotConfigListResp) Reset()         { *m = DCSBotConfigListResp{} }
func (m *DCSBotConfigListResp) String() string { return proto.CompactTextString(m) }
func (*DCSBotConfigListResp) ProtoMessage()    {}
func (*DCSBotConfigListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_5eb59fa7f4b732bc, []int{1}
}

func (m *DCSBotConfigListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DCSBotConfigListResp.Unmarshal(m, b)
}
func (m *DCSBotConfigListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DCSBotConfigListResp.Marshal(b, m, deterministic)
}
func (m *DCSBotConfigListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DCSBotConfigListResp.Merge(m, src)
}
func (m *DCSBotConfigListResp) XXX_Size() int {
	return xxx_messageInfo_DCSBotConfigListResp.Size(m)
}
func (m *DCSBotConfigListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DCSBotConfigListResp.DiscardUnknown(m)
}

var xxx_messageInfo_DCSBotConfigListResp proto.InternalMessageInfo

func (m *DCSBotConfigListResp) GetData() []*DscBotDetail {
	if m != nil {
		return m.Data
	}
	return nil
}

func (m *DCSBotConfigListResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *DCSBotConfigListResp) GetCurrentPage() uint32 {
	if m != nil {
		return m.CurrentPage
	}
	return 0
}

func (m *DCSBotConfigListResp) GetPerPage() uint32 {
	if m != nil {
		return m.PerPage
	}
	return 0
}

type DscBotDetail struct {
	// ID
	Id uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	// 项目名称
	Project string `protobuf:"bytes,2,opt,name=project,proto3" json:"project"`
	// DC账号
	DscName string `protobuf:"bytes,3,opt,name=dsc_name,json=dscName,proto3" json:"dsc_name"`
	// 机器人描述
	BotDesc string `protobuf:"bytes,4,opt,name=bot_desc,json=botDesc,proto3" json:"bot_desc"`
	// Application ID
	AppId string `protobuf:"bytes,5,opt,name=app_id,json=appId,proto3" json:"app_id"`
	// 机器人配置信息
	BotConfig *DscBotConfig `protobuf:"bytes,6,opt,name=bot_config,json=botConfig,proto3" json:"bot_config"`
	// 是否删除
	IsDelete bool `protobuf:"varint,7,opt,name=is_delete,json=isDelete,proto3" json:"is_delete"`
	// 用户ID
	UserId string `protobuf:"bytes,8,opt,name=user_id,json=userId,proto3" json:"user_id"`
	// 用户名称
	Username string `protobuf:"bytes,9,opt,name=username,proto3" json:"username"`
	// 标识
	Discriminator string `protobuf:"bytes,10,opt,name=discriminator,proto3" json:"discriminator"`
	// 操作人
	UpdatedBy string `protobuf:"bytes,11,opt,name=updated_by,json=updatedBy,proto3" json:"updated_by"`
	// 创建时间
	CreatedAt string `protobuf:"bytes,12,opt,name=created_at,json=createdAt,proto3" json:"created_at"`
	// 更新时间
	UpdatedAt string `protobuf:"bytes,13,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at"`
	// 机器人状态 0处理中，1已监听，2异常
	BotStatus            uint32   `protobuf:"varint,14,opt,name=bot_status,json=botStatus,proto3" json:"bot_status"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *DscBotDetail) Reset()         { *m = DscBotDetail{} }
func (m *DscBotDetail) String() string { return proto.CompactTextString(m) }
func (*DscBotDetail) ProtoMessage()    {}
func (*DscBotDetail) Descriptor() ([]byte, []int) {
	return fileDescriptor_5eb59fa7f4b732bc, []int{2}
}

func (m *DscBotDetail) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DscBotDetail.Unmarshal(m, b)
}
func (m *DscBotDetail) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DscBotDetail.Marshal(b, m, deterministic)
}
func (m *DscBotDetail) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DscBotDetail.Merge(m, src)
}
func (m *DscBotDetail) XXX_Size() int {
	return xxx_messageInfo_DscBotDetail.Size(m)
}
func (m *DscBotDetail) XXX_DiscardUnknown() {
	xxx_messageInfo_DscBotDetail.DiscardUnknown(m)
}

var xxx_messageInfo_DscBotDetail proto.InternalMessageInfo

func (m *DscBotDetail) GetId() uint64 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *DscBotDetail) GetProject() string {
	if m != nil {
		return m.Project
	}
	return ""
}

func (m *DscBotDetail) GetDscName() string {
	if m != nil {
		return m.DscName
	}
	return ""
}

func (m *DscBotDetail) GetBotDesc() string {
	if m != nil {
		return m.BotDesc
	}
	return ""
}

func (m *DscBotDetail) GetAppId() string {
	if m != nil {
		return m.AppId
	}
	return ""
}

func (m *DscBotDetail) GetBotConfig() *DscBotConfig {
	if m != nil {
		return m.BotConfig
	}
	return nil
}

func (m *DscBotDetail) GetIsDelete() bool {
	if m != nil {
		return m.IsDelete
	}
	return false
}

func (m *DscBotDetail) GetUserId() string {
	if m != nil {
		return m.UserId
	}
	return ""
}

func (m *DscBotDetail) GetUsername() string {
	if m != nil {
		return m.Username
	}
	return ""
}

func (m *DscBotDetail) GetDiscriminator() string {
	if m != nil {
		return m.Discriminator
	}
	return ""
}

func (m *DscBotDetail) GetUpdatedBy() string {
	if m != nil {
		return m.UpdatedBy
	}
	return ""
}

func (m *DscBotDetail) GetCreatedAt() string {
	if m != nil {
		return m.CreatedAt
	}
	return ""
}

func (m *DscBotDetail) GetUpdatedAt() string {
	if m != nil {
		return m.UpdatedAt
	}
	return ""
}

func (m *DscBotDetail) GetBotStatus() uint32 {
	if m != nil {
		return m.BotStatus
	}
	return 0
}

// DscBotConfig Discord机器人配置
type DscBotConfig struct {
	// Application ID
	ClientId string `protobuf:"bytes,1,opt,name=client_id,json=clientId,proto3" json:"client_id"`
	// Public Key
	PublicKey string `protobuf:"bytes,2,opt,name=public_key,json=publicKey,proto3" json:"public_key"`
	// Bot Token
	BotToken string `protobuf:"bytes,3,opt,name=bot_token,json=botToken,proto3" json:"bot_token"`
	// Sever ID
	GuildId string `protobuf:"bytes,4,opt,name=guild_id,json=guildId,proto3" json:"guild_id"`
	// 服务器名称
	GuildDesc string `protobuf:"bytes,5,opt,name=guild_desc,json=guildDesc,proto3" json:"guild_desc"`
	// 项目名称
	Project string `protobuf:"bytes,6,opt,name=project,proto3" json:"project"`
	// 欢迎消息
	WelcomeMessage       string   `protobuf:"bytes,7,opt,name=welcome_message,json=welcomeMessage,proto3" json:"welcome_message"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *DscBotConfig) Reset()         { *m = DscBotConfig{} }
func (m *DscBotConfig) String() string { return proto.CompactTextString(m) }
func (*DscBotConfig) ProtoMessage()    {}
func (*DscBotConfig) Descriptor() ([]byte, []int) {
	return fileDescriptor_5eb59fa7f4b732bc, []int{3}
}

func (m *DscBotConfig) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DscBotConfig.Unmarshal(m, b)
}
func (m *DscBotConfig) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DscBotConfig.Marshal(b, m, deterministic)
}
func (m *DscBotConfig) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DscBotConfig.Merge(m, src)
}
func (m *DscBotConfig) XXX_Size() int {
	return xxx_messageInfo_DscBotConfig.Size(m)
}
func (m *DscBotConfig) XXX_DiscardUnknown() {
	xxx_messageInfo_DscBotConfig.DiscardUnknown(m)
}

var xxx_messageInfo_DscBotConfig proto.InternalMessageInfo

func (m *DscBotConfig) GetClientId() string {
	if m != nil {
		return m.ClientId
	}
	return ""
}

func (m *DscBotConfig) GetPublicKey() string {
	if m != nil {
		return m.PublicKey
	}
	return ""
}

func (m *DscBotConfig) GetBotToken() string {
	if m != nil {
		return m.BotToken
	}
	return ""
}

func (m *DscBotConfig) GetGuildId() string {
	if m != nil {
		return m.GuildId
	}
	return ""
}

func (m *DscBotConfig) GetGuildDesc() string {
	if m != nil {
		return m.GuildDesc
	}
	return ""
}

func (m *DscBotConfig) GetProject() string {
	if m != nil {
		return m.Project
	}
	return ""
}

func (m *DscBotConfig) GetWelcomeMessage() string {
	if m != nil {
		return m.WelcomeMessage
	}
	return ""
}

// DCSBotConfigListReq DC机器人配置列表请求
type DCSBotConfigListReq struct {
	Page                 uint32   `protobuf:"varint,1,opt,name=page,proto3" json:"page"`
	PageSize             uint32   `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *DCSBotConfigListReq) Reset()         { *m = DCSBotConfigListReq{} }
func (m *DCSBotConfigListReq) String() string { return proto.CompactTextString(m) }
func (*DCSBotConfigListReq) ProtoMessage()    {}
func (*DCSBotConfigListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_5eb59fa7f4b732bc, []int{4}
}

func (m *DCSBotConfigListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DCSBotConfigListReq.Unmarshal(m, b)
}
func (m *DCSBotConfigListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DCSBotConfigListReq.Marshal(b, m, deterministic)
}
func (m *DCSBotConfigListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DCSBotConfigListReq.Merge(m, src)
}
func (m *DCSBotConfigListReq) XXX_Size() int {
	return xxx_messageInfo_DCSBotConfigListReq.Size(m)
}
func (m *DCSBotConfigListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DCSBotConfigListReq.DiscardUnknown(m)
}

var xxx_messageInfo_DCSBotConfigListReq proto.InternalMessageInfo

func (m *DCSBotConfigListReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *DCSBotConfigListReq) GetPageSize() uint32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

// UpdateDCSBotConfigWelcomeMessageReq 更新DC机器人配置欢迎消息
type UpdateDCSBotConfigWelcomeMessageReq struct {
	// 机器人ID
	Id uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	// 欢迎消息
	WelcomeMessage       string   `protobuf:"bytes,2,opt,name=welcome_message,json=welcomeMessage,proto3" json:"welcome_message"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *UpdateDCSBotConfigWelcomeMessageReq) Reset()         { *m = UpdateDCSBotConfigWelcomeMessageReq{} }
func (m *UpdateDCSBotConfigWelcomeMessageReq) String() string { return proto.CompactTextString(m) }
func (*UpdateDCSBotConfigWelcomeMessageReq) ProtoMessage()    {}
func (*UpdateDCSBotConfigWelcomeMessageReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_5eb59fa7f4b732bc, []int{5}
}

func (m *UpdateDCSBotConfigWelcomeMessageReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateDCSBotConfigWelcomeMessageReq.Unmarshal(m, b)
}
func (m *UpdateDCSBotConfigWelcomeMessageReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateDCSBotConfigWelcomeMessageReq.Marshal(b, m, deterministic)
}
func (m *UpdateDCSBotConfigWelcomeMessageReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateDCSBotConfigWelcomeMessageReq.Merge(m, src)
}
func (m *UpdateDCSBotConfigWelcomeMessageReq) XXX_Size() int {
	return xxx_messageInfo_UpdateDCSBotConfigWelcomeMessageReq.Size(m)
}
func (m *UpdateDCSBotConfigWelcomeMessageReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateDCSBotConfigWelcomeMessageReq.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateDCSBotConfigWelcomeMessageReq proto.InternalMessageInfo

func (m *UpdateDCSBotConfigWelcomeMessageReq) GetId() uint64 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *UpdateDCSBotConfigWelcomeMessageReq) GetWelcomeMessage() string {
	if m != nil {
		return m.WelcomeMessage
	}
	return ""
}

func init() {
	proto.RegisterType((*DCSBotConfigAddReq)(nil), "pb.DCSBotConfigAddReq")
	proto.RegisterType((*DCSBotConfigListResp)(nil), "pb.DCSBotConfigListResp")
	proto.RegisterType((*DscBotDetail)(nil), "pb.DscBotDetail")
	proto.RegisterType((*DscBotConfig)(nil), "pb.DscBotConfig")
	proto.RegisterType((*DCSBotConfigListReq)(nil), "pb.DCSBotConfigListReq")
	proto.RegisterType((*UpdateDCSBotConfigWelcomeMessageReq)(nil), "pb.UpdateDCSBotConfigWelcomeMessageReq")
}

func init() {
	proto.RegisterFile("dsc_bot_config.proto", fileDescriptor_5eb59fa7f4b732bc)
}

var fileDescriptor_5eb59fa7f4b732bc = []byte{
	// 776 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x94, 0x55, 0xdd, 0x6e, 0xdb, 0x36,
	0x14, 0x86, 0xe4, 0x3f, 0x89, 0xb1, 0x93, 0x8d, 0xc9, 0x16, 0xc5, 0x4b, 0x06, 0x4f, 0x09, 0x90,
	0x20, 0x17, 0x36, 0x90, 0x61, 0x37, 0xd9, 0x55, 0x12, 0x6f, 0x43, 0xb0, 0x1f, 0x0c, 0xf2, 0x86,
	0x0d, 0x45, 0x51, 0x81, 0x22, 0x59, 0x97, 0x8d, 0x24, 0x32, 0x22, 0xdd, 0xc2, 0xb9, 0xec, 0x4d,
	0xaf, 0x0a, 0x14, 0xe8, 0x5b, 0xf4, 0x59, 0x7a, 0xd7, 0x57, 0xc8, 0x83, 0x14, 0x24, 0x95, 0x44,
	0x76, 0x6c, 0xa4, 0xbd, 0x32, 0xcf, 0xf7, 0x89, 0xdf, 0x39, 0x3c, 0xdf, 0x21, 0x0d, 0x36, 0x88,
	0xc4, 0x71, 0xc2, 0x55, 0x8c, 0x79, 0xfe, 0x94, 0x8d, 0xfb, 0xa2, 0xe0, 0x8a, 0x43, 0x57, 0x24,
	0xdd, 0x36, 0xe6, 0x59, 0xc6, 0x73, 0x8b, 0x74, 0xb7, 0xc7, 0x9c, 0x8f, 0x53, 0x3a, 0x40, 0x82,
	0x0d, 0x50, 0x9e, 0x73, 0x85, 0x14, 0xe3, 0xb9, 0xb4, 0x6c, 0xf8, 0xda, 0x05, 0x70, 0x78, 0x36,
	0x3a, 0xe5, 0xea, 0xcc, 0xc8, 0x9c, 0x10, 0x12, 0xd1, 0x4b, 0x18, 0x80, 0x96, 0x28, 0xf8, 0x73,
	0x8a, 0x55, 0xe0, 0xf4, 0x9c, 0x03, 0x3f, 0xba, 0x09, 0xe1, 0x16, 0xf0, 0x74, 0xe2, 0x1c, 0x65,
	0x34, 0x70, 0x2d, 0x45, 0x24, 0xfe, 0x0b, 0x65, 0x54, 0x53, 0xba, 0x1e, 0x42, 0x25, 0x0e, 0x6a,
	0x96, 0x4a, 0xb8, 0x1a, 0x52, 0x89, 0xe1, 0x37, 0xa0, 0x89, 0x84, 0x88, 0x19, 0x09, 0xea, 0x86,
	0x68, 0x20, 0x21, 0xce, 0x09, 0x1c, 0x00, 0x70, 0x77, 0x82, 0xa0, 0xd1, 0x73, 0x0e, 0x56, 0x8e,
	0xbe, 0xea, 0x8b, 0xa4, 0x3f, 0x94, 0xf8, 0xb6, 0xa4, 0xc8, 0x4f, 0x6e, 0x96, 0x70, 0x13, 0xb4,
	0x26, 0x92, 0x16, 0x5a, 0xa8, 0x69, 0x84, 0x9a, 0x3a, 0x3c, 0x27, 0xb0, 0x0b, 0x3c, 0xbd, 0x32,
	0x65, 0xb5, 0x0c, 0x73, 0x1b, 0xc3, 0x3d, 0xd0, 0x21, 0x4c, 0xe2, 0x82, 0x65, 0x2c, 0x47, 0x8a,
	0x17, 0x81, 0x67, 0x3e, 0x98, 0x05, 0xc3, 0xb7, 0x0e, 0xd8, 0xa8, 0x76, 0xe2, 0x0f, 0x26, 0x55,
	0x44, 0xa5, 0x80, 0x7b, 0xa0, 0x4e, 0x90, 0x42, 0x81, 0xd3, 0xab, 0xcd, 0x96, 0x37, 0xa4, 0x0a,
	0xb1, 0x34, 0x32, 0x2c, 0xdc, 0x00, 0x0d, 0xc5, 0x15, 0x4a, 0x4d, 0x53, 0x3a, 0x91, 0x0d, 0xe0,
	0x0f, 0xa0, 0x8d, 0x27, 0x45, 0x41, 0x73, 0x15, 0x0b, 0x34, 0xa6, 0xa6, 0x2d, 0x9d, 0x68, 0xa5,
	0xc4, 0xfe, 0x46, 0x63, 0xd3, 0x35, 0x41, 0x0b, 0x4b, 0xd7, 0x0d, 0xdd, 0x12, 0xb4, 0xd0, 0x54,
	0xf8, 0xbe, 0x06, 0xda, 0xd5, 0x54, 0x70, 0x15, 0xb8, 0x8c, 0x18, 0x47, 0xea, 0x91, 0xcb, 0x48,
	0xd5, 0x26, 0x77, 0xb9, 0x4d, 0xb5, 0xe5, 0x36, 0xd5, 0x97, 0xd9, 0xd4, 0x58, 0x6e, 0x53, 0xf3,
	0x61, 0x9b, 0xbe, 0x03, 0x3e, 0x93, 0x31, 0xa1, 0x29, 0x55, 0xd6, 0x0e, 0x2f, 0xf2, 0x98, 0x1c,
	0x9a, 0xb8, 0xea, 0xa1, 0xb7, 0xd4, 0x43, 0xff, 0x21, 0x0f, 0xc1, 0x02, 0x0f, 0xe1, 0x0e, 0x00,
	0x13, 0x41, 0x90, 0xa2, 0x24, 0x4e, 0xa6, 0xc1, 0x8a, 0xf9, 0xc4, 0x2f, 0x91, 0xd3, 0xa9, 0xa6,
	0x71, 0x41, 0x0d, 0x8d, 0x54, 0xd0, 0xb6, 0x74, 0x89, 0x9c, 0xa8, 0xea, 0x6e, 0xa4, 0x82, 0xce,
	0xcc, 0x6e, 0x4b, 0xeb, 0x2e, 0x48, 0x85, 0xd4, 0x44, 0x06, 0xab, 0xc6, 0x2a, 0x7d, 0xe6, 0x91,
	0x01, 0xc2, 0x6b, 0xe7, 0xc6, 0xac, 0xbb, 0x26, 0xe0, 0x94, 0x69, 0xeb, 0x4b, 0xcf, 0xfc, 0xc8,
	0xb3, 0xc0, 0x39, 0xd1, 0x62, 0x62, 0x92, 0xa4, 0x0c, 0xc7, 0x17, 0x74, 0x5a, 0x9a, 0xe7, 0x5b,
	0xe4, 0x77, 0x3a, 0xd5, 0x7b, 0x75, 0x2e, 0xc5, 0x2f, 0x68, 0x5e, 0xfa, 0xa7, 0x4d, 0xfb, 0x47,
	0xc7, 0xda, 0xc0, 0xf1, 0x84, 0xa5, 0xe4, 0xee, 0x3a, 0xb5, 0x4c, 0x6c, 0x65, 0x2d, 0x65, 0xdc,
	0xb5, 0x26, 0xfa, 0x06, 0x31, 0xfe, 0x56, 0xe6, 0xa5, 0x39, 0x3b, 0x2f, 0xfb, 0x60, 0xed, 0x25,
	0x4d, 0x31, 0xcf, 0x68, 0x9c, 0x51, 0x29, 0xf5, 0x30, 0xda, 0x6b, 0xb4, 0x5a, 0xc2, 0x7f, 0x5a,
	0x34, 0xfc, 0x15, 0xac, 0xdf, 0xbf, 0x25, 0x97, 0x10, 0x82, 0xba, 0x99, 0x60, 0xc7, 0xb4, 0xc5,
	0xac, 0xf5, 0x21, 0xf4, 0x6f, 0x2c, 0xd9, 0x15, 0x2d, 0xaf, 0x85, 0xa7, 0x81, 0x11, 0xbb, 0xa2,
	0xe1, 0x13, 0xb0, 0xfb, 0xaf, 0x69, 0x6d, 0x55, 0xed, 0xbf, 0x99, 0x5c, 0x5a, 0x77, 0x7e, 0xe2,
	0x17, 0xd4, 0xe9, 0x2e, 0xaa, 0xf3, 0xe8, 0x43, 0x6d, 0xb6, 0xd0, 0x11, 0x2d, 0x5e, 0x30, 0x4c,
	0xe1, 0xff, 0x60, 0xed, 0x84, 0x90, 0x2a, 0x03, 0xbf, 0x35, 0xa3, 0x7c, 0xef, 0x11, 0xec, 0xfa,
	0x1a, 0xff, 0x25, 0x13, 0x6a, 0x1a, 0x86, 0xaf, 0x3e, 0x5e, 0xbf, 0x73, 0xb7, 0xc3, 0x4d, 0xf3,
	0x8c, 0xce, 0xbe, 0xbc, 0x03, 0x44, 0xc8, 0xb1, 0x73, 0x08, 0x1f, 0x83, 0xaf, 0xcf, 0x9e, 0x51,
	0x7c, 0xf1, 0xa5, 0xda, 0x7b, 0x46, 0xfb, 0xfb, 0x70, 0x6b, 0x91, 0x36, 0xd6, 0x8a, 0x5a, 0xfd,
	0x8d, 0x03, 0x7a, 0x0f, 0x35, 0x0c, 0xee, 0x6b, 0xd5, 0xcf, 0x68, 0x6b, 0x35, 0xfd, 0x4f, 0x26,
	0xfd, 0x20, 0x3c, 0x5c, 0x94, 0xde, 0x4e, 0x7f, 0x3c, 0xd7, 0x72, 0x5d, 0x4f, 0x06, 0xd6, 0x7f,
	0xa3, 0x6a, 0x7e, 0x14, 0xe0, 0xe6, 0xfc, 0x79, 0xcb, 0x01, 0xe9, 0x06, 0x8b, 0x09, 0x29, 0xc2,
	0x5d, 0x53, 0xc0, 0x4e, 0x18, 0x2c, 0x2a, 0x20, 0x65, 0x52, 0x1d, 0x3b, 0x87, 0xa7, 0xcd, 0x47,
	0xf5, 0xfe, 0xcf, 0x22, 0x49, 0x9a, 0xe6, 0x6f, 0xeb, 0xc7, 0x4f, 0x01, 0x00, 0x00, 0xff, 0xff,
	0xa2, 0x36, 0x8c, 0x28, 0xfe, 0x06, 0x00, 0x00,
}
