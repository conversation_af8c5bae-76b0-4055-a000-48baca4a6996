// Code generated by protoc-gen-go. DO NOT EDIT.
// source: common.proto

package pb

import (
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	anypb "google.golang.org/protobuf/types/known/anypb"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

// Empty 空对象
type Empty struct {
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *Empty) Reset()         { *m = Empty{} }
func (m *Empty) String() string { return proto.CompactTextString(m) }
func (*Empty) ProtoMessage()    {}
func (*Empty) Descriptor() ([]byte, []int) {
	return fileDescriptor_555bd8c177793206, []int{0}
}

func (m *Empty) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Empty.Unmarshal(m, b)
}
func (m *Empty) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Empty.Marshal(b, m, deterministic)
}
func (m *Empty) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Empty.Merge(m, src)
}
func (m *Empty) XXX_Size() int {
	return xxx_messageInfo_Empty.Size(m)
}
func (m *Empty) XXX_DiscardUnknown() {
	xxx_messageInfo_Empty.DiscardUnknown(m)
}

var xxx_messageInfo_Empty proto.InternalMessageInfo

// EnableReq 启用禁用
type EnableReq struct {
	// 对象ID @gotags: validate:"required"
	ObjectId uint32 `protobuf:"varint,1,opt,name=object_id,json=objectId,proto3" json:"object_id" validate:"required"`
	// 启用 true false
	Enable               bool     `protobuf:"varint,2,opt,name=enable,proto3" json:"enable"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *EnableReq) Reset()         { *m = EnableReq{} }
func (m *EnableReq) String() string { return proto.CompactTextString(m) }
func (*EnableReq) ProtoMessage()    {}
func (*EnableReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_555bd8c177793206, []int{1}
}

func (m *EnableReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_EnableReq.Unmarshal(m, b)
}
func (m *EnableReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_EnableReq.Marshal(b, m, deterministic)
}
func (m *EnableReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_EnableReq.Merge(m, src)
}
func (m *EnableReq) XXX_Size() int {
	return xxx_messageInfo_EnableReq.Size(m)
}
func (m *EnableReq) XXX_DiscardUnknown() {
	xxx_messageInfo_EnableReq.DiscardUnknown(m)
}

var xxx_messageInfo_EnableReq proto.InternalMessageInfo

func (m *EnableReq) GetObjectId() uint32 {
	if m != nil {
		return m.ObjectId
	}
	return 0
}

func (m *EnableReq) GetEnable() bool {
	if m != nil {
		return m.Enable
	}
	return false
}

type Language struct {
	Language             map[string]string `protobuf:"bytes,1,rep,name=language,proto3" json:"language" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-" gorm:"-"`
	XXX_unrecognized     []byte            `json:"-" gorm:"-"`
	XXX_sizecache        int32             `json:"-" gorm:"-"`
}

func (m *Language) Reset()         { *m = Language{} }
func (m *Language) String() string { return proto.CompactTextString(m) }
func (*Language) ProtoMessage()    {}
func (*Language) Descriptor() ([]byte, []int) {
	return fileDescriptor_555bd8c177793206, []int{2}
}

func (m *Language) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Language.Unmarshal(m, b)
}
func (m *Language) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Language.Marshal(b, m, deterministic)
}
func (m *Language) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Language.Merge(m, src)
}
func (m *Language) XXX_Size() int {
	return xxx_messageInfo_Language.Size(m)
}
func (m *Language) XXX_DiscardUnknown() {
	xxx_messageInfo_Language.DiscardUnknown(m)
}

var xxx_messageInfo_Language proto.InternalMessageInfo

func (m *Language) GetLanguage() map[string]string {
	if m != nil {
		return m.Language
	}
	return nil
}

type ProjectLang struct {
	// 项目 @gotags: validate:"required"
	Project string `protobuf:"bytes,1,opt,name=project,proto3" json:"project" validate:"required"`
	// 语言
	Lang                 string   `protobuf:"bytes,2,opt,name=lang,proto3" json:"lang"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *ProjectLang) Reset()         { *m = ProjectLang{} }
func (m *ProjectLang) String() string { return proto.CompactTextString(m) }
func (*ProjectLang) ProtoMessage()    {}
func (*ProjectLang) Descriptor() ([]byte, []int) {
	return fileDescriptor_555bd8c177793206, []int{3}
}

func (m *ProjectLang) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ProjectLang.Unmarshal(m, b)
}
func (m *ProjectLang) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ProjectLang.Marshal(b, m, deterministic)
}
func (m *ProjectLang) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ProjectLang.Merge(m, src)
}
func (m *ProjectLang) XXX_Size() int {
	return xxx_messageInfo_ProjectLang.Size(m)
}
func (m *ProjectLang) XXX_DiscardUnknown() {
	xxx_messageInfo_ProjectLang.DiscardUnknown(m)
}

var xxx_messageInfo_ProjectLang proto.InternalMessageInfo

func (m *ProjectLang) GetProject() string {
	if m != nil {
		return m.Project
	}
	return ""
}

func (m *ProjectLang) GetLang() string {
	if m != nil {
		return m.Lang
	}
	return ""
}

// Props 枚举
type Props struct {
	// 枚举名
	Label string `protobuf:"bytes,1,opt,name=label,proto3" json:"label"`
	// 枚举值
	Value                string   `protobuf:"bytes,2,opt,name=value,proto3" json:"value"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *Props) Reset()         { *m = Props{} }
func (m *Props) String() string { return proto.CompactTextString(m) }
func (*Props) ProtoMessage()    {}
func (*Props) Descriptor() ([]byte, []int) {
	return fileDescriptor_555bd8c177793206, []int{4}
}

func (m *Props) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Props.Unmarshal(m, b)
}
func (m *Props) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Props.Marshal(b, m, deterministic)
}
func (m *Props) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Props.Merge(m, src)
}
func (m *Props) XXX_Size() int {
	return xxx_messageInfo_Props.Size(m)
}
func (m *Props) XXX_DiscardUnknown() {
	xxx_messageInfo_Props.DiscardUnknown(m)
}

var xxx_messageInfo_Props proto.InternalMessageInfo

func (m *Props) GetLabel() string {
	if m != nil {
		return m.Label
	}
	return ""
}

func (m *Props) GetValue() string {
	if m != nil {
		return m.Value
	}
	return ""
}

type BaseInfo struct {
	// 游戏id
	GameId uint64 `protobuf:"varint,1,opt,name=game_id,json=gameId,proto3" json:"game_id"`
	// 游戏区分
	GameProject string `protobuf:"bytes,2,opt,name=game_project,json=gameProject,proto3" json:"game_project"`
	// lang 语言
	Lang string `protobuf:"bytes,3,opt,name=lang,proto3" json:"lang"`
	// uuid 设备ID
	Uuid string `protobuf:"bytes,4,opt,name=uuid,proto3" json:"uuid"`
	// os
	Os string `protobuf:"bytes,5,opt,name=os,proto3" json:"os"`
	// sdk_version
	SdkVersion string `protobuf:"bytes,6,opt,name=sdk_version,json=sdkVersion,proto3" json:"sdk_version"`
	// fpid
	Fpid uint64 `protobuf:"varint,7,opt,name=fpid,proto3" json:"fpid"`
	// uid 用户ID
	Uid uint64 `protobuf:"varint,8,opt,name=uid,proto3" json:"uid"`
	// account_id
	AccountId string `protobuf:"bytes,9,opt,name=account_id,json=accountId,proto3" json:"account_id"`
	// role_id 角色ID
	RoleId string `protobuf:"bytes,10,opt,name=role_id,json=roleId,proto3" json:"role_id"`
	// sid 区服
	Sid string `protobuf:"bytes,11,opt,name=sid,proto3" json:"sid"`
	// channel 渠道
	Channel string `protobuf:"bytes,12,opt,name=channel,proto3" json:"channel"`
	// nickname 昵称
	Nickname    string  `protobuf:"bytes,13,opt,name=nickname,proto3" json:"nickname"`
	Country     string  `protobuf:"bytes,14,opt,name=country,proto3" json:"country"`
	TotalPay    float64 `protobuf:"fixed64,15,opt,name=total_pay,json=totalPay,proto3" json:"total_pay"`
	CountryCode string  `protobuf:"bytes,16,opt,name=country_code,json=countryCode,proto3" json:"country_code"`
	// fpx_app_id
	FpxAppId string `protobuf:"bytes,17,opt,name=fpx_app_id,json=fpxAppId,proto3" json:"fpx_app_id"`
	// bi_app_id
	BiAppId string `protobuf:"bytes,18,opt,name=bi_app_id,json=biAppId,proto3" json:"bi_app_id"`
	// 场景枚举
	Scene                uint32   `protobuf:"varint,29,opt,name=scene,proto3" json:"scene"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *BaseInfo) Reset()         { *m = BaseInfo{} }
func (m *BaseInfo) String() string { return proto.CompactTextString(m) }
func (*BaseInfo) ProtoMessage()    {}
func (*BaseInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_555bd8c177793206, []int{5}
}

func (m *BaseInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_BaseInfo.Unmarshal(m, b)
}
func (m *BaseInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_BaseInfo.Marshal(b, m, deterministic)
}
func (m *BaseInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_BaseInfo.Merge(m, src)
}
func (m *BaseInfo) XXX_Size() int {
	return xxx_messageInfo_BaseInfo.Size(m)
}
func (m *BaseInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_BaseInfo.DiscardUnknown(m)
}

var xxx_messageInfo_BaseInfo proto.InternalMessageInfo

func (m *BaseInfo) GetGameId() uint64 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *BaseInfo) GetGameProject() string {
	if m != nil {
		return m.GameProject
	}
	return ""
}

func (m *BaseInfo) GetLang() string {
	if m != nil {
		return m.Lang
	}
	return ""
}

func (m *BaseInfo) GetUuid() string {
	if m != nil {
		return m.Uuid
	}
	return ""
}

func (m *BaseInfo) GetOs() string {
	if m != nil {
		return m.Os
	}
	return ""
}

func (m *BaseInfo) GetSdkVersion() string {
	if m != nil {
		return m.SdkVersion
	}
	return ""
}

func (m *BaseInfo) GetFpid() uint64 {
	if m != nil {
		return m.Fpid
	}
	return 0
}

func (m *BaseInfo) GetUid() uint64 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *BaseInfo) GetAccountId() string {
	if m != nil {
		return m.AccountId
	}
	return ""
}

func (m *BaseInfo) GetRoleId() string {
	if m != nil {
		return m.RoleId
	}
	return ""
}

func (m *BaseInfo) GetSid() string {
	if m != nil {
		return m.Sid
	}
	return ""
}

func (m *BaseInfo) GetChannel() string {
	if m != nil {
		return m.Channel
	}
	return ""
}

func (m *BaseInfo) GetNickname() string {
	if m != nil {
		return m.Nickname
	}
	return ""
}

func (m *BaseInfo) GetCountry() string {
	if m != nil {
		return m.Country
	}
	return ""
}

func (m *BaseInfo) GetTotalPay() float64 {
	if m != nil {
		return m.TotalPay
	}
	return 0
}

func (m *BaseInfo) GetCountryCode() string {
	if m != nil {
		return m.CountryCode
	}
	return ""
}

func (m *BaseInfo) GetFpxAppId() string {
	if m != nil {
		return m.FpxAppId
	}
	return ""
}

func (m *BaseInfo) GetBiAppId() string {
	if m != nil {
		return m.BiAppId
	}
	return ""
}

func (m *BaseInfo) GetScene() uint32 {
	if m != nil {
		return m.Scene
	}
	return 0
}

type Response struct {
	// 状态码
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code"`
	// 错误信息
	Msg string `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg"`
	// 数据
	Data                 *anypb.Any `protobuf:"bytes,3,opt,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}   `json:"-" gorm:"-"`
	XXX_unrecognized     []byte     `json:"-" gorm:"-"`
	XXX_sizecache        int32      `json:"-" gorm:"-"`
}

func (m *Response) Reset()         { *m = Response{} }
func (m *Response) String() string { return proto.CompactTextString(m) }
func (*Response) ProtoMessage()    {}
func (*Response) Descriptor() ([]byte, []int) {
	return fileDescriptor_555bd8c177793206, []int{6}
}

func (m *Response) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Response.Unmarshal(m, b)
}
func (m *Response) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Response.Marshal(b, m, deterministic)
}
func (m *Response) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Response.Merge(m, src)
}
func (m *Response) XXX_Size() int {
	return xxx_messageInfo_Response.Size(m)
}
func (m *Response) XXX_DiscardUnknown() {
	xxx_messageInfo_Response.DiscardUnknown(m)
}

var xxx_messageInfo_Response proto.InternalMessageInfo

func (m *Response) GetCode() int32 {
	if m != nil {
		return m.Code
	}
	return 0
}

func (m *Response) GetMsg() string {
	if m != nil {
		return m.Msg
	}
	return ""
}

func (m *Response) GetData() *anypb.Any {
	if m != nil {
		return m.Data
	}
	return nil
}

type ServerSplit struct {
	Ids                  []int64                  `protobuf:"varint,1,rep,packed,name=ids,proto3" json:"ids"`
	Btw                  []*ServerSplit_ServerBtw `protobuf:"bytes,2,rep,name=btw,proto3" json:"btw"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-" gorm:"-"`
	XXX_unrecognized     []byte                   `json:"-" gorm:"-"`
	XXX_sizecache        int32                    `json:"-" gorm:"-"`
}

func (m *ServerSplit) Reset()         { *m = ServerSplit{} }
func (m *ServerSplit) String() string { return proto.CompactTextString(m) }
func (*ServerSplit) ProtoMessage()    {}
func (*ServerSplit) Descriptor() ([]byte, []int) {
	return fileDescriptor_555bd8c177793206, []int{7}
}

func (m *ServerSplit) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ServerSplit.Unmarshal(m, b)
}
func (m *ServerSplit) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ServerSplit.Marshal(b, m, deterministic)
}
func (m *ServerSplit) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ServerSplit.Merge(m, src)
}
func (m *ServerSplit) XXX_Size() int {
	return xxx_messageInfo_ServerSplit.Size(m)
}
func (m *ServerSplit) XXX_DiscardUnknown() {
	xxx_messageInfo_ServerSplit.DiscardUnknown(m)
}

var xxx_messageInfo_ServerSplit proto.InternalMessageInfo

func (m *ServerSplit) GetIds() []int64 {
	if m != nil {
		return m.Ids
	}
	return nil
}

func (m *ServerSplit) GetBtw() []*ServerSplit_ServerBtw {
	if m != nil {
		return m.Btw
	}
	return nil
}

type ServerSplit_ServerBtw struct {
	Start                int64    `protobuf:"varint,1,opt,name=start,proto3" json:"start"`
	End                  int64    `protobuf:"varint,2,opt,name=end,proto3" json:"end"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *ServerSplit_ServerBtw) Reset()         { *m = ServerSplit_ServerBtw{} }
func (m *ServerSplit_ServerBtw) String() string { return proto.CompactTextString(m) }
func (*ServerSplit_ServerBtw) ProtoMessage()    {}
func (*ServerSplit_ServerBtw) Descriptor() ([]byte, []int) {
	return fileDescriptor_555bd8c177793206, []int{7, 0}
}

func (m *ServerSplit_ServerBtw) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ServerSplit_ServerBtw.Unmarshal(m, b)
}
func (m *ServerSplit_ServerBtw) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ServerSplit_ServerBtw.Marshal(b, m, deterministic)
}
func (m *ServerSplit_ServerBtw) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ServerSplit_ServerBtw.Merge(m, src)
}
func (m *ServerSplit_ServerBtw) XXX_Size() int {
	return xxx_messageInfo_ServerSplit_ServerBtw.Size(m)
}
func (m *ServerSplit_ServerBtw) XXX_DiscardUnknown() {
	xxx_messageInfo_ServerSplit_ServerBtw.DiscardUnknown(m)
}

var xxx_messageInfo_ServerSplit_ServerBtw proto.InternalMessageInfo

func (m *ServerSplit_ServerBtw) GetStart() int64 {
	if m != nil {
		return m.Start
	}
	return 0
}

func (m *ServerSplit_ServerBtw) GetEnd() int64 {
	if m != nil {
		return m.End
	}
	return 0
}

type Project struct {
	// 项目名称
	ProjectName string `protobuf:"bytes,1,opt,name=project_name,json=projectName,proto3" json:"project_name"`
	// 标签库名称
	LibName              string   `protobuf:"bytes,2,opt,name=lib_name,json=libName,proto3" json:"lib_name"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *Project) Reset()         { *m = Project{} }
func (m *Project) String() string { return proto.CompactTextString(m) }
func (*Project) ProtoMessage()    {}
func (*Project) Descriptor() ([]byte, []int) {
	return fileDescriptor_555bd8c177793206, []int{8}
}

func (m *Project) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Project.Unmarshal(m, b)
}
func (m *Project) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Project.Marshal(b, m, deterministic)
}
func (m *Project) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Project.Merge(m, src)
}
func (m *Project) XXX_Size() int {
	return xxx_messageInfo_Project.Size(m)
}
func (m *Project) XXX_DiscardUnknown() {
	xxx_messageInfo_Project.DiscardUnknown(m)
}

var xxx_messageInfo_Project proto.InternalMessageInfo

func (m *Project) GetProjectName() string {
	if m != nil {
		return m.ProjectName
	}
	return ""
}

func (m *Project) GetLibName() string {
	if m != nil {
		return m.LibName
	}
	return ""
}

type Projects struct {
	// 项目名称 @gotags: validate:"required"
	Projects             []string `protobuf:"bytes,1,rep,name=projects,proto3" json:"projects" validate:"required"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *Projects) Reset()         { *m = Projects{} }
func (m *Projects) String() string { return proto.CompactTextString(m) }
func (*Projects) ProtoMessage()    {}
func (*Projects) Descriptor() ([]byte, []int) {
	return fileDescriptor_555bd8c177793206, []int{9}
}

func (m *Projects) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Projects.Unmarshal(m, b)
}
func (m *Projects) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Projects.Marshal(b, m, deterministic)
}
func (m *Projects) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Projects.Merge(m, src)
}
func (m *Projects) XXX_Size() int {
	return xxx_messageInfo_Projects.Size(m)
}
func (m *Projects) XXX_DiscardUnknown() {
	xxx_messageInfo_Projects.DiscardUnknown(m)
}

var xxx_messageInfo_Projects proto.InternalMessageInfo

func (m *Projects) GetProjects() []string {
	if m != nil {
		return m.Projects
	}
	return nil
}

type ProjectTagLib struct {
	// 项目名称 @gotags: validate:"required"
	ProjectName string `protobuf:"bytes,1,opt,name=project_name,json=projectName,proto3" json:"project_name" validate:"required"`
	// 组id
	LibId                uint32   `protobuf:"varint,2,opt,name=lib_id,json=libId,proto3" json:"lib_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *ProjectTagLib) Reset()         { *m = ProjectTagLib{} }
func (m *ProjectTagLib) String() string { return proto.CompactTextString(m) }
func (*ProjectTagLib) ProtoMessage()    {}
func (*ProjectTagLib) Descriptor() ([]byte, []int) {
	return fileDescriptor_555bd8c177793206, []int{10}
}

func (m *ProjectTagLib) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ProjectTagLib.Unmarshal(m, b)
}
func (m *ProjectTagLib) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ProjectTagLib.Marshal(b, m, deterministic)
}
func (m *ProjectTagLib) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ProjectTagLib.Merge(m, src)
}
func (m *ProjectTagLib) XXX_Size() int {
	return xxx_messageInfo_ProjectTagLib.Size(m)
}
func (m *ProjectTagLib) XXX_DiscardUnknown() {
	xxx_messageInfo_ProjectTagLib.DiscardUnknown(m)
}

var xxx_messageInfo_ProjectTagLib proto.InternalMessageInfo

func (m *ProjectTagLib) GetProjectName() string {
	if m != nil {
		return m.ProjectName
	}
	return ""
}

func (m *ProjectTagLib) GetLibId() uint32 {
	if m != nil {
		return m.LibId
	}
	return 0
}

func init() {
	proto.RegisterType((*Empty)(nil), "pb.Empty")
	proto.RegisterType((*EnableReq)(nil), "pb.EnableReq")
	proto.RegisterType((*Language)(nil), "pb.Language")
	proto.RegisterMapType((map[string]string)(nil), "pb.Language.LanguageEntry")
	proto.RegisterType((*ProjectLang)(nil), "pb.ProjectLang")
	proto.RegisterType((*Props)(nil), "pb.Props")
	proto.RegisterType((*BaseInfo)(nil), "pb.BaseInfo")
	proto.RegisterType((*Response)(nil), "pb.Response")
	proto.RegisterType((*ServerSplit)(nil), "pb.ServerSplit")
	proto.RegisterType((*ServerSplit_ServerBtw)(nil), "pb.ServerSplit.ServerBtw")
	proto.RegisterType((*Project)(nil), "pb.Project")
	proto.RegisterType((*Projects)(nil), "pb.Projects")
	proto.RegisterType((*ProjectTagLib)(nil), "pb.ProjectTagLib")
}

func init() {
	proto.RegisterFile("common.proto", fileDescriptor_555bd8c177793206)
}

var fileDescriptor_555bd8c177793206 = []byte{
	// 708 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x8c, 0x53, 0xdd, 0x8f, 0xe3, 0x34,
	0x10, 0x57, 0x9a, 0x7e, 0x24, 0x93, 0xf6, 0x38, 0xac, 0x05, 0xbc, 0x85, 0x13, 0xbd, 0x3c, 0xa0,
	0x4a, 0x48, 0x3d, 0xe9, 0x56, 0x42, 0x88, 0x7d, 0xe1, 0x16, 0xad, 0x50, 0xa4, 0x13, 0xaa, 0x7c,
	0x88, 0x87, 0x7b, 0xa9, 0x9c, 0xd8, 0x2d, 0xa6, 0xa9, 0x6d, 0x92, 0x74, 0x77, 0xf3, 0x84, 0xf8,
	0x8b, 0xf9, 0x17, 0xd0, 0xd8, 0x4e, 0xf9, 0x10, 0x0f, 0xbc, 0xfd, 0x7e, 0xbf, 0x19, 0xcf, 0x8c,
	0xe7, 0x03, 0xe6, 0x95, 0x39, 0x9d, 0x8c, 0xde, 0xd8, 0xc6, 0x74, 0x86, 0x8c, 0x6c, 0xb9, 0xbc,
	0x3e, 0x18, 0x73, 0xa8, 0xe5, 0x2b, 0xa7, 0x94, 0xe7, 0xfd, 0x2b, 0xae, 0x7b, 0x6f, 0xce, 0x67,
	0x30, 0xb9, 0x3f, 0xd9, 0xae, 0xcf, 0xbf, 0x85, 0xf4, 0x5e, 0xf3, 0xb2, 0x96, 0x4c, 0xfe, 0x4a,
	0x3e, 0x85, 0xd4, 0x94, 0xbf, 0xc8, 0xaa, 0xdb, 0x29, 0x41, 0xa3, 0x55, 0xb4, 0x5e, 0xb0, 0xc4,
	0x0b, 0x85, 0x20, 0x1f, 0xc3, 0x54, 0x3a, 0x4f, 0x3a, 0x5a, 0x45, 0xeb, 0x84, 0x05, 0x96, 0xff,
	0x06, 0xc9, 0x5b, 0xae, 0x0f, 0x67, 0x7e, 0x90, 0xe4, 0x2b, 0x48, 0xea, 0x80, 0x69, 0xb4, 0x8a,
	0xd7, 0xd9, 0xeb, 0xe5, 0xc6, 0x96, 0x9b, 0xc1, 0x7e, 0x01, 0xf7, 0xba, 0x6b, 0x7a, 0x76, 0xf1,
	0x5d, 0xde, 0xc2, 0xe2, 0x1f, 0x26, 0xf2, 0x1c, 0xe2, 0xa3, 0xec, 0x5d, 0x0d, 0x29, 0x43, 0x48,
	0xae, 0x60, 0xf2, 0xc0, 0xeb, 0xb3, 0xcf, 0x9e, 0x32, 0x4f, 0xbe, 0x19, 0x7d, 0x1d, 0xe5, 0xb7,
	0x90, 0x6d, 0x1b, 0x83, 0x55, 0x62, 0x0c, 0x42, 0x61, 0x66, 0x3d, 0x0d, 0xcf, 0x07, 0x4a, 0x08,
	0x8c, 0x31, 0x63, 0x88, 0xe0, 0x70, 0x7e, 0x03, 0x93, 0x6d, 0x63, 0x6c, 0x8b, 0xf1, 0x6b, 0x5e,
	0xca, 0x3a, 0x3c, 0xf2, 0xe4, 0xbf, 0xb3, 0xe6, 0x7f, 0xc4, 0x90, 0xdc, 0xf1, 0x56, 0x16, 0x7a,
	0x6f, 0xc8, 0x27, 0x30, 0x3b, 0xf0, 0x93, 0x1c, 0x5a, 0x36, 0x66, 0x53, 0xa4, 0x85, 0x20, 0x2f,
	0x61, 0xee, 0x0c, 0x43, 0x35, 0x3e, 0x44, 0x86, 0xda, 0xf6, 0x5f, 0x15, 0xc5, 0x7f, 0x55, 0x84,
	0xda, 0xf9, 0xac, 0x04, 0x1d, 0x7b, 0x0d, 0x31, 0x79, 0x06, 0x23, 0xd3, 0xd2, 0x89, 0x53, 0x46,
	0xa6, 0x25, 0x9f, 0x43, 0xd6, 0x8a, 0xe3, 0xee, 0x41, 0x36, 0xad, 0x32, 0x9a, 0x4e, 0x9d, 0x01,
	0x5a, 0x71, 0xfc, 0xc9, 0x2b, 0x18, 0x64, 0x6f, 0x95, 0xa0, 0x33, 0x57, 0x91, 0xc3, 0xd8, 0x53,
	0x8c, 0x9b, 0x38, 0x09, 0x21, 0x79, 0x01, 0xc0, 0xab, 0xca, 0x9c, 0xb5, 0x1b, 0x78, 0xea, 0xa2,
	0xa4, 0x41, 0x29, 0x04, 0xfe, 0xac, 0x31, 0xb5, 0xfb, 0x19, 0x38, 0xdb, 0x14, 0x69, 0xe1, 0x22,
	0xb5, 0x4a, 0xd0, 0xcc, 0x4f, 0xa7, 0x55, 0x02, 0x9b, 0x5e, 0xfd, 0xcc, 0xb5, 0x96, 0x35, 0x9d,
	0xfb, 0xa6, 0x07, 0x4a, 0x96, 0x90, 0x68, 0x55, 0x1d, 0x35, 0x3f, 0x49, 0xba, 0x70, 0xa6, 0x0b,
	0x77, 0xaf, 0x30, 0x57, 0xd3, 0xd3, 0x67, 0xe1, 0x95, 0xa7, 0xb8, 0x89, 0x9d, 0xe9, 0x78, 0xbd,
	0xb3, 0xbc, 0xa7, 0x1f, 0xac, 0xa2, 0x75, 0xc4, 0x12, 0x27, 0x6c, 0x79, 0x8f, 0x8d, 0x0d, 0x7e,
	0xbb, 0xca, 0x08, 0x49, 0x9f, 0xfb, 0xc6, 0x06, 0xed, 0x3b, 0x23, 0x24, 0xf9, 0x0c, 0x60, 0x6f,
	0x9f, 0x76, 0xdc, 0x5a, 0xac, 0xfe, 0x43, 0x9f, 0x77, 0x6f, 0x9f, 0xde, 0x58, 0x5b, 0x08, 0xb2,
	0x84, 0xb4, 0x54, 0x83, 0x91, 0xf8, 0xcc, 0xa5, 0xf2, 0xb6, 0x2b, 0x98, 0xb4, 0x95, 0xd4, 0x92,
	0xbe, 0x70, 0xfb, 0xef, 0x49, 0xfe, 0x1e, 0x12, 0x26, 0x5b, 0x6b, 0x74, 0x2b, 0xb1, 0xb7, 0x2e,
	0x2d, 0x4e, 0x7b, 0xc2, 0x1c, 0xc6, 0x8e, 0x9c, 0xda, 0x61, 0xb3, 0x10, 0x92, 0x35, 0x8c, 0x05,
	0xef, 0xb8, 0x1b, 0x6d, 0xf6, 0xfa, 0x6a, 0xe3, 0x6f, 0x71, 0x33, 0xdc, 0xe2, 0xe6, 0x8d, 0xee,
	0x99, 0xf3, 0xc8, 0x7f, 0x8f, 0x20, 0x7b, 0x27, 0x9b, 0x07, 0xd9, 0xbc, 0xb3, 0xb5, 0xea, 0x30,
	0x96, 0x12, 0xad, 0xbb, 0x9f, 0x98, 0x21, 0x24, 0x5f, 0x42, 0x5c, 0x76, 0x8f, 0x74, 0xe4, 0x2e,
	0xea, 0x1a, 0x2f, 0xea, 0x6f, 0xfe, 0x01, 0xdf, 0x75, 0x8f, 0x0c, 0xbd, 0x96, 0x37, 0x90, 0x5e,
	0x14, 0xf7, 0x9b, 0x8e, 0x37, 0xfe, 0x14, 0x62, 0xe6, 0x09, 0x66, 0x90, 0x5a, 0xb8, 0x6a, 0x63,
	0x86, 0x30, 0xff, 0x1e, 0x66, 0xc3, 0x4e, 0xbe, 0x84, 0x79, 0xd8, 0xd8, 0x9d, 0x1b, 0x9a, 0xbf,
	0x87, 0x2c, 0x68, 0x3f, 0xe0, 0xdc, 0xae, 0x21, 0xa9, 0x55, 0xe9, 0xcd, 0xfe, 0xcb, 0xb3, 0x5a,
	0x95, 0x68, 0xca, 0xbf, 0x80, 0x24, 0x04, 0x6a, 0x71, 0xf4, 0xe1, 0x95, 0xff, 0x4d, 0xca, 0x2e,
	0x3c, 0x2f, 0x60, 0x11, 0xfc, 0x7e, 0xe4, 0x87, 0xb7, 0xaa, 0xfc, 0x3f, 0x69, 0x3f, 0x82, 0x29,
	0xa6, 0x55, 0xbe, 0xf2, 0x05, 0x9b, 0xd4, 0xaa, 0x2c, 0xc4, 0xdd, 0xf4, 0xfd, 0x78, 0x73, 0x6b,
	0xcb, 0x72, 0xea, 0x7a, 0x7b, 0xf3, 0x67, 0x00, 0x00, 0x00, 0xff, 0xff, 0xeb, 0xea, 0x2a, 0x9f,
	0x09, 0x05, 0x00, 0x00,
}
