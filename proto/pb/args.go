package pb

type PlayerCommuWatchResult struct {
	Error           error  // 报错信息
	NeedDelWatchKey bool   // 是否需要删除任务 key
	TicketId        uint64 // 工单id
}

// 质检单 - 保存 - req
type ExamineDscOrderSaveDf struct {
	// 质检单-id
	DscExamineId uint64 `protobuf:"varint,1,opt,name=dsc_examine_id,json=dscExamineId,proto3" json:"dsc_examine_id"`
	// 是否是修改单 : true：表示是修改单； false：表示是新增单
	IsModify bool `protobuf:"varint,2,opt,name=is_modify,json=isModify,proto3" json:"is_modify"`
	// 自定义表单数据 eg: {"操作1": ["xxxx","xx"],"操作2": "xxxx"}
	DefineField map[string]any `protobuf:"bytes,20,opt,name=define_field,json=defineField,proto3" json:"define_field"`
	// 通用结果字段
	CommonField *ExamineDscOrderSaveReq_Common `protobuf:"bytes,21,opt,name=common_field,json=commonField,proto3" json:"common_field"`
}

// 质检单 - 详情 - resp
type ExamineDscOrderDetailDf struct {
	// 质检单 - id
	DscExamineId uint64 `protobuf:"varint,1,opt,name=dsc_examine_id,json=dscExamineId,proto3" json:"dsc_examine_id"`
	// 任务id
	TaskId uint64 `protobuf:"varint,2,opt,name=task_id,json=taskId,proto3" json:"task_id"`
	// 模板id
	TplId uint64 `protobuf:"varint,3,opt,name=tpl_id,json=tplId,proto3" json:"tpl_id"`
	// 项目
	Project string `protobuf:"bytes,4,opt,name=project,proto3" json:"project"`
	// 玩家 DC ID
	DscUserId string `protobuf:"bytes,5,opt,name=dsc_user_id,json=dscUserId,proto3" json:"dsc_user_id"`
	// 机器人 id
	DscBotId string `protobuf:"bytes,6,opt,name=dsc_bot_id,json=dscBotId,proto3" json:"dsc_bot_id"`
	// 渠道id
	DscChannelId string `protobuf:"bytes,7,opt,name=dsc_channel_id,json=dscChannelId,proto3" json:"dsc_channel_id"`
	// 质检员
	Inspector string `protobuf:"bytes,8,opt,name=inspector,proto3" json:"inspector"`
	// 质检状态
	Status ExamineStateDf `protobuf:"varint,9,opt,name=status,proto3,enum=pb.ExamineStateDf" json:"status"`
	// 创建时间
	CreatedAt string `protobuf:"bytes,10,opt,name=created_at,json=createdAt,proto3" json:"created_at"`
	// 结案时间
	FinishedAt string `protobuf:"bytes,11,opt,name=finished_at,json=finishedAt,proto3" json:"finished_at"`
	// 是否可以编辑：true:可编辑； false:不可编辑
	CanEdited bool `protobuf:"varint,12,opt,name=can_edited,json=canEdited,proto3" json:"can_edited"`
	// 自定义表单数据
	DefineField map[string]any `protobuf:"bytes,20,opt,name=define_field,json=defineField,proto3" json:"define_field"`
	// 通用结果字段
	CommonField *ExamineDscOrderDetailResp_Common `protobuf:"bytes,21,opt,name=common_field,json=commonField,proto3" json:"common_field"`
}
