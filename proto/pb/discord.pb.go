// Code generated by protoc-gen-go. DO NOT EDIT.
// source: discord.proto

package pb

import (
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

type DscUserListReq struct {
	// 游戏
	Project []string `protobuf:"bytes,1,rep,name=project,proto3" json:"project"`
	// 回复时间
	RepliedAt []string `protobuf:"bytes,2,rep,name=replied_at,json=repliedAt,proto3" json:"replied_at"`
	// 状态 - 1:未回复、2:已回复
	Status []uint32 `protobuf:"varint,3,rep,packed,name=status,proto3" json:"status"`
	// 玩家输入信息 - 模糊查询
	UserContent string `protobuf:"bytes,4,opt,name=user_content,json=userContent,proto3" json:"user_content"`
	// 备注信息 - 玩家备注信息 - 模糊查询
	UserDetailRemark string `protobuf:"bytes,5,opt,name=user_detail_remark,json=userDetailRemark,proto3" json:"user_detail_remark"`
	// 维护人
	Processor []string `protobuf:"bytes,6,rep,name=processor,proto3" json:"processor"`
	// 玩家 DC昵称
	DscUserNickname []string `protobuf:"bytes,7,rep,name=dsc_user_nickname,json=dscUserNickname,proto3" json:"dsc_user_nickname"`
	// 玩家 DC ID
	DscUserId string `protobuf:"bytes,8,opt,name=dsc_user_id,json=dscUserId,proto3" json:"dsc_user_id"`
	Uid       uint64 `protobuf:"varint,9,opt,name=uid,proto3" json:"uid"`
	Fpid      string `protobuf:"bytes,10,opt,name=fpid,proto3" json:"fpid"`
	Sid       string `protobuf:"bytes,11,opt,name=sid,proto3" json:"sid"`
	// 最近登录时间
	LastLogin []string `protobuf:"bytes,12,rep,name=last_login,json=lastLogin,proto3" json:"last_login"`
	// 累计付费金额
	PayAll []int64 `protobuf:"varint,13,rep,packed,name=pay_all,json=payAll,proto3" json:"pay_all"`
	// 最近30天付费金额
	PayLastThirtyDays []int64 `protobuf:"varint,14,rep,packed,name=pay_last_thirty_days,json=payLastThirtyDays,proto3" json:"pay_last_thirty_days"`
	// 玩家VIP状态
	VipState uint32 `protobuf:"varint,15,opt,name=vip_state,json=vipState,proto3" json:"vip_state"`
	// 玩家画像标签--弃用
	Tag      []string `protobuf:"bytes,16,rep,name=tag,proto3" json:"tag"`
	Birthday []string `protobuf:"bytes,17,rep,name=birthday,proto3" json:"birthday"`
	Lang     string   `protobuf:"bytes,18,opt,name=lang,proto3" json:"lang"`
	// 最近处理人
	LastReplyService []string `protobuf:"bytes,19,rep,name=last_reply_service,json=lastReplyService,proto3" json:"last_reply_service"`
	// 排序字段(废弃)
	SortField string `protobuf:"bytes,20,opt,name=sort_field,json=sortField,proto3" json:"sort_field"`
	// 排序顺序 asc升序，desc降序
	Order string `protobuf:"bytes,21,opt,name=order,proto3" json:"order"`
	// @gotags: validate:"required"
	Page uint32 `protobuf:"varint,22,opt,name=page,proto3" json:"page" validate:"required"`
	// @gotags: validate:"required"
	PageSize uint32 `protobuf:"varint,23,opt,name=page_size,json=pageSize,proto3" json:"page_size" validate:"required"`
	// 机器人bot_id
	BotIds []string `protobuf:"bytes,24,rep,name=bot_ids,json=botIds,proto3" json:"bot_ids"`
	// 新用字段
	Uids string `protobuf:"bytes,25,opt,name=uids,proto3" json:"uids"`
	// 新用标签
	Tags []uint32 `protobuf:"varint,26,rep,packed,name=tags,proto3" json:"tags"`
	// 标签类型
	TagType FilterTagEnum `protobuf:"varint,27,opt,name=tag_type,json=tagType,proto3,enum=pb.FilterTagEnum" json:"tag_type"`
	// 排序方式
	SortBy DcPoolSort `protobuf:"varint,28,opt,name=sort_by,json=sortBy,proto3,enum=pb.DcPoolSort" json:"sort_by"`
	// 工单语言
	Language             []string `protobuf:"bytes,29,rep,name=language,proto3" json:"language"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *DscUserListReq) Reset()         { *m = DscUserListReq{} }
func (m *DscUserListReq) String() string { return proto.CompactTextString(m) }
func (*DscUserListReq) ProtoMessage()    {}
func (*DscUserListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_d37f1fb6677774d8, []int{0}
}

func (m *DscUserListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DscUserListReq.Unmarshal(m, b)
}
func (m *DscUserListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DscUserListReq.Marshal(b, m, deterministic)
}
func (m *DscUserListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DscUserListReq.Merge(m, src)
}
func (m *DscUserListReq) XXX_Size() int {
	return xxx_messageInfo_DscUserListReq.Size(m)
}
func (m *DscUserListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DscUserListReq.DiscardUnknown(m)
}

var xxx_messageInfo_DscUserListReq proto.InternalMessageInfo

func (m *DscUserListReq) GetProject() []string {
	if m != nil {
		return m.Project
	}
	return nil
}

func (m *DscUserListReq) GetRepliedAt() []string {
	if m != nil {
		return m.RepliedAt
	}
	return nil
}

func (m *DscUserListReq) GetStatus() []uint32 {
	if m != nil {
		return m.Status
	}
	return nil
}

func (m *DscUserListReq) GetUserContent() string {
	if m != nil {
		return m.UserContent
	}
	return ""
}

func (m *DscUserListReq) GetUserDetailRemark() string {
	if m != nil {
		return m.UserDetailRemark
	}
	return ""
}

func (m *DscUserListReq) GetProcessor() []string {
	if m != nil {
		return m.Processor
	}
	return nil
}

func (m *DscUserListReq) GetDscUserNickname() []string {
	if m != nil {
		return m.DscUserNickname
	}
	return nil
}

func (m *DscUserListReq) GetDscUserId() string {
	if m != nil {
		return m.DscUserId
	}
	return ""
}

func (m *DscUserListReq) GetUid() uint64 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *DscUserListReq) GetFpid() string {
	if m != nil {
		return m.Fpid
	}
	return ""
}

func (m *DscUserListReq) GetSid() string {
	if m != nil {
		return m.Sid
	}
	return ""
}

func (m *DscUserListReq) GetLastLogin() []string {
	if m != nil {
		return m.LastLogin
	}
	return nil
}

func (m *DscUserListReq) GetPayAll() []int64 {
	if m != nil {
		return m.PayAll
	}
	return nil
}

func (m *DscUserListReq) GetPayLastThirtyDays() []int64 {
	if m != nil {
		return m.PayLastThirtyDays
	}
	return nil
}

func (m *DscUserListReq) GetVipState() uint32 {
	if m != nil {
		return m.VipState
	}
	return 0
}

func (m *DscUserListReq) GetTag() []string {
	if m != nil {
		return m.Tag
	}
	return nil
}

func (m *DscUserListReq) GetBirthday() []string {
	if m != nil {
		return m.Birthday
	}
	return nil
}

func (m *DscUserListReq) GetLang() string {
	if m != nil {
		return m.Lang
	}
	return ""
}

func (m *DscUserListReq) GetLastReplyService() []string {
	if m != nil {
		return m.LastReplyService
	}
	return nil
}

func (m *DscUserListReq) GetSortField() string {
	if m != nil {
		return m.SortField
	}
	return ""
}

func (m *DscUserListReq) GetOrder() string {
	if m != nil {
		return m.Order
	}
	return ""
}

func (m *DscUserListReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *DscUserListReq) GetPageSize() uint32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *DscUserListReq) GetBotIds() []string {
	if m != nil {
		return m.BotIds
	}
	return nil
}

func (m *DscUserListReq) GetUids() string {
	if m != nil {
		return m.Uids
	}
	return ""
}

func (m *DscUserListReq) GetTags() []uint32 {
	if m != nil {
		return m.Tags
	}
	return nil
}

func (m *DscUserListReq) GetTagType() FilterTagEnum {
	if m != nil {
		return m.TagType
	}
	return FilterTagEnum_All
}

func (m *DscUserListReq) GetSortBy() DcPoolSort {
	if m != nil {
		return m.SortBy
	}
	return DcPoolSort_DcPoolSortWaitDefault
}

func (m *DscUserListReq) GetLanguage() []string {
	if m != nil {
		return m.Language
	}
	return nil
}

type DscUserListResp struct {
	CurrentPage          uint32                     `protobuf:"varint,1,opt,name=current_page,json=currentPage,proto3" json:"current_page"`
	PerPage              uint32                     `protobuf:"varint,2,opt,name=per_page,json=perPage,proto3" json:"per_page"`
	Total                uint32                     `protobuf:"varint,3,opt,name=total,proto3" json:"total"`
	Data                 []*DscUserListResp_DscUser `protobuf:"bytes,4,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}                   `json:"-" gorm:"-"`
	XXX_unrecognized     []byte                     `json:"-" gorm:"-"`
	XXX_sizecache        int32                      `json:"-" gorm:"-"`
}

func (m *DscUserListResp) Reset()         { *m = DscUserListResp{} }
func (m *DscUserListResp) String() string { return proto.CompactTextString(m) }
func (*DscUserListResp) ProtoMessage()    {}
func (*DscUserListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_d37f1fb6677774d8, []int{1}
}

func (m *DscUserListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DscUserListResp.Unmarshal(m, b)
}
func (m *DscUserListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DscUserListResp.Marshal(b, m, deterministic)
}
func (m *DscUserListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DscUserListResp.Merge(m, src)
}
func (m *DscUserListResp) XXX_Size() int {
	return xxx_messageInfo_DscUserListResp.Size(m)
}
func (m *DscUserListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DscUserListResp.DiscardUnknown(m)
}

var xxx_messageInfo_DscUserListResp proto.InternalMessageInfo

func (m *DscUserListResp) GetCurrentPage() uint32 {
	if m != nil {
		return m.CurrentPage
	}
	return 0
}

func (m *DscUserListResp) GetPerPage() uint32 {
	if m != nil {
		return m.PerPage
	}
	return 0
}

func (m *DscUserListResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *DscUserListResp) GetData() []*DscUserListResp_DscUser {
	if m != nil {
		return m.Data
	}
	return nil
}

type DscUserListResp_DscUser struct {
	// 游戏
	Project string `protobuf:"bytes,1,opt,name=project,proto3" json:"project"`
	// 玩家 DC ID
	DscUserId string `protobuf:"bytes,2,opt,name=dsc_user_id,json=dscUserId,proto3" json:"dsc_user_id"`
	// 玩家名称 - 前端展示使用此字段
	UserName string `protobuf:"bytes,3,opt,name=user_name,json=userName,proto3" json:"user_name"`
	// 玩家 global name
	GlobalName string `protobuf:"bytes,4,opt,name=global_name,json=globalName,proto3" json:"global_name"`
	// dm channel
	DmChannel string `protobuf:"bytes,5,opt,name=dm_channel,json=dmChannel,proto3" json:"dm_channel"`
	// guild id
	GuildId string `protobuf:"bytes,6,opt,name=guild_id,json=guildId,proto3" json:"guild_id"`
	// 当前维护人
	Processor string `protobuf:"bytes,7,opt,name=processor,proto3" json:"processor"`
	// 累付金额
	TotalPay float64 `protobuf:"fixed64,8,opt,name=total_pay,json=totalPay,proto3" json:"total_pay"`
	// 最近30天付费金额
	PayLastThirtyDays float64 `protobuf:"fixed64,9,opt,name=pay_last_thirty_days,json=payLastThirtyDays,proto3" json:"pay_last_thirty_days"`
	// 最近登录时间
	LastLogin string `protobuf:"bytes,10,opt,name=last_login,json=lastLogin,proto3" json:"last_login"`
	// 玩家信息回复状态
	Status uint32 `protobuf:"varint,11,opt,name=status,proto3" json:"status"`
	// VIP 等级
	VipLevel uint32 `protobuf:"varint,12,opt,name=vip_level,json=vipLevel,proto3" json:"vip_level"`
	Fpid     string `protobuf:"bytes,13,opt,name=fpid,proto3" json:"fpid"`
	Uid      uint64 `protobuf:"varint,14,opt,name=uid,proto3" json:"uid"`
	Sid      string `protobuf:"bytes,15,opt,name=sid,proto3" json:"sid"`
	// 机器人 user_id
	BotId string `protobuf:"bytes,16,opt,name=bot_id,json=botId,proto3" json:"bot_id"`
	// 机器人昵称
	BotShow    string `protobuf:"bytes,19,opt,name=bot_show,json=botShow,proto3" json:"bot_show"`
	Note       string `protobuf:"bytes,17,opt,name=note,proto3" json:"note"`
	PlayerNick string `protobuf:"bytes,18,opt,name=player_nick,json=playerNick,proto3" json:"player_nick"`
	Birthday   string `protobuf:"bytes,22,opt,name=birthday,proto3" json:"birthday"`
	Lang       string `protobuf:"bytes,20,opt,name=lang,proto3" json:"lang"`
	Checked    bool   `protobuf:"varint,21,opt,name=checked,proto3" json:"checked"`
	// 客服最后回复时间
	LastReplyTime string `protobuf:"bytes,24,opt,name=last_reply_time,json=lastReplyTime,proto3" json:"last_reply_time"`
	// 玩家等待时长
	WaitingTime string `protobuf:"bytes,25,opt,name=waiting_time,json=waitingTime,proto3" json:"waiting_time"`
	// 七天内工单创建个数
	TicketCreateCount int64 `protobuf:"varint,26,opt,name=ticket_create_count,json=ticketCreateCount,proto3" json:"ticket_create_count"`
	// 七天内最后工单创建时间
	LastTicketCreateTime string `protobuf:"bytes,27,opt,name=last_ticket_create_time,json=lastTicketCreateTime,proto3" json:"last_ticket_create_time"`
	// 七天内创建最近工单ID
	TicketId             uint64   `protobuf:"varint,28,opt,name=ticket_id,json=ticketId,proto3" json:"ticket_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *DscUserListResp_DscUser) Reset()         { *m = DscUserListResp_DscUser{} }
func (m *DscUserListResp_DscUser) String() string { return proto.CompactTextString(m) }
func (*DscUserListResp_DscUser) ProtoMessage()    {}
func (*DscUserListResp_DscUser) Descriptor() ([]byte, []int) {
	return fileDescriptor_d37f1fb6677774d8, []int{1, 0}
}

func (m *DscUserListResp_DscUser) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DscUserListResp_DscUser.Unmarshal(m, b)
}
func (m *DscUserListResp_DscUser) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DscUserListResp_DscUser.Marshal(b, m, deterministic)
}
func (m *DscUserListResp_DscUser) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DscUserListResp_DscUser.Merge(m, src)
}
func (m *DscUserListResp_DscUser) XXX_Size() int {
	return xxx_messageInfo_DscUserListResp_DscUser.Size(m)
}
func (m *DscUserListResp_DscUser) XXX_DiscardUnknown() {
	xxx_messageInfo_DscUserListResp_DscUser.DiscardUnknown(m)
}

var xxx_messageInfo_DscUserListResp_DscUser proto.InternalMessageInfo

func (m *DscUserListResp_DscUser) GetProject() string {
	if m != nil {
		return m.Project
	}
	return ""
}

func (m *DscUserListResp_DscUser) GetDscUserId() string {
	if m != nil {
		return m.DscUserId
	}
	return ""
}

func (m *DscUserListResp_DscUser) GetUserName() string {
	if m != nil {
		return m.UserName
	}
	return ""
}

func (m *DscUserListResp_DscUser) GetGlobalName() string {
	if m != nil {
		return m.GlobalName
	}
	return ""
}

func (m *DscUserListResp_DscUser) GetDmChannel() string {
	if m != nil {
		return m.DmChannel
	}
	return ""
}

func (m *DscUserListResp_DscUser) GetGuildId() string {
	if m != nil {
		return m.GuildId
	}
	return ""
}

func (m *DscUserListResp_DscUser) GetProcessor() string {
	if m != nil {
		return m.Processor
	}
	return ""
}

func (m *DscUserListResp_DscUser) GetTotalPay() float64 {
	if m != nil {
		return m.TotalPay
	}
	return 0
}

func (m *DscUserListResp_DscUser) GetPayLastThirtyDays() float64 {
	if m != nil {
		return m.PayLastThirtyDays
	}
	return 0
}

func (m *DscUserListResp_DscUser) GetLastLogin() string {
	if m != nil {
		return m.LastLogin
	}
	return ""
}

func (m *DscUserListResp_DscUser) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *DscUserListResp_DscUser) GetVipLevel() uint32 {
	if m != nil {
		return m.VipLevel
	}
	return 0
}

func (m *DscUserListResp_DscUser) GetFpid() string {
	if m != nil {
		return m.Fpid
	}
	return ""
}

func (m *DscUserListResp_DscUser) GetUid() uint64 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *DscUserListResp_DscUser) GetSid() string {
	if m != nil {
		return m.Sid
	}
	return ""
}

func (m *DscUserListResp_DscUser) GetBotId() string {
	if m != nil {
		return m.BotId
	}
	return ""
}

func (m *DscUserListResp_DscUser) GetBotShow() string {
	if m != nil {
		return m.BotShow
	}
	return ""
}

func (m *DscUserListResp_DscUser) GetNote() string {
	if m != nil {
		return m.Note
	}
	return ""
}

func (m *DscUserListResp_DscUser) GetPlayerNick() string {
	if m != nil {
		return m.PlayerNick
	}
	return ""
}

func (m *DscUserListResp_DscUser) GetBirthday() string {
	if m != nil {
		return m.Birthday
	}
	return ""
}

func (m *DscUserListResp_DscUser) GetLang() string {
	if m != nil {
		return m.Lang
	}
	return ""
}

func (m *DscUserListResp_DscUser) GetChecked() bool {
	if m != nil {
		return m.Checked
	}
	return false
}

func (m *DscUserListResp_DscUser) GetLastReplyTime() string {
	if m != nil {
		return m.LastReplyTime
	}
	return ""
}

func (m *DscUserListResp_DscUser) GetWaitingTime() string {
	if m != nil {
		return m.WaitingTime
	}
	return ""
}

func (m *DscUserListResp_DscUser) GetTicketCreateCount() int64 {
	if m != nil {
		return m.TicketCreateCount
	}
	return 0
}

func (m *DscUserListResp_DscUser) GetLastTicketCreateTime() string {
	if m != nil {
		return m.LastTicketCreateTime
	}
	return ""
}

func (m *DscUserListResp_DscUser) GetTicketId() uint64 {
	if m != nil {
		return m.TicketId
	}
	return 0
}

type DscUserDetailReq struct {
	// 玩家 DC ID
	DscUserId            string   `protobuf:"bytes,1,opt,name=dsc_user_id,json=dscUserId,proto3" json:"dsc_user_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *DscUserDetailReq) Reset()         { *m = DscUserDetailReq{} }
func (m *DscUserDetailReq) String() string { return proto.CompactTextString(m) }
func (*DscUserDetailReq) ProtoMessage()    {}
func (*DscUserDetailReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_d37f1fb6677774d8, []int{2}
}

func (m *DscUserDetailReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DscUserDetailReq.Unmarshal(m, b)
}
func (m *DscUserDetailReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DscUserDetailReq.Marshal(b, m, deterministic)
}
func (m *DscUserDetailReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DscUserDetailReq.Merge(m, src)
}
func (m *DscUserDetailReq) XXX_Size() int {
	return xxx_messageInfo_DscUserDetailReq.Size(m)
}
func (m *DscUserDetailReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DscUserDetailReq.DiscardUnknown(m)
}

var xxx_messageInfo_DscUserDetailReq proto.InternalMessageInfo

func (m *DscUserDetailReq) GetDscUserId() string {
	if m != nil {
		return m.DscUserId
	}
	return ""
}

type DscUserDetailResp struct {
	// 基本信息
	UserDetail           *DscUserDetailResp_UserDetail `protobuf:"bytes,10,opt,name=user_detail,json=userDetail,proto3" json:"user_detail"`
	XXX_NoUnkeyedLiteral struct{}                      `json:"-" gorm:"-"`
	XXX_unrecognized     []byte                        `json:"-" gorm:"-"`
	XXX_sizecache        int32                         `json:"-" gorm:"-"`
}

func (m *DscUserDetailResp) Reset()         { *m = DscUserDetailResp{} }
func (m *DscUserDetailResp) String() string { return proto.CompactTextString(m) }
func (*DscUserDetailResp) ProtoMessage()    {}
func (*DscUserDetailResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_d37f1fb6677774d8, []int{3}
}

func (m *DscUserDetailResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DscUserDetailResp.Unmarshal(m, b)
}
func (m *DscUserDetailResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DscUserDetailResp.Marshal(b, m, deterministic)
}
func (m *DscUserDetailResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DscUserDetailResp.Merge(m, src)
}
func (m *DscUserDetailResp) XXX_Size() int {
	return xxx_messageInfo_DscUserDetailResp.Size(m)
}
func (m *DscUserDetailResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DscUserDetailResp.DiscardUnknown(m)
}

var xxx_messageInfo_DscUserDetailResp proto.InternalMessageInfo

func (m *DscUserDetailResp) GetUserDetail() *DscUserDetailResp_UserDetail {
	if m != nil {
		return m.UserDetail
	}
	return nil
}

type DscUserDetailResp_UserDetail struct {
	// 游戏
	Project string `protobuf:"bytes,1,opt,name=project,proto3" json:"project"`
	// 玩家 DC ID
	DscUserId string `protobuf:"bytes,2,opt,name=dsc_user_id,json=dscUserId,proto3" json:"dsc_user_id"`
	// 玩家名称 - 前端展示使用此字段
	UserName string `protobuf:"bytes,3,opt,name=user_name,json=userName,proto3" json:"user_name"`
	// 玩家 global name
	GlobalName string `protobuf:"bytes,4,opt,name=global_name,json=globalName,proto3" json:"global_name"`
	// dm channel
	DmChannel string `protobuf:"bytes,5,opt,name=dm_channel,json=dmChannel,proto3" json:"dm_channel"`
	// guild id
	GuildId string `protobuf:"bytes,6,opt,name=guild_id,json=guildId,proto3" json:"guild_id"`
	// 当前维护人
	Processor            string   `protobuf:"bytes,9,opt,name=processor,proto3" json:"processor"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *DscUserDetailResp_UserDetail) Reset()         { *m = DscUserDetailResp_UserDetail{} }
func (m *DscUserDetailResp_UserDetail) String() string { return proto.CompactTextString(m) }
func (*DscUserDetailResp_UserDetail) ProtoMessage()    {}
func (*DscUserDetailResp_UserDetail) Descriptor() ([]byte, []int) {
	return fileDescriptor_d37f1fb6677774d8, []int{3, 0}
}

func (m *DscUserDetailResp_UserDetail) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DscUserDetailResp_UserDetail.Unmarshal(m, b)
}
func (m *DscUserDetailResp_UserDetail) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DscUserDetailResp_UserDetail.Marshal(b, m, deterministic)
}
func (m *DscUserDetailResp_UserDetail) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DscUserDetailResp_UserDetail.Merge(m, src)
}
func (m *DscUserDetailResp_UserDetail) XXX_Size() int {
	return xxx_messageInfo_DscUserDetailResp_UserDetail.Size(m)
}
func (m *DscUserDetailResp_UserDetail) XXX_DiscardUnknown() {
	xxx_messageInfo_DscUserDetailResp_UserDetail.DiscardUnknown(m)
}

var xxx_messageInfo_DscUserDetailResp_UserDetail proto.InternalMessageInfo

func (m *DscUserDetailResp_UserDetail) GetProject() string {
	if m != nil {
		return m.Project
	}
	return ""
}

func (m *DscUserDetailResp_UserDetail) GetDscUserId() string {
	if m != nil {
		return m.DscUserId
	}
	return ""
}

func (m *DscUserDetailResp_UserDetail) GetUserName() string {
	if m != nil {
		return m.UserName
	}
	return ""
}

func (m *DscUserDetailResp_UserDetail) GetGlobalName() string {
	if m != nil {
		return m.GlobalName
	}
	return ""
}

func (m *DscUserDetailResp_UserDetail) GetDmChannel() string {
	if m != nil {
		return m.DmChannel
	}
	return ""
}

func (m *DscUserDetailResp_UserDetail) GetGuildId() string {
	if m != nil {
		return m.GuildId
	}
	return ""
}

func (m *DscUserDetailResp_UserDetail) GetProcessor() string {
	if m != nil {
		return m.Processor
	}
	return ""
}

type DscChannelDialogReq struct {
	// 玩家 DC ID - 必传
	ChannelId string `protobuf:"bytes,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id"`
	// 向上翻页 - 开始消息id - 对应 msg_id
	Before string `protobuf:"bytes,2,opt,name=before,proto3" json:"before"`
	// 向下翻页 - 开始消息id - 对应 msg_id
	After string `protobuf:"bytes,3,opt,name=after,proto3" json:"after"`
	// 单页限定条数 -  默认 50条
	Limit int64 `protobuf:"varint,4,opt,name=limit,proto3" json:"limit"`
	// 指定查询 msg_id
	MsgIds               []string `protobuf:"bytes,5,rep,name=msg_ids,json=msgIds,proto3" json:"msg_ids"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *DscChannelDialogReq) Reset()         { *m = DscChannelDialogReq{} }
func (m *DscChannelDialogReq) String() string { return proto.CompactTextString(m) }
func (*DscChannelDialogReq) ProtoMessage()    {}
func (*DscChannelDialogReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_d37f1fb6677774d8, []int{4}
}

func (m *DscChannelDialogReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DscChannelDialogReq.Unmarshal(m, b)
}
func (m *DscChannelDialogReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DscChannelDialogReq.Marshal(b, m, deterministic)
}
func (m *DscChannelDialogReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DscChannelDialogReq.Merge(m, src)
}
func (m *DscChannelDialogReq) XXX_Size() int {
	return xxx_messageInfo_DscChannelDialogReq.Size(m)
}
func (m *DscChannelDialogReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DscChannelDialogReq.DiscardUnknown(m)
}

var xxx_messageInfo_DscChannelDialogReq proto.InternalMessageInfo

func (m *DscChannelDialogReq) GetChannelId() string {
	if m != nil {
		return m.ChannelId
	}
	return ""
}

func (m *DscChannelDialogReq) GetBefore() string {
	if m != nil {
		return m.Before
	}
	return ""
}

func (m *DscChannelDialogReq) GetAfter() string {
	if m != nil {
		return m.After
	}
	return ""
}

func (m *DscChannelDialogReq) GetLimit() int64 {
	if m != nil {
		return m.Limit
	}
	return 0
}

func (m *DscChannelDialogReq) GetMsgIds() []string {
	if m != nil {
		return m.MsgIds
	}
	return nil
}

type DscDialogDetailResp struct {
	DialogList           []*DscDialogDetail `protobuf:"bytes,1,rep,name=dialog_list,json=dialogList,proto3" json:"dialog_list"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-" gorm:"-"`
	XXX_unrecognized     []byte             `json:"-" gorm:"-"`
	XXX_sizecache        int32              `json:"-" gorm:"-"`
}

func (m *DscDialogDetailResp) Reset()         { *m = DscDialogDetailResp{} }
func (m *DscDialogDetailResp) String() string { return proto.CompactTextString(m) }
func (*DscDialogDetailResp) ProtoMessage()    {}
func (*DscDialogDetailResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_d37f1fb6677774d8, []int{5}
}

func (m *DscDialogDetailResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DscDialogDetailResp.Unmarshal(m, b)
}
func (m *DscDialogDetailResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DscDialogDetailResp.Marshal(b, m, deterministic)
}
func (m *DscDialogDetailResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DscDialogDetailResp.Merge(m, src)
}
func (m *DscDialogDetailResp) XXX_Size() int {
	return xxx_messageInfo_DscDialogDetailResp.Size(m)
}
func (m *DscDialogDetailResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DscDialogDetailResp.DiscardUnknown(m)
}

var xxx_messageInfo_DscDialogDetailResp proto.InternalMessageInfo

func (m *DscDialogDetailResp) GetDialogList() []*DscDialogDetail {
	if m != nil {
		return m.DialogList
	}
	return nil
}

type DscDialogDetail struct {
	// 消息 id
	MsgId string `protobuf:"bytes,1,opt,name=msg_id,json=msgId,proto3" json:"msg_id"`
	// project
	Project string `protobuf:"bytes,2,opt,name=project,proto3" json:"project"`
	// 消息来源id
	FromUserId string `protobuf:"bytes,3,opt,name=from_user_id,json=fromUserId,proto3" json:"from_user_id"`
	// dm channel_id
	ChannelId string `protobuf:"bytes,4,opt,name=channel_id,json=channelId,proto3" json:"channel_id"`
	// 消息时间格式
	CreatedAt string `protobuf:"bytes,6,opt,name=created_at,json=createdAt,proto3" json:"created_at"`
	// 消息是否被修改
	IsEdited bool `protobuf:"varint,7,opt,name=is_edited,json=isEdited,proto3" json:"is_edited"`
	// author : 消息发送人详情
	Author *DscAuthor `protobuf:"bytes,8,opt,name=author,proto3" json:"author"`
	// 文本消息 - 内容
	Content string `protobuf:"bytes,9,opt,name=content,proto3" json:"content"`
	// 附件消息 - 附件列表
	Attach []*DscMsgAttach `protobuf:"bytes,10,rep,name=attach,proto3" json:"attach"`
	// embeds - any embedded content
	Embed []*DscMsgEmbed `protobuf:"bytes,11,rep,name=embed,proto3" json:"embed"`
	// 贴纸 - sticker_items
	Stickers []*DscMsgSticker `protobuf:"bytes,16,rep,name=stickers,proto3" json:"stickers"`
	// 所有反应
	Reactions []*DscMsgReaction `protobuf:"bytes,12,rep,name=reactions,proto3" json:"reactions"`
	// 投票 @gotags: json:"poll,omitempty"
	Poll *DscMsgPoll `protobuf:"bytes,13,opt,name=poll,proto3" json:"poll,omitempty"`
	// 针对特定一条消息回复
	ReferencedMsgId string `protobuf:"bytes,14,opt,name=referenced_msg_id,json=referencedMsgId,proto3" json:"referenced_msg_id"`
	// 针对特定一条消息回复-原消息详情 @gotags: json:"referenced_msg,omitempty"
	ReferencedMsg        *DscDialogDetail `protobuf:"bytes,15,opt,name=referenced_msg,json=referencedMsg,proto3" json:"referenced_msg,omitempty"`
	Checked              bool             `protobuf:"varint,17,opt,name=checked,proto3" json:"checked"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-" gorm:"-"`
	XXX_unrecognized     []byte           `json:"-" gorm:"-"`
	XXX_sizecache        int32            `json:"-" gorm:"-"`
}

func (m *DscDialogDetail) Reset()         { *m = DscDialogDetail{} }
func (m *DscDialogDetail) String() string { return proto.CompactTextString(m) }
func (*DscDialogDetail) ProtoMessage()    {}
func (*DscDialogDetail) Descriptor() ([]byte, []int) {
	return fileDescriptor_d37f1fb6677774d8, []int{6}
}

func (m *DscDialogDetail) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DscDialogDetail.Unmarshal(m, b)
}
func (m *DscDialogDetail) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DscDialogDetail.Marshal(b, m, deterministic)
}
func (m *DscDialogDetail) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DscDialogDetail.Merge(m, src)
}
func (m *DscDialogDetail) XXX_Size() int {
	return xxx_messageInfo_DscDialogDetail.Size(m)
}
func (m *DscDialogDetail) XXX_DiscardUnknown() {
	xxx_messageInfo_DscDialogDetail.DiscardUnknown(m)
}

var xxx_messageInfo_DscDialogDetail proto.InternalMessageInfo

func (m *DscDialogDetail) GetMsgId() string {
	if m != nil {
		return m.MsgId
	}
	return ""
}

func (m *DscDialogDetail) GetProject() string {
	if m != nil {
		return m.Project
	}
	return ""
}

func (m *DscDialogDetail) GetFromUserId() string {
	if m != nil {
		return m.FromUserId
	}
	return ""
}

func (m *DscDialogDetail) GetChannelId() string {
	if m != nil {
		return m.ChannelId
	}
	return ""
}

func (m *DscDialogDetail) GetCreatedAt() string {
	if m != nil {
		return m.CreatedAt
	}
	return ""
}

func (m *DscDialogDetail) GetIsEdited() bool {
	if m != nil {
		return m.IsEdited
	}
	return false
}

func (m *DscDialogDetail) GetAuthor() *DscAuthor {
	if m != nil {
		return m.Author
	}
	return nil
}

func (m *DscDialogDetail) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *DscDialogDetail) GetAttach() []*DscMsgAttach {
	if m != nil {
		return m.Attach
	}
	return nil
}

func (m *DscDialogDetail) GetEmbed() []*DscMsgEmbed {
	if m != nil {
		return m.Embed
	}
	return nil
}

func (m *DscDialogDetail) GetStickers() []*DscMsgSticker {
	if m != nil {
		return m.Stickers
	}
	return nil
}

func (m *DscDialogDetail) GetReactions() []*DscMsgReaction {
	if m != nil {
		return m.Reactions
	}
	return nil
}

func (m *DscDialogDetail) GetPoll() *DscMsgPoll {
	if m != nil {
		return m.Poll
	}
	return nil
}

func (m *DscDialogDetail) GetReferencedMsgId() string {
	if m != nil {
		return m.ReferencedMsgId
	}
	return ""
}

func (m *DscDialogDetail) GetReferencedMsg() *DscDialogDetail {
	if m != nil {
		return m.ReferencedMsg
	}
	return nil
}

func (m *DscDialogDetail) GetChecked() bool {
	if m != nil {
		return m.Checked
	}
	return false
}

type DscMsgSticker struct {
	// 贴纸id
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id"`
	// 贴纸name
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name"`
	// 贴纸 类型：StickerFormat： 1: PNG, 2: APNG, 3: LOTTIE， 4: GIF ： 参考：https://discord.com/developers/docs/resources/sticker#sticker-object-sticker-format-types
	FormatType           int32    `protobuf:"varint,3,opt,name=format_type,json=formatType,proto3" json:"format_type"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *DscMsgSticker) Reset()         { *m = DscMsgSticker{} }
func (m *DscMsgSticker) String() string { return proto.CompactTextString(m) }
func (*DscMsgSticker) ProtoMessage()    {}
func (*DscMsgSticker) Descriptor() ([]byte, []int) {
	return fileDescriptor_d37f1fb6677774d8, []int{7}
}

func (m *DscMsgSticker) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DscMsgSticker.Unmarshal(m, b)
}
func (m *DscMsgSticker) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DscMsgSticker.Marshal(b, m, deterministic)
}
func (m *DscMsgSticker) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DscMsgSticker.Merge(m, src)
}
func (m *DscMsgSticker) XXX_Size() int {
	return xxx_messageInfo_DscMsgSticker.Size(m)
}
func (m *DscMsgSticker) XXX_DiscardUnknown() {
	xxx_messageInfo_DscMsgSticker.DiscardUnknown(m)
}

var xxx_messageInfo_DscMsgSticker proto.InternalMessageInfo

func (m *DscMsgSticker) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *DscMsgSticker) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *DscMsgSticker) GetFormatType() int32 {
	if m != nil {
		return m.FormatType
	}
	return 0
}

type DscMsgReaction struct {
	// 消息id
	MsgId string `protobuf:"bytes,1,opt,name=msg_id,json=msgId,proto3" json:"msg_id"`
	// 表情
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name"`
	// 添加表情的用户 user_id
	UserId string `protobuf:"bytes,3,opt,name=user_id,json=userId,proto3" json:"user_id"`
	// 用户名称
	UserName string `protobuf:"bytes,4,opt,name=user_name,json=userName,proto3" json:"user_name"`
	// 表情添加时间
	CreatedAt            string   `protobuf:"bytes,5,opt,name=created_at,json=createdAt,proto3" json:"created_at"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *DscMsgReaction) Reset()         { *m = DscMsgReaction{} }
func (m *DscMsgReaction) String() string { return proto.CompactTextString(m) }
func (*DscMsgReaction) ProtoMessage()    {}
func (*DscMsgReaction) Descriptor() ([]byte, []int) {
	return fileDescriptor_d37f1fb6677774d8, []int{8}
}

func (m *DscMsgReaction) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DscMsgReaction.Unmarshal(m, b)
}
func (m *DscMsgReaction) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DscMsgReaction.Marshal(b, m, deterministic)
}
func (m *DscMsgReaction) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DscMsgReaction.Merge(m, src)
}
func (m *DscMsgReaction) XXX_Size() int {
	return xxx_messageInfo_DscMsgReaction.Size(m)
}
func (m *DscMsgReaction) XXX_DiscardUnknown() {
	xxx_messageInfo_DscMsgReaction.DiscardUnknown(m)
}

var xxx_messageInfo_DscMsgReaction proto.InternalMessageInfo

func (m *DscMsgReaction) GetMsgId() string {
	if m != nil {
		return m.MsgId
	}
	return ""
}

func (m *DscMsgReaction) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

func (m *DscMsgReaction) GetUserId() string {
	if m != nil {
		return m.UserId
	}
	return ""
}

func (m *DscMsgReaction) GetUserName() string {
	if m != nil {
		return m.UserName
	}
	return ""
}

func (m *DscMsgReaction) GetCreatedAt() string {
	if m != nil {
		return m.CreatedAt
	}
	return ""
}

type DscMsgAttach struct {
	// 附件id
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id"`
	// 附件url
	Url string `protobuf:"bytes,2,opt,name=url,proto3" json:"url"`
	// 附件代理url
	ProxyUrl string `protobuf:"bytes,3,opt,name=proxy_url,json=proxyUrl,proto3" json:"proxy_url"`
	// 附件名称
	Filename string `protobuf:"bytes,4,opt,name=filename,proto3" json:"filename"`
	// 附件类型: the attachment's media type : https://en.wikipedia.org/wiki/Media_type
	ContentType string `protobuf:"bytes,5,opt,name=content_type,json=contentType,proto3" json:"content_type"`
	// 附件宽度
	Width int32 `protobuf:"varint,6,opt,name=width,proto3" json:"width"`
	// 附件高度
	Height int32 `protobuf:"varint,7,opt,name=height,proto3" json:"height"`
	// 附件大小
	Size int32 `protobuf:"varint,8,opt,name=size,proto3" json:"size"`
	// 是否是临时附件
	Ephemeral            bool     `protobuf:"varint,9,opt,name=ephemeral,proto3" json:"ephemeral"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *DscMsgAttach) Reset()         { *m = DscMsgAttach{} }
func (m *DscMsgAttach) String() string { return proto.CompactTextString(m) }
func (*DscMsgAttach) ProtoMessage()    {}
func (*DscMsgAttach) Descriptor() ([]byte, []int) {
	return fileDescriptor_d37f1fb6677774d8, []int{9}
}

func (m *DscMsgAttach) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DscMsgAttach.Unmarshal(m, b)
}
func (m *DscMsgAttach) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DscMsgAttach.Marshal(b, m, deterministic)
}
func (m *DscMsgAttach) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DscMsgAttach.Merge(m, src)
}
func (m *DscMsgAttach) XXX_Size() int {
	return xxx_messageInfo_DscMsgAttach.Size(m)
}
func (m *DscMsgAttach) XXX_DiscardUnknown() {
	xxx_messageInfo_DscMsgAttach.DiscardUnknown(m)
}

var xxx_messageInfo_DscMsgAttach proto.InternalMessageInfo

func (m *DscMsgAttach) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *DscMsgAttach) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

func (m *DscMsgAttach) GetProxyUrl() string {
	if m != nil {
		return m.ProxyUrl
	}
	return ""
}

func (m *DscMsgAttach) GetFilename() string {
	if m != nil {
		return m.Filename
	}
	return ""
}

func (m *DscMsgAttach) GetContentType() string {
	if m != nil {
		return m.ContentType
	}
	return ""
}

func (m *DscMsgAttach) GetWidth() int32 {
	if m != nil {
		return m.Width
	}
	return 0
}

func (m *DscMsgAttach) GetHeight() int32 {
	if m != nil {
		return m.Height
	}
	return 0
}

func (m *DscMsgAttach) GetSize() int32 {
	if m != nil {
		return m.Size
	}
	return 0
}

func (m *DscMsgAttach) GetEphemeral() bool {
	if m != nil {
		return m.Ephemeral
	}
	return false
}

type DscMsgEmbed struct {
	// 标题
	Title string `protobuf:"bytes,1,opt,name=title,proto3" json:"title"`
	// type: "rich", "image", "video", "gifv", "article", "link"
	Type string `protobuf:"bytes,2,opt,name=type,proto3" json:"type"`
	// 描述
	Description string `protobuf:"bytes,3,opt,name=description,proto3" json:"description"`
	// url
	Url string `protobuf:"bytes,4,opt,name=url,proto3" json:"url"`
	// color
	Color int64 `protobuf:"varint,5,opt,name=color,proto3" json:"color"`
	// 来源标识：MessageEmbedProvider is a part of a MessageEmbed struct.
	Provider *DscMsgEmbed_Provider `protobuf:"bytes,6,opt,name=provider,proto3" json:"provider"`
	// MessageEmbedThumbnail is a part of a MessageEmbed struct.
	Thumbnail *DscMsgEmbed_Thumbnail `protobuf:"bytes,7,opt,name=thumbnail,proto3" json:"thumbnail"`
	// MessageEmbedVideo is a part of a MessageEmbed struct.
	Video                *DscMsgEmbed_Video `protobuf:"bytes,8,opt,name=video,proto3" json:"video"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-" gorm:"-"`
	XXX_unrecognized     []byte             `json:"-" gorm:"-"`
	XXX_sizecache        int32              `json:"-" gorm:"-"`
}

func (m *DscMsgEmbed) Reset()         { *m = DscMsgEmbed{} }
func (m *DscMsgEmbed) String() string { return proto.CompactTextString(m) }
func (*DscMsgEmbed) ProtoMessage()    {}
func (*DscMsgEmbed) Descriptor() ([]byte, []int) {
	return fileDescriptor_d37f1fb6677774d8, []int{10}
}

func (m *DscMsgEmbed) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DscMsgEmbed.Unmarshal(m, b)
}
func (m *DscMsgEmbed) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DscMsgEmbed.Marshal(b, m, deterministic)
}
func (m *DscMsgEmbed) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DscMsgEmbed.Merge(m, src)
}
func (m *DscMsgEmbed) XXX_Size() int {
	return xxx_messageInfo_DscMsgEmbed.Size(m)
}
func (m *DscMsgEmbed) XXX_DiscardUnknown() {
	xxx_messageInfo_DscMsgEmbed.DiscardUnknown(m)
}

var xxx_messageInfo_DscMsgEmbed proto.InternalMessageInfo

func (m *DscMsgEmbed) GetTitle() string {
	if m != nil {
		return m.Title
	}
	return ""
}

func (m *DscMsgEmbed) GetType() string {
	if m != nil {
		return m.Type
	}
	return ""
}

func (m *DscMsgEmbed) GetDescription() string {
	if m != nil {
		return m.Description
	}
	return ""
}

func (m *DscMsgEmbed) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

func (m *DscMsgEmbed) GetColor() int64 {
	if m != nil {
		return m.Color
	}
	return 0
}

func (m *DscMsgEmbed) GetProvider() *DscMsgEmbed_Provider {
	if m != nil {
		return m.Provider
	}
	return nil
}

func (m *DscMsgEmbed) GetThumbnail() *DscMsgEmbed_Thumbnail {
	if m != nil {
		return m.Thumbnail
	}
	return nil
}

func (m *DscMsgEmbed) GetVideo() *DscMsgEmbed_Video {
	if m != nil {
		return m.Video
	}
	return nil
}

type DscMsgEmbed_Provider struct {
	// 来源地址
	Url string `protobuf:"bytes,1,opt,name=url,proto3" json:"url"`
	// 来源名称
	Name                 string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *DscMsgEmbed_Provider) Reset()         { *m = DscMsgEmbed_Provider{} }
func (m *DscMsgEmbed_Provider) String() string { return proto.CompactTextString(m) }
func (*DscMsgEmbed_Provider) ProtoMessage()    {}
func (*DscMsgEmbed_Provider) Descriptor() ([]byte, []int) {
	return fileDescriptor_d37f1fb6677774d8, []int{10, 0}
}

func (m *DscMsgEmbed_Provider) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DscMsgEmbed_Provider.Unmarshal(m, b)
}
func (m *DscMsgEmbed_Provider) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DscMsgEmbed_Provider.Marshal(b, m, deterministic)
}
func (m *DscMsgEmbed_Provider) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DscMsgEmbed_Provider.Merge(m, src)
}
func (m *DscMsgEmbed_Provider) XXX_Size() int {
	return xxx_messageInfo_DscMsgEmbed_Provider.Size(m)
}
func (m *DscMsgEmbed_Provider) XXX_DiscardUnknown() {
	xxx_messageInfo_DscMsgEmbed_Provider.DiscardUnknown(m)
}

var xxx_messageInfo_DscMsgEmbed_Provider proto.InternalMessageInfo

func (m *DscMsgEmbed_Provider) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

func (m *DscMsgEmbed_Provider) GetName() string {
	if m != nil {
		return m.Name
	}
	return ""
}

type DscMsgEmbed_Thumbnail struct {
	// url
	Url string `protobuf:"bytes,1,opt,name=url,proto3" json:"url"`
	// proxy url
	ProxyUrl string `protobuf:"bytes,2,opt,name=proxy_url,json=proxyUrl,proto3" json:"proxy_url"`
	// width
	Width int32 `protobuf:"varint,3,opt,name=width,proto3" json:"width"`
	// height
	Height               int32    `protobuf:"varint,4,opt,name=height,proto3" json:"height"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *DscMsgEmbed_Thumbnail) Reset()         { *m = DscMsgEmbed_Thumbnail{} }
func (m *DscMsgEmbed_Thumbnail) String() string { return proto.CompactTextString(m) }
func (*DscMsgEmbed_Thumbnail) ProtoMessage()    {}
func (*DscMsgEmbed_Thumbnail) Descriptor() ([]byte, []int) {
	return fileDescriptor_d37f1fb6677774d8, []int{10, 1}
}

func (m *DscMsgEmbed_Thumbnail) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DscMsgEmbed_Thumbnail.Unmarshal(m, b)
}
func (m *DscMsgEmbed_Thumbnail) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DscMsgEmbed_Thumbnail.Marshal(b, m, deterministic)
}
func (m *DscMsgEmbed_Thumbnail) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DscMsgEmbed_Thumbnail.Merge(m, src)
}
func (m *DscMsgEmbed_Thumbnail) XXX_Size() int {
	return xxx_messageInfo_DscMsgEmbed_Thumbnail.Size(m)
}
func (m *DscMsgEmbed_Thumbnail) XXX_DiscardUnknown() {
	xxx_messageInfo_DscMsgEmbed_Thumbnail.DiscardUnknown(m)
}

var xxx_messageInfo_DscMsgEmbed_Thumbnail proto.InternalMessageInfo

func (m *DscMsgEmbed_Thumbnail) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

func (m *DscMsgEmbed_Thumbnail) GetProxyUrl() string {
	if m != nil {
		return m.ProxyUrl
	}
	return ""
}

func (m *DscMsgEmbed_Thumbnail) GetWidth() int32 {
	if m != nil {
		return m.Width
	}
	return 0
}

func (m *DscMsgEmbed_Thumbnail) GetHeight() int32 {
	if m != nil {
		return m.Height
	}
	return 0
}

type DscMsgEmbed_Video struct {
	Url                  string   `protobuf:"bytes,1,opt,name=url,proto3" json:"url"`
	Width                int32    `protobuf:"varint,2,opt,name=width,proto3" json:"width"`
	Height               int32    `protobuf:"varint,3,opt,name=height,proto3" json:"height"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *DscMsgEmbed_Video) Reset()         { *m = DscMsgEmbed_Video{} }
func (m *DscMsgEmbed_Video) String() string { return proto.CompactTextString(m) }
func (*DscMsgEmbed_Video) ProtoMessage()    {}
func (*DscMsgEmbed_Video) Descriptor() ([]byte, []int) {
	return fileDescriptor_d37f1fb6677774d8, []int{10, 2}
}

func (m *DscMsgEmbed_Video) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DscMsgEmbed_Video.Unmarshal(m, b)
}
func (m *DscMsgEmbed_Video) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DscMsgEmbed_Video.Marshal(b, m, deterministic)
}
func (m *DscMsgEmbed_Video) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DscMsgEmbed_Video.Merge(m, src)
}
func (m *DscMsgEmbed_Video) XXX_Size() int {
	return xxx_messageInfo_DscMsgEmbed_Video.Size(m)
}
func (m *DscMsgEmbed_Video) XXX_DiscardUnknown() {
	xxx_messageInfo_DscMsgEmbed_Video.DiscardUnknown(m)
}

var xxx_messageInfo_DscMsgEmbed_Video proto.InternalMessageInfo

func (m *DscMsgEmbed_Video) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

func (m *DscMsgEmbed_Video) GetWidth() int32 {
	if m != nil {
		return m.Width
	}
	return 0
}

func (m *DscMsgEmbed_Video) GetHeight() int32 {
	if m != nil {
		return m.Height
	}
	return 0
}

type DscMsgPoll struct {
	// 问题描述
	QuestionText string `protobuf:"bytes,1,opt,name=question_text,json=questionText,proto3" json:"question_text"`
	// 答案列表
	Answers []*DscMsgPollAnswer `protobuf:"bytes,2,rep,name=answers,proto3" json:"answers"`
	// 是否允许多选
	AllowMultiselect bool `protobuf:"varint,3,opt,name=allow_multiselect,json=allowMultiselect,proto3" json:"allow_multiselect"`
	// NOTE: should be set only on creation, when fetching use Expiry.
	Duration int32 `protobuf:"varint,4,opt,name=duration,proto3" json:"duration"`
	// NOTE: as Discord documentation notes, this field might be null even when fetching.
	Expiry uint64 `protobuf:"varint,5,opt,name=expiry,proto3" json:"expiry"`
	// results
	Results              *DscMsgPollResult `protobuf:"bytes,6,opt,name=results,proto3" json:"results"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-" gorm:"-"`
	XXX_unrecognized     []byte            `json:"-" gorm:"-"`
	XXX_sizecache        int32             `json:"-" gorm:"-"`
}

func (m *DscMsgPoll) Reset()         { *m = DscMsgPoll{} }
func (m *DscMsgPoll) String() string { return proto.CompactTextString(m) }
func (*DscMsgPoll) ProtoMessage()    {}
func (*DscMsgPoll) Descriptor() ([]byte, []int) {
	return fileDescriptor_d37f1fb6677774d8, []int{11}
}

func (m *DscMsgPoll) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DscMsgPoll.Unmarshal(m, b)
}
func (m *DscMsgPoll) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DscMsgPoll.Marshal(b, m, deterministic)
}
func (m *DscMsgPoll) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DscMsgPoll.Merge(m, src)
}
func (m *DscMsgPoll) XXX_Size() int {
	return xxx_messageInfo_DscMsgPoll.Size(m)
}
func (m *DscMsgPoll) XXX_DiscardUnknown() {
	xxx_messageInfo_DscMsgPoll.DiscardUnknown(m)
}

var xxx_messageInfo_DscMsgPoll proto.InternalMessageInfo

func (m *DscMsgPoll) GetQuestionText() string {
	if m != nil {
		return m.QuestionText
	}
	return ""
}

func (m *DscMsgPoll) GetAnswers() []*DscMsgPollAnswer {
	if m != nil {
		return m.Answers
	}
	return nil
}

func (m *DscMsgPoll) GetAllowMultiselect() bool {
	if m != nil {
		return m.AllowMultiselect
	}
	return false
}

func (m *DscMsgPoll) GetDuration() int32 {
	if m != nil {
		return m.Duration
	}
	return 0
}

func (m *DscMsgPoll) GetExpiry() uint64 {
	if m != nil {
		return m.Expiry
	}
	return 0
}

func (m *DscMsgPoll) GetResults() *DscMsgPollResult {
	if m != nil {
		return m.Results
	}
	return nil
}

type DscMsgPollResult struct {
	// 是否已经过期
	IsFinalized bool `protobuf:"varint,1,opt,name=is_finalized,json=isFinalized,proto3" json:"is_finalized"`
	// 投票结果
	AnswerCount          []*DscMsgPollAnswerCount `protobuf:"bytes,2,rep,name=answer_count,json=answerCount,proto3" json:"answer_count"`
	XXX_NoUnkeyedLiteral struct{}                 `json:"-" gorm:"-"`
	XXX_unrecognized     []byte                   `json:"-" gorm:"-"`
	XXX_sizecache        int32                    `json:"-" gorm:"-"`
}

func (m *DscMsgPollResult) Reset()         { *m = DscMsgPollResult{} }
func (m *DscMsgPollResult) String() string { return proto.CompactTextString(m) }
func (*DscMsgPollResult) ProtoMessage()    {}
func (*DscMsgPollResult) Descriptor() ([]byte, []int) {
	return fileDescriptor_d37f1fb6677774d8, []int{12}
}

func (m *DscMsgPollResult) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DscMsgPollResult.Unmarshal(m, b)
}
func (m *DscMsgPollResult) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DscMsgPollResult.Marshal(b, m, deterministic)
}
func (m *DscMsgPollResult) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DscMsgPollResult.Merge(m, src)
}
func (m *DscMsgPollResult) XXX_Size() int {
	return xxx_messageInfo_DscMsgPollResult.Size(m)
}
func (m *DscMsgPollResult) XXX_DiscardUnknown() {
	xxx_messageInfo_DscMsgPollResult.DiscardUnknown(m)
}

var xxx_messageInfo_DscMsgPollResult proto.InternalMessageInfo

func (m *DscMsgPollResult) GetIsFinalized() bool {
	if m != nil {
		return m.IsFinalized
	}
	return false
}

func (m *DscMsgPollResult) GetAnswerCount() []*DscMsgPollAnswerCount {
	if m != nil {
		return m.AnswerCount
	}
	return nil
}

type DscMsgPollAnswerCount struct {
	// 答案 id
	Id uint32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	// 答案数量
	Count uint32 `protobuf:"varint,2,opt,name=count,proto3" json:"count"`
	// 是否我投票
	MeVoted              bool     `protobuf:"varint,3,opt,name=me_voted,json=meVoted,proto3" json:"me_voted"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *DscMsgPollAnswerCount) Reset()         { *m = DscMsgPollAnswerCount{} }
func (m *DscMsgPollAnswerCount) String() string { return proto.CompactTextString(m) }
func (*DscMsgPollAnswerCount) ProtoMessage()    {}
func (*DscMsgPollAnswerCount) Descriptor() ([]byte, []int) {
	return fileDescriptor_d37f1fb6677774d8, []int{13}
}

func (m *DscMsgPollAnswerCount) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DscMsgPollAnswerCount.Unmarshal(m, b)
}
func (m *DscMsgPollAnswerCount) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DscMsgPollAnswerCount.Marshal(b, m, deterministic)
}
func (m *DscMsgPollAnswerCount) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DscMsgPollAnswerCount.Merge(m, src)
}
func (m *DscMsgPollAnswerCount) XXX_Size() int {
	return xxx_messageInfo_DscMsgPollAnswerCount.Size(m)
}
func (m *DscMsgPollAnswerCount) XXX_DiscardUnknown() {
	xxx_messageInfo_DscMsgPollAnswerCount.DiscardUnknown(m)
}

var xxx_messageInfo_DscMsgPollAnswerCount proto.InternalMessageInfo

func (m *DscMsgPollAnswerCount) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *DscMsgPollAnswerCount) GetCount() uint32 {
	if m != nil {
		return m.Count
	}
	return 0
}

func (m *DscMsgPollAnswerCount) GetMeVoted() bool {
	if m != nil {
		return m.MeVoted
	}
	return false
}

type DscMsgPollAnswer struct {
	// 答案 id
	AnswerId uint32 `protobuf:"varint,1,opt,name=answer_id,json=answerId,proto3" json:"answer_id"`
	// 答案描述
	AnswerText           string   `protobuf:"bytes,2,opt,name=answer_text,json=answerText,proto3" json:"answer_text"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *DscMsgPollAnswer) Reset()         { *m = DscMsgPollAnswer{} }
func (m *DscMsgPollAnswer) String() string { return proto.CompactTextString(m) }
func (*DscMsgPollAnswer) ProtoMessage()    {}
func (*DscMsgPollAnswer) Descriptor() ([]byte, []int) {
	return fileDescriptor_d37f1fb6677774d8, []int{14}
}

func (m *DscMsgPollAnswer) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DscMsgPollAnswer.Unmarshal(m, b)
}
func (m *DscMsgPollAnswer) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DscMsgPollAnswer.Marshal(b, m, deterministic)
}
func (m *DscMsgPollAnswer) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DscMsgPollAnswer.Merge(m, src)
}
func (m *DscMsgPollAnswer) XXX_Size() int {
	return xxx_messageInfo_DscMsgPollAnswer.Size(m)
}
func (m *DscMsgPollAnswer) XXX_DiscardUnknown() {
	xxx_messageInfo_DscMsgPollAnswer.DiscardUnknown(m)
}

var xxx_messageInfo_DscMsgPollAnswer proto.InternalMessageInfo

func (m *DscMsgPollAnswer) GetAnswerId() uint32 {
	if m != nil {
		return m.AnswerId
	}
	return 0
}

func (m *DscMsgPollAnswer) GetAnswerText() string {
	if m != nil {
		return m.AnswerText
	}
	return ""
}

type DscAuthor struct {
	// dsc_user_id-用户id
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id"`
	// 用户名称
	Username string `protobuf:"bytes,2,opt,name=username,proto3" json:"username"`
	// 用户头像
	Avatar string `protobuf:"bytes,3,opt,name=avatar,proto3" json:"avatar"`
	// global name
	GlobalName string `protobuf:"bytes,4,opt,name=global_name,json=globalName,proto3" json:"global_name"`
	// 是否是机器人
	Bot                  bool     `protobuf:"varint,5,opt,name=bot,proto3" json:"bot"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *DscAuthor) Reset()         { *m = DscAuthor{} }
func (m *DscAuthor) String() string { return proto.CompactTextString(m) }
func (*DscAuthor) ProtoMessage()    {}
func (*DscAuthor) Descriptor() ([]byte, []int) {
	return fileDescriptor_d37f1fb6677774d8, []int{15}
}

func (m *DscAuthor) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DscAuthor.Unmarshal(m, b)
}
func (m *DscAuthor) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DscAuthor.Marshal(b, m, deterministic)
}
func (m *DscAuthor) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DscAuthor.Merge(m, src)
}
func (m *DscAuthor) XXX_Size() int {
	return xxx_messageInfo_DscAuthor.Size(m)
}
func (m *DscAuthor) XXX_DiscardUnknown() {
	xxx_messageInfo_DscAuthor.DiscardUnknown(m)
}

var xxx_messageInfo_DscAuthor proto.InternalMessageInfo

func (m *DscAuthor) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *DscAuthor) GetUsername() string {
	if m != nil {
		return m.Username
	}
	return ""
}

func (m *DscAuthor) GetAvatar() string {
	if m != nil {
		return m.Avatar
	}
	return ""
}

func (m *DscAuthor) GetGlobalName() string {
	if m != nil {
		return m.GlobalName
	}
	return ""
}

func (m *DscAuthor) GetBot() bool {
	if m != nil {
		return m.Bot
	}
	return false
}

type DscChannelMsgCreateReq struct {
	// 玩家 DC_CHANNEL_ID-必传 @gotags: validate:"required"
	ChannelId string `protobuf:"bytes,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id" validate:"required"`
	// 机器人信息  @gotags: validate:"required"
	BotId string `protobuf:"bytes,2,opt,name=bot_id,json=botId,proto3" json:"bot_id" validate:"required"`
	// 回复内容 @gotags: validate:"required"
	Content string `protobuf:"bytes,3,opt,name=content,proto3" json:"content" validate:"required"`
	// 是否强制忽略 回复状态: 1不调整回复状态
	IgnoreState          int32    `protobuf:"varint,4,opt,name=ignore_state,json=ignoreState,proto3" json:"ignore_state"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *DscChannelMsgCreateReq) Reset()         { *m = DscChannelMsgCreateReq{} }
func (m *DscChannelMsgCreateReq) String() string { return proto.CompactTextString(m) }
func (*DscChannelMsgCreateReq) ProtoMessage()    {}
func (*DscChannelMsgCreateReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_d37f1fb6677774d8, []int{16}
}

func (m *DscChannelMsgCreateReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DscChannelMsgCreateReq.Unmarshal(m, b)
}
func (m *DscChannelMsgCreateReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DscChannelMsgCreateReq.Marshal(b, m, deterministic)
}
func (m *DscChannelMsgCreateReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DscChannelMsgCreateReq.Merge(m, src)
}
func (m *DscChannelMsgCreateReq) XXX_Size() int {
	return xxx_messageInfo_DscChannelMsgCreateReq.Size(m)
}
func (m *DscChannelMsgCreateReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DscChannelMsgCreateReq.DiscardUnknown(m)
}

var xxx_messageInfo_DscChannelMsgCreateReq proto.InternalMessageInfo

func (m *DscChannelMsgCreateReq) GetChannelId() string {
	if m != nil {
		return m.ChannelId
	}
	return ""
}

func (m *DscChannelMsgCreateReq) GetBotId() string {
	if m != nil {
		return m.BotId
	}
	return ""
}

func (m *DscChannelMsgCreateReq) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *DscChannelMsgCreateReq) GetIgnoreState() int32 {
	if m != nil {
		return m.IgnoreState
	}
	return 0
}

type DscChannelMsgCreateResp struct {
	// 回复消息id
	MsgId string `protobuf:"bytes,1,opt,name=msg_id,json=msgId,proto3" json:"msg_id"`
	// 发送渠道
	ChannelId            string   `protobuf:"bytes,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *DscChannelMsgCreateResp) Reset()         { *m = DscChannelMsgCreateResp{} }
func (m *DscChannelMsgCreateResp) String() string { return proto.CompactTextString(m) }
func (*DscChannelMsgCreateResp) ProtoMessage()    {}
func (*DscChannelMsgCreateResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_d37f1fb6677774d8, []int{17}
}

func (m *DscChannelMsgCreateResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DscChannelMsgCreateResp.Unmarshal(m, b)
}
func (m *DscChannelMsgCreateResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DscChannelMsgCreateResp.Marshal(b, m, deterministic)
}
func (m *DscChannelMsgCreateResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DscChannelMsgCreateResp.Merge(m, src)
}
func (m *DscChannelMsgCreateResp) XXX_Size() int {
	return xxx_messageInfo_DscChannelMsgCreateResp.Size(m)
}
func (m *DscChannelMsgCreateResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DscChannelMsgCreateResp.DiscardUnknown(m)
}

var xxx_messageInfo_DscChannelMsgCreateResp proto.InternalMessageInfo

func (m *DscChannelMsgCreateResp) GetMsgId() string {
	if m != nil {
		return m.MsgId
	}
	return ""
}

func (m *DscChannelMsgCreateResp) GetChannelId() string {
	if m != nil {
		return m.ChannelId
	}
	return ""
}

type DscChannelMsgEditReq struct {
	// 玩家 DC_CHANNEL_ID-必传 @gotags: validate:"required"
	ChannelId string `protobuf:"bytes,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id" validate:"required"`
	// 机器人信息  @gotags: validate:"required"
	BotId string `protobuf:"bytes,2,opt,name=bot_id,json=botId,proto3" json:"bot_id" validate:"required"`
	// 消息id @gotags: validate:"required"
	MsgId string `protobuf:"bytes,3,opt,name=msg_id,json=msgId,proto3" json:"msg_id" validate:"required"`
	// 回复内容 @gotags: validate:"required"
	Content              string   `protobuf:"bytes,4,opt,name=content,proto3" json:"content" validate:"required"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *DscChannelMsgEditReq) Reset()         { *m = DscChannelMsgEditReq{} }
func (m *DscChannelMsgEditReq) String() string { return proto.CompactTextString(m) }
func (*DscChannelMsgEditReq) ProtoMessage()    {}
func (*DscChannelMsgEditReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_d37f1fb6677774d8, []int{18}
}

func (m *DscChannelMsgEditReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DscChannelMsgEditReq.Unmarshal(m, b)
}
func (m *DscChannelMsgEditReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DscChannelMsgEditReq.Marshal(b, m, deterministic)
}
func (m *DscChannelMsgEditReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DscChannelMsgEditReq.Merge(m, src)
}
func (m *DscChannelMsgEditReq) XXX_Size() int {
	return xxx_messageInfo_DscChannelMsgEditReq.Size(m)
}
func (m *DscChannelMsgEditReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DscChannelMsgEditReq.DiscardUnknown(m)
}

var xxx_messageInfo_DscChannelMsgEditReq proto.InternalMessageInfo

func (m *DscChannelMsgEditReq) GetChannelId() string {
	if m != nil {
		return m.ChannelId
	}
	return ""
}

func (m *DscChannelMsgEditReq) GetBotId() string {
	if m != nil {
		return m.BotId
	}
	return ""
}

func (m *DscChannelMsgEditReq) GetMsgId() string {
	if m != nil {
		return m.MsgId
	}
	return ""
}

func (m *DscChannelMsgEditReq) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

type DscChannelFileCreateReq struct {
	// 玩家 DC_CHANNEL_ID-必传 @gotags: validate:"required"
	ChannelId string `protobuf:"bytes,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id" validate:"required"`
	// 机器人信息 @gotags: validate:"required"
	BotId                string   `protobuf:"bytes,2,opt,name=bot_id,json=botId,proto3" json:"bot_id" validate:"required"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *DscChannelFileCreateReq) Reset()         { *m = DscChannelFileCreateReq{} }
func (m *DscChannelFileCreateReq) String() string { return proto.CompactTextString(m) }
func (*DscChannelFileCreateReq) ProtoMessage()    {}
func (*DscChannelFileCreateReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_d37f1fb6677774d8, []int{19}
}

func (m *DscChannelFileCreateReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DscChannelFileCreateReq.Unmarshal(m, b)
}
func (m *DscChannelFileCreateReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DscChannelFileCreateReq.Marshal(b, m, deterministic)
}
func (m *DscChannelFileCreateReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DscChannelFileCreateReq.Merge(m, src)
}
func (m *DscChannelFileCreateReq) XXX_Size() int {
	return xxx_messageInfo_DscChannelFileCreateReq.Size(m)
}
func (m *DscChannelFileCreateReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DscChannelFileCreateReq.DiscardUnknown(m)
}

var xxx_messageInfo_DscChannelFileCreateReq proto.InternalMessageInfo

func (m *DscChannelFileCreateReq) GetChannelId() string {
	if m != nil {
		return m.ChannelId
	}
	return ""
}

func (m *DscChannelFileCreateReq) GetBotId() string {
	if m != nil {
		return m.BotId
	}
	return ""
}

type DscChannelDialogFreshReq struct {
	// 玩家 DC ID - 必传
	ChannelId string `protobuf:"bytes,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id"`
	// 向下翻页 - 开始消息id - 对应 msg_id
	After                string   `protobuf:"bytes,2,opt,name=after,proto3" json:"after"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *DscChannelDialogFreshReq) Reset()         { *m = DscChannelDialogFreshReq{} }
func (m *DscChannelDialogFreshReq) String() string { return proto.CompactTextString(m) }
func (*DscChannelDialogFreshReq) ProtoMessage()    {}
func (*DscChannelDialogFreshReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_d37f1fb6677774d8, []int{20}
}

func (m *DscChannelDialogFreshReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DscChannelDialogFreshReq.Unmarshal(m, b)
}
func (m *DscChannelDialogFreshReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DscChannelDialogFreshReq.Marshal(b, m, deterministic)
}
func (m *DscChannelDialogFreshReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DscChannelDialogFreshReq.Merge(m, src)
}
func (m *DscChannelDialogFreshReq) XXX_Size() int {
	return xxx_messageInfo_DscChannelDialogFreshReq.Size(m)
}
func (m *DscChannelDialogFreshReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DscChannelDialogFreshReq.DiscardUnknown(m)
}

var xxx_messageInfo_DscChannelDialogFreshReq proto.InternalMessageInfo

func (m *DscChannelDialogFreshReq) GetChannelId() string {
	if m != nil {
		return m.ChannelId
	}
	return ""
}

func (m *DscChannelDialogFreshReq) GetAfter() string {
	if m != nil {
		return m.After
	}
	return ""
}

type DscDialogFreshEvent struct {
	// 事件类型：详细看 DscEvTpDf
	EventType DscEvTpDf `protobuf:"varint,1,opt,name=event_type,json=eventType,proto3,enum=pb.DscEvTpDf" json:"event_type"`
	// 新增/修改消息-详情 @gotags: json:"msg_detail,omitempty"
	MsgDetail *DscDialogDetail `protobuf:"bytes,2,opt,name=msg_detail,json=msgDetail,proto3" json:"msg_detail,omitempty"`
	// 删除消息 - 具体详情 @gotags: json:"del_msg_ids,omitempty"
	DelMsgIds []string `protobuf:"bytes,3,rep,name=del_msg_ids,json=delMsgIds,proto3" json:"del_msg_ids,omitempty"`
	// 新增表情 - 详细信息 @gotags: json:"add_reaction,omitempty"
	AddReaction []*DscMsgReaction `protobuf:"bytes,4,rep,name=add_reaction,json=addReaction,proto3" json:"add_reaction,omitempty"`
	// 删除表情 - 详情 @gotags: json:"del_reaction,omitempty"
	DelReaction          []*DscMsgReaction `protobuf:"bytes,5,rep,name=del_reaction,json=delReaction,proto3" json:"del_reaction,omitempty"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-" gorm:"-"`
	XXX_unrecognized     []byte            `json:"-" gorm:"-"`
	XXX_sizecache        int32             `json:"-" gorm:"-"`
}

func (m *DscDialogFreshEvent) Reset()         { *m = DscDialogFreshEvent{} }
func (m *DscDialogFreshEvent) String() string { return proto.CompactTextString(m) }
func (*DscDialogFreshEvent) ProtoMessage()    {}
func (*DscDialogFreshEvent) Descriptor() ([]byte, []int) {
	return fileDescriptor_d37f1fb6677774d8, []int{21}
}

func (m *DscDialogFreshEvent) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DscDialogFreshEvent.Unmarshal(m, b)
}
func (m *DscDialogFreshEvent) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DscDialogFreshEvent.Marshal(b, m, deterministic)
}
func (m *DscDialogFreshEvent) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DscDialogFreshEvent.Merge(m, src)
}
func (m *DscDialogFreshEvent) XXX_Size() int {
	return xxx_messageInfo_DscDialogFreshEvent.Size(m)
}
func (m *DscDialogFreshEvent) XXX_DiscardUnknown() {
	xxx_messageInfo_DscDialogFreshEvent.DiscardUnknown(m)
}

var xxx_messageInfo_DscDialogFreshEvent proto.InternalMessageInfo

func (m *DscDialogFreshEvent) GetEventType() DscEvTpDf {
	if m != nil {
		return m.EventType
	}
	return DscEvTpDf_DscEvTpDfUnknown
}

func (m *DscDialogFreshEvent) GetMsgDetail() *DscDialogDetail {
	if m != nil {
		return m.MsgDetail
	}
	return nil
}

func (m *DscDialogFreshEvent) GetDelMsgIds() []string {
	if m != nil {
		return m.DelMsgIds
	}
	return nil
}

func (m *DscDialogFreshEvent) GetAddReaction() []*DscMsgReaction {
	if m != nil {
		return m.AddReaction
	}
	return nil
}

func (m *DscDialogFreshEvent) GetDelReaction() []*DscMsgReaction {
	if m != nil {
		return m.DelReaction
	}
	return nil
}

type DscChannelDialogFreshResp struct {
	// 新增会话列表
	DialogList []*DscDialogDetail `protobuf:"bytes,1,rep,name=dialog_list,json=dialogList,proto3" json:"dialog_list"`
	// 新增事件数据
	FreshEvent           []*DscDialogFreshEvent `protobuf:"bytes,2,rep,name=fresh_event,json=freshEvent,proto3" json:"fresh_event"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-" gorm:"-"`
	XXX_unrecognized     []byte                 `json:"-" gorm:"-"`
	XXX_sizecache        int32                  `json:"-" gorm:"-"`
}

func (m *DscChannelDialogFreshResp) Reset()         { *m = DscChannelDialogFreshResp{} }
func (m *DscChannelDialogFreshResp) String() string { return proto.CompactTextString(m) }
func (*DscChannelDialogFreshResp) ProtoMessage()    {}
func (*DscChannelDialogFreshResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_d37f1fb6677774d8, []int{22}
}

func (m *DscChannelDialogFreshResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DscChannelDialogFreshResp.Unmarshal(m, b)
}
func (m *DscChannelDialogFreshResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DscChannelDialogFreshResp.Marshal(b, m, deterministic)
}
func (m *DscChannelDialogFreshResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DscChannelDialogFreshResp.Merge(m, src)
}
func (m *DscChannelDialogFreshResp) XXX_Size() int {
	return xxx_messageInfo_DscChannelDialogFreshResp.Size(m)
}
func (m *DscChannelDialogFreshResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DscChannelDialogFreshResp.DiscardUnknown(m)
}

var xxx_messageInfo_DscChannelDialogFreshResp proto.InternalMessageInfo

func (m *DscChannelDialogFreshResp) GetDialogList() []*DscDialogDetail {
	if m != nil {
		return m.DialogList
	}
	return nil
}

func (m *DscChannelDialogFreshResp) GetFreshEvent() []*DscDialogFreshEvent {
	if m != nil {
		return m.FreshEvent
	}
	return nil
}

type DscPoolInfo struct {
	// 游戏
	Project string `protobuf:"bytes,1,opt,name=project,proto3" json:"project"`
	// 玩家 DC ID
	DscUserId string `protobuf:"bytes,2,opt,name=dsc_user_id,json=dscUserId,proto3" json:"dsc_user_id"`
	// 玩家名称 - 前端展示使用此字段
	UserName string `protobuf:"bytes,3,opt,name=user_name,json=userName,proto3" json:"user_name"`
	// 玩家 global name
	GlobalName string `protobuf:"bytes,4,opt,name=global_name,json=globalName,proto3" json:"global_name"`
	// dm channel
	DmChannel string `protobuf:"bytes,5,opt,name=dm_channel,json=dmChannel,proto3" json:"dm_channel"`
	// guild id
	GuildId string `protobuf:"bytes,6,opt,name=guild_id,json=guildId,proto3" json:"guild_id"`
	// 当前维护人
	Processor string `protobuf:"bytes,7,opt,name=processor,proto3" json:"processor"`
	BotId     string `protobuf:"bytes,8,opt,name=bot_id,json=botId,proto3" json:"bot_id"`
	Uid       uint64 `protobuf:"varint,9,opt,name=uid,proto3" json:"uid"`
	AccountId string `protobuf:"bytes,10,opt,name=account_id,json=accountId,proto3" json:"account_id"`
	// 服务器
	Sid string `protobuf:"bytes,11,opt,name=sid,proto3" json:"sid"`
	// 最近登录时间
	LastLogin string `protobuf:"bytes,12,opt,name=last_login,json=lastLogin,proto3" json:"last_login"`
	// 累计付费金额
	PayAll int64 `protobuf:"varint,13,opt,name=pay_all,json=payAll,proto3" json:"pay_all"`
	// 最近30天付费金额
	PayLastThirtyDays int64  `protobuf:"varint,14,opt,name=pay_last_thirty_days,json=payLastThirtyDays,proto3" json:"pay_last_thirty_days"`
	Status            uint32 `protobuf:"varint,15,opt,name=status,proto3" json:"status"`
	VipLevel          uint32 `protobuf:"varint,16,opt,name=vip_level,json=vipLevel,proto3" json:"vip_level"`
	Note              string `protobuf:"bytes,17,opt,name=note,proto3" json:"note"`
	PlayerNick        string `protobuf:"bytes,18,opt,name=player_nick,json=playerNick,proto3" json:"player_nick"`
	Birthday          string `protobuf:"bytes,19,opt,name=birthday,proto3" json:"birthday"`
	Lang              string `protobuf:"bytes,20,opt,name=lang,proto3" json:"lang"`
	// 客服最后回复时间
	LastReplyTime        string   `protobuf:"bytes,21,opt,name=last_reply_time,json=lastReplyTime,proto3" json:"last_reply_time"`
	WaitingTime          string   `protobuf:"bytes,22,opt,name=waiting_time,json=waitingTime,proto3" json:"waiting_time"`
	TicketCreateCount    int64    `protobuf:"varint,23,opt,name=ticket_create_count,json=ticketCreateCount,proto3" json:"ticket_create_count"`
	LastTicketCreateTime string   `protobuf:"bytes,24,opt,name=last_ticket_create_time,json=lastTicketCreateTime,proto3" json:"last_ticket_create_time"`
	TicketId             uint64   `protobuf:"varint,25,opt,name=ticket_id,json=ticketId,proto3" json:"ticket_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *DscPoolInfo) Reset()         { *m = DscPoolInfo{} }
func (m *DscPoolInfo) String() string { return proto.CompactTextString(m) }
func (*DscPoolInfo) ProtoMessage()    {}
func (*DscPoolInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_d37f1fb6677774d8, []int{23}
}

func (m *DscPoolInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DscPoolInfo.Unmarshal(m, b)
}
func (m *DscPoolInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DscPoolInfo.Marshal(b, m, deterministic)
}
func (m *DscPoolInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DscPoolInfo.Merge(m, src)
}
func (m *DscPoolInfo) XXX_Size() int {
	return xxx_messageInfo_DscPoolInfo.Size(m)
}
func (m *DscPoolInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_DscPoolInfo.DiscardUnknown(m)
}

var xxx_messageInfo_DscPoolInfo proto.InternalMessageInfo

func (m *DscPoolInfo) GetProject() string {
	if m != nil {
		return m.Project
	}
	return ""
}

func (m *DscPoolInfo) GetDscUserId() string {
	if m != nil {
		return m.DscUserId
	}
	return ""
}

func (m *DscPoolInfo) GetUserName() string {
	if m != nil {
		return m.UserName
	}
	return ""
}

func (m *DscPoolInfo) GetGlobalName() string {
	if m != nil {
		return m.GlobalName
	}
	return ""
}

func (m *DscPoolInfo) GetDmChannel() string {
	if m != nil {
		return m.DmChannel
	}
	return ""
}

func (m *DscPoolInfo) GetGuildId() string {
	if m != nil {
		return m.GuildId
	}
	return ""
}

func (m *DscPoolInfo) GetProcessor() string {
	if m != nil {
		return m.Processor
	}
	return ""
}

func (m *DscPoolInfo) GetBotId() string {
	if m != nil {
		return m.BotId
	}
	return ""
}

func (m *DscPoolInfo) GetUid() uint64 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *DscPoolInfo) GetAccountId() string {
	if m != nil {
		return m.AccountId
	}
	return ""
}

func (m *DscPoolInfo) GetSid() string {
	if m != nil {
		return m.Sid
	}
	return ""
}

func (m *DscPoolInfo) GetLastLogin() string {
	if m != nil {
		return m.LastLogin
	}
	return ""
}

func (m *DscPoolInfo) GetPayAll() int64 {
	if m != nil {
		return m.PayAll
	}
	return 0
}

func (m *DscPoolInfo) GetPayLastThirtyDays() int64 {
	if m != nil {
		return m.PayLastThirtyDays
	}
	return 0
}

func (m *DscPoolInfo) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *DscPoolInfo) GetVipLevel() uint32 {
	if m != nil {
		return m.VipLevel
	}
	return 0
}

func (m *DscPoolInfo) GetNote() string {
	if m != nil {
		return m.Note
	}
	return ""
}

func (m *DscPoolInfo) GetPlayerNick() string {
	if m != nil {
		return m.PlayerNick
	}
	return ""
}

func (m *DscPoolInfo) GetBirthday() string {
	if m != nil {
		return m.Birthday
	}
	return ""
}

func (m *DscPoolInfo) GetLang() string {
	if m != nil {
		return m.Lang
	}
	return ""
}

func (m *DscPoolInfo) GetLastReplyTime() string {
	if m != nil {
		return m.LastReplyTime
	}
	return ""
}

func (m *DscPoolInfo) GetWaitingTime() string {
	if m != nil {
		return m.WaitingTime
	}
	return ""
}

func (m *DscPoolInfo) GetTicketCreateCount() int64 {
	if m != nil {
		return m.TicketCreateCount
	}
	return 0
}

func (m *DscPoolInfo) GetLastTicketCreateTime() string {
	if m != nil {
		return m.LastTicketCreateTime
	}
	return ""
}

func (m *DscPoolInfo) GetTicketId() uint64 {
	if m != nil {
		return m.TicketId
	}
	return 0
}

type DiscordStatsReq struct {
	Project              []string `protobuf:"bytes,1,rep,name=project,proto3" json:"project"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *DiscordStatsReq) Reset()         { *m = DiscordStatsReq{} }
func (m *DiscordStatsReq) String() string { return proto.CompactTextString(m) }
func (*DiscordStatsReq) ProtoMessage()    {}
func (*DiscordStatsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_d37f1fb6677774d8, []int{24}
}

func (m *DiscordStatsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DiscordStatsReq.Unmarshal(m, b)
}
func (m *DiscordStatsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DiscordStatsReq.Marshal(b, m, deterministic)
}
func (m *DiscordStatsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DiscordStatsReq.Merge(m, src)
}
func (m *DiscordStatsReq) XXX_Size() int {
	return xxx_messageInfo_DiscordStatsReq.Size(m)
}
func (m *DiscordStatsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DiscordStatsReq.DiscardUnknown(m)
}

var xxx_messageInfo_DiscordStatsReq proto.InternalMessageInfo

func (m *DiscordStatsReq) GetProject() []string {
	if m != nil {
		return m.Project
	}
	return nil
}

type DiscordStatsResp struct {
	DiscordUserCount      int64    `protobuf:"varint,1,opt,name=discord_user_count,json=discordUserCount,proto3" json:"discord_user_count"`
	WaitReplyAccounts     int64    `protobuf:"varint,2,opt,name=wait_reply_accounts,json=waitReplyAccounts,proto3" json:"wait_reply_accounts"`
	MineWaitReplyAccounts int64    `protobuf:"varint,3,opt,name=mine_wait_reply_accounts,json=mineWaitReplyAccounts,proto3" json:"mine_wait_reply_accounts"`
	XXX_NoUnkeyedLiteral  struct{} `json:"-" gorm:"-"`
	XXX_unrecognized      []byte   `json:"-" gorm:"-"`
	XXX_sizecache         int32    `json:"-" gorm:"-"`
}

func (m *DiscordStatsResp) Reset()         { *m = DiscordStatsResp{} }
func (m *DiscordStatsResp) String() string { return proto.CompactTextString(m) }
func (*DiscordStatsResp) ProtoMessage()    {}
func (*DiscordStatsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_d37f1fb6677774d8, []int{25}
}

func (m *DiscordStatsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DiscordStatsResp.Unmarshal(m, b)
}
func (m *DiscordStatsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DiscordStatsResp.Marshal(b, m, deterministic)
}
func (m *DiscordStatsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DiscordStatsResp.Merge(m, src)
}
func (m *DiscordStatsResp) XXX_Size() int {
	return xxx_messageInfo_DiscordStatsResp.Size(m)
}
func (m *DiscordStatsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DiscordStatsResp.DiscardUnknown(m)
}

var xxx_messageInfo_DiscordStatsResp proto.InternalMessageInfo

func (m *DiscordStatsResp) GetDiscordUserCount() int64 {
	if m != nil {
		return m.DiscordUserCount
	}
	return 0
}

func (m *DiscordStatsResp) GetWaitReplyAccounts() int64 {
	if m != nil {
		return m.WaitReplyAccounts
	}
	return 0
}

func (m *DiscordStatsResp) GetMineWaitReplyAccounts() int64 {
	if m != nil {
		return m.MineWaitReplyAccounts
	}
	return 0
}

type DscProxyResourceReq struct {
	// @gotags: validate:"required"
	Url string `protobuf:"bytes,1,opt,name=url,proto3" json:"url" validate:"required"`
	// 仅代理
	OnlyProxy            bool     `protobuf:"varint,2,opt,name=only_proxy,json=onlyProxy,proto3" json:"only_proxy"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *DscProxyResourceReq) Reset()         { *m = DscProxyResourceReq{} }
func (m *DscProxyResourceReq) String() string { return proto.CompactTextString(m) }
func (*DscProxyResourceReq) ProtoMessage()    {}
func (*DscProxyResourceReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_d37f1fb6677774d8, []int{26}
}

func (m *DscProxyResourceReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DscProxyResourceReq.Unmarshal(m, b)
}
func (m *DscProxyResourceReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DscProxyResourceReq.Marshal(b, m, deterministic)
}
func (m *DscProxyResourceReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DscProxyResourceReq.Merge(m, src)
}
func (m *DscProxyResourceReq) XXX_Size() int {
	return xxx_messageInfo_DscProxyResourceReq.Size(m)
}
func (m *DscProxyResourceReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DscProxyResourceReq.DiscardUnknown(m)
}

var xxx_messageInfo_DscProxyResourceReq proto.InternalMessageInfo

func (m *DscProxyResourceReq) GetUrl() string {
	if m != nil {
		return m.Url
	}
	return ""
}

func (m *DscProxyResourceReq) GetOnlyProxy() bool {
	if m != nil {
		return m.OnlyProxy
	}
	return false
}

type DiscordPlayerInteractStatsReq struct {
	Project              []string `protobuf:"bytes,1,rep,name=project,proto3" json:"project"`
	Date                 []string `protobuf:"bytes,2,rep,name=date,proto3" json:"date"`
	Operator             []string `protobuf:"bytes,3,rep,name=operator,proto3" json:"operator"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *DiscordPlayerInteractStatsReq) Reset()         { *m = DiscordPlayerInteractStatsReq{} }
func (m *DiscordPlayerInteractStatsReq) String() string { return proto.CompactTextString(m) }
func (*DiscordPlayerInteractStatsReq) ProtoMessage()    {}
func (*DiscordPlayerInteractStatsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_d37f1fb6677774d8, []int{27}
}

func (m *DiscordPlayerInteractStatsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DiscordPlayerInteractStatsReq.Unmarshal(m, b)
}
func (m *DiscordPlayerInteractStatsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DiscordPlayerInteractStatsReq.Marshal(b, m, deterministic)
}
func (m *DiscordPlayerInteractStatsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DiscordPlayerInteractStatsReq.Merge(m, src)
}
func (m *DiscordPlayerInteractStatsReq) XXX_Size() int {
	return xxx_messageInfo_DiscordPlayerInteractStatsReq.Size(m)
}
func (m *DiscordPlayerInteractStatsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DiscordPlayerInteractStatsReq.DiscardUnknown(m)
}

var xxx_messageInfo_DiscordPlayerInteractStatsReq proto.InternalMessageInfo

func (m *DiscordPlayerInteractStatsReq) GetProject() []string {
	if m != nil {
		return m.Project
	}
	return nil
}

func (m *DiscordPlayerInteractStatsReq) GetDate() []string {
	if m != nil {
		return m.Date
	}
	return nil
}

func (m *DiscordPlayerInteractStatsReq) GetOperator() []string {
	if m != nil {
		return m.Operator
	}
	return nil
}

type DiscordMessageCountReq struct {
	Project              []string `protobuf:"bytes,1,rep,name=project,proto3" json:"project"`
	Date                 []string `protobuf:"bytes,2,rep,name=date,proto3" json:"date"`
	Operator             []string `protobuf:"bytes,3,rep,name=operator,proto3" json:"operator"`
	Uid                  []int64  `protobuf:"varint,4,rep,packed,name=uid,proto3" json:"uid"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *DiscordMessageCountReq) Reset()         { *m = DiscordMessageCountReq{} }
func (m *DiscordMessageCountReq) String() string { return proto.CompactTextString(m) }
func (*DiscordMessageCountReq) ProtoMessage()    {}
func (*DiscordMessageCountReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_d37f1fb6677774d8, []int{28}
}

func (m *DiscordMessageCountReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DiscordMessageCountReq.Unmarshal(m, b)
}
func (m *DiscordMessageCountReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DiscordMessageCountReq.Marshal(b, m, deterministic)
}
func (m *DiscordMessageCountReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DiscordMessageCountReq.Merge(m, src)
}
func (m *DiscordMessageCountReq) XXX_Size() int {
	return xxx_messageInfo_DiscordMessageCountReq.Size(m)
}
func (m *DiscordMessageCountReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DiscordMessageCountReq.DiscardUnknown(m)
}

var xxx_messageInfo_DiscordMessageCountReq proto.InternalMessageInfo

func (m *DiscordMessageCountReq) GetProject() []string {
	if m != nil {
		return m.Project
	}
	return nil
}

func (m *DiscordMessageCountReq) GetDate() []string {
	if m != nil {
		return m.Date
	}
	return nil
}

func (m *DiscordMessageCountReq) GetOperator() []string {
	if m != nil {
		return m.Operator
	}
	return nil
}

func (m *DiscordMessageCountReq) GetUid() []int64 {
	if m != nil {
		return m.Uid
	}
	return nil
}

type DiscordMessageCountResp struct {
	Data                 []*DiscordMessageCountResp_MessageCountDetail `protobuf:"bytes,1,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}                                      `json:"-" gorm:"-"`
	XXX_unrecognized     []byte                                        `json:"-" gorm:"-"`
	XXX_sizecache        int32                                         `json:"-" gorm:"-"`
}

func (m *DiscordMessageCountResp) Reset()         { *m = DiscordMessageCountResp{} }
func (m *DiscordMessageCountResp) String() string { return proto.CompactTextString(m) }
func (*DiscordMessageCountResp) ProtoMessage()    {}
func (*DiscordMessageCountResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_d37f1fb6677774d8, []int{29}
}

func (m *DiscordMessageCountResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DiscordMessageCountResp.Unmarshal(m, b)
}
func (m *DiscordMessageCountResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DiscordMessageCountResp.Marshal(b, m, deterministic)
}
func (m *DiscordMessageCountResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DiscordMessageCountResp.Merge(m, src)
}
func (m *DiscordMessageCountResp) XXX_Size() int {
	return xxx_messageInfo_DiscordMessageCountResp.Size(m)
}
func (m *DiscordMessageCountResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DiscordMessageCountResp.DiscardUnknown(m)
}

var xxx_messageInfo_DiscordMessageCountResp proto.InternalMessageInfo

func (m *DiscordMessageCountResp) GetData() []*DiscordMessageCountResp_MessageCountDetail {
	if m != nil {
		return m.Data
	}
	return nil
}

type DiscordMessageCountResp_MessageCountDetail struct {
	RowName              string   `protobuf:"bytes,1,opt,name=row_name,json=rowName,proto3" json:"row_name"`
	PlayerMessageCount   int64    `protobuf:"varint,2,opt,name=player_message_count,json=playerMessageCount,proto3" json:"player_message_count"`
	ServiceMessageCount  int64    `protobuf:"varint,3,opt,name=service_message_count,json=serviceMessageCount,proto3" json:"service_message_count"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *DiscordMessageCountResp_MessageCountDetail) Reset() {
	*m = DiscordMessageCountResp_MessageCountDetail{}
}
func (m *DiscordMessageCountResp_MessageCountDetail) String() string {
	return proto.CompactTextString(m)
}
func (*DiscordMessageCountResp_MessageCountDetail) ProtoMessage() {}
func (*DiscordMessageCountResp_MessageCountDetail) Descriptor() ([]byte, []int) {
	return fileDescriptor_d37f1fb6677774d8, []int{29, 0}
}

func (m *DiscordMessageCountResp_MessageCountDetail) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DiscordMessageCountResp_MessageCountDetail.Unmarshal(m, b)
}
func (m *DiscordMessageCountResp_MessageCountDetail) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DiscordMessageCountResp_MessageCountDetail.Marshal(b, m, deterministic)
}
func (m *DiscordMessageCountResp_MessageCountDetail) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DiscordMessageCountResp_MessageCountDetail.Merge(m, src)
}
func (m *DiscordMessageCountResp_MessageCountDetail) XXX_Size() int {
	return xxx_messageInfo_DiscordMessageCountResp_MessageCountDetail.Size(m)
}
func (m *DiscordMessageCountResp_MessageCountDetail) XXX_DiscardUnknown() {
	xxx_messageInfo_DiscordMessageCountResp_MessageCountDetail.DiscardUnknown(m)
}

var xxx_messageInfo_DiscordMessageCountResp_MessageCountDetail proto.InternalMessageInfo

func (m *DiscordMessageCountResp_MessageCountDetail) GetRowName() string {
	if m != nil {
		return m.RowName
	}
	return ""
}

func (m *DiscordMessageCountResp_MessageCountDetail) GetPlayerMessageCount() int64 {
	if m != nil {
		return m.PlayerMessageCount
	}
	return 0
}

func (m *DiscordMessageCountResp_MessageCountDetail) GetServiceMessageCount() int64 {
	if m != nil {
		return m.ServiceMessageCount
	}
	return 0
}

type DiscordPlayerUidReq struct {
	// @gotags: validate:"required"
	Project []string `protobuf:"bytes,1,rep,name=project,proto3" json:"project" validate:"required"`
	// 搜索值
	Uid                  int64    `protobuf:"varint,2,opt,name=uid,proto3" json:"uid"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *DiscordPlayerUidReq) Reset()         { *m = DiscordPlayerUidReq{} }
func (m *DiscordPlayerUidReq) String() string { return proto.CompactTextString(m) }
func (*DiscordPlayerUidReq) ProtoMessage()    {}
func (*DiscordPlayerUidReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_d37f1fb6677774d8, []int{30}
}

func (m *DiscordPlayerUidReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DiscordPlayerUidReq.Unmarshal(m, b)
}
func (m *DiscordPlayerUidReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DiscordPlayerUidReq.Marshal(b, m, deterministic)
}
func (m *DiscordPlayerUidReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DiscordPlayerUidReq.Merge(m, src)
}
func (m *DiscordPlayerUidReq) XXX_Size() int {
	return xxx_messageInfo_DiscordPlayerUidReq.Size(m)
}
func (m *DiscordPlayerUidReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DiscordPlayerUidReq.DiscardUnknown(m)
}

var xxx_messageInfo_DiscordPlayerUidReq proto.InternalMessageInfo

func (m *DiscordPlayerUidReq) GetProject() []string {
	if m != nil {
		return m.Project
	}
	return nil
}

func (m *DiscordPlayerUidReq) GetUid() int64 {
	if m != nil {
		return m.Uid
	}
	return 0
}

type DscMsgBatchSendReq struct {
	// 玩家uid列表 @gotags: validate:"required"
	UidList []int64 `protobuf:"varint,1,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list" validate:"required"`
	// 发送消息的游戏标识game_project @gotags: validate:"required"
	Project string `protobuf:"bytes,3,opt,name=project,proto3" json:"project" validate:"required"`
	// 私信内容 @gotags: validate:"required"
	Content              string   `protobuf:"bytes,4,opt,name=content,proto3" json:"content" validate:"required"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *DscMsgBatchSendReq) Reset()         { *m = DscMsgBatchSendReq{} }
func (m *DscMsgBatchSendReq) String() string { return proto.CompactTextString(m) }
func (*DscMsgBatchSendReq) ProtoMessage()    {}
func (*DscMsgBatchSendReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_d37f1fb6677774d8, []int{31}
}

func (m *DscMsgBatchSendReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DscMsgBatchSendReq.Unmarshal(m, b)
}
func (m *DscMsgBatchSendReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DscMsgBatchSendReq.Marshal(b, m, deterministic)
}
func (m *DscMsgBatchSendReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DscMsgBatchSendReq.Merge(m, src)
}
func (m *DscMsgBatchSendReq) XXX_Size() int {
	return xxx_messageInfo_DscMsgBatchSendReq.Size(m)
}
func (m *DscMsgBatchSendReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DscMsgBatchSendReq.DiscardUnknown(m)
}

var xxx_messageInfo_DscMsgBatchSendReq proto.InternalMessageInfo

func (m *DscMsgBatchSendReq) GetUidList() []int64 {
	if m != nil {
		return m.UidList
	}
	return nil
}

func (m *DscMsgBatchSendReq) GetProject() string {
	if m != nil {
		return m.Project
	}
	return ""
}

func (m *DscMsgBatchSendReq) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

type DscUserChannelDf struct {
	// app_id bot_id 机器人id
	AppId string `protobuf:"bytes,1,opt,name=app_id,json=appId,proto3" json:"app_id"`
	// 玩家 dsc 账号 id
	DscUserId string `protobuf:"bytes,2,opt,name=dsc_user_id,json=dscUserId,proto3" json:"dsc_user_id"`
	// 消息收发通道
	PrivChannelId        string   `protobuf:"bytes,3,opt,name=priv_channel_id,json=privChannelId,proto3" json:"priv_channel_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *DscUserChannelDf) Reset()         { *m = DscUserChannelDf{} }
func (m *DscUserChannelDf) String() string { return proto.CompactTextString(m) }
func (*DscUserChannelDf) ProtoMessage()    {}
func (*DscUserChannelDf) Descriptor() ([]byte, []int) {
	return fileDescriptor_d37f1fb6677774d8, []int{32}
}

func (m *DscUserChannelDf) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DscUserChannelDf.Unmarshal(m, b)
}
func (m *DscUserChannelDf) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DscUserChannelDf.Marshal(b, m, deterministic)
}
func (m *DscUserChannelDf) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DscUserChannelDf.Merge(m, src)
}
func (m *DscUserChannelDf) XXX_Size() int {
	return xxx_messageInfo_DscUserChannelDf.Size(m)
}
func (m *DscUserChannelDf) XXX_DiscardUnknown() {
	xxx_messageInfo_DscUserChannelDf.DiscardUnknown(m)
}

var xxx_messageInfo_DscUserChannelDf proto.InternalMessageInfo

func (m *DscUserChannelDf) GetAppId() string {
	if m != nil {
		return m.AppId
	}
	return ""
}

func (m *DscUserChannelDf) GetDscUserId() string {
	if m != nil {
		return m.DscUserId
	}
	return ""
}

func (m *DscUserChannelDf) GetPrivChannelId() string {
	if m != nil {
		return m.PrivChannelId
	}
	return ""
}

type DscMsgBatchSendResp struct {
	Data                 []*DscMsgBatchSendResp_MessageSendDetail `protobuf:"bytes,1,rep,name=data,proto3" json:"data"`
	Success              int32                                    `protobuf:"varint,2,opt,name=success,proto3" json:"success"`
	FailedUidList        []int64                                  `protobuf:"varint,3,rep,packed,name=failed_uid_list,json=failedUidList,proto3" json:"failed_uid_list"`
	XXX_NoUnkeyedLiteral struct{}                                 `json:"-" gorm:"-"`
	XXX_unrecognized     []byte                                   `json:"-" gorm:"-"`
	XXX_sizecache        int32                                    `json:"-" gorm:"-"`
}

func (m *DscMsgBatchSendResp) Reset()         { *m = DscMsgBatchSendResp{} }
func (m *DscMsgBatchSendResp) String() string { return proto.CompactTextString(m) }
func (*DscMsgBatchSendResp) ProtoMessage()    {}
func (*DscMsgBatchSendResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_d37f1fb6677774d8, []int{33}
}

func (m *DscMsgBatchSendResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DscMsgBatchSendResp.Unmarshal(m, b)
}
func (m *DscMsgBatchSendResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DscMsgBatchSendResp.Marshal(b, m, deterministic)
}
func (m *DscMsgBatchSendResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DscMsgBatchSendResp.Merge(m, src)
}
func (m *DscMsgBatchSendResp) XXX_Size() int {
	return xxx_messageInfo_DscMsgBatchSendResp.Size(m)
}
func (m *DscMsgBatchSendResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DscMsgBatchSendResp.DiscardUnknown(m)
}

var xxx_messageInfo_DscMsgBatchSendResp proto.InternalMessageInfo

func (m *DscMsgBatchSendResp) GetData() []*DscMsgBatchSendResp_MessageSendDetail {
	if m != nil {
		return m.Data
	}
	return nil
}

func (m *DscMsgBatchSendResp) GetSuccess() int32 {
	if m != nil {
		return m.Success
	}
	return 0
}

func (m *DscMsgBatchSendResp) GetFailedUidList() []int64 {
	if m != nil {
		return m.FailedUidList
	}
	return nil
}

type DscMsgBatchSendResp_MessageSendDetail struct {
	// 私信消息id
	MsgId string `protobuf:"bytes,1,opt,name=msg_id,json=msgId,proto3" json:"msg_id"`
	// 发送渠道
	ChannelId            string   `protobuf:"bytes,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *DscMsgBatchSendResp_MessageSendDetail) Reset()         { *m = DscMsgBatchSendResp_MessageSendDetail{} }
func (m *DscMsgBatchSendResp_MessageSendDetail) String() string { return proto.CompactTextString(m) }
func (*DscMsgBatchSendResp_MessageSendDetail) ProtoMessage()    {}
func (*DscMsgBatchSendResp_MessageSendDetail) Descriptor() ([]byte, []int) {
	return fileDescriptor_d37f1fb6677774d8, []int{33, 0}
}

func (m *DscMsgBatchSendResp_MessageSendDetail) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DscMsgBatchSendResp_MessageSendDetail.Unmarshal(m, b)
}
func (m *DscMsgBatchSendResp_MessageSendDetail) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DscMsgBatchSendResp_MessageSendDetail.Marshal(b, m, deterministic)
}
func (m *DscMsgBatchSendResp_MessageSendDetail) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DscMsgBatchSendResp_MessageSendDetail.Merge(m, src)
}
func (m *DscMsgBatchSendResp_MessageSendDetail) XXX_Size() int {
	return xxx_messageInfo_DscMsgBatchSendResp_MessageSendDetail.Size(m)
}
func (m *DscMsgBatchSendResp_MessageSendDetail) XXX_DiscardUnknown() {
	xxx_messageInfo_DscMsgBatchSendResp_MessageSendDetail.DiscardUnknown(m)
}

var xxx_messageInfo_DscMsgBatchSendResp_MessageSendDetail proto.InternalMessageInfo

func (m *DscMsgBatchSendResp_MessageSendDetail) GetMsgId() string {
	if m != nil {
		return m.MsgId
	}
	return ""
}

func (m *DscMsgBatchSendResp_MessageSendDetail) GetChannelId() string {
	if m != nil {
		return m.ChannelId
	}
	return ""
}

type DscFileBatchSendReq struct {
	// 玩家uid列表 @gotags: validate:"required"
	UidList []int64 `protobuf:"varint,1,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list" validate:"required"`
	// 发送消息的游戏标识game_project @gotags: validate:"required"
	Project string `protobuf:"bytes,3,opt,name=project,proto3" json:"project" validate:"required"`
	// 文件url  @gotags: validate:"required"
	FileUrl              string   `protobuf:"bytes,4,opt,name=file_url,json=fileUrl,proto3" json:"file_url" validate:"required"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *DscFileBatchSendReq) Reset()         { *m = DscFileBatchSendReq{} }
func (m *DscFileBatchSendReq) String() string { return proto.CompactTextString(m) }
func (*DscFileBatchSendReq) ProtoMessage()    {}
func (*DscFileBatchSendReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_d37f1fb6677774d8, []int{34}
}

func (m *DscFileBatchSendReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DscFileBatchSendReq.Unmarshal(m, b)
}
func (m *DscFileBatchSendReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DscFileBatchSendReq.Marshal(b, m, deterministic)
}
func (m *DscFileBatchSendReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DscFileBatchSendReq.Merge(m, src)
}
func (m *DscFileBatchSendReq) XXX_Size() int {
	return xxx_messageInfo_DscFileBatchSendReq.Size(m)
}
func (m *DscFileBatchSendReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DscFileBatchSendReq.DiscardUnknown(m)
}

var xxx_messageInfo_DscFileBatchSendReq proto.InternalMessageInfo

func (m *DscFileBatchSendReq) GetUidList() []int64 {
	if m != nil {
		return m.UidList
	}
	return nil
}

func (m *DscFileBatchSendReq) GetProject() string {
	if m != nil {
		return m.Project
	}
	return ""
}

func (m *DscFileBatchSendReq) GetFileUrl() string {
	if m != nil {
		return m.FileUrl
	}
	return ""
}

type DscAscBatchSendReq struct {
	// 玩家uid列表 @gotags: validate:"required"
	UidList []int64 `protobuf:"varint,1,rep,packed,name=uid_list,json=uidList,proto3" json:"uid_list" validate:"required"`
	// 发送消息的游戏标识game_project @gotags: validate:"required"
	Project string `protobuf:"bytes,3,opt,name=project,proto3" json:"project" validate:"required"`
	// 文件url
	FileUrl string `protobuf:"bytes,4,opt,name=file_url,json=fileUrl,proto3" json:"file_url"`
	// 私信内容
	Content              string   `protobuf:"bytes,5,opt,name=content,proto3" json:"content"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *DscAscBatchSendReq) Reset()         { *m = DscAscBatchSendReq{} }
func (m *DscAscBatchSendReq) String() string { return proto.CompactTextString(m) }
func (*DscAscBatchSendReq) ProtoMessage()    {}
func (*DscAscBatchSendReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_d37f1fb6677774d8, []int{35}
}

func (m *DscAscBatchSendReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DscAscBatchSendReq.Unmarshal(m, b)
}
func (m *DscAscBatchSendReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DscAscBatchSendReq.Marshal(b, m, deterministic)
}
func (m *DscAscBatchSendReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DscAscBatchSendReq.Merge(m, src)
}
func (m *DscAscBatchSendReq) XXX_Size() int {
	return xxx_messageInfo_DscAscBatchSendReq.Size(m)
}
func (m *DscAscBatchSendReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DscAscBatchSendReq.DiscardUnknown(m)
}

var xxx_messageInfo_DscAscBatchSendReq proto.InternalMessageInfo

func (m *DscAscBatchSendReq) GetUidList() []int64 {
	if m != nil {
		return m.UidList
	}
	return nil
}

func (m *DscAscBatchSendReq) GetProject() string {
	if m != nil {
		return m.Project
	}
	return ""
}

func (m *DscAscBatchSendReq) GetFileUrl() string {
	if m != nil {
		return m.FileUrl
	}
	return ""
}

func (m *DscAscBatchSendReq) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

type DscUserRemarkAddReq struct {
	//  @gotags: validate:"required"
	ChannelId string `protobuf:"bytes,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id" validate:"required"`
	// 备注
	Note                 string   `protobuf:"bytes,2,opt,name=note,proto3" json:"note"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *DscUserRemarkAddReq) Reset()         { *m = DscUserRemarkAddReq{} }
func (m *DscUserRemarkAddReq) String() string { return proto.CompactTextString(m) }
func (*DscUserRemarkAddReq) ProtoMessage()    {}
func (*DscUserRemarkAddReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_d37f1fb6677774d8, []int{36}
}

func (m *DscUserRemarkAddReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DscUserRemarkAddReq.Unmarshal(m, b)
}
func (m *DscUserRemarkAddReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DscUserRemarkAddReq.Marshal(b, m, deterministic)
}
func (m *DscUserRemarkAddReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DscUserRemarkAddReq.Merge(m, src)
}
func (m *DscUserRemarkAddReq) XXX_Size() int {
	return xxx_messageInfo_DscUserRemarkAddReq.Size(m)
}
func (m *DscUserRemarkAddReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DscUserRemarkAddReq.DiscardUnknown(m)
}

var xxx_messageInfo_DscUserRemarkAddReq proto.InternalMessageInfo

func (m *DscUserRemarkAddReq) GetChannelId() string {
	if m != nil {
		return m.ChannelId
	}
	return ""
}

func (m *DscUserRemarkAddReq) GetNote() string {
	if m != nil {
		return m.Note
	}
	return ""
}

type DiscordTabAddReq struct {
	// @gotags: validate:"required"
	TabName string `protobuf:"bytes,1,opt,name=tab_name,json=tabName,proto3" json:"tab_name" validate:"required"`
	// @gotags: validate:"required,oneof=1 2"
	Public int32 `protobuf:"varint,2,opt,name=public,proto3" json:"public" validate:"required,oneof=1 2"`
	// 搜索条件组合 @gotags: validate:"required"
	Detail               *DscUserListReq `protobuf:"bytes,3,opt,name=detail,proto3" json:"detail" validate:"required"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-" gorm:"-"`
	XXX_unrecognized     []byte          `json:"-" gorm:"-"`
	XXX_sizecache        int32           `json:"-" gorm:"-"`
}

func (m *DiscordTabAddReq) Reset()         { *m = DiscordTabAddReq{} }
func (m *DiscordTabAddReq) String() string { return proto.CompactTextString(m) }
func (*DiscordTabAddReq) ProtoMessage()    {}
func (*DiscordTabAddReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_d37f1fb6677774d8, []int{37}
}

func (m *DiscordTabAddReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DiscordTabAddReq.Unmarshal(m, b)
}
func (m *DiscordTabAddReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DiscordTabAddReq.Marshal(b, m, deterministic)
}
func (m *DiscordTabAddReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DiscordTabAddReq.Merge(m, src)
}
func (m *DiscordTabAddReq) XXX_Size() int {
	return xxx_messageInfo_DiscordTabAddReq.Size(m)
}
func (m *DiscordTabAddReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DiscordTabAddReq.DiscardUnknown(m)
}

var xxx_messageInfo_DiscordTabAddReq proto.InternalMessageInfo

func (m *DiscordTabAddReq) GetTabName() string {
	if m != nil {
		return m.TabName
	}
	return ""
}

func (m *DiscordTabAddReq) GetPublic() int32 {
	if m != nil {
		return m.Public
	}
	return 0
}

func (m *DiscordTabAddReq) GetDetail() *DscUserListReq {
	if m != nil {
		return m.Detail
	}
	return nil
}

type DiscordTabEditReq struct {
	// @gotags: validate:"required"
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id" validate:"required"`
	// @gotags: validate:"required"
	TabName string `protobuf:"bytes,2,opt,name=tab_name,json=tabName,proto3" json:"tab_name" validate:"required"`
	// @gotags: validate:"required,oneof=1 2"
	Public               int32    `protobuf:"varint,3,opt,name=public,proto3" json:"public" validate:"required,oneof=1 2"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *DiscordTabEditReq) Reset()         { *m = DiscordTabEditReq{} }
func (m *DiscordTabEditReq) String() string { return proto.CompactTextString(m) }
func (*DiscordTabEditReq) ProtoMessage()    {}
func (*DiscordTabEditReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_d37f1fb6677774d8, []int{38}
}

func (m *DiscordTabEditReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DiscordTabEditReq.Unmarshal(m, b)
}
func (m *DiscordTabEditReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DiscordTabEditReq.Marshal(b, m, deterministic)
}
func (m *DiscordTabEditReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DiscordTabEditReq.Merge(m, src)
}
func (m *DiscordTabEditReq) XXX_Size() int {
	return xxx_messageInfo_DiscordTabEditReq.Size(m)
}
func (m *DiscordTabEditReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DiscordTabEditReq.DiscardUnknown(m)
}

var xxx_messageInfo_DiscordTabEditReq proto.InternalMessageInfo

func (m *DiscordTabEditReq) GetId() int64 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *DiscordTabEditReq) GetTabName() string {
	if m != nil {
		return m.TabName
	}
	return ""
}

func (m *DiscordTabEditReq) GetPublic() int32 {
	if m != nil {
		return m.Public
	}
	return 0
}

type DiscordTabDelReq struct {
	// @gotags: validate:"required"
	Id                   int64    `protobuf:"varint,1,opt,name=id,proto3" json:"id" validate:"required"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *DiscordTabDelReq) Reset()         { *m = DiscordTabDelReq{} }
func (m *DiscordTabDelReq) String() string { return proto.CompactTextString(m) }
func (*DiscordTabDelReq) ProtoMessage()    {}
func (*DiscordTabDelReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_d37f1fb6677774d8, []int{39}
}

func (m *DiscordTabDelReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DiscordTabDelReq.Unmarshal(m, b)
}
func (m *DiscordTabDelReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DiscordTabDelReq.Marshal(b, m, deterministic)
}
func (m *DiscordTabDelReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DiscordTabDelReq.Merge(m, src)
}
func (m *DiscordTabDelReq) XXX_Size() int {
	return xxx_messageInfo_DiscordTabDelReq.Size(m)
}
func (m *DiscordTabDelReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DiscordTabDelReq.DiscardUnknown(m)
}

var xxx_messageInfo_DiscordTabDelReq proto.InternalMessageInfo

func (m *DiscordTabDelReq) GetId() int64 {
	if m != nil {
		return m.Id
	}
	return 0
}

type DiscordTabListResp struct {
	Data                 []*DiscordTabListResp_DiscordTabDetail `protobuf:"bytes,1,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}                               `json:"-" gorm:"-"`
	XXX_unrecognized     []byte                                 `json:"-" gorm:"-"`
	XXX_sizecache        int32                                  `json:"-" gorm:"-"`
}

func (m *DiscordTabListResp) Reset()         { *m = DiscordTabListResp{} }
func (m *DiscordTabListResp) String() string { return proto.CompactTextString(m) }
func (*DiscordTabListResp) ProtoMessage()    {}
func (*DiscordTabListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_d37f1fb6677774d8, []int{40}
}

func (m *DiscordTabListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DiscordTabListResp.Unmarshal(m, b)
}
func (m *DiscordTabListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DiscordTabListResp.Marshal(b, m, deterministic)
}
func (m *DiscordTabListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DiscordTabListResp.Merge(m, src)
}
func (m *DiscordTabListResp) XXX_Size() int {
	return xxx_messageInfo_DiscordTabListResp.Size(m)
}
func (m *DiscordTabListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DiscordTabListResp.DiscardUnknown(m)
}

var xxx_messageInfo_DiscordTabListResp proto.InternalMessageInfo

func (m *DiscordTabListResp) GetData() []*DiscordTabListResp_DiscordTabDetail {
	if m != nil {
		return m.Data
	}
	return nil
}

type DiscordTabListResp_DiscordTabDetail struct {
	Tab                  []*DiscordTabListResp_TabInfo `protobuf:"bytes,1,rep,name=tab,proto3" json:"tab"`
	Project              string                        `protobuf:"bytes,2,opt,name=project,proto3" json:"project"`
	XXX_NoUnkeyedLiteral struct{}                      `json:"-" gorm:"-"`
	XXX_unrecognized     []byte                        `json:"-" gorm:"-"`
	XXX_sizecache        int32                         `json:"-" gorm:"-"`
}

func (m *DiscordTabListResp_DiscordTabDetail) Reset()         { *m = DiscordTabListResp_DiscordTabDetail{} }
func (m *DiscordTabListResp_DiscordTabDetail) String() string { return proto.CompactTextString(m) }
func (*DiscordTabListResp_DiscordTabDetail) ProtoMessage()    {}
func (*DiscordTabListResp_DiscordTabDetail) Descriptor() ([]byte, []int) {
	return fileDescriptor_d37f1fb6677774d8, []int{40, 0}
}

func (m *DiscordTabListResp_DiscordTabDetail) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DiscordTabListResp_DiscordTabDetail.Unmarshal(m, b)
}
func (m *DiscordTabListResp_DiscordTabDetail) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DiscordTabListResp_DiscordTabDetail.Marshal(b, m, deterministic)
}
func (m *DiscordTabListResp_DiscordTabDetail) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DiscordTabListResp_DiscordTabDetail.Merge(m, src)
}
func (m *DiscordTabListResp_DiscordTabDetail) XXX_Size() int {
	return xxx_messageInfo_DiscordTabListResp_DiscordTabDetail.Size(m)
}
func (m *DiscordTabListResp_DiscordTabDetail) XXX_DiscardUnknown() {
	xxx_messageInfo_DiscordTabListResp_DiscordTabDetail.DiscardUnknown(m)
}

var xxx_messageInfo_DiscordTabListResp_DiscordTabDetail proto.InternalMessageInfo

func (m *DiscordTabListResp_DiscordTabDetail) GetTab() []*DiscordTabListResp_TabInfo {
	if m != nil {
		return m.Tab
	}
	return nil
}

func (m *DiscordTabListResp_DiscordTabDetail) GetProject() string {
	if m != nil {
		return m.Project
	}
	return ""
}

type DiscordTabListResp_TabInfo struct {
	Id                   int64           `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	TabName              string          `protobuf:"bytes,2,opt,name=tab_name,json=tabName,proto3" json:"tab_name"`
	Detail               *DscUserListReq `protobuf:"bytes,3,opt,name=detail,proto3" json:"detail"`
	Public               int32           `protobuf:"varint,4,opt,name=public,proto3" json:"public"`
	Operator             string          `protobuf:"bytes,5,opt,name=operator,proto3" json:"operator"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-" gorm:"-"`
	XXX_unrecognized     []byte          `json:"-" gorm:"-"`
	XXX_sizecache        int32           `json:"-" gorm:"-"`
}

func (m *DiscordTabListResp_TabInfo) Reset()         { *m = DiscordTabListResp_TabInfo{} }
func (m *DiscordTabListResp_TabInfo) String() string { return proto.CompactTextString(m) }
func (*DiscordTabListResp_TabInfo) ProtoMessage()    {}
func (*DiscordTabListResp_TabInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_d37f1fb6677774d8, []int{40, 1}
}

func (m *DiscordTabListResp_TabInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DiscordTabListResp_TabInfo.Unmarshal(m, b)
}
func (m *DiscordTabListResp_TabInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DiscordTabListResp_TabInfo.Marshal(b, m, deterministic)
}
func (m *DiscordTabListResp_TabInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DiscordTabListResp_TabInfo.Merge(m, src)
}
func (m *DiscordTabListResp_TabInfo) XXX_Size() int {
	return xxx_messageInfo_DiscordTabListResp_TabInfo.Size(m)
}
func (m *DiscordTabListResp_TabInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_DiscordTabListResp_TabInfo.DiscardUnknown(m)
}

var xxx_messageInfo_DiscordTabListResp_TabInfo proto.InternalMessageInfo

func (m *DiscordTabListResp_TabInfo) GetId() int64 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *DiscordTabListResp_TabInfo) GetTabName() string {
	if m != nil {
		return m.TabName
	}
	return ""
}

func (m *DiscordTabListResp_TabInfo) GetDetail() *DscUserListReq {
	if m != nil {
		return m.Detail
	}
	return nil
}

func (m *DiscordTabListResp_TabInfo) GetPublic() int32 {
	if m != nil {
		return m.Public
	}
	return 0
}

func (m *DiscordTabListResp_TabInfo) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

type DiscordReplyTimeReq struct {
	Project              []string `protobuf:"bytes,1,rep,name=project,proto3" json:"project"`
	Date                 []string `protobuf:"bytes,2,rep,name=date,proto3" json:"date"`
	TagIds               []uint64 `protobuf:"varint,3,rep,packed,name=tag_ids,json=tagIds,proto3" json:"tag_ids"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *DiscordReplyTimeReq) Reset()         { *m = DiscordReplyTimeReq{} }
func (m *DiscordReplyTimeReq) String() string { return proto.CompactTextString(m) }
func (*DiscordReplyTimeReq) ProtoMessage()    {}
func (*DiscordReplyTimeReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_d37f1fb6677774d8, []int{41}
}

func (m *DiscordReplyTimeReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DiscordReplyTimeReq.Unmarshal(m, b)
}
func (m *DiscordReplyTimeReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DiscordReplyTimeReq.Marshal(b, m, deterministic)
}
func (m *DiscordReplyTimeReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DiscordReplyTimeReq.Merge(m, src)
}
func (m *DiscordReplyTimeReq) XXX_Size() int {
	return xxx_messageInfo_DiscordReplyTimeReq.Size(m)
}
func (m *DiscordReplyTimeReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DiscordReplyTimeReq.DiscardUnknown(m)
}

var xxx_messageInfo_DiscordReplyTimeReq proto.InternalMessageInfo

func (m *DiscordReplyTimeReq) GetProject() []string {
	if m != nil {
		return m.Project
	}
	return nil
}

func (m *DiscordReplyTimeReq) GetDate() []string {
	if m != nil {
		return m.Date
	}
	return nil
}

func (m *DiscordReplyTimeReq) GetTagIds() []uint64 {
	if m != nil {
		return m.TagIds
	}
	return nil
}

type DiscordReplyTimeResp struct {
	ReplyCountData       []*DiscordReplyTimeResp_ReplyTimeCountDetail  `protobuf:"bytes,1,rep,name=reply_count_data,json=replyCountData,proto3" json:"reply_count_data"`
	ReplyAvgData         []*DiscordReplyTimeResp_ReplyTimeAvgDayDetail `protobuf:"bytes,2,rep,name=reply_avg_data,json=replyAvgData,proto3" json:"reply_avg_data"`
	XXX_NoUnkeyedLiteral struct{}                                      `json:"-" gorm:"-"`
	XXX_unrecognized     []byte                                        `json:"-" gorm:"-"`
	XXX_sizecache        int32                                         `json:"-" gorm:"-"`
}

func (m *DiscordReplyTimeResp) Reset()         { *m = DiscordReplyTimeResp{} }
func (m *DiscordReplyTimeResp) String() string { return proto.CompactTextString(m) }
func (*DiscordReplyTimeResp) ProtoMessage()    {}
func (*DiscordReplyTimeResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_d37f1fb6677774d8, []int{42}
}

func (m *DiscordReplyTimeResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DiscordReplyTimeResp.Unmarshal(m, b)
}
func (m *DiscordReplyTimeResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DiscordReplyTimeResp.Marshal(b, m, deterministic)
}
func (m *DiscordReplyTimeResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DiscordReplyTimeResp.Merge(m, src)
}
func (m *DiscordReplyTimeResp) XXX_Size() int {
	return xxx_messageInfo_DiscordReplyTimeResp.Size(m)
}
func (m *DiscordReplyTimeResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DiscordReplyTimeResp.DiscardUnknown(m)
}

var xxx_messageInfo_DiscordReplyTimeResp proto.InternalMessageInfo

func (m *DiscordReplyTimeResp) GetReplyCountData() []*DiscordReplyTimeResp_ReplyTimeCountDetail {
	if m != nil {
		return m.ReplyCountData
	}
	return nil
}

func (m *DiscordReplyTimeResp) GetReplyAvgData() []*DiscordReplyTimeResp_ReplyTimeAvgDayDetail {
	if m != nil {
		return m.ReplyAvgData
	}
	return nil
}

type DiscordReplyTimeResp_ReplyTimeCountDetail struct {
	RowName              string   `protobuf:"bytes,1,opt,name=row_name,json=rowName,proto3" json:"row_name"`
	ReplyCountDetail     int64    `protobuf:"varint,2,opt,name=reply_count_detail,json=replyCountDetail,proto3" json:"reply_count_detail"`
	ReplyCountRate       float64  `protobuf:"fixed64,3,opt,name=reply_count_rate,json=replyCountRate,proto3" json:"reply_count_rate"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *DiscordReplyTimeResp_ReplyTimeCountDetail) Reset() {
	*m = DiscordReplyTimeResp_ReplyTimeCountDetail{}
}
func (m *DiscordReplyTimeResp_ReplyTimeCountDetail) String() string {
	return proto.CompactTextString(m)
}
func (*DiscordReplyTimeResp_ReplyTimeCountDetail) ProtoMessage() {}
func (*DiscordReplyTimeResp_ReplyTimeCountDetail) Descriptor() ([]byte, []int) {
	return fileDescriptor_d37f1fb6677774d8, []int{42, 0}
}

func (m *DiscordReplyTimeResp_ReplyTimeCountDetail) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DiscordReplyTimeResp_ReplyTimeCountDetail.Unmarshal(m, b)
}
func (m *DiscordReplyTimeResp_ReplyTimeCountDetail) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DiscordReplyTimeResp_ReplyTimeCountDetail.Marshal(b, m, deterministic)
}
func (m *DiscordReplyTimeResp_ReplyTimeCountDetail) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DiscordReplyTimeResp_ReplyTimeCountDetail.Merge(m, src)
}
func (m *DiscordReplyTimeResp_ReplyTimeCountDetail) XXX_Size() int {
	return xxx_messageInfo_DiscordReplyTimeResp_ReplyTimeCountDetail.Size(m)
}
func (m *DiscordReplyTimeResp_ReplyTimeCountDetail) XXX_DiscardUnknown() {
	xxx_messageInfo_DiscordReplyTimeResp_ReplyTimeCountDetail.DiscardUnknown(m)
}

var xxx_messageInfo_DiscordReplyTimeResp_ReplyTimeCountDetail proto.InternalMessageInfo

func (m *DiscordReplyTimeResp_ReplyTimeCountDetail) GetRowName() string {
	if m != nil {
		return m.RowName
	}
	return ""
}

func (m *DiscordReplyTimeResp_ReplyTimeCountDetail) GetReplyCountDetail() int64 {
	if m != nil {
		return m.ReplyCountDetail
	}
	return 0
}

func (m *DiscordReplyTimeResp_ReplyTimeCountDetail) GetReplyCountRate() float64 {
	if m != nil {
		return m.ReplyCountRate
	}
	return 0
}

type DiscordReplyTimeResp_ReplyTimeAvgDayDetail struct {
	Date                 string   `protobuf:"bytes,1,opt,name=date,proto3" json:"date"`
	AvgReplyTime         float64  `protobuf:"fixed64,2,opt,name=avg_reply_time,json=avgReplyTime,proto3" json:"avg_reply_time"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *DiscordReplyTimeResp_ReplyTimeAvgDayDetail) Reset() {
	*m = DiscordReplyTimeResp_ReplyTimeAvgDayDetail{}
}
func (m *DiscordReplyTimeResp_ReplyTimeAvgDayDetail) String() string {
	return proto.CompactTextString(m)
}
func (*DiscordReplyTimeResp_ReplyTimeAvgDayDetail) ProtoMessage() {}
func (*DiscordReplyTimeResp_ReplyTimeAvgDayDetail) Descriptor() ([]byte, []int) {
	return fileDescriptor_d37f1fb6677774d8, []int{42, 1}
}

func (m *DiscordReplyTimeResp_ReplyTimeAvgDayDetail) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DiscordReplyTimeResp_ReplyTimeAvgDayDetail.Unmarshal(m, b)
}
func (m *DiscordReplyTimeResp_ReplyTimeAvgDayDetail) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DiscordReplyTimeResp_ReplyTimeAvgDayDetail.Marshal(b, m, deterministic)
}
func (m *DiscordReplyTimeResp_ReplyTimeAvgDayDetail) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DiscordReplyTimeResp_ReplyTimeAvgDayDetail.Merge(m, src)
}
func (m *DiscordReplyTimeResp_ReplyTimeAvgDayDetail) XXX_Size() int {
	return xxx_messageInfo_DiscordReplyTimeResp_ReplyTimeAvgDayDetail.Size(m)
}
func (m *DiscordReplyTimeResp_ReplyTimeAvgDayDetail) XXX_DiscardUnknown() {
	xxx_messageInfo_DiscordReplyTimeResp_ReplyTimeAvgDayDetail.DiscardUnknown(m)
}

var xxx_messageInfo_DiscordReplyTimeResp_ReplyTimeAvgDayDetail proto.InternalMessageInfo

func (m *DiscordReplyTimeResp_ReplyTimeAvgDayDetail) GetDate() string {
	if m != nil {
		return m.Date
	}
	return ""
}

func (m *DiscordReplyTimeResp_ReplyTimeAvgDayDetail) GetAvgReplyTime() float64 {
	if m != nil {
		return m.AvgReplyTime
	}
	return 0
}

type DiscordReplyStatusRectifyReq struct {
	// @gotags: validate:"required"
	ChannelId string `protobuf:"bytes,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id" validate:"required"`
	// @gotags: validate:"required"
	DscUserId string `protobuf:"bytes,2,opt,name=dsc_user_id,json=dscUserId,proto3" json:"dsc_user_id" validate:"required"`
	// @gotags: validate:"required,oneof=1 2"
	OldReplyStatus uint32 `protobuf:"varint,3,opt,name=old_reply_status,json=oldReplyStatus,proto3" json:"old_reply_status" validate:"required,oneof=1 2"`
	// @gotags: validate:"required,oneof=1 2"
	NewReplyStatus       uint32   `protobuf:"varint,4,opt,name=new_reply_status,json=newReplyStatus,proto3" json:"new_reply_status" validate:"required,oneof=1 2"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *DiscordReplyStatusRectifyReq) Reset()         { *m = DiscordReplyStatusRectifyReq{} }
func (m *DiscordReplyStatusRectifyReq) String() string { return proto.CompactTextString(m) }
func (*DiscordReplyStatusRectifyReq) ProtoMessage()    {}
func (*DiscordReplyStatusRectifyReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_d37f1fb6677774d8, []int{43}
}

func (m *DiscordReplyStatusRectifyReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DiscordReplyStatusRectifyReq.Unmarshal(m, b)
}
func (m *DiscordReplyStatusRectifyReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DiscordReplyStatusRectifyReq.Marshal(b, m, deterministic)
}
func (m *DiscordReplyStatusRectifyReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DiscordReplyStatusRectifyReq.Merge(m, src)
}
func (m *DiscordReplyStatusRectifyReq) XXX_Size() int {
	return xxx_messageInfo_DiscordReplyStatusRectifyReq.Size(m)
}
func (m *DiscordReplyStatusRectifyReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DiscordReplyStatusRectifyReq.DiscardUnknown(m)
}

var xxx_messageInfo_DiscordReplyStatusRectifyReq proto.InternalMessageInfo

func (m *DiscordReplyStatusRectifyReq) GetChannelId() string {
	if m != nil {
		return m.ChannelId
	}
	return ""
}

func (m *DiscordReplyStatusRectifyReq) GetDscUserId() string {
	if m != nil {
		return m.DscUserId
	}
	return ""
}

func (m *DiscordReplyStatusRectifyReq) GetOldReplyStatus() uint32 {
	if m != nil {
		return m.OldReplyStatus
	}
	return 0
}

func (m *DiscordReplyStatusRectifyReq) GetNewReplyStatus() uint32 {
	if m != nil {
		return m.NewReplyStatus
	}
	return 0
}

type DiscordTabCountResp struct {
	Detail               []*DiscordTabCountResp_DiscordTabCount `protobuf:"bytes,1,rep,name=detail,proto3" json:"detail"`
	XXX_NoUnkeyedLiteral struct{}                               `json:"-" gorm:"-"`
	XXX_unrecognized     []byte                                 `json:"-" gorm:"-"`
	XXX_sizecache        int32                                  `json:"-" gorm:"-"`
}

func (m *DiscordTabCountResp) Reset()         { *m = DiscordTabCountResp{} }
func (m *DiscordTabCountResp) String() string { return proto.CompactTextString(m) }
func (*DiscordTabCountResp) ProtoMessage()    {}
func (*DiscordTabCountResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_d37f1fb6677774d8, []int{44}
}

func (m *DiscordTabCountResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DiscordTabCountResp.Unmarshal(m, b)
}
func (m *DiscordTabCountResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DiscordTabCountResp.Marshal(b, m, deterministic)
}
func (m *DiscordTabCountResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DiscordTabCountResp.Merge(m, src)
}
func (m *DiscordTabCountResp) XXX_Size() int {
	return xxx_messageInfo_DiscordTabCountResp.Size(m)
}
func (m *DiscordTabCountResp) XXX_DiscardUnknown() {
	xxx_messageInfo_DiscordTabCountResp.DiscardUnknown(m)
}

var xxx_messageInfo_DiscordTabCountResp proto.InternalMessageInfo

func (m *DiscordTabCountResp) GetDetail() []*DiscordTabCountResp_DiscordTabCount {
	if m != nil {
		return m.Detail
	}
	return nil
}

type DiscordTabCountResp_DiscordTabCount struct {
	Tab                  []*DiscordTabCountResp_TabCountDetail `protobuf:"bytes,1,rep,name=tab,proto3" json:"tab"`
	Project              string                                `protobuf:"bytes,2,opt,name=project,proto3" json:"project"`
	XXX_NoUnkeyedLiteral struct{}                              `json:"-" gorm:"-"`
	XXX_unrecognized     []byte                                `json:"-" gorm:"-"`
	XXX_sizecache        int32                                 `json:"-" gorm:"-"`
}

func (m *DiscordTabCountResp_DiscordTabCount) Reset()         { *m = DiscordTabCountResp_DiscordTabCount{} }
func (m *DiscordTabCountResp_DiscordTabCount) String() string { return proto.CompactTextString(m) }
func (*DiscordTabCountResp_DiscordTabCount) ProtoMessage()    {}
func (*DiscordTabCountResp_DiscordTabCount) Descriptor() ([]byte, []int) {
	return fileDescriptor_d37f1fb6677774d8, []int{44, 0}
}

func (m *DiscordTabCountResp_DiscordTabCount) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DiscordTabCountResp_DiscordTabCount.Unmarshal(m, b)
}
func (m *DiscordTabCountResp_DiscordTabCount) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DiscordTabCountResp_DiscordTabCount.Marshal(b, m, deterministic)
}
func (m *DiscordTabCountResp_DiscordTabCount) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DiscordTabCountResp_DiscordTabCount.Merge(m, src)
}
func (m *DiscordTabCountResp_DiscordTabCount) XXX_Size() int {
	return xxx_messageInfo_DiscordTabCountResp_DiscordTabCount.Size(m)
}
func (m *DiscordTabCountResp_DiscordTabCount) XXX_DiscardUnknown() {
	xxx_messageInfo_DiscordTabCountResp_DiscordTabCount.DiscardUnknown(m)
}

var xxx_messageInfo_DiscordTabCountResp_DiscordTabCount proto.InternalMessageInfo

func (m *DiscordTabCountResp_DiscordTabCount) GetTab() []*DiscordTabCountResp_TabCountDetail {
	if m != nil {
		return m.Tab
	}
	return nil
}

func (m *DiscordTabCountResp_DiscordTabCount) GetProject() string {
	if m != nil {
		return m.Project
	}
	return ""
}

type DiscordTabCountResp_TabCountDetail struct {
	TabName              string   `protobuf:"bytes,2,opt,name=tab_name,json=tabName,proto3" json:"tab_name"`
	Count                uint64   `protobuf:"varint,5,opt,name=count,proto3" json:"count"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *DiscordTabCountResp_TabCountDetail) Reset()         { *m = DiscordTabCountResp_TabCountDetail{} }
func (m *DiscordTabCountResp_TabCountDetail) String() string { return proto.CompactTextString(m) }
func (*DiscordTabCountResp_TabCountDetail) ProtoMessage()    {}
func (*DiscordTabCountResp_TabCountDetail) Descriptor() ([]byte, []int) {
	return fileDescriptor_d37f1fb6677774d8, []int{44, 1}
}

func (m *DiscordTabCountResp_TabCountDetail) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DiscordTabCountResp_TabCountDetail.Unmarshal(m, b)
}
func (m *DiscordTabCountResp_TabCountDetail) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DiscordTabCountResp_TabCountDetail.Marshal(b, m, deterministic)
}
func (m *DiscordTabCountResp_TabCountDetail) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DiscordTabCountResp_TabCountDetail.Merge(m, src)
}
func (m *DiscordTabCountResp_TabCountDetail) XXX_Size() int {
	return xxx_messageInfo_DiscordTabCountResp_TabCountDetail.Size(m)
}
func (m *DiscordTabCountResp_TabCountDetail) XXX_DiscardUnknown() {
	xxx_messageInfo_DiscordTabCountResp_TabCountDetail.DiscardUnknown(m)
}

var xxx_messageInfo_DiscordTabCountResp_TabCountDetail proto.InternalMessageInfo

func (m *DiscordTabCountResp_TabCountDetail) GetTabName() string {
	if m != nil {
		return m.TabName
	}
	return ""
}

func (m *DiscordTabCountResp_TabCountDetail) GetCount() uint64 {
	if m != nil {
		return m.Count
	}
	return 0
}

type DiscordTabUpdateSortReq struct {
	// @gotags: validate:"required"
	SortSetting          string   `protobuf:"bytes,1,opt,name=sort_setting,json=sortSetting,proto3" json:"sort_setting" validate:"required"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *DiscordTabUpdateSortReq) Reset()         { *m = DiscordTabUpdateSortReq{} }
func (m *DiscordTabUpdateSortReq) String() string { return proto.CompactTextString(m) }
func (*DiscordTabUpdateSortReq) ProtoMessage()    {}
func (*DiscordTabUpdateSortReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_d37f1fb6677774d8, []int{45}
}

func (m *DiscordTabUpdateSortReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_DiscordTabUpdateSortReq.Unmarshal(m, b)
}
func (m *DiscordTabUpdateSortReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_DiscordTabUpdateSortReq.Marshal(b, m, deterministic)
}
func (m *DiscordTabUpdateSortReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_DiscordTabUpdateSortReq.Merge(m, src)
}
func (m *DiscordTabUpdateSortReq) XXX_Size() int {
	return xxx_messageInfo_DiscordTabUpdateSortReq.Size(m)
}
func (m *DiscordTabUpdateSortReq) XXX_DiscardUnknown() {
	xxx_messageInfo_DiscordTabUpdateSortReq.DiscardUnknown(m)
}

var xxx_messageInfo_DiscordTabUpdateSortReq proto.InternalMessageInfo

func (m *DiscordTabUpdateSortReq) GetSortSetting() string {
	if m != nil {
		return m.SortSetting
	}
	return ""
}

func init() {
	proto.RegisterType((*DscUserListReq)(nil), "pb.DscUserListReq")
	proto.RegisterType((*DscUserListResp)(nil), "pb.DscUserListResp")
	proto.RegisterType((*DscUserListResp_DscUser)(nil), "pb.DscUserListResp.DscUser")
	proto.RegisterType((*DscUserDetailReq)(nil), "pb.DscUserDetailReq")
	proto.RegisterType((*DscUserDetailResp)(nil), "pb.DscUserDetailResp")
	proto.RegisterType((*DscUserDetailResp_UserDetail)(nil), "pb.DscUserDetailResp.UserDetail")
	proto.RegisterType((*DscChannelDialogReq)(nil), "pb.DscChannelDialogReq")
	proto.RegisterType((*DscDialogDetailResp)(nil), "pb.DscDialogDetailResp")
	proto.RegisterType((*DscDialogDetail)(nil), "pb.DscDialogDetail")
	proto.RegisterType((*DscMsgSticker)(nil), "pb.DscMsgSticker")
	proto.RegisterType((*DscMsgReaction)(nil), "pb.DscMsgReaction")
	proto.RegisterType((*DscMsgAttach)(nil), "pb.DscMsgAttach")
	proto.RegisterType((*DscMsgEmbed)(nil), "pb.DscMsgEmbed")
	proto.RegisterType((*DscMsgEmbed_Provider)(nil), "pb.DscMsgEmbed.Provider")
	proto.RegisterType((*DscMsgEmbed_Thumbnail)(nil), "pb.DscMsgEmbed.Thumbnail")
	proto.RegisterType((*DscMsgEmbed_Video)(nil), "pb.DscMsgEmbed.Video")
	proto.RegisterType((*DscMsgPoll)(nil), "pb.DscMsgPoll")
	proto.RegisterType((*DscMsgPollResult)(nil), "pb.DscMsgPollResult")
	proto.RegisterType((*DscMsgPollAnswerCount)(nil), "pb.DscMsgPollAnswerCount")
	proto.RegisterType((*DscMsgPollAnswer)(nil), "pb.DscMsgPollAnswer")
	proto.RegisterType((*DscAuthor)(nil), "pb.DscAuthor")
	proto.RegisterType((*DscChannelMsgCreateReq)(nil), "pb.DscChannelMsgCreateReq")
	proto.RegisterType((*DscChannelMsgCreateResp)(nil), "pb.DscChannelMsgCreateResp")
	proto.RegisterType((*DscChannelMsgEditReq)(nil), "pb.DscChannelMsgEditReq")
	proto.RegisterType((*DscChannelFileCreateReq)(nil), "pb.DscChannelFileCreateReq")
	proto.RegisterType((*DscChannelDialogFreshReq)(nil), "pb.DscChannelDialogFreshReq")
	proto.RegisterType((*DscDialogFreshEvent)(nil), "pb.DscDialogFreshEvent")
	proto.RegisterType((*DscChannelDialogFreshResp)(nil), "pb.DscChannelDialogFreshResp")
	proto.RegisterType((*DscPoolInfo)(nil), "pb.DscPoolInfo")
	proto.RegisterType((*DiscordStatsReq)(nil), "pb.DiscordStatsReq")
	proto.RegisterType((*DiscordStatsResp)(nil), "pb.DiscordStatsResp")
	proto.RegisterType((*DscProxyResourceReq)(nil), "pb.DscProxyResourceReq")
	proto.RegisterType((*DiscordPlayerInteractStatsReq)(nil), "pb.DiscordPlayerInteractStatsReq")
	proto.RegisterType((*DiscordMessageCountReq)(nil), "pb.DiscordMessageCountReq")
	proto.RegisterType((*DiscordMessageCountResp)(nil), "pb.DiscordMessageCountResp")
	proto.RegisterType((*DiscordMessageCountResp_MessageCountDetail)(nil), "pb.DiscordMessageCountResp.MessageCountDetail")
	proto.RegisterType((*DiscordPlayerUidReq)(nil), "pb.DiscordPlayerUidReq")
	proto.RegisterType((*DscMsgBatchSendReq)(nil), "pb.DscMsgBatchSendReq")
	proto.RegisterType((*DscUserChannelDf)(nil), "pb.DscUserChannelDf")
	proto.RegisterType((*DscMsgBatchSendResp)(nil), "pb.DscMsgBatchSendResp")
	proto.RegisterType((*DscMsgBatchSendResp_MessageSendDetail)(nil), "pb.DscMsgBatchSendResp.MessageSendDetail")
	proto.RegisterType((*DscFileBatchSendReq)(nil), "pb.DscFileBatchSendReq")
	proto.RegisterType((*DscAscBatchSendReq)(nil), "pb.DscAscBatchSendReq")
	proto.RegisterType((*DscUserRemarkAddReq)(nil), "pb.DscUserRemarkAddReq")
	proto.RegisterType((*DiscordTabAddReq)(nil), "pb.DiscordTabAddReq")
	proto.RegisterType((*DiscordTabEditReq)(nil), "pb.DiscordTabEditReq")
	proto.RegisterType((*DiscordTabDelReq)(nil), "pb.DiscordTabDelReq")
	proto.RegisterType((*DiscordTabListResp)(nil), "pb.DiscordTabListResp")
	proto.RegisterType((*DiscordTabListResp_DiscordTabDetail)(nil), "pb.DiscordTabListResp.DiscordTabDetail")
	proto.RegisterType((*DiscordTabListResp_TabInfo)(nil), "pb.DiscordTabListResp.TabInfo")
	proto.RegisterType((*DiscordReplyTimeReq)(nil), "pb.DiscordReplyTimeReq")
	proto.RegisterType((*DiscordReplyTimeResp)(nil), "pb.DiscordReplyTimeResp")
	proto.RegisterType((*DiscordReplyTimeResp_ReplyTimeCountDetail)(nil), "pb.DiscordReplyTimeResp.ReplyTimeCountDetail")
	proto.RegisterType((*DiscordReplyTimeResp_ReplyTimeAvgDayDetail)(nil), "pb.DiscordReplyTimeResp.ReplyTimeAvgDayDetail")
	proto.RegisterType((*DiscordReplyStatusRectifyReq)(nil), "pb.DiscordReplyStatusRectifyReq")
	proto.RegisterType((*DiscordTabCountResp)(nil), "pb.DiscordTabCountResp")
	proto.RegisterType((*DiscordTabCountResp_DiscordTabCount)(nil), "pb.DiscordTabCountResp.DiscordTabCount")
	proto.RegisterType((*DiscordTabCountResp_TabCountDetail)(nil), "pb.DiscordTabCountResp.TabCountDetail")
	proto.RegisterType((*DiscordTabUpdateSortReq)(nil), "pb.DiscordTabUpdateSortReq")
}

func init() {
	proto.RegisterFile("discord.proto", fileDescriptor_d37f1fb6677774d8)
}

var fileDescriptor_d37f1fb6677774d8 = []byte{
	// 3757 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xdc, 0x3a, 0x4d, 0x6f, 0x64, 0x49,
	0x52, 0xaa, 0x0f, 0xd7, 0x47, 0x94, 0x3f, 0xd3, 0x76, 0xfb, 0xb9, 0xba, 0x3d, 0xed, 0x79, 0xcc,
	0xcc, 0x9a, 0x9e, 0x59, 0x7b, 0x64, 0x76, 0xb4, 0xab, 0xd9, 0x45, 0xc8, 0xd3, 0xee, 0x06, 0x8b,
	0xee, 0x59, 0xf3, 0xec, 0x9e, 0x45, 0x08, 0xf6, 0x29, 0xeb, 0xbd, 0x74, 0xd5, 0x63, 0x5e, 0xd5,
	0x7b, 0xfd, 0x32, 0xab, 0x3c, 0x35, 0x12, 0x2b, 0x34, 0x12, 0x87, 0x15, 0x17, 0x10, 0xf0, 0x0f,
	0xb8, 0x70, 0x44, 0x88, 0xff, 0xc0, 0x01, 0x4e, 0x5c, 0x38, 0x70, 0x41, 0x9a, 0x23, 0x27, 0x24,
	0xee, 0x28, 0x22, 0xf3, 0x7d, 0xd5, 0x87, 0xdb, 0x33, 0x03, 0x07, 0xf6, 0x54, 0x2f, 0x23, 0x22,
	0x23, 0x32, 0x22, 0x23, 0x22, 0x23, 0x23, 0x0b, 0xd6, 0xfc, 0x40, 0x7a, 0x51, 0xe2, 0x1f, 0xc7,
	0x49, 0xa4, 0x22, 0x56, 0x8d, 0x7b, 0xdd, 0x47, 0xfd, 0x28, 0xea, 0x87, 0xe2, 0x84, 0xc7, 0xc1,
	0x09, 0x1f, 0x8d, 0x22, 0xc5, 0x55, 0x10, 0x8d, 0xa4, 0xa6, 0xe8, 0x32, 0x33, 0xc1, 0x15, 0xa3,
	0xf1, 0xd0, 0xc0, 0xa0, 0xf0, 0xdd, 0xf6, 0xa5, 0x67, 0x3e, 0x57, 0xbd, 0x68, 0x38, 0x8c, 0x46,
	0x7a, 0x64, 0x7f, 0xdd, 0x80, 0xf5, 0x73, 0xe9, 0xbd, 0x92, 0x22, 0x79, 0x11, 0x48, 0xe5, 0x88,
	0xd7, 0xcc, 0x82, 0x66, 0x9c, 0x44, 0x7f, 0x2c, 0x3c, 0x65, 0x55, 0x0e, 0x6b, 0x47, 0x6d, 0x27,
	0x1d, 0xb2, 0x03, 0x80, 0x44, 0xc4, 0x61, 0x20, 0x7c, 0x97, 0x2b, 0xab, 0x4a, 0xc8, 0xb6, 0x81,
	0x9c, 0x29, 0xf6, 0x00, 0x1a, 0x52, 0x71, 0x35, 0x96, 0x56, 0xed, 0xb0, 0x76, 0xb4, 0xe6, 0x98,
	0x11, 0x7b, 0x1b, 0x56, 0xc7, 0x52, 0x24, 0xae, 0x17, 0x8d, 0x94, 0x18, 0x29, 0xab, 0x7e, 0x58,
	0x39, 0x6a, 0x3b, 0x1d, 0x84, 0x3d, 0xd5, 0x20, 0xf6, 0x01, 0x30, 0x22, 0xf1, 0x85, 0xe2, 0x41,
	0xe8, 0x26, 0x62, 0xc8, 0x93, 0xcf, 0xad, 0x15, 0x22, 0xdc, 0x44, 0xcc, 0x39, 0x21, 0x1c, 0x82,
	0xb3, 0x47, 0xd0, 0x8e, 0x93, 0xc8, 0x13, 0x52, 0x46, 0x89, 0xd5, 0xd0, 0xcb, 0xc8, 0x00, 0xec,
	0x09, 0x6c, 0xf9, 0xd2, 0x73, 0x89, 0xdf, 0x28, 0xf0, 0x3e, 0x1f, 0xf1, 0xa1, 0xb0, 0x9a, 0x44,
	0xb5, 0xe1, 0x6b, 0x55, 0x3f, 0x35, 0x60, 0xf6, 0x16, 0x74, 0x32, 0xda, 0xc0, 0xb7, 0x5a, 0x24,
	0xb0, 0x6d, 0xa8, 0x2e, 0x7c, 0xb6, 0x09, 0xb5, 0x71, 0xe0, 0x5b, 0xed, 0xc3, 0xca, 0x51, 0xdd,
	0xc1, 0x4f, 0xc6, 0xa0, 0x7e, 0x13, 0x07, 0xbe, 0x05, 0x44, 0x4a, 0xdf, 0x48, 0x25, 0x03, 0xdf,
	0xea, 0x10, 0x08, 0x3f, 0xd1, 0x52, 0x21, 0x97, 0xca, 0x0d, 0xa3, 0x7e, 0x30, 0xb2, 0x56, 0xf5,
	0x12, 0x11, 0xf2, 0x02, 0x01, 0x6c, 0x0f, 0x9a, 0x31, 0x9f, 0xba, 0x3c, 0x0c, 0xad, 0xb5, 0xc3,
	0xda, 0x51, 0xcd, 0x69, 0xc4, 0x7c, 0x7a, 0x16, 0x86, 0xec, 0x04, 0x76, 0x10, 0x41, 0x73, 0xd5,
	0x20, 0x48, 0xd4, 0xd4, 0xf5, 0xf9, 0x54, 0x5a, 0xeb, 0x44, 0xb5, 0x15, 0xf3, 0xe9, 0x0b, 0x2e,
	0xd5, 0x35, 0x61, 0xce, 0xf9, 0x54, 0xb2, 0x87, 0xd0, 0x9e, 0x04, 0xb1, 0x8b, 0x96, 0x16, 0xd6,
	0xc6, 0x61, 0xe5, 0x68, 0xcd, 0x69, 0x4d, 0x82, 0xf8, 0x0a, 0xc7, 0xb8, 0x2e, 0xc5, 0xfb, 0xd6,
	0x26, 0x89, 0xc7, 0x4f, 0xd6, 0x85, 0x56, 0x2f, 0x48, 0xd4, 0xc0, 0xe7, 0x53, 0x6b, 0x8b, 0xc0,
	0xd9, 0x18, 0x35, 0x0b, 0xf9, 0xa8, 0x6f, 0x31, 0xad, 0x19, 0x7e, 0xe3, 0xbe, 0xd0, 0x5a, 0x70,
	0x93, 0xa7, 0xae, 0x14, 0xc9, 0x24, 0xf0, 0x84, 0xb5, 0x4d, 0x33, 0x37, 0x11, 0xe3, 0x20, 0xe2,
	0x4a, 0xc3, 0x51, 0x6b, 0x19, 0x25, 0xca, 0xbd, 0x09, 0x44, 0xe8, 0x5b, 0x3b, 0xda, 0x98, 0x08,
	0x79, 0x8e, 0x00, 0xb6, 0x03, 0x2b, 0x51, 0xe2, 0x8b, 0xc4, 0xda, 0x25, 0x8c, 0x1e, 0xa0, 0xd8,
	0x98, 0xf7, 0x85, 0xf5, 0x80, 0x16, 0x4f, 0xdf, 0xa8, 0x15, 0xfe, 0xba, 0x32, 0xf8, 0x52, 0x58,
	0x7b, 0x5a, 0x2b, 0x04, 0x5c, 0x05, 0x5f, 0x0a, 0x34, 0x5e, 0x2f, 0x52, 0x6e, 0xe0, 0x4b, 0xcb,
	0xa2, 0x85, 0x34, 0x7a, 0x91, 0xba, 0xf0, 0x25, 0x72, 0x1a, 0x23, 0x74, 0x5f, 0x2b, 0x80, 0xdf,
	0x08, 0x53, 0xbc, 0x2f, 0xad, 0x2e, 0x79, 0x24, 0x7d, 0xb3, 0x0f, 0xa0, 0xa5, 0x78, 0xdf, 0x55,
	0xd3, 0x58, 0x58, 0x0f, 0x0f, 0x2b, 0x47, 0xeb, 0xa7, 0x5b, 0xc7, 0x71, 0xef, 0xf8, 0x79, 0x10,
	0x2a, 0x91, 0x5c, 0xf3, 0xfe, 0xb3, 0xd1, 0x78, 0xe8, 0x34, 0x15, 0xef, 0x5f, 0x4f, 0x63, 0xc1,
	0xbe, 0x07, 0x4d, 0x52, 0xaa, 0x37, 0xb5, 0x1e, 0x11, 0xf1, 0x3a, 0x12, 0x9f, 0x7b, 0x97, 0x51,
	0x14, 0x5e, 0x45, 0x89, 0x72, 0x1a, 0x88, 0xfe, 0x64, 0x8a, 0xb6, 0x45, 0x9b, 0x8d, 0x51, 0x99,
	0x03, 0x6d, 0xdb, 0x74, 0x6c, 0xff, 0x73, 0x13, 0x36, 0x4a, 0x61, 0x26, 0x63, 0x0c, 0x0b, 0x6f,
	0x9c, 0x24, 0x62, 0xa4, 0x5c, 0x32, 0x40, 0x85, 0xf4, 0xec, 0x18, 0xd8, 0x25, 0xda, 0x61, 0x1f,
	0x5a, 0xb1, 0x48, 0x34, 0xba, 0x4a, 0xe8, 0x66, 0x2c, 0x12, 0x42, 0xed, 0xc0, 0x8a, 0x8a, 0x14,
	0x0f, 0xad, 0x1a, 0xc1, 0xf5, 0x80, 0x9d, 0x40, 0xdd, 0xe7, 0x8a, 0x5b, 0xf5, 0xc3, 0xda, 0x51,
	0xe7, 0xf4, 0x21, 0xad, 0xb4, 0x2c, 0x36, 0x1d, 0x3b, 0x44, 0xd8, 0xfd, 0x87, 0x06, 0x34, 0x0d,
	0xa4, 0x1c, 0xf8, 0x95, 0x62, 0xe0, 0xcf, 0x84, 0x49, 0x75, 0x36, 0x4c, 0x1e, 0x42, 0x5b, 0x87,
	0x1b, 0x86, 0x5a, 0x8d, 0xb0, 0x2d, 0x04, 0x7c, 0x8a, 0x31, 0xf6, 0x18, 0x3a, 0xfd, 0x30, 0xea,
	0xf1, 0x50, 0xa3, 0x75, 0xf4, 0x83, 0x06, 0x11, 0xc1, 0x01, 0x80, 0x3f, 0x74, 0xbd, 0x01, 0x1f,
	0x8d, 0x44, 0x68, 0x82, 0xbe, 0xed, 0x0f, 0x9f, 0x6a, 0x00, 0x1a, 0xa1, 0x3f, 0x0e, 0x42, 0x1f,
	0x25, 0x37, 0xf4, 0xba, 0x68, 0x7c, 0xe1, 0x97, 0x13, 0x41, 0x53, 0x4f, 0xcc, 0x13, 0xc1, 0x43,
	0x68, 0x93, 0x55, 0xdc, 0x98, 0x4f, 0x29, 0xb4, 0x2b, 0x4e, 0x8b, 0x00, 0x97, 0x7c, 0xba, 0x34,
	0xd2, 0xda, 0x44, 0xb7, 0x20, 0xd2, 0xca, 0x21, 0xad, 0xc3, 0xbf, 0x10, 0xd2, 0x79, 0xf2, 0xeb,
	0xd0, 0x86, 0xa4, 0xc9, 0xcf, 0x04, 0x68, 0x28, 0x26, 0x22, 0xb4, 0x56, 0xb3, 0x00, 0x7d, 0x81,
	0xe3, 0x2c, 0x99, 0xac, 0x95, 0x93, 0x09, 0xa6, 0x9c, 0xf5, 0x3c, 0xe5, 0x98, 0xf4, 0xb2, 0x91,
	0xa7, 0x97, 0x5d, 0x68, 0xe8, 0x10, 0xb0, 0x36, 0x75, 0x28, 0x51, 0x04, 0xa0, 0xa5, 0x10, 0x2c,
	0x07, 0xd1, 0xad, 0xb5, 0xad, 0x2d, 0xd5, 0x8b, 0xd4, 0xd5, 0x20, 0xba, 0x45, 0x49, 0xa3, 0x48,
	0x09, 0x6b, 0x4b, 0x4b, 0xc2, 0x6f, 0xdc, 0x98, 0x38, 0xe4, 0x53, 0x93, 0x26, 0x4d, 0xdc, 0x83,
	0x06, 0x61, 0x86, 0x2c, 0x65, 0x8b, 0x07, 0x7a, 0x57, 0xe7, 0xb2, 0xc5, 0x4e, 0x21, 0x5b, 0x58,
	0xd0, 0xf4, 0x06, 0xc2, 0xfb, 0x5c, 0xf8, 0x14, 0xe2, 0x2d, 0x27, 0x1d, 0xb2, 0xf7, 0x60, 0xa3,
	0x90, 0x47, 0x54, 0x30, 0x14, 0x96, 0x45, 0x13, 0xd7, 0xb2, 0x24, 0x72, 0x1d, 0x0c, 0x05, 0xc6,
	0xc4, 0x2d, 0x0f, 0x54, 0x30, 0xea, 0x6b, 0x22, 0x1d, 0xca, 0x1d, 0x03, 0x23, 0x92, 0x63, 0xd8,
	0x56, 0x81, 0xf7, 0xb9, 0x50, 0xae, 0x97, 0x08, 0xae, 0x84, 0xeb, 0x45, 0xe3, 0x91, 0xb2, 0xba,
	0x87, 0x15, 0xcc, 0x90, 0x1a, 0xf5, 0x94, 0x30, 0x4f, 0x11, 0xc1, 0x3e, 0x82, 0x3d, 0xbd, 0xc9,
	0xa5, 0x49, 0xc4, 0xfd, 0x21, 0x71, 0xdf, 0x41, 0xf4, 0x75, 0x61, 0x1e, 0x89, 0x41, 0xe7, 0xd1,
	0x33, 0x02, 0x9f, 0x02, 0xbf, 0xee, 0xb4, 0x34, 0xe0, 0xc2, 0xb7, 0x4f, 0x61, 0xd3, 0x04, 0x4d,
	0x7a, 0x2e, 0xbd, 0x9e, 0x8d, 0x91, 0xca, 0x4c, 0x8c, 0xd8, 0xff, 0x58, 0x85, 0xad, 0x99, 0x49,
	0x32, 0x66, 0x67, 0xd0, 0x29, 0x1c, 0x7c, 0xe4, 0x56, 0x9d, 0xd3, 0xc3, 0x42, 0xdc, 0xe6, 0xb4,
	0xc7, 0x85, 0x21, 0xe4, 0x67, 0x62, 0xf7, 0xdf, 0x2b, 0x00, 0x39, 0xea, 0xff, 0x7b, 0x14, 0xb7,
	0x67, 0xa2, 0xd8, 0xfe, 0xcb, 0x0a, 0x6c, 0x9f, 0x4b, 0xcf, 0xf0, 0x39, 0x0f, 0x78, 0x18, 0xf5,
	0xd1, 0xde, 0x07, 0x00, 0x46, 0x58, 0xc1, 0xdc, 0x06, 0x72, 0xe1, 0x63, 0x3c, 0xf6, 0xc4, 0x4d,
	0x94, 0x08, 0xa3, 0xa7, 0x19, 0x61, 0xde, 0xe4, 0x37, 0x4a, 0x24, 0x46, 0x41, 0x3d, 0x40, 0x68,
	0x18, 0x0c, 0x03, 0x5d, 0x9b, 0xd4, 0x1c, 0x3d, 0xc0, 0x93, 0x66, 0x28, 0xfb, 0x74, 0xd2, 0xac,
	0xe8, 0x93, 0x66, 0x28, 0xfb, 0x17, 0xbe, 0xb4, 0x7f, 0x97, 0x96, 0xa4, 0xd7, 0x52, 0xd8, 0xcc,
	0x1f, 0x40, 0xc7, 0x27, 0x98, 0x1b, 0x06, 0x52, 0x57, 0x4f, 0x9d, 0xd3, 0x6d, 0xb3, 0x99, 0x25,
	0x6a, 0xd0, 0x74, 0x98, 0x94, 0xed, 0x7f, 0xab, 0xd3, 0xd9, 0x50, 0xc4, 0x63, 0x80, 0x6b, 0xc9,
	0x46, 0xb1, 0x15, 0x12, 0x5c, 0xdc, 0xdb, 0x6a, 0x79, 0x6f, 0x0f, 0x61, 0xf5, 0x26, 0x89, 0x86,
	0xd9, 0xe6, 0x6a, 0xed, 0x00, 0x61, 0x66, 0x77, 0xcb, 0xf6, 0xaa, 0xcf, 0xda, 0x0b, 0xd1, 0xe4,
	0xfd, 0x54, 0xdb, 0x35, 0x0c, 0x5a, 0x43, 0xce, 0x14, 0xfa, 0x46, 0x20, 0x5d, 0xe1, 0x07, 0x4a,
	0xf8, 0x94, 0x69, 0x5b, 0x4e, 0x2b, 0x90, 0xcf, 0x68, 0xcc, 0xde, 0x85, 0x06, 0x1f, 0xab, 0x41,
	0x94, 0x50, 0x96, 0xed, 0x9c, 0xae, 0x19, 0x95, 0xcf, 0x08, 0xe8, 0x18, 0x24, 0xa5, 0x07, 0x53,
	0x02, 0xea, 0x5d, 0x4e, 0x87, 0xec, 0x08, 0x1a, 0x5c, 0x29, 0xee, 0x0d, 0x2c, 0x20, 0x9b, 0x6d,
	0x1a, 0x06, 0x2f, 0x65, 0xff, 0x8c, 0xe0, 0x8e, 0xc1, 0xb3, 0x77, 0x61, 0x45, 0x0c, 0x7b, 0x02,
	0x8b, 0x2d, 0x24, 0xdc, 0xc8, 0x09, 0x9f, 0x21, 0xd8, 0xd1, 0x58, 0xf6, 0x7d, 0x68, 0x49, 0x8a,
	0xd6, 0x44, 0x52, 0xf9, 0xd3, 0xd1, 0x47, 0xbc, 0xa6, 0xbc, 0xd2, 0x18, 0x27, 0x23, 0x61, 0x1f,
	0x42, 0x3b, 0x11, 0xdc, 0xa3, 0x8a, 0x9a, 0xaa, 0xb5, 0xce, 0x29, 0xcb, 0xe9, 0x1d, 0x83, 0x72,
	0x72, 0x22, 0x66, 0x43, 0x3d, 0x8e, 0xa8, 0x7c, 0x43, 0x85, 0xd7, 0x73, 0xe2, 0xcb, 0x28, 0x0c,
	0x1d, 0xc2, 0x61, 0x21, 0x9a, 0x88, 0x1b, 0x91, 0x88, 0x91, 0x27, 0x7c, 0xd7, 0xec, 0xe7, 0x3a,
	0x69, 0xbe, 0x91, 0x23, 0x5e, 0xd2, 0xce, 0x7e, 0x0c, 0xeb, 0x65, 0x5a, 0x4a, 0xf7, 0x4b, 0xbc,
	0x67, 0xad, 0x34, 0xbb, 0x98, 0x76, 0xb7, 0x4a, 0x69, 0xd7, 0xbe, 0x86, 0xb5, 0x92, 0xca, 0x6c,
	0x1d, 0xaa, 0x99, 0x4f, 0x55, 0x75, 0x35, 0x4b, 0xe1, 0x5c, 0x35, 0xc7, 0x82, 0x89, 0xf4, 0x9b,
	0x28, 0x19, 0x72, 0xa5, 0x2b, 0x24, 0xf4, 0xa4, 0x15, 0x07, 0x34, 0x08, 0x2b, 0x22, 0xfb, 0x2f,
	0x2a, 0x74, 0x67, 0x28, 0x58, 0x66, 0x99, 0xbf, 0x2e, 0x62, 0xbf, 0x07, 0xcd, 0xb2, 0x93, 0x36,
	0xc6, 0x0b, 0xd2, 0x4f, 0x7d, 0x26, 0xfd, 0x94, 0xdd, 0x73, 0x65, 0xc6, 0x3d, 0xed, 0xff, 0xac,
	0xc0, 0x6a, 0xd1, 0x5f, 0xe6, 0x14, 0xc5, 0x53, 0x35, 0x09, 0xcd, 0x42, 0xf0, 0x93, 0x6a, 0xcc,
	0x24, 0xfa, 0x62, 0xea, 0x22, 0xdc, 0x64, 0x3b, 0x02, 0xbc, 0x4a, 0x42, 0x3c, 0xf9, 0x6e, 0x82,
	0x50, 0x14, 0x97, 0x92, 0x8e, 0xa9, 0x6e, 0xd3, 0x7e, 0xab, 0x0d, 0xa4, 0x17, 0xd3, 0x31, 0x30,
	0xaa, 0x19, 0x77, 0x60, 0xe5, 0x36, 0xf0, 0xd5, 0x80, 0xe2, 0x68, 0xc5, 0xd1, 0x03, 0x4c, 0x49,
	0x03, 0x11, 0xf4, 0x07, 0x8a, 0x02, 0x68, 0xc5, 0x31, 0x23, 0xb4, 0x12, 0x15, 0xba, 0x2d, 0x82,
	0xd2, 0x37, 0xe6, 0x44, 0x11, 0x0f, 0xc4, 0x50, 0x24, 0x3c, 0xa4, 0x68, 0x69, 0x39, 0x39, 0xc0,
	0xfe, 0xaa, 0x0e, 0x9d, 0x82, 0xd7, 0x53, 0x31, 0x18, 0xa8, 0x50, 0xa4, 0xd6, 0xa7, 0x01, 0xd5,
	0xbe, 0xb8, 0x40, 0x63, 0x7d, 0xfc, 0x66, 0x87, 0xd0, 0xf1, 0x85, 0xf4, 0x92, 0x20, 0xc6, 0x7d,
	0x33, 0x7a, 0x17, 0x41, 0xa9, 0xa5, 0xea, 0xb9, 0xa5, 0x76, 0x60, 0xc5, 0x8b, 0xc2, 0x28, 0x21,
	0x4d, 0x6b, 0x8e, 0x1e, 0xb0, 0x1f, 0x00, 0x9a, 0x6b, 0x12, 0x60, 0x41, 0xdf, 0x20, 0x5f, 0xb5,
	0x66, 0x82, 0xf1, 0xf8, 0xd2, 0xe0, 0x9d, 0x8c, 0x92, 0xfd, 0x10, 0xda, 0x6a, 0x30, 0x1e, 0xf6,
	0x46, 0x78, 0xda, 0x35, 0x69, 0xda, 0xfe, 0xec, 0xb4, 0xeb, 0x94, 0xc0, 0xc9, 0x69, 0xd9, 0xfb,
	0xb0, 0x82, 0x1c, 0x22, 0x93, 0x62, 0x76, 0x67, 0x27, 0x7d, 0x86, 0x48, 0x47, 0xd3, 0x74, 0x3f,
	0x84, 0x56, 0x2a, 0x3b, 0xd5, 0xa7, 0x92, 0xeb, 0xb3, 0xc0, 0x2b, 0xbb, 0x03, 0x68, 0x67, 0x62,
	0x17, 0x4c, 0x29, 0x39, 0x4b, 0x75, 0xc6, 0x59, 0xb2, 0xdd, 0xae, 0x2d, 0xde, 0xed, 0x7a, 0x71,
	0xb7, 0xbb, 0xbf, 0x0d, 0x2b, 0xb4, 0xd6, 0x05, 0x52, 0x32, 0x46, 0xd5, 0xc5, 0x8c, 0x6a, 0x45,
	0x46, 0xf6, 0x7f, 0x55, 0x00, 0xf2, 0x9c, 0xc3, 0x7e, 0x0d, 0xd6, 0x5e, 0x8f, 0x85, 0xc4, 0x3d,
	0x74, 0x95, 0xf8, 0x22, 0x3d, 0xfd, 0x57, 0x53, 0xe0, 0xb5, 0xf8, 0x42, 0xb1, 0x63, 0x68, 0xf2,
	0x91, 0xbc, 0xc5, 0xb4, 0x58, 0xa5, 0x34, 0xb7, 0x53, 0xce, 0x5c, 0x67, 0x84, 0x74, 0x52, 0x22,
	0xf6, 0x3e, 0x6c, 0xf1, 0x30, 0x8c, 0x6e, 0xdd, 0xe1, 0x38, 0x54, 0x81, 0x14, 0x21, 0x1e, 0x3d,
	0x35, 0x72, 0xc7, 0x4d, 0x42, 0xbc, 0xcc, 0xe1, 0x18, 0x34, 0xfe, 0x38, 0xa1, 0xbe, 0x84, 0xd1,
	0x39, 0x1b, 0xa3, 0x12, 0xe2, 0x8b, 0x38, 0x48, 0xa6, 0xe4, 0x44, 0x75, 0xc7, 0x8c, 0x70, 0x41,
	0x89, 0x90, 0xe3, 0x50, 0x49, 0xe3, 0x44, 0x33, 0x0b, 0x72, 0x08, 0xe9, 0xa4, 0x44, 0xb6, 0xa4,
	0xca, 0xab, 0x84, 0xc4, 0x80, 0x0c, 0xa4, 0x7b, 0x13, 0x8c, 0x78, 0x18, 0x7c, 0x29, 0x74, 0xd4,
	0xb7, 0x9c, 0x4e, 0x20, 0x9f, 0xa7, 0x20, 0xf6, 0x13, 0x58, 0xd5, 0x2a, 0x99, 0x6a, 0x51, 0x2b,
	0xbf, 0xbf, 0x48, 0x79, 0xaa, 0x1a, 0x9d, 0x0e, 0xcf, 0x07, 0xf6, 0xef, 0xc3, 0xee, 0x42, 0xaa,
	0x42, 0x96, 0x59, 0xa3, 0x2c, 0x43, 0x91, 0xa2, 0xf9, 0xd3, 0xa5, 0x8c, 0x06, 0x58, 0xfa, 0x0c,
	0x85, 0x3b, 0x89, 0xf0, 0xe8, 0xd4, 0xb6, 0x6b, 0x0e, 0xc5, 0x67, 0x38, 0xb4, 0x2f, 0x8b, 0xea,
	0x68, 0xce, 0xe8, 0x6b, 0x66, 0xad, 0x19, 0xef, 0x96, 0x06, 0x5c, 0xf8, 0x98, 0x9c, 0x0d, 0x92,
	0xf6, 0x58, 0xbb, 0x22, 0x68, 0x10, 0xee, 0xb0, 0xfd, 0x55, 0x05, 0xda, 0xd9, 0xd1, 0x3b, 0x97,
	0x06, 0xbb, 0x40, 0x29, 0xb5, 0xe0, 0xfe, 0xd9, 0x18, 0xb7, 0x88, 0x4f, 0xb8, 0xe2, 0x69, 0x69,
	0x64, 0x46, 0x6f, 0xae, 0xfc, 0x36, 0xa1, 0xd6, 0x8b, 0x74, 0x52, 0x6e, 0x39, 0xf8, 0x69, 0xff,
	0x79, 0x05, 0x1e, 0xe4, 0x35, 0xdb, 0x4b, 0xd9, 0xd7, 0x85, 0xf5, 0x3d, 0xca, 0xb6, 0xfc, 0x66,
	0x53, 0x2d, 0xde, 0x6c, 0x0a, 0xa5, 0x43, 0xad, 0x5c, 0x3a, 0xe0, 0xe6, 0xf7, 0x47, 0x51, 0x22,
	0x4c, 0x0f, 0x44, 0x3b, 0x5e, 0x47, 0xc3, 0xa8, 0x0d, 0x62, 0xff, 0x14, 0xf6, 0x16, 0x2e, 0x46,
	0xc6, 0xcb, 0xce, 0xad, 0xf2, 0x22, 0xab, 0x33, 0x8b, 0xb4, 0xff, 0x04, 0x76, 0x4a, 0x0c, 0xb1,
	0x0c, 0xfa, 0xf6, 0xba, 0xe5, 0x6b, 0xa8, 0xcd, 0xd4, 0x7a, 0xe5, 0x86, 0x59, 0x3a, 0x2c, 0xeb,
	0xf3, 0x3c, 0x08, 0xc5, 0x77, 0xb4, 0xae, 0xfd, 0x53, 0xb0, 0x66, 0x2b, 0xec, 0xe7, 0x89, 0x90,
	0x83, 0x7b, 0x70, 0xcc, 0xca, 0xe9, 0x6a, 0xa1, 0x9c, 0xb6, 0xff, 0xac, 0x5a, 0x28, 0x90, 0x89,
	0xd5, 0xb3, 0x89, 0x6e, 0xf3, 0x81, 0x98, 0x64, 0x07, 0x67, 0x85, 0xda, 0x29, 0x69, 0xb1, 0xf8,
	0x6c, 0x72, 0x1d, 0x9f, 0xdf, 0x38, 0x6d, 0x22, 0xa0, 0x53, 0xf4, 0x14, 0x00, 0x0d, 0x63, 0xae,
	0x46, 0xd5, 0xe5, 0xf5, 0x50, 0x7b, 0x28, 0xd3, 0xc2, 0x19, 0xef, 0x38, 0x22, 0x74, 0xd3, 0xb2,
	0xbd, 0xa6, 0x3b, 0x6f, 0x3e, 0x6d, 0xd3, 0x85, 0x2f, 0xd9, 0x47, 0xb0, 0xca, 0x7d, 0xdf, 0x4d,
	0x0b, 0x39, 0xd3, 0x28, 0x59, 0x54, 0xec, 0x75, 0xb8, 0xef, 0x67, 0xf5, 0xcd, 0x47, 0xb0, 0x8a,
	0x6c, 0xb3, 0x69, 0x2b, 0xcb, 0xa7, 0xf9, 0x22, 0x4c, 0x07, 0x18, 0x07, 0xfb, 0x4b, 0x2c, 0xfb,
	0x6d, 0xaf, 0x0b, 0xec, 0x47, 0xd0, 0xb9, 0x41, 0x16, 0x2e, 0x19, 0xca, 0x64, 0xb2, 0xbd, 0xd2,
	0xac, 0xdc, 0xe2, 0x78, 0x03, 0x48, 0xbf, 0xed, 0x5f, 0x36, 0xa8, 0x6a, 0xb8, 0x8c, 0xa2, 0xf0,
	0x62, 0x74, 0x13, 0xfd, 0xca, 0xf5, 0x7b, 0x72, 0xef, 0x6e, 0x15, 0xe3, 0x6b, 0xbe, 0x87, 0x7b,
	0x00, 0xc0, 0x3d, 0xca, 0xcd, 0x6e, 0xd6, 0xc9, 0x6d, 0x1b, 0xc8, 0xc5, 0x7d, 0xda, 0xb9, 0x95,
	0x3b, 0xda, 0xb9, 0x95, 0x7b, 0xb5, 0x73, 0x2b, 0x8b, 0xdb, 0xb9, 0x79, 0x17, 0x69, 0x63, 0x79,
	0x17, 0x69, 0x73, 0xbe, 0x8b, 0xf4, 0xdd, 0x7a, 0x3b, 0xdb, 0xf7, 0xe8, 0xed, 0x2c, 0xe8, 0xe0,
	0xec, 0xde, 0xa7, 0x83, 0xf3, 0xe0, 0xde, 0x1d, 0x9c, 0xbd, 0x6f, 0xd1, 0xc1, 0xb1, 0xee, 0xdb,
	0xc1, 0xd9, 0x9f, 0xe9, 0xe0, 0xbc, 0x0f, 0x1b, 0xe7, 0xfa, 0xc9, 0x04, 0xcf, 0x08, 0x79, 0xe7,
	0xbb, 0x87, 0xfd, 0x77, 0x15, 0xd8, 0x2c, 0x53, 0xcb, 0x98, 0x7d, 0x00, 0xd9, 0xa3, 0x8b, 0x79,
	0xdd, 0x40, 0x25, 0x2a, 0xa4, 0xc4, 0xa6, 0xc1, 0xbc, 0x92, 0x69, 0xa5, 0x70, 0x0c, 0xdb, 0x68,
	0x02, 0x63, 0x3e, 0xe3, 0x6b, 0x92, 0x22, 0xab, 0xe6, 0x6c, 0x21, 0x8a, 0x4c, 0x78, 0x66, 0x10,
	0xec, 0x87, 0x60, 0x0d, 0x83, 0x91, 0x70, 0x17, 0x4d, 0xaa, 0xd1, 0xa4, 0x5d, 0xc4, 0xff, 0x6c,
	0x76, 0xa2, 0xfd, 0x9c, 0x32, 0xef, 0x25, 0xd6, 0xa6, 0x8e, 0x90, 0xd1, 0x38, 0xf1, 0xe8, 0x60,
	0x98, 0x2f, 0x36, 0x0f, 0x00, 0xa2, 0x51, 0x38, 0x75, 0xa9, 0x8c, 0xa5, 0x85, 0xb4, 0x9c, 0x36,
	0x42, 0x68, 0xae, 0x1d, 0xc0, 0x81, 0x51, 0xf9, 0x92, 0x9c, 0xe6, 0x62, 0xa4, 0x44, 0xc2, 0x3d,
	0xf5, 0x66, 0x73, 0xa1, 0xfb, 0xf8, 0x78, 0x14, 0xeb, 0x07, 0x22, 0xfa, 0x46, 0x77, 0x8b, 0x62,
	0x91, 0x70, 0x15, 0x25, 0x26, 0x29, 0x67, 0x63, 0x5b, 0xc1, 0x03, 0x23, 0xea, 0xa5, 0x90, 0x92,
	0xf7, 0xf5, 0xb6, 0xff, 0xaf, 0xca, 0x48, 0x93, 0x40, 0x9d, 0xde, 0x51, 0xf0, 0xd3, 0xfe, 0xef,
	0x0a, 0xec, 0x2d, 0x14, 0x2b, 0x63, 0xf6, 0x89, 0x69, 0xa3, 0xeb, 0x94, 0x7c, 0x4c, 0xc9, 0x75,
	0x31, 0xe9, 0x71, 0x11, 0x60, 0xb2, 0xb5, 0xee, 0xac, 0xff, 0x4d, 0x05, 0xd8, 0x3c, 0x12, 0xb3,
	0x5b, 0x12, 0xdd, 0xea, 0xd4, 0x68, 0xb2, 0x6e, 0x12, 0xdd, 0x52, 0x5e, 0xfc, 0x10, 0x76, 0x4c,
	0xcc, 0x0e, 0xf5, 0x3c, 0x37, 0x2f, 0x26, 0x6b, 0x0e, 0xd3, 0xb8, 0x22, 0x4b, 0x76, 0x0a, 0xbb,
	0xe6, 0x4d, 0x66, 0x66, 0x8a, 0x76, 0x91, 0x6d, 0x83, 0x2c, 0xce, 0xb1, 0xcf, 0x60, 0xbb, 0xb4,
	0xb1, 0xaf, 0x02, 0xff, 0x6e, 0x53, 0x1b, 0xd3, 0xe9, 0x55, 0x90, 0xe9, 0x3c, 0x60, 0xfa, 0xd4,
	0xfb, 0x84, 0x2b, 0x6f, 0x70, 0x25, 0x46, 0xc4, 0x61, 0x1f, 0x5a, 0xe3, 0xc0, 0xcf, 0xcf, 0xb2,
	0x9a, 0xd3, 0x1c, 0x07, 0x3e, 0x9d, 0x59, 0x05, 0xe6, 0xb5, 0xf2, 0x49, 0xb3, 0xbc, 0xca, 0x79,
	0x9d, 0xf5, 0x58, 0xd3, 0xe3, 0xf3, 0x06, 0x33, 0x3c, 0x8f, 0xe3, 0x42, 0xb9, 0xc6, 0xe3, 0xf8,
	0xc2, 0x7f, 0xe3, 0x71, 0xf5, 0x1e, 0x6c, 0xc4, 0x49, 0x30, 0x71, 0x0b, 0x85, 0x8c, 0x5e, 0xc6,
	0x1a, 0x82, 0x9f, 0x66, 0x75, 0xdd, 0x7f, 0xe8, 0x56, 0x63, 0x59, 0x31, 0x19, 0xb3, 0xdf, 0x2c,
	0xb9, 0xc3, 0xaf, 0xe7, 0xa7, 0x7e, 0x89, 0x2c, 0x75, 0x05, 0x1c, 0x17, 0x3d, 0x01, 0x75, 0x94,
	0x63, 0x0f, 0x0f, 0x29, 0x73, 0xb1, 0x4b, 0x87, 0xb8, 0xb0, 0x1b, 0x1e, 0x84, 0xc2, 0x77, 0x33,
	0xcb, 0xd5, 0xc8, 0x72, 0x6b, 0x1a, 0xfc, 0x4a, 0xdb, 0xaf, 0x7b, 0x01, 0x5b, 0x73, 0xcc, 0xbf,
	0x65, 0xed, 0x2a, 0x48, 0x45, 0xac, 0x1a, 0xbf, 0xfb, 0xe6, 0xed, 0xeb, 0x2e, 0x89, 0x9b, 0xf7,
	0x0b, 0x9a, 0x38, 0x7e, 0x95, 0x84, 0xf6, 0x2f, 0xc8, 0x45, 0xce, 0xa4, 0xf7, 0x7f, 0x29, 0xa5,
	0xe8, 0x3d, 0x2b, 0x65, 0xef, 0xf9, 0x1d, 0x52, 0x93, 0x1e, 0xba, 0xe8, 0xcd, 0xf8, 0xcc, 0xf7,
	0xef, 0x51, 0xcd, 0xa6, 0x27, 0x69, 0x35, 0x3f, 0x49, 0xc9, 0x0f, 0x75, 0xbc, 0x5c, 0xf3, 0x9e,
	0x61, 0xb3, 0x0f, 0x2d, 0xc5, 0x7b, 0xa5, 0x20, 0x56, 0xbc, 0xf7, 0xa9, 0xb9, 0x45, 0xc5, 0xe3,
	0x5e, 0x18, 0x78, 0x66, 0xaf, 0xcd, 0x88, 0x3d, 0x81, 0x86, 0x29, 0x64, 0x6b, 0x54, 0xc8, 0xb2,
	0xb9, 0xb7, 0xb9, 0xd7, 0x8e, 0xa1, 0xb0, 0x3f, 0x83, 0xad, 0x5c, 0x64, 0x7a, 0xb9, 0xc8, 0xaf,
	0x72, 0x35, 0xba, 0xca, 0x15, 0xd7, 0x50, 0x5d, 0xb6, 0x86, 0x5a, 0x71, 0x0d, 0xb6, 0x5d, 0x54,
	0xe5, 0x5c, 0x84, 0x0b, 0xd8, 0xda, 0xff, 0x54, 0x05, 0x96, 0x13, 0x65, 0x8f, 0x95, 0x3f, 0x2e,
	0x85, 0xc0, 0xf7, 0x0a, 0x19, 0xb1, 0x40, 0x75, 0x5c, 0xe4, 0x5e, 0x48, 0x85, 0x3f, 0x2f, 0xcb,
	0x25, 0xef, 0xfd, 0x10, 0x6a, 0x8a, 0xf7, 0x0c, 0xbf, 0xb7, 0x96, 0xf0, 0xbb, 0xe6, 0x3d, 0xac,
	0x54, 0x1d, 0x24, 0x5d, 0xde, 0xfc, 0xee, 0xfe, 0x75, 0x05, 0x9a, 0x86, 0xf4, 0x9b, 0x98, 0xe9,
	0x1b, 0x6c, 0x49, 0xc1, 0xa4, 0xf5, 0xd2, 0xb6, 0x16, 0xcf, 0x1c, 0xed, 0x82, 0xf9, 0xb9, 0xf6,
	0x87, 0x59, 0xa6, 0xcd, 0xca, 0xa3, 0x6f, 0x7e, 0xa8, 0xed, 0x41, 0x53, 0xf1, 0xfc, 0x32, 0x53,
	0x77, 0x1a, 0x8a, 0xd3, 0x1b, 0xc4, 0xdf, 0xd6, 0x60, 0x67, 0x9e, 0xbd, 0x8c, 0xd9, 0xcf, 0x60,
	0x53, 0x17, 0x0c, 0xba, 0xc2, 0x2d, 0x6c, 0xdb, 0xf7, 0x0b, 0x66, 0x2e, 0xcd, 0x39, 0xce, 0x46,
	0xc5, 0x73, 0x6c, 0x9d, 0xd8, 0x68, 0x08, 0xe6, 0xb1, 0x6b, 0x58, 0x37, 0x95, 0xc8, 0xa4, 0xaf,
	0xd9, 0x56, 0xe7, 0xce, 0xc7, 0x25, 0x6c, 0xcf, 0x26, 0xfd, 0x73, 0x3e, 0x35, 0x7c, 0x57, 0x89,
	0x0b, 0x81, 0x14, 0xef, 0xfe, 0xb2, 0x02, 0x3b, 0x8b, 0xc4, 0xdf, 0x75, 0x52, 0x7e, 0x00, 0xac,
	0xa4, 0x62, 0x7e, 0x43, 0xac, 0x39, 0x9b, 0x85, 0x55, 0x6b, 0x46, 0x47, 0x65, 0x83, 0x24, 0x68,
	0xe2, 0x1a, 0x3d, 0xf3, 0x16, 0x34, 0x74, 0xb8, 0x12, 0xdd, 0xdf, 0x83, 0xdd, 0x85, 0x4b, 0xce,
	0x76, 0x46, 0xaf, 0x43, 0xef, 0xcc, 0x3b, 0xb0, 0x8e, 0x86, 0x28, 0x14, 0xc4, 0x55, 0x62, 0xba,
	0xca, 0x27, 0xfd, 0x8c, 0x8b, 0xfd, 0xf7, 0x15, 0x78, 0x54, 0xb4, 0xcd, 0x15, 0x15, 0xf4, 0x8e,
	0xf0, 0x54, 0x70, 0x33, 0xbd, 0x47, 0x4a, 0x7a, 0xd3, 0xd9, 0x76, 0x04, 0x9b, 0x51, 0xe8, 0xa7,
	0x7f, 0xd0, 0x48, 0xff, 0x7e, 0x83, 0x17, 0x84, 0xf5, 0x28, 0x2c, 0x8a, 0x44, 0xca, 0x91, 0xb8,
	0x2d, 0x53, 0xd6, 0x35, 0xe5, 0x48, 0xdc, 0x16, 0x28, 0xed, 0x3f, 0xad, 0x66, 0x9e, 0x7b, 0xcd,
	0x7b, 0x79, 0x59, 0xf4, 0x5b, 0x59, 0xc0, 0x2c, 0x4c, 0x03, 0x79, 0x51, 0x34, 0x0b, 0x33, 0xd3,
	0xba, 0x22, 0xab, 0xba, 0x53, 0x14, 0xfb, 0x51, 0x31, 0x0f, 0xbc, 0xb7, 0x8c, 0x61, 0x3a, 0x30,
	0x1e, 0xf4, 0x86, 0x7c, 0x70, 0x06, 0xeb, 0xe5, 0x09, 0x77, 0x65, 0x81, 0xac, 0x67, 0xa7, 0x1b,
	0x93, 0x7a, 0x60, 0xff, 0x24, 0x2b, 0x0e, 0xaf, 0x79, 0xef, 0x55, 0x8c, 0x1b, 0x4e, 0x7f, 0xf6,
	0x10, 0xaf, 0xf1, 0x86, 0x43, 0x7f, 0x08, 0x91, 0x42, 0xe1, 0x95, 0xc6, 0x6c, 0x59, 0x07, 0x61,
	0x57, 0x1a, 0x74, 0xfa, 0x2f, 0xab, 0xd0, 0xc0, 0xe3, 0x2f, 0x0e, 0xd8, 0x2b, 0xba, 0x73, 0xa7,
	0x29, 0x85, 0x2d, 0xc8, 0x31, 0xdd, 0xed, 0x05, 0x7f, 0xd3, 0xb0, 0x0f, 0xbe, 0xfa, 0xd7, 0xaf,
	0xff, 0xaa, 0xba, 0x67, 0x33, 0xfa, 0xc7, 0x97, 0x2f, 0xbd, 0x13, 0x74, 0x81, 0x93, 0x38, 0x8a,
	0xc2, 0x8f, 0x2b, 0x4f, 0xd8, 0x1f, 0xd1, 0xcb, 0x4e, 0xe1, 0xd9, 0x77, 0x67, 0xc1, 0x9b, 0xf1,
	0xeb, 0xee, 0xee, 0xc2, 0x97, 0x64, 0xfb, 0x31, 0x31, 0xdf, 0xb7, 0x77, 0xca, 0xcc, 0xf5, 0x2e,
	0x21, 0xfb, 0x01, 0x15, 0x5f, 0xa5, 0xbe, 0x05, 0x4b, 0x7b, 0x0c, 0xb3, 0x2f, 0xb1, 0xdd, 0xbd,
	0x45, 0x2d, 0x0b, 0x14, 0x63, 0x93, 0x98, 0x47, 0xf6, 0x5e, 0x26, 0xc6, 0xf8, 0xf5, 0x89, 0xee,
	0x67, 0xa0, 0xa4, 0x5f, 0x50, 0x6f, 0x75, 0xbe, 0x43, 0xc2, 0x1e, 0x2d, 0x12, 0x97, 0xb6, 0xa5,
	0xba, 0x07, 0x77, 0x60, 0x65, 0x6c, 0x1f, 0x91, 0x64, 0xdb, 0x3e, 0x58, 0x22, 0xd9, 0xa5, 0xb6,
	0x08, 0xca, 0x9f, 0xd0, 0xe3, 0x2b, 0x79, 0xbf, 0x29, 0xb1, 0x58, 0xb7, 0xcc, 0xbb, 0xd8, 0xbe,
	0xec, 0x3e, 0x5c, 0x8a, 0x93, 0xb1, 0xfd, 0x84, 0xa4, 0xbe, 0x63, 0x3f, 0x9e, 0x93, 0x9a, 0x55,
	0xe7, 0x44, 0x8c, 0x72, 0x5f, 0xd3, 0x1b, 0x1a, 0x1e, 0xee, 0xa9, 0x58, 0x6b, 0x8e, 0xb5, 0x39,
	0xfa, 0xef, 0x16, 0xba, 0x5c, 0xd5, 0x54, 0xa8, 0xf0, 0x03, 0x85, 0x22, 0x6f, 0xc8, 0x15, 0xb1,
	0x16, 0xc3, 0xf2, 0x8f, 0xcd, 0x70, 0x2d, 0x35, 0x12, 0xbb, 0x8b, 0xda, 0x50, 0xf6, 0xbb, 0x24,
	0xea, 0xb1, 0xdd, 0x9d, 0x13, 0x25, 0xc5, 0xc8, 0x77, 0xb1, 0x2a, 0x43, 0x39, 0x2e, 0x99, 0xf4,
	0x32, 0x4a, 0x54, 0xc2, 0x03, 0x45, 0xa7, 0x32, 0xb1, 0x2b, 0x42, 0x50, 0xc6, 0xce, 0x3c, 0x50,
	0xc6, 0xf6, 0xdb, 0x24, 0xe4, 0xa1, 0xfd, 0x20, 0x13, 0x12, 0x1b, 0x92, 0x93, 0x60, 0x74, 0x13,
	0xa1, 0x80, 0xeb, 0x92, 0x00, 0xb4, 0x52, 0x59, 0x40, 0x6a, 0xb7, 0x36, 0x02, 0x9f, 0x0d, 0x63,
	0x35, 0xbd, 0x8b, 0x6b, 0x6a, 0x9e, 0x4b, 0x32, 0x4f, 0x96, 0x98, 0xf2, 0xc9, 0xc6, 0xc1, 0xe7,
	0xd3, 0xd2, 0x82, 0x20, 0x55, 0xbc, 0x77, 0x42, 0x19, 0x44, 0x07, 0x29, 0xae, 0xf3, 0x53, 0x71,
	0xfb, 0x34, 0x1a, 0x0e, 0xc7, 0x57, 0x7c, 0x22, 0xd8, 0xe3, 0x02, 0xab, 0x14, 0xe1, 0x08, 0x1c,
	0xe9, 0xd2, 0xb2, 0xb8, 0xe6, 0xb7, 0x88, 0xbb, 0x65, 0x6f, 0x13, 0xf7, 0x91, 0xb8, 0x3d, 0xf1,
	0x90, 0xfc, 0x44, 0xf2, 0x09, 0xd9, 0xf9, 0xe7, 0x25, 0xf6, 0x64, 0x86, 0xc3, 0xa5, 0xec, 0x17,
	0xd8, 0x64, 0x19, 0xff, 0xd4, 0x20, 0x93, 0x12, 0x7f, 0x4a, 0x5f, 0xcb, 0xf9, 0xa7, 0xc9, 0xec,
	0xed, 0x37, 0x50, 0xc8, 0x78, 0xa9, 0x5c, 0xbc, 0x16, 0xa0, 0xdc, 0x17, 0xf4, 0xae, 0x65, 0x4a,
	0x5f, 0xb6, 0x5b, 0x36, 0xfe, 0x02, 0x3d, 0x1e, 0x11, 0xbf, 0x07, 0xf6, 0x56, 0x69, 0x17, 0x52,
	0x2d, 0x38, 0xbd, 0x0c, 0x5f, 0x52, 0xb9, 0x76, 0xcd, 0xd3, 0x34, 0x66, 0x6e, 0xc0, 0x29, 0x14,
	0x39, 0x5a, 0x8b, 0x11, 0x73, 0x0b, 0xd6, 0x02, 0xfa, 0x27, 0xba, 0x02, 0xd4, 0x0e, 0xbf, 0x45,
	0x0b, 0xd6, 0xf7, 0xc1, 0x73, 0x11, 0x0a, 0x25, 0xd8, 0x7e, 0x69, 0xdd, 0x45, 0x54, 0x71, 0xed,
	0xef, 0x10, 0xeb, 0xb7, 0xec, 0xfd, 0x12, 0xeb, 0x1e, 0x12, 0xbb, 0x3e, 0x51, 0x7f, 0x5c, 0x79,
	0xf2, 0x49, 0xe3, 0x0f, 0xea, 0xc7, 0x3f, 0x8e, 0x7b, 0xbd, 0x06, 0xfd, 0x69, 0xf7, 0x37, 0xfe,
	0x27, 0x00, 0x00, 0xff, 0xff, 0x44, 0xfb, 0xcf, 0xa9, 0x20, 0x2c, 0x00, 0x00,
}
