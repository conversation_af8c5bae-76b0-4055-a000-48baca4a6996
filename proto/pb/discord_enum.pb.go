// Code generated by protoc-gen-go. DO NOT EDIT.
// source: discord_enum.proto

package pb

import (
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

type DscEvTpDf int32

const (
	// 未知事件
	DscEvTpDf_DscEvTpDfUnknown DscEvTpDf = 0
	// 消息 - 新增
	DscEvTpDf_DscEvTpDfMsgAdd DscEvTpDf = 1
	// 消息 - 编辑
	DscEvTpDf_DscEvTpDfMsgEdit DscEvTpDf = 2
	// 消息 - 删除
	DscEvTpDf_DscEvTpDfMsgDel DscEvTpDf = 3
	// 反应 - 新增
	DscEvTpDf_DscEvTpDfReactionAdd DscEvTpDf = 4
	// 反应 - 删除
	DscEvTpDf_DscEvTpDfReactionDel DscEvTpDf = 5
)

var DscEvTpDf_name = map[int32]string{
	0: "DscEvTpDfUnknown",
	1: "DscEvTpDfMsgAdd",
	2: "DscEvTpDfMsgEdit",
	3: "DscEvTpDfMsgDel",
	4: "DscEvTpDfReactionAdd",
	5: "DscEvTpDfReactionDel",
}

var DscEvTpDf_value = map[string]int32{
	"DscEvTpDfUnknown":     0,
	"DscEvTpDfMsgAdd":      1,
	"DscEvTpDfMsgEdit":     2,
	"DscEvTpDfMsgDel":      3,
	"DscEvTpDfReactionAdd": 4,
	"DscEvTpDfReactionDel": 5,
}

func (x DscEvTpDf) String() string {
	return proto.EnumName(DscEvTpDf_name, int32(x))
}

func (DscEvTpDf) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_e60ee9bd64265060, []int{0}
}

type DscMsgTpDf int32

const (
	// 未知消息
	DscMsgTpDf_DscMsgTpDfUnknown DscMsgTpDf = 0
	// 文本消息
	DscMsgTpDf_DscMsgTpDfText DscMsgTpDf = 1
	// 附件消息
	DscMsgTpDf_DscMsgTpDfAttach DscMsgTpDf = 2
	// Embed消息
	DscMsgTpDf_DscMsgTpDfEmbed DscMsgTpDf = 3
)

var DscMsgTpDf_name = map[int32]string{
	0: "DscMsgTpDfUnknown",
	1: "DscMsgTpDfText",
	2: "DscMsgTpDfAttach",
	3: "DscMsgTpDfEmbed",
}

var DscMsgTpDf_value = map[string]int32{
	"DscMsgTpDfUnknown": 0,
	"DscMsgTpDfText":    1,
	"DscMsgTpDfAttach":  2,
	"DscMsgTpDfEmbed":   3,
}

func (x DscMsgTpDf) String() string {
	return proto.EnumName(DscMsgTpDf_name, int32(x))
}

func (DscMsgTpDf) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_e60ee9bd64265060, []int{1}
}

type DscMsgEmbedTpDf int32

const (
	// 未知Embed消息
	DscMsgEmbedTpDf_DscMsgEmbedTpDfUnknown DscMsgEmbedTpDf = 0
	// 富文本 - 	generic embed rendered from embed attributes
	DscMsgEmbedTpDf_Rich DscMsgEmbedTpDf = 1
	// 图片 - image
	DscMsgEmbedTpDf_Image DscMsgEmbedTpDf = 2
	// 音频 - video embed
	DscMsgEmbedTpDf_video DscMsgEmbedTpDf = 3
	// 动图 - animated gif image embed rendered as a video embed
	DscMsgEmbedTpDf_Gifv DscMsgEmbedTpDf = 4
	//article - article embed
	DscMsgEmbedTpDf_Article DscMsgEmbedTpDf = 5
	//link - link embed
	DscMsgEmbedTpDf_Link DscMsgEmbedTpDf = 6
)

var DscMsgEmbedTpDf_name = map[int32]string{
	0: "DscMsgEmbedTpDfUnknown",
	1: "Rich",
	2: "Image",
	3: "video",
	4: "Gifv",
	5: "Article",
	6: "Link",
}

var DscMsgEmbedTpDf_value = map[string]int32{
	"DscMsgEmbedTpDfUnknown": 0,
	"Rich":                   1,
	"Image":                  2,
	"video":                  3,
	"Gifv":                   4,
	"Article":                5,
	"Link":                   6,
}

func (x DscMsgEmbedTpDf) String() string {
	return proto.EnumName(DscMsgEmbedTpDf_name, int32(x))
}

func (DscMsgEmbedTpDf) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_e60ee9bd64265060, []int{2}
}

type DscReplyTpDf int32

const (
	// 未知状态
	DscReplyTpDf_DscReplyTpDfUnknown DscReplyTpDf = 0
	// 未回复
	DscReplyTpDf_DscReplyTpDfUnReply DscReplyTpDf = 1
	// 已回复
	DscReplyTpDf_DscReplyTpDfReplied DscReplyTpDf = 2
)

var DscReplyTpDf_name = map[int32]string{
	0: "DscReplyTpDfUnknown",
	1: "DscReplyTpDfUnReply",
	2: "DscReplyTpDfReplied",
}

var DscReplyTpDf_value = map[string]int32{
	"DscReplyTpDfUnknown": 0,
	"DscReplyTpDfUnReply": 1,
	"DscReplyTpDfReplied": 2,
}

func (x DscReplyTpDf) String() string {
	return proto.EnumName(DscReplyTpDf_name, int32(x))
}

func (DscReplyTpDf) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_e60ee9bd64265060, []int{3}
}

type DscMsgFromTpDf int32

const (
	// 未知来源
	DscMsgFromTpDf_DscMsgFromTpDfUnknown DscMsgFromTpDf = 0
	// 用户
	DscMsgFromTpDf_DscMsgFromTpDfUser DscMsgFromTpDf = 1
	// 机器人
	DscMsgFromTpDf_DscMsgFromTpDfRobot DscMsgFromTpDf = 2
)

var DscMsgFromTpDf_name = map[int32]string{
	0: "DscMsgFromTpDfUnknown",
	1: "DscMsgFromTpDfUser",
	2: "DscMsgFromTpDfRobot",
}

var DscMsgFromTpDf_value = map[string]int32{
	"DscMsgFromTpDfUnknown": 0,
	"DscMsgFromTpDfUser":    1,
	"DscMsgFromTpDfRobot":   2,
}

func (x DscMsgFromTpDf) String() string {
	return proto.EnumName(DscMsgFromTpDf_name, int32(x))
}

func (DscMsgFromTpDf) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_e60ee9bd64265060, []int{4}
}

type DscMsgTaskStatus int32

const (
	//未开始
	DscMsgTaskStatus_ProcessStatusInit DscMsgTaskStatus = 0
	//处理中
	DscMsgTaskStatus_ProcessStatusDoing DscMsgTaskStatus = 10
	//处理失败
	DscMsgTaskStatus_ProcessStatusFail DscMsgTaskStatus = 20
	//处理成功
	DscMsgTaskStatus_ProcessStatusSuccess DscMsgTaskStatus = 30
)

var DscMsgTaskStatus_name = map[int32]string{
	0:  "ProcessStatusInit",
	10: "ProcessStatusDoing",
	20: "ProcessStatusFail",
	30: "ProcessStatusSuccess",
}

var DscMsgTaskStatus_value = map[string]int32{
	"ProcessStatusInit":    0,
	"ProcessStatusDoing":   10,
	"ProcessStatusFail":    20,
	"ProcessStatusSuccess": 30,
}

func (x DscMsgTaskStatus) String() string {
	return proto.EnumName(DscMsgTaskStatus_name, int32(x))
}

func (DscMsgTaskStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_e60ee9bd64265060, []int{5}
}

type DscMsgTaskDetailStatus int32

const (
	// 未知消息
	DscMsgTaskDetailStatus_DscTaskDetailStatusUnknown DscMsgTaskDetailStatus = 0
	//未发送
	DscMsgTaskDetailStatus_DscTaskDetailStatusDoing DscMsgTaskDetailStatus = 10
	//失败
	DscMsgTaskDetailStatus_DscTaskDetailStatusFail DscMsgTaskDetailStatus = 20
	//成功
	DscMsgTaskDetailStatus_DscTaskDetailStatusSuccess DscMsgTaskDetailStatus = 30
)

var DscMsgTaskDetailStatus_name = map[int32]string{
	0:  "DscTaskDetailStatusUnknown",
	10: "DscTaskDetailStatusDoing",
	20: "DscTaskDetailStatusFail",
	30: "DscTaskDetailStatusSuccess",
}

var DscMsgTaskDetailStatus_value = map[string]int32{
	"DscTaskDetailStatusUnknown": 0,
	"DscTaskDetailStatusDoing":   10,
	"DscTaskDetailStatusFail":    20,
	"DscTaskDetailStatusSuccess": 30,
}

func (x DscMsgTaskDetailStatus) String() string {
	return proto.EnumName(DscMsgTaskDetailStatus_name, int32(x))
}

func (DscMsgTaskDetailStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_e60ee9bd64265060, []int{6}
}

func init() {
	proto.RegisterEnum("pb.DscEvTpDf", DscEvTpDf_name, DscEvTpDf_value)
	proto.RegisterEnum("pb.DscMsgTpDf", DscMsgTpDf_name, DscMsgTpDf_value)
	proto.RegisterEnum("pb.DscMsgEmbedTpDf", DscMsgEmbedTpDf_name, DscMsgEmbedTpDf_value)
	proto.RegisterEnum("pb.DscReplyTpDf", DscReplyTpDf_name, DscReplyTpDf_value)
	proto.RegisterEnum("pb.DscMsgFromTpDf", DscMsgFromTpDf_name, DscMsgFromTpDf_value)
	proto.RegisterEnum("pb.DscMsgTaskStatus", DscMsgTaskStatus_name, DscMsgTaskStatus_value)
	proto.RegisterEnum("pb.DscMsgTaskDetailStatus", DscMsgTaskDetailStatus_name, DscMsgTaskDetailStatus_value)
}

func init() {
	proto.RegisterFile("discord_enum.proto", fileDescriptor_e60ee9bd64265060)
}

var fileDescriptor_e60ee9bd64265060 = []byte{
	// 409 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x6c, 0x93, 0xcd, 0x6e, 0xd4, 0x30,
	0x14, 0x85, 0x49, 0x26, 0x33, 0xd0, 0x0b, 0x82, 0x8b, 0x3b, 0xfd, 0xa1, 0xa0, 0x3e, 0x40, 0x16,
	0xdd, 0xb0, 0x64, 0x35, 0xc8, 0x53, 0x54, 0x89, 0x91, 0x50, 0x5a, 0x16, 0x20, 0x24, 0x94, 0xd8,
	0x6e, 0x6a, 0x4d, 0x62, 0x47, 0xb1, 0x67, 0x80, 0xe7, 0x40, 0xbc, 0x2f, 0x72, 0x9a, 0x38, 0x4d,
	0x9a, 0x9d, 0xef, 0x77, 0x9c, 0x73, 0x4e, 0xec, 0x04, 0x08, 0x97, 0x86, 0xe9, 0x9a, 0xff, 0x14,
	0x6a, 0x57, 0x5e, 0x54, 0xb5, 0xb6, 0x9a, 0x84, 0x55, 0x16, 0xff, 0x0b, 0xe0, 0x80, 0x1a, 0xb6,
	0xde, 0xdf, 0x54, 0xf4, 0x96, 0x2c, 0x01, 0xfd, 0xf0, 0x55, 0x6d, 0x95, 0xfe, 0xa5, 0xf0, 0x09,
	0x39, 0x84, 0x57, 0x9e, 0x6e, 0x4c, 0xbe, 0xe2, 0x1c, 0x83, 0xc1, 0xd6, 0x8d, 0xc9, 0xd7, 0x5c,
	0x5a, 0x0c, 0xc7, 0x5b, 0xa9, 0x28, 0x70, 0x46, 0x4e, 0x61, 0xe9, 0x61, 0x22, 0x52, 0x66, 0xa5,
	0x56, 0xce, 0x24, 0x9a, 0x54, 0xdc, 0x33, 0xf3, 0x38, 0x03, 0xa0, 0x86, 0x6d, 0x4c, 0xde, 0xf4,
	0x3a, 0x82, 0xd7, 0xfd, 0xd4, 0x17, 0x23, 0xf0, 0xb2, 0xc7, 0x37, 0xe2, 0xb7, 0xf5, 0xbd, 0x5a,
	0xb6, 0xb2, 0x36, 0x65, 0x77, 0xbe, 0x57, 0x4b, 0xd7, 0x65, 0x26, 0x38, 0xce, 0x62, 0xd5, 0xc1,
	0x06, 0x34, 0x41, 0x67, 0x70, 0x3c, 0x42, 0x7d, 0xda, 0x33, 0x88, 0x12, 0xc9, 0xee, 0x30, 0x20,
	0x07, 0x30, 0xbf, 0x2a, 0xd3, 0x5c, 0x60, 0xe8, 0x96, 0x7b, 0xc9, 0x85, 0xc6, 0x99, 0xd3, 0x3f,
	0xc9, 0xdb, 0x3d, 0x46, 0xe4, 0x39, 0x3c, 0x5d, 0xd5, 0x56, 0xb2, 0x42, 0xe0, 0xdc, 0xe1, 0xcf,
	0x52, 0x6d, 0x71, 0x11, 0x7f, 0x83, 0x17, 0xd4, 0xb0, 0x44, 0x54, 0xc5, 0x9f, 0x26, 0xec, 0x04,
	0x0e, 0x1f, 0xce, 0x7d, 0xd2, 0x23, 0xa1, 0x59, 0x62, 0x30, 0x16, 0xdc, 0x42, 0x0a, 0x8e, 0x61,
	0xfc, 0xa3, 0x3b, 0x89, 0xcb, 0x5a, 0x97, 0x8d, 0xf9, 0x1b, 0x38, 0x1a, 0x92, 0xde, 0xfe, 0x18,
	0xc8, 0x48, 0x32, 0xa2, 0xf6, 0xee, 0x0f, 0x78, 0xa2, 0x33, 0x6d, 0x31, 0x8c, 0x6b, 0x7f, 0xa6,
	0xa9, 0xd9, 0x5e, 0xdb, 0xd4, 0xee, 0x8c, 0xbb, 0x92, 0x2f, 0xb5, 0x66, 0xc2, 0x98, 0x7b, 0x70,
	0xa5, 0xa4, 0xbd, 0xf7, 0x1e, 0x60, 0xaa, 0xa5, 0xca, 0x11, 0x1e, 0x6d, 0xbf, 0x4c, 0x65, 0x81,
	0x4b, 0xf7, 0x01, 0x0c, 0xf0, 0xf5, 0x8e, 0xb9, 0x01, 0xcf, 0xe3, 0xbf, 0x41, 0x77, 0x15, 0x2e,
	0x94, 0x0a, 0x9b, 0xca, 0xa2, 0x8d, 0x3e, 0x87, 0x33, 0x6a, 0xd8, 0x18, 0xf7, 0xef, 0xf7, 0x0e,
	0x4e, 0x27, 0xf4, 0xae, 0xc9, 0x5b, 0x38, 0x99, 0x50, 0xdb, 0x3e, 0xd3, 0xd6, 0xbe, 0xd5, 0xc7,
	0xc5, 0xf7, 0xe8, 0xe2, 0x43, 0x95, 0x65, 0x8b, 0xe6, 0x0f, 0x7a, 0xff, 0x3f, 0x00, 0x00, 0xff,
	0xff, 0x57, 0xcc, 0x51, 0x07, 0x57, 0x03, 0x00, 0x00,
}
