// Code generated by protoc-gen-go. DO NOT EDIT.
// source: question.proto

package pb

import (
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

// QuestionAddReq 语料新增
type QuestionSaveReq struct {
	// 问题id
	QuestionId int64 `protobuf:"varint,1,opt,name=question_id,json=questionId,proto3" json:"question_id"`
	// @gotags: validate:"required"
	Project string `protobuf:"bytes,2,opt,name=project,proto3" json:"project" validate:"required"`
	// @gotags: validate:"required"
	QuestionContent string `protobuf:"bytes,3,opt,name=question_content,json=questionContent,proto3" json:"question_content" validate:"required"`
	// @gotags: validate:"required"
	Lang string `protobuf:"bytes,4,opt,name=lang,proto3" json:"lang" validate:"required"`
	// @gotags: validate:"required"
	CatId uint32 `protobuf:"varint,5,opt,name=cat_id,json=catId,proto3" json:"cat_id" validate:"required"`
	// @gotags: validate:"required"
	Answer               string   `protobuf:"bytes,6,opt,name=answer,proto3" json:"answer" validate:"required"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *QuestionSaveReq) Reset()         { *m = QuestionSaveReq{} }
func (m *QuestionSaveReq) String() string { return proto.CompactTextString(m) }
func (*QuestionSaveReq) ProtoMessage()    {}
func (*QuestionSaveReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_64d310e1b23c85d7, []int{0}
}

func (m *QuestionSaveReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QuestionSaveReq.Unmarshal(m, b)
}
func (m *QuestionSaveReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QuestionSaveReq.Marshal(b, m, deterministic)
}
func (m *QuestionSaveReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QuestionSaveReq.Merge(m, src)
}
func (m *QuestionSaveReq) XXX_Size() int {
	return xxx_messageInfo_QuestionSaveReq.Size(m)
}
func (m *QuestionSaveReq) XXX_DiscardUnknown() {
	xxx_messageInfo_QuestionSaveReq.DiscardUnknown(m)
}

var xxx_messageInfo_QuestionSaveReq proto.InternalMessageInfo

func (m *QuestionSaveReq) GetQuestionId() int64 {
	if m != nil {
		return m.QuestionId
	}
	return 0
}

func (m *QuestionSaveReq) GetProject() string {
	if m != nil {
		return m.Project
	}
	return ""
}

func (m *QuestionSaveReq) GetQuestionContent() string {
	if m != nil {
		return m.QuestionContent
	}
	return ""
}

func (m *QuestionSaveReq) GetLang() string {
	if m != nil {
		return m.Lang
	}
	return ""
}

func (m *QuestionSaveReq) GetCatId() uint32 {
	if m != nil {
		return m.CatId
	}
	return 0
}

func (m *QuestionSaveReq) GetAnswer() string {
	if m != nil {
		return m.Answer
	}
	return ""
}

// QuestionAddReq 工单知识库训练
type QuestionTrainingReq struct {
	// @gotags: validate:"required"
	Project string `protobuf:"bytes,1,opt,name=project,proto3" json:"project" validate:"required"`
	// true代表训练该游戏下的所有语种
	IsAll                bool     `protobuf:"varint,2,opt,name=is_all,json=isAll,proto3" json:"is_all"`
	Lang                 []string `protobuf:"bytes,3,rep,name=lang,proto3" json:"lang"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *QuestionTrainingReq) Reset()         { *m = QuestionTrainingReq{} }
func (m *QuestionTrainingReq) String() string { return proto.CompactTextString(m) }
func (*QuestionTrainingReq) ProtoMessage()    {}
func (*QuestionTrainingReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_64d310e1b23c85d7, []int{1}
}

func (m *QuestionTrainingReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QuestionTrainingReq.Unmarshal(m, b)
}
func (m *QuestionTrainingReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QuestionTrainingReq.Marshal(b, m, deterministic)
}
func (m *QuestionTrainingReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QuestionTrainingReq.Merge(m, src)
}
func (m *QuestionTrainingReq) XXX_Size() int {
	return xxx_messageInfo_QuestionTrainingReq.Size(m)
}
func (m *QuestionTrainingReq) XXX_DiscardUnknown() {
	xxx_messageInfo_QuestionTrainingReq.DiscardUnknown(m)
}

var xxx_messageInfo_QuestionTrainingReq proto.InternalMessageInfo

func (m *QuestionTrainingReq) GetProject() string {
	if m != nil {
		return m.Project
	}
	return ""
}

func (m *QuestionTrainingReq) GetIsAll() bool {
	if m != nil {
		return m.IsAll
	}
	return false
}

func (m *QuestionTrainingReq) GetLang() []string {
	if m != nil {
		return m.Lang
	}
	return nil
}

// QuestionListReq 工单知识库列表req
type QuestionListReq struct {
	// 游戏
	Project []string `protobuf:"bytes,1,rep,name=project,proto3" json:"project"`
	// 问题分类id
	CatId []uint32 `protobuf:"varint,2,rep,packed,name=cat_id,json=catId,proto3" json:"cat_id"`
	// 语料id
	QuestionId int64 `protobuf:"varint,3,opt,name=question_id,json=questionId,proto3" json:"question_id"`
	// 语料内容
	QuestionContent string `protobuf:"bytes,4,opt,name=question_content,json=questionContent,proto3" json:"question_content"`
	// 语种
	Lang []string `protobuf:"bytes,5,rep,name=lang,proto3" json:"lang"`
	// 更新时间
	UpdateDate []string `protobuf:"bytes,6,rep,name=update_date,json=updateDate,proto3" json:"update_date"`
	// @gotags: validate:"required"
	Page uint32 `protobuf:"varint,7,opt,name=page,proto3" json:"page" validate:"required"`
	// @gotags: validate:"required"
	PageSize             uint32   `protobuf:"varint,8,opt,name=page_size,json=pageSize,proto3" json:"page_size" validate:"required"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *QuestionListReq) Reset()         { *m = QuestionListReq{} }
func (m *QuestionListReq) String() string { return proto.CompactTextString(m) }
func (*QuestionListReq) ProtoMessage()    {}
func (*QuestionListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_64d310e1b23c85d7, []int{2}
}

func (m *QuestionListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QuestionListReq.Unmarshal(m, b)
}
func (m *QuestionListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QuestionListReq.Marshal(b, m, deterministic)
}
func (m *QuestionListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QuestionListReq.Merge(m, src)
}
func (m *QuestionListReq) XXX_Size() int {
	return xxx_messageInfo_QuestionListReq.Size(m)
}
func (m *QuestionListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_QuestionListReq.DiscardUnknown(m)
}

var xxx_messageInfo_QuestionListReq proto.InternalMessageInfo

func (m *QuestionListReq) GetProject() []string {
	if m != nil {
		return m.Project
	}
	return nil
}

func (m *QuestionListReq) GetCatId() []uint32 {
	if m != nil {
		return m.CatId
	}
	return nil
}

func (m *QuestionListReq) GetQuestionId() int64 {
	if m != nil {
		return m.QuestionId
	}
	return 0
}

func (m *QuestionListReq) GetQuestionContent() string {
	if m != nil {
		return m.QuestionContent
	}
	return ""
}

func (m *QuestionListReq) GetLang() []string {
	if m != nil {
		return m.Lang
	}
	return nil
}

func (m *QuestionListReq) GetUpdateDate() []string {
	if m != nil {
		return m.UpdateDate
	}
	return nil
}

func (m *QuestionListReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *QuestionListReq) GetPageSize() uint32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

// QuestionAddResp 工单知识库列表resp
type QuestionListResp struct {
	CurrentPage          uint32                             `protobuf:"varint,1,opt,name=current_page,json=currentPage,proto3" json:"current_page"`
	PerPage              uint32                             `protobuf:"varint,2,opt,name=per_page,json=perPage,proto3" json:"per_page"`
	Total                uint32                             `protobuf:"varint,3,opt,name=total,proto3" json:"total"`
	Data                 []*QuestionListResp_QuestionRecord `protobuf:"bytes,4,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}                           `json:"-" gorm:"-"`
	XXX_unrecognized     []byte                             `json:"-" gorm:"-"`
	XXX_sizecache        int32                              `json:"-" gorm:"-"`
}

func (m *QuestionListResp) Reset()         { *m = QuestionListResp{} }
func (m *QuestionListResp) String() string { return proto.CompactTextString(m) }
func (*QuestionListResp) ProtoMessage()    {}
func (*QuestionListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_64d310e1b23c85d7, []int{3}
}

func (m *QuestionListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QuestionListResp.Unmarshal(m, b)
}
func (m *QuestionListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QuestionListResp.Marshal(b, m, deterministic)
}
func (m *QuestionListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QuestionListResp.Merge(m, src)
}
func (m *QuestionListResp) XXX_Size() int {
	return xxx_messageInfo_QuestionListResp.Size(m)
}
func (m *QuestionListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_QuestionListResp.DiscardUnknown(m)
}

var xxx_messageInfo_QuestionListResp proto.InternalMessageInfo

func (m *QuestionListResp) GetCurrentPage() uint32 {
	if m != nil {
		return m.CurrentPage
	}
	return 0
}

func (m *QuestionListResp) GetPerPage() uint32 {
	if m != nil {
		return m.PerPage
	}
	return 0
}

func (m *QuestionListResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *QuestionListResp) GetData() []*QuestionListResp_QuestionRecord {
	if m != nil {
		return m.Data
	}
	return nil
}

type QuestionListResp_QuestionRecord struct {
	// 语料id
	QuestionId int64 `protobuf:"varint,1,opt,name=question_id,json=questionId,proto3" json:"question_id"`
	// 语料内容
	QuestionContent string `protobuf:"bytes,2,opt,name=question_content,json=questionContent,proto3" json:"question_content"`
	// 语种
	Lang string `protobuf:"bytes,3,opt,name=lang,proto3" json:"lang"`
	// 游戏
	Project string `protobuf:"bytes,4,opt,name=project,proto3" json:"project"`
	// 问题分类
	Category string `protobuf:"bytes,5,opt,name=category,proto3" json:"category"`
	// 问题分类id,便于回显
	CatId uint32 `protobuf:"varint,6,opt,name=cat_id,json=catId,proto3" json:"cat_id"`
	// 更新时间
	UpdateDate string `protobuf:"bytes,7,opt,name=update_date,json=updateDate,proto3" json:"update_date"`
	// 更新人
	Operator string `protobuf:"bytes,8,opt,name=operator,proto3" json:"operator"`
	// 富文本答案
	AnswerRichText       string   `protobuf:"bytes,9,opt,name=answer_rich_text,json=answerRichText,proto3" json:"answer_rich_text"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *QuestionListResp_QuestionRecord) Reset()         { *m = QuestionListResp_QuestionRecord{} }
func (m *QuestionListResp_QuestionRecord) String() string { return proto.CompactTextString(m) }
func (*QuestionListResp_QuestionRecord) ProtoMessage()    {}
func (*QuestionListResp_QuestionRecord) Descriptor() ([]byte, []int) {
	return fileDescriptor_64d310e1b23c85d7, []int{3, 0}
}

func (m *QuestionListResp_QuestionRecord) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QuestionListResp_QuestionRecord.Unmarshal(m, b)
}
func (m *QuestionListResp_QuestionRecord) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QuestionListResp_QuestionRecord.Marshal(b, m, deterministic)
}
func (m *QuestionListResp_QuestionRecord) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QuestionListResp_QuestionRecord.Merge(m, src)
}
func (m *QuestionListResp_QuestionRecord) XXX_Size() int {
	return xxx_messageInfo_QuestionListResp_QuestionRecord.Size(m)
}
func (m *QuestionListResp_QuestionRecord) XXX_DiscardUnknown() {
	xxx_messageInfo_QuestionListResp_QuestionRecord.DiscardUnknown(m)
}

var xxx_messageInfo_QuestionListResp_QuestionRecord proto.InternalMessageInfo

func (m *QuestionListResp_QuestionRecord) GetQuestionId() int64 {
	if m != nil {
		return m.QuestionId
	}
	return 0
}

func (m *QuestionListResp_QuestionRecord) GetQuestionContent() string {
	if m != nil {
		return m.QuestionContent
	}
	return ""
}

func (m *QuestionListResp_QuestionRecord) GetLang() string {
	if m != nil {
		return m.Lang
	}
	return ""
}

func (m *QuestionListResp_QuestionRecord) GetProject() string {
	if m != nil {
		return m.Project
	}
	return ""
}

func (m *QuestionListResp_QuestionRecord) GetCategory() string {
	if m != nil {
		return m.Category
	}
	return ""
}

func (m *QuestionListResp_QuestionRecord) GetCatId() uint32 {
	if m != nil {
		return m.CatId
	}
	return 0
}

func (m *QuestionListResp_QuestionRecord) GetUpdateDate() string {
	if m != nil {
		return m.UpdateDate
	}
	return ""
}

func (m *QuestionListResp_QuestionRecord) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

func (m *QuestionListResp_QuestionRecord) GetAnswerRichText() string {
	if m != nil {
		return m.AnswerRichText
	}
	return ""
}

// QuestionDelReq 工单知识库删除
type QuestionDelReq struct {
	// @gotags: validate:"required"
	QuestionId           int64    `protobuf:"varint,1,opt,name=question_id,json=questionId,proto3" json:"question_id" validate:"required"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *QuestionDelReq) Reset()         { *m = QuestionDelReq{} }
func (m *QuestionDelReq) String() string { return proto.CompactTextString(m) }
func (*QuestionDelReq) ProtoMessage()    {}
func (*QuestionDelReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_64d310e1b23c85d7, []int{4}
}

func (m *QuestionDelReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QuestionDelReq.Unmarshal(m, b)
}
func (m *QuestionDelReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QuestionDelReq.Marshal(b, m, deterministic)
}
func (m *QuestionDelReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QuestionDelReq.Merge(m, src)
}
func (m *QuestionDelReq) XXX_Size() int {
	return xxx_messageInfo_QuestionDelReq.Size(m)
}
func (m *QuestionDelReq) XXX_DiscardUnknown() {
	xxx_messageInfo_QuestionDelReq.DiscardUnknown(m)
}

var xxx_messageInfo_QuestionDelReq proto.InternalMessageInfo

func (m *QuestionDelReq) GetQuestionId() int64 {
	if m != nil {
		return m.QuestionId
	}
	return 0
}

// QuestionBatchImportReq 工单知识库批量导入
type QuestionBatchImportReq struct {
	// 文件url @gotags: validate:"required"
	FileName             string   `protobuf:"bytes,1,opt,name=file_name,json=fileName,proto3" json:"file_name" validate:"required"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *QuestionBatchImportReq) Reset()         { *m = QuestionBatchImportReq{} }
func (m *QuestionBatchImportReq) String() string { return proto.CompactTextString(m) }
func (*QuestionBatchImportReq) ProtoMessage()    {}
func (*QuestionBatchImportReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_64d310e1b23c85d7, []int{5}
}

func (m *QuestionBatchImportReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QuestionBatchImportReq.Unmarshal(m, b)
}
func (m *QuestionBatchImportReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QuestionBatchImportReq.Marshal(b, m, deterministic)
}
func (m *QuestionBatchImportReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QuestionBatchImportReq.Merge(m, src)
}
func (m *QuestionBatchImportReq) XXX_Size() int {
	return xxx_messageInfo_QuestionBatchImportReq.Size(m)
}
func (m *QuestionBatchImportReq) XXX_DiscardUnknown() {
	xxx_messageInfo_QuestionBatchImportReq.DiscardUnknown(m)
}

var xxx_messageInfo_QuestionBatchImportReq proto.InternalMessageInfo

func (m *QuestionBatchImportReq) GetFileName() string {
	if m != nil {
		return m.FileName
	}
	return ""
}

type QuestionTrainLogReq struct {
	// @gotags: validate:"required"
	Page uint32 `protobuf:"varint,1,opt,name=page,proto3" json:"page" validate:"required"`
	// @gotags: validate:"required"
	PageSize             uint32   `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size" validate:"required"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *QuestionTrainLogReq) Reset()         { *m = QuestionTrainLogReq{} }
func (m *QuestionTrainLogReq) String() string { return proto.CompactTextString(m) }
func (*QuestionTrainLogReq) ProtoMessage()    {}
func (*QuestionTrainLogReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_64d310e1b23c85d7, []int{6}
}

func (m *QuestionTrainLogReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QuestionTrainLogReq.Unmarshal(m, b)
}
func (m *QuestionTrainLogReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QuestionTrainLogReq.Marshal(b, m, deterministic)
}
func (m *QuestionTrainLogReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QuestionTrainLogReq.Merge(m, src)
}
func (m *QuestionTrainLogReq) XXX_Size() int {
	return xxx_messageInfo_QuestionTrainLogReq.Size(m)
}
func (m *QuestionTrainLogReq) XXX_DiscardUnknown() {
	xxx_messageInfo_QuestionTrainLogReq.DiscardUnknown(m)
}

var xxx_messageInfo_QuestionTrainLogReq proto.InternalMessageInfo

func (m *QuestionTrainLogReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *QuestionTrainLogReq) GetPageSize() uint32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

// QuestionAddResp 训练列表resp
type QuestionTrainLogResp struct {
	CurrentPage          uint32                              `protobuf:"varint,1,opt,name=current_page,json=currentPage,proto3" json:"current_page"`
	PerPage              uint32                              `protobuf:"varint,2,opt,name=per_page,json=perPage,proto3" json:"per_page"`
	Total                int64                               `protobuf:"varint,3,opt,name=total,proto3" json:"total"`
	Data                 []*QuestionTrainLogResp_TrainRecord `protobuf:"bytes,4,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}                            `json:"-" gorm:"-"`
	XXX_unrecognized     []byte                              `json:"-" gorm:"-"`
	XXX_sizecache        int32                               `json:"-" gorm:"-"`
}

func (m *QuestionTrainLogResp) Reset()         { *m = QuestionTrainLogResp{} }
func (m *QuestionTrainLogResp) String() string { return proto.CompactTextString(m) }
func (*QuestionTrainLogResp) ProtoMessage()    {}
func (*QuestionTrainLogResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_64d310e1b23c85d7, []int{7}
}

func (m *QuestionTrainLogResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QuestionTrainLogResp.Unmarshal(m, b)
}
func (m *QuestionTrainLogResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QuestionTrainLogResp.Marshal(b, m, deterministic)
}
func (m *QuestionTrainLogResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QuestionTrainLogResp.Merge(m, src)
}
func (m *QuestionTrainLogResp) XXX_Size() int {
	return xxx_messageInfo_QuestionTrainLogResp.Size(m)
}
func (m *QuestionTrainLogResp) XXX_DiscardUnknown() {
	xxx_messageInfo_QuestionTrainLogResp.DiscardUnknown(m)
}

var xxx_messageInfo_QuestionTrainLogResp proto.InternalMessageInfo

func (m *QuestionTrainLogResp) GetCurrentPage() uint32 {
	if m != nil {
		return m.CurrentPage
	}
	return 0
}

func (m *QuestionTrainLogResp) GetPerPage() uint32 {
	if m != nil {
		return m.PerPage
	}
	return 0
}

func (m *QuestionTrainLogResp) GetTotal() int64 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *QuestionTrainLogResp) GetData() []*QuestionTrainLogResp_TrainRecord {
	if m != nil {
		return m.Data
	}
	return nil
}

type QuestionTrainLogResp_TrainRecord struct {
	// 训练时间
	CreatedAt string `protobuf:"bytes,1,opt,name=created_at,json=createdAt,proto3" json:"created_at"`
	// 操作人
	Operator string `protobuf:"bytes,2,opt,name=operator,proto3" json:"operator"`
	// 训练状态
	Status uint32 `protobuf:"varint,3,opt,name=status,proto3" json:"status"`
	// 游戏
	Project string `protobuf:"bytes,4,opt,name=project,proto3" json:"project"`
	// 语种
	Lang                 string   `protobuf:"bytes,5,opt,name=lang,proto3" json:"lang"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *QuestionTrainLogResp_TrainRecord) Reset()         { *m = QuestionTrainLogResp_TrainRecord{} }
func (m *QuestionTrainLogResp_TrainRecord) String() string { return proto.CompactTextString(m) }
func (*QuestionTrainLogResp_TrainRecord) ProtoMessage()    {}
func (*QuestionTrainLogResp_TrainRecord) Descriptor() ([]byte, []int) {
	return fileDescriptor_64d310e1b23c85d7, []int{7, 0}
}

func (m *QuestionTrainLogResp_TrainRecord) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QuestionTrainLogResp_TrainRecord.Unmarshal(m, b)
}
func (m *QuestionTrainLogResp_TrainRecord) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QuestionTrainLogResp_TrainRecord.Marshal(b, m, deterministic)
}
func (m *QuestionTrainLogResp_TrainRecord) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QuestionTrainLogResp_TrainRecord.Merge(m, src)
}
func (m *QuestionTrainLogResp_TrainRecord) XXX_Size() int {
	return xxx_messageInfo_QuestionTrainLogResp_TrainRecord.Size(m)
}
func (m *QuestionTrainLogResp_TrainRecord) XXX_DiscardUnknown() {
	xxx_messageInfo_QuestionTrainLogResp_TrainRecord.DiscardUnknown(m)
}

var xxx_messageInfo_QuestionTrainLogResp_TrainRecord proto.InternalMessageInfo

func (m *QuestionTrainLogResp_TrainRecord) GetCreatedAt() string {
	if m != nil {
		return m.CreatedAt
	}
	return ""
}

func (m *QuestionTrainLogResp_TrainRecord) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

func (m *QuestionTrainLogResp_TrainRecord) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *QuestionTrainLogResp_TrainRecord) GetProject() string {
	if m != nil {
		return m.Project
	}
	return ""
}

func (m *QuestionTrainLogResp_TrainRecord) GetLang() string {
	if m != nil {
		return m.Lang
	}
	return ""
}

func init() {
	proto.RegisterType((*QuestionSaveReq)(nil), "pb.QuestionSaveReq")
	proto.RegisterType((*QuestionTrainingReq)(nil), "pb.QuestionTrainingReq")
	proto.RegisterType((*QuestionListReq)(nil), "pb.QuestionListReq")
	proto.RegisterType((*QuestionListResp)(nil), "pb.QuestionListResp")
	proto.RegisterType((*QuestionListResp_QuestionRecord)(nil), "pb.QuestionListResp.QuestionRecord")
	proto.RegisterType((*QuestionDelReq)(nil), "pb.QuestionDelReq")
	proto.RegisterType((*QuestionBatchImportReq)(nil), "pb.QuestionBatchImportReq")
	proto.RegisterType((*QuestionTrainLogReq)(nil), "pb.QuestionTrainLogReq")
	proto.RegisterType((*QuestionTrainLogResp)(nil), "pb.QuestionTrainLogResp")
	proto.RegisterType((*QuestionTrainLogResp_TrainRecord)(nil), "pb.QuestionTrainLogResp.TrainRecord")
}

func init() {
	proto.RegisterFile("question.proto", fileDescriptor_64d310e1b23c85d7)
}

var fileDescriptor_64d310e1b23c85d7 = []byte{
	// 626 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xac, 0x54, 0xcf, 0x6e, 0xd3, 0x4e,
	0x10, 0x96, 0xff, 0xc4, 0xb1, 0x27, 0xbf, 0xfe, 0xd1, 0xfe, 0xda, 0xca, 0xb4, 0x42, 0x04, 0xc3,
	0x21, 0x5c, 0x22, 0x01, 0x42, 0x20, 0x71, 0x6a, 0xa9, 0x90, 0x2a, 0x55, 0x08, 0xdc, 0x9e, 0x7a,
	0xb1, 0x36, 0xf6, 0x90, 0x1a, 0xb9, 0xde, 0xed, 0x7a, 0x02, 0xa5, 0x0f, 0xc1, 0x53, 0xf0, 0x0a,
	0x5c, 0x78, 0x00, 0x9e, 0x88, 0x07, 0x40, 0xde, 0xd8, 0x89, 0xed, 0x96, 0xc0, 0x81, 0x4b, 0xe2,
	0xf9, 0x66, 0x67, 0xfc, 0xf9, 0x9b, 0x6f, 0x16, 0xd6, 0x2f, 0x67, 0x58, 0x50, 0x2a, 0xf2, 0xb1,
	0x54, 0x82, 0x04, 0x33, 0xe5, 0x24, 0xf8, 0x6e, 0xc0, 0xc6, 0xbb, 0x0a, 0x3e, 0xe1, 0x1f, 0x31,
	0xc4, 0x4b, 0x76, 0x0f, 0x06, 0xf5, 0xc9, 0x28, 0x4d, 0x7c, 0x63, 0x68, 0x8c, 0xac, 0x10, 0x6a,
	0xe8, 0x28, 0x61, 0x3e, 0xf4, 0xa5, 0x12, 0x1f, 0x30, 0x26, 0xdf, 0x1c, 0x1a, 0x23, 0x2f, 0xac,
	0x43, 0xf6, 0x08, 0x36, 0x17, 0xa5, 0xb1, 0xc8, 0x09, 0x73, 0xf2, 0x2d, 0x7d, 0x64, 0xa3, 0xc6,
	0x5f, 0xcd, 0x61, 0xc6, 0xc0, 0xce, 0x78, 0x3e, 0xf5, 0x6d, 0x9d, 0xd6, 0xcf, 0x6c, 0x1b, 0x9c,
	0x98, 0x53, 0xf9, 0xd2, 0xde, 0xd0, 0x18, 0xad, 0x85, 0xbd, 0x98, 0xd3, 0x51, 0xc2, 0x76, 0xc0,
	0xe1, 0x79, 0xf1, 0x09, 0x95, 0xef, 0xe8, 0xc3, 0x55, 0x14, 0x9c, 0xc1, 0xff, 0x35, 0xf7, 0x53,
	0xc5, 0xd3, 0x3c, 0xcd, 0xa7, 0x25, 0xff, 0x06, 0x3d, 0xa3, 0x4d, 0x6f, 0x1b, 0x9c, 0xb4, 0x88,
	0x78, 0x96, 0x69, 0xde, 0x6e, 0xd8, 0x4b, 0x8b, 0xfd, 0x2c, 0x5b, 0x50, 0xb1, 0x86, 0x56, 0x4d,
	0x25, 0xf8, 0xd9, 0x10, 0xe6, 0x38, 0x2d, 0xe8, 0x46, 0x63, 0xab, 0xd3, 0xb8, 0x22, 0x6e, 0x0e,
	0xad, 0x25, 0xf1, 0x8e, 0x92, 0xd6, 0x0d, 0x25, 0x6f, 0xd3, 0xcb, 0x5e, 0xad, 0x57, 0x6f, 0x49,
	0xb2, 0xec, 0x3f, 0x93, 0x09, 0x27, 0x8c, 0xca, 0x1f, 0xdf, 0xd1, 0x29, 0x98, 0x43, 0x87, 0x9c,
	0xb0, 0x2c, 0x92, 0x7c, 0x8a, 0x7e, 0x5f, 0xcb, 0xa9, 0x9f, 0xd9, 0x1e, 0x78, 0xe5, 0x7f, 0x54,
	0xa4, 0xd7, 0xe8, 0xbb, 0x3a, 0xe1, 0x96, 0xc0, 0x49, 0x7a, 0x8d, 0xc1, 0x0f, 0x0b, 0x36, 0xdb,
	0x9f, 0x5d, 0x48, 0x76, 0x1f, 0xfe, 0x8b, 0x67, 0x4a, 0x61, 0x4e, 0x91, 0xee, 0x66, 0xe8, 0xa2,
	0x41, 0x85, 0xbd, 0x2d, 0x9b, 0xde, 0x01, 0x57, 0xa2, 0x9a, 0xa7, 0x4d, 0x9d, 0xee, 0x4b, 0x54,
	0x3a, 0xb5, 0x05, 0x3d, 0x12, 0xc4, 0x33, 0xfd, 0xf9, 0x6b, 0xe1, 0x3c, 0x60, 0xcf, 0xc1, 0x4e,
	0x38, 0x71, 0xdf, 0x1e, 0x5a, 0xa3, 0xc1, 0x93, 0x07, 0x63, 0x39, 0x19, 0x77, 0xdf, 0xbb, 0x00,
	0x42, 0x8c, 0x85, 0x4a, 0x42, 0x5d, 0xb0, 0xfb, 0xd5, 0x84, 0xf5, 0x76, 0xe2, 0xcf, 0x86, 0xbd,
	0x4d, 0x66, 0x73, 0xb5, 0xcc, 0x56, 0xc3, 0x96, 0x8d, 0xb9, 0xdb, 0x6d, 0x43, 0xed, 0x82, 0x1b,
	0x73, 0xc2, 0xa9, 0x50, 0x9f, 0xb5, 0x65, 0xbd, 0x70, 0x11, 0x37, 0x3c, 0xe1, 0x34, 0xcd, 0xdc,
	0x99, 0x59, 0x5f, 0x57, 0x35, 0x67, 0xb6, 0x0b, 0xae, 0x90, 0xa8, 0x38, 0x09, 0xa5, 0xc7, 0xe3,
	0x85, 0x8b, 0x98, 0x8d, 0x60, 0x73, 0xee, 0xfd, 0x48, 0xa5, 0xf1, 0x79, 0x44, 0x78, 0x45, 0xbe,
	0xa7, 0xcf, 0xac, 0xcf, 0xf1, 0x30, 0x8d, 0xcf, 0x4f, 0xf1, 0x8a, 0x82, 0xc7, 0x4b, 0x95, 0x0e,
	0x31, 0xfb, 0x9b, 0xb5, 0x0e, 0x9e, 0xc1, 0x4e, 0x5d, 0x72, 0xc0, 0x29, 0x3e, 0x3f, 0xba, 0x90,
	0x42, 0x69, 0xe3, 0xef, 0x81, 0xf7, 0x3e, 0xcd, 0x30, 0xca, 0xf9, 0x05, 0x56, 0x3b, 0xe5, 0x96,
	0xc0, 0x1b, 0x7e, 0x81, 0xc1, 0xeb, 0xce, 0x16, 0x1e, 0x0b, 0xbd, 0x85, 0xb5, 0xf5, 0x8c, 0xdf,
	0x59, 0xcf, 0xec, 0x58, 0xef, 0x9b, 0x09, 0x5b, 0x37, 0x1b, 0xfd, 0x5b, 0xfb, 0x59, 0xb5, 0xfd,
	0x5e, 0xb4, 0xec, 0xf7, 0xb0, 0x69, 0xbf, 0xe6, 0xbb, 0xc7, 0x3a, 0x68, 0xf9, 0xef, 0x8b, 0x01,
	0x83, 0x06, 0xca, 0xee, 0x02, 0xc4, 0x0a, 0x39, 0x61, 0x12, 0xf1, 0xfa, 0xc2, 0xf1, 0x2a, 0x64,
	0x9f, 0x5a, 0xd3, 0x34, 0x3b, 0xd3, 0xdc, 0x01, 0xa7, 0x20, 0x4e, 0xb3, 0xa2, 0x5a, 0x8d, 0x2a,
	0x5a, 0xe1, 0xb7, 0xe5, 0x25, 0xb0, 0x70, 0xe7, 0x81, 0x73, 0x66, 0x8f, 0x5f, 0xca, 0xc9, 0xc4,
	0xd1, 0xb7, 0xfa, 0xd3, 0x5f, 0x01, 0x00, 0x00, 0xff, 0xff, 0x93, 0x0d, 0xf9, 0x50, 0xe7, 0x05,
	0x00, 0x00,
}
