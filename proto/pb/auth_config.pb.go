// Code generated by protoc-gen-go. DO NOT EDIT.
// source: auth_config.proto

package pb

import (
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

// AuthConfigSyncReq 从老工单同步auth配置数据
type AuthConfigSyncReq struct {
	// @gotags: validate:"required"
	GameProject string `protobuf:"bytes,1,opt,name=game_project,json=gameProject,proto3" json:"game_project" validate:"required"`
	// @gotags: validate:"required"
	AuthKey string `protobuf:"bytes,2,opt,name=auth_key,json=authKey,proto3" json:"auth_key" validate:"required"`
	// @gotags: validate:"required"
	Secret               string   `protobuf:"bytes,3,opt,name=secret,proto3" json:"secret" validate:"required"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *AuthConfigSyncReq) Reset()         { *m = AuthConfigSyncReq{} }
func (m *AuthConfigSyncReq) String() string { return proto.CompactTextString(m) }
func (*AuthConfigSyncReq) ProtoMessage()    {}
func (*AuthConfigSyncReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_1fc3c9dae7c25069, []int{0}
}

func (m *AuthConfigSyncReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AuthConfigSyncReq.Unmarshal(m, b)
}
func (m *AuthConfigSyncReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AuthConfigSyncReq.Marshal(b, m, deterministic)
}
func (m *AuthConfigSyncReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AuthConfigSyncReq.Merge(m, src)
}
func (m *AuthConfigSyncReq) XXX_Size() int {
	return xxx_messageInfo_AuthConfigSyncReq.Size(m)
}
func (m *AuthConfigSyncReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AuthConfigSyncReq.DiscardUnknown(m)
}

var xxx_messageInfo_AuthConfigSyncReq proto.InternalMessageInfo

func (m *AuthConfigSyncReq) GetGameProject() string {
	if m != nil {
		return m.GameProject
	}
	return ""
}

func (m *AuthConfigSyncReq) GetAuthKey() string {
	if m != nil {
		return m.AuthKey
	}
	return ""
}

func (m *AuthConfigSyncReq) GetSecret() string {
	if m != nil {
		return m.Secret
	}
	return ""
}

func init() {
	proto.RegisterType((*AuthConfigSyncReq)(nil), "pb.AuthConfigSyncReq")
}

func init() {
	proto.RegisterFile("auth_config.proto", fileDescriptor_1fc3c9dae7c25069)
}

var fileDescriptor_1fc3c9dae7c25069 = []byte{
	// 141 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xe2, 0x12, 0x4c, 0x2c, 0x2d, 0xc9,
	0x88, 0x4f, 0xce, 0xcf, 0x4b, 0xcb, 0x4c, 0xd7, 0x2b, 0x28, 0xca, 0x2f, 0xc9, 0x17, 0x62, 0x2a,
	0x48, 0x52, 0xca, 0xe4, 0x12, 0x74, 0x2c, 0x2d, 0xc9, 0x70, 0x06, 0x8b, 0x07, 0x57, 0xe6, 0x25,
	0x07, 0xa5, 0x16, 0x0a, 0x29, 0x72, 0xf1, 0xa4, 0x27, 0xe6, 0xa6, 0xc6, 0x17, 0x14, 0xe5, 0x67,
	0xa5, 0x26, 0x97, 0x48, 0x30, 0x2a, 0x30, 0x6a, 0x70, 0x06, 0x71, 0x83, 0xc4, 0x02, 0x20, 0x42,
	0x42, 0x92, 0x5c, 0x1c, 0x60, 0x03, 0xb3, 0x53, 0x2b, 0x25, 0x98, 0xc0, 0xd2, 0xec, 0x20, 0xbe,
	0x77, 0x6a, 0xa5, 0x90, 0x18, 0x17, 0x5b, 0x71, 0x6a, 0x72, 0x51, 0x6a, 0x89, 0x04, 0x33, 0x58,
	0x02, 0xca, 0x73, 0x62, 0x8b, 0x62, 0xd1, 0xb3, 0x2e, 0x48, 0x4a, 0x62, 0x03, 0xdb, 0x6e, 0x0c,
	0x08, 0x00, 0x00, 0xff, 0xff, 0x36, 0x99, 0x5a, 0x27, 0x92, 0x00, 0x00, 0x00,
}
