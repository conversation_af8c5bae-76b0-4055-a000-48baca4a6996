// Code generated by protoc-gen-go. DO NOT EDIT.
// source: category.proto

package pb

import (
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

// 分类
type CatLevel int32

const (
	CatLevel_UnKnown                 CatLevel = 0
	CatLevel_PrimaryClassification   CatLevel = 1
	CatLevel_SecondaryClassification CatLevel = 2
	CatLevel_ThreeClassification     CatLevel = 3
)

var CatLevel_name = map[int32]string{
	0: "UnKnown",
	1: "PrimaryClassification",
	2: "SecondaryClassification",
	3: "ThreeClassification",
}

var CatLevel_value = map[string]int32{
	"UnKnown":                 0,
	"PrimaryClassification":   1,
	"SecondaryClassification": 2,
	"ThreeClassification":     3,
}

func (x CatLevel) String() string {
	return proto.EnumName(CatLevel_name, int32(x))
}

func (CatLevel) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_1c6ef5ed29d8d1a1, []int{0}
}

// 工单分类scope
type CatScopeEnum int32

const (
	CatScopeEnum_CatScopeDefault CatScopeEnum = 0
	CatScopeEnum_CatScopeInner   CatScopeEnum = 1
	CatScopeEnum_CatScopeAll     CatScopeEnum = -1
)

var CatScopeEnum_name = map[int32]string{
	0:  "CatScopeDefault",
	1:  "CatScopeInner",
	-1: "CatScopeAll",
}

var CatScopeEnum_value = map[string]int32{
	"CatScopeDefault": 0,
	"CatScopeInner":   1,
	"CatScopeAll":     -1,
}

func (x CatScopeEnum) String() string {
	return proto.EnumName(CatScopeEnum_name, int32(x))
}

func (CatScopeEnum) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_1c6ef5ed29d8d1a1, []int{1}
}

type Lang struct {
	Category             map[string]string `protobuf:"bytes,1,rep,name=category,proto3" json:"category" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-" gorm:"-"`
	XXX_unrecognized     []byte            `json:"-" gorm:"-"`
	XXX_sizecache        int32             `json:"-" gorm:"-"`
}

func (m *Lang) Reset()         { *m = Lang{} }
func (m *Lang) String() string { return proto.CompactTextString(m) }
func (*Lang) ProtoMessage()    {}
func (*Lang) Descriptor() ([]byte, []int) {
	return fileDescriptor_1c6ef5ed29d8d1a1, []int{0}
}

func (m *Lang) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Lang.Unmarshal(m, b)
}
func (m *Lang) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Lang.Marshal(b, m, deterministic)
}
func (m *Lang) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Lang.Merge(m, src)
}
func (m *Lang) XXX_Size() int {
	return xxx_messageInfo_Lang.Size(m)
}
func (m *Lang) XXX_DiscardUnknown() {
	xxx_messageInfo_Lang.DiscardUnknown(m)
}

var xxx_messageInfo_Lang proto.InternalMessageInfo

func (m *Lang) GetCategory() map[string]string {
	if m != nil {
		return m.Category
	}
	return nil
}

// CatTplPrefer 三级分类个性化配置
type CatTplPrefer struct {
	Channel   []string `protobuf:"bytes,1,rep,name=channel,proto3" json:"channel"`
	ServerStr string   `protobuf:"bytes,2,opt,name=server_str,json=serverStr,proto3" json:"server_str"`
	Country   []string `protobuf:"bytes,3,rep,name=country,proto3" json:"country"`
	// 模版ID @gotags: validate:"required_if=CatLevel 3"
	TplId                uint32          `protobuf:"varint,4,opt,name=tpl_id,json=tplId,proto3" json:"tpl_id" validate:"required_if=CatLevel 3"`
	Mark                 map[string]bool `protobuf:"bytes,5,rep,name=mark,proto3" json:"mark" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}        `json:"-" gorm:"-"`
	XXX_unrecognized     []byte          `json:"-" gorm:"-"`
	XXX_sizecache        int32           `json:"-" gorm:"-"`
}

func (m *CatTplPrefer) Reset()         { *m = CatTplPrefer{} }
func (m *CatTplPrefer) String() string { return proto.CompactTextString(m) }
func (*CatTplPrefer) ProtoMessage()    {}
func (*CatTplPrefer) Descriptor() ([]byte, []int) {
	return fileDescriptor_1c6ef5ed29d8d1a1, []int{1}
}

func (m *CatTplPrefer) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CatTplPrefer.Unmarshal(m, b)
}
func (m *CatTplPrefer) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CatTplPrefer.Marshal(b, m, deterministic)
}
func (m *CatTplPrefer) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CatTplPrefer.Merge(m, src)
}
func (m *CatTplPrefer) XXX_Size() int {
	return xxx_messageInfo_CatTplPrefer.Size(m)
}
func (m *CatTplPrefer) XXX_DiscardUnknown() {
	xxx_messageInfo_CatTplPrefer.DiscardUnknown(m)
}

var xxx_messageInfo_CatTplPrefer proto.InternalMessageInfo

func (m *CatTplPrefer) GetChannel() []string {
	if m != nil {
		return m.Channel
	}
	return nil
}

func (m *CatTplPrefer) GetServerStr() string {
	if m != nil {
		return m.ServerStr
	}
	return ""
}

func (m *CatTplPrefer) GetCountry() []string {
	if m != nil {
		return m.Country
	}
	return nil
}

func (m *CatTplPrefer) GetTplId() uint32 {
	if m != nil {
		return m.TplId
	}
	return 0
}

func (m *CatTplPrefer) GetMark() map[string]bool {
	if m != nil {
		return m.Mark
	}
	return nil
}

// CatProbSaveReq 修改三级分类 请求参数
type CatProbSaveReq struct {
	// 分类ID @gotags: validate:"required"
	CatId uint32 `protobuf:"varint,1,opt,name=cat_id,json=catId,proto3" json:"cat_id" validate:"required"`
	// 分类名称 @gotags: validate:"required"
	Category string `protobuf:"bytes,2,opt,name=category,proto3" json:"category" validate:"required"`
	// 模版ID @gotags: validate:"required_if=RelateType 1"
	TplId uint32 `protobuf:"varint,3,opt,name=tpl_id,json=tplId,proto3" json:"tpl_id" validate:"required_if=RelateType 1"`
	// 技能组ID
	GroupId uint32 `protobuf:"varint,4,opt,name=group_id,json=groupId,proto3" json:"group_id"`
	// 紧急度 1:高 2:中 3:低 @gotags: validate:"required_if=CatLevel 3"
	Priority uint32 `protobuf:"varint,5,opt,name=priority,proto3" json:"priority" validate:"required_if=CatLevel 3"`
	// 完成时间
	Deadline uint64 `protobuf:"varint,6,opt,name=deadline,proto3" json:"deadline"`
	// 分类的等级 @gotags: validate:"required,oneof=1 2 3"
	CatLevel uint32 `protobuf:"varint,7,opt,name=cat_level,json=catLevel,proto3" json:"cat_level" validate:"required,oneof=1 2 3"`
	// 是否评价 是:true 否:false
	Evaluation bool `protobuf:"varint,8,opt,name=evaluation,proto3" json:"evaluation"`
	// 多语言
	Langs *Lang `protobuf:"bytes,9,opt,name=langs,proto3" json:"langs"`
	// 个性化模板配置
	TplList []*CatTplPrefer `protobuf:"bytes,10,rep,name=tpl_list,json=tplList,proto3" json:"tpl_list"`
	// 回复模板id
	ReplyTplId uint32 `protobuf:"varint,11,opt,name=reply_tpl_id,json=replyTplId,proto3" json:"reply_tpl_id"`
	// 关联类型 1:表单 2:流程 @gotags: validate:"required_if=CatLevel 3"
	RelateType uint32 `protobuf:"varint,12,opt,name=relate_type,json=relateType,proto3" json:"relate_type" validate:"required_if=CatLevel 3"`
	// 流程id @gotags: validate:"required_if=RelateType 2"
	ProcessId uint32 `protobuf:"varint,13,opt,name=process_id,json=processId,proto3" json:"process_id" validate:"required_if=RelateType 2"`
	// 是否重开 1：是 0：否
	Reopen bool `protobuf:"varint,14,opt,name=reopen,proto3" json:"reopen"`
	// 重开次数
	ReopenNum            uint32   `protobuf:"varint,15,opt,name=reopen_num,json=reopenNum,proto3" json:"reopen_num"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *CatProbSaveReq) Reset()         { *m = CatProbSaveReq{} }
func (m *CatProbSaveReq) String() string { return proto.CompactTextString(m) }
func (*CatProbSaveReq) ProtoMessage()    {}
func (*CatProbSaveReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_1c6ef5ed29d8d1a1, []int{2}
}

func (m *CatProbSaveReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CatProbSaveReq.Unmarshal(m, b)
}
func (m *CatProbSaveReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CatProbSaveReq.Marshal(b, m, deterministic)
}
func (m *CatProbSaveReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CatProbSaveReq.Merge(m, src)
}
func (m *CatProbSaveReq) XXX_Size() int {
	return xxx_messageInfo_CatProbSaveReq.Size(m)
}
func (m *CatProbSaveReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CatProbSaveReq.DiscardUnknown(m)
}

var xxx_messageInfo_CatProbSaveReq proto.InternalMessageInfo

func (m *CatProbSaveReq) GetCatId() uint32 {
	if m != nil {
		return m.CatId
	}
	return 0
}

func (m *CatProbSaveReq) GetCategory() string {
	if m != nil {
		return m.Category
	}
	return ""
}

func (m *CatProbSaveReq) GetTplId() uint32 {
	if m != nil {
		return m.TplId
	}
	return 0
}

func (m *CatProbSaveReq) GetGroupId() uint32 {
	if m != nil {
		return m.GroupId
	}
	return 0
}

func (m *CatProbSaveReq) GetPriority() uint32 {
	if m != nil {
		return m.Priority
	}
	return 0
}

func (m *CatProbSaveReq) GetDeadline() uint64 {
	if m != nil {
		return m.Deadline
	}
	return 0
}

func (m *CatProbSaveReq) GetCatLevel() uint32 {
	if m != nil {
		return m.CatLevel
	}
	return 0
}

func (m *CatProbSaveReq) GetEvaluation() bool {
	if m != nil {
		return m.Evaluation
	}
	return false
}

func (m *CatProbSaveReq) GetLangs() *Lang {
	if m != nil {
		return m.Langs
	}
	return nil
}

func (m *CatProbSaveReq) GetTplList() []*CatTplPrefer {
	if m != nil {
		return m.TplList
	}
	return nil
}

func (m *CatProbSaveReq) GetReplyTplId() uint32 {
	if m != nil {
		return m.ReplyTplId
	}
	return 0
}

func (m *CatProbSaveReq) GetRelateType() uint32 {
	if m != nil {
		return m.RelateType
	}
	return 0
}

func (m *CatProbSaveReq) GetProcessId() uint32 {
	if m != nil {
		return m.ProcessId
	}
	return 0
}

func (m *CatProbSaveReq) GetReopen() bool {
	if m != nil {
		return m.Reopen
	}
	return false
}

func (m *CatProbSaveReq) GetReopenNum() uint32 {
	if m != nil {
		return m.ReopenNum
	}
	return 0
}

// CatOptsList 问题分类配置(下拉级联多选)列表
type CatOptsListResp struct {
	Data                 []*CatOptsListResp_Cat `protobuf:"bytes,1,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-" gorm:"-"`
	XXX_unrecognized     []byte                 `json:"-" gorm:"-"`
	XXX_sizecache        int32                  `json:"-" gorm:"-"`
}

func (m *CatOptsListResp) Reset()         { *m = CatOptsListResp{} }
func (m *CatOptsListResp) String() string { return proto.CompactTextString(m) }
func (*CatOptsListResp) ProtoMessage()    {}
func (*CatOptsListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_1c6ef5ed29d8d1a1, []int{3}
}

func (m *CatOptsListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CatOptsListResp.Unmarshal(m, b)
}
func (m *CatOptsListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CatOptsListResp.Marshal(b, m, deterministic)
}
func (m *CatOptsListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CatOptsListResp.Merge(m, src)
}
func (m *CatOptsListResp) XXX_Size() int {
	return xxx_messageInfo_CatOptsListResp.Size(m)
}
func (m *CatOptsListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CatOptsListResp.DiscardUnknown(m)
}

var xxx_messageInfo_CatOptsListResp proto.InternalMessageInfo

func (m *CatOptsListResp) GetData() []*CatOptsListResp_Cat {
	if m != nil {
		return m.Data
	}
	return nil
}

type CatOptsListResp_Cat struct {
	Id    uint32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	Label string `protobuf:"bytes,2,opt,name=label,proto3" json:"label"`
	Level uint32 `protobuf:"varint,3,opt,name=level,proto3" json:"level"`
	TplId uint32 `protobuf:"varint,4,opt,name=tpl_id,json=tplId,proto3" json:"tpl_id"`
	// 关联类型 1:表单 2:流程
	RelateType uint32 `protobuf:"varint,5,opt,name=relate_type,json=relateType,proto3" json:"relate_type"`
	// 自动化流程id
	ProcessId uint32 `protobuf:"varint,6,opt,name=process_id,json=processId,proto3" json:"process_id"`
	// 子结构 @gotags: json:"children,omitempty"
	Children             []*CatOptsListResp_Cat `protobuf:"bytes,7,rep,name=children,proto3" json:"children,omitempty"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-" gorm:"-"`
	XXX_unrecognized     []byte                 `json:"-" gorm:"-"`
	XXX_sizecache        int32                  `json:"-" gorm:"-"`
}

func (m *CatOptsListResp_Cat) Reset()         { *m = CatOptsListResp_Cat{} }
func (m *CatOptsListResp_Cat) String() string { return proto.CompactTextString(m) }
func (*CatOptsListResp_Cat) ProtoMessage()    {}
func (*CatOptsListResp_Cat) Descriptor() ([]byte, []int) {
	return fileDescriptor_1c6ef5ed29d8d1a1, []int{3, 0}
}

func (m *CatOptsListResp_Cat) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CatOptsListResp_Cat.Unmarshal(m, b)
}
func (m *CatOptsListResp_Cat) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CatOptsListResp_Cat.Marshal(b, m, deterministic)
}
func (m *CatOptsListResp_Cat) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CatOptsListResp_Cat.Merge(m, src)
}
func (m *CatOptsListResp_Cat) XXX_Size() int {
	return xxx_messageInfo_CatOptsListResp_Cat.Size(m)
}
func (m *CatOptsListResp_Cat) XXX_DiscardUnknown() {
	xxx_messageInfo_CatOptsListResp_Cat.DiscardUnknown(m)
}

var xxx_messageInfo_CatOptsListResp_Cat proto.InternalMessageInfo

func (m *CatOptsListResp_Cat) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *CatOptsListResp_Cat) GetLabel() string {
	if m != nil {
		return m.Label
	}
	return ""
}

func (m *CatOptsListResp_Cat) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *CatOptsListResp_Cat) GetTplId() uint32 {
	if m != nil {
		return m.TplId
	}
	return 0
}

func (m *CatOptsListResp_Cat) GetRelateType() uint32 {
	if m != nil {
		return m.RelateType
	}
	return 0
}

func (m *CatOptsListResp_Cat) GetProcessId() uint32 {
	if m != nil {
		return m.ProcessId
	}
	return 0
}

func (m *CatOptsListResp_Cat) GetChildren() []*CatOptsListResp_Cat {
	if m != nil {
		return m.Children
	}
	return nil
}

// CatIdReq 类别Id 请求参数
type CatIdReq struct {
	// 分类ID @gotags: validate:"required"
	CatId                uint32   `protobuf:"varint,1,opt,name=cat_id,json=catId,proto3" json:"cat_id" validate:"required"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *CatIdReq) Reset()         { *m = CatIdReq{} }
func (m *CatIdReq) String() string { return proto.CompactTextString(m) }
func (*CatIdReq) ProtoMessage()    {}
func (*CatIdReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_1c6ef5ed29d8d1a1, []int{4}
}

func (m *CatIdReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CatIdReq.Unmarshal(m, b)
}
func (m *CatIdReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CatIdReq.Marshal(b, m, deterministic)
}
func (m *CatIdReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CatIdReq.Merge(m, src)
}
func (m *CatIdReq) XXX_Size() int {
	return xxx_messageInfo_CatIdReq.Size(m)
}
func (m *CatIdReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CatIdReq.DiscardUnknown(m)
}

var xxx_messageInfo_CatIdReq proto.InternalMessageInfo

func (m *CatIdReq) GetCatId() uint32 {
	if m != nil {
		return m.CatId
	}
	return 0
}

// CatInfo 类别信息
type CatInfoResp struct {
	CatId uint32 `protobuf:"varint,1,opt,name=cat_id,json=catId,proto3" json:"cat_id"`
	// 分类名称
	Category string `protobuf:"bytes,2,opt,name=category,proto3" json:"category"`
	// 一级分类
	OneLevel uint32 `protobuf:"varint,3,opt,name=one_level,json=oneLevel,proto3" json:"one_level"`
	// 二级分类
	SecondLevel uint32 `protobuf:"varint,4,opt,name=second_level,json=secondLevel,proto3" json:"second_level"`
	// 分类层级
	CatLevel uint32 `protobuf:"varint,5,opt,name=cat_level,json=catLevel,proto3" json:"cat_level"`
	// 模版ID
	TplId uint32 `protobuf:"varint,6,opt,name=tpl_id,json=tplId,proto3" json:"tpl_id"`
	// 紧急度 1:高 2:中 3:低
	Priority uint32 `protobuf:"varint,8,opt,name=priority,proto3" json:"priority"`
	// 完成时间
	Deadline uint64 `protobuf:"varint,9,opt,name=deadline,proto3" json:"deadline"`
	// 启用 0:false 1:true
	Enable bool `protobuf:"varint,10,opt,name=enable,proto3" json:"enable"`
	// 是否评价 是:true 否:false
	Evaluation bool `protobuf:"varint,11,opt,name=evaluation,proto3" json:"evaluation"`
	// op @gotags: json:"operator,omitempty"
	Operator string `protobuf:"bytes,12,opt,name=operator,proto3" json:"operator,omitempty"`
	// 更新时间 @gotags: json:"updated_at,omitempty"
	UpdatedAt string `protobuf:"bytes,13,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	// 多语言 @gotags: json:"langs,omitempty"
	Langs *Lang `protobuf:"bytes,14,opt,name=langs,proto3" json:"langs,omitempty"`
	// 个性化模板配置
	TplList []*CatTplPrefer `protobuf:"bytes,15,rep,name=tpl_list,json=tplList,proto3" json:"tpl_list"`
	// 回复模板id
	ReplyTplId uint32 `protobuf:"varint,16,opt,name=reply_tpl_id,json=replyTplId,proto3" json:"reply_tpl_id"`
	// 关联类型 1:表单 2:流程
	RelateType uint32 `protobuf:"varint,17,opt,name=relate_type,json=relateType,proto3" json:"relate_type"`
	// 流程id
	ProcessId uint32 `protobuf:"varint,18,opt,name=process_id,json=processId,proto3" json:"process_id"`
	// 是否重开 1：是 0：否
	Reopen bool `protobuf:"varint,19,opt,name=reopen,proto3" json:"reopen"`
	// 重开次数
	ReopenNum            uint32   `protobuf:"varint,20,opt,name=reopen_num,json=reopenNum,proto3" json:"reopen_num"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *CatInfoResp) Reset()         { *m = CatInfoResp{} }
func (m *CatInfoResp) String() string { return proto.CompactTextString(m) }
func (*CatInfoResp) ProtoMessage()    {}
func (*CatInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_1c6ef5ed29d8d1a1, []int{5}
}

func (m *CatInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CatInfoResp.Unmarshal(m, b)
}
func (m *CatInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CatInfoResp.Marshal(b, m, deterministic)
}
func (m *CatInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CatInfoResp.Merge(m, src)
}
func (m *CatInfoResp) XXX_Size() int {
	return xxx_messageInfo_CatInfoResp.Size(m)
}
func (m *CatInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CatInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_CatInfoResp proto.InternalMessageInfo

func (m *CatInfoResp) GetCatId() uint32 {
	if m != nil {
		return m.CatId
	}
	return 0
}

func (m *CatInfoResp) GetCategory() string {
	if m != nil {
		return m.Category
	}
	return ""
}

func (m *CatInfoResp) GetOneLevel() uint32 {
	if m != nil {
		return m.OneLevel
	}
	return 0
}

func (m *CatInfoResp) GetSecondLevel() uint32 {
	if m != nil {
		return m.SecondLevel
	}
	return 0
}

func (m *CatInfoResp) GetCatLevel() uint32 {
	if m != nil {
		return m.CatLevel
	}
	return 0
}

func (m *CatInfoResp) GetTplId() uint32 {
	if m != nil {
		return m.TplId
	}
	return 0
}

func (m *CatInfoResp) GetPriority() uint32 {
	if m != nil {
		return m.Priority
	}
	return 0
}

func (m *CatInfoResp) GetDeadline() uint64 {
	if m != nil {
		return m.Deadline
	}
	return 0
}

func (m *CatInfoResp) GetEnable() bool {
	if m != nil {
		return m.Enable
	}
	return false
}

func (m *CatInfoResp) GetEvaluation() bool {
	if m != nil {
		return m.Evaluation
	}
	return false
}

func (m *CatInfoResp) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

func (m *CatInfoResp) GetUpdatedAt() string {
	if m != nil {
		return m.UpdatedAt
	}
	return ""
}

func (m *CatInfoResp) GetLangs() *Lang {
	if m != nil {
		return m.Langs
	}
	return nil
}

func (m *CatInfoResp) GetTplList() []*CatTplPrefer {
	if m != nil {
		return m.TplList
	}
	return nil
}

func (m *CatInfoResp) GetReplyTplId() uint32 {
	if m != nil {
		return m.ReplyTplId
	}
	return 0
}

func (m *CatInfoResp) GetRelateType() uint32 {
	if m != nil {
		return m.RelateType
	}
	return 0
}

func (m *CatInfoResp) GetProcessId() uint32 {
	if m != nil {
		return m.ProcessId
	}
	return 0
}

func (m *CatInfoResp) GetReopen() bool {
	if m != nil {
		return m.Reopen
	}
	return false
}

func (m *CatInfoResp) GetReopenNum() uint32 {
	if m != nil {
		return m.ReopenNum
	}
	return 0
}

// CatInfoListResp 三级类别列表
type CatInfoListResp struct {
	// 当前页
	CurrentPage uint32 `protobuf:"varint,1,opt,name=current_page,json=currentPage,proto3" json:"current_page"`
	// 页大小
	PerPage uint32 `protobuf:"varint,2,opt,name=per_page,json=perPage,proto3" json:"per_page"`
	// 总数
	Total uint32 `protobuf:"varint,3,opt,name=total,proto3" json:"total"`
	// 数据列表
	Data                 []*CatInfoResp `protobuf:"bytes,4,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-" gorm:"-"`
	XXX_unrecognized     []byte         `json:"-" gorm:"-"`
	XXX_sizecache        int32          `json:"-" gorm:"-"`
}

func (m *CatInfoListResp) Reset()         { *m = CatInfoListResp{} }
func (m *CatInfoListResp) String() string { return proto.CompactTextString(m) }
func (*CatInfoListResp) ProtoMessage()    {}
func (*CatInfoListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_1c6ef5ed29d8d1a1, []int{6}
}

func (m *CatInfoListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CatInfoListResp.Unmarshal(m, b)
}
func (m *CatInfoListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CatInfoListResp.Marshal(b, m, deterministic)
}
func (m *CatInfoListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CatInfoListResp.Merge(m, src)
}
func (m *CatInfoListResp) XXX_Size() int {
	return xxx_messageInfo_CatInfoListResp.Size(m)
}
func (m *CatInfoListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CatInfoListResp.DiscardUnknown(m)
}

var xxx_messageInfo_CatInfoListResp proto.InternalMessageInfo

func (m *CatInfoListResp) GetCurrentPage() uint32 {
	if m != nil {
		return m.CurrentPage
	}
	return 0
}

func (m *CatInfoListResp) GetPerPage() uint32 {
	if m != nil {
		return m.PerPage
	}
	return 0
}

func (m *CatInfoListResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *CatInfoListResp) GetData() []*CatInfoResp {
	if m != nil {
		return m.Data
	}
	return nil
}

type CatOptsReq struct {
	// 项目 @gotags: validate:"required"
	Project string `protobuf:"bytes,1,opt,name=project,proto3" json:"project" validate:"required"`
	// 语言
	Lang                 string   `protobuf:"bytes,2,opt,name=lang,proto3" json:"lang"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *CatOptsReq) Reset()         { *m = CatOptsReq{} }
func (m *CatOptsReq) String() string { return proto.CompactTextString(m) }
func (*CatOptsReq) ProtoMessage()    {}
func (*CatOptsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_1c6ef5ed29d8d1a1, []int{7}
}

func (m *CatOptsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CatOptsReq.Unmarshal(m, b)
}
func (m *CatOptsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CatOptsReq.Marshal(b, m, deterministic)
}
func (m *CatOptsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CatOptsReq.Merge(m, src)
}
func (m *CatOptsReq) XXX_Size() int {
	return xxx_messageInfo_CatOptsReq.Size(m)
}
func (m *CatOptsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CatOptsReq.DiscardUnknown(m)
}

var xxx_messageInfo_CatOptsReq proto.InternalMessageInfo

func (m *CatOptsReq) GetProject() string {
	if m != nil {
		return m.Project
	}
	return ""
}

func (m *CatOptsReq) GetLang() string {
	if m != nil {
		return m.Lang
	}
	return ""
}

// CatOptsResp 问题分类配置(下拉级联多选)列表
type CatOptsResp struct {
	Data                 []*CatOptsResp_Cat `protobuf:"bytes,1,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-" gorm:"-"`
	XXX_unrecognized     []byte             `json:"-" gorm:"-"`
	XXX_sizecache        int32              `json:"-" gorm:"-"`
}

func (m *CatOptsResp) Reset()         { *m = CatOptsResp{} }
func (m *CatOptsResp) String() string { return proto.CompactTextString(m) }
func (*CatOptsResp) ProtoMessage()    {}
func (*CatOptsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_1c6ef5ed29d8d1a1, []int{8}
}

func (m *CatOptsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CatOptsResp.Unmarshal(m, b)
}
func (m *CatOptsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CatOptsResp.Marshal(b, m, deterministic)
}
func (m *CatOptsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CatOptsResp.Merge(m, src)
}
func (m *CatOptsResp) XXX_Size() int {
	return xxx_messageInfo_CatOptsResp.Size(m)
}
func (m *CatOptsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_CatOptsResp.DiscardUnknown(m)
}

var xxx_messageInfo_CatOptsResp proto.InternalMessageInfo

func (m *CatOptsResp) GetData() []*CatOptsResp_Cat {
	if m != nil {
		return m.Data
	}
	return nil
}

type CatOptsResp_Cat struct {
	Id    uint32 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	Label string `protobuf:"bytes,2,opt,name=label,proto3" json:"label"`
	Level uint32 `protobuf:"varint,3,opt,name=level,proto3" json:"level"`
	// 子结构 @gotags: json:"children,omitempty"
	Children             []*CatOptsResp_Cat `protobuf:"bytes,7,rep,name=children,proto3" json:"children,omitempty"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-" gorm:"-"`
	XXX_unrecognized     []byte             `json:"-" gorm:"-"`
	XXX_sizecache        int32              `json:"-" gorm:"-"`
}

func (m *CatOptsResp_Cat) Reset()         { *m = CatOptsResp_Cat{} }
func (m *CatOptsResp_Cat) String() string { return proto.CompactTextString(m) }
func (*CatOptsResp_Cat) ProtoMessage()    {}
func (*CatOptsResp_Cat) Descriptor() ([]byte, []int) {
	return fileDescriptor_1c6ef5ed29d8d1a1, []int{8, 0}
}

func (m *CatOptsResp_Cat) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CatOptsResp_Cat.Unmarshal(m, b)
}
func (m *CatOptsResp_Cat) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CatOptsResp_Cat.Marshal(b, m, deterministic)
}
func (m *CatOptsResp_Cat) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CatOptsResp_Cat.Merge(m, src)
}
func (m *CatOptsResp_Cat) XXX_Size() int {
	return xxx_messageInfo_CatOptsResp_Cat.Size(m)
}
func (m *CatOptsResp_Cat) XXX_DiscardUnknown() {
	xxx_messageInfo_CatOptsResp_Cat.DiscardUnknown(m)
}

var xxx_messageInfo_CatOptsResp_Cat proto.InternalMessageInfo

func (m *CatOptsResp_Cat) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *CatOptsResp_Cat) GetLabel() string {
	if m != nil {
		return m.Label
	}
	return ""
}

func (m *CatOptsResp_Cat) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *CatOptsResp_Cat) GetChildren() []*CatOptsResp_Cat {
	if m != nil {
		return m.Children
	}
	return nil
}

// CatItems cat item
type CatItems struct {
	CatId                uint32   `protobuf:"varint,1,opt,name=cat_id,json=catId,proto3" json:"cat_id"`
	OneLevel             uint32   `protobuf:"varint,2,opt,name=one_level,json=oneLevel,proto3" json:"one_level"`
	SecondLevel          uint32   `protobuf:"varint,3,opt,name=second_level,json=secondLevel,proto3" json:"second_level"`
	Level                uint32   `protobuf:"varint,4,opt,name=level,proto3" json:"level"`
	Category             string   `protobuf:"bytes,6,opt,name=category,proto3" json:"category"`
	LangCategory         string   `protobuf:"bytes,7,opt,name=lang_category,json=langCategory,proto3" json:"lang_category"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *CatItems) Reset()         { *m = CatItems{} }
func (m *CatItems) String() string { return proto.CompactTextString(m) }
func (*CatItems) ProtoMessage()    {}
func (*CatItems) Descriptor() ([]byte, []int) {
	return fileDescriptor_1c6ef5ed29d8d1a1, []int{9}
}

func (m *CatItems) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CatItems.Unmarshal(m, b)
}
func (m *CatItems) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CatItems.Marshal(b, m, deterministic)
}
func (m *CatItems) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CatItems.Merge(m, src)
}
func (m *CatItems) XXX_Size() int {
	return xxx_messageInfo_CatItems.Size(m)
}
func (m *CatItems) XXX_DiscardUnknown() {
	xxx_messageInfo_CatItems.DiscardUnknown(m)
}

var xxx_messageInfo_CatItems proto.InternalMessageInfo

func (m *CatItems) GetCatId() uint32 {
	if m != nil {
		return m.CatId
	}
	return 0
}

func (m *CatItems) GetOneLevel() uint32 {
	if m != nil {
		return m.OneLevel
	}
	return 0
}

func (m *CatItems) GetSecondLevel() uint32 {
	if m != nil {
		return m.SecondLevel
	}
	return 0
}

func (m *CatItems) GetLevel() uint32 {
	if m != nil {
		return m.Level
	}
	return 0
}

func (m *CatItems) GetCategory() string {
	if m != nil {
		return m.Category
	}
	return ""
}

func (m *CatItems) GetLangCategory() string {
	if m != nil {
		return m.LangCategory
	}
	return ""
}

type CatProjectLang struct {
	// 项目 @gotags: validate:"required"
	Project string `protobuf:"bytes,1,opt,name=project,proto3" json:"project" validate:"required"`
	// 语言
	Lang string `protobuf:"bytes,2,opt,name=lang,proto3" json:"lang"`
	// 范围 0:默认 1:内部分类
	Scope                CatScopeEnum `protobuf:"varint,3,opt,name=scope,proto3,enum=pb.CatScopeEnum" json:"scope"`
	XXX_NoUnkeyedLiteral struct{}     `json:"-" gorm:"-"`
	XXX_unrecognized     []byte       `json:"-" gorm:"-"`
	XXX_sizecache        int32        `json:"-" gorm:"-"`
}

func (m *CatProjectLang) Reset()         { *m = CatProjectLang{} }
func (m *CatProjectLang) String() string { return proto.CompactTextString(m) }
func (*CatProjectLang) ProtoMessage()    {}
func (*CatProjectLang) Descriptor() ([]byte, []int) {
	return fileDescriptor_1c6ef5ed29d8d1a1, []int{10}
}

func (m *CatProjectLang) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CatProjectLang.Unmarshal(m, b)
}
func (m *CatProjectLang) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CatProjectLang.Marshal(b, m, deterministic)
}
func (m *CatProjectLang) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CatProjectLang.Merge(m, src)
}
func (m *CatProjectLang) XXX_Size() int {
	return xxx_messageInfo_CatProjectLang.Size(m)
}
func (m *CatProjectLang) XXX_DiscardUnknown() {
	xxx_messageInfo_CatProjectLang.DiscardUnknown(m)
}

var xxx_messageInfo_CatProjectLang proto.InternalMessageInfo

func (m *CatProjectLang) GetProject() string {
	if m != nil {
		return m.Project
	}
	return ""
}

func (m *CatProjectLang) GetLang() string {
	if m != nil {
		return m.Lang
	}
	return ""
}

func (m *CatProjectLang) GetScope() CatScopeEnum {
	if m != nil {
		return m.Scope
	}
	return CatScopeEnum_CatScopeDefault
}

func init() {
	proto.RegisterEnum("pb.CatLevel", CatLevel_name, CatLevel_value)
	proto.RegisterEnum("pb.CatScopeEnum", CatScopeEnum_name, CatScopeEnum_value)
	proto.RegisterType((*Lang)(nil), "pb.Lang")
	proto.RegisterMapType((map[string]string)(nil), "pb.Lang.CategoryEntry")
	proto.RegisterType((*CatTplPrefer)(nil), "pb.CatTplPrefer")
	proto.RegisterMapType((map[string]bool)(nil), "pb.CatTplPrefer.MarkEntry")
	proto.RegisterType((*CatProbSaveReq)(nil), "pb.CatProbSaveReq")
	proto.RegisterType((*CatOptsListResp)(nil), "pb.CatOptsListResp")
	proto.RegisterType((*CatOptsListResp_Cat)(nil), "pb.CatOptsListResp.Cat")
	proto.RegisterType((*CatIdReq)(nil), "pb.CatIdReq")
	proto.RegisterType((*CatInfoResp)(nil), "pb.CatInfoResp")
	proto.RegisterType((*CatInfoListResp)(nil), "pb.CatInfoListResp")
	proto.RegisterType((*CatOptsReq)(nil), "pb.CatOptsReq")
	proto.RegisterType((*CatOptsResp)(nil), "pb.CatOptsResp")
	proto.RegisterType((*CatOptsResp_Cat)(nil), "pb.CatOptsResp.Cat")
	proto.RegisterType((*CatItems)(nil), "pb.CatItems")
	proto.RegisterType((*CatProjectLang)(nil), "pb.CatProjectLang")
}

func init() {
	proto.RegisterFile("category.proto", fileDescriptor_1c6ef5ed29d8d1a1)
}

var fileDescriptor_1c6ef5ed29d8d1a1 = []byte{
	// 1127 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xa4, 0x56, 0xcd, 0x6e, 0x23, 0x45,
	0x17, 0x9d, 0xf6, 0x6f, 0xf7, 0xb5, 0xe3, 0x78, 0x2a, 0x7f, 0x3d, 0xce, 0xf7, 0x0d, 0x89, 0x47,
	0x82, 0x28, 0x23, 0x39, 0x52, 0x66, 0x01, 0xca, 0xac, 0x32, 0x26, 0x8b, 0x40, 0x06, 0xac, 0x4e,
	0xd8, 0xb0, 0xb1, 0xca, 0xdd, 0x37, 0x4e, 0x93, 0x76, 0x55, 0x51, 0x5d, 0xce, 0xc8, 0x5b, 0x96,
	0x2c, 0xd8, 0xf0, 0x16, 0x3c, 0x01, 0xef, 0x80, 0x90, 0x90, 0x78, 0x02, 0x24, 0xde, 0x03, 0x54,
	0x55, 0xed, 0x4e, 0xc7, 0x21, 0xb1, 0x06, 0xb2, 0xea, 0x7b, 0xee, 0xad, 0xca, 0x3d, 0xb7, 0xce,
	0x29, 0x17, 0xb4, 0x42, 0xaa, 0x70, 0xcc, 0xe5, 0xac, 0x27, 0x24, 0x57, 0x9c, 0x94, 0xc4, 0xa8,
	0xf3, 0xbf, 0x31, 0xe7, 0xe3, 0x04, 0x0f, 0xa8, 0x88, 0x0f, 0x28, 0x63, 0x5c, 0x51, 0x15, 0x73,
	0x96, 0xda, 0x8a, 0x4e, 0x33, 0xe4, 0x93, 0x09, 0x67, 0x36, 0xea, 0xbe, 0x83, 0xca, 0x19, 0x65,
	0x63, 0x72, 0x08, 0xee, 0x7c, 0x27, 0xdf, 0xd9, 0x29, 0xef, 0x35, 0x0e, 0x37, 0x7b, 0x62, 0xd4,
	0xd3, 0xb9, 0x5e, 0x3f, 0x4b, 0x9c, 0x30, 0x25, 0x67, 0x41, 0x5e, 0xd7, 0x79, 0x0d, 0x2b, 0x77,
	0x52, 0xa4, 0x0d, 0xe5, 0x6b, 0xd4, 0xeb, 0x9d, 0x3d, 0x2f, 0xd0, 0x9f, 0x64, 0x1d, 0xaa, 0x37,
	0x34, 0x99, 0xa2, 0x5f, 0x32, 0x98, 0x0d, 0x8e, 0x4a, 0x9f, 0x38, 0xdd, 0x3f, 0x1c, 0x68, 0xf6,
	0xa9, 0xba, 0x10, 0xc9, 0x40, 0xe2, 0x25, 0x4a, 0xe2, 0x43, 0x3d, 0xbc, 0xa2, 0x8c, 0x61, 0x62,
	0x1a, 0xf0, 0x82, 0x79, 0x48, 0xfe, 0x0f, 0x90, 0xa2, 0xbc, 0x41, 0x39, 0x4c, 0x95, 0xcc, 0x76,
	0xf2, 0x2c, 0x72, 0xae, 0xec, 0x42, 0x3e, 0xd5, 0x0d, 0xf8, 0xe5, 0x6c, 0xa1, 0x0d, 0xc9, 0x06,
	0xd4, 0x94, 0x48, 0x86, 0x71, 0xe4, 0x57, 0x76, 0x9c, 0xbd, 0x95, 0xa0, 0xaa, 0x44, 0x72, 0x1a,
	0x91, 0x1e, 0x54, 0x26, 0x54, 0x5e, 0xfb, 0x55, 0xc3, 0xb3, 0xa3, 0x79, 0x16, 0x3b, 0xe9, 0xbd,
	0xa5, 0xf2, 0xda, 0x72, 0x35, 0x75, 0x9d, 0x8f, 0xc1, 0xcb, 0xa1, 0x65, 0x1c, 0xdd, 0x22, 0xc7,
	0xdf, 0xca, 0xd0, 0xea, 0x53, 0x35, 0x90, 0x7c, 0x74, 0x4e, 0x6f, 0x30, 0xc0, 0x6f, 0x75, 0x4b,
	0x21, 0x55, 0xba, 0x25, 0xc7, 0xb6, 0x14, 0x52, 0x75, 0x1a, 0x91, 0x4e, 0x61, 0xfc, 0x96, 0x60,
	0x1e, 0x17, 0x58, 0x94, 0x8b, 0x2c, 0x9e, 0x81, 0x3b, 0x96, 0x7c, 0x2a, 0x6e, 0xe9, 0xd5, 0x4d,
	0x6c, 0x77, 0x13, 0x32, 0xe6, 0x32, 0x56, 0x33, 0xbf, 0x6a, 0x52, 0x79, 0xac, 0x73, 0x11, 0xd2,
	0x28, 0x89, 0x19, 0xfa, 0xb5, 0x1d, 0x67, 0xaf, 0x12, 0xe4, 0x31, 0xd9, 0x06, 0x4f, 0x37, 0x97,
	0xe0, 0x0d, 0x26, 0x7e, 0xdd, 0x2e, 0x0c, 0xa9, 0x3a, 0xd3, 0x31, 0x79, 0x0e, 0x80, 0x9a, 0x9a,
	0x11, 0x93, 0xef, 0x1a, 0xae, 0x05, 0x84, 0x3c, 0x87, 0x6a, 0x42, 0xd9, 0x38, 0xf5, 0xbd, 0x1d,
	0x67, 0xaf, 0x71, 0xe8, 0xce, 0xe5, 0x13, 0x58, 0x98, 0xbc, 0x04, 0x57, 0xd3, 0x48, 0xe2, 0x54,
	0xf9, 0x60, 0x26, 0xdf, 0x5e, 0x9c, 0x7c, 0x50, 0x57, 0x22, 0x39, 0x8b, 0x53, 0x45, 0x76, 0xa0,
	0x29, 0x51, 0x24, 0xb3, 0x61, 0xc6, 0xbc, 0x61, 0x9a, 0x01, 0x83, 0x5d, 0x18, 0xfa, 0x1f, 0x40,
	0x43, 0x62, 0x42, 0x15, 0x0e, 0xd5, 0x4c, 0xa0, 0xdf, 0x9c, 0x17, 0x68, 0xe8, 0x62, 0x26, 0x50,
	0xab, 0x46, 0x48, 0x1e, 0x62, 0x9a, 0xea, 0x0d, 0x56, 0x4c, 0xde, 0xcb, 0x90, 0xd3, 0x88, 0x6c,
	0x42, 0x4d, 0x22, 0x17, 0xc8, 0xfc, 0x96, 0xa1, 0x92, 0x45, 0x7a, 0x99, 0xfd, 0x1a, 0xb2, 0xe9,
	0xc4, 0x5f, 0xb5, 0xcb, 0x2c, 0xf2, 0xc5, 0x74, 0xd2, 0xfd, 0xa1, 0x04, 0xab, 0x7d, 0xaa, 0xbe,
	0x14, 0x2a, 0xd5, 0x8d, 0x06, 0x98, 0x0a, 0xf2, 0x12, 0x2a, 0x11, 0x55, 0x34, 0xf3, 0xcd, 0x56,
	0xc6, 0xaa, 0x58, 0xa2, 0xe3, 0xc0, 0x14, 0x75, 0x7e, 0x75, 0xa0, 0xdc, 0xa7, 0x8a, 0xb4, 0xa0,
	0x94, 0x8b, 0xa0, 0x14, 0x47, 0x5a, 0x45, 0x09, 0x1d, 0x61, 0x32, 0x77, 0x8a, 0x09, 0x0c, 0x6a,
	0x4e, 0x23, 0x3b, 0x7a, 0x13, 0x3c, 0xa4, 0xeb, 0x85, 0x91, 0x54, 0x97, 0x8c, 0xa4, 0xb6, 0x38,
	0x92, 0x57, 0xe0, 0x86, 0x57, 0x71, 0x12, 0x49, 0x64, 0x7e, 0xfd, 0x71, 0x2e, 0x79, 0x61, 0x77,
	0x17, 0xdc, 0xbe, 0x96, 0xf0, 0xc3, 0xe2, 0xee, 0xfe, 0x52, 0x81, 0x86, 0xae, 0x61, 0x97, 0xdc,
	0xcc, 0xeb, 0x5f, 0x78, 0x60, 0x1b, 0x3c, 0xce, 0x70, 0x58, 0x9c, 0x85, 0xcb, 0x19, 0x5a, 0x65,
	0xee, 0x42, 0x33, 0xc5, 0x90, 0xb3, 0x28, 0xcb, 0xdb, 0xa1, 0x34, 0x2c, 0x66, 0x4b, 0xee, 0x28,
	0xbb, 0xba, 0xa0, 0xec, 0xdb, 0x71, 0xd6, 0x8a, 0xe3, 0x2c, 0xba, 0xc8, 0x7d, 0xc4, 0x45, 0xde,
	0x82, 0x8b, 0x36, 0xa1, 0x86, 0x8c, 0x8e, 0x12, 0xf4, 0xc1, 0x2a, 0xcb, 0x46, 0x0b, 0x06, 0x6a,
	0xdc, 0x33, 0x50, 0x07, 0x5c, 0x2e, 0x50, 0x52, 0xc5, 0xa5, 0x91, 0xb3, 0x17, 0xe4, 0xb1, 0x3e,
	0xb9, 0xa9, 0x88, 0xa8, 0xc2, 0x68, 0x48, 0x95, 0x11, 0xb3, 0x17, 0x78, 0x19, 0x72, 0xac, 0x6e,
	0xbd, 0xd7, 0x5a, 0xee, 0xbd, 0xd5, 0xf7, 0xf5, 0x5e, 0x7b, 0x99, 0xf7, 0x9e, 0x2e, 0x11, 0x1a,
	0x79, 0xd8, 0x7b, 0x6b, 0x8f, 0x78, 0x6f, 0x7d, 0xc1, 0x7b, 0x9f, 0x55, 0xdc, 0x7a, 0xdb, 0xed,
	0x7e, 0xef, 0x18, 0x07, 0x6a, 0x35, 0xe5, 0x0e, 0xdc, 0x85, 0x66, 0x38, 0x95, 0x12, 0x99, 0x1a,
	0x0a, 0x3a, 0xc6, 0x4c, 0x57, 0x8d, 0x0c, 0x1b, 0xd0, 0x31, 0xea, 0xeb, 0x52, 0xa0, 0xb4, 0xe9,
	0x92, 0xbd, 0x2e, 0x05, 0x4a, 0x93, 0x5a, 0x87, 0xaa, 0xe2, 0x8a, 0xe6, 0x26, 0x33, 0x01, 0x79,
	0x91, 0xb9, 0xba, 0x62, 0xe6, 0xb5, 0x9a, 0xcd, 0x6b, 0x2e, 0x62, 0xeb, 0xe6, 0xee, 0x11, 0x40,
	0x66, 0x0f, 0xad, 0x7f, 0x1f, 0xea, 0x42, 0xf2, 0x6f, 0x30, 0x54, 0xd9, 0xef, 0xc3, 0x3c, 0x24,
	0x04, 0x2a, 0xfa, 0x24, 0x32, 0x5d, 0x9b, 0xef, 0xee, 0x4f, 0x8e, 0xb1, 0x85, 0x5d, 0x9c, 0x0a,
	0xf2, 0xd1, 0x9d, 0x6b, 0x64, 0xad, 0x60, 0xbd, 0x85, 0x2b, 0x44, 0xfe, 0xf7, 0x1b, 0xe4, 0xe0,
	0x9e, 0xd5, 0xff, 0xf1, 0xff, 0xdd, 0xda, 0xfc, 0x67, 0xc7, 0xfa, 0x5c, 0xe1, 0x24, 0x7d, 0xc8,
	0xc0, 0x77, 0x4c, 0x5a, 0x5a, 0x62, 0xd2, 0xf2, 0x7d, 0x93, 0xe6, 0xad, 0x56, 0x8a, 0xad, 0x16,
	0xaf, 0x85, 0xda, 0xc2, 0xb5, 0xf0, 0x02, 0x56, 0xf4, 0x28, 0x87, 0x79, 0x41, 0xdd, 0x14, 0x34,
	0x35, 0x38, 0x7f, 0x9a, 0x74, 0x2f, 0xe7, 0x3f, 0xc2, 0xfa, 0x24, 0xcc, 0x63, 0xe7, 0xbd, 0xce,
	0x89, 0x7c, 0x08, 0xd5, 0x34, 0xe4, 0x02, 0x4d, 0xcb, 0xad, 0xdc, 0x39, 0xe7, 0x1a, 0x3b, 0x61,
	0xd3, 0x49, 0x60, 0xd3, 0xfb, 0x57, 0x66, 0x42, 0x96, 0x4a, 0x03, 0xea, 0x5f, 0xb1, 0xcf, 0x19,
	0x7f, 0xc7, 0xda, 0x4f, 0xc8, 0x33, 0xd8, 0x18, 0xc8, 0x78, 0x42, 0xe5, 0xac, 0x9f, 0xd0, 0x34,
	0x8d, 0x2f, 0xe3, 0xd0, 0x38, 0xbe, 0xed, 0x90, 0x6d, 0xd8, 0x3a, 0x37, 0x13, 0xb8, 0x9f, 0x2c,
	0x91, 0x2d, 0x58, 0xbb, 0xb8, 0x92, 0x88, 0x0b, 0x89, 0xf2, 0xfe, 0xc0, 0x3c, 0x9d, 0xf2, 0x06,
	0xc8, 0x9a, 0x71, 0x84, 0x89, 0x3f, 0xc5, 0x4b, 0x3a, 0x4d, 0x54, 0xfb, 0x09, 0x79, 0x6a, 0x5e,
	0x67, 0x06, 0x3c, 0x65, 0x0c, 0x65, 0xdb, 0x21, 0xbe, 0x11, 0x9c, 0x81, 0x8e, 0x93, 0xa4, 0xfd,
	0xd7, 0xfc, 0xcf, 0x39, 0x0c, 0x81, 0xcc, 0xe7, 0xf5, 0x86, 0x86, 0xd7, 0xc8, 0xa2, 0xe3, 0xc1,
	0x29, 0x79, 0x6b, 0xf6, 0x35, 0x68, 0x80, 0x09, 0xd2, 0x14, 0x09, 0xc9, 0xd8, 0x17, 0xc6, 0xd9,
	0xf1, 0x34, 0x76, 0x32, 0x11, 0x6a, 0xd6, 0xdd, 0xfe, 0xee, 0xf7, 0x3f, 0x7f, 0x2c, 0x6d, 0x74,
	0xdb, 0xe6, 0xf1, 0x19, 0x52, 0x75, 0x20, 0xed, 0xc2, 0x23, 0x67, 0xff, 0x4d, 0xed, 0xeb, 0x4a,
	0xef, 0xb5, 0x18, 0x8d, 0x6a, 0xe6, 0xe9, 0xf9, 0xea, 0xef, 0x00, 0x00, 0x00, 0xff, 0xff, 0x25,
	0x93, 0x6b, 0x0c, 0xbc, 0x0a, 0x00, 0x00,
}
