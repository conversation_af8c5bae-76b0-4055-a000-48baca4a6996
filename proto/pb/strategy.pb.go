// Code generated by protoc-gen-go. DO NOT EDIT.
// source: strategy.proto

package pb

import (
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

type StrategySplit struct {
	Ids                  []int64                 `protobuf:"varint,1,rep,packed,name=ids,proto3" json:"ids"`
	Btw                  []*StrategySplit_ObjBtw `protobuf:"bytes,2,rep,name=btw,proto3" json:"btw"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-" gorm:"-"`
	XXX_unrecognized     []byte                  `json:"-" gorm:"-"`
	XXX_sizecache        int32                   `json:"-" gorm:"-"`
}

func (m *StrategySplit) Reset()         { *m = StrategySplit{} }
func (m *StrategySplit) String() string { return proto.CompactTextString(m) }
func (*StrategySplit) ProtoMessage()    {}
func (*StrategySplit) Descriptor() ([]byte, []int) {
	return fileDescriptor_46ec5ce6dd46feab, []int{0}
}

func (m *StrategySplit) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StrategySplit.Unmarshal(m, b)
}
func (m *StrategySplit) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StrategySplit.Marshal(b, m, deterministic)
}
func (m *StrategySplit) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StrategySplit.Merge(m, src)
}
func (m *StrategySplit) XXX_Size() int {
	return xxx_messageInfo_StrategySplit.Size(m)
}
func (m *StrategySplit) XXX_DiscardUnknown() {
	xxx_messageInfo_StrategySplit.DiscardUnknown(m)
}

var xxx_messageInfo_StrategySplit proto.InternalMessageInfo

func (m *StrategySplit) GetIds() []int64 {
	if m != nil {
		return m.Ids
	}
	return nil
}

func (m *StrategySplit) GetBtw() []*StrategySplit_ObjBtw {
	if m != nil {
		return m.Btw
	}
	return nil
}

type StrategySplit_ObjBtw struct {
	Start                int64    `protobuf:"varint,1,opt,name=start,proto3" json:"start"`
	End                  int64    `protobuf:"varint,2,opt,name=end,proto3" json:"end"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *StrategySplit_ObjBtw) Reset()         { *m = StrategySplit_ObjBtw{} }
func (m *StrategySplit_ObjBtw) String() string { return proto.CompactTextString(m) }
func (*StrategySplit_ObjBtw) ProtoMessage()    {}
func (*StrategySplit_ObjBtw) Descriptor() ([]byte, []int) {
	return fileDescriptor_46ec5ce6dd46feab, []int{0, 0}
}

func (m *StrategySplit_ObjBtw) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StrategySplit_ObjBtw.Unmarshal(m, b)
}
func (m *StrategySplit_ObjBtw) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StrategySplit_ObjBtw.Marshal(b, m, deterministic)
}
func (m *StrategySplit_ObjBtw) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StrategySplit_ObjBtw.Merge(m, src)
}
func (m *StrategySplit_ObjBtw) XXX_Size() int {
	return xxx_messageInfo_StrategySplit_ObjBtw.Size(m)
}
func (m *StrategySplit_ObjBtw) XXX_DiscardUnknown() {
	xxx_messageInfo_StrategySplit_ObjBtw.DiscardUnknown(m)
}

var xxx_messageInfo_StrategySplit_ObjBtw proto.InternalMessageInfo

func (m *StrategySplit_ObjBtw) GetStart() int64 {
	if m != nil {
		return m.Start
	}
	return 0
}

func (m *StrategySplit_ObjBtw) GetEnd() int64 {
	if m != nil {
		return m.End
	}
	return 0
}

type StrategyFilters struct {
	IsSeverAll           bool           `protobuf:"varint,1,opt,name=IsSeverAll,proto3" json:"IsSeverAll"`
	Server               *StrategySplit `protobuf:"bytes,2,opt,name=server,proto3" json:"server"`
	PayRange             *StrategySplit `protobuf:"bytes,3,opt,name=pay_range,json=payRange,proto3" json:"pay_range"`
	CastleLevel          *StrategySplit `protobuf:"bytes,4,opt,name=castle_level,json=castleLevel,proto3" json:"castle_level"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-" gorm:"-"`
	XXX_unrecognized     []byte         `json:"-" gorm:"-"`
	XXX_sizecache        int32          `json:"-" gorm:"-"`
}

func (m *StrategyFilters) Reset()         { *m = StrategyFilters{} }
func (m *StrategyFilters) String() string { return proto.CompactTextString(m) }
func (*StrategyFilters) ProtoMessage()    {}
func (*StrategyFilters) Descriptor() ([]byte, []int) {
	return fileDescriptor_46ec5ce6dd46feab, []int{1}
}

func (m *StrategyFilters) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StrategyFilters.Unmarshal(m, b)
}
func (m *StrategyFilters) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StrategyFilters.Marshal(b, m, deterministic)
}
func (m *StrategyFilters) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StrategyFilters.Merge(m, src)
}
func (m *StrategyFilters) XXX_Size() int {
	return xxx_messageInfo_StrategyFilters.Size(m)
}
func (m *StrategyFilters) XXX_DiscardUnknown() {
	xxx_messageInfo_StrategyFilters.DiscardUnknown(m)
}

var xxx_messageInfo_StrategyFilters proto.InternalMessageInfo

func (m *StrategyFilters) GetIsSeverAll() bool {
	if m != nil {
		return m.IsSeverAll
	}
	return false
}

func (m *StrategyFilters) GetServer() *StrategySplit {
	if m != nil {
		return m.Server
	}
	return nil
}

func (m *StrategyFilters) GetPayRange() *StrategySplit {
	if m != nil {
		return m.PayRange
	}
	return nil
}

func (m *StrategyFilters) GetCastleLevel() *StrategySplit {
	if m != nil {
		return m.CastleLevel
	}
	return nil
}

// StrategyAddReq 策略新增
type StrategyAddReq struct {
	// 策略id
	StrategyId uint64 `protobuf:"varint,1,opt,name=strategy_id,json=strategyId,proto3" json:"strategy_id"`
	// @gotags: validate:"required"
	Project string `protobuf:"bytes,2,opt,name=project,proto3" json:"project" validate:"required"`
	// @gotags: validate:"required"
	StrategyName      string `protobuf:"bytes,3,opt,name=strategy_name,json=strategyName,proto3" json:"strategy_name" validate:"required"`
	FilterServerLists string `protobuf:"bytes,5,opt,name=filter_server_lists,json=filterServerLists,proto3" json:"filter_server_lists"`
	// @gotags: validate:"required"
	FilterPayRangeLists    string `protobuf:"bytes,6,opt,name=filter_pay_range_lists,json=filterPayRangeLists,proto3" json:"filter_pay_range_lists" validate:"required"`
	FilterCastleLevelLists string `protobuf:"bytes,7,opt,name=filter_castle_level_lists,json=filterCastleLevelLists,proto3" json:"filter_castle_level_lists"`
	// 服务器是否为全部  @gotags: validate:"required"
	Type                 uint32   `protobuf:"varint,8,opt,name=type,proto3" json:"type" validate:"required"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *StrategyAddReq) Reset()         { *m = StrategyAddReq{} }
func (m *StrategyAddReq) String() string { return proto.CompactTextString(m) }
func (*StrategyAddReq) ProtoMessage()    {}
func (*StrategyAddReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_46ec5ce6dd46feab, []int{2}
}

func (m *StrategyAddReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StrategyAddReq.Unmarshal(m, b)
}
func (m *StrategyAddReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StrategyAddReq.Marshal(b, m, deterministic)
}
func (m *StrategyAddReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StrategyAddReq.Merge(m, src)
}
func (m *StrategyAddReq) XXX_Size() int {
	return xxx_messageInfo_StrategyAddReq.Size(m)
}
func (m *StrategyAddReq) XXX_DiscardUnknown() {
	xxx_messageInfo_StrategyAddReq.DiscardUnknown(m)
}

var xxx_messageInfo_StrategyAddReq proto.InternalMessageInfo

func (m *StrategyAddReq) GetStrategyId() uint64 {
	if m != nil {
		return m.StrategyId
	}
	return 0
}

func (m *StrategyAddReq) GetProject() string {
	if m != nil {
		return m.Project
	}
	return ""
}

func (m *StrategyAddReq) GetStrategyName() string {
	if m != nil {
		return m.StrategyName
	}
	return ""
}

func (m *StrategyAddReq) GetFilterServerLists() string {
	if m != nil {
		return m.FilterServerLists
	}
	return ""
}

func (m *StrategyAddReq) GetFilterPayRangeLists() string {
	if m != nil {
		return m.FilterPayRangeLists
	}
	return ""
}

func (m *StrategyAddReq) GetFilterCastleLevelLists() string {
	if m != nil {
		return m.FilterCastleLevelLists
	}
	return ""
}

func (m *StrategyAddReq) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

// StrategyDelReq 策略删除
type StrategyDelReq struct {
	// @gotags: validate:"required"
	StrategyId           int64    `protobuf:"varint,1,opt,name=strategy_id,json=strategyId,proto3" json:"strategy_id" validate:"required"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *StrategyDelReq) Reset()         { *m = StrategyDelReq{} }
func (m *StrategyDelReq) String() string { return proto.CompactTextString(m) }
func (*StrategyDelReq) ProtoMessage()    {}
func (*StrategyDelReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_46ec5ce6dd46feab, []int{3}
}

func (m *StrategyDelReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StrategyDelReq.Unmarshal(m, b)
}
func (m *StrategyDelReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StrategyDelReq.Marshal(b, m, deterministic)
}
func (m *StrategyDelReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StrategyDelReq.Merge(m, src)
}
func (m *StrategyDelReq) XXX_Size() int {
	return xxx_messageInfo_StrategyDelReq.Size(m)
}
func (m *StrategyDelReq) XXX_DiscardUnknown() {
	xxx_messageInfo_StrategyDelReq.DiscardUnknown(m)
}

var xxx_messageInfo_StrategyDelReq proto.InternalMessageInfo

func (m *StrategyDelReq) GetStrategyId() int64 {
	if m != nil {
		return m.StrategyId
	}
	return 0
}

// StrategyEnabelReq 策略禁/启用
type StrategyEnabelReq struct {
	// @gotags: validate:"required"
	StrategyId int64 `protobuf:"varint,1,opt,name=strategy_id,json=strategyId,proto3" json:"strategy_id" validate:"required"`
	// 启用 true false
	Enable               uint32   `protobuf:"varint,2,opt,name=enable,proto3" json:"enable"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *StrategyEnabelReq) Reset()         { *m = StrategyEnabelReq{} }
func (m *StrategyEnabelReq) String() string { return proto.CompactTextString(m) }
func (*StrategyEnabelReq) ProtoMessage()    {}
func (*StrategyEnabelReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_46ec5ce6dd46feab, []int{4}
}

func (m *StrategyEnabelReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StrategyEnabelReq.Unmarshal(m, b)
}
func (m *StrategyEnabelReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StrategyEnabelReq.Marshal(b, m, deterministic)
}
func (m *StrategyEnabelReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StrategyEnabelReq.Merge(m, src)
}
func (m *StrategyEnabelReq) XXX_Size() int {
	return xxx_messageInfo_StrategyEnabelReq.Size(m)
}
func (m *StrategyEnabelReq) XXX_DiscardUnknown() {
	xxx_messageInfo_StrategyEnabelReq.DiscardUnknown(m)
}

var xxx_messageInfo_StrategyEnabelReq proto.InternalMessageInfo

func (m *StrategyEnabelReq) GetStrategyId() int64 {
	if m != nil {
		return m.StrategyId
	}
	return 0
}

func (m *StrategyEnabelReq) GetEnable() uint32 {
	if m != nil {
		return m.Enable
	}
	return 0
}

// StrategyListReq 工单策略列表req
type StrategyListReq struct {
	// 游戏
	Project []string `protobuf:"bytes,1,rep,name=project,proto3" json:"project"`
	// 策略名称
	StrategyName string `protobuf:"bytes,2,opt,name=strategy_name,json=strategyName,proto3" json:"strategy_name"`
	// @gotags: validate:"required"
	Page uint32 `protobuf:"varint,7,opt,name=page,proto3" json:"page" validate:"required"`
	// @gotags: validate:"required"
	PageSize             uint32   `protobuf:"varint,8,opt,name=page_size,json=pageSize,proto3" json:"page_size" validate:"required"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *StrategyListReq) Reset()         { *m = StrategyListReq{} }
func (m *StrategyListReq) String() string { return proto.CompactTextString(m) }
func (*StrategyListReq) ProtoMessage()    {}
func (*StrategyListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_46ec5ce6dd46feab, []int{5}
}

func (m *StrategyListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StrategyListReq.Unmarshal(m, b)
}
func (m *StrategyListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StrategyListReq.Marshal(b, m, deterministic)
}
func (m *StrategyListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StrategyListReq.Merge(m, src)
}
func (m *StrategyListReq) XXX_Size() int {
	return xxx_messageInfo_StrategyListReq.Size(m)
}
func (m *StrategyListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_StrategyListReq.DiscardUnknown(m)
}

var xxx_messageInfo_StrategyListReq proto.InternalMessageInfo

func (m *StrategyListReq) GetProject() []string {
	if m != nil {
		return m.Project
	}
	return nil
}

func (m *StrategyListReq) GetStrategyName() string {
	if m != nil {
		return m.StrategyName
	}
	return ""
}

func (m *StrategyListReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *StrategyListReq) GetPageSize() uint32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

// StrategyListResp 工单策略列表resp
type StrategyListResp struct {
	CurrentPage          uint32                             `protobuf:"varint,1,opt,name=current_page,json=currentPage,proto3" json:"current_page"`
	PerPage              uint32                             `protobuf:"varint,2,opt,name=per_page,json=perPage,proto3" json:"per_page"`
	Total                uint32                             `protobuf:"varint,3,opt,name=total,proto3" json:"total"`
	Data                 []*StrategyListResp_StrategyRecord `protobuf:"bytes,4,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}                           `json:"-" gorm:"-"`
	XXX_unrecognized     []byte                             `json:"-" gorm:"-"`
	XXX_sizecache        int32                              `json:"-" gorm:"-"`
}

func (m *StrategyListResp) Reset()         { *m = StrategyListResp{} }
func (m *StrategyListResp) String() string { return proto.CompactTextString(m) }
func (*StrategyListResp) ProtoMessage()    {}
func (*StrategyListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_46ec5ce6dd46feab, []int{6}
}

func (m *StrategyListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StrategyListResp.Unmarshal(m, b)
}
func (m *StrategyListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StrategyListResp.Marshal(b, m, deterministic)
}
func (m *StrategyListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StrategyListResp.Merge(m, src)
}
func (m *StrategyListResp) XXX_Size() int {
	return xxx_messageInfo_StrategyListResp.Size(m)
}
func (m *StrategyListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_StrategyListResp.DiscardUnknown(m)
}

var xxx_messageInfo_StrategyListResp proto.InternalMessageInfo

func (m *StrategyListResp) GetCurrentPage() uint32 {
	if m != nil {
		return m.CurrentPage
	}
	return 0
}

func (m *StrategyListResp) GetPerPage() uint32 {
	if m != nil {
		return m.PerPage
	}
	return 0
}

func (m *StrategyListResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *StrategyListResp) GetData() []*StrategyListResp_StrategyRecord {
	if m != nil {
		return m.Data
	}
	return nil
}

type StrategyListResp_StrategyRecord struct {
	// 策略id
	StrategyId int64 `protobuf:"varint,1,opt,name=strategy_id,json=strategyId,proto3" json:"strategy_id"`
	// 策略名称
	StrategyName string `protobuf:"bytes,2,opt,name=strategy_name,json=strategyName,proto3" json:"strategy_name"`
	// 游戏
	Project string `protobuf:"bytes,3,opt,name=project,proto3" json:"project"`
	// 禁/启用
	Enable uint32 `protobuf:"varint,4,opt,name=enable,proto3" json:"enable"`
	// 生效服务器
	FilterServerLists string `protobuf:"bytes,5,opt,name=filter_server_lists,json=filterServerLists,proto3" json:"filter_server_lists"`
	// 充值区间
	FilterPayRangeLists string `protobuf:"bytes,6,opt,name=filter_pay_range_lists,json=filterPayRangeLists,proto3" json:"filter_pay_range_lists"`
	// 城堡等级
	FilterCastleLevelLists string `protobuf:"bytes,7,opt,name=filter_castle_level_lists,json=filterCastleLevelLists,proto3" json:"filter_castle_level_lists"`
	// 服务器是否全部
	Type                 uint32   `protobuf:"varint,8,opt,name=type,proto3" json:"type"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *StrategyListResp_StrategyRecord) Reset()         { *m = StrategyListResp_StrategyRecord{} }
func (m *StrategyListResp_StrategyRecord) String() string { return proto.CompactTextString(m) }
func (*StrategyListResp_StrategyRecord) ProtoMessage()    {}
func (*StrategyListResp_StrategyRecord) Descriptor() ([]byte, []int) {
	return fileDescriptor_46ec5ce6dd46feab, []int{6, 0}
}

func (m *StrategyListResp_StrategyRecord) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_StrategyListResp_StrategyRecord.Unmarshal(m, b)
}
func (m *StrategyListResp_StrategyRecord) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_StrategyListResp_StrategyRecord.Marshal(b, m, deterministic)
}
func (m *StrategyListResp_StrategyRecord) XXX_Merge(src proto.Message) {
	xxx_messageInfo_StrategyListResp_StrategyRecord.Merge(m, src)
}
func (m *StrategyListResp_StrategyRecord) XXX_Size() int {
	return xxx_messageInfo_StrategyListResp_StrategyRecord.Size(m)
}
func (m *StrategyListResp_StrategyRecord) XXX_DiscardUnknown() {
	xxx_messageInfo_StrategyListResp_StrategyRecord.DiscardUnknown(m)
}

var xxx_messageInfo_StrategyListResp_StrategyRecord proto.InternalMessageInfo

func (m *StrategyListResp_StrategyRecord) GetStrategyId() int64 {
	if m != nil {
		return m.StrategyId
	}
	return 0
}

func (m *StrategyListResp_StrategyRecord) GetStrategyName() string {
	if m != nil {
		return m.StrategyName
	}
	return ""
}

func (m *StrategyListResp_StrategyRecord) GetProject() string {
	if m != nil {
		return m.Project
	}
	return ""
}

func (m *StrategyListResp_StrategyRecord) GetEnable() uint32 {
	if m != nil {
		return m.Enable
	}
	return 0
}

func (m *StrategyListResp_StrategyRecord) GetFilterServerLists() string {
	if m != nil {
		return m.FilterServerLists
	}
	return ""
}

func (m *StrategyListResp_StrategyRecord) GetFilterPayRangeLists() string {
	if m != nil {
		return m.FilterPayRangeLists
	}
	return ""
}

func (m *StrategyListResp_StrategyRecord) GetFilterCastleLevelLists() string {
	if m != nil {
		return m.FilterCastleLevelLists
	}
	return ""
}

func (m *StrategyListResp_StrategyRecord) GetType() uint32 {
	if m != nil {
		return m.Type
	}
	return 0
}

func init() {
	proto.RegisterType((*StrategySplit)(nil), "pb.StrategySplit")
	proto.RegisterType((*StrategySplit_ObjBtw)(nil), "pb.StrategySplit.ObjBtw")
	proto.RegisterType((*StrategyFilters)(nil), "pb.StrategyFilters")
	proto.RegisterType((*StrategyAddReq)(nil), "pb.StrategyAddReq")
	proto.RegisterType((*StrategyDelReq)(nil), "pb.StrategyDelReq")
	proto.RegisterType((*StrategyEnabelReq)(nil), "pb.StrategyEnabelReq")
	proto.RegisterType((*StrategyListReq)(nil), "pb.StrategyListReq")
	proto.RegisterType((*StrategyListResp)(nil), "pb.StrategyListResp")
	proto.RegisterType((*StrategyListResp_StrategyRecord)(nil), "pb.StrategyListResp.StrategyRecord")
}

func init() {
	proto.RegisterFile("strategy.proto", fileDescriptor_46ec5ce6dd46feab)
}

var fileDescriptor_46ec5ce6dd46feab = []byte{
	// 574 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xd4, 0x54, 0xcd, 0x6e, 0xd3, 0x4c,
	0x14, 0x95, 0x63, 0xd7, 0x4d, 0x6f, 0xe2, 0x7e, 0xcd, 0x7c, 0xa8, 0x72, 0x8b, 0x04, 0xc1, 0xdd,
	0x04, 0x16, 0x16, 0xb4, 0x48, 0x08, 0xb1, 0x6a, 0xf9, 0x91, 0x2a, 0x45, 0x50, 0x4d, 0x76, 0x6c,
	0xac, 0x71, 0x7c, 0xb1, 0x5c, 0xb9, 0xce, 0x30, 0x33, 0xa4, 0x4a, 0x37, 0x88, 0x77, 0xe1, 0x31,
	0x58, 0xf1, 0x20, 0x3c, 0x0b, 0x9a, 0xf1, 0x38, 0x71, 0x04, 0x81, 0x6e, 0xd9, 0xcd, 0xbd, 0xf7,
	0x9c, 0x93, 0x99, 0x73, 0x8f, 0x03, 0xbb, 0x52, 0x09, 0xa6, 0x30, 0x5f, 0xc4, 0x5c, 0xcc, 0xd4,
	0x8c, 0x74, 0x78, 0x1a, 0x7d, 0x86, 0x60, 0x62, 0xbb, 0x13, 0x5e, 0x16, 0x8a, 0xec, 0x81, 0x5b,
	0x64, 0x32, 0x74, 0x86, 0xee, 0xc8, 0xa5, 0xfa, 0x48, 0x1e, 0x81, 0x9b, 0xaa, 0xeb, 0xb0, 0x33,
	0x74, 0x47, 0xbd, 0xe3, 0x30, 0xe6, 0x69, 0xbc, 0xc6, 0x88, 0xdf, 0xa5, 0x97, 0x67, 0xea, 0x9a,
	0x6a, 0xd0, 0xe1, 0x63, 0xf0, 0xeb, 0x92, 0xdc, 0x81, 0x2d, 0xa9, 0x98, 0x50, 0xa1, 0x33, 0x74,
	0x46, 0x2e, 0xad, 0x0b, 0xad, 0x8e, 0x55, 0x16, 0x76, 0x4c, 0x4f, 0x1f, 0xa3, 0xef, 0x0e, 0xfc,
	0xd7, 0xe8, 0xbd, 0x29, 0x4a, 0x85, 0x42, 0x92, 0x7b, 0x00, 0xe7, 0x72, 0x82, 0x73, 0x14, 0xa7,
	0x65, 0x69, 0x04, 0xba, 0xb4, 0xd5, 0x21, 0x0f, 0xc1, 0x97, 0x28, 0xe6, 0x28, 0x8c, 0x50, 0xef,
	0x78, 0xf0, 0xcb, 0xa5, 0xa8, 0x05, 0x90, 0x18, 0x76, 0x38, 0x5b, 0x24, 0x82, 0x55, 0x39, 0x86,
	0xee, 0x26, 0x74, 0x97, 0xb3, 0x05, 0xd5, 0x10, 0xf2, 0x14, 0xfa, 0x53, 0x26, 0x55, 0x89, 0x49,
	0x89, 0x73, 0x2c, 0x43, 0x6f, 0x13, 0xa5, 0x57, 0xc3, 0xc6, 0x1a, 0x15, 0x7d, 0xed, 0xc0, 0x6e,
	0x33, 0x3e, 0xcd, 0x32, 0x8a, 0x1f, 0xc9, 0x7d, 0xe8, 0x35, 0x76, 0x27, 0x45, 0x66, 0x1e, 0xe1,
	0x51, 0x68, 0x5a, 0xe7, 0x19, 0x09, 0x61, 0x9b, 0x8b, 0xd9, 0x25, 0x4e, 0x95, 0x79, 0xc5, 0x0e,
	0x6d, 0x4a, 0x72, 0x04, 0xc1, 0x92, 0x5a, 0xb1, 0xab, 0xfa, 0xde, 0x3b, 0xb4, 0xdf, 0x34, 0xdf,
	0xb2, 0x2b, 0x24, 0x31, 0xfc, 0xff, 0xc1, 0xd8, 0x95, 0xd4, 0x2f, 0x4d, 0xca, 0x42, 0x2a, 0x19,
	0x6e, 0x19, 0xe8, 0xa0, 0x1e, 0x4d, 0xcc, 0x64, 0xac, 0x07, 0xe4, 0x04, 0xf6, 0x2d, 0x7e, 0xe9,
	0x87, 0xa5, 0xf8, 0x86, 0x62, 0xd5, 0x2e, 0xac, 0x11, 0x35, 0xe9, 0x39, 0x1c, 0x58, 0x52, 0xdb,
	0x14, 0xcb, 0xdb, 0x36, 0x3c, 0xab, 0xfa, 0x72, 0xe5, 0x46, 0x4d, 0x25, 0xe0, 0xa9, 0x05, 0xc7,
	0xb0, 0x3b, 0x74, 0x46, 0x01, 0x35, 0xe7, 0xe8, 0xc9, 0xca, 0xa5, 0x57, 0x58, 0x6e, 0x70, 0xc9,
	0x6d, 0xbb, 0x14, 0x8d, 0x61, 0xd0, 0x50, 0x5e, 0x57, 0x2c, 0xbd, 0x1d, 0x8b, 0xec, 0x83, 0x8f,
	0x15, 0x4b, 0x4b, 0x34, 0xd6, 0x06, 0xd4, 0x56, 0xd1, 0x97, 0x56, 0xd8, 0xf4, 0x35, 0xb5, 0x58,
	0x6b, 0x0f, 0x3a, 0xf4, 0x7f, 0xda, 0x43, 0xe7, 0x37, 0x7b, 0x20, 0xe0, 0x71, 0x96, 0xa3, 0x71,
	0x23, 0xa0, 0xe6, 0x4c, 0xee, 0xea, 0xd0, 0xe5, 0x98, 0xc8, 0xe2, 0xa6, 0x31, 0xa0, 0xab, 0x1b,
	0x93, 0xe2, 0x06, 0xa3, 0x1f, 0x2e, 0xec, 0xad, 0xdf, 0x41, 0x72, 0xf2, 0x00, 0xfa, 0xd3, 0x4f,
	0x42, 0x60, 0xa5, 0x12, 0xa3, 0xe6, 0x18, 0x52, 0xcf, 0xf6, 0x2e, 0xb4, 0xe8, 0x01, 0x74, 0xb9,
	0xd9, 0x5e, 0xde, 0xbc, 0x6a, 0x9b, 0xeb, 0x7d, 0xe5, 0xa8, 0xbf, 0x35, 0x35, 0x53, 0xac, 0x34,
	0x41, 0x09, 0x68, 0x5d, 0x90, 0x67, 0xe0, 0x65, 0x4c, 0xb1, 0xd0, 0x33, 0x1f, 0xee, 0x51, 0x3b,
	0xc2, 0xcd, 0xef, 0x2e, 0x1b, 0x14, 0xa7, 0x33, 0x91, 0x51, 0x43, 0x38, 0xfc, 0xd6, 0x4a, 0x73,
	0x3d, 0xf8, 0xbb, 0xe3, 0xb7, 0xf2, 0xaa, 0x65, 0xb5, 0xbb, 0x1e, 0xf9, 0xd5, 0xc2, 0xbc, 0xf6,
	0xc2, 0xfe, 0xc5, 0x94, 0x9f, 0xf9, 0xef, 0xbd, 0xf8, 0x05, 0x4f, 0x53, 0xdf, 0xfc, 0xcb, 0x9e,
	0xfc, 0x0c, 0x00, 0x00, 0xff, 0xff, 0x9f, 0xe8, 0x58, 0xa3, 0x77, 0x05, 0x00, 0x00,
}
