// Code generated by protoc-gen-go. DO NOT EDIT.
// source: ai.proto

package pb

import (
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

// AISummaryReq ai-总结
// 需要传工单号对工单详情进行总结
type AISummaryReq struct {
	// 工单ID @gotags: validate:"required"
	TicketId             uint64   `protobuf:"varint,1,opt,name=ticket_id,json=ticketId,proto3" json:"ticket_id" validate:"required"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *AISummaryReq) Reset()         { *m = AISummaryReq{} }
func (m *AISummaryReq) String() string { return proto.CompactTextString(m) }
func (*AISummaryReq) ProtoMessage()    {}
func (*AISummaryReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_4bdfe6a5fd51d81f, []int{0}
}

func (m *AISummaryReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AISummaryReq.Unmarshal(m, b)
}
func (m *AISummaryReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AISummaryReq.Marshal(b, m, deterministic)
}
func (m *AISummaryReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AISummaryReq.Merge(m, src)
}
func (m *AISummaryReq) XXX_Size() int {
	return xxx_messageInfo_AISummaryReq.Size(m)
}
func (m *AISummaryReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AISummaryReq.DiscardUnknown(m)
}

var xxx_messageInfo_AISummaryReq proto.InternalMessageInfo

func (m *AISummaryReq) GetTicketId() uint64 {
	if m != nil {
		return m.TicketId
	}
	return 0
}

// AISummaryReq ai-润色
type AIPolishReq struct {
	// 工单ID @gotags: validate:"required"
	TicketId uint64 `protobuf:"varint,1,opt,name=ticket_id,json=ticketId,proto3" json:"ticket_id" validate:"required"`
	// 润色内容 @gotags: validate:"required"
	Content string `protobuf:"bytes,2,opt,name=content,proto3" json:"content" validate:"required"`
	// 润色类型 @gotags: validate:"required"
	Style                string   `protobuf:"bytes,3,opt,name=style,proto3" json:"style" validate:"required"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *AIPolishReq) Reset()         { *m = AIPolishReq{} }
func (m *AIPolishReq) String() string { return proto.CompactTextString(m) }
func (*AIPolishReq) ProtoMessage()    {}
func (*AIPolishReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_4bdfe6a5fd51d81f, []int{1}
}

func (m *AIPolishReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AIPolishReq.Unmarshal(m, b)
}
func (m *AIPolishReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AIPolishReq.Marshal(b, m, deterministic)
}
func (m *AIPolishReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AIPolishReq.Merge(m, src)
}
func (m *AIPolishReq) XXX_Size() int {
	return xxx_messageInfo_AIPolishReq.Size(m)
}
func (m *AIPolishReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AIPolishReq.DiscardUnknown(m)
}

var xxx_messageInfo_AIPolishReq proto.InternalMessageInfo

func (m *AIPolishReq) GetTicketId() uint64 {
	if m != nil {
		return m.TicketId
	}
	return 0
}

func (m *AIPolishReq) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *AIPolishReq) GetStyle() string {
	if m != nil {
		return m.Style
	}
	return ""
}

// AIPreReplyReq ai-预回复
type AIPreReplyReq struct {
	// 工单ID @gotags: validate:"required"
	TicketId uint64 `protobuf:"varint,1,opt,name=ticket_id,json=ticketId,proto3" json:"ticket_id" validate:"required"`
	// 预回复内容
	Content              string   `protobuf:"bytes,2,opt,name=content,proto3" json:"content"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *AIPreReplyReq) Reset()         { *m = AIPreReplyReq{} }
func (m *AIPreReplyReq) String() string { return proto.CompactTextString(m) }
func (*AIPreReplyReq) ProtoMessage()    {}
func (*AIPreReplyReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_4bdfe6a5fd51d81f, []int{2}
}

func (m *AIPreReplyReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AIPreReplyReq.Unmarshal(m, b)
}
func (m *AIPreReplyReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AIPreReplyReq.Marshal(b, m, deterministic)
}
func (m *AIPreReplyReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AIPreReplyReq.Merge(m, src)
}
func (m *AIPreReplyReq) XXX_Size() int {
	return xxx_messageInfo_AIPreReplyReq.Size(m)
}
func (m *AIPreReplyReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AIPreReplyReq.DiscardUnknown(m)
}

var xxx_messageInfo_AIPreReplyReq proto.InternalMessageInfo

func (m *AIPreReplyReq) GetTicketId() uint64 {
	if m != nil {
		return m.TicketId
	}
	return 0
}

func (m *AIPreReplyReq) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

// AIFaqReq ai-faq
type AIFaqReq struct {
	// 工单ID @gotags: validate:"required"
	TicketId uint64 `protobuf:"varint,1,opt,name=ticket_id,json=ticketId,proto3" json:"ticket_id" validate:"required"`
	// 预回复内容 @gotags: validate:"required"
	Content              string   `protobuf:"bytes,2,opt,name=content,proto3" json:"content" validate:"required"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *AIFaqReq) Reset()         { *m = AIFaqReq{} }
func (m *AIFaqReq) String() string { return proto.CompactTextString(m) }
func (*AIFaqReq) ProtoMessage()    {}
func (*AIFaqReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_4bdfe6a5fd51d81f, []int{3}
}

func (m *AIFaqReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AIFaqReq.Unmarshal(m, b)
}
func (m *AIFaqReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AIFaqReq.Marshal(b, m, deterministic)
}
func (m *AIFaqReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AIFaqReq.Merge(m, src)
}
func (m *AIFaqReq) XXX_Size() int {
	return xxx_messageInfo_AIFaqReq.Size(m)
}
func (m *AIFaqReq) XXX_DiscardUnknown() {
	xxx_messageInfo_AIFaqReq.DiscardUnknown(m)
}

var xxx_messageInfo_AIFaqReq proto.InternalMessageInfo

func (m *AIFaqReq) GetTicketId() uint64 {
	if m != nil {
		return m.TicketId
	}
	return 0
}

func (m *AIFaqReq) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

// AIFaqResp ai-faq
type AIFaqResp struct {
	// 问题
	Question string `protobuf:"bytes,1,opt,name=question,proto3" json:"question"`
	// 答案
	Answer               string   `protobuf:"bytes,2,opt,name=answer,proto3" json:"answer"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *AIFaqResp) Reset()         { *m = AIFaqResp{} }
func (m *AIFaqResp) String() string { return proto.CompactTextString(m) }
func (*AIFaqResp) ProtoMessage()    {}
func (*AIFaqResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_4bdfe6a5fd51d81f, []int{4}
}

func (m *AIFaqResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_AIFaqResp.Unmarshal(m, b)
}
func (m *AIFaqResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_AIFaqResp.Marshal(b, m, deterministic)
}
func (m *AIFaqResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_AIFaqResp.Merge(m, src)
}
func (m *AIFaqResp) XXX_Size() int {
	return xxx_messageInfo_AIFaqResp.Size(m)
}
func (m *AIFaqResp) XXX_DiscardUnknown() {
	xxx_messageInfo_AIFaqResp.DiscardUnknown(m)
}

var xxx_messageInfo_AIFaqResp proto.InternalMessageInfo

func (m *AIFaqResp) GetQuestion() string {
	if m != nil {
		return m.Question
	}
	return ""
}

func (m *AIFaqResp) GetAnswer() string {
	if m != nil {
		return m.Answer
	}
	return ""
}

func init() {
	proto.RegisterType((*AISummaryReq)(nil), "pb.AISummaryReq")
	proto.RegisterType((*AIPolishReq)(nil), "pb.AIPolishReq")
	proto.RegisterType((*AIPreReplyReq)(nil), "pb.AIPreReplyReq")
	proto.RegisterType((*AIFaqReq)(nil), "pb.AIFaqReq")
	proto.RegisterType((*AIFaqResp)(nil), "pb.AIFaqResp")
}

func init() {
	proto.RegisterFile("ai.proto", fileDescriptor_4bdfe6a5fd51d81f)
}

var fileDescriptor_4bdfe6a5fd51d81f = []byte{
	// 208 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xe2, 0xe2, 0x48, 0xcc, 0xd4, 0x2b,
	0x28, 0xca, 0x2f, 0xc9, 0x17, 0x62, 0x2a, 0x48, 0x52, 0xd2, 0xe6, 0xe2, 0x71, 0xf4, 0x0c, 0x2e,
	0xcd, 0xcd, 0x4d, 0x2c, 0xaa, 0x0c, 0x4a, 0x2d, 0x14, 0x92, 0xe6, 0xe2, 0x2c, 0xc9, 0x4c, 0xce,
	0x4e, 0x2d, 0x89, 0xcf, 0x4c, 0x91, 0x60, 0x54, 0x60, 0xd4, 0x60, 0x09, 0xe2, 0x80, 0x08, 0x78,
	0xa6, 0x28, 0x45, 0x71, 0x71, 0x3b, 0x7a, 0x06, 0xe4, 0xe7, 0x64, 0x16, 0x67, 0x10, 0x52, 0x2b,
	0x24, 0xc1, 0xc5, 0x9e, 0x9c, 0x9f, 0x57, 0x92, 0x9a, 0x57, 0x22, 0xc1, 0xa4, 0xc0, 0xa8, 0xc1,
	0x19, 0x04, 0xe3, 0x0a, 0x89, 0x70, 0xb1, 0x16, 0x97, 0x54, 0xe6, 0xa4, 0x4a, 0x30, 0x83, 0xc5,
	0x21, 0x1c, 0x25, 0x37, 0x2e, 0x5e, 0x47, 0xcf, 0x80, 0xa2, 0xd4, 0xa0, 0xd4, 0x82, 0x9c, 0x4a,
	0xf2, 0x4d, 0x57, 0x72, 0xe4, 0xe2, 0x70, 0xf4, 0x74, 0x4b, 0x2c, 0xa4, 0xc0, 0x08, 0x7b, 0x2e,
	0x4e, 0xa8, 0x11, 0xc5, 0x05, 0x42, 0x52, 0x5c, 0x1c, 0x85, 0xa5, 0xa9, 0xc5, 0x25, 0x99, 0xf9,
	0x79, 0x60, 0x23, 0x38, 0x83, 0xe0, 0x7c, 0x21, 0x31, 0x2e, 0xb6, 0xc4, 0xbc, 0xe2, 0xf2, 0xd4,
	0x22, 0xa8, 0x09, 0x50, 0x9e, 0x13, 0x5b, 0x14, 0x8b, 0x9e, 0x75, 0x41, 0x52, 0x12, 0x1b, 0x38,
	0x9c, 0x8d, 0x01, 0x01, 0x00, 0x00, 0xff, 0xff, 0x2a, 0xed, 0x6b, 0x42, 0x73, 0x01, 0x00, 0x00,
}
