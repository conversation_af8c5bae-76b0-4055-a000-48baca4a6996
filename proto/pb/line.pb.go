// Code generated by protoc-gen-go. DO NOT EDIT.
// source: line.proto

package pb

import (
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

type LineUserListReq struct {
	// 游戏
	Project []string `protobuf:"bytes,1,rep,name=project,proto3" json:"project"`
	// 回复时间
	RepliedAt []string `protobuf:"bytes,2,rep,name=replied_at,json=repliedAt,proto3" json:"replied_at"`
	// 状态 - 1:未回复、2:已回复
	Status []uint32 `protobuf:"varint,3,rep,packed,name=status,proto3" json:"status"`
	// 玩家输入信息 - 模糊查询
	UserContent string `protobuf:"bytes,4,opt,name=user_content,json=userContent,proto3" json:"user_content"`
	// 备注信息 - 玩家备注信息 - 模糊查询
	UserDetailRemark string `protobuf:"bytes,5,opt,name=user_detail_remark,json=userDetailRemark,proto3" json:"user_detail_remark"`
	// 维护人
	Maintainer []string `protobuf:"bytes,6,rep,name=maintainer,proto3" json:"maintainer"`
	// 玩家 line昵称
	DisplayName string `protobuf:"bytes,7,opt,name=display_name,json=displayName,proto3" json:"display_name"`
	// 玩家 line ID
	LineUserId string `protobuf:"bytes,8,opt,name=line_user_id,json=lineUserId,proto3" json:"line_user_id"`
	Uid        uint64 `protobuf:"varint,9,opt,name=uid,proto3" json:"uid"`
	Fpid       string `protobuf:"bytes,10,opt,name=fpid,proto3" json:"fpid"`
	Sid        string `protobuf:"bytes,11,opt,name=sid,proto3" json:"sid"`
	// 最近登录时间
	LastLogin []string `protobuf:"bytes,12,rep,name=last_login,json=lastLogin,proto3" json:"last_login"`
	// 累计付费金额
	PayAll []int64 `protobuf:"varint,13,rep,packed,name=pay_all,json=payAll,proto3" json:"pay_all"`
	// 最近30天付费金额
	PayLastThirtyDays []int64 `protobuf:"varint,14,rep,packed,name=pay_last_thirty_days,json=payLastThirtyDays,proto3" json:"pay_last_thirty_days"`
	// 玩家VIP状态
	VipState uint32   `protobuf:"varint,15,opt,name=vip_state,json=vipState,proto3" json:"vip_state"`
	Birthday []string `protobuf:"bytes,17,rep,name=birthday,proto3" json:"birthday"`
	Lang     string   `protobuf:"bytes,18,opt,name=lang,proto3" json:"lang"`
	// 最近处理人
	LastReplyService []string `protobuf:"bytes,19,rep,name=last_reply_service,json=lastReplyService,proto3" json:"last_reply_service"`
	// 排序字段
	SortBy DcPoolSort `protobuf:"varint,20,opt,name=sort_by,json=sortBy,proto3,enum=pb.DcPoolSort" json:"sort_by"`
	// 排序顺序 asc升序，desc降序
	Order string `protobuf:"bytes,21,opt,name=order,proto3" json:"order"`
	// @gotags: validate:"required"
	Page uint32 `protobuf:"varint,22,opt,name=page,proto3" json:"page" validate:"required"`
	// @gotags: validate:"required"
	PageSize uint32 `protobuf:"varint,23,opt,name=page_size,json=pageSize,proto3" json:"page_size" validate:"required"`
	// 机器人bot_id
	BotIds []string `protobuf:"bytes,24,rep,name=bot_ids,json=botIds,proto3" json:"bot_ids"`
	// 新用字段
	Uids string `protobuf:"bytes,25,opt,name=uids,proto3" json:"uids"`
	// channel_id
	ChannelId []string `protobuf:"bytes,26,rep,name=channel_id,json=channelId,proto3" json:"channel_id"`
	// 新用标签
	Tags []uint32 `protobuf:"varint,27,rep,packed,name=tags,proto3" json:"tags"`
	// 标签类型
	TagType              FilterTagEnum `protobuf:"varint,28,opt,name=tag_type,json=tagType,proto3,enum=pb.FilterTagEnum" json:"tag_type"`
	XXX_NoUnkeyedLiteral struct{}      `json:"-" gorm:"-"`
	XXX_unrecognized     []byte        `json:"-" gorm:"-"`
	XXX_sizecache        int32         `json:"-" gorm:"-"`
}

func (m *LineUserListReq) Reset()         { *m = LineUserListReq{} }
func (m *LineUserListReq) String() string { return proto.CompactTextString(m) }
func (*LineUserListReq) ProtoMessage()    {}
func (*LineUserListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_951bc87fae86fd79, []int{0}
}

func (m *LineUserListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LineUserListReq.Unmarshal(m, b)
}
func (m *LineUserListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LineUserListReq.Marshal(b, m, deterministic)
}
func (m *LineUserListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LineUserListReq.Merge(m, src)
}
func (m *LineUserListReq) XXX_Size() int {
	return xxx_messageInfo_LineUserListReq.Size(m)
}
func (m *LineUserListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_LineUserListReq.DiscardUnknown(m)
}

var xxx_messageInfo_LineUserListReq proto.InternalMessageInfo

func (m *LineUserListReq) GetProject() []string {
	if m != nil {
		return m.Project
	}
	return nil
}

func (m *LineUserListReq) GetRepliedAt() []string {
	if m != nil {
		return m.RepliedAt
	}
	return nil
}

func (m *LineUserListReq) GetStatus() []uint32 {
	if m != nil {
		return m.Status
	}
	return nil
}

func (m *LineUserListReq) GetUserContent() string {
	if m != nil {
		return m.UserContent
	}
	return ""
}

func (m *LineUserListReq) GetUserDetailRemark() string {
	if m != nil {
		return m.UserDetailRemark
	}
	return ""
}

func (m *LineUserListReq) GetMaintainer() []string {
	if m != nil {
		return m.Maintainer
	}
	return nil
}

func (m *LineUserListReq) GetDisplayName() string {
	if m != nil {
		return m.DisplayName
	}
	return ""
}

func (m *LineUserListReq) GetLineUserId() string {
	if m != nil {
		return m.LineUserId
	}
	return ""
}

func (m *LineUserListReq) GetUid() uint64 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *LineUserListReq) GetFpid() string {
	if m != nil {
		return m.Fpid
	}
	return ""
}

func (m *LineUserListReq) GetSid() string {
	if m != nil {
		return m.Sid
	}
	return ""
}

func (m *LineUserListReq) GetLastLogin() []string {
	if m != nil {
		return m.LastLogin
	}
	return nil
}

func (m *LineUserListReq) GetPayAll() []int64 {
	if m != nil {
		return m.PayAll
	}
	return nil
}

func (m *LineUserListReq) GetPayLastThirtyDays() []int64 {
	if m != nil {
		return m.PayLastThirtyDays
	}
	return nil
}

func (m *LineUserListReq) GetVipState() uint32 {
	if m != nil {
		return m.VipState
	}
	return 0
}

func (m *LineUserListReq) GetBirthday() []string {
	if m != nil {
		return m.Birthday
	}
	return nil
}

func (m *LineUserListReq) GetLang() string {
	if m != nil {
		return m.Lang
	}
	return ""
}

func (m *LineUserListReq) GetLastReplyService() []string {
	if m != nil {
		return m.LastReplyService
	}
	return nil
}

func (m *LineUserListReq) GetSortBy() DcPoolSort {
	if m != nil {
		return m.SortBy
	}
	return DcPoolSort_DcPoolSortWaitDefault
}

func (m *LineUserListReq) GetOrder() string {
	if m != nil {
		return m.Order
	}
	return ""
}

func (m *LineUserListReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *LineUserListReq) GetPageSize() uint32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *LineUserListReq) GetBotIds() []string {
	if m != nil {
		return m.BotIds
	}
	return nil
}

func (m *LineUserListReq) GetUids() string {
	if m != nil {
		return m.Uids
	}
	return ""
}

func (m *LineUserListReq) GetChannelId() []string {
	if m != nil {
		return m.ChannelId
	}
	return nil
}

func (m *LineUserListReq) GetTags() []uint32 {
	if m != nil {
		return m.Tags
	}
	return nil
}

func (m *LineUserListReq) GetTagType() FilterTagEnum {
	if m != nil {
		return m.TagType
	}
	return FilterTagEnum_All
}

type LineUserListResp struct {
	CurrentPage          uint32                       `protobuf:"varint,1,opt,name=current_page,json=currentPage,proto3" json:"current_page"`
	PerPage              uint32                       `protobuf:"varint,2,opt,name=per_page,json=perPage,proto3" json:"per_page"`
	Total                uint32                       `protobuf:"varint,3,opt,name=total,proto3" json:"total"`
	Data                 []*LineUserListResp_LineUser `protobuf:"bytes,4,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}                     `json:"-" gorm:"-"`
	XXX_unrecognized     []byte                       `json:"-" gorm:"-"`
	XXX_sizecache        int32                        `json:"-" gorm:"-"`
}

func (m *LineUserListResp) Reset()         { *m = LineUserListResp{} }
func (m *LineUserListResp) String() string { return proto.CompactTextString(m) }
func (*LineUserListResp) ProtoMessage()    {}
func (*LineUserListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_951bc87fae86fd79, []int{1}
}

func (m *LineUserListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LineUserListResp.Unmarshal(m, b)
}
func (m *LineUserListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LineUserListResp.Marshal(b, m, deterministic)
}
func (m *LineUserListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LineUserListResp.Merge(m, src)
}
func (m *LineUserListResp) XXX_Size() int {
	return xxx_messageInfo_LineUserListResp.Size(m)
}
func (m *LineUserListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_LineUserListResp.DiscardUnknown(m)
}

var xxx_messageInfo_LineUserListResp proto.InternalMessageInfo

func (m *LineUserListResp) GetCurrentPage() uint32 {
	if m != nil {
		return m.CurrentPage
	}
	return 0
}

func (m *LineUserListResp) GetPerPage() uint32 {
	if m != nil {
		return m.PerPage
	}
	return 0
}

func (m *LineUserListResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *LineUserListResp) GetData() []*LineUserListResp_LineUser {
	if m != nil {
		return m.Data
	}
	return nil
}

type LineUserListResp_LineUser struct {
	// 游戏
	Project string `protobuf:"bytes,1,opt,name=project,proto3" json:"project"`
	// 玩家 line ID
	LineUserId string `protobuf:"bytes,2,opt,name=line_user_id,json=lineUserId,proto3" json:"line_user_id"`
	// 玩家名称 - 前端展示使用此字段
	DisplayName string `protobuf:"bytes,3,opt,name=display_name,json=displayName,proto3" json:"display_name"`
	// line channel
	ChannelId string `protobuf:"bytes,4,opt,name=channel_id,json=channelId,proto3" json:"channel_id"`
	// 当前维护人
	Maintainer string `protobuf:"bytes,5,opt,name=maintainer,proto3" json:"maintainer"`
	// 累付金额
	TotalPay float64 `protobuf:"fixed64,6,opt,name=total_pay,json=totalPay,proto3" json:"total_pay"`
	// 最近30天付费金额
	PayLastThirtyDays float64 `protobuf:"fixed64,7,opt,name=pay_last_thirty_days,json=payLastThirtyDays,proto3" json:"pay_last_thirty_days"`
	// 最近登录时间
	LastLogin string `protobuf:"bytes,8,opt,name=last_login,json=lastLogin,proto3" json:"last_login"`
	// 玩家信息回复状态
	Status uint32 `protobuf:"varint,9,opt,name=status,proto3" json:"status"`
	// VIP 等级
	VipLevel uint32 `protobuf:"varint,10,opt,name=vip_level,json=vipLevel,proto3" json:"vip_level"`
	Fpid     string `protobuf:"bytes,11,opt,name=fpid,proto3" json:"fpid"`
	Uid      uint64 `protobuf:"varint,12,opt,name=uid,proto3" json:"uid"`
	Sid      string `protobuf:"bytes,13,opt,name=sid,proto3" json:"sid"`
	// 机器人 user_id
	BotId      string `protobuf:"bytes,14,opt,name=bot_id,json=botId,proto3" json:"bot_id"`
	Note       string `protobuf:"bytes,15,opt,name=note,proto3" json:"note"`
	PlayerNick string `protobuf:"bytes,16,opt,name=player_nick,json=playerNick,proto3" json:"player_nick"`
	Birthday   string `protobuf:"bytes,17,opt,name=birthday,proto3" json:"birthday"`
	Lang       string `protobuf:"bytes,18,opt,name=lang,proto3" json:"lang"`
	Checked    bool   `protobuf:"varint,19,opt,name=checked,proto3" json:"checked"`
	// channel show
	ChannelShow string `protobuf:"bytes,20,opt,name=channel_show,json=channelShow,proto3" json:"channel_show"`
	// follow status : 1:follow 2:unfollow
	FollowStatus         int32    `protobuf:"varint,21,opt,name=follow_status,json=followStatus,proto3" json:"follow_status"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *LineUserListResp_LineUser) Reset()         { *m = LineUserListResp_LineUser{} }
func (m *LineUserListResp_LineUser) String() string { return proto.CompactTextString(m) }
func (*LineUserListResp_LineUser) ProtoMessage()    {}
func (*LineUserListResp_LineUser) Descriptor() ([]byte, []int) {
	return fileDescriptor_951bc87fae86fd79, []int{1, 0}
}

func (m *LineUserListResp_LineUser) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LineUserListResp_LineUser.Unmarshal(m, b)
}
func (m *LineUserListResp_LineUser) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LineUserListResp_LineUser.Marshal(b, m, deterministic)
}
func (m *LineUserListResp_LineUser) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LineUserListResp_LineUser.Merge(m, src)
}
func (m *LineUserListResp_LineUser) XXX_Size() int {
	return xxx_messageInfo_LineUserListResp_LineUser.Size(m)
}
func (m *LineUserListResp_LineUser) XXX_DiscardUnknown() {
	xxx_messageInfo_LineUserListResp_LineUser.DiscardUnknown(m)
}

var xxx_messageInfo_LineUserListResp_LineUser proto.InternalMessageInfo

func (m *LineUserListResp_LineUser) GetProject() string {
	if m != nil {
		return m.Project
	}
	return ""
}

func (m *LineUserListResp_LineUser) GetLineUserId() string {
	if m != nil {
		return m.LineUserId
	}
	return ""
}

func (m *LineUserListResp_LineUser) GetDisplayName() string {
	if m != nil {
		return m.DisplayName
	}
	return ""
}

func (m *LineUserListResp_LineUser) GetChannelId() string {
	if m != nil {
		return m.ChannelId
	}
	return ""
}

func (m *LineUserListResp_LineUser) GetMaintainer() string {
	if m != nil {
		return m.Maintainer
	}
	return ""
}

func (m *LineUserListResp_LineUser) GetTotalPay() float64 {
	if m != nil {
		return m.TotalPay
	}
	return 0
}

func (m *LineUserListResp_LineUser) GetPayLastThirtyDays() float64 {
	if m != nil {
		return m.PayLastThirtyDays
	}
	return 0
}

func (m *LineUserListResp_LineUser) GetLastLogin() string {
	if m != nil {
		return m.LastLogin
	}
	return ""
}

func (m *LineUserListResp_LineUser) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *LineUserListResp_LineUser) GetVipLevel() uint32 {
	if m != nil {
		return m.VipLevel
	}
	return 0
}

func (m *LineUserListResp_LineUser) GetFpid() string {
	if m != nil {
		return m.Fpid
	}
	return ""
}

func (m *LineUserListResp_LineUser) GetUid() uint64 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *LineUserListResp_LineUser) GetSid() string {
	if m != nil {
		return m.Sid
	}
	return ""
}

func (m *LineUserListResp_LineUser) GetBotId() string {
	if m != nil {
		return m.BotId
	}
	return ""
}

func (m *LineUserListResp_LineUser) GetNote() string {
	if m != nil {
		return m.Note
	}
	return ""
}

func (m *LineUserListResp_LineUser) GetPlayerNick() string {
	if m != nil {
		return m.PlayerNick
	}
	return ""
}

func (m *LineUserListResp_LineUser) GetBirthday() string {
	if m != nil {
		return m.Birthday
	}
	return ""
}

func (m *LineUserListResp_LineUser) GetLang() string {
	if m != nil {
		return m.Lang
	}
	return ""
}

func (m *LineUserListResp_LineUser) GetChecked() bool {
	if m != nil {
		return m.Checked
	}
	return false
}

func (m *LineUserListResp_LineUser) GetChannelShow() string {
	if m != nil {
		return m.ChannelShow
	}
	return ""
}

func (m *LineUserListResp_LineUser) GetFollowStatus() int32 {
	if m != nil {
		return m.FollowStatus
	}
	return 0
}

type LineUserDetailReq struct {
	// 玩家 DC ID
	LineUserId           string   `protobuf:"bytes,1,opt,name=line_user_id,json=lineUserId,proto3" json:"line_user_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *LineUserDetailReq) Reset()         { *m = LineUserDetailReq{} }
func (m *LineUserDetailReq) String() string { return proto.CompactTextString(m) }
func (*LineUserDetailReq) ProtoMessage()    {}
func (*LineUserDetailReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_951bc87fae86fd79, []int{2}
}

func (m *LineUserDetailReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LineUserDetailReq.Unmarshal(m, b)
}
func (m *LineUserDetailReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LineUserDetailReq.Marshal(b, m, deterministic)
}
func (m *LineUserDetailReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LineUserDetailReq.Merge(m, src)
}
func (m *LineUserDetailReq) XXX_Size() int {
	return xxx_messageInfo_LineUserDetailReq.Size(m)
}
func (m *LineUserDetailReq) XXX_DiscardUnknown() {
	xxx_messageInfo_LineUserDetailReq.DiscardUnknown(m)
}

var xxx_messageInfo_LineUserDetailReq proto.InternalMessageInfo

func (m *LineUserDetailReq) GetLineUserId() string {
	if m != nil {
		return m.LineUserId
	}
	return ""
}

type LineUserDetailResp struct {
	// 基本信息
	LineUserDetail       *LineUserDetailResp_LineUserDetail `protobuf:"bytes,1,opt,name=line_user_detail,json=lineUserDetail,proto3" json:"line_user_detail"`
	XXX_NoUnkeyedLiteral struct{}                           `json:"-" gorm:"-"`
	XXX_unrecognized     []byte                             `json:"-" gorm:"-"`
	XXX_sizecache        int32                              `json:"-" gorm:"-"`
}

func (m *LineUserDetailResp) Reset()         { *m = LineUserDetailResp{} }
func (m *LineUserDetailResp) String() string { return proto.CompactTextString(m) }
func (*LineUserDetailResp) ProtoMessage()    {}
func (*LineUserDetailResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_951bc87fae86fd79, []int{3}
}

func (m *LineUserDetailResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LineUserDetailResp.Unmarshal(m, b)
}
func (m *LineUserDetailResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LineUserDetailResp.Marshal(b, m, deterministic)
}
func (m *LineUserDetailResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LineUserDetailResp.Merge(m, src)
}
func (m *LineUserDetailResp) XXX_Size() int {
	return xxx_messageInfo_LineUserDetailResp.Size(m)
}
func (m *LineUserDetailResp) XXX_DiscardUnknown() {
	xxx_messageInfo_LineUserDetailResp.DiscardUnknown(m)
}

var xxx_messageInfo_LineUserDetailResp proto.InternalMessageInfo

func (m *LineUserDetailResp) GetLineUserDetail() *LineUserDetailResp_LineUserDetail {
	if m != nil {
		return m.LineUserDetail
	}
	return nil
}

type LineUserDetailResp_LineUserDetail struct {
	// 游戏
	Project string `protobuf:"bytes,1,opt,name=project,proto3" json:"project"`
	// 玩家 DC ID
	LineUserId string `protobuf:"bytes,2,opt,name=line_user_id,json=lineUserId,proto3" json:"line_user_id"`
	// 玩家名称 - 前端展示使用此字段
	DisplayName string `protobuf:"bytes,3,opt,name=display_name,json=displayName,proto3" json:"display_name"`
	// dm channel
	ChannelId string `protobuf:"bytes,4,opt,name=channel_id,json=channelId,proto3" json:"channel_id"`
	// 当前维护人
	Maintainer           string   `protobuf:"bytes,5,opt,name=maintainer,proto3" json:"maintainer"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *LineUserDetailResp_LineUserDetail) Reset()         { *m = LineUserDetailResp_LineUserDetail{} }
func (m *LineUserDetailResp_LineUserDetail) String() string { return proto.CompactTextString(m) }
func (*LineUserDetailResp_LineUserDetail) ProtoMessage()    {}
func (*LineUserDetailResp_LineUserDetail) Descriptor() ([]byte, []int) {
	return fileDescriptor_951bc87fae86fd79, []int{3, 0}
}

func (m *LineUserDetailResp_LineUserDetail) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LineUserDetailResp_LineUserDetail.Unmarshal(m, b)
}
func (m *LineUserDetailResp_LineUserDetail) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LineUserDetailResp_LineUserDetail.Marshal(b, m, deterministic)
}
func (m *LineUserDetailResp_LineUserDetail) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LineUserDetailResp_LineUserDetail.Merge(m, src)
}
func (m *LineUserDetailResp_LineUserDetail) XXX_Size() int {
	return xxx_messageInfo_LineUserDetailResp_LineUserDetail.Size(m)
}
func (m *LineUserDetailResp_LineUserDetail) XXX_DiscardUnknown() {
	xxx_messageInfo_LineUserDetailResp_LineUserDetail.DiscardUnknown(m)
}

var xxx_messageInfo_LineUserDetailResp_LineUserDetail proto.InternalMessageInfo

func (m *LineUserDetailResp_LineUserDetail) GetProject() string {
	if m != nil {
		return m.Project
	}
	return ""
}

func (m *LineUserDetailResp_LineUserDetail) GetLineUserId() string {
	if m != nil {
		return m.LineUserId
	}
	return ""
}

func (m *LineUserDetailResp_LineUserDetail) GetDisplayName() string {
	if m != nil {
		return m.DisplayName
	}
	return ""
}

func (m *LineUserDetailResp_LineUserDetail) GetChannelId() string {
	if m != nil {
		return m.ChannelId
	}
	return ""
}

func (m *LineUserDetailResp_LineUserDetail) GetMaintainer() string {
	if m != nil {
		return m.Maintainer
	}
	return ""
}

type LinePoolInfo struct {
	// 游戏
	Project string `protobuf:"bytes,1,opt,name=project,proto3" json:"project"`
	// 玩家 Line ID
	LineUserId string `protobuf:"bytes,2,opt,name=line_user_id,json=lineUserId,proto3" json:"line_user_id"`
	// 玩家名称 - 前端展示使用此字段
	DisplayName string `protobuf:"bytes,3,opt,name=display_name,json=displayName,proto3" json:"display_name"`
	// line channel
	ChannelId string `protobuf:"bytes,4,opt,name=channel_id,json=channelId,proto3" json:"channel_id"`
	// channel show
	ChannelShow string `protobuf:"bytes,5,opt,name=channel_show,json=channelShow,proto3" json:"channel_show"`
	// 当前维护人
	Maintainer string `protobuf:"bytes,6,opt,name=maintainer,proto3" json:"maintainer"`
	BotId      string `protobuf:"bytes,7,opt,name=bot_id,json=botId,proto3" json:"bot_id"`
	Uid        uint64 `protobuf:"varint,8,opt,name=uid,proto3" json:"uid"`
	AccountId  string `protobuf:"bytes,9,opt,name=account_id,json=accountId,proto3" json:"account_id"`
	// 服务器
	Sid string `protobuf:"bytes,10,opt,name=sid,proto3" json:"sid"`
	// 最近登录时间
	LastLogin string `protobuf:"bytes,11,opt,name=last_login,json=lastLogin,proto3" json:"last_login"`
	// 累计付费金额
	PayAll int64 `protobuf:"varint,12,opt,name=pay_all,json=payAll,proto3" json:"pay_all"`
	// 最近30天付费金额
	PayLastThirtyDays int64  `protobuf:"varint,13,opt,name=pay_last_thirty_days,json=payLastThirtyDays,proto3" json:"pay_last_thirty_days"`
	Status            uint32 `protobuf:"varint,14,opt,name=status,proto3" json:"status"`
	VipLevel          uint32 `protobuf:"varint,15,opt,name=vip_level,json=vipLevel,proto3" json:"vip_level"`
	Note              string `protobuf:"bytes,16,opt,name=note,proto3" json:"note"`
	PlayerNick        string `protobuf:"bytes,17,opt,name=player_nick,json=playerNick,proto3" json:"player_nick"`
	Birthday          string `protobuf:"bytes,18,opt,name=birthday,proto3" json:"birthday"`
	Lang              string `protobuf:"bytes,19,opt,name=lang,proto3" json:"lang"`
	// follow status : 1:follow 2:unfollow
	FollowStatus         int32    `protobuf:"varint,20,opt,name=follow_status,json=followStatus,proto3" json:"follow_status"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *LinePoolInfo) Reset()         { *m = LinePoolInfo{} }
func (m *LinePoolInfo) String() string { return proto.CompactTextString(m) }
func (*LinePoolInfo) ProtoMessage()    {}
func (*LinePoolInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_951bc87fae86fd79, []int{4}
}

func (m *LinePoolInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LinePoolInfo.Unmarshal(m, b)
}
func (m *LinePoolInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LinePoolInfo.Marshal(b, m, deterministic)
}
func (m *LinePoolInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LinePoolInfo.Merge(m, src)
}
func (m *LinePoolInfo) XXX_Size() int {
	return xxx_messageInfo_LinePoolInfo.Size(m)
}
func (m *LinePoolInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_LinePoolInfo.DiscardUnknown(m)
}

var xxx_messageInfo_LinePoolInfo proto.InternalMessageInfo

func (m *LinePoolInfo) GetProject() string {
	if m != nil {
		return m.Project
	}
	return ""
}

func (m *LinePoolInfo) GetLineUserId() string {
	if m != nil {
		return m.LineUserId
	}
	return ""
}

func (m *LinePoolInfo) GetDisplayName() string {
	if m != nil {
		return m.DisplayName
	}
	return ""
}

func (m *LinePoolInfo) GetChannelId() string {
	if m != nil {
		return m.ChannelId
	}
	return ""
}

func (m *LinePoolInfo) GetChannelShow() string {
	if m != nil {
		return m.ChannelShow
	}
	return ""
}

func (m *LinePoolInfo) GetMaintainer() string {
	if m != nil {
		return m.Maintainer
	}
	return ""
}

func (m *LinePoolInfo) GetBotId() string {
	if m != nil {
		return m.BotId
	}
	return ""
}

func (m *LinePoolInfo) GetUid() uint64 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *LinePoolInfo) GetAccountId() string {
	if m != nil {
		return m.AccountId
	}
	return ""
}

func (m *LinePoolInfo) GetSid() string {
	if m != nil {
		return m.Sid
	}
	return ""
}

func (m *LinePoolInfo) GetLastLogin() string {
	if m != nil {
		return m.LastLogin
	}
	return ""
}

func (m *LinePoolInfo) GetPayAll() int64 {
	if m != nil {
		return m.PayAll
	}
	return 0
}

func (m *LinePoolInfo) GetPayLastThirtyDays() int64 {
	if m != nil {
		return m.PayLastThirtyDays
	}
	return 0
}

func (m *LinePoolInfo) GetStatus() uint32 {
	if m != nil {
		return m.Status
	}
	return 0
}

func (m *LinePoolInfo) GetVipLevel() uint32 {
	if m != nil {
		return m.VipLevel
	}
	return 0
}

func (m *LinePoolInfo) GetNote() string {
	if m != nil {
		return m.Note
	}
	return ""
}

func (m *LinePoolInfo) GetPlayerNick() string {
	if m != nil {
		return m.PlayerNick
	}
	return ""
}

func (m *LinePoolInfo) GetBirthday() string {
	if m != nil {
		return m.Birthday
	}
	return ""
}

func (m *LinePoolInfo) GetLang() string {
	if m != nil {
		return m.Lang
	}
	return ""
}

func (m *LinePoolInfo) GetFollowStatus() int32 {
	if m != nil {
		return m.FollowStatus
	}
	return 0
}

type LineStatsReq struct {
	Project              []string `protobuf:"bytes,1,rep,name=project,proto3" json:"project"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *LineStatsReq) Reset()         { *m = LineStatsReq{} }
func (m *LineStatsReq) String() string { return proto.CompactTextString(m) }
func (*LineStatsReq) ProtoMessage()    {}
func (*LineStatsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_951bc87fae86fd79, []int{5}
}

func (m *LineStatsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LineStatsReq.Unmarshal(m, b)
}
func (m *LineStatsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LineStatsReq.Marshal(b, m, deterministic)
}
func (m *LineStatsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LineStatsReq.Merge(m, src)
}
func (m *LineStatsReq) XXX_Size() int {
	return xxx_messageInfo_LineStatsReq.Size(m)
}
func (m *LineStatsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_LineStatsReq.DiscardUnknown(m)
}

var xxx_messageInfo_LineStatsReq proto.InternalMessageInfo

func (m *LineStatsReq) GetProject() []string {
	if m != nil {
		return m.Project
	}
	return nil
}

type LineStatsResp struct {
	LineUserCount         int64    `protobuf:"varint,1,opt,name=line_user_count,json=lineUserCount,proto3" json:"line_user_count"`
	WaitReplyAccounts     int64    `protobuf:"varint,2,opt,name=wait_reply_accounts,json=waitReplyAccounts,proto3" json:"wait_reply_accounts"`
	MineWaitReplyAccounts int64    `protobuf:"varint,3,opt,name=mine_wait_reply_accounts,json=mineWaitReplyAccounts,proto3" json:"mine_wait_reply_accounts"`
	XXX_NoUnkeyedLiteral  struct{} `json:"-" gorm:"-"`
	XXX_unrecognized      []byte   `json:"-" gorm:"-"`
	XXX_sizecache         int32    `json:"-" gorm:"-"`
}

func (m *LineStatsResp) Reset()         { *m = LineStatsResp{} }
func (m *LineStatsResp) String() string { return proto.CompactTextString(m) }
func (*LineStatsResp) ProtoMessage()    {}
func (*LineStatsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_951bc87fae86fd79, []int{6}
}

func (m *LineStatsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LineStatsResp.Unmarshal(m, b)
}
func (m *LineStatsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LineStatsResp.Marshal(b, m, deterministic)
}
func (m *LineStatsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LineStatsResp.Merge(m, src)
}
func (m *LineStatsResp) XXX_Size() int {
	return xxx_messageInfo_LineStatsResp.Size(m)
}
func (m *LineStatsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_LineStatsResp.DiscardUnknown(m)
}

var xxx_messageInfo_LineStatsResp proto.InternalMessageInfo

func (m *LineStatsResp) GetLineUserCount() int64 {
	if m != nil {
		return m.LineUserCount
	}
	return 0
}

func (m *LineStatsResp) GetWaitReplyAccounts() int64 {
	if m != nil {
		return m.WaitReplyAccounts
	}
	return 0
}

func (m *LineStatsResp) GetMineWaitReplyAccounts() int64 {
	if m != nil {
		return m.MineWaitReplyAccounts
	}
	return 0
}

type LineSendTextMessageReq struct {
	// 频道id @gotags: validate:"required"
	ChannelId string `protobuf:"bytes,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id" validate:"required"`
	// 机器人id @gotags: validate:"required"
	BotId string `protobuf:"bytes,2,opt,name=bot_id,json=botId,proto3" json:"bot_id" validate:"required"`
	// 玩家的line_user_id @gotags: validate:"required"
	LineUserId string `protobuf:"bytes,3,opt,name=line_user_id,json=lineUserId,proto3" json:"line_user_id" validate:"required"`
	// 内容 @gotags: validate:"required"
	Content string `protobuf:"bytes,4,opt,name=content,proto3" json:"content" validate:"required"`
	// 表情符号，非必传
	Emojis               []*LineSendTextMessageReq_Emoji `protobuf:"bytes,5,rep,name=emojis,proto3" json:"emojis"`
	XXX_NoUnkeyedLiteral struct{}                        `json:"-" gorm:"-"`
	XXX_unrecognized     []byte                          `json:"-" gorm:"-"`
	XXX_sizecache        int32                           `json:"-" gorm:"-"`
}

func (m *LineSendTextMessageReq) Reset()         { *m = LineSendTextMessageReq{} }
func (m *LineSendTextMessageReq) String() string { return proto.CompactTextString(m) }
func (*LineSendTextMessageReq) ProtoMessage()    {}
func (*LineSendTextMessageReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_951bc87fae86fd79, []int{7}
}

func (m *LineSendTextMessageReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LineSendTextMessageReq.Unmarshal(m, b)
}
func (m *LineSendTextMessageReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LineSendTextMessageReq.Marshal(b, m, deterministic)
}
func (m *LineSendTextMessageReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LineSendTextMessageReq.Merge(m, src)
}
func (m *LineSendTextMessageReq) XXX_Size() int {
	return xxx_messageInfo_LineSendTextMessageReq.Size(m)
}
func (m *LineSendTextMessageReq) XXX_DiscardUnknown() {
	xxx_messageInfo_LineSendTextMessageReq.DiscardUnknown(m)
}

var xxx_messageInfo_LineSendTextMessageReq proto.InternalMessageInfo

func (m *LineSendTextMessageReq) GetChannelId() string {
	if m != nil {
		return m.ChannelId
	}
	return ""
}

func (m *LineSendTextMessageReq) GetBotId() string {
	if m != nil {
		return m.BotId
	}
	return ""
}

func (m *LineSendTextMessageReq) GetLineUserId() string {
	if m != nil {
		return m.LineUserId
	}
	return ""
}

func (m *LineSendTextMessageReq) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *LineSendTextMessageReq) GetEmojis() []*LineSendTextMessageReq_Emoji {
	if m != nil {
		return m.Emojis
	}
	return nil
}

type LineSendTextMessageReq_Emoji struct {
	Index                uint32   `protobuf:"varint,1,opt,name=index,proto3" json:"index"`
	Length               uint32   `protobuf:"varint,2,opt,name=length,proto3" json:"length"`
	ProductId            string   `protobuf:"bytes,3,opt,name=product_id,json=productId,proto3" json:"product_id"`
	EmojiId              string   `protobuf:"bytes,4,opt,name=emoji_id,json=emojiId,proto3" json:"emoji_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *LineSendTextMessageReq_Emoji) Reset()         { *m = LineSendTextMessageReq_Emoji{} }
func (m *LineSendTextMessageReq_Emoji) String() string { return proto.CompactTextString(m) }
func (*LineSendTextMessageReq_Emoji) ProtoMessage()    {}
func (*LineSendTextMessageReq_Emoji) Descriptor() ([]byte, []int) {
	return fileDescriptor_951bc87fae86fd79, []int{7, 0}
}

func (m *LineSendTextMessageReq_Emoji) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LineSendTextMessageReq_Emoji.Unmarshal(m, b)
}
func (m *LineSendTextMessageReq_Emoji) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LineSendTextMessageReq_Emoji.Marshal(b, m, deterministic)
}
func (m *LineSendTextMessageReq_Emoji) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LineSendTextMessageReq_Emoji.Merge(m, src)
}
func (m *LineSendTextMessageReq_Emoji) XXX_Size() int {
	return xxx_messageInfo_LineSendTextMessageReq_Emoji.Size(m)
}
func (m *LineSendTextMessageReq_Emoji) XXX_DiscardUnknown() {
	xxx_messageInfo_LineSendTextMessageReq_Emoji.DiscardUnknown(m)
}

var xxx_messageInfo_LineSendTextMessageReq_Emoji proto.InternalMessageInfo

func (m *LineSendTextMessageReq_Emoji) GetIndex() uint32 {
	if m != nil {
		return m.Index
	}
	return 0
}

func (m *LineSendTextMessageReq_Emoji) GetLength() uint32 {
	if m != nil {
		return m.Length
	}
	return 0
}

func (m *LineSendTextMessageReq_Emoji) GetProductId() string {
	if m != nil {
		return m.ProductId
	}
	return ""
}

func (m *LineSendTextMessageReq_Emoji) GetEmojiId() string {
	if m != nil {
		return m.EmojiId
	}
	return ""
}

type LineSendMessageResp struct {
	// 消息id
	MsgId string `protobuf:"bytes,1,opt,name=msg_id,json=msgId,proto3" json:"msg_id"`
	// 发送渠道
	ChannelId            string   `protobuf:"bytes,2,opt,name=channel_id,json=channelId,proto3" json:"channel_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *LineSendMessageResp) Reset()         { *m = LineSendMessageResp{} }
func (m *LineSendMessageResp) String() string { return proto.CompactTextString(m) }
func (*LineSendMessageResp) ProtoMessage()    {}
func (*LineSendMessageResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_951bc87fae86fd79, []int{8}
}

func (m *LineSendMessageResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LineSendMessageResp.Unmarshal(m, b)
}
func (m *LineSendMessageResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LineSendMessageResp.Marshal(b, m, deterministic)
}
func (m *LineSendMessageResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LineSendMessageResp.Merge(m, src)
}
func (m *LineSendMessageResp) XXX_Size() int {
	return xxx_messageInfo_LineSendMessageResp.Size(m)
}
func (m *LineSendMessageResp) XXX_DiscardUnknown() {
	xxx_messageInfo_LineSendMessageResp.DiscardUnknown(m)
}

var xxx_messageInfo_LineSendMessageResp proto.InternalMessageInfo

func (m *LineSendMessageResp) GetMsgId() string {
	if m != nil {
		return m.MsgId
	}
	return ""
}

func (m *LineSendMessageResp) GetChannelId() string {
	if m != nil {
		return m.ChannelId
	}
	return ""
}

type LineSendFileReq struct {
	// 频道id
	ChannelId string `protobuf:"bytes,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id"`
	// 机器人id
	BotId string `protobuf:"bytes,2,opt,name=bot_id,json=botId,proto3" json:"bot_id"`
	// 玩家的line_user_id
	LineUserId string `protobuf:"bytes,3,opt,name=line_user_id,json=lineUserId,proto3" json:"line_user_id"`
	// 文件类型
	FileType string `protobuf:"bytes,4,opt,name=file_type,json=fileType,proto3" json:"file_type"`
	// 文件格式
	Format               string   `protobuf:"bytes,5,opt,name=format,proto3" json:"format"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *LineSendFileReq) Reset()         { *m = LineSendFileReq{} }
func (m *LineSendFileReq) String() string { return proto.CompactTextString(m) }
func (*LineSendFileReq) ProtoMessage()    {}
func (*LineSendFileReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_951bc87fae86fd79, []int{9}
}

func (m *LineSendFileReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LineSendFileReq.Unmarshal(m, b)
}
func (m *LineSendFileReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LineSendFileReq.Marshal(b, m, deterministic)
}
func (m *LineSendFileReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LineSendFileReq.Merge(m, src)
}
func (m *LineSendFileReq) XXX_Size() int {
	return xxx_messageInfo_LineSendFileReq.Size(m)
}
func (m *LineSendFileReq) XXX_DiscardUnknown() {
	xxx_messageInfo_LineSendFileReq.DiscardUnknown(m)
}

var xxx_messageInfo_LineSendFileReq proto.InternalMessageInfo

func (m *LineSendFileReq) GetChannelId() string {
	if m != nil {
		return m.ChannelId
	}
	return ""
}

func (m *LineSendFileReq) GetBotId() string {
	if m != nil {
		return m.BotId
	}
	return ""
}

func (m *LineSendFileReq) GetLineUserId() string {
	if m != nil {
		return m.LineUserId
	}
	return ""
}

func (m *LineSendFileReq) GetFileType() string {
	if m != nil {
		return m.FileType
	}
	return ""
}

func (m *LineSendFileReq) GetFormat() string {
	if m != nil {
		return m.Format
	}
	return ""
}

type LineDialogueHistoryReq struct {
	// 频道id @gotags: validate:"required"
	ChannelId string `protobuf:"bytes,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id" validate:"required"`
	// 机器人id @gotags: validate:"required"
	BotId string `protobuf:"bytes,2,opt,name=bot_id,json=botId,proto3" json:"bot_id" validate:"required"`
	// 玩家的line_user_id @gotags: validate:"required"
	LineUserId string `protobuf:"bytes,3,opt,name=line_user_id,json=lineUserId,proto3" json:"line_user_id" validate:"required"`
	Before     bool   `protobuf:"varint,4,opt,name=before,proto3" json:"before"`
	// 消息的时间戳
	TickTime             uint64   `protobuf:"varint,5,opt,name=tick_time,json=tickTime,proto3" json:"tick_time"`
	Page                 uint32   `protobuf:"varint,6,opt,name=page,proto3" json:"page"`
	PageSize             uint32   `protobuf:"varint,7,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *LineDialogueHistoryReq) Reset()         { *m = LineDialogueHistoryReq{} }
func (m *LineDialogueHistoryReq) String() string { return proto.CompactTextString(m) }
func (*LineDialogueHistoryReq) ProtoMessage()    {}
func (*LineDialogueHistoryReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_951bc87fae86fd79, []int{10}
}

func (m *LineDialogueHistoryReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LineDialogueHistoryReq.Unmarshal(m, b)
}
func (m *LineDialogueHistoryReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LineDialogueHistoryReq.Marshal(b, m, deterministic)
}
func (m *LineDialogueHistoryReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LineDialogueHistoryReq.Merge(m, src)
}
func (m *LineDialogueHistoryReq) XXX_Size() int {
	return xxx_messageInfo_LineDialogueHistoryReq.Size(m)
}
func (m *LineDialogueHistoryReq) XXX_DiscardUnknown() {
	xxx_messageInfo_LineDialogueHistoryReq.DiscardUnknown(m)
}

var xxx_messageInfo_LineDialogueHistoryReq proto.InternalMessageInfo

func (m *LineDialogueHistoryReq) GetChannelId() string {
	if m != nil {
		return m.ChannelId
	}
	return ""
}

func (m *LineDialogueHistoryReq) GetBotId() string {
	if m != nil {
		return m.BotId
	}
	return ""
}

func (m *LineDialogueHistoryReq) GetLineUserId() string {
	if m != nil {
		return m.LineUserId
	}
	return ""
}

func (m *LineDialogueHistoryReq) GetBefore() bool {
	if m != nil {
		return m.Before
	}
	return false
}

func (m *LineDialogueHistoryReq) GetTickTime() uint64 {
	if m != nil {
		return m.TickTime
	}
	return 0
}

func (m *LineDialogueHistoryReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *LineDialogueHistoryReq) GetPageSize() uint32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

type LineDialogueHistoryResp struct {
	CurrentPage          uint32                              `protobuf:"varint,1,opt,name=current_page,json=currentPage,proto3" json:"current_page"`
	PerPage              uint32                              `protobuf:"varint,2,opt,name=per_page,json=perPage,proto3" json:"per_page"`
	Total                uint32                              `protobuf:"varint,3,opt,name=total,proto3" json:"total"`
	Data                 []*LineDialogueHistoryResp_Dialogue `protobuf:"bytes,4,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}                            `json:"-" gorm:"-"`
	XXX_unrecognized     []byte                              `json:"-" gorm:"-"`
	XXX_sizecache        int32                               `json:"-" gorm:"-"`
}

func (m *LineDialogueHistoryResp) Reset()         { *m = LineDialogueHistoryResp{} }
func (m *LineDialogueHistoryResp) String() string { return proto.CompactTextString(m) }
func (*LineDialogueHistoryResp) ProtoMessage()    {}
func (*LineDialogueHistoryResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_951bc87fae86fd79, []int{11}
}

func (m *LineDialogueHistoryResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LineDialogueHistoryResp.Unmarshal(m, b)
}
func (m *LineDialogueHistoryResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LineDialogueHistoryResp.Marshal(b, m, deterministic)
}
func (m *LineDialogueHistoryResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LineDialogueHistoryResp.Merge(m, src)
}
func (m *LineDialogueHistoryResp) XXX_Size() int {
	return xxx_messageInfo_LineDialogueHistoryResp.Size(m)
}
func (m *LineDialogueHistoryResp) XXX_DiscardUnknown() {
	xxx_messageInfo_LineDialogueHistoryResp.DiscardUnknown(m)
}

var xxx_messageInfo_LineDialogueHistoryResp proto.InternalMessageInfo

func (m *LineDialogueHistoryResp) GetCurrentPage() uint32 {
	if m != nil {
		return m.CurrentPage
	}
	return 0
}

func (m *LineDialogueHistoryResp) GetPerPage() uint32 {
	if m != nil {
		return m.PerPage
	}
	return 0
}

func (m *LineDialogueHistoryResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *LineDialogueHistoryResp) GetData() []*LineDialogueHistoryResp_Dialogue {
	if m != nil {
		return m.Data
	}
	return nil
}

type LineDialogueHistoryResp_Dialogue struct {
	MessageId  string `protobuf:"bytes,1,opt,name=message_id,json=messageId,proto3" json:"message_id"`
	BotId      string `protobuf:"bytes,2,opt,name=bot_id,json=botId,proto3" json:"bot_id"`
	LineUserId string `protobuf:"bytes,3,opt,name=line_user_id,json=lineUserId,proto3" json:"line_user_id"`
	// 玩家昵称
	DisplayName string `protobuf:"bytes,4,opt,name=display_name,json=displayName,proto3" json:"display_name"`
	ChannelId   string `protobuf:"bytes,5,opt,name=channel_id,json=channelId,proto3" json:"channel_id"`
	// 消息来源 player/service
	MessageFrom string `protobuf:"bytes,6,opt,name=message_from,json=messageFrom,proto3" json:"message_from"`
	// 发送者 客服账号
	Sender      string `protobuf:"bytes,7,opt,name=sender,proto3" json:"sender"`
	MessageType string `protobuf:"bytes,8,opt,name=message_type,json=messageType,proto3" json:"message_type"`
	// 引用token, 引用回复需要此字段
	QuoteToken string `protobuf:"bytes,9,opt,name=quote_token,json=quoteToken,proto3" json:"quote_token"`
	Content    string `protobuf:"bytes,10,opt,name=content,proto3" json:"content"`
	// 消息是否已被玩家撤回
	Revoke bool `protobuf:"varint,11,opt,name=revoke,proto3" json:"revoke"`
	// 原始文件(视频/图片/音频)url
	OriginalContentUrl string `protobuf:"bytes,12,opt,name=original_content_url,json=originalContentUrl,proto3" json:"original_content_url"`
	// 文件预览图url
	PreviewImageUrl string `protobuf:"bytes,13,opt,name=preview_image_url,json=previewImageUrl,proto3" json:"preview_image_url"`
	// 音频时长
	Duration int32 `protobuf:"varint,14,opt,name=duration,proto3" json:"duration"`
	// 贴纸信息
	StickerInfo string `protobuf:"bytes,15,opt,name=sticker_info,json=stickerInfo,proto3" json:"sticker_info"`
	// 位置信息
	Location string `protobuf:"bytes,16,opt,name=location,proto3" json:"location"`
	// 消息的时间戳
	TickTime             uint64   `protobuf:"varint,17,opt,name=tick_time,json=tickTime,proto3" json:"tick_time"`
	CreateTime           string   `protobuf:"bytes,18,opt,name=create_time,json=createTime,proto3" json:"create_time"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *LineDialogueHistoryResp_Dialogue) Reset()         { *m = LineDialogueHistoryResp_Dialogue{} }
func (m *LineDialogueHistoryResp_Dialogue) String() string { return proto.CompactTextString(m) }
func (*LineDialogueHistoryResp_Dialogue) ProtoMessage()    {}
func (*LineDialogueHistoryResp_Dialogue) Descriptor() ([]byte, []int) {
	return fileDescriptor_951bc87fae86fd79, []int{11, 0}
}

func (m *LineDialogueHistoryResp_Dialogue) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LineDialogueHistoryResp_Dialogue.Unmarshal(m, b)
}
func (m *LineDialogueHistoryResp_Dialogue) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LineDialogueHistoryResp_Dialogue.Marshal(b, m, deterministic)
}
func (m *LineDialogueHistoryResp_Dialogue) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LineDialogueHistoryResp_Dialogue.Merge(m, src)
}
func (m *LineDialogueHistoryResp_Dialogue) XXX_Size() int {
	return xxx_messageInfo_LineDialogueHistoryResp_Dialogue.Size(m)
}
func (m *LineDialogueHistoryResp_Dialogue) XXX_DiscardUnknown() {
	xxx_messageInfo_LineDialogueHistoryResp_Dialogue.DiscardUnknown(m)
}

var xxx_messageInfo_LineDialogueHistoryResp_Dialogue proto.InternalMessageInfo

func (m *LineDialogueHistoryResp_Dialogue) GetMessageId() string {
	if m != nil {
		return m.MessageId
	}
	return ""
}

func (m *LineDialogueHistoryResp_Dialogue) GetBotId() string {
	if m != nil {
		return m.BotId
	}
	return ""
}

func (m *LineDialogueHistoryResp_Dialogue) GetLineUserId() string {
	if m != nil {
		return m.LineUserId
	}
	return ""
}

func (m *LineDialogueHistoryResp_Dialogue) GetDisplayName() string {
	if m != nil {
		return m.DisplayName
	}
	return ""
}

func (m *LineDialogueHistoryResp_Dialogue) GetChannelId() string {
	if m != nil {
		return m.ChannelId
	}
	return ""
}

func (m *LineDialogueHistoryResp_Dialogue) GetMessageFrom() string {
	if m != nil {
		return m.MessageFrom
	}
	return ""
}

func (m *LineDialogueHistoryResp_Dialogue) GetSender() string {
	if m != nil {
		return m.Sender
	}
	return ""
}

func (m *LineDialogueHistoryResp_Dialogue) GetMessageType() string {
	if m != nil {
		return m.MessageType
	}
	return ""
}

func (m *LineDialogueHistoryResp_Dialogue) GetQuoteToken() string {
	if m != nil {
		return m.QuoteToken
	}
	return ""
}

func (m *LineDialogueHistoryResp_Dialogue) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *LineDialogueHistoryResp_Dialogue) GetRevoke() bool {
	if m != nil {
		return m.Revoke
	}
	return false
}

func (m *LineDialogueHistoryResp_Dialogue) GetOriginalContentUrl() string {
	if m != nil {
		return m.OriginalContentUrl
	}
	return ""
}

func (m *LineDialogueHistoryResp_Dialogue) GetPreviewImageUrl() string {
	if m != nil {
		return m.PreviewImageUrl
	}
	return ""
}

func (m *LineDialogueHistoryResp_Dialogue) GetDuration() int32 {
	if m != nil {
		return m.Duration
	}
	return 0
}

func (m *LineDialogueHistoryResp_Dialogue) GetStickerInfo() string {
	if m != nil {
		return m.StickerInfo
	}
	return ""
}

func (m *LineDialogueHistoryResp_Dialogue) GetLocation() string {
	if m != nil {
		return m.Location
	}
	return ""
}

func (m *LineDialogueHistoryResp_Dialogue) GetTickTime() uint64 {
	if m != nil {
		return m.TickTime
	}
	return 0
}

func (m *LineDialogueHistoryResp_Dialogue) GetCreateTime() string {
	if m != nil {
		return m.CreateTime
	}
	return ""
}

type LineDialogFreshReq struct {
	// 频道id @gotags: validate:"required"
	ChannelId string `protobuf:"bytes,1,opt,name=channel_id,json=channelId,proto3" json:"channel_id" validate:"required"`
	// 机器人id @gotags: validate:"required"
	BotId string `protobuf:"bytes,2,opt,name=bot_id,json=botId,proto3" json:"bot_id" validate:"required"`
	// 玩家的line_user_id @gotags: validate:"required"
	LineUserId string `protobuf:"bytes,3,opt,name=line_user_id,json=lineUserId,proto3" json:"line_user_id" validate:"required"`
	// 向下翻页 - 开始消息id对应的时间戳
	TickTime             int64    `protobuf:"varint,4,opt,name=tick_time,json=tickTime,proto3" json:"tick_time"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *LineDialogFreshReq) Reset()         { *m = LineDialogFreshReq{} }
func (m *LineDialogFreshReq) String() string { return proto.CompactTextString(m) }
func (*LineDialogFreshReq) ProtoMessage()    {}
func (*LineDialogFreshReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_951bc87fae86fd79, []int{12}
}

func (m *LineDialogFreshReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LineDialogFreshReq.Unmarshal(m, b)
}
func (m *LineDialogFreshReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LineDialogFreshReq.Marshal(b, m, deterministic)
}
func (m *LineDialogFreshReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LineDialogFreshReq.Merge(m, src)
}
func (m *LineDialogFreshReq) XXX_Size() int {
	return xxx_messageInfo_LineDialogFreshReq.Size(m)
}
func (m *LineDialogFreshReq) XXX_DiscardUnknown() {
	xxx_messageInfo_LineDialogFreshReq.DiscardUnknown(m)
}

var xxx_messageInfo_LineDialogFreshReq proto.InternalMessageInfo

func (m *LineDialogFreshReq) GetChannelId() string {
	if m != nil {
		return m.ChannelId
	}
	return ""
}

func (m *LineDialogFreshReq) GetBotId() string {
	if m != nil {
		return m.BotId
	}
	return ""
}

func (m *LineDialogFreshReq) GetLineUserId() string {
	if m != nil {
		return m.LineUserId
	}
	return ""
}

func (m *LineDialogFreshReq) GetTickTime() int64 {
	if m != nil {
		return m.TickTime
	}
	return 0
}

// LinePortraitInfoReq 获取玩家画像信息
type LinePortraitInfoReq struct {
	// @gotags: validate:"required"
	LineUserId string `protobuf:"bytes,1,opt,name=line_user_id,json=lineUserId,proto3" json:"line_user_id" validate:"required"`
	// @gotags: validate:"required"
	Project              string   `protobuf:"bytes,2,opt,name=project,proto3" json:"project" validate:"required"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *LinePortraitInfoReq) Reset()         { *m = LinePortraitInfoReq{} }
func (m *LinePortraitInfoReq) String() string { return proto.CompactTextString(m) }
func (*LinePortraitInfoReq) ProtoMessage()    {}
func (*LinePortraitInfoReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_951bc87fae86fd79, []int{13}
}

func (m *LinePortraitInfoReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LinePortraitInfoReq.Unmarshal(m, b)
}
func (m *LinePortraitInfoReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LinePortraitInfoReq.Marshal(b, m, deterministic)
}
func (m *LinePortraitInfoReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LinePortraitInfoReq.Merge(m, src)
}
func (m *LinePortraitInfoReq) XXX_Size() int {
	return xxx_messageInfo_LinePortraitInfoReq.Size(m)
}
func (m *LinePortraitInfoReq) XXX_DiscardUnknown() {
	xxx_messageInfo_LinePortraitInfoReq.DiscardUnknown(m)
}

var xxx_messageInfo_LinePortraitInfoReq proto.InternalMessageInfo

func (m *LinePortraitInfoReq) GetLineUserId() string {
	if m != nil {
		return m.LineUserId
	}
	return ""
}

func (m *LinePortraitInfoReq) GetProject() string {
	if m != nil {
		return m.Project
	}
	return ""
}

type LinePortraitInfoResp struct {
	Id                   uint64                              `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	Label                []*LinePortraitInfoResp_LabelDetail `protobuf:"bytes,2,rep,name=label,proto3" json:"label"`
	Gender               uint32                              `protobuf:"varint,3,opt,name=gender,proto3" json:"gender"`
	Birthday             string                              `protobuf:"bytes,4,opt,name=birthday,proto3" json:"birthday"`
	Career               string                              `protobuf:"bytes,5,opt,name=career,proto3" json:"career"`
	EducationLevel       uint32                              `protobuf:"varint,6,opt,name=education_level,json=educationLevel,proto3" json:"education_level"`
	MarriedState         uint32                              `protobuf:"varint,7,opt,name=married_state,json=marriedState,proto3" json:"married_state"`
	FertilityState       uint32                              `protobuf:"varint,8,opt,name=fertility_state,json=fertilityState,proto3" json:"fertility_state"`
	Remark               string                              `protobuf:"bytes,9,opt,name=remark,proto3" json:"remark"`
	LineUserId           string                              `protobuf:"bytes,10,opt,name=line_user_id,json=lineUserId,proto3" json:"line_user_id"`
	Project              string                              `protobuf:"bytes,11,opt,name=project,proto3" json:"project"`
	XXX_NoUnkeyedLiteral struct{}                            `json:"-" gorm:"-"`
	XXX_unrecognized     []byte                              `json:"-" gorm:"-"`
	XXX_sizecache        int32                               `json:"-" gorm:"-"`
}

func (m *LinePortraitInfoResp) Reset()         { *m = LinePortraitInfoResp{} }
func (m *LinePortraitInfoResp) String() string { return proto.CompactTextString(m) }
func (*LinePortraitInfoResp) ProtoMessage()    {}
func (*LinePortraitInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_951bc87fae86fd79, []int{14}
}

func (m *LinePortraitInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LinePortraitInfoResp.Unmarshal(m, b)
}
func (m *LinePortraitInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LinePortraitInfoResp.Marshal(b, m, deterministic)
}
func (m *LinePortraitInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LinePortraitInfoResp.Merge(m, src)
}
func (m *LinePortraitInfoResp) XXX_Size() int {
	return xxx_messageInfo_LinePortraitInfoResp.Size(m)
}
func (m *LinePortraitInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_LinePortraitInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_LinePortraitInfoResp proto.InternalMessageInfo

func (m *LinePortraitInfoResp) GetId() uint64 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *LinePortraitInfoResp) GetLabel() []*LinePortraitInfoResp_LabelDetail {
	if m != nil {
		return m.Label
	}
	return nil
}

func (m *LinePortraitInfoResp) GetGender() uint32 {
	if m != nil {
		return m.Gender
	}
	return 0
}

func (m *LinePortraitInfoResp) GetBirthday() string {
	if m != nil {
		return m.Birthday
	}
	return ""
}

func (m *LinePortraitInfoResp) GetCareer() string {
	if m != nil {
		return m.Career
	}
	return ""
}

func (m *LinePortraitInfoResp) GetEducationLevel() uint32 {
	if m != nil {
		return m.EducationLevel
	}
	return 0
}

func (m *LinePortraitInfoResp) GetMarriedState() uint32 {
	if m != nil {
		return m.MarriedState
	}
	return 0
}

func (m *LinePortraitInfoResp) GetFertilityState() uint32 {
	if m != nil {
		return m.FertilityState
	}
	return 0
}

func (m *LinePortraitInfoResp) GetRemark() string {
	if m != nil {
		return m.Remark
	}
	return ""
}

func (m *LinePortraitInfoResp) GetLineUserId() string {
	if m != nil {
		return m.LineUserId
	}
	return ""
}

func (m *LinePortraitInfoResp) GetProject() string {
	if m != nil {
		return m.Project
	}
	return ""
}

type LinePortraitInfoResp_LabelDetail struct {
	TagId                uint64   `protobuf:"varint,1,opt,name=tag_id,json=tagId,proto3" json:"tag_id"`
	TagDesc              string   `protobuf:"bytes,2,opt,name=tag_desc,json=tagDesc,proto3" json:"tag_desc"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *LinePortraitInfoResp_LabelDetail) Reset()         { *m = LinePortraitInfoResp_LabelDetail{} }
func (m *LinePortraitInfoResp_LabelDetail) String() string { return proto.CompactTextString(m) }
func (*LinePortraitInfoResp_LabelDetail) ProtoMessage()    {}
func (*LinePortraitInfoResp_LabelDetail) Descriptor() ([]byte, []int) {
	return fileDescriptor_951bc87fae86fd79, []int{14, 0}
}

func (m *LinePortraitInfoResp_LabelDetail) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LinePortraitInfoResp_LabelDetail.Unmarshal(m, b)
}
func (m *LinePortraitInfoResp_LabelDetail) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LinePortraitInfoResp_LabelDetail.Marshal(b, m, deterministic)
}
func (m *LinePortraitInfoResp_LabelDetail) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LinePortraitInfoResp_LabelDetail.Merge(m, src)
}
func (m *LinePortraitInfoResp_LabelDetail) XXX_Size() int {
	return xxx_messageInfo_LinePortraitInfoResp_LabelDetail.Size(m)
}
func (m *LinePortraitInfoResp_LabelDetail) XXX_DiscardUnknown() {
	xxx_messageInfo_LinePortraitInfoResp_LabelDetail.DiscardUnknown(m)
}

var xxx_messageInfo_LinePortraitInfoResp_LabelDetail proto.InternalMessageInfo

func (m *LinePortraitInfoResp_LabelDetail) GetTagId() uint64 {
	if m != nil {
		return m.TagId
	}
	return 0
}

func (m *LinePortraitInfoResp_LabelDetail) GetTagDesc() string {
	if m != nil {
		return m.TagDesc
	}
	return ""
}

// LinePortraitEditReq 添加/编辑 画像标签信息
type LinePortraitEditTagReq struct {
	Id uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	// @gotags: validate:"required_without=Id"
	Project string `protobuf:"bytes,2,opt,name=project,proto3" json:"project" validate:"required_without=Id"`
	Tag     string `protobuf:"bytes,3,opt,name=tag,proto3" json:"tag"`
	// @gotags: validate:"required_without=Id"
	LineUserId           string   `protobuf:"bytes,4,opt,name=line_user_id,json=lineUserId,proto3" json:"line_user_id" validate:"required_without=Id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *LinePortraitEditTagReq) Reset()         { *m = LinePortraitEditTagReq{} }
func (m *LinePortraitEditTagReq) String() string { return proto.CompactTextString(m) }
func (*LinePortraitEditTagReq) ProtoMessage()    {}
func (*LinePortraitEditTagReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_951bc87fae86fd79, []int{15}
}

func (m *LinePortraitEditTagReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LinePortraitEditTagReq.Unmarshal(m, b)
}
func (m *LinePortraitEditTagReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LinePortraitEditTagReq.Marshal(b, m, deterministic)
}
func (m *LinePortraitEditTagReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LinePortraitEditTagReq.Merge(m, src)
}
func (m *LinePortraitEditTagReq) XXX_Size() int {
	return xxx_messageInfo_LinePortraitEditTagReq.Size(m)
}
func (m *LinePortraitEditTagReq) XXX_DiscardUnknown() {
	xxx_messageInfo_LinePortraitEditTagReq.DiscardUnknown(m)
}

var xxx_messageInfo_LinePortraitEditTagReq proto.InternalMessageInfo

func (m *LinePortraitEditTagReq) GetId() uint64 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *LinePortraitEditTagReq) GetProject() string {
	if m != nil {
		return m.Project
	}
	return ""
}

func (m *LinePortraitEditTagReq) GetTag() string {
	if m != nil {
		return m.Tag
	}
	return ""
}

func (m *LinePortraitEditTagReq) GetLineUserId() string {
	if m != nil {
		return m.LineUserId
	}
	return ""
}

// LinePortraitEditReq 添加/编辑 画像基础属性信息
type LinePortraitEditBasicReq struct {
	Id uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	// @gotags: validate:"required_without=Id"
	Project        string `protobuf:"bytes,2,opt,name=project,proto3" json:"project" validate:"required_without=Id"`
	Gender         uint32 `protobuf:"varint,3,opt,name=gender,proto3" json:"gender"`
	Birthday       string `protobuf:"bytes,4,opt,name=birthday,proto3" json:"birthday"`
	Career         string `protobuf:"bytes,5,opt,name=career,proto3" json:"career"`
	EducationLevel uint32 `protobuf:"varint,6,opt,name=education_level,json=educationLevel,proto3" json:"education_level"`
	MarriedState   uint32 `protobuf:"varint,7,opt,name=married_state,json=marriedState,proto3" json:"married_state"`
	FertilityState uint32 `protobuf:"varint,8,opt,name=fertility_state,json=fertilityState,proto3" json:"fertility_state"`
	// @gotags: validate:"required_without=Id"
	LineUserId           string   `protobuf:"bytes,9,opt,name=line_user_id,json=lineUserId,proto3" json:"line_user_id" validate:"required_without=Id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *LinePortraitEditBasicReq) Reset()         { *m = LinePortraitEditBasicReq{} }
func (m *LinePortraitEditBasicReq) String() string { return proto.CompactTextString(m) }
func (*LinePortraitEditBasicReq) ProtoMessage()    {}
func (*LinePortraitEditBasicReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_951bc87fae86fd79, []int{16}
}

func (m *LinePortraitEditBasicReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LinePortraitEditBasicReq.Unmarshal(m, b)
}
func (m *LinePortraitEditBasicReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LinePortraitEditBasicReq.Marshal(b, m, deterministic)
}
func (m *LinePortraitEditBasicReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LinePortraitEditBasicReq.Merge(m, src)
}
func (m *LinePortraitEditBasicReq) XXX_Size() int {
	return xxx_messageInfo_LinePortraitEditBasicReq.Size(m)
}
func (m *LinePortraitEditBasicReq) XXX_DiscardUnknown() {
	xxx_messageInfo_LinePortraitEditBasicReq.DiscardUnknown(m)
}

var xxx_messageInfo_LinePortraitEditBasicReq proto.InternalMessageInfo

func (m *LinePortraitEditBasicReq) GetId() uint64 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *LinePortraitEditBasicReq) GetProject() string {
	if m != nil {
		return m.Project
	}
	return ""
}

func (m *LinePortraitEditBasicReq) GetGender() uint32 {
	if m != nil {
		return m.Gender
	}
	return 0
}

func (m *LinePortraitEditBasicReq) GetBirthday() string {
	if m != nil {
		return m.Birthday
	}
	return ""
}

func (m *LinePortraitEditBasicReq) GetCareer() string {
	if m != nil {
		return m.Career
	}
	return ""
}

func (m *LinePortraitEditBasicReq) GetEducationLevel() uint32 {
	if m != nil {
		return m.EducationLevel
	}
	return 0
}

func (m *LinePortraitEditBasicReq) GetMarriedState() uint32 {
	if m != nil {
		return m.MarriedState
	}
	return 0
}

func (m *LinePortraitEditBasicReq) GetFertilityState() uint32 {
	if m != nil {
		return m.FertilityState
	}
	return 0
}

func (m *LinePortraitEditBasicReq) GetLineUserId() string {
	if m != nil {
		return m.LineUserId
	}
	return ""
}

// LinePortraitEditReq 添加/编辑 画像备注信息
type LinePortraitEditRemarkReq struct {
	Id uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	// @gotags: validate:"required_without=Id"
	Project string `protobuf:"bytes,2,opt,name=project,proto3" json:"project" validate:"required_without=Id"`
	Remark  string `protobuf:"bytes,3,opt,name=remark,proto3" json:"remark"`
	// @gotags: validate:"required_without=Id"
	LineUserId           string   `protobuf:"bytes,4,opt,name=line_user_id,json=lineUserId,proto3" json:"line_user_id" validate:"required_without=Id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *LinePortraitEditRemarkReq) Reset()         { *m = LinePortraitEditRemarkReq{} }
func (m *LinePortraitEditRemarkReq) String() string { return proto.CompactTextString(m) }
func (*LinePortraitEditRemarkReq) ProtoMessage()    {}
func (*LinePortraitEditRemarkReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_951bc87fae86fd79, []int{17}
}

func (m *LinePortraitEditRemarkReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LinePortraitEditRemarkReq.Unmarshal(m, b)
}
func (m *LinePortraitEditRemarkReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LinePortraitEditRemarkReq.Marshal(b, m, deterministic)
}
func (m *LinePortraitEditRemarkReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LinePortraitEditRemarkReq.Merge(m, src)
}
func (m *LinePortraitEditRemarkReq) XXX_Size() int {
	return xxx_messageInfo_LinePortraitEditRemarkReq.Size(m)
}
func (m *LinePortraitEditRemarkReq) XXX_DiscardUnknown() {
	xxx_messageInfo_LinePortraitEditRemarkReq.DiscardUnknown(m)
}

var xxx_messageInfo_LinePortraitEditRemarkReq proto.InternalMessageInfo

func (m *LinePortraitEditRemarkReq) GetId() uint64 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *LinePortraitEditRemarkReq) GetProject() string {
	if m != nil {
		return m.Project
	}
	return ""
}

func (m *LinePortraitEditRemarkReq) GetRemark() string {
	if m != nil {
		return m.Remark
	}
	return ""
}

func (m *LinePortraitEditRemarkReq) GetLineUserId() string {
	if m != nil {
		return m.LineUserId
	}
	return ""
}

// LineMaintainConfigDelReq 删除玩家维护配置
type LineMaintainConfigDelReq struct {
	// id @gotags: validate:"required"
	Id uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id" validate:"required"`
	// @gotags: validate:"required"
	LineUserId string `protobuf:"bytes,2,opt,name=line_user_id,json=lineUserId,proto3" json:"line_user_id" validate:"required"`
	//  @gotags: validate:"required"
	Project              string   `protobuf:"bytes,3,opt,name=project,proto3" json:"project" validate:"required"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *LineMaintainConfigDelReq) Reset()         { *m = LineMaintainConfigDelReq{} }
func (m *LineMaintainConfigDelReq) String() string { return proto.CompactTextString(m) }
func (*LineMaintainConfigDelReq) ProtoMessage()    {}
func (*LineMaintainConfigDelReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_951bc87fae86fd79, []int{18}
}

func (m *LineMaintainConfigDelReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LineMaintainConfigDelReq.Unmarshal(m, b)
}
func (m *LineMaintainConfigDelReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LineMaintainConfigDelReq.Marshal(b, m, deterministic)
}
func (m *LineMaintainConfigDelReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LineMaintainConfigDelReq.Merge(m, src)
}
func (m *LineMaintainConfigDelReq) XXX_Size() int {
	return xxx_messageInfo_LineMaintainConfigDelReq.Size(m)
}
func (m *LineMaintainConfigDelReq) XXX_DiscardUnknown() {
	xxx_messageInfo_LineMaintainConfigDelReq.DiscardUnknown(m)
}

var xxx_messageInfo_LineMaintainConfigDelReq proto.InternalMessageInfo

func (m *LineMaintainConfigDelReq) GetId() uint64 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *LineMaintainConfigDelReq) GetLineUserId() string {
	if m != nil {
		return m.LineUserId
	}
	return ""
}

func (m *LineMaintainConfigDelReq) GetProject() string {
	if m != nil {
		return m.Project
	}
	return ""
}

// LineMaintainConfigListReq 玩家维护配置查询请求参数
type LineMaintainConfigListReq struct {
	// 游戏, 支持多选
	Project []string `protobuf:"bytes,1,rep,name=project,proto3" json:"project"`
	// 维护专员, 支持多选
	Maintainer []string `protobuf:"bytes,2,rep,name=maintainer,proto3" json:"maintainer"`
	// 昵称, 支持多选
	NickName []string `protobuf:"bytes,3,rep,name=nick_name,json=nickName,proto3" json:"nick_name"`
	// 玩家账号,单选
	LineUserId           string   `protobuf:"bytes,4,opt,name=Line_user_id,json=LineUserId,proto3" json:"Line_user_id"`
	VipState             uint32   `protobuf:"varint,5,opt,name=vip_state,json=vipState,proto3" json:"vip_state"`
	Page                 uint32   `protobuf:"varint,6,opt,name=page,proto3" json:"page"`
	PageSize             uint32   `protobuf:"varint,7,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *LineMaintainConfigListReq) Reset()         { *m = LineMaintainConfigListReq{} }
func (m *LineMaintainConfigListReq) String() string { return proto.CompactTextString(m) }
func (*LineMaintainConfigListReq) ProtoMessage()    {}
func (*LineMaintainConfigListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_951bc87fae86fd79, []int{19}
}

func (m *LineMaintainConfigListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LineMaintainConfigListReq.Unmarshal(m, b)
}
func (m *LineMaintainConfigListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LineMaintainConfigListReq.Marshal(b, m, deterministic)
}
func (m *LineMaintainConfigListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LineMaintainConfigListReq.Merge(m, src)
}
func (m *LineMaintainConfigListReq) XXX_Size() int {
	return xxx_messageInfo_LineMaintainConfigListReq.Size(m)
}
func (m *LineMaintainConfigListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_LineMaintainConfigListReq.DiscardUnknown(m)
}

var xxx_messageInfo_LineMaintainConfigListReq proto.InternalMessageInfo

func (m *LineMaintainConfigListReq) GetProject() []string {
	if m != nil {
		return m.Project
	}
	return nil
}

func (m *LineMaintainConfigListReq) GetMaintainer() []string {
	if m != nil {
		return m.Maintainer
	}
	return nil
}

func (m *LineMaintainConfigListReq) GetNickName() []string {
	if m != nil {
		return m.NickName
	}
	return nil
}

func (m *LineMaintainConfigListReq) GetLineUserId() string {
	if m != nil {
		return m.LineUserId
	}
	return ""
}

func (m *LineMaintainConfigListReq) GetVipState() uint32 {
	if m != nil {
		return m.VipState
	}
	return 0
}

func (m *LineMaintainConfigListReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *LineMaintainConfigListReq) GetPageSize() uint32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

// LineMaintainConfigListResp 玩家维护配置查询响应
type LineMaintainConfigListResp struct {
	CurrentPage          uint32                                               `protobuf:"varint,1,opt,name=current_page,json=currentPage,proto3" json:"current_page"`
	PerPage              uint32                                               `protobuf:"varint,2,opt,name=per_page,json=perPage,proto3" json:"per_page"`
	Total                uint32                                               `protobuf:"varint,3,opt,name=total,proto3" json:"total"`
	Data                 []*LineMaintainConfigListResp_LineMaintainConfigInfo `protobuf:"bytes,4,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}                                             `json:"-" gorm:"-"`
	XXX_unrecognized     []byte                                               `json:"-" gorm:"-"`
	XXX_sizecache        int32                                                `json:"-" gorm:"-"`
}

func (m *LineMaintainConfigListResp) Reset()         { *m = LineMaintainConfigListResp{} }
func (m *LineMaintainConfigListResp) String() string { return proto.CompactTextString(m) }
func (*LineMaintainConfigListResp) ProtoMessage()    {}
func (*LineMaintainConfigListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_951bc87fae86fd79, []int{20}
}

func (m *LineMaintainConfigListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LineMaintainConfigListResp.Unmarshal(m, b)
}
func (m *LineMaintainConfigListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LineMaintainConfigListResp.Marshal(b, m, deterministic)
}
func (m *LineMaintainConfigListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LineMaintainConfigListResp.Merge(m, src)
}
func (m *LineMaintainConfigListResp) XXX_Size() int {
	return xxx_messageInfo_LineMaintainConfigListResp.Size(m)
}
func (m *LineMaintainConfigListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_LineMaintainConfigListResp.DiscardUnknown(m)
}

var xxx_messageInfo_LineMaintainConfigListResp proto.InternalMessageInfo

func (m *LineMaintainConfigListResp) GetCurrentPage() uint32 {
	if m != nil {
		return m.CurrentPage
	}
	return 0
}

func (m *LineMaintainConfigListResp) GetPerPage() uint32 {
	if m != nil {
		return m.PerPage
	}
	return 0
}

func (m *LineMaintainConfigListResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *LineMaintainConfigListResp) GetData() []*LineMaintainConfigListResp_LineMaintainConfigInfo {
	if m != nil {
		return m.Data
	}
	return nil
}

type LineMaintainConfigListResp_LineMaintainConfigInfo struct {
	Id uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	// 玩家discord_id
	LineUserId string `protobuf:"bytes,2,opt,name=line_user_id,json=lineUserId,proto3" json:"line_user_id"`
	// 玩家discord 昵称
	NickName string `protobuf:"bytes,3,opt,name=nick_name,json=nickName,proto3" json:"nick_name"`
	// 玩家fpid
	Fpid string `protobuf:"bytes,4,opt,name=fpid,proto3" json:"fpid"`
	Uid  uint64 `protobuf:"varint,5,opt,name=uid,proto3" json:"uid"`
	// 服务器
	Sid string `protobuf:"bytes,6,opt,name=sid,proto3" json:"sid"`
	// 维护专员
	Maintainer string `protobuf:"bytes,7,opt,name=maintainer,proto3" json:"maintainer"`
	// 游戏
	Project string `protobuf:"bytes,8,opt,name=project,proto3" json:"project"`
	// 操作人
	Operator string `protobuf:"bytes,9,opt,name=operator,proto3" json:"operator"`
	VipState uint32 `protobuf:"varint,10,opt,name=vip_state,json=vipState,proto3" json:"vip_state"`
	// 操作时间
	UpdateTime           string   `protobuf:"bytes,11,opt,name=update_time,json=updateTime,proto3" json:"update_time"`
	Lang                 string   `protobuf:"bytes,12,opt,name=lang,proto3" json:"lang"`
	Birthday             string   `protobuf:"bytes,13,opt,name=birthday,proto3" json:"birthday"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *LineMaintainConfigListResp_LineMaintainConfigInfo) Reset() {
	*m = LineMaintainConfigListResp_LineMaintainConfigInfo{}
}
func (m *LineMaintainConfigListResp_LineMaintainConfigInfo) String() string {
	return proto.CompactTextString(m)
}
func (*LineMaintainConfigListResp_LineMaintainConfigInfo) ProtoMessage() {}
func (*LineMaintainConfigListResp_LineMaintainConfigInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_951bc87fae86fd79, []int{20, 0}
}

func (m *LineMaintainConfigListResp_LineMaintainConfigInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LineMaintainConfigListResp_LineMaintainConfigInfo.Unmarshal(m, b)
}
func (m *LineMaintainConfigListResp_LineMaintainConfigInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LineMaintainConfigListResp_LineMaintainConfigInfo.Marshal(b, m, deterministic)
}
func (m *LineMaintainConfigListResp_LineMaintainConfigInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LineMaintainConfigListResp_LineMaintainConfigInfo.Merge(m, src)
}
func (m *LineMaintainConfigListResp_LineMaintainConfigInfo) XXX_Size() int {
	return xxx_messageInfo_LineMaintainConfigListResp_LineMaintainConfigInfo.Size(m)
}
func (m *LineMaintainConfigListResp_LineMaintainConfigInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_LineMaintainConfigListResp_LineMaintainConfigInfo.DiscardUnknown(m)
}

var xxx_messageInfo_LineMaintainConfigListResp_LineMaintainConfigInfo proto.InternalMessageInfo

func (m *LineMaintainConfigListResp_LineMaintainConfigInfo) GetId() uint64 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *LineMaintainConfigListResp_LineMaintainConfigInfo) GetLineUserId() string {
	if m != nil {
		return m.LineUserId
	}
	return ""
}

func (m *LineMaintainConfigListResp_LineMaintainConfigInfo) GetNickName() string {
	if m != nil {
		return m.NickName
	}
	return ""
}

func (m *LineMaintainConfigListResp_LineMaintainConfigInfo) GetFpid() string {
	if m != nil {
		return m.Fpid
	}
	return ""
}

func (m *LineMaintainConfigListResp_LineMaintainConfigInfo) GetUid() uint64 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *LineMaintainConfigListResp_LineMaintainConfigInfo) GetSid() string {
	if m != nil {
		return m.Sid
	}
	return ""
}

func (m *LineMaintainConfigListResp_LineMaintainConfigInfo) GetMaintainer() string {
	if m != nil {
		return m.Maintainer
	}
	return ""
}

func (m *LineMaintainConfigListResp_LineMaintainConfigInfo) GetProject() string {
	if m != nil {
		return m.Project
	}
	return ""
}

func (m *LineMaintainConfigListResp_LineMaintainConfigInfo) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

func (m *LineMaintainConfigListResp_LineMaintainConfigInfo) GetVipState() uint32 {
	if m != nil {
		return m.VipState
	}
	return 0
}

func (m *LineMaintainConfigListResp_LineMaintainConfigInfo) GetUpdateTime() string {
	if m != nil {
		return m.UpdateTime
	}
	return ""
}

func (m *LineMaintainConfigListResp_LineMaintainConfigInfo) GetLang() string {
	if m != nil {
		return m.Lang
	}
	return ""
}

func (m *LineMaintainConfigListResp_LineMaintainConfigInfo) GetBirthday() string {
	if m != nil {
		return m.Birthday
	}
	return ""
}

type LineMaintainConfigEditReq struct {
	// @gotags: validate:"required"
	Id uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id" validate:"required"`
	// 玩家fpid
	Fpid string `protobuf:"bytes,2,opt,name=fpid,proto3" json:"fpid"`
	// 维护专员
	Maintainer string `protobuf:"bytes,3,opt,name=maintainer,proto3" json:"maintainer"`
	Uid        uint64 `protobuf:"varint,4,opt,name=uid,proto3" json:"uid"`
	VipState   uint32 `protobuf:"varint,5,opt,name=vip_state,json=vipState,proto3" json:"vip_state"`
	// @gotags: validate:"required"
	Project              string   `protobuf:"bytes,6,opt,name=project,proto3" json:"project" validate:"required"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *LineMaintainConfigEditReq) Reset()         { *m = LineMaintainConfigEditReq{} }
func (m *LineMaintainConfigEditReq) String() string { return proto.CompactTextString(m) }
func (*LineMaintainConfigEditReq) ProtoMessage()    {}
func (*LineMaintainConfigEditReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_951bc87fae86fd79, []int{21}
}

func (m *LineMaintainConfigEditReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LineMaintainConfigEditReq.Unmarshal(m, b)
}
func (m *LineMaintainConfigEditReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LineMaintainConfigEditReq.Marshal(b, m, deterministic)
}
func (m *LineMaintainConfigEditReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LineMaintainConfigEditReq.Merge(m, src)
}
func (m *LineMaintainConfigEditReq) XXX_Size() int {
	return xxx_messageInfo_LineMaintainConfigEditReq.Size(m)
}
func (m *LineMaintainConfigEditReq) XXX_DiscardUnknown() {
	xxx_messageInfo_LineMaintainConfigEditReq.DiscardUnknown(m)
}

var xxx_messageInfo_LineMaintainConfigEditReq proto.InternalMessageInfo

func (m *LineMaintainConfigEditReq) GetId() uint64 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *LineMaintainConfigEditReq) GetFpid() string {
	if m != nil {
		return m.Fpid
	}
	return ""
}

func (m *LineMaintainConfigEditReq) GetMaintainer() string {
	if m != nil {
		return m.Maintainer
	}
	return ""
}

func (m *LineMaintainConfigEditReq) GetUid() uint64 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *LineMaintainConfigEditReq) GetVipState() uint32 {
	if m != nil {
		return m.VipState
	}
	return 0
}

func (m *LineMaintainConfigEditReq) GetProject() string {
	if m != nil {
		return m.Project
	}
	return ""
}

type LinePlayerAccountsReq struct {
	// @gotags: validate:"required"
	Project string `protobuf:"bytes,1,opt,name=project,proto3" json:"project" validate:"required"`
	// 搜索值
	LineUserId           string   `protobuf:"bytes,2,opt,name=line_user_id,json=lineUserId,proto3" json:"line_user_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *LinePlayerAccountsReq) Reset()         { *m = LinePlayerAccountsReq{} }
func (m *LinePlayerAccountsReq) String() string { return proto.CompactTextString(m) }
func (*LinePlayerAccountsReq) ProtoMessage()    {}
func (*LinePlayerAccountsReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_951bc87fae86fd79, []int{22}
}

func (m *LinePlayerAccountsReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LinePlayerAccountsReq.Unmarshal(m, b)
}
func (m *LinePlayerAccountsReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LinePlayerAccountsReq.Marshal(b, m, deterministic)
}
func (m *LinePlayerAccountsReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LinePlayerAccountsReq.Merge(m, src)
}
func (m *LinePlayerAccountsReq) XXX_Size() int {
	return xxx_messageInfo_LinePlayerAccountsReq.Size(m)
}
func (m *LinePlayerAccountsReq) XXX_DiscardUnknown() {
	xxx_messageInfo_LinePlayerAccountsReq.DiscardUnknown(m)
}

var xxx_messageInfo_LinePlayerAccountsReq proto.InternalMessageInfo

func (m *LinePlayerAccountsReq) GetProject() string {
	if m != nil {
		return m.Project
	}
	return ""
}

func (m *LinePlayerAccountsReq) GetLineUserId() string {
	if m != nil {
		return m.LineUserId
	}
	return ""
}

type LinePlayerAccountsResp struct {
	LineUserIds          []string `protobuf:"bytes,1,rep,name=line_user_ids,json=lineUserIds,proto3" json:"line_user_ids"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *LinePlayerAccountsResp) Reset()         { *m = LinePlayerAccountsResp{} }
func (m *LinePlayerAccountsResp) String() string { return proto.CompactTextString(m) }
func (*LinePlayerAccountsResp) ProtoMessage()    {}
func (*LinePlayerAccountsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_951bc87fae86fd79, []int{23}
}

func (m *LinePlayerAccountsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LinePlayerAccountsResp.Unmarshal(m, b)
}
func (m *LinePlayerAccountsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LinePlayerAccountsResp.Marshal(b, m, deterministic)
}
func (m *LinePlayerAccountsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LinePlayerAccountsResp.Merge(m, src)
}
func (m *LinePlayerAccountsResp) XXX_Size() int {
	return xxx_messageInfo_LinePlayerAccountsResp.Size(m)
}
func (m *LinePlayerAccountsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_LinePlayerAccountsResp.DiscardUnknown(m)
}

var xxx_messageInfo_LinePlayerAccountsResp proto.InternalMessageInfo

func (m *LinePlayerAccountsResp) GetLineUserIds() []string {
	if m != nil {
		return m.LineUserIds
	}
	return nil
}

type LineCommuRecordAddReq struct {
	// 沟通日期 @gotags: validate:"required"
	CommuDate string `protobuf:"bytes,1,opt,name=commu_date,json=commuDate,proto3" json:"commu_date" validate:"required"`
	// 游戏 @gotags: validate:"required"
	Project string `protobuf:"bytes,2,opt,name=project,proto3" json:"project" validate:"required"`
	// 沟通问题 @gotags: validate:"required"
	Question string `protobuf:"bytes,3,opt,name=question,proto3" json:"question" validate:"required"`
	// 问题类型Id @gotags: validate:"required"
	CatId uint32 `protobuf:"varint,4,opt,name=cat_id,json=catId,proto3" json:"cat_id" validate:"required"`
	// 处理状态 @gotags: validate:"required"
	HandleStatus int32 `protobuf:"varint,5,opt,name=handle_status,json=handleStatus,proto3" json:"handle_status" validate:"required"`
	// 备注
	Remark string `protobuf:"bytes,6,opt,name=remark,proto3" json:"remark"`
	// 涉及对话信息 @gotags: validate:"required"
	MsgIds string `protobuf:"bytes,7,opt,name=msg_ids,json=msgIds,proto3" json:"msg_ids" validate:"required"`
	// 玩家line_user_id @gotags: validate:"required"
	LineUserId string `protobuf:"bytes,8,opt,name=line_user_id,json=lineUserId,proto3" json:"line_user_id" validate:"required"`
	// 玩家uid
	Uid int64 `protobuf:"varint,9,opt,name=uid,proto3" json:"uid"`
	// 玩家所在的服务器
	Sid string `protobuf:"bytes,10,opt,name=sid,proto3" json:"sid"`
	// 玩家昵称 @gotags: validate:"required"
	NickName string `protobuf:"bytes,11,opt,name=nick_name,json=nickName,proto3" json:"nick_name" validate:"required"`
	// 玩家累付金额
	PayAll float64 `protobuf:"fixed64,12,opt,name=pay_all,json=payAll,proto3" json:"pay_all"`
	// 类别:line or discord @gotags: validate:"required,oneof=1 2"
	CatType              uint32   `protobuf:"varint,13,opt,name=cat_type,json=catType,proto3" json:"cat_type" validate:"required,oneof=1 2"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *LineCommuRecordAddReq) Reset()         { *m = LineCommuRecordAddReq{} }
func (m *LineCommuRecordAddReq) String() string { return proto.CompactTextString(m) }
func (*LineCommuRecordAddReq) ProtoMessage()    {}
func (*LineCommuRecordAddReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_951bc87fae86fd79, []int{24}
}

func (m *LineCommuRecordAddReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LineCommuRecordAddReq.Unmarshal(m, b)
}
func (m *LineCommuRecordAddReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LineCommuRecordAddReq.Marshal(b, m, deterministic)
}
func (m *LineCommuRecordAddReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LineCommuRecordAddReq.Merge(m, src)
}
func (m *LineCommuRecordAddReq) XXX_Size() int {
	return xxx_messageInfo_LineCommuRecordAddReq.Size(m)
}
func (m *LineCommuRecordAddReq) XXX_DiscardUnknown() {
	xxx_messageInfo_LineCommuRecordAddReq.DiscardUnknown(m)
}

var xxx_messageInfo_LineCommuRecordAddReq proto.InternalMessageInfo

func (m *LineCommuRecordAddReq) GetCommuDate() string {
	if m != nil {
		return m.CommuDate
	}
	return ""
}

func (m *LineCommuRecordAddReq) GetProject() string {
	if m != nil {
		return m.Project
	}
	return ""
}

func (m *LineCommuRecordAddReq) GetQuestion() string {
	if m != nil {
		return m.Question
	}
	return ""
}

func (m *LineCommuRecordAddReq) GetCatId() uint32 {
	if m != nil {
		return m.CatId
	}
	return 0
}

func (m *LineCommuRecordAddReq) GetHandleStatus() int32 {
	if m != nil {
		return m.HandleStatus
	}
	return 0
}

func (m *LineCommuRecordAddReq) GetRemark() string {
	if m != nil {
		return m.Remark
	}
	return ""
}

func (m *LineCommuRecordAddReq) GetMsgIds() string {
	if m != nil {
		return m.MsgIds
	}
	return ""
}

func (m *LineCommuRecordAddReq) GetLineUserId() string {
	if m != nil {
		return m.LineUserId
	}
	return ""
}

func (m *LineCommuRecordAddReq) GetUid() int64 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *LineCommuRecordAddReq) GetSid() string {
	if m != nil {
		return m.Sid
	}
	return ""
}

func (m *LineCommuRecordAddReq) GetNickName() string {
	if m != nil {
		return m.NickName
	}
	return ""
}

func (m *LineCommuRecordAddReq) GetPayAll() float64 {
	if m != nil {
		return m.PayAll
	}
	return 0
}

func (m *LineCommuRecordAddReq) GetCatType() uint32 {
	if m != nil {
		return m.CatType
	}
	return 0
}

type LineCommuRecordEditReq struct {
	// @gotags: validate:"required"
	Id                   int64    `protobuf:"varint,1,opt,name=id,proto3" json:"id" validate:"required"`
	CommuDate            string   `protobuf:"bytes,2,opt,name=commu_date,json=commuDate,proto3" json:"commu_date"`
	Question             string   `protobuf:"bytes,3,opt,name=question,proto3" json:"question"`
	CatId                uint32   `protobuf:"varint,4,opt,name=cat_id,json=catId,proto3" json:"cat_id"`
	HandleStatus         int32    `protobuf:"varint,5,opt,name=handle_status,json=handleStatus,proto3" json:"handle_status"`
	Remark               string   `protobuf:"bytes,6,opt,name=remark,proto3" json:"remark"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *LineCommuRecordEditReq) Reset()         { *m = LineCommuRecordEditReq{} }
func (m *LineCommuRecordEditReq) String() string { return proto.CompactTextString(m) }
func (*LineCommuRecordEditReq) ProtoMessage()    {}
func (*LineCommuRecordEditReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_951bc87fae86fd79, []int{25}
}

func (m *LineCommuRecordEditReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LineCommuRecordEditReq.Unmarshal(m, b)
}
func (m *LineCommuRecordEditReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LineCommuRecordEditReq.Marshal(b, m, deterministic)
}
func (m *LineCommuRecordEditReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LineCommuRecordEditReq.Merge(m, src)
}
func (m *LineCommuRecordEditReq) XXX_Size() int {
	return xxx_messageInfo_LineCommuRecordEditReq.Size(m)
}
func (m *LineCommuRecordEditReq) XXX_DiscardUnknown() {
	xxx_messageInfo_LineCommuRecordEditReq.DiscardUnknown(m)
}

var xxx_messageInfo_LineCommuRecordEditReq proto.InternalMessageInfo

func (m *LineCommuRecordEditReq) GetId() int64 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *LineCommuRecordEditReq) GetCommuDate() string {
	if m != nil {
		return m.CommuDate
	}
	return ""
}

func (m *LineCommuRecordEditReq) GetQuestion() string {
	if m != nil {
		return m.Question
	}
	return ""
}

func (m *LineCommuRecordEditReq) GetCatId() uint32 {
	if m != nil {
		return m.CatId
	}
	return 0
}

func (m *LineCommuRecordEditReq) GetHandleStatus() int32 {
	if m != nil {
		return m.HandleStatus
	}
	return 0
}

func (m *LineCommuRecordEditReq) GetRemark() string {
	if m != nil {
		return m.Remark
	}
	return ""
}

type LineCommuRecordListReq struct {
	Project      string   `protobuf:"bytes,1,opt,name=project,proto3" json:"project"`
	CommuDate    []string `protobuf:"bytes,2,rep,name=commu_date,json=commuDate,proto3" json:"commu_date"`
	Operator     []string `protobuf:"bytes,3,rep,name=operator,proto3" json:"operator"`
	Uid          int64    `protobuf:"varint,4,opt,name=uid,proto3" json:"uid"`
	CatId        uint32   `protobuf:"varint,5,opt,name=cat_id,json=catId,proto3" json:"cat_id"`
	HandleStatus []int32  `protobuf:"varint,6,rep,packed,name=handle_status,json=handleStatus,proto3" json:"handle_status"`
	// 类别:line or discord @gotags: validate:"required,oneof=1 2"
	CatType uint32 `protobuf:"varint,7,opt,name=cat_type,json=catType,proto3" json:"cat_type" validate:"required,oneof=1 2"`
	// @gotags: validate:"required"
	Page uint32 `protobuf:"varint,8,opt,name=page,proto3" json:"page" validate:"required"`
	// @gotags: validate:"required"
	PageSize uint32 `protobuf:"varint,9,opt,name=page_size,json=pageSize,proto3" json:"page_size" validate:"required"`
	// 标签多选
	Label                []uint64 `protobuf:"varint,10,rep,packed,name=label,proto3" json:"label"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *LineCommuRecordListReq) Reset()         { *m = LineCommuRecordListReq{} }
func (m *LineCommuRecordListReq) String() string { return proto.CompactTextString(m) }
func (*LineCommuRecordListReq) ProtoMessage()    {}
func (*LineCommuRecordListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_951bc87fae86fd79, []int{26}
}

func (m *LineCommuRecordListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LineCommuRecordListReq.Unmarshal(m, b)
}
func (m *LineCommuRecordListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LineCommuRecordListReq.Marshal(b, m, deterministic)
}
func (m *LineCommuRecordListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LineCommuRecordListReq.Merge(m, src)
}
func (m *LineCommuRecordListReq) XXX_Size() int {
	return xxx_messageInfo_LineCommuRecordListReq.Size(m)
}
func (m *LineCommuRecordListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_LineCommuRecordListReq.DiscardUnknown(m)
}

var xxx_messageInfo_LineCommuRecordListReq proto.InternalMessageInfo

func (m *LineCommuRecordListReq) GetProject() string {
	if m != nil {
		return m.Project
	}
	return ""
}

func (m *LineCommuRecordListReq) GetCommuDate() []string {
	if m != nil {
		return m.CommuDate
	}
	return nil
}

func (m *LineCommuRecordListReq) GetOperator() []string {
	if m != nil {
		return m.Operator
	}
	return nil
}

func (m *LineCommuRecordListReq) GetUid() int64 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *LineCommuRecordListReq) GetCatId() uint32 {
	if m != nil {
		return m.CatId
	}
	return 0
}

func (m *LineCommuRecordListReq) GetHandleStatus() []int32 {
	if m != nil {
		return m.HandleStatus
	}
	return nil
}

func (m *LineCommuRecordListReq) GetCatType() uint32 {
	if m != nil {
		return m.CatType
	}
	return 0
}

func (m *LineCommuRecordListReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *LineCommuRecordListReq) GetPageSize() uint32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

func (m *LineCommuRecordListReq) GetLabel() []uint64 {
	if m != nil {
		return m.Label
	}
	return nil
}

type LineCommuRecordListResp struct {
	CurrentPage          uint32                                     `protobuf:"varint,1,opt,name=current_page,json=currentPage,proto3" json:"current_page"`
	PerPage              uint32                                     `protobuf:"varint,2,opt,name=per_page,json=perPage,proto3" json:"per_page"`
	Total                uint32                                     `protobuf:"varint,3,opt,name=total,proto3" json:"total"`
	Data                 []*LineCommuRecordListResp_LineCommuRecord `protobuf:"bytes,4,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}                                   `json:"-" gorm:"-"`
	XXX_unrecognized     []byte                                     `json:"-" gorm:"-"`
	XXX_sizecache        int32                                      `json:"-" gorm:"-"`
}

func (m *LineCommuRecordListResp) Reset()         { *m = LineCommuRecordListResp{} }
func (m *LineCommuRecordListResp) String() string { return proto.CompactTextString(m) }
func (*LineCommuRecordListResp) ProtoMessage()    {}
func (*LineCommuRecordListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_951bc87fae86fd79, []int{27}
}

func (m *LineCommuRecordListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LineCommuRecordListResp.Unmarshal(m, b)
}
func (m *LineCommuRecordListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LineCommuRecordListResp.Marshal(b, m, deterministic)
}
func (m *LineCommuRecordListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LineCommuRecordListResp.Merge(m, src)
}
func (m *LineCommuRecordListResp) XXX_Size() int {
	return xxx_messageInfo_LineCommuRecordListResp.Size(m)
}
func (m *LineCommuRecordListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_LineCommuRecordListResp.DiscardUnknown(m)
}

var xxx_messageInfo_LineCommuRecordListResp proto.InternalMessageInfo

func (m *LineCommuRecordListResp) GetCurrentPage() uint32 {
	if m != nil {
		return m.CurrentPage
	}
	return 0
}

func (m *LineCommuRecordListResp) GetPerPage() uint32 {
	if m != nil {
		return m.PerPage
	}
	return 0
}

func (m *LineCommuRecordListResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *LineCommuRecordListResp) GetData() []*LineCommuRecordListResp_LineCommuRecord {
	if m != nil {
		return m.Data
	}
	return nil
}

type LineCommuRecordListResp_NewLabel struct {
	TagId                uint32   `protobuf:"varint,1,opt,name=tag_id,json=tagId,proto3" json:"tag_id"`
	TagName              string   `protobuf:"bytes,2,opt,name=tag_name,json=tagName,proto3" json:"tag_name"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *LineCommuRecordListResp_NewLabel) Reset()         { *m = LineCommuRecordListResp_NewLabel{} }
func (m *LineCommuRecordListResp_NewLabel) String() string { return proto.CompactTextString(m) }
func (*LineCommuRecordListResp_NewLabel) ProtoMessage()    {}
func (*LineCommuRecordListResp_NewLabel) Descriptor() ([]byte, []int) {
	return fileDescriptor_951bc87fae86fd79, []int{27, 0}
}

func (m *LineCommuRecordListResp_NewLabel) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LineCommuRecordListResp_NewLabel.Unmarshal(m, b)
}
func (m *LineCommuRecordListResp_NewLabel) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LineCommuRecordListResp_NewLabel.Marshal(b, m, deterministic)
}
func (m *LineCommuRecordListResp_NewLabel) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LineCommuRecordListResp_NewLabel.Merge(m, src)
}
func (m *LineCommuRecordListResp_NewLabel) XXX_Size() int {
	return xxx_messageInfo_LineCommuRecordListResp_NewLabel.Size(m)
}
func (m *LineCommuRecordListResp_NewLabel) XXX_DiscardUnknown() {
	xxx_messageInfo_LineCommuRecordListResp_NewLabel.DiscardUnknown(m)
}

var xxx_messageInfo_LineCommuRecordListResp_NewLabel proto.InternalMessageInfo

func (m *LineCommuRecordListResp_NewLabel) GetTagId() uint32 {
	if m != nil {
		return m.TagId
	}
	return 0
}

func (m *LineCommuRecordListResp_NewLabel) GetTagName() string {
	if m != nil {
		return m.TagName
	}
	return ""
}

type LineCommuRecordListResp_DialogueItem struct {
	Role                 string   `protobuf:"bytes,1,opt,name=role,proto3" json:"role"`
	Content              string   `protobuf:"bytes,2,opt,name=content,proto3" json:"content"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *LineCommuRecordListResp_DialogueItem) Reset()         { *m = LineCommuRecordListResp_DialogueItem{} }
func (m *LineCommuRecordListResp_DialogueItem) String() string { return proto.CompactTextString(m) }
func (*LineCommuRecordListResp_DialogueItem) ProtoMessage()    {}
func (*LineCommuRecordListResp_DialogueItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_951bc87fae86fd79, []int{27, 1}
}

func (m *LineCommuRecordListResp_DialogueItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LineCommuRecordListResp_DialogueItem.Unmarshal(m, b)
}
func (m *LineCommuRecordListResp_DialogueItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LineCommuRecordListResp_DialogueItem.Marshal(b, m, deterministic)
}
func (m *LineCommuRecordListResp_DialogueItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LineCommuRecordListResp_DialogueItem.Merge(m, src)
}
func (m *LineCommuRecordListResp_DialogueItem) XXX_Size() int {
	return xxx_messageInfo_LineCommuRecordListResp_DialogueItem.Size(m)
}
func (m *LineCommuRecordListResp_DialogueItem) XXX_DiscardUnknown() {
	xxx_messageInfo_LineCommuRecordListResp_DialogueItem.DiscardUnknown(m)
}

var xxx_messageInfo_LineCommuRecordListResp_DialogueItem proto.InternalMessageInfo

func (m *LineCommuRecordListResp_DialogueItem) GetRole() string {
	if m != nil {
		return m.Role
	}
	return ""
}

func (m *LineCommuRecordListResp_DialogueItem) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

type LineCommuRecordListResp_LineCommuRecord struct {
	Id                   int64                                   `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	CommuDate            string                                  `protobuf:"bytes,2,opt,name=commu_date,json=commuDate,proto3" json:"commu_date"`
	Project              string                                  `protobuf:"bytes,3,opt,name=project,proto3" json:"project"`
	Uid                  int64                                   `protobuf:"varint,4,opt,name=uid,proto3" json:"uid"`
	Sid                  string                                  `protobuf:"bytes,5,opt,name=sid,proto3" json:"sid"`
	NickName             string                                  `protobuf:"bytes,6,opt,name=nick_name,json=nickName,proto3" json:"nick_name"`
	PayAll               float64                                 `protobuf:"fixed64,7,opt,name=pay_all,json=payAll,proto3" json:"pay_all"`
	Question             string                                  `protobuf:"bytes,8,opt,name=question,proto3" json:"question"`
	HandleStatus         int32                                   `protobuf:"varint,9,opt,name=handle_status,json=handleStatus,proto3" json:"handle_status"`
	Remark               string                                  `protobuf:"bytes,10,opt,name=remark,proto3" json:"remark"`
	Dialogue             []*LineCommuRecordListResp_DialogueItem `protobuf:"bytes,11,rep,name=dialogue,proto3" json:"dialogue"`
	Operator             string                                  `protobuf:"bytes,12,opt,name=operator,proto3" json:"operator"`
	Maintainer           string                                  `protobuf:"bytes,13,opt,name=maintainer,proto3" json:"maintainer"`
	Category             string                                  `protobuf:"bytes,14,opt,name=category,proto3" json:"category"`
	CatId                int32                                   `protobuf:"varint,16,opt,name=cat_id,json=catId,proto3" json:"cat_id"`
	Label                []*LineCommuRecordListResp_NewLabel     `protobuf:"bytes,17,rep,name=label,proto3" json:"label"`
	XXX_NoUnkeyedLiteral struct{}                                `json:"-" gorm:"-"`
	XXX_unrecognized     []byte                                  `json:"-" gorm:"-"`
	XXX_sizecache        int32                                   `json:"-" gorm:"-"`
}

func (m *LineCommuRecordListResp_LineCommuRecord) Reset() {
	*m = LineCommuRecordListResp_LineCommuRecord{}
}
func (m *LineCommuRecordListResp_LineCommuRecord) String() string { return proto.CompactTextString(m) }
func (*LineCommuRecordListResp_LineCommuRecord) ProtoMessage()    {}
func (*LineCommuRecordListResp_LineCommuRecord) Descriptor() ([]byte, []int) {
	return fileDescriptor_951bc87fae86fd79, []int{27, 2}
}

func (m *LineCommuRecordListResp_LineCommuRecord) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LineCommuRecordListResp_LineCommuRecord.Unmarshal(m, b)
}
func (m *LineCommuRecordListResp_LineCommuRecord) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LineCommuRecordListResp_LineCommuRecord.Marshal(b, m, deterministic)
}
func (m *LineCommuRecordListResp_LineCommuRecord) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LineCommuRecordListResp_LineCommuRecord.Merge(m, src)
}
func (m *LineCommuRecordListResp_LineCommuRecord) XXX_Size() int {
	return xxx_messageInfo_LineCommuRecordListResp_LineCommuRecord.Size(m)
}
func (m *LineCommuRecordListResp_LineCommuRecord) XXX_DiscardUnknown() {
	xxx_messageInfo_LineCommuRecordListResp_LineCommuRecord.DiscardUnknown(m)
}

var xxx_messageInfo_LineCommuRecordListResp_LineCommuRecord proto.InternalMessageInfo

func (m *LineCommuRecordListResp_LineCommuRecord) GetId() int64 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *LineCommuRecordListResp_LineCommuRecord) GetCommuDate() string {
	if m != nil {
		return m.CommuDate
	}
	return ""
}

func (m *LineCommuRecordListResp_LineCommuRecord) GetProject() string {
	if m != nil {
		return m.Project
	}
	return ""
}

func (m *LineCommuRecordListResp_LineCommuRecord) GetUid() int64 {
	if m != nil {
		return m.Uid
	}
	return 0
}

func (m *LineCommuRecordListResp_LineCommuRecord) GetSid() string {
	if m != nil {
		return m.Sid
	}
	return ""
}

func (m *LineCommuRecordListResp_LineCommuRecord) GetNickName() string {
	if m != nil {
		return m.NickName
	}
	return ""
}

func (m *LineCommuRecordListResp_LineCommuRecord) GetPayAll() float64 {
	if m != nil {
		return m.PayAll
	}
	return 0
}

func (m *LineCommuRecordListResp_LineCommuRecord) GetQuestion() string {
	if m != nil {
		return m.Question
	}
	return ""
}

func (m *LineCommuRecordListResp_LineCommuRecord) GetHandleStatus() int32 {
	if m != nil {
		return m.HandleStatus
	}
	return 0
}

func (m *LineCommuRecordListResp_LineCommuRecord) GetRemark() string {
	if m != nil {
		return m.Remark
	}
	return ""
}

func (m *LineCommuRecordListResp_LineCommuRecord) GetDialogue() []*LineCommuRecordListResp_DialogueItem {
	if m != nil {
		return m.Dialogue
	}
	return nil
}

func (m *LineCommuRecordListResp_LineCommuRecord) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

func (m *LineCommuRecordListResp_LineCommuRecord) GetMaintainer() string {
	if m != nil {
		return m.Maintainer
	}
	return ""
}

func (m *LineCommuRecordListResp_LineCommuRecord) GetCategory() string {
	if m != nil {
		return m.Category
	}
	return ""
}

func (m *LineCommuRecordListResp_LineCommuRecord) GetCatId() int32 {
	if m != nil {
		return m.CatId
	}
	return 0
}

func (m *LineCommuRecordListResp_LineCommuRecord) GetLabel() []*LineCommuRecordListResp_NewLabel {
	if m != nil {
		return m.Label
	}
	return nil
}

type LineUserRemarkAddReq struct {
	// 玩家line_user_id @gotags: validate:"required"
	LineUserId string `protobuf:"bytes,1,opt,name=line_user_id,json=lineUserId,proto3" json:"line_user_id" validate:"required"`
	// 备注 @gotags: validate:"required"
	Note                 string   `protobuf:"bytes,2,opt,name=note,proto3" json:"note" validate:"required"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *LineUserRemarkAddReq) Reset()         { *m = LineUserRemarkAddReq{} }
func (m *LineUserRemarkAddReq) String() string { return proto.CompactTextString(m) }
func (*LineUserRemarkAddReq) ProtoMessage()    {}
func (*LineUserRemarkAddReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_951bc87fae86fd79, []int{28}
}

func (m *LineUserRemarkAddReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LineUserRemarkAddReq.Unmarshal(m, b)
}
func (m *LineUserRemarkAddReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LineUserRemarkAddReq.Marshal(b, m, deterministic)
}
func (m *LineUserRemarkAddReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LineUserRemarkAddReq.Merge(m, src)
}
func (m *LineUserRemarkAddReq) XXX_Size() int {
	return xxx_messageInfo_LineUserRemarkAddReq.Size(m)
}
func (m *LineUserRemarkAddReq) XXX_DiscardUnknown() {
	xxx_messageInfo_LineUserRemarkAddReq.DiscardUnknown(m)
}

var xxx_messageInfo_LineUserRemarkAddReq proto.InternalMessageInfo

func (m *LineUserRemarkAddReq) GetLineUserId() string {
	if m != nil {
		return m.LineUserId
	}
	return ""
}

func (m *LineUserRemarkAddReq) GetNote() string {
	if m != nil {
		return m.Note
	}
	return ""
}

type LineTabAddReq struct {
	// @gotags: validate:"required"
	TabName string `protobuf:"bytes,1,opt,name=tab_name,json=tabName,proto3" json:"tab_name" validate:"required"`
	// @gotags: validate:"required,oneof=1 2"
	Public int32 `protobuf:"varint,2,opt,name=public,proto3" json:"public" validate:"required,oneof=1 2"`
	// 搜索条件组合 @gotags: validate:"required"
	Detail               *LineUserListReq `protobuf:"bytes,3,opt,name=detail,proto3" json:"detail" validate:"required"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-" gorm:"-"`
	XXX_unrecognized     []byte           `json:"-" gorm:"-"`
	XXX_sizecache        int32            `json:"-" gorm:"-"`
}

func (m *LineTabAddReq) Reset()         { *m = LineTabAddReq{} }
func (m *LineTabAddReq) String() string { return proto.CompactTextString(m) }
func (*LineTabAddReq) ProtoMessage()    {}
func (*LineTabAddReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_951bc87fae86fd79, []int{29}
}

func (m *LineTabAddReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LineTabAddReq.Unmarshal(m, b)
}
func (m *LineTabAddReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LineTabAddReq.Marshal(b, m, deterministic)
}
func (m *LineTabAddReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LineTabAddReq.Merge(m, src)
}
func (m *LineTabAddReq) XXX_Size() int {
	return xxx_messageInfo_LineTabAddReq.Size(m)
}
func (m *LineTabAddReq) XXX_DiscardUnknown() {
	xxx_messageInfo_LineTabAddReq.DiscardUnknown(m)
}

var xxx_messageInfo_LineTabAddReq proto.InternalMessageInfo

func (m *LineTabAddReq) GetTabName() string {
	if m != nil {
		return m.TabName
	}
	return ""
}

func (m *LineTabAddReq) GetPublic() int32 {
	if m != nil {
		return m.Public
	}
	return 0
}

func (m *LineTabAddReq) GetDetail() *LineUserListReq {
	if m != nil {
		return m.Detail
	}
	return nil
}

type LineTabEditReq struct {
	// @gotags: validate:"required"
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id" validate:"required"`
	// @gotags: validate:"required"
	TabName string `protobuf:"bytes,2,opt,name=tab_name,json=tabName,proto3" json:"tab_name" validate:"required"`
	// @gotags: validate:"required,oneof=1 2"
	Public               int32    `protobuf:"varint,3,opt,name=public,proto3" json:"public" validate:"required,oneof=1 2"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *LineTabEditReq) Reset()         { *m = LineTabEditReq{} }
func (m *LineTabEditReq) String() string { return proto.CompactTextString(m) }
func (*LineTabEditReq) ProtoMessage()    {}
func (*LineTabEditReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_951bc87fae86fd79, []int{30}
}

func (m *LineTabEditReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LineTabEditReq.Unmarshal(m, b)
}
func (m *LineTabEditReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LineTabEditReq.Marshal(b, m, deterministic)
}
func (m *LineTabEditReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LineTabEditReq.Merge(m, src)
}
func (m *LineTabEditReq) XXX_Size() int {
	return xxx_messageInfo_LineTabEditReq.Size(m)
}
func (m *LineTabEditReq) XXX_DiscardUnknown() {
	xxx_messageInfo_LineTabEditReq.DiscardUnknown(m)
}

var xxx_messageInfo_LineTabEditReq proto.InternalMessageInfo

func (m *LineTabEditReq) GetId() int64 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *LineTabEditReq) GetTabName() string {
	if m != nil {
		return m.TabName
	}
	return ""
}

func (m *LineTabEditReq) GetPublic() int32 {
	if m != nil {
		return m.Public
	}
	return 0
}

type LineTabDelReq struct {
	// @gotags: validate:"required"
	Id                   int64    `protobuf:"varint,1,opt,name=id,proto3" json:"id" validate:"required"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *LineTabDelReq) Reset()         { *m = LineTabDelReq{} }
func (m *LineTabDelReq) String() string { return proto.CompactTextString(m) }
func (*LineTabDelReq) ProtoMessage()    {}
func (*LineTabDelReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_951bc87fae86fd79, []int{31}
}

func (m *LineTabDelReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LineTabDelReq.Unmarshal(m, b)
}
func (m *LineTabDelReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LineTabDelReq.Marshal(b, m, deterministic)
}
func (m *LineTabDelReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LineTabDelReq.Merge(m, src)
}
func (m *LineTabDelReq) XXX_Size() int {
	return xxx_messageInfo_LineTabDelReq.Size(m)
}
func (m *LineTabDelReq) XXX_DiscardUnknown() {
	xxx_messageInfo_LineTabDelReq.DiscardUnknown(m)
}

var xxx_messageInfo_LineTabDelReq proto.InternalMessageInfo

func (m *LineTabDelReq) GetId() int64 {
	if m != nil {
		return m.Id
	}
	return 0
}

type LineTabListResp struct {
	Data                 []*LineTabListResp_LineTabDetail `protobuf:"bytes,1,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}                         `json:"-" gorm:"-"`
	XXX_unrecognized     []byte                           `json:"-" gorm:"-"`
	XXX_sizecache        int32                            `json:"-" gorm:"-"`
}

func (m *LineTabListResp) Reset()         { *m = LineTabListResp{} }
func (m *LineTabListResp) String() string { return proto.CompactTextString(m) }
func (*LineTabListResp) ProtoMessage()    {}
func (*LineTabListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_951bc87fae86fd79, []int{32}
}

func (m *LineTabListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LineTabListResp.Unmarshal(m, b)
}
func (m *LineTabListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LineTabListResp.Marshal(b, m, deterministic)
}
func (m *LineTabListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LineTabListResp.Merge(m, src)
}
func (m *LineTabListResp) XXX_Size() int {
	return xxx_messageInfo_LineTabListResp.Size(m)
}
func (m *LineTabListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_LineTabListResp.DiscardUnknown(m)
}

var xxx_messageInfo_LineTabListResp proto.InternalMessageInfo

func (m *LineTabListResp) GetData() []*LineTabListResp_LineTabDetail {
	if m != nil {
		return m.Data
	}
	return nil
}

type LineTabListResp_LineTabDetail struct {
	Tab                  []*LineTabListResp_TabInfo `protobuf:"bytes,1,rep,name=tab,proto3" json:"tab"`
	Project              string                     `protobuf:"bytes,2,opt,name=project,proto3" json:"project"`
	XXX_NoUnkeyedLiteral struct{}                   `json:"-" gorm:"-"`
	XXX_unrecognized     []byte                     `json:"-" gorm:"-"`
	XXX_sizecache        int32                      `json:"-" gorm:"-"`
}

func (m *LineTabListResp_LineTabDetail) Reset()         { *m = LineTabListResp_LineTabDetail{} }
func (m *LineTabListResp_LineTabDetail) String() string { return proto.CompactTextString(m) }
func (*LineTabListResp_LineTabDetail) ProtoMessage()    {}
func (*LineTabListResp_LineTabDetail) Descriptor() ([]byte, []int) {
	return fileDescriptor_951bc87fae86fd79, []int{32, 0}
}

func (m *LineTabListResp_LineTabDetail) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LineTabListResp_LineTabDetail.Unmarshal(m, b)
}
func (m *LineTabListResp_LineTabDetail) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LineTabListResp_LineTabDetail.Marshal(b, m, deterministic)
}
func (m *LineTabListResp_LineTabDetail) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LineTabListResp_LineTabDetail.Merge(m, src)
}
func (m *LineTabListResp_LineTabDetail) XXX_Size() int {
	return xxx_messageInfo_LineTabListResp_LineTabDetail.Size(m)
}
func (m *LineTabListResp_LineTabDetail) XXX_DiscardUnknown() {
	xxx_messageInfo_LineTabListResp_LineTabDetail.DiscardUnknown(m)
}

var xxx_messageInfo_LineTabListResp_LineTabDetail proto.InternalMessageInfo

func (m *LineTabListResp_LineTabDetail) GetTab() []*LineTabListResp_TabInfo {
	if m != nil {
		return m.Tab
	}
	return nil
}

func (m *LineTabListResp_LineTabDetail) GetProject() string {
	if m != nil {
		return m.Project
	}
	return ""
}

type LineTabListResp_TabInfo struct {
	Id                   int64            `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	TabName              string           `protobuf:"bytes,2,opt,name=tab_name,json=tabName,proto3" json:"tab_name"`
	Detail               *LineUserListReq `protobuf:"bytes,3,opt,name=detail,proto3" json:"detail"`
	Public               int32            `protobuf:"varint,4,opt,name=public,proto3" json:"public"`
	Operator             string           `protobuf:"bytes,5,opt,name=operator,proto3" json:"operator"`
	XXX_NoUnkeyedLiteral struct{}         `json:"-" gorm:"-"`
	XXX_unrecognized     []byte           `json:"-" gorm:"-"`
	XXX_sizecache        int32            `json:"-" gorm:"-"`
}

func (m *LineTabListResp_TabInfo) Reset()         { *m = LineTabListResp_TabInfo{} }
func (m *LineTabListResp_TabInfo) String() string { return proto.CompactTextString(m) }
func (*LineTabListResp_TabInfo) ProtoMessage()    {}
func (*LineTabListResp_TabInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_951bc87fae86fd79, []int{32, 1}
}

func (m *LineTabListResp_TabInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LineTabListResp_TabInfo.Unmarshal(m, b)
}
func (m *LineTabListResp_TabInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LineTabListResp_TabInfo.Marshal(b, m, deterministic)
}
func (m *LineTabListResp_TabInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LineTabListResp_TabInfo.Merge(m, src)
}
func (m *LineTabListResp_TabInfo) XXX_Size() int {
	return xxx_messageInfo_LineTabListResp_TabInfo.Size(m)
}
func (m *LineTabListResp_TabInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_LineTabListResp_TabInfo.DiscardUnknown(m)
}

var xxx_messageInfo_LineTabListResp_TabInfo proto.InternalMessageInfo

func (m *LineTabListResp_TabInfo) GetId() int64 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *LineTabListResp_TabInfo) GetTabName() string {
	if m != nil {
		return m.TabName
	}
	return ""
}

func (m *LineTabListResp_TabInfo) GetDetail() *LineUserListReq {
	if m != nil {
		return m.Detail
	}
	return nil
}

func (m *LineTabListResp_TabInfo) GetPublic() int32 {
	if m != nil {
		return m.Public
	}
	return 0
}

func (m *LineTabListResp_TabInfo) GetOperator() string {
	if m != nil {
		return m.Operator
	}
	return ""
}

type LineTabCountResp struct {
	Detail               []*LineTabCountResp_LineTabCount `protobuf:"bytes,1,rep,name=detail,proto3" json:"detail"`
	XXX_NoUnkeyedLiteral struct{}                         `json:"-" gorm:"-"`
	XXX_unrecognized     []byte                           `json:"-" gorm:"-"`
	XXX_sizecache        int32                            `json:"-" gorm:"-"`
}

func (m *LineTabCountResp) Reset()         { *m = LineTabCountResp{} }
func (m *LineTabCountResp) String() string { return proto.CompactTextString(m) }
func (*LineTabCountResp) ProtoMessage()    {}
func (*LineTabCountResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_951bc87fae86fd79, []int{33}
}

func (m *LineTabCountResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LineTabCountResp.Unmarshal(m, b)
}
func (m *LineTabCountResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LineTabCountResp.Marshal(b, m, deterministic)
}
func (m *LineTabCountResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LineTabCountResp.Merge(m, src)
}
func (m *LineTabCountResp) XXX_Size() int {
	return xxx_messageInfo_LineTabCountResp.Size(m)
}
func (m *LineTabCountResp) XXX_DiscardUnknown() {
	xxx_messageInfo_LineTabCountResp.DiscardUnknown(m)
}

var xxx_messageInfo_LineTabCountResp proto.InternalMessageInfo

func (m *LineTabCountResp) GetDetail() []*LineTabCountResp_LineTabCount {
	if m != nil {
		return m.Detail
	}
	return nil
}

type LineTabCountResp_LineTabCount struct {
	Tab                  []*LineTabCountResp_TabCountDetail `protobuf:"bytes,1,rep,name=tab,proto3" json:"tab"`
	Project              string                             `protobuf:"bytes,2,opt,name=project,proto3" json:"project"`
	XXX_NoUnkeyedLiteral struct{}                           `json:"-" gorm:"-"`
	XXX_unrecognized     []byte                             `json:"-" gorm:"-"`
	XXX_sizecache        int32                              `json:"-" gorm:"-"`
}

func (m *LineTabCountResp_LineTabCount) Reset()         { *m = LineTabCountResp_LineTabCount{} }
func (m *LineTabCountResp_LineTabCount) String() string { return proto.CompactTextString(m) }
func (*LineTabCountResp_LineTabCount) ProtoMessage()    {}
func (*LineTabCountResp_LineTabCount) Descriptor() ([]byte, []int) {
	return fileDescriptor_951bc87fae86fd79, []int{33, 0}
}

func (m *LineTabCountResp_LineTabCount) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LineTabCountResp_LineTabCount.Unmarshal(m, b)
}
func (m *LineTabCountResp_LineTabCount) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LineTabCountResp_LineTabCount.Marshal(b, m, deterministic)
}
func (m *LineTabCountResp_LineTabCount) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LineTabCountResp_LineTabCount.Merge(m, src)
}
func (m *LineTabCountResp_LineTabCount) XXX_Size() int {
	return xxx_messageInfo_LineTabCountResp_LineTabCount.Size(m)
}
func (m *LineTabCountResp_LineTabCount) XXX_DiscardUnknown() {
	xxx_messageInfo_LineTabCountResp_LineTabCount.DiscardUnknown(m)
}

var xxx_messageInfo_LineTabCountResp_LineTabCount proto.InternalMessageInfo

func (m *LineTabCountResp_LineTabCount) GetTab() []*LineTabCountResp_TabCountDetail {
	if m != nil {
		return m.Tab
	}
	return nil
}

func (m *LineTabCountResp_LineTabCount) GetProject() string {
	if m != nil {
		return m.Project
	}
	return ""
}

type LineTabCountResp_TabCountDetail struct {
	TabName              string   `protobuf:"bytes,2,opt,name=tab_name,json=tabName,proto3" json:"tab_name"`
	Count                uint64   `protobuf:"varint,5,opt,name=count,proto3" json:"count"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *LineTabCountResp_TabCountDetail) Reset()         { *m = LineTabCountResp_TabCountDetail{} }
func (m *LineTabCountResp_TabCountDetail) String() string { return proto.CompactTextString(m) }
func (*LineTabCountResp_TabCountDetail) ProtoMessage()    {}
func (*LineTabCountResp_TabCountDetail) Descriptor() ([]byte, []int) {
	return fileDescriptor_951bc87fae86fd79, []int{33, 1}
}

func (m *LineTabCountResp_TabCountDetail) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LineTabCountResp_TabCountDetail.Unmarshal(m, b)
}
func (m *LineTabCountResp_TabCountDetail) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LineTabCountResp_TabCountDetail.Marshal(b, m, deterministic)
}
func (m *LineTabCountResp_TabCountDetail) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LineTabCountResp_TabCountDetail.Merge(m, src)
}
func (m *LineTabCountResp_TabCountDetail) XXX_Size() int {
	return xxx_messageInfo_LineTabCountResp_TabCountDetail.Size(m)
}
func (m *LineTabCountResp_TabCountDetail) XXX_DiscardUnknown() {
	xxx_messageInfo_LineTabCountResp_TabCountDetail.DiscardUnknown(m)
}

var xxx_messageInfo_LineTabCountResp_TabCountDetail proto.InternalMessageInfo

func (m *LineTabCountResp_TabCountDetail) GetTabName() string {
	if m != nil {
		return m.TabName
	}
	return ""
}

func (m *LineTabCountResp_TabCountDetail) GetCount() uint64 {
	if m != nil {
		return m.Count
	}
	return 0
}

type LineTabUpdateSortReq struct {
	// @gotags: validate:"required"
	SortSetting          string   `protobuf:"bytes,1,opt,name=sort_setting,json=sortSetting,proto3" json:"sort_setting" validate:"required"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *LineTabUpdateSortReq) Reset()         { *m = LineTabUpdateSortReq{} }
func (m *LineTabUpdateSortReq) String() string { return proto.CompactTextString(m) }
func (*LineTabUpdateSortReq) ProtoMessage()    {}
func (*LineTabUpdateSortReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_951bc87fae86fd79, []int{34}
}

func (m *LineTabUpdateSortReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_LineTabUpdateSortReq.Unmarshal(m, b)
}
func (m *LineTabUpdateSortReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_LineTabUpdateSortReq.Marshal(b, m, deterministic)
}
func (m *LineTabUpdateSortReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_LineTabUpdateSortReq.Merge(m, src)
}
func (m *LineTabUpdateSortReq) XXX_Size() int {
	return xxx_messageInfo_LineTabUpdateSortReq.Size(m)
}
func (m *LineTabUpdateSortReq) XXX_DiscardUnknown() {
	xxx_messageInfo_LineTabUpdateSortReq.DiscardUnknown(m)
}

var xxx_messageInfo_LineTabUpdateSortReq proto.InternalMessageInfo

func (m *LineTabUpdateSortReq) GetSortSetting() string {
	if m != nil {
		return m.SortSetting
	}
	return ""
}

func init() {
	proto.RegisterType((*LineUserListReq)(nil), "pb.LineUserListReq")
	proto.RegisterType((*LineUserListResp)(nil), "pb.LineUserListResp")
	proto.RegisterType((*LineUserListResp_LineUser)(nil), "pb.LineUserListResp.LineUser")
	proto.RegisterType((*LineUserDetailReq)(nil), "pb.LineUserDetailReq")
	proto.RegisterType((*LineUserDetailResp)(nil), "pb.LineUserDetailResp")
	proto.RegisterType((*LineUserDetailResp_LineUserDetail)(nil), "pb.LineUserDetailResp.LineUserDetail")
	proto.RegisterType((*LinePoolInfo)(nil), "pb.LinePoolInfo")
	proto.RegisterType((*LineStatsReq)(nil), "pb.LineStatsReq")
	proto.RegisterType((*LineStatsResp)(nil), "pb.LineStatsResp")
	proto.RegisterType((*LineSendTextMessageReq)(nil), "pb.LineSendTextMessageReq")
	proto.RegisterType((*LineSendTextMessageReq_Emoji)(nil), "pb.LineSendTextMessageReq.Emoji")
	proto.RegisterType((*LineSendMessageResp)(nil), "pb.LineSendMessageResp")
	proto.RegisterType((*LineSendFileReq)(nil), "pb.LineSendFileReq")
	proto.RegisterType((*LineDialogueHistoryReq)(nil), "pb.LineDialogueHistoryReq")
	proto.RegisterType((*LineDialogueHistoryResp)(nil), "pb.LineDialogueHistoryResp")
	proto.RegisterType((*LineDialogueHistoryResp_Dialogue)(nil), "pb.LineDialogueHistoryResp.Dialogue")
	proto.RegisterType((*LineDialogFreshReq)(nil), "pb.LineDialogFreshReq")
	proto.RegisterType((*LinePortraitInfoReq)(nil), "pb.LinePortraitInfoReq")
	proto.RegisterType((*LinePortraitInfoResp)(nil), "pb.LinePortraitInfoResp")
	proto.RegisterType((*LinePortraitInfoResp_LabelDetail)(nil), "pb.LinePortraitInfoResp.LabelDetail")
	proto.RegisterType((*LinePortraitEditTagReq)(nil), "pb.LinePortraitEditTagReq")
	proto.RegisterType((*LinePortraitEditBasicReq)(nil), "pb.LinePortraitEditBasicReq")
	proto.RegisterType((*LinePortraitEditRemarkReq)(nil), "pb.LinePortraitEditRemarkReq")
	proto.RegisterType((*LineMaintainConfigDelReq)(nil), "pb.LineMaintainConfigDelReq")
	proto.RegisterType((*LineMaintainConfigListReq)(nil), "pb.LineMaintainConfigListReq")
	proto.RegisterType((*LineMaintainConfigListResp)(nil), "pb.LineMaintainConfigListResp")
	proto.RegisterType((*LineMaintainConfigListResp_LineMaintainConfigInfo)(nil), "pb.LineMaintainConfigListResp.LineMaintainConfigInfo")
	proto.RegisterType((*LineMaintainConfigEditReq)(nil), "pb.LineMaintainConfigEditReq")
	proto.RegisterType((*LinePlayerAccountsReq)(nil), "pb.LinePlayerAccountsReq")
	proto.RegisterType((*LinePlayerAccountsResp)(nil), "pb.LinePlayerAccountsResp")
	proto.RegisterType((*LineCommuRecordAddReq)(nil), "pb.LineCommuRecordAddReq")
	proto.RegisterType((*LineCommuRecordEditReq)(nil), "pb.LineCommuRecordEditReq")
	proto.RegisterType((*LineCommuRecordListReq)(nil), "pb.LineCommuRecordListReq")
	proto.RegisterType((*LineCommuRecordListResp)(nil), "pb.LineCommuRecordListResp")
	proto.RegisterType((*LineCommuRecordListResp_NewLabel)(nil), "pb.LineCommuRecordListResp.New_label")
	proto.RegisterType((*LineCommuRecordListResp_DialogueItem)(nil), "pb.LineCommuRecordListResp.DialogueItem")
	proto.RegisterType((*LineCommuRecordListResp_LineCommuRecord)(nil), "pb.LineCommuRecordListResp.LineCommuRecord")
	proto.RegisterType((*LineUserRemarkAddReq)(nil), "pb.LineUserRemarkAddReq")
	proto.RegisterType((*LineTabAddReq)(nil), "pb.LineTabAddReq")
	proto.RegisterType((*LineTabEditReq)(nil), "pb.LineTabEditReq")
	proto.RegisterType((*LineTabDelReq)(nil), "pb.LineTabDelReq")
	proto.RegisterType((*LineTabListResp)(nil), "pb.LineTabListResp")
	proto.RegisterType((*LineTabListResp_LineTabDetail)(nil), "pb.LineTabListResp.LineTabDetail")
	proto.RegisterType((*LineTabListResp_TabInfo)(nil), "pb.LineTabListResp.TabInfo")
	proto.RegisterType((*LineTabCountResp)(nil), "pb.LineTabCountResp")
	proto.RegisterType((*LineTabCountResp_LineTabCount)(nil), "pb.LineTabCountResp.LineTabCount")
	proto.RegisterType((*LineTabCountResp_TabCountDetail)(nil), "pb.LineTabCountResp.TabCountDetail")
	proto.RegisterType((*LineTabUpdateSortReq)(nil), "pb.LineTabUpdateSortReq")
}

func init() {
	proto.RegisterFile("line.proto", fileDescriptor_951bc87fae86fd79)
}

var fileDescriptor_951bc87fae86fd79 = []byte{
	// 2941 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xdc, 0x5a, 0x4b, 0x6f, 0x24, 0x57,
	0xf5, 0x57, 0x75, 0xf5, 0xf3, 0xb6, 0xdb, 0x8f, 0xb2, 0xc7, 0x53, 0xd3, 0x93, 0xc9, 0x74, 0x6a,
	0xfe, 0xf9, 0xc7, 0x4a, 0x82, 0x07, 0x06, 0x8d, 0xc8, 0x0b, 0x45, 0x93, 0xf1, 0x8c, 0x62, 0xe2,
	0x24, 0x43, 0xd9, 0x23, 0x10, 0x9b, 0xd2, 0xed, 0xae, 0xeb, 0xf6, 0x8d, 0xab, 0xeb, 0xd6, 0x54,
	0xdd, 0xf6, 0xa4, 0xb3, 0x41, 0x62, 0x87, 0x58, 0x22, 0xd8, 0x81, 0x60, 0x07, 0x0b, 0x58, 0x20,
	0x21, 0xa4, 0x7c, 0x06, 0x36, 0x08, 0x89, 0x35, 0x02, 0x3e, 0x00, 0x6c, 0x58, 0x21, 0x81, 0xce,
	0xb9, 0xb7, 0xaa, 0xeb, 0xd1, 0x6d, 0xc7, 0xa0, 0x01, 0xc4, 0xca, 0x7d, 0xce, 0x7d, 0x9d, 0x7b,
	0xce, 0xf9, 0x9d, 0xc7, 0x2d, 0x13, 0x12, 0xf0, 0x90, 0xed, 0x46, 0xb1, 0x90, 0xc2, 0xaa, 0x45,
	0xc3, 0x3e, 0x61, 0xe1, 0x74, 0xa2, 0xe8, 0xfe, 0xca, 0x48, 0x4c, 0x26, 0x22, 0xd4, 0xd4, 0x73,
	0x63, 0x21, 0xc6, 0x01, 0xbb, 0x4d, 0x23, 0x7e, 0x9b, 0x86, 0xa1, 0x90, 0x54, 0x72, 0x11, 0x26,
	0x6a, 0xd4, 0xf9, 0x45, 0x93, 0xac, 0x1d, 0xf0, 0x90, 0x3d, 0x4e, 0x58, 0x7c, 0xc0, 0x13, 0xe9,
	0xb2, 0x27, 0x96, 0x4d, 0x5a, 0x51, 0x2c, 0x3e, 0x62, 0x23, 0x69, 0x1b, 0x03, 0x73, 0xa7, 0xe3,
	0xa6, 0xa4, 0x75, 0x83, 0x90, 0x98, 0x45, 0x01, 0x67, 0xbe, 0x47, 0xa5, 0x5d, 0xc3, 0xc1, 0x8e,
	0xe6, 0xdc, 0x93, 0xd6, 0x36, 0x69, 0x26, 0x92, 0xca, 0x69, 0x62, 0x9b, 0x03, 0x73, 0xa7, 0xe7,
	0x6a, 0xca, 0x7a, 0x81, 0xac, 0x4c, 0x13, 0x16, 0x7b, 0x23, 0x11, 0x4a, 0x16, 0x4a, 0xbb, 0x3e,
	0x30, 0x76, 0x3a, 0x6e, 0x17, 0x78, 0xf7, 0x15, 0xcb, 0x7a, 0x95, 0x58, 0x38, 0xc5, 0x67, 0x92,
	0xf2, 0xc0, 0x8b, 0xd9, 0x84, 0xc6, 0xa7, 0x76, 0x03, 0x27, 0xae, 0xc3, 0xc8, 0x1e, 0x0e, 0xb8,
	0xc8, 0xb7, 0x9e, 0x27, 0x64, 0x42, 0x79, 0x28, 0x29, 0x0f, 0x59, 0x6c, 0x37, 0x51, 0x8e, 0x1c,
	0x07, 0x0e, 0xf4, 0x79, 0x12, 0x05, 0x74, 0xe6, 0x85, 0x74, 0xc2, 0xec, 0x96, 0x3a, 0x50, 0xf3,
	0x3e, 0xa0, 0x13, 0x66, 0x0d, 0xc8, 0x0a, 0xa8, 0xd0, 0xc3, 0x53, 0xb9, 0x6f, 0xb7, 0x71, 0x0a,
	0xaa, 0x15, 0x74, 0xb1, 0xef, 0x5b, 0xeb, 0xc4, 0x9c, 0x72, 0xdf, 0xee, 0x0c, 0x8c, 0x9d, 0xba,
	0x0b, 0x3f, 0x2d, 0x8b, 0xd4, 0x8f, 0x23, 0xee, 0xdb, 0x04, 0xe7, 0xe2, 0x6f, 0x98, 0x95, 0x70,
	0xdf, 0xee, 0x22, 0x0b, 0x7e, 0x82, 0x92, 0x02, 0x9a, 0x48, 0x2f, 0x10, 0x63, 0x1e, 0xda, 0x2b,
	0x4a, 0x49, 0xc0, 0x39, 0x00, 0x86, 0x75, 0x95, 0xb4, 0x22, 0x3a, 0xf3, 0x68, 0x10, 0xd8, 0xbd,
	0x81, 0xb9, 0x63, 0xba, 0xcd, 0x88, 0xce, 0xee, 0x05, 0x81, 0x75, 0x9b, 0x6c, 0xc1, 0x00, 0xae,
	0x95, 0x27, 0x3c, 0x96, 0x33, 0xcf, 0xa7, 0xb3, 0xc4, 0x5e, 0xc5, 0x59, 0x1b, 0x11, 0x9d, 0x1d,
	0xd0, 0x44, 0x1e, 0xe1, 0xc8, 0x1e, 0x9d, 0x25, 0xd6, 0x75, 0xd2, 0x39, 0xe3, 0x91, 0x07, 0x4a,
	0x66, 0xf6, 0xda, 0xc0, 0xd8, 0xe9, 0xb9, 0xed, 0x33, 0x1e, 0x1d, 0x02, 0x6d, 0xf5, 0x49, 0x7b,
	0xc8, 0x63, 0x79, 0xe2, 0xd3, 0x99, 0xbd, 0x81, 0x32, 0x64, 0x34, 0xdc, 0x23, 0xa0, 0xe1, 0xd8,
	0xb6, 0xd4, 0x3d, 0xe0, 0x37, 0x18, 0x00, 0x4f, 0x06, 0x6b, 0xce, 0xbc, 0x84, 0xc5, 0x67, 0x7c,
	0xc4, 0xec, 0x4d, 0x5c, 0xb9, 0x0e, 0x23, 0x2e, 0x0c, 0x1c, 0x2a, 0xbe, 0xf5, 0x12, 0x69, 0x25,
	0x22, 0x96, 0xde, 0x70, 0x66, 0x6f, 0x0d, 0x8c, 0x9d, 0xd5, 0x3b, 0xab, 0xbb, 0xd1, 0x70, 0x77,
	0x6f, 0xf4, 0x48, 0x88, 0xe0, 0x50, 0xc4, 0xd2, 0x6d, 0xc2, 0xf0, 0x3b, 0x33, 0x6b, 0x8b, 0x34,
	0x44, 0xec, 0xb3, 0xd8, 0xbe, 0x82, 0x67, 0x29, 0x02, 0x04, 0x88, 0xe8, 0x98, 0xd9, 0xdb, 0x28,
	0x34, 0xfe, 0x86, 0xdb, 0xc0, 0x5f, 0x2f, 0xe1, 0x9f, 0x30, 0xfb, 0xaa, 0xba, 0x0d, 0x30, 0x0e,
	0xf9, 0x27, 0x0c, 0x94, 0x36, 0x14, 0xd2, 0xe3, 0x7e, 0x62, 0xdb, 0x28, 0x52, 0x73, 0x28, 0xe4,
	0xbe, 0x9f, 0xc0, 0x4e, 0x53, 0xe0, 0x5e, 0x53, 0x57, 0x81, 0xdf, 0x60, 0x80, 0xd1, 0x09, 0x0d,
	0x43, 0x16, 0x80, 0x61, 0xfb, 0xca, 0x00, 0x9a, 0xb3, 0x8f, 0x56, 0x94, 0x74, 0x9c, 0xd8, 0xd7,
	0xd1, 0x47, 0xf1, 0xb7, 0xf5, 0x2a, 0x69, 0x4b, 0x3a, 0xf6, 0xe4, 0x2c, 0x62, 0xf6, 0x73, 0x78,
	0xa1, 0x0d, 0xb8, 0xd0, 0x43, 0x1e, 0x48, 0x16, 0x1f, 0xd1, 0xf1, 0x83, 0x70, 0x3a, 0x71, 0x5b,
	0x92, 0x8e, 0x8f, 0x66, 0x11, 0x73, 0xfe, 0xd6, 0x20, 0xeb, 0x45, 0xd0, 0x24, 0x11, 0xf8, 0xdc,
	0x68, 0x1a, 0xc7, 0x2c, 0x94, 0x1e, 0xde, 0xcd, 0xc0, 0x2b, 0x74, 0x35, 0xef, 0x11, 0x5c, 0xf1,
	0x1a, 0x69, 0x47, 0x2c, 0x56, 0xc3, 0x35, 0x1c, 0x6e, 0x45, 0x2c, 0xc6, 0xa1, 0x2d, 0xd2, 0x90,
	0x42, 0xd2, 0xc0, 0x36, 0x91, 0xaf, 0x08, 0xeb, 0x0b, 0xa4, 0xee, 0x53, 0x49, 0xed, 0xfa, 0xc0,
	0xdc, 0xe9, 0xde, 0xb9, 0x01, 0x22, 0x95, 0xcf, 0xcd, 0x18, 0x2e, 0x4e, 0xed, 0xff, 0xbe, 0x4e,
	0xda, 0x29, 0xab, 0x88, 0x64, 0x23, 0x8f, 0xe4, 0xb2, 0xfb, 0xd7, 0x2a, 0xee, 0x5f, 0xc6, 0x90,
	0x59, 0xc5, 0x50, 0x51, 0xd1, 0x0a, 0xd5, 0x39, 0x45, 0x17, 0x51, 0xaa, 0xb0, 0x9c, 0x47, 0xe9,
	0x75, 0xd2, 0xc1, 0x6b, 0x7a, 0x11, 0x9d, 0xd9, 0xcd, 0x81, 0xb1, 0x63, 0xb8, 0x6d, 0x64, 0x3c,
	0xa2, 0xb3, 0xa5, 0x68, 0x68, 0xe1, 0xbc, 0x05, 0x68, 0x28, 0xc2, 0x4e, 0xc1, 0x39, 0x07, 0xbb,
	0x79, 0x6c, 0xea, 0xa0, 0x86, 0xd3, 0xd8, 0xa4, 0x41, 0x14, 0xb0, 0x33, 0x16, 0x20, 0xb0, 0x15,
	0x88, 0x0e, 0x80, 0xce, 0x00, 0xdf, 0x2d, 0x02, 0x1e, 0xc2, 0xc2, 0xca, 0x3c, 0x2c, 0xe8, 0x10,
	0xd0, 0x9b, 0x87, 0x80, 0x2b, 0xa4, 0xa9, 0xdc, 0xd5, 0x5e, 0x55, 0x6e, 0x8f, 0xde, 0x0a, 0xdb,
	0x85, 0x42, 0x63, 0xb5, 0xe3, 0xe2, 0x6f, 0xeb, 0x26, 0xe9, 0x82, 0x3e, 0x59, 0xec, 0x85, 0x7c,
	0x74, 0x6a, 0xaf, 0x2b, 0x2d, 0x29, 0xd6, 0x07, 0x7c, 0x74, 0x5a, 0x02, 0xb2, 0x71, 0x21, 0x90,
	0x6d, 0xd2, 0x1a, 0x9d, 0xb0, 0xd1, 0x29, 0xf3, 0xed, 0xcd, 0x81, 0xb1, 0xd3, 0x76, 0x53, 0x12,
	0x3d, 0x54, 0x9b, 0x2b, 0x39, 0x11, 0x4f, 0x11, 0xb9, 0x1d, 0xb7, 0xab, 0x79, 0x87, 0x27, 0xe2,
	0xa9, 0x75, 0x8b, 0xf4, 0x8e, 0x45, 0x10, 0x88, 0xa7, 0x9e, 0x56, 0x16, 0xc0, 0xb6, 0xe1, 0xae,
	0x28, 0xe6, 0x21, 0xf2, 0x9c, 0xbb, 0x64, 0x23, 0xf5, 0xb0, 0x34, 0x2a, 0x3f, 0xa9, 0x38, 0x94,
	0x51, 0x76, 0x28, 0xe7, 0xfb, 0x35, 0x62, 0x95, 0xd7, 0x25, 0x91, 0xf5, 0x21, 0x59, 0x9f, 0x2f,
	0x54, 0xe1, 0x1f, 0x17, 0x77, 0xef, 0xbc, 0x98, 0xf7, 0xf7, 0xf9, 0x8a, 0x32, 0x6b, 0x35, 0x28,
	0xd0, 0xfd, 0x9f, 0x1b, 0x64, 0xb5, 0x38, 0xe5, 0xbf, 0x1b, 0x07, 0xce, 0x6f, 0xea, 0x64, 0x05,
	0x04, 0x86, 0xe0, 0xb9, 0x1f, 0x1e, 0x8b, 0xff, 0xb0, 0xb8, 0x65, 0x37, 0x69, 0x54, 0xdd, 0xa4,
	0x9c, 0x7f, 0xcb, 0xc8, 0x9e, 0xfb, 0x7f, 0x2b, 0xef, 0xff, 0x1a, 0x3a, 0xed, 0x39, 0x74, 0x6e,
	0x10, 0x42, 0x47, 0x23, 0x31, 0x0d, 0x71, 0x72, 0x47, 0x89, 0xa2, 0x39, 0xfb, 0x19, 0xb2, 0xc8,
	0xb2, 0xe4, 0xda, 0x2d, 0xa3, 0x3c, 0x97, 0x5c, 0x01, 0xa0, 0x17, 0x27, 0xd7, 0x1e, 0xce, 0x5a,
	0x10, 0x4e, 0xe6, 0xf1, 0x62, 0x75, 0x79, 0xbc, 0x58, 0xab, 0xc6, 0x0b, 0x04, 0xf8, 0xfa, 0x72,
	0x80, 0x6f, 0x9c, 0x0b, 0x70, 0x6b, 0x09, 0xc0, 0x37, 0x73, 0x00, 0xaf, 0x60, 0x74, 0x6b, 0x01,
	0x46, 0x77, 0x94, 0x4b, 0x01, 0x95, 0x9c, 0x5b, 0xd3, 0x39, 0x3f, 0x32, 0x48, 0x2f, 0x37, 0x35,
	0x89, 0xac, 0xff, 0x27, 0x6b, 0x73, 0x27, 0x43, 0x53, 0xa0, 0x1b, 0x9a, 0x6e, 0x2f, 0xf5, 0xb3,
	0xfb, 0xc0, 0xb4, 0x76, 0xc9, 0xe6, 0x53, 0xca, 0xd3, 0x92, 0x41, 0x5b, 0x2d, 0x41, 0x9f, 0x34,
	0xdd, 0x0d, 0x18, 0xc2, 0x9a, 0xe1, 0x9e, 0x1e, 0xb0, 0xbe, 0x44, 0xec, 0x09, 0xec, 0xbb, 0x68,
	0x91, 0x89, 0x8b, 0xae, 0xc0, 0xf8, 0xd7, 0xca, 0x0b, 0x9d, 0x5f, 0xd6, 0xc8, 0x36, 0x8a, 0xc8,
	0x42, 0xff, 0x88, 0x7d, 0x2c, 0xdf, 0x67, 0x49, 0x42, 0xc7, 0x0c, 0xee, 0x55, 0xf4, 0x65, 0xa3,
	0xec, 0xcb, 0x73, 0x47, 0xac, 0xe5, 0x1d, 0xb1, 0x0c, 0x23, 0xb3, 0x02, 0x23, 0x88, 0xa2, 0x85,
	0x6a, 0x35, 0x25, 0xad, 0xd7, 0x48, 0x93, 0x4d, 0xc4, 0x47, 0x3c, 0xb1, 0x1b, 0x98, 0x95, 0x07,
	0x69, 0x94, 0xaa, 0x4a, 0xb7, 0xfb, 0x00, 0x26, 0xba, 0x7a, 0x7e, 0x5f, 0x90, 0x06, 0x32, 0x20,
	0xd9, 0xf3, 0xd0, 0x67, 0x1f, 0xeb, 0x1a, 0x41, 0x11, 0xe0, 0x71, 0x01, 0x0b, 0xc7, 0xf2, 0x44,
	0xd7, 0x06, 0x9a, 0x82, 0x2b, 0x46, 0xb1, 0xf0, 0xa7, 0x23, 0x39, 0x17, 0xb5, 0xa3, 0x39, 0xfb,
	0x3e, 0x14, 0x15, 0xb8, 0xff, 0x1c, 0xcb, 0x2d, 0xa4, 0xf7, 0x7d, 0xe7, 0x3d, 0xb2, 0x99, 0x0a,
	0x96, 0x09, 0x95, 0x44, 0xa0, 0x94, 0x49, 0x32, 0x9e, 0xeb, 0xab, 0x31, 0x49, 0xc6, 0xfb, 0x7e,
	0x49, 0x95, 0xb5, 0x92, 0x2a, 0x9d, 0x1f, 0x1a, 0xaa, 0x53, 0x80, 0xdd, 0x1e, 0xf2, 0xe0, 0x99,
	0x6a, 0xff, 0x3a, 0xe9, 0x1c, 0xf3, 0x80, 0xa9, 0x7a, 0x4c, 0x5d, 0xaa, 0x0d, 0x0c, 0xa8, 0xbe,
	0x40, 0x4f, 0xc7, 0x22, 0x9e, 0x50, 0xa9, 0x23, 0x93, 0xa6, 0x9c, 0xdf, 0x19, 0xca, 0x4b, 0xf6,
	0x38, 0x0d, 0xc4, 0x78, 0xca, 0xde, 0xe5, 0x89, 0x14, 0xf1, 0xec, 0x59, 0xca, 0xb9, 0x4d, 0x9a,
	0x43, 0x76, 0x2c, 0x62, 0x25, 0x64, 0xdb, 0xd5, 0x14, 0x56, 0x36, 0x7c, 0x74, 0xea, 0x49, 0x3e,
	0x61, 0x28, 0x65, 0xdd, 0x6d, 0x03, 0xe3, 0x88, 0x4f, 0x58, 0x56, 0xfc, 0x36, 0x97, 0x15, 0xbf,
	0xad, 0x62, 0xf1, 0xeb, 0xfc, 0xb5, 0x41, 0xae, 0x2e, 0xbc, 0xd8, 0x33, 0xaa, 0x3a, 0x5f, 0x2b,
	0x54, 0x9d, 0xff, 0x97, 0xfa, 0xf7, 0x82, 0xe3, 0x77, 0x53, 0x9e, 0x2e, 0x3e, 0x7f, 0x5d, 0x27,
	0xed, 0x94, 0x05, 0x4a, 0x9f, 0x28, 0xaf, 0xcb, 0x29, 0x5d, 0x73, 0xfe, 0x15, 0xa5, 0x97, 0x33,
	0x5c, 0xfd, 0xa2, 0x0c, 0xd7, 0x58, 0x90, 0xe1, 0x52, 0xc9, 0x8e, 0x63, 0x31, 0xd1, 0x09, 0xac,
	0xab, 0x79, 0x0f, 0x63, 0x31, 0xc1, 0xf0, 0xcf, 0x42, 0x68, 0x5c, 0x54, 0x06, 0xd3, 0x54, 0x7e,
	0x29, 0x3a, 0x67, 0xbb, 0xb0, 0x14, 0xfd, 0xf3, 0x26, 0xe9, 0x3e, 0x99, 0x0a, 0xc9, 0x3c, 0x29,
	0x4e, 0x59, 0xa8, 0x93, 0x1a, 0x41, 0xd6, 0x11, 0x70, 0xf2, 0xb1, 0x85, 0x14, 0x63, 0xcb, 0x36,
	0x69, 0xc6, 0xec, 0x4c, 0x9c, 0x32, 0xcc, 0x6c, 0x6d, 0x57, 0x53, 0xd6, 0xe7, 0xc9, 0x96, 0x88,
	0xf9, 0x98, 0x87, 0x34, 0x48, 0x9b, 0x68, 0x6f, 0x1a, 0xab, 0x1c, 0xd7, 0x71, 0xad, 0x74, 0x4c,
	0x37, 0xd3, 0x8f, 0xe3, 0xc0, 0x7a, 0x99, 0x6c, 0x44, 0x31, 0x3b, 0xe3, 0xec, 0xa9, 0xc7, 0x27,
	0x20, 0x2d, 0x4c, 0x57, 0x15, 0xea, 0x9a, 0x1e, 0xd8, 0x07, 0x3e, 0xcc, 0xed, 0x93, 0xb6, 0x3f,
	0x8d, 0xf1, 0x59, 0x00, 0x93, 0x5d, 0xc3, 0xcd, 0x68, 0xb8, 0x6f, 0x02, 0x9e, 0x0b, 0xc6, 0x08,
	0x8f, 0x85, 0x2e, 0x5d, 0xbb, 0x9a, 0x87, 0xd5, 0x4a, 0x9f, 0xb4, 0x03, 0x31, 0x52, 0xcb, 0x55,
	0xe2, 0xcb, 0xe8, 0x22, 0x10, 0x36, 0x4a, 0x40, 0xb8, 0x49, 0xba, 0xa3, 0x98, 0x51, 0xd0, 0x14,
	0x0c, 0xab, 0xdc, 0x47, 0x14, 0x0b, 0x26, 0x38, 0xdf, 0x36, 0x54, 0xc5, 0xa8, 0x5c, 0xea, 0x61,
	0xcc, 0x92, 0x93, 0x67, 0x1c, 0x75, 0xe6, 0xc2, 0xd6, 0x31, 0x21, 0x65, 0xc2, 0x3a, 0x5f, 0x55,
	0xb1, 0xf4, 0x91, 0x88, 0x65, 0x4c, 0xb9, 0x84, 0x9b, 0x7f, 0xa6, 0xb2, 0x37, 0x9f, 0x79, 0x6b,
	0x85, 0x62, 0xce, 0xf9, 0xd4, 0x24, 0x5b, 0xd5, 0x3d, 0x93, 0xc8, 0x5a, 0x25, 0x35, 0xbd, 0x55,
	0xdd, 0xad, 0x71, 0xdf, 0x7a, 0x83, 0x34, 0x02, 0x3a, 0x64, 0x01, 0xbe, 0xb8, 0xe4, 0x10, 0x59,
	0x5e, 0xb8, 0x7b, 0x00, 0xb3, 0x74, 0x59, 0xac, 0x96, 0x80, 0x4b, 0x8d, 0x95, 0x23, 0x2b, 0x8c,
	0x6b, 0xaa, 0x50, 0x75, 0xd4, 0x4b, 0x55, 0xc7, 0x36, 0x69, 0x8e, 0x68, 0xcc, 0xb2, 0x62, 0x55,
	0x53, 0xd6, 0x4b, 0x64, 0x8d, 0xf9, 0x53, 0x65, 0x5a, 0x5d, 0x01, 0xa9, 0x20, 0xb6, 0x9a, 0xb1,
	0x55, 0x1d, 0x74, 0x8b, 0xf4, 0x26, 0x34, 0x8e, 0x39, 0xf3, 0xf5, 0xeb, 0x84, 0x0a, 0x69, 0x2b,
	0x9a, 0xa9, 0x5e, 0x28, 0x5e, 0x22, 0x6b, 0xc7, 0x2c, 0x96, 0x3c, 0xe0, 0x72, 0xa6, 0xa7, 0xb5,
	0xd5, 0x6e, 0x19, 0x5b, 0x4d, 0x44, 0x54, 0xe0, 0x7b, 0x90, 0xc2, 0x92, 0xa6, 0x2a, 0xba, 0x27,
	0xe7, 0xe9, 0xbe, 0x5b, 0xd0, 0x7d, 0xff, 0x6d, 0xd2, 0xcd, 0x29, 0x0b, 0x7c, 0x06, 0xfa, 0xff,
	0x4c, 0xeb, 0x0d, 0x49, 0xc7, 0x2a, 0xb7, 0x02, 0xdb, 0x67, 0xc9, 0x28, 0x35, 0x9e, 0xa4, 0xe3,
	0x3d, 0x96, 0x8c, 0x9c, 0x33, 0x95, 0x6c, 0x52, 0x13, 0x3c, 0xf0, 0xb9, 0x3c, 0xa2, 0x63, 0x70,
	0x89, 0xb2, 0xf5, 0x96, 0x3a, 0x00, 0x94, 0xb7, 0x92, 0x8e, 0xb5, 0x27, 0xc2, 0xcf, 0xca, 0x95,
	0xea, 0x95, 0x2e, 0xea, 0xa7, 0x35, 0x62, 0x97, 0x0f, 0x7e, 0x87, 0x26, 0x7c, 0x74, 0xb9, 0xa3,
	0xff, 0x07, 0xdc, 0xa2, 0xac, 0xab, 0x4e, 0x45, 0x57, 0xdf, 0x24, 0xd7, 0xca, 0xaa, 0x52, 0x0f,
	0x88, 0x97, 0xd6, 0x95, 0xf6, 0x3f, 0xf3, 0x5c, 0xff, 0xab, 0x1a, 0xeb, 0x58, 0xd9, 0xea, 0x7d,
	0xdd, 0x19, 0xdd, 0x17, 0xe1, 0x31, 0x1f, 0xef, 0xb1, 0x60, 0xd1, 0xf9, 0x17, 0xb7, 0x76, 0x39,
	0x09, 0xcd, 0x62, 0x24, 0xf9, 0x83, 0xa1, 0x6e, 0x5a, 0x3c, 0xe8, 0xe2, 0xf7, 0xdc, 0x62, 0x1f,
	0x57, 0xab, 0xbc, 0xa3, 0x5e, 0x27, 0x1d, 0x68, 0x5a, 0xd2, 0x4e, 0x12, 0x5f, 0x11, 0x81, 0x91,
	0xbe, 0xa0, 0x1e, 0x2c, 0xb8, 0xfe, 0x41, 0x21, 0xa0, 0xce, 0x1f, 0x28, 0x1b, 0xa5, 0x07, 0xca,
	0x4b, 0x97, 0x41, 0x3f, 0xae, 0x93, 0xfe, 0xb2, 0x4b, 0x3e, 0xa3, 0x4a, 0x68, 0xbf, 0x50, 0x09,
	0xdd, 0x4d, 0xe3, 0xee, 0x62, 0x09, 0x16, 0x0c, 0x61, 0x60, 0x56, 0xa5, 0xd1, 0x1f, 0x75, 0x0f,
	0x53, 0x9d, 0xf0, 0x4f, 0x78, 0x42, 0xc9, 0x2e, 0x46, 0xc1, 0x2e, 0xe9, 0xa3, 0x55, 0xbd, 0xfa,
	0x68, 0xd5, 0xa8, 0x3c, 0x5a, 0x35, 0xe7, 0xad, 0x75, 0xd1, 0x19, 0x5a, 0x95, 0xa6, 0x3e, 0xe7,
	0x46, 0xed, 0x22, 0x40, 0xfa, 0xa4, 0x2d, 0x22, 0x16, 0x53, 0x29, 0x62, 0x8d, 0xc2, 0x8c, 0x2e,
	0xfa, 0x00, 0x29, 0xf9, 0xc0, 0x4d, 0xd2, 0x9d, 0x46, 0x7e, 0x56, 0x01, 0xa8, 0x18, 0x4d, 0x14,
	0x2b, 0xad, 0x95, 0xb1, 0xff, 0x5d, 0xc9, 0xf5, 0xbf, 0xf9, 0x10, 0xd5, 0x2b, 0x86, 0x28, 0xe7,
	0x27, 0x0b, 0x81, 0xa0, 0x80, 0x5f, 0x85, 0x5c, 0xaa, 0xa9, 0x5a, 0x4e, 0x53, 0x45, 0x2d, 0x98,
	0x15, 0x2d, 0x68, 0x4d, 0xd6, 0xe7, 0x9a, 0x3c, 0xd7, 0xcb, 0x73, 0x4a, 0x6b, 0x16, 0x31, 0x7b,
	0x48, 0xae, 0x60, 0x70, 0xc2, 0x87, 0x80, 0xb4, 0xd5, 0xad, 0xc0, 0xf5, 0x72, 0xaf, 0x3f, 0xce,
	0x5b, 0x3a, 0x2b, 0x95, 0x36, 0x4d, 0x22, 0xcb, 0x21, 0xbd, 0xfc, 0xda, 0x44, 0x87, 0x82, 0xee,
	0x7c, 0x71, 0xe2, 0xfc, 0xb9, 0xa6, 0x64, 0xba, 0x2f, 0x26, 0x93, 0xa9, 0xcb, 0x46, 0x22, 0xf6,
	0xef, 0xf9, 0x7e, 0x5a, 0x72, 0x01, 0xd3, 0x03, 0xcb, 0x64, 0x25, 0x17, 0x70, 0xf6, 0x4a, 0xb7,
	0xac, 0x55, 0x5c, 0xe3, 0xc9, 0x94, 0x25, 0x58, 0x1c, 0x6a, 0x47, 0x4d, 0x69, 0x48, 0xba, 0x23,
	0x2a, 0xd3, 0xd0, 0xd1, 0x73, 0x1b, 0x23, 0x0a, 0x85, 0xda, 0x2d, 0xd2, 0x3b, 0xa1, 0xa1, 0x1f,
	0xb0, 0xf4, 0x7d, 0xa3, 0xa1, 0xde, 0x37, 0x14, 0x53, 0xbd, 0x6f, 0xe4, 0x62, 0x72, 0xb3, 0x10,
	0x93, 0xaf, 0x92, 0x96, 0xea, 0x6d, 0x93, 0xb4, 0x70, 0xc7, 0xe6, 0x36, 0xb9, 0xdc, 0xf7, 0x1e,
	0xb3, 0x80, 0x91, 0xdc, 0xf3, 0x53, 0x01, 0x78, 0xdd, 0x12, 0xf0, 0x4a, 0x8f, 0x4f, 0x46, 0xf6,
	0xf8, 0x74, 0x8d, 0xb4, 0xe1, 0xa2, 0xd8, 0x30, 0xf4, 0x54, 0xdc, 0x19, 0x51, 0x89, 0x9f, 0x12,
	0x7e, 0xa5, 0x9b, 0xd6, 0x9c, 0xca, 0xab, 0xde, 0x6a, 0xa2, 0xb7, 0x16, 0x6d, 0x50, 0x2b, 0xdb,
	0xe0, 0xdf, 0xac, 0x69, 0xe7, 0x07, 0xb5, 0x8a, 0xe4, 0x0b, 0x13, 0x8e, 0x51, 0xfa, 0x80, 0x58,
	0xb8, 0x83, 0x59, 0xb9, 0x43, 0x16, 0x48, 0x74, 0xba, 0xc9, 0x02, 0x49, 0x0e, 0x78, 0xda, 0x3c,
	0xf3, 0x5b, 0x35, 0xce, 0xbd, 0x55, 0x73, 0x60, 0x56, 0x6e, 0x95, 0x37, 0x49, 0xab, 0x60, 0x92,
	0x2c, 0x31, 0xb5, 0x97, 0x25, 0xa6, 0x4e, 0xe9, 0xe3, 0xd4, 0x56, 0x5a, 0x9e, 0x93, 0x81, 0x09,
	0xb5, 0x23, 0x12, 0xce, 0xcf, 0x9a, 0xaa, 0x6b, 0xaf, 0xe8, 0xe7, 0x19, 0xe5, 0xaa, 0xb7, 0x0b,
	0xb9, 0xea, 0x95, 0x34, 0x57, 0x2d, 0x38, 0xbe, 0xcc, 0xd7, 0x19, 0xea, 0xcb, 0xa4, 0xf3, 0x01,
	0x7b, 0xea, 0xa9, 0xb6, 0xa1, 0x58, 0x10, 0xf7, 0x4a, 0x05, 0x31, 0xba, 0xff, 0xbc, 0x20, 0x06,
	0xef, 0xef, 0xbf, 0x45, 0x56, 0xd2, 0xd6, 0x7f, 0x5f, 0xb2, 0x09, 0xa8, 0x31, 0x16, 0x41, 0x1a,
	0x2c, 0xf0, 0x77, 0xbe, 0xf3, 0xad, 0x15, 0x3a, 0xdf, 0xfe, 0xdf, 0x4d, 0xf5, 0xba, 0x94, 0x13,
	0xeb, 0xb2, 0x00, 0x58, 0x5a, 0x1e, 0x2d, 0x70, 0x1d, 0x8d, 0xec, 0xc6, 0x12, 0x64, 0x37, 0x97,
	0x23, 0xbb, 0x55, 0x40, 0x76, 0x1e, 0x74, 0xed, 0x12, 0xe8, 0x2a, 0x7e, 0xd8, 0x39, 0x17, 0x5d,
	0xa4, 0x10, 0xc7, 0xf6, 0x48, 0xdb, 0xd7, 0xda, 0xb4, 0xbb, 0x68, 0xd1, 0x9d, 0xf3, 0x2c, 0x9a,
	0xd7, 0xbc, 0x9b, 0xad, 0x2c, 0xe0, 0x69, 0xa5, 0x94, 0x98, 0x8b, 0x89, 0xae, 0x57, 0x49, 0x74,
	0x7d, 0x44, 0x08, 0x1b, 0x8b, 0x78, 0xa6, 0xbf, 0x62, 0x65, 0x74, 0x0e, 0x79, 0xeb, 0x78, 0x27,
	0x8d, 0xbc, 0x37, 0x53, 0x20, 0x6c, 0xa0, 0xc4, 0x2f, 0x9e, 0x27, 0x71, 0xe6, 0x6a, 0x29, 0x5e,
	0x0e, 0x54, 0x33, 0x8c, 0x9f, 0x32, 0x51, 0x07, 0x3a, 0xf5, 0x5c, 0xdc, 0x61, 0xa7, 0xaf, 0xee,
	0xb5, 0xf9, 0xab, 0xbb, 0x23, 0xd4, 0xa3, 0xf6, 0x11, 0x1d, 0xea, 0x6d, 0xd0, 0x73, 0x87, 0xca,
	0xbc, 0x46, 0xea, 0xb9, 0x43, 0xb4, 0xee, 0x36, 0x69, 0x46, 0xd3, 0x61, 0xc0, 0x55, 0x8f, 0xd7,
	0x70, 0x35, 0x65, 0xbd, 0x42, 0x9a, 0xfa, 0x7b, 0x94, 0x89, 0xdf, 0xa3, 0x36, 0xab, 0xdf, 0x5f,
	0x9f, 0xb8, 0x7a, 0x8a, 0x73, 0xa8, 0x3e, 0x3a, 0x1d, 0xd1, 0xe1, 0xb2, 0xf8, 0x9d, 0x97, 0xa0,
	0xb6, 0x4c, 0x02, 0x33, 0x2f, 0x81, 0x73, 0x33, 0xbb, 0x45, 0xa5, 0x69, 0xc0, 0x3d, 0x9d, 0x4f,
	0x6b, 0x0a, 0x36, 0x47, 0x74, 0x98, 0x05, 0x97, 0xbb, 0x3a, 0x10, 0x18, 0x68, 0x84, 0x17, 0x52,
	0xa1, 0x73, 0x53, 0x76, 0xb3, 0x4d, 0xf1, 0xa5, 0x40, 0xc1, 0xff, 0xeb, 0xb9, 0xb3, 0xb0, 0x27,
	0xfe, 0x1c, 0x74, 0xa7, 0x43, 0xbd, 0xcd, 0xf5, 0x45, 0xdb, 0x1c, 0xd1, 0x21, 0x56, 0xb8, 0x30,
	0x6f, 0x79, 0x0d, 0xd0, 0xff, 0x9e, 0x41, 0x5a, 0x7a, 0xea, 0x65, 0x94, 0x72, 0x19, 0xf5, 0xe7,
	0x34, 0x58, 0x2f, 0xd8, 0x30, 0x8f, 0x80, 0x46, 0x11, 0x01, 0xce, 0x5f, 0x0c, 0xf5, 0x19, 0xff,
	0x88, 0x0e, 0xf1, 0x83, 0x06, 0x6a, 0xef, 0xf5, 0xec, 0xd4, 0xaa, 0xfe, 0xb2, 0x59, 0x45, 0x86,
	0x5e, 0xd0, 0xf7, 0x54, 0x43, 0x94, 0xf2, 0xad, 0xbb, 0x79, 0x05, 0xde, 0x5a, 0xb8, 0x4f, 0x4a,
	0x68, 0x4b, 0x5c, 0xa0, 0xc8, 0x7b, 0x64, 0xb5, 0xb8, 0xe0, 0x3c, 0xf5, 0x6d, 0x91, 0x86, 0xfa,
	0x76, 0xa3, 0x8a, 0x7e, 0x45, 0x38, 0xaf, 0x2b, 0x94, 0x1d, 0xd1, 0xe1, 0x63, 0xac, 0xb2, 0xf1,
	0x9f, 0x35, 0xd8, 0x13, 0x7c, 0xe7, 0x13, 0xb1, 0xf4, 0x12, 0x26, 0x25, 0x0f, 0xc7, 0x1a, 0x22,
	0x5d, 0xe0, 0x1d, 0x2a, 0xd6, 0x9d, 0xef, 0x98, 0xa4, 0x05, 0x6b, 0xef, 0x45, 0xdc, 0xfa, 0x0a,
	0xe9, 0xea, 0x6d, 0x0e, 0xe9, 0x19, 0xb3, 0x36, 0x72, 0x97, 0x53, 0x78, 0xeb, 0x77, 0x80, 0xf5,
	0x60, 0x12, 0xc9, 0x99, 0x73, 0xe3, 0x5b, 0xbf, 0xfd, 0xd3, 0x77, 0x6b, 0x57, 0x1d, 0x0b, 0xff,
	0x07, 0x09, 0x80, 0x7b, 0x5b, 0xd2, 0xe1, 0xed, 0x84, 0x9e, 0xb1, 0x37, 0x8c, 0x97, 0xad, 0x77,
	0x09, 0x99, 0x3b, 0x79, 0x61, 0x2b, 0xe5, 0xf4, 0xf9, 0xad, 0x9e, 0xc3, 0xad, 0xb6, 0x9d, 0x8d,
	0xe2, 0x56, 0x3e, 0x0b, 0x60, 0xa7, 0x83, 0x4c, 0x2a, 0x70, 0x0f, 0x6b, 0xbe, 0xae, 0xbf, 0xb9,
	0xc0, 0x7d, 0x97, 0xc9, 0x15, 0xf0, 0x44, 0xc2, 0x6e, 0x1f, 0x96, 0xcc, 0x99, 0xdb, 0x6e, 0x6b,
	0x91, 0x31, 0x9d, 0xe7, 0x71, 0x3f, 0xdb, 0xd9, 0x2c, 0xee, 0x87, 0x8a, 0x87, 0x0d, 0xdf, 0xcb,
	0xc4, 0x83, 0x10, 0x61, 0x59, 0xb9, 0x4d, 0x74, 0xcc, 0xf8, 0x0c, 0x5a, 0x63, 0x3e, 0x87, 0xcd,
	0xde, 0x69, 0x7e, 0xa3, 0xbe, 0xfb, 0x66, 0x34, 0x1c, 0x36, 0xf1, 0xff, 0xb8, 0xbe, 0xf8, 0x8f,
	0x00, 0x00, 0x00, 0xff, 0xff, 0x36, 0xad, 0x78, 0x34, 0x11, 0x26, 0x00, 0x00,
}
