// Code generated by protoc-gen-go. DO NOT EDIT.
// source: conversation.proto

package pb

import (
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

type CloseChatReq struct {
	ConversationId       string   `protobuf:"bytes,1,opt,name=conversation_id,json=conversationId,proto3" json:"conversation_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *CloseChatReq) Reset()         { *m = CloseChatReq{} }
func (m *CloseChatReq) String() string { return proto.CompactTextString(m) }
func (*CloseChatReq) ProtoMessage()    {}
func (*CloseChatReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_d6ae46d001825652, []int{0}
}

func (m *CloseChatReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_CloseChatReq.Unmarshal(m, b)
}
func (m *CloseChatReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_CloseChatReq.Marshal(b, m, deterministic)
}
func (m *CloseChatReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_CloseChatReq.Merge(m, src)
}
func (m *CloseChatReq) XXX_Size() int {
	return xxx_messageInfo_CloseChatReq.Size(m)
}
func (m *CloseChatReq) XXX_DiscardUnknown() {
	xxx_messageInfo_CloseChatReq.DiscardUnknown(m)
}

var xxx_messageInfo_CloseChatReq proto.InternalMessageInfo

func (m *CloseChatReq) GetConversationId() string {
	if m != nil {
		return m.ConversationId
	}
	return ""
}

type ContinueChatResponse struct {
	HasActiveConversation bool     `protobuf:"varint,1,opt,name=has_active_conversation,json=hasActiveConversation,proto3" json:"has_active_conversation"`
	CatId                 int32    `protobuf:"varint,2,opt,name=cat_id,json=catId,proto3" json:"cat_id"`
	ConversationId        string   `protobuf:"bytes,3,opt,name=conversation_id,json=conversationId,proto3" json:"conversation_id"`
	XXX_NoUnkeyedLiteral  struct{} `json:"-" gorm:"-"`
	XXX_unrecognized      []byte   `json:"-" gorm:"-"`
	XXX_sizecache         int32    `json:"-" gorm:"-"`
}

func (m *ContinueChatResponse) Reset()         { *m = ContinueChatResponse{} }
func (m *ContinueChatResponse) String() string { return proto.CompactTextString(m) }
func (*ContinueChatResponse) ProtoMessage()    {}
func (*ContinueChatResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_d6ae46d001825652, []int{1}
}

func (m *ContinueChatResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ContinueChatResponse.Unmarshal(m, b)
}
func (m *ContinueChatResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ContinueChatResponse.Marshal(b, m, deterministic)
}
func (m *ContinueChatResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ContinueChatResponse.Merge(m, src)
}
func (m *ContinueChatResponse) XXX_Size() int {
	return xxx_messageInfo_ContinueChatResponse.Size(m)
}
func (m *ContinueChatResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ContinueChatResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ContinueChatResponse proto.InternalMessageInfo

func (m *ContinueChatResponse) GetHasActiveConversation() bool {
	if m != nil {
		return m.HasActiveConversation
	}
	return false
}

func (m *ContinueChatResponse) GetCatId() int32 {
	if m != nil {
		return m.CatId
	}
	return 0
}

func (m *ContinueChatResponse) GetConversationId() string {
	if m != nil {
		return m.ConversationId
	}
	return ""
}

type UserChatReq struct {
	UserId               string   `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id"`
	GameId               string   `protobuf:"bytes,2,opt,name=game_id,json=gameId,proto3" json:"game_id"`
	FpxAppId             string   `protobuf:"bytes,3,opt,name=fpx_app_id,json=fpxAppId,proto3" json:"fpx_app_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *UserChatReq) Reset()         { *m = UserChatReq{} }
func (m *UserChatReq) String() string { return proto.CompactTextString(m) }
func (*UserChatReq) ProtoMessage()    {}
func (*UserChatReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_d6ae46d001825652, []int{2}
}

func (m *UserChatReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UserChatReq.Unmarshal(m, b)
}
func (m *UserChatReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UserChatReq.Marshal(b, m, deterministic)
}
func (m *UserChatReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UserChatReq.Merge(m, src)
}
func (m *UserChatReq) XXX_Size() int {
	return xxx_messageInfo_UserChatReq.Size(m)
}
func (m *UserChatReq) XXX_DiscardUnknown() {
	xxx_messageInfo_UserChatReq.DiscardUnknown(m)
}

var xxx_messageInfo_UserChatReq proto.InternalMessageInfo

func (m *UserChatReq) GetUserId() string {
	if m != nil {
		return m.UserId
	}
	return ""
}

func (m *UserChatReq) GetGameId() string {
	if m != nil {
		return m.GameId
	}
	return ""
}

func (m *UserChatReq) GetFpxAppId() string {
	if m != nil {
		return m.FpxAppId
	}
	return ""
}

// 更新对话请求
type UpdateConversationRequest struct {
	ConversationId       string         `protobuf:"bytes,1,opt,name=conversation_id,json=conversationId,proto3" json:"conversation_id"`
	UserId               string         `protobuf:"bytes,2,opt,name=user_id,json=userId,proto3" json:"user_id"`
	CatId                int32          `protobuf:"varint,3,opt,name=cat_id,json=catId,proto3" json:"cat_id"`
	TicketId             int64          `protobuf:"varint,4,opt,name=ticket_id,json=ticketId,proto3" json:"ticket_id"`
	History              []*History     `protobuf:"bytes,5,rep,name=history,proto3" json:"history"`
	QuestionGetList      []*QuestionGet `protobuf:"bytes,6,rep,name=question_get_list,json=questionGetList,proto3" json:"question_get_list"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-" gorm:"-"`
	XXX_unrecognized     []byte         `json:"-" gorm:"-"`
	XXX_sizecache        int32          `json:"-" gorm:"-"`
}

func (m *UpdateConversationRequest) Reset()         { *m = UpdateConversationRequest{} }
func (m *UpdateConversationRequest) String() string { return proto.CompactTextString(m) }
func (*UpdateConversationRequest) ProtoMessage()    {}
func (*UpdateConversationRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_d6ae46d001825652, []int{3}
}

func (m *UpdateConversationRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateConversationRequest.Unmarshal(m, b)
}
func (m *UpdateConversationRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateConversationRequest.Marshal(b, m, deterministic)
}
func (m *UpdateConversationRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateConversationRequest.Merge(m, src)
}
func (m *UpdateConversationRequest) XXX_Size() int {
	return xxx_messageInfo_UpdateConversationRequest.Size(m)
}
func (m *UpdateConversationRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateConversationRequest.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateConversationRequest proto.InternalMessageInfo

func (m *UpdateConversationRequest) GetConversationId() string {
	if m != nil {
		return m.ConversationId
	}
	return ""
}

func (m *UpdateConversationRequest) GetUserId() string {
	if m != nil {
		return m.UserId
	}
	return ""
}

func (m *UpdateConversationRequest) GetCatId() int32 {
	if m != nil {
		return m.CatId
	}
	return 0
}

func (m *UpdateConversationRequest) GetTicketId() int64 {
	if m != nil {
		return m.TicketId
	}
	return 0
}

func (m *UpdateConversationRequest) GetHistory() []*History {
	if m != nil {
		return m.History
	}
	return nil
}

func (m *UpdateConversationRequest) GetQuestionGetList() []*QuestionGet {
	if m != nil {
		return m.QuestionGetList
	}
	return nil
}

// 更新对话响应
type UpdateConversationResponse struct {
	Uuid                 string   `protobuf:"bytes,1,opt,name=uuid,proto3" json:"uuid"`
	ConversationId       string   `protobuf:"bytes,2,opt,name=conversation_id,json=conversationId,proto3" json:"conversation_id"`
	Timestamp            int64    `protobuf:"varint,3,opt,name=timestamp,proto3" json:"timestamp"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *UpdateConversationResponse) Reset()         { *m = UpdateConversationResponse{} }
func (m *UpdateConversationResponse) String() string { return proto.CompactTextString(m) }
func (*UpdateConversationResponse) ProtoMessage()    {}
func (*UpdateConversationResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_d6ae46d001825652, []int{4}
}

func (m *UpdateConversationResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_UpdateConversationResponse.Unmarshal(m, b)
}
func (m *UpdateConversationResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_UpdateConversationResponse.Marshal(b, m, deterministic)
}
func (m *UpdateConversationResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_UpdateConversationResponse.Merge(m, src)
}
func (m *UpdateConversationResponse) XXX_Size() int {
	return xxx_messageInfo_UpdateConversationResponse.Size(m)
}
func (m *UpdateConversationResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_UpdateConversationResponse.DiscardUnknown(m)
}

var xxx_messageInfo_UpdateConversationResponse proto.InternalMessageInfo

func (m *UpdateConversationResponse) GetUuid() string {
	if m != nil {
		return m.Uuid
	}
	return ""
}

func (m *UpdateConversationResponse) GetConversationId() string {
	if m != nil {
		return m.ConversationId
	}
	return ""
}

func (m *UpdateConversationResponse) GetTimestamp() int64 {
	if m != nil {
		return m.Timestamp
	}
	return 0
}

// 获取历史请求
type GetHistoryRequest struct {
	// 项目标识 @gotags: validate:"required_without=FpxAppId"
	GameId uint32 `protobuf:"varint,1,opt,name=game_id,json=gameId,proto3" json:"game_id" validate:"required_without=FpxAppId"`
	// 项目标识 @gotags: validate:"required_without=GameId"
	FpxAppId string `protobuf:"bytes,2,opt,name=fpx_app_id,json=fpxAppId,proto3" json:"fpx_app_id" validate:"required_without=GameId"`
	// 语言标识 @gotags: validate:"required"
	Lang                 string   `protobuf:"bytes,3,opt,name=lang,proto3" json:"lang" validate:"required"`
	ConversationId       string   `protobuf:"bytes,4,opt,name=conversation_id,json=conversationId,proto3" json:"conversation_id"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *GetHistoryRequest) Reset()         { *m = GetHistoryRequest{} }
func (m *GetHistoryRequest) String() string { return proto.CompactTextString(m) }
func (*GetHistoryRequest) ProtoMessage()    {}
func (*GetHistoryRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_d6ae46d001825652, []int{5}
}

func (m *GetHistoryRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetHistoryRequest.Unmarshal(m, b)
}
func (m *GetHistoryRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetHistoryRequest.Marshal(b, m, deterministic)
}
func (m *GetHistoryRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetHistoryRequest.Merge(m, src)
}
func (m *GetHistoryRequest) XXX_Size() int {
	return xxx_messageInfo_GetHistoryRequest.Size(m)
}
func (m *GetHistoryRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_GetHistoryRequest.DiscardUnknown(m)
}

var xxx_messageInfo_GetHistoryRequest proto.InternalMessageInfo

func (m *GetHistoryRequest) GetGameId() uint32 {
	if m != nil {
		return m.GameId
	}
	return 0
}

func (m *GetHistoryRequest) GetFpxAppId() string {
	if m != nil {
		return m.FpxAppId
	}
	return ""
}

func (m *GetHistoryRequest) GetLang() string {
	if m != nil {
		return m.Lang
	}
	return ""
}

func (m *GetHistoryRequest) GetConversationId() string {
	if m != nil {
		return m.ConversationId
	}
	return ""
}

// 获取历史响应
type GetHistoryResponse struct {
	History         []*HistoryItem `protobuf:"bytes,1,rep,name=history,proto3" json:"history"`
	QuestionHitList []*QuestionHit `protobuf:"bytes,2,rep,name=question_hit_list,json=questionHitList,proto3" json:"question_hit_list"`
	QuestionGetList []*QuestionGet `protobuf:"bytes,3,rep,name=question_get_list,json=questionGetList,proto3" json:"question_get_list"`
	// ticket_id
	TicketId             int64    `protobuf:"varint,4,opt,name=ticket_id,json=ticketId,proto3" json:"ticket_id"`
	ConversationStatus   int32    `protobuf:"varint,5,opt,name=conversation_status,json=conversationStatus,proto3" json:"conversation_status"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *GetHistoryResponse) Reset()         { *m = GetHistoryResponse{} }
func (m *GetHistoryResponse) String() string { return proto.CompactTextString(m) }
func (*GetHistoryResponse) ProtoMessage()    {}
func (*GetHistoryResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_d6ae46d001825652, []int{6}
}

func (m *GetHistoryResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_GetHistoryResponse.Unmarshal(m, b)
}
func (m *GetHistoryResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_GetHistoryResponse.Marshal(b, m, deterministic)
}
func (m *GetHistoryResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_GetHistoryResponse.Merge(m, src)
}
func (m *GetHistoryResponse) XXX_Size() int {
	return xxx_messageInfo_GetHistoryResponse.Size(m)
}
func (m *GetHistoryResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_GetHistoryResponse.DiscardUnknown(m)
}

var xxx_messageInfo_GetHistoryResponse proto.InternalMessageInfo

func (m *GetHistoryResponse) GetHistory() []*HistoryItem {
	if m != nil {
		return m.History
	}
	return nil
}

func (m *GetHistoryResponse) GetQuestionHitList() []*QuestionHit {
	if m != nil {
		return m.QuestionHitList
	}
	return nil
}

func (m *GetHistoryResponse) GetQuestionGetList() []*QuestionGet {
	if m != nil {
		return m.QuestionGetList
	}
	return nil
}

func (m *GetHistoryResponse) GetTicketId() int64 {
	if m != nil {
		return m.TicketId
	}
	return 0
}

func (m *GetHistoryResponse) GetConversationStatus() int32 {
	if m != nil {
		return m.ConversationStatus
	}
	return 0
}

// 对话请求
type ChatRequest struct {
	ConversationId       string             `protobuf:"bytes,1,opt,name=conversation_id,json=conversationId,proto3" json:"conversation_id"`
	NowQuestionKey       string             `protobuf:"bytes,2,opt,name=now_question_key,json=nowQuestionKey,proto3" json:"now_question_key"`
	NowQuestionContent   string             `protobuf:"bytes,3,opt,name=now_question_content,json=nowQuestionContent,proto3" json:"now_question_content"`
	NowAnswerContent     []*Content         `protobuf:"bytes,4,rep,name=now_answer_content,json=nowAnswerContent,proto3" json:"now_answer_content"`
	QuestionList         []*QuestionContent `protobuf:"bytes,5,rep,name=question_list,json=questionList,proto3" json:"question_list"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-" gorm:"-"`
	XXX_unrecognized     []byte             `json:"-" gorm:"-"`
	XXX_sizecache        int32              `json:"-" gorm:"-"`
}

func (m *ChatRequest) Reset()         { *m = ChatRequest{} }
func (m *ChatRequest) String() string { return proto.CompactTextString(m) }
func (*ChatRequest) ProtoMessage()    {}
func (*ChatRequest) Descriptor() ([]byte, []int) {
	return fileDescriptor_d6ae46d001825652, []int{7}
}

func (m *ChatRequest) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChatRequest.Unmarshal(m, b)
}
func (m *ChatRequest) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChatRequest.Marshal(b, m, deterministic)
}
func (m *ChatRequest) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChatRequest.Merge(m, src)
}
func (m *ChatRequest) XXX_Size() int {
	return xxx_messageInfo_ChatRequest.Size(m)
}
func (m *ChatRequest) XXX_DiscardUnknown() {
	xxx_messageInfo_ChatRequest.DiscardUnknown(m)
}

var xxx_messageInfo_ChatRequest proto.InternalMessageInfo

func (m *ChatRequest) GetConversationId() string {
	if m != nil {
		return m.ConversationId
	}
	return ""
}

func (m *ChatRequest) GetNowQuestionKey() string {
	if m != nil {
		return m.NowQuestionKey
	}
	return ""
}

func (m *ChatRequest) GetNowQuestionContent() string {
	if m != nil {
		return m.NowQuestionContent
	}
	return ""
}

func (m *ChatRequest) GetNowAnswerContent() []*Content {
	if m != nil {
		return m.NowAnswerContent
	}
	return nil
}

func (m *ChatRequest) GetQuestionList() []*QuestionContent {
	if m != nil {
		return m.QuestionList
	}
	return nil
}

// 问题内容
type QuestionContent struct {
	QuestionKey          string   `protobuf:"bytes,1,opt,name=question_key,json=questionKey,proto3" json:"question_key"`
	QuestionContent      string   `protobuf:"bytes,2,opt,name=question_content,json=questionContent,proto3" json:"question_content"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *QuestionContent) Reset()         { *m = QuestionContent{} }
func (m *QuestionContent) String() string { return proto.CompactTextString(m) }
func (*QuestionContent) ProtoMessage()    {}
func (*QuestionContent) Descriptor() ([]byte, []int) {
	return fileDescriptor_d6ae46d001825652, []int{8}
}

func (m *QuestionContent) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QuestionContent.Unmarshal(m, b)
}
func (m *QuestionContent) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QuestionContent.Marshal(b, m, deterministic)
}
func (m *QuestionContent) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QuestionContent.Merge(m, src)
}
func (m *QuestionContent) XXX_Size() int {
	return xxx_messageInfo_QuestionContent.Size(m)
}
func (m *QuestionContent) XXX_DiscardUnknown() {
	xxx_messageInfo_QuestionContent.DiscardUnknown(m)
}

var xxx_messageInfo_QuestionContent proto.InternalMessageInfo

func (m *QuestionContent) GetQuestionKey() string {
	if m != nil {
		return m.QuestionKey
	}
	return ""
}

func (m *QuestionContent) GetQuestionContent() string {
	if m != nil {
		return m.QuestionContent
	}
	return ""
}

// 对话响应
type ChatResponse struct {
	HistoryAnswerList    []*QuestionGet `protobuf:"bytes,1,rep,name=history_answer_list,json=historyAnswerList,proto3" json:"history_answer_list"`
	NowQuestionKey       string         `protobuf:"bytes,2,opt,name=now_question_key,json=nowQuestionKey,proto3" json:"now_question_key"`
	NowAnswerContent     string         `protobuf:"bytes,3,opt,name=now_answer_content,json=nowAnswerContent,proto3" json:"now_answer_content"`
	HitQuestion          bool           `protobuf:"varint,4,opt,name=hit_question,json=hitQuestion,proto3" json:"hit_question"`
	Completed            bool           `protobuf:"varint,5,opt,name=completed,proto3" json:"completed"`
	XXX_NoUnkeyedLiteral struct{}       `json:"-" gorm:"-"`
	XXX_unrecognized     []byte         `json:"-" gorm:"-"`
	XXX_sizecache        int32          `json:"-" gorm:"-"`
}

func (m *ChatResponse) Reset()         { *m = ChatResponse{} }
func (m *ChatResponse) String() string { return proto.CompactTextString(m) }
func (*ChatResponse) ProtoMessage()    {}
func (*ChatResponse) Descriptor() ([]byte, []int) {
	return fileDescriptor_d6ae46d001825652, []int{9}
}

func (m *ChatResponse) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ChatResponse.Unmarshal(m, b)
}
func (m *ChatResponse) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ChatResponse.Marshal(b, m, deterministic)
}
func (m *ChatResponse) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChatResponse.Merge(m, src)
}
func (m *ChatResponse) XXX_Size() int {
	return xxx_messageInfo_ChatResponse.Size(m)
}
func (m *ChatResponse) XXX_DiscardUnknown() {
	xxx_messageInfo_ChatResponse.DiscardUnknown(m)
}

var xxx_messageInfo_ChatResponse proto.InternalMessageInfo

func (m *ChatResponse) GetHistoryAnswerList() []*QuestionGet {
	if m != nil {
		return m.HistoryAnswerList
	}
	return nil
}

func (m *ChatResponse) GetNowQuestionKey() string {
	if m != nil {
		return m.NowQuestionKey
	}
	return ""
}

func (m *ChatResponse) GetNowAnswerContent() string {
	if m != nil {
		return m.NowAnswerContent
	}
	return ""
}

func (m *ChatResponse) GetHitQuestion() bool {
	if m != nil {
		return m.HitQuestion
	}
	return false
}

func (m *ChatResponse) GetCompleted() bool {
	if m != nil {
		return m.Completed
	}
	return false
}

// 历史记录
type History struct {
	Role                 string   `protobuf:"bytes,1,opt,name=role,proto3" json:"role"`
	QuestionKey          string   `protobuf:"bytes,2,opt,name=question_key,json=questionKey,proto3" json:"question_key"`
	Content              string   `protobuf:"bytes,3,opt,name=content,proto3" json:"content"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *History) Reset()         { *m = History{} }
func (m *History) String() string { return proto.CompactTextString(m) }
func (*History) ProtoMessage()    {}
func (*History) Descriptor() ([]byte, []int) {
	return fileDescriptor_d6ae46d001825652, []int{10}
}

func (m *History) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_History.Unmarshal(m, b)
}
func (m *History) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_History.Marshal(b, m, deterministic)
}
func (m *History) XXX_Merge(src proto.Message) {
	xxx_messageInfo_History.Merge(m, src)
}
func (m *History) XXX_Size() int {
	return xxx_messageInfo_History.Size(m)
}
func (m *History) XXX_DiscardUnknown() {
	xxx_messageInfo_History.DiscardUnknown(m)
}

var xxx_messageInfo_History proto.InternalMessageInfo

func (m *History) GetRole() string {
	if m != nil {
		return m.Role
	}
	return ""
}

func (m *History) GetQuestionKey() string {
	if m != nil {
		return m.QuestionKey
	}
	return ""
}

func (m *History) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

// 历史记录项
type HistoryItem struct {
	Role                 string   `protobuf:"bytes,1,opt,name=role,proto3" json:"role"`
	Content              string   `protobuf:"bytes,2,opt,name=content,proto3" json:"content"`
	QuestionKey          string   `protobuf:"bytes,3,opt,name=question_key,json=questionKey,proto3" json:"question_key"`
	Timestamp            int64    `protobuf:"varint,4,opt,name=timestamp,proto3" json:"timestamp"`
	Uuid                 string   `protobuf:"bytes,5,opt,name=uuid,proto3" json:"uuid"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *HistoryItem) Reset()         { *m = HistoryItem{} }
func (m *HistoryItem) String() string { return proto.CompactTextString(m) }
func (*HistoryItem) ProtoMessage()    {}
func (*HistoryItem) Descriptor() ([]byte, []int) {
	return fileDescriptor_d6ae46d001825652, []int{11}
}

func (m *HistoryItem) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_HistoryItem.Unmarshal(m, b)
}
func (m *HistoryItem) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_HistoryItem.Marshal(b, m, deterministic)
}
func (m *HistoryItem) XXX_Merge(src proto.Message) {
	xxx_messageInfo_HistoryItem.Merge(m, src)
}
func (m *HistoryItem) XXX_Size() int {
	return xxx_messageInfo_HistoryItem.Size(m)
}
func (m *HistoryItem) XXX_DiscardUnknown() {
	xxx_messageInfo_HistoryItem.DiscardUnknown(m)
}

var xxx_messageInfo_HistoryItem proto.InternalMessageInfo

func (m *HistoryItem) GetRole() string {
	if m != nil {
		return m.Role
	}
	return ""
}

func (m *HistoryItem) GetContent() string {
	if m != nil {
		return m.Content
	}
	return ""
}

func (m *HistoryItem) GetQuestionKey() string {
	if m != nil {
		return m.QuestionKey
	}
	return ""
}

func (m *HistoryItem) GetTimestamp() int64 {
	if m != nil {
		return m.Timestamp
	}
	return 0
}

func (m *HistoryItem) GetUuid() string {
	if m != nil {
		return m.Uuid
	}
	return ""
}

// 问题命中
type QuestionHit struct {
	QuestionKey          string   `protobuf:"bytes,1,opt,name=question_key,json=questionKey,proto3" json:"question_key"`
	HasAnswer            bool     `protobuf:"varint,2,opt,name=has_answer,json=hasAnswer,proto3" json:"has_answer"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *QuestionHit) Reset()         { *m = QuestionHit{} }
func (m *QuestionHit) String() string { return proto.CompactTextString(m) }
func (*QuestionHit) ProtoMessage()    {}
func (*QuestionHit) Descriptor() ([]byte, []int) {
	return fileDescriptor_d6ae46d001825652, []int{12}
}

func (m *QuestionHit) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QuestionHit.Unmarshal(m, b)
}
func (m *QuestionHit) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QuestionHit.Marshal(b, m, deterministic)
}
func (m *QuestionHit) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QuestionHit.Merge(m, src)
}
func (m *QuestionHit) XXX_Size() int {
	return xxx_messageInfo_QuestionHit.Size(m)
}
func (m *QuestionHit) XXX_DiscardUnknown() {
	xxx_messageInfo_QuestionHit.DiscardUnknown(m)
}

var xxx_messageInfo_QuestionHit proto.InternalMessageInfo

func (m *QuestionHit) GetQuestionKey() string {
	if m != nil {
		return m.QuestionKey
	}
	return ""
}

func (m *QuestionHit) GetHasAnswer() bool {
	if m != nil {
		return m.HasAnswer
	}
	return false
}

// 问题获取
type QuestionGet struct {
	QuestionKey          string   `protobuf:"bytes,1,opt,name=question_key,json=questionKey,proto3" json:"question_key"`
	Answer               string   `protobuf:"bytes,2,opt,name=answer,proto3" json:"answer"`
	HasAnswer            bool     `protobuf:"varint,3,opt,name=has_answer,json=hasAnswer,proto3" json:"has_answer"`
	QuestionAskCount     int32    `protobuf:"varint,4,opt,name=question_ask_count,json=questionAskCount,proto3" json:"question_ask_count"`
	QuestionContent      string   `protobuf:"bytes,5,opt,name=question_content,json=questionContent,proto3" json:"question_content"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *QuestionGet) Reset()         { *m = QuestionGet{} }
func (m *QuestionGet) String() string { return proto.CompactTextString(m) }
func (*QuestionGet) ProtoMessage()    {}
func (*QuestionGet) Descriptor() ([]byte, []int) {
	return fileDescriptor_d6ae46d001825652, []int{13}
}

func (m *QuestionGet) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_QuestionGet.Unmarshal(m, b)
}
func (m *QuestionGet) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_QuestionGet.Marshal(b, m, deterministic)
}
func (m *QuestionGet) XXX_Merge(src proto.Message) {
	xxx_messageInfo_QuestionGet.Merge(m, src)
}
func (m *QuestionGet) XXX_Size() int {
	return xxx_messageInfo_QuestionGet.Size(m)
}
func (m *QuestionGet) XXX_DiscardUnknown() {
	xxx_messageInfo_QuestionGet.DiscardUnknown(m)
}

var xxx_messageInfo_QuestionGet proto.InternalMessageInfo

func (m *QuestionGet) GetQuestionKey() string {
	if m != nil {
		return m.QuestionKey
	}
	return ""
}

func (m *QuestionGet) GetAnswer() string {
	if m != nil {
		return m.Answer
	}
	return ""
}

func (m *QuestionGet) GetHasAnswer() bool {
	if m != nil {
		return m.HasAnswer
	}
	return false
}

func (m *QuestionGet) GetQuestionAskCount() int32 {
	if m != nil {
		return m.QuestionAskCount
	}
	return 0
}

func (m *QuestionGet) GetQuestionContent() string {
	if m != nil {
		return m.QuestionContent
	}
	return ""
}

// 内容
type Content struct {
	Text                 string   `protobuf:"bytes,1,opt,name=text,proto3" json:"text"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *Content) Reset()         { *m = Content{} }
func (m *Content) String() string { return proto.CompactTextString(m) }
func (*Content) ProtoMessage()    {}
func (*Content) Descriptor() ([]byte, []int) {
	return fileDescriptor_d6ae46d001825652, []int{14}
}

func (m *Content) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_Content.Unmarshal(m, b)
}
func (m *Content) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_Content.Marshal(b, m, deterministic)
}
func (m *Content) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Content.Merge(m, src)
}
func (m *Content) XXX_Size() int {
	return xxx_messageInfo_Content.Size(m)
}
func (m *Content) XXX_DiscardUnknown() {
	xxx_messageInfo_Content.DiscardUnknown(m)
}

var xxx_messageInfo_Content proto.InternalMessageInfo

func (m *Content) GetText() string {
	if m != nil {
		return m.Text
	}
	return ""
}

func init() {
	proto.RegisterType((*CloseChatReq)(nil), "pb.CloseChatReq")
	proto.RegisterType((*ContinueChatResponse)(nil), "pb.ContinueChatResponse")
	proto.RegisterType((*UserChatReq)(nil), "pb.UserChatReq")
	proto.RegisterType((*UpdateConversationRequest)(nil), "pb.UpdateConversationRequest")
	proto.RegisterType((*UpdateConversationResponse)(nil), "pb.UpdateConversationResponse")
	proto.RegisterType((*GetHistoryRequest)(nil), "pb.GetHistoryRequest")
	proto.RegisterType((*GetHistoryResponse)(nil), "pb.GetHistoryResponse")
	proto.RegisterType((*ChatRequest)(nil), "pb.ChatRequest")
	proto.RegisterType((*QuestionContent)(nil), "pb.QuestionContent")
	proto.RegisterType((*ChatResponse)(nil), "pb.ChatResponse")
	proto.RegisterType((*History)(nil), "pb.History")
	proto.RegisterType((*HistoryItem)(nil), "pb.HistoryItem")
	proto.RegisterType((*QuestionHit)(nil), "pb.QuestionHit")
	proto.RegisterType((*QuestionGet)(nil), "pb.QuestionGet")
	proto.RegisterType((*Content)(nil), "pb.Content")
}

func init() {
	proto.RegisterFile("conversation.proto", fileDescriptor_d6ae46d001825652)
}

var fileDescriptor_d6ae46d001825652 = []byte{
	// 1017 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x94, 0x56, 0xcd, 0x6e, 0x1b, 0x37,
	0x10, 0xc6, 0xae, 0xfe, 0x47, 0x4a, 0x1d, 0xd3, 0xb1, 0xa3, 0x28, 0x76, 0xaa, 0x10, 0x4d, 0xab,
	0x04, 0x85, 0x55, 0xa4, 0x40, 0x7f, 0x92, 0x43, 0xe1, 0x0a, 0x85, 0x2d, 0xb4, 0x40, 0xd0, 0x0d,
	0x7c, 0x09, 0x10, 0x08, 0xf4, 0x2e, 0x23, 0x2d, 0x2c, 0x2d, 0xd7, 0x22, 0xe5, 0x1f, 0xf4, 0xd6,
	0x4b, 0x7a, 0x2a, 0x0a, 0xf4, 0x85, 0xfa, 0x0e, 0x7d, 0x85, 0x1e, 0xfa, 0x02, 0xbd, 0x17, 0xe4,
	0x92, 0x5a, 0xae, 0x44, 0x17, 0xea, 0x6d, 0x77, 0x38, 0x7f, 0xdf, 0xcc, 0x37, 0x43, 0x02, 0x0a,
	0x59, 0x72, 0x49, 0xe7, 0x9c, 0x88, 0x98, 0x25, 0x87, 0xe9, 0x9c, 0x09, 0x86, 0xfc, 0xf4, 0xac,
	0xb3, 0x3f, 0x66, 0x6c, 0x3c, 0xa5, 0x7d, 0x92, 0xc6, 0x7d, 0x92, 0x24, 0x4c, 0x28, 0x05, 0x9e,
	0x69, 0x74, 0x5a, 0x21, 0x9b, 0xcd, 0x8c, 0x3e, 0xfe, 0x12, 0x5a, 0x83, 0x29, 0xe3, 0x74, 0x30,
	0x21, 0x22, 0xa0, 0x17, 0xe8, 0x13, 0xd8, 0xb2, 0xbd, 0x8e, 0xe2, 0xa8, 0xed, 0x75, 0xbd, 0x5e,
	0x23, 0xf8, 0xc0, 0x16, 0x0f, 0x23, 0xfc, 0xab, 0x07, 0xf7, 0x06, 0x2c, 0x11, 0x71, 0xb2, 0xd0,
	0xc6, 0x3c, 0x65, 0x09, 0xa7, 0xe8, 0x0b, 0xb8, 0x3f, 0x21, 0x7c, 0x44, 0x42, 0x11, 0x5f, 0xd2,
	0x91, 0x6d, 0xa5, 0x3c, 0xd5, 0x83, 0xdd, 0x09, 0xe1, 0x47, 0xea, 0x74, 0x60, 0x1d, 0xa2, 0x5d,
	0xa8, 0x86, 0x44, 0xc8, 0x80, 0x7e, 0xd7, 0xeb, 0x55, 0x82, 0x4a, 0x48, 0xc4, 0x30, 0x72, 0x25,
	0x54, 0x72, 0x26, 0xf4, 0x16, 0x9a, 0xa7, 0x9c, 0xce, 0x0d, 0x90, 0xfb, 0x50, 0x5b, 0x70, 0x3a,
	0xcf, 0x01, 0x54, 0xe5, 0xef, 0x30, 0x92, 0x07, 0x63, 0x32, 0xa3, 0x26, 0x50, 0x23, 0xa8, 0xca,
	0xdf, 0x61, 0x84, 0xf6, 0x01, 0xde, 0xa5, 0xd7, 0x23, 0x92, 0xa6, 0x79, 0x90, 0xfa, 0xbb, 0xf4,
	0xfa, 0x28, 0x4d, 0x87, 0x11, 0xfe, 0xc7, 0x83, 0x07, 0xa7, 0x69, 0x44, 0x44, 0x21, 0xeb, 0x80,
	0x5e, 0x2c, 0x28, 0x17, 0x1b, 0x97, 0xcd, 0x4e, 0xcb, 0x2f, 0xa4, 0x95, 0xc3, 0x2f, 0xd9, 0xf0,
	0x1f, 0x42, 0x43, 0xc4, 0xe1, 0x39, 0x55, 0x27, 0xe5, 0xae, 0xd7, 0x2b, 0x05, 0xf5, 0x4c, 0x30,
	0x8c, 0xd0, 0x13, 0xa8, 0x4d, 0x62, 0x2e, 0xd8, 0xfc, 0xa6, 0x5d, 0xe9, 0x96, 0x7a, 0xcd, 0xe7,
	0xcd, 0xc3, 0xf4, 0xec, 0xf0, 0x24, 0x13, 0x05, 0xe6, 0x0c, 0xbd, 0x84, 0x6d, 0x95, 0xa5, 0x4c,
	0x6c, 0x4c, 0xc5, 0x68, 0x1a, 0x73, 0xd1, 0xae, 0x2a, 0x83, 0x2d, 0x69, 0xf0, 0xa3, 0x3e, 0x3c,
	0xa6, 0x22, 0xd8, 0xba, 0xc8, 0x7f, 0x7e, 0x88, 0xb9, 0xc0, 0x57, 0xd0, 0x71, 0xc1, 0xd6, 0xcd,
	0x46, 0x50, 0x5e, 0x2c, 0x96, 0x60, 0xd5, 0xb7, 0xab, 0x16, 0xbe, 0xb3, 0x16, 0xfb, 0x12, 0xdb,
	0x8c, 0x72, 0x41, 0x66, 0xa9, 0x42, 0x5d, 0x0a, 0x72, 0x01, 0x7e, 0xef, 0xc1, 0xf6, 0x31, 0x15,
	0x06, 0x8d, 0x2e, 0xb4, 0xd5, 0x3d, 0x19, 0xf3, 0xce, 0x2d, 0xdd, 0xf3, 0x8b, 0xdd, 0x93, 0x79,
	0x4e, 0x49, 0x32, 0xd6, 0x5d, 0x55, 0xdf, 0xae, 0x3c, 0xcb, 0x4e, 0x66, 0xbd, 0xf7, 0x01, 0xd9,
	0x99, 0x68, 0xec, 0x4f, 0xf3, 0xea, 0x7b, 0x79, 0x31, 0xb5, 0xd6, 0x50, 0xd0, 0x99, 0xbb, 0x03,
	0x93, 0x58, 0x77, 0xc0, 0x5f, 0xef, 0xc0, 0x49, 0x6c, 0x75, 0xe0, 0x24, 0x56, 0x1d, 0x70, 0xb7,
	0xaf, 0xb4, 0x59, 0xfb, 0xfe, 0x9b, 0x3f, 0x7d, 0xd8, 0x29, 0x54, 0x80, 0x0b, 0x22, 0x16, 0xbc,
	0x5d, 0x51, 0x04, 0x2c, 0x6c, 0x97, 0xd7, 0xea, 0x44, 0x56, 0xa2, 0xa9, 0x07, 0xec, 0xff, 0xd1,
	0xbe, 0x07, 0x77, 0x13, 0x76, 0x35, 0x5a, 0xe2, 0x38, 0xa7, 0x37, 0x86, 0x14, 0x09, 0xbb, 0x32,
	0x08, 0xbe, 0xa7, 0x37, 0xe8, 0x33, 0xb8, 0x57, 0xd0, 0x0c, 0x59, 0x22, 0x68, 0x22, 0x74, 0xe7,
	0x90, 0xa5, 0x3d, 0xc8, 0x4e, 0xd0, 0xd7, 0x20, 0xa5, 0x23, 0x92, 0xf0, 0x2b, 0x3a, 0x5f, 0xea,
	0x97, 0xf3, 0x81, 0xd0, 0x8a, 0x81, 0x4c, 0xe1, 0x48, 0x69, 0x19, 0xd3, 0xaf, 0xe0, 0xce, 0x32,
	0x90, 0x2a, 0x6b, 0x36, 0x46, 0x3b, 0x76, 0x59, 0x8d, 0x75, 0xcb, 0x68, 0xaa, 0xb1, 0x18, 0xc1,
	0xd6, 0x6a, 0x1e, 0x8f, 0xa1, 0x55, 0xc0, 0x97, 0x55, 0xa2, 0x79, 0x61, 0x81, 0x7b, 0x0a, 0x77,
	0xd7, 0x80, 0x65, 0x65, 0x58, 0x36, 0x4e, 0x7b, 0xc3, 0x7f, 0x7b, 0xd0, 0x2a, 0xec, 0xd5, 0x6f,
	0x60, 0x47, 0xd3, 0xc9, 0x40, 0x55, 0x19, 0x7b, 0x6e, 0x22, 0x6c, 0x6b, 0xdd, 0x0c, 0xaf, 0xa2,
	0xc2, 0xe6, 0x3d, 0xf8, 0xd4, 0x59, 0xd1, 0xac, 0x03, 0xeb, 0x45, 0x7c, 0x0c, 0x2d, 0xc9, 0x69,
	0xe3, 0x57, 0xb1, 0xac, 0x1e, 0x34, 0x27, 0xb1, 0x30, 0x3e, 0xe5, 0xa4, 0x87, 0x6c, 0x96, 0x4e,
	0xa9, 0xa0, 0x91, 0xa2, 0x57, 0x3d, 0xc8, 0x05, 0xf8, 0x0d, 0xd4, 0xf4, 0xd4, 0xc8, 0x39, 0x9d,
	0xb3, 0x29, 0x35, 0xfb, 0x44, 0x7e, 0xaf, 0xd5, 0xd5, 0x5f, 0xaf, 0x6b, 0x1b, 0x6a, 0xc5, 0x2c,
	0xcd, 0x2f, 0xfe, 0xcd, 0x83, 0xa6, 0x35, 0x92, 0xce, 0x00, 0x96, 0xb5, 0x5f, 0xb0, 0x5e, 0x0b,
	0x5d, 0x5a, 0x0f, 0x5d, 0x58, 0x62, 0xe5, 0x95, 0x25, 0xb6, 0xdc, 0x8f, 0x95, 0x7c, 0x3f, 0xe2,
	0x57, 0xd0, 0xb4, 0xe6, 0x7d, 0x13, 0xda, 0x1c, 0x00, 0xa8, 0x2b, 0x55, 0x95, 0x5d, 0xe5, 0x58,
	0x0f, 0x1a, 0xf2, 0x16, 0x55, 0x02, 0xfc, 0x87, 0x97, 0x7b, 0x3c, 0xa6, 0x1b, 0x79, 0xdc, 0x83,
	0xaa, 0xe5, 0xad, 0x11, 0xe8, 0xbf, 0x95, 0x48, 0xa5, 0x95, 0x48, 0x92, 0x18, 0x4b, 0xcf, 0x84,
	0x9f, 0x8f, 0x42, 0xb6, 0x50, 0xa3, 0x26, 0xf7, 0xc5, 0x92, 0xd9, 0x47, 0xfc, 0x7c, 0x20, 0xe5,
	0x4e, 0xb6, 0x57, 0xdc, 0x6c, 0x3f, 0x80, 0x9a, 0xa1, 0x13, 0x82, 0xb2, 0xa0, 0xd7, 0xc2, 0x74,
	0x48, 0x7e, 0x3f, 0xff, 0xa5, 0x0c, 0x3b, 0xf6, 0xfd, 0xf3, 0x9a, 0xce, 0x2f, 0xe3, 0x90, 0xa2,
	0x9f, 0x00, 0xad, 0x5f, 0x4e, 0xe8, 0x40, 0x0e, 0xc3, 0xad, 0x77, 0x75, 0xe7, 0xd1, 0x6d, 0xc7,
	0xd9, 0xa0, 0xe1, 0x8f, 0x7f, 0xfe, 0xf3, 0xaf, 0xdf, 0xfd, 0x2e, 0x7e, 0xd8, 0xa7, 0xe3, 0x39,
	0xe5, 0xbc, 0x6f, 0xef, 0xb2, 0xfe, 0x42, 0x19, 0xbe, 0xf0, 0x9e, 0xa1, 0x19, 0xec, 0x1d, 0x53,
	0x61, 0xbb, 0x30, 0x2c, 0xde, 0x95, 0x11, 0xd6, 0xee, 0xae, 0xce, 0xde, 0xaa, 0x58, 0x07, 0xfc,
	0x48, 0x05, 0x7c, 0x84, 0xf7, 0x9d, 0x01, 0xcd, 0x1d, 0x72, 0x0a, 0x70, 0x42, 0x92, 0x68, 0xaa,
	0x5e, 0x5b, 0x48, 0x0d, 0xbc, 0xb5, 0x8a, 0x3b, 0x77, 0x73, 0xc1, 0x8a, 0xdb, 0x07, 0x4e, 0xb7,
	0xe1, 0x84, 0x08, 0x89, 0xe2, 0x2d, 0xb4, 0xec, 0x67, 0x1c, 0x6a, 0x48, 0x3f, 0xdf, 0xcd, 0x52,
	0x71, 0xd3, 0x69, 0x9b, 0xe5, 0xb9, 0xfa, 0xc6, 0xc3, 0x3d, 0xe5, 0x1a, 0xe3, 0x03, 0xb7, 0x6b,
	0x6d, 0x22, 0xdd, 0xbf, 0x82, 0xc6, 0xf2, 0x7d, 0x89, 0xb2, 0x1c, 0xad, 0xe7, 0x66, 0x27, 0x8f,
	0x86, 0x9f, 0x28, 0x9f, 0x1f, 0xe2, 0x8e, 0xdb, 0xa7, 0xb4, 0x7a, 0xe1, 0x3d, 0xfb, 0xb6, 0xfa,
	0xa6, 0x7c, 0xf8, 0x32, 0x3d, 0x3b, 0xab, 0xaa, 0xf7, 0xeb, 0xe7, 0xff, 0x06, 0x00, 0x00, 0xff,
	0xff, 0xcb, 0xdf, 0xa6, 0xce, 0x05, 0x0b, 0x00, 0x00,
}
