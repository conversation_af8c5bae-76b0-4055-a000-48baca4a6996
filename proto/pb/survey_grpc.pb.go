// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.14.0
// source: survey.proto

package pb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	SurveyApi_SurveyConfigList_FullMethodName   = "/pb.SurveyApi/SurveyConfigList"
	SurveyApi_SurveyConfigEdit_FullMethodName   = "/pb.SurveyApi/SurveyConfigEdit"
	SurveyApi_SurveyConfigInfo_FullMethodName   = "/pb.SurveyApi/SurveyConfigInfo"
	SurveyApi_SurveyConfigEnable_FullMethodName = "/pb.SurveyApi/SurveyConfigEnable"
	SurveyApi_SurveyGenLink_FullMethodName      = "/pb.SurveyApi/SurveyGenLink"
	SurveyApi_SurveyStats_FullMethodName        = "/pb.SurveyApi/SurveyStats"
	SurveyApi_SurveyStatsExport_FullMethodName  = "/pb.SurveyApi/SurveyStatsExport"
	SurveyApi_SurveyTemplate_FullMethodName     = "/pb.SurveyApi/SurveyTemplate"
	SurveyApi_SurveySubmit_FullMethodName       = "/pb.SurveyApi/SurveySubmit"
)

// SurveyApiClient is the client API for SurveyApi service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type SurveyApiClient interface {
	// survey 调查问卷 - 列表
	SurveyConfigList(ctx context.Context, in *SurveyListReq, opts ...grpc.CallOption) (*SurveyListResp, error)
	// survey 调查问卷 - 修改
	SurveyConfigEdit(ctx context.Context, in *SurveyEditReq, opts ...grpc.CallOption) (*Empty, error)
	// survey 调查问卷 - 详情
	SurveyConfigInfo(ctx context.Context, in *SurveyInfoReq, opts ...grpc.CallOption) (*SurveyInfoResp, error)
	// survey 调查问卷 - enable
	SurveyConfigEnable(ctx context.Context, in *EnableReq, opts ...grpc.CallOption) (*Empty, error)
	// survey 调查问卷 - 生成链接
	SurveyGenLink(ctx context.Context, in *SurveyGenLinkReq, opts ...grpc.CallOption) (*SurveyGenLinkResp, error)
	// survey 报表
	SurveyStats(ctx context.Context, in *DiscordPlayerSatisfactionStatsReq, opts ...grpc.CallOption) (*Empty, error)
	// survey 报表 - 导出
	SurveyStatsExport(ctx context.Context, in *DiscordPlayerSatisfactionStatsReq, opts ...grpc.CallOption) (*Empty, error)
	// C端接口-转发 - 获取模板详情
	SurveyTemplate(ctx context.Context, in *SurveyTemplateReq, opts ...grpc.CallOption) (*SurveyTemplateResp, error)
	// C端接口-转发 - 问卷提交
	SurveySubmit(ctx context.Context, in *SurveySubmitReq, opts ...grpc.CallOption) (*Empty, error)
}

type surveyApiClient struct {
	cc grpc.ClientConnInterface
}

func NewSurveyApiClient(cc grpc.ClientConnInterface) SurveyApiClient {
	return &surveyApiClient{cc}
}

func (c *surveyApiClient) SurveyConfigList(ctx context.Context, in *SurveyListReq, opts ...grpc.CallOption) (*SurveyListResp, error) {
	out := new(SurveyListResp)
	err := c.cc.Invoke(ctx, SurveyApi_SurveyConfigList_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *surveyApiClient) SurveyConfigEdit(ctx context.Context, in *SurveyEditReq, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, SurveyApi_SurveyConfigEdit_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *surveyApiClient) SurveyConfigInfo(ctx context.Context, in *SurveyInfoReq, opts ...grpc.CallOption) (*SurveyInfoResp, error) {
	out := new(SurveyInfoResp)
	err := c.cc.Invoke(ctx, SurveyApi_SurveyConfigInfo_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *surveyApiClient) SurveyConfigEnable(ctx context.Context, in *EnableReq, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, SurveyApi_SurveyConfigEnable_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *surveyApiClient) SurveyGenLink(ctx context.Context, in *SurveyGenLinkReq, opts ...grpc.CallOption) (*SurveyGenLinkResp, error) {
	out := new(SurveyGenLinkResp)
	err := c.cc.Invoke(ctx, SurveyApi_SurveyGenLink_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *surveyApiClient) SurveyStats(ctx context.Context, in *DiscordPlayerSatisfactionStatsReq, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, SurveyApi_SurveyStats_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *surveyApiClient) SurveyStatsExport(ctx context.Context, in *DiscordPlayerSatisfactionStatsReq, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, SurveyApi_SurveyStatsExport_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *surveyApiClient) SurveyTemplate(ctx context.Context, in *SurveyTemplateReq, opts ...grpc.CallOption) (*SurveyTemplateResp, error) {
	out := new(SurveyTemplateResp)
	err := c.cc.Invoke(ctx, SurveyApi_SurveyTemplate_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *surveyApiClient) SurveySubmit(ctx context.Context, in *SurveySubmitReq, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, SurveyApi_SurveySubmit_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// SurveyApiServer is the server API for SurveyApi service.
// All implementations must embed UnimplementedSurveyApiServer
// for forward compatibility
type SurveyApiServer interface {
	// survey 调查问卷 - 列表
	SurveyConfigList(context.Context, *SurveyListReq) (*SurveyListResp, error)
	// survey 调查问卷 - 修改
	SurveyConfigEdit(context.Context, *SurveyEditReq) (*Empty, error)
	// survey 调查问卷 - 详情
	SurveyConfigInfo(context.Context, *SurveyInfoReq) (*SurveyInfoResp, error)
	// survey 调查问卷 - enable
	SurveyConfigEnable(context.Context, *EnableReq) (*Empty, error)
	// survey 调查问卷 - 生成链接
	SurveyGenLink(context.Context, *SurveyGenLinkReq) (*SurveyGenLinkResp, error)
	// survey 报表
	SurveyStats(context.Context, *DiscordPlayerSatisfactionStatsReq) (*Empty, error)
	// survey 报表 - 导出
	SurveyStatsExport(context.Context, *DiscordPlayerSatisfactionStatsReq) (*Empty, error)
	// C端接口-转发 - 获取模板详情
	SurveyTemplate(context.Context, *SurveyTemplateReq) (*SurveyTemplateResp, error)
	// C端接口-转发 - 问卷提交
	SurveySubmit(context.Context, *SurveySubmitReq) (*Empty, error)
	mustEmbedUnimplementedSurveyApiServer()
}

// UnimplementedSurveyApiServer must be embedded to have forward compatible implementations.
type UnimplementedSurveyApiServer struct {
}

func (UnimplementedSurveyApiServer) SurveyConfigList(context.Context, *SurveyListReq) (*SurveyListResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SurveyConfigList not implemented")
}
func (UnimplementedSurveyApiServer) SurveyConfigEdit(context.Context, *SurveyEditReq) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SurveyConfigEdit not implemented")
}
func (UnimplementedSurveyApiServer) SurveyConfigInfo(context.Context, *SurveyInfoReq) (*SurveyInfoResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SurveyConfigInfo not implemented")
}
func (UnimplementedSurveyApiServer) SurveyConfigEnable(context.Context, *EnableReq) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SurveyConfigEnable not implemented")
}
func (UnimplementedSurveyApiServer) SurveyGenLink(context.Context, *SurveyGenLinkReq) (*SurveyGenLinkResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SurveyGenLink not implemented")
}
func (UnimplementedSurveyApiServer) SurveyStats(context.Context, *DiscordPlayerSatisfactionStatsReq) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SurveyStats not implemented")
}
func (UnimplementedSurveyApiServer) SurveyStatsExport(context.Context, *DiscordPlayerSatisfactionStatsReq) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SurveyStatsExport not implemented")
}
func (UnimplementedSurveyApiServer) SurveyTemplate(context.Context, *SurveyTemplateReq) (*SurveyTemplateResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SurveyTemplate not implemented")
}
func (UnimplementedSurveyApiServer) SurveySubmit(context.Context, *SurveySubmitReq) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SurveySubmit not implemented")
}
func (UnimplementedSurveyApiServer) mustEmbedUnimplementedSurveyApiServer() {}

// UnsafeSurveyApiServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to SurveyApiServer will
// result in compilation errors.
type UnsafeSurveyApiServer interface {
	mustEmbedUnimplementedSurveyApiServer()
}

func RegisterSurveyApiServer(s grpc.ServiceRegistrar, srv SurveyApiServer) {
	s.RegisterService(&SurveyApi_ServiceDesc, srv)
}

func _SurveyApi_SurveyConfigList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SurveyListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SurveyApiServer).SurveyConfigList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SurveyApi_SurveyConfigList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SurveyApiServer).SurveyConfigList(ctx, req.(*SurveyListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SurveyApi_SurveyConfigEdit_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SurveyEditReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SurveyApiServer).SurveyConfigEdit(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SurveyApi_SurveyConfigEdit_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SurveyApiServer).SurveyConfigEdit(ctx, req.(*SurveyEditReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SurveyApi_SurveyConfigInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SurveyInfoReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SurveyApiServer).SurveyConfigInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SurveyApi_SurveyConfigInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SurveyApiServer).SurveyConfigInfo(ctx, req.(*SurveyInfoReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SurveyApi_SurveyConfigEnable_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(EnableReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SurveyApiServer).SurveyConfigEnable(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SurveyApi_SurveyConfigEnable_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SurveyApiServer).SurveyConfigEnable(ctx, req.(*EnableReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SurveyApi_SurveyGenLink_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SurveyGenLinkReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SurveyApiServer).SurveyGenLink(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SurveyApi_SurveyGenLink_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SurveyApiServer).SurveyGenLink(ctx, req.(*SurveyGenLinkReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SurveyApi_SurveyStats_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DiscordPlayerSatisfactionStatsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SurveyApiServer).SurveyStats(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SurveyApi_SurveyStats_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SurveyApiServer).SurveyStats(ctx, req.(*DiscordPlayerSatisfactionStatsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SurveyApi_SurveyStatsExport_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DiscordPlayerSatisfactionStatsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SurveyApiServer).SurveyStatsExport(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SurveyApi_SurveyStatsExport_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SurveyApiServer).SurveyStatsExport(ctx, req.(*DiscordPlayerSatisfactionStatsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SurveyApi_SurveyTemplate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SurveyTemplateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SurveyApiServer).SurveyTemplate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SurveyApi_SurveyTemplate_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SurveyApiServer).SurveyTemplate(ctx, req.(*SurveyTemplateReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _SurveyApi_SurveySubmit_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SurveySubmitReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SurveyApiServer).SurveySubmit(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SurveyApi_SurveySubmit_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SurveyApiServer).SurveySubmit(ctx, req.(*SurveySubmitReq))
	}
	return interceptor(ctx, in, info, handler)
}

// SurveyApi_ServiceDesc is the grpc.ServiceDesc for SurveyApi service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var SurveyApi_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "pb.SurveyApi",
	HandlerType: (*SurveyApiServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SurveyConfigList",
			Handler:    _SurveyApi_SurveyConfigList_Handler,
		},
		{
			MethodName: "SurveyConfigEdit",
			Handler:    _SurveyApi_SurveyConfigEdit_Handler,
		},
		{
			MethodName: "SurveyConfigInfo",
			Handler:    _SurveyApi_SurveyConfigInfo_Handler,
		},
		{
			MethodName: "SurveyConfigEnable",
			Handler:    _SurveyApi_SurveyConfigEnable_Handler,
		},
		{
			MethodName: "SurveyGenLink",
			Handler:    _SurveyApi_SurveyGenLink_Handler,
		},
		{
			MethodName: "SurveyStats",
			Handler:    _SurveyApi_SurveyStats_Handler,
		},
		{
			MethodName: "SurveyStatsExport",
			Handler:    _SurveyApi_SurveyStatsExport_Handler,
		},
		{
			MethodName: "SurveyTemplate",
			Handler:    _SurveyApi_SurveyTemplate_Handler,
		},
		{
			MethodName: "SurveySubmit",
			Handler:    _SurveyApi_SurveySubmit_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "survey.proto",
}
