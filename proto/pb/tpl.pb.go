// Code generated by protoc-gen-go. DO NOT EDIT.
// source: tpl.proto

package pb

import (
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

// TplListReq 模板配置列表-请求参数
type TplListReq struct {
	// 模版名称
	Tpl string `protobuf:"bytes,1,opt,name=tpl,proto3" json:"tpl"`
	// 页码
	Page uint32 `protobuf:"varint,2,opt,name=page,proto3" json:"page"`
	// 页大小
	PageSize             uint32   `protobuf:"varint,3,opt,name=page_size,json=pageSize,proto3" json:"page_size"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *TplListReq) Reset()         { *m = TplListReq{} }
func (m *TplListReq) String() string { return proto.CompactTextString(m) }
func (*TplListReq) ProtoMessage()    {}
func (*TplListReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_9b67f539a49c8ace, []int{0}
}

func (m *TplListReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TplListReq.Unmarshal(m, b)
}
func (m *TplListReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TplListReq.Marshal(b, m, deterministic)
}
func (m *TplListReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TplListReq.Merge(m, src)
}
func (m *TplListReq) XXX_Size() int {
	return xxx_messageInfo_TplListReq.Size(m)
}
func (m *TplListReq) XXX_DiscardUnknown() {
	xxx_messageInfo_TplListReq.DiscardUnknown(m)
}

var xxx_messageInfo_TplListReq proto.InternalMessageInfo

func (m *TplListReq) GetTpl() string {
	if m != nil {
		return m.Tpl
	}
	return ""
}

func (m *TplListReq) GetPage() uint32 {
	if m != nil {
		return m.Page
	}
	return 0
}

func (m *TplListReq) GetPageSize() uint32 {
	if m != nil {
		return m.PageSize
	}
	return 0
}

// TplListResp 模板配置列表-响应结果
type TplListResp struct {
	CurrentPage          uint32                 `protobuf:"varint,1,opt,name=current_page,json=currentPage,proto3" json:"current_page"`
	PerPage              uint32                 `protobuf:"varint,2,opt,name=per_page,json=perPage,proto3" json:"per_page"`
	Total                uint32                 `protobuf:"varint,3,opt,name=total,proto3" json:"total"`
	Data                 []*TplListResp_TplInfo `protobuf:"bytes,4,rep,name=data,proto3" json:"data"`
	XXX_NoUnkeyedLiteral struct{}               `json:"-" gorm:"-"`
	XXX_unrecognized     []byte                 `json:"-" gorm:"-"`
	XXX_sizecache        int32                  `json:"-" gorm:"-"`
}

func (m *TplListResp) Reset()         { *m = TplListResp{} }
func (m *TplListResp) String() string { return proto.CompactTextString(m) }
func (*TplListResp) ProtoMessage()    {}
func (*TplListResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_9b67f539a49c8ace, []int{1}
}

func (m *TplListResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TplListResp.Unmarshal(m, b)
}
func (m *TplListResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TplListResp.Marshal(b, m, deterministic)
}
func (m *TplListResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TplListResp.Merge(m, src)
}
func (m *TplListResp) XXX_Size() int {
	return xxx_messageInfo_TplListResp.Size(m)
}
func (m *TplListResp) XXX_DiscardUnknown() {
	xxx_messageInfo_TplListResp.DiscardUnknown(m)
}

var xxx_messageInfo_TplListResp proto.InternalMessageInfo

func (m *TplListResp) GetCurrentPage() uint32 {
	if m != nil {
		return m.CurrentPage
	}
	return 0
}

func (m *TplListResp) GetPerPage() uint32 {
	if m != nil {
		return m.PerPage
	}
	return 0
}

func (m *TplListResp) GetTotal() uint32 {
	if m != nil {
		return m.Total
	}
	return 0
}

func (m *TplListResp) GetData() []*TplListResp_TplInfo {
	if m != nil {
		return m.Data
	}
	return nil
}

type TplListResp_TplInfo struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	Tpl                  string   `protobuf:"bytes,2,opt,name=tpl,proto3" json:"tpl"`
	UpdatedAt            string   `protobuf:"bytes,3,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at"`
	Op                   string   `protobuf:"bytes,4,opt,name=op,proto3" json:"op"`
	Enable               bool     `protobuf:"varint,5,opt,name=enable,proto3" json:"enable"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *TplListResp_TplInfo) Reset()         { *m = TplListResp_TplInfo{} }
func (m *TplListResp_TplInfo) String() string { return proto.CompactTextString(m) }
func (*TplListResp_TplInfo) ProtoMessage()    {}
func (*TplListResp_TplInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_9b67f539a49c8ace, []int{1, 0}
}

func (m *TplListResp_TplInfo) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TplListResp_TplInfo.Unmarshal(m, b)
}
func (m *TplListResp_TplInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TplListResp_TplInfo.Marshal(b, m, deterministic)
}
func (m *TplListResp_TplInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TplListResp_TplInfo.Merge(m, src)
}
func (m *TplListResp_TplInfo) XXX_Size() int {
	return xxx_messageInfo_TplListResp_TplInfo.Size(m)
}
func (m *TplListResp_TplInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_TplListResp_TplInfo.DiscardUnknown(m)
}

var xxx_messageInfo_TplListResp_TplInfo proto.InternalMessageInfo

func (m *TplListResp_TplInfo) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *TplListResp_TplInfo) GetTpl() string {
	if m != nil {
		return m.Tpl
	}
	return ""
}

func (m *TplListResp_TplInfo) GetUpdatedAt() string {
	if m != nil {
		return m.UpdatedAt
	}
	return ""
}

func (m *TplListResp_TplInfo) GetOp() string {
	if m != nil {
		return m.Op
	}
	return ""
}

func (m *TplListResp_TplInfo) GetEnable() bool {
	if m != nil {
		return m.Enable
	}
	return false
}

// TplOptsResp 模板配置筛选列表-响应结果
type TplOptsResp struct {
	List                 []*TplOptsResp_Opts `protobuf:"bytes,2,rep,name=list,proto3" json:"list"`
	XXX_NoUnkeyedLiteral struct{}            `json:"-" gorm:"-"`
	XXX_unrecognized     []byte              `json:"-" gorm:"-"`
	XXX_sizecache        int32               `json:"-" gorm:"-"`
}

func (m *TplOptsResp) Reset()         { *m = TplOptsResp{} }
func (m *TplOptsResp) String() string { return proto.CompactTextString(m) }
func (*TplOptsResp) ProtoMessage()    {}
func (*TplOptsResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_9b67f539a49c8ace, []int{2}
}

func (m *TplOptsResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TplOptsResp.Unmarshal(m, b)
}
func (m *TplOptsResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TplOptsResp.Marshal(b, m, deterministic)
}
func (m *TplOptsResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TplOptsResp.Merge(m, src)
}
func (m *TplOptsResp) XXX_Size() int {
	return xxx_messageInfo_TplOptsResp.Size(m)
}
func (m *TplOptsResp) XXX_DiscardUnknown() {
	xxx_messageInfo_TplOptsResp.DiscardUnknown(m)
}

var xxx_messageInfo_TplOptsResp proto.InternalMessageInfo

func (m *TplOptsResp) GetList() []*TplOptsResp_Opts {
	if m != nil {
		return m.List
	}
	return nil
}

type TplOptsResp_Opts struct {
	Id                   uint32   `protobuf:"varint,1,opt,name=id,proto3" json:"id"`
	Tpl                  string   `protobuf:"bytes,2,opt,name=tpl,proto3" json:"tpl"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *TplOptsResp_Opts) Reset()         { *m = TplOptsResp_Opts{} }
func (m *TplOptsResp_Opts) String() string { return proto.CompactTextString(m) }
func (*TplOptsResp_Opts) ProtoMessage()    {}
func (*TplOptsResp_Opts) Descriptor() ([]byte, []int) {
	return fileDescriptor_9b67f539a49c8ace, []int{2, 0}
}

func (m *TplOptsResp_Opts) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TplOptsResp_Opts.Unmarshal(m, b)
}
func (m *TplOptsResp_Opts) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TplOptsResp_Opts.Marshal(b, m, deterministic)
}
func (m *TplOptsResp_Opts) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TplOptsResp_Opts.Merge(m, src)
}
func (m *TplOptsResp_Opts) XXX_Size() int {
	return xxx_messageInfo_TplOptsResp_Opts.Size(m)
}
func (m *TplOptsResp_Opts) XXX_DiscardUnknown() {
	xxx_messageInfo_TplOptsResp_Opts.DiscardUnknown(m)
}

var xxx_messageInfo_TplOptsResp_Opts proto.InternalMessageInfo

func (m *TplOptsResp_Opts) GetId() uint32 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *TplOptsResp_Opts) GetTpl() string {
	if m != nil {
		return m.Tpl
	}
	return ""
}

// GroupAddReq 添加模板配置-请求参数
type TplAddReq struct {
	// 模版名称 @gotags: validate:"required"
	Tpl string `protobuf:"bytes,1,opt,name=tpl,proto3" json:"tpl" validate:"required"`
	// 模版表单
	Fields string `protobuf:"bytes,2,opt,name=fields,proto3" json:"fields"`
	// project 所属项目
	Project              string   `protobuf:"bytes,3,opt,name=project,proto3" json:"project"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *TplAddReq) Reset()         { *m = TplAddReq{} }
func (m *TplAddReq) String() string { return proto.CompactTextString(m) }
func (*TplAddReq) ProtoMessage()    {}
func (*TplAddReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_9b67f539a49c8ace, []int{3}
}

func (m *TplAddReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TplAddReq.Unmarshal(m, b)
}
func (m *TplAddReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TplAddReq.Marshal(b, m, deterministic)
}
func (m *TplAddReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TplAddReq.Merge(m, src)
}
func (m *TplAddReq) XXX_Size() int {
	return xxx_messageInfo_TplAddReq.Size(m)
}
func (m *TplAddReq) XXX_DiscardUnknown() {
	xxx_messageInfo_TplAddReq.DiscardUnknown(m)
}

var xxx_messageInfo_TplAddReq proto.InternalMessageInfo

func (m *TplAddReq) GetTpl() string {
	if m != nil {
		return m.Tpl
	}
	return ""
}

func (m *TplAddReq) GetFields() string {
	if m != nil {
		return m.Fields
	}
	return ""
}

func (m *TplAddReq) GetProject() string {
	if m != nil {
		return m.Project
	}
	return ""
}

// ReplyTplAddReq 添加回复模板配置-请求参数
type ReplyTplAddReq struct {
	// 模版名称 @gotags: validate:"required"
	Tpl string `protobuf:"bytes,1,opt,name=tpl,proto3" json:"tpl" validate:"required"`
	// project 所属项目
	Project string `protobuf:"bytes,2,opt,name=project,proto3" json:"project"`
	// 回复模版
	ReplyContent         map[string]string `protobuf:"bytes,3,rep,name=reply_content,json=replyContent,proto3" json:"reply_content" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-" gorm:"-"`
	XXX_unrecognized     []byte            `json:"-" gorm:"-"`
	XXX_sizecache        int32             `json:"-" gorm:"-"`
}

func (m *ReplyTplAddReq) Reset()         { *m = ReplyTplAddReq{} }
func (m *ReplyTplAddReq) String() string { return proto.CompactTextString(m) }
func (*ReplyTplAddReq) ProtoMessage()    {}
func (*ReplyTplAddReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_9b67f539a49c8ace, []int{4}
}

func (m *ReplyTplAddReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReplyTplAddReq.Unmarshal(m, b)
}
func (m *ReplyTplAddReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReplyTplAddReq.Marshal(b, m, deterministic)
}
func (m *ReplyTplAddReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReplyTplAddReq.Merge(m, src)
}
func (m *ReplyTplAddReq) XXX_Size() int {
	return xxx_messageInfo_ReplyTplAddReq.Size(m)
}
func (m *ReplyTplAddReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ReplyTplAddReq.DiscardUnknown(m)
}

var xxx_messageInfo_ReplyTplAddReq proto.InternalMessageInfo

func (m *ReplyTplAddReq) GetTpl() string {
	if m != nil {
		return m.Tpl
	}
	return ""
}

func (m *ReplyTplAddReq) GetProject() string {
	if m != nil {
		return m.Project
	}
	return ""
}

func (m *ReplyTplAddReq) GetReplyContent() map[string]string {
	if m != nil {
		return m.ReplyContent
	}
	return nil
}

// TplId 模板id 请求参数
type TplIdReq struct {
	// 模版ID @gotags: validate:"required"
	TplId                uint32   `protobuf:"varint,1,opt,name=tpl_id,json=tplId,proto3" json:"tpl_id" validate:"required"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *TplIdReq) Reset()         { *m = TplIdReq{} }
func (m *TplIdReq) String() string { return proto.CompactTextString(m) }
func (*TplIdReq) ProtoMessage()    {}
func (*TplIdReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_9b67f539a49c8ace, []int{5}
}

func (m *TplIdReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TplIdReq.Unmarshal(m, b)
}
func (m *TplIdReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TplIdReq.Marshal(b, m, deterministic)
}
func (m *TplIdReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TplIdReq.Merge(m, src)
}
func (m *TplIdReq) XXX_Size() int {
	return xxx_messageInfo_TplIdReq.Size(m)
}
func (m *TplIdReq) XXX_DiscardUnknown() {
	xxx_messageInfo_TplIdReq.DiscardUnknown(m)
}

var xxx_messageInfo_TplIdReq proto.InternalMessageInfo

func (m *TplIdReq) GetTplId() uint32 {
	if m != nil {
		return m.TplId
	}
	return 0
}

// TplInfoResp 模板配置信息
type TplInfoResp struct {
	TplId                uint32            `protobuf:"varint,1,opt,name=tpl_id,json=tplId,proto3" json:"tpl_id"`
	Tpl                  string            `protobuf:"bytes,2,opt,name=tpl,proto3" json:"tpl"`
	Fields               string            `protobuf:"bytes,3,opt,name=fields,proto3" json:"fields"`
	Category             string            `protobuf:"bytes,4,opt,name=category,proto3" json:"category"`
	ReplyContent         map[string]string `protobuf:"bytes,5,rep,name=reply_content,json=replyContent,proto3" json:"reply_content" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-" gorm:"-"`
	XXX_unrecognized     []byte            `json:"-" gorm:"-"`
	XXX_sizecache        int32             `json:"-" gorm:"-"`
}

func (m *TplInfoResp) Reset()         { *m = TplInfoResp{} }
func (m *TplInfoResp) String() string { return proto.CompactTextString(m) }
func (*TplInfoResp) ProtoMessage()    {}
func (*TplInfoResp) Descriptor() ([]byte, []int) {
	return fileDescriptor_9b67f539a49c8ace, []int{6}
}

func (m *TplInfoResp) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TplInfoResp.Unmarshal(m, b)
}
func (m *TplInfoResp) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TplInfoResp.Marshal(b, m, deterministic)
}
func (m *TplInfoResp) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TplInfoResp.Merge(m, src)
}
func (m *TplInfoResp) XXX_Size() int {
	return xxx_messageInfo_TplInfoResp.Size(m)
}
func (m *TplInfoResp) XXX_DiscardUnknown() {
	xxx_messageInfo_TplInfoResp.DiscardUnknown(m)
}

var xxx_messageInfo_TplInfoResp proto.InternalMessageInfo

func (m *TplInfoResp) GetTplId() uint32 {
	if m != nil {
		return m.TplId
	}
	return 0
}

func (m *TplInfoResp) GetTpl() string {
	if m != nil {
		return m.Tpl
	}
	return ""
}

func (m *TplInfoResp) GetFields() string {
	if m != nil {
		return m.Fields
	}
	return ""
}

func (m *TplInfoResp) GetCategory() string {
	if m != nil {
		return m.Category
	}
	return ""
}

func (m *TplInfoResp) GetReplyContent() map[string]string {
	if m != nil {
		return m.ReplyContent
	}
	return nil
}

// GroupEditReq 修改技能组请求参数
type TplSaveReq struct {
	// 模版ID @gotags: validate:"required"
	TplId uint32 `protobuf:"varint,1,opt,name=tpl_id,json=tplId,proto3" json:"tpl_id" validate:"required"`
	// 模版名称 @gotags: validate:"required"
	Tpl string `protobuf:"bytes,2,opt,name=tpl,proto3" json:"tpl" validate:"required"`
	// 模版表单
	Fields               string   `protobuf:"bytes,3,opt,name=fields,proto3" json:"fields"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" gorm:"-"`
	XXX_unrecognized     []byte   `json:"-" gorm:"-"`
	XXX_sizecache        int32    `json:"-" gorm:"-"`
}

func (m *TplSaveReq) Reset()         { *m = TplSaveReq{} }
func (m *TplSaveReq) String() string { return proto.CompactTextString(m) }
func (*TplSaveReq) ProtoMessage()    {}
func (*TplSaveReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_9b67f539a49c8ace, []int{7}
}

func (m *TplSaveReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_TplSaveReq.Unmarshal(m, b)
}
func (m *TplSaveReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_TplSaveReq.Marshal(b, m, deterministic)
}
func (m *TplSaveReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TplSaveReq.Merge(m, src)
}
func (m *TplSaveReq) XXX_Size() int {
	return xxx_messageInfo_TplSaveReq.Size(m)
}
func (m *TplSaveReq) XXX_DiscardUnknown() {
	xxx_messageInfo_TplSaveReq.DiscardUnknown(m)
}

var xxx_messageInfo_TplSaveReq proto.InternalMessageInfo

func (m *TplSaveReq) GetTplId() uint32 {
	if m != nil {
		return m.TplId
	}
	return 0
}

func (m *TplSaveReq) GetTpl() string {
	if m != nil {
		return m.Tpl
	}
	return ""
}

func (m *TplSaveReq) GetFields() string {
	if m != nil {
		return m.Fields
	}
	return ""
}

// ReplyTplSaveReq 添加回复模板配置-请求参数
type ReplyTplSaveReq struct {
	// 模版名称 @gotags: validate:"required"
	TplId uint32 `protobuf:"varint,1,opt,name=tpl_id,json=tplId,proto3" json:"tpl_id" validate:"required"`
	// project 所属项目
	Tpl string `protobuf:"bytes,2,opt,name=tpl,proto3" json:"tpl"`
	// 回复模版
	ReplyContent         map[string]string `protobuf:"bytes,3,rep,name=reply_content,json=replyContent,proto3" json:"reply_content" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	XXX_NoUnkeyedLiteral struct{}          `json:"-" gorm:"-"`
	XXX_unrecognized     []byte            `json:"-" gorm:"-"`
	XXX_sizecache        int32             `json:"-" gorm:"-"`
}

func (m *ReplyTplSaveReq) Reset()         { *m = ReplyTplSaveReq{} }
func (m *ReplyTplSaveReq) String() string { return proto.CompactTextString(m) }
func (*ReplyTplSaveReq) ProtoMessage()    {}
func (*ReplyTplSaveReq) Descriptor() ([]byte, []int) {
	return fileDescriptor_9b67f539a49c8ace, []int{8}
}

func (m *ReplyTplSaveReq) XXX_Unmarshal(b []byte) error {
	return xxx_messageInfo_ReplyTplSaveReq.Unmarshal(m, b)
}
func (m *ReplyTplSaveReq) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	return xxx_messageInfo_ReplyTplSaveReq.Marshal(b, m, deterministic)
}
func (m *ReplyTplSaveReq) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ReplyTplSaveReq.Merge(m, src)
}
func (m *ReplyTplSaveReq) XXX_Size() int {
	return xxx_messageInfo_ReplyTplSaveReq.Size(m)
}
func (m *ReplyTplSaveReq) XXX_DiscardUnknown() {
	xxx_messageInfo_ReplyTplSaveReq.DiscardUnknown(m)
}

var xxx_messageInfo_ReplyTplSaveReq proto.InternalMessageInfo

func (m *ReplyTplSaveReq) GetTplId() uint32 {
	if m != nil {
		return m.TplId
	}
	return 0
}

func (m *ReplyTplSaveReq) GetTpl() string {
	if m != nil {
		return m.Tpl
	}
	return ""
}

func (m *ReplyTplSaveReq) GetReplyContent() map[string]string {
	if m != nil {
		return m.ReplyContent
	}
	return nil
}

func init() {
	proto.RegisterType((*TplListReq)(nil), "pb.TplListReq")
	proto.RegisterType((*TplListResp)(nil), "pb.TplListResp")
	proto.RegisterType((*TplListResp_TplInfo)(nil), "pb.TplListResp.TplInfo")
	proto.RegisterType((*TplOptsResp)(nil), "pb.TplOptsResp")
	proto.RegisterType((*TplOptsResp_Opts)(nil), "pb.TplOptsResp.Opts")
	proto.RegisterType((*TplAddReq)(nil), "pb.TplAddReq")
	proto.RegisterType((*ReplyTplAddReq)(nil), "pb.ReplyTplAddReq")
	proto.RegisterMapType((map[string]string)(nil), "pb.ReplyTplAddReq.ReplyContentEntry")
	proto.RegisterType((*TplIdReq)(nil), "pb.TplIdReq")
	proto.RegisterType((*TplInfoResp)(nil), "pb.TplInfoResp")
	proto.RegisterMapType((map[string]string)(nil), "pb.TplInfoResp.ReplyContentEntry")
	proto.RegisterType((*TplSaveReq)(nil), "pb.TplSaveReq")
	proto.RegisterType((*ReplyTplSaveReq)(nil), "pb.ReplyTplSaveReq")
	proto.RegisterMapType((map[string]string)(nil), "pb.ReplyTplSaveReq.ReplyContentEntry")
}

func init() {
	proto.RegisterFile("tpl.proto", fileDescriptor_9b67f539a49c8ace)
}

var fileDescriptor_9b67f539a49c8ace = []byte{
	// 523 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xb4, 0x54, 0x4d, 0x6f, 0xd3, 0x40,
	0x10, 0x95, 0x37, 0x8e, 0x63, 0x4f, 0xda, 0x02, 0xab, 0x52, 0x4c, 0x10, 0x52, 0x62, 0x81, 0x64,
	0x09, 0xc9, 0x07, 0xb8, 0x20, 0x38, 0xa0, 0x82, 0x40, 0x2a, 0x02, 0x05, 0xb9, 0x3d, 0x71, 0xb1,
	0xd6, 0xf1, 0xb6, 0x32, 0x5d, 0xd9, 0xcb, 0x7a, 0x53, 0xc9, 0x3d, 0xf2, 0xe7, 0xe0, 0xff, 0xf0,
	0x07, 0xd0, 0x4e, 0xd6, 0xa9, 0x03, 0xe1, 0xeb, 0xd0, 0x53, 0xf6, 0xcd, 0xce, 0xbe, 0x79, 0xf3,
	0x66, 0x62, 0x08, 0xb4, 0x14, 0x89, 0x54, 0xb5, 0xae, 0x29, 0x91, 0x79, 0x34, 0x07, 0x38, 0x91,
	0xe2, 0x5d, 0xd9, 0xe8, 0x94, 0x7f, 0xa6, 0x37, 0x61, 0xa0, 0xa5, 0x08, 0x9d, 0xa9, 0x13, 0x07,
	0xa9, 0x39, 0x52, 0x0a, 0xae, 0x64, 0x67, 0x3c, 0x24, 0x53, 0x27, 0xde, 0x4d, 0xf1, 0x4c, 0xef,
	0x41, 0x60, 0x7e, 0xb3, 0xa6, 0xbc, 0xe4, 0xe1, 0x00, 0x2f, 0x7c, 0x13, 0x38, 0x2e, 0x2f, 0x79,
	0xf4, 0x85, 0xc0, 0x78, 0xcd, 0xd8, 0x48, 0x3a, 0x83, 0x9d, 0xc5, 0x52, 0x29, 0x5e, 0xe9, 0x0c,
	0x89, 0x1c, 0xcc, 0x1f, 0xdb, 0xd8, 0x07, 0xc3, 0x77, 0x17, 0x7c, 0xc9, 0x55, 0xd6, 0xab, 0x33,
	0x92, 0x5c, 0xe1, 0xd5, 0x3e, 0x0c, 0x75, 0xad, 0x99, 0xb0, 0x65, 0x56, 0x80, 0x3e, 0x02, 0xb7,
	0x60, 0x9a, 0x85, 0xee, 0x74, 0x10, 0x8f, 0x1f, 0xdf, 0x49, 0x64, 0x9e, 0xf4, 0x4a, 0x9a, 0xf3,
	0x51, 0x75, 0x5a, 0xa7, 0x98, 0x34, 0x51, 0x30, 0xb2, 0x01, 0xba, 0x07, 0xa4, 0x2c, 0xac, 0x02,
	0x52, 0x16, 0x5d, 0xbb, 0xe4, 0xaa, 0xdd, 0xfb, 0x00, 0x4b, 0x59, 0x30, 0xcd, 0x8b, 0x8c, 0x69,
	0x2c, 0x1a, 0xa4, 0x81, 0x8d, 0x1c, 0x6a, 0x43, 0x50, 0xcb, 0xd0, 0xc5, 0x30, 0xa9, 0x25, 0x3d,
	0x00, 0x8f, 0x57, 0x2c, 0x17, 0x3c, 0x1c, 0x4e, 0x9d, 0xd8, 0x4f, 0x2d, 0x8a, 0x18, 0x7a, 0x30,
	0x97, 0xba, 0x41, 0x0f, 0x62, 0x70, 0x45, 0xd9, 0xe8, 0x90, 0xa0, 0xde, 0x7d, 0xab, 0xb7, 0xbb,
	0x4e, 0xf0, 0x80, 0x19, 0x93, 0x18, 0x5c, 0x83, 0xfe, 0xae, 0x34, 0x9a, 0x43, 0x70, 0x22, 0xc5,
	0x61, 0x51, 0x6c, 0x9f, 0xdb, 0x01, 0x78, 0xa7, 0x25, 0x17, 0x45, 0x63, 0xdf, 0x58, 0x44, 0x43,
	0x18, 0x49, 0x55, 0x7f, 0xe2, 0x8b, 0xae, 0xbb, 0x0e, 0x46, 0xdf, 0x1c, 0xd8, 0x4b, 0xb9, 0x14,
	0xed, 0x9f, 0x68, 0x7b, 0xcf, 0xc9, 0xc6, 0x73, 0x7a, 0x04, 0xbb, 0xca, 0xbc, 0xce, 0x16, 0x75,
	0xa5, 0x79, 0x65, 0xe8, 0x4d, 0xb3, 0x0f, 0x4c, 0xb3, 0x9b, 0xb4, 0x2b, 0xf8, 0x6a, 0x95, 0xf6,
	0xba, 0xd2, 0xaa, 0x4d, 0x77, 0x54, 0x2f, 0x34, 0x79, 0x01, 0xb7, 0x7e, 0x49, 0x31, 0x5a, 0xce,
	0x79, 0xdb, 0x69, 0x39, 0xe7, 0xad, 0xd9, 0x8d, 0x0b, 0x26, 0x96, 0xdc, 0x2a, 0x59, 0x81, 0x67,
	0xe4, 0xa9, 0x13, 0xcd, 0xc0, 0x37, 0x23, 0xc7, 0x1e, 0x6e, 0x83, 0xa7, 0xa5, 0xc8, 0xd6, 0x6e,
	0x0e, 0xb5, 0xb9, 0x89, 0xbe, 0x3b, 0x38, 0x22, 0xdc, 0x13, 0x33, 0xa2, 0xed, 0x69, 0x5b, 0x36,
	0xe4, 0xca, 0xd8, 0xc1, 0x86, 0xb1, 0x13, 0xf0, 0x17, 0x4c, 0xf3, 0xb3, 0x5a, 0xb5, 0x76, 0x41,
	0xd6, 0x98, 0xbe, 0xf9, 0xd9, 0x9b, 0x21, 0x7a, 0x33, 0xb3, 0x8b, 0xd0, 0x89, 0xb8, 0x7e, 0x63,
	0xde, 0xe3, 0xbf, 0xfd, 0x98, 0x5d, 0xf0, 0xdf, 0x5b, 0xf3, 0xef, 0x3d, 0x47, 0x5f, 0x1d, 0xb8,
	0xd1, 0xcd, 0xf6, 0xbf, 0x49, 0xdf, 0x6e, 0x5f, 0x98, 0x87, 0xfd, 0x85, 0xb1, 0xa4, 0xd7, 0x6e,
	0xcc, 0x4b, 0xef, 0xa3, 0x9b, 0x3c, 0x97, 0x79, 0xee, 0xe1, 0x97, 0xf1, 0xc9, 0x8f, 0x00, 0x00,
	0x00, 0xff, 0xff, 0x01, 0xdd, 0x69, 0xe6, 0x26, 0x05, 0x00, 0x00,
}
