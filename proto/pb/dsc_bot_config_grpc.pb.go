// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.14.0
// source: dsc_bot_config.proto

package pb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	DCSBotConfigService_AddDCSBotConfig_FullMethodName                  = "/pb.DCSBotConfigService/AddDCSBotConfig"
	DCSBotConfigService_CheckDCSBotConfig_FullMethodName                = "/pb.DCSBotConfigService/CheckDCSBotConfig"
	DCSBotConfigService_UpdateDCSBotConfigWelcomeMessage_FullMethodName = "/pb.DCSBotConfigService/UpdateDCSBotConfigWelcomeMessage"
	DCSBotConfigService_GetDCSBotConfigList_FullMethodName              = "/pb.DCSBotConfigService/GetDCSBotConfigList"
)

// DCSBotConfigServiceClient is the client API for DCSBotConfigService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type DCSBotConfigServiceClient interface {
	// 新增DC机器人配置
	AddDCSBotConfig(ctx context.Context, in *DCSBotConfigAddReq, opts ...grpc.CallOption) (*Empty, error)
	// 验证DC机器人配置
	CheckDCSBotConfig(ctx context.Context, in *DCSBotConfigAddReq, opts ...grpc.CallOption) (*Empty, error)
	// 更新DC机器人配置欢迎消息
	UpdateDCSBotConfigWelcomeMessage(ctx context.Context, in *UpdateDCSBotConfigWelcomeMessageReq, opts ...grpc.CallOption) (*Empty, error)
	// 获取DC机器人配置列表
	GetDCSBotConfigList(ctx context.Context, in *DCSBotConfigListReq, opts ...grpc.CallOption) (*DCSBotConfigListResp, error)
}

type dCSBotConfigServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewDCSBotConfigServiceClient(cc grpc.ClientConnInterface) DCSBotConfigServiceClient {
	return &dCSBotConfigServiceClient{cc}
}

func (c *dCSBotConfigServiceClient) AddDCSBotConfig(ctx context.Context, in *DCSBotConfigAddReq, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, DCSBotConfigService_AddDCSBotConfig_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dCSBotConfigServiceClient) CheckDCSBotConfig(ctx context.Context, in *DCSBotConfigAddReq, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, DCSBotConfigService_CheckDCSBotConfig_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dCSBotConfigServiceClient) UpdateDCSBotConfigWelcomeMessage(ctx context.Context, in *UpdateDCSBotConfigWelcomeMessageReq, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, DCSBotConfigService_UpdateDCSBotConfigWelcomeMessage_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dCSBotConfigServiceClient) GetDCSBotConfigList(ctx context.Context, in *DCSBotConfigListReq, opts ...grpc.CallOption) (*DCSBotConfigListResp, error) {
	out := new(DCSBotConfigListResp)
	err := c.cc.Invoke(ctx, DCSBotConfigService_GetDCSBotConfigList_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// DCSBotConfigServiceServer is the server API for DCSBotConfigService service.
// All implementations must embed UnimplementedDCSBotConfigServiceServer
// for forward compatibility
type DCSBotConfigServiceServer interface {
	// 新增DC机器人配置
	AddDCSBotConfig(context.Context, *DCSBotConfigAddReq) (*Empty, error)
	// 验证DC机器人配置
	CheckDCSBotConfig(context.Context, *DCSBotConfigAddReq) (*Empty, error)
	// 更新DC机器人配置欢迎消息
	UpdateDCSBotConfigWelcomeMessage(context.Context, *UpdateDCSBotConfigWelcomeMessageReq) (*Empty, error)
	// 获取DC机器人配置列表
	GetDCSBotConfigList(context.Context, *DCSBotConfigListReq) (*DCSBotConfigListResp, error)
	mustEmbedUnimplementedDCSBotConfigServiceServer()
}

// UnimplementedDCSBotConfigServiceServer must be embedded to have forward compatible implementations.
type UnimplementedDCSBotConfigServiceServer struct {
}

func (UnimplementedDCSBotConfigServiceServer) AddDCSBotConfig(context.Context, *DCSBotConfigAddReq) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddDCSBotConfig not implemented")
}
func (UnimplementedDCSBotConfigServiceServer) CheckDCSBotConfig(context.Context, *DCSBotConfigAddReq) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckDCSBotConfig not implemented")
}
func (UnimplementedDCSBotConfigServiceServer) UpdateDCSBotConfigWelcomeMessage(context.Context, *UpdateDCSBotConfigWelcomeMessageReq) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateDCSBotConfigWelcomeMessage not implemented")
}
func (UnimplementedDCSBotConfigServiceServer) GetDCSBotConfigList(context.Context, *DCSBotConfigListReq) (*DCSBotConfigListResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDCSBotConfigList not implemented")
}
func (UnimplementedDCSBotConfigServiceServer) mustEmbedUnimplementedDCSBotConfigServiceServer() {}

// UnsafeDCSBotConfigServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to DCSBotConfigServiceServer will
// result in compilation errors.
type UnsafeDCSBotConfigServiceServer interface {
	mustEmbedUnimplementedDCSBotConfigServiceServer()
}

func RegisterDCSBotConfigServiceServer(s grpc.ServiceRegistrar, srv DCSBotConfigServiceServer) {
	s.RegisterService(&DCSBotConfigService_ServiceDesc, srv)
}

func _DCSBotConfigService_AddDCSBotConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DCSBotConfigAddReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DCSBotConfigServiceServer).AddDCSBotConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DCSBotConfigService_AddDCSBotConfig_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DCSBotConfigServiceServer).AddDCSBotConfig(ctx, req.(*DCSBotConfigAddReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _DCSBotConfigService_CheckDCSBotConfig_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DCSBotConfigAddReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DCSBotConfigServiceServer).CheckDCSBotConfig(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DCSBotConfigService_CheckDCSBotConfig_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DCSBotConfigServiceServer).CheckDCSBotConfig(ctx, req.(*DCSBotConfigAddReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _DCSBotConfigService_UpdateDCSBotConfigWelcomeMessage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateDCSBotConfigWelcomeMessageReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DCSBotConfigServiceServer).UpdateDCSBotConfigWelcomeMessage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DCSBotConfigService_UpdateDCSBotConfigWelcomeMessage_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DCSBotConfigServiceServer).UpdateDCSBotConfigWelcomeMessage(ctx, req.(*UpdateDCSBotConfigWelcomeMessageReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _DCSBotConfigService_GetDCSBotConfigList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DCSBotConfigListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DCSBotConfigServiceServer).GetDCSBotConfigList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DCSBotConfigService_GetDCSBotConfigList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DCSBotConfigServiceServer).GetDCSBotConfigList(ctx, req.(*DCSBotConfigListReq))
	}
	return interceptor(ctx, in, info, handler)
}

// DCSBotConfigService_ServiceDesc is the grpc.ServiceDesc for DCSBotConfigService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var DCSBotConfigService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "pb.DCSBotConfigService",
	HandlerType: (*DCSBotConfigServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "AddDCSBotConfig",
			Handler:    _DCSBotConfigService_AddDCSBotConfig_Handler,
		},
		{
			MethodName: "CheckDCSBotConfig",
			Handler:    _DCSBotConfigService_CheckDCSBotConfig_Handler,
		},
		{
			MethodName: "UpdateDCSBotConfigWelcomeMessage",
			Handler:    _DCSBotConfigService_UpdateDCSBotConfigWelcomeMessage_Handler,
		},
		{
			MethodName: "GetDCSBotConfigList",
			Handler:    _DCSBotConfigService_GetDCSBotConfigList_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "dsc_bot_config.proto",
}
