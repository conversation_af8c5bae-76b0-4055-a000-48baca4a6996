syntax = "proto3";

package pb;
option go_package = ".;pb";

enum Bind {
  UnBind = 0;
  Bound = 1;
  Update = 2;
}

// 场景
enum SceneType {
  // 通用场景 - 废弃: 定制版游戏强制转换成3
  General = 0;
  // 游戏外 - 加载入口
  Loading = 1;
  // 游戏外 - 封号入口
  AccountBan = 2;
  // 游戏内 - 游戏内入口
  InGame = 3;
}
// 工单来源
enum Origin {
  Nobody = 0;
  // 玩家端提交 - 目前只有此来源
  Player = 1;
  //    VipService = 2;
  //    NormalService = 3;
}

// 工单状态
enum Status {
  // 待接入工单
  Untreated = 0;
  // 处理中工单
  Processing = 1;
  // 已完成
  Done = 2;
}

// CsiLevel 工单评星
enum CsiLevel {
  ZeroStar = 0;
  // 一星
  OneStar = 1;
  // 二星
  SecondStar = 2;
  // 三星
  ThreeStar = 3;
  // 四星
  FourStar = 4;
  // 五星
  FiveStar = 5;
}

// NpsLevel NPS 评分
enum NpsLevel {
  NpsScoreZero = 0;
  // 1分
  NpsOneScore = 1;
  // 2分
  NpsTwoScore = 2;
  // 3分
  NpsThreeScore = 3;
  // 4分
  NpsFourScore = 4;
  // 5分
  NpsFiveScore = 5;
}

enum CsiLabel {
  NullCsiLb = 0;
  TheService = 2;
  TheOutcome = 3;
  ProcessingSpeed = 4;
  FastProcessingSpeed = 5;
  SatisfiedResult = 6;
  SatisfiedService = 7;
}

// RetEnum 枚举
message RetEnum {
  string name = 1;   // 枚举名
  uint32 value = 2;  // 枚举值
}

// FilterTagEnum 标签过滤枚举
enum FilterTagEnum {
  // 全部, 只是用来占位, 无意义
  All = 0;
  // 空白
  IsNull = 1;
  // 包含
  IsIn = 2;
  // 不包含
  IsNotIn = 3;
  // 全部有标签的
  IsAll = 4;
}

// FilterEnum 基础过滤枚举
enum FilterEnum {
  // 全部
  FilterAll = 0;
  // 空白
  FilterIsNull = 1;
  // 包含
  FilterIsIn = 2;
  // 不包含
  FilterIsNotIn = 3;
  // 系统
  FilterSystem = 4;
}

// CreatorType
enum CreatorType {
  No = 0;
  UID = 1;
  Fpid = 2;
  Nickname = 3;
}


// Workbench 工作台
enum Workbench {
  // 总览
  OverviewWorkStation = 0;
  // 一线工作台
  FirstTierWorkStation = 1;
  // 二线工作台
  SecondTierWorkStation = 2;
  // vip工作台
  VipWorkStation = 3;
  // 三线工作台
  ThirdTierWorkStation = 4;
  // 一线管理工作台
  FirstTierAdminWorkStation = 5;
}

// TicketSys 使用哪个工单系统
enum TicketSys {
  // 默认老版本工单
  TicketSysOld = 0;
  // 新工单系统
  ticketSysNew = 1;
}

// RelateType 分类类型
enum RelateType {
  // 默认为 tpl
  RelateTypeDefault = 0;
  // tpl 模版
  RelateTypeTpl = 1;
  // 自动化流程
  RelateTypeProcess = 2;
}

// 工单事件
enum TicketEvent {
  // 工单创建
  Create = 0;
  // 一线接单自动分配
  Allocation = 1;
  // 一线接单
  Receipt = 2;
  // 工单流转
  Transfered = 3;
  // 工单结案 有满意度评价
  DoneCase = 4;
  // 工单指派
  Assign = 5;
  // 调整紧急度
  Emergency = 6;
  // 工单备注
  Remark = 7;
  // 工单回复
  Reply = 8;
  // 工单关闭 无满意度评价
  Closed = 9;
  // 标签操作
  Tag = 10;
  // 推送
  Relate = 11;
  // 撤回
  Revoke = 12;
  // 重开
  Reopen = 13;
  // 修正问题分类
  RectifyCat = 14;
}

// 工单事件
enum TkEvent {
  TkEventUnknown = 0;
  // 工单创建
  TkEventCreate = 1;
  // 玩家补填；
  TkEventPlayerRefill = 2;
  // 工单 - 自动分配
  TkEventAutoAlloc = 3;
  // 工单结案 有满意度评价
  TkEventDoneCase = 4;
  // 工单指派
  TkEventAssign = 5;
  // 工单流转给某人
  TkEventTurn = 6;
  // 玩家端回复
  TkEventUserCommu = 7;
  // 客服 - 回复
  TkEventCommu = 8;
  // 客服 - 回复&关单
  TkEventCommuClose = 9;
  // 客服 - 拒单
  TkEventRefused = 10;
  // 客服 - 工单升级/降级
  TkEventUpgrade = 11;
  // 客服 - 增加备注
  TkEventRemark = 12;
  // 重开
  TkEventReopen = 13;
  // 玩家3天未回复 - 设置超时关闭
  TkEventUserNoAnsTimout = 14;
  // 标签 - 新增标签
  TkEventTagAdd = 15;
  // 标签 - 删除
  TkEventTagDel = 16;
  // 超时 - 分配处理人 2h 未处理
  TkEventNoOpTimeout = 17;
  // 退回工单池
  TkEventReturnPool = 18;
}
// UserRole 角色
enum UserRole {
  NobodyRole = 0;
  // 玩家端
  PlayerRole = 1;
  // 服务端
  ServiceRole = 2;
  // 系统操作
  SystemRole = 3;
  VIPServiceRole = 4;
}

// TkStatus 工单状态定义
enum TkStatus {
  // 未知状态
  TkStatusUnknown = 0;
  // 待接入工单
  TkStatusUntreated = 1;
  // 处理中工单
  TkStatusProcessing = 2;
  // 已完成
  TkStatusDone = 3;
}

// TkStage 节点流转
enum TkStage  {
  TkStageUnknown = 0;
  // 工单状态 - New 待接单 - 玩家提交工单&未分配
  TkStageNew = 1;
  // 工单状态 - New For Agent 待处理 - 已分配/指派客服
  TkStageNewForAgent = 2;
  // 工单状态 - Agent Replied 处理中 - 待玩家回复 - 客服已回复待玩家回复
  TkStageAgentReplied = 3;
  // 工单状态 - Waiting For Agent 处理中 - 玩家已回复 待客服再次回复
  TkStageWaitingForAgent = 4;
  // 工单状态 - Agent Resolved 超时关闭 - 玩家3天内未回复，超时关闭
  TkStageAgentResolved = 5;
  // 工单状态 - Reopen 重开 - 玩家重新打开工单
  TkStageAgentReopen = 6;
  // 工单状态 - Rejected 拒单关闭 - 客服点击"拒单"，结束会话
  TkStageAgentRejected = 7;
  // 工单状态 - Completed 已完成 - 客服回复&关单
  TkStageAgentCompleted = 8;
}

// 工单流转节点部分
enum TicketStage {
  // 待处理
  Pending = 0;
  // 一线处理中
  FirstTierProcessing = 1;
  // 一线补填中
  FirstTierRefill = 2;
  // 二线处理中
  SecondTierProcessing = 3;
  // 二线审核区
  SecondTierAudit = 4;
  // VIP补填中
  VipAgentRefill = 5;
  // 处理完成 玩家有评价
  TicketResolved = 6;
  // 玩家补填
  TicketRefill = 7;
  // 关闭工单 玩家无评价
  TicketClosed = 8;
  // 暂时回复玩家
  TemporaryReply = 9;
  // vip处理
  VipAgentProcessing = 10;
  // 二线补填中
  SecondTierRefill = 11;
  // 三线处理
  ThirdTierProcessing = 12;
  // 三线审核区
  ThirdTierAudit = 13;
  // 一线管理处理中
  FirstTierAdminProcessing = 14;
  // 一线管理补填中
  FirstTierAdminRefill = 15;
  // VIP处理完成
  VipAgentResolved = 16;
}

// TkProgress 当前状态
enum TkProgress {
  TkProgressUnknown = 0;
  // 1：已完成
  TkProgressDone = 1;
  // 2：待补充
  TkProgressUserRefill = 2;
  // 3：处理中
  TkProgressUserDoing = 3;
  // 4：超时关闭
  TkProgressUserRefillOverTime = 4;
  // 5: 重新打开
  TkProgressReopen = 5;
  // 6: 拒单关闭
  TkProgressReject = 6;
}
// 关单角色
enum TkClosedRole {
  TkClosedNone = 0;
  TkPlayerClosed = 1;
  TkServiceClosed = 2;
  TkSystemTimeoutClosed = 3;
  TkSystemReplyClosed = 4;
}
// 客服人员是否在线状态定义
enum UserLoginStatus{
  UserLoginStatusUnknown = 0;
  // 在线
  UserLoginStatusYes = 1;
  // 离线
  UserLoginStatusNo = 2;
}

// 会话数据分类
enum CommuType{
  CommuTypeUnknown = 0;
  // 正常会话
  CommuTypeDialogue = 1;
  // 备注消息
  CommuTypeRemark = 2;
}

// discord相关

// 客服回复状态
enum DiscordServiceReplyStatus {
  StatusUnknown = 0;
  WaitServiceReply = 1;
  ServiceHasReplied = 2;
}

// 玩家性别
enum PlayerGender {
  GenderUnknown = 0;
  Male = 1;
  Female = 2;
}

// 玩家教育程度
enum PlayerEducationLevel {
  UnknownEducationLevel = 0;
  PrimarySchool = 1;
  JuniorHighSchool = 2;
  SeniorHighSchool = 3;
  UnderGraduate = 4;
  Graduate = 5;
  Doctor = 6;
  Postdoc = 7;
  Others = 8;
}

// 玩家婚姻状况
enum PlayerMarriageState {
  UnknownMarriageState = 0;
  Married = 1;
  Single = 2;
  Divorced = 3;
  Widowed = 4;
}

// 玩家生育状况
enum PlayerFertilityState {
  UnknownFertilityState = 0;
  NoChild = 1;
  OneChild = 2;
  TwoKids = 3;
  ThreeKids = 4;
  MoreThanThreeKids = 5;
}

enum PlayerVipState {
  UnknownVipState = 0;
  NonVip = 1;
  Vip = 2;
}

// discord 操作日志 - 分组
enum OpGroup {
  OpGroupUnknown = 0;
  OpGroupPortrait = 1; // 玩家画像
  OpGroupMaintainConfig = 2; // 玩家维护关系配置
  OpGroupSendTextMessage = 3; // 发送文本消息
  OpGroupEditMessage = 4; // 编辑消息
  OpGroupSendFile = 5; // 发送文件
  OpGroupExamineTpl = 20; // 质检 - examineTpl配置
  OpGroupExamineDsc = 21; // 质检 - 质检单变动
  OpGroupSurveySend = 22; // 调查问卷 - 发放记录
  OpGroupSurveyBatchGen = 23; // 调查问卷 - 批量生成
}

// discord 操作日志 - 操作类型
enum OpAction {
  OpActionUnknown = 0;
  OpActionAdd = 1; // 新增
  OpActionUpdate = 2; // 修改
  OpActionDelete = 3; // 删除
  OpActionStatus = 4; // 修改状态
}

// discord 沟通问题类型
enum QuestionType {
  UnknownQuestionType = 0;
  GameConsultation = 1; // 游戏咨询
  GameSuggestion = 2;  // 游戏建议
  GameException = 3;  // 游戏异常
  ServerMatchAndServerMerge = 4;   // 服务器匹配&合服
  ComplaintOrNegativeFeedback = 5; // 抱怨/负面反馈
  OtherQuestion = 6; // 其他问题
}

// discord 沟通问题处理状态
enum QuestionHandleStatus {
  UnknownHandleStatus = 0;
  UnderProcess = 1; // 处理中
  Finished = 2;   // 已完成
}

enum TicketSystemTag {
  UnknownTag = 0;
  GreenChannelUser = 10;
  PrivateZoneUser = 11;
  ElfinTypeUser = 12;
  PrivateZoneCard = 13;
}

// ai 润色标签
enum AIPolishLabel {
  UnknownPolishLabel = 0;
  PolishFriendlyLabel = 1;
  PolishConciseLabel = 2;
  PolishFormalLabel = 3;
  PolishPoliteLabel = 4;
  PolishCustomizeLabel = 5;
}

// 标签类型
enum TagConfigType {
  UnknownConfigType = 0;
  TicketConfigType = 1; // 工单标签类型
  DiscordConfigType = 2;   // Discord标签类型
  LineConfigType = 3;   // Line标签类型
}

// DC列表排序方式
enum DcPoolSort {
  DcPoolSortWaitDefault = 0;
  // 等待时长 - 默认
  DcPoolSortWaitTm = 1;
  // 客服最后回复时间
  DcPoolSortLastReplyTm = 2;
  // 累付金额
  DcPoolSortpaidAmount = 3;
}

// SVIP
enum SVIP {
  SVIPNone = 0;
  SVIPYes = 1;
  SVIPNo = 2;
}

// 查询类型
enum SearchTypeEnum {
  SearchTypeNone = 0;
  SearchTypeSubmitUser = 1;
  SearchTypeQuestionUser = 2;
}

// 用户类型
enum UserTypeEnum {
  UserTypeNone = 0;
  UserTypeLongVipUser = 1;
  UserTypeLimitTimeUser = 2;
  UserTypePaidUser = 3;
  UserTypeRegularUser = 4;
}

// 红点类型
enum RedPointTypeEnum {
  RedPointTypeNone = 0;
  RedPointTypeMessage = 1;
  RedPointTypeOvertime = 2;
}


//表单对话类型

enum ChatOrFormType {
  ChatOrFormTypeUnknown = 0;
  ChatOrFormTypeChat = 1;
  ChatOrFormTypeForm = 2;
}

enum TrainingTaskStatus{
  //未开始
  ProcessTicketStatusInit = 0;
  //处理中
  ProcessTicketStatusDoing = 10;
  //处理失败
  ProcessTicketStatusFail = 20;
  //处理成功
  ProcessTicketStatusSuccess = 30;
}


enum ConversationStatus  {
  ConversationStatusUnknown = 0;
  ConversationStatusOn = 1;
  ConversationStatusWait = 2;
  ConversationStatusClose = 10;
}

enum SolveType  {
  SolveTypeUnknowTicket = 0;
  SolveTypeManualTicket = 1;
  SolveTypeAutoReplyTicket = 2;
  SolveTypeInvalidTicket = 3;
  SolveTypeStrategyTicket = 4;
}

enum TicketType  {
  TicketTypeNormal = 0;
  TicketTypeNewAi = 1;
}


enum TicketTagType  {
  TicketTagTypeUnknown = 0;
  TicketTagTypeNormal = 1;
}

enum TicketAiTagType{
  TicketAiTagTypeUnknown = 0;
  TicketAiTagTypeAsk = 1;
  TicketAiTagTypeComplaint = 2;
  TicketAiTagTypeSuggestion = 3;
  TicketAiTagTypeElse = 4;
  TicketAiTagNotTag = 5;
}