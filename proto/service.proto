syntax = "proto3";

package pb;
option go_package = ".;pb";

import "google/api/annotations.proto";
import "ticket.proto";
import "common.proto";
import "label_group.proto";
import "user.proto";
import "channel_category.proto";
import "user_assign_ticket.proto";
import "overtime_tpl.proto";
import "team_config.proto";
import "module_category.proto";
import "category.proto";
import "module.proto";
import "batch_ticket.proto";
import "question.proto";
import "strategy.proto";

// 工单
service TicketApi {
    // 工单池 - 搜索接口
    rpc TicketWorkPool(TicketPoolNewListReq) returns (TicketPoolNewListResp) {
        option (google.api.http) = {
            post: "/api/ticket/pool"
            body: "*"
        };
    }

    // 工单池 - 工单池数据概览
    rpc TicketCount(TicketCountReq) returns (TicketCountResp) {
        option (google.api.http) = {
            post: "/api/ticket/count"
            body: "*"
        };
    }

    // 工单池 - 工单导出接口
    rpc TicketWorkPoolExport(TicketPoolNewListReq) returns (Empty) {
        option (google.api.http) = {
            post: "/api/ticket/export"
            body: "*"
        };
    }

    // 工单池 - 指派接口
    rpc Reassign(AssignmentReq) returns (Empty) {
        option (google.api.http) = {
            post: "/api/ticket/reassign"
            body: "*"
        };
    }
    // 工单池 - 所有基础数据
    rpc TicketInfo(TicketIdReq) returns (TicketResponse) {
        option (google.api.http) = {
            post: "/api/ticket/info"
            body: "*"
        };
    }

    // 工单池 -- 工单已绑定标签
    rpc TicketReTags(TicketIdReq) returns (TicketTagRes) {
        option (google.api.http) = {
            post: "/api/ticket/tags"
            body: "*"
        };
    }

    // 工单池 -- 添加标签
    rpc TicketReTagging(TicketRetaggingReq) returns (Empty) {
        option (google.api.http) = {
            post: "/api/ticket/retagging"
            body: "*"
        };
    }

    // 工单池 -- 公共标签
    rpc TicketPublicTag(TicketPublicTagReq) returns(TicketPublicTagResp){
        option (google.api.http) = {
            post: "/api/ticket/tag/public"
            body: "*"
        };
    }

    // 工单池 -- 批量删除标签
//    rpc TicketTagBatchDelete(TicketTagBatchDelete) returns(Empty){
//        option (google.api.http) = {
//            post: "/api/ticket/tag/batch_delete"
//            body: "*"
//        };
//    }

    // 工单池 -- 添加备注
    rpc TicketAddRemark(TicketRemarkReq) returns (Empty) {
        option (google.api.http) = {
            post: "/api/ticket/remark"
            body: "*"
        };
    }


    // 工单池 -- 添加备注
    rpc TicketBatchRemark(TicketBatchRemarkReq) returns (Empty) {
        option (google.api.http) = {
            post: "/api/ticket/batch_remark"
            body: "*"
        };
    }

    // 工单池 - 工单升级
    rpc TicketUpgrade(TicketUpgradeReq) returns (Empty) {
        option (google.api.http) = {
            post: "/api/ticket/upgrade"
            body: "*"
        };
    }

    // 工单 - 工单流转
    rpc TicketTurn(AssignmentReq) returns (Empty) {
        option (google.api.http) = {
            post: "/api/ticket/turn"
            body: "*"
        };
    }

    // 工单 - 工单状态变更：工单回复|工单关单|工单拒单
    rpc TicketReply(TicketTransferReq) returns (Empty) {
        option (google.api.http) = {
            post: "/api/ticket/transfer"
            body: "*"
        };
    }

    // 客服是否在线
    rpc TicketIsAcceptor(GroupUserReq) returns (TicketIsUserResp) {
        option (google.api.http) = {
            post: "/api/ticket/is_acceptor"
            body: "*"
        };
    }
}
// 精分数据-数据平台
service DataPlatApi{
    // 游戏 item - 道具ID保存
    rpc DataPlatGameItemSave(DataPlatGameItemBatchSaveReq) returns (Empty){
        option (google.api.http) = {
            post: "/api/data_plat/game_item/batch_save"
            body: "*"
        };
    }
    // 游戏 item - 道具ID列表
    rpc DataPlatGameItemList(DataPlatGameItemListReq) returns (DataPlatGameItemListResp){
        option (google.api.http) = {
            post: "/api/data_plat/game_item/list"
            body: "*"
        };
    }
    // 游戏 item - 道具ID列表
    rpc DataPlatGameItemOpts(DataPlatGameItemOptsReq) returns (DataPlatGameItemOptsResp){
        option (google.api.http) = {
            post: "/api/data_plat/game_item/opts"
            body: "*"
        };
    }
    // 精分玩家数据 - 金币查询
    rpc DataPlatUserGoldInfo(DataPlatUserGoldInfoReq) returns (DataPlatUserGoldInfoResp){
        option (google.api.http) = {
            post: "/api/data_plat/user/gold_info"
            body: "*"
        };
    }
    // 精分玩家数据 - 道具查询
    rpc DataPlatUserItemInfo(DataPlatUserItemInfoReq) returns (DataPlatUserItemInfoResp){
        option (google.api.http) = {
            post: "/api/data_plat/user/item_info"
            body: "*"
        };
    }
    // 精分玩家数据 - 支付查询
    rpc DataPlatUserPayInfo(DataPlatUserPayInfoReq) returns (DataPlatUserPayInfoResp){
        option (google.api.http) = {
            post: "/api/data_plat/user/pay_info"
            body: "*"
        };
    }
    // 精分玩家数据 - 登录查询
    rpc DataPlatUserLoginInfo(DataPlatUserLoginInfoReq) returns (DataPlatUserLoginInfoResp){
        option (google.api.http) = {
            post: "/api/data_plat/user/login_info"
            body: "*"
        };
    }
}
// 公共组
service PunlicApi{
    // 基础接口 - 枚举
    rpc AddonsEnum(Empty) returns (Empty) {
        option (google.api.http) = {
            post: "/api/addons/enum"
            body: "*"
        };
    }
    // 语言列表
    rpc AddonsLang(Empty) returns (Empty) {
        option (google.api.http) = {
            post: "/api/addons/lang"
            body: "*"
        };
    }

    // 游戏列表
    rpc AddonsGameList(Empty) returns (Empty) {
        option (google.api.http) = {
            post: "/api/addons/game_list"
            body: "*"
        };
    }
    // 客服列表
    rpc AddonsUserist(Empty) returns (UserListResp) {
        option (google.api.http) = {
            post: "/api/addons/user_list"
            body: "*"
        };
    }
    // discord机器人列表
    rpc AddonsDscBotList(Projects) returns (Empty) {
        option (google.api.http) = {
            post: "/api/addons/dsc_bot_list"
            body: "*"
        };
    }

    // 渠道列表
    rpc AddonsChannelList(Project) returns (Empty) {
        option (google.api.http) = {
            post: "/api/addons/channel"
            body: "*"
        };
    }
    // 渠道+子渠道列表
    rpc AddonsChannelTreeList(Project) returns (Empty) {
        option (google.api.http) = {
            post: "/api/addons/channel_tree"
            body: "*"
        };
    }
    // 渠道+渠道号列表
    rpc AddonsChannelPackageIDList(Project) returns (Empty) {
        option (google.api.http) = {
            post: "/api/addons/channel_package_id"
            body: "*"
        };
    }
}
// 标签库（废弃）
service TagLibApi {
    // 标签列表 - 筛选
    rpc TagOpts(TagOptsReq) returns (TagOptsResp) {
        option (google.api.http) = {
            post: "/api/tags/opts"
            body: "*"
        };
    }

    // 标签库 - 筛选列表接口
    rpc TagLibOpts(TagOptsReq) returns (TagLibOptsResp) {
        option (google.api.http) = {
            post: "/api/tag_lib/opts"
            body: "*"
        };
    }
    // 标签库配置 - 列表接口
    rpc TagLibList(TagLibListReq) returns (TagLibListResp) {
        option (google.api.http) = {
            post: "/api/tag_lib/list"
            body: "*"
        };
    }
    // 标签库配置 - 详情
    rpc TagLibInfo(TagLibID) returns (TagLibInfoResp) {
        option (google.api.http) = {
            post: "/api/tag_lib/info"
            body: "*"
        };
    }

    // 标签库配置 -  新增&修改
    rpc TagLibSave(TagLibSaveReq) returns (TagLibID) {
        option (google.api.http) = {
            post: "/api/tag_lib/save"
            body: "*"
        };
    }
    // 标签库配置 - 修改禁用启用
    rpc TagLibEnable(EnableReq) returns (Empty) {
        option (google.api.http) = {
            post: "/api/tag_lib/enable"
            body: "*"
        };
    }
}

// 新标签库
service AllTagLibApi {
    // 标签列表 - 筛选
    rpc AllTagOpts(TagOptsReq) returns (TagOptsResp) {
        option (google.api.http) = {
            post: "/api/new/tags/opts"
            body: "*"
        };
    }

    // 标签库配置 - 列表接口
    rpc AllTagLibList(TagLibListReq) returns (TagLibListResp) {
        option (google.api.http) = {
            post: "/api/new/tag_lib/list"
            body: "*"
        };
    }
    // 标签库配置 - 详情
    rpc AllTagLibInfo(TagLibID) returns (TagLibInfoResp) {
        option (google.api.http) = {
            post: "/api/new/api/tag_lib/info"
            body: "*"
        };
    }

    // 标签库配置 -  新增&修改
    rpc TagLibSave(AllTagLibSaveReq) returns (TagLibID) {
        option (google.api.http) = {
            post: "/api/new/tag_lib/save"
            body: "*"
        };
    }
    // 标签库配置 - 修改禁用启用
    rpc TagLibEnable(EnableReq) returns (Empty) {
        option (google.api.http) = {
            post: "/api/new/tag_lib/enable"
            body: "*"
        };
    }
}
// 分单配置
service UserAssignTicketApi {
    // 分单配置 - 添加
    rpc UserAssignTicketAdd(UserAssignTicketAddReq) returns (Empty) {
        option (google.api.http) = {
            post: "/api/user_assign_ticket/add"
            body: "*"
        };
    };
    // 分单配置 - 列表
    rpc GroupAdd(UserAssignTicketListReq) returns (UserAssignTicketListResp) {
        option (google.api.http) = {
            post: "/api/user_assign_ticket/list"
            body: "*"
        };
    };

    // 分单配置 - 修改
    rpc GroupInfo(UserAssignTicketEditReq) returns (Empty) {
        option (google.api.http) = {
            post: "/api/user_assign_ticket/edit"
            body: "*"
        };
    };

    // 分单配置 - 详情
    rpc GroupOpts(UserAssignTicketInfoReq) returns (UserAssignTicketListResp.Detail) {
        option (google.api.http) = {
            post: "/api/user_assign_ticket/info"
            body: "*"
        };
    };

    // 分单配置 - 分单逻辑
    rpc AllocGroupEnable(UserAssignTicketDelReq) returns (Empty) {
        option (google.api.http) = {
            post: "/api/user_assign_ticket/del"
            body: "*"
        };
    }
}

// Dc/Line问题分类
service ChannelCatApi {
    // discord 分类 - 获取分类树
    rpc ChannelCatTree (CatProjectReq) returns (ChannelCatTreeResp) {
        option (google.api.http) = {
            post: "/api/channel/cat/tree"
            body: "*"
        };
    }
    // discord 分类 - 添加分类
    rpc ChannelCatAdd (ChannelCatAddReq) returns (Empty) {
        option (google.api.http) = {
            post: "/api/channel/cat/add"
            body: "*"
        };
    }
    // discord 分类 - 修改分类
    rpc DscChannelCatSave (ChannelCatSaveReq) returns (Empty) {
        option (google.api.http) = {
            post: "/api/channel/cat/save"
            body: "*"
        };
    }
    // discord 分类 - 删除分类
    rpc DscChannelCatDel (ChannelCatIdReq) returns (Empty) {
        option (google.api.http) = {
            post: "/api/channel/cat/del"
            body: "*"
        };
    }
}

// 超时模版
service OverTimeApi {
    // 超时模版 - 列表
    rpc OvertimeTplList(OverTimeTplListReq) returns (OverTimeTplListResp) {
        option (google.api.http) = {
            post: "/api/overtime_tpl/list"
            body: "*"
        };
    }
    // 超时模版 - 添加
    rpc OvertimeTplAdd(OverTimeTplAddReq) returns (Empty) {
        option (google.api.http) = {
            post: "/api/overtime_tpl/add"
            body: "*"
        };
    }
    // 超时模版 - 编辑
    rpc OvertimeTplEdit(OverTimeTplEditReq) returns (Empty) {
        option (google.api.http) = {
            post: "/api/overtime_tpl/edit"
            body: "*"
        };
    }
    // 超时模版 - 删除
    rpc OvertimeTplDel(OverTimeTplDelReq) returns (Empty) {
        option (google.api.http) = {
            post: "/api/overtime_tpl/delete"
            body: "*"
        };
    }
    // 超时模版 - 启用/禁用
    rpc OvertimeTplEnable(OverTimeTplEnableReq) returns (Empty) {
        option (google.api.http) = {
            post: "/api/overtime_tpl/enable"
            body: "*"
        };
    }
    // 超时模版 - 编辑
    rpc OvertimeTplOpts(OverTimeTplOptsReq) returns (OverTimeTplOptsRes) {
        option (google.api.http) = {
            post: "/api/overtime_tpl/opts"
            body: "*"
        };
    }
}

// 模版类型分类
service ModuleCatApi {
    // 模版分类 - 添加分类
    rpc ModuleCatAdd (ModuleCatAddReq) returns (Empty) {
        option (google.api.http) = {
            post: "/api/module/cat/add"
            body: "*"
        };
    }
    // 模版分类 - 修改分类
    rpc ModuleCatSave (ModuleCatSaveReq) returns (Empty) {
        option (google.api.http) = {
            post: "/api/module/cat/save"
            body: "*"
        };
    }
    // 模版分类 - 删除分类
    rpc ModuleCatDel (CatIdReq) returns (Empty) {
        option (google.api.http) = {
            post: "/api/module/cat/del"
            body: "*"
        };
    }
    // 模版分类 - 返回分类树
    rpc ModuleCatTree (ModuleCatTreeReq) returns (Empty) {
        option (google.api.http) = {
            post: "/api/module/cat/tree"
            body: "*"
        };
    }
}

// 团队配置
service TeamConfigApi {
    // 团队配置list
    rpc TeamConfigList (TeamConfigListReq) returns (TeamConfigListResp) {
        option (google.api.http) = {
            post: "/team_config/list"
            body: "*"
        };
    }
}

// 工单模版
service TicketModuleApi {
    // 工单模版 - 添加模版
    rpc TicketModuleSave (ModuleSaveReq) returns (Empty) {
        option (google.api.http) = {
            post: "/api/module/save"
            body: "*"
        };
    }
    // 工单模版 - 修改模版
    rpc TicketModuleEdit (ModuleEditReq) returns (Empty) {
        option (google.api.http) = {
            post: "/api/module/edit"
            body: "*"
        };
    }
    // 工单模版 - 删除模版
    rpc TicketModuleDel (ModuleDelReq) returns (Empty) {
        option (google.api.http) = {
            post: "/api/module/del"
            body: "*"
        };
    }
    // 工单模版 - 返回列表
    rpc TicketModuleList (ModuleListReq) returns (ModuleListResp) {
        option (google.api.http) = {
            post: "/api/module/list"
            body: "*"
        };
    }
}
service TicketTabApi{
    // 工单tab编辑
    rpc TicketTabEdit(TicketTabEditReq) returns (Empty) {
        option (google.api.http) = {
            post: "/api/ticket/tab/edit"
            body: "*"
        };
    }
}

service QuestionApi{
    // 工单知识库保存
    rpc QuestionSave(QuestionSaveReq) returns (Empty) {
        option (google.api.http) = {
            post: "/api/v2/question/save"
            body: "*"
        };
    }
    // 工单知识库列表
    rpc QuestionList(QuestionListReq) returns (QuestionListResp) {
        option (google.api.http) = {
            post: "/api/v2/question/list"
            body: "*"
        };
    }
    // 工单知识库下载
    rpc QuestionExport(QuestionListReq) returns (Empty) {
        option (google.api.http) = {
            post: "/api/v2/question/export"
            body: "*"
        };
    }
    // 工单知识库删除
    rpc QuestionDel(QuestionDelReq) returns (Empty) {
        option (google.api.http) = {
            post: "/api/v2/question/del"
            body: "*"
        };
    }
    // 工单知识库批量导入
    rpc QuestionBatchImport(QuestionBatchImportReq) returns (Empty) {
        option (google.api.http) = {
            post: "/api/v2/question/batch_import"
            body: "*"
        };
    }
    // 工单知识库训练
    rpc QuestionTraining(QuestionTrainingReq) returns (Empty) {
        option (google.api.http) = {
            post: "/api/v2/question/training"
            body: "*"
        };
    }
    // 工单知识库训练结果页面
    rpc QuestionTrainLog(QuestionTrainLogReq) returns (QuestionTrainLogResp) {
        option (google.api.http) = {
            post: "/api/v2/question/trainlog"
            body: "*"
        };
    }

}

service TicketStrategyApi{
    // 工单策略保存
    rpc TicketStrategySave(StrategyAddReq) returns (Empty) {
        option (google.api.http) = {
            post: "/api/v2/strategy/save"
            body: "*"
        };
    }
    // 工单策略删除
    rpc TicketStrategyDel(StrategyDelReq) returns (Empty) {
        option (google.api.http) = {
            post: "/api/v2/strategy/del"
            body: "*"
        };
    }
    // 工单策略禁/启用
    rpc TicketStrategyEnable(StrategyEnabelReq) returns (Empty) {
        option (google.api.http) = {
            post: "/api/v2/strategy/enable"
            body: "*"
        };
    }
    // 工单策略列表
    rpc TicketStrategyList(StrategyListReq) returns (StrategyListResp) {
        option (google.api.http) = {
            post: "/api/v2/strategy/list"
            body: "*"
        };
    }
}

