syntax = "proto3";

package pb;
option go_package = ".;pb";

enum DscEvTpDf {
    // 未知事件
    DscEvTpDfUnknown = 0;
    // 消息 - 新增
    DscEvTpDfMsgAdd = 1;
    // 消息 - 编辑
    DscEvTpDfMsgEdit = 2;
    // 消息 - 删除
    DscEvTpDfMsgDel = 3;
    // 反应 - 新增
    DscEvTpDfReactionAdd = 4;
    // 反应 - 删除
    DscEvTpDfReactionDel = 5;
}

enum DscMsgTpDf {
    // 未知消息
    DscMsgTpDfUnknown = 0;
    // 文本消息
    DscMsgTpDfText = 1;
    // 附件消息
    DscMsgTpDfAttach = 2;
    // Embed消息
    DscMsgTpDfEmbed = 3;
}

enum DscMsgEmbedTpDf {
    // 未知Embed消息
    DscMsgEmbedTpDfUnknown = 0;
    // 富文本 - 	generic embed rendered from embed attributes
    Rich = 1;
    // 图片 - image
    Image = 2;
    // 音频 - video embed
    video = 3;
    // 动图 - animated gif image embed rendered as a video embed
    Gifv = 4;
    //article - article embed
    Article = 5;
    //link - link embed
    Link = 6;
}

enum DscReplyTpDf {
    // 未知状态
    DscReplyTpDfUnknown = 0;
    // 未回复
    DscReplyTpDfUnReply = 1;
    // 已回复
    DscReplyTpDfReplied = 2;
}
enum DscMsgFromTpDf{
    // 未知来源
    DscMsgFromTpDfUnknown = 0;
    // 用户
    DscMsgFromTpDfUser = 1;
    // 机器人
    DscMsgFromTpDfRobot = 2;
}
enum DscMsgTaskStatus{
    //未开始
    ProcessStatusInit = 0;
    //处理中
    ProcessStatusDoing = 10;
    //处理失败
    ProcessStatusFail = 20;
    //处理成功
    ProcessStatusSuccess = 30;
}

enum DscMsgTaskDetailStatus{
    // 未知消息
    DscTaskDetailStatusUnknown = 0;
    //未发送
    DscTaskDetailStatusDoing = 10;
    //失败
    DscTaskDetailStatusFail = 20;
    //成功
    DscTaskDetailStatusSuccess = 30;
}