syntax = "proto3";

package pb;
option go_package = ".;pb";

import "google/api/annotations.proto";
import "discord_enum.proto";
import "enum.proto";
import "dsc.proto";
import "common.proto";

// discord功能实现 - 接口 API
service DscApi {
    // discord 绑定玩家 - 列表查询
    rpc DscUserList(DscUserListReq) returns (DscUserListResp) {
        option (google.api.http) = {
            post: "/api/dsc/user/pool"
            body: "*"
        };
    }
    // discord 绑定玩家 - 详情查询 undo
    rpc DscUserDetail(DscUserDetailReq) returns (DscUserDetailResp) {
        option (google.api.http) = {
            post: "/api/dsc/user/detail"
            body: "*"
        };
    }
    // discord 玩家对话 - 会话历史
    rpc DscChannelDialog (DscChannelDialogReq) returns (DscDialogDetailResp) {
        option (google.api.http) = {
            post: "/api/dsc/channel/dialog"
            body: "*"
        };
    }
    // discord 玩家对话 - 新增会话+事件
    rpc DscChannelDialogFresh (DscChannelDialogFreshReq) returns (DscChannelDialogFreshResp) {
        option (google.api.http) = {
            post: "/api/dsc/channel/dialog_fresh"
            body: "*"
        };
    }
    // discord 消息 - 发送/回复消息
    rpc DscReplyMessage (DscChannelMsgCreateReq) returns (DscChannelMsgCreateResp) {
        option (google.api.http) = {
            post: "/api/dsc/channel/message_create"
            body: "*"
        };
    }
    // discord 消息 - 修改消息
    rpc DscEditMessage (DscChannelMsgEditReq) returns (DscChannelMsgCreateResp) {
        option (google.api.http) = {
            post: "/api/dsc/channel/message_edit"
            body: "*"
        };
    }
    rpc DscSendFile (DscChannelFileCreateReq) returns (DscDialogDetail) {
        option (google.api.http) = {
            post: "/api/dsc/channel/send_file"
            body: "*"
        };
    }
    rpc DscPortraitInfo(PortraitInfoReq) returns (PortraitInfoResp) {
        option (google.api.http) = {
            post: "/api/dsc/portrait/info"
            body: "*"
        };
    }
    rpc DscPortraitEdit(PortraitEditReq) returns (Empty) {
        option (google.api.http) = {
            post: "/api/dsc/portrait/edit"
            body: "*"
        };
    }
    rpc DscTabCount(Empty) returns (DiscordTabCountResp) {
        option (google.api.http) = {
            post: "/api/dsc/tab/count"
            body: "*"
        };
    }
    rpc DscNewCommuSave(DiscordNewCommuRecordAddReq) returns (Empty) {
        option (google.api.http) = {
            post: "/api/new/commu/save"
            body: "*"
        };
    }
    rpc DscNewCommuEdit(DiscordNewCommuRecordEditReq) returns (Empty) {
        option (google.api.http) = {
            post: "/api/new/commu/edit"
            body: "*"
        };
    }
    rpc DscNewCommuList(DiscordNewCommuRecordListReq) returns (DiscordNewCommuRecordListResp) {
        option (google.api.http) = {
            post: "/api/new/commu/list"
            body: "*"
        };
    }
    rpc DscTabEdit(DiscordTabEditReq) returns (Empty) {
        option (google.api.http) = {
            post: "/api/dsc/tab/edit"
            body: "*"
        };
    }

    // Discord -- 公共标签
    rpc DscPublicTag(DiscordPublicTagReq) returns(DiscordPublicTagResp){
        option (google.api.http) = {
            post: "/api/dsc/tag/public"
            body: "*"
        };
    }

    // Discord -- 批量删除标签
    rpc DscTagBatchDelete(DiscordTagBatchDelete) returns(Empty){
        option (google.api.http) = {
            post: "/api/dsc/tag/batch_delete"
            body: "*"
        };
    }
}

message DscUserListReq{
    // 游戏
    repeated string project = 1;
    // 回复时间
    repeated string replied_at = 2;
    // 状态 - 1:未回复、2:已回复
    repeated uint32 status = 3;
    // 玩家输入信息 - 模糊查询
    string user_content = 4;
    // 备注信息 - 玩家备注信息 - 模糊查询
    string user_detail_remark = 5;
    // 维护人
    repeated string processor = 6;
    // 玩家 DC昵称
    repeated string dsc_user_nickname = 7 ;
    // 玩家 DC ID
    string dsc_user_id = 8;
    uint64 uid = 9;
    string fpid = 10;
    string sid = 11;
    // 最近登录时间
    repeated string last_login = 12;
    // 累计付费金额
    repeated int64 pay_all = 13;
    // 最近30天付费金额
    repeated int64 pay_last_thirty_days = 14;
    // 玩家VIP状态
    uint32 vip_state = 15;
    // 玩家画像标签--弃用
    repeated string tag = 16;
    repeated string birthday = 17;
    string lang = 18;
    // 最近处理人
    repeated string last_reply_service = 19;
    // 排序字段(废弃)
    string sort_field = 20;
    // 排序顺序 asc升序，desc降序
    string order = 21;
    // @gotags: validate:"required"
    uint32 page = 22;
    // @gotags: validate:"required"
    uint32 page_size = 23;
    // 机器人bot_id
    repeated string bot_ids = 24;
    // 新用字段
    string uids = 25;
    // 新用标签
    repeated uint32 tags = 26;
    // 标签类型
    FilterTagEnum tag_type = 27;
    // 排序方式
    DcPoolSort sort_by = 28;
    // 工单语言
    repeated string language = 29;
}
message DscUserListResp{
    message DscUser{
        // 游戏
        string project = 1;
        // 玩家 DC ID
        string dsc_user_id = 2;
        // 玩家名称 - 前端展示使用此字段
        string user_name = 3;
        // 玩家 global name
        string global_name = 4;
        // dm channel
        string dm_channel = 5;
        // guild id
        string guild_id = 6;
        // 当前维护人
        string processor = 7;
        // 累付金额
        double total_pay = 8;
        // 最近30天付费金额
        double pay_last_thirty_days = 9;
        // 最近登录时间
        string last_login = 10;
        // 玩家信息回复状态
        uint32 status = 11;
        // VIP 等级
        uint32 vip_level = 12;
        string fpid = 13;
        uint64 uid = 14;
        string sid = 15;
        // 机器人 user_id
        string bot_id = 16;
        // 机器人昵称
        string bot_show = 19;
        string note = 17;
        string player_nick = 18;
        string birthday = 22;
        string lang = 20;
        bool checked = 21;
        // 客服最后回复时间
        string last_reply_time = 24;
        // 玩家等待时长
        string waiting_time = 25;
        // 七天内工单创建个数
        int64 ticket_create_count = 26;
        // 七天内最后工单创建时间
        string last_ticket_create_time = 27;
        // 七天内创建最近工单ID
        uint64 ticket_id = 28;
    }

    uint32 current_page = 1;
    uint32 per_page = 2;
    uint32 total = 3;
    repeated DscUser data = 4;
}

message DscUserDetailReq{
    // 玩家 DC ID
    string dsc_user_id = 1;
}

message DscUserDetailResp{
    message UserDetail {
        // 游戏
        string project = 1;
        // 玩家 DC ID
        string dsc_user_id = 2;
        // 玩家名称 - 前端展示使用此字段
        string user_name = 3;
        // 玩家 global name
        string global_name = 4;
        // dm channel
        string dm_channel = 5;
        // guild id
        string guild_id = 6;
        // 当前维护人
        string processor = 9;
    }
    // 基本信息
    UserDetail user_detail = 10;
}

message DscChannelDialogReq{
    // 玩家 DC ID - 必传
    string channel_id = 1;
    // 向上翻页 - 开始消息id - 对应 msg_id
    string before = 2;
    // 向下翻页 - 开始消息id - 对应 msg_id
    string after = 3;
    // 单页限定条数 -  默认 50条
    int64 limit = 4;

    // 指定查询 msg_id
    repeated string msg_ids = 5;
}

message DscDialogDetailResp{
    repeated DscDialogDetail dialog_list = 1;
}

message DscDialogDetail {
    // 消息 id
    string msg_id = 1;
    // project
    string project = 2;
    // 消息来源id
    string from_user_id = 3;
    // dm channel_id
    string channel_id = 4;
    // 消息类型：DscMsgTpDf
    //    DscMsgTpDf dsc_msg_type = 5;

    // 消息时间格式
    string created_at = 6;
    // 消息是否被修改
    bool is_edited = 7;
    // author : 消息发送人详情
    DscAuthor author = 8;

    // 文本消息 - 内容
    string content = 9;
    // 附件消息 - 附件列表
    repeated DscMsgAttach attach = 10;
    // embeds - any embedded content
    repeated DscMsgEmbed embed = 11;
    // 贴纸 - sticker_items
    repeated DscMsgSticker stickers = 16;
    // 所有反应
    repeated DscMsgReaction reactions = 12;
    // 投票 @gotags: json:"poll,omitempty"
    DscMsgPoll poll = 13;
    // 针对特定一条消息回复
    string referenced_msg_id = 14;
    // 针对特定一条消息回复-原消息详情 @gotags: json:"referenced_msg,omitempty"
    DscDialogDetail referenced_msg = 15;
    bool checked = 17;
}

message DscMsgSticker {
    // 贴纸id
    string id = 1;
    // 贴纸name
    string name = 2;
    // 贴纸 类型：StickerFormat： 1: PNG, 2: APNG, 3: LOTTIE， 4: GIF ： 参考：https://discord.com/developers/docs/resources/sticker#sticker-object-sticker-format-types
    int32 format_type = 3;
}

message DscMsgReaction{
    // 消息id
    string msg_id = 1;
    // 表情
    string name = 2;
    // 添加表情的用户 user_id
    string user_id = 3;
    // 用户名称
    string user_name = 4;
    // 表情添加时间
    string created_at = 5;
}

message DscMsgAttach{
    // 附件id
    string id = 1;
    // 附件url
    string url = 2;
    // 附件代理url
    string proxy_url = 3;
    // 附件名称
    string filename = 4;
    // 附件类型: the attachment's media type : https://en.wikipedia.org/wiki/Media_type
    string content_type = 5;
    // 附件宽度
    int32 width = 6;
    // 附件高度
    int32 height = 7;
    // 附件大小
    int32 size = 8;
    // 是否是临时附件
    bool ephemeral = 9;
}

message DscMsgEmbed {
    message Provider {
        // 来源地址
        string url = 1;
        // 来源名称
        string name = 2;
    }
    message Thumbnail {
        // url
        string url = 1;
        // proxy url
        string proxy_url = 2;
        // width
        int32 width = 3;
        // height
        int32 height = 4;
    }
    message Video {
        string url = 1;
        int32 width = 2;
        int32 height = 3;
    }

    // 标题
    string title = 1;
    // type: "rich", "image", "video", "gifv", "article", "link"
    string type = 2;
    // 描述
    string description = 3;
    // url
    string url = 4;
    // color
    int64 color = 5;
    // 来源标识：MessageEmbedProvider is a part of a MessageEmbed struct.
    Provider provider = 6;
    // MessageEmbedThumbnail is a part of a MessageEmbed struct.
    Thumbnail thumbnail = 7;
    // MessageEmbedVideo is a part of a MessageEmbed struct.
    Video video = 8;
}
message DscMsgPoll {
    // 问题描述
    string question_text = 1;
    // 答案列表
    repeated DscMsgPollAnswer answers = 2;
    // 是否允许多选
    bool allow_multiselect = 3;
    // NOTE: should be set only on creation, when fetching use Expiry.
    int32 duration = 4;
    // NOTE: as Discord documentation notes, this field might be null even when fetching.
    uint64 expiry = 5;
    // results
    DscMsgPollResult results = 6;
}
message DscMsgPollResult {
    // 是否已经过期
    bool is_finalized = 1;
    // 投票结果
    repeated DscMsgPollAnswerCount answer_count = 2;
}
message DscMsgPollAnswerCount {
    // 答案 id
    uint32 id = 1;
    // 答案数量
    uint32 count = 2;
    // 是否我投票
    bool me_voted = 3;
}
message DscMsgPollAnswer {
    // 答案 id
    uint32  answer_id = 1;
    // 答案描述
    string answer_text = 2;

}
message DscAuthor {
    // dsc_user_id-用户id
    string  id = 1;
    // 用户名称
    string username = 2;
    // 用户头像
    string avatar = 3;
    // global name
    string global_name = 4;
    // 是否是机器人
    bool bot = 5;
}
message DscChannelMsgCreateReq{
    // 玩家 DC_CHANNEL_ID-必传 @gotags: validate:"required"
    string channel_id = 1;
    // 机器人信息  @gotags: validate:"required"
    string bot_id = 2;
    // 回复内容 @gotags: validate:"required"
    string content = 3;
    // 是否强制忽略 回复状态: 1不调整回复状态
    int32 ignore_state = 4;
}
message DscChannelMsgCreateResp{
    // 回复消息id
    string msg_id = 1;
    // 发送渠道
    string channel_id = 2;
}

message DscChannelMsgEditReq{
    // 玩家 DC_CHANNEL_ID-必传 @gotags: validate:"required"
    string channel_id = 1;
    // 机器人信息  @gotags: validate:"required"
    string bot_id = 2;
    // 消息id @gotags: validate:"required"
    string msg_id = 3;
    // 回复内容 @gotags: validate:"required"
    string content = 4;
}

message DscChannelFileCreateReq{
    // 玩家 DC_CHANNEL_ID-必传 @gotags: validate:"required"
    string channel_id = 1;
    // 机器人信息 @gotags: validate:"required"
    string bot_id = 2;
}

message DscChannelDialogFreshReq{
    // 玩家 DC ID - 必传
    string channel_id = 1;
    // 向下翻页 - 开始消息id - 对应 msg_id
    string after = 2;
}

message DscDialogFreshEvent {
    // 事件类型：详细看 DscEvTpDf
    DscEvTpDf event_type = 1;
    // 新增/修改消息-详情 @gotags: json:"msg_detail,omitempty"
    DscDialogDetail msg_detail = 2;
    // 删除消息 - 具体详情 @gotags: json:"del_msg_ids,omitempty"
    repeated string del_msg_ids = 3;
    // 新增表情 - 详细信息 @gotags: json:"add_reaction,omitempty"
    repeated DscMsgReaction add_reaction = 4;
    // 删除表情 - 详情 @gotags: json:"del_reaction,omitempty"
    repeated DscMsgReaction del_reaction = 5;
}

message DscChannelDialogFreshResp{
    // 新增会话列表
    repeated DscDialogDetail dialog_list = 1;
    // 新增事件数据
    repeated DscDialogFreshEvent fresh_event = 2;
}

message DscPoolInfo{
    // 游戏
    string project = 1;
    // 玩家 DC ID
    string dsc_user_id = 2;
    // 玩家名称 - 前端展示使用此字段
    string user_name = 3;
    // 玩家 global name
    string global_name = 4;
    // dm channel
    string dm_channel = 5;
    // guild id
    string guild_id = 6;
    // 当前维护人
    string processor = 7;
    string bot_id = 8;
    uint64 uid = 9;
    string account_id = 10;
    // 服务器
    string sid = 11;
    // 最近登录时间
    string last_login = 12;
    // 累计付费金额
    int64 pay_all = 13;
    // 最近30天付费金额
    int64 pay_last_thirty_days = 14;
    uint32 status = 15;
    uint32 vip_level = 16;
    string note = 17;
    string player_nick = 18;
    string birthday = 19;
    string lang = 20;
    // 客服最后回复时间
    string last_reply_time = 21;
    string waiting_time = 22;
    int64 ticket_create_count = 23;
    string last_ticket_create_time = 24;
    uint64 ticket_id = 25;
}

message DiscordStatsReq {
    repeated string project = 1;
}

message DiscordStatsResp {
    int64 discord_user_count = 1;
    int64 wait_reply_accounts = 2;
    int64 mine_wait_reply_accounts = 3;
}

message DscProxyResourceReq {
    // @gotags: validate:"required"
    string url = 1;
    // 仅代理
    bool only_proxy = 2;
}

message DiscordPlayerInteractStatsReq {
    repeated string project = 1;
    repeated string date = 2;
    repeated string operator = 3;
}

message DiscordMessageCountReq {
    repeated string project = 1;
    repeated string date = 2;
    repeated string operator = 3;
    repeated int64 uid = 4;
}

message DiscordMessageCountResp {
    message MessageCountDetail {
        string row_name = 1;
        int64 player_message_count = 2;
        int64 service_message_count = 3;
    }
    repeated MessageCountDetail data = 1;
}

message DiscordPlayerUidReq {
    // @gotags: validate:"required"
    repeated string project = 1;
    // 搜索值
    int64 uid = 2;
}

message DscMsgBatchSendReq{
    // 玩家uid列表 @gotags: validate:"required"
    repeated int64 uid_list = 1;
    // 发送消息的游戏标识game_project @gotags: validate:"required"
    string project = 3;
    // 私信内容 @gotags: validate:"required"
    string content = 4;
}

message DscUserChannelDf {
    // app_id bot_id 机器人id
    string app_id = 1;
    // 玩家 dsc 账号 id
    string dsc_user_id = 2;
    // 消息收发通道
    string priv_channel_id = 3;
}
message DscMsgBatchSendResp {
    message MessageSendDetail {
        // 私信消息id
        string msg_id = 1;
        // 发送渠道
        string channel_id = 2;
    }
    repeated MessageSendDetail data = 1;
    int32 success = 2;
    repeated int64 failed_uid_list = 3;
}

message DscFileBatchSendReq{
    // 玩家uid列表 @gotags: validate:"required"
    repeated int64 uid_list = 1;
    // 发送消息的游戏标识game_project @gotags: validate:"required"
    string project = 3;
    // 文件url  @gotags: validate:"required"
    string file_url = 4;
}

message DscAscBatchSendReq{
    // 玩家uid列表 @gotags: validate:"required"
    repeated int64 uid_list = 1;
    // 发送消息的游戏标识game_project @gotags: validate:"required"
    string project = 3;
    // 文件url
    string file_url = 4;
    // 私信内容
    string content = 5;
}

message DscUserRemarkAddReq {
    //  @gotags: validate:"required"
    string channel_id = 1;
    // 备注
    string note = 2;
}

message DiscordTabAddReq {
    // @gotags: validate:"required"
    string tab_name = 1;
    // @gotags: validate:"required,oneof=1 2"
    int32 public = 2;
    // 搜索条件组合 @gotags: validate:"required"
    DscUserListReq detail = 3;
}

message DiscordTabEditReq {
    // @gotags: validate:"required"
    int64 id = 1;
    // @gotags: validate:"required"
    string tab_name = 2;
    // @gotags: validate:"required,oneof=1 2"
    int32 public = 3;
}

message DiscordTabDelReq {
    // @gotags: validate:"required"
    int64 id = 1;
}

message DiscordTabListResp {
    message DiscordTabDetail {
        repeated TabInfo tab = 1;
        string project = 2;
    }
    message TabInfo{
        int64 id = 1;
        string tab_name = 2;
        DscUserListReq detail = 3;
        int32 public = 4;
        string operator = 5;
    }
    repeated DiscordTabDetail data = 1;
}

message DiscordReplyTimeReq {
    repeated string project = 1;
    repeated string date = 2;
    repeated uint64 tag_ids = 3;
}

message DiscordReplyTimeResp {
    message ReplyTimeCountDetail {
        string row_name = 1;
        int64 reply_count_detail = 2;
        double reply_count_rate = 3;
    }
    message ReplyTimeAvgDayDetail{
        string date = 1;
        double avg_reply_time = 2;
    }
    repeated ReplyTimeCountDetail reply_count_data = 1;
    repeated ReplyTimeAvgDayDetail reply_avg_data = 2;
}

message DiscordReplyStatusRectifyReq {
    // @gotags: validate:"required"
    string channel_id = 1;
    // @gotags: validate:"required"
    string dsc_user_id = 2;
    // @gotags: validate:"required,oneof=1 2"
    uint32 old_reply_status = 3;
    // @gotags: validate:"required,oneof=1 2"
    uint32 new_reply_status = 4;
}

message DiscordTabCountResp {
    message DiscordTabCount {
        repeated TabCountDetail tab = 1;
        string project = 2;
    }
    message TabCountDetail{
        string tab_name = 2;
        uint64 count = 5;
    }
    repeated DiscordTabCount detail = 1;
}

message DiscordTabUpdateSortReq {
    // @gotags: validate:"required"
    string sort_setting = 1;
}