syntax = "proto3";

package pb;
option go_package = ".;pb";

import "google/api/annotations.proto";
import "common.proto";



message GetCatTitlesReq {
    repeated int32  cat_ids = 1; // 项目id
    string lang = 2;
    string game_project = 3; // 游戏项目id
}


message GetCatTitlesResp {
    repeated CatTitle cat_titles = 1; // 分类标题
}
message CatTitle {
    int32 cat_id = 1; // 分类id
    string cat_name = 2; // 分类名称
}


service CategoryInnerAPI {
    // 分类获取title
    rpc GetCatTitles(GetCatTitlesReq) returns (Empty) {
        option (google.api.http) = {
            post: "/inner/api/cat/titles"
            body: "*"
        };
    }
}