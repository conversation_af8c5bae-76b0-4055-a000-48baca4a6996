syntax = "proto3";

package pb;
option go_package = ".;pb";

// TagLibOptsResp 标签组列表
message TagLibOptsResp {
    message TagLib {
        // 标签ID
        uint32 lib_id = 1;
        // 标签名称
        string lib_name = 2;
    }
    repeated TagLib data = 1;
}

// TagLibListReq 标签库列表搜索
message TagLibListReq{
    // 标签库名称
    string lib_name = 1;
    // 标签类型 @gotags: validate:"required,oneof=1 2 3"
    uint32 lib_type = 5;
}


// TagLibIDReq 标签库ID请求参数
message TagLibID {
    // 标签库ID @gotags: validate:"required"
    uint32 lib_id = 1;
}

// TagLibListResp 标签库列表响应结果
message TagLibListResp {
    message TagLib {
        // 标签库ID
        uint32 lib_id = 1;
        // 标签库名称
        string lib_name = 2;
        // 标签数据 - 原始文件地址
        string tag_upload_file = 3;
        // 更新时间
        string updated_at = 5;
        // 更新用户
        string op = 6;
        // 状态
        bool enable = 7;
        // 项目列表 @gotags: gorm:"-"
        repeated string projects = 9;
    }
    repeated TagLib data = 1;
}

// TagLibInfoResp 标签库信息
message TagLibInfoResp {
    // 标签库ID
    uint32 lib_id = 1;
    // 标签库名称
    string lib_name = 2;
    // 标签库描述
    string lib_file_url = 3;
    // 标签数据 - 原始文件地址
    repeated string projects = 4;
    // @gotags: json:"project"
    repeated string project = 5;
}


// GroupAddReq 添加标签库请求参数
message TagLibAddReq {
    // 标签库名称 @gotags: validate:"required"
    string lib_name = 1;
    // 标签数据 - 原始文件地址 @gotags: validate:"required"
    string lib_file_url = 2;
    // 关联游戏 @gotags: validate:"required,gt=0"
    repeated string projects = 3;
}

// TagLibSaveReq 更新标签库请求参数
message TagLibSaveReq {
    // 标签库ID
    uint32 lib_id = 1;
    // 标签库名称 @gotags: validate:"required"
    string lib_name = 2;
    // 标签数据-原始文件地址 @gotags: validate:"required"
    string lib_file_url = 3;
    // 关联游戏 @gotags: validate:"required,gt=0"
    repeated string projects = 4;
}


// TagLibSaveReq 更新标签库请求参数
message AllTagLibSaveReq {
    // 标签库ID
    uint32 lib_id = 1;
    // 标签库名称 @gotags: validate:"required"
    string lib_name = 2;
    // 标签数据-原始文件地址 @gotags: validate:"required"
    string lib_file_url = 3;
    // 关联游戏 @gotags: validate:"required,gt=0"
    repeated string projects = 4;
    // 标签类型 @gotags: validate:"required,oneof=1 2 3"
    uint32 lib_type = 5;
}

message TagOptsReq {
    // 项目名称-根据项目名称进行搜索 @gotags: validate:"required"
    string project_name = 1;
    // 标签库ID - 根据标签库ID进行搜索
    uint32 lib_id = 2;
    // 标签类型 @gotags: validate:"required,oneof=1 2 3"
    uint32 lib_type = 5;
}


// TagOptsResp 标签列表
message TagOptsResp {
    message Tag {
        // 标签ID
        uint32 tag_id = 1;
        // 标签名称
        string tag_name = 2;
        // 级别
        uint32 level = 3;
        // 启用
        bool enable = 4;
        bool disable = 5;
        // 子结构 @gotags: json:"children,omitempty"
        repeated Tag children = 6;
    }
    repeated Tag data = 1;
}


// GroupListReq 团队分单列表请求参数
message GroupListReq {
    // 团队名称
    string group_desc = 2;
    // user
    string user = 3;
    // 页码
    uint32 page = 4;
    // 页大小
    uint32 page_size = 5;
}

// GroupListResp 技能组列表】响应结果
message GroupListResp {
    message Group {
        // 团队 user的id
        uint32 id = 1;
        // 团队id
        uint32  group_id = 2;
        // 团队名称
        string group_desc = 3;
        // 人员列表
        //        repeated string user = 4;
        string user = 4;
        // 游戏列表
        repeated string game = 5;
        // 更新时间
        string updated_at = 6;
        // 更新用户
        string operator = 7;
        // 状态
        bool enable = 8;
        // 语言
        repeated string language = 9;
        // 技能组接单上限
        uint32 upper_limit = 10;
    }
    // 当前页
    uint32 current_page = 1;
    // 页大小
    uint32 per_page = 2;
    // 总数
    uint32 total = 3;
    // 数据列表
    repeated Group data = 4;
}

//
//// GroupSaveReq 修改技能组 请求参数
//message GroupSaveReq {
//    // 技能组ID id > 0 为修改
//    uint32 group_id = 1;
//    // 团队名称 @gotags: validate:"required"
//    string group_desc = 2;
//    // 人员列表 @gotags: validate:"required,gt=0"
//    repeated string user = 3;
//    // 游戏列表 @gotags: validate:"required,gt=0"
//    repeated string game = 4;
//    // 语言 @gotags: validate:"required"
//    string language = 5;
//    // 问题分类
//    string categories = 6;
//    // 技能组接单上限 @gotags: validate:"required,gt=0"
//    uint32 upper_limit = 7;
//}

// GroupsIdReq 技能组ID 请求参数
message GroupIdReq {
    // 技能组ID @gotags: validate:"required"
    uint32 group_id = 1;
}


// GroupEditReq 技能组信息 响应结果
message GroupInfoResp {
    // 技能组ID
    uint32 group_id = 1;
    // 团队名称
    string group_desc = 2;
    // 人员列表
    repeated string user = 3;
    //    string user = 3;
    // 游戏列表
    repeated string game = 4;
    //    string game = 4;
    // 语言
    repeated string language = 5;
    // 问题分类
    repeated string categories = 6;
    // 技能组接单上限
    int32 upper_limit = 7;
}

// 客服在线状态请求参数
message GroupUserReq {
    string account = 1;
    uint32 status = 2; //1:在线  2:离线
}
// 客服在线状态响应参数
message GroupUserResp {
    string status = 1;
}

// 删除分单定义
message GroupDelReq {
    uint32 id = 1;
    string user = 2;
}


// 更新分单定义
message GroupSaveReq {
    uint32 group_id = 1; // 组id
    string group_desc = 2;  // 团队
    int32 upper_limit = 3; // 接单上限
    int32 id = 4; // 更新人员Id
    repeated string game = 5; // 游戏
    repeated string language = 6; // 语言
    repeated string user = 7;  // 人员
}


// TagConfigReq 标签配置请求参数
message TagConfigAddReq {
    // 标签库ID @gotags: validate:"required"
    uint32 lib_id = 1;
    // 级别 @gotags: validate:"required"
    uint32 level = 2;
    // 父级ID
    uint32 pid = 3;
    // 标签名称 @gotags: validate:"required"
    string tag_name = 4;
}


// TagInfoReq 标签信息响应参数
message TagConfigInfoResp {
    // 标签ID
    uint32 tag_id = 1;
    // 标签名称
    string tag_name = 2;
    // 级别
    uint32 level = 3;
}

// TagConfigEditReq 标签配置修改
message TagConfigEditReq {
    // @gotags: validate:"required"
    uint32 tag_id = 1;
    // @gotags: validate:"required"
    string tag_name = 2;
}