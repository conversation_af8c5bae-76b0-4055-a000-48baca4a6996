syntax = "proto3";

package pb;
option go_package = ".;pb";
import "common.proto";
import "google/api/annotations.proto";

// DCSBotConfigService DC机器人配置服务
service DCSBotConfigService {
    // 新增DC机器人配置
    rpc AddDCSBotConfig(DCSBotConfigAddReq) returns (Empty) {
      option (google.api.http) = {
        post: "/api/dsc_bot_config/add"
        body: "*"
      };
    }

    // 验证DC机器人配置
    rpc CheckDCSBotConfig(DCSBotConfigAddReq) returns (Empty) {
      option (google.api.http) = {
        post: "/api/dsc_bot_config/check"
        body: "*"
      };
    }

    // 更新DC机器人配置欢迎消息
    rpc UpdateDCSBotConfigWelcomeMessage(UpdateDCSBotConfigWelcomeMessageReq) returns (Empty) {
      option (google.api.http) = {
        post: "/api/dsc_bot_config/update_welcome_message"
        body: "*"
      };
    }
  
    // 获取DC机器人配置列表
    rpc GetDCSBotConfigList(DCSBotConfigListReq) returns (DCSBotConfigListResp) {
      option (google.api.http) = {
        post: "/api/dsc_bot_config/list"
        body: "*"
      };
    }
  }

// DCSBotConfigAddReq 新增DC机器人配置
message DCSBotConfigAddReq {
  // 项目名称 @gotags: validate:"required"
  string project = 1;
  // DC账号
  string dsc_name = 2;
  // 机器人描述
  string bot_desc = 3;
  // Application ID
  string app_id = 4;
  // 机器人配置信息 @gotags: validate:"required"
  DscBotConfig bot_config = 5;
  // 用户ID 
  string user_id = 6;
  // 用户名称
  string username = 7;
  // 标识
  string discriminator = 8;
}

// DCSBotConfigListResp DC机器人配置列表响应
message DCSBotConfigListResp {
  repeated DscBotDetail data = 1;
  uint32 total = 2;
  uint32 current_page = 3;
  uint32 per_page = 4;
}


message DscBotDetail {
  // ID
  uint64 id = 1;
  // 项目名称
  string project = 2;
  // DC账号
  string dsc_name = 3;
  // 机器人描述
  string bot_desc = 4;
  // Application ID
  string app_id = 5;
  // 机器人配置信息
  DscBotConfig bot_config = 6;
  // 是否删除
  bool is_delete = 7;
  // 用户ID
  string user_id = 8;
  // 用户名称
  string username = 9;
  // 标识
  string discriminator = 10;
  // 操作人
  string updated_by = 11;
  // 创建时间
  string created_at = 12;
  // 更新时间
  string updated_at = 13;
  // 机器人状态 0处理中，1已监听，2异常
  uint32 bot_status = 14;
}

// DscBotConfig Discord机器人配置
message DscBotConfig {
  // Application ID
  string client_id = 1;
  // Public Key
  string public_key = 2;
  // Bot Token
  string bot_token = 3;
  // Sever ID
  string guild_id = 4;
  // 服务器名称
  string guild_desc = 5;
  // 项目名称
  string project = 6;
  // 欢迎消息
  string welcome_message = 7;
}

// DCSBotConfigListReq DC机器人配置列表请求
message DCSBotConfigListReq {    
  uint32 page = 1;
  uint32 page_size = 2;
}


// UpdateDCSBotConfigWelcomeMessageReq 更新DC机器人配置欢迎消息
message UpdateDCSBotConfigWelcomeMessageReq {
  // 机器人ID
  uint64 id = 1;
  // 欢迎消息
  string welcome_message = 2;
}
