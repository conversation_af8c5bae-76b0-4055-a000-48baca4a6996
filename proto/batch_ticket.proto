syntax = "proto3";

package pb;
option go_package = ".;pb";

import "ticket.proto";

// TicketBatchTagReq 工单批量打标签
message TicketRetaggingBatchReq {
  // 工单ID @gotags: validate:"required"
  repeated uint64 ticket_ids = 1;
  // 要打的标签ID集合 @gotags: validate:"required"
  repeated uint32 label_id = 2;
}

message TicketBatchTransferReq {
  // 工单ID @gotags: validate:"required"
  repeated uint64 ticket_ids = 1;
  // 操作分类：1:客服回复;2:回复&关单;3:拒单 @gotags: validate:"required,gt=0"
  TkTransferOpType op_type = 2;
  // 回复内容
  string content = 3;
  // 是否同步到ai精灵
  bool is_sync_ai_elfin = 4;
  // 问题
  string question = 5;
  // 答案
  string answer = 6;
}

// TicketPublicTagReq 工单公共标签
message TicketPublicTagReq {
  // 工单ID @gotags: validate:"required"
  repeated uint64 ticket_ids = 1;
}

message TicketPublicTagResp {
  message TagInfo{
    // 标签ID
    uint32 tag_id = 1;
    // 标签名称
    string tag_name = 2;
  }
  repeated TagInfo tags = 1;
}

message TicketTagBatchDelete{
  // 待删除工单ID @gotags: validate:"required"
  repeated uint64 ticket_ids = 1;
  // 待删除标签ID @gotags: validate:"required"
  repeated uint32 tag_ids = 2;
}