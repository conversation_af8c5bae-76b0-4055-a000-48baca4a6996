syntax = "proto3";

package pb;
option go_package = ".;pb";

import "google/api/annotations.proto";
import "egress_ticket.proto";
import "common.proto";
import "enum.proto";

// 对外服务接口-老工单接口实现
service EgressAPI {
    // 工单 - sdk - 红点v2
    rpc TkSdkNoticeV2(NoticeRequestV2) returns (NoticeResp) {
        option (google.api.http) = {
            post: "/api/v2/notice"
            body: "*"
        };
    }

    // 工单 - 创建工单接口
    rpc TkCreate(TkCreateReq) returns (TkCreateResp) {
        option (google.api.http) = {
            post: "/egress/ticket/create"
            body: "*"
        };
    }
    // 工单 - 创建工单接口
    rpc TkMine(TkMineReq) returns (TkMineResp) {
        option (google.api.http) = {
            post: "/egress/ticket/mine"
            body: "*"
        };
    }
    // 工单 - 工单详情接口
    rpc TkDetail(TkDetailReq) returns (TkDetailResp) {
        option (google.api.http) = {
            post: "/egress/ticket/detail"
            body: "*"
        };
    }
    // 工单 - 工单补填接口
    rpc TkReplenish(TkReplenishReq) returns (Empty) {
        option (google.api.http) = {
            post: "/egress/ticket/replenish"
            body: "*"
        };
    }
    // 工单 - 工单重开接口
    rpc TkReopen(TkReplenishReq) returns (Empty) {
        option (google.api.http) = {
            post: "/egress/ticket/reopen"
            body: "*"
        };
    }
    // 工单 - 工单评价接口
    rpc TkAppraise(TkAppraiseReq) returns (Empty) {
        option (google.api.http) = {
            post: "/egress/ticket/appraise"
            body: "*"
        };
    }
    // 工单 - 工单评价反馈接口
    rpc TkAppraiseFeedback(TkAppraiseFeedbackReq) returns (Empty) {
        option (google.api.http) = {
            post: "/egress/ticket/appraise/feedback"
            body: "*"
        };
    }
    // 工单 - 给客服发消息
    rpc TkCommunicate(TkCommunicateReq) returns (Empty) {
        option (google.api.http) = {
            post: "/egress/ticket/communicate"
            body: "*"
        };
    }


        // 三级分类信息
    rpc CategoryInfo(CatSubRequest) returns (EgressCatInfoResp) {
        option (google.api.http) = {
            post: "/egress/cat/info"
            body: "*"
        };
    }
}

message CatSubRequest {
    // 项目标识 @gotags: validate:"required_without=FpxAppId"
    uint32 game_id = 1;
    // 项目标识 @gotags: validate:"required_without=GameId"
    string fpx_app_id = 2;
    // 语言标识 @gotags: validate:"required"
    string lang = 3;
    // 类别ID @gotags: validate:"required"
    uint32 cat_id = 4;
    // 国家code
    string country_code = 5;
    // 渠道
    string channel = 6;
    // 区服
    string sid = 7;
    // json_data
    string json_data = 8;
}

message EgressCatInfoResp {
    uint32 cat_id = 1;
    string cat_name = 2;
    uint32 relate_type = 3;
    uint32 process_id = 4;
    uint32 tpl_id = 5;    // 模版ID
    string tpl = 6;       // 模版名称
    string fields = 7;    // 模版表单
    string category = 8;  // 类别信息
    map<string, string> reply_content = 9;  // 回复模版

    // 问题列表
    repeated QuestionInfo question_list = 10;
    // chatOrForm 1: 聊天 2: 表单
    ChatOrFormType chat_or_form = 11;

}

message QuestionInfo {
    string question_key = 1;
    string question_name = 2;
}



message CatOptRequest {
    // 项目标识 @gotags: validate:"required_without=FpxAppId"
    uint32 game_id = 1;
    // 项目标识 @gotags: validate:"required_without=GameId"
    string fpx_app_id = 2;
    // 语言标识 @gotags: validate:"required"
    string lang = 3;
}
