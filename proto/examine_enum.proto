syntax = "proto3";

package pb;
option go_package = ".;pb";
// 质检打分表-字段值类型定义
enum ExamineFieldTpDf {
    // 不需要定义
    ExamineFieldTpDfUnknown = 0;
    // 下拉单选
    ExamineFieldTpSel = 1;
    // 下拉多选
    ExamineFieldTpMulSel = 2;
    // 单行文本
    ExamineFieldTpText = 3;
    // 多行文档 - 富文本textarea
    ExamineFieldTpTextarea = 4;
}

// 质检任务-状态定义
enum ExamineTaskStateDf {
    ExamineTaskStateDfUnknown = 0;
    // 未开始
    ExamineTaskStateDfInit = 1;
    // 进行中
    ExamineTaskStateDfDoing = 10;
    // 成功
    ExamineTaskStateDfSuccess = 100;
    // 失败
    ExamineTaskStateDfFail = 200;
}

// 质检任务-任务类型
enum ExamineTaskGroupDf {
    ExamineTaskGroupDfUnknown = 0;
    // 工单
    ExamineTaskGroupDfTicket = 1;
    // discord
    ExamineTaskGroupDfDiscord = 2;
}

// ExamineStateDf 质检单 当前状态
enum ExamineStateDf {
    ExamineStateDfUnknown = 0;
    // 未开始
    ExamineStateDfInit = 1;
    // 进行中
    ExamineStateDfDoing = 10;
    // 已完成
    ExamineStateDfSuccess = 100;
}

// ExamineFinalResultDf 质检单 最终结果
enum ExamineFinalResultDf {
    ExamineFinalResultDfUnknown = 0;
    // 通过
    ExamineFinalResultDfPass = 1;
    // 不通过
    ExamineFinalResultDfFail = 2;
}

// ExamineNoticeMsgGroupDf 消息分类
enum ExamineNoticeMsgGroupDf{
    ExamineNoticeMsgGroupDfUnknown = 0;
    // 质检结果通知
    ExamineNoticeMsgGroupDfResult = 1;
}
// ExamineNoticeStateDf 消息状态
enum ExamineNoticeStateDf {
    ExamineNoticeStateDfUnknown = 0;
    // 未读
    ExamineNoticeStateDfUnread = 1;
    // 已读
    ExamineNoticeStateDfRead = 2;
}

// 质检任务-抽检规则
enum ExamineTaskRulesDf {
    ExamineTaskRulesDfUnknown = 0;
    // 无差别随机抽检
    ExamineTaskRulesDfRandom = 1;
    // 定向等量抽检
    ExamineTaskRulesDfAvg = 2;
}
