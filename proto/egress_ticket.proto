syntax = "proto3";

package pb;
option go_package = ".;pb";

import "enum.proto";
// TkCreateReq 创建工单
message TkCreateReq {
    // 工单来源 1:玩家 2:VIP客服 3:普通客服 @gotags: validate:"required"
    uint32 origin = 1;
    // 场景枚举
    uint32 scene = 2;
    // Deprecated: 废弃，后续整合到fpx_app_id中，统一使用`fpx_app_id`
    // 游戏ID @gotags: validate:"required_without=FpxAppId"
    uint32 game_id = 3;
    // 项目标识 @gotags: validate:"required_without=GameId"
    string fpx_app_id = 4;
    // lang 语言 @gotags: validate:"required"
    string lang = 5;
    // uuid 设备ID
    string uuid = 6;
    // Deprecated: 废弃，fpid 后续整合到 account_id中，统一使用`account_id`
    uint64 fpid = 7;
    // uid 用户ID
    uint64 uid = 8;
    // account_id
    string account_id = 9;
    // role_id 角色ID
    string role_id = 10;
    // sid 区服
    string sid = 11;
    // channel 渠道
    string channel = 12;
    // nickname 昵称
    string nickname = 13;
    // cat_id 问题类别ID @gotags: validate:"required"
    uint32 cat_id = 14;
    // fields 自定义字段  @gotags: validate:"required"
    string fields = 15;
    string device_type = 16;
    string os = 17;
    string os_version = 18;
    string app_version = 19;
    string rom_gb = 20;
    string remain_rom = 21;
    string ram_mb = 22;
    string network_info = 23;
    string sdk_version = 24;
    // 国家
    string country = 25;
    // 历史充值总额
    double total_pay = 26;
    repeated uint32 label_id = 27;
    string extend = 28;
    // Deprecated: 废弃，后续使用 total_pay，统一使用`total_pay`
    double pay_amount = 29;
    // Deprecated: 废弃，后续 使用country，统一使用`country`
    string country_code = 30;
    // 重提工单ID
    uint32 related_cat = 31;
    uint64 from_ticket_id = 32;
    // subchannel 子渠道
    string subchannel = 33;
    // 微信内小游戏 - openid
    string openid = 34;
    // 流程会话
    string process_session = 35;
    // 流程ID
    int64 process_id = 36;
    // ss vip绿色通道标识
    string qfrom = 40;
    // 私域来源请求标识
    string zone_from = 41;
    // 问题用户 account_id(前端不传)
    string trouble_account_id = 43;
    // 问题用户 uid
    uint64 trouble_uid = 44;
    // 渠道号
    string packageId = 45;
    // 私域 zone_token
    string zone_token = 46;
    // 对话ID
    string conversation_id = 47;
    // 是否为新版ai客服单
    bool ticket_type = 48;
    // 新版客服无效单判断逻辑 (仅为描述与建议这两个key，且结果为暂未收集到有效信息)
    uint32 is_invalid = 49;
    // 私域 R级参数
    uint64 zone_vip_level = 50;
    // 私域权益使用
    bool priv_rights_use = 51;
}

// TkCreateResp 工单ID
message TkCreateResp {
    // 工单ID @gotags: validate:"required"
    uint64 ticket_id = 1;
    // 使用哪个工单系统
    TicketSys ticket_sys_type = 2;
}
// TkUpResp 常规操作返回
message TkUpResp {
    // 工单ID
    uint64 ticket_id = 1;
    // 使用哪个工单系统
    TicketSys ticket_sys_type = 2;
}

// TkMineReq 我的工单列表
message TkMineReq {
    // 场景枚举
    uint32 scene = 1;
    // Deprecated: 废弃，后续整合到fpx_app_id中
    // 游戏ID @gotags: validate:"required_without=FpxAppId"
    uint32 game_id = 3;
    // 项目标识 @gotags: validate:"required_without=GameId"
    string fpx_app_id = 4;
    // lang 语言 @gotags: validate:"required"
    string lang = 5;
    // uuid 设备ID
    string uuid = 6;
    // Deprecated: 废弃，fpid 后续整合到 account_id中
    uint64 fpid = 7;
    // uid 用户ID
    uint64 uid = 8;
    // account_id
    string account_id = 9;
    // role_id 角色ID
    string role_id = 10;
    // sid 区服
    string sid = 11;
    // channel 渠道
    string channel = 12;
}

// TkMineResp 工单历史 - 老工单字段及定义
message TkMineResp {
    message TkList {
        // 工单ID
        uint64 ticket_id = 1;
        // 创建时间
        string created_at = 2;
        // 类别
        string category = 3;
        // 当前状态 1：已完成 2：待补充 3：处理中 4：补充超时 5：重新打开
        TkProgress progress = 4;
        // 当前数据状态： 前端忽略
        TkStatus status = 5;
        // 消息已读状态 1:已读 0:未读
        uint32 read = 6;
        // 使用哪个工单系统
        TicketSys ticket_sys_type = 7;
    }
    repeated TkList data = 1;
}


// TkDetailReq 工单详情
message TkDetailReq {
    // 项目标识 @gotags: validate:"required"
    uint64 ticket_id = 1;
    // Deprecated: 废弃，后续整合到fpx_app_id中
    // 游戏ID @gotags: validate:"required_without=FpxAppId"
    uint32 game_id = 3;
    // 项目标识 @gotags: validate:"required_without=GameId"
    string fpx_app_id = 4;
    // lang 语言 @gotags: validate:"required"
    string lang = 5;
    string uuid = 6;
    // Deprecated: 废弃，fpid 后续整合到 account_id中
    uint64 fpid = 7;

    // uid 用户ID
    uint64 uid = 8;
    // account_id
    string account_id = 9;
    // role_id 角色ID
    string role_id = 10;
    // sid 区服
    string sid = 11;
    // channel 渠道
    string channel = 12;

}


// TicketDetailResp 工单详细信息
message TkDetailResp {
    message Replenish {
        uint64 replenish_id = 1;
        // 工单ID
        uint64 ticket_id = 2;
        // 备注信息
        string remark = 3;
        // 补填内容
        string fill_content = 4;
        // 文件
        string files = 5;
        // 操作 9:回复信息 7:打回补填 8:关闭工单 6:处理完成
        uint32 op = 6;
        // 创建时间
        string created_at = 7;
        // 创建时间ts
        uint64 created_ts = 8;
    }
    message Reopen {
        // 重开id
        uint64 reopen_id = 1;
        // 重开提交内容
        string fill_content = 2;
        // 重开附件
        string files = 3;
        // 补填+回复信息
        repeated Replenish replenish = 4;
        // 创建时间
        string created_at = 5;
        // 回复id - 执行重开 - commu
        uint64 reply_id = 6;
        // 重开单 - 客服回复&关单 的id - commu
        uint64 response_reply_id = 7;
    }
    // 对话记录
    message Commu {
        // 消息内容
        string  content = 1;
        // 消息来源: 1:玩家端； 2客服回复
        UserRole role = 2;
        // 客服回复人员名称
        string custom_nick_name = 3;
        // 消息时间
        string created_at = 4;
        string picture = 5;
        string video = 6;
        // 操作类型
        uint32 op_type = 7;
    }
    // 工单ID
    uint64 ticket_id = 1;
    // 问题分类
    string category = 2;
    // 表单信息
    string filed = 3;
    // 流转
    uint32 transfer = 4;
    // 解决方案
    string solution = 5;
    // 状态 true:已结单
    bool done = 6;
    // 关闭 0:未关闭 1:结单 2:关闭 3:超时关闭
    uint32 closed = 7;
    // 补充举证 true:需举证 - 去补填按钮
    bool evidence = 8;
    // 评价 true：需评价
    bool appraise = 9;
    // 补充列表 -- 存在 空着 - 不需要
    repeated Replenish replenish = 10;
    // 沟通记录
    repeated Commu commu = 11;
    // 评分
    uint32 csi = 12;
    // 评价标签
    repeated uint32 label = 13;
    // 关联类型,1:表单 2:自动化流程
    uint32 relate_type = 16;
    // 重开信息
    repeated Reopen reopen = 18;
    // 超过重开次数+不支持重开：true
    bool reopen_disabled = 19;
    // 问题类别ID
    uint32 cat_id = 20;
    // 使用哪个工单系统
    TicketSys ticket_sys_type = 21;
    // 超时回复文案
    string overtime_reply = 22;
    // 超时时间
    string overtime = 23;
    // 是否为新版ai客服 - 0为不是，1为是
    uint32 is_chat_ticket = 24;
}

// TicketDetailModel
message TicketDetailModel {
    uint64 ticket_id = 1;
    string uuid = 2;
    uint32 origin = 3;
    uint32 cat_id = 5;
    uint32 evaluation = 6;
    uint32 csi = 7;
    uint32 proof = 8;
    uint32 stage = 9;
    uint32 status = 10;
    uint32 closed = 11;
    uint64 closed_at = 12;
    string field = 13;
    // account_id
    string account_id = 16;
    // project
    string project = 17;
    // game_id
    string gm_id = 18;
    // project
    uint32 scene = 20;
    // 重开次数
    uint32 reopen_num = 21;
    // 自动回复模版id
    uint32 auto_reply_id = 22;
    // 是否升级单
    uint32 priority = 23;
    uint32 ticket_type = 24;
}


message NoticeResp {
    // 消息ID @gotags: json:"notice_id,omitempty"
    uint64 notice_id = 1;
    // 来源 @gotags: json:"from,omitempty"
    uint32 from = 2;
    // 对象ID @gotags: json:"object_id,omitempty"
    uint64 object_id = 3;
    // 场景
    uint32 scene = 4;
    // 使用哪个工单系统
    TicketSys ticket_sys_type = 5;
    // 消息未读数量 @gotags: json:"notice_count"
    uint64 notice_count = 6;
}

// TkReplenishReq 补填信息
message TkReplenishReq {
    // 工单ID @gotags: validate:"required"
    uint64 ticket_id = 1;
    // 工单内容 @gotags: validate:"required"
    string content = 2;
    // 工单附件 @gotags: validate:"required"
    string files = 3;
    // Deprecated: 废弃，后续整合到fpx_app_id中
    // 游戏ID @gotags: validate:"required_without=FpxAppId"
    uint32 game_id = 4;
    // 项目标识 @gotags: validate:"required_without=GameId"
    string fpx_app_id = 5;
    // lang 语言 @gotags: validate:"required"
    string lang = 6;
    // uuid 设备ID
    string uuid = 7;
    // Deprecated: 废弃，fpid 后续整合到 account_id中
    uint64 fpid = 8;
    // uid 用户ID
    uint64 uid = 9;
    // account_id
    string account_id = 10;
    // role_id 角色ID
    string role_id = 11;
    // sid 区服
    string sid = 12;
    // channel 渠道
    string channel = 13;
    // nickname 昵称
    string nickname = 14;
    // subchannel 子渠道
    string subchannel = 34;
}


// TkAppraiseReq 评价
message TkAppraiseReq {
    // 工单ID @gotags: validate:"required"
    uint64 ticket_id = 1;
    // Deprecated: 废弃，后续整合到fpx_app_id中
    // 游戏ID @gotags: validate:"required_without=FpxAppId"
    uint32 game_id = 4;
    // 项目标识 @gotags: validate:"required_without=GameId"
    string fpx_app_id = 5;
    // lang 语言 @gotags: validate:"required"
    string lang = 6;
    // Deprecated: 废弃，fpid 后续整合到 account_id中
    uint64 fpid = 8;
    // uid 用户ID
    uint64 uid = 9;
    // account_id
    string account_id = 10;
    // role_id 角色ID
    string role_id = 11;
    // sid 区服
    string sid = 12;
    // channel 渠道
    string channel = 13;
    // 评价等级 @gotags: validate:"required"
    uint32 appraise = 14;
    // 服务态度评分 - 老接口字段 - 废弃
    uint32 service_rating = 15;
    // 处理速度评分 - 老接口字段 - 废弃
    uint32 service_time_rating = 16;
    // 处理方案评分 - 老接口字段 - 废弃
    uint32 service_solution_rating = 17;
    // 推荐给别人的意愿程度 @gotags: validate:"required_if=ServiceRating 0"
    uint32 recommendation_level = 18;
    // 评价内容
    string remark = 19;
}

// TkAppraiseReq 工单评价
message TkAppraiseFeedbackReq {
    // 工单ID @gotags: validate:"required"
    uint64 ticket_id = 1;
    // Deprecated: 废弃，后续整合到fpx_app_id中
    // 游戏ID @gotags: validate:"required_without=FpxAppId"
    uint32 game_id = 4;
    // 项目标识 @gotags: validate:"required_without=GameId"
    string fpx_app_id = 5;
    // lang 语言 @gotags: validate:"required"
    string lang = 6;
    // Deprecated: 废弃，fpid 后续整合到 account_id中
    uint64 fpid = 8;
    // uid 用户ID
    uint64 uid = 9;
    // account_id
    string account_id = 10;
    // role_id 角色ID
    string role_id = 11;
    // sid 区服
    string sid = 12;
    // channel 渠道
    string channel = 13;

    // 是否解决  1：解决 2：未解决
    uint32 resolved = 14;
}

// TkCommunicateReq 沟通发送消息
message TkCommunicateReq {
    // 工单ID @gotags: validate:"required"
    uint64 ticket_id = 1;
    // Deprecated: 废弃，后续整合到fpx_app_id中
    // 游戏ID @gotags: validate:"required_without=FpxAppId"
    uint32 game_id = 4;
    // 项目标识 @gotags: validate:"required_without=GameId"
    string fpx_app_id = 5;
    // lang 语言 @gotags: validate:"required"
    string lang = 6;
    // Deprecated: 废弃，fpid 后续整合到 account_id中
    uint64 fpid = 8;
    // uid 用户ID
    uint64 uid = 9;
    // account_id
    string account_id = 10;
    // role_id 角色ID
    string role_id = 11;
    // sid 区服
    string sid = 12;
    // channel 渠道
    string channel = 13;
    // 发送消息内容
    string content = 14;
    // 玩家上传的图片url链接
    string picture = 15;
    // 玩家上传的视频url链接
    string video = 16;
    // 场景
    uint32 scene = 17;
    // uuid 设备ID
    string uuid = 18;
}

message NoticeRequestV2 {
    // 项目标识 @gotags: validate:"required"
    string appId = 1;
    // 项目标识 @gotags: validate:"required"
    uint32 gameId = 2;
    // 唯一标识 @gotags: validate:"required_without=Fpid"
    string device_id = 3;
    // fpid @gotags: validate:"required_without=DeviceId"
    uint64 fpid = 4;
    // uid
    uint64 uid = 5;
    // 区服标识
    string sid = 6;
    // 渠道标识
    string channel = 7;
    // 语言标识
    string lang = 8;
    // Sdk版本
    string sdk_version = 9;
    // 系统版本
    string os = 10;
    // 时间戳
    uint64 ts = 11;
    // 客户端版
    string auth = 12;
    // 场景
    uint32 scene = 13;
}


// NoticeFpxRequest fpx 消息通知
message NoticeFpxRequestV3 {
    // 项目标识 @gotags: validate:"required"
    string appId = 1;
    // 项目标识:对应kg:game_id/fpx:fpx_app_id @gotags: validate:"required"
    string fpx_app_id = 2;
    // 唯一标识：游戏外场景为uuid @gotags: validate:"required"
    string device_id = 4;
    // account_id - 对应kg:fpid/fpx:account_id
    string account_id = 5;
    // sid 区服ID
    string sid = 6;
    // role_id 角色ID
    string role_id = 7;
    // 时间戳
    uint64 ts = 8;
    // 客户端版
    string auth = 9;
    // 场景
    uint32 scene = 10;
}

// 私域服务端请求查询工单未读消息 - 请求参数
message InnerNoticeRequest {
    // 请求来源标识 @gotags: validate:"required"
    string source_app_id = 1;
    // 项目标识：kg:game_id/fpx:fpx_app_id @gotags: validate:"required"
    string fpx_app_id = 2;
    // 唯一标识：设备id @gotags: validate:"required_without=AccountId"
    string device_id = 3;
    // 对应用户标识：kg:fpid/fpx:account_id @gotags: validate:"required_without=DeviceId"
    string account_id = 4;
    // 游戏内用户uid：kg:uid/fpx:uid @gotags: validate:"required_without=DeviceId"
    string uid = 5;
    // 场景来源： 3： 游戏内场景
    uint32 scene = 6;
}
// 私域服务端请求查询工单未读消息 - 响应数据
message InnerNoticeResp {
    // 是否有未读消息
    bool has_unread = 1;
    // 最新一条消息ID @gotags: json:"notice_id"
    uint64 notice_id = 2;
    // 最新一条对象ID @gotags: json:"object_id"
    uint64 object_id = 3;
    // 场景
    uint32 scene = 4;
}
// 自动回复模板 - 结构体定义
message TkCatAutoReplyTplPrefer {
    // swift回复模板id
    uint32 swift_reply_tpl_id = 1;
    // 生效规则： false:全部生效 true:部分生效: 需过滤条件
    bool reply_flag_for_filter = 2;
    // 过滤条件： 累付金额 小于等于xx美金 自动回复, 两位小数
    double total_pay = 3;
}
// 过滤:自动回复模板 - 请求参数
message TkCatAutoReplyParamDf {
    // 充值总额 - 美金*rate
    uint64 total_pay = 1;
}