syntax = "proto3";

package pb;
option go_package = ".;pb";

import "google/api/annotations.proto";
import "common.proto";

// 对话服务
service ConversationService {
    // 更新对话
    rpc UpdateConversation(UpdateConversationRequest) returns (UpdateConversationResponse) {
        option (google.api.http) = {
            post: "/egress/conversation/update"
            body: "*"
        };
    }
    // 获取对话历史
    rpc GetConversationHistory(GetHistoryRequest) returns (GetHistoryResponse) {
        option (google.api.http) = {
            post: "/egress/conversation/history"
        };
    }
    // 对话交互
    rpc HandleChat(ChatRequest) returns (ChatResponse) {
        option (google.api.http) = {
            post: "/egress/conversation/chat"
            body: "*"
        };
    }
    //egress/conversation/continue
    rpc ContinueChat(Empty) returns (ContinueChatResponse) {
        option (google.api.http) = {
            post: "/egress/conversation/continue"
            body: "*"
        };
    }

    //egress/conversation/close
    rpc CloseChat(CloseChatReq) returns (Empty) {
        option (google.api.http) = {
            post: "/egress/conversation/close"
            body: "*"
        };
    }
}

message CloseChatReq {
    string conversation_id = 1;
}

message ContinueChatResponse {
    bool has_active_conversation = 1;
    int32 cat_id = 2;
    string conversation_id = 3;
}



message UserChatReq {
    string user_id = 1;
    string game_id = 2;
    string fpx_app_id = 3;

}

// 更新对话请求
message UpdateConversationRequest {
    string conversation_id = 1;  // 会话ID
    string user_id = 2;         // 用户ID
    int32 cat_id = 3;          // 分类ID
    int64 ticket_id = 4;       // 工单ID
    repeated History history = 5;  // 历史记录
    repeated QuestionGet question_get_list = 6;  // 问题获取列表
}

// 更新对话响应
message UpdateConversationResponse {
    string uuid = 1;           // 操作唯一标识
    string conversation_id = 2; // 会话ID
    int64 timestamp = 3;       // 时间戳
}

// 获取历史请求
message GetHistoryRequest {

    // 项目标识 @gotags: validate:"required_without=FpxAppId"
    uint32 game_id = 1;
    // 项目标识 @gotags: validate:"required_without=GameId"
    string fpx_app_id = 2;
    // 语言标识 @gotags: validate:"required"
    string lang = 3;
    string conversation_id = 4;  // 会话ID
}

// 获取历史响应
message GetHistoryResponse {
    repeated HistoryItem history = 1;           // 对话历史
    repeated QuestionHit question_hit_list = 2;  // 问题命中列表
    repeated QuestionGet question_get_list = 3;  // 问题获取列表
    // ticket_id
    int64 ticket_id = 4;
    // completed
//    bool completed = 5;

    int32 conversation_status = 5;

}

// 对话请求
message ChatRequest {
    string conversation_id = 1;     // 会话ID
    string now_question_key = 2;    // 当前问题key
    string now_question_content = 3; // 当前问题内容
    repeated Content now_answer_content = 4; // 当前回答内容
    repeated QuestionContent question_list = 5; // 问题列表
}

// 问题内容
message QuestionContent {
    string question_key = 1; // 问题key
    string question_content = 2; // 问题内容
}

// 对话响应
message ChatResponse {
    repeated QuestionGet history_answer_list = 1; // 历史回答列表
    string now_question_key = 2;     // 当前问题key
    string now_answer_content = 3;    // 当前回答内容
    bool hit_question = 4;           // 是否命中问题
    bool completed = 5;              // 是否完成所有问题
}

// 历史记录
message History {
    string role = 1;          // 角色：user/assistant
    string question_key = 2;  // 问题key
    string content = 3;       // 内容
}

// 历史记录项
message HistoryItem {
    string role = 1;          // 角色
    string content = 2;       // 内容
    string question_key = 3;  // 问题key
    int64 timestamp = 4;      // 时间戳
    string uuid = 5;          // 唯一标识
}

// 问题命中
message QuestionHit {
    string question_key = 1;  // 问题key
    bool has_answer = 2;      // 是否有答案
}

// 问题获取
message QuestionGet {
    string question_key = 1;   // 问题key
    string answer = 2;         // 答案
    bool has_answer = 3;       // 是否有答案
    int32 question_ask_count = 4; // 问题询问次数
    string question_content = 5; //

}

// 内容
message Content {
    string text = 1;  // 文本内容
}