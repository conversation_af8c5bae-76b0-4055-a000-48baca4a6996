syntax = "proto3";

package pb;
option go_package = ".;pb";
import "enum.proto";
import "common.proto";
import "google/api/annotations.proto";


// line功能实现 - 接口 API
service LineApi {
  rpc LineTabSave(LineTabAddReq) returns (Empty) {
    option (google.api.http) = {
      post: "/api/line/tab/save"
      body: "*"
    };
  }
  rpc LineTabDel(LineTabDelReq) returns (Empty) {
    option (google.api.http) = {
      post: "/api/line/tab/del"
      body: "*"
    };
  }
  rpc LineTabList(Empty) returns (LineTabListResp) {
    option (google.api.http) = {
      post: "/api/line/tab/list"
      body: "*"
    };
  }
  rpc LineTabCount(Empty) returns (LineTabCountResp) {
    option (google.api.http) = {
      post: "/api/line/tab/count"
      body: "*"
    };
  }
  rpc LineTabEdit(LineTabEditReq) returns (Empty) {
    option (google.api.http) = {
      post: "/api/line/tab/edit"
      body: "*"
    };
  }
}

message LineUserListReq{
  // 游戏
  repeated string project = 1;
  // 回复时间
  repeated string replied_at = 2;
  // 状态 - 1:未回复、2:已回复
  repeated uint32 status = 3;
  // 玩家输入信息 - 模糊查询
  string user_content = 4;
  // 备注信息 - 玩家备注信息 - 模糊查询
  string user_detail_remark = 5;
  // 维护人
  repeated string maintainer = 6;
  // 玩家 line昵称
  string display_name = 7 ;
  // 玩家 line ID
  string line_user_id = 8;
  uint64 uid = 9;
  string fpid = 10;
  string sid = 11;
  // 最近登录时间
  repeated string last_login = 12;
  // 累计付费金额
  repeated int64 pay_all = 13;
  // 最近30天付费金额
  repeated int64 pay_last_thirty_days = 14;
  // 玩家VIP状态
  uint32 vip_state = 15;
  repeated string birthday = 17;
  string lang = 18;
  // 最近处理人
  repeated string last_reply_service = 19;
  // 排序字段
  DcPoolSort sort_by = 20;
  // 排序顺序 asc升序，desc降序
  string order = 21;
  // @gotags: validate:"required"
  uint32 page = 22;
  // @gotags: validate:"required"
  uint32 page_size = 23;
  // 机器人bot_id
  repeated string bot_ids = 24;
  // 新用字段
  string uids = 25;
  // channel_id
  repeated string channel_id = 26;
  // 新用标签
  repeated uint32 tags = 27;
  // 标签类型
  FilterTagEnum tag_type = 28;
}
message LineUserListResp{
  message LineUser{
    // 游戏
    string project = 1;
    // 玩家 line ID
    string line_user_id = 2;
    // 玩家名称 - 前端展示使用此字段
    string display_name = 3;
    // line channel
    string channel_id = 4;
    // 当前维护人
    string maintainer = 5;
    // 累付金额
    double total_pay = 6;
    // 最近30天付费金额
    double pay_last_thirty_days = 7;
    // 最近登录时间
    string last_login = 8;
    // 玩家信息回复状态
    uint32 status = 9;
    // VIP 等级
    uint32 vip_level = 10;
    string fpid = 11;
    uint64 uid = 12;
    string sid = 13;
    // 机器人 user_id
    string bot_id = 14;
    string note = 15;
    string player_nick = 16;
    string birthday = 17;
    string lang = 18;
    bool checked = 19;
    // channel show
    string channel_show = 20;
    // follow status : 1:follow 2:unfollow
    int32 follow_status = 21;
  }

  uint32 current_page = 1;
  uint32 per_page = 2;
  uint32 total = 3;
  repeated LineUser data = 4;
}

message LineUserDetailReq{
  // 玩家 DC ID
  string line_user_id = 1;
}

message LineUserDetailResp{
  message LineUserDetail {
    // 游戏
    string project = 1;
    // 玩家 DC ID
    string line_user_id = 2;
    // 玩家名称 - 前端展示使用此字段
    string display_name = 3;
    // dm channel
    string channel_id = 4;
    // 当前维护人
    string maintainer = 5;
  }
  // 基本信息
  LineUserDetail line_user_detail = 1;
}

message LinePoolInfo{
  // 游戏
  string project = 1;
  // 玩家 Line ID
  string line_user_id = 2;
  // 玩家名称 - 前端展示使用此字段
  string display_name = 3;
  // line channel
  string channel_id = 4;
  // channel show
  string channel_show = 5;
  // 当前维护人
  string maintainer = 6;
  string bot_id = 7;
  uint64 uid = 8;
  string account_id = 9;
  // 服务器
  string sid = 10;
  // 最近登录时间
  string last_login = 11;
  // 累计付费金额
  int64 pay_all = 12;
  // 最近30天付费金额
  int64 pay_last_thirty_days = 13;
  uint32 status = 14;
  uint32 vip_level = 15;
  string note = 16;
  string player_nick = 17;
  string birthday = 18;
  string lang = 19;
  // follow status : 1:follow 2:unfollow
  int32 follow_status = 20;
}

message LineStatsReq {
  repeated string project = 1;
}

message LineStatsResp {
  int64 line_user_count = 1;
  int64 wait_reply_accounts = 2;
  int64 mine_wait_reply_accounts = 3;
}

message LineSendTextMessageReq{
  message Emoji {
    uint32 index = 1;
    uint32 length = 2;
    string product_id = 3;
    string emoji_id = 4;
  }
  // 频道id @gotags: validate:"required"
  string channel_id = 1;
  // 机器人id @gotags: validate:"required"
  string bot_id = 2;
  // 玩家的line_user_id @gotags: validate:"required"
  string line_user_id = 3;
  // 内容 @gotags: validate:"required"
  string content = 4;
  // 表情符号，非必传
  repeated Emoji emojis = 5;
}

message LineSendMessageResp{
  // 消息id
  string msg_id = 1;
  // 发送渠道
  string channel_id = 2;
}

message LineSendFileReq {
  // 频道id
  string channel_id = 1;
  // 机器人id
  string bot_id = 2;
  // 玩家的line_user_id
  string line_user_id = 3;
  // 文件类型
  string file_type = 4;
  // 文件格式
  string format = 5;
}

message LineDialogueHistoryReq {
  // 频道id @gotags: validate:"required"
  string channel_id = 1;
  // 机器人id @gotags: validate:"required"
  string bot_id = 2;
  // 玩家的line_user_id @gotags: validate:"required"
  string line_user_id = 3;
  bool before = 4;
  // 消息的时间戳
  uint64 tick_time = 5;
  uint32 page = 6;
  uint32 page_size = 7;
}

message LineDialogueHistoryResp {
  message Dialogue {
    string message_id = 1;
    string bot_id = 2;
    string line_user_id = 3;
    // 玩家昵称
    string display_name = 4;
    string channel_id = 5;
    // 消息来源 player/service
    string message_from = 6;
    // 发送者 客服账号
    string sender = 7;
    string message_type = 8;
    // 引用token, 引用回复需要此字段
    string quote_token = 9;
    string content = 10;
    // 消息是否已被玩家撤回
    bool revoke = 11;
    // 原始文件(视频/图片/音频)url
    string original_content_url = 12;
    // 文件预览图url
    string preview_image_url = 13;
    // 音频时长
    int32 duration = 14;
    // 贴纸信息
    string sticker_info = 15;
    // 位置信息
    string location = 16;
    // 消息的时间戳
    uint64 tick_time = 17;
    string create_time = 18;
  }
  uint32 current_page = 1;
  uint32 per_page = 2;
  uint32 total = 3;
  repeated Dialogue data = 4;
}

message LineDialogFreshReq{
  // 频道id @gotags: validate:"required"
  string channel_id = 1;
  // 机器人id @gotags: validate:"required"
  string bot_id = 2;
  // 玩家的line_user_id @gotags: validate:"required"
  string line_user_id = 3;
  // 向下翻页 - 开始消息id对应的时间戳
  int64 tick_time = 4;
}

// LinePortraitInfoReq 获取玩家画像信息
message LinePortraitInfoReq {
  // @gotags: validate:"required"
  string line_user_id = 1;
  // @gotags: validate:"required"
  string project = 2;
}

message LinePortraitInfoResp {
  message LabelDetail {
    uint64 tag_id = 1;
    string tag_desc = 2;
  }
  uint64 id = 1;
  repeated LabelDetail label = 2;
  uint32 gender = 3;
  string birthday = 4;
  string career = 5;
  uint32 education_level = 6;
  uint32 married_state = 7;
  uint32 fertility_state = 8;
  string remark = 9;
  string line_user_id = 10;
  string project = 11;
}

// LinePortraitEditReq 添加/编辑 画像标签信息
message LinePortraitEditTagReq {
  uint64 id = 1;
  // @gotags: validate:"required_without=Id"
  string project = 2;
  string tag = 3;
  // @gotags: validate:"required_without=Id"
  string line_user_id = 4;
}

// LinePortraitEditReq 添加/编辑 画像基础属性信息
message LinePortraitEditBasicReq {
  uint64 id = 1;
  // @gotags: validate:"required_without=Id"
  string project = 2;
  uint32 gender = 3;
  string birthday = 4;
  string career = 5;
  uint32 education_level = 6;
  uint32 married_state = 7;
  uint32 fertility_state = 8;
  // @gotags: validate:"required_without=Id"
  string line_user_id = 9;
}

// LinePortraitEditReq 添加/编辑 画像备注信息
message LinePortraitEditRemarkReq {
  uint64 id = 1;
  // @gotags: validate:"required_without=Id"
  string project = 2;
  string remark = 3;
  // @gotags: validate:"required_without=Id"
  string line_user_id = 4;
}

// LineMaintainConfigDelReq 删除玩家维护配置
message LineMaintainConfigDelReq {
  // id @gotags: validate:"required"
  uint64 id = 1;
  // @gotags: validate:"required"
  string line_user_id = 2;
  //  @gotags: validate:"required"
  string project = 3;
}

// LineMaintainConfigListReq 玩家维护配置查询请求参数
message LineMaintainConfigListReq {
  // 游戏, 支持多选
  repeated string project = 1;
  // 维护专员, 支持多选
  repeated string maintainer = 2;
  // 昵称, 支持多选
  repeated string nick_name = 3;
  // 玩家账号,单选
  string Line_user_id = 4;
  uint32 vip_state = 5;
  uint32  page = 6;
  uint32  page_size = 7;
}

// LineMaintainConfigListResp 玩家维护配置查询响应
message LineMaintainConfigListResp {
  message LineMaintainConfigInfo{
    uint64 id = 1;
    // 玩家discord_id
    string line_user_id = 2;
    // 玩家discord 昵称
    string nick_name = 3;
    // 玩家fpid
    string fpid = 4;
    uint64 uid = 5;
    // 服务器
    string sid = 6;
    // 维护专员
    string maintainer = 7;
    // 游戏
    string project = 8;
    // 操作人
    string operator = 9;
    uint32 vip_state = 10;
    // 操作时间
    string update_time = 11;
    string lang = 12;
    string birthday = 13;
  }

  uint32 current_page = 1;
  uint32 per_page = 2;
  uint32 total = 3;
  repeated LineMaintainConfigInfo data = 4;
}

message LineMaintainConfigEditReq {
  // @gotags: validate:"required"
  uint64 id = 1;
  // 玩家fpid
  string fpid = 2;
  // 维护专员
  string maintainer = 3;
  uint64 uid = 4;
  uint32 vip_state = 5;
  // @gotags: validate:"required"
  string project = 6;
}

message LinePlayerAccountsReq {
  // @gotags: validate:"required"
  string project = 1;
  // 搜索值
  string line_user_id = 2;
}

message LinePlayerAccountsResp {
  repeated string line_user_ids = 1;
}

message LineCommuRecordAddReq {
  // 沟通日期 @gotags: validate:"required"
  string commu_date = 1;
  // 游戏 @gotags: validate:"required"
  string project = 2;
  // 沟通问题 @gotags: validate:"required"
  string question = 3;
  // 问题类型Id @gotags: validate:"required"
  uint32 cat_id = 4;
  // 处理状态 @gotags: validate:"required"
  int32 handle_status = 5;
  // 备注
  string remark = 6;
  // 涉及对话信息 @gotags: validate:"required"
  string msg_ids = 7;
  // 玩家line_user_id @gotags: validate:"required"
  string line_user_id = 8;
  // 玩家uid
  int64 uid = 9;
  // 玩家所在的服务器
  string sid = 10;
  // 玩家昵称 @gotags: validate:"required"
  string nick_name = 11;
  // 玩家累付金额
  double pay_all = 12;
  // 类别:line or discord @gotags: validate:"required,oneof=1 2"
  uint32 cat_type = 13;
}

message LineCommuRecordEditReq {
  // @gotags: validate:"required"
  int64 id = 1;
  string commu_date = 2;
  string question = 3;
  uint32 cat_id = 4;
  int32 handle_status = 5;
  string remark = 6;
}

message LineCommuRecordListReq {
  string project = 1;
  repeated string commu_date = 2;
  repeated string operator = 3;
  int64 uid = 4;
  uint32 cat_id = 5;
  repeated int32 handle_status = 6;
  // 类别:line or discord @gotags: validate:"required,oneof=1 2"
  uint32 cat_type = 7;
  // @gotags: validate:"required"
  uint32 page = 8;
  // @gotags: validate:"required"
  uint32 page_size = 9;
  // 标签多选
  repeated uint64 label = 10;
}

message LineCommuRecordListResp {
  message New_label{
    uint32 tag_id = 1;
    string tag_name = 2;
  }
  message DialogueItem {
    string role = 1;
    string content = 2;
  }
  message LineCommuRecord {
    int64 id = 1;
    string commu_date = 2;
    string project = 3;
    int64 uid = 4;
    string sid = 5;
    string nick_name = 6;
    double pay_all = 7;
    string question = 8;
    int32 handle_status = 9;
    string remark = 10;
    repeated DialogueItem dialogue = 11;
    string operator = 12;
    string maintainer = 13;
    string category = 14;
    int32 cat_id = 16;
    repeated New_label label = 17;
  }
  uint32 current_page = 1;
  uint32 per_page = 2;
  uint32 total = 3;
  repeated LineCommuRecord data = 4;
}


message LineUserRemarkAddReq {
  // 玩家line_user_id @gotags: validate:"required"
  string line_user_id = 1;
  // 备注 @gotags: validate:"required"
  string note = 2;
}

message LineTabAddReq {
  // @gotags: validate:"required"
  string tab_name = 1;
  // @gotags: validate:"required,oneof=1 2"
  int32 public = 2;
  // 搜索条件组合 @gotags: validate:"required"
  LineUserListReq detail = 3;
}

message LineTabEditReq {
  // @gotags: validate:"required"
  int64 id = 1;
  // @gotags: validate:"required"
  string tab_name = 2;
  // @gotags: validate:"required,oneof=1 2"
  int32 public = 3;
}

message LineTabDelReq {
  // @gotags: validate:"required"
  int64 id = 1;
}

message LineTabListResp {
  message LineTabDetail {
    repeated TabInfo tab = 1;
    string project = 2;
  }
  message TabInfo{
    int64 id = 1;
    string tab_name = 2;
    LineUserListReq detail = 3;
    int32 public = 4;
    string operator = 5;
  }
  repeated LineTabDetail data = 1;
}

message LineTabCountResp {
  message LineTabCount {
    repeated TabCountDetail tab = 1;
    string project = 2;
  }
  message TabCountDetail{
    string tab_name = 2;
    uint64 count = 5;
  }
  repeated LineTabCount detail = 1;
}

message LineTabUpdateSortReq {
  // @gotags: validate:"required"
  string sort_setting = 1;
}