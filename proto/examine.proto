syntax = "proto3";

package pb;
option go_package = ".;pb";

import "google/api/annotations.proto";
import "examine_enum.proto";
import "common.proto";
import "google/protobuf/any.proto";

// examine 质检模块 - 接口 API
service ExamineApi {
    // examine 质检打分表配置 - 列表
    rpc ExamineTplList(ExamineTplListReq) returns (ExamineTplListResp) {
        option (google.api.http) = {
            post: "/api/examine/tpl/list"
            body: "*"
        };
    }
    // examine 质检打分表配置 - 详情
    rpc ExamineTplDetail(ExamineTplDetailReq) returns (ExamineTplDetailResp) {
        option (google.api.http) = {
            post: "/api/examine/tpl/detail"
            body: "*"
        };
    }
    // examine 质检打分表配置 - 下来选项
    rpc ExamineTplOpts(Empty) returns (ExamineTplOptsResp) {
        option (google.api.http) = {
            post: "/api/examine/tpl/opts"
            body: "*"
        };
    }

    // examine 质检打分表配置 - 保存 add&save
    rpc ExamineTplSave (ExamineTplSaveReq) returns (Empty) {
        option (google.api.http) = {
            post: "/api/examine/tpl/save"
            body: "*"
        };
    }
    // examine 质检打分表配置 - 保存 add&save
    rpc ExamineTplCopy (ExamineTplCopyReq) returns (Empty) {
        option (google.api.http) = {
            post: "/api/examine/tpl/copy"
            body: "*"
        };
    }
    // examine 质检打分表配置 - enable&disable
    rpc ExamineTplEnable (EnableReq) returns (Empty) {
        option (google.api.http) = {
            post: "/api/examine/tpl/enable"
            body: "*"
        };
    }
    // examine 质检打分表配置 - 删除
    rpc ExamineTplDel (ExamineTplDelReq) returns (Empty) {
        option (google.api.http) = {
            post: "/api/examine/tpl/del"
            body: "*"
        };
    }

    // examine 任务配置 - 列表
    rpc ExamineTaskList(ExamineTaskListReq) returns (ExamineTaskListResp) {
        option (google.api.http) = {
            post: "/api/examine/task/list"
            body: "*"
        };
    }
    // examine 任务配置 - 详情
    rpc ExamineTaskDetail(ExamineTaskDetailReq) returns (ExamineTaskDetailResp) {
        option (google.api.http) = {
            post: "/api/examine/task/detail"
            body: "*"
        };
    }
    // examine 任务配置 - 保存 add&save
    rpc ExamineTaskSave (ExamineTaskSaveReq) returns (Empty) {
        option (google.api.http) = {
            post: "/api/examine/task/save"
            body: "*"
        };
    }
    // examine 任务配置 - 过滤 全检标准 - 统计
    rpc ExamineTaskDscFilterCount (ExamineTaskDscFilterCountReq) returns (ExamineTaskDscFilterCountResp) {
        option (google.api.http) = {
            post: "/api/examine/task/dsc/filter_count"
            body: "*"
        };
    }

    // examine 质检单数据 - 保存
    rpc ExamineDscOrderSave (ExamineDscOrderSaveReq) returns (Empty) {
        option (google.api.http) = {
            post: "/api/examine/order/dsc/save"
            body: "*"
        };
    }
    // examine 质检单数据 - 详情
    rpc ExamineDscOrderDetail(ExamineDscOrderDetailReq) returns (ExamineDscOrderDetailResp) {
        option (google.api.http) = {
            post: "/api/examine/order/dsc/detail"
            body: "*"
        };
    }
    // examine 质检单数据 - 列表
    rpc ExamineDscOrderList(ExamineDscOrderListReq) returns (ExamineDscOrderListResp) {
        option (google.api.http) = {
            post: "/api/examine/order/dsc/list"
            body: "*"
        };
    }
    // examine 质检单数据 - 列表Export
    rpc ExamineDscOrderListExport(ExamineDscOrderListReq) returns (Empty) {
        option (google.api.http) = {
            post: "/api/examine/order/dsc/list_export"
            body: "*"
        };
    }
    // examine 质检单数据 - 统计数据
    rpc ExamineDscOrderStats(ExamineDscOrderStatsReq) returns (ExamineDscOrderStatsResp) {
        option (google.api.http) = {
            post: "/api/examine/order/dsc/stats"
            body: "*"
        };
    }

    // examine 质检单红点通知 - 是否有红点
    rpc ExamineOrderNoticeAcceptor(Empty) returns (ExamineOrderNoticeAcceptorResp) {
        option (google.api.http) = {
            post: "/api/examine/order/notice/acceptor"
            body: "*"
        };
    }
    // examine 质检单红点通知 - 列表
    rpc ExamineOrderNoticeList(ExamineOrderNoticeListReq) returns (ExamineOrderNoticeListResp) {
        option (google.api.http) = {
            post: "/api/examine/order/notice/list"
            body: "*"
        };
    }
}

// 打分表配置 - 列表 - req
message ExamineTplListReq{
    // 标题
    string tpl_desc = 1;
    // 页码
    uint32 page = 2;
    // 页大小
    uint32 page_size = 3;
}
// 打分表配置 - 列表 - resp
message ExamineTplListResp{
    message ExamineTpl{
        // id
        uint64 id = 1;
        // 打分表名字
        string tpl_desc = 2;
        // 操作人
        string operator = 3;
        // 操作时间
        string updated_at = 4;
        // 当前状态: true/false
        bool enable = 5;
    }
    uint32 current_page = 1;
    uint32 per_page = 2;
    int64 total = 3;
    repeated ExamineTpl data = 4;
}

// 打分表配置 - 详情 - req
message ExamineTplDetailReq{
    // 打分表 id
    uint64 tpl_id = 1;
}
// 打分表配置 - 详情 - 模块 字段定义
message ExamineFieldDt{
    // 字段名
    string field_name = 1;
    // 字段类型
    ExamineFieldTpDf field_type = 3;
    // 字段值选项
    repeated string field_opts = 4;
}
// 打分表配置 - 详情 - 模块定义
message ExamineModuleDt {
    // 模块名称 gotags: validate:"required"
    string name = 1;
    // 考核项 gotags: validate:"required"
    repeated ExamineFieldDt field_detail = 2;
}
// 打分表配置 - 详情 - resp
message ExamineTplDetailResp{
    // 打分表名字：
    string tpl_desc = 1;
    // 模块定义
    repeated ExamineModuleDt module_detail = 2;
}
message ExamineTplOptsResp{
    message ExamineTplOpts{
        // 打分表 id
        uint64 tpl_id = 1;
        // 打分表名字
        string tpl_dsc = 2;
    }
    // 打分表选项
    repeated ExamineTplOpts list = 1;
}
// 打分表配置 - 保存 - req
message ExamineTplSaveReq{
    // id
    uint64 id = 1;
    // 打分表名字 @gotags: validate:"required"
    string tpl_desc = 2;
    // 模块定义 @gotags: validate:"required"
    repeated ExamineModuleDt module_detail = 3;
}

// 打分表配置 - 复制 - req
message ExamineTplCopyReq{
    // 打分表 id @gotags: validate:"required"
    uint64 from_tpl_id = 1;
    // 复制打分表字段信息： @gotags: validate:"required"
    string tpl_desc = 2;
}

// 打分表配置 - 保存 - resp
message ExamineTplSaveResp{
    uint64 tpl_id = 1;
}
// 打分表配置 - del - req
message ExamineTplDelReq{
    // 打分表 id
    uint64 tpl_id = 1;
}

// 任务配置 - 列表 - req
message ExamineTaskListReq{
    // 质检任务名称
    string task_name = 1;
    // 页码
    uint32 page = 2;
    // 页大小
    uint32 page_size = 3;
}
// 任务配置 - 列表 - resp
message ExamineTaskListResp{
    message ExamineTask{
        // id
        uint64 id = 1;
        // 任务名称
        string task_name = 2;
        // 任务状态
        ExamineTaskStateDf status = 3;
        // 操作人
        string operator = 4;
        // 操作时间
        string updated_at = 5;
        // 快照详情
        string link = 6;
    }
    uint32 current_page = 1;
    uint32 per_page = 2;
    int64 total = 3;
    repeated ExamineTask data = 4;
}

// 任务配置 - 详情 - req
message ExamineTaskDetailReq{
    uint64 id = 1;
}
// 任务配置 - 详情 - resp
message ExamineTaskDetailResp{
    // 任务id
    uint64 id = 1;
    // 任务名称
    string task_name = 2;
    // 游戏
    string project = 3;
    // 关联打分表
    uint64 tpl_id = 4;
    // 抽检库分组
    ExamineTaskGroupDf task_group = 5;

    // discord 抽查规则 - 回复时间
    repeated string filter_dsc_replied_at = 20;
    // discord 抽查规则 - 全检标准：充值金额
    int64 filter_dsc_all_total_pay_gte = 21;
    // discord 抽查规则 - 剩下抽检范围 - 是否全部： true 全部；false 部分
    bool filter_dsc_other_all = 22;
    //    // discord 抽查规则 - 剩下抽检范围-部分-圈选规则-标签
    //    repeated string filter_dsc_other_label = 23;
    // discord 抽查规则 - 剩下抽检范围-部分-圈选规则-vip 状态
    repeated uint32 filter_dsc_other_vip_state = 24;
    // discord 抽查规则 - 剩下抽检范围-部分-圈选规则-维护人
    repeated string filter_dsc_other_maintainer = 25;
    // discord 抽查规则 - 剩下抽检范围-部分-圈选规则-抽检数量
    int32 filter_dsc_other_num = 26;
    // discord 抽查规则 - 剩下抽检范围-部分-圈选规则-分配人员
    repeated string filter_dsc_assign_account = 27;
}
// 任务配置 - 保存 - req
message ExamineTaskSaveReq{
    // 任务名称 @gotags: validate:"required"
    string task_name = 1;
    // 游戏 @gotags: validate:"required"
    string project = 2;
    // 关联打分表 @gotags: validate:"required"
    uint64 tpl_id = 3;
    // 抽检库分组 @gotags: validate:"required,gte=1"
    ExamineTaskGroupDf task_group = 4;
    // discord 抽查规则 - 抽检方式 @gotags: validate:"required"
    ExamineTaskRulesDf task_rule = 5;
    // discord抽查规则-回复时间 @gotags: validate:"required_if=TaskGroup 2"
    repeated string filter_dsc_replied_at = 20;
    // discord抽查规则-全检标准:充值金额 @gotags: validate:"required_if=TaskGroup 2"
    int64 filter_dsc_all_total_pay_gte = 21;
    // discord抽查规则-剩下抽检范围-是否全部：true:全部；false:部分
    bool filter_dsc_other_all = 22;
    //    // discord抽查规则-剩下抽检范围-部分-圈选规则-标签
    //    repeated string filter_dsc_other_tag = 23;
    // discord抽查规则-剩下抽检范围-部分-圈选规则-维护人
    repeated string filter_dsc_other_maintainer = 24;
    // discord抽查规则-剩下抽检范围-部分-圈选规则-vip状态
    repeated uint32 filter_dsc_other_vip_state = 25;
    // discord抽查规则-剩下抽检范围-部分-圈选规则-抽检数量"
    uint32 filter_dsc_other_num = 26;
    // discord抽查规则-剩下抽检范围-部分-圈选规则-分配人员" @gotags: validate:"required_if=TaskGroup 2"
    repeated string filter_dsc_assign_account = 27;
    // 详情快照图地址
    string link = 28;
}

message ExamineTaskDscFilterCountReq{
    // 游戏 @gotags: validate:"required"
    string project = 1;
    // 回复时间 @gotags: validate:"required"
    repeated string replied_at = 2;
    // 充值金额 @gotags: validate:"required,gte=1"
    int64 total_pay = 3;
}

// 抽检数量
message ExamineTaskDscPartFilterCountReq{
    // 游戏 @gotags: validate:"required"
    string project = 1;
    // 回复时间 @gotags: validate:"required"
    repeated string replied_at = 2;
    // 充值金额 @gotags: validate:"required,gte=1"
    int64 total_pay = 3;
    // 客服
    repeated string maintainer = 4;
    // vip状态
    repeated uint32 vip_state = 5;
    // 是否抽检
    bool is_part = 6;
}

message ExamineTaskDscFilterCountResp{
    // 数量
    int64 num = 1;
}

// 质检单 - 搜索 - req
message ExamineDscOrderListReq{
    // 游戏
    repeated string project = 1;
    // 质检单 ID
    uint64 dsc_examine_id = 2;
    // 质检状态
    repeated ExamineStateDf status = 3;
    // 质检结果
    repeated ExamineFinalResultDf final_result = 4;
    // 被检员：质检打分时关联的员工
    repeated string related_account = 5;
    // 质检员：进行质检打分的人员
    repeated string inspector = 6;
    // 创建时间：质检任务的创建时间
    repeated string created_at = 7;
    // 结案时间：质检任务的完成时间
    repeated string finished_at = 8;
    // 玩家VIP状态
    repeated uint32 vip_state = 12;
    // 页码
    uint32 page = 9;
    // 页大小
    uint32 page_size = 10;
}

// 质检单 - 搜索 - resp
message ExamineDscOrderListResp {
    message ExamineDscOrder {
        // 质检单 ID
        uint64 dsc_examine_id = 1;
        // 任务id
        uint64 task_id = 6;
        // 模板id
        uint64 tpl_id = 7;
        // 游戏
        string project = 2;
        // 玩家 DC ID
        string dsc_user_id = 3;
        // 玩家名称 - 前端展示使用此字段
        string user_name = 4;
        // dm channel
        string dm_channel = 5;
        // 累付金额
        double total_pay = 8;
        // 最近30天付费金额
        double pay_last_thirty_days = 9;
        // 最近登录时间
        string last_login = 10;
        // 玩家信息回复状态
        uint32 replied_status = 11;
        // 玩家VIP状态
        uint32 vip_state = 12;
        string fpid = 13;
        uint64 uid = 14;
        string sid = 15;
        // 机器人 user_id
        string bot_id = 16;
        // 机器人昵称
        string bot_show = 19;
        // 质检状态
        ExamineStateDf status = 20;
        // 质检结果
        ExamineFinalResultDf final_result = 21;
        // 创建时间：质检任务的创建时间
        string created_at = 22;
        // 结案时间：质检任务的完成时间
        string finished_at = 23;
        // 质检员
        string inspector = 24;
    }
    uint32 current_page = 1;
    uint32 per_page = 2;
    int64 total = 3;
    repeated ExamineDscOrder data = 4;
}

// 质检单 - 详情 - req
message ExamineDscOrderDetailReq{
    // 质检单 ID
    uint64 dsc_examine_id = 1;
}

// 质检单 - 详情 - resp
message ExamineDscOrderDetailResp{
    message Common {
        // 质检结果 @gotags: validate:"required,gt=0"
        ExamineFinalResultDf final_result = 1;
        // 质检分数
        int32 final_score = 2;
        // 错误根源
        string final_reason = 3;
        // 质检结果关联员工：下拉多选，支持搜索
        repeated string related_account = 4;
        // 同步质检结果：
        repeated string notice_account = 5;
        // 质检问题描述
        string final_desc = 6;
        // 修改原因备注
        string final_result_modify_comment = 7;
    }
    // 质检单 - id
    uint64 dsc_examine_id = 1;
    // 任务id
    uint64 task_id = 2;
    // 模板id
    uint64 tpl_id = 3;
    // 项目
    string project = 4;
    // 玩家 DC ID
    string dsc_user_id = 5;
    // 机器人 id
    string dsc_bot_id = 6;
    // 渠道id
    string dsc_channel_id = 7;
    // 质检员
    string inspector = 8;
    // 质检状态
    ExamineStateDf status = 9;
    // 创建时间
    string created_at = 10;
    // 结案时间
    string finished_at = 11;
    // 是否可以编辑：true:可编辑； false:不可编辑
    bool can_edited = 12;

    // 自定义表单数据
    google.protobuf.Any define_field = 20;
    // 通用结果字段
    Common common_field = 21;
}
// 质检单 - 保存 - req
message ExamineDscOrderSaveReq{
    message Common {
        // 质检结果 @gotags: validate:"required,gt=0"
        ExamineFinalResultDf final_result = 1;
        // 质检分数
        int32 final_score = 2;
        // 错误根源
        string final_reason = 3;
        // 质检结果关联员工：下拉多选，支持搜索 @gotags: validate:"required"
        repeated string related_account = 4;
        // 同步质检结果：
        repeated string notice_account = 5;
        // 质检问题描述
        string final_desc = 6;
        // 修改原因备注
        string final_result_modify_comment = 7;
    }
    // 质检单-id
    uint64 dsc_examine_id = 1;
    // 自定义表单数据 eg: {"操作1": ["xxxx","xx"],"操作2": "xxxx"}
    google.protobuf.Any define_field = 20;
    // 通用结果字段
    Common common_field = 21;
}
// discord 质检单统计数据 - req
message ExamineDscOrderStatsReq {
    repeated string project = 1;
}
// discord 质检单统计数据 - resp
message ExamineDscOrderStatsResp {
    // 全部数据量
    int64 examine_dsc_count = 1;
    // 待处理质检任务
    int64 examine_dsc_wait_count = 2;
    // 我的待处理质检任务
    int64 examine_dsc_mine_wait_count = 3;
    // 我的已完成质检任务
    int64 examine_dsc_mine_done_count = 4;
    // 质检不合格任务
    int64 examine_dsc_unqualified_count = 5;
    // 我的质检不合格任务
    int64 examine_dsc_mine_unqualified_count = 6;
}

// 质检 - 红点
message ExamineOrderNoticeAcceptorResp{
    // 是否有 未读消息
    bool new_notice = 1;
}

message ExamineOrderNoticeListReq{
    // 页码
    uint32 page = 2;
    // 页大小
    uint32 page_size = 3;
}

// 打分表配置 - 列表 - resp
message ExamineOrderNoticeListResp{
    message ExamineNoticeDetail{
        // id
        uint64 id = 1;
        // 消息类型
        string msg_group = 2;
        // 抽检库分组
        ExamineTaskGroupDf task_group = 3;
        // 抽检单id
        uint64 detail_id = 4;
        // 通知人
        string to_user = 5;
        // 创建时间
        string created_at = 6;
        // 当前状态-是否已读: true/false
        bool has_read = 7;
    }
    uint32 current_page = 1;
    uint32 per_page = 2;
    int64 total = 3;
    repeated ExamineNoticeDetail data = 4;
}
