syntax = "proto3";

package pb;
option go_package = ".;pb";

// TeamConfigAddReq 新增团队配置
message TeamConfigAddReq {
  // 团队名称 @gotags: validate:"required"
  string team_name = 1;
  // 团队成员 @gotags: validate:"required"
  string team_member = 2;
}

// TeamConfigEditReq 编辑修改团队配置信息
message TeamConfigEditReq {
  // 团队Id @gotags: validate:"required"
  uint32 team_id = 1;
  // 团队名称
  string team_name = 2;
  // 团队成员
  string team_member = 3;
}

message TeamConfigDelReq {
  // 团队Id @gotags: validate:"required"
  uint32 team_id = 1;
}

message TeamConfigListReq {
  string team_name = 1;
  string team_member = 2;
  uint32  page = 3;
  uint32  page_size = 4;
  bool isAll = 5; // 分页or全部
}

message TeamConfigListResp {
  message Detail {
    int64 team_id = 1;
    string team_name = 2;
    string team_member = 3;
    string update_time = 4;
    string updater = 5;
  }
  uint32 current_page = 1;
  uint32 per_page = 2;
  uint32 total = 3;
  repeated Detail data = 4;
}