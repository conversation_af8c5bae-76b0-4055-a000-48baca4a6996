syntax = "proto3";

package pb;
option go_package = ".;pb";

import "enum.proto";

// WorkbenchPart 我的工单
enum WorkbenchPart {
    AllPart = 0;
    // 1:待处理工单部分
    PendingPart = 1;
    // 2:已处理工单部分
    DonePart = 2;
    // 3:待补填工单部分
    FillUpPart = 3;
}


// TicketPoolListReq 工单池列表响应结果
message TicketPoolListResp {
    uint32 current_page = 1;
    uint32 per_page = 2;
    uint32 total = 3;
    repeated TicketInfo data = 4;
}


message TicketInfo {
    // 工单ID
    uint64 ticket_id = 1;
    // 所属项目
    string project = 2;
    // 问题分类
    //    string category = 3;
    // 工单来源
    uint32 origin = 4;
    // 工单节点
    uint32 conversion_node = 5;
    // 状态
    uint32 status = 6;
    // 创建时间
    string created_at = 7;
    // 结案时间
    string closed_at = 8;
    // 创建人
    string creator = 9;
    // 受理员
    string acceptor = 10;
    // 语言
    string lang = 11;
    // vip等级
    uint32 vip = 12;
    // 紧急度
    uint32 priority = 13;
    // account_id
    string account_id = 15;
    // csi
    uint32 csi = 16;
    // 评语
    string comments = 17;
    // 团队 工单池
    //    string team = 18;  //todo
    // conversion_node
    //    uint32 transfer = 19;  //todo
    // 流转对象 todo
    //  Transfer transfer_node = 20;
}


// 补填状态
enum FillStatus {
    FillNone = 0;
    // 玩家已补填单
    PlayerRefillSt = 1;
    // VIP已补填单
    VipRefillSt = 2;
}

//工单总量请求参数
message TicketCountReq {
    // 游戏权限
    repeated string project = 1;
    // 工单状态
    repeated uint32 stage = 2;
    // 受理人
    repeated string acceptor = 3;
    // 语言
    repeated string lang = 4;
    // VIP
    repeated uint32 vip = 5;
    // 升级区
    uint32 priority = 6;
}

//工单总量响应值
message TicketCountResp {
    // 待处理工单条数
    uint64 pending_count = 1;
    // 当前用户待处理工单量
    uint64 user_pending_count = 2;
    // 当前用户已完成工单量
    uint64 user_completed_count = 3;
    // 待处理中文工单条数
    uint64 pending_cn_count = 4;
    // 待处理vip工单条数
    uint64 pending_vip_count = 5;
    // 升级区工单条数
    uint64 priority_count = 6;

}


// TicketPoolNewListReq 新工单池请求参数
message TicketPoolNewListReq {
    // 游戏
    repeated string project = 1;
    // 创建时间
    repeated string created_at = 2;
    // 结单时间
    repeated string closed_at = 3;
    // 评价时间
    repeated string evaluate_at = 4;
    // 工单状态  - 工单池
    repeated uint32 status = 5;
    // 工单来源
    repeated uint32 scene = 6;
    // 渠道包
    repeated string channel = 7;
    // 标签
    repeated uint32 label = 8;
    // NPS
    repeated uint32 nps = 9;
    // 工单评星
    repeated uint32 csi = 10;
    // 工单语言
    repeated string language = 11;
    // 服务器
    string sid = 12;
    // 工单ID
    uint64 ticket_id = 13;
    // 处理人类型 1:空白 2:包含
    FilterEnum acceptor_type = 14;
    // 处理人 - 工单池
    string acceptor = 15;
    // 备注
    string remark = 16;
    // 提交人查询类型 1:玩家fpid 2：玩家姓名 3:客服账号，4：account_id 5:uid
    CreatorType creator_type = 17;
    // 提交人查询值
    string creator = 18;
    // 页码
    uint32 page = 19;
    // 页大小
    uint32 page_size = 20;
    // 问题分类
    repeated uint32 cat_id = 21;
    repeated uint32 system_label = 22;
    // 表单信息
    string field = 23;
    // 是否是 VIP: true：表示是 VIP
    bool is_vip = 24;
    // 是否是 升级单： true: 表示是 升级单
    bool is_upgrade = 25;
    // 标签类型
    FilterTagEnum tag_type = 26;
    // 数据排序方式： 1：等待时长； 2：创建时间；3：充值金额
    TkPoolSort sort_by = 27;
    // 排序顺序，升序asc 逆序desc 默认asc
    string order = 28;
    // 查询类型
    uint32 search_type = 29; // 提单用户1,问题用户2
    // 用户类型
    repeated uint32 user_type = 30; // 长期VIP1，限时VIP2，付费用户3，普通用户4
    // 0默认全部，1是，2不是
    string svip = 31;
    // // 0默认全部，1是，2不是
    string vip_crm = 32; //1 0
    // 工单号s
    string ticket_ids = 33;
    // 客服s
    repeated string acceptors = 34;
    // 团队
    repeated int64 team_ids = 35;
    // 重开次数
    string reopen_num = 36;
    // 升级次数
    string upgrade_num = 37;
    // 渠道号
    repeated string packageId = 38;
    // 游戏版本
    repeated string game_version = 39;
    // 工单处理类型
    repeated uint32 solve_type = 40;
    // 私域R级参数
    repeated string zone_vip_level = 41;
    // 玩家累付金额
    repeated int64 pay_all = 42;
}

// 工单列表排序方式
enum TkPoolSort {
    TkPoolSortWaitDefault = 0;
    // 等待时长 - 默认
    TkPoolSortWaitTm = 1;
    // 创建时间
    TkPoolSortCreateTm = 2;
    // 充值金额
    TkPoolSortRecharge = 3;
}

// TicketPoolHistoryListReq 历史工单池请求参数
message TicketPoolHistoryListReq {
    // 工单ID
    uint64 ticket_id = 1;
    // 页码
    uint32 page = 2;
    // 页大小
    uint32 page_size = 3;
}

// TicketPoolNewListResp 新工单池响应参数
message TicketPoolNewListResp {
    message TicketPoolInfo{
        // 游戏
        string project = 1;
        // 工单ID
        uint64 ticket_id = 2;
        // 玩家输入第一句话
        string detail = 3;
        // 累计金额
        double recharge = 4;
        // 工单状态
        uint32 status = 5;
        // 等待时长
        string waiting_time = 6;
        // 服务评分
        uint32 csi = 7;
        // 当前处理人
        string acceptor = 8;
        // ss vip绿色通道单
        bool crm_vip_user_flag = 9;
        bool checked = 10;
        // 是否7日内在dc端发起过对话
        bool dc_flag = 11;
        // DC对话创建时间
        string dc_create_time = 12;
        // 用户AccountID
        string account_id = 13;
        //系统标签
        repeated uint32 system_label = 22;
    }

    uint32 current_page = 1;
    uint32 per_page = 2;
    uint32 total = 3;
    repeated TicketPoolInfo data = 4;
}


// TicketPoolNewTopResp 顶部信息
message TicketPoolTopResp {
    // 工单ID
    uint64 ticket_id = 1;
    // - 处理人（当没有处理人时，此处显示：待接单） todo 代码处理
    string acceptor = 2;
    // 玩家昵称
    string nickname = 3;
    // Fpid
    string account_id = 4;
    // Uid
    uint64 uid = 5;
    // 游戏版本
    string app_version = 6;
    // 服务器
    uint32 sid = 7;
    // 游戏
    string project = 8;
    // 升级单： 1：正常单；2：升级单
    uint32 priority = 9;
    // 当前工单流转状态
    TkStage status = 10;
    uint32 csi = 11;
}

// TicketPoolNewTopResp 顶部信息
message TicketPoolNewTopResp {
    message top{
        // 工单ID
        repeated uint64 ticket_id = 1;
        // - 处理人（当没有处理人时，此处显示：待接单） todo 代码处理
        string acceptor = 2;
        // 玩家昵称
        repeated uint64 nickname = 3;
        // Fpid
        repeated uint32 fpid = 4;
        // Uid
        repeated uint32 uid = 5;
        // 游戏版本
        repeated string game_version = 6;
        // 服务器
        repeated uint32 sid = 7;
    }

    message UserInfoResp {
        // 标签
        repeated int32 label = 1;
        // 问题类型
        string category = 2;
        // 创建时间
        string created_at = 3;

        // 渠道包
        string channel = 4;
        // 设备型号
        string device_type = 5;
        // 存储总量
        double rom_gb = 6;
        // 存储剩余总量
        double remain_rom = 7;
        // 充值金额
        string recharge = 8;
        // 玩家语言
        repeated string language = 9;
        // 国家
        repeated string country = 10;
        // 系统版本
        string os_version = 11;

    }
}

// TicketDialogueInfoResp 详情对话 todo  带时间戳
message TicketDialogueInfoResp {
    // 被举报人昵称
    // 被举报海域
    // 详情
    // 截图    以上调用接口

    // 对话信息
    repeated string dialogue_information = 1;
    // 客服备注消息
    repeated string remarks = 2;
    // 标签
    repeated int32 label = 3;

}

// TicketRecordResp 历史工单记录
message TicketRecordResp {
    message Record {
        // 游戏
        string project = 1;
        // 工单ID
        uint64 ticket_id = 2;
        // 玩家输入第一句话
        string detail = 3;
        // 累计金额
        double recharge = 4;
        // 工单状态
        uint32 status = 5;
        // 等待时长
        string waiting_time = 6;
    }
    uint32 current_page = 1;
    uint32 per_page = 2;
    uint32 total = 3;
    repeated Record data = 4;
}


// UserInfoResp 基础信息
message UserInfoResp {
    // 标签
    repeated string tag_name = 1;
    // 问题类型
    string category = 2;
    // 创建时间
    string created_at = 3;
    // 渠道包
    string channel = 4;
    // 设备型号
    string device_type = 5;
    // 存储总量
    double rom_gb = 6;
    // 存储剩余总量
    double remain_rom = 7;
    // 充值金额
    double recharge = 8;
    // 玩家语言
    string lang = 9;
    // 国家
    string country = 10;
    // 系统版本
    string os_version = 11;
    // IP地址
    string ip = 12;
    // 渠道号
    string packageId = 13;
    // 私域R级
    string zone_vip_level = 14;

}

// TicketAppraiseResp 工单评价信息
message TicketAppraiseResp {
    string created_at = 1;
    uint32 csi = 2;
    uint32 recommendation_level = 3;
    string remark = 4;
}

// 工单详情
message TicketResponse {
    // 工单详情 - 顶部信息
    TicketPoolTopResp top_info = 1;
    // 工单详情 - 基本信息
    UserInfoResp user_info = 2;
    // 工单详情 - 历史工单
    TicketRecordResp record_info = 3;
    // 工单详情 - 对话
    repeated TicketDialogueResp commu_info = 4;
    // 工单详情 - 分单日志
    repeated TicketHistResp history = 5;
    TicketAppraiseResp ticket_appraise = 6;
}


// AssignmentReq 指派请求参数
message AssignmentReq {
    // 工单ID @gotags: validate:"required"
    uint64 ticket_id = 1;
    // 其他客服 @gotags: validate:"required"
    string acceptor = 2;
}
// AssignmentReq 批量指派请求参数
message BatchAssignmentReq {
    // 工单ID @gotags: validate:"required"
    repeated uint64 ticket_id = 1;
    // 其他客服 @gotags: validate:"required"
    string acceptor = 2;
}
// TicketRemarkReq 添加工单备注
message TicketRemarkReq {
    // 工单ID @gotags: validate:"required,gte=1"
    uint64 ticket_id = 1;
    // 备注 @gotags: validate:"required"
    string content = 2;
}

// TicketBatchRemarkReq 批量
message TicketBatchRemarkReq {
    // 工单ID @gotags: validate:"required"
    repeated uint64 ticket_ids = 1;
    // 备注 @gotags: validate:"required"
    string content = 2;
}


// TicketTagReq 重新给现有的工单打标签
message TicketRetaggingReq {
    // 工单ID @gotags: validate:"required"
    uint64 ticket_id = 1;
    // 更新后的标签ID
    repeated uint32 label_id = 2;
}

message TicketTagRes {
    uint64  ticket_id = 1;
    repeated uint32 label_id = 2;
}

message TicketLabel {
    // 工单ID @gotags: validate:"required"
    uint64 ticket_id = 1;
    // 删除的标签ID
    repeated uint32 label_id = 2;
}

// 获取多个工单绑定的 label
message TicketTagsReq {
    // 工单ID @gotags: validate:"required,gt=0"
    repeated uint64 ticket_id = 1;
}

// 获取多个工单绑定的 label 相应详情
message TicketTagsResp {
    // 工单绑定关系
    repeated TicketLabel details = 1;
}

// TicketHistoryReq 工单日志请求
message TicketHistoriesReq {
    // 工单ID @gotags: validate:"required"
    uint64 ticket_id = 1;
    // 关联类型  1:工单 2:流程
    uint32 relate_type = 2;
}

// TicketIsUserRsq 客服在线状态
message TicketIsUserReq {
    // 客服工号
    uint32 empno = 1;
}

// TicketIsUserResp 客服在线状态
message TicketIsUserResp {
    // 客服在线状态  0:不在线 1:在线
    uint32 status = 1;
}

// TicketHistoriesResp 工单日志列表
message TicketHistoriesResp {
    // 操作日志
    repeated TicketHistoryInfo data = 1;
}


// TicketHistoryInfo 工单日志信息
message TicketHistoryInfo {
    // 工单ID
    uint64 ticket_id = 1;
    // 操作
    string operate = 2;
    // 子操作
    string op_detail = 3;
    // 备注
    string remark = 4;
    // 创建时间
    string created_at = 5;
    // 旧工单ID @gotags: json:"from_ticket_id,omitempty"
    uint64 from_ticket_id = 6;
}

// 工单表单 对话信息
message TicketDialogueResp {
    // 对话内容 、工单 form
    string detail = 1;
    string created_at = 2;
    // 操作员
    string operator = 3;
    //回复角色类型  1:玩家 2:客服 3:系统',
    uint32 from_role = 4;
    // '消息类型： CommuTypeDialogue：对话消息；CommuTypeRemark：增加备注',
    string commu_type = 5;
    // 工单详情 1:是工单form详情; 2: 重开提交信息
    uint32 is_ticket = 6;
    // 重开单 - 图片/视频资源
    string files = 7;
    // 时间 - 时间戳
    uint64 created_time = 8;
    // 玩家上传的图片
    string picture = 9;
    // 玩家上传的视频
    string video = 10;
}

// 工单 - 日志记录详情
message TicketHistResp {
    // 工单id
    uint64 ticket_id = 1;
    // 分组描述
    string type = 2;
    // 详情
    string  remark = 3;
    // 时间
    string created_at = 4;
}

// TicketHistoryInfo 分单日志信息
message TicketGroupInfoResp {
    // 工单ID
    uint64 ticket_id = 1;
    // 操作
    string operate = 2;
    // 子操作
    string op_detail = 3;
    // 备注
    string remark = 4;
    // 创建时间
    string created_at = 5;
    // 旧工单ID @gotags: json:"from_ticket_id,omitempty"
    uint64 from_ticket_id = 6;
}


// TicketStatusReq 工单状态
message TicketStatusResp {
    // 工单状态 1,待接单  2,待处理 3,处理中-待玩家回复 4,处理中-玩家已回复 5,超时关闭 6,重开 7,拒单关闭 8,已完成
    uint32 status = 1;
}

// TicketUserListReq 客服昵称列表
message TicketUserListReq {
    // 客服昵称
    uint32 nickname = 1;
}

// TicketId 工单ID
message TicketIdReq {
    // 工单ID @gotags: validate:"required"
    uint64 ticket_id = 1;
    // 使用哪个工单系统
    TicketSys ticket_sys_type = 2;
    // 页码
    uint32 page = 3;
    // 页大小
    uint32 page_size = 4;
}

message CustomerService {
    uint32 id = 1;
    int32 order_count = 2;
    int32 wait_time = 3;
}

enum TkTransferOpType{
    TkTransferOpTypeUnknown = 0;
    // 客服回复
    TkTransferOpTypeCommu = 1;
    // 回复&关单
    TkTransferOpTypeCommuClose = 2;
    // 拒单
    TkTransferOpTypeRejected = 3;

}
message TicketTransferReq {
    // 工单ID @gotags: validate:"required"
    uint64 ticket_id = 1;
    // 操作分类：1:客服回复;2:回复&关单;3:拒单 @gotags: validate:"required,gt=0"
    TkTransferOpType op_type = 2;
    // 回复内容
    string content = 3;
    // 是否同步到ai精灵
    bool is_sync_ai_elfin = 4;
    // 问题
    string question = 5;
    // 答案
    string answer = 6;
}
message TicketUpgradeReq{
    // 工单ID @gotags: validate:"required,gt=0"
    uint64 ticket_id = 1;
    // 工单升级/降级：1:降级；2:升级 @gotags: validate:"required,oneof=1 2"
    uint32 upgrade = 2;
}

//ai精灵报表统计过滤后工单量
message TicketCountStatisReq{
    Filter filter = 1;
    repeated int64 cat_ids = 2;
}
//用户过滤条件
message Filter{
    // 游戏标识 @gotags: validate:"required"
    string game_project = 1;
    // 开始时间：
    string start_time = 2;
    // 结束时间：
    string end_time = 3;
    // 渠道
    repeated string channel = 4;
    // 搜索语种
    repeated string lang = 5;
    // 多个区服id用逗号分隔，如："1,2,3"
    string server_ids = 6;
    // 区服数组，如: [[1,100],[101,200]]
    repeated ServerBtw server_arr = 7;
}

message ServerBtw {
    int64 gte = 1; // 区服ID - 大于等于
    int64 lte = 2; // 区服ID - 小于等于
}

// 退回工单池
message TicketReturnPoolReq{
    // 工单ID @gotags: validate:"required,gt=0"
    uint64 ticket_id = 1;
}

// 精分数据 - 游戏item - 道具 ID 保存
message DataPlatGameItemBatchSaveReq{
    // 游戏标识 @gotags: validate:"required"
    string project = 1;
    // 导入文件地址 @gotags: validate:"required"
    string file_name = 2;
}
// 精分数据 - 游戏item - 道具 ID list
message DataPlatGameItemListReq{
    // 游戏标识
    string project = 1;
    // 页码
    uint32 page = 19;
    // 页大小
    uint32 page_size = 20;
}
message DataPlatGameItemOptsReq{
    // 游戏标识 @gotags: validate:"required"
    string project = 1;
}
message DataPlatGameItemOptsResp {
    message Item {
        // 道具 id
        string value = 1;
        // 道具名称
        string label = 2;
    }
    repeated Item data = 1;
}
// 精分数据 - 游戏item - 道具 ID list resp
message DataPlatGameItemListResp {
    message ItemDetail {
        // id
        uint64  id = 1;
        // item_id
        string item_id = 2;
        // 道具名称
        string item_name = 3;
        // 更新时间
        string updated_at = 4;
        // 变更人
        string operator = 5;
    }
    uint32 current_page = 1;
    uint32 per_page = 2;
    uint32 total = 3;
    repeated ItemDetail data = 4;
}
// 精分数据 - 金币查询 req
message DataPlatUserGoldInfoReq{
    // 游戏标识
    string project = 1;
    // uid
    uint64 uid = 2;
    // 查询时间段 1天数据
    repeated string created_at = 3;
}

// 精分数据 - 金币查询 resp
message DataPlatUserGoldInfoResp {
    // 游戏标识
    string project = 1;
    // 变化量
    string change = 2;
    // 变化前
    string before = 3;
    // 变化后
    string after = 4;
    // 时间
    string change_time = 5;
    // 原因
    string reason = 6;
}

// 精分数据 - 物品(道具)查询 req
message DataPlatUserItemInfoReq{
    // 游戏标识
    string project = 1;
    // uid
    uint64 uid = 2;
    // 道具 id
    string item_id = 3;
    // 查询时间段
    repeated string created_at = 4;
}
// 精分数据 - 物品(道具)查询 resp
message DataPlatUserItemInfoResp{
    // 游戏标识
    string project = 1;
    // 物品 id
    string item_id = 2;
    // 物品名称
    string item_name = 3;
    // 变化量
    string change = 4;
    // 变化前
    string before = 5;
    // 变化后
    string after = 6;
    // 时间
    string change_time = 7;
    // 原因
    string reason = 8;
}


// 精分数据 - 支付查询 req
message DataPlatUserPayInfoReq{
    // 游戏标识
    string project = 1;
    // uid
    uint64 uid = 2;
    // 查询时间段
    repeated string created_at = 3;
}

// 精分数据 - 支付查询 resp
message DataPlatUserPayInfoResp{
    // 游戏标识
    string project = 1;
    // 商品名称 - iap_product_name
    string product_name = 2;
    // 支付渠道 - payment_processor
    string pay_channel = 3;
    // 价格 - price
    string price = 4;
    // 基础价格 - base_price
    string basic_price = 5;
    // 币种 - currency
    string currency = 6;
    // 支付结果 - result
    string status = 7;
    // 支付时间 - finish_time
    string paid_at = 8;
}

// 精分数据 - 登录查询 req
message DataPlatUserLoginInfoReq{
    // 游戏标识
    string project = 1;
    // uid
    uint64 uid = 2;
    // 查询时间段
    repeated string created_at = 3;
}
// 精分数据 - 登录查询 resp
message DataPlatUserLoginInfoResp{
    // 游戏标识
    string project = 1;
    // 登录设备
    string fp_device_id = 2;
    // 设备型号
    string device_type = 3;
    // 登录 IP
    string ip = 4;
    // IP 位置
    string ip_loc = 5;
    // 登录时间
    string login_at = 8;
}

// 工单备注支持保存草稿
message TicketDraftSaveReq{
    // 草稿id
    uint64 id = 1;
    // 工单id @gotags: validate:"required"
    uint64 ticket_id = 2;
    // 操作内容json @gotags: validate:"required"
    string content = 3;
}

message TicketDraftInfoReq{
    // 工单id @gotags: validate:"required"
    uint64 ticket_id = 1;
}

message TicketDraftInfoResp{
    // 草稿id
    uint64 id = 1;
    // 工单id
    uint64 ticket_id = 2;
    // 操作内容json
    string content = 3;
    // 上次保存时间
    string update_time = 4;
}

message TicketTabAddReq {
    // @gotags: validate:"required"
    string tab_name = 1;
    // @gotags: validate:"required,oneof=1 2"
    int32 public = 2;
    // 搜索条件组合 @gotags: validate:"required"
    TicketPoolNewListReq detail = 3;

}

message TicketTabEditReq {
    // @gotags: validate:"required"
    int64 id = 1;
    // @gotags: validate:"required"
    string tab_name = 2;
    // @gotags: validate:"required,oneof=1 2"
    int32 public = 3;
}

message TicketTabDelReq {
    // @gotags: validate:"required"
    int64 id = 1;
}

message TicketTabListResp {
    message TicketTabDetail {
        repeated TabInfo tab = 1;
        string project = 2;
    }
    message TabInfo{
        int64 id = 1;
        string tab_name = 2;
        TicketPoolNewListReq detail = 3;
        int32 public = 4;
        string operator = 5;
    }
    repeated TicketTabDetail data = 1;
}

message TicketTabCountResp {
    message TicketTabCount {
        repeated TabCountDetail tab = 1;
        string project = 2;
    }
    message TabCountDetail{
        string tab_name = 2;
        uint64 count = 5;
    }
    repeated TicketTabCount detail = 1;
}

message TicketTabUpdateSortReq {
    // @gotags: validate:"required"
    string sort_setting = 1;
}