syntax = "proto3";

package pb;
option go_package = ".;pb";

import "google/api/annotations.proto";
import "common.proto";

// 分类
enum CatLevel {
    UnKnown = 0;
    PrimaryClassification = 1;
    SecondaryClassification = 2;
    ThreeClassification = 3;
}

message Lang {
    map<string, string> category = 1;
}

// CatTplPrefer 三级分类个性化配置
message CatTplPrefer {
    repeated string channel = 1;
    string server_str = 2;
    repeated string country = 3;
    // 模版ID @gotags: validate:"required_if=CatLevel 3"
    uint32 tpl_id = 4;
    map<string, bool> mark = 5;
}

// CatProbSaveReq 修改三级分类 请求参数
message CatProbSaveReq {
    // 分类ID @gotags: validate:"required"
    uint32 cat_id = 1;
    // 分类名称 @gotags: validate:"required"
    string category = 2;
    // 模版ID @gotags: validate:"required_if=RelateType 1"
    uint32 tpl_id = 3;
    // 技能组ID
    uint32 group_id = 4;
    // 紧急度 1:高 2:中 3:低 @gotags: validate:"required_if=CatLevel 3"
    uint32 priority = 5;
    // 完成时间
    uint64 deadline = 6;
    // 分类的等级 @gotags: validate:"required,oneof=1 2 3"
    uint32 cat_level = 7;
    // 是否评价 是:true 否:false
    bool evaluation = 8;
    // 多语言
    Lang langs = 9;
    // 个性化模板配置
    repeated CatTplPrefer tpl_list = 10;
    // 回复模板id
    uint32 reply_tpl_id = 11;
    // 关联类型 1:表单 2:流程 @gotags: validate:"required_if=CatLevel 3"
    uint32 relate_type = 12;
    // 流程id @gotags: validate:"required_if=RelateType 2"
    uint32 process_id = 13;
    // 是否重开 1：是 0：否
    bool reopen = 14;
    // 重开次数
    uint32 reopen_num = 15;
}

// CatOptsList 问题分类配置(下拉级联多选)列表
message CatOptsListResp {
    message Cat {
        uint32 id = 1;     // 类别ID
        string label = 2;  // 类别名称
        uint32 level = 3;
        uint32 tpl_id = 4;
        // 关联类型 1:表单 2:流程
        uint32 relate_type = 5;
        // 自动化流程id
        uint32 process_id = 6;
        // 子结构 @gotags: json:"children,omitempty"
        repeated Cat children = 7;
    }
    repeated Cat data = 1;
}

// CatIdReq 类别Id 请求参数
message CatIdReq {
    // 分类ID @gotags: validate:"required"
    uint32 cat_id = 1;
}

// CatInfo 类别信息
message CatInfoResp {
    uint32 cat_id = 1;
    // 分类名称
    string category = 2;
    // 一级分类
    uint32 one_level = 3;
    // 二级分类
    uint32 second_level = 4;
    // 分类层级
    uint32 cat_level = 5;
    // 模版ID
    uint32 tpl_id = 6;
    // 关联问题分类
    reserved 7;
    // 紧急度 1:高 2:中 3:低
    uint32 priority = 8;
    // 完成时间
    uint64 deadline = 9;
    // 启用 0:false 1:true
    bool enable = 10;
    // 是否评价 是:true 否:false
    bool evaluation = 11;
    // op @gotags: json:"operator,omitempty"
    string operator = 12;
    // 更新时间 @gotags: json:"updated_at,omitempty"
    string updated_at = 13;
    // 多语言 @gotags: json:"langs,omitempty"
    Lang langs = 14;
    // 个性化模板配置
    repeated CatTplPrefer tpl_list = 15;
    // 回复模板id
    uint32 reply_tpl_id = 16;
    // 关联类型 1:表单 2:流程
    uint32 relate_type = 17;
    // 流程id
    uint32 process_id = 18;
    // 是否重开 1：是 0：否
    bool reopen = 19;
    // 重开次数
    uint32 reopen_num = 20;
}


// CatInfoListResp 三级类别列表
message CatInfoListResp {
    // 当前页
    uint32 current_page = 1;
    // 页大小
    uint32 per_page = 2;
    // 总数
    uint32 total = 3;
    // 数据列表
    repeated CatInfoResp data = 4;
}

message CatOptsReq {
    // 项目 @gotags: validate:"required"
    string project = 1;
    // 语言
    string lang = 2;
}

// CatOptsResp 问题分类配置(下拉级联多选)列表
message CatOptsResp {
    message Cat {
        uint32 id = 1;     // 类别ID
        string label = 2;  // 类别名称
        uint32 level = 3;
        // 子结构 @gotags: json:"children,omitempty"
        repeated Cat children = 7;
    }
    repeated Cat data = 1;
}

// CatItems cat item
message CatItems {
    uint32 cat_id = 1;
    uint32 one_level = 2;
    uint32 second_level = 3;
    uint32 level = 4;
    string category = 6;
    string lang_category = 7;
}

// 工单分类scope
enum CatScopeEnum {
    CatScopeDefault = 0;
    CatScopeInner = 1;
    CatScopeAll = -1;
}


message CatProjectLang {
    // 项目 @gotags: validate:"required"
    string project = 1;
    // 语言
    string lang = 2;
    // 范围 0:默认 1:内部分类
    CatScopeEnum scope = 3;
}



service CategoryBackendAPI {
    // 模版发布- 清除缓存
    rpc CategoryRelease(CatProjectLang) returns (Empty) {
        option (google.api.http) = {
            post: "/api/cat/release"
            body: "*"
        };
    }
}