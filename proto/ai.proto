syntax = "proto3";

package pb;
option go_package = ".;pb";

// AISummaryReq ai-总结
// 需要传工单号对工单详情进行总结
message AISummaryReq{
  // 工单ID @gotags: validate:"required"
  uint64 ticket_id = 1;
}

// AISummaryReq ai-润色
message AIPolishReq{
  // 工单ID @gotags: validate:"required"
  uint64 ticket_id = 1;
  // 润色内容 @gotags: validate:"required"
  string content = 2;
  // 润色类型 @gotags: validate:"required"
  string style = 3;
}
// AIPreReplyReq ai-预回复
message AIPreReplyReq{
  // 工单ID @gotags: validate:"required"
  uint64 ticket_id = 1;
  // 预回复内容
  string content = 2;
}

// AIFaqReq ai-faq
message AIFaqReq{
  // 工单ID @gotags: validate:"required"
  uint64 ticket_id = 1;
  // 预回复内容 @gotags: validate:"required"
  string content = 2;
}

// AIFaqResp ai-faq
message AIFaqResp{
  // 问题
  string question = 1;
  // 答案
  string answer = 2;
}