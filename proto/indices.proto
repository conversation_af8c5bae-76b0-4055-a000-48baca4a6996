syntax = "proto3";

package pb;
option go_package = ".;pb";

import "common.proto";
import "google/api/annotations.proto";

service IndicesApi {
  // 顶部数据-配置内容列表接口
  rpc Config(ProjectLang) returns (IndicesCfgResp) {
    option (google.api.http) = {
      post: "/api/indices/cfg"
      body: "*"
    };
  }
  // 顶部数据-列表接口
  rpc List(ProjectLang) returns (IndicesListResp) {
    option (google.api.http) = {
      post: "/api/indices/list"
      body: "*"
    };
  }
  // 顶部数据-新增/保存接口
  rpc Save(IndicesSaveReq) returns (Empty) {
    option (google.api.http) = {
      post: "/api/indices/save"
      body: "*"
    };
  }
  // 顶部数据-启用/禁用接口
  rpc Enable(IndicesEnableReq) returns (Empty) {
    option (google.api.http) = {
      post: "/api/indices/enable"
      body: "*"
    };
  }
  // 顶部数据-删除接口
  rpc Delete(IndicesDelReq) returns (Empty) {
    option (google.api.http) = {
      post: "/api/indices/del"
      body: "*"
    };
  }
  // 顶部数据-发布接口
  rpc Release(ProjectLang) returns (Empty) {
    option (google.api.http) = {
      post: "/api/indices/release"
      body: "*"
    };
  }
}

message IndicesListResp {
  message item {
    uint64 id = 1;
    string cat_type = 2;
    string name = 3;
    repeated uint32 cat_ids = 4;
    string updated_at = 5;
    string updated_user = 6;
    string project = 7;
    // 启用true 禁用false
    bool enable = 8;
  }
  // 列表
  repeated item data = 1;
}

message IndicesSaveReq {
  // 保存接口必传，新增接口不传
  uint64 id = 1;
  // 配置内容类型 @gotags: validate:"required"
  string cat_type = 2;
  // 关联问题分类 @gotags: validate:"required"
  repeated uint32 cat_ids = 3;
  // project 新增接口必传
  string project = 4;
}

message IndicesEnableReq {
  // id @gotags: validate:"required"
  uint64 id = 1;
  // 启用true 禁用false
  bool enable = 2;
}

message IndicesDelReq {
  // id @gotags: validate:"required"
  uint64 id = 1;
}

message IndicesCfgResp {
  // 内容配置json
  map<string, string> cat_types = 1;
}

