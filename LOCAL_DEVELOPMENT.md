# ops-ticket-api 本地开发环境指南

本分支 (`feature/local-development-setup`) 包含了运行 ops-ticket-api 项目所需的本地开发环境配置，支持两种数据库连接方式。

## 🚀 快速启动

### 🌟 方式一：使用远程测试数据库（推荐）

**优势**: 真实测试数据、无需Docker、快速启动

```bash
# 直接使用远程AWS RDS数据库启动
go run main.go -c ./config/global-test.yaml
```

**特点**:
- ✅ 包含完整的真实测试数据（工单、用户、配置等）
- ✅ 无需启动本地数据库服务
- ✅ 与测试环境数据保持同步
- ✅ 一条命令即可启动

### 🐳 方式二：使用本地Docker数据库

**优势**: 完全离线、数据隔离、可自定义

#### 1. 启动依赖服务

```bash
# 启动 MySQL, Redis, Elasticsearch
docker-compose up -d

# 检查服务状态
docker-compose ps
```

#### 2. 启动API服务

```bash
# 使用 Makefile（使用 dev.yaml 配置）
make run

# 或直接使用 Go
go run main.go -c ./config/dev.yaml
```

## 🔍 验证服务

启动成功后，访问以下地址验证服务：

- **🌐 API服务**: http://localhost:9000
- **❤️ 健康检查**: http://localhost:9000/health
- **📚 API文档**: http://localhost:9000/swagger/index.html

### 快速测试API

```bash
# 测试工单Tab列表（应返回真实数据）
curl -X POST 'http://localhost:9000/api/ticket/tab/list' \
  -H 'content-type: application/json' \
  -H 'lang: zh-cn' \
  --data-raw '{}'
```

## 📋 依赖服务对比

### 🌟 远程测试数据库
| 服务 | 地址 | 说明 |
|------|------|------|
| MySQL 8.0 | AWS RDS (us-west-2) | 测试环境主从数据库 |
| Redis | 测试环境 | 缓存和消息队列 |
| Elasticsearch | 测试环境 | 搜索引擎 |

### 🐳 本地Docker服务
| 服务 | 端口 | 用途 |
|------|------|------|
| MySQL 8.0 | 3306 | 本地主数据库 |
| Redis 7-alpine | 6379 | 本地缓存和消息队列 |
| Elasticsearch 7.17.0 | 9200 | 本地搜索引擎 |

## 🔧 配置文件详解

| 配置文件 | 用途 | 数据库 | 认证 |
|---------|------|--------|------|
| `config/global-test.yaml` | **远程测试环境** | AWS RDS 测试库 | 跳过认证 ✅ |
| `config/dev.yaml` | 本地开发环境 | 本地Docker | 跳过认证 ✅ |
| `config/local.yaml` | 本地配置模板 | 本地Docker | 需要认证 ❌ |
| `docker-compose.yml` | Docker服务编排 | - | - |

## � 数据库详情

### 🌟 远程测试数据库（推荐）

**连接信息**:
- **主库**: `all-service-offline.cluster-c6h1gwzealou.us-west-2.rds.amazonaws.com:3306`
- **从库**: `all-service-offline.cluster-ro-c6h1gwzealou.us-west-2.rds.amazonaws.com:3306`
- **数据库**: `fp_ops_ticket_new_test`, `fp_ops_ticket_test`
- **用户**: `opstools` / `Funplus@888`

**数据统计**:
- 🎫 工单Tab配置: 15+ 条记录（mo_global, wm_global, ss_global）
- 🔐 认证配置: 18 条记录
- 🤖 Discord机器人: 4 条配置
- 🎮 游戏道具: 13,588 条记录
- 🌍 国际化数据: 552 条记录

### 🐳 本地Docker数据库

**特点**:
- 完全离线运行
- 数据隔离，不影响测试环境
- 可自定义数据和配置
- 需要手动导入数据结构

## 🧪 测试API

```bash
# 测试工单Tab列表
curl -X POST 'http://localhost:9000/api/ticket/tab/list' \
  -H 'content-type: application/json' \
  -H 'lang: zh-cn' \
  --data-raw '{}'

# 测试系统枚举
curl -X POST 'http://localhost:9000/api/addons/enum' \
  -H 'content-type: application/json' \
  -H 'lang: zh-cn' \
  --data-raw '{}'
```

## 🔄 分支管理

### 切换到开发分支
```bash
git checkout feature/local-development-setup
```

### 切换回主分支
```bash
git checkout master
```

### 更新开发分支
```bash
git checkout feature/local-development-setup
git add .
git commit -m "你的提交信息"
```

## 🛑 停止服务

```bash
# 停止API服务
# 在运行 make run 的终端按 Ctrl+C

# 停止依赖服务
docker-compose down

# 停止并删除数据卷（谨慎使用）
docker-compose down -v
```

## ⚠️ 注意事项

1. **认证跳过**: 当前配置跳过了JWT认证，仅用于开发测试
2. **外部依赖**: 某些API需要连接外部服务，在本地环境可能会失败
3. **数据持久化**: 数据库数据保存在Docker volumes中
4. **端口冲突**: 确保端口 3306, 6379, 9000, 9200 未被占用

## 🔍 故障排除

### 端口被占用
```bash
# 检查端口占用
lsof -i :9000
lsof -i :3306

# 停止占用端口的进程
kill -9 <PID>
```

### Docker 问题
```bash
# 重启 Docker 服务
# macOS: 重启 Docker Desktop
# Linux: sudo systemctl restart docker

# 清理 Docker 资源
docker system prune -f
```

### 数据库连接问题
```bash
# 检查 MySQL 容器日志
docker logs ops-ticket-mysql

# 手动连接测试
docker exec -it ops-ticket-mysql mysql -u root -pmysqluser
```

## 📞 支持

如果遇到问题，请检查：
1. Docker 是否正常运行
2. 所需端口是否可用
3. 配置文件是否正确
4. 依赖服务是否启动成功
