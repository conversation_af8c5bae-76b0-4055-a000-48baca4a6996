---
description: 后台api规范
globs: 
---

参考示例 
- 接口路径 
    /api/team_config/

- 路由配置文件
commands/cmds/router/backend.go
```
	// 团队配置
	apiRoute.POST("/team_config/add", inner.AddTeamConfig)   // 新增
	apiRoute.POST("/team_config/edit", inner.EditTeamConfig) // 修改
	apiRoute.POST("/team_config/list", inner.TeamConfigList) // 查询
	apiRoute.POST("/team_config/del", inner.DelTeamConfig)   // 删除
```

- 接口描述文件
proto/team_config.proto
proto/pb/team_config.pb.go

- handlers控制器
handlers/inner/team_config.go
```
// TeamConfigList 团队配置查询
func TeamConfigList(ctx echo.Context) error {
	var req = &pb.TeamConfigListReq{}
	if err := validator.BindValidate(ctx, req); err != nil {
		return err
	}
	// get data
	resp, err := configure.TeamConfigList(ctx, req)
	if err != nil {
		return err
	}
	return ctxresp.Out(ctx, resp)
}
```

- services层
services/configure/team_config.go
```
// TeamConfigList 团队配置列表
func TeamConfigList(ctx echo.Context, req *pb.TeamConfigListReq) (*pb.TeamConfigListResp, error) {
	resp, err := dto.NewTeamConfigRepo().TeamConfigList(ctx, req)
	if err != nil {
		return nil, err
	}
	return resp, nil
}
```

- internal层
internal/persistence/team_config.go
```
func (dto *TeamConfig) TeamConfigList(ctx echo.Context, param *pb.TeamConfigListReq) (*pb.TeamConfigListResp, error) {
	// filter data
	dest := []*models.FpOpsTeamConfig{}
	query := dto.db.WithContext(ctx.Request().Context()).Table(models.GetFpOpsTeamConfigTableName()).Select("*")
	result := &pb.TeamConfigListResp{Data: []*pb.TeamConfigListResp_Detail{}}
	result.CurrentPage = param.Page
	result.PerPage = param.PageSize
	result.Total = uint32(total)
	for _, cfg := range dest {
		rowDetail := &pb.TeamConfigListResp_Detail{
			TeamId:     cfg.ID,
			TeamName:   cfg.TeamName,
		}
		result.Data = append(result.Data, rowDetail)
	}
	return result, nil
}
```

- model模型
models/fp_ops_team_config.go