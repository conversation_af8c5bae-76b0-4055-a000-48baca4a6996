local env = os.getenv("POD_RUNMODE")
print(env)
local dataFrom = "GLOBAL"
local nowTime = utils.strtotime("now")
local diffStartDate = params.diff_start_date or utils.date("Y-m-d", utils.strtotime("-2 week"))
local diffEndDate = params.diff_end_date or utils.date("Y-m-d")
-- local diffEndDate = params.diff_end_date or utils.date("Y-m-d",utils.strtotime("+1 day"))
local testFeishuRobit = "https://open.feishu.cn/open-apis/bot/v2/hook/c260d7d7-47ac-4c0e-8aab-7942e008c4a7"
--local prodFeishuRobit = "https://open.feishu.cn/open-apis/bot/v2/hook/5b61253d-7c72-4beb-812f-ee163e3215a0"
--local test_lark_url = "https://open.feishu.cn/open-apis/bot/v2/hook/fec862a4-8639-4ba0-b694-b84133f1c2c7"

local sdb_conn = brdb.new("dose5571b73594892f37ef64fb4be4d19d69", "1ed125db-f427-4c16-b804-ef75c35b07ca","funplus-other.cloud.databricks.com:443/sql/1.0/warehouses/3a0c7c5edc7c3a77")
-- local sdb_conn = sdb.new("<EMAIL>", "Xa0GwVbAQgl2xczIjn5E", "funplus_data", "FUNPLUS_DW", "STAGE", "RD_PLATFORM", "RD_PLATFORM")
--local filterGmList = utils.join(',', { "'ss_global'", "'koa_global'", "'gog_global'", "'mc_global'", "'mce_global'", "'mo_global'", "'dc.global.prod'","'entropy_global'"})
local filterGmList = utils.join(',', { "'ss_global'", "'koa_global'", "'mo_global'", "'dc.global.prod'"})
print(diffStartDate, diffEndDate)

local error = ""
-- 捕捉异常 告警
local function errorHandler(err)
    str = utils.sprintf("[%v][%v]\n\n 捕捉异常 line: %v", env, game, err)
    print(str)
    if string.len(err) > 100 then
        err = string.sub(err, -100)
    end
    str = utils.sprintf("[%v][%v]\n\n 捕捉异常 line: %v", env, game, err)
    error = str
    -- utils.alarm("fa124bc4-44d8-40c9-a253-89aa8ddc5c1a",10001,str)
end

-- 初始函数
function main()
    local success, result = xpcall(handle, errorHandler)
    if success then
        return result
    else
        return { status = 100005, msg = env == "PROD" and "sytem error！" or error }
    end
end

local xdate = {}
function handle()
    toXdate()
    print("xdate!!!!!!!---", diffStartDate, xdate)
    local text = ""
    --pv  + uv
    --local yAxisPv, pvSeries,yAxisUv, uvSeries, err = elfinSDKSuccPv()
    --print(yAxisPv, pvSeries,yAxisUv, uvSeries, err)
    --local code, image = ExchangeData(utils.sprintf("最近14天-%v-AI精灵-精灵PV", dataFrom), xdate, yAxisPv, pvSeries, true)
    --local image_key = utils.upload_remoteurl_feishu(image)
    --text = text .. "![AI精灵-SDK加载成功-PV](" .. image_key .. ")\n --------------\n"
    --local code, image = ExchangeData(utils.sprintf("最近14天-%v-AI精灵-精灵UV", dataFrom), xdate, yAxisUv, uvSeries, true)
    --local image_key = utils.upload_remoteurl_feishu(image)
    --text = text .. "![AI精灵-SDK加载成功-UV](" .. image_key .. ")\n --------------\n"

    -- 提问量、过滤后提问量、精确匹配量、精确匹配率
    local yAxisV, vSeries, yAxisR, rSeries, err = elfinAskOld()
    --print(yAxisV, vSeries, yAxisR, rSeries, err)
    --local code, image = ExchangeData(utils.sprintf("最近14天-%v-AI精灵-提问量", dataFrom), xdate, yAxisV, vSeries, true)
    --print("chart_url:::",code, image)
    --local image_key = utils.upload_remoteurl_feishu(image)
    --text = text .. "![AI精灵-提问量](" .. image_key .. ")\n --------------\n"

    local code, image = ExchangeData(utils.sprintf("最近14天-%v-AI精灵-精确匹配率-旧版", dataFrom), xdate, yAxisR, rSeries, true)
    local image_key = utils.upload_remoteurl_feishu(image)
    text = text .. "![AI精灵-精确匹配率](" .. image_key .. ")\n --------------\n"


    local RyAxisV, RvSeries, RyAxisR, RrSeries, err = elfinAskB()
    --print(RyAxisV, RvSeries, yAxisR, rSeries, err)

    local code, image = ExchangeData(utils.sprintf("最近14天-%v-AI精灵-精确匹配率-非润色", dataFrom), xdate, RyAxisR, RrSeries, true)
    local image_key = utils.upload_remoteurl_feishu(image)
    text = text .. "![AI精灵-精确匹配率](" .. image_key .. ")\n --------------\n"


    local ByAxisV, BvSeries, ByAxisR, BrSeries, err = elfinAskR()
    --print(RyAxisV, RvSeries, yAxisR, rSeries, err)

    local code, image = ExchangeData(utils.sprintf("最近14天-%v-AI精灵-精确匹配率-润色", dataFrom), xdate, ByAxisR, BrSeries, true)
    local image_key = utils.upload_remoteurl_feishu(image)
    text = text .. "![AI精灵-精确匹配率](" .. image_key .. ")\n --------------\n"


    utils.send_mark_msg(testFeishuRobit, text, "比对日报", "feishu")

end

function toXdate()
    print(string.sub(diffStartDate, 1, 4), string.sub(diffStartDate, 6, 7), string.sub(diffStartDate, 9, 10))
    local currentDate = os.date("*t", os.time({ year = string.sub(diffStartDate, 1, 4), month = string.sub(diffStartDate, 6, 7), day = string.sub(diffStartDate, 9, 10) }))  -- 获取当前日期和时间的表

    for i = 1, 14 do
        local startDate = os.date("%Y-%m-%d", os.time(currentDate))
        table.insert(xdate, startDate)
        currentDate.day = currentDate.day + 1  -- 递增当前日期
    end
end



-- 润色
function elfinAskR ()
    local sData, err = sdb_conn:query([[SELECT
                LEFT(ts, 10) AS DAY,
    msg_body:g_project AS g_project,
    COUNT(1) AS send_count,
    NVL(SUM(
        CASE
            WHEN (msg_body:detail:if_game_question IN ('0', '1')
                  AND msg_body:detail:answer_type != 'vague_question'
                  AND msg_body:detail:answer_type != 'unk')
            THEN 1 ELSE 0 END
    ), 0) AS match_count,
    NVL(SUM(
        CASE
            WHEN (msg_body:detail:if_game_question IN ('0', '1')
                  AND msg_body:detail:answer_type != 'vague_question')
            THEN 1 ELSE 0 END
    ), 0) AS filter_question_count
FROM FPDW.OPSTOOLS_SHARE.ALLGAME_CMS_NEW_PIPE
           WHERE
               msg_body:event = 'current_faq'
               AND msg_body:detail:event = 'elfin_backend_answer_log_v2'
               AND msg_body:detail:message_id <> ''
               AND ts >= '%v 00:00:00'
               AND ts < '%v 23:59:59'
               AND msg_body:g_project in (%v)
               GROUP BY g_project,DAY
               ORDER BY DAY,g_project]], diffStartDate, diffEndDate, filterGmList)
    sData = sData == nil and {} or sData

    local vSeries, rSeries = {}, {}
    local yAxisV = {
        { title = { text = "精灵-问题量统计" } },
    }
    local yAxisR = {
        { title = { text = "精灵-精确匹配率统计 [精确匹配率:精确匹配量/过滤后提问量]" } },
    }

    local num = 1
    local gmList = {}
    local gmDayList = {}
    for i, v in pairs(sData) do
        -- print(i,v)
        local gm = v.g_project
        local day = string.sub(v.DAY, 1, 10)
        local send_count, match_count, filter_question_count = v.send_count, v.match_count, v.filter_question_count
        print("--||----", gm, day, send_count, match_count, filter_question_count)
        if utils.in_array(gm, gmList) == false then
            table.insert(gmList, gm)
        end

        if gmDayList[gm] == nil then
            gmDayList[gm] = {}
        end
        gmDayList[gm][day] = { sendNum = send_count, matchCountNum = match_count, filterQuestionCountNum = filter_question_count }
    end

    for i, gm in pairs(gmList) do
        local send, match, filter = {}, {}, {}
        local matchR = {} -- 匹配率:匹配量/过滤后的提问量
        for j, day in pairs(xdate) do
            if gmDayList[gm][day] == nil then
                --                 table.insert(send, 0)
                table.insert(match, 0)
                table.insert(filter, 0)
                table.insert(matchR, 100)
            else
                --                 table.insert(send, tonumber(gmDayList[gm][day].sendNum))
                table.insert(match, tonumber(gmDayList[gm][day].matchCountNum))
                table.insert(filter, tonumber(gmDayList[gm][day].filterQuestionCountNum))

                local matchNum = gmDayList[gm][day].matchCountNum ~= nil and tonumber(gmDayList[gm][day].matchCountNum) or 0
                local fqcNum = gmDayList[gm][day].filterQuestionCountNum ~= nil and tonumber(gmDayList[gm][day].filterQuestionCountNum) or 0
                table.insert(matchR, toRate(matchNum, fqcNum))
            end
        end
        --         table.insert(vSeries, { name = gm .. "-提问量", type = "line", data = send })
        table.insert(vSeries, { name = gm .. "-过滤后提问量", type = "line", data = filter })
        --         table.insert(vSeries, { name = gm .. "-匹配量", type = "line", data = match })

        table.insert(rSeries, { name = gm .. "-精确匹配率", type = "line", data = matchR })
    end
    return yAxisV, vSeries, yAxisR, rSeries, err
end


-- 非润色
function elfinAskB ()
    local sData, err = sdb_conn:query([[SELECT
                LEFT(ts, 10) AS DAY,
    msg_body:g_project AS g_project,
    COUNT(1) AS send_count,
    NVL(SUM(
        CASE
            WHEN (msg_body:detail:if_game_question IN ('0', '1')
                  AND msg_body:detail:answer_type != 'vague_question'
                  AND msg_body:detail:answer_type != 'unk')
            THEN 1 ELSE 0 END
    ) - SUM(
        CASE
            WHEN (msg_body:detail:if_game_question IN ('0', '1')
                  AND msg_body:detail:answer_type != 'vague_question'
                  AND msg_body:detail:answer_type != 'unk'
                  AND msg_body:detail:gpt_unknown = true)
            THEN 1 ELSE 0 END
    ), 0) AS match_count,
    NVL(SUM(
        CASE
            WHEN (msg_body:detail:if_game_question IN ('0', '1')
                  AND msg_body:detail:answer_type != 'vague_question')
            THEN 1 ELSE 0 END
    ), 0) AS filter_question_count
           FROM FPDW.OPSTOOLS_SHARE.ALLGAME_CMS_NEW_PIPE
           WHERE
               msg_body:event = 'current_faq'
               AND msg_body:detail:event = 'elfin_backend_answer_log_v2'
               AND msg_body:detail:message_id <> ''
               AND ts >= '%v 00:00:00'
               AND ts < '%v 23:59:59'
               AND msg_body:g_project in (%v)
               GROUP BY g_project,DAY
               ORDER BY DAY,g_project]], diffStartDate, diffEndDate, filterGmList)
    sData = sData == nil and {} or sData

    local vSeries, rSeries = {}, {}
    local yAxisV = {
        { title = { text = "精灵-问题量统计" } },
    }
    local yAxisR = {
        { title = { text = "精灵-精确匹配率统计 [精确匹配率:精确匹配量/过滤后提问量]" } },
    }

    local num = 1
    local gmList = {}
    local gmDayList = {}
    for i, v in pairs(sData) do
        -- print(i,v)
        local gm = v.g_project
        local day = string.sub(v.DAY, 1, 10)
        local send_count, match_count, filter_question_count = v.send_count, v.match_count, v.filter_question_count
        print("--||----", gm, day, send_count, match_count, filter_question_count)
        if utils.in_array(gm, gmList) == false then
            table.insert(gmList, gm)
        end

        if gmDayList[gm] == nil then
            gmDayList[gm] = {}
        end
        gmDayList[gm][day] = { sendNum = send_count, matchCountNum = match_count, filterQuestionCountNum = filter_question_count }
    end

    for i, gm in pairs(gmList) do
        local send, match, filter = {}, {}, {}
        local matchR = {} -- 匹配率:匹配量/过滤后的提问量
        for j, day in pairs(xdate) do
            if gmDayList[gm][day] == nil then
                --                 table.insert(send, 0)
                table.insert(match, 0)
                table.insert(filter, 0)
                table.insert(matchR, 100)
            else
                --                 table.insert(send, tonumber(gmDayList[gm][day].sendNum))
                table.insert(match, tonumber(gmDayList[gm][day].matchCountNum))
                table.insert(filter, tonumber(gmDayList[gm][day].filterQuestionCountNum))

                local matchNum = gmDayList[gm][day].matchCountNum ~= nil and tonumber(gmDayList[gm][day].matchCountNum) or 0
                local fqcNum = gmDayList[gm][day].filterQuestionCountNum ~= nil and tonumber(gmDayList[gm][day].filterQuestionCountNum) or 0
                table.insert(matchR, toRate(matchNum, fqcNum))
            end
        end
        --         table.insert(vSeries, { name = gm .. "-提问量", type = "line", data = send })
        table.insert(vSeries, { name = gm .. "-过滤后提问量", type = "line", data = filter })
        --         table.insert(vSeries, { name = gm .. "-匹配量", type = "line", data = match })

        table.insert(rSeries, { name = gm .. "-精确匹配率", type = "line", data = matchR })
    end
    return yAxisV, vSeries, yAxisR, rSeries, err
end


-- 旧版当天问题总量 、过滤后提问量、精确匹配量、精确匹配率
function elfinAskOld ()
    local sData, err = sdb_conn:query([[SELECT
               LEFT(ts,10) AS DAY,
               msg_body:g_project AS g_project,
               count(1) as send_count,
               nvl(sum(CASE WHEN (msg_body:detail:if_effective_query ='1' AND msg_body:detail:if_game_question IN ('0', '1') AND msg_body:detail:mark_scene IN ('1')) THEN 1 ELSE 0 END), 0) AS match_count,
               nvl(sum(CASE WHEN (msg_body:detail:if_effective_query ='1' AND msg_body:detail:if_game_question IN ('0', '1')) THEN 1 ELSE 0 END), 0) AS filter_question_count
           FROM FPDW.OPSTOOLS_SHARE.ALLGAME_CMS_NEW_PIPE
           WHERE
               msg_body:event = 'current_faq'
               AND msg_body:detail:event = 'elfin_backend_ack'
               AND msg_body:detail:message_id <> ''
               AND ts >= '%v 00:00:00'
               AND ts < '%v 23:59:59'
               AND msg_body:g_project in (%v)
               GROUP BY g_project,DAY
               ORDER BY DAY,g_project]], diffStartDate, diffEndDate, filterGmList)
    sData = sData == nil and {} or sData

    local vSeries, rSeries = {}, {}
    local yAxisV = {
        { title = { text = "精灵-问题量统计" } },
    }
    local yAxisR = {
        { title = { text = "精灵-精确匹配率统计 [精确匹配率:精确匹配量/过滤后提问量]" } },
    }

    local num = 1
    local gmList = {}
    local gmDayList = {}
    for i, v in pairs(sData) do
        -- print(i,v)
        local gm = v.g_project
        local day = string.sub(v.DAY, 1, 10)
        local send_count, match_count, filter_question_count = v.send_count, v.match_count, v.filter_question_count
        print("--||----", gm, day, send_count, match_count, filter_question_count)
        if utils.in_array(gm, gmList) == false then
            table.insert(gmList, gm)
        end

        if gmDayList[gm] == nil then
            gmDayList[gm] = {}
        end
        gmDayList[gm][day] = { sendNum = send_count, matchCountNum = match_count, filterQuestionCountNum = filter_question_count }
    end

    for i, gm in pairs(gmList) do
        local send, match, filter = {}, {}, {}
        local matchR = {} -- 匹配率:匹配量/过滤后的提问量
        for j, day in pairs(xdate) do
            if gmDayList[gm][day] == nil then
                --                 table.insert(send, 0)
                table.insert(match, 0)
                table.insert(filter, 0)
                table.insert(matchR, 100)
            else
                --                 table.insert(send, tonumber(gmDayList[gm][day].sendNum))
                table.insert(match, tonumber(gmDayList[gm][day].matchCountNum))
                table.insert(filter, tonumber(gmDayList[gm][day].filterQuestionCountNum))

                local matchNum = gmDayList[gm][day].matchCountNum ~= nil and tonumber(gmDayList[gm][day].matchCountNum) or 0
                local fqcNum = gmDayList[gm][day].filterQuestionCountNum ~= nil and tonumber(gmDayList[gm][day].filterQuestionCountNum) or 0
                table.insert(matchR, toRate(matchNum, fqcNum))
            end
        end
        --         table.insert(vSeries, { name = gm .. "-提问量", type = "line", data = send })
        table.insert(vSeries, { name = gm .. "-过滤后提问量", type = "line", data = filter })
        --         table.insert(vSeries, { name = gm .. "-匹配量", type = "line", data = match })

        table.insert(rSeries, { name = gm .. "-精确匹配率", type = "line", data = matchR })
    end
    return yAxisV, vSeries, yAxisR, rSeries, err
end

function ExchangeData(title, categories, yAxis, series, isPlot)
    local charts = {
        title = {
            text = title
        },
        xAxis = {
            categories = categories
        },
        yAxis = yAxis,
        series = series,
    }
    if isPlot then
        charts.plotOptions = {
            line = {
                dataLabels = {
                    enabled = true,
                    verticalAlign = "bottom",
                    allowOverlap = true
                },
            },
            column = {
                dataLabels = {
                    enabled = true,
                    verticalAlign = "top",
                    allowOverlap = true,
                },
            },
            bar = {
                dataLabels = {
                    enabled = true,
                    allowOverlap = true,
                },
            }
        }
    end
    print(charts)
    return GenerateChart(charts)
end

function GenerateChart(infile)
    -- local url = "http://charts-node-export-server-svc.general-cms.svc.cluster.local:81/"
    local url = "https://charts-node-export-server.funplus.com"
    local body = {
        async = true,
        asyncRendering = false,
        callback = "function(chart) {chart.renderer.label('This label is added in the callback', 1200, 1200).attr({fill : '#90ed7d',padding: 10, r: 10,zIndex: 10}).css({color: 'black',width: '1200px'}).add();}",
        constr = "Chart",
        scale = false,
        styledMode = false,
        infile = infile,
        type = "image/png",
        width = 1500
    }
    local conn = http.new("POST", url, body)
    print(utils.json_encode(body))
    conn:set_header("content-type", "application/json") -- POST请求设置请求头部
    local res, err = conn:request() -- 发起请求
    local header = res:header() -- 获取请求返回的header头信息
    local status = res:status() -- 获取HTTP返回码
    local data = res:data() -- 获取返回数据
    print(data)
    if status ~= 200 then
        return { status = -1, msg = utils.sprintf("https://charts-node-export-server.funplus.com/请求返回:%v", status) }
    end

    local img_conn = http.new("GET", utils.sprintf("https://acs-go-test.funplus.com/lua?p1=18&p2=111&path=%v", data))
    conn:set_header("content-type", "application/json") -- POST请求设置请求头部
    local res_img, err = img_conn:request() -- 发起请求
    local body_img = res_img:data() -- 获取返回数据
    body_data = utils.json_decode(body_img)

    print(body_data["data"])
    return 0, body_data["data"] == nil and "https://charts-node-export-server.funplus.com/" .. data or body_data["data"]
end

function toRate(a, b)
    if b == 0 then
        return 100
    end
    local rate = a * 100 / b
    local formattedRate = string.format("%.2f", rate)
    return tonumber(formattedRate)
end

main()