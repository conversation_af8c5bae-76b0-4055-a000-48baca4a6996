package configure

import (
	"bytes"
	"encoding/json"
	"errors"
	"github.com/labstack/echo/v4"
	"github.com/spf13/cast"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"ops-ticket-api/internal/code"
	"ops-ticket-api/internal/cst"
	"ops-ticket-api/internal/framework/lang"
	"ops-ticket-api/internal/framework/xerrors"
	p "ops-ticket-api/internal/persistence"
	"ops-ticket-api/models"
	"ops-ticket-api/proto/pb"
	"ops-ticket-api/utils"
)

// OvertimeTplList 模板列表
func OvertimeTplList(ctx echo.Context, req *pb.OverTimeTplListReq) (*pb.OverTimeTplListResp, error) {
	list, total, err := p.NewOvertimeTpl().OvertimeTplList(ctx, req)
	if err != nil {
		return nil, err
	}
	return &pb.OverTimeTplListResp{
		CurrentPage: req.Page,
		PerPage:     req.GetPageSize(),
		Total:       total,
		Data:        list,
	}, nil
}

// OvertimeTplAdd 添加模版接口
func OvertimeTplAdd(ctx echo.Context, req *pb.OverTimeTplAddReq) error {
	// 校验有无相同模版名称
	if err := p.NewOvertimeTpl().OvertimeTplCheck(ctx, req.TplName, 0); err != nil {
		return err
	}

	current := utils.NowTimestamp()
	//contentStr, err := jsoniter.ConfigCompatibleWithStandardLibrary.MarshalToString(req.Content)
	var buf bytes.Buffer
	encoder := json.NewEncoder(&buf)
	encoder.SetEscapeHTML(false) // 禁用 HTML 转义
	if err := encoder.Encode(req.Content); err != nil {
		logger.Errorf(ctx.Request().Context(), "overtime tpl encoder.Encode error: %v", err)
		return err
	}
	contentStr := buf.String()
	tplInfo := &models.FpOpsOvertimeTpl{
		TplName:     req.TplName,
		Content:     contentStr,
		Operator:    cast.ToString(ctx.Get(cst.AccountInfoCtx)),
		Overtime:    uint64(req.RemindTime),
		Enable:      code.StatusFalse,
		CreateTime:  current,
		UpdatedTime: current,
	}
	for _, gameProject := range req.GameProject {
		tplGame := &models.FpOpsOvertimeTplGame{
			TplID:       tplInfo.ID,
			GameProject: gameProject,
			Enable:      tplInfo.Enable,
			CreateTime:  current,
			UpdatedTime: current,
		}
		tplInfo.GameProject = append(tplInfo.GameProject, tplGame)
	}

	if err := p.NewOvertimeTpl().OvertimeTplAdd(ctx, tplInfo); err != nil {
		return err
	}
	return nil
}

func OvertimeTplEdit(ctx echo.Context, req *pb.OverTimeTplEditReq) error {
	// 检查模版是否存在
	tplInfo, err := p.NewOvertimeTpl().GetOvertimeInfoIfExist(req.TplId)
	if err != nil {
		return err
	}
	// 检查模版名称
	if err := p.NewOvertimeTpl().OvertimeTplCheck(ctx, req.TplName, req.TplId); err != nil {
		return err
	}
	current := utils.NowTimestamp()
	var buf bytes.Buffer
	encoder := json.NewEncoder(&buf)
	encoder.SetEscapeHTML(false) // 禁用 HTML 转义
	if err := encoder.Encode(req.Content); err != nil {
		logger.Errorf(ctx.Request().Context(), "overtime tpl encoder.Encode error: %v", err)
		return err
	}
	contentStr := buf.String()

	tplInfo.TplName = req.TplName
	tplInfo.Content = contentStr
	tplInfo.Overtime = uint64(req.RemindTime)
	tplInfo.Operator = ctx.Get(cst.AccountInfoCtx).(string)
	tplInfo.UpdatedTime = current

	for _, gameProject := range req.GameProject {
		tplGame := &models.FpOpsOvertimeTplGame{
			TplID:       tplInfo.ID,
			GameProject: gameProject,
			Enable:      tplInfo.Enable,
			CreateTime:  current,
			UpdatedTime: current,
		}
		tplInfo.GameProject = append(tplInfo.GameProject, tplGame)
	}
	if err := p.NewOvertimeTpl().OvertimeTplEdit(ctx.Request().Context(), req.TplId, tplInfo); err != nil {
		xErr, ok := err.(xerrors.Error)
		if ok {
			if xErr.Code() == code.MissingParams {
				return xerrors.New(lang.FormatText(ctx, xErr.Error()), xErr.Code())
			}
		}
		return err
	}
	return nil
}

func OvertimeTplDel(ctx echo.Context, req *pb.OverTimeTplDelReq) error {
	query := map[string]interface{}{
		"overtime_reply_id": req.Id,
	}
	exist, err := p.NewCat().GetCatExistByTplId(ctx, query)
	if err != nil {
		return err
	}
	if exist {
		return errors.New("该模板正在使用，无法删除")
	}
	return p.NewOvertimeTpl().OvertimeTplDel(ctx, req)
}

func OvertimeTplEnabel(ctx echo.Context, req *pb.OverTimeTplEnableReq) error {
	return p.NewOvertimeTpl().OvertimeTplEnable(ctx, req)
}

// OvertimeTplOpts 模版筛选列表接口
func OvertimeTplOpts(ctx echo.Context, param *pb.OverTimeTplOptsReq) (*pb.OverTimeTplOptsRes, error) {
	res, err := p.NewOvertimeTpl().OvertimeTplOpts(ctx.Request().Context(), param.GameProject)
	if err != nil {
		return nil, err
	}
	return res, nil
}
