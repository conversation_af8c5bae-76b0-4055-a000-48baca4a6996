// Copyright 2021 funplus Authors. All Rights Reserved.
// @Description:
// @Author: Darcy
// @Date: 2022/11/21 11:35

package configure

import (
	"errors"
	"time"

	"ops-ticket-api/internal/code"
	"ops-ticket-api/internal/cst"
	"ops-ticket-api/internal/framework/lang"
	"ops-ticket-api/internal/framework/xerrors"
	dto "ops-ticket-api/internal/persistence"
	"ops-ticket-api/models"
	"ops-ticket-api/proto/pb"

	"github.com/go-sql-driver/mysql"
	"github.com/labstack/echo/v4"
	"github.com/spf13/cast"
)

type dictSvc struct {
	repo *dto.DictRepo
}

var DefaultDictSvc = newDictSvc()

func newDictSvc() *dictSvc {
	return &dictSvc{
		repo: dto.NewDictRepo(),
	}
}

func (svc *dictSvc) DictOpts(ctx echo.Context) ([]*pb.DictOptsResp_Opts, error) {
	return svc.repo.GetDictOps(ctx.Request().Context())
}

func (svc *dictSvc) DictList(ctx echo.Context) ([]*pb.DictListResp_Dict, error) {
	return svc.repo.DictList(ctx.Request().Context())
}

func (svc *dictSvc) DictInfo(ctx echo.Context, dictId uint32) (*pb.DictInfoResp, error) {
	return svc.repo.DictInfo(ctx.Request().Context(), dictId)
}

func (svc *dictSvc) DictAdd(ctx echo.Context, req *pb.DictAddReq) error {
	dict := &models.FpOpsDict{
		DictName:  req.DictName,
		DictKey:   req.DictKey,
		Operator:  cast.ToString(ctx.Get(cst.AccountInfoCtx)),
		CreatedAt: uint64(time.Now().Unix()),
		UpdatedAt: uint64(time.Now().Unix()),
	}

	if err := svc.repo.DictAdd(ctx.Request().Context(), dict); err != nil {
		var dbErr *mysql.MySQLError
		if errors.As(err, &dbErr) && dbErr.Number == 1062 {
			return xerrors.New(lang.FormatText(ctx, "RepeatDataTip"), code.IdempotenceErr)
		}
		return xerrors.New(err.Error(), code.DbError)
	}
	return nil
}

func (svc *dictSvc) DictSave(ctx echo.Context, req *pb.DictInfoResp) error {
	if err := svc.repo.DictSave(ctx.Request().Context(), req.DictId, map[string]interface{}{
		"dict_name":  req.DictName,
		"dict_key":   req.DictKey,
		"operator":   cast.ToString(ctx.Get(cst.AccountInfoCtx)),
		"updated_at": time.Now().Unix(),
	}); err != nil {
		var dbErr *mysql.MySQLError
		if errors.As(err, &dbErr) && dbErr.Number == 1062 {
			return xerrors.New(lang.FormatText(ctx, "RepeatDataTip"), code.IdempotenceErr)
		}
		return xerrors.New(err.Error(), code.DbError)
	}
	return nil
}

func (svc *dictSvc) DictEnable(ctx echo.Context, req *pb.EnableReq) error {
	return svc.repo.DictSave(ctx.Request().Context(), req.ObjectId, map[string]interface{}{
		"enable":     req.Enable,
		"operator":   cast.ToString(ctx.Get(cst.AccountInfoCtx)),
		"updated_at": time.Now().Unix(),
	})
}
