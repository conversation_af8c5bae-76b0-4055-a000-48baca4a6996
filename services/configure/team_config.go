package configure

import (
	"github.com/labstack/echo/v4"
	dto "ops-ticket-api/internal/persistence"
	"ops-ticket-api/proto/pb"
)

// TeamConfigList 团队配置列表
func TeamConfigList(ctx echo.Context, req *pb.TeamConfigListReq) (*pb.TeamConfigListResp, error) {
	resp, err := dto.NewTeamConfigRepo().TeamConfigList(ctx, req)
	if err != nil {
		return nil, err
	}
	return resp, nil
}

// AddTeamConfig 新增团队配置
func AddTeamConfig(ctx echo.Context, req *pb.TeamConfigAddReq) error {
	return dto.NewTeamConfigRepo().AddTeamConfig(ctx, req)
}

// EditTeamConfig 修改团队配置
func EditTeamConfig(ctx echo.Context, req *pb.TeamConfigEditReq) error {
	return dto.NewTeamConfigRepo().EditTeamConfig(ctx, req)
}

// DelTeamConfig 删除团队配置
func DelTeamConfig(ctx echo.Context, req *pb.TeamConfigDelReq) error {
	return dto.NewTeamConfigRepo().DeleteTeamConfig(ctx, req)
}
