package configure

import (
	"github.com/labstack/echo/v4"
	p "ops-ticket-api/internal/persistence"
	"ops-ticket-api/proto/pb"
)

// ModuleList 模板列表
func ModuleList(ctx echo.Context, req *pb.ModuleListReq) (*pb.ModuleListResp, error) {
	list, total, err := p.NewModule().ModuleList(ctx, req)
	if err != nil {
		return nil, err
	}
	return &pb.ModuleListResp{
		CurrentPage: req.Page,
		PerPage:     req.GetPageSize(),
		Total:       total,
		Data:        list,
	}, nil
}

func ModuleSave(ctx echo.Context, req *pb.ModuleSaveReq) error {
	return p.NewModule().ModuleSave(ctx, req)
}

func ModuleEdit(ctx echo.Context, req *pb.ModuleEditReq) error {
	return p.NewModule().ModuleEdit(ctx, req)
}

func ModuleDel(ctx echo.Context, req *pb.ModuleDelReq) error {
	return p.NewModule().ModuleDel(ctx, req)
}

func ModuleOpts(ctx echo.Context, req *pb.ModuleOptsReq) ([]*pb.ModuleOptsResp, error) {
	return p.NewModule().ModuleOpts(ctx, req)
}
