// Copyright 2021 funplus Authors. All Rights Reserved.
// @Description: 标签组
// @Author: Darcy
// @Date: 2021/10/26 3:46 PM

package configure

import (
	"context"
	"fmt"
	"github.com/labstack/echo/v4"
	"github.com/spf13/cast"
	"github.com/xuri/excelize/v2"
	"golang.org/x/sync/errgroup"
	"gorm.io/gorm"
	"io"
	"math/rand"
	"net/http"
	"ops-ticket-api/internal/code"
	"ops-ticket-api/utils"
	"strings"

	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"ops-ticket-api/internal/cst"
	"ops-ticket-api/internal/framework/lang"
	"ops-ticket-api/internal/framework/xerrors"
	dto "ops-ticket-api/internal/persistence"
	"ops-ticket-api/models"
	"ops-ticket-api/proto/pb"
	"os"
	"sort"
	"time"
)

// AllTagLibSaveNew 保存标签列表
func AllTagLibSaveNew(ctx echo.Context, req *pb.AllTagLibSaveReq) (uint32, error) {
	srv := newAllTagLibSrv(ctx)
	return srv.LibSave(ctx, req)
}

func newAllTagLibSrv(ctx echo.Context) *alltagLibSaveSrv {
	return &alltagLibSaveSrv{
		ctx: ctx,
	}
}

type alltagLibSaveSrv struct {
	FileNoModified bool
	LibDetail      *models.FpOpsTagsLib
	ctx            echo.Context
}

func (srv *alltagLibSaveSrv) LibSave(ctx echo.Context, req *pb.AllTagLibSaveReq) (uint32, error) {
	var (
		fileNoModified                      bool
		err                                 error
		fun                                 = "tagLibSaveSrv.LibSave -->"
		newTagTree, oldTagTree, saveTagTree []*TagNode
		addTags, delTags                    []*models.FpOpsTags
	)
	// 1. check update
	req.LibName = strings.TrimSpace(req.LibName)
	has, err := dto.NewAllTagLib().AllCheckTagLibNameHasExist(ctx.Request().Context(), req.LibId, req.LibType, req.LibName)
	if err != nil {
		return 0, err
	}
	if has {
		return 0, xerrors.New(lang.FormatText(ctx, "RepeatDataTip"), code.IdempotenceErr)
	}

	if req.LibId > 0 {
		tagLib, err := dto.NewAllTagLib().AllGetTagLibInfo(ctx.Request().Context(), req.LibId)
		if err != nil {
			return 0, err
		}
		srv.LibDetail = tagLib
		if tagLib.TagUploadFile == req.LibFileUrl { // 标签文件未变动
			fileNoModified = true
		}
	}
	// 2. get upload file data
	if fileNoModified == false {
		newTagTree, err = srv.allGetUploadFileTree(ctx, req.LibFileUrl)
		if err != nil {
			logger.Errorf(ctx.Request().Context(), "%s getUploadFileTree err. file:%s. err:%v", fun, req.LibFileUrl, err)
			return 0, err
		}
	}
	// 获取当前数据的 node
	if req.LibId > 0 {
		oldTagTree, err = srv.allGetLibTagsTree(ctx, req.LibId)
		if err != nil {
			logger.Errorf(ctx.Request().Context(), "%s getLibTagsTree err. libId:%d. err:%v", fun, req.LibId, err)
			return 0, xerrors.New(err, code.IdempotenceErr)
		}
	}
	// 3. compare: mark tags to delete and add
	if len(newTagTree) > 0 { // 有旧数据和新数据
		saveTagTree, addTags, delTags = srv.allCompareTagsTree(ctx, newTagTree, oldTagTree)
		//} else if len(newTagTree) > 0 { // 无旧数据 - 新数据，全量新增
		//	saveTagTree = newTagTree
	}
	_ = addTags

	// 4. check tags can delete
	if req.LibType == uint32(pb.TagConfigType_TicketConfigType) && len(delTags) > 0 {
		var delIds = make([]uint32, 0, len(delTags))
		for _, tg := range delTags {
			delIds = append(delIds, tg.TagID)
		}
		if has, err := dto.NewTicket().CheckTagIdBind(ctx.Request().Context(), delIds); err != nil {
			return 0, err
		} else if has {
			logger.Errorf(ctx.Request().Context(), "%s CheckTagIdBind return true. tagIds:%+v", fun, delIds)
			return 0, fmt.Errorf("部分被删除标签被使用，不允许删除")
		}
	}
	// 5. save tags -- transaction
	lid, err := srv.libTagSave(ctx, req, saveTagTree, delTags)
	if err != nil {
		logger.Errorf(ctx.Request().Context(), "%s libTagSave return err. req:%+v. err:%v", fun, req, err)
	}

	return lid, err
}

// 保存标签数据
func (srv *alltagLibSaveSrv) libTagSave(ctx echo.Context, req *pb.AllTagLibSaveReq, tagFinalTree []*TagNode, delTags []*models.FpOpsTags) (libId uint32, err error) {

	var (
		fun      = "tagLibSaveSrv.libTagSave"
		now      = utils.NowTimestamp()
		enable   = code.StatusFalse
		operator = cast.ToString(ctx.Get(cst.AccountInfoCtx))
		tx       = dto.NewAllTagLib().NewGetDb(ctx.Request().Context()).Begin()
	)

	defer func() {
		if err != nil {
			if _rErr := tx.Rollback(); _rErr != nil {
				logger.Errorf(ctx.Request().Context(), "%s rollback return err. req:%+v. err:%v. _rErr:%v", fun, req, err, _rErr)
			}
		}
	}()

	// 1. save tag_lib
	if req.LibId > 0 { // update lib & gm
		libId = req.LibId
		var libD = &models.FpOpsTagsLib{}
		if err = tx.Model(libD).Where("lib_id = ?", libId).First(libD).Error; err != nil {
			return
		}
		enable = int(libD.Enable)
		if err = tx.Model(&models.FpOpsTagsLib{}).Where("lib_id = ?", libId).Updates(map[string]interface{}{
			"lib_name":        req.LibName,
			"tag_upload_file": req.LibFileUrl,
			"enable":          enable,
			"operator":        operator,
			"updated_at":      now,
		}).Error; err != nil {
			return
		}
		if err = tx.Model(&models.FpOpsTagsLibGm{}).Where("lib_id = ?", libId).Delete(&models.FpOpsTagsLibGm{}).Error; err != nil {
			return
		}
	} else { // insert lib & gm
		var libDetail = &models.FpOpsTagsLib{
			LibName:       req.LibName,
			TagUploadFile: req.LibFileUrl,
			Operator:      operator,
			Enable:        uint32(enable),
			CreatedAt:     now,
			UpdatedAt:     now,
			LibType:       uint16(req.LibType),
		}
		if err = tx.Model(libDetail).Create(libDetail).Error; err != nil {
			return
		}
		if libDetail.LibID == 0 {
			err = fmt.Errorf("create lib return id eq0. break")
			return
		}
		libId = libDetail.LibID
	}

	// 2. save tag_lib_gm
	for _, gm := range req.Projects {
		libGm := &models.FpOpsTagsLibGm{
			Project:   gm,
			LibID:     libId,
			Enable:    uint32(enable),
			CreatedAt: now,
			UpdatedAt: now,
		}
		if err = tx.Model(libGm).Create(libGm).Error; err != nil {
			return
		}
	}
	// 3. save tags
	if len(delTags) > 0 { // do del
		var tagIds = make([]uint32, 0, len(delTags))
		for _, tg := range delTags {
			tagIds = append(tagIds, tg.TagID)
		}
		if err = tx.Model(&models.FpOpsTags{}).Where("tag_id in (?)", tagIds).
			Delete(&models.FpOpsTags{}).Error; err != nil {
			return
		}
	}

	var (
		addTagF = func(tagNode *TagNode, toEnable int, txDb *gorm.DB) error {
			if tagNode.State == TagNodeStateUpdate {
				if _err := txDb.Model(&models.FpOpsTags{}).Where("tag_id = ?", tagNode.TagId).
					Updates(map[string]interface{}{
						"tag_name":   tagNode.Name,
						"parent_id":  tagNode.ParentId,
						"enable":     toEnable,
						"lft":        0,
						"rgt":        0,
						"operator":   operator,
						"updated_at": now,
					}).Error; _err != nil {
					return _err
				}
			} else if tagNode.State == TagNodeStateAdd {
				// 查询该标签是否已经存在，如果已存在就不添加
				var tag = &models.FpOpsTags{
					LibID:     libId,
					TagName:   tagNode.Name,
					ParentID:  tagNode.ParentId,
					Lft:       0,
					Rgt:       0,
					Level:     tagNode.Level,
					Enable:    uint32(toEnable),
					Operator:  operator,
					CreatedAt: now,
					UpdatedAt: now,
				}
				if _err := txDb.Model(tag).Create(tag).Error; _err != nil {
					return _err
				}
				// 回填信息
				tagNode.TagId = tag.TagID
			}
			return nil
		}

		setParentId = func(nodes *[]*TagNode, parentId uint32) {
			if nodes == nil {
				return
			}
			for _, n := range *nodes {
				n.ParentId = parentId
			}
		}
	)
	for _, oneTag := range tagFinalTree {
		// 第一层
		if err = addTagF(oneTag, enable, tx); err != nil {
			return
		}
		setParentId(&oneTag.Children, oneTag.TagId)

		for _, twoTag := range oneTag.Children {
			// 第二层
			if err = addTagF(twoTag, enable, tx); err != nil {
				return
			}
			setParentId(&twoTag.Children, twoTag.TagId)

			for _, threeTag := range twoTag.Children {
				// 第三层
				if err = addTagF(threeTag, enable, tx); err != nil {
					return
				}
				setParentId(&threeTag.Children, threeTag.TagId)
			}
		}
	}
	// 6. commit
	err = tx.Commit().Error
	return
}

func (srv *alltagLibSaveSrv) allCompareTagsTree(ctx echo.Context, newTagsTree, oldTagsTree []*TagNode) ([]*TagNode, []*models.FpOpsTags, []*models.FpOpsTags) {

	var (
		toDelete      = make([]*models.FpOpsTags, 0)
		toAdd         = make([]*models.FpOpsTags, 0)
		finalTagsTree = make([]*TagNode, 0)
	)

	// 递归比较树结构
	allCompareGenFinalTree(&oldTagsTree, &newTagsTree, &finalTagsTree, &toDelete, &toAdd)
	return finalTagsTree, toAdd, toDelete
}

func (srv *alltagLibSaveSrv) allGetLibTagsTree(ctx echo.Context, libId uint32) ([]*TagNode, error) {
	tags, err := dto.NewTags().GetTagsByLibID(ctx.Request().Context(), libId)
	if err != nil {
		return nil, err
	}
	root := BuildTagTreeList(tags)
	return root.Children, nil
}
func (srv *alltagLibSaveSrv) allGetUploadFileTree(ctx echo.Context, fileName string) ([]*TagNode, error) {
	fun := "tagLibSaveSrv.getUploadFileTree -->"
	libData, err := AllTagLibUpload(ctx.Request().Context(), fileName)
	if err != nil {
		logger.Errorf(ctx.Request().Context(), "%s getUploadFileTree err. file:%s. err:%v", fun, fileName, err)
		return nil, xerrors.New("upload file read return fail"+err.Error(), code.IdempotenceErr)
	}
	libData, err = checkFileLibFormat(ctx.Request().Context(), libData)
	if err != nil {
		logger.Errorf(ctx.Request().Context(), "%s checkFileLibFormat err. file:%s. err:%v", fun, fileName, err)
		return nil, err
	}
	// 数据修剪成 树结构；
	NewTagTree := AllBuildTagTree(libData)
	AllPrintTagTree(NewTagTree, 0)
	return NewTagTree.Children, nil
}

func allCheckFileLibFormat(ctx context.Context, data []*pb.TagGroup) ([]*pb.TagGroup, error) {
	var newData = make([]*pb.TagGroup, 0)
	var tgDesc = make(map[string]struct{}, 0)
	for _, row := range data {
		if row.LevelOne == "" && row.LevelTwo == "" && row.LevelThree == "" {
			continue
		}
		if row.LevelOne == "" {
			return newData, fmt.Errorf("请检测数据：一级标签不能为空. one:%s. two:%s. three:%s", row.LevelOne, row.LevelTwo, row.LevelThree)
		}
		if row.LevelTwo == "" && row.LevelThree != "" {
			return newData, fmt.Errorf("请检测数据：二级标签不能为空. one:%s. two:%s. three:%s", row.LevelOne, row.LevelTwo, row.LevelThree)
		}
		newData = append(newData, row)

		// check 标签重名：- 去重校验：仅有一级的情况下，不允许一级名称相同；有两级的情况下，不允许一级和二级名称完全相同；有三级的情况下，不允许一级、二级和三级的名称完全相同
		if row.LevelTwo == "" {
			if _, ok := tgDesc[row.LevelOne]; ok {
				return newData, fmt.Errorf("请检测数据：名字存在重复. %s", row.LevelOne)
			}
			tgDesc[row.LevelOne] = struct{}{}
		} else if row.LevelThree == "" {
			if _, ok := tgDesc[row.LevelOne+"||"+row.LevelTwo]; ok {
				return newData, fmt.Errorf("请检测数据：名字存在重复. %s-%s", row.LevelOne, row.LevelTwo)
			}
			if row.LevelOne == row.LevelTwo {
				return newData, fmt.Errorf("请检测数据：一二级名字存在重复. %s-%s", row.LevelOne, row.LevelTwo)
			}
			tgDesc[row.LevelOne] = struct{}{}
			tgDesc[row.LevelOne+"||"+row.LevelTwo] = struct{}{}
		} else {
			if _, ok := tgDesc[row.LevelOne+"||"+row.LevelTwo+"||"+row.LevelThree]; ok {
				return newData, fmt.Errorf("请检测数据：名字存在重复. %s-%s-%s", row.LevelOne, row.LevelTwo, row.LevelThree)
			}
			if row.LevelOne == row.LevelTwo && row.LevelTwo == row.LevelThree {
				return newData, fmt.Errorf("请检测数据：一二三级名字存在重复. %s-%s-%s", row.LevelOne, row.LevelTwo, row.LevelThree)
			}
			tgDesc[row.LevelOne] = struct{}{}
			tgDesc[row.LevelOne+"||"+row.LevelTwo] = struct{}{}
			tgDesc[row.LevelOne+"||"+row.LevelTwo+"||"+row.LevelThree] = struct{}{}
		}
	}
	if len(newData) == 0 {
		return newData, fmt.Errorf("请检测数据：数据为空")
	}
	return newData, nil
}

// AllTagOpts 标签筛选列表接口
func AllTagOpts(ctx echo.Context, param *pb.TagOptsReq) (*pb.TagOptsResp, error) {
	account := cast.ToString(ctx.Get(cst.AccountInfoCtx))
	//if ctx.Get(cst.AccountIsAdminCtx).(bool) {
	//	account = ""
	//}
	tagLibs, err := dto.NewAllTagLib().AllTagLibByPerms(ctx.Request().Context(), account, param)
	if err != nil {
		return nil, err
	}
	var eg errgroup.Group
	tagChan := make(chan *pb.TagOptsResp_Tag, len(tagLibs))
	for _, tagLib := range tagLibs {
		tagLib := tagLib
		eg.Go(func() error {
			tagList, err := allTreeRecursive(ctx, tagLib.LibID, true)
			if err != nil {
				return err
			}
			if len(tagList) == 0 {
				return nil
			}
			tagChan <- &pb.TagOptsResp_Tag{
				TagId:    tagLib.LibID,
				TagName:  tagLib.LibName,
				Disable:  false,
				Level:    0,
				Children: tagList,
			}
			return nil
		})
	}
	go func() {
		defer func() {
			close(tagChan)
			if err := recover(); err != nil {
				logger.Errorf(ctx.Request().Context(), "TagOpts err:%v", err)
			}
		}()
		if err := eg.Wait(); err != nil {
			logger.Errorf(ctx.Request().Context(), "Tag opts errgroup err:%v", err.Error())
		}
	}()
	tagList := make([]*pb.TagOptsResp_Tag, 0)
	for tag := range tagChan {
		tagList = append(tagList, tag)
	}
	sort.SliceStable(tagList, func(i, j int) bool {
		return tagList[i].TagId < tagList[j].TagId
	})
	return &pb.TagOptsResp{Data: tagList}, nil
}

// AllTagLibList 标签组列表
func AllTagLibList(ctx echo.Context, req *pb.TagLibListReq) (*pb.TagLibListResp, error) {
	list, err := dto.NewAllTagLib().AllGetTagLibList(ctx.Request().Context(), req)
	if err != nil {
		return nil, err
	}
	return &pb.TagLibListResp{Data: list}, nil
}

// AllTagLibEnable 标签组禁用启用接口
func AllTagLibEnable(ctx echo.Context, req *pb.EnableReq) error {
	return dto.NewTagLib().TagLibEnable(ctx.Request().Context(), req.ObjectId, req.Enable, cast.ToString(ctx.Get(cst.AccountInfoCtx)))
}

// AllTagLibInfo 标签组信息
func AllTagLibInfo(ctx echo.Context, tagLibId uint32) (*pb.TagLibInfoResp, error) {
	tagLibInfo, err := dto.NewAllTagLib().AllGetTagLibInfo(ctx.Request().Context(), tagLibId)
	if err != nil {
		return nil, err
	}
	dest := &pb.TagLibInfoResp{
		LibId:    tagLibInfo.LibID,
		LibName:  tagLibInfo.LibName,
		Projects: make([]string, 0),
	}
	for _, t := range tagLibInfo.Projects {
		dest.Projects = append(dest.Projects, t.Project)
	}
	return dest, nil
}

func allCompareGenFinalTree(oldNode, newNode, finalNode *[]*TagNode, toDelete *[]*models.FpOpsTags, toAdd *[]*models.FpOpsTags) {

	//if oldNode == nil {
	//	*oldNode = make([]*TagNode, 0)
	//}
	//if newNode == nil {
	//	*newNode = make([]*TagNode, 0)
	//}
	//if finalNode == nil {
	//	*finalNode = make([]*TagNode, 0)
	//}
	if len(*oldNode) == 0 && len(*newNode) == 0 {
		return
	}
	// 1. 以旧数据为基准，比较新数据
	if oldNode != nil {
		for _, old := range *oldNode {
			var fnHas bool
			var curFinal *TagNode
			for _, fn := range *finalNode {
				if fn.Name == old.Name {
					fnHas = true
					curFinal = fn
					break
				}
			}
			if !fnHas {
				curFinal = &TagNode{
					Name:     old.Name,
					Level:    old.Level,
					TagId:    old.TagId,
					State:    TagNodeStateUpdate,
					ParentId: old.ParentId,
					Children: make([]*TagNode, 0),
				}
				*finalNode = append(*finalNode, curFinal)
			}

			var nwHas bool
			var nwNode = &TagNode{}
			for _, nw := range *newNode {
				if nw.Name == old.Name {
					nwHas = true
					nwNode = nw
					break
				}
			}
			if !nwHas { // 不存在
				curFinal.State = TagNodeStateDel
				*toDelete = append(*toDelete, &models.FpOpsTags{TagID: old.TagId, TagName: old.Name, ParentID: old.ParentId, Level: old.Level})
			}
			// 递归判断下一层
			if old.Children == nil {
				old.Children = make([]*TagNode, 0)
			}
			if nwNode.Children == nil {
				nwNode.Children = make([]*TagNode, 0)
			}
			allCompareGenFinalTree(&old.Children, &nwNode.Children, &curFinal.Children, toDelete, toAdd)
		}
	}
	// 2. 以新数据为基准，比较旧数据
	for _, nw := range *newNode {
		var fnHas bool
		var curFinal *TagNode
		for _, fn := range *finalNode {
			if fn.Name == nw.Name {
				fnHas = true
				curFinal = fn
				break
			}
		}

		if !fnHas {
			curFinal = &TagNode{
				Name:     nw.Name,
				Level:    nw.Level,
				State:    TagNodeStateAdd,
				Children: make([]*TagNode, 0),
			}
			*finalNode = append(*finalNode, curFinal)
			*toAdd = append(*toAdd, &models.FpOpsTags{TagName: nw.Name, Level: nw.Level})
		}
		// 递归判断下一层
		if nw.Children == nil {
			nw.Children = make([]*TagNode, 0)
		}
		oldEmpty := make([]*TagNode, 0)
		allCompareGenFinalTree(&oldEmpty, &nw.Children, &curFinal.Children, toDelete, toAdd)
	}
}

func AllgenerateFileName(baseName string) string {
	rand.Seed(time.Now().UnixNano())
	randomNum := rand.Intn(1000)
	timeStamp := time.Now().Format("20060102150405") + fmt.Sprintf("%03d", randomNum)
	return fmt.Sprintf("%s_%s.xlsx", baseName, timeStamp)
}

// AlldownloadFile 下载远程文件到本地
func AlldownloadFile(url, outputPath string) error {
	resp, err := http.Get(url)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	// 创建本地文件
	file, err := os.Create(outputPath)
	if err != nil {
		return err
	}
	defer file.Close()

	// 将 HTTP 响应体写入本地文件
	_, err = io.Copy(file, resp.Body)
	if err != nil {
		return err
	}

	return nil
}

func AllTagLibUpload(ctx context.Context, libFileUrl string) ([]*pb.TagGroup, error) {
	fun := "configure.AllTagLibUpload"
	fileName := AllgenerateFileName("downloaded_file")
	err := AlldownloadFile(libFileUrl, fileName)
	if err != nil {
		logger.Errorf(ctx, "%s downloading file return err. file:%s. err:%v", fun, libFileUrl, err)
		return nil, err
	}
	defer func() {
		// 判断 本地文件 fileName 是否存在，存在则删除
		if _, err := os.Stat(fileName); err == nil {
			logger.Infof(ctx, "%s remove temp file. originFile:%s. locFile:%s", fun, libFileUrl, fileName)
			_ = os.Remove(fileName)
		}
	}()
	xlsx, err := excelize.OpenFile(fileName)
	if err != nil {
		return nil, err
	}
	defer xlsx.Close()
	var headerF = func(_row []string) error {
		_header := []string{"一级", "二级", "三级"}
		if len(_row) < len(_header) {
			return fmt.Errorf("文件 Header 头信息不匹配")
		}
		for i, h := range _header {
			if _row[i] != h {
				return fmt.Errorf("文件 Header 头信息不匹配. idx:%d. need:%s. current:%s", i+1, h, _row[i])
			}
		}
		return nil
	}

	rows, err := xlsx.GetRows("Sheet1")
	if err != nil {
		return nil, err
	}

	var tagGroups []*pb.TagGroup
	for idx, row := range rows {

		if idx == 0 { // first
			if err := headerF(row); err != nil {
				return nil, err
			}
			continue
		}
		if len(row) < 1 {
			continue // 忽略空行
		}

		allEmpty := true
		for _, cell := range row {
			if cell != "" {
				allEmpty = false
				break
			}
		}

		if allEmpty {
			continue
		}

		tagGroup := &pb.TagGroup{}
		if len(row) >= 1 {
			tagGroup.LevelOne = strings.TrimSpace(row[0])
		}
		if len(row) >= 2 {
			tagGroup.LevelTwo = strings.TrimSpace(row[1])
		}
		if len(row) >= 3 {
			tagGroup.LevelThree = strings.TrimSpace(row[2])
		}

		tagGroups = append(tagGroups, tagGroup)
	}

	return tagGroups, nil
}

type AllTagNodeState int

const (
	AllTagNodeStateAdd AllTagNodeState = iota + 1
	AllTagNodeStateDel
	AllTagNodeStateUpdate
)

type AllTagNode struct {
	Name     string       // tag 名称
	Level    uint32       // 层级 ： 1 2 3
	TagId    uint32       // tag_id 标签id
	ParentId uint32       // 父级id
	State    TagNodeState // 状态
	Children []*TagNode
}

func AllBuildTagTree(tags []*pb.TagGroup) *TagNode {
	root := &TagNode{Name: "Root", Children: []*TagNode{}}
	tagMap := make(map[string]*TagNode)

	for _, tag := range tags {
		levelOne, levelTwo, levelThree := tag.LevelOne, tag.LevelTwo, tag.LevelThree

		// Create or retrieve level one node
		if levelOne == "" {
			continue
		}
		levelOneNode, ok := tagMap[levelOne]
		if !ok {
			levelOneNode = &TagNode{Name: levelOne, Level: 1, Children: []*TagNode{}}
			tagMap[levelOne] = levelOneNode
			root.Children = append(root.Children, levelOneNode)
		}

		// Create or retrieve level two node
		if levelTwo == "" {
			continue
		}
		keyTwo := fmt.Sprintf("%s||%s", levelOne, levelTwo)
		levelTwoNode, ok := tagMap[keyTwo]
		if !ok {
			levelTwoNode = &TagNode{Name: levelTwo, Level: 2, Children: []*TagNode{}}
			tagMap[keyTwo] = levelTwoNode
			levelOneNode.Children = append(levelOneNode.Children, levelTwoNode)
		}

		// Create level three node and add it to level two node
		if levelThree == "" {
			continue
		}
		keyThree := fmt.Sprintf("%s||%s||%s", levelOne, levelTwo, levelThree)
		levelThreeNode := &TagNode{Name: levelThree, Level: 3, Children: []*TagNode{}}
		tagMap[keyThree] = levelThreeNode
		levelTwoNode.Children = append(levelTwoNode.Children, levelThreeNode)
	}
	return root
}

func AllPrintTagTree(node *TagNode, level int) {
	if node == nil {
		return
	}

	for _, child := range node.Children {
		for i := 0; i < level; i++ {
			fmt.Print("  ")
		}
		AllPrintTagTree(child, level+1)
	}
}

func allTreeRecursive(ctx echo.Context, libId uint32, enable bool) ([]*pb.TagOptsResp_Tag, error) {
	var recursive func(list []*models.FpOpsTags, pid uint32) []*pb.TagOptsResp_Tag
	recursive = func(list []*models.FpOpsTags, pid uint32) []*pb.TagOptsResp_Tag {
		treeItem := make([]*pb.TagOptsResp_Tag, 0)
		for _, item := range list {
			if item.ParentID == pid {
				tag := &pb.TagOptsResp_Tag{
					TagId:    item.TagID,
					TagName:  item.TagName,
					Level:    item.Level,
					Enable:   item.Enable > 0,
					Children: recursive(list, item.TagID),
				}
				treeItem = append(treeItem, tag)
			}
		}
		return treeItem
	}
	list, err := dto.NewTags().TagList(ctx.Request().Context(), libId, enable)
	if err != nil {
		return nil, err
	}
	sort.SliceStable(list, func(i, j int) bool {
		return list[i].Level < list[j].Level
	})
	return recursive(list, 0), nil
}
