package configure

import (
	"github.com/labstack/echo/v4"
	"ops-ticket-api/internal/persistence"
	"ops-ticket-api/proto/pb"
)

// PortraitInfo 获取玩家画像信息
func PortraitInfo(ctx echo.Context, req *pb.PortraitInfoReq) (*pb.PortraitInfoResp, error) {
	return persistence.NewPortrait().DiscordPortraitInfo(ctx, req)
}

// PortraitEditInfo 编辑玩家画像信息
func PortraitEditInfo(ctx echo.Context, req *pb.PortraitEditReq) error {
	return persistence.NewPortrait().EditPortraitInfo(ctx, req)
}
