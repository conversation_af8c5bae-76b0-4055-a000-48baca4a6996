package configure

import (
	"fmt"
	"github.com/labstack/echo/v4"
	"github.com/spf13/viper"

	"github.com/tidwall/gjson"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"gitlab-ee.funplus.io/ops-tools/compkg/httpclient"
	"go.uber.org/zap"
	"net/http"
	"ops-ticket-api/internal/code"
	"ops-ticket-api/internal/cst"
	"ops-ticket-api/internal/framework/xerrors"
	"ops-ticket-api/services/commsrv"
	"ops-ticket-api/utils"
	"strings"
)

type (
	sProjectCfg struct {
		c echo.Context
	}
)

func ProjectCfg(ctx echo.Context) *sProjectCfg {
	return &sProjectCfg{
		c: ctx,
	}
}

func (s *sProjectCfg) GameList() (map[string]interface{}, error) {
	account := s.c.Get(cst.AccountInfoCtx).(string)
	resp := make(map[string]interface{})
	gwGameList, err := s.GwGameList()
	if err != nil {
		return resp, err
	}
	if len(gwGameList) == 0 {
		return resp, nil
	}
	// 获取用户权限
	permList, err := s.PermGameList()
	if err != nil {
		return resp, err
	}
	// set user perm game list. super_admin  is empty.
	commsrv.SetPermGmList(s.c.Request().Context(), account, permList)
	for prd, pjData := range gwGameList {
		resp[prd] = pjData
	}
	if len(permList) > 0 && len(resp) > 0 {
		permResp := make(map[string]interface{})
		for _, gm := range permList {
			if data, ok := resp[gm]; ok {
				permResp[gm] = data
			}
		}
		return permResp, nil
	}
	return resp, nil
}

func (s *sProjectCfg) GwGameList() (map[string]map[string]interface{}, error) {
	gwToken := s.c.Request().Header.Get("Admin-Gateway-Token")
	client := httpclient.New()
	client.SetHeader("Admin-Gateway-Token", gwToken)
	api := viper.GetString("pkg.gw_gameList_api")
	logger.Info(s.c.Request().Context(), "gateway gameList api start", zap.String("api", api))
	ret, err := client.Get(s.c.Request().Context(), api, nil)
	if err != nil {
		logger.Info(s.c.Request().Context(), "gateway gameList api error", zap.Error(err))
		return nil, err
	}
	respBody := ret.String()
	logger.Info(s.c.Request().Context(), "gateway gameList api finish", zap.String("data", respBody))
	respJson := gjson.Parse(respBody)
	if respJson.Get("code").Int() != 0 {
		logger.Info(s.c.Request().Context(), "gateway gameList api response code error", zap.Int64("code", respJson.Get("code").Int()))
		return nil, xerrors.New(code.StatusText(code.Error), code.Error)
	}
	gwGameList := make(map[string]map[string]interface{})
	respJson.Get("data").ForEach(func(key, value gjson.Result) bool {
		gwGameList[key.String()] = value.Value().(map[string]interface{})
		return true
	})
	return gwGameList, nil
}

func (s *sProjectCfg) PermGameList() ([]string, error) {
	gameList := make([]string, 0)
	if s.c.Get(cst.AccountIsAdminCtx).(bool) {
		return gameList, nil
	}
	gwToken := s.c.Request().Header.Get("Admin-Gateway-Token")
	api := viper.GetString("pkg.gw_perm_api")
	body := map[string]interface{}{
		"prd_code": "ops-ticket-api",
	}
	client := httpclient.New()
	client.SetHeader("admin-gateway-token", gwToken)
	logger.Info(s.c.Request().Context(), "[ChatPermCheck] start request gateway", zap.String("perm-api", api), zap.Any("body", body))
	resp, err := client.PostJson(s.c.Request().Context(), api, body)
	if err != nil {
		logger.Error(s.c.Request().Context(), "[ChatPermCheck] gateway perm-api error", zap.Error(err))
		return gameList, err
	}
	logger.Info(s.c.Request().Context(), "[ChatPermCheck] end request gateway", zap.String("perm-api", api), zap.String("resp", resp.String()))
	if resp.StatusCode != http.StatusOK {
		err = fmt.Errorf("response http-code:%d", resp.StatusCode)
		logger.Error(s.c.Request().Context(), "[ChatPermCheck] gateway perm-api error", zap.Error(err))
		return gameList, err
	}
	respData := gjson.Parse(resp.String())
	if respData.Get("code").Int() != 0 {
		err = fmt.Errorf("response body-code:%d", respData.Get("code").Int())
		logger.Error(s.c.Request().Context(), "[ChatPermCheck] gateway perm-api", zap.Error(err))
		return gameList, err
	}
	respData.Get("data.game_list").ForEach(func(key, value gjson.Result) bool {
		gameStr := strings.TrimSpace(value.String())
		gameList = append(gameList, gameStr)
		return true
	})
	if len(gameList) > 0 {
		gameList = utils.RmDupAny(gameList)
	}
	return gameList, nil
}
