package configure

import (
	"bytes"
	"encoding/csv"
	"fmt"
	"github.com/labstack/echo/v4"
	"github.com/spf13/cast"
	"io"
	p "ops-ticket-api/internal/persistence"
	"ops-ticket-api/models"
	"ops-ticket-api/proto/pb"
)

var lineMaintainConfigListExportHeader = []string{
	"玩家Line ID", "玩家Line昵称", "fpid", "uid", "服务器", "VIP专员", "VIP状态", "生日",
}

// LineMaintainConfigList 玩家维护关系配置列表
func LineMaintainConfigList(ctx echo.Context, req *pb.LineMaintainConfigListReq) (*pb.LineMaintainConfigListResp, error) {
	list, total, err := p.NewLineMaintainConfig().MaintainConfigList(ctx, req, false)
	if err != nil {
		return nil, err
	}
	return &pb.LineMaintainConfigListResp{
		CurrentPage: req.Page,
		PerPage:     req.GetPageSize(),
		Total:       total,
		Data:        list,
	}, nil
}

func LineMaintainConfigListExport(ctx echo.Context, req *pb.LineMaintainConfigListReq) (chan []byte, error) {
	data, total, err := p.NewLineMaintainConfig().MaintainConfigList(ctx, req, true)
	if err != nil {
		return nil, err
	}
	// 限制单次导出数据不超过6000条
	if total > 6000 {
		return nil, fmt.Errorf("export data should not surpass %d, you export %d", 6000, total)
	}
	var records = make([][]string, 0)
	for _, row := range data {
		vipState := ""
		if row.VipState == 1 {
			vipState = "非VIP"
		} else {
			vipState = "VIP"
		}
		records = append(records, []string{
			row.LineUserId, row.NickName, row.Fpid + "\t", cast.ToString(row.Uid) + "\t", row.Sid, row.Maintainer, vipState, row.Birthday,
		})
	}
	buf := bytes.Buffer{}
	buf.WriteString("\xEF\xBB\xBF")
	recordChan := make(chan []byte)
	go func(buff *bytes.Buffer) {
		writer := csv.NewWriter(buff)
		_ = writer.Write(lineMaintainConfigListExportHeader)
		batch := 0
		for _, record := range records {
			if err := writer.Write(record); err != nil {
				continue
			}
			batch++
			if batch > 32 {
				writer.Flush()
				byteInfo, _ := io.ReadAll(buff)
				recordChan <- byteInfo
				batch = 0
			}
		}
		writer.Flush()
		recordChan <- buff.Bytes()
		close(recordChan)
	}(&buf)

	return recordChan, err
}

func LineMaintainConfigEdit(ctx echo.Context, req *pb.LineMaintainConfigEditReq, player models.DiscordPlayer) error {
	return p.NewLineMaintainConfig().MaintainConfigEdit(ctx, req, player)
}

func LineMaintainConfigDel(ctx echo.Context, req *pb.LineMaintainConfigDelReq) error {
	return p.NewLineMaintainConfig().MaintainConfigDel(ctx, req)
}

func LinePlayerAccounts(ctx echo.Context, req *pb.LinePlayerAccountsReq) (*pb.LinePlayerAccountsResp, error) {
	resp := new(pb.LinePlayerAccountsResp)
	data, err := p.NewLineMaintainConfig().LinePlayerAccounts(ctx, req)
	if err != nil {
		return resp, err
	}
	resp.LineUserIds = data
	return resp, nil
}

func LinePlayerUidList(ctx echo.Context, req *pb.DiscordPlayerUidReq) ([]int64, error) {
	data, err := p.NewLineMaintainConfig().LinePlayerUidList(ctx, req)
	if err != nil {
		return data, err
	}
	return data, nil
}
