package configure

import (
	"ops-ticket-api/internal/persistence"
	"ops-ticket-api/proto/pb"

	"github.com/labstack/echo/v4"
)

var DCBotConfigService = new(dcBotConfigService)

type dcBotConfigService struct{}

// Add 新增Discord机器人配置
func (s *dcBotConfigService) Add(ctx echo.Context, req *pb.DCSBotConfigAddReq) error {
	return persistence.NewDcsBotConfigRepo().Add(ctx, req)
}

// Check 验证Discord机器人配置
func (s *dcBotConfigService) Check(ctx echo.Context, req *pb.DCSBotConfigAddReq) error {
	return persistence.NewDcsBotConfigRepo().Check(ctx, req)
}

// List 获取Discord机器人配置列表
func (s *dcBotConfigService) List(ctx echo.Context, req *pb.DCSBotConfigListReq) (*pb.DCSBotConfigListResp, error) {
	return persistence.NewDcsBotConfigRepo().List(ctx, req)
}

// UpdateWelcomeMessage 更新DC机器人配置欢迎消息
func (s *dcBotConfigService) UpdateWelcomeMessage(ctx echo.Context, req *pb.UpdateDCSBotConfigWelcomeMessageReq) error {
	return persistence.NewDcsBotConfigRepo().UpdateWelcomeMessage(ctx, req)
}
