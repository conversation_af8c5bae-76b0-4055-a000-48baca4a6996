package configure

import (
	"context"
	"fmt"
	"sort"
	"strings"

	"ops-ticket-api/internal/code"
	"ops-ticket-api/internal/framework/cache/keys"
	"ops-ticket-api/internal/framework/cache/rds"
	dto "ops-ticket-api/internal/persistence"
	"ops-ticket-api/models"
	"ops-ticket-api/proto/pb"
	"ops-ticket-api/utils"

	jsoniter "github.com/json-iterator/go"
	"github.com/labstack/echo/v4"
	"github.com/spf13/cast"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"go.uber.org/zap"
)

// MatchCatTpl 获取问题分类子列表
func MatchCatTpl(ctx context.Context, country, channel string, sid int64, tplJson string) uint32 {
	// 筛选3级分类模板
	var tplList []*pb.CatTplPrefer
	if country != "" {
		country = strings.ToLower(country)
	}
	jsoniter.ConfigFastest.UnmarshalFromString(tplJson, &tplList)
	var tplId uint32
	for _, tpl := range tplList {
		if len(tpl.Country) == 0 && len(tpl.Channel) == 0 && tpl.ServerStr == "" {
			tplId = tpl.TplId
			continue
		}
		if len(tpl.Country) > 0 && !utils.InArrayAny(country, tpl.Country) {
			continue
		}
		if len(tpl.Channel) > 0 && !utils.InArrayAny(channel, tpl.Channel) {
			continue
		}
		if tpl.ServerStr == "" {
			return tpl.TplId
		}
		serv := utils.SplitServer(tpl.ServerStr)
		if utils.InArrayAny(sid, serv.Ids) {
			return tpl.TplId
		}
		for _, sBtw := range serv.Btw {
			if sid >= sBtw.Start && sid <= sBtw.End {
				return tpl.TplId
			}
		}
	}
	return tplId
}

// OldTicketCatInfo 获取问题分类子列表
func OldTicketCatInfo(ctx context.Context, project string, catId uint32, params map[string]string) (*models.FpOpsCategory, error) {
	catInfo, err := dto.NewCat().OldTicketCatInfoById(ctx, project, catId)
	if err != nil {
		return nil, err
	}
	country := params["country"]
	channel := params["channel"]
	sid := params["sid"]
	if pb.CatLevel_ThreeClassification == pb.CatLevel(catInfo.Level) &&
		catInfo.RelateType == code.RelateTypeTpl &&
		catInfo.TplList != "" && sid != "" {
		// 筛选3级分类模板
		sidInt := cast.ToInt64(sid)
		tplId := MatchCatTpl(ctx, country, channel, sidInt, catInfo.TplList)
		if tplId > 0 {
			catInfo.TplID = tplId
		}
	}
	return catInfo, nil
}

// CatInfo 获取问题分类子列表 -- 去掉 根据server 筛选逻辑
func CatInfo(ctx echo.Context, project string, catId uint32, params map[string]string) (*models.FpOpsCategory, error) {
	catInfo, err := dto.NewCat().CatInfoById(ctx.Request().Context(), project, catId)
	if err != nil {
		return nil, err
	}
	return catInfo, nil
}

// CatTree 问题分类配置(树)列表接口
func CatTree(ctx echo.Context, lang, project string) (*pb.CatOptsResp, error) {
	treeInfoCat := make([]*pb.CatOptsResp_Cat, 0)
	getTree := func(v interface{}) error {
		catInfoList, err := dto.NewCat().CatTree(ctx.Request().Context(), project, lang)
		if err != nil {
			return err
		}
		data := buildData(catInfoList, lang)
		treeInfoCat = buildTree(0, 1, data)
		return nil
	}
	if err := rds.RCli.QueryRowHash(ctx.Request().Context(), fmt.Sprintf(keys.TicketCatOpts, project), lang, &treeInfoCat, false, getTree); err != nil {
		return nil, err
	}
	return &pb.CatOptsResp{Data: treeInfoCat}, nil
}

func buildData(list []*pb.CatItems, lang string) map[uint32]map[uint32]*pb.CatOptsResp_Cat {
	data := make(map[uint32]map[uint32]*pb.CatOptsResp_Cat)
	for _, cat := range list {
		var pid uint32
		switch pb.CatLevel(cat.Level) {
		case pb.CatLevel_PrimaryClassification:
			pid = 0
		case pb.CatLevel_SecondaryClassification:
			pid = cat.OneLevel
		case pb.CatLevel_ThreeClassification:
			pid = cat.SecondLevel
		}
		if _, ok := data[pid]; !ok {
			data[pid] = make(map[uint32]*pb.CatOptsResp_Cat)
		}
		data[pid][cat.CatId] = &pb.CatOptsResp_Cat{
			Id:       cat.CatId,
			Label:    cat.Category,
			Level:    cat.Level,
			Children: make([]*pb.CatOptsResp_Cat, 0),
		}
		if lang != "" && cat.LangCategory != "" {
			data[pid][cat.CatId].Label = cat.LangCategory
		}
	}
	return data
}

func buildTree(parentId, level uint32, data map[uint32]map[uint32]*pb.CatOptsResp_Cat) []*pb.CatOptsResp_Cat {
	node := make([]*pb.CatOptsResp_Cat, 0)
	for id, item := range data[parentId] {
		item.Children = nil
		if data[id] != nil {
			level++
			item.Children = buildTree(id, level, data)
			level--
		}
		if level <= uint32(pb.CatLevel_SecondaryClassification) &&
			(item.Children == nil || len(item.Children) == 0) {
			continue
		}
		node = append(node, item)
	}
	sort.SliceStable(node, func(i, j int) bool {
		return node[i].Id < node[j].Id
	})
	return node
}

// CategoryRelease 配置更新接口
func CategoryRelease(ctx echo.Context, param *pb.CatProjectLang) error {
	project := param.Project
	// todo  迁移完成时 补足缓存删除功能 和 刷缓存功能

	pipe := rds.RCli.Pipeline()
	pipe.Del(ctx.Request().Context(), keys.TicketCatInfo+project)
	pipe.Del(ctx.Request().Context(), fmt.Sprintf(keys.TicketCatOpts, project))

	if _, err := pipe.Exec(ctx.Request().Context()); err != nil {
		logger.Error(ctx.Request().Context(), "del cat info from cache err", zap.String("project", project), zap.String("err", err.Error()))
		return err
	}

	return nil
}
