package configure

import (
	"errors"
	"github.com/go-sql-driver/mysql"
	"github.com/labstack/echo/v4"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"ops-ticket-api/internal/code"
	"ops-ticket-api/internal/cst"
	"ops-ticket-api/internal/framework/lang"
	"ops-ticket-api/internal/framework/xerrors"
	dto "ops-ticket-api/internal/persistence"
	"ops-ticket-api/models"
	"ops-ticket-api/proto/pb"
	"sort"
	"time"
)

func CatModuleTree(ctx echo.Context, project string) (*pb.ModuleCatTreeResp, error) {
	treeInfoCat := make([]*pb.ModuleCatTreeResp_Cat, 0)
	catInfoList, err := dto.NewModuleCat().CatModuleTree(ctx.Request().Context(), project)
	if err != nil {
		return nil, err
	}
	data := buildModuleCategoryData(catInfoList)
	treeInfoCat = buildModuleCategoryTree(0, 1, data)
	return &pb.ModuleCatTreeResp{Data: treeInfoCat}, nil
}

func buildModuleCategoryData(list []*pb.ModuleCatItems) map[uint32]map[uint32]*pb.ModuleCatTreeResp_Cat {
	data := make(map[uint32]map[uint32]*pb.ModuleCatTreeResp_Cat)
	for _, cat := range list {
		pid := cat.ParentId
		if _, ok := data[pid]; !ok {
			data[pid] = make(map[uint32]*pb.ModuleCatTreeResp_Cat)
		}
		data[pid][cat.CatId] = &pb.ModuleCatTreeResp_Cat{
			CatId:    cat.CatId,
			Category: cat.Category,
			Level:    cat.Level,
			Children: make([]*pb.ModuleCatTreeResp_Cat, 0),
		}
	}
	return data
}

func buildModuleCategoryTree(parentId, level uint32, data map[uint32]map[uint32]*pb.ModuleCatTreeResp_Cat) []*pb.ModuleCatTreeResp_Cat {
	node := make([]*pb.ModuleCatTreeResp_Cat, 0)
	for id, item := range data[parentId] {
		item.Children = nil
		if data[id] != nil {
			level++
			item.Children = buildModuleCategoryTree(id, level, data)
			level--
		}
		node = append(node, item)
	}
	sort.SliceStable(node, func(i, j int) bool {
		return node[i].CatId < node[j].CatId
	})
	return node
}

// CatModuleAdd 添加问题分类接口
func CatModuleAdd(ctx echo.Context, req *pb.ModuleCatAddReq) error {
	// 同一个游戏下分类是否相同
	err := dto.NewModuleCat().CheckCatModuleName(ctx.Request().Context(), req.Category, req.Project, 0)
	if err != nil {
		return err
	}
	// 创建分类记录
	cat := &models.FpOpsModuleCategory{
		Project:   req.Project,
		Category:  req.Category,
		Level:     req.GetCatLevel(),
		ParentID:  req.ParentId,
		Operator:  ctx.Get(cst.AccountInfoCtx).(string),
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}
	if err := dto.NewModuleCat().CatModuleAdd(ctx.Request().Context(), cat); err != nil {
		logger.Errorf(ctx.Request().Context(), "CatModuleAdd error: %v", err)
		return xerrors.New(err.Error(), code.DbError)
	}
	return nil
}

// CatModuleSave 修改问题分类接口
func CatModuleSave(ctx echo.Context, req *pb.ChannelCatSaveReq) error {
	catInfo, err := dto.NewModuleCat().ModuleDetail(ctx, req.CatId)
	if err != nil {
		return err
	}
	if catInfo.Level != req.GetCatLevel() {
		return xerrors.New("层级参数错误", code.InvalidParams)
	}

	// 同一个游戏下分类是否相同
	err = dto.NewModuleCat().CheckCatModuleName(ctx.Request().Context(), req.Category, catInfo.Project, req.CatId)
	if err != nil {
		return err
	}
	cat := map[string]interface{}{
		"category":   req.Category,
		"operator":   ctx.Get(cst.AccountInfoCtx).(string),
		"updated_at": time.Now(),
	}
	if err := dto.NewModuleCat().CatModuleSave(ctx.Request().Context(), req.CatId, cat); err != nil {
		var dbErr *mysql.MySQLError
		if errors.As(err, &dbErr) && dbErr.Number == 1062 {
			return xerrors.New(lang.FormatText(ctx, "RepeatDataTip"), code.IdempotenceErr)
		}
		return xerrors.New(err.Error(), code.DbError)
	}
	return nil
}

// CatModuleDel 问题分类删除
func CatModuleDel(ctx echo.Context, catId uint32) error {
	if err := dto.NewModuleCat().CatModuleDel(ctx.Request().Context(), catId, ctx.Get(cst.AccountInfoCtx).(string)); err != nil {
		return err
	}
	return nil
}
