package configure

import (
	"github.com/labstack/echo/v4"
	"ops-ticket-api/internal/persistence"
	"ops-ticket-api/proto/pb"
)

// LinePortraitInfo 获取玩家画像信息
func LinePortraitInfo(ctx echo.Context, req *pb.LinePortraitInfoReq) (*pb.LinePortraitInfoResp, error) {
	return persistence.NewLinePortrait().LinePortraitInfo(ctx, req)
}

// LinePortraitEditTag 编辑玩家画像标签
func LinePortraitEditTag(ctx echo.Context, req *pb.LinePortraitEditTagReq) error {
	return persistence.NewLinePortrait().EditPortraitTag(ctx, req)
}

// LinePortraitEditBasic 编辑玩家画像基础信息
func LinePortraitEditBasic(ctx echo.Context, req *pb.LinePortraitEditBasicReq) error {
	return persistence.NewLinePortrait().EditPortraitBasic(ctx, req)
}

// LinePortraitEditRemark 编辑玩家画像备注信息
func LinePortraitEditRemark(ctx echo.Context, req *pb.LinePortraitEditRemarkReq) error {
	return persistence.NewLinePortrait().EditPortraitRemark(ctx, req)
}
