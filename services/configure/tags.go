// Copyright 2021 funplus Authors. All Rights Reserved.
// @Description: 标签
// @Author: Darcy
// @Date: 2021/10/26 3:46 PM

package configure

import (
	"context"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"ops-ticket-api/models"
	"sort"
)

func BuildTagTreeList(tags []*models.FpOpsTags) *TagNode {
	sort.SliceStable(tags, func(i, j int) bool {
		return tags[i].Level < tags[j].Level
	})
	tagMap := make(map[uint32]*TagNode)
	root := &TagNode{Name: "Root", Level: 0}

	for _, tag := range tags {
		node := &TagNode{Name: tag.TagName, Level: tag.Level, TagId: tag.TagID, ParentId: tag.ParentID}
		tagMap[tag.TagID] = node

		if tag.ParentID == 0 {
			root.Children = append(root.Children, node)
		} else {
			if parentNode, ok := tagMap[tag.ParentID]; ok {
				parentNode.Children = append(parentNode.Children, node)
			} else {
				logger.Errorf(context.TODO(), "tag parent not found, tagID: %d, parentID: %d", tag.TagID, tag.ParentID)
				continue
			}
		}
	}

	return root
}
