package configure

import (
	"bytes"
	"encoding/csv"
	"fmt"
	"github.com/labstack/echo/v4"
	"github.com/spf13/cast"
	"io"
	p "ops-ticket-api/internal/persistence"
	"ops-ticket-api/models"
	"ops-ticket-api/proto/pb"
)

var maintainConfigListExportHeader = []string{
	"玩家DC ID", "玩家DC昵称", "fpid", "uid", "服务器", "语言", "VIP专员", "VIP状态", "生日",
}

// MaintainConfigList 玩家维护关系配置列表
func MaintainConfigList(ctx echo.Context, req *pb.MaintainConfigListReq) (*pb.MaintainConfigListResp, error) {
	list, total, err := p.NewMaintainConfig().MaintainConfigList(ctx, req, false)
	if err != nil {
		return nil, err
	}
	return &pb.MaintainConfigListResp{
		CurrentPage: req.Page,
		PerPage:     req.GetPageSize(),
		Total:       total,
		Data:        list,
	}, nil
}

func MaintainConfigListExport(ctx echo.Context, req *pb.MaintainConfigListReq) (chan []byte, error) {
	data, total, err := p.NewMaintainConfig().MaintainConfigList(ctx, req, true)
	if err != nil {
		return nil, err
	}
	// 限制单次导出数据不超过6000条
	if total > 6000 {
		return nil, fmt.Errorf("export data should not surpass %d, you export %d", 6000, total)
	}
	var records = make([][]string, 0)
	for _, row := range data {
		vipState := ""
		if row.VipState == 1 {
			vipState = "非VIP"
		} else {
			vipState = "VIP"
		}
		records = append(records, []string{
			row.DscUserId + "\t", row.NickName, row.Fpid + "\t", cast.ToString(row.Uid) + "\t", row.Sid, row.Lang, row.Maintainer, vipState, row.Birthday,
		})
	}
	buf := bytes.Buffer{}
	buf.WriteString("\xEF\xBB\xBF")
	recordChan := make(chan []byte)
	go func(buff *bytes.Buffer) {
		writer := csv.NewWriter(buff)
		_ = writer.Write(maintainConfigListExportHeader)
		batch := 0
		for _, record := range records {
			if err := writer.Write(record); err != nil {
				continue
			}
			batch++
			if batch > 32 {
				writer.Flush()
				byteInfo, _ := io.ReadAll(buff)
				recordChan <- byteInfo
				batch = 0
			}
		}
		writer.Flush()
		recordChan <- buff.Bytes()
		close(recordChan)
	}(&buf)

	return recordChan, err
}

func MaintainConfigSave(ctx echo.Context, req *pb.MaintainConfigNewReq) error {
	return p.NewMaintainConfig().MaintainConfigSave(ctx, req)
}

func MaintainConfigEdit(ctx echo.Context, req *pb.MaintainConfigEditReq, player models.DiscordPlayer) error {
	return p.NewMaintainConfig().MaintainConfigEdit(ctx, req, player)
}

func MaintainConfigDel(ctx echo.Context, req *pb.MaintainConfigDelReq) ([]models.FpDscUser, error) {
	return p.NewMaintainConfig().MaintainConfigDel(ctx, req)
}

func DiscordPlayerAccounts(ctx echo.Context, req *pb.DiscordPlayerAccountsReq) (*pb.DiscordPlayerAccountsResp, error) {
	resp := new(pb.DiscordPlayerAccountsResp)
	data, err := p.NewMaintainConfig().DiscordPlayerAccounts(ctx, req)
	if err != nil {
		return resp, err
	}
	resp.DscUserIds = data
	return resp, nil
}

func DiscordPlayerUidList(ctx echo.Context, req *pb.DiscordPlayerUidReq) ([]int64, error) {
	data, err := p.NewMaintainConfig().DiscordPlayerUidList(ctx, req)
	if err != nil {
		return data, err
	}
	return data, nil
}
