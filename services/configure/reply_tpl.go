package configure

import (
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"go.uber.org/zap"
	"time"

	"ops-ticket-api/internal/code"
	"ops-ticket-api/internal/cst"
	"ops-ticket-api/internal/framework/lang"
	"ops-ticket-api/internal/framework/xerrors"
	dto "ops-ticket-api/internal/persistence"
	"ops-ticket-api/models"
	"ops-ticket-api/proto/pb"
	"ops-ticket-api/utils"

	"github.com/labstack/echo/v4"
)

// ReplyTplOpts 模版筛选列表接口
func ReplyTplOpts(ctx echo.Context) (*pb.TplOptsResp, error) {
	list, err := dto.NewReplyTpl(ctx).ReplyTplOpts(ctx.Request().Context())
	if err != nil {
		return nil, err
	}
	return &pb.TplOptsResp{List: list}, nil
}

// ReplyTplList 模版列表接口
func ReplyTplList(ctx echo.Context, req *pb.TplListReq) (*pb.TplListResp, error) {
	list, total, err := dto.NewReplyTpl(ctx).ReplyTplList(ctx.Request().Context(), req.GetTpl(), &req.Page, &req.PageSize)
	if err != nil {
		return nil, err
	}
	data := make([]*pb.TplListResp_TplInfo, 0)
	for _, v := range list {
		data = append(data, &pb.TplListResp_TplInfo{
			Id:        v.ID,
			Tpl:       v.ReplyTpl,
			UpdatedAt: utils.TimeFormat(v.UpdatedAt.Unix()),
			Op:        v.Operator,
			Enable:    v.Enable,
		})
	}
	return &pb.TplListResp{
		CurrentPage: req.Page,
		PerPage:     req.GetPageSize(),
		Total:       total,
		Data:        data,
	}, nil
}

// ReplyTplInfo 模版信息
func ReplyTplInfo(ctx echo.Context, tplId uint32) (*pb.TplInfoResp, error) {
	tplInfo, err := dto.NewReplyTpl(ctx).ReplyTplInfo(ctx.Request().Context(), tplId, true)
	if err != nil {
		return nil, err
	}
	replyContent := make(map[string]string)
	for _, tplLang := range tplInfo.Language {
		replyContent[tplLang.Lang] = tplLang.ReplyContent
	}
	return &pb.TplInfoResp{
		TplId:        tplInfo.ID,
		Tpl:          tplInfo.ReplyTpl,
		ReplyContent: replyContent,
	}, nil
}

// ReplyTplAdd 添加模版接口
func ReplyTplAdd(ctx echo.Context, req *pb.ReplyTplAddReq) error {
	tplModel := &models.FpOpsReplyTpl{
		Project:  req.Project,
		ReplyTpl: req.Tpl,
		Operator: ctx.Get(cst.AccountInfoCtx).(string),
		Language: make([]*models.FpOpsReplyTplLang, 0),
	}
	for lang, content := range req.GetReplyContent() {
		if content == "" {
			continue
		}
		tplLang := models.FpOpsReplyTplLang{
			Project:      req.Project,
			Lang:         lang,
			ReplyTpl:     req.Tpl,
			ReplyContent: content,
			CreatedAt:    time.Now().UTC(),
		}
		tplModel.Language = append(tplModel.Language, &tplLang)
	}
	if len(tplModel.Language) == 0 {
		return xerrors.New(lang.FormatText(ctx, "FillFormMultilingualTextTip"), code.MissingParams)
	}

	if err := dto.NewReplyTpl(ctx).ReplyTplAdd(ctx.Request().Context(), tplModel); err != nil {
		return err
	}
	return nil
}

// ReplyTplSave 修改模版接口
func ReplyTplSave(ctx echo.Context, req *pb.ReplyTplSaveReq) error {
	tplModel := &models.FpOpsReplyTpl{
		ReplyTpl:  req.Tpl,
		Operator:  ctx.Get(cst.AccountInfoCtx).(string),
		UpdatedAt: time.Now().UTC(),
		Language:  make([]*models.FpOpsReplyTplLang, 0),
	}
	tplInfo, err := dto.NewReplyTpl(ctx).ReplyTplInfo(ctx.Request().Context(), req.TplId, false)
	if err != nil {
		return err
	}
	for lang, content := range req.GetReplyContent() {
		if content == "" {
			continue
		}
		tplLang := models.FpOpsReplyTplLang{
			ReplyTplID:   req.TplId,
			Project:      tplInfo.Project,
			Lang:         lang,
			ReplyTpl:     req.Tpl,
			ReplyContent: content,
			CreatedAt:    time.Now().UTC(),
		}
		tplModel.Language = append(tplModel.Language, &tplLang)
	}
	if len(tplModel.Language) == 0 {
		return xerrors.New(lang.FormatText(ctx, "FillFormMultilingualTextTip"), code.MissingParams)
	}
	if err := dto.NewReplyTpl(ctx).ReplyTplSave(ctx.Request().Context(), req.TplId, tplModel); err != nil {
		xErr, ok := err.(xerrors.Error)
		if ok {
			if xErr.Code() == code.MissingParams {
				return xerrors.New(lang.FormatText(ctx, xErr.Error()), xErr.Code())
			}
		}
		return err
	}
	return nil
}

// ReplyTplEnable 模版禁用启用接口
func ReplyTplEnable(ctx echo.Context, req *pb.EnableReq) error {
	query := map[string]interface{}{
		"swift_reply_tpl_id": req.ObjectId,
	}
	exist, err := dto.NewCat().GetCatExistByTplId(ctx, query)
	if err != nil {
		return err
	}
	if exist && !req.Enable {
		logger.Info(ctx.Request().Context(), "The reply template has been bound,can't be disable", zap.Uint32("reply_tpl_id", req.ObjectId))
		return xerrors.New("EventDisableCatLog", code.Error)
	}
	return dto.NewReplyTpl(ctx).ReplyTplEnable(ctx.Request().Context(), req.ObjectId, req.Enable, ctx.Get(cst.AccountInfoCtx).(string))
}
