package configure

import (
	"github.com/labstack/echo/v4"
	dto "ops-ticket-api/internal/persistence"
	"ops-ticket-api/proto/pb"
	"ops-ticket-api/utils"
)

// GroupList 分单配置列表
func GroupList(ctx echo.Context, req *pb.GroupListReq) (*pb.GroupListResp, error) {
	list, total, err := dto.NewGroupRepo().GroupList(ctx.Request().Context(), req)
	if err != nil {
		return nil, err
	}
	return &pb.GroupListResp{
		CurrentPage: req.Page,
		PerPage:     req.GetPageSize(),
		Total:       total,
		Data:        list,
	}, nil
}

// GroupInfo 团队分单信息
func GroupInfo(ctx echo.Context, groupID uint32) (*pb.GroupInfoResp, error) {
	groupInfo, err := dto.NewGroupRepo().GroupInfo(ctx.Request().Context(), groupID)
	if err != nil {
		return nil, err
	}

	resp := &pb.GroupInfoResp{
		GroupDesc:  groupInfo.GroupDesc,
		User:       make([]string, 0),
		Game:       make([]string, 0),
		Language:   make([]string, 0),
		UpperLimit: groupInfo.UpperLimit,
	}

	for _, user := range groupInfo.User {
		resp.User = append(resp.User, user.User)
		resp.Game = utils.StrToSlice(user.Game)
		resp.Language = utils.StrToSlice(user.Language)
	}

	return resp, nil
}

// GroupSave 添加技能组接口
func GroupSave(ctx echo.Context, req *pb.GroupSaveReq) error {
	if req.Id == 0 { // 新增团队
		return dto.NewGroupRepo().GroupAdd(ctx, req)
	} else { // 更新单个用户
		return dto.NewGroupRepo().GroupUpdate(ctx, req)
	}
}

// GroupDel 删除技能组接口
func GroupDel(ctx echo.Context, req *pb.GroupDelReq) error {
	return dto.NewGroupRepo().GroupDel(ctx.Request().Context(), req)
}
