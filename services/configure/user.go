package configure

import (
	"github.com/labstack/echo/v4"
	dto "ops-ticket-api/internal/persistence"
	"ops-ticket-api/proto/pb"
)

type userSvc struct {
	repo *dto.UserRepo
}

var DefaultUserSvc = newUserSvc()

func newUserSvc() *userSvc {
	return &userSvc{
		repo: dto.NewUserRepo(),
	}
}

func (svc *userSvc) GetList(ctx echo.Context) ([]*pb.UserListResp, error) {
	list, err := svc.repo.List(ctx.Request().Context())
	if err != nil {
		return nil, err
	}
	return list, nil
}
