package configure

import (
	"errors"
	"github.com/go-sql-driver/mysql"
	"github.com/labstack/echo/v4"
	"ops-ticket-api/internal/code"
	"ops-ticket-api/internal/cst"
	"ops-ticket-api/internal/framework/lang"
	"ops-ticket-api/internal/framework/xerrors"
	dto "ops-ticket-api/internal/persistence"
	"ops-ticket-api/models"
	"ops-ticket-api/proto/pb"
	"sort"
	"time"
)

func CatChannelTree(ctx echo.Context, project string, catType uint32) (*pb.ChannelCatTreeResp, error) {
	treeInfoCat := make([]*pb.ChannelCatTreeResp_Cat, 0)
	catInfoList, err := dto.NewChannelCat().CatChannelTree(ctx.Request().Context(), project, catType)
	if err != nil {
		return nil, err
	}
	data := buildCategoryData(catInfoList)
	treeInfoCat = buildCategoryTree(0, 1, data)
	return &pb.ChannelCatTreeResp{Data: treeInfoCat}, nil
}

func buildCategoryData(list []*pb.ChannelCatItems) map[uint32]map[uint32]*pb.ChannelCatTreeResp_Cat {
	data := make(map[uint32]map[uint32]*pb.ChannelCatTreeResp_Cat)
	for _, cat := range list {
		var pid uint32
		switch pb.CatLevel(cat.Level) {
		case pb.CatLevel_PrimaryClassification:
			pid = 0
		case pb.CatLevel_SecondaryClassification:
			pid = cat.OneLevel
		case pb.CatLevel_ThreeClassification:
			pid = cat.SecondLevel
		}
		if _, ok := data[pid]; !ok {
			data[pid] = make(map[uint32]*pb.ChannelCatTreeResp_Cat)
		}
		data[pid][cat.CatId] = &pb.ChannelCatTreeResp_Cat{
			Id:       cat.CatId,
			Label:    cat.Category,
			Level:    cat.Level,
			Children: make([]*pb.ChannelCatTreeResp_Cat, 0),
		}
	}
	return data
}

func buildCategoryTree(parentId, level uint32, data map[uint32]map[uint32]*pb.ChannelCatTreeResp_Cat) []*pb.ChannelCatTreeResp_Cat {
	node := make([]*pb.ChannelCatTreeResp_Cat, 0)
	for id, item := range data[parentId] {
		item.Children = nil
		if data[id] != nil {
			level++
			item.Children = buildCategoryTree(id, level, data)
			level--
		}
		node = append(node, item)
	}
	sort.SliceStable(node, func(i, j int) bool {
		return node[i].Id < node[j].Id
	})
	return node
}

// CatChannelAdd 添加问题分类接口
func CatChannelAdd(ctx echo.Context, req *pb.ChannelCatAddReq) error {
	// 创建分类记录
	cat := &models.FpOpsChannelCategory{
		Project:     req.Project,
		CatType:     uint16(req.CatType),
		Category:    req.Category,
		Level:       req.GetCatLevel(),
		Operator:    ctx.Get(cst.AccountInfoCtx).(string),
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
		OneLevel:    req.OneLevel,
		SecondLevel: req.SecondLevel,
	}
	if err := dto.NewChannelCat().CatChannelAdd(ctx.Request().Context(), cat); err != nil {
		var dbErr *mysql.MySQLError
		if errors.As(err, &dbErr) && dbErr.Number == 1062 {
			return xerrors.New(lang.FormatText(ctx, "RepeatDataTip"), code.IdempotenceErr)
		}
		return xerrors.New(err.Error(), code.DbError)
	}
	return nil
}

// CatChannelSave 修改问题分类接口
func CatChannelSave(ctx echo.Context, req *pb.ChannelCatSaveReq) error {
	catInfo, err := dto.NewChannelCat().Detail(ctx, req.CatId)
	if err != nil {
		return err
	}
	if catInfo.Level != req.GetCatLevel() {
		return xerrors.New(code.StatusText(code.MissingParams), code.MissingParams)
	}

	cat := map[string]interface{}{
		"category":   req.Category,
		"operator":   ctx.Get(cst.AccountInfoCtx).(string),
		"updated_at": time.Now(),
	}
	if err := dto.NewChannelCat().CatChannelSave(ctx.Request().Context(), req.CatId, cat); err != nil {
		var dbErr *mysql.MySQLError
		if errors.As(err, &dbErr) && dbErr.Number == 1062 {
			return xerrors.New(lang.FormatText(ctx, "RepeatDataTip"), code.IdempotenceErr)
		}
		return xerrors.New(err.Error(), code.DbError)
	}
	return nil
}

// CatChannelDel 问题分类删除
func CatChannelDel(ctx echo.Context, catId uint32) error {
	if err := dto.NewChannelCat().CatChannelDel(ctx.Request().Context(), catId, ctx.Get(cst.AccountInfoCtx).(string)); err != nil {
		return err
	}
	return nil
}
