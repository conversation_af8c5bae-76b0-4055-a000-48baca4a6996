package configure

import (
	"encoding/json"
	"github.com/labstack/echo/v4"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	dto "ops-ticket-api/internal/persistence"
	"ops-ticket-api/proto/pb"
	"ops-ticket-api/utils"
	"time"
)

// UserAssignTicketList 分单配置列表
func UserAssignTicketList(ctx echo.Context, req *pb.UserAssignTicketListReq) (*pb.UserAssignTicketListResp, error) {
	resp, err := dto.NewUserAssignTicketRepo().UserAssignTicketList(ctx.Request().Context(), req)
	if err != nil {
		return nil, err
	}
	return resp, nil
}

// UserAssignTicketInfo 用户分单配置信息
func UserAssignTicketInfo(ctx echo.Context, req *pb.UserAssignTicketInfoReq) (*pb.UserAssignTicketListResp_Detail, error) {
	dest, err := dto.NewUserAssignTicketRepo().UserAssignTicketInfo(ctx.Request().Context(), req)
	if err != nil {
		return nil, err
	}
	resp := &pb.UserAssignTicketListResp_Detail{
		Id:         dest.ID,
		Account:    dest.Account,
		Game:       utils.StrToSlice(dest.Game),
		Operator:   dest.Operator,
		Lang:       utils.StrToSlice(dest.Lang),
		UpperLimit: dest.UpperLimit,
		UpdatedAt:  dest.UpdatedAt.Format(time.RFC3339),
	}
	err = json.Unmarshal([]byte(dest.GameCat), &resp.Detail)
	if err != nil {
		logger.Infof(ctx.Request().Context(), "UserAssignTicketInfo Unmarshal GameCat err: %v", err)
	}
	return resp, nil
}

// UserAssignTicketAdd 添加用户分单配置信息
func UserAssignTicketAdd(ctx echo.Context, req *pb.UserAssignTicketAddReq) error {
	return dto.NewUserAssignTicketRepo().UserAssignTicketAdd(ctx, req)
}

// UserAssignTicketEdit 修改用户分单配置信息
func UserAssignTicketEdit(ctx echo.Context, req *pb.UserAssignTicketEditReq) error {
	return dto.NewUserAssignTicketRepo().UserAssignTicketEdit(ctx, req)
}

// UserAssignTicketDel 删除用户分单配置信息
func UserAssignTicketDel(ctx echo.Context, req *pb.UserAssignTicketDelReq) error {
	return dto.NewUserAssignTicketRepo().UserAssignTicketDel(ctx.Request().Context(), req)
}
