package services

import (
	"errors"
	jsoniter "github.com/json-iterator/go"
	"github.com/labstack/echo/v4"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"gorm.io/gorm"
	"ops-ticket-api/internal/code"
	"ops-ticket-api/internal/cst"
	"ops-ticket-api/internal/framework/xerrors"
	"ops-ticket-api/internal/persistence"
	"ops-ticket-api/models"
	"ops-ticket-api/proto/pb"
	"ops-ticket-api/utils"
	"time"
)

func NewStrategySrv() *StrategyService {
	return &StrategyService{}
}

type StrategyService struct {
}

func (s *StrategyService) StrategySave(ctx echo.Context, req *pb.StrategyAddReq) error {
	strategyDetail := &models.FpOpsTicketsStrategy{}
	var enable = uint8(code.ModuleDisable)
	var isEdited bool
	if req.StrategyId > 0 {
		isEdited = true
		strategyInfo, err := persistence.NewStrategyDao().GetStrategyById(int64(req.StrategyId))
		if err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return xerrors.New("参数错误，该策略不存在", code.InvalidParams)
			}
			return xerrors.New(code.StatusText(code.DbError), code.DbError)
		}
		enable = strategyInfo.Enable
	}
	// 1.校验是否游戏已存在分级策略
	cnt, err := persistence.NewStrategyDao().CheckIfHasProject(req, isEdited)
	if err != nil {
		logger.Errorf(ctx.Request().Context(), "CheckIfHasProject err. err:%v", err)
		return err
	}
	if cnt > 0 {
		return xerrors.New("保存失败！该游戏已存在关联的分级策略", code.InvalidParams)
	}

	var serverFilter = &pb.StrategySplit{}
	if req.Type == 2 {
		serverFilter, err = utils.SplitStrategyFilter(req.FilterServerLists)
		if err != nil {
			return xerrors.New("保存失败！服务器填写格式有误", code.InvalidParams)
		}
	}
	castleLevelFilter, err := utils.SplitStrategyFilter(req.FilterCastleLevelLists)
	if err != nil {
		return xerrors.New("保存失败！城堡等级填写格式有误", code.InvalidParams)
	}
	payRangeFilter, err := utils.SplitStrategyFilter(req.FilterPayRangeLists)
	if err != nil {
		return xerrors.New("保存失败！付费区间填写格式有误", code.InvalidParams)
	}
	// 2.组装过滤器数据
	var _filter = &pb.StrategyFilters{
		Server:      serverFilter,
		CastleLevel: castleLevelFilter,
		PayRange:    payRangeFilter,
		IsSeverAll:  false,
	}
	if req.Type == 1 {
		_filter.IsSeverAll = true
	}
	filterJson, err := jsoniter.MarshalToString(_filter)
	if err != nil {
		logger.Errorf(ctx.Request().Context(), "jsoniter.MarshalToString err. err:%v", err)
		return err
	}
	// 3.保存数据库
	strategyDetail = &models.FpOpsTicketsStrategy{
		Project:      req.Project,
		StrategyName: req.StrategyName,
		Filters:      filterJson,
		Enable:       enable,
		Creator:      ctx.Get(cst.AccountInfoCtx).(string),
		CreatedAt:    uint64(time.Now().Unix()),
		UpdatedAt:    uint64(time.Now().Unix()),
	}
	if isEdited {
		strategyDetail.ID = req.StrategyId
	}
	return persistence.NewStrategyDao().SaveStrategyDetail(strategyDetail)
}

// StrategyDel 工单策略del
func (s *StrategyService) StrategyDel(ctx echo.Context, req *pb.StrategyDelReq) error {
	// check
	_, err := persistence.NewStrategyDao().GetStrategyById(req.StrategyId)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return xerrors.New("参数错误，该策略不存在", code.InvalidParams)
		}
		return xerrors.New(code.StatusText(code.DbError), code.DbError)
	}
	// del
	err = persistence.NewStrategyDao().DelStrategy(req.StrategyId)
	if err != nil {
		return err
	}
	return nil
}

// StrategyEnable 工单策略enable
func (s *StrategyService) StrategyEnable(ctx echo.Context, req *pb.StrategyEnabelReq) error {
	// check
	strategyInfo, err := persistence.NewStrategyDao().GetStrategyById(req.StrategyId)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return xerrors.New("参数错误，该策略不存在", code.InvalidParams)
		}
		return xerrors.New(code.StatusText(code.DbError), code.DbError)
	}
	if strategyInfo.Enable == uint8(req.Enable) {
		return xerrors.New("参数错误，该策略已处于该状态", code.InvalidParams)
	}
	// del
	err = persistence.NewStrategyDao().EnableStrategy(ctx, req)
	if err != nil {
		return err
	}
	return nil
}

// StrategyList 工单策略列表
func (s *StrategyService) StrategyList(ctx echo.Context, req *pb.StrategyListReq) (*pb.StrategyListResp, error) {
	list, total, err := persistence.NewStrategyDao().StrategyList(ctx, req, false)
	if err != nil {
		return nil, err
	}
	return &pb.StrategyListResp{
		CurrentPage: req.Page,
		PerPage:     req.PageSize,
		Total:       total,
		Data:        list,
	}, nil
}
