package examinesrv

import (
	"github.com/labstack/echo/v4"
	"ops-ticket-api/internal/cst"
	"time"

	"ops-ticket-api/internal/code"
	"ops-ticket-api/internal/framework/xerrors"
	dto "ops-ticket-api/internal/persistence"
	"ops-ticket-api/models"
	"ops-ticket-api/proto/pb"
)

func NewExamineTplSrv() *examineTplSrv {
	return &examineTplSrv{}
}

type examineTplSrv struct {
}

func (srv *examineTplSrv) TplSave(ctx echo.Context, req *pb.ExamineTplSaveReq) (config *models.FpExamineTplConfig, err error) {
	// 1. gen data
	detail, err := srv.genSaveTplDetails(ctx, req)
	if err != nil {
		return nil, err
	}
	// 2. to save data
	if err := dto.NewExamineSettings().TplSave(ctx, detail); err != nil {
		return nil, err
	}
	// 3. return data
	return detail, nil
}

func (srv *examineTplSrv) TplCopy(ctx echo.Context, req *pb.ExamineTplCopyReq) (*models.FpExamineTplConfig, error) {
	//  get curren detail
	curTplDetail, err := srv.TplDetail(ctx, req.FromTplId)
	if err != nil {
		return nil, err
	}
	var newTplSaveParam = &pb.ExamineTplSaveReq{
		TplDesc:      req.TplDesc,
		ModuleDetail: curTplDetail.ModuleDetail,
	}
	return srv.TplSave(ctx, newTplSaveParam)
}

func (srv *examineTplSrv) TplDetail(ctx echo.Context, tplId uint64) (*pb.ExamineTplDetailResp, error) {
	// get detail
	tplDt, err := dto.NewExamineSettings().GetExamineDetailByTplId(ctx, tplId)
	if err != nil {
		return nil, err
	}
	if tplDt == nil {
		return nil, xerrors.New(code.StatusText(code.NotFound), code.NotFound)
	}
	var resp = &pb.ExamineTplDetailResp{TplDesc: tplDt.TplDesc, ModuleDetail: make([]*pb.ExamineModuleDt, 0)}
	fields, err := tplDt.ConvertShowModuleDetails(ctx.Request().Context(), tplDt.ModuleDetails)
	if err != nil {
		return nil, err
	}
	resp.ModuleDetail = fields
	return resp, nil
}

func (srv *examineTplSrv) TplList(ctx echo.Context, req *pb.ExamineTplListReq) (*pb.ExamineTplListResp, error) {
	total, tpls, err := dto.NewExamineSettings().GetTplList(ctx, req.TplDesc, &req.Page, &req.PageSize)
	if err != nil {
		return nil, err
	}
	var resp = &pb.ExamineTplListResp{
		CurrentPage: req.Page,
		PerPage:     req.PageSize,
		Total:       total,
		Data:        make([]*pb.ExamineTplListResp_ExamineTpl, 0, len(tpls)),
	}
	for _, row := range tpls {
		resp.Data = append(resp.Data, &pb.ExamineTplListResp_ExamineTpl{
			Id:        row.ID,
			TplDesc:   row.TplDesc,
			Operator:  row.Operator,
			UpdatedAt: row.UpdatedAt.Format(time.DateTime),
			Enable:    row.Status == code.ModuleEnable,
		})
	}
	return resp, nil
}

func (srv *examineTplSrv) genSaveTplDetails(ctx echo.Context, req *pb.ExamineTplSaveReq) (*models.FpExamineTplConfig, error) {
	var (
		newTpl, oldTpl *models.FpExamineTplConfig
		now            = time.Now()
	)
	if req.Id > 0 { // edit check
		curTplDetail, err := dto.NewExamineSettings().GetExamineDetailByTplId(ctx, req.Id)
		if err != nil {
			return nil, err
		}
		if curTplDetail == nil {
			return nil, xerrors.New("tpl detail not found", code.DbDataUnexpect)
		}
		if curTplDetail.CanDelete == code.ModuleDisable { // check has been used
			return nil, xerrors.New(code.StatusText(code.ExamineTplForbidden), code.ExamineTplForbidden)
		}
		oldTpl = curTplDetail
	}
	// 1. gen tpl
	newTpl = &models.FpExamineTplConfig{
		ID:            0,
		TplDesc:       req.TplDesc,
		CanDelete:     code.ModuleEnable,
		Status:        int8(code.ModuleEnable),
		Operator:      ctx.Get(cst.AccountInfoCtx).(string),
		CreatedAt:     now,
		UpdatedAt:     now,
		ModuleDetails: make([]*models.FpExamineTplConfigDetail, 0),
	}

	fields, err := newTpl.GenModuleDetails(ctx.Request().Context(), req.ModuleDetail)
	if err != nil {
		return nil, err
	}
	newTpl.ModuleDetails = fields
	if oldTpl != nil {
		newTpl.ID = oldTpl.ID
		newTpl.CreatedAt = oldTpl.CreatedAt
		newTpl.CanDelete = oldTpl.CanDelete
		newTpl.Status = oldTpl.Status
	}

	return newTpl, nil
}
