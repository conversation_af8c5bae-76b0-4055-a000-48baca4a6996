package examinesrv

import (
	"encoding/json"
	"fmt"
	"github.com/labstack/echo/v4"
	"github.com/olivere/elastic/v7"
	"github.com/spf13/cast"
	"github.com/spf13/viper"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"golang.org/x/sync/errgroup"
	"ops-ticket-api/internal/code"
	"ops-ticket-api/internal/cst"
	"ops-ticket-api/internal/elasticsearch"
	"ops-ticket-api/internal/framework/lang"
	"ops-ticket-api/internal/framework/xerrors"
	"ops-ticket-api/internal/persistence"
	"ops-ticket-api/models"
	"ops-ticket-api/pkg/filter"
	"ops-ticket-api/proto/pb"
	"ops-ticket-api/services/communicate"
	"ops-ticket-api/utils"
	"reflect"
	"strings"
	"time"
)

func NewExamineOrderDscSrv() *examineDscOrderSrv {
	return &examineDscOrderSrv{}
}

type examineDscOrderSrv struct {
}

func (m *examineDscOrderSrv) DscOrderList(ctx echo.Context, param *pb.ExamineDscOrderListReq) (*pb.ExamineDscOrderListResp, error) {
	fun := "examineDscOrderSrv.DscOrderList ->"
	opts, err := m.optionsExamineDscFilterEs(ctx, param)
	var esFields = []string{"project"}
	var sort []elastic.Sorter = []elastic.Sorter{elastic.NewFieldSort("examine_dsc_id").Desc()}
	list, total, err := elasticsearch.DefaultExamineDscEsSvc.GetExamineDscPool(ctx.Request().Context(), int(param.Page), int(param.PageSize), esFields, sort, opts...)
	if err != nil {
		logger.Error(ctx.Request().Context(), "get examine dsc data from elasticsearch err", logger.Any("err", err), logger.String("fun", fun))
		return nil, err
	}

	var res = &pb.ExamineDscOrderListResp{
		CurrentPage: param.Page,
		PerPage:     param.PageSize,
		Total:       total,
		Data:        make([]*pb.ExamineDscOrderListResp_ExamineDscOrder, 0, len(list)),
	}

	for _, row := range list {
		examineInfo := &models.ExamineDscOrderDoc{}
		if err := json.Unmarshal(row.Source, &examineInfo); err != nil {
			logger.Error(ctx.Request().Context(), "ExamineDsc Unmarshal err", logger.String("err", err.Error()), logger.String("fun", fun))
			return nil, err
		}
		res.Data = append(res.Data, &pb.ExamineDscOrderListResp_ExamineDscOrder{
			DscExamineId:      examineInfo.ExamineDscID,
			TaskId:            examineInfo.TaskID,
			TplId:             examineInfo.TplID,
			Project:           examineInfo.Project,
			DscUserId:         examineInfo.DscUserID,
			UserName:          examineInfo.DscUserName,
			DmChannel:         examineInfo.ChannelID,
			TotalPay:          utils.PayAmountToFloat64(examineInfo.GenPayAll),
			PayLastThirtyDays: utils.PayAmountToFloat64(examineInfo.GenPayLastThirtyDays),
			LastLogin:         utils.TimeFormat(examineInfo.GenLastLogin),
			RepliedStatus:     uint32(examineInfo.GenReplyType),
			VipState:          uint32(examineInfo.GenVipState),
			Fpid:              examineInfo.GenAccountID,
			Uid:               uint64(examineInfo.GenUID),
			Sid:               examineInfo.GenSid,
			BotId:             examineInfo.BotID,
			BotShow:           "",
			Status:            pb.ExamineStateDf(examineInfo.Status),
			FinalResult:       pb.ExamineFinalResultDf(examineInfo.FinalResult),
			CreatedAt:         utils.TimeFormat(int64(examineInfo.CreatedAt)),
			FinishedAt:        utils.TimeFormat(int64(examineInfo.FinishedAt)),
			Inspector:         examineInfo.Inspector,
		})
	}
	return res, nil
}

func (m *examineDscOrderSrv) optionsExamineDscFilterEs(ctx echo.Context, req *pb.ExamineDscOrderListReq) ([]elasticsearch.Option, error) {
	option, err := filter.NewAdHocWrapper().ExamineDscPool(ctx.Request().Context(), req)
	if err != nil {
		return nil, err
	}
	return option, nil
}

// DscOrderListExport 质检 discord导出
func (m *examineDscOrderSrv) DscOrderListExport(ctx echo.Context, req *pb.ExamineDscOrderListReq) (string, error) {
	//fun := "examineDscOrderSrv.DscOrderListExport ->"
	opts, err := m.optionsExamineDscFilterEs(ctx, req)
	if err != nil {
		return "", err
	}

	records, err := elasticsearch.DefaultExamineDscEsSvc.GetExamineDscPoolScroll(ctx.Request().Context(), opts...)
	if err != nil && err.Error() != "EOF" {
		return "", err
	}

	var dest = make([][]interface{}, 0)
	for user := range records {
		fields := make([]string, 0)
		for _, v := range user.ExamineFields {
			fields = append(fields, fmt.Sprintf("%s:%s", v.FieldName, utils.ToJson(v.FieldValue)))
		}
		detail := []interface{}{
			utils.TimeFormat(int64(user.CreatedAt)), utils.TimeFormat(int64(user.FinishedAt)), user.Project, user.ExamineDscID, lang.FormatText(ctx, pb.ExamineStateDf(user.Status).String()),
			user.Inspector, utils.ToJson(user.RelatedAccount), user.DscUserName, user.DscUserID, user.GenSid, utils.PayAmountToFloat64(user.GenPayAll), "",
			lang.FormatText(ctx, pb.ExamineFinalResultDf(user.FinalResult).String()), user.FinalScore, user.FinalReason, strings.Join(fields, "\n")}
		dest = append(dest, detail)
	}
	_fileName := fmt.Sprintf("examin_dsc_export_%d.csv", time.Now().UnixNano())
	err = utils.CreateCsvFile(_fileName, viper.GetStringSlice("export_header.ExamineDscRecordExport.zh-cn"), dest)
	if err != nil {
		return "", err
	}
	return _fileName, err
}

func (m *examineDscOrderSrv) OrderDscState(ctx echo.Context, param *pb.ExamineDscOrderStatsReq) (*pb.ExamineDscOrderStatsResp, error) {
	var (
		wg      = errgroup.Group{}
		resp    = &pb.ExamineDscOrderStatsResp{}
		account = ctx.Get(cst.AccountNameCtx).(string)
	)
	// total
	wg.Go(func() error { // 1. 全部任务：“质检任务类型=DC质检”的全部质检任务数据
		_iReq := &pb.ExamineDscOrderListReq{Project: param.Project}
		total, err := m.orderDscStatCount(ctx, _iReq)
		resp.ExamineDscCount = total
		return err
	})
	wg.Go(func() error { // 2. 待处理质检任务：“质检任务类型=DC质检”且“质检状态=处理中”的全部质检任务数据
		_iReq := &pb.ExamineDscOrderListReq{Project: param.Project, Status: []pb.ExamineStateDf{pb.ExamineStateDf_ExamineStateDfInit, pb.ExamineStateDf_ExamineStateDfDoing}}
		examineDscWaitCount, err := m.orderDscStatCount(ctx, _iReq)
		resp.ExamineDscWaitCount = examineDscWaitCount
		return err
	})
	wg.Go(func() error { // 3. 我的待处理质检任务：“质检任务类型=DC质检”且“质检状态=处理中”且“质检人=当前登陆人”的全部质检任务数据
		_iReq := &pb.ExamineDscOrderListReq{Project: param.Project, Inspector: []string{account}, Status: []pb.ExamineStateDf{pb.ExamineStateDf_ExamineStateDfInit, pb.ExamineStateDf_ExamineStateDfDoing}}
		examineDscMineWaitCount, err := m.orderDscStatCount(ctx, _iReq)
		resp.ExamineDscMineWaitCount = examineDscMineWaitCount
		return err
	})
	wg.Go(func() error { // 4. 我的已完成质检任务：“质检任务类型=DC质检”且“质检状态=已完成”且“质检人=当前登陆人”的全部质检任务数据
		_iReq := &pb.ExamineDscOrderListReq{Project: param.Project, Inspector: []string{account}, Status: []pb.ExamineStateDf{pb.ExamineStateDf_ExamineStateDfSuccess}}
		examineDscMineDoneCount, err := m.orderDscStatCount(ctx, _iReq)
		resp.ExamineDscMineDoneCount = examineDscMineDoneCount
		return err
	})
	wg.Go(func() error { // 5. 质检不合格任务：“质检任务类型=DC质检”且“质检结果=不通过”的全部质检任务数据
		_iReq := &pb.ExamineDscOrderListReq{Project: param.Project, FinalResult: []pb.ExamineFinalResultDf{pb.ExamineFinalResultDf_ExamineFinalResultDfFail}}
		examineDscUnqualifiedCount, err := m.orderDscStatCount(ctx, _iReq)
		resp.ExamineDscUnqualifiedCount = examineDscUnqualifiedCount
		return err
	})
	wg.Go(func() error { // 5. 质检不合格任务：“质检任务类型=DC质检”且“质检结果=不通过”的全部质检任务数据
		_iReq := &pb.ExamineDscOrderListReq{Project: param.Project, Inspector: []string{account}, FinalResult: []pb.ExamineFinalResultDf{pb.ExamineFinalResultDf_ExamineFinalResultDfFail}}
		examineDscMineUnqualifiedCount, err := m.orderDscStatCount(ctx, _iReq)
		resp.ExamineDscMineUnqualifiedCount = examineDscMineUnqualifiedCount
		return err
	})

	if err := wg.Wait(); err != nil {
		return resp, err
	}
	return resp, nil
}

func (m *examineDscOrderSrv) orderDscStatCount(ctx echo.Context, param *pb.ExamineDscOrderListReq) (int64, error) {
	opts, err := m.optionsExamineDscFilterEs(ctx, param)
	if err != nil {
		return 0, err
	}
	return elasticsearch.DefaultExamineDscEsSvc.GetExamineDscCount(ctx.Request().Context(), opts...)
}
func (m *examineDscOrderSrv) OrderDetail(ctx echo.Context, param *pb.ExamineDscOrderDetailReq) (*pb.ExamineDscOrderDetailDf, error) {
	var (
		fun = "examineDscOrderSrv.OrderDetail ->"
	)
	detail, err := persistence.NewExamineOrder().DscOrderDetail(ctx.Request().Context(), param.GetDscExamineId())
	if err != nil {
		return nil, err
	}
	if detail == nil {
		return nil, fmt.Errorf("%s examine detail get nil id:%d", fun, param.DscExamineId)
	}

	res, err := m.convertOrderDetail(ctx, detail)
	if err != nil {
		return nil, err
	}
	{
		account := ctx.Get(cst.AccountNameCtx).(string)
		nCtx := utils.ContextOnlyValue{ctx.Request().Context()}
		go persistence.NewExamineOrder().SetOrderNoticeRead(nCtx, pb.ExamineTaskGroupDf_ExamineTaskGroupDfDiscord, detail.ID, account)
	}
	return res, nil
}

func (m *examineDscOrderSrv) convertOrderDetail(ctx echo.Context, detail *models.FpExamineDiscordDetail) (*pb.ExamineDscOrderDetailDf, error) {
	_finishedAt := detail.FinishedAt.Format(time.DateTime)
	if detail.FinishedAt.IsZero() {
		_finishedAt = "-"
	}
	res := &pb.ExamineDscOrderDetailDf{
		DscExamineId: detail.ID,
		TaskId:       detail.TaskID,
		TplId:        detail.TplID,
		Project:      detail.Project,
		DscUserId:    detail.DscUserID,
		DscBotId:     detail.DscUserID,
		DscChannelId: detail.ChannelID,
		Inspector:    detail.Inspector,
		Status:       pb.ExamineStateDf(detail.Status),
		CreatedAt:    detail.CreatedAt.Format(time.DateTime),
		FinishedAt:   _finishedAt,
		DefineField:  make(map[string]any, len(detail.FpExamineFields)),
		CommonField: &pb.ExamineDscOrderDetailResp_Common{
			FinalResult:              pb.ExamineFinalResultDf(detail.FinalResult),
			FinalScore:               int32(detail.FinalScore),
			FinalReason:              detail.FinalReason,
			RelatedAccount:           detail.DecodeRelatedAccount(ctx.Request().Context(), detail.RelatedAccount),
			NoticeAccount:            detail.DecodeNoticeAccount(ctx.Request().Context(), detail.NoticeAccount),
			FinalDesc:                detail.FinalDesc,
			FinalResultModifyComment: detail.FinalResultModifyComment,
		},
	}
	for _, fl := range detail.FpExamineFields {
		switch pb.ExamineFieldTpDf(fl.FieldType) {
		case pb.ExamineFieldTpDf_ExamineFieldTpText, pb.ExamineFieldTpDf_ExamineFieldTpSel, pb.ExamineFieldTpDf_ExamineFieldTpTextarea:
			res.DefineField[fl.FieldShow] = fl.FieldVal
		default:
			var fv []string
			err := json.Unmarshal([]byte(fl.FieldVal), &fv)
			if err != nil {
				return nil, err
			}
			res.DefineField[fl.FieldShow] = fv
		}
	}
	// can edit
	can, err := persistence.NewExamineOrder().DscOrderCanEdit(ctx.Request().Context(), res.DscExamineId)
	if err != nil {
		return nil, err
	}
	res.CanEdited = can
	// return
	return res, nil
}

func (m *examineDscOrderSrv) OrderSave(ctx echo.Context, param *pb.ExamineDscOrderSaveDf) error {
	detail, err := m._genSaveOrder(ctx, param)
	if err != nil {
		return err
	}
	err = persistence.NewExamineOrder().SaveDscTaskOrder(ctx, detail)
	if err != nil {
		return err
	}
	// gen notice
	if notices := utils.JsonToStrSlice(detail.NoticeAccount); len(notices) > 0 {
		operator := ctx.Get(cst.AccountNameCtx).(string)
		nCtx := utils.ContextOnlyValue{ctx.Request().Context()}
		go persistence.NewExamineOrder().SaveNoticeInfo(nCtx, pb.ExamineNoticeMsgGroupDf_ExamineNoticeMsgGroupDfResult, pb.ExamineTaskGroupDf_ExamineTaskGroupDfDiscord, detail.ID, detail.TaskID, detail.TplID, notices, operator)
		communicate.PubExamineOrderFinished(nCtx, detail.ID, pb.ExamineTaskGroupDf_ExamineTaskGroupDfDiscord, detail.TaskID)
		//go examinecore.ExamineOrderSyncTaskState(nCtx, detail.TaskID, pb.ExamineTaskGroupDf_ExamineTaskGroupDfDiscord, detail.ID)
	}
	if err := elasticsearch.DefaultExamineDscEsSvc.SaveDscOrderExamine(ctx.Request().Context(), detail); err != nil {
		logger.Errorf(ctx.Request().Context(), "SaveDscOrderExamine to Es return err. err:%v. order:%+v", err, detail)
	}
	return nil
}
func (m *examineDscOrderSrv) _genSaveOrder(ctx echo.Context, param *pb.ExamineDscOrderSaveDf) (*models.FpExamineDiscordDetail, error) {
	var (
		err               error
		now               = time.Now()
		fun               = "examineDscOrderSrv._genSaveOrder -> "
		examineDscDetail  *models.FpExamineDiscordDetail
		account           = ctx.Get(cst.AccountInfoCtx).(string)
		_genExamineFieldF = func(key string, val any, field *models.FpExamineTplConfigDetail) (*models.FpExamineFields, error) {
			var dt = &models.FpExamineFields{
				ExamineType: pb.ExamineTaskGroupDf_ExamineTaskGroupDfDiscord.String(),
				FieldShow:   key,
				FieldSort:   uint(field.FieldSort),
				FieldType:   field.FieldType,
				FieldVal:    "",
			}
			vv := reflect.ValueOf(val)
			switch vv.Kind() {
			case reflect.String:
				if !utils.InArrayAny(field.FieldType, []int{int(pb.ExamineFieldTpDf_ExamineFieldTpSel), int(pb.ExamineFieldTpDf_ExamineFieldTpText), int(pb.ExamineFieldTpDf_ExamineFieldTpTextarea)}) {
					return nil, fmt.Errorf("key:%s. val:%+v. string data type not match", key, val)
				}
				dt.FieldVal = vv.String()
			case reflect.Slice:
				if !utils.InArrayAny(field.FieldType, []int{int(pb.ExamineFieldTpDf_ExamineFieldTpMulSel)}) {
					return nil, fmt.Errorf("key:%s. val:%+v slice data type not match", key, val)
				}
				var em []string
				for i := 0; i < vv.Len(); i++ {
					if vv.Index(i).Kind() != reflect.Interface {
						return nil, fmt.Errorf("key:%s. val:%+v. slice[%d].Kind neq String. data type not match. %+v", key, val, i, vv.Index(i).Kind())
					}
					em = append(em, cast.ToString(vv.Index(i).Interface()))
				}
				if len(em) == 0 {
					return nil, fmt.Errorf("key:%s. val:%+v. len eq0. ", key, val)
				}
				dt.FieldVal = utils.ToJson(em)
			default:
				return nil, fmt.Errorf("key:%s. val:%+v. reflect Kind unknown. %+v", key, val, vv.Kind())
			}
			return dt, nil
		}
	)
	examineDscDetail, err = persistence.NewExamineOrder().DscOrderDetail(ctx.Request().Context(), param.DscExamineId)
	if err != nil {
		return nil, err
	}
	tplDetail, err := persistence.NewExamineSettings().GetExamineDetailByTplId(ctx, examineDscDetail.TplID)
	if err != nil {
		return nil, err
	}
	if tplDetail == nil || len(tplDetail.ModuleDetails) == 0 {
		return nil, fmt.Errorf("tpl config detail return len eq0")
	}
	// check
	if account != examineDscDetail.Inspector {
		return nil, xerrors.New(code.StatusText(code.ExamineOrderInspectorNoMatch), code.ExamineOrderInspectorNoMatch)
	}
	// can edit
	can, err := persistence.NewExamineOrder().DscOrderCanEdit(ctx.Request().Context(), examineDscDetail.ID)
	if err != nil {
		return nil, err
	}
	if !can {
		return nil, xerrors.New(code.StatusText(code.ExamineOrderModifiedOnlyOnce), code.ExamineOrderModifiedOnlyOnce)
	}

	examineDscDetail.Status = int(pb.ExamineStateDf_ExamineStateDfSuccess)
	examineDscDetail.RelatedAccount = utils.ToJson(param.CommonField.RelatedAccount)
	examineDscDetail.NoticeAccount = utils.ToJson(param.CommonField.NoticeAccount)
	examineDscDetail.FinalResult = int(param.CommonField.FinalResult)
	examineDscDetail.FinalScore = int(param.CommonField.FinalScore)
	examineDscDetail.FinalReason = param.CommonField.FinalReason
	examineDscDetail.FinalDesc = param.CommonField.FinalDesc
	examineDscDetail.FinalResultModifyComment = param.CommonField.FinalResultModifyComment
	examineDscDetail.FinishedAt = now
	examineDscDetail.FinalModifiedAt = now
	examineDscDetail.UpdatedAt = now

	examineDscDetail.FpExamineFields = make([]*models.FpExamineFields, 0)
	for key, val := range param.DefineField {
		var field *models.FpExamineTplConfigDetail
		for _, fl := range tplDetail.ModuleDetails {
			if fl.FieldType > 0 && fl.FieldShow == key {
				field = fl
			}
		}
		if field == nil {
			return nil, fmt.Errorf("%s get tpl detail return nil. key:%s. val:%s", fun, key, val)
		}

		dt, err := _genExamineFieldF(key, val, field)
		if err != nil {
			return nil, fmt.Errorf("%s err:%+v", fun, err)
		}
		dt.TaskID = examineDscDetail.TaskID
		dt.Project = examineDscDetail.Project
		dt.TplID = examineDscDetail.TplID
		dt.DetailID = int64(examineDscDetail.ID)
		dt.CreatedAt = now
		dt.UpdatedAt = now
		examineDscDetail.FpExamineFields = append(examineDscDetail.FpExamineFields, dt)
	}
	return examineDscDetail, nil
}
