package examinesrv

import (
	"fmt"
	"github.com/labstack/echo/v4"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"ops-ticket-api/internal/cst"
	"ops-ticket-api/internal/elasticsearch"
	dto "ops-ticket-api/internal/persistence"
	"ops-ticket-api/models"
	"ops-ticket-api/proto/pb"
	"ops-ticket-api/services/communicate"
	"ops-ticket-api/services/examinesrv/examinecore"
	"ops-ticket-api/utils"
	"time"
)

func NewExamineTaskSrv() *examineTaskSrv {
	return &examineTaskSrv{}
}

type examineTaskSrv struct {
}

func (srv *examineTaskSrv) TaskSave(ctx echo.Context, param *pb.ExamineTaskSaveReq) (*models.FpExamineTask, error) {
	task, err := srv.genSaveTask(ctx, param)
	if err != nil {
		return nil, err
	}
	if err := dto.NewExamineSettings().TaskSave(ctx, task); err != nil {
		return nil, err
	}

	<-time.NewTicker(1 * time.Second).C
	cc := utils.ContextOnlyValue{ctx.Request().Context()}
	newC := ctx.Echo().AcquireContext()
	newC.SetRequest(ctx.Request().WithContext(cc))
	newC.Set(cst.AccountInfoCtx, pb.UserRole_SystemRole.String())
	communicate.PubExamineTaskCreate(newC, task.ID)

	return task, nil
}
func (srv *examineTaskSrv) genSaveTask(ctx echo.Context, param *pb.ExamineTaskSaveReq) (*models.FpExamineTask, error) {
	var (
		fun = "examineTaskSrv.genSaveTask -->"
		now = time.Now()
	)
	var task = &models.FpExamineTask{
		TaskName:   param.TaskName,
		Project:    param.Project,
		TplID:      param.TplId,
		TaskGroup:  param.TaskGroup.String(),
		Filters:    "{}",
		TaskStatus: int(pb.ExamineTaskStateDf_ExamineTaskStateDfInit),
		TaskResult: "{}",
		Operator:   ctx.Get(cst.AccountInfoCtx).(string),
		CreatedAt:  now,
		FinishedAt: time.Time{},
		UpdatedAt:  now,
		Link:       param.Link,
	}
	// CN 只支持 工单类 质检任务
	switch param.TaskGroup {
	case pb.ExamineTaskGroupDf_ExamineTaskGroupDfDiscord:
		if elasticsearch.DefaultExamineDscEsSvc == nil {
			return nil, fmt.Errorf("examine dsc es service not init. currentTaskGroup:%s", param.TaskGroup.String())
		}
	}

	filter, err := examinecore.GetExamineImpl(param.TaskGroup).GenExamineTaskFilter(ctx, param)
	if err != nil {
		logger.Errorf(ctx.Request().Context(), "%s GenExamineTaskFilter err:%v. param:%+v", fun, err, param)
		return nil, err
	}
	task.Filters = utils.ToJson(filter)
	return task, nil
}

func (srv *examineTaskSrv) ExamineTaskDscFilterCount(ctx echo.Context, param *pb.ExamineTaskDscFilterCountReq) (*pb.ExamineTaskDscFilterCountResp, error) {
	res, err := examinecore.NewDscFilterCount().TaskDscFilterCount(ctx, param)
	if err != nil {
		return nil, err
	}
	return res, nil
}
func (srv *examineTaskSrv) TaskDetail(ctx echo.Context, param *pb.ExamineTaskDetailReq) (*pb.ExamineTaskDetailResp, error) {
	task, filter, err := dto.NewExamineSettings().GetTaskDetail(ctx, param.Id)
	if err != nil {
		return nil, err
	}
	var res = &pb.ExamineTaskDetailResp{
		Id:                      task.ID,
		TaskName:                task.TaskName,
		Project:                 task.Project,
		TplId:                   task.TplID,
		TaskGroup:               pb.ExamineTaskGroupDf(pb.ExamineTaskGroupDf_value[task.TaskGroup]),
		FilterDscRepliedAt:      make([]string, 0),
		FilterDscAllTotalPayGte: 0,
		FilterDscOtherAll:       false,
		//FilterDscOtherLabel:      make([]string, 0),
		FilterDscOtherVipState:   make([]uint32, 0),
		FilterDscOtherMaintainer: make([]string, 0),
		FilterDscOtherNum:        0,
		FilterDscAssignAccount:   make([]string, 0),
	}
	switch res.TaskGroup {
	case pb.ExamineTaskGroupDf_ExamineTaskGroupDfDiscord:
		res.FilterDscRepliedAt = filter.DscFilter.DscRepliedAt
		res.FilterDscAllTotalPayGte = filter.DscFilter.DscAllTotalPayGte
		res.FilterDscOtherAll = filter.DscFilter.DscOtherAll
		res.FilterDscOtherNum = int32(filter.DscFilter.DscOtherNum)
		res.FilterDscAssignAccount = filter.DscFilter.DscAssignAccount
		if res.FilterDscOtherAll == true {
			//if filter.DscFilter.DscOtherTag != nil {
			//	res.FilterDscOtherLabel = filter.DscFilter.DscOtherTag
			//}
			if filter.DscFilter.DscOtherVipState != nil {
				res.FilterDscOtherVipState = filter.DscFilter.DscOtherVipState
			}
			if filter.DscFilter.DscOtherMaintainer != nil {
				res.FilterDscOtherMaintainer = filter.DscFilter.DscOtherMaintainer
			}
		}
	}
	return res, nil
}

func (srv *examineTaskSrv) TaskList(ctx echo.Context, param *pb.ExamineTaskListReq) (*pb.ExamineTaskListResp, error) {
	var res = &pb.ExamineTaskListResp{}
	total, list, err := dto.NewExamineSettings().GetTaskList(ctx, param.TaskName, &param.Page, &param.PageSize)
	if err != nil {
		return nil, err
	}
	res.Total = total
	res.CurrentPage = param.Page
	res.PerPage = param.PageSize
	for _, tk := range list {
		res.Data = append(res.Data, &pb.ExamineTaskListResp_ExamineTask{
			Id:        tk.ID,
			TaskName:  tk.TaskName,
			Status:    pb.ExamineTaskStateDf(tk.TaskStatus),
			Operator:  tk.Operator,
			UpdatedAt: tk.UpdatedAt.Format(time.DateTime),
			Link:      tk.Link,
		})
	}

	return res, nil
}
func (srv *examineTaskSrv) ExamineTaskDscPartFilterCount(ctx echo.Context, param *pb.ExamineTaskDscPartFilterCountReq) (*pb.ExamineTaskDscFilterCountResp, error) {
	res, err := examinecore.NewDscFilterCount().TaskDscPartFilterCount(ctx, param)
	if err != nil {
		return nil, err
	}
	return res, nil
}
