package examinesrv

import (
	"github.com/labstack/echo/v4"
	"github.com/spf13/cast"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"ops-ticket-api/internal/cst"
	"ops-ticket-api/internal/framework/lang"
	"ops-ticket-api/internal/persistence"
	"ops-ticket-api/proto/pb"
	"time"
)

type examineOrderNoticeSrv struct {
}

func NewExamineOrderNoticeSrv() *examineOrderNoticeSrv {
	return &examineOrderNoticeSrv{}
}

func (srv *examineOrderNoticeSrv) OrderNoticeAcceptor(ctx echo.Context, account string) (*pb.ExamineOrderNoticeAcceptorResp, error) {
	boo, err := persistence.NewExamineOrder().HasUnreadNotice(ctx, account)
	if err != nil {
		return nil, err
	}
	return &pb.ExamineOrderNoticeAcceptorResp{NewNotice: boo}, nil
}

func (srv *examineOrderNoticeSrv) OrderNoticeList(ctx echo.Context, param *pb.ExamineOrderNoticeListReq) (*pb.ExamineOrderNoticeListResp, error) {
	account := cast.ToString(ctx.Get(cst.AccountNameCtx))
	total, notice, err := persistence.NewExamineOrder().GetOrderNoticeList(ctx, account, &param.Page, &param.PageSize)
	if err != nil {
		return nil, err
	}
	var res = &pb.ExamineOrderNoticeListResp{
		CurrentPage: param.Page,
		PerPage:     param.PageSize,
		Total:       total,
		Data:        make([]*pb.ExamineOrderNoticeListResp_ExamineNoticeDetail, 0, len(notice)),
	}
	var cleanReadStateIds []uint64
	for _, row := range notice {
		var dt = &pb.ExamineOrderNoticeListResp_ExamineNoticeDetail{
			Id:        row.ID,
			MsgGroup:  lang.FormatText(ctx, row.MsgGroup),
			TaskGroup: pb.ExamineTaskGroupDf(pb.ExamineTaskGroupDf_value[row.ExamineType]),
			DetailId:  uint64(row.DetailID),
			ToUser:    row.ToUser,
			CreatedAt: row.CreatedAt.Format(time.DateTime),
			HasRead:   true,
		}
		if row.NoticeStatus == int(pb.ExamineNoticeStateDf_ExamineNoticeStateDfUnread) {
			dt.HasRead = false
			cleanReadStateIds = append(cleanReadStateIds, row.ID)
		}
		res.Data = append(res.Data, dt)
	}
	logger.Infof(ctx.Request().Context(), "%s. cleanReadStateIds:%+v", "OrderNoticeList", cleanReadStateIds)
	return res, nil
}
