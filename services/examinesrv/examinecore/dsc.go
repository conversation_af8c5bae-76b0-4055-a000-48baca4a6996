package examinecore

import (
	"context"
	"errors"
	"fmt"
	"github.com/avast/retry-go"
	"github.com/labstack/echo/v4"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"io"
	"ops-ticket-api/internal/elasticsearch"
	"ops-ticket-api/internal/persistence"
	"ops-ticket-api/models"
	filter2 "ops-ticket-api/pkg/filter"
	"ops-ticket-api/proto/pb"
	"ops-ticket-api/utils"
	"time"
)

type examineDscFilterCount interface {
	TaskDscFilterCount(ctx echo.Context, param *pb.ExamineTaskDscFilterCountReq) (*pb.ExamineTaskDscFilterCountResp, error)
	TaskDscPartFilterCount(ctx echo.Context, param *pb.ExamineTaskDscPartFilterCountReq) (*pb.ExamineTaskDscFilterCountResp, error) // 新增抽检单数量查询
}

type examineDscImpl struct {
}

func NewDscFilterCount() examineDscFilterCount {
	return examineDscImpl{}

}

// TaskExamineFinished 检测当前任务下所有质检单是否已全部质检
func (dsc examineDscImpl) TaskExamineFinished(ctx context.Context, taskId uint64) (bool, error) {
	boo, err := persistence.NewExamineOrder().DscTaskOrderAllFinished(ctx, taskId)
	if err != nil {
		logger.Errorf(ctx, "examineDscImpl.TaskExamineFinished -->  DscTaskOrderAllFinished return err. err:%v. taskId:%d", err, taskId)
	}
	return boo, err
}

// GenExamineTaskFilter 生成批任务 filter 信息
func (dsc examineDscImpl) GenExamineTaskFilter(ctx echo.Context, param *pb.ExamineTaskSaveReq) (*models.ExamineTaskFilterDf, error) {
	var (
		dscFilter = &models.ExamineTaskFilterDscDf{
			DscRepliedAt:      nil,
			DscAllTotalPayGte: param.FilterDscAllTotalPayGte,
			DscOtherAll:       param.FilterDscOtherAll,
			//DscOtherTag:        param.FilterDscOtherTag,
			DscOtherMaintainer: param.FilterDscOtherMaintainer,
			DscOtherVipState:   param.FilterDscOtherVipState,
			DscOtherNum:        int(param.FilterDscOtherNum),
			DscAssignAccount:   utils.UniqueValue(param.FilterDscAssignAccount),
			DscOtherRules:      param.TaskRule,
		}
	)
	repliedAt, err := relyTimeF(param.FilterDscRepliedAt)
	if err != nil {
		return nil, err
	}
	dscFilter.DscRepliedAt = repliedAt

	if dscFilter.DscOtherAll == false && dscFilter.DscOtherNum < 1 {
		return nil, fmt.Errorf("dsc_other_num lt 1")
	}
	if len(dscFilter.DscAssignAccount) < 1 {
		return nil, fmt.Errorf("dsc_assign_account is empty")
	}

	if dscFilter.DscOtherAll == false {
		if (len(dscFilter.DscOtherTag) == 0 && len(dscFilter.DscOtherVipState) == 0 && len(dscFilter.DscOtherMaintainer) == 0) ||
			dscFilter.DscOtherNum < 1 || dscFilter.DscOtherRules == pb.ExamineTaskRulesDf_ExamineTaskRulesDfUnknown {
			return nil, fmt.Errorf("dsc_other_all is false, but dsc_other_label or dsc_other_vip_state or dsc_other_num or dsc_other_rules is empty")
		}

	}
	return &models.ExamineTaskFilterDf{
		Project:   param.Project,
		TaskGroup: param.TaskGroup,
		DscFilter: dscFilter,
	}, nil
}

// GenExamineTaskLogs 生成批任务详情
func (dsc examineDscImpl) GenExamineTaskLogs(ctx echo.Context, task *models.FpExamineTask, filter *models.ExamineTaskFilterDf) (*ExamineTaskResult, error) {
	var (
		fun = "examineDscImpl.GenExamineTaskLogs -->"
		res ExamineTaskResult
	)
	// get user list
	examineOrders, err := dsc.getUsersByFilter(ctx, task, filter)
	if err != nil {
		logger.Errorf(ctx.Request().Context(), "%s getUsersByFilter err:%v. task:%+v filter:%+v", fun, err, task, filter)
		res.Error = err
		return &res, err
	}
	// save user list
	res.TotalCount = int64(len(examineOrders))
	for _, order := range examineOrders {
		err := retry.Do(func() error { return dsc.genExamineOrder(ctx, order) },
			retry.Attempts(3), retry.Delay(1000*time.Millisecond), retry.LastErrorOnly(true))
		if err != nil {
			logger.Errorf(ctx.Request().Context(), "%s genExamineOrder err:%v. order:%+v", fun, err, order)
			res.FailedCount = res.FailedCount + 1
		} else {
			res.SuccessCount = res.SuccessCount + 1
		}
	}
	return &res, nil
}

func (dsc examineDscImpl) genExamineOrder(ctx echo.Context, detail *models.FpExamineDiscordDetail) error {
	// 1. check order has exist
	boo, err := persistence.NewExamineOrder().DscTaskOrderExists(ctx.Request().Context(), detail.TaskID, detail.BotID, detail.DscUserID)
	if err != nil {
		return err
	}
	if boo == true {
		return nil
	}
	// 2. save order
	err = persistence.NewExamineOrder().CreateDscTaskOrder(ctx.Request().Context(), detail)
	return err
}

func (dsc examineDscImpl) getUsersByFilter(ctx echo.Context, task *models.FpExamineTask, filter *models.ExamineTaskFilterDf) ([]*models.FpExamineDiscordDetail, error) {
	// 1. filter all
	var (
		fun           = "examineDscImpl.getUsersByFilter ->"
		has           = map[string]struct{}{}
		users         = make([]*models.FpDscUserDoc, 0, 1024)
		otherUsers    = make([]*models.FpDscUserDoc, 0, 1024)
		innerCountReq = &pb.ExamineTaskDscFilterCountReq{
			Project:   filter.Project,
			RepliedAt: filter.DscFilter.DscRepliedAt,
			TotalPay:  filter.DscFilter.DscAllTotalPayGte,
		}
		innerPartCountReq = &pb.ExamineTaskDscPartFilterCountReq{
			Project:    filter.Project,
			RepliedAt:  filter.DscFilter.DscRepliedAt,
			TotalPay:   filter.DscFilter.DscAllTotalPayGte,
			Maintainer: filter.DscFilter.DscOtherMaintainer,
			VipState:   filter.DscFilter.DscOtherVipState,
		}
		now = time.Now()
	)
	{ // 1. 全量用户过滤
		opts, err := filter2.NewAdHocWrapper().ExamineDscFilterCount(ctx.Request().Context(), innerCountReq)
		if err != nil {
			return nil, err
		}
		allPayUsers, err := elasticsearch.DefaultDscEsSvc.GetDiscordPoolScroll(ctx.Request().Context(), opts...)
		if err == nil {
			for user := range allPayUsers {
				if user.PrivChannelID == "" {
					logger.Infof(ctx.Request().Context(), "%s user %s priv_channel_id is empty. user:%+v", fun, user.DscUserID, user)
					continue
				}
				if _, ok := has[user.PrivChannelID]; ok {
					continue
				}
				has[user.PrivChannelID] = struct{}{}
				users = append(users, user)
			}
		} else if !errors.Is(err, io.EOF) {
			logger.Warn(ctx.Request().Context(), "getUsersByFilter -> GetDiscordPoolScroll for allTotalPayUsers return err.", logger.String("fun", fun), logger.Any("err", err))
			return nil, err
		}
	}
	logger.Infof(ctx.Request().Context(), "%s allTotalPayUsers len:%d", fun, len(users))
	{
		// 2. filter other
		if filter.DscFilter.DscOtherAll == false {
			innerPartCountReq.IsPart = true
		}
		opts, err := filter2.NewAdHocWrapper().ExamineDscPartFilterCount(ctx.Request().Context(), innerPartCountReq)
		if err != nil {
			return nil, err
		}
		otherPayUsers, err := elasticsearch.DefaultDscEsSvc.GetDiscordPoolScroll(ctx.Request().Context(), opts...)
		if err == nil {
			for user := range otherPayUsers {
				if user.PrivChannelID == "" {
					logger.Infof(ctx.Request().Context(), "%s user %s otherPayUsers priv_channel_id is empty. user:%+v", fun, user.DscUserID, user)
					continue
				}
				if _, ok := has[user.PrivChannelID]; ok {
					continue
				}
				has[user.PrivChannelID] = struct{}{}
				otherUsers = append(otherUsers, user)
			}
			users = append(users, dsc._samplingUser(ctx, filter.DscFilter, otherUsers)...)
		} else if !errors.Is(err, io.EOF) {
			logger.Warn(ctx.Request().Context(), "getUsersByFilter -> GetDiscordPoolScroll for otherFilterUsers return err.", logger.String("fun", fun), logger.Any("err", err))
			return nil, err
		}
	}
	logger.Infof(ctx.Request().Context(), "%s allTotalPayUsers and otherFilterUsers len:%d", fun, len(otherUsers))
	// 4. return users
	details := make([]*models.FpExamineDiscordDetail, 0, len(users))
	for _, user := range users {
		dt := &models.FpExamineDiscordDetail{
			TaskID:               task.ID,
			TplID:                task.TplID,
			Project:              task.Project,
			BotID:                user.AppID,
			DscUserID:            user.DscUserID,
			ChannelID:            user.PrivChannelID,
			GenUID:               int64(user.UID),
			GenAccountID:         user.AccountId,
			GenSid:               user.Sid,
			GenPayAll:            user.PayAll,
			GenPayLastThirtyDays: user.PayLastThirtyDays,
			GenReplyType:         int64(user.ReplyType),
			GenLastLogin:         user.LastLogin,
			GenVipState:          int8(user.VipState),
			GenVipLevel:          int8(user.VipLevel),
			Inspector:            filter.DscFilter.DscAssignAccount[randIdx(len(filter.DscFilter.DscAssignAccount))],
			Status:               int(pb.ExamineStateDf_ExamineStateDfInit),
			RelatedAccount:       "[]",
			NoticeAccount:        "[]",
			CreatedAt:            now,
			FinishedAt:           time.Time{},
			FinalModifiedAt:      time.Time{},
			UpdatedAt:            now,
		}
		details = append(details, dt)
	}

	{
		otherUsers, has, users = nil, nil, nil
	}
	return details, nil
}

func (dsc examineDscImpl) _samplingUser(ctc echo.Context, filter *models.ExamineTaskFilterDscDf, originUsers []*models.FpDscUserDoc) []*models.FpDscUserDoc {
	var (
		sampleUsers = make([]*models.FpDscUserDoc, 0, len(originUsers))
	)
	// 获取循环长度
	var l = filter.DscOtherNum
	if len(originUsers) < l {
		l = len(originUsers)
	}
	//
	switch filter.DscOtherRules {
	// 无差别随机抽检
	case pb.ExamineTaskRulesDf_ExamineTaskRulesDfRandom:
		for i := l; i > 0; i-- {
			randI := randIdx(len(originUsers))
			sampleUsers = append(sampleUsers, originUsers[randI])
			originUsers = append(originUsers[:randI], originUsers[randI+1:]...)
		}
	// 定向等量抽检
	case pb.ExamineTaskRulesDf_ExamineTaskRulesDfAvg:
		MaintainerMap := make(map[string][]*models.FpDscUserDoc, len(originUsers))
		for _, user := range originUsers {
			MaintainerMap[user.Maintainer] = append(MaintainerMap[user.Maintainer], user)
		}

		for len(sampleUsers) < l {
			//轮询所有分组
			for maintainer, users := range MaintainerMap {
				if len(users) == 0 {
					continue
				}
				// 从当前分组随机取一个用户
				randI := randIdx(len(users))
				sampleUsers = append(sampleUsers, users[randI])
				// 从分组中移除取出的用户
				MaintainerMap[maintainer] = append(users[:randI], users[randI+1:]...)

				// 检查是否达到采样数量上限
				if len(sampleUsers) >= l {
					break
				}
			}
		}
	}

	// 2. return
	return sampleUsers
}

// TaskDscFilterCount 统计全局符合条件数量
func (dsc examineDscImpl) TaskDscFilterCount(ctx echo.Context, param *pb.ExamineTaskDscFilterCountReq) (*pb.ExamineTaskDscFilterCountResp, error) {
	opts, err := filter2.NewAdHocWrapper().ExamineDscFilterCount(ctx.Request().Context(), param)
	if err != nil {
		return nil, err
	}
	count, err := elasticsearch.DefaultDscEsSvc.QueryCount(ctx.Request().Context(), opts...)
	if err != nil {
		return nil, err
	}
	return &pb.ExamineTaskDscFilterCountResp{Num: count}, nil
}

// TaskDscPartFilterCount 统计抽检符合条件数量
func (dsc examineDscImpl) TaskDscPartFilterCount(ctx echo.Context, param *pb.ExamineTaskDscPartFilterCountReq) (*pb.ExamineTaskDscFilterCountResp, error) {
	opts, err := filter2.NewAdHocWrapper().ExamineDscPartFilterCount(ctx.Request().Context(), param)
	if err != nil {
		return nil, err
	}
	count, err := elasticsearch.DefaultDscEsSvc.QueryCount(ctx.Request().Context(), opts...)
	if err != nil {
		return nil, err
	}
	return &pb.ExamineTaskDscFilterCountResp{Num: count}, nil
}
