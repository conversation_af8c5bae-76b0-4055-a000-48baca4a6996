package examinecore

import (
	"context"
	"fmt"
	"github.com/labstack/echo/v4"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"golang.org/x/exp/rand"
	"ops-ticket-api/internal/persistence"
	"ops-ticket-api/models"
	"ops-ticket-api/proto/pb"
	"time"
)

type ExamineDfI interface {
	// GenExamineTaskFilter 生成批任务 filter 信息
	GenExamineTaskFilter(ctx echo.Context, param *pb.ExamineTaskSaveReq) (*models.ExamineTaskFilterDf, error)
	// GenExamineTaskLogs 生成批任务
	GenExamineTaskLogs(ctx echo.Context, task *models.FpExamineTask, filter *models.ExamineTaskFilterDf) (*ExamineTaskResult, error)
	// TaskExamineFinished 检测批次任务是否已全部完成: true:已完成；false:部分审核单未完成
	TaskExamineFinished(ctx context.Context, taskId uint64) (bool, error)
}

type (
	ExamineTaskResult struct {
		Error        error `json:"error"`
		TotalCount   int64 `json:"total_count"`
		SuccessCount int64 `json:"success_count"`
		FailedCount  int64 `json:"failed_count"`
	}
)

// GetExamineImpl 获取审核实现 各自不同渠道操作
func GetExamineImpl(examineGroup pb.ExamineTaskGroupDf) ExamineDfI {
	switch examineGroup {
	case pb.ExamineTaskGroupDf_ExamineTaskGroupDfDiscord:
		return &examineDscImpl{}
	default:
		return &examineEmptyImpl{}
	}
}

var ( // func define
	relyTimeF = func(replyTm []string) ([]string, error) {
		//if len(replyTm) == 0 {
		//	return nil, nil
		//}
		if len(replyTm) != 2 {
			return nil, fmt.Errorf("reply_time.len neq 2")
		}
		var relyTime []time.Time
		for _, v := range replyTm {
			tm, err := time.ParseInLocation("2006-01-02 15:04:05", v, time.Local)
			if err != nil {
				return nil, err
			}
			relyTime = append(relyTime, tm)
		}

		if relyTime[0].After(relyTime[1]) {
			return nil, fmt.Errorf("rely_time[0] > rely_time[1]")
		}
		return replyTm, nil
	}
	randIdx = func(maxI int) int {
		rand.Seed(uint64(time.Now().UnixNano()))
		return rand.Intn(maxI)
	}
)

func ExamineOrderSyncTaskState(ctx context.Context, taskId uint64, examineType pb.ExamineTaskGroupDf, detailId uint64) error {
	<-time.NewTimer(time.Millisecond * 500).C
	// check 对应类型内数据是否还存在未处理任务；
	boo, err := GetExamineImpl(examineType).TaskExamineFinished(ctx, taskId)
	if err != nil {
		logger.Errorf(ctx, "ExamineOrderSyncTaskState -> TaskExamineFinished failed. examineType:%v. taskId:%d. err:%v", examineType, taskId, err)
		return err
	}

	// 全部 已处理则 更新任务最后状态
	if boo == true {
		if err := persistence.NewExamineSettings().TaskUpdates(ctx,
			map[string]interface{}{"id": taskId, "task_status": []int{int(pb.ExamineTaskStateDf_ExamineTaskStateDfSuccess)}},
			map[string]interface{}{"finished_at": time.Now(), "updated_at": time.Now()}); err != nil {
			logger.Errorf(ctx, "ExamineOrderSyncTaskState -> TaskUpdates failed. examineType:%v. taskId:%d. err:%v", examineType, taskId, err)
			return err
		}
	}
	return nil
}
