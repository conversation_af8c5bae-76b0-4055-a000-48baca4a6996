package examinecore

import (
	"context"
	"fmt"
	"github.com/labstack/echo/v4"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"ops-ticket-api/models"
	"ops-ticket-api/proto/pb"
)

type examineEmptyImpl struct {
}

func (dsc examineEmptyImpl) GenExamineTaskFilter(ctx echo.Context, param *pb.ExamineTaskSaveReq) (*models.ExamineTaskFilterDf, error) {
	fun := "examineEmptyImpl.GenExamineTaskFilter -->"
	logger.Errorf(ctx.Request().Context(), "%s current examine empty impl not support this method. param:%+v", fun, param)
	return nil, fmt.Errorf("current examine empty impl not support this method")
}

func (dsc examineEmptyImpl) GenExamineTaskLogs(ctx echo.Context, task *models.FpExamineTask, filter *models.ExamineTaskFilterDf) (*ExamineTaskResult, error) {
	logger.Errorf(ctx.Request().Context(), "examineEmptyImpl.GenExamineTaskLogs --> current examine empty impl not support this method. task:%+v, filter:%+v", task, filter)
	return nil, nil
}

func (dsc examineEmptyImpl) TaskExamineFinished(ctx context.Context, taskId uint64) (bool, error) {
	logger.Errorf(ctx, "examineEmptyImpl.TaskExamineFinished --> current examine empty impl not support this method. taskId:%d", taskId)
	return true, nil
}
