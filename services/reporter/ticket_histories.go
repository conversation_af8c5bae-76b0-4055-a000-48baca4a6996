package reporter

import (
	"context"
	"fmt"
	"github.com/spf13/cast"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"ops-ticket-api/internal/persistence"
	"ops-ticket-api/models"
	"ops-ticket-api/proto/pb"
	"time"
)

// PubCreate 工单创建
func PubCreate(ctx context.Context, ticket *models.FpOpsTickets) {
	fun := "PubCreate"
	defer func() {
		if err := recover(); err != nil {
			logger.Errorf(ctx, "%s [ticket create] recover err:%v", fun, err)
		}
	}()
	hist := models.NewHis().HistCreate(ticket.TicketID, ticket.Creator)
	hist.Operator = ticket.UUID
	if ticket.AccountID != "" {
		hist.Operator = ticket.AccountID
	}
	if err := persistence.NewTicket().AddTicketHistory(ctx, nil, hist); err != nil {
		logger.Error(ctx, "add reassign op ticket history err",
			logger.String("Operator", hist.Operator),
			logger.Uint64("ticket_id", ticket.TicketID),
			logger.String("err", err.Error()),
		)
	}
}

// PlayerRefill 玩家补填
func PlayerRefill(ctx context.Context, ticketId uint64, accountId, uuid, content string, beforeStage uint32) {
	history := &models.FpOpsTicketsHistory{
		TicketID: ticketId,
		Acceptor: "",
		Operate:  uint8(pb.TkEvent_TkEventPlayerRefill),
		OpRole:   uint8(pb.UserRole_PlayerRole),
		Operator: cast.ToString(accountId),
		Remark:   content,
	}
	if accountId == "" {
		history.Operator = uuid
	}
	history.OpDetail = beforeStage
	if err := persistence.NewTicket().AddTicketHistory(ctx, nil, history); err != nil {
		logger.Error(ctx, "add fill up op ticket history err", zap.Uint64("ticket_id", ticketId), zap.String("err", err.Error()))
	}
}

// PubReopen 工单重开
func PubReopen(ctx context.Context, tx *gorm.DB, ticket *models.FpOpsTickets, reopenInfo *models.FpOpsTicketsReopen) (hist *models.FpOpsTicketsHistory, err error) {
	defer func() {
		if e := recover(); e != nil {
			err = fmt.Errorf("ticket reopen error, %v", e)
			logger.Errorf(ctx, "[ticket reopen] recover err:%v", e)
			return
		}
	}()
	operator := ticket.UUID
	if ticket.AccountID != "" {
		operator = ticket.AccountID
	}

	hist = &models.FpOpsTicketsHistory{
		TicketID:  ticket.TicketID,
		Acceptor:  ticket.Acceptor,
		Operate:   uint8(pb.TkEvent_TkEventReopen),
		OpDetail:  ticket.ConversionNode,
		OpRole:    uint8(pb.UserRole_PlayerRole),
		Operator:  operator,
		Remark:    reopenInfo.Content,
		CreatedAt: uint64(time.Now().Unix()),
		UpdatedAt: uint64(time.Now().Unix()),
	}
	err = persistence.NewTicket().AddTicketHistory(ctx, tx, hist)
	if err != nil {
		logger.Error(ctx, "add ticket reopen history error",
			zap.String("Operator", hist.Operator),
			zap.Uint64("ticket_id", ticket.TicketID),
			zap.Error(err),
		)
	}
	return
}

// AutoAlloc 自动分配
func AutoAlloc(ctx context.Context, ticketId uint64, curStage uint32, newAcceptor string) (*models.FpOpsTicketsHistory, error) {
	hist := &models.FpOpsTicketsHistory{
		TicketID:  ticketId,
		Acceptor:  newAcceptor,
		Operate:   uint8(pb.TkEvent_TkEventAutoAlloc),
		OpDetail:  curStage,
		OpRole:    uint8(pb.UserRole_SystemRole),
		Operator:  "system",
		Remark:    "",
		CreatedAt: uint64(time.Now().Unix()),
		UpdatedAt: uint64(time.Now().Unix()),
	}

	err := persistence.NewTicket().AddTicketHistory(ctx, nil, hist)
	if err != nil {
		logger.Error(ctx, "add ticket autoAlloc history error",
			zap.String("Operator", hist.Operator),
			zap.String("acceptor", newAcceptor),
			zap.Uint64("ticket_id", ticketId),
			zap.Error(err),
		)
		return hist, err
	}
	return hist, nil
}

// PubAssign 工单指派记录
func PubAssign(ctx context.Context, ticketId uint64, fromStage uint32, lastAcceptor, newAcceptor, operator string) (*models.FpOpsTicketsHistory, error) {
	hist := &models.FpOpsTicketsHistory{
		TicketID:  ticketId,
		Acceptor:  newAcceptor,
		Operate:   uint8(pb.TkEvent_TkEventAssign),
		OpDetail:  fromStage,
		OpRole:    uint8(pb.UserRole_ServiceRole),
		Operator:  operator,
		Remark:    "from=" + lastAcceptor,
		CreatedAt: uint64(time.Now().Unix()),
		UpdatedAt: uint64(time.Now().Unix()),
	}

	err := persistence.NewTicket().AddTicketHistory(ctx, nil, hist)
	if err != nil {
		logger.Error(ctx, "add ticket reassigned history error",
			zap.String("Operator", hist.Operator),
			zap.String("acceptor", newAcceptor),
			zap.Uint64("ticket_id", ticketId),
			zap.Error(err),
		)
		return hist, err
	}
	return hist, nil
}

// PubTurn 工单流转给某人 - 类似于指派
func PubTurn(ctx context.Context, ticketId uint64, fromStage uint32, lastAcceptor, newAcceptor, operator string) (*models.FpOpsTicketsHistory, error) {
	hist := &models.FpOpsTicketsHistory{
		TicketID:  ticketId,
		Acceptor:  newAcceptor,
		Operate:   uint8(pb.TkEvent_TkEventTurn),
		OpDetail:  fromStage,
		OpRole:    uint8(pb.UserRole_ServiceRole),
		Operator:  operator,
		Remark:    "from=" + lastAcceptor,
		CreatedAt: uint64(time.Now().Unix()),
		UpdatedAt: uint64(time.Now().Unix()),
	}

	err := persistence.NewTicket().AddTicketHistory(ctx, nil, hist)
	if err != nil {
		logger.Error(ctx, "add ticket reassigned history error",
			zap.String("Operator", hist.Operator),
			zap.String("acceptor", newAcceptor),
			zap.Uint64("ticket_id", ticketId),
			zap.Error(err),
		)
		return hist, err
	}
	return hist, nil
}
