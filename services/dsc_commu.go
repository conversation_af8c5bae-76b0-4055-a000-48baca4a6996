package services

import (
	"fmt"
	"github.com/labstack/echo/v4"
	"github.com/spf13/cast"
	"github.com/spf13/viper"
	p "ops-ticket-api/internal/persistence"
	"ops-ticket-api/proto/pb"
	"ops-ticket-api/utils"
	"time"
)

func DiscordCommuSave(ctx echo.Context, req *pb.DiscordCommuRecordAddReq) error {
	return p.NewDiscordCommu().DiscordCommuSave(ctx, req)
}

func DiscordCommuEdit(ctx echo.Context, req *pb.DiscordCommuRecordEditReq) error {
	return p.NewDiscordCommu().DiscordCommuEdit(ctx, req)
}

// DiscordCommuList 沟通记录列表
func DiscordCommuList(ctx echo.Context, req *pb.DiscordCommuRecordListReq) (*pb.DiscordCommuRecordListResp, error) {
	list, total, err := p.New<PERSON>om<PERSON>().DiscordCommuList(ctx, req, false)
	if err != nil {
		return nil, err
	}
	return &pb.DiscordCommuRecordListResp{
		CurrentPage: req.Page,
		PerPage:     req.PageSize,
		Total:       total,
		Data:        list,
	}, nil
}

func DiscordCommuListExport(ctx echo.Context, req *pb.DiscordCommuRecordListReq) (string, error) {
	records, _, err := p.NewDiscordCommu().DiscordCommuList(ctx, req, true)
	if err != nil {
		return "", err
	}
	var dest = make([][]interface{}, 0)
	for i := range records {
		detail := records[i]
		questionType := ""
		switch detail.QuestionType {
		case 1:
			questionType = "游戏咨询"
		case 2:
			questionType = "游戏建议"
		case 3:
			questionType = "游戏异常"
		case 4:
			questionType = "服务器匹配&合服"
		case 5:
			questionType = "抱怨/负面反馈"
		default:
			questionType = "其他问题"
		}
		handleStatus := ""
		if detail.HandleStatus == 1 {
			handleStatus = "处理中"
		} else {
			handleStatus = "已完成"
		}
		record := []interface{}{detail.CommuDate, detail.Project, cast.ToString(detail.Uid), detail.Sid, detail.NickName, cast.ToString(detail.PayAll), detail.Question, questionType, handleStatus, detail.Remark, detail.Operator, detail.Maintainer}
		dest = append(dest, record)
	}
	_fileName := fmt.Sprintf("discord_commu_record_export_%d.csv", time.Now().UnixNano())
	err = utils.CreateCsvFile(_fileName, viper.GetStringSlice("export_header.DiscodCommuRecordExport.zh-cn"), dest)
	if err != nil {
		return "", err
	}
	return _fileName, err
}
