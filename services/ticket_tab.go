package services

import (
	"encoding/json"
	"github.com/labstack/echo/v4"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"ops-ticket-api/internal/cst"
	"ops-ticket-api/internal/elasticsearch"
	dto "ops-ticket-api/internal/persistence"
	"ops-ticket-api/models"
	"ops-ticket-api/proto/pb"
	"ops-ticket-api/services/commsrv"
	"sync"
)

func TicketTabSave(ctx echo.Context, req *pb.TicketTabAddReq) error {
	return dto.NewTicketTab().TicketTabSave(ctx, req)
}

func TicketTabDel(ctx echo.Context, req *pb.TicketTabDelReq) error {
	return dto.NewTicketTab().TicketTabDel(ctx, req)
}

// TicketTabList ticket 搜索tab列表
func TicketTabList(ctx echo.Context) (*pb.TicketTabListResp, error) {
	resp, err := dto.NewTicketTab().TicketTabList(ctx)
	if err != nil {
		return nil, err
	}
	return resp, nil
}

// TicketTabCount ticket tab数量
func TicketTabCount(ctx echo.Context) (*pb.TicketTabCountResp, error) {
	permList, _ := commsrv.LoadPermGmList(ctx.Request().Context(), ctx.Get(cst.AccountInfoCtx).(string))
	if len(permList) < 0 {
		logger.Infof(ctx.Request().Context(), "GetAllTabInfo No game permissions available.")
		return nil, nil
	}
	// 获取所有tab内容
	tabList, err := dto.NewTicketTab().GetAllTabInfo(ctx, permList)
	if err != nil {
		return nil, err
	}
	// 用 map 按项目分组存储结果
	projectMap := make(map[string][]*pb.TicketTabCountResp_TabCountDetail)
	var wg sync.WaitGroup
	// 控制并发量为20
	sem := make(chan struct{}, 20)
	results := make(chan *pb.TicketTabCountResp_TicketTabCount, len(tabList))

	for _, tab := range tabList {
		wg.Add(1)
		sem <- struct{}{}
		go func(tab *models.FpOpsTicketsTab) {
			defer func() {
				<-sem
				wg.Done()
			}()
			searchDetail := pb.TicketPoolNewListReq{}
			if err := json.Unmarshal([]byte(tab.Detail), &searchDetail); err != nil {
				logger.Infof(ctx.Request().Context(), "failed to unmarshal detail, err:%v", err)
				return
			}

			// 筛选条件
			options, err := optionsFilterEs(ctx, &searchDetail)
			if err != nil {
				logger.Errorf(ctx.Request().Context(), "TicketTabCount optionsFilterEs err:%v", err)
				return
			}

			// 查询
			count, err := elasticsearch.DefaultTicketEsRepo.GetTicketTabCount(ctx.Request().Context(), options...)
			if err != nil {
				return
			}

			// 按项目分组加入 map
			results <- &pb.TicketTabCountResp_TicketTabCount{
				Tab: []*pb.TicketTabCountResp_TabCountDetail{
					{TabName: tab.TabName, Count: uint64(count)},
				},
				Project: tab.Project,
			}
		}(tab)
	}

	wg.Wait()
	close(results)

	// 收集结果并根据项目分组
	for result := range results {
		projectMap[result.Project] = append(projectMap[result.Project], result.Tab...)
	}

	resp := &pb.TicketTabCountResp{
		Detail: make([]*pb.TicketTabCountResp_TicketTabCount, 0, len(projectMap)),
	}

	for project, tabInfo := range projectMap {
		resp.Detail = append(resp.Detail, &pb.TicketTabCountResp_TicketTabCount{
			Tab:     tabInfo,
			Project: project,
		})
	}

	return resp, nil
}

func TicketTabEdit(ctx echo.Context, req *pb.TicketTabEditReq) error {
	return dto.NewTicketTab().TicketTabEdit(ctx, req)
}

func TicketTabUpdateSort(ctx echo.Context, req *pb.TicketTabUpdateSortReq) error {
	return dto.NewTicketTab().TicketTabUpdateSort(ctx, req)
}
