package llm

import (
	"context"
)

// MessageContent 消息内容结构
type MessageContent struct {
	Type string `json:"type"`
	Text string `json:"text"`
}

// HistoryMessage 历史消息记录
type HistoryMessage struct {
	Role    string           `json:"role"`
	Content []MessageContent `json:"content"`
}

// Question 问题结构
type Question struct {
	QuestionKey     string `json:"question_key"`
	QuestionContent string `json:"question_content"`
}

// AnalyzeRequest LLM分析请求
type AnalyzeRequest struct {
	MsgID              string           `json:"msg_id"`               // 消息ID
	HelloWord          string           `json:"hello_word"`           // 初始打招呼问题
	QuestionList       []Question       `json:"question_list"`        // 问题列表
	NowQuestionKey     string           `json:"now_question_key"`     // 当前问题key
	NowQuestionContent string           `json:"now_question_content"` // 当前问题内容
	NowAnswerContent   string           `json:"now_answer_content"`   // 用户回答问题
	Hists              []HistoryMessage `json:"hists"`                // 历史对话记录
}

// HistoryAnswer 历史回答记录
type HistoryAnswer struct {
	QuestionKey    string `json:"question_key"`    // 问题key
	QuestionAnswer string `json:"question_answer"` // 问题答案
}

// AnalyzeResponse LLM分析响应
type AnalyzeResponse struct {
	Code    int    `json:"code"`
	Message string `json:"msg"`
	Data    struct {
		HistoryAnswerList []HistoryAnswer `json:"history_answer_list"` // 回答列表
		NowQuestionKey    string          `json:"now_question_key"`    // 下一个问题key
		NowAnswerContent  string          `json:"now_answer_content"`  // 当前回答问题
		HitQuestion       bool            `json:"hit_question"`        // 命中当前问题
	} `json:"data"`
}

// Adapter LLM适配器
type Adapter struct {
	client *Client
}

// NewAdapter 创建LLM适配器
func NewAdapter() (*Adapter, error) {
	client, err := NewClient()
	if err != nil {
		return nil, err
	}
	return &Adapter{client: client}, nil
}

// AnalyzeAnswer 实现服务接口
func (a *Adapter) AnalyzeAnswer(ctx context.Context, req *AnalyzeRequest) (*AnalyzeResponse, error) {
	resp, err := a.client.AnalyzeAnswer(ctx, req)
	if err != nil {
		return nil, err
	}

	return resp, nil
}
