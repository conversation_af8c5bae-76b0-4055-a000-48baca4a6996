package llm

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"

	"github.com/pkg/errors"
	"github.com/spf13/viper"
)

// Config LLM配置
type Config struct {
	BaseURL    string `mapstructure:"base_url"`
	Timeout    int    `mapstructure:"timeout"`
	RetryCount int    `mapstructure:"retry_count"`
	Endpoints  struct {
		Analyze string `mapstructure:"analyze"`
	} `mapstructure:"endpoints"`
}

// Client LLM客户端
type Client struct {
	config     *Config
	httpClient *http.Client
}

// NewClient 创建LLM客户端
func NewClient() (*Client, error) {
	var config Config
	if err := viper.UnmarshalKey("llm", &config); err != nil {
		return nil, errors.Wrap(err, "failed to unmarshal llm config")
	}

	if config.Timeout <= 0 {
		config.Timeout = 30
	}

	if config.RetryCount <= 0 {
		config.RetryCount = 3
	}

	return &Client{
		config: &config,
		httpClient: &http.Client{
			Timeout: time.Duration(config.Timeout) * time.Second,
		},
	}, nil
}

// AnalyzeAnswer 分析答案
func (c *Client) AnalyzeAnswer(ctx context.Context, req *AnalyzeRequest) (*AnalyzeResponse, error) {
	url := fmt.Sprintf("%s%s", c.config.BaseURL, c.config.Endpoints.Analyze)

	var resp *AnalyzeResponse
	var lastErr error

	// 重试机制
	for i := 0; i < c.config.RetryCount; i++ {
		resp, lastErr = c.doAnalyze(ctx, url, req)
		if lastErr == nil {
			break
		}
		time.Sleep(time.Second * time.Duration(i+1))
	}

	if lastErr != nil {
		return nil, errors.Wrap(lastErr, "failed to analyze answer after retries")
	}

	return resp, nil
}

// doAnalyze 执行分析请求
func (c *Client) doAnalyze(ctx context.Context, url string, req *AnalyzeRequest) (*AnalyzeResponse, error) {
	reqBody, err := json.Marshal(req)
	if err != nil {
		return nil, errors.Wrap(err, "failed to marshal request")
	}

	httpReq, err := http.NewRequestWithContext(ctx, http.MethodPost, url, bytes.NewReader(reqBody))
	if err != nil {
		return nil, errors.Wrap(err, "failed to create request")
	}
	httpReq.Header.Set("Content-Type", "application/json")

	resp, err := c.httpClient.Do(httpReq)
	if err != nil {
		return nil, errors.Wrap(err, "failed to do request")
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, errors.Wrap(err, "failed to read response body")
	}

	if resp.StatusCode != http.StatusOK {
		return nil, errors.Errorf("unexpected status code: %d, body: %s", resp.StatusCode, string(body))
	}

	var analyzeResp AnalyzeResponse
	if err := json.Unmarshal(body, &analyzeResp); err != nil {
		return nil, errors.Wrap(err, "failed to unmarshal response")
	}

	if analyzeResp.Code != 0 {
		return nil, errors.Errorf("analyze failed: %s", analyzeResp.Message)
	}

	return &analyzeResp, nil
}
