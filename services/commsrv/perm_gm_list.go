package commsrv

import (
	"context"
	"ops-ticket-api/internal/framework/cache/rds"
	"strings"
)

// SetPermGmList 设置用户权限列表： 超管为空；非超管为有权限的游戏列表
func SetPermGmList(ctx context.Context, operator string, list []string) error {
	var v string
	if len(list) > 0 {
		v = strings.Join(list, "|")
	}
	return rds.RCli.HSet(ctx, "ops:new:ticket:perm_list", operator, v).Err()
}

func LoadPermGmList(ctx context.Context, operator string) ([]string, error) {
	v := rds.RCli.HGet(ctx, "ops:new:ticket:perm_list", operator).Val()
	if v == "" {
		// todo
		//return nil, nil
		return nil, nil
	}
	return strings.Split(v, "|"), nil
}
