package services

import (
	"fmt"
	"github.com/labstack/echo/v4"
	"github.com/spf13/cast"
	"github.com/spf13/viper"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"ops-ticket-api/entity/stats"
	"ops-ticket-api/internal/framework/lang"
	"ops-ticket-api/internal/framework/xerrors"
	"ops-ticket-api/internal/persistence"
	"ops-ticket-api/models"
	"ops-ticket-api/proto/pb"
	"ops-ticket-api/utils"
	"sort"
	"sync"
	"time"
)

// InteractStats discord玩家交互数据统计
func InteractStats(ctx echo.Context, req *pb.DiscordPlayerInteractStatsReq) (*stats.DiscordPlayerInteractStatsResp, error) {
	wg := &sync.WaitGroup{}
	var sumCount int64
	var operatorDateStats []*stats.OperatorDateCountDetail
	var operatorStats []*stats.OperatorCountDetail
	var dateStats []*stats.DateCountDetail
	var sumErr, operatorDateErr, operatorErr, dateErr error
	wg.Add(4)
	// 获取总的交互数据
	go func() {
		defer func() {
			if err := recover(); err != nil {
				logger.Errorf(ctx.Request().Context(), "InteractSumStats.recover err:%v", err)
			}
		}()
		defer wg.Done()
		sumCount, sumErr = persistence.NewDiscordInteract().InteractSumStats(req)
	}()
	// 获取处理人每天的交互数据
	go func() {
		defer func() {
			if err := recover(); err != nil {
				logger.Errorf(ctx.Request().Context(), "ProcessorDateStats.recover err:%v", err)
			}
		}()
		defer wg.Done()
		operatorDateStats, operatorDateErr = persistence.NewDiscordInteract().ProcessorDateStats(req)
	}()
	// 获取处理人的交互数据
	go func() {
		defer func() {
			if err := recover(); err != nil {
				logger.Errorf(ctx.Request().Context(), "ProcessorStats.recover err:%v", err)
			}
		}()
		defer wg.Done()
		operatorStats, operatorErr = persistence.NewDiscordInteract().ProcessorStats(req)
	}()
	// 获取每天的交互数据
	go func() {
		defer func() {
			if err := recover(); err != nil {
				logger.Errorf(ctx.Request().Context(), "InteractDateStats.recover err:%v", err)
			}
		}()
		defer wg.Done()
		dateStats, dateErr = persistence.NewDiscordInteract().InteractDateStats(req)
	}()
	wg.Wait()

	if sumErr != nil || operatorDateErr != nil || operatorErr != nil || dateErr != nil {
		return nil, xerrors.New(fmt.Sprintf("sumErr:%v. operatorDateErr:%v. operatorErr:%v. dateErr:%v", sumErr, operatorDateErr, operatorErr, dateErr))
	}
	// 组装数据
	resp := new(stats.DiscordPlayerInteractStatsResp)
	dateMap := make(map[string]map[string]interface{})
	operatorData := make(map[string]interface{})
	operatorData["row_name"] = "sum"
	operatorData["sum_count"] = sumCount
	for _, v := range operatorStats {
		operatorData[v.Operator] = v.OperatorCount
	}
	for _, v := range dateStats {
		if _, ok := dateMap[v.InteractDate]; !ok {
			dateMap[v.InteractDate] = map[string]interface{}{
				"row_name":  v.InteractDate,
				"sum_count": v.DateCount,
			}
		}
	}
	for _, v := range operatorDateStats {
		if m, ok := dateMap[v.InteractDate]; ok {
			m[v.Operator] = v.OperatorDateCount
			dateMap[v.InteractDate] = m
		}
	}
	dateData := []map[string]interface{}{}
	statsData := []map[string]interface{}{}
	for _, m := range dateMap {
		dateData = append(dateData, m)
	}
	sort.Slice(dateData, func(i, j int) bool {
		return utils.TimeStrToDayUnix(dateData[i]["row_name"].(string)) > utils.TimeStrToDayUnix(dateData[j]["row_name"].(string))
	})
	statsData = append(statsData, operatorData)
	statsData = append(statsData, dateData...)
	resp.Data = statsData
	return resp, nil
}

func InteractDetailExport(ctx echo.Context, req *pb.DiscordPlayerInteractStatsReq) (string, error) {
	details, err := persistence.NewDiscordInteract().InteractDetails(req)
	if err != nil {
		return "", err
	}
	var dest = make([][]interface{}, 0)
	for i := range details {
		detail := details[i]
		record := []interface{}{detail.Project, detail.InteractDate, detail.DscUserID + "\t", detail.NickName, cast.ToString(detail.UID) + "\t", detail.AccountId + "\t", detail.Operator}
		dest = append(dest, record)
	}
	_fileName := fmt.Sprintf("discord_interact_detail_export_%d.csv", time.Now().UnixNano())
	err = utils.CreateCsvFile(_fileName, viper.GetStringSlice("export_header.DiscordInteractDetailExport.zh-cn"), dest)
	if err != nil {
		return "", err
	}
	return _fileName, err
}

func MessageCountDateStats(ctx echo.Context, req *pb.DiscordMessageCountReq) (*pb.DiscordMessageCountResp, error) {
	wg := &sync.WaitGroup{}
	var sumMessageCountStats *models.SumMessageCountStats
	var dateMessageCountStats []*pb.DiscordMessageCountResp_MessageCountDetail
	var sumErr, dateErr error
	wg.Add(2)
	// 获取总的信息量数据
	go func() {
		defer func() {
			if err := recover(); err != nil {
				logger.Errorf(ctx.Request().Context(), "SumMessageCountStats.recover err:%v", err)
			}
		}()
		defer wg.Done()
		sumMessageCountStats, sumErr = persistence.NewDiscordInteract().SumMessageCountStats(req)
	}()
	// 获取每天的信息量数据
	go func() {
		defer func() {
			if err := recover(); err != nil {
				logger.Errorf(ctx.Request().Context(), "DateMessageCountStats.recover err:%v", err)
			}
		}()
		defer wg.Done()
		dateMessageCountStats, dateErr = persistence.NewDiscordInteract().DateMessageCountStats(req)
	}()

	wg.Wait()
	if sumErr != nil || dateErr != nil {
		return nil, xerrors.New(fmt.Sprintf("sumErr:%v. dateErr:%v", sumErr, dateErr))
	}
	resp := new(pb.DiscordMessageCountResp)
	resp.Data = append(resp.Data, &pb.DiscordMessageCountResp_MessageCountDetail{
		RowName:             "sum_count",
		PlayerMessageCount:  sumMessageCountStats.PlayerMessageCount,
		ServiceMessageCount: sumMessageCountStats.ServiceMessageCount,
	})
	resp.Data = append(resp.Data, dateMessageCountStats...)
	return resp, nil
}

func MessageCountOperatorStats(ctx echo.Context, req *pb.DiscordMessageCountReq) (*pb.DiscordMessageCountResp, error) {
	wg := &sync.WaitGroup{}
	var sumMessageCountStats *models.SumMessageCountStats
	var operatorMessageCountStats []*pb.DiscordMessageCountResp_MessageCountDetail
	var sumErr, operatorErr error
	wg.Add(2)
	// 获取总的信息量数据
	go func() {
		defer func() {
			if err := recover(); err != nil {
				logger.Errorf(ctx.Request().Context(), "SumMessageCountStats.recover err:%v", err)
			}
		}()
		defer wg.Done()
		sumMessageCountStats, sumErr = persistence.NewDiscordInteract().SumMessageCountStats(req)
	}()
	// 获取处理人的信息量数据
	go func() {
		defer func() {
			if err := recover(); err != nil {
				logger.Errorf(ctx.Request().Context(), "OperatorMessageCountStats.recover err:%v", err)
			}
		}()
		defer wg.Done()
		operatorMessageCountStats, operatorErr = persistence.NewDiscordInteract().OperatorMessageCountStats(req)
	}()

	wg.Wait()
	if sumErr != nil || operatorErr != nil {
		return nil, xerrors.New(fmt.Sprintf("sumErr:%v. operatorErr:%v", sumErr, operatorErr))
	}
	resp := new(pb.DiscordMessageCountResp)
	resp.Data = append(resp.Data, &pb.DiscordMessageCountResp_MessageCountDetail{
		RowName:             "sum_count",
		PlayerMessageCount:  sumMessageCountStats.PlayerMessageCount,
		ServiceMessageCount: sumMessageCountStats.ServiceMessageCount,
	})
	resp.Data = append(resp.Data, operatorMessageCountStats...)
	return resp, nil
}

func MessageCountDetailExport(ctx echo.Context, req *pb.DiscordMessageCountReq) (string, error) {
	details, err := persistence.NewDiscordInteract().MessageCountDetails(req)
	if err != nil {
		return "", err
	}
	var dest = make([][]interface{}, 0)
	for i := range details {
		detail := details[i]
		record := []interface{}{detail.Project, detail.InteractDate, detail.DscUserID + "\t", detail.NickName, cast.ToString(detail.UID) + "\t", detail.AccountId + "\t", cast.ToString(detail.PlayerMessageCount), detail.Operator, cast.ToString(detail.ServiceMessageCount)}
		dest = append(dest, record)
	}
	_fileName := fmt.Sprintf("discord_message_count_detail_export_%d.csv", time.Now().UnixNano())
	err = utils.CreateCsvFile(_fileName, viper.GetStringSlice("export_header.DiscordMessageCountDetailExport.zh-cn"), dest)
	if err != nil {
		return "", err
	}
	return _fileName, err
}

func ReplyTimeDetailStats(ctx echo.Context, req *pb.DiscordReplyTimeReq) (*pb.DiscordReplyTimeResp, error) {
	wg := &sync.WaitGroup{}
	var replyTimeCountDetail = []*pb.DiscordReplyTimeResp_ReplyTimeCountDetail{}
	var replyTimeAvgDayDetail = []*pb.DiscordReplyTimeResp_ReplyTimeAvgDayDetail{}
	var countErr, timeErr error
	wg.Add(2)
	// 获取时间段的回复次数和占比
	go func() {
		defer func() {
			if err := recover(); err != nil {
				logger.Errorf(ctx.Request().Context(), "ReplyTimeDetailStats.recover err:%v", err)
			}
		}()
		defer wg.Done()
		replyTimeCountDetail, countErr = persistence.NewDiscordInteract().ReplyTimeDetailStats(req)
	}()
	// 获取每天的平均回复时间
	go func() {
		defer func() {
			if err := recover(); err != nil {
				logger.Errorf(ctx.Request().Context(), "OperatorMessageCountStats.recover err:%v", err)
			}
		}()
		defer wg.Done()
		replyTimeAvgDayDetail, timeErr = persistence.NewDiscordInteract().ReplyTimeAvgDayDetailStats(req)
	}()

	wg.Wait()
	if countErr != nil || timeErr != nil {
		return nil, xerrors.New(fmt.Sprintf("sumErr:%v. operatorErr:%v", countErr, timeErr))
	}
	resp := new(pb.DiscordReplyTimeResp)
	if len(replyTimeCountDetail) == 0 {
		replyTimeCountDetail = []*pb.DiscordReplyTimeResp_ReplyTimeCountDetail{}
	}
	resp.ReplyCountData = replyTimeCountDetail
	if len(replyTimeAvgDayDetail) == 0 {
		replyTimeAvgDayDetail = []*pb.DiscordReplyTimeResp_ReplyTimeAvgDayDetail{}
	}
	resp.ReplyAvgData = replyTimeAvgDayDetail
	return resp, nil
}

func ReplyTimeDetailExport(ctx echo.Context, req *pb.DiscordReplyTimeReq) (string, error) {
	details, err := persistence.NewDiscordInteract().ReplyTimeAvgDayDetailStats(req)
	if err != nil {
		return "", err
	}
	var dest = make([][]interface{}, 0)
	for i := range details {
		detail := details[i]
		record := []interface{}{detail.Date, detail.AvgReplyTime}
		dest = append(dest, record)
	}
	_fileName := fmt.Sprintf("discord_reply_time_detail_export_%d.csv", time.Now().UnixNano())
	err = utils.CreateCsvFile(_fileName, []string{"日期", "平均等待时长（h）"}, dest)
	if err != nil {
		return "", err
	}
	return _fileName, err
}

// SatisfactionStats discord调查问卷满意度报表
func SatisfactionStats(ctx echo.Context, req *pb.DiscordPlayerSatisfactionStatsReq) (*stats.DiscordPlayerSatisfactionStatsResp, error) {
	var (
		data   []map[string]interface{}
		err    error
		result = &stats.DiscordPlayerSatisfactionStatsResp{Data: make([]map[string]interface{}, 0)}
	)
	switch req.StatType {
	case pb.SurveyStatType_SurveyStatDay:
		data, err = SatisfactionSurveyDayStats(ctx, req)
	case pb.SurveyStatType_SurveyStatGame:
		data, err = SatisfactionSurveyGameStats(ctx, req)
	case pb.SurveyStatType_SurveyStatAccount:
		data, err = SatisfactionSurveyAccountStats(ctx, req)
	default:
		data, err = []map[string]interface{}{}, fmt.Errorf("invalid stat type:%d", req.StatType)
	}
	if err != nil {
		return nil, err
	}
	result.Data = data
	return result, nil
}
func SatisfactionSurveyDayStats(ctx echo.Context, req *pb.DiscordPlayerSatisfactionStatsReq) ([]map[string]interface{}, error) {
	var (
		total = map[string]interface{}{
			"evaluation_date": "合计",
			"1":               0,
			"2":               0,
			"3":               0,
			"4":               0,
			"5":               0,
			"all":             0,
			"gt3_rate":        0,
		}
		dest = make([]map[string]interface{}, 0)
	)
	details, err := persistence.NewDcSatisfactionSurvey().SatisfactionSurveyDateStats(req)
	if err != nil {
		return dest, err
	}

	for _, row := range details {
		if len(dest) == 0 || cast.ToString(dest[len(dest)-1]["evaluation_date"]) != row.EvaluationDate {
			dest = append(dest, map[string]interface{}{
				"evaluation_date": row.EvaluationDate,
				"1":               0,
				"2":               0,
				"3":               0,
				"4":               0,
				"5":               0,
				"all":             0,
				"gt3_rate":        0,
			})
		}
		dest[len(dest)-1][fmt.Sprintf("%d", row.Rating)] = int(row.DateCount)
	}
	// 计算值
	for _, row := range dest {
		row["all"] = row["1"].(int) + row["2"].(int) + row["3"].(int) + row["4"].(int) + row["5"].(int)
		if row["all"].(int) > 0 {
			row["gt3_rate"] = utils.FloatRound(float64(row["4"].(int)+row["5"].(int))/float64(row["all"].(int)), 2)
		}
		row["gt3_rate"] = utils.FloatRound(float64(row["4"].(int)+row["5"].(int))/float64(row["all"].(int)), 2)
		total["1"] = total["1"].(int) + row["1"].(int)
		total["2"] = total["2"].(int) + row["2"].(int)
		total["3"] = total["3"].(int) + row["3"].(int)
		total["4"] = total["4"].(int) + row["4"].(int)
		total["5"] = total["5"].(int) + row["5"].(int)
		total["all"] = total["all"].(int) + row["all"].(int)
	}
	if total["all"].(int) > 0 {
		total["gt3_rate"] = utils.FloatRound(float64(total["4"].(int)+total["5"].(int))/float64(total["all"].(int)), 2)
	}
	dest = append([]map[string]interface{}{total}, dest...)
	return dest, nil
}

func SatisfactionSurveyAccountStats(ctx echo.Context, req *pb.DiscordPlayerSatisfactionStatsReq) ([]map[string]interface{}, error) {
	var (
		total = map[string]interface{}{
			"operator": "合计",
			"1":        0,
			"2":        0,
			"3":        0,
			"4":        0,
			"5":        0,
			"all":      0,
			"gt3_rate": 0,
		}
		dest = make([]map[string]interface{}, 0)
	)
	details, err := persistence.NewDcSatisfactionSurvey().SatisfactionSurveyAccountStats(req)
	if err != nil {
		return dest, err
	}

	for _, row := range details {
		if len(dest) == 0 || cast.ToString(dest[len(dest)-1]["operator"]) != row.Operator {
			dest = append(dest, map[string]interface{}{
				"operator": row.Operator,
				"1":        0,
				"2":        0,
				"3":        0,
				"4":        0,
				"5":        0,
				"all":      0,
				"gt3_rate": 0,
			})
		}
		dest[len(dest)-1][fmt.Sprintf("%d", row.Rating)] = int(row.DateCount)
	}
	// 计算值
	for _, row := range dest {
		row["all"] = row["1"].(int) + row["2"].(int) + row["3"].(int) + row["4"].(int) + row["5"].(int)
		if row["all"].(int) > 0 {
			row["gt3_rate"] = utils.FloatRound(float64(row["4"].(int)+row["5"].(int))/float64(row["all"].(int)), 2)
		}
		total["1"] = total["1"].(int) + row["1"].(int)
		total["2"] = total["2"].(int) + row["2"].(int)
		total["3"] = total["3"].(int) + row["3"].(int)
		total["4"] = total["4"].(int) + row["4"].(int)
		total["5"] = total["5"].(int) + row["5"].(int)
		total["all"] = total["all"].(int) + row["all"].(int)
	}
	if total["all"].(int) > 0 {
		total["gt3_rate"] = utils.FloatRound(float64(total["4"].(int)+total["5"].(int))/float64(total["all"].(int)), 2)
	}
	dest = append([]map[string]interface{}{total}, dest...)
	return dest, nil
}
func SatisfactionSurveyGameStats(ctx echo.Context, req *pb.DiscordPlayerSatisfactionStatsReq) ([]map[string]interface{}, error) {
	var (
		dest = make([]map[string]interface{}, 0)
	)
	details, err := persistence.NewDcSatisfactionSurvey().SatisfactionSurveyGameStats(req)
	if err != nil {
		return dest, err
	}

	for _, row := range details {
		if len(dest) == 0 || cast.ToString(dest[len(dest)-1]["project"]) != row.Project {
			dest = append(dest, map[string]interface{}{
				"project":  row.Project,
				"1":        0,
				"2":        0,
				"3":        0,
				"4":        0,
				"5":        0,
				"all":      0,
				"gt3_rate": 0,
			})
		}
		dest[len(dest)-1][fmt.Sprintf("%d", row.Rating)] = int(row.DateCount)
	}
	// 计算值
	for _, row := range dest {
		row["all"] = row["1"].(int) + row["2"].(int) + row["3"].(int) + row["4"].(int) + row["5"].(int)
		if row["all"].(int) > 0 {
			row["gt3_rate"] = utils.FloatRound(float64(row["4"].(int)+row["5"].(int))/float64(row["all"].(int)), 2)
		}
	}
	return dest, nil
}
func SatisfactionStatsExport(ctx echo.Context, req *pb.DiscordPlayerSatisfactionStatsReq) (string, error) {
	details, err := persistence.NewDcSatisfactionSurvey().SatisfactionSurveyDetails(req)
	if err != nil {
		return "", err
	}
	var dest = make([][]interface{}, 0)
	for i := range details {
		detail := details[i]
		record := []interface{}{detail.Project, detail.CreatedAt.Format(time.DateOnly), detail.CreatedAt.Format(time.DateTime),
			detail.DscUserID + "\t", detail.NickName, cast.ToString(detail.UID) + "\t", detail.AccountID + "\t", detail.Operator, detail.Maintainer, detail.Operators, detail.Initiator,
			detail.Rating, lang.FormatText(ctx, pb.SurveyQstType(detail.EvaluationTarget).String()), detail.RatingReason}
		dest = append(dest, record)
	}
	_fileName := fmt.Sprintf("survey_dsc_stat_detail_export_%d.csv", time.Now().UnixNano())
	err = utils.CreateCsvFile(_fileName, viper.GetStringSlice("export_header.SurveyDscDetailStatExport.zh-cn"), dest)
	if err != nil {
		return "", err
	}
	return _fileName, err
}
