package priv

import (
	"encoding/json"
	"errors"
	"fmt"

	"github.com/spf13/viper"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"gitlab-ee.funplus.io/ops-tools/compkg/sign"
	"golang.org/x/net/context"
)

type PrivCardUseReq struct {
	Uid     string `json:"uid"`
	Fpid    string `json:"fpid"`
	Project string `json:"project"`
	// 分类id 工单分类id
	CatId uint32 `json:"cat_id"`
}

func UsePrivCard(ctx context.Context, req *PrivCardUseReq) (bool, error) {
	// 请求成功则表示使用成功
	path := fmt.Sprintf("%s%s", viper.GetString("thirdparty.priv.inner_host"), viper.GetString("thirdparty.priv.card_use"))

	bodyBytes, err := json.Marshal(req)
	if err != nil {
		return false, err
	}

	sign, err := PrivSign(ctx, string(bodyBytes))
	if err != nil {
		return false, err
	}
	header := map[string]string{
		"Sign": sign,
	}

	_, err = acClient.PostJson(ctx, path, string(bodyBytes), header)
	if err != nil {
		return false, err
	}
	return true, nil
}

func PrivSign(ctx context.Context, body string) (string, error) {
	scrt := viper.GetString("auth.priv_secret")
	if scrt == "" {
		logger.Errorf(ctx, "middleware .checkUserSign curl get secret empty.  body:%s", string(body))
		return "", errors.New("secret empty")
	}
	mysign := sign.Base64WithSha256(body, scrt)
	return mysign, nil
}
