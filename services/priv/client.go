package priv

import (
	"encoding/json"
	"errors"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"net/http"
	"time"

	"gitlab-ee.funplus.io/ops-tools/compkg/httpclient"
	"gitlab-ee.funplus.io/ops-tools/compkg/metrics/metricsapi"
	"golang.org/x/net/context"
)

var acClient *AcClient

func init() {
	ac := httpclient.New()
	ac.SetClient(&http.Client{
		Timeout: 15 * time.Second,
		Transport: &http.Transport{
			MaxIdleConns:        100,
			MaxIdleConnsPerHost: 100,
			MaxConnsPerHost:     400,
		},
	})
	acClient = &AcClient{client: ac}
}

type AcClient struct {
	client *httpclient.HttpClient
}

type Resp struct {
	Code int    `json:"code"`
	Msg  string `json:"msg"`
}

var accountSummary = metricsapi.NewSummary("account_req", []string{"app_name", "path", "code"})

// todo promethues & alert
func (ac *AcClient) PostJson(ctx context.Context, path string, data interface{}, header map[string]string) (*httpclient.Response, error) {
	resp := &Resp{}
	// startTime := time.Now()
	res, err := ac.client.PostJsonWithHeader(ctx, path, data, header)
	// defer func() {
	// 	accountSummary.With(metricsapi.Labels{
	// 		"app_name": viper.GetString("app.name"),
	// 		"path":     path,
	// 		"code":     fmt.Sprintf("%d", resp.Code),
	// 	}).Set(float64(time.Since(startTime).Milliseconds()))
	// }()
	if err != nil {
		return nil, err
	}
	_ = json.Unmarshal(res.Bytes(), resp)
	if resp.Code != 0 {
		logger.Errorf(ctx, "middleware .checkUserSign curl get error.  path:%s, code:%d, msg:%s", path, resp.Code, resp.Msg)
		return nil, errors.New(resp.Msg)
	}
	return res, err
}
