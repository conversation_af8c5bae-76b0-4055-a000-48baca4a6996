// Copyright 2021 funplus Authors. All Rights Reserved.
// @Description: 工单信息
// @Author: Darcy
// @Date: 2022/5/30 16:32

package services

import (
	"bytes"
	"encoding/csv"
	"github.com/labstack/echo/v4"
	"github.com/olivere/elastic/v7"
	"github.com/spf13/viper"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"io"
	"ops-ticket-api/internal/elasticsearch"
	dto "ops-ticket-api/internal/persistence"
	"ops-ticket-api/pkg/filter"
	"ops-ticket-api/pkg/formatter"
	"ops-ticket-api/proto/pb"
	"ops-ticket-api/services/ticket"
	"ops-ticket-api/utils"
)

// TicketCount 工单总量
func TicketCount(ctx echo.Context, req *pb.TicketCountReq) (uint64, error) {
	total, err := elasticsearch.DefaultTicketEsRepo.GetTicketCount(ctx.Request().Context(), req)
	if err != nil {
		logger.Warn(ctx.Request().Context(), "get data from elasticsearch err", logger.Any("err", err))
		return 0, err
	}
	return uint64(total), nil
}

func TicketWorkPool(ctx echo.Context, req *pb.TicketPoolNewListReq) ([]*pb.TicketPoolNewListResp_TicketPoolInfo, uint32, error) {
	opts, err := optionsFilterEs(ctx, req)
	if err != nil {
		return nil, 0, err
	}
	var esFields = []string{"commus"}
	var sort []elastic.Sorter

	switch req.SortBy {
	case pb.TkPoolSort_TkPoolSortCreateTm:
		if req.Order == "desc" {
			sort = []elastic.Sorter{
				elastic.NewFieldSort("created_at").Desc(),
				elastic.NewFieldSort("ticket_id").Desc(),
			}
		} else {
			sort = []elastic.Sorter{
				elastic.NewFieldSort("created_at").Asc(),
				elastic.NewFieldSort("ticket_id").Asc(),
			}
		}
	case pb.TkPoolSort_TkPoolSortRecharge:
		if req.Order == "desc" {
			sort = []elastic.Sorter{
				elastic.NewFieldSort("recharge").Desc(),
				elastic.NewFieldSort("ticket_id").Desc(),
			}
		} else {
			sort = []elastic.Sorter{
				elastic.NewFieldSort("recharge").Asc(),
				elastic.NewFieldSort("ticket_id").Asc(),
			}
		}
	default:
		sort = []elastic.Sorter{
			elastic.NewFieldSort("sort_wait_start_at").Asc(),
			elastic.NewFieldSort("ticket_id").Desc(),
		}
	}
	dest, total, err := elasticsearch.DefaultTicketEsRepo.GetTicketPool(ctx.Request().Context(), int(req.Page), int(req.PageSize), esFields, sort, opts...)
	if err != nil {
		logger.Warn(ctx.Request().Context(), "get data from elasticsearch err", logger.Any("err", err))
		return nil, 0, err
	}

	resp, err := formatter.DefaultTicketFormatter.PoolTicketInfoFormatEs(ctx, dest)
	if err != nil {
		return nil, 0, err
	}
	return resp, uint32(total), nil
}

// TicketWorkPoolExport 工单导出
func TicketWorkPoolExport(ctx echo.Context, req *pb.TicketPoolNewListReq) (chan []byte, error) {

	opts, err := optionsFilterEs(ctx, req)
	if err != nil {
		return nil, err
	}
	record, err := ticket.RecordInfoExport(ctx, opts)
	if err != nil {
		return nil, err
	}
	buf := bytes.Buffer{}
	buf.WriteString("\xEF\xBB\xBF")
	recordChan := make(chan []byte)
	go func(buff *bytes.Buffer) {
		writer := csv.NewWriter(buff)
		_ = writer.Write(viper.GetStringSlice("export_header.TicketPoolExport.zh-cn"))
		batch := 0
		for tk := range record {
			if err := writer.Write(tk); err != nil {
				continue
			}
			batch++
			if batch > 32 {
				writer.Flush()
				byteInfo, _ := io.ReadAll(buff)
				recordChan <- byteInfo
				batch = 0
			}
		}
		writer.Flush()
		recordChan <- buff.Bytes()
		close(recordChan)
	}(&buf)

	return recordChan, nil
}

func optionsFilterEs(ctx echo.Context, req *pb.TicketPoolNewListReq) ([]elasticsearch.Option, error) {
	// 添加需要处理的字段
	fields := map[string]string{
		"ticket_ids": req.TicketIds,
	}
	// 调用统一处理函数
	preparedFields := utils.PrepareStringObjsForPropertyTerm(fields)

	// 根据teamids得到去重后的member列表
	members, err := dto.NewTeamConfigRepo().GetMembersWithIds(ctx, req.TeamIds)

	//
	req.Channel = utils.ChannelFilter(req.Channel, req.PackageId)
	option, err := filter.NewAdHocWrapper().TicketPool(ctx.Request().Context(), req, preparedFields, members)
	if err != nil {
		return nil, err
	}
	// 数据权限过滤
	//if !ctx.Get(cst.AccountIsAdminCtx).(bool) {
	//	userGroupOpt, _, err := NewUserSrv().UserGroupOptionEs(ctx, uint8(req.Line), req.Project)
	//	if err != nil {
	//		return nil, err
	//	}
	//	option = append(option, userGroupOpt...)
	//}
	return option, nil
}
