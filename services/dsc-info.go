package services

import (
	"bytes"
	"encoding/csv"
	"github.com/labstack/echo/v4"
	"github.com/olivere/elastic/v7"
	"github.com/spf13/cast"
	"github.com/spf13/viper"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"io"
	"ops-ticket-api/internal/cst"
	"ops-ticket-api/internal/elasticsearch"
	"ops-ticket-api/internal/persistence"
	"ops-ticket-api/pkg/filter"
	"ops-ticket-api/pkg/formatter"
	"ops-ticket-api/proto/pb"
	"ops-ticket-api/utils"
)

// DiscordAccountsCount  discord玩家账号总数
func DiscordAccountsCount(ctx echo.Context, projects []interface{}) (int64, error) {
	total, err := elasticsearch.DefaultDscEsSvc.GetDiscordAccountsCount(ctx.Request().Context(), projects)
	if err != nil {
		logger.Error(ctx.Request().Context(), "GetDiscordAccountsCount get data from elasticsearch err", logger.Any("err", err))
		return 0, err
	}
	return total, nil
}

// WaitReplyAccountsCount  待回复玩家账号总数
func WaitReplyAccountsCount(ctx echo.Context, projects []interface{}) (int64, error) {
	total, err := elasticsearch.DefaultDscEsSvc.WaitReplyAccountsCount(ctx.Request().Context(), projects)
	if err != nil {
		logger.Error(ctx.Request().Context(), "WaitReplyAccountsCount get data from elasticsearch err", logger.Any("err", err))
		return 0, err
	}
	return total, nil
}

// MineWaitReplyAccountsCount  我的待回复玩家账号总数
func MineWaitReplyAccountsCount(ctx echo.Context, projects []interface{}) (int64, error) {
	maintainer := cast.ToString(ctx.Get(cst.AccountInfoCtx))
	total, err := elasticsearch.DefaultDscEsSvc.MineWaitReplyAccountsCount(ctx.Request().Context(), maintainer, projects)
	if err != nil {
		logger.Error(ctx.Request().Context(), "MineWaitReplyAccountsCount get data from elasticsearch err", logger.Any("err", err))
		return 0, err
	}
	return total, nil
}

func DiscordPool(ctx echo.Context, req *pb.DscUserListReq) ([]*pb.DscPoolInfo, uint32, error) {
	opts, err := optionsDiscordFilterEs(ctx, req)
	if err != nil {
		return nil, 0, err
	}
	var esFields = []string{"project"}
	var sort []elastic.Sorter
	switch req.SortBy {
	case pb.DcPoolSort_DcPoolSortWaitTm:
		sort = []elastic.Sorter{
			elastic.NewFieldSort("sort_wait_start_at").Asc(),
		}
	case pb.DcPoolSort_DcPoolSortLastReplyTm:
		if req.Order == "desc" {
			sort = []elastic.Sorter{
				elastic.NewFieldSort("last_reply_time").Desc(),
			}
		} else {
			sort = []elastic.Sorter{
				elastic.NewFieldSort("last_reply_time").Asc(),
			}
		}
	case pb.DcPoolSort_DcPoolSortpaidAmount:
		if req.Order == "desc" {
			sort = []elastic.Sorter{
				elastic.NewFieldSort("pay_all").Desc(),
			}
		} else {
			sort = []elastic.Sorter{
				elastic.NewFieldSort("pay_all").Asc(),
			}
		}
	}
	dest, total, err := elasticsearch.DefaultDscEsSvc.GetDscPool(ctx.Request().Context(), int(req.Page), int(req.PageSize), esFields, sort, opts...)
	if err != nil {
		logger.Error(ctx.Request().Context(), "get discord data from elasticsearch err", logger.Any("err", err))
		return nil, 0, err
	}

	resp, err := formatter.DefaultDiscordFormatter.PoolDiscordInfoFormatEs(ctx, dest)
	if err != nil {
		return nil, 0, err
	}
	return resp, uint32(total), nil
}

// DscPoolExport Discord导出
func DscPoolExport(ctx echo.Context, req *pb.DscUserListReq) (chan []byte, error) {
	opts, err := optionsDiscordFilterEs(ctx, req)
	if err != nil {
		return nil, err
	}
	record, err := DscPoolExportGetData(ctx, opts)
	if err != nil {
		return nil, err
	}
	buf := bytes.Buffer{}
	buf.WriteString("\xEF\xBB\xBF")
	recordChan := make(chan []byte)
	go func(buff *bytes.Buffer) {
		writer := csv.NewWriter(buff)
		_ = writer.Write(viper.GetStringSlice("export_header.DiscordPoolExport.zh-cn"))
		batch := 0
		for tk := range record {
			if err := writer.Write(tk); err != nil {
				continue
			}
			batch++
			if batch > 32 {
				writer.Flush()
				byteInfo, _ := io.ReadAll(buff)
				recordChan <- byteInfo
				batch = 0
			}
		}
		writer.Flush()
		recordChan <- buff.Bytes()
		close(recordChan)
	}(&buf)

	return recordChan, nil
}

func optionsDiscordFilterEs(ctx echo.Context, req *pb.DscUserListReq) ([]elasticsearch.Option, error) {
	// 需要处理的字段
	fields := map[string]string{
		"uids": req.Uids,
	}
	// 调用统一处理函数
	preparedFields := utils.PrepareStringObjsForPropertyTerm(fields)
	option, err := filter.NewAdHocWrapper().DiscordPool(ctx.Request().Context(), req, preparedFields)
	if err != nil {
		return nil, err
	}
	return option, nil
}

func DiscordRemarkSave(ctx echo.Context, req *pb.DscUserRemarkAddReq) error {
	dest := map[string]interface{}{
		"note":       req.Note,
		"updated_at": utils.NowTimestamp(),
	}
	if err := elasticsearch.DefaultDscEsSvc.UpdateDsc(ctx.Request().Context(), req.ChannelId, dest); err != nil {
		return err
	}
	return nil
}

func DiscordReplyStatusRectify(ctx echo.Context, req *pb.DiscordReplyStatusRectifyReq) error {
	// 检查是否允许修改回复状态,这取决于实际的回复状态与要修改的目标状态是否一致，一致则允许修改，否则不允许修改
	allow, err := persistence.NewDiscordInteract().CheckReplyStatusByDscUserId(ctx, req.ChannelId, req.DscUserId, req.NewReplyStatus)
	if err != nil {
		return err
	}
	// 不允许证明状态是对的，直接返回成功
	if !allow {
		return nil
	}
	dest := map[string]interface{}{
		"reply_type": req.NewReplyStatus,
		"updated_at": utils.NowTimestamp(),
	}
	if err = elasticsearch.DefaultDscEsSvc.UpdateDsc(ctx.Request().Context(), req.ChannelId, dest); err != nil {
		return err
	}
	return nil
}
