package services

import (
	"fmt"
	"github.com/labstack/echo/v4"
	"github.com/spf13/cast"
	"github.com/spf13/viper"
	p "ops-ticket-api/internal/persistence"
	"ops-ticket-api/proto/pb"
	"ops-ticket-api/utils"
	"strings"
	"time"
)

func LineCommuSave(ctx echo.Context, req *pb.LineCommuRecordAddReq) error {
	return p.NewLineCommu().LineCommuSave(ctx, req)
}

func LineCommuEdit(ctx echo.Context, req *pb.LineCommuRecordEditReq) error {
	return p.NewLineCommu().LineCommuEdit(ctx, req)
}

// LineCommuList 沟通记录列表
func LineCommuList(ctx echo.Context, req *pb.LineCommuRecordListReq) (*pb.LineCommuRecordListResp, error) {
	list, total, err := p.NewLineCommu().LineCommuList(ctx, req, false)
	if err != nil {
		return nil, err
	}
	return &pb.LineCommuRecordListResp{
		CurrentPage: req.Page,
		PerPage:     req.PageSize,
		Total:       total,
		Data:        list,
	}, nil
}

func LineCommuListExport(ctx echo.Context, req *pb.LineCommuRecordListReq) (string, error) {
	records, _, err := p.NewLineCommu().LineCommuList(ctx, req, true)
	if err != nil {
		return "", err
	}
	var dest = make([][]interface{}, 0)
	for i := range records {
		detail := records[i]
		handleStatus := ""
		if detail.HandleStatus == 1 {
			handleStatus = "处理中"
		} else {
			handleStatus = "已完成"
		}
		tagDesc := []string{}
		for _, tag := range detail.Label {
			tagDesc = append(tagDesc, tag.TagName)
		}
		tagStr := strings.Join(tagDesc, ",")
		record := []interface{}{detail.CommuDate, detail.Project, tagStr, cast.ToString(detail.Uid) + "\t", detail.Sid, detail.NickName, cast.ToString(detail.PayAll), detail.Question, detail.Category, handleStatus, detail.Remark, detail.Operator, detail.Maintainer}
		dest = append(dest, record)
	}
	_fileName := fmt.Sprintf("line_commu_record_export_%d.csv", time.Now().UnixNano())
	err = utils.CreateCsvFile(_fileName, viper.GetStringSlice("export_header.LineCommuRecordExport.zh-cn"), dest)
	if err != nil {
		return "", err
	}
	return _fileName, err
}
