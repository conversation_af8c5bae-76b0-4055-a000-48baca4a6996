// Copyright 2021 funplus Authors. All Rights Reserved.
// @Author: <PERSON><PERSON>
// @Date: 2025/4/23 13:16

package services

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"github.com/spf13/cast"
	"github.com/spf13/viper"
	"ops-ticket-api/models"
	"ops-ticket-api/pkg/game"
	"ops-ticket-api/proto/pb"
	"ops-ticket-api/services/pc_assist"
	"strings"
)

var afterNoticeExecHooks []noticeAfterHook = []noticeAfterHook{
	pushPcNotice,
}

type noticeAfterHook func(ctx context.Context, replyId uint64, tkInfo *models.FpOpsTickets, redType pb.RedPointTypeEnum) error

// fpx_app_id
type pcLinkParams struct {
	FpxAppID  string `json:"fpx_app_id"`
	AccountID string `json:"account_id"`
	UID       uint64 `json:"uid"`
	TicketID  uint64 `json:"ticket_id"`
	Lang      string `json:"lang"`
}

// 通知pc小助手
func pushPcNotice(ctx context.Context, replyId uint64, tkInfo *models.FpOpsTickets, redType pb.RedPointTypeEnum) error {

	params := &pcLinkParams{
		FpxAppID:  tkInfo.GmID,
		AccountID: tkInfo.AccountID,
		UID:       tkInfo.UID,
		TicketID:  tkInfo.TicketID,
		Lang:      tkInfo.Lang,
	}

	infos, _ := json.Marshal(params)

	bs64c := base64.StdEncoding.EncodeToString(infos)

	project, err := game.NewGameInfo().GetGameProjectById(params.FpxAppID)
	if err != nil {
		return err
	}
	gameName := DealGameProjectToGameName(project)

	jumpUrl := viper.GetString("config.pc_notice_url") + "?data=" + bs64c + "&game=" + gameName

	req := &pc_assist.PushPcAssistRequest{
		GameProject: project,
		Uid:         cast.ToString(params.UID),
		Fpid:        params.AccountID,
		TicketId:    cast.ToString(params.TicketID),
		JumpUrl:     jumpUrl,
	}
	return pc_assist.PushPcAssist(ctx, req)
}

// 特殊处理的游戏项目
var gameProjectSpecialMap = map[string]string{
	"mo_global":      "soc",
	"mce_global":     "st",
	"pk.global.prod": "l",
}

func DealGameProjectToGameName(gameProject string) string {

	if v, ok := gameProjectSpecialMap[gameProject]; ok {
		return v
	}

	//旧版sdk ss_global
	arr := strings.Split(gameProject, "_")
	if len(arr) > 1 {
		return arr[0]
	}
	// fpx sdk dc.global.prod
	fpxArr := strings.Split(gameProject, ".")
	if len(fpxArr) > 1 {
		return fpxArr[0]
	}
	// 返回空
	return gameProject
}
