package dataremote

import (
	"context"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"ops-ticket-api/internal/persistence"
	"sync"
)

var (
	locI18nMaps = map[string]*sync.Map{}
	locItemMaps = map[string]*sync.Map{}

	locItemEventChan = make(chan string, 1024)
	locI18nEventChan = make(chan string, 1024)
)

func RefreshI18n(project string) {
	locI18nEventChan <- project
}

func RefreshItems(project string) {
	locItemEventChan <- project
}

func init() {
	ctx := context.TODO()
	go doRefreshLocI18n(ctx)
	go doRefreshLocItem(ctx)
}

func doRefreshLocItem(ctx context.Context) {
	for project := range locItemEventChan {
		list, err := persistence.NewDataPlatGameItems().GetItems(ctx, project)
		if err != nil {
			logger.Errorf(ctx, "doRefreshLocItem GetItems err:%v", err)
			continue
		}
		for _, row := range list {
			if _, ok := locItemMaps[row.Project]; !ok {
				locItemMaps[row.Project] = &sync.Map{}
			}
			locItemMaps[row.Project].Store(row.ItemID, row.ItemName)
		}
	}
}
func doRefreshLocI18n(ctx context.Context) {
	for project := range locI18nEventChan {
		list, err := persistence.NewDataPlatGameItems().GetI18n(ctx, project)
		if err != nil {
			logger.Errorf(ctx, "doRefreshLocI18n GetI18n err:%v", err)
			continue
		}
		for _, row := range list {
			if _, ok := locI18nMaps[row.Project]; !ok {
				locI18nMaps[row.Project] = &sync.Map{}
			}
			locI18nMaps[row.Project].Store(row.UniqueKey, row.ShowMsg)
		}
	}
}

func getItemName(project, itemId string) string {
	if mp, ok := locItemMaps[project]; ok {
		if v, ok := mp.Load(itemId); ok {
			return v.(string)
		}
	}
	return itemId
}
func getI18nShow(project, uniqueKey string) string {
	if mp, ok := locI18nMaps[project]; ok {
		if v, ok := mp.Load(uniqueKey); ok {
			return v.(string)
		}
	}
	return uniqueKey
}
