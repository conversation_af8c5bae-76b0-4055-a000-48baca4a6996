package dataremote

import (
	"context"
	"github.com/spf13/viper"
	"os"
)

var (
	dataPlatInterfaceMoUrl = viper.GetString("thirdparty.ops_backend_api.data_plat_server_host")
	env                    = os.Getenv("environment")
)

func isLocal() bool {
	return env == "local"
}

type (
	userGoldInfoImp  func(ctx context.Context, def *GoldInfoReqDef) ([]*GoldInfoResultDtDef, error)
	userItemInfoImp  func(ctx context.Context, def *ItemInfoReqDef) ([]*ItemInfoResultDtDef, error)
	userPayInfoImp   func(ctx context.Context, def *PayInfoReqDef) ([]*PayInfoResultDtDef, error)
	userLoginInfoImp func(ctx context.Context, def *LoginInfoReqDef) ([]*LoginInfoResultDtDef, error)

	// 金币查询 参数映射
	GoldInfoReqDef struct {
		Start   string `json:"start"`
		End     string `json:"end"`
		Uid     uint64 `json:"uid"`
		Project string `json:"project"`
	}
	GoldInfoResultDtDef struct {
		Uid       string `json:"uid"`
		AccountId string `json:"account_id"`
		Action    string `json:"action"`
		Reason    string `json:"reason"`
		Change    string `json:"change"`
		Before    string `json:"before"`
		After     string `json:"after"`
		EventTime string `json:"event_time"`
	}

	// 道具查询 参数映射
	ItemInfoReqDef struct {
		Start   string `json:"start"`
		End     string `json:"end"`
		Uid     uint64 `json:"uid"`
		ItemId  string `json:"item_id"`
		Project string `json:"project"`
	}
	ItemInfoResultDtDef struct {
		Uid       string `json:"uid"`
		AccountId string `json:"account_id"`
		ItemId    string `json:"item_id"`
		ItemName  string `json:"item_name"`
		Action    string `json:"action"`
		Reason    string `json:"reason"`
		Change    string `json:"change"`
		Before    string `json:"before"`
		After     string `json:"after"`
		EventTime string `json:"event_time"`
	}

	// 支付查询 参数映射
	PayInfoReqDef struct {
		Start   string `json:"start"`
		End     string `json:"end"`
		Uid     uint64 `json:"uid"`
		Project string `json:"project"`
	}
	PayInfoResultDtDef struct {
		AllReward        string `json:"all_reward"`        // 奖励信息 - 备注
		BasePrice        string `json:"base_price"`        // 基础价格
		Currency         string `json:"currency"`          // 付款 - 币种
		ProductName      string `json:"product_name"`      // 产品名称
		PaymentProcessor string `json:"payment_processor"` // 付款 - 支付平台
		Price            string `json:"price"`             // 价格
		PriceUsd         string `json:"price_usd"`         // 价格 - 美元
		Result           string `json:"result"`            // 付款 - 结果
		EventTime        string `json:"event_time"`        // 订单时间
		FinishTime       string `json:"finish_time"`       // 完成时间
	}

	// 登录信息查询 参数映射
	LoginInfoReqDef struct {
		Start   string `json:"start"`
		End     string `json:"end"`
		Uid     uint64 `json:"uid"`
		Project string `json:"project"`
	}
	LoginInfoResultDtDef struct {
		Uid        string `json:"uid"`
		AccountId  string `json:"account_id"`
		Country    string `json:"country"`     // 国家
		DeviceType string `json:"device_type"` // 设备类型
		DeviceId   string `json:"device_id"`   // 设备标识
		Ip         string `json:"ip"`          // ip 地址
		IpCountry  string `json:"ip_country"`  // ip 地址 - 国家
		IpCity     string `json:"ip_city"`     // ip 地址 - 城市
		EventTime  string `json:"event_time"`  // 订单时间
	}
)

func GetGameNameFromProject(project string) string {
	var gameNameMap = map[string]string{
		"mo_global":      "mo",
		"entropy_global": "zday",
	}
	if gameName, ok := gameNameMap[project]; ok {
		return gameName
	}
	return project
}

// GetUserGoldInfoF 获取游戏内玩家金币变动
func GetUserGoldInfoF(ctx context.Context, project string, req *GoldInfoReqDef) userGoldInfoImp {
	switch project {
	case "mo_global": // mo_logic
		return moGetUserGoldInfo
	case "entropy_global": //
		return entropyGetUserGoldInfo
	default:
		return emptyGetUserGoldInfo
	}
}

// GetUserItemInfoF 获取游戏内玩家道具变动
func GetUserItemInfoF(ctx context.Context, project string, req *ItemInfoReqDef) userItemInfoImp {
	switch project {
	case "mo_global": // mo_logic
		return moGetUserItemInfo
	case "entropy_global":
		return entropyGetUserItemInfo
	default:
		return emptyGetUserItemInfo
	}
}

func GetUserPayInfoF(ctx context.Context, project string, req *PayInfoReqDef) userPayInfoImp {
	switch project {
	case "mo_global": // mo_logic
		return moGetUserPayInfo
	case "entropy_global":
		return entropyGetUserPayInfo
	default:
		return emptyGetUserPayInfo
	}
}

func GetUserLoginInfoF(ctx context.Context, project string, req *LoginInfoReqDef) userLoginInfoImp {
	switch project {
	case "mo_global": // mo_logic
		return moGetUserLoginInfo
	case "entropy_global":
		return entropyGetUserLoginInfo
	default:
		return emptyGetUserLoginInfo
	}
}
