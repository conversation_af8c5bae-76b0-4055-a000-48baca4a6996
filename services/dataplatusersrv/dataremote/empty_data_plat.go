package dataremote

import (
	"context"
)

func emptyGetUserGoldInfo(ctx context.Context, req *GoldInfoReqDef) ([]*GoldInfoResultDtDef, error) {
	return nil, nil
}

func emptyGetUserItemInfo(ctx context.Context, req *ItemInfoReqDef) ([]*ItemInfoResultDtDef, error) {
	return nil, nil
}

func emptyGetUserPayInfo(ctx context.Context, req *PayInfoReqDef) ([]*PayInfoResultDtDef, error) {
	return nil, nil
}

func emptyGetUserLoginInfo(ctx context.Context, req *LoginInfoReqDef) ([]*LoginInfoResultDtDef, error) {
	return nil, nil
}
