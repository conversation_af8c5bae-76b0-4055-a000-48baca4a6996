package dataremote

import (
	"context"
	"fmt"
	"github.com/spf13/viper"
	"github.com/tidwall/gjson"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"gitlab-ee.funplus.io/ops-tools/compkg/httpclient"
	"net/http"
	"sync"
	"time"
)

func moGetUserGoldInfo(ctx context.Context, req *GoldInfoReqDef) ([]*GoldInfoResultDtDef, error) {
	var (
		dataDetail []*GoldInfoResultDtDef
		cli        = httpclient.New()
		actionsMap = map[string]string{}
		actions    = []string{}
	)

	var query = map[string]interface{}{
		"start":     req.Start,
		"end":       req.End,
		"uid":       req.Uid,
		"game_name": GetGameNameFromProject(req.Project),
	}
	cli.SetTimeout(time.Minute)
	if isLocal() {
		cli.SetDebug(true)
	}

	res, err := cli.Get(ctx, fmt.Sprintf("%s/v1/api/get_gold_info", dataPlatInterfaceMoUrl), query)
	if err != nil {
		return nil, err
	}
	if res.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("http status code is not 200. status code: %d", res.StatusCode)
	}
	resBody := res.String()
	//fmt.Println(resBody)
	for _, row := range gjson.Parse(resBody).Get("data").Array() {
		rowDt := row.Map()
		action := rowDt["action"].String()
		change := rowDt["change"].String()
		consumeType := rowDt["consume_type"].String()
		if consumeType == "spent" {
			change = "-" + change
		} else if consumeType == "received" {
			change = "+" + change
		}
		dataDetail = append(dataDetail, &GoldInfoResultDtDef{
			Uid:       rowDt["uid"].String(),
			AccountId: rowDt["fpid"].String(),
			Action:    action,
			Reason:    action,
			Change:    change,
			Before:    rowDt["before"].String(),
			After:     rowDt["after"].String(),
			EventTime: rowDt["event_time"].String(),
		})
		if _, ok := actionsMap[action]; !ok && action != "" {
			actionsMap[action] = action
			actions = append(actions, action)
		}
	}
	// 回填 action -> reason
	for _, row := range dataDetail {
		if row.Action != "" {
			row.Reason = getI18nShow(req.Project, row.Action)
		}
	}
	return dataDetail, nil
}

func moGetUserItemInfo(ctx context.Context, req *ItemInfoReqDef) ([]*ItemInfoResultDtDef, error) {
	var (
		dataDetail []*ItemInfoResultDtDef
		cli        = httpclient.New()
		actionsMap = map[string]string{}
		actions    = []string{}
	)

	var query = map[string]interface{}{
		"start":     req.Start,
		"end":       req.End,
		"uid":       req.Uid,
		"game_name": GetGameNameFromProject(req.Project),
	}
	if req.ItemId != "" {
		query["item_id"] = req.ItemId
	}
	cli.SetTimeout(time.Minute)
	if isLocal() {
		cli.SetDebug(true)
	}

	res, err := cli.Get(ctx, fmt.Sprintf("%s/v1/api/get_item_info", dataPlatInterfaceMoUrl), query)
	if err != nil {
		return nil, err
	}
	if res.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("http status code is not 200. status code: %d", res.StatusCode)
	}
	resBody := res.String()
	//fmt.Println(fmt.Sprintf("%s/v1/api/get_item_info", dataPlatInterfaceMoUrl), query, resBody)
	for _, row := range gjson.Parse(resBody).Get("data").Array() {
		rowDt := row.Map()
		action := rowDt["action"].String()
		change := rowDt["change"].String()
		consumeType := rowDt["consume_type"].String()
		if consumeType == "spent" {
			change = "-" + change
		} else if consumeType == "received" {
			change = "+" + change
		}
		dataDetail = append(dataDetail, &ItemInfoResultDtDef{
			Uid:       rowDt["uid"].String(),
			AccountId: rowDt["fpid"].String(),
			ItemId:    rowDt["item_id"].String(),
			ItemName:  rowDt["item_id"].String(),
			Action:    action,
			Reason:    action,
			Change:    change,
			Before:    rowDt["before"].String(),
			After:     rowDt["after"].String(),
			EventTime: rowDt["event_time"].String(),
		})
		if _, ok := actionsMap[action]; !ok && action != "" {
			actionsMap[action] = action
			actions = append(actions, action)
		}
	}
	// 回填 action -> reason && 回填 item_id -> item_name
	for _, row := range dataDetail {
		if row.Action != "" {
			row.Reason = getI18nShow(req.Project, row.Action)
		}
		if row.ItemId != "" {
			row.ItemName = getItemName(req.Project, row.ItemId)
		}
	}
	return dataDetail, nil
}

func moGetUserPayInfo(ctx context.Context, req *PayInfoReqDef) ([]*PayInfoResultDtDef, error) {
	var (
		dataDetail []*PayInfoResultDtDef
		cli        = httpclient.New()
	)

	var query = map[string]interface{}{
		"start":     req.Start,
		"end":       req.End,
		"uid":       req.Uid,
		"game_name": GetGameNameFromProject(req.Project),
	}
	cli.SetTimeout(time.Minute)
	if isLocal() {
		cli.SetDebug(true)
	}

	res, err := cli.Get(ctx, fmt.Sprintf("%s/v1/api/get_pay_info", dataPlatInterfaceMoUrl), query)
	if err != nil {
		return nil, err
	}
	if res.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("http status code is not 200. status code: %d", res.StatusCode)
	}
	resBody := res.String()

	for _, row := range gjson.Parse(resBody).Get("data").Array() {
		rowDt := row.Map()
		dataDetail = append(dataDetail, &PayInfoResultDtDef{
			AllReward:        rowDt["all_reward"].String(),
			BasePrice:        rowDt["base_price"].String(),
			Currency:         rowDt["currency"].String(),
			ProductName:      rowDt["iap_product_name"].String(),
			PaymentProcessor: rowDt["payment_processor"].String(),
			Price:            rowDt["price"].String(),
			Result:           rowDt["result"].String(),
			EventTime:        rowDt["event_time"].String(),
			FinishTime:       rowDt["finish_time"].String(),
		})
	}
	return dataDetail, nil
}

func moGetUserLoginInfo(ctx context.Context, req *LoginInfoReqDef) ([]*LoginInfoResultDtDef, error) {
	var (
		dataDetail []*LoginInfoResultDtDef
		cli        = httpclient.New()
		ips        = map[string]*ip2LocDt{}
	)

	var query = map[string]interface{}{
		"start":  req.Start,
		"end":    req.End,
		"uid":    req.Uid,
		"game":   GetGameNameFromProject(req.Project),
		"limit":  2000,
		"offset": 0,
	}
	cli.SetTimeout(time.Minute)
	if isLocal() {
		cli.SetDebug(true)
	}

	res, err := cli.Get(ctx, fmt.Sprintf("%s/v1/api/getLoginInfoByUidV2Db", dataPlatInterfaceMoUrl), query)
	if err != nil {
		return nil, err
	}
	if res.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("http status code is not 200. status code: %d", res.StatusCode)
	}
	resBody := res.String()

	for _, row := range gjson.Parse(resBody).Get("data").Array() {
		rowDt := row.Map()
		ip := rowDt["IP"].String()
		dataDetail = append(dataDetail, &LoginInfoResultDtDef{
			Uid:        rowDt["UID"].String(),
			AccountId:  rowDt["FPID"].String(),
			Country:    rowDt["COUNTRY"].String(),
			DeviceType: rowDt["DEVICE_TYPE"].String(),
			DeviceId:   rowDt["FP_DEVICE_ID"].String(),
			Ip:         ip,
			EventTime:  rowDt["EVENT_TIME"].String(),
		})
		if _, ok := ips[ip]; !ok {
			ips[ip] = nil
		}
	}
	// ip 2 loc
	wg := sync.WaitGroup{}
	var i int
	for ip := range ips {
		if i > 100 { // 单用户基本不会超过 100 个 ip； 保护下游服务
			continue
		}
		_ip := ip
		wg.Add(1)
		go func(_innerIp string) {
			defer wg.Done()
			loc, err := getIp2Loc(ctx, _innerIp)
			if err != nil {
				logger.Errorf(ctx, "get ip2loc failed. ip: %s, err: %v", _innerIp, err)
				return
			}
			ips[_innerIp] = loc
		}(_ip)
		i++
	}
	wg.Wait()
	for i, v := range dataDetail {
		if loc, ok := ips[v.Ip]; ok && loc != nil {
			dataDetail[i].IpCountry = loc.CountryCode
			dataDetail[i].IpCity = loc.City
		}
	}
	return dataDetail, nil
}

type ip2LocDt struct {
	CountryCode string `json:"country_code"`
	CountryName string `json:"country_name"`
	City        string `json:"city"`
}

func getIp2Loc(ctx context.Context, ip string) (*ip2LocDt, error) {
	ip2localUrl := viper.GetString("thirdparty.ip2loc_url")
	if ip2localUrl == "" {
		return nil, nil
	}
	cli := httpclient.New()
	cli.SetTimeout(time.Second * 5)
	if isLocal() {
		cli.SetDebug(true)
	}
	queryBody := map[string]interface{}{
		"ip":      ip,
		"project": "ss-community",
		"token":   "525779b81ecf3c7c60631094850fc663",
	}
	res, err := cli.PostJson(ctx, ip2localUrl, queryBody)
	if err != nil {
		return nil, err
	}
	if res.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("http status code is not 200. status code: %d", res.StatusCode)
	}
	resBody := res.String()

	data := gjson.Parse(resBody).Get("data").Map()
	return &ip2LocDt{
		CountryCode: data["country_code"].String(),
		CountryName: data["country_name"].String(),
		City:        data["city_name"].String(),
	}, nil
}
