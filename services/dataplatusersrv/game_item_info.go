package dataplatusersrv

import (
	"fmt"
	"github.com/labstack/echo/v4"
	"github.com/spf13/cast"
	"ops-ticket-api/internal/cst"
	"ops-ticket-api/internal/persistence"
	"ops-ticket-api/models"
	"ops-ticket-api/proto/pb"
	"ops-ticket-api/services/communicate"
	"ops-ticket-api/utils"
	"strings"
	"time"
)

func GameItemList(ctx echo.Context, param *pb.DataPlatGameItemListReq) (*pb.DataPlatGameItemListResp, error) {
	return persistence.NewDataPlatGameItems().GetItemList(ctx, param)
}

func GameItemOpts(ctx echo.Context, project string) ([]*pb.DataPlatGameItemOptsResp_Item, error) {
	list, err := persistence.NewDataPlatGameItems().GetItemOpts(ctx, project)
	if err != nil {
		return nil, err
	}
	var res = make([]*pb.DataPlatGameItemOptsResp_Item, 0, len(list))
	for _, item := range list {
		res = append(res, &pb.DataPlatGameItemOptsResp_Item{
			Value: item.ItemID,
			Label: item.ItemName + " - " + item.ItemID,
		})
	}
	return res, nil
}
func GameItemBatchSave(ctx echo.Context, param *pb.DataPlatGameItemBatchSaveReq) error {
	items, err := readGameItemFileDetail(ctx, param)
	if err != nil {
		return err
	}
	if len(items) > 5000 {
		return fmt.Errorf("单次导入最多 5000条数据, 当前导入数据量：%d", len(items))
	}
	for _, item := range items {
		if err := persistence.NewDataPlatGameItems().UpsertGameItem(ctx, item); err != nil {
			return err
		}
	}
	// refresh local cache
	communicate.PublicDataPlatItemI18nChange(ctx.Request().Context(), communicate.DataPlatPubTpItem, param.Project)
	return nil
}

func readGameItemFileDetail(ctx echo.Context, param *pb.DataPlatGameItemBatchSaveReq) ([]*models.FpDataplatGameItem, error) {
	var (
		fun      = "readGameItemFileDetail"
		header   = []string{"道具ID", "道具描述"}
		has      = map[string]struct{}{}
		items    []*models.FpDataplatGameItem
		operator = cast.ToString(ctx.Get(cst.AccountInfoCtx))
	)
	details, err := utils.ReadExcelOnline(param.FileName)
	if err != nil {
		return nil, err
	}
	if len(details) < 2 {
		return nil, fmt.Errorf("%s FileParse:excel file is empty", fun)
	}
	if len(details[0]) < 2 {
		return nil, fmt.Errorf("%s FileParse:excel file row[0] unexpect:%v", fun, details[0])
	}
	if details[0][0] != header[0] || details[0][1] != header[1] {
		return nil, fmt.Errorf("%s FileParse:excel file header unexpect2 :%v. expect:%v", fun, details[0], header)
	}
	for _, row := range details[1:] {
		if len(row) < 2 {
			continue
		}
		id := strings.TrimLeft(strings.TrimRight(strings.TrimSpace(row[0]), "\""), "\"")
		name := strings.TrimSpace(row[1])
		if id == "" || name == "" {
			continue
		}
		if _, ok := has[id]; ok {
			continue
		}
		has[id] = struct{}{}
		items = append(items, &models.FpDataplatGameItem{
			Project:    param.Project,
			ItemID:     id,
			ItemName:   name,
			Operator:   operator,
			CreateTime: time.Now(),
			UpdateTime: time.Now(),
		})
	}
	return items, nil
}
