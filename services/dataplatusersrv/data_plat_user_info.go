package dataplatusersrv

import (
	"github.com/labstack/echo/v4"
	"ops-ticket-api/proto/pb"
	"ops-ticket-api/services/dataplatusersrv/dataremote"
)

func GoldInfo(ctx echo.Context, param *pb.DataPlatUserGoldInfoReq) ([]*pb.DataPlatUserGoldInfoResp, error) {
	var query = &dataremote.GoldInfoReqDef{
		Start:   param.CreatedAt[0],
		End:     param.CreatedAt[1],
		Uid:     param.Uid,
		Project: param.GetProject(),
	}
	result, err := dataremote.GetUserGoldInfoF(ctx.Request().Context(), param.GetProject(), query)(ctx.Request().Context(), query)
	if err != nil {
		return nil, err
	}
	var resp []*pb.DataPlatUserGoldInfoResp
	for _, row := range result {
		resp = append(resp, &pb.DataPlatUserGoldInfoResp{
			Project:    param.Project,
			Change:     row.Change,
			Before:     row.Before,
			After:      row.After,
			ChangeTime: row.EventTime,
			Reason:     row.Reason,
		})
	}
	return resp, nil
}

func ItemInfo(ctx echo.Context, param *pb.DataPlatUserItemInfoReq) ([]*pb.DataPlatUserItemInfoResp, error) {
	var query = &dataremote.ItemInfoReqDef{
		Start:   param.CreatedAt[0],
		End:     param.CreatedAt[1],
		Uid:     param.Uid,
		ItemId:  param.ItemId,
		Project: param.GetProject(),
	}
	result, err := dataremote.GetUserItemInfoF(ctx.Request().Context(), param.GetProject(), query)(ctx.Request().Context(), query)
	if err != nil {
		return nil, err
	}
	var resp []*pb.DataPlatUserItemInfoResp
	for _, row := range result {
		resp = append(resp, &pb.DataPlatUserItemInfoResp{
			Project:    param.Project,
			ItemId:     row.ItemId,
			ItemName:   row.ItemName,
			Change:     row.Change,
			Before:     row.Before,
			After:      row.After,
			ChangeTime: row.EventTime,
			Reason:     row.Reason,
		})
	}
	return resp, nil
}

func PayInfo(ctx echo.Context, param *pb.DataPlatUserPayInfoReq) ([]*pb.DataPlatUserPayInfoResp, error) {
	var query = &dataremote.PayInfoReqDef{
		Start:   param.CreatedAt[0],
		End:     param.CreatedAt[1],
		Uid:     param.Uid,
		Project: param.GetProject(),
	}
	result, err := dataremote.GetUserPayInfoF(ctx.Request().Context(), param.GetProject(), query)(ctx.Request().Context(), query)
	if err != nil {
		return nil, err
	}
	var resp []*pb.DataPlatUserPayInfoResp
	for _, row := range result {
		resp = append(resp, &pb.DataPlatUserPayInfoResp{
			Project:     param.Project,
			ProductName: row.ProductName,
			PayChannel:  row.PaymentProcessor,
			Price:       row.Price,
			BasicPrice:  row.BasePrice,
			Currency:    row.Currency,
			Status:      row.Result,
			PaidAt:      row.FinishTime,
		})
	}
	return resp, nil
}

func LoginInfo(ctx echo.Context, param *pb.DataPlatUserLoginInfoReq) ([]*pb.DataPlatUserLoginInfoResp, error) {
	var query = &dataremote.LoginInfoReqDef{
		Start:   param.CreatedAt[0],
		End:     param.CreatedAt[1],
		Uid:     param.Uid,
		Project: param.GetProject(),
	}
	result, err := dataremote.GetUserLoginInfoF(ctx.Request().Context(), param.GetProject(), query)(ctx.Request().Context(), query)
	if err != nil {
		return nil, err
	}
	var resp []*pb.DataPlatUserLoginInfoResp
	for _, row := range result {
		resp = append(resp, &pb.DataPlatUserLoginInfoResp{
			Project:    param.Project,
			FpDeviceId: row.DeviceId,
			DeviceType: row.DeviceType,
			Ip:         row.Ip,
			IpLoc:      row.IpCountry + " - " + row.IpCity,
			LoginAt:    row.EventTime,
		})
	}
	return resp, nil
}
