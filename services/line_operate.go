package services

import (
	"context"
	"fmt"
	"github.com/google/uuid"
	"github.com/labstack/echo/v4"
	"github.com/spf13/cast"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"ops-ticket-api/internal/code"
	"ops-ticket-api/internal/cst"
	"ops-ticket-api/internal/elasticsearch"
	"ops-ticket-api/internal/framework/stores/s3"
	"ops-ticket-api/internal/persistence"
	"ops-ticket-api/models"
	"ops-ticket-api/proto/pb"
	"ops-ticket-api/utils"
	"strings"
	"sync"
	"time"
)

func NewLineSrv() *LineService {
	return &LineService{}
}

type LineService struct {
}

func (srv *LineService) GetUserPool(ctx echo.Context, param *pb.LineUserListReq) ([]*pb.LineUserListResp_LineUser, uint32, error) {
	dest, total, err := LinePool(ctx, param)
	if err != nil {
		return nil, 0, err
	}
	res := make([]*pb.LineUserListResp_LineUser, len(dest))
	for i := range dest {
		user := dest[i]
		dscUser := &pb.LineUserListResp_LineUser{
			Project:           user.Project,
			LineUserId:        user.LineUserId,
			DisplayName:       user.DisplayName,
			ChannelId:         user.ChannelId,
			ChannelShow:       user.ChannelShow,
			Maintainer:        user.Maintainer,
			Uid:               user.Uid,
			Sid:               user.Sid,
			LastLogin:         user.LastLogin,
			TotalPay:          utils.PayAmountToFloat64(user.PayAll),
			PayLastThirtyDays: utils.PayAmountToFloat64(user.PayLastThirtyDays),
			VipLevel:          user.VipLevel,
			Status:            user.Status,
			Fpid:              user.AccountId,
			BotId:             user.BotId,
			Note:              user.Note,
			PlayerNick:        user.PlayerNick,
			Birthday:          user.Birthday,
			Lang:              user.Lang,
			Checked:           false,
			FollowStatus:      user.FollowStatus,
		}
		res[i] = dscUser
	}
	return res, total, nil
}

func (srv *LineService) SendTextMessage(ctx echo.Context, param *pb.LineSendTextMessageReq) (*pb.LineSendMessageResp, error) {
	// 创建带超时的上下文, 设置60秒超时
	timeoutCtx, cancel := context.WithTimeout(context.Background(), 60*time.Second)
	// 确保超时后取消
	defer cancel()
	// 1 通过channel_id获取频道的access_token
	channel, err := persistence.NewLineInteractions().FetchChanelById(timeoutCtx, param.ChannelId)
	if err != nil {
		return nil, err
	}
	// 2 构建消息体
	var message interface{}
	if len(param.Emojis) == 0 {
		message = utils.NewTextMessage(param.Content, nil)
	} else {
		emojis := make([]utils.Emoji, len(param.Emojis))
		for i, emoji := range param.Emojis {
			emojis[i] = utils.Emoji{
				Index:     int(emoji.Index),
				Length:    int(emoji.Length),
				ProductID: emoji.ProductId,
				EmojiID:   emoji.EmojiId,
			}
		}
		message = utils.NewTextMessage(param.Content, emojis)
	}
	messages := []interface{}{
		message,
	}
	// 3 发送消息
	resp, err := utils.LinePushMessage(param.LineUserId, channel.AccessToken, messages)
	if err != nil {
		return nil, err
	}
	// 4 写入消息记录表
	if len(resp.SentMessages) > 0 {
		operator := cast.ToString(ctx.Get(cst.AccountInfoCtx))
		respInfo := resp.SentMessages[0]
		msgId, quoteMsgToken := respInfo.ID, respInfo.QuoteToken
		content := ""
		eventMessage := &utils.Message{
			Type: "text",
			Text: param.Content,
		}
		if len(param.Emojis) > 0 {
			emojis := make([]utils.Emoji, len(param.Emojis))
			for i, emoji := range param.Emojis {
				emojis[i] = utils.Emoji{
					Index:     int(emoji.Index),
					Length:    int(emoji.Length),
					ProductID: emoji.ProductId,
					EmojiID:   emoji.EmojiId,
				}
			}
			eventMessage.Emojis = emojis
			content = utils.ReconstructMessage(eventMessage)
		} else {
			content = eventMessage.Text
		}
		// 写入消息记录表
		record := &models.FpLineCommu{
			MsgID:       msgId,
			Project:     channel.Project,
			FromUserID:  param.BotId,
			BotID:       param.BotId,
			LineUserID:  param.LineUserId,
			ChannelID:   param.ChannelId,
			Content:     content,
			QuoteToken:  quoteMsgToken,
			MessageType: code.LineMessageTypeText,
			TickTime:    utils.NowTimestampMill(),
			CreatedAt:   time.Now().UTC(),
			UpdatedAt:   time.Now().UTC(),
		}
		// 写入客服绑定消息记录表
		send := &models.FpLineSend{
			MsgID:     msgId,
			BotID:     param.BotId,
			ChannelID: channel.ChannelID,
			Operator:  operator,
			CreatedAt: time.Now().UTC(),
			UpdatedAt: time.Now().UTC(),
		}
		// 记录操作日志
		opDetail := &models.FpLineOperateLog{
			OperationGroup:  pb.OpGroup_OpGroupSendTextMessage.String(),
			OperationAction: pb.OpAction_OpActionAdd.String(),
			BaseID:          msgId,
			UniqueID:        uuid.New().String(),
			Project:         channel.Project,
			BeforeDetail:    "{}",
			AfterDetail:     utils.ToJson(send),
			CreateTime:      time.Now(),
			Operator:        operator,
		}
		if err = persistence.NewLineInteractions().SaveBotCommu(timeoutCtx, record, send, opDetail); err != nil {
			return nil, err
		}
		sendMsgResp := &pb.LineSendMessageResp{
			MsgId:     msgId,
			ChannelId: param.ChannelId,
		}
		return sendMsgResp, nil
	}
	return nil, fmt.Errorf("line push message err:%v", resp.SentMessages)
}

func (srv *LineService) LineSendFile(ctx echo.Context, duration int64, fileName, operator string, originalFileContent, previewFileContent []byte, param *pb.LineSendFileReq) (*pb.LineSendMessageResp, error) {
	// 创建带超时的上下文, 设置60秒超时
	timeoutCtx, cancel := context.WithTimeout(context.Background(), 60*time.Second)
	// 确保超时后取消
	defer cancel()
	originalContentURL, err := s3.Defaults3Sess.Upload(timeoutCtx, originalFileContent, "tickets/"+fileName)
	if err != nil {
		return nil, err
	}
	previewImageURL := ""
	var e error
	if len(previewFileContent) > 0 {
		tempFileName := strings.Split(fileName, ",")[0]
		previewFileName := fmt.Sprintf("%s_preview.jpeg", tempFileName)
		previewImageURL, e = s3.Defaults3Sess.Upload(timeoutCtx, previewFileContent, "tickets/"+previewFileName)
		if e != nil {
			return nil, e
		}
	}
	// 1 通过channel_id获取频道的access_token
	channel, err := persistence.NewLineInteractions().FetchChanelById(timeoutCtx, param.ChannelId)
	if err != nil {
		return nil, err
	}
	// 2 构建消息体
	var message interface{}
	switch param.FileType {
	case "image":
		message = utils.NewImageMessage(originalContentURL, previewImageURL)
	case "video":
		message = utils.NewVideoMessage(originalContentURL, previewImageURL)
	case "audio":
		message = utils.NewAudioMessage(originalContentURL, int(duration))
	}
	messages := []interface{}{
		message,
	}
	// 3 发送消息
	resp, err := utils.LinePushMessage(param.LineUserId, channel.AccessToken, messages)
	if err != nil {
		return nil, err
	}
	// 4 写入消息记录表
	if len(resp.SentMessages) > 0 {
		respInfo := resp.SentMessages[0]
		msgId, quoteMsgToken := respInfo.ID, respInfo.QuoteToken
		// 写入消息记录表
		record := &models.FpLineCommu{
			MsgID:      msgId,
			Project:    channel.Project,
			FromUserID: param.BotId,
			BotID:      param.BotId,
			LineUserID: param.LineUserId,
			ChannelID:  param.ChannelId,
			QuoteToken: quoteMsgToken,
			TickTime:   utils.NowTimestampMill(),
			CreatedAt:  time.Now().UTC(),
			UpdatedAt:  time.Now().UTC(),
		}
		switch param.FileType {
		case "image":
			record.MessageType = code.LineMessageTypeImage
			record.OriginalContentUrl = originalContentURL
			record.PreviewImageUrl = previewImageURL
		case "video":
			record.MessageType = code.LineMessageTypeVideo
			record.OriginalContentUrl = originalContentURL
			record.PreviewImageUrl = previewImageURL
		case "audio":
			record.MessageType = code.LineMessageTypeAudio
			record.OriginalContentUrl = originalContentURL
			record.Duration = uint64(duration)
		}
		// 写入客服绑定消息记录表
		send := &models.FpLineSend{
			MsgID:     msgId,
			BotID:     param.BotId,
			ChannelID: channel.ChannelID,
			Operator:  operator,
			CreatedAt: time.Now().UTC(),
			UpdatedAt: time.Now().UTC(),
		}
		// 记录操作日志
		opDetail := &models.FpLineOperateLog{
			OperationGroup:  pb.OpGroup_OpGroupSendFile.String(),
			OperationAction: pb.OpAction_OpActionAdd.String(),
			BaseID:          msgId,
			UniqueID:        uuid.New().String(),
			Project:         channel.Project,
			BeforeDetail:    "{}",
			AfterDetail:     utils.ToJson(send),
			CreateTime:      time.Now(),
			Operator:        operator,
		}
		if err = persistence.NewLineInteractions().SaveBotCommu(timeoutCtx, record, send, opDetail); err != nil {
			return nil, err
		}
		sendMsgResp := &pb.LineSendMessageResp{
			MsgId:     msgId,
			ChannelId: param.ChannelId,
		}
		return sendMsgResp, nil
	}
	return nil, fmt.Errorf("line push message err:%v", resp.SentMessages)
}

func (srv *LineService) DialogueHistory(ctx echo.Context, req *pb.LineDialogueHistoryReq) (*pb.LineDialogueHistoryResp, error) {
	// 创建带超时的上下文, 设置60秒超时
	timeoutCtx, cancel := context.WithTimeout(context.Background(), 60*time.Second)
	// 确保超时后取消
	defer cancel()
	total, commus, err := persistence.NewLineInteractions().GetLineCommusHistory(timeoutCtx, req)
	if err != nil {
		return nil, err
	}
	msgIds := make([]string, len(commus))
	lineUserIds := []string{}
	mark := make(map[string]bool)
	for i, commu := range commus {
		msgIds[i] = commu.MsgID
		if !mark[commu.LineUserID] {
			lineUserIds = append(lineUserIds, commu.LineUserID)
			mark[commu.LineUserID] = true
		}
	}
	var senderErr, playerErr error
	senderMap, playerMap := make(map[string]string), make(map[string]string)
	wg := &sync.WaitGroup{}
	wg.Add(2)
	go func() {
		defer func() {
			if err := recover(); err != nil {
				logger.Errorf(timeoutCtx, "DialogueHistory.recover err:%v", err)
			}
		}()
		defer wg.Done()
		senderMap, senderErr = persistence.NewLineInteractions().GetSendersByMsdId(timeoutCtx, msgIds)
	}()
	go func() {
		defer func() {
			if err := recover(); err != nil {
				logger.Errorf(timeoutCtx, "DialogueHistory.recover err:%v", err)
			}
		}()
		defer wg.Done()
		playerMap, playerErr = persistence.NewLineInteractions().GetPlayerDisplayNameByLineUserId(timeoutCtx, lineUserIds)
	}()
	wg.Wait()

	if senderErr != nil || playerErr != nil {
		return nil, fmt.Errorf("senderErr :%v, playerErr:%v", senderErr, playerErr)
	}
	resp := new(pb.LineDialogueHistoryResp)
	resp.CurrentPage = req.Page
	resp.PerPage = req.PageSize
	resp.Total = uint32(total)
	data := make([]*pb.LineDialogueHistoryResp_Dialogue, len(commus))
	for i, commu := range commus {
		messageType := ""
		switch commu.MessageType {
		case code.LineMessageTypeText:
			messageType = "text"
		case code.LineMessageTypeSticker:
			messageType = "sticker"
		case code.LineMessageTypeImage:
			messageType = "image"
		case code.LineMessageTypeVideo:
			messageType = "video"
		case code.LineMessageTypeAudio:
			messageType = "audio"
		case code.LineMessageTypeFile:
			messageType = "file"
		case code.LineMessageTypeLocation:
			messageType = "location"
		default:
			messageType = "others"
		}
		messageFrom := "player"
		if commu.FromUserID == commu.BotID {
			messageFrom = "service"
		}
		sender := senderMap[commu.MsgID]
		displayName := playerMap[commu.LineUserID]
		data[i] = &pb.LineDialogueHistoryResp_Dialogue{
			MessageId:          commu.MsgID,
			BotId:              commu.BotID,
			LineUserId:         commu.LineUserID,
			DisplayName:        displayName,
			ChannelId:          commu.ChannelID,
			MessageFrom:        messageFrom,
			Sender:             sender,
			MessageType:        messageType,
			QuoteToken:         commu.QuoteToken,
			Content:            commu.Content,
			Revoke:             commu.Revoke,
			OriginalContentUrl: commu.OriginalContentUrl,
			PreviewImageUrl:    commu.PreviewImageUrl,
			Duration:           int32(commu.Duration),
			StickerInfo:        commu.StickerInfo,
			Location:           commu.Location,
			TickTime:           commu.TickTime,
			CreateTime:         utils.TransferTimeToString(commu.CreatedAt),
		}
	}
	resp.Data = data
	return resp, nil
}

func (srv *LineService) DialogueRefresh(ctx echo.Context, req *pb.LineDialogFreshReq) ([]*pb.LineDialogueHistoryResp_Dialogue, error) {
	// 创建带超时的上下文, 设置60秒超时
	timeoutCtx, cancel := context.WithTimeout(context.Background(), 60*time.Second)
	// 确保超时后取消
	defer cancel()
	commus, err := persistence.NewLineInteractions().GetLineRefreshCommus(timeoutCtx, req)
	if err != nil {
		return nil, err
	}
	msgIds := make([]string, len(commus))
	lineUserIds := []string{}
	mark := make(map[string]bool)
	for i, commu := range commus {
		msgIds[i] = commu.MsgID
		if !mark[commu.LineUserID] {
			lineUserIds = append(lineUserIds, commu.LineUserID)
			mark[commu.LineUserID] = true
		}
	}
	var senderErr, playerErr error
	senderMap, playerMap := make(map[string]string), make(map[string]string)
	wg := &sync.WaitGroup{}
	wg.Add(2)
	go func() {
		defer func() {
			if err := recover(); err != nil {
				logger.Errorf(timeoutCtx, "DialogueHistory.recover err:%v", err)
			}
		}()
		defer wg.Done()
		senderMap, senderErr = persistence.NewLineInteractions().GetSendersByMsdId(timeoutCtx, msgIds)
	}()
	go func() {
		defer func() {
			if err := recover(); err != nil {
				logger.Errorf(timeoutCtx, "DialogueHistory.recover err:%v", err)
			}
		}()
		defer wg.Done()
		playerMap, playerErr = persistence.NewLineInteractions().GetPlayerDisplayNameByLineUserId(timeoutCtx, lineUserIds)
	}()
	wg.Wait()

	if senderErr != nil || playerErr != nil {
		return nil, fmt.Errorf("senderErr :%v, playerErr:%v", senderErr, playerErr)
	}
	data := make([]*pb.LineDialogueHistoryResp_Dialogue, len(commus))
	for i, commu := range commus {
		messageType := ""
		switch commu.MessageType {
		case code.LineMessageTypeText:
			messageType = "text"
		case code.LineMessageTypeSticker:
			messageType = "sticker"
		case code.LineMessageTypeImage:
			messageType = "image"
		case code.LineMessageTypeVideo:
			messageType = "video"
		case code.LineMessageTypeAudio:
			messageType = "audio"
		case code.LineMessageTypeFile:
			messageType = "file"
		case code.LineMessageTypeLocation:
			messageType = "location"
		default:
			messageType = "others"
		}
		messageFrom := "player"
		if commu.FromUserID == commu.BotID {
			messageFrom = "service"
		}
		sender := senderMap[commu.MsgID]
		displayName := playerMap[commu.LineUserID]
		data[i] = &pb.LineDialogueHistoryResp_Dialogue{
			MessageId:          commu.MsgID,
			BotId:              commu.BotID,
			LineUserId:         commu.LineUserID,
			DisplayName:        displayName,
			ChannelId:          commu.ChannelID,
			MessageFrom:        messageFrom,
			Sender:             sender,
			MessageType:        messageType,
			QuoteToken:         commu.QuoteToken,
			Content:            commu.Content,
			Revoke:             commu.Revoke,
			OriginalContentUrl: commu.OriginalContentUrl,
			PreviewImageUrl:    commu.PreviewImageUrl,
			Duration:           int32(commu.Duration),
			StickerInfo:        commu.StickerInfo,
			Location:           commu.Location,
			TickTime:           commu.TickTime,
			CreateTime:         utils.TransferTimeToString(commu.CreatedAt),
		}
	}
	return data, nil
}

func LineRemarkSave(ctx echo.Context, req *pb.LineUserRemarkAddReq) error {
	dest := map[string]interface{}{
		"note":       req.Note,
		"updated_at": utils.NowTimestamp(),
	}
	if err := elasticsearch.DefaultLineEsSvc.UpdateLine(ctx.Request().Context(), req.LineUserId, dest); err != nil {
		return err
	}
	return nil
}
