package dsccore

import (
	"context"
	"ops-ticket-api/internal/persistence"
	"ops-ticket-api/models"
	"ops-ticket-api/proto/pb"
	"ops-ticket-api/utils"
)

var (
	ChanMsgUnreadDefault = &chanMsgUnread{}
)

type chanMsgUnread struct{}

func (c *chanMsgUnread) DmMsgAdd(ctx context.Context, channelId string, commu *models.FpDscDmCommu) error {
	return persistence.NewDscInteractions().DmCommuUnreadSave(ctx, channelId, commu.FromUserID, pb.DscEvTpDf_DscEvTpDfMsgAdd, utils.ToJson(commu))
}

func (c *chanMsgUnread) DmMsgUpdate(ctx context.Context, channelId string, commu *models.FpDscDmCommu) error {
	return persistence.NewDscInteractions().DmCommuUnreadSave(ctx, channelId, commu.FromUserID, pb.DscEvTpDf_DscEvTpDfMsgEdit, utils.ToJson(commu))
}
func (c *chanMsgUnread) DmMgsDel(ctx context.Context, channelId string, msgIds []string) error {
	return persistence.NewDscInteractions().DmCommuUnreadSave(ctx, channelId, "", pb.DscEvTpDf_DscEvTpDfMsgDel, utils.ToJson(msgIds))
}
func (c *chanMsgUnread) DmMsgReactionAdd(ctx context.Context, channelId string, userId string, reaction *models.FpDscDmCommuReaction) error {
	return persistence.NewDscInteractions().DmCommuUnreadSave(ctx, channelId, userId, pb.DscEvTpDf_DscEvTpDfReactionAdd, utils.ToJson(reaction))
}
func (c *chanMsgUnread) DmMsgReactionDel(ctx context.Context, channelId, userId string, reaction *models.FpDscDmCommuReaction) error {
	return persistence.NewDscInteractions().DmCommuUnreadSave(ctx, channelId, userId, pb.DscEvTpDf_DscEvTpDfReactionDel, utils.ToJson(reaction))
}
