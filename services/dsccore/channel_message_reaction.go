package dsccore

import (
	"context"
	"github.com/bwmarrin/discordgo"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"ops-ticket-api/internal/persistence"
	"ops-ticket-api/models"
	"ops-ticket-api/utils"
	"time"
)

func (srv *dscChannelMessage) MessageReactionAdd(ctx context.Context, botId string, reaction *discordgo.MessageReaction) (*models.FpDscDmCommuReaction, error) {
	var (
		fun = "dscChannelMessage.MessageReactionAdd"
	)

	msgReaction, err := srv.genMsgReaction(ctx, reaction)
	if err != nil {
		return nil, err
	}
	if err := persistence.NewDscInteractions().ChannelMessageSave(ctx, nil, nil, msgReaction, nil); err != nil {
		logger.Warn(ctx, "ChannelMessageSave err", logger.Any("msgReaction", msgReaction), logger.Any("err", err), logger.String("fun", fun))
		return nil, err
	}
	newCtx := utils.ContextOnlyValue{Context: ctx}
	//var replyTp = pb.DscReplyTpDf_DscReplyTpDfReplied
	//if reaction.UserID != botId {
	//	replyTp = pb.DscReplyTpDf_DscReplyTpDfUnReply
	//}
	go ChanMsgUnreadDefault.DmMsgReactionAdd(newCtx, reaction.ChannelID, reaction.UserID, msgReaction)
	// go elasticsearch.DefaultDscEsSvc.AddDscReaction(newCtx, msgReaction, replyTp)
	return msgReaction, nil
}

func (srv *dscChannelMessage) MessageReactionRemove(ctx context.Context, botId string, reaction *discordgo.MessageReaction) error {
	var (
		fun = "dscChannelMessage.MessageReactionRemove"
	)
	msgReaction, err := srv.genMsgReaction(ctx, reaction)
	if err != nil {
		return err
	}
	var userId = reaction.UserID
	var channelId = reaction.ChannelID
	var msgId = reaction.MessageID
	var emojiNameMd5 = utils.Md5(reaction.Emoji.Name)
	if err := persistence.NewDscInteractions().DmChannelCommuReactionRemove(ctx, msgId, userId, channelId, emojiNameMd5); err != nil {
		logger.Warn(ctx, "ChannelMessageDelete err", logger.Any("msgReaction", msgReaction), logger.Any("err", err), logger.String("fun", fun))
		return err
	}
	go ChanMsgUnreadDefault.DmMsgReactionDel(utils.ContextOnlyValue{Context: ctx}, channelId, reaction.UserID, msgReaction)
	return nil
}

func (srv *dscChannelMessage) genMsgReaction(ctx context.Context, reaction *discordgo.MessageReaction) (*models.FpDscDmCommuReaction, error) {
	var detail = &models.FpDscDmCommuReaction{
		MsgID:        reaction.MessageID,
		FromUserID:   reaction.UserID,
		ChannelID:    reaction.ChannelID,
		EmojiNameMd5: utils.Md5(reaction.Emoji.Name),
		EmojiDetail:  utils.ToJson(reaction.Emoji),
		CreatedAt:    time.Now(),
		UpdatedAt:    time.Now(),
	}
	return detail, nil
}

func (srv *dscChannelMessage) genMsgReactions(commu *models.FpDscDmCommu, reactions []*discordgo.MessageReactions) ([]*models.FpDscDmCommuReaction, error) {
	var dmReactions = make([]*models.FpDscDmCommuReaction, 0)
	for _, reaction := range reactions {
		detail := &models.FpDscDmCommuReaction{
			MsgID:        commu.MsgID,
			FromUserID:   commu.FromUserID,
			ChannelID:    commu.ChannelID,
			EmojiNameMd5: utils.Md5(reaction.Emoji.Name),
			EmojiDetail:  utils.ToJson(reaction.Emoji),
			CreatedAt:    time.Now(),
			UpdatedAt:    time.Now(),
		}
		dmReactions = append(dmReactions, detail)
	}
	return dmReactions, nil
}
