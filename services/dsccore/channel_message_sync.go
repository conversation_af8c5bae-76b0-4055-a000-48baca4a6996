package dsccore

import (
	"context"
	"fmt"
	"github.com/bwmarrin/discordgo"
	"ops-ticket-api/internal/persistence"
	"ops-ticket-api/models"
	"ops-ticket-api/proto/pb"
)

func (srv *dscChannelMessage) CompareAndSaveDscMessageDetail(ctx context.Context, user *models.FpDscUser, dscMsg *discordgo.Message) error {
	if dscMsg == nil {
		return nil
	}
	commuMsgDetail, err := persistence.NewDscInteractions().GetChannelMessageByMsgId(ctx, dscMsg.ID)
	if err != nil {
		return err
	}
	if commuMsgDetail == nil || commuMsgDetail.ID == 0 { // add discord message
		_, err := srv.MessageCreate(ctx, user.Project, user.AppID, dscMsg, false)
		return err
	}
	if commuMsgDetail.MsgID != dscMsg.ID {
		return fmt.Errorf("msgId not match. commuMsgDetail.MsgID:%s. msg.ID:%s", commuMsgDetail.MsgID, dscMsg.ID)
	}

	boo, err := srv._compareDscMsgWithCommu(ctx, dscMsg, commuMsgDetail)
	if err != nil {
		return err
	}
	if boo { // 需要重新生成消息
		_, err := srv.MessageUpdate(ctx, user.Project, user.AppID, dscMsg, false)
		if err != nil {
			return err
		}
	}
	return nil
}

// return bool: false - no need update, true - show update or add
// return error: nil - success, err - failed
func (srv *dscChannelMessage) _compareDscMsgWithCommu(ctx context.Context, dscMsg *discordgo.Message, commuMsgDetail *models.FpDscDmCommu) (bool, error) {
	if dscMsg.ChannelID != commuMsgDetail.ChannelID {
		return false, fmt.Errorf("channelId not match. dscMsg.ChannelID:%s. commuMsgDetail.ChannelID:%s", dscMsg.ChannelID, commuMsgDetail.ChannelID)
	}
	if dscMsg.ID != commuMsgDetail.MsgID {
		return false, fmt.Errorf("msgId not match. dscMsg.ID:%s. commuMsgDetail.MsgID:%s", dscMsg.ID, commuMsgDetail.MsgID)
	}
	// content not match
	if dscMsg.Content != commuMsgDetail.Content {
		return true, nil
	}
	if _cmpDscMsgAttachF(dscMsg.Attachments, commuMsgDetail.DecodeAttach()) {
		return true, nil
	}
	if _cmpDscStickerF(dscMsg.StickerItems, commuMsgDetail.DecodeSticker()) {
		return true, nil
	}
	var emoji = make([]*discordgo.Emoji, 0)
	for _, v := range commuMsgDetail.Reactions {
		if _emo := v.DecodeEmoji(); _emo.Name != "" {
			emoji = append(emoji, _emo)
		}
	}
	if _cmpDscReactionsF(dscMsg.Reactions, emoji) {
		return true, nil
	}
	if _cmpDscPollF(dscMsg.Poll, commuMsgDetail.DecodePoll()) {
		return true, nil
	}

	return false, nil
}

var (
	// bool: false- no need update, true - diff:show update or add
	_cmpDscMsgAttachF = func(msgAttach []*discordgo.MessageAttachment, commuAttach []*pb.DscMsgAttach) bool {
		// compare attach
		for _, m := range msgAttach {
			var flag bool
			for _, c := range commuAttach {
				if c.Id == m.ID {
					flag = true
					break
				}
			}
			if !flag {
				return true
			}
		}
		for _, c := range commuAttach {
			var flag bool
			for _, m := range msgAttach {
				if c.Id == m.ID {
					flag = true
					break
				}
			}
			if !flag {
				return true
			}
		}
		return false
	}
	_cmpDscStickerF = func(msgSticker []*discordgo.StickerItem, commuSticker []*pb.DscMsgSticker) bool {
		// compare sticker
		for _, m := range msgSticker {
			var flag bool
			for _, c := range commuSticker {
				if c.Id == m.ID {
					flag = true
					break
				}
			}
			if !flag {
				return true
			}
		}
		for _, c := range commuSticker {
			var flag bool
			for _, m := range msgSticker {
				if c.Id == m.ID {
					flag = true
					break
				}
			}
			if !flag {
				return true
			}
		}
		return false
	}
	_cmpDscReactionsF = func(msgReactions []*discordgo.MessageReactions, commuReactions []*discordgo.Emoji) bool {
		// compare reactions
		for _, m := range msgReactions {
			if m.Emoji == nil {
				continue
			}
			var flag bool
			for _, c := range commuReactions {
				if c.Name == m.Emoji.Name {
					flag = true
					break
				}
			}
			if !flag {
				return true
			}
		}
		for _, c := range commuReactions {
			var flag bool
			for _, m := range msgReactions {
				if m.Emoji == nil {
					continue
				}
				if c.Name == m.Emoji.Name {
					flag = true
					break
				}
			}
			if !flag {
				return true
			}
		}
		return false
	}
	_cmpDscPollF = func(msgPoll *discordgo.Poll, commuPoll *pb.DscMsgPoll) bool {
		if msgPoll == nil {
			if msgPoll == nil || len(msgPoll.Answers) == 0 {
				return false
			}
		}
		if commuPoll == nil {
			if commuPoll == nil || len(commuPoll.Answers) == 0 {
				return false
			}
		}
		if msgPoll.Question.Text != commuPoll.QuestionText || len(msgPoll.Answers) != len(commuPoll.Answers) {
			return true
		}
		for _, an := range msgPoll.Answers {
			if an.Media == nil {
				continue
			}
			var flg bool
			for _, cAn := range commuPoll.Answers {
				if uint32(an.AnswerID) == cAn.AnswerId {
					if an.Media.Text != cAn.AnswerText {
						return true
					}
					flg = true
					break
				}
			}

			if !flg {
				return true
			}
		}
		// poll vote
		if msgPoll.Results != nil {
			if commuPoll.Results == nil {
				return true
			}
			if msgPoll.Results.Finalized != commuPoll.Results.IsFinalized ||
				len(msgPoll.Results.AnswerCounts) != len(commuPoll.Results.AnswerCount) { //有投票结果更新、已完成状态不同
				return true
			}
			for _, po := range msgPoll.Results.AnswerCounts {
				var _flg bool
				for _, cpo := range commuPoll.Results.AnswerCount {
					if uint32(po.ID) == cpo.Id {
						if uint32(po.Count) != cpo.Count || po.MeVoted != cpo.MeVoted { // diff
							return true
						}
						_flg = true
						break
					}
				}

				if !_flg {
					return true
				}
			}
		}

		return false
	}
)
