package dsccore

import (
	"context"
	"github.com/bluele/gcache"
	"ops-ticket-api/internal/persistence"
	"sync"
	"time"
)

var (
	DefaultCacheUser = &cacheUser{
		botOnce:       sync.Once{},
		lock:          new(sync.Mutex),
		channelUser:   gcache.New(10240).LFU().Expiration(time.Hour).Build(),
		userToNameMap: gcache.New(10240).LFU().Expiration(time.Hour).Build(),
	}
)

type cacheUser struct {
	botOnce       sync.Once
	lock          *sync.Mutex
	channelUser   gcache.Cache
	userToNameMap gcache.Cache
}

func (c *cacheUser) UserName(ctx context.Context, userId string) string {
	return c.userName(ctx, userId, false)
}
func (c *cacheUser) userName(ctx context.Context, userId string, again bool) string {
	if userId == "" {
		return ""
	}
	if name, err := c.userToNameMap.Get(userId); err == nil {
		return name.(string)
	}
	if again { // 重试一次
		return ""
	}
	// load data
	if err := c.loadUser(ctx, userId); err != nil {
		return ""
	}
	return c.userName(ctx, userId, true)
}

func (c *cacheUser) loadUser(ctx context.Context, userId string) error {
	// user
	user, err := persistence.NewDscInteractions().GetDscUserForMaster(ctx, userId, "")
	if err != nil {
		return err
	}
	if user != nil && user.DscUserID != "" {
		uname := user.GlobalName
		if user.GlobalName == "" {
			uname = user.UserName
		}
		c.userToNameMap.Set(userId, uname)
		return nil
	}

	// bot detail
	var bErr error
	c.botOnce.Do(func() {
		bots, err := persistence.NewDscInteractions().GetDscBotAll(ctx)
		if err != nil {
			bErr = err
		}
		for _, bot := range bots {
			if bot.AppID != "" {
				c.userToNameMap.Set(bot.AppID, bot.Username)
			}
		}
	})
	if bErr != nil {
		c.botOnce = sync.Once{}
		return bErr
	}
	return nil
}
