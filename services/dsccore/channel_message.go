package dsccore

import (
	"context"
	"fmt"
	"github.com/bluele/gcache"
	"github.com/bwmarrin/discordgo"
	"github.com/pkg/errors"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"ops-ticket-api/internal/elasticsearch"
	"ops-ticket-api/internal/framework/cache/keys"
	"ops-ticket-api/internal/framework/cache/rds"
	"ops-ticket-api/internal/persistence"
	"ops-ticket-api/models"
	"ops-ticket-api/utils"
	"time"
)

var (
	ChannelMessageSrv = dscChannelMessage{
		savedChannels: gcache.New(4096).LFU().Build(),
	}
)

type (
	dscChannelMessage struct {
		savedChannels gcache.Cache
	}
)

func (srv *dscChannelMessage) MessageDel(ctx context.Context, project, botId, channelId string, msgIds []string) error {
	if err := persistence.NewDscInteractions().ChannelMessageDel(ctx, msgIds); err != nil {
		return err
	}

	newCtx := utils.ContextOnlyValue{Context: ctx}
	// 未读消息删除
	go ChanMsgUnreadDefault.DmMgsDel(newCtx, channelId, msgIds)
	// del es message
	go elasticsearch.DefaultDscEsSvc.DelDscCommus(newCtx, channelId, msgIds)
	return nil
}
func (srv *dscChannelMessage) MessageUpdate(ctx context.Context, project, botId string, msg *discordgo.Message, isEvent bool) (*models.FpDscDmCommu, error) {
	//dump.Dump("dscChannelMessage.MessageUpdate", project, botId, msg)
	var (
		fun = "dscChannelMessage.MessageUpdate"
	)
	//
	repo := persistence.NewDscInteractions()
	msgDetail, err := repo.GetChannelMessageByMsgId(ctx, msg.ID)
	if err != nil {
		logger.Errorf(ctx, "%s GetChannelMessageByMsgId return err.  id :%s. err:%v", fun, msg.ID, err)
		return nil, err
	}
	if msgDetail == nil || msgDetail.ID == 0 { // 不存在 - add
		if msg.Author == nil {
			logger.Error(ctx, "msg.Author is nil. msgDetail is empty. need create. continue msg", zap.String("fun", fun), zap.Any("msg", msg), zap.Any("msgDetail", msgDetail))
			return nil, fmt.Errorf("msgDetail not found. msg.Author isNil")
		}
		return srv.MessageCreate(ctx, project, botId, msg, isEvent)
	}

	// 重新升级数据
	{
		createMsg, err := srv.genCreateMessage(project, botId, msgDetail.DscUserID, msg)
		if err != nil {
			return nil, err
		}
		createMsg.ID = msgDetail.ID
		createMsg.CreatedAt = msgDetail.CreatedAt
		if msg.Author == nil { // embed/attach some special case: msg author is nil
			createMsg.FromUserID = msgDetail.FromUserID
			createMsg.Author = msgDetail.Author
		}
		msgDetail = createMsg
	}

	msgDetail.Content = msg.Content
	msgDetail.UpdatedAt = time.Now()
	msgDetail.Edited = true
	if msg.EditedTimestamp != nil {
		msgDetail.EditedAt = *msg.EditedTimestamp
	}
	// save data
	if err := repo.ChannelMessageSave(ctx, nil, msgDetail, nil, nil); err != nil {
		return nil, err
	}

	newCtx := utils.ContextOnlyValue{Context: ctx}
	defer func() {
		go SetRdsMsgEventGet(newCtx, msg.ID) // 设置消息已接收
	}()
	// 未读消息
	if isEvent {
		go ChanMsgUnreadDefault.DmMsgUpdate(newCtx, msg.ChannelID, msgDetail)
	}
	// 保存消息到 es
	lastReplyService := "others"
	// 如果这个消息是客服发给玩家的，则根据msg_id找到对应的客服
	if msgDetail.FromUserID == msgDetail.BotID {
		operator, e := persistence.NewDiscordInteract().FetchOperatorByMsgId(msgDetail.MsgID)
		if e == nil {
			lastReplyService = operator
		}
	}
	go elasticsearch.DefaultDscEsSvc.UpsertDscCommus(newCtx, msgDetail, false, lastReplyService) // 修改 - 不变更是否回复状态
	return nil, nil
}

// gateway Event channel message create
func (srv *dscChannelMessage) MessageCreate(ctx context.Context, project, botId string, msg *discordgo.Message, isEvent bool) (*models.FpDscDmCommu, error) {
	var (
		fun = "dscChannelMessage.MessageCreate"
		now = time.Now()
	)

	repo := persistence.NewDscInteractions()
	userDetail, err := repo.GetDscUserWithChannelForMaster(ctx, msg.ChannelID)
	if err != nil {
		return nil, err
	}
	if userDetail.AppID != botId {
		return nil, fmt.Errorf("%s dm channel user's botId neq botId user:%+v. botId:%s", fun, userDetail, botId)
	}
	commu, err := srv.genCreateMessage(project, botId, userDetail.DscUserID, msg)
	if err != nil {
		return nil, err
	}
	var channelDetail *models.FpDscDmChannel
	if !srv.savedChannels.Has(msg.ChannelID) {
		channelDetail = &models.FpDscDmChannel{
			Project:   project,
			ChannelID: msg.ChannelID,
			BotID:     botId,
			DscUserID: userDetail.DscUserID,
			CreatedAt: now,
			EditedAt:  now,
			UpdatedAt: now,
		}
	}

	// save data
	if err := repo.ChannelMessageSave(ctx, nil, commu, nil, channelDetail); err != nil {
		return nil, err
	}

	defer func() { // set local channel
		go srv.savedChannels.Set(msg.ChannelID, userDetail)
	}()

	newCtx := utils.ContextOnlyValue{Context: ctx}
	defer func() {
		go SetRdsMsgEventGet(newCtx, msg.ID) // 设置消息已接收
	}()
	// 未读消息设置
	if isEvent {
		go ChanMsgUnreadDefault.DmMsgAdd(newCtx, msg.ChannelID, commu)
	}
	// 保存消息到 es
	lastReplyService := "others"
	// 如果这个消息是客服发给玩家的，则根据msg_id找到对应的客服
	if commu.FromUserID == commu.BotID {
		time.Sleep(500 * time.Millisecond)
		operator, e := persistence.NewDiscordInteract().FetchOperatorByMsgId(commu.MsgID)
		if e == nil {
			lastReplyService = operator
		}
		//msgid如果在批量私信任务中 或 推送调查问卷，则不改变回复状态
		if isTask, checkErr := persistence.NewDscMessageTask().CheckHasMsgId(msg.ID); checkErr != nil {
			logger.Errorf(ctx, "CheckHasMsgId err:%+v", checkErr)
		} else if isTask == true { // 存在
			isEvent = false
		}
		if ignore, checkErr := persistence.NewDscInteractions().CheckIsIgnoreMsgId(ctx, msg.ID); checkErr != nil {
			if !errors.Is(checkErr, gorm.ErrRecordNotFound) {
				logger.Errorf(ctx, "CheckIgnoreMsgId err:%+v", checkErr)
			}
		} else if ignore == true {
			isEvent = false
			lastReplyService = ""
		}
	}

	go elasticsearch.DefaultDscEsSvc.UpsertDscCommus(newCtx, commu, isEvent == true, lastReplyService)
	return commu, nil
}

func (srv *dscChannelMessage) genCreateMessage(project, botId, dscUserId string, msg *discordgo.Message) (*models.FpDscDmCommu, error) {
	var (
		fun = "dscChannelMessage.genCreateMessage"
		now = time.Now()
	)
	if msg == nil || msg.ID == "" {
		return nil, fmt.Errorf("%s msg empty.  %+v.", fun, msg)
	}
	commu := &models.FpDscDmCommu{
		MsgID:           msg.ID,
		Project:         project,
		FromUserID:      "",
		BotID:           botId,
		DscUserID:       dscUserId,
		ChannelID:       msg.ChannelID,
		Content:         msg.Content,
		Attachments:     "[]",
		Embeds:          "[]",
		Stickers:        "[]",
		Poll:            "{}",
		Author:          utils.ToJson(msg.Author),
		ReferencedMsgID: "",
		CreatedAt:       msg.Timestamp,
		EditedAt:        msg.Timestamp,
		UpdatedAt:       now,
	}
	if msg.Author != nil { // author
		commu.FromUserID = msg.Author.ID
	}
	// 单条会话回复 - 针对某条消息进行回复
	if msg.MessageReference != nil && msg.MessageReference.MessageID != "" {
		commu.ReferencedMsgID = msg.MessageReference.MessageID
	}
	// 附件形式：
	if msg.Attachments != nil && len(msg.Attachments) > 0 {
		commu.Attachments = utils.ToJson(msg.Attachments)
	}
	// 嵌入形式：
	if msg.Embeds != nil && len(msg.Embeds) > 0 {
		commu.Embeds = utils.ToJson(msg.Embeds)
	}
	// 贴纸
	if msg.StickerItems != nil && len(msg.StickerItems) > 0 {
		commu.Stickers = utils.ToJson(msg.StickerItems)
	}
	// 回复表情 -
	if len(msg.Reactions) > 0 {
		emojis, err := srv.genMsgReactions(commu, msg.Reactions)
		if err != nil {
			return nil, err
		}
		if len(emojis) > 0 {
			commu.Reactions = emojis
		}
	}
	// - 发起投票
	if msg.Poll != nil && len(msg.Poll.Answers) > 0 {
		commu.Poll = utils.ToJson(msg.Poll)
	}
	return commu, nil
}

func SetRdsMsgEventGet(ctx context.Context, msgId string) error {
	return rds.NewRCache().SetEX(ctx, fmt.Sprintf(keys.DiscordMsgHasGetKey, msgId), 1, time.Minute).Err()
}

func AddBotUnRead(ctx context.Context, dscUserId, channelId string) error {
	var key = []string{
		fmt.Sprintf(keys.DiscordMsgUnReadUserKey, dscUserId),
		fmt.Sprintf(keys.DiscordMsgUnReadChannelKey, channelId),
	}
	pipe := rds.NewRCache().Pipeline()
	pipe.IncrBy(ctx, key[0], 1)
	pipe.IncrBy(ctx, key[1], 1)

	_, err := pipe.Exec(ctx)
	if err != nil {
		logger.Errorf(ctx, "AddBotUnRead rds incr return err. keys:%+v. err:%v", key, err)
	}
	return err
}

func ClearBotUnread(ctx context.Context, dscUserId, channelId string) error {
	var key = []string{
		fmt.Sprintf(keys.DiscordMsgUnReadUserKey, dscUserId),
		fmt.Sprintf(keys.DiscordMsgUnReadChannelKey, channelId),
	}
	err := rds.NewRCache().Del(ctx, key...).Err()
	if err != nil {
		logger.Errorf(ctx, "ClearBotUnread rds del key return err. keys:%+v. err:%v", key, err)
	}
	return err
}
