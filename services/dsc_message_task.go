package services

import (
	"bytes"
	"errors"
	"fmt"
	"github.com/avast/retry-go"
	"github.com/bwmarrin/discordgo"
	"github.com/labstack/echo/v4"
	"github.com/spf13/cast"
	"github.com/spf13/viper"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"gorm.io/gorm"
	"io"
	"ops-ticket-api/internal/dsc"
	"ops-ticket-api/internal/framework/lang"
	"ops-ticket-api/internal/persistence"
	"ops-ticket-api/models"
	"ops-ticket-api/proto/pb"
	"ops-ticket-api/utils"
	"time"
)

func ReadyMessageBatchTask(ctx echo.Context, taskId int64) error {

	fun := "ReadyMessageBatchTask-->"
	var successCount, failedCount int
	task, err := persistence.NewDscMessageTask().GetTaskById(ctx.Request().Context(), taskId)
	if err != nil {
		return fmt.Errorf("%s models.GetTaskById return err. err:%v. %d", fun, err, taskId)
	}
	if task.Status != int(pb.DscMsgTaskStatus_ProcessStatusInit) && task.Status != int(pb.DscMsgTaskStatus_ProcessStatusDoing) {
		return fmt.Errorf("%s status neq enums.ProcessStatusInit or enums.ProcessStatusDoing. task:%+v taskId:%d", fun, task, taskId)
	}

	// 1.先更新状态
	task.Status = int(pb.DscMsgTaskStatus_ProcessStatusDoing)
	task.UpdateAt = time.Now()
	if num, err := persistence.NewDscMessageTask().UpdateMessageTask(map[string]interface{}{
		"updated_at": task.UpdateAt,
		"status":     task.Status,
	}, map[string]interface{}{
		"id":     task.ID,
		"status": int(pb.DscMsgTaskStatus_ProcessStatusInit),
	}); num != 1 || err != nil {
		logger.Errorf(ctx.Request().Context(), "%s update status final fail. task:%+v. effectNum:%d. err:%v", fun, task, num, err)
	}

	// 2.更新最终状态
	defer func() {
		task.FinishedAt = time.Now()
		task.UpdateAt = time.Now()
		if num, err := persistence.NewDscMessageTask().UpdateMessageTask(map[string]interface{}{
			"status":        task.Status,
			"updated_at":    task.UpdateAt,
			"finished_at":   task.FinishedAt,
			"success_count": successCount,
			"failed_count":  failedCount,
		}, map[string]interface{}{
			"id":     task.ID,
			"status": int(pb.DscMsgTaskStatus_ProcessStatusDoing),
		}); num != 1 || err != nil {
			logger.Errorf(ctx.Request().Context(), "%s update status final fail. task:%+v. effectNum:%d. err:%v", fun, task, num, err)
		}
	}()

	if task.Content != "" {
		if successCount, failedCount, err = MessageBatchSend(ctx, task); err != nil {
			logger.Errorf(ctx.Request().Context(), "%s DoMessageBatchTask return err. err:%v. task:%+v", fun, err, task)
			task.Status = int(pb.DscMsgTaskStatus_ProcessStatusFail)
			return err
		} else {
			task.Status = int(pb.DscMsgTaskStatus_ProcessStatusSuccess)
		}
	} else if task.FileUrl != "" {
		if successCount, failedCount, err = DscFileBatchSend(ctx, task); err != nil {
			logger.Errorf(ctx.Request().Context(), "%s DoMessageBatchTask return err. err:%v. task:%+v", fun, err, task)
			task.Status = int(pb.DscMsgTaskStatus_ProcessStatusFail)
			return err
		} else {
			task.Status = int(pb.DscMsgTaskStatus_ProcessStatusSuccess)
		}
	}
	return nil
}

// MessageBatchSend 批量发送消息
func MessageBatchSend(ctx echo.Context, task *models.FpDscMessageTask) (int, int, error) {
	var successCount, failCount int
	var fun = "MessageBatchSend"
	for _, user := range task.Users {
		//成功或失败跳过
		if user.Status == int(pb.DscMsgTaskDetailStatus_DscTaskDetailStatusSuccess) {
			failCount++
			logger.Infof(ctx.Request().Context(), "user detail status is already failed. project:%v. uid:%+v", task.Project, user.UID)
			continue
		}
		if user.Status == int(pb.DscMsgTaskDetailStatus_DscTaskDetailStatusFail) {
			successCount++
			logger.Infof(ctx.Request().Context(), "user detail status is already success. project:%v. uid:%+v", task.Project, user.UID)
			continue
		}
		// 用户不存在
		_, err := persistence.NewDiscordInteract().FetchUserInfoByUID(user.UID, task.Project)
		if err != nil {
			logger.Infof(ctx.Request().Context(), "%s %v FetchUserInfoByUID err:%v", fun, user.UID, err)
			if errors.Is(err, gorm.ErrRecordNotFound) {
				failCount++
				//记录失败日志
				updateErr := UpdateBatchMessageTaskDetail(ctx, errors.New("UID does not exist").Error(), "", "", task.ID, user.UID, int(pb.DscMsgTaskDetailStatus_DscTaskDetailStatusFail), nil)
				if updateErr != nil {
					logger.Errorf(ctx.Request().Context(), "GetChannelIdsByProjectUid and UpdateBatchMessageTaskDetail err:%v. project:%s. uid:%d", err, task.Project, user.UID)
				}
				continue
			}
		}
		channelIds, err := persistence.NewDscMessageTask().GetChannelByGameAndUid(ctx.Request().Context(), user.UID, task.ID, task.Project, user.BotID)
		if err != nil {
			logger.Infof(ctx.Request().Context(), "GetChannelByGameAndUid err:%v. project:%s. uid:%d", err, task.Project, user.UID)
			failCount++
			//记录失败日志
			updateErr := UpdateBatchMessageTaskDetail(ctx, errors.New("get channelIDs by game and uid failed").Error(), "", "", task.ID, user.UID, int(pb.DscMsgTaskDetailStatus_DscTaskDetailStatusFail), nil)
			if updateErr != nil {
				logger.Errorf(ctx.Request().Context(), "GetChannelIdsByProjectUid and UpdateBatchMessageTaskDetail err:%v. project:%s. uid:%d", err, task.Project, user.UID)
			}
			continue
		}
		//生成随机时间间隔，和为1s
		delays := utils.GenerateDelays()
		messageCount := 0
		for _, channelId := range channelIds {
			client := dsc.GetClient(ctx.Request().Context(), user.BotID)
			if client == nil || channelId == "" {
				logger.Infof(ctx.Request().Context(), "get discord session client: message create: bot not found. uid:%d. channelId:%+v", user.UID, channelId)
				//记录失败日志
				failCount++
				updateErr := UpdateBatchMessageTaskDetail(ctx, errors.New("get discord session client: message create: bot not found").Error(), "", channelId, task.ID, user.UID, int(pb.DscMsgTaskDetailStatus_DscTaskDetailStatusFail), nil)
				if updateErr != nil {
					logger.Errorf(ctx.Request().Context(), "get discord session client and UpdateBatchMessageTaskDetail err:%v. project:%s. uid:%d", err, task.Project, user.UID)
				}
				continue
			}
			var msgDetail *discordgo.Message
			//fixme 200*time.Millisecond
			err := retry.Do(func() error {
				var _innErr error
				msgDetail, _innErr = client.ChannelMessageSend(channelId, task.Content)
				if _innErr != nil {
					logger.Infof(ctx.Request().Context(), "ChannelMessageSend err:%v. project:%v. channelId:%v. uid:%+v", _innErr, task.Project, channelId, user.UID)
					return _innErr
				}
				return nil
			}, retry.Attempts(3), retry.Delay(200*time.Millisecond), retry.LastErrorOnly(true))

			if err != nil {
				logger.Infof(ctx.Request().Context(), "ChannelMessageSend err:%v. channelId:%s. uid:%+v", err, channelId, user.UID)
				//记录失败日志
				failCount++
				updateErr := UpdateBatchMessageTaskDetail(ctx, err.Error(), "", channelId, task.ID, user.UID, int(pb.DscMsgTaskDetailStatus_DscTaskDetailStatusFail), nil)
				if updateErr != nil {
					logger.Errorf(ctx.Request().Context(), "ChannelMessageSend and UpdateBatchMessageTaskDetail err:%v. project:%s. uid:%d", err, task.Project, user.UID)
				}
				continue
			}
			//记录成功日志
			now := time.Now()
			successCount++
			updateErr := UpdateBatchMessageTaskDetail(ctx, "", msgDetail.ID, channelId, task.ID, user.UID, int(pb.DscMsgTaskDetailStatus_DscTaskDetailStatusSuccess), &now)
			if updateErr != nil {
				logger.Errorf(ctx.Request().Context(), "send successfully and UpdateBatchMessageTaskDetail err:%v. project:%s. uid:%d", err, task.Project, user.UID)
			}
			// 记录操作日志
			persistence.NewDscInteractions().DmCommuAccountSend(ctx.Request().Context(), msgDetail.ID, user.BotID, channelId, task.Operator, pb.OpGroup_OpGroupSendTextMessage.String(), pb.OpAction_OpActionAdd.String(), 0)
			//控制调用频率
			time.Sleep(delays[messageCount])
			messageCount = (messageCount + 1) % 5
		}
	}
	return successCount, failCount, nil
}

// DscFileBatchSend 批量发送文件
func DscFileBatchSend(ctx echo.Context, task *models.FpDscMessageTask) (int, int, error) {
	var successCount, failCount int
	var fun = "DscFileBatchSend"
	// 从URL中提取文件名
	fileName, _ := utils.ExtractFileNameFromURL(task.FileUrl)
	// 下载文件并校验文件大小
	fileDst, err := utils.DownloadAndValidateFile(task.FileUrl)
	if err != nil {
		//整个批次全部失败
		rowEffected, err := persistence.NewDscMessageTask().UpdateAllTaskDetail(task.ID, errors.New("failed to download or validate file").Error(), int(pb.DscMsgTaskDetailStatus_DscTaskDetailStatusFail))
		if err != nil {
			logger.Infof(ctx.Request().Context(), "DownloadAndValidateFile and UpdateAllTaskDetail err:%v. project:%s. taskId:%d", err, task.Project, task.ID)
		}
		return 0, int(rowEffected), fmt.Errorf("failed to download or validate file: %v\n", err)
	}
	defer fileDst.Close()
	// 将文件读到内存，以便重复使用
	fileData, err := io.ReadAll(fileDst)
	if err != nil {
		//整个批次全部失败
		rowEffected, err := persistence.NewDscMessageTask().UpdateAllTaskDetail(task.ID, errors.New("read file to memory return err").Error(), int(pb.DscMsgTaskDetailStatus_DscTaskDetailStatusFail))
		if err != nil {
			logger.Infof(ctx.Request().Context(), "io.ReadAll and UpdateAllTaskDetail err:%v. project:%s. taskId:%d", err, task.Project, task.ID)
		}
		return 0, int(rowEffected), fmt.Errorf("read file to memory return err: %v", err)
	}
	for _, user := range task.Users {
		//成功或失败跳过
		if user.Status == int(pb.DscMsgTaskDetailStatus_DscTaskDetailStatusFail) {
			failCount++
			logger.Infof(ctx.Request().Context(), "user detail status is already failed. project:%v. uid:%+v", task.Project, user.UID)
			continue
		}
		if user.Status == int(pb.DscMsgTaskDetailStatus_DscTaskDetailStatusSuccess) {
			successCount++
			logger.Infof(ctx.Request().Context(), "user detail status is already success. project:%v. uid:%+v", task.Project, user.UID)
			continue
		}
		// 用户不存在
		_, err := persistence.NewDiscordInteract().FetchUserInfoByUID(user.UID, task.Project)
		if err != nil {
			logger.Infof(ctx.Request().Context(), "%s %v FetchUserInfoByUID err:%v", fun, user.UID, err)
			if errors.Is(err, gorm.ErrRecordNotFound) {
				failCount++
				//记录失败日志
				updateErr := UpdateBatchMessageTaskDetail(ctx, errors.New("UID does not exist").Error(), "", "", task.ID, user.UID, int(pb.DscMsgTaskDetailStatus_DscTaskDetailStatusFail), nil)
				if updateErr != nil {
					logger.Errorf(ctx.Request().Context(), "GetChannelIdsByProjectUid and UpdateBatchMessageTaskDetail err:%v. project:%s. uid:%d", err, task.Project, user.UID)
				}
				continue
			}
		}
		channelIds, err := persistence.NewDscMessageTask().GetChannelByGameAndUid(ctx.Request().Context(), user.UID, task.ID, task.Project, user.BotID)
		if err != nil {
			logger.Infof(ctx.Request().Context(), "GetChannelByGameAndUid err:%v. project:%s. uid:%d", err, task.Project, user.UID)
			failCount++
			//记录失败日志
			updateErr := UpdateBatchMessageTaskDetail(ctx, errors.New("get channelIDs by game and uid failed").Error(), "", "", task.ID, user.UID, int(pb.DscMsgTaskDetailStatus_DscTaskDetailStatusFail), nil)
			if updateErr != nil {
				logger.Errorf(ctx.Request().Context(), "GetChannelIdsByProjectUid and UpdateBatchMessageTaskDetail err:%v. project:%s. uid:%d", err, task.Project, user.UID)
			}
			continue
		}
		//生成随机时间间隔，和为1s
		delays := utils.GenerateDelays()
		messageCount := 0
		for _, channelId := range channelIds {
			client := dsc.GetClient(ctx.Request().Context(), user.BotID)
			if client == nil || channelId == "" {
				logger.Infof(ctx.Request().Context(), "get discord session client: message create: bot not found. uid:%d. channelId:%+v", user.UID, channelId)
				//记录失败日志
				failCount++
				updateErr := UpdateBatchMessageTaskDetail(ctx, errors.New("get discord session client: message create: bot not found").Error(), "", channelId, task.ID, user.UID, int(pb.DscMsgTaskDetailStatus_DscTaskDetailStatusFail), nil)
				if updateErr != nil {
					logger.Errorf(ctx.Request().Context(), "get discord session client and UpdateBatchMessageTaskDetail err:%v. project:%s. uid:%d", err, task.Project, user.UID)
				}
				continue
			}
			var msgDetail *discordgo.Message
			// 创建新的io.Reader
			newFileReader := bytes.NewReader(fileData)
			// 创建新的cFile
			cFile := &discordgo.MessageSend{
				Files: []*discordgo.File{{
					Name:   fileName,
					Reader: newFileReader,
				}},
			}
			//fixme 200*time.Millisecond
			err := retry.Do(func() error {
				var _innErr error
				msgDetail, _innErr = client.ChannelMessageSendComplex(channelId, cFile)
				if _innErr != nil {
					logger.Infof(ctx.Request().Context(), "ChannelMessageSend err:%v. project:%v. channelId:%v. uid:%+v", _innErr, task.Project, channelId, user.UID)
					return _innErr
				}
				return nil
			}, retry.Attempts(3), retry.Delay(200*time.Millisecond), retry.LastErrorOnly(true))

			if err != nil {
				logger.Infof(ctx.Request().Context(), "ChannelMessageSend err:%v. channelId:%s. uid:%+v", err, channelId, user.UID)
				//记录失败日志
				failCount++
				updateErr := UpdateBatchMessageTaskDetail(ctx, err.Error(), "", channelId, task.ID, user.UID, int(pb.DscMsgTaskDetailStatus_DscTaskDetailStatusFail), nil)
				if updateErr != nil {
					logger.Errorf(ctx.Request().Context(), "ChannelMessageSend and UpdateBatchMessageTaskDetail err:%v. project:%s. uid:%d", err, task.Project, user.UID)
				}
				continue
			}
			//记录成功日志
			now := time.Now()
			successCount++
			updateErr := UpdateBatchMessageTaskDetail(ctx, "", msgDetail.ID, channelId, task.ID, user.UID, int(pb.DscMsgTaskDetailStatus_DscTaskDetailStatusSuccess), &now)
			if updateErr != nil {
				logger.Errorf(ctx.Request().Context(), "send successfully and UpdateBatchMessageTaskDetail err:%v. project:%s. uid:%d", err, task.Project, user.UID)
			}
			// 记录操作日志
			persistence.NewDscInteractions().DmCommuAccountSend(ctx.Request().Context(), msgDetail.ID, user.BotID, channelId, task.Operator, pb.OpGroup_OpGroupSendTextMessage.String(), pb.OpAction_OpActionAdd.String(), 0)
			//控制调用频率
			time.Sleep(delays[messageCount])
			messageCount = (messageCount + 1) % 5
		}
	}
	return successCount, failCount, nil
}

// UpdateBatchMessageTaskDetail 更新明细表的状态
func UpdateBatchMessageTaskDetail(ctx echo.Context, failLog, msgId, channel string, taskId uint64, uid int64, status int, finishedAt *time.Time) error {
	updateData := map[string]interface{}{
		"status":      status,
		"fail_log":    failLog,
		"finished_at": finishedAt,
		"msg_id":      msgId,
		"update_at":   time.Now(),
	}
	if finishedAt != nil {
		updateData["finished_at"] = *finishedAt
	}
	whereData := map[string]interface{}{
		"task_id": taskId,
		"uid":     uid,
	}
	if channel != "" {
		whereData["channel_id"] = channel
	}
	if num, err := persistence.NewDscMessageTask().UpdateMessageTaskDetail(updateData, whereData); num != 1 || err != nil {
		logger.Errorf(ctx.Request().Context(), "UpdateBatchMessageTaskDetail update status final fail. task_id:%d. uid:%d. effectNum:%d. err:%v", taskId, uid, num, err)
		return err
	}
	return nil
}

func MessageTaskList(ctx echo.Context, req *pb.DiscordMessageTaskListReq) (*pb.DiscordMessageTaskListResp, error) {
	list, total, err := persistence.NewDscMessageTask().MessageTaskList(ctx.Request().Context(), req, false)
	if err != nil {
		return nil, err
	}
	return &pb.DiscordMessageTaskListResp{
		CurrentPage: req.Page,
		PerPage:     req.PageSize,
		Total:       total,
		Data:        list,
	}, nil
}

func MessageTaskListExport(ctx echo.Context, req *pb.DiscordMessageTaskListReq) (string, error) {
	records, _, err := persistence.NewDscMessageTask().MessageTaskList(ctx.Request().Context(), req, true)
	if err != nil {
		return "", err
	}
	var dest = make([][]interface{}, 0)
	for i := range records {
		detail := records[i]
		content := ""
		if detail.ReplyContent.Content != "" {
			content = detail.ReplyContent.Content
		} else {
			fileName, _ := utils.ExtractFileNameFromURL(detail.ReplyContent.FileUrl)
			content = fileName
		}
		//status
		status := lang.FormatText(ctx, pb.DscMsgTaskStatus_name[detail.Status])
		record := []interface{}{detail.TaskId, detail.Project, content, status, detail.Total, detail.Count.SuccessCount, detail.Count.FailedCount, detail.Operator, detail.CreateAt, detail.FinishedAt}
		dest = append(dest, record)
	}
	_fileName := fmt.Sprintf("discord_message_task_record_export_%d.csv", time.Now().UnixNano())
	err = utils.CreateCsvFile(_fileName, viper.GetStringSlice("export_header.DiscodMessageTaskRecordExport.zh-cn"), dest)
	if err != nil {
		return "", err
	}
	return _fileName, err
}

func MessageTaskDetailExport(ctx echo.Context, req *pb.DiscordMessageTaskDetailExportReq) (string, error) {
	task, err := persistence.NewDscMessageTask().GetTaskById(ctx.Request().Context(), int64(req.TaskId))
	if err != nil {
		return "", err
	}
	users := task.Users
	var dest = make([][]interface{}, 0)
	for i := range users {
		detail := users[i]
		finishedAt := detail.FinishedAt.Format("2006-01-02 15:04:05")
		status, botDesc, nickName, err := GetMissedInfo(ctx, detail)
		if err != nil {
			return "", err
		}
		if detail.FailLog != "" {
			finishedAt = ""
		}
		record := []interface{}{detail.DscUserID + "\t", nickName, cast.ToString(detail.UID) + "\t", detail.AccountID, botDesc, status, finishedAt, detail.FailLog}
		dest = append(dest, record)
	}
	_fileName := fmt.Sprintf("discord_message_task_detail_record_export_%d.csv", time.Now().UnixNano())
	err = utils.CreateCsvFile(_fileName, viper.GetStringSlice("export_header.DiscodMessageTaskDetailRecordExport.zh-cn"), dest)
	if err != nil {
		return "", err
	}
	return _fileName, err
}

// GetMissedInfo 获取机器人，用户昵称及状态
func GetMissedInfo(ctx echo.Context, user *models.FpDscMessageTaskDetail) (string, string, string, error) {
	var status, botDesc, nickName string
	var exist bool
	//获取状态
	status = lang.FormatText(ctx, pb.DscMsgTaskDetailStatus_name[int32(user.Status)])
	//如果uid不存在就返回
	if user.BotID == "" && user.DscUserID == "" {
		return status, "", "", nil
	}
	//获取机器人
	data, err := NewAddonsSvc().DscBotList(ctx, []string{user.Project})
	if err != nil {
		return "", "", "", err
	}
	botMap, _ := data.(map[string]string)
	if botDesc, exist = botMap[user.BotID]; !exist {
		return "", "", "", err
	}
	//获取nickname
	userObj, err := persistence.NewDscInteractions().GetDscUserForMaster(ctx.Request().Context(), user.DscUserID, user.BotID)
	if err != nil {
		return "", "", "", err
	}
	nickName = userObj.GlobalName
	if userObj.GlobalName == "" {
		nickName = userObj.UserName
	}
	return status, botDesc, nickName, nil
}
