// Copyright 2021 funplus Authors. All Rights Reserved.
// @Description: 配置项
// @Author: Darcy
// @Date: 2021/11/2 2:56 PM

package services

import (
	"ops-ticket-api/internal/cst"
	"ops-ticket-api/internal/framework/lang"
	"ops-ticket-api/internal/persistence"
	"ops-ticket-api/pkg/game"
	"ops-ticket-api/proto/pb"
	"ops-ticket-api/utils"
	"sort"
	"strings"
	"sync"

	"github.com/labstack/echo/v4"
)

var (
	addonsOnce sync.Once
	addonsSvc  *AddonsSvc
)

type AddonsSvc struct{}

func NewAddonsSvc() *AddonsSvc {
	addonsOnce.Do(func() {
		addonsSvc = &AddonsSvc{}
	})
	return addonsSvc
}

func (svc *AddonsSvc) ChannelList(ctx echo.Context, project string) []string {
	return game.NewGameInfo().GetChannelByProject(project)
}

func (svc *AddonsSvc) SubChannelList(ctx echo.Context, project string) map[string]interface{} {
	return game.NewGameInfo().GetSubChannelByProject(project)
}

func (svc *AddonsSvc) PackageIDList(ctx echo.Context, project string) map[string]interface{} {
	return game.NewGameInfo().GetPackageIDByProject(project)
}

func (svc *AddonsSvc) EnumResp(ctx echo.Context) (map[string][]interface{}, error) {
	result := make(map[string][]interface{})
	idxInclude := []string{"FillStatus"}
	VipExclude := []string{"ModuleGroupStart", "ModuleGroupEnd", "ResolvedAck"}
	var enumIntMap = make(map[string][]*pb.RetEnum)
	for enumType, list := range cst.EnumList {
		for v, name := range list {
			if utils.InArrayAny(name, VipExclude) {
				continue
			}
			if v == 0 && !utils.InArrayAny(enumType, idxInclude) {
				continue
			}
			if enumType == "ExamineState" && v == 10 {
				continue
			}
			enumIntMap[enumType] = append(enumIntMap[enumType], &pb.RetEnum{
				Name:  lang.FormatText(ctx, name),
				Value: uint32(v),
			})
		}
		sort.Slice(enumIntMap[enumType], func(i, j int) bool {
			return enumIntMap[enumType][i].Value < enumIntMap[enumType][j].Value
		})
	}
	for k, dt := range enumIntMap {
		result[k] = make([]interface{}, 0)
		for _, v := range dt {
			result[k] = append(result[k], v)
		}
	}

	// map 类型 string string
	for enT, list := range cst.EnumMapList {
		for k, v := range list {
			result[enT] = append(result[enT], map[string]string{"name": lang.FormatText(ctx, k), "value": v})
		}

	}
	for enT, list := range cst.EnumMapConfList {
		for k, v := range list {
			result[enT] = append(result[enT], map[string]string{"name": k, "value": v})
		}
	}

	return result, nil
}

func (svc *AddonsSvc) LanguageList(ctx echo.Context) (interface{}, error) {
	list, err := persistence.NewAddons().LanguageList(ctx.Request().Context())
	if err != nil {
		return nil, err
	}
	for k, item := range list {
		list[k].Name = lang.FormatText(ctx, strings.ToUpper(item.Code))
	}
	return list, nil
}

func (svc *AddonsSvc) DscBotList(ctx echo.Context, gms []string) (interface{}, error) {
	bots, err := persistence.NewDscInteractions().GetDscBotList(ctx.Request().Context(), gms)
	if err != nil {
		return nil, err
	}
	var botMap = make(map[string]string, 0)
	for _, v := range bots {
		botMap[v.AppID] = v.BotDesc
	}
	return botMap, nil
}

func (svc *AddonsSvc) LineChannelList(ctx echo.Context, gms []string) (interface{}, error) {
	channels, err := persistence.NewLineInteractions().GetLineChannelList(ctx.Request().Context(), gms)
	if err != nil {
		return nil, err
	}
	var channelMap = make(map[string]string, 0)
	for _, v := range channels {
		channelMap[v.ChannelID] = v.Provider
	}
	return channelMap, nil
}
