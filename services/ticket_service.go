package services

import (
	"encoding/json"
	"errors"
	"fmt"
	"sort"
	"strings"

	"time"

	"ops-ticket-api/internal/code"
	"ops-ticket-api/internal/cst"
	"ops-ticket-api/internal/elasticsearch"
	"ops-ticket-api/internal/framework/cache/keys"
	"ops-ticket-api/internal/framework/cache/rds"
	"ops-ticket-api/internal/framework/cfg"
	"ops-ticket-api/internal/framework/database"
	"ops-ticket-api/internal/framework/lang"
	"ops-ticket-api/internal/framework/xerrors"
	"ops-ticket-api/internal/persistence"
	"ops-ticket-api/models"
	"ops-ticket-api/pkg/privatezone"
	"ops-ticket-api/pkg/workflow"
	"ops-ticket-api/proto/pb"
	"ops-ticket-api/services/configure"
	"ops-ticket-api/services/monitor"
	"ops-ticket-api/services/priv"
	"ops-ticket-api/services/reporter"
	ticketSrv "ops-ticket-api/services/ticket"
	"ops-ticket-api/utils"

	"github.com/go-redis/redis/v8"
	"github.com/labstack/echo/v4"
	"github.com/spf13/cast"
	"github.com/spf13/viper"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"go.uber.org/zap"
	"golang.org/x/sync/errgroup"
	"gorm.io/gorm"
)

func NewTicketSrv() *ticketService {
	return &ticketService{}
}

type ticketService struct{}

func (srv *ticketService) TicketDetail(ctx echo.Context, param *pb.TkDetailReq) (*pb.TkDetailResp, error) {
	fun := "ticketService.TicketDetail ->"
	tkInfo, err := persistence.NewTicket().GetTicketDetail(ctx, param.TicketId)
	if err != nil {
		return nil, err
	}
	if tkInfo == nil || tkInfo.TicketId == 0 {
		logger.Error(ctx.Request().Context(), "ticket detail empty.", zap.Uint64("ticket_id", param.TicketId), zap.String("fun", fun))
		return nil, xerrors.New("ticket detail empty.", code.DbDataUnexpect)
	}
	if err := srv.validateTicketDetail(ctx, param, tkInfo); err != nil {
		logger.Error(ctx.Request().Context(), "validateTicketDetail return fail", zap.Uint64("ticket_id", param.TicketId), zap.String("fun", fun), zap.Any("detail", tkInfo))
		return nil, xerrors.New(code.StatusText(code.InvalidParams), code.InvalidParams)
	}

	if param.Lang == "" {
		param.Lang = ctx.Request().Header.Get("lang")
	}

	var (
		eg errgroup.Group
		// replenishList []*pb.TkDetailResp_Replenish
		// replies    []*pb.TkDetailResp_Replenish
		reopenList []*models.FpOpsTicketsReopen
	)

	ticketDest := &pb.TkDetailResp{
		TicketId:      param.TicketId,
		Filed:         tkInfo.Field,
		Done:          tkInfo.Closed > 0,
		Closed:        tkInfo.Closed,
		Csi:           tkInfo.Csi,
		CatId:         tkInfo.CatId,
		TicketSysType: pb.TicketSys_ticketSysNew,
		Label:         make([]uint32, 0),
		Replenish:     make([]*pb.TkDetailResp_Replenish, 0),
		Reopen:        make([]*pb.TkDetailResp_Reopen, 0),
		Commu:         make([]*pb.TkDetailResp_Commu, 0),
		IsChatTicket:  tkInfo.TicketType,
	}

	// 问题分类
	eg.Go(func() error {
		if category, _ := persistence.NewCat().CatNameSlice(ctx.Request().Context(), tkInfo.CatId, param.Lang); len(category) > 0 {
			ticketDest.Category = category[len(category)-1]
		}
		return nil
	})
	// 回复
	//eg.Go(func() error {
	//	replies, _ = persistence.NewTicket().GetTicketRepliesForPlayer(ctx.Request().Context(), nil, param.TicketId)
	//	if len(replies) > 0 {
	//		for i := len(replies) - 1; i >= 0; i-- {
	//			if replies[i].Op == uint32(pb.TicketStage_TicketResolved) || replies[i].Op == uint32(pb.TicketStage_TicketClosed) {
	//				ticketDest.Solution = replies[i].Remark
	//				break
	//			}
	//		}
	//	}
	//	return nil
	//})
	// 对话记录
	var commus []*models.FpOpsTicketsCommu
	eg.Go(func() error {
		commus, _ = persistence.NewTicket().GetTicketCommuns(ctx.Request().Context(), param.TicketId)
		if len(commus) > 0 {
			sort.SliceStable(ticketDest.Commu, func(i, j int) bool {
				return ticketDest.Commu[i].CreatedAt < ticketDest.Commu[j].CreatedAt
			})
		}
		return nil
	})
	//// 补填
	//if tkInfo.Proof != 0 {
	//	eg.Go(func() error {
	//		replenishList, _ = persistence.NewTicket().PlayerRefillListByTicketId(ctx, param.TicketId)
	//		return nil
	//	})
	//}

	// 重开单信息
	if tkInfo.ReopenNum > 0 {
		eg.Go(func() error {
			reopen, err := persistence.NewTicket().GetReopenList(ctx.Request().Context(), param.TicketId)
			if err != nil {
				return err
			}
			reopenList = reopen
			return nil
		})
	}
	// 获取超时模版
	ticketDest.OvertimeReply = ""
	eg.Go(func() error {
		tkCatInfo, err := configure.CatInfo(ctx, tkInfo.Project, tkInfo.CatId, map[string]string{})
		if err != nil {
			return err
		}
		//  模版id查询overtime和文案
		remindTime, contentMap, err := persistence.NewOvertimeTpl().GetOvertimeByTplId(ctx.Request().Context(), tkCatInfo.OvertimeReplyID)
		if err != nil {
			return err
		}
		// 没有配置文案
		if remindTime == 0 {
			return nil
		}
		//  根据超时key来获取超时时间
		curScore, err := rds.RCli.ZScore(ctx.Request().Context(), keys.TicketTimeWatchPlayerListV3, cast.ToString(tkInfo.TicketId)).Result()
		if errors.Is(err, redis.Nil) { // 不存在
			return nil
		} else if err != nil {
			logger.Errorf(ctx.Request().Context(), "%s get overtime zset score by ticketId err. ticketId:%d. err:%v", fun, tkInfo.TicketId, err)
			return err
		} else {
			// 已变更状态，不需要设置超时未回复
			if tkInfo.Stage != uint32(pb.TkStage_TkStageAgentReplied) || tkInfo.Priority == 1 {
				return nil
			}
			// 未到提醒时间
			if curScore-float64(remindTime*60) > float64(utils.NowTimestamp()) {
				return nil
			}
			// 根据语言返回超时文案
			if _, ok := contentMap[param.Lang]; !ok {
				param.Lang = "en"
			}
			ticketDest.OvertimeReply = contentMap[param.Lang]
			ticketDest.Overtime = utils.TimeFormatInLoc(int64(curScore))
		}
		return nil
	})

	if err := eg.Wait(); err != nil {
		return nil, err
	}

	// ticket 会话 、处理结果、重开信息等
	showCommus := make([]*pb.TkDetailResp_Commu, 0)
	showReopen := make([]*pb.TkDetailResp_Reopen, 0)
	for _, v := range reopenList {
		// showReopen = append(showReopen, &pb.TkDetailResp_Reopen{
		// 	ReplyId:         v.ReplyId,
		// 	ResponseReplyId: v.ResponseReplyId,
		// 	ReopenId:        v.ID,
		// 	FillContent:     v.Content,
		// 	Files:           v.Files,
		// 	Replenish:       make([]*pb.TkDetailResp_Replenish, 0),
		// 	CreatedAt:       utils.TimeFormatInLoc(int64(v.CreatedAt)),
		// })

		type fileStruct struct {
			FileType string `json:"file_type"`
			Url      string `json:"url"`
		}

		var files []fileStruct
		if err := json.Unmarshal([]byte(v.Files), &files); err != nil {
			logger.Error(ctx.Request().Context(), "json.Unmarshal pictureUrl err", zap.String("err", err.Error()))
		}

		pictureUrl := make([]string, 0)
		videoUrl := make([]string, 0)
		for _, file := range files {
			if file.FileType == "image" {
				pictureUrl = append(pictureUrl, file.Url)
			}
			if file.FileType == "video" {
				videoUrl = append(videoUrl, file.Url)
			}
		}

		// 对话展示
		dt := &pb.TkDetailResp_Commu{
			Content:        v.Content,
			Role:           pb.UserRole(pb.UserRole_PlayerRole),
			CustomNickName: "",
			Picture:        strings.Join(pictureUrl, ","),
			Video:          strings.Join(videoUrl, ","),
			OpType:         uint32(pb.TkEvent_TkEventReopen),
			CreatedAt:      utils.TimeFormat(int64(v.CreatedAt)),
		}
		showCommus = append(showCommus, dt)
	}
	// var solutionFlag bool
	for _, commu := range commus {
		if commu.CommuType == pb.CommuType_CommuTypeRemark.String() {
			continue
		}
		// if commu.OpType == uint8(pb.TkEvent_TkEventCommuClose) { // 回复 && 关单
		// 	if solutionFlag == false { // 处理结果
		// 		ticketDest.Solution = commu.Detail
		// 		ticketDest.Replenish = append(ticketDest.Replenish, &pb.TkDetailResp_Replenish{
		// 			TicketId:  commu.TicketID,
		// 			Remark:    commu.Detail,
		// 			Op:        6, // 处理完成 -- todo 待确认
		// 			CreatedAt: utils.TimeFormatInLoc(int64(commu.CreatedAt)),
		// 			CreatedTs: commu.CreatedAt,
		// 		})
		// 		solutionFlag = true
		// 		continue
		// 	}
		// 	var has bool
		// 	for i, ropen := range showReopen { // 重开 - 处理结果
		// 		if ropen.ResponseReplyId == commu.ID {
		// 			showReopen[i].Replenish = append(showReopen[i].Replenish, &pb.TkDetailResp_Replenish{
		// 				ReplenishId: 0,
		// 				TicketId:    commu.TicketID,
		// 				Remark:      commu.Detail,
		// 				Op:          6, // 处理完成 -- todo 待确认
		// 				CreatedAt:   utils.TimeFormatInLoc(int64(commu.CreatedAt)),
		// 				CreatedTs:   commu.CreatedAt,
		// 			})
		// 			has = true
		// 			break
		// 		}
		// 	}
		// 	if has { // 已处理 - 非 对话展示
		// 		continue
		// 	}
		// }
		// 对话展示
		dt := &pb.TkDetailResp_Commu{
			Content:        commu.Detail,
			Role:           pb.UserRole(commu.FromRole),
			CustomNickName: "",
			Picture:        commu.Picture,
			Video:          commu.Video,
			OpType:         uint32(commu.OpType),
			CreatedAt:      utils.TimeFormat(int64(commu.CreatedAt)),
		}
		if dt.Role == pb.UserRole_ServiceRole {
			dt.CustomNickName = commu.Operator
		}
		showCommus = append(showCommus, dt)
	}
	sort.Slice(showCommus, func(i, j int) bool {
		return showCommus[i].CreatedAt < showCommus[j].CreatedAt
	})
	ticketDest.Commu = showCommus
	ticketDest.Reopen = showReopen

	//replenishList = append(replenishList, replies...)
	//if len(replenishList) > 0 {
	//	sort.SliceStable(replenishList, func(i, j int) bool {
	//		return replenishList[i].CreatedTs < replenishList[j].CreatedTs
	//	})
	//	if len(reopenList) == 0 { // 没有重开信息
	//		ticketDest.Replenish = replenishList
	//	}
	//}
	//_replenishIdx := 0
	//_respReopenList := make([]*pb.TkDetailResp_Reopen, len(reopenList))
	//for _reopenIdx := range reopenList {
	//	_respReopenList[_reopenIdx] = &pb.TkDetailResp_Reopen{
	//		ReopenId:    reopenList[_reopenIdx].ID,
	//		FillContent: reopenList[_reopenIdx].Content,
	//		Files:       reopenList[_reopenIdx].Files,
	//		Replenish:   make([]*pb.TkDetailResp_Replenish, 0),
	//		CreatedAt:   utils.TimeFormatInLoc(int64(reopenList[_reopenIdx].CreatedAt)),
	//	}
	//	for ; _replenishIdx < len(replenishList); _replenishIdx++ {
	//		_replenishTs := replenishList[_replenishIdx].CreatedTs
	//		if _replenishTs > reopenList[_reopenIdx].CreatedAt {
	//			if _reopenIdx == len(reopenList)-1 || _replenishTs < reopenList[_reopenIdx+1].CreatedAt {
	//				_respReopenList[_reopenIdx].Replenish = append(_respReopenList[_reopenIdx].Replenish, replenishList[_replenishIdx])
	//			} else {
	//				break
	//			}
	//		} else if _reopenIdx == 0 {
	//			ticketDest.Replenish = append(ticketDest.Replenish, replenishList[_replenishIdx])
	//		}
	//	}
	//}
	//ticketDest.Reopen = _respReopenList

	// tips -- todo change status
	transfer, ok := workflow.TransferTipsToShow[pb.TkStage(tkInfo.Stage)]
	if !ok {
		transfer = 1
	}
	ticketDest.Transfer = uint32(transfer)

	if utils.InArrayAny(tkInfo.Stage, workflow.TkDoneStage) { // 已完成
		if tkInfo.Closed == uint32(pb.UserRole_ServiceRole) {
			ticketDest.Closed = uint32(pb.UserRole_PlayerRole)
		}
		if tkInfo.Stage == uint32(pb.TkStage_TkStageAgentRejected) ||
			tkInfo.Stage == uint32(pb.TkStage_TkStageAgentResolved) || tkInfo.Csi != 0 || tkInfo.AutoReplyId > 0 { // 拒单、超时关单、已评价、自动回复 - 不允许重开
			ticketDest.ReopenDisabled = true
		}
		// 工单处理完成7天内才允许评价
		if tkInfo.Csi == 0 && tkInfo.Stage != uint32(pb.TkStage_TkStageAgentRejected) && int64(tkInfo.ClosedAt+viper.GetUint64("ticket.evaluation_time")) >= time.Now().Unix() { // 允许评价 (完成&非拒单)
			ticketDest.Appraise = true
		}
	}

	// 消除红点
	if err := persistence.NewTicket().SetMsgRead(ctx.Request().Context(),
		uint8(tkInfo.Scene), tkInfo.GmId, tkInfo.Uuid, tkInfo.AccountId, param.TicketId); err != nil {
		logger.Error(ctx.Request().Context(), "SetMsgRead err",
			zap.String("gameId", tkInfo.GmId),
			zap.String("accountId", tkInfo.AccountId),
			zap.String("uuid", tkInfo.Uuid),
			zap.Uint64("msgId", param.TicketId),
			zap.String("err", err.Error()),
		)
	}

	return ticketDest, nil
}

func (srv *ticketService) validateTicketDetail(ctx echo.Context, param *pb.TkDetailReq, tkInfo *pb.TicketDetailModel) error {
	if tkInfo == nil || tkInfo.TicketId == 0 {
		return xerrors.New("ticket detail empty.", code.DbDataUnexpect)
	}
	// 判断是否本人查询订单
	bMatchDetail := false
	if param.AccountId != "" {
		if param.AccountId == tkInfo.AccountId {
			bMatchDetail = true
		}
	}
	if !bMatchDetail && param.Uuid != "" {
		if param.Uuid == tkInfo.Uuid {
			bMatchDetail = true
		}
	}
	if !bMatchDetail {
		return fmt.Errorf("ticket detail verify user failed, all params in-match")
	}
	return nil
}

func (srv *ticketService) TicketMine(ctx echo.Context, project string, param *pb.TkMineReq) ([]*pb.TkMineResp_TkList, error) {
	fun := "ticketService.TicketMine ->"
	list, err := persistence.NewTicket().MineTickets(ctx.Request().Context(), project, param.Uuid, param.AccountId, pb.SceneType(param.Scene))
	resp := make([]*pb.TkMineResp_TkList, 0, len(list))
	if err != nil {
		return resp, err
	}
	tkNotice := rds.TicketNoticeAll(ctx.Request().Context(), param.FpxAppId, param.Uuid, param.AccountId, param.Scene)
	for _, row := range list {
		tk := &pb.TkMineResp_TkList{
			TicketId:      row.TicketID,
			CreatedAt:     utils.TimeFormatInLoc(int64(row.CreatedAt)),
			Status:        pb.TkStatus(row.Status),
			Progress:      pb.TkProgress_TkProgressUserDoing,
			Read:          1,
			TicketSysType: pb.TicketSys_ticketSysNew,
		}
		// 多语言类别, 获取
		catNameList, err := persistence.NewCat().CatNameSlice(ctx.Request().Context(), row.CatID, param.Lang)
		if err != nil {
			logger.Errorf(ctx.Request().Context(), "%s CatNameSlice return err. catId:%d. lang:%v", fun, row.CatID, param.Lang)
		}
		tk.Category = catNameList[0]

		// 是否已读
		if len(tkNotice) > 0 {
			if _, ok := tkNotice[row.TicketID]; ok {
				tk.Read = 0
			}
		}
		tk.Progress = workflow.TkStageToProgress[pb.TkStage(row.ConversionNode)]
		resp = append(resp, tk)
	}
	return resp, nil
}

func (srv *ticketService) TicketCreate(ctx echo.Context, project string, param *models.TicketCreateParam) (uint64, bool, error) {
	if param.Input.Fields == "" || param.Input.Fields == "{}" {
		return 0, false, xerrors.New(code.StatusText(code.InvalidParams), code.InvalidParams)
	}
	// 拦截重复的工单创建请求(2分钟内不允许重复创建)
	ticketCreateUniqueKey := fmt.Sprintf("%v:%v:%v:%v", param.Input.Uid, param.Input.Fpid, param.Input.AccountId, param.Input.Uuid)
	redisKey := keys.TicketCreateNoRepeat + ticketCreateUniqueKey
	redisTTL := 2 * time.Second
	ok, err := rds.RCli.Client.SetNX(ctx.Request().Context(), redisKey, true, redisTTL).Result()
	if err != nil {
		logger.Error(ctx.Request().Context(), "error setting redis key for ticket creation", zap.String("ticketCreateUniqueKey", ticketCreateUniqueKey))
		return 0, false, err
	}
	if !ok {
		// Key already exists, meaning a similar request was made within the last 2 minutes
		return 0, false, xerrors.New(code.StatusText(code.RepeatData), code.RepeatData)
	}

	// check frequency rules
	if _, err := ticketSrv.NewTicketCreateRules().CheckCreateTicketRule(ctx, param); err != nil {
		return 0, false, err
	}

	tkCatInfo, err := configure.CatInfo(ctx, project, param.Input.CatId, map[string]string{
		"country": param.Input.Country,
		"channel": param.Input.Channel,
		"sid":     param.Input.Sid,
	})
	if err != nil {
		return 0, false, err
	}
	param.CatIdFirst = tkCatInfo.OneLevel
	param.CatIdSecond = tkCatInfo.SecondLevel
	if tkCatInfo.Level != uint32(pb.CatLevel_ThreeClassification) {
		return 0, false, xerrors.New(code.StatusText(code.InvalidParams), code.InvalidParams)
	}
	// 用户/设备在该问题类型下是否存在未完成工单
	//if ok := persistence.NewTicket().ExistTicketByCatId(ctx, project, param.Input.Uuid, param.Input.AccountId, param.Input.Scene, param.Input.CatId); ok {
	//	return 0, xerrors.New(code.StatusText(code.TicketCatRepeat), code.TicketCatRepeat)
	//}

	// 查询提单用户及问题用户的crmVip
	param.SubmitUser = CrmVip(ctx, project, int64(param.Input.Uid), int64(param.Input.Fpid), param.Input.GameId, param.Input.FpxAppId, param.Input.AccountId)
	if param.SubmitUser.Uid == 0 { // crm未查到此用户
		param.SubmitUser.Uid = int64(param.Input.Uid)
	}

	// 判断是否是玩家给自己提单
	if param.Input.TroubleUid > 0 && param.Input.Uid != param.Input.TroubleUid {
		// 数据组接口根据uid和game_project 换 fpid
		troubleUserTotalPay, troubleUserChannel := float64(0), ""
		param.Input.TroubleAccountId, troubleUserTotalPay, troubleUserChannel = DwhUserInfo(ctx, project, int64(param.Input.TroubleUid))
		if param.Input.TroubleAccountId != "" {
			troubleUser := CrmVip(ctx, project, int64(param.Input.TroubleUid), 0, param.Input.GameId, param.Input.FpxAppId, param.Input.TroubleAccountId)
			if troubleUser.Uid > 0 { // crm系统存在此用户
				param.TroubleUser = troubleUser
			} else {
				userType := int8(pb.UserTypeEnum_UserTypeRegularUser)
				if troubleUserTotalPay > 0 {
					userType = int8(pb.UserTypeEnum_UserTypePaidUser)
				}
				param.TroubleUser = models.TicketUser{
					Uid:      int64(param.Input.TroubleUid),
					UserType: userType,
					TotalPay: troubleUserTotalPay,
					Channel:  troubleUserChannel,
					SVIP:     int8(pb.SVIP_SVIPNo),
				}
			}
		}
	} else {
		param.TroubleUser = param.SubmitUser
	}

	tkObj := models.TicketAssign(project, tkCatInfo, param)
	tkObj.Recharge = cast.ToUint64(param.Input.TotalPay * code.RechargeRate)
	if tkObj.Recharge > code.VipRechargePayAmount { // vip 用户优先分配
		tkObj.Vip = 2
	}
	now := uint64(time.Now().Unix())
	if utils.IsVipUser(param.Input.Qfrom) {
		tkObj.SystemTags = append(tkObj.SystemTags, &models.FpOpsTicketsSystemTag{
			LabelID:   code.LabelTicketVipUser,
			Op:        "system",
			CreatedAt: now,
			UpdatedAt: now,
		})
	}
	if utils.IsPrivateZone(param.Input.ZoneFrom) {
		tkObj.SystemTags = append(tkObj.SystemTags, &models.FpOpsTicketsSystemTag{
			LabelID:   code.LabelPrivateZone,
			Op:        "system",
			CreatedAt: now,
			UpdatedAt: now,
		})
	}
	// 判断是否为新版AI客服单
	if param.Input.TicketType {
		tkObj.TicketType = uint32(pb.TicketType_TicketTypeNewAi)
		tkObj.IsInvalid = param.Input.IsInvalid

	} else {
		tkObj.SolveType = uint32(pb.SolveType_SolveTypeManualTicket)
	}

	//  获取私域R级
	tkObj.ZoneVipLevel = privatezone.PrivateVipLevel(ctx.Request().Context(), param.Input.Uid, project)

	// 是否配置自动回复
	useNewDb := true
	//replyTplID := tkCatInfo.SwiftReplyTplID
	//if replyTplID == 0 {
	//	useNewDb = false
	//	replyTplID = tkCatInfo.ReplyTplID
	//}
	replyTplID := tkCatInfo.GetSwiftReplyTplID(ctx.Request().Context(), &pb.TkCatAutoReplyParamDf{TotalPay: tkObj.Recharge})
	if replyTplID != 0 {
		logger.Info(ctx.Request().Context(), "category config system reply, need modify ticket acceptor",
			zap.Uint32("cat_id", param.Input.CatId), zap.Uint32("reply_tpl_id", replyTplID))
		tkObj.Status = uint32(pb.TkStatus_TkStatusProcessing) // fixme
		tkObj.Acceptor = pb.UserRole_SystemRole.String()
		tkObj.FirstReplyAt = uint64(time.Now().Unix())
		tkObj.AutoReplyID = replyTplID
		tkObj.SolveType = uint32(pb.SolveType_SolveTypeAutoReplyTicket)
		ctx.Set(cst.TicketReplyTplCtx, replyTplID)
	}

	if (param.TroubleUser.Uid > 0 || param.TroubleUser.AccountID != "") && param.TroubleUser.UserType > 0 {
		tkObj.TroubleUser = models.TroubleUser(ctx, param, tkObj)
		if param.TroubleUser.TotalPay > 0 {
			tkObj.TroubleUser.Recharge = cast.ToUint64(param.TroubleUser.TotalPay * code.RechargeRate)
			if tkObj.TroubleUser.Recharge > code.VipRechargePayAmount {
				tkObj.TroubleUser.VIP = 2
			}
		}
	}

	trans := database.Db().Begin()
	defer trans.Rollback()

	if param.Input.PrivRightsUse {
		// 私域权益单
		tkObj.SystemTags = append(tkObj.SystemTags, &models.FpOpsTicketsSystemTag{
			LabelID:   uint32(pb.TicketSystemTag_PrivateZoneCard),
			Op:        "system",
			CreatedAt: now,
			UpdatedAt: now,
		})
	}

	tkObj.ConversationID = param.Input.ConversationId
	ticketID, err := persistence.NewTicket().TicketCreate(ctx, trans, tkObj)
	if err != nil {
		return 0, false, err
	}
	// 更新
	if tkObj.ConversationID != "" {
		err := persistence.NewConversationRepo(trans).UpdateConversationStatus(ctx.Request().Context(), tkObj.ConversationID, int32(pb.ConversationStatus_ConversationStatusClose), ticketID)
		if err != nil {
			return 0, false, err
		}
	}

	// 私域权益使用
	if param.Input.PrivRightsUse {
		privCardUseReq := &priv.PrivCardUseReq{
			Uid:     cast.ToString(param.Input.Uid),
			Fpid:    cast.ToString(param.Input.AccountId),
			Project: project,
			CatId:   param.Input.CatId,
		}
		_, err := priv.UsePrivCard(ctx.Request().Context(), privCardUseReq)
		if err != nil {
			logger.Errorf(ctx.Request().Context(), "TicketCreate return err. err:%v", err)
			// todo 权益卡使用失败错误码
			return 0, false, xerrors.New(lang.FormatText(ctx, "NoAvailablePrivCard"), code.NoAvailablePrivCard)
		}
		// 权益卡使用成功
	}

	// 提交事务
	if err := trans.Commit().Error; err != nil {
		return 0, false, err
	}

	// do save after

	// 不同问题分类积攒5个飞书群报警`
	go func() {
		if err := monitor.NewTicketMonitor().MonitorTicketCreate(
			ctx.Request().Context(),
			project,
			param.Input.CatId,
			tkObj.TicketID,
		); err != nil {
			logger.Error(ctx.Request().Context(), "monitor ticket create failed",
				zap.String("project", project),
				zap.Uint32("cat_id", param.Input.CatId),
				zap.Error(err))
		}
	}()

	eg, egCtx := errgroup.WithContext(ctx.Request().Context())
	eg.Go(func() error {
		if param.TroubleUser.Uid > 0 && param.TroubleUser.Uid != param.SubmitUser.Uid && param.TroubleUser.UserType > 0 {
			tkObj.TroubleUser.TicketID = tkObj.TicketID
			return persistence.DefaultTroubleUserRepo.AddTicketTroubleUser(ctx, tkObj.TroubleUser)
		}
		return nil
	})
	eg.Go(func() error { // write to es
		defer func() {
			if err := recover(); err != nil {
				logger.Errorf(ctx.Request().Context(), "recover err:%v", err)
			}
		}()
		elasticsearch.DefaultTicketSyncSvc.CreateTicket(egCtx, tkObj)
		reporter.PubCreate(egCtx, tkObj)
		return nil
	})
	eg.Go(func() error { // set ticket create rules
		ticketSrv.NewTicketCreateRules().TicketCreateSucSetRule(egCtx, param)
		return nil
	})
	eg.Wait()
	return tkObj.TicketID, useNewDb, nil
}

func (srv *ticketService) TicketReplenish(ctx echo.Context, req *pb.TkReplenishReq) error {
	ticketInfo, err := persistence.NewTicket().GetTicketInfoFromMaster(ctx.Request().Context(), req.TicketId)
	if err != nil {
		return xerrors.New(code.StatusText(code.NotFound), code.NotFound)
	}
	if utils.InArrayAny(ticketInfo.ConversionNode, workflow.TkDoneStage) {
		return xerrors.New(code.StatusText(code.ProofClosedByTimeout), code.ProofClosedByTimeout)
	}
	refillDest, err := persistence.NewTicket().GetPlayerProofRefillDest(ctx.Request().Context(), req.TicketId)
	if err != nil {
		return err
	}
	fromStage := ticketInfo.ConversionNode
	toStage := uint32(pb.TkStage_TkStageWaitingForAgent)
	if err := persistence.NewTicket().RefillSave(ctx, refillDest.ID, req.TicketId, req.Content, req.Files, toStage); err != nil {
		return err
	}
	newCtx := utils.ContextOnlyValue{ctx.Request().Context()}
	go func() {
		reporter.PlayerRefill(newCtx, req.TicketId, req.AccountId, req.Uuid, req.Content, fromStage)
		elasticsearch.DefaultTicketSyncSvc.RefillStatus(newCtx, req.TicketId, fromStage, toStage)
	}()
	return nil
}

// TicketReopen 重开单
func (srv *ticketService) TicketReopen(ctx echo.Context, req *pb.TkReplenishReq) error {
	fun := "ticketService.TicketReopen"
	// 幂等
	reopenK := fmt.Sprintf("ops:new:ticket:reopen:%d", req.TicketId)
	success, err := rds.RCli.SetNX(ctx.Request().Context(), reopenK, "1", time.Minute).Result()
	if err != nil {
		return xerrors.New(code.StatusText(code.DbError), code.DbError)
	}
	if !success {
		logger.Info(ctx.Request().Context(), "ticket reopen submit concurrent", zap.Uint64("ticket_id", req.TicketId))
		return nil
	}
	defer rds.RCli.Del(ctx.Request().Context(), reopenK)
	// 1. 获取详情
	ticketInfo, err := persistence.NewTicket().GetTicketInfoFromMaster(ctx.Request().Context(), req.TicketId)
	if err != nil {
		return xerrors.New(code.StatusText(code.NotFound), code.NotFound)
	}
	if ticketInfo.Status != uint32(pb.TkStatus_TkStatusDone) {
		return xerrors.New(code.StatusText(code.MissingParams), code.MissingParams)
	}
	// // 已评价
	if ticketInfo.Evaluation && ticketInfo.Csi > 0 {
		return xerrors.New(code.StatusText(code.MissingParams), code.MissingParams)
	}
	// 同一分类只允许有一个
	//if ok := persistence.NewTicket().ExistTicketByCatId(ctx, ticketInfo.Project, req.Uuid, req.AccountId, uint32(ticketInfo.Scene), ticketInfo.CatID); ok {
	//	return xerrors.New(code.StatusText(code.TicketCatRepeat), code.TicketCatRepeat)
	//}

	// 过滤重开单
	if ticketInfo.AutoReplyID > 0 {
		return xerrors.New(code.StatusText(code.TicketReopenReply), code.TicketReopenReply)
	}

	var reopenInfo *models.FpOpsTicketsReopen
	ticketRepo := persistence.NewTicket()
	eg, _ := errgroup.WithContext(ctx.Request().Context())
	eg.Go(func() error {
		_reopenInfo, _err := ticketRepo.LastReopenInfo(ctx.Request().Context(), req.TicketId)
		if _err != nil {
			return _err
		}
		reopenInfo = _reopenInfo
		return nil
	})
	if err = eg.Wait(); err != nil {
		return xerrors.New(code.StatusText(code.DbError), code.DbError)
	}
	ticketInfo.ReopenNum = reopenInfo.Num + 1
	// 获取最新的回复内容 写入reopen表
	finishCommu, _ := ticketRepo.GetTicketSolution(ctx.Request().Context(), nil, req.TicketId)
	if finishCommu == nil {
		return xerrors.New(code.StatusText(code.MissingParams), code.MissingParams)
	}

	now := time.Now()
	tx := database.Db().WithContext(ctx.Request().Context()).Begin()
	defer tx.Rollback()

	reopenData := &models.FpOpsTicketsReopen{
		TicketID:  req.TicketId,
		ReplyId:   finishCommu.ID,
		Content:   req.Content,
		Files:     req.Files,
		Origin:    uint8(pb.Origin_Player),
		Num:       reopenInfo.Num + 1,
		CreatedAt: uint64(now.Unix()),
		UpdatedAt: uint64(now.Unix()),
	}
	var hist *models.FpOpsTicketsHistory

	eg.Go(func() error {
		// 重开日志
		_err := persistence.NewTicket().AddReopen(ctx.Request().Context(), tx, reopenData)
		if _err != nil {
			return _err
		}
		// 工单历史增加重开日志
		_hist, _err := reporter.PubReopen(ctx.Request().Context(), tx, ticketInfo, reopenInfo)
		if _err != nil {
			return _err
		}
		hist = _hist
		return nil
	})
	if err = eg.Wait(); err != nil {
		return xerrors.New(code.StatusText(code.DbError), code.DbError)
	}
	// reopen ticket -> add to watch list
	defer func() {
		if err := rds.RCli.ZAdd(ctx.Request().Context(), keys.TicketAcceptor2HNoActionListV3, &redis.Z{
			Score: float64(time.Now().Unix() + cfg.GetAllocNoOpTimeout()), Member: req.TicketId,
		}).Err(); err != nil {
			logger.Errorf(ctx.Request().Context(), "%s reopen no alloc watch list. err:%v. ticketId:%d.", fun, req.TicketId)
		}
	}()
	// 修改工单ES doc
	if err = elasticsearch.DefaultTicketSyncSvc.AddReopen(ctx.Request().Context(), req.TicketId, hist, reopenData); err != nil {
		return xerrors.New(code.StatusText(code.DbError), code.DbError)
	}
	return tx.Commit().Error
}

func (srv *ticketService) TkAppraise(ctx echo.Context, param *pb.TkAppraiseReq) error {
	fun := "ticketService.TkAppraise"
	err := persistence.NewTicket().TkAppraise(ctx, param)
	if err != nil {
		logger.Error(ctx.Request().Context(), "ticket_tkAppraise return err.", zap.String("fun", fun), zap.Any("err", err), zap.Uint64("ticket_id", param.TicketId))
		return err
	}
	return nil
}

func (srv *ticketService) TkAppraiseFeedback(ctx echo.Context, param *pb.TkAppraiseFeedbackReq) error {
	fun := "ticketService.TkAppraiseFeedback"
	saved, err := persistence.NewTicket().TkAppraiseFeedback(ctx.Request().Context(), param)
	if err != nil {
		logger.Error(ctx.Request().Context(), "ticket_tkAppraise return err.", zap.String("fun", fun), zap.Any("err", err), zap.Uint64("ticket_id", param.TicketId))
		return err
	}
	if saved {
		newCtx := utils.ContextOnlyValue{ctx.Request().Context()}
		elasticsearch.DefaultTicketSyncSvc.UpdateTicket(newCtx, param.TicketId, map[string]interface{}{
			"resolve_confirmed": param.Resolved,
		})
	}
	return nil
}

// TkCommunicate 玩家回复消息
func (srv *ticketService) TkCommunicate(ctx echo.Context, param *pb.TkCommunicateReq) error {
	fun := "ticketService.TkCommunicate"
	// 1. check ticket detail
	tkDetail, err := persistence.NewTicket().GetTicketInfoFromMaster(ctx.Request().Context(), param.TicketId)
	if err != nil {
		logger.Errorf(ctx.Request().Context(), "%s GetTicketInfoFromMaster return err. err:%v. %d", fun, err, param.TicketId)
		return xerrors.New(code.StatusText(code.NotFound), code.NotFound)
	}

	if tkDetail.AccountID != param.AccountId {
		if param.Scene == uint32(pb.SceneType_Loading) && tkDetail.UUID != "" && tkDetail.UUID == param.Uuid {
			// 游戏外 --  uuid 相同 继续执行沟通逻辑
		} else {
			logger.Errorf(ctx.Request().Context(), "%s ticket detail accountId not match .%d .%+v", fun, param.TicketId, param)
			return xerrors.New(code.StatusText(code.InvalidParams), code.InvalidParams)
		}
	}
	if tkDetail.Status == uint32(pb.TkStatus_TkStatusDone) { // 已完成 - 禁止再沟通
		logger.Info(ctx.Request().Context(), "ticket detail status has done forbidden communicate.", zap.String("fun", fun), zap.Uint64("ticket_id", param.TicketId), zap.Any("param", param))
		return xerrors.New(code.StatusText(code.ClosedTkForbidCommu), code.ClosedTkForbidCommu)
	}

	now := utils.NowTimestamp()
	// 2. save communicate log
	tkCommu := &models.FpOpsTicketsCommu{
		TicketID:  param.TicketId,
		FromRole:  uint32(pb.UserRole_PlayerRole),
		CommuType: pb.CommuType_CommuTypeDialogue.String(),
		Detail:    param.Content,
		Operator:  param.AccountId,
		Picture:   param.Picture,
		Video:     param.Video,
		CreatedAt: now,
		UpdatedAt: now,
	}
	hist := &models.FpOpsTicketsHistory{
		TicketID:  param.TicketId,
		Acceptor:  tkDetail.Acceptor,
		Operate:   uint8(pb.TkEvent_TkEventUserCommu),
		OpDetail:  tkDetail.ConversionNode,
		OpRole:    uint8(pb.UserRole_PlayerRole),
		Remark:    param.Content,
		Operator:  param.AccountId,
		CreatedAt: now,
		UpdatedAt: now,
	}

	dest := map[string]interface{}{
		"sort_wait_start_at": now,
		"updated_at":         utils.NowTimestamp(),
	}

	if utils.InArrayAny(tkDetail.ConversionNode, workflow.TkUserCommuChangeStage) {
		dest["conversion_node"] = int32(pb.TkStage_TkStageWaitingForAgent)
	}
	err = persistence.NewTicket().TicketTransfer(ctx.Request().Context(), param.TicketId, dest, tkCommu, hist, func(txDb *gorm.DB) error {
		// update es ticket
		return elasticsearch.DefaultTicketSyncSvc.UpdateTicket(ctx.Request().Context(), param.TicketId, dest)
	})
	if err != nil {
		logger.Errorf(ctx.Request().Context(), "%s ticket user add tkCommu .TicketTransfer. return err. ticketId:%d. err:%v. commu:%+v", fun, param.TicketId, err, tkCommu)
		return xerrors.New("EventUpdateFailLog", code.DbError)
	}
	// add to watch list
	defer func() {
		if err := rds.RCli.ZAdd(ctx.Request().Context(), keys.TicketAcceptor2HNoActionListV3, &redis.Z{
			Score: float64(time.Now().Unix() + cfg.GetAllocNoOpTimeout()), Member: param.TicketId,
		}).Err(); err != nil {
			logger.Errorf(ctx.Request().Context(), "%s player communicate no alloc watch list. err:%v. ticketId:%d.", fun, param.TicketId)
		}
	}()
	return nil
}

// NoticeV3 通用版本消息通知
func (srv *ticketService) NoticeV3(ctx echo.Context, param *pb.NoticeFpxRequestV3) (*pb.NoticeResp, error) {
	if param.Scene == 0 {
		param.Scene = 3
	}
	dest := &pb.NoticeResp{
		Scene:         param.Scene,
		TicketSysType: pb.TicketSys_ticketSysNew,
	}
	sceneList := []int{int(param.Scene)}
	for _, scene := range sceneList {
		var notice *pb.NoticeResp
		// 通用版新红点逻辑
		notice = rds.TicketGetNotice(ctx.Request().Context(), param.FpxAppId, param.DeviceId, param.AccountId, uint8(scene))
		if notice == nil {
			logger.Info(ctx.Request().Context(), "no message notice", zap.Int("scene", scene))
			continue
		}
		dest.From = notice.From
		dest.NoticeId = notice.NoticeId
		dest.ObjectId = notice.ObjectId
		dest.NoticeCount = notice.NoticeCount
		break
	}
	if dest.ObjectId == 0 {
		return dest, xerrors.New("no message", code.NotFound)
	}
	return dest, nil
}
