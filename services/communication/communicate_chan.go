package communication

import (
	"context"
	"github.com/shiningrush/goevent"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"go.uber.org/zap"
)

// 文件导入批量处理
var (
	messageBatchSendEventCommunicateChan = &communicateChan{
		id: make(chan int64, 1024), //
	}
	QuestionTrainingCommunicateChan = &communicate<PERSON>han{
		id: make(chan int64, 1024), //
	}
)

func init() {
	if err := goevent.Subscribe(&messageBatchSendEvent{}); err != nil {
		logger.Fatal(context.TODO(), "goevent.Subscribe err .messageBatchSendEvent ", zap.Any("err", err))
	}
	if err := goevent.Subscribe(&questionTrainingEvent{}); err != nil {
		logger.Fatal(context.TODO(), "goevent.Subscribe err .questionTrainingEvent ", zap.Any("err", err))
	}
}

// PushMessageBatchSend 批量私信入队列
func PushMessageBatchSend(id int64) {
	goevent.Publish(&messageBatchSendEvent{Id: id})
}

// PopMessageBatchSend 批量私信出队列
func PopMessageBatchSend() int64 {
	return messageBatchSendEventCommunicateChan.Pop()
}

// 内部实现pub/sub 功能 - 不阻塞业务
type messageBatchSendEvent struct {
	Id int64
}

func (e *messageBatchSendEvent) Topic() []string {
	return []string{"MessageBatchSend"}
}
func (e *messageBatchSendEvent) Handle(ctx context.Context, event goevent.Event) {
	e, ok := event.(*messageBatchSendEvent)
	if !ok {
		logger.Errorf(ctx, "%s: %v. event:%+v", "messageBatchSendEvent.Handle", "event type error", event)
		return
	}
	if e.Id < 1 {
		logger.Errorf(ctx, "%s: %v. event.Id:%d", "messageBatchSendEvent.Handle", "event id lte0", e.Id)
	}
	var len = messageBatchSendEventCommunicateChan.Len()
	var cap = messageBatchSendEventCommunicateChan.Cap()
	defer func() {
		if len > 50 {
			logger.Errorf(ctx, "%s: %v. messageBatchSendEventCommunicateChan.Len():%d", "messageBatchSendEvent.Handle", "messageBatchSendEventCommunicateChan.Len() > 50", len)
		}
	}()
	if cap-len > 2 {
		messageBatchSendEventCommunicateChan.Push(e.Id)
	} else {
		logger.Errorf(ctx, "%s: %v. Cap:%d, Len:%d", "messageBatchSendEvent.Handle", "messageBatchSendEventCommunicateChan Cap()-Len() <= 2", cap, len)
	}
}

// PushQuestionTraining 工单知识库训练入队列
func PushQuestionTraining(id int64) {
	goevent.Publish(&questionTrainingEvent{Id: id})
}

// PopQuestionTraining 批量私信出队列
func PopQuestionTraining() int64 {
	return QuestionTrainingCommunicateChan.Pop()
}

// 内部实现pub/sub 功能 - 不阻塞业务
type questionTrainingEvent struct {
	Id int64
}

func (e *questionTrainingEvent) Topic() []string {
	return []string{"questionTraining"}
}
func (e *questionTrainingEvent) Handle(ctx context.Context, event goevent.Event) {
	e, ok := event.(*questionTrainingEvent)
	if !ok {
		logger.Errorf(ctx, "%s: %v. event:%+v", "questionTrainingEvent.Handle", "event type error", event)
		return
	}
	if e.Id < 1 {
		logger.Errorf(ctx, "%s: %v. event.Id:%d", "questionTrainingEvent.Handle", "event id lte0", e.Id)
	}
	var len = QuestionTrainingCommunicateChan.Len()
	var cap = QuestionTrainingCommunicateChan.Cap()
	defer func() {
		if len > 50 {
			logger.Errorf(ctx, "%s: %v. QuestionTrainingCommunicateChan.Len():%d", "questionTrainingEvent.Handle", "QuestionTrainingCommunicateChan.Len() > 50", len)
		}
	}()
	if cap-len > 2 {
		QuestionTrainingCommunicateChan.Push(e.Id)
	} else {
		logger.Errorf(ctx, "%s: %v. Cap:%d, Len:%d", "questionTrainingEvent.Handle", "QuestionTrainingCommunicateChan Cap()-Len() <= 2", cap, len)
	}
}

// communicateChan 通信通道
type communicateChan struct {
	id chan int64
}

func (c *communicateChan) Push(id int64) {
	c.id <- id
}
func (c *communicateChan) Pop() int64 {
	return <-c.id
}
func (c *communicateChan) Len() int {
	return len(c.id)
}
func (c *communicateChan) Cap() int {
	return cap(c.id)
}
