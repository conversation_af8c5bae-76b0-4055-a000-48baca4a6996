package services

import (
	"github.com/labstack/echo/v4"
	"github.com/spf13/cast"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"go.uber.org/zap"
	"ops-ticket-api/internal/elasticsearch"
	"ops-ticket-api/internal/persistence"
	"ops-ticket-api/models"
	"ops-ticket-api/utils"
	"runtime/debug"
	"strings"
)

// DiscordInfoExportFromEs discord数据拉取
func DiscordInfoExportFromEs(ctx echo.Context, opts []elasticsearch.Option) ([]*models.FpDscUserDoc, error) {
	discords, err := elasticsearch.DefaultDscEsSvc.GetDiscordPoolScroll(ctx.Request().Context(), opts...)
	if err != nil {
		return nil, err
	}
	dest := []*models.FpDscUserDoc{}
	for dsc := range discords {
		dest = append(dest, dsc)
	}
	return dest, nil
}

// DscPoolExportGetData Discord导出
func DscPoolExportGetData(ctx echo.Context, opts []elasticsearch.Option) (<-chan []string, error) {
	records, err := DiscordInfoExportFromEs(ctx, opts)
	if err != nil && err.Error() != "EOF" {
		return nil, err
	}
	DscRecord := make(chan []string, 256)

	go func() {
		defer func() {
			if err := recover(); err != nil {
				logger.Error(ctx.Request().Context(), "Dsc export recover err",
					zap.Any("err", err),
					zap.String("stack", string(debug.Stack())))
			}
			close(DscRecord)
		}()
		libList := make(map[uint32][]string)
		var libFunc = func(libId []uint32) string {
			var show []string
			var noCacheId []uint32
			for _, id := range libId {
				if v, ok := libList[id]; ok {
					show = append(show, strings.Join(v, "-"))
				} else {
					noCacheId = append(noCacheId, id)
				}
			}
			if len(noCacheId) > 0 {
				if libs := persistence.NewTags().GetTagsSlice(ctx.Request().Context(), noCacheId); libs != nil {
					for _, id := range noCacheId {
						if v, ok := libs[id]; ok && len(v) > 0 {
							libList[id] = v
							show = append(show, strings.Join(v, "-"))
						} else {
							libList[id] = []string{"unknown"}
							show = append(show, "unknown")
						}
					}
				}
			}
			return strings.Join(show, ", ")
		}
		for i := range records {
			user := records[i]
			nickName := user.GlobalName
			if nickName == "" {
				nickName = user.UserName
			}

			payAll := utils.PayAmountToFloat64(user.PayAll)
			payLastThirtyDays := utils.PayAmountToFloat64(user.PayLastThirtyDays)

			// 1:未回复、2:已回复
			status := ""
			if user.ReplyType == 1 {
				status = "未回复"
			} else {
				status = "已回复"
			}

			lastLogin := utils.TimeFormat(user.LastLogin)
			detail := []string{
				user.DscUserID + "\t",
				nickName,
				cast.ToString(user.UID) + "\t",
				user.Sid,
				user.Project,
				status,
				lastLogin,
				cast.ToString(payAll),
				cast.ToString(payLastThirtyDays),
				cast.ToString(user.VipLevel),
				libFunc(user.Tags),
			}
			DscRecord <- detail
		}

	}()
	return DscRecord, nil
}
