package services

import (
	"encoding/json"
	"github.com/labstack/echo/v4"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"ops-ticket-api/internal/cst"
	"ops-ticket-api/internal/elasticsearch"
	p "ops-ticket-api/internal/persistence"
	"ops-ticket-api/models"
	"ops-ticket-api/proto/pb"
	"ops-ticket-api/services/commsrv"
	"sync"
)

func DiscordTabSave(ctx echo.Context, req *pb.DiscordTabAddReq) error {
	return p.NewDiscordTab().DiscordTabSave(ctx, req)
}

func DiscordTabDel(ctx echo.Context, req *pb.DiscordTabDelReq) error {
	return p.NewDiscordTab().DiscordTabDel(ctx, req)
}

// DiscordTabList discord 搜索tab列表
func DiscordTabList(ctx echo.Context) (*pb.DiscordTabListResp, error) {
	resp, err := p.NewDiscordTab().DiscordTabList(ctx)
	if err != nil {
		return nil, err
	}
	return resp, nil
}

// DiscordTabCount Dsc tab数量
func DiscordTabCount(ctx echo.Context) (*pb.DiscordTabCountResp, error) {
	permList, _ := commsrv.LoadPermGmList(ctx.Request().Context(), ctx.Get(cst.AccountInfoCtx).(string))
	if len(permList) < 0 {
		logger.Infof(ctx.Request().Context(), "GetAllTabInfo No game permissions available.")
		return nil, nil
	}
	// 获取所有tab内容
	tabList, err := p.NewDiscordTab().GetAllTabInfo(ctx, permList)
	if err != nil {
		return nil, err
	}
	// 用 map 按项目分组存储结果
	projectMap := make(map[string][]*pb.DiscordTabCountResp_TabCountDetail)
	var wg sync.WaitGroup
	// 控制并发量为20
	sem := make(chan struct{}, 20)
	results := make(chan *pb.DiscordTabCountResp_DiscordTabCount, len(tabList))

	for _, tab := range tabList {
		wg.Add(1)
		sem <- struct{}{}
		go func(tab *models.FpDscTab) {
			defer func() {
				<-sem
				wg.Done()
			}()
			searchDetail := pb.DscUserListReq{}
			if err := json.Unmarshal([]byte(tab.Detail), &searchDetail); err != nil {
				logger.Infof(ctx.Request().Context(), "failed to unmarshal detail, err:%v", err)
				return
			}
			// 筛选条件
			opts, err := optionsDiscordFilterEs(ctx, &searchDetail)
			if err != nil {
				logger.Errorf(ctx.Request().Context(), "TicketTabCount optionsFilterEs err:%v", err)
				return
			}

			// 查询
			count, err := elasticsearch.DefaultDscEsSvc.GetDiscordTabCount(ctx.Request().Context(), opts...)
			if err != nil {
				return
			}

			// 按项目分组加入 map
			results <- &pb.DiscordTabCountResp_DiscordTabCount{
				Tab: []*pb.DiscordTabCountResp_TabCountDetail{
					{TabName: tab.TabName, Count: uint64(count)},
				},
				Project: tab.Project,
			}
		}(tab)
	}
	wg.Wait()
	close(results)

	// 收集结果并根据项目分组
	for result := range results {
		projectMap[result.Project] = append(projectMap[result.Project], result.Tab...)
	}

	resp := &pb.DiscordTabCountResp{
		Detail: make([]*pb.DiscordTabCountResp_DiscordTabCount, 0, len(projectMap)),
	}

	for project, tabInfo := range projectMap {
		resp.Detail = append(resp.Detail, &pb.DiscordTabCountResp_DiscordTabCount{
			Tab:     tabInfo,
			Project: project,
		})
	}

	return resp, nil
}

func DiscordTabEdit(ctx echo.Context, req *pb.DiscordTabEditReq) error {
	return p.NewDiscordTab().DiscordTabEdit(ctx, req)
}

func DscTabUpdateSort(ctx echo.Context, req *pb.DiscordTabUpdateSortReq) error {
	return p.NewDiscordTab().DscTabUpdateSort(ctx, req)
}
