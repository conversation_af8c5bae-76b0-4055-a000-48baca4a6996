package services

import (
	"encoding/json"
	"fmt"
	"github.com/labstack/echo/v4"
	dto "ops-ticket-api/internal/persistence"
	"ops-ticket-api/models"
	"ops-ticket-api/proto/pb"
	"ops-ticket-api/utils"
	"sort"
	"strings"
)

func NewUserSrv() *userSrv {
	return &userSrv{}
}

type (
	userSrv struct{}
)

func (srv *userSrv) AccountSync(ctx echo.Context, account, nickname string, isAdmin bool) {
	dto.NewDictRepo()
	repo := dto.NewUserRepo()
	info, err := repo.GetUserInfo(ctx.Request().Context(), account)
	if err != nil {
		return
	}
	//if info != nil && info.Nickname == nickname && info.Admin == isAdmin {
	if info != nil && info.Nickname == nickname {
		return
	}
	if err := repo.FirstOrCreate(ctx.Request().Context(), account, nickname, isAdmin); err != nil {
		return
	}
	return
}

// IsAcceptorEdit 修改客服在线状态
func IsAcceptorEdit(ctx echo.Context, req *pb.GroupUserReq) error {
	dto.NewDictRepo()
	repo := dto.NewUserRepo()

	return repo.IsAcceptorEdit(ctx.Request().Context(), req.Account, req.Status)
}

// GetAcceptorStatus 客服在线状态
func GetAcceptorStatus(ctx echo.Context, req *pb.GroupUserReq) (*pb.LoginStatus, error) {
	dto.NewDictRepo()
	repo := dto.NewUserRepo()
	return repo.GetAcceptorStatus(ctx.Request().Context(), req.Account)

}

// TopTicketInfo 顶部数据
func TopTicketInfo(ctx echo.Context, req *pb.TicketIdReq) (*pb.TicketPoolTopResp, error) {
	dto.NewDictRepo()
	repo := dto.NewTicket()
	info, err := repo.TopTicketInfo(ctx.Request().Context(), req.TicketId)
	if err != nil {
		return nil, err
	}
	return info, nil
}

// TicketUserInfo 基础数据
func TicketUserInfo(ctx echo.Context, req *pb.TicketIdReq) (*pb.UserInfoResp, error) {
	dto.NewDictRepo()
	repo := dto.NewTicket()
	info, err := repo.TicketUserInfo(ctx, req.TicketId)
	if err != nil {
		return nil, err
	}
	return info, nil
}

// TicketsRecords 工单历史记录
func TicketsRecords(ctx echo.Context, ticketId uint64) (*pb.TicketRecordResp, error) {
	repo := dto.NewTicket()
	info, err := repo.TicketsUserRecords(ctx.Request().Context(), ticketId)
	if err != nil {
		return nil, err
	}
	return info, nil
}

// TicketTags 工单绑定标签列表
func TicketTags(ctx echo.Context, ticketId uint64) (*pb.TicketTagRes, error) {
	var tkTags = &pb.TicketTagRes{
		TicketId: ticketId,
		LabelId:  make([]uint32, 0),
	}
	tags, err := dto.NewTicket().GetTicketTags(ctx.Request().Context(), ticketId)
	if err != nil {
		return nil, err
	}
	for _, tagId := range tags {
		tkTags.LabelId = append(tkTags.LabelId, tagId)
	}
	return tkTags, nil
}

// TicketsDialogue 工单对话记录
func TicketsDialogue(ctx echo.Context, ticketId uint64) ([]*pb.TicketDialogueResp, error) {
	repo := dto.NewTicket()
	communs := make([]*pb.TicketDialogueResp, 0)
	// 表单详情
	tkFields, err := repo.GetTicketField(ctx.Request().Context(), ticketId)
	communs = append(communs, &pb.TicketDialogueResp{
		Detail:    tkFields.Field,
		CreatedAt: utils.TimeFormat(int64(tkFields.CreatedAt)),
		Operator:  "",
		FromRole:  uint32(pb.UserRole_PlayerRole),
		CommuType: pb.CommuType_CommuTypeDialogue.String(),
		IsTicket:  1,
	})
	if err != nil {
		return communs, err
	}
	// 会话记录
	info, err := repo.TicketsDialogue(ctx.Request().Context(), ticketId)
	if err != nil {
		return nil, err
	}

	// 重开单， 重开信息
	reopen, err := repo.ReopenInfo(ctx.Request().Context(), ticketId)
	if err != nil {
		return nil, err
	}
	for _, v := range reopen {
		info = append(info, &pb.TicketDialogueResp{
			Detail:      v.Content,
			CreatedAt:   utils.TimeFormat(int64(v.CreatedAt)),
			CreatedTime: v.CreatedAt,
			Operator:    "",
			IsTicket:    2,
			Files:       v.Files,
			FromRole:    uint32(pb.UserRole_PlayerRole),
			CommuType:   pb.CommuType_CommuTypeDialogue.String(),
		})
	}
	if len(info) > 0 {
		sort.SliceStable(info, func(i, j int) bool {
			return info[i].CreatedTime < info[j].CreatedTime
		})
	}
	communs = append(communs, info...)
	return communs, nil
}

// TicketsHists 工单历史变动记录
func TicketsHists(ctx echo.Context, ticketId uint64) ([]*pb.TicketHistResp, error) {
	hists, err := dto.NewTicket().GetTicketHists(ctx.Request().Context(), ticketId)
	if err != nil {
		return nil, err
	}
	var logs = make([]*pb.TicketHistResp, 0, len(hists))
	for _, hist := range hists {
		typeMsg, showMsg := genTicketHistShowDesc(ctx, hist)
		if showMsg != "" || typeMsg != "" {
			logs = append(logs, &pb.TicketHistResp{
				TicketId:  hist.TicketID,
				Type:      typeMsg,
				Remark:    showMsg,
				CreatedAt: utils.TimeFormat(int64(hist.CreatedAt)),
			})
		}
	}
	return logs, nil
}

func TicketAppraiseInfo(ctx echo.Context, ticketId uint64) (*pb.TicketAppraiseResp, error) {
	ticketAppraise, err := dto.NewTicket().GetTicketAppraiseInfo(ctx.Request().Context(), ticketId)
	if err != nil {
		return nil, err
	}
	dest := &pb.TicketAppraiseResp{
		CreatedAt:           utils.TimeFormat(int64(ticketAppraise.CreatedAt)),
		Csi:                 uint32(ticketAppraise.Csi),
		RecommendationLevel: uint32(ticketAppraise.RecommendationLevel),
		Remark:              ticketAppraise.Remark,
	}
	return dest, nil
}

func genTicketHistShowDesc(ctx echo.Context, hist *models.FpOpsTicketsHistory) (string, string) {
	var show, typeShow string
	switch pb.TkEvent(hist.Operate) {
	case pb.TkEvent_TkEventCreate, pb.TkEvent_TkEventPlayerRefill, pb.TkEvent_TkEventDoneCase, pb.TkEvent_TkEventUserCommu:
		show = ""
	case pb.TkEvent_TkEventAutoAlloc:
		typeShow = "系统分单"
		show = fmt.Sprintf("系统把工单分配给客服【%s】", hist.Acceptor)
	case pb.TkEvent_TkEventAssign:
		typeShow = "工单指派"
		show = fmt.Sprintf("客服【%s】把工单指派给客服【%s】", hist.Operator, hist.Acceptor)
	case pb.TkEvent_TkEventTurn:
		typeShow = "工单流转"
		show = fmt.Sprintf("客服【%s】把工单流转给客服【%s】", hist.Operator, hist.Acceptor)
	case pb.TkEvent_TkEventCommu:
		typeShow = "客服回复"
		show = fmt.Sprintf("客服【%s】回复消息", hist.Operator)
	case pb.TkEvent_TkEventCommuClose:
		typeShow = "回复关单"
		show = fmt.Sprintf("客服【%s】回复消息&关单", hist.Operator)
	case pb.TkEvent_TkEventRefused:
		typeShow = "拒单"
		show = fmt.Sprintf("客服【%s】拒单", hist.Operator)
	case pb.TkEvent_TkEventUpgrade:
		if hist.OpDetail == 1 {
			typeShow = "工单升级"
			show = fmt.Sprintf("客服【%s】把工单升级至升级区", hist.Operator)
		} else {
			typeShow = "工单降级"
			show = fmt.Sprintf("客服【%s】把工单从升级区移除", hist.Operator)
		}
	case pb.TkEvent_TkEventRemark:
		typeShow = "工单备注"
		show = fmt.Sprintf("客服【%s】新增备注", hist.Operator)
	case pb.TkEvent_TkEventReopen:
		typeShow = "工单重开"
		show = fmt.Sprintf("玩家重开工单")
	case pb.TkEvent_TkEventUserNoAnsTimout:
		typeShow = "超时关闭"
		show = fmt.Sprintf("玩家3天未回复 - 超时关单")
	case pb.TkEvent_TkEventTagAdd:
		// todo logic
		typeShow = "工单标签"
		var tagsDesc []string
		var tagIds = make([]uint32, 0)
		json.Unmarshal([]byte(hist.OpObject), &tagIds)
		if len(tagIds) > 0 {
			for _, v := range dto.NewTags().GetTagsSlice(ctx.Request().Context(), tagIds) {
				tagsDesc = append(tagsDesc, strings.Join(v, "-"))
			}
		}
		show = fmt.Sprintf("客服【%s】新增标签：%s", hist.Operator, strings.Join(tagsDesc, ", "))
	case pb.TkEvent_TkEventTagDel:
		typeShow = "工单标签"
		var tagsDesc []string
		var tagIds = make([]uint32, 0)
		json.Unmarshal([]byte(hist.OpObject), &tagIds)
		if len(tagIds) > 0 {
			for _, v := range dto.NewTags().GetTagsSlice(ctx.Request().Context(), tagIds) {
				tagsDesc = append(tagsDesc, strings.Join(v, "-"))
			}
		}
		show = fmt.Sprintf("客服【%s】删除标签：%s", hist.Operator, strings.Join(tagsDesc, ", "))
	case pb.TkEvent_TkEventNoOpTimeout:
		typeShow = "超时退单"
		show = fmt.Sprintf("客服【%s】规定时间内未处理", hist.Remark)
	case pb.TkEvent_TkEventReturnPool:
		typeShow = "人工退单"
		show = fmt.Sprintf("客服【%s】把工单退回工单池", hist.Operator)
	default:
		show = fmt.Sprintf("未知 case ，请技术排查")
	}
	return typeShow, show
}
