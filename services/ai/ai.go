package ai

import (
	"errors"
	"github.com/labstack/echo/v4"
	"github.com/spf13/cast"
	"github.com/spf13/viper"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog"
	"gitlab-ee.funplus.io/ops-tools/compkg/httpclient"
	"gitlab-ee.funplus.io/ops-tools/compkg/sign"
	"ops-ticket-api/internal/framework/lang"
	dto "ops-ticket-api/internal/persistence"
	"ops-ticket-api/proto/pb"
	"time"
)

type AlgorithmResp struct {
	Code int    `json:"code"`
	Msg  string `json:"msg"`
	Data struct {
		Answer   string `json:"answer"`
		Question string `json:"question"`
	} `json:"data"`
}

type KnowledgeSearchResp struct {
	Code int                                    `json:"code"`
	Msg  string                                 `json:"msg"`
	Data map[string][]KnowledgeSearchRespEntity `json:"data"`
}

type KnowledgeSearchRespEntity struct {
	Score   float64                `json:"score"`
	Content map[string]interface{} `json:"content"`
}

type AlgorithmMeaningResp struct {
	Code int    `json:"code"`
	Msg  string `json:"msg"`
	Data struct {
		Label int `json:"label"`
	} `json:"data"`
}

// Summary ai总结
func Summary(ctx echo.Context, req *pb.AISummaryReq) (string, error) {

	// 查工单详情
	var dialogue string
	// 1. 获取会话记录
	// 1.1 获取表单详情
	tkFields, err := dto.NewTicket().GetTicketField(ctx.Request().Context(), req.TicketId)
	if err != nil {
		elog.Errorf("AI Summary GetTicketField err:%+v", err)
		return "", err
	}
	if tkFields.Field != "" {
		dialogue += "玩家: \n" + tkFields.Field + "\n"
	}

	// 1.2 获取对话记录
	info, err := dto.NewTicket().TicketsDialogue(ctx.Request().Context(), req.TicketId)
	if err != nil {
		elog.Errorf("AI Summary TicketsDialogue err:%+v", err)
		return "", err
	}

	for _, v := range info {

		if v.FromRole == 1 {
			dialogue += "玩家: \n" + v.Detail + "\n"
		}
		if v.FromRole == 2 {
			dialogue += "客服: \n" + v.Detail + "\n"
		}
	}

	if dialogue == "" {
		elog.Warnf("AI Summary dialogue is empty. ticket_id:%d", req.TicketId)
		return "", nil
	}
	// 2. 调用ai接口
	requestParams := make(map[string]interface{})
	requestParams["chats"] = dialogue
	algorithmResponse, err := httpclient.PostJson(ctx.Request().Context(), viper.GetString("thirdparty.algorithm_url")+"/summary", requestParams)
	if err != nil || algorithmResponse == nil {
		elog.Errorf("AI Summary algorithm response err:%+v", err)
		return "", err
	}
	if algorithmResponse.StatusCode != 200 {
		elog.Warnf("AI Summary algorithm response code:%d", algorithmResponse.StatusCode)
		return "", err
	}

	// 3. 返回总结
	return algorithmResponse.String(), nil
}

// Polish ai润色
func Polish(ctx echo.Context, req *pb.AIPolishReq) (string, error) {

	// 1. 调用ai接口
	requestParams := make(map[string]interface{})
	requestParams["chats"] = req.Content
	requestParams["style"] = req.Style
	algorithmResponse, err := httpclient.PostJson(ctx.Request().Context(), viper.GetString("thirdparty.algorithm_url")+"/polish", requestParams)
	if err != nil || algorithmResponse == nil {
		elog.Errorf("AI Polish algorithm response err:%+v", err)
		return "", err
	}
	if algorithmResponse.StatusCode != 200 {
		elog.Warnf("AI Polish algorithm response code:%d", algorithmResponse.StatusCode)
		return "", err
	}

	// 2. 返回结果
	return algorithmResponse.String(), nil
}

// PreReply ai预回复
func PreReply(ctx echo.Context, req *pb.AIPreReplyReq) (string, error) {

	tkInfo, err := dto.NewTicket().GetTicketInfoFromMaster(ctx.Request().Context(), req.TicketId)
	if err != nil {
		elog.Errorf("AI PreReply GetTicketInfoFromMaster err:%+v", err)
		return "", err
	}

	// 1. 调用ai总结,提炼出问题
	algorithmResp, err := AiFaq(ctx, &pb.AIFaqReq{TicketId: req.TicketId, Content: req.Content})
	if err != nil {
		elog.Errorf("AI PreReply GetAiFaq err:%+v", err)
		return "", err
	}
	question := algorithmResp.Data.Question

	// 1. 调用查询向量库接口 search_elf
	claims := sign.NewClaims()
	claims.Time = time.Now().UTC()
	claims.Set("username", "cs_ticket")
	claims.Set("nickname", "cs_ticket")
	claims.Set("prd_id", "cs_ticket")
	claims.Set("game_project", tkInfo.Project)

	apiKey := viper.GetString("auth.admin_gateway")
	token, err := sign.JwtEncode(claims, apiKey)
	if err != nil {
		elog.Errorf("AI PreReply JwtEncode err. err:%+v", err)
		return "", err
	}

	api := viper.GetString("thirdparty.ops_backend_api.cs_api_server_host")

	var path = api + "/front/inner/knowledge/search_elf"
	client := httpclient.New().SetHeader("Authorization", "Bearer "+token)

	var requestParams = map[string]interface{}{
		"query":          question,
		"game_project":   tkInfo.Project,
		"lang":           tkInfo.Lang,
		"min_similarity": 0.8, // 最小相似度
		"top_k":          10,  // 返回条数
		"filtering":      map[string]string{"game_project": tkInfo.Project, "lang": tkInfo.Lang, "server": "", "pkg_channel": "", "model_type": "1"},
	}
	res, err := client.PostJson(ctx.Request().Context(), path, requestParams)
	if err != nil {
		elog.Errorf("AI PreReply get search_elf err. err:%+v", err)
		return "", err
	}

	if res.StatusCode != 200 {
		elog.Errorf("AI PreReply get search_elf response status code. res:%v", res)
		return "", errors.New("search_elf response err")
	}

	var resp KnowledgeSearchResp

	// {"doc":[],"faq":[],"faq_filter":[],"knowledge":[],"words_library":[]}
	if err = res.Decode(&resp); err != nil {
		elog.Errorf("AI PreReply get search_elf response status code. res:%v", res)
		return "", err
	}

	if resp.Data == nil {
		elog.Infof("AI PreReply get search_elf response data nil. res:%v", res)
		return lang.FormatText(ctx, "defaultAiPreReply"), nil
	}

	// 2. 返回结果
	if len(resp.Data["faq_filter"]) > 0 {
		return cast.ToString(resp.Data["faq_filter"][0].Content["answer_rich_text"]), nil
	} else if len(resp.Data["words_library"]) > 0 {
		for i, v := range resp.Data["words_library"] {
			if cast.ToInt64(v.Content["cat_id"]) > 0 { // 过滤关联工单类型
				continue
			}
			return cast.ToString(resp.Data["words_library"][i].Content["answer_rich_text"]), nil
		}
	}
	return lang.FormatText(ctx, "defaultAiPreReply"), nil
}

// AiFaq ai-生成faq
func AiFaq(ctx echo.Context, req *pb.AIFaqReq) (*AlgorithmResp, error) {

	// 查工单详情
	var dialogue string
	// 1 获取表单详情
	tkFields, err := dto.NewTicket().GetTicketField(ctx.Request().Context(), req.TicketId)
	if err != nil {
		elog.Errorf("AI AiFaq GetTicketField return err. err:%+v", err)
		return nil, err
	}
	if tkFields.Field != "" {
		dialogue += "玩家: \n" + tkFields.Field + "\n"
	}

	// 2. 获取会话记录
	info, err := dto.NewTicket().TicketsDialogue(ctx.Request().Context(), req.TicketId)
	if err != nil {
		elog.Errorf("AI AiFaq TicketsDialogue return err. err:%+v", err)
		return nil, err
	}

	for _, v := range info {

		if v.FromRole == 1 {
			dialogue += "玩家: \n" + v.Detail + "\n"
		}
		if v.FromRole == 2 {
			dialogue += "客服: \n" + v.Detail + "\n"
		}
	}

	if req.Content != "" {
		dialogue += "客服: \n" + req.Content // 客服预回复内容
	}

	// 1. 调用算法生成faq
	requestParams := make(map[string]interface{})
	requestParams["chats"] = dialogue
	algorithmResponse, err := httpclient.PostJson(ctx.Request().Context(), viper.GetString("thirdparty.algorithm_url")+"/rewrite_q", requestParams)
	if err != nil || algorithmResponse == nil {
		elog.Errorf("AI AiFaq get algorithm rewrite_q return err. err:%+v", err)
		return nil, err
	}
	if algorithmResponse.StatusCode != 200 {
		elog.Errorf("AI AiFaq get algorithm rewrite_q return response. res:%+v", algorithmResponse)
		return nil, err
	}

	var resp AlgorithmResp
	if err = algorithmResponse.Decode(&resp); err != nil {
		elog.Errorf("AI AiFaq get algorithm rewrite_q response decode err. err:%+v", err)
		return nil, err
	}

	if resp.Code != 1 { // 1成功0失败
		elog.Warnf("AI AiFaq get algorithm rewrite_q return resp. res:%+v", resp)
		return nil, errors.New(resp.Msg)
	}

	// 2. 返回结果
	return &resp, nil
}

// CheckEffectiveQuery 判断是否无效提问
func CheckEffectiveQuery(ctx echo.Context, query string) int {
	requestParams := make(map[string]interface{})
	requestParams["content"] = query
	algorithmResponse, err := httpclient.PostJson(ctx.Request().Context(), viper.GetString("thirdparty.algorithm_url")+"/meaning", requestParams)
	if err != nil || algorithmResponse == nil {
		elog.Errorf("CheckEffectiveQuery response err. params:%+v. err:%v ", requestParams, err)
		return 0
	}
	if algorithmResponse.StatusCode != 200 {
		elog.Warnf("CheckEffectiveQuery response %d. params:%+v", algorithmResponse.StatusCode, requestParams)
		return 0
	}
	var resp AlgorithmMeaningResp
	if err := algorithmResponse.Decode(&resp); err != nil {
		return 0
	}
	code := cast.ToInt(resp.Code)
	if code != 200 {
		elog.Infof("CheckEffectiveQuery response code not 200, is %d. params:%+v", algorithmResponse.StatusCode, requestParams)
		return 0
	}
	return resp.Data.Label + 1
}
