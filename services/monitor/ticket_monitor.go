package monitor

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strconv"
	"strings"
	"time"

	"ops-ticket-api/internal/framework/cache/keys"
	"ops-ticket-api/internal/framework/cache/rds"
	"ops-ticket-api/internal/framework/database"
	"ops-ticket-api/models"
	"ops-ticket-api/utils"

	"github.com/go-redis/redis/v8"
	"github.com/spf13/viper"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"go.uber.org/zap"
)

const (
	MonitorWindowMinutes = 30
	AlertThreshold       = 8
)

type TicketMonitor struct{}

func NewTicketMonitor() *TicketMonitor {
	return &TicketMonitor{}
}

// MonitorTicketCreate 监控工单创建
func (m *TicketMonitor) MonitorTicketCreate(ctx context.Context, gameProject string, catId uint32, ticketId uint64) error {
	//check是否为需要判断的游戏及该游戏下的问题分类，否则return
	if !checkGameConfig(gameProject, int(catId)) {
		return nil
	}
	//构建带有游戏的key即可，无论该游戏是哪个问题分类(配置中的)都会被统计到
	countKey := fmt.Sprintf(keys.TicketMonitorKey, gameProject, catId)
	now := time.Now().Unix()
	//zadd一条，带有ticketid，now时间戳的记录，过期时间为30min
	rdb := rds.RCli.Client
	rdb.ZAdd(ctx, countKey, &redis.Z{
		Score:  float64(now),
		Member: ticketId,
	})
	rdb.Expire(ctx, countKey, MonitorWindowMinutes*time.Minute) // 设置过期时间

	//统计过去30分钟的工单数量，判断是否发送飞书
	startTime := now - MonitorWindowMinutes*60
	endTime := now
	// 删除过期的记录（30分钟窗口外的数据）
	rdb.ZRemRangeByScore(ctx, countKey, "-inf", strconv.FormatInt(startTime-1, 10))
	//统计过去30分钟的工单数量
	count, err := rdb.ZCount(ctx, countKey, strconv.FormatInt(startTime, 10), strconv.FormatInt(endTime, 10)).Result()
	if err != nil {
		logger.Error(ctx, "monitor ticket create error", zap.Error(err))
		return err
	}
	//如果工单数到5，调用飞书发送接口并删除统计的这30分钟内的所有工单
	if count >= AlertThreshold {
		// 获取所有工单ID
		ticketIds, err := rdb.ZRangeByScore(ctx, countKey, &redis.ZRangeBy{
			Min: strconv.FormatInt(startTime, 10), // 30分钟前
			Max: strconv.FormatInt(endTime, 10),   // 当前时间
		}).Result()
		if err != nil {
			logger.Error(ctx, "get ticket ids error", zap.Error(err))
			return err
		}
		// 生成告警消息
		alertMessage := genFeishuAlert(gameProject, int(catId), count, ticketIds)
		// 发送飞书告警
		webHookURl := viper.GetString(fmt.Sprintf("ticket_monitor.games.%s.feishu_webhook", gameProject))
		go func() {
			if err := sendFeishuMessage(webHookURl, alertMessage); err != nil {
				logger.Error(ctx, "send feishu message error", zap.Error(err))
			}
		}()
		// 删除已统计的工单endFeishuAlert(gameConfig.FeishuGroupID, gameID, count)
		rdb.ZRemRangeByScore(ctx, countKey, strconv.FormatInt(startTime, 10), strconv.FormatInt(endTime, 10))
	}
	return nil
}

// getGameConfig 获取游戏配置
func checkGameConfig(project string, catID int) bool {
	gameConfig := viper.GetStringMap(fmt.Sprintf("ticket_monitor.games.%s", project))
	if len(gameConfig) == 0 {
		return false // 不需要监控的游戏
	}

	// 检查是否是需要监控的分类
	loginCats := viper.GetIntSlice(fmt.Sprintf("ticket_monitor.games.%s.categories.login", project))
	paymentNotCreditedCats := viper.GetIntSlice(fmt.Sprintf("ticket_monitor.games.%s.categories.payment_not_credited", project))
	paymentCannotPayCats := viper.GetIntSlice(fmt.Sprintf("ticket_monitor.games.%s.categories.payment_cannot_pay", project))
	if !utils.InArrayAny(catID, loginCats) && !utils.InArrayAny(catID, paymentNotCreditedCats) && !utils.InArrayAny(catID, paymentCannotPayCats) {
		return false // 不需要监控的分类
	}
	return true

}

func genFeishuAlert(gameProject string, catId int, count int64, ticketIds []string) map[string]interface{} {
	// 获取问题类型
	var problemType string
	if utils.InArrayAny(catId, viper.GetIntSlice(fmt.Sprintf("ticket_monitor.games.%s.categories.login", gameProject))) {
		problemType = "登录问题"
	} else if utils.InArrayAny(catId, viper.GetIntSlice(fmt.Sprintf("ticket_monitor.games.%s.categories.payment_not_credited", gameProject))) {
		problemType = "充值未到账"
	} else if utils.InArrayAny(catId, viper.GetIntSlice(fmt.Sprintf("ticket_monitor.games.%s.categories.payment_cannot_pay", gameProject))) {
		problemType = "无法充值"
	}

	db := database.Db()
	var tickets []*models.FpOpsTickets
	db.Model(&models.FpOpsTickets{}).Preload("Device").Where("ticket_id IN (?)", ticketIds).Find(&tickets)
	// 构建工单明细
	detail := make(map[string]string)
	for _, ticket := range tickets {
		detail[strconv.FormatUint(ticket.TicketID, 10)] = fmt.Sprintf("工单号：%s\n", ticket.TicketID)
	}

	utcTime := time.Unix(time.Now().Unix(), 0).UTC().Format("2006-01-02 15:04:05")

	tableRows := make([]map[string]interface{}, 0)
	for _, ticket := range tickets {
		tableRows = append(tableRows, map[string]interface{}{
			"ticket_id": ticket.TicketID,
			"uid":       ticket.UID,
			"fpid":      ticket.AccountID,
			"country":   ticket.Device.Country,
			"channel":   ticket.Channel,
		})
	}

	var msg strings.Builder
	msg.WriteString(fmt.Sprintf("告警时间：%s（utc）\n", utcTime))
	msg.WriteString(fmt.Sprintf("问题分类：%s（id：%d）\n", problemType, catId))
	msg.WriteString(fmt.Sprintf("30分钟内单量：%d\n", count))
	msg.WriteString("工单明细")

	// 构建卡片消息
	message := map[string]interface{}{
		"msg_type": "interactive",
		"card": map[string]interface{}{
			"schema": "2.0",
			"config": map[string]interface{}{
				"update_multi": true,
				"style": map[string]interface{}{
					"text_size": map[string]interface{}{
						"normal_v2": map[string]interface{}{
							"default": "normal",
							"pc":      "normal",
							"mobile":  "heading",
						},
					},
				},
			},
			"header": map[string]interface{}{
				"title": map[string]interface{}{
					"tag":     "plain_text",
					"content": fmt.Sprintf("【%s-30分钟内关键客诉问题-单量告警信息】", gameProject),
				},
				"template": "blue",
			},
			"body": map[string]interface{}{
				"direction": "vertical",
				"padding":   "12px 12px 12px 12px",
				"elements": []map[string]interface{}{
					{
						"tag": "div",
						"text": map[string]interface{}{
							"tag":     "plain_text",
							"content": msg.String(),
						},
					},
					{
						"tag":       "table",
						"page_size": 10,
						"columns": []map[string]interface{}{
							{ // 添加列，列的数据类型为不带格式的普通文本。
								"name":         "ticket_id", // 自定义列的标记。必填。用于唯一指定行数据对象数组中，需要将数据填充至这一行的具体哪个单元格中。
								"display_name": "工单号",       // 列名称。为空时不展示列名称。
								"width":        "80px",
							},
							{
								"name":         "uid",
								"display_name": "uid",
								"width":        "120px",
							},
							{
								"name":         "fpid",
								"display_name": "fpid",
							},
							{
								"name":         "country",
								"display_name": "国家",
								"width":        "80px",
							},
							{
								"name":         "channel",
								"display_name": "渠道",
								"width":        "300px",
							},
						},
						"rows": tableRows,
					},
					{
						"tag": "div",
						"text": map[string]interface{}{
							"tag":     "lark_md",
							"content": "<at id=all></at>",
						},
					},
				},
			},
		},
	}

	return message
}

func sendFeishuMessage(webhook string, message map[string]interface{}) error {
	ctx := context.TODO()
	jsonData, err := json.Marshal(message)
	if err != nil {
		logger.Error(ctx, "send feishu message Failed to marshal message to JSON: %v", zap.Error(err))
		return err
	}

	req, err := http.NewRequestWithContext(ctx, "POST", webhook, bytes.NewBuffer(jsonData))
	if err != nil {
		logger.Error(ctx, "send feishu message Failed to create request: %v", zap.Error(err))
		return err
	}
	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		logger.Error(ctx, "send feishu messageFailed to send request: %v", zap.Error(err))
		return err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		logger.Info(ctx, "send feishu message error returned status code: %d", zap.Any("resp", resp.StatusCode))
	} else {
		body, err := io.ReadAll(resp.Body)
		if err != nil {
			logger.Error(ctx, "send feishu message Failed to read response body: %v", zap.Error(err))
			return err
		}
		logger.Info(ctx, "send feishu message resp:", zap.Any("resp", string(body)))
	}

	return nil
}
