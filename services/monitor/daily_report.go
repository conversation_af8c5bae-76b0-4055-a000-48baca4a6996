package monitor

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"ops-ticket-api/internal/persistence"

	"github.com/spf13/viper"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"go.uber.org/zap"
)

// BanDailyReport 生成每日工单报告
func BanDailyReport(ctx context.Context) error {
	logger.Info(ctx, "BanDailyReport start")

	gameProjects := viper.GetStringSlice("ticket_monitor.ban_daily_report_game_projects")
	for _, gameProject := range gameProjects {
		statData, err := persistence.NewMaintainConfig().GetTicketStatByCatId(ctx, 511, gameProject)
		if err != nil {
			logger.Error(ctx, "GetTicketCountByCatId error", zap.Error(err))
			return err
		}
		logger.Info(ctx, "BanDailyReport finished", zap.Any("statData", statData))

		exportData := map[string]interface{}{
			"start_date": time.Now().AddDate(0, 0, -1).Format("2006-01-02 00:00"),
			"end_date":   time.Now().Format("2006-01-02 00:00"),
			"project":    gameProject,
			"cat_id":     511,
		}
		jsonData, _ := json.Marshal(exportData)
		exportCode := base64.StdEncoding.EncodeToString(jsonData)
		downloadUrl := fmt.Sprintf("%s/inner/ticket/ban-export?export_code=ew%s", viper.GetString("ticket_monitor.ops_ticket_inner_api_url"), exportCode)

		// 生成告警消息
		alertMessage := genFeishuBanDailyReportAlert(gameProject, statData, downloadUrl)
		// 发送飞书告警
		webHookURl := viper.GetString(fmt.Sprintf("ticket_monitor.games.%s.feishu_webhook", gameProject))
		if err := sendFeishuMessage(webHookURl, alertMessage); err != nil {
			logger.Error(ctx, "send feishu message error", zap.Error(err))
		}
	}
	logger.Info(ctx, "BanDailyReport finished")
	return nil
}

func genFeishuBanDailyReportAlert(gameProject string, statData []persistence.TicketDailyCount, downloadUrl string) map[string]interface{} {
	lastDayStat := statData[len(statData)-1]

	var msg strings.Builder
	msg.WriteString(fmt.Sprintf("%s日单量：%d", statData[len(statData)-1].ShortDay, statData[len(statData)-1].TicketCount))
	if lastDayStat.TicketCount > 0 {
		msg.WriteString(fmt.Sprintf("（<a href='%s'>点击下载明细数据</a>）", downloadUrl))
	}

	statRows := make([]map[string]interface{}, 0)
	for _, v := range statData {
		statRows = append(statRows, map[string]interface{}{
			"time":  v.ShortDay,
			"value": v.TicketCount,
		})
	}

	// 构建卡片消息
	message := map[string]interface{}{
		"msg_type": "interactive",
		"card": map[string]interface{}{
			"schema": "2.0",
			"config": map[string]interface{}{
				"update_multi": true,
				"style": map[string]interface{}{
					"text_size": map[string]interface{}{
						"normal_v2": map[string]interface{}{
							"default": "normal",
							"pc":      "normal",
							"mobile":  "heading",
						},
					},
				},
			},
			"header": map[string]interface{}{
				"title": map[string]interface{}{
					"tag":     "plain_text",
					"content": fmt.Sprintf("【%s-帐号被封客诉问题-日报】", gameProject),
				},
				"template": "blue",
			},
			"body": map[string]interface{}{
				"direction": "vertical",
				"padding":   "12px 12px 12px 12px",
				"elements": []map[string]interface{}{
					{
						"tag": "div",
						"text": map[string]interface{}{
							"tag":     "lark_md",
							"content": msg.String(),
						},
					},
					{
						"tag": "chart",
						"chart_spec": map[string]interface{}{
							"type": "line",
							"title": map[string]interface{}{
								"text": "近14日帐号被封客诉趋势图",
							},
							"data": map[string]interface{}{
								"values": statRows, // 此处传入数据。
							},
							"xField": "time",
							"yField": "value",
						},
					},
				},
			},
		},
	}

	return message
}
