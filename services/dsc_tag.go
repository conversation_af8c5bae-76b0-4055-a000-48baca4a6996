package services

import (
	"context"
	"fmt"
	"github.com/labstack/echo/v4"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"ops-ticket-api/internal/cst"
	p "ops-ticket-api/internal/persistence"
	"ops-ticket-api/proto/pb"
	"sync"
)

func DiscordTagSave(ctx echo.Context, req *pb.DiscordTagAddReq) error {
	return p.NewDiscordTag().DiscordTagSave(ctx, req)
}

func DiscordTagEdit(ctx echo.Context, req *pb.DiscordTagEditReq) error {
	return p.NewDiscordTag().DiscordTagEdit(ctx, req)
}

// DiscordTagList discord标签列表
func DiscordTagList(ctx echo.Context, req *pb.DiscordTagListReq) (*pb.DiscordTagListResp, error) {
	resp, err := p.NewDiscordTag().DiscordTagList(ctx, req)
	if err != nil {
		return nil, err
	}
	return resp, nil
}

func DiscordBatchTag(ctx echo.Context, req *pb.DiscordBatchTagReq) error {
	return p.NewDiscordTag().DiscordBatchTag(ctx, req)
}

// DiscordPublicTag 获取公共标签
func DiscordPublicTag(ctx echo.Context, req *pb.DiscordPublicTagReq) (*pb.DiscordPublicTagResp, error) {
	var (
		fun = "discord.DiscordPublicTag"
	)
	// 获取公共标签
	tags, err := p.NewDiscordTag().GetPublicTags(ctx.Request().Context(), req.DscUserIdList)
	if err != nil {
		logger.Errorf(ctx.Request().Context(), "%s GetPublicTags return err. err:%v", fun, err)
		return nil, err
	}
	return &pb.DiscordPublicTagResp{Tags: tags}, nil
}

// DiscordBatchDeleteTags 批量删除标签
func DiscordBatchDeleteTags(ctx echo.Context, req *pb.DiscordTagBatchDelete) error {
	fun := "discord.DiscordBatchDeleteTags"
	operator := ctx.Get(cst.AccountInfoCtx).(string)
	var wg sync.WaitGroup
	errChan := make(chan error, len(req.DscUserIdList))

	// 动态并发数控制，工单数量不超过一定上限的并发数
	maxConcurrency := 50
	concurrency := len(req.DscUserIdList)
	if concurrency > maxConcurrency {
		concurrency = maxConcurrency
	}
	sem := make(chan struct{}, concurrency)

	for _, dscUserID := range req.DscUserIdList {
		wg.Add(1)
		go func(dscUserID string) {
			defer wg.Done()
			// 占用一个通道位置，限制并发数
			sem <- struct{}{}
			// 释放通道位置
			defer func() {
				<-sem
			}()
			// 单个工单打标签业务逻辑
			if err := processSingleDeleteDiscordTag(ctx.Request().Context(), dscUserID, req.TagIds, operator, req.Project); err != nil {
				// 将DscUserIdID包含在错误中
				errChan <- fmt.Errorf("ticketID %d: %w", dscUserID, err)
			}
		}(dscUserID)
	}

	wg.Wait()
	close(errChan)

	// 收集错误信息
	var errs []error
	for err := range errChan {
		errs = append(errs, err)
	}

	// 返回包含所有错误的汇总信息
	if len(errs) > 0 {
		return fmt.Errorf("%s errors: %v", fun, errs)
	}
	return nil
}

func processSingleDeleteDiscordTag(ctx context.Context, dscUserID string, labelIDs []uint32, operator, project string) error {
	// 3. 保存到数据库并同步至ES
	if err := p.NewDiscordTag().BatchDeleteDiscordTags(ctx, dscUserID, labelIDs, operator, project); err != nil {
		logger.Errorf(ctx, "processSingleDeleteDiscordTag BatchDeleteDiscordTags return err. err:%v. dscUserId:%s", err, dscUserID)
		return err
	}
	return nil
}
