package services

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/bwmarrin/discordgo"
	"github.com/go-sql-driver/mysql"
	jsoniter "github.com/json-iterator/go"
	"github.com/labstack/echo/v4"
	"github.com/spf13/viper"
	"github.com/tidwall/gjson"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"gitlab-ee.funplus.io/ops-tools/compkg/httpclient"
	"gitlab-ee.funplus.io/ops-tools/compkg/sign"
	"go.uber.org/zap"
	"golang.org/x/sync/errgroup"
	"gorm.io/gorm"
	"io"
	"ops-ticket-api/internal/code"
	"ops-ticket-api/internal/cst"
	"ops-ticket-api/internal/dsc"
	"ops-ticket-api/internal/framework/xerrors"
	"ops-ticket-api/internal/persistence"
	"ops-ticket-api/models"
	"ops-ticket-api/proto/pb"
	"ops-ticket-api/services/communication"
	"ops-ticket-api/services/dsccore"
	"ops-ticket-api/utils"
	"sort"
	"time"
)

func NewDscSrv() *dscService {
	return &dscService{}
}

type dscService struct {
}

type Response struct {
	Code int         `json:"code"`
	Msg  string      `json:"msg"`
	Data interface{} `json:"data"`
}

func (srv *dscService) DiscordPlayer(ctx echo.Context, fpid, gameProject string, uid uint64) (models.DiscordPlayer, error) {
	c := ctx.Request().Context()
	//discordPlayer := models.DiscordPlayer{}
	// 首先尝试从缓存中获取数据，若能获取到则直接返回
	//err := rds.RCli.GetCacheVal(c, fmt.Sprintf(keys.DiscordCrmPlayerKey, fpid), discordPlayer)
	//if err == nil {
	//	return discordPlayer
	//}
	// 缓存中如果没数据，再通过HTTP请求去CRM获取数据
	defaultDiscordPlayer := models.DiscordPlayer{
		TotalPay: 0,
		VipLevel: 0,
	}
	api := viper.GetString("thirdparty.ops_backend_api.crm_discord_info")
	if api == "" || fpid == "" {
		return defaultDiscordPlayer, xerrors.New("参数错误", code.InvalidParams)
	}
	claims := sign.NewClaims()
	claims.Time = time.Now().UTC()
	claims.Set("username", "cs_ticket")
	claims.Set("nickname", "cs_ticket")
	claims.Set("admin", false)
	claims.Set("prd_id", "cs_ticket")
	claims.Set("game_project", gameProject)
	token, _ := sign.JwtEncode(claims, viper.GetString("thirdparty.ops_backend_api.crm_auth_key"))
	client := httpclient.New()
	client.SetHeader(echo.HeaderAuthorization, "Bearer "+token)

	logger.Info(c, "CrmVip api call start",
		zap.String("fpid", fpid),
		zap.String("game_project", gameProject))

	ret, err := client.PostJson(c, api, map[string]interface{}{
		"game_project": gameProject,
		"fpid":         fpid,
		"uid":          uid,
	})
	if err != nil {
		logger.Info(c, "discord player api call error", zap.Error(err))
		return defaultDiscordPlayer, xerrors.New("调用CRM服务错误", code.RpcCallErr)
	}
	respBody := ret.String()
	var response Response
	err = json.Unmarshal([]byte(respBody), &response)
	if err != nil {
		logger.Errorf(c, "解析JSON失败:", zap.String("error", err.Error()))
		return defaultDiscordPlayer, err
	}
	if response.Code != 0 {
		logger.Errorf(c, "discord player api call error", zap.String("error", response.Msg))
		return defaultDiscordPlayer, xerrors.New(response.Msg, code.DbDataUnexpect)
	}
	// logger.Info(c, "discord player api call success", zap.String("data", respBody))
	jsonBody := gjson.Parse(respBody)
	var vipLevel, crmVipType uint8
	var totalPay, lastThirtyDaysPay int64
	var channel, lastLogin, sid, lang, playerNick string
	var userId int64
	if jsonBody.Get("data.uid").Exists() {
		userId = jsonBody.Get("data.uid").Int()
	}
	if jsonBody.Get("data.vip_type").Exists() {
		crmVipType = uint8(jsonBody.Get("data.vip_type").Int()) // vip类型 1默认为非vip; 2:长期vip; 3:限时vip; 4:过期
	}
	if jsonBody.Get("data.vip_level").Exists() {
		vipLevel = uint8(jsonBody.Get("data.vip_level").Int())
	}
	if jsonBody.Get("data.pay_total").Exists() {
		totalPay = jsonBody.Get("data.pay_total").Int()
	}
	if jsonBody.Get("data.last_thirty_days_pay").Exists() {
		lastThirtyDaysPay = jsonBody.Get("data.last_thirty_days_pay").Int()
	}
	if jsonBody.Get("data.channel").Exists() {
		channel = jsonBody.Get("data.channel").String()
	}
	if jsonBody.Get("data.last_login").Exists() {
		lastLogin = jsonBody.Get("data.last_login").String()
	}
	if jsonBody.Get("data.sid").Exists() {
		sid = jsonBody.Get("data.sid").String()
	}
	if jsonBody.Get("data.lang").Exists() {
		lang = jsonBody.Get("data.lang").String()
	}
	if jsonBody.Get("data.player_nick").Exists() {
		playerNick = jsonBody.Get("data.player_nick").String()
	}
	res := models.DiscordPlayer{
		Uid:               userId,
		Fpid:              fpid,
		VipLevel:          vipLevel,
		VipType:           uint16(crmVipType),
		TotalPay:          totalPay,
		LastThirtyDaysPay: lastThirtyDaysPay,
		LastLogin:         lastLogin,
		Channel:           channel,
		Sid:               sid,
		Lang:              lang,
		PlayerNick:        playerNick,
	}
	// 缓存通过HTTP请求CRM获取到的数据
	//if err = rds.RCli.SetCacheVal(c, fmt.Sprintf(keys.DiscordCrmPlayerKey, fpid), res, time.Hour*12); err != nil {
	//	logger.Errorf(c, "get discordPlayer from cache error", zap.String("error", err.Error()))
	//}
	return res, nil
}

func (srv *dscService) GetUserPool(ctx echo.Context, param *pb.DscUserListReq) ([]*pb.DscUserListResp_DscUser, uint32, error) {
	dest, total, err := DiscordPool(ctx, param)
	if err != nil {
		return nil, 0, err
	}
	res := make([]*pb.DscUserListResp_DscUser, len(dest))
	for i := range dest {
		user := dest[i]
		dscUser := &pb.DscUserListResp_DscUser{
			Project:              user.Project,
			DscUserId:            user.DscUserId,
			UserName:             user.UserName,
			GlobalName:           user.GlobalName,
			DmChannel:            user.DmChannel,
			GuildId:              user.GuildId,
			Processor:            user.Processor,
			Uid:                  user.Uid,
			Sid:                  user.Sid,
			LastLogin:            user.LastLogin,
			TotalPay:             utils.PayAmountToFloat64(user.PayAll),
			PayLastThirtyDays:    utils.PayAmountToFloat64(user.PayLastThirtyDays),
			VipLevel:             user.VipLevel,
			Status:               user.Status,
			Fpid:                 user.AccountId,
			BotId:                user.BotId,
			BotShow:              dsc.GetAppIdShow(user.BotId),
			Note:                 user.Note,
			PlayerNick:           user.PlayerNick,
			Birthday:             user.Birthday,
			Lang:                 user.Lang,
			Checked:              false,
			LastReplyTime:        user.LastReplyTime,
			WaitingTime:          user.WaitingTime,
			TicketCreateCount:    user.TicketCreateCount,
			LastTicketCreateTime: user.LastTicketCreateTime,
			TicketId:             user.TicketId,
		}
		res[i] = dscUser
	}
	return res, total, nil
}

func (srv *dscService) ChannelMsgUnread(ctx echo.Context, channelId string) ([]*pb.DscDialogFreshEvent, error) {
	// 未读消息 查询
	var (
		fun        = "dscService.ChannelMsgUnread"
		unreadList = make([]*pb.DscDialogFreshEvent, 0)
	)
	list, err := persistence.NewDscInteractions().MsgUnreadList(ctx.Request().Context(), channelId)
	if err != nil {
		return nil, err
	}
	for _, unread := range list {
		var detail, err = srv.genMsgUnreadEventDetail(ctx.Request().Context(), unread)
		if err != nil {
			logger.Errorf(ctx.Request().Context(), "%s genMsgUnreadEventDetail return err. err:%v", fun, err)
			continue
		}
		unreadList = append(unreadList, detail)
	}
	return unreadList, nil
}

func (srv *dscService) genMsgUnreadEventDetail(ctx context.Context, msg *models.FpDscDmUnread) (*pb.DscDialogFreshEvent, error) {
	var (
		detail = &pb.DscDialogFreshEvent{
			EventType:   pb.DscEvTpDf(pb.DscEvTpDf_value[msg.UniqType]),
			MsgDetail:   nil,
			DelMsgIds:   nil,
			AddReaction: nil,
			DelReaction: nil,
		}
		err    error
		logOrg []byte
	)
	logOrg, err = utils.Decompress(msg.Log)
	if err != nil || string(logOrg) == "" || string(logOrg) == "{}" || string(logOrg) == "[]" {
		return nil, err
	}
	switch pb.DscEvTpDf(pb.DscEvTpDf_value[msg.UniqType]) {
	case pb.DscEvTpDf_DscEvTpDfMsgAdd, pb.DscEvTpDf_DscEvTpDfMsgEdit:
		var msgDt = &models.FpDscDmCommu{}
		if err = jsoniter.Unmarshal(logOrg, msgDt); err != nil {
			return nil, err
		}
		detail.MsgDetail, err = srv.convertSingleMsg(msgDt)
	case pb.DscEvTpDf_DscEvTpDfMsgDel:
		var msgIds = make([]string, 0)
		if err = jsoniter.Unmarshal(logOrg, &msgIds); err != nil {
			return nil, err
		}
		detail.DelMsgIds = msgIds
	case pb.DscEvTpDf_DscEvTpDfReactionAdd:
		var reaction = &models.FpDscDmCommuReaction{}
		if err = jsoniter.Unmarshal(logOrg, reaction); err != nil {
			return nil, err
		}
		rct, _ := srv.convertSingleReaction(ctx, msg.UserID, reaction)
		detail.AddReaction = []*pb.DscMsgReaction{rct}
	case pb.DscEvTpDf_DscEvTpDfReactionDel:
		var reaction = &models.FpDscDmCommuReaction{}
		if err = jsoniter.Unmarshal(logOrg, reaction); err != nil {
			return nil, err
		}
		rct, _ := srv.convertSingleReaction(ctx, msg.UserID, reaction)
		detail.DelReaction = []*pb.DscMsgReaction{rct}
	default:
		return nil, fmt.Errorf("genMsgUnreadEventDetail: unknown event type: %s", msg.UniqType)
	}
	return detail, err
}

func (srv *dscService) ChannelDialogForRemote(ctx echo.Context, param *pb.DscChannelDialogReq) ([]*pb.DscDialogDetail, error) {
	var (
		msgIds      = make([]string, 0)
		referMsgIds = make([]string, 0)
		hists       = make([]*pb.DscDialogDetail, 0)
		wg          errgroup.Group
	)

	if param.Limit == 0 {
		param.Limit = 50
	}
	if param.Limit > 100 {
		param.Limit = 100
	}

	user, err := persistence.NewDscInteractions().GetDscUserByChannel(ctx.Request().Context(), param.ChannelId)
	if err != nil {
		return nil, err
	}
	if user == nil || user.ID == 0 {
		return nil, fmt.Errorf("user not exist. channelId:" + param.ChannelId)
	}
	sess := dsc.GetClient(ctx.Request().Context(), user.AppID)
	if sess == nil {
		return nil, fmt.Errorf("get session return nil. " + user.AppID)
	}

	mss, err := sess.ChannelMessages(user.PrivChannelID, int(param.Limit), param.Before, param.After, "")
	if err != nil {
		return nil, err
	}
	// message detail
	for _, ms := range mss {
		if ms.ID == "1262738230693072956" || ms.ID == "1257907160403152989" {
			fmt.Println(utils.ToJson(ms), "-----")
		}
		msgIds = append(msgIds, ms.ID)
		dial, _ := srv.convertSingleRemoteMsg(ctx.Request().Context(), user, ms)
		if dial.ReferencedMsgId != "" {
			referMsgIds = append(referMsgIds, dial.ReferencedMsgId)
		}
		hists = append(hists, dial)
	}
	// .1 clean unread message
	wg.Go(func() error {
		if _err := persistence.NewDscInteractions().MsgUnreadRemove(ctx.Request().Context(), param.ChannelId); _err != nil {
			logger.Errorf(ctx.Request().Context(), "ChannelDialogForRemote MsgUnreadRemove err. err:%v. channelId:%s", _err, param.ChannelId)
		}
		return nil
	})
	// .2 get message refer
	wg.Go(func() error {
		if len(referMsgIds) == 0 {
			return nil
		}
		refers, err := persistence.NewDscInteractions().ChannelMessageHistories(ctx.Request().Context(), &pb.DscChannelDialogReq{
			ChannelId: param.ChannelId,
			Limit:     param.Limit,
			MsgIds:    referMsgIds,
		})
		if err != nil {
			return err
		}
		for i, hist := range hists {
			if hist.ReferencedMsgId == "" {
				continue
			}
			for _, refer := range refers {
				if refer.MsgID == hist.ReferencedMsgId {
					_rf, _ := srv.convertSingleMsg(refer)
					hists[i].ReferencedMsg = _rf
				}
			}
		}
		return nil
	})

	// .3 bot_user rewrite to user_name
	wg.Go(func() error {
		if len(msgIds) == 0 {
			return nil
		}
		sends, err := persistence.NewDscInteractions().GetMsgIdSendUsers(ctx.Request().Context(), msgIds)
		if err != nil {
			return err
		}
		for i, hist := range hists {
			if _, ok := sends[hist.MsgId]; ok {
				hists[i].Author.Username = sends[hist.MsgId]
				hists[i].Checked = false
			}
		}
		return nil
	})
	// wait
	if err := wg.Wait(); err != nil {
		return nil, err
	}
	// sort
	sort.SliceStable(hists, func(i, j int) bool {
		return hists[i].MsgId < hists[j].MsgId
	})
	// return
	return hists, nil
}

func (srv *dscService) ChannelDialog(ctx echo.Context, param *pb.DscChannelDialogReq) ([]*pb.DscDialogDetail, error) {
	// 1. 获取会话历史
	var (
		msgIds      = make([]string, 0)
		referMsgIds = make([]string, 0)
		hists       = make([]*pb.DscDialogDetail, 0)
		wg          errgroup.Group
	)
	if param.Limit == 0 {
		param.Limit = 50
	} else if param.Limit > 1000 {
		param.Limit = 1000
	}
	communs, err := persistence.NewDscInteractions().ChannelMessageHistories(ctx.Request().Context(), param)
	if err != nil {
		return nil, err
	}
	// 获取数据
	for _, commu := range communs {
		msgIds = append(msgIds, commu.MsgID)
		dial, _ := srv.convertSingleMsg(commu)
		if dial.ReferencedMsgId != "" {
			referMsgIds = append(referMsgIds, dial.ReferencedMsgId)
		}
		hists = append(hists, dial)
	}
	// .1 被引用消息 回补
	wg.Go(func() error {
		if len(referMsgIds) == 0 {
			return nil
		}
		refers, err := persistence.NewDscInteractions().ChannelMessageHistories(ctx.Request().Context(), &pb.DscChannelDialogReq{
			ChannelId: param.ChannelId,
			Limit:     param.Limit,
			MsgIds:    referMsgIds,
		})
		if err != nil {
			return err
		}
		for i, hist := range hists {
			if hist.ReferencedMsgId == "" {
				continue
			}
			for _, refer := range refers {
				if refer.MsgID == hist.ReferencedMsgId {
					_rf, _ := srv.convertSingleMsg(refer)
					hists[i].ReferencedMsg = _rf
				}
			}
		}
		return nil
	})

	// .2 消息表情回补
	wg.Go(func() error {
		if len(msgIds) == 0 {
			return nil
		}
		reactions, err := persistence.NewDscInteractions().CommuReactionList(ctx.Request().Context(), msgIds)
		if err != nil {
			return err
		}
		for i, hist := range hists {
			if _, ok := reactions[hist.MsgId]; ok {
				for _, reaction := range reactions[hist.MsgId] {
					rct, _ := srv.convertSingleReaction(ctx.Request().Context(), hist.FromUserId, reaction)
					hists[i].Reactions = append(hists[i].Reactions, rct)
				}
			}
		}
		return nil
	})

	// .3 回补 发送人信息
	wg.Go(func() error {
		if len(msgIds) == 0 {
			return nil
		}
		sends, err := persistence.NewDscInteractions().GetMsgIdSendUsers(ctx.Request().Context(), msgIds)
		if err != nil {
			return err
		}
		for i, hist := range hists {
			if _, ok := sends[hist.MsgId]; ok && hist.Author != nil {
				hists[i].Author.Username = sends[hist.MsgId]
			}
		}
		return nil
	})
	if err := wg.Wait(); err != nil {
		return hists, err
	}

	// sort
	sort.SliceStable(hists, func(i, j int) bool {
		return hists[i].MsgId < hists[j].MsgId
	})

	// todo add clear unread
	return hists, nil
}
func (srv *dscService) convertSingleReaction(ctx context.Context, userId string, reaction *models.FpDscDmCommuReaction) (*pb.DscMsgReaction, error) {
	emoji := reaction.DecodeEmoji()
	return &pb.DscMsgReaction{
		MsgId:     reaction.MsgID,
		Name:      emoji.Name,
		UserName:  dsccore.DefaultCacheUser.UserName(ctx, userId),
		UserId:    reaction.FromUserID,
		CreatedAt: utils.TimeFormat(reaction.CreatedAt.Unix()),
	}, nil
}

func (srv *dscService) convertSingleRemoteMsg(ctx context.Context, user *models.FpDscUser, ms *discordgo.Message) (*pb.DscDialogDetail, error) {
	var _cm = &models.FpDscDmCommu{
		Author:      utils.ToJson(ms.Author),
		Attachments: utils.ToJson(ms.Attachments),
		Embeds:      utils.ToJson(ms.Embeds),
		Stickers:    utils.ToJson(ms.StickerItems),
		Poll:        utils.ToJson(ms.Poll),
	}

	//var fromUserId  string
	var (
		me, his string = user.AppID, user.DscUserID
	)
	var dial = &pb.DscDialogDetail{
		MsgId:           ms.ID,
		Project:         user.Project,
		FromUserId:      ms.Author.ID,
		ChannelId:       ms.ChannelID,
		CreatedAt:       ms.Timestamp.UTC().Format("2006-01-02 15:04:05"),
		IsEdited:        ms.EditedTimestamp != nil,
		Author:          _cm.DecodeAuthor(),
		Content:         ms.Content,
		Attach:          _cm.DecodeAttach(),
		Embed:           _cm.DecodeEmbed(),
		Stickers:        _cm.DecodeSticker(),
		Poll:            nil,
		Reactions:       make([]*pb.DscMsgReaction, 0),
		ReferencedMsgId: "",
	}
	if ms.MessageReference != nil && ms.MessageReference.MessageID != "" {
		dial.ReferencedMsgId = ms.MessageReference.MessageID
	}
	if p := _cm.DecodePoll(); p != nil && p.QuestionText != "" {
		dial.Poll = p
	}

	for _, r := range ms.Reactions {
		var _me string
		if r.Me {
			_me = me
		} else {
			_me = his
		}
		dial.Reactions = append(dial.Reactions, &pb.DscMsgReaction{
			MsgId:     ms.ID,
			Name:      r.Emoji.Name,
			UserId:    ms.Author.ID,
			UserName:  dsccore.DefaultCacheUser.UserName(ctx, _me),
			CreatedAt: "-",
		})
	}
	return dial, nil
}

func (srv *dscService) convertSingleMsg(commu *models.FpDscDmCommu) (*pb.DscDialogDetail, error) {
	var dial = &pb.DscDialogDetail{
		MsgId:           commu.MsgID,
		Project:         commu.Project,
		FromUserId:      commu.FromUserID,
		ChannelId:       commu.ChannelID,
		CreatedAt:       utils.TimeFormat(commu.CreatedAt.Unix()),
		IsEdited:        commu.Edited,
		Author:          commu.DecodeAuthor(),
		Content:         commu.Content,
		Attach:          commu.DecodeAttach(),
		Embed:           commu.DecodeEmbed(),
		Stickers:        commu.DecodeSticker(),
		Reactions:       make([]*pb.DscMsgReaction, 0),
		Poll:            nil,
		ReferencedMsgId: commu.ReferencedMsgID,
	}
	if p := commu.DecodePoll(); p != nil && p.QuestionText != "" {
		dial.Poll = p
	}
	return dial, nil
}
func (srv *dscService) DialogFresh(ctx echo.Context, param *pb.DscChannelDialogFreshReq) (*pb.DscChannelDialogFreshResp, error) {
	var (
		fun  = "dscService.DialogFresh"
		resp = &pb.DscChannelDialogFreshResp{
			DialogList: make([]*pb.DscDialogDetail, 0),
			FreshEvent: make([]*pb.DscDialogFreshEvent, 0),
		}
		eg errgroup.Group
	)
	_ = fun
	eg.Go(func() error { // 获取新增 会话历史
		his, err := srv.ChannelDialog(ctx, &pb.DscChannelDialogReq{
			ChannelId: param.ChannelId,
			After:     param.After,
			Limit:     500,
		})
		if err != nil {
			return err
		}
		resp.DialogList = his
		return nil
	})
	eg.Go(func() error { // 获取新增 event
		unreadEv, err := srv.ChannelMsgUnread(ctx, param.ChannelId)
		if err != nil {
			return err
		}
		resp.FreshEvent = unreadEv
		return nil
	})
	if err := eg.Wait(); err != nil {
		return nil, err
	}
	return resp, nil
}
func (srv *dscService) MessageCreate(ctx echo.Context, param *pb.DscChannelMsgCreateReq) (*discordgo.Message, error) {
	// 1. 执行消息发送
	client := dsc.GetClient(ctx.Request().Context(), param.BotId)
	if client == nil {
		return nil, fmt.Errorf("get discord session client: message create: bot not found: %s", param.BotId)
	}
	// 2. 发送消息
	msgDetail, err := client.ChannelMessageSend(param.ChannelId, param.Content)
	if err != nil {
		return nil, err
	}
	persistence.NewDscInteractions().DmCommuAccountSend(ctx.Request().Context(), msgDetail.ID, param.BotId, param.ChannelId, ctx.Get(cst.AccountInfoCtx).(string), pb.OpGroup_OpGroupSendTextMessage.String(), pb.OpAction_OpActionAdd.String(), param.IgnoreState)
	return msgDetail, nil
}

func (srv *dscService) MessageBatchCreate(ctx echo.Context, param *pb.DscMsgBatchSendReq) (int, []int64, []*discordgo.Message, error) {
	success := 0
	failedUidList := []int64{}
	dest := []*discordgo.Message{}
	for _, uid := range param.UidList {
		if uid == 0 {
			failedUidList = append(failedUidList, uid)
			continue
		}
		channelIds, err := persistence.NewDiscordCommu().GetChannelIdsByProjectUid(param.Project, uid)
		if err != nil {
			logger.Errorf(ctx.Request().Context(), "GetChannelIdByUid err:%v. project:%s. uid:%d", err, param.Project, uid)
			failedUidList = append(failedUidList, uid)
			continue
		}
		for _, channelDt := range channelIds {
			client := dsc.GetClient(ctx.Request().Context(), channelDt.AppId)
			if client == nil || channelDt.PrivChannelId == "" {
				logger.Errorf(ctx.Request().Context(), "get discord session client: message create: bot not found. param:%+v. uid:%d. channelDt:%+v", param, uid, channelDt)
				failedUidList = append(failedUidList, uid)
				continue
			}
			msgDetail, err := client.ChannelMessageSend(channelDt.PrivChannelId, param.Content)
			if err != nil {
				logger.Errorf(ctx.Request().Context(), "ChannelMessageSend err:%v. channelId:%s. param:%+v", err, channelDt.PrivChannelId, param)
				failedUidList = append(failedUidList, uid)
				continue
			}
			dest = append(dest, msgDetail)
			success++
			// 记录操作日志
			persistence.NewDscInteractions().DmCommuAccountSend(ctx.Request().Context(), msgDetail.ID, channelDt.AppId, channelDt.PrivChannelId, ctx.Get(cst.AccountInfoCtx).(string), pb.OpGroup_OpGroupSendTextMessage.String(), pb.OpAction_OpActionAdd.String(), 0)
		}
	}
	return success, failedUidList, dest, nil
}

// DelGuildMember 将玩家从服务器剔除
func (srv *dscService) DelGuildMember(ctx context.Context, botId, guildId, userId string) error {
	client := dsc.GetClient(ctx, botId)
	if client == nil {
		return fmt.Errorf("get discord session client: delGuildMember: bot not found")
	}
	err := client.GuildMemberDelete(guildId, userId)
	if err != nil {
		return err
	}
	return nil
}

func (srv *dscService) MessageEdit(ctx echo.Context, param *pb.DscChannelMsgEditReq) (*discordgo.Message, error) {
	// 1. 执行消息发送
	client := dsc.GetClient(ctx.Request().Context(), param.BotId)
	if client == nil {
		return nil, fmt.Errorf("get discord session client: message edit: bot not found: %s", param.BotId)
	}
	// 2. 发送消息
	msgDetail, err := client.ChannelMessageEdit(param.ChannelId, param.MsgId, param.Content)
	if err != nil {
		return nil, err
	}
	return msgDetail, nil
}

func (srv *dscService) DscFileBatchSend(ctx echo.Context, param *pb.DscFileBatchSendReq, fileName string, fileReader io.Reader) (int, []int64, []*discordgo.Message, error) {
	fun := "DscFileBatchSend"
	// 将文件读到内存，以便重复使用
	fileData, err := io.ReadAll(fileReader)
	if err != nil {
		return 0, param.UidList, nil, fmt.Errorf("read file to memory return err: %v", err)
	}
	success := 0
	failedUidList := []int64{}
	dest := []*discordgo.Message{}
	for _, uid := range param.UidList {
		if uid == 0 {
			failedUidList = append(failedUidList, uid)
			continue
		}

		// 获取uid对应的channelIds
		channelIds, err := persistence.NewDiscordCommu().GetChannelIdsByProjectUid(param.Project, uid)
		if err != nil {
			logger.Errorf(ctx.Request().Context(), "%s GetChannelIdByUid err:%v. project:%s. uid:%d", fun, err, param.Project, uid)
			failedUidList = append(failedUidList, uid)
			continue
		}
		// 处理每一个channel_id -- 支持单有多bot
		for _, channelDt := range channelIds {
			// 获取 client
			client := dsc.GetClient(ctx.Request().Context(), channelDt.AppId)
			if client == nil || channelDt.PrivChannelId == "" {
				logger.Errorf(ctx.Request().Context(), "%s get discord session client: message create: bot not found. param:%+v. uid:%d. channelDt:%+v", fun, param, uid, channelDt)
				failedUidList = append(failedUidList, uid)
				continue
			}

			// 创建新的io.Reader
			newFileReader := bytes.NewReader(fileData)
			// 创建新的cFile
			cFile := &discordgo.MessageSend{
				Files: []*discordgo.File{{
					Name:   fileName,
					Reader: newFileReader,
				}},
			}
			msgDetail, err := client.ChannelMessageSendComplex(channelDt.PrivChannelId, cFile)
			if err != nil {
				logger.Errorf(ctx.Request().Context(), "%s ChannelMessageSendComplex err:%v. channelDt:%+v", fun, err, channelDt)
				failedUidList = append(failedUidList, uid)
				continue
			}
			dest = append(dest, msgDetail)
			success++
			// 记录操作日志
			persistence.NewDscInteractions().DmCommuAccountSend(ctx.Request().Context(), msgDetail.ID, channelDt.AppId, channelDt.PrivChannelId, ctx.Get(cst.AccountInfoCtx).(string), pb.OpGroup_OpGroupSendFile.String(), pb.OpAction_OpActionAdd.String(), 0)
		}
	}
	return success, failedUidList, dest, nil
}

func (srv *dscService) DscFileSend(ctx echo.Context, param *pb.DscChannelFileCreateReq, fileName string, fileReader io.Reader) (*discordgo.Message, error) {
	// 1. 执行消息发送
	client := dsc.GetClient(ctx.Request().Context(), param.BotId)
	if client == nil {
		return nil, fmt.Errorf("get discord session client: file send: bot not found: %s", param.BotId)
	}
	var cFile = &discordgo.MessageSend{
		Files: []*discordgo.File{{
			Name:   fileName,
			Reader: fileReader,
		},
		},
	}

	msgDetail, err := client.ChannelMessageSendComplex(param.ChannelId, cFile)
	fmt.Println("ChannelMessageSendComplex:", err, utils.ToJson(msgDetail))
	if err != nil {
		return nil, err
	}
	persistence.NewDscInteractions().DmCommuAccountSend(ctx.Request().Context(), msgDetail.ID, param.BotId, param.ChannelId, ctx.Get(cst.AccountInfoCtx).(string), pb.OpGroup_OpGroupSendFile.String(), pb.OpAction_OpActionAdd.String(), 0)
	return msgDetail, nil
}

func (srv *dscService) MessageReply(ctx echo.Context, param *pb.DscChannelMsgEditReq) (*discordgo.Message, error) {
	client := dsc.GetClient(ctx.Request().Context(), param.BotId)
	if client == nil {
		return nil, fmt.Errorf("get discord session client: message create: bot not found: %s", param.BotId)
	}
	reference := &discordgo.MessageReference{
		MessageID: param.MsgId,
		ChannelID: param.ChannelId,
	}
	msgDetail, err := client.ChannelMessageSendReply(param.ChannelId, param.Content, reference)
	if err != nil {
		return nil, err
	}
	persistence.NewDscInteractions().DmCommuAccountSend(ctx.Request().Context(), msgDetail.ID, param.BotId, param.ChannelId, ctx.Get(cst.AccountInfoCtx).(string), pb.OpGroup_OpGroupSendTextMessage.String(), pb.OpAction_OpActionAdd.String(), 0)
	return msgDetail, nil
}

func (srv *dscService) AsyncMessageBatchCreate(ctx echo.Context, param *pb.DscAscBatchSendReq) error {
	var task = &models.FpDscMessageTask{
		Project:  param.Project,
		Operator: ctx.Get(cst.AccountInfoCtx).(string),
		Status:   int(pb.DscMsgTaskStatus_ProcessStatusInit),
		Content:  param.Content,
		FileUrl:  param.FileUrl,
	}
	// 初始化两张表
	var taskDetails []*models.FpDscMessageTaskDetail
	for _, uid := range param.UidList {
		if uid == 0 {
			continue
		}
		// 获取用户信息
		accountId, err := persistence.NewDiscordInteract().FetchUserInfoByUID(uid, param.Project)
		if err != nil {
			logger.Infof(ctx.Request().Context(), "%v FetchUserInfoByUID err:%v", uid, err)
			if !errors.Is(err, gorm.ErrRecordNotFound) {
				return err
			}
			//uid不存在
			taskDetail := models.FpDscMessageTaskDetail{
				Project:   param.Project,
				UID:       uid,
				AccountID: "",
				DscUserID: "",
				BotID:     "",
				ChannelID: "", // 保存该UID的channelId
				Status:    int(pb.DscMsgTaskDetailStatus_DscTaskDetailStatusDoing),
			}
			// 将taskDetail添加到log.Users列表
			taskDetails = append(taskDetails, &taskDetail)
			continue
		}

		channelIds, err := persistence.NewDiscordCommu().GetChannelIdsByProjectUid(param.Project, uid)
		if err != nil {
			logger.Errorf(ctx.Request().Context(), "GetChannelIdsByProjectUid err:%v for UID:%v", err, uid)
			return err
		}
		// 遍历每个UID对应的所有channelId
		for _, channelDt := range channelIds {
			taskDetail := models.FpDscMessageTaskDetail{
				Project:   param.Project,
				UID:       uid,
				AccountID: accountId,
				DscUserID: channelDt.DscUserId,
				BotID:     channelDt.AppId,
				ChannelID: channelDt.PrivChannelId, // 保存该UID的channelId
				Status:    int(pb.DscMsgTaskDetailStatus_DscTaskDetailStatusDoing),
			}
			// 将taskDetail添加到log.Users列表
			taskDetails = append(taskDetails, &taskDetail)
		}
	}
	// 先插入主任务task
	if err := persistence.NewDscMessageTask().TaskAdd(ctx.Request().Context(), task); err != nil {
		return err
	}
	for _, detail := range taskDetails {
		detail.TaskID = task.ID
	}
	// 批量插入taskDetails
	if err := persistence.NewDscMessageTask().TaskBatchAdd(ctx.Request().Context(), taskDetails); err != nil {
		return err
	}
	if task.ID < 1 {
		return errors.New("return log id lte0")
	}
	//推送信号
	communication.PushMessageBatchSend(int64(task.ID))
	return nil
}

func (srv *dscService) AsyncDiffContentBatchCreate(ctx echo.Context, project string, allSurveyLinks []*models.FpDscSurveyLinks) error {
	messageCount := 0
	delays := utils.GenerateDelays()
	for _, link := range allSurveyLinks {
		linkParam := link.DecodeTokenParam(ctx.Request().Context())
		if link.UID == 0 || linkParam.MomentAttrs == nil || linkParam.MomentAttrs.DscChannelId == "" || linkParam.MomentAttrs.DscBotId == "" {
			logger.Errorf(ctx.Request().Context(), "AsyncDiffContentBatchCreate linkParam err:%v for UID:%v. linkDetail:%+v.", linkParam, link.UID, link)
			continue
		}

		// 2. 校验是否已发送消息
		uniqueId := fmt.Sprintf("%d.%d.%s", link.SurveyID, link.BatchID, link.DscUserID)
		if has, err := persistence.NewDscInteractions().OpLogExistCheck(pb.OpGroup_OpGroupSurveySend, pb.OpAction_OpActionAdd, uniqueId); err != nil {
			logger.Errorf(ctx.Request().Context(), "OpLogExistCheck err:%v for UID:%v. linkDetail:%+v.", err, link.UID, link)
			continue
		} else if has {
			logger.Infof(ctx.Request().Context(), "OpLogExistCheck has for UID:%v. linkDetail:%+v.", link.UID, link)
			continue
		}

		//  3. add 发送消息
		if err := persistence.NewDscInteractions().AddSendSurveyLinkLog(link.SurveyID, link.DscUserID, link.Project, link.BatchID, link); err != nil {
			logger.Errorf(ctx.Request().Context(), "AddSendSurveyLinkLog err:%v for UID:%v. linkDetail:%+v.", err, link.UID, link)
			if _sqlErr, ok := err.(*mysql.MySQLError); ok && _sqlErr.Number == 1062 {
				continue
			}
			return err
		}

		// 3. todo add logic 推送消息不修改消息的状态
		//if r, _innErr := srv.MessageCreate(ctx, &pb.DscChannelMsgCreateReq{ChannelId: channelIds[0].PrivChannelId, BotId: channelIds[0].AppId, Content: link.LinkURL, IgnoreState: 1}); _innErr != nil {
		if r, _innErr := srv.MessageCreate(ctx, &pb.DscChannelMsgCreateReq{ChannelId: linkParam.MomentAttrs.DscChannelId, BotId: linkParam.MomentAttrs.DscBotId, Content: link.LinkURL, IgnoreState: 1}); _innErr != nil {
			logger.Warn(ctx.Request().Context(), "AsyncDiffContentBatchCreate messageCreate-> return err.", logger.Any("err", _innErr), logger.Any("message", r), logger.Any("linkParam", linkParam))
		} else {
			logger.Info(ctx.Request().Context(), "AsyncDiffContentBatchCreate messageCreate-> return success.", logger.Any("linkParam", linkParam), logger.Any("link", link))
		}
		time.Sleep(delays[messageCount])
		messageCount = (messageCount + 1) % 5
	}
	return nil
}
