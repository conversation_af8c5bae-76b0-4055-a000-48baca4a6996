// Copyright 2021 funplus Authors. All Rights Reserved.
// @Description:
// @Author: Darcy
// @Date: 2022/7/28 15:18

package ticket

import (
	"encoding/json"
	"fmt"
	"go.uber.org/zap"
	"ops-ticket-api/internal/code"
	"ops-ticket-api/internal/persistence"
	"ops-ticket-api/models"
	"runtime/debug"
	"strings"

	"ops-ticket-api/internal/elasticsearch"
	"ops-ticket-api/internal/framework/lang"
	"ops-ticket-api/proto/pb"
	"ops-ticket-api/utils"

	"github.com/labstack/echo/v4"
	"github.com/spf13/cast"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
)

// RecordInfoExport 工单池导出工单
func RecordInfoExport(ctx echo.Context, opts []elasticsearch.Option) (<-chan []string, error) {
	fun := "RecordInfoExport -->"
	tickets, err := elasticsearch.DefaultTicketEsRepo.GetTicketPoolScroll(ctx.Request().Context(), opts...)
	if err != nil {
		return nil, err
	}
	ticketsRecord := make(chan []string, 256)
	go func() {
		defer func() {
			if err := recover(); err != nil {
				logger.Error(ctx.Request().Context(), "export recover err",
					zap.Any("err", err),
					zap.String("stack", string(debug.Stack())))
			}
			close(ticketsRecord)
		}()
		var (
			now     = utils.NowTimestamp()
			catRepo = persistence.NewCat()
			libRepo = persistence.NewTags()
			catList = make(map[uint32][]string, 0)
			libList = make(map[uint32][]string, 0)
			catFunc = func(catId uint32) string {
				if v, ok := catList[catId]; ok {
					return strings.Join(v, "-")
				}
				cat, _innerErr := catRepo.CatNameSlice(ctx.Request().Context(), catId, "")
				if _innerErr != nil {
					logger.Errorf(ctx.Request().Context(), "%s get cat name err. catid: %d, err: %v", fun, catId, _innerErr)
				}
				if cat == nil || len(cat) == 0 {
					cat = []string{"unknown"}
				}
				catList[catId] = cat
				return strings.Join(cat, "-")
			}
			libFunc = func(libId []uint32) string {
				var show = make([]string, 0)
				var noCacheId []uint32
				for _, id := range libId {
					if v, ok := libList[id]; ok {
						show = append(show, strings.Join(v, "-"))
					} else {
						noCacheId = append(noCacheId, id)
					}
				}
				if len(noCacheId) > 0 {
					if libs := libRepo.GetTagsSlice(ctx.Request().Context(), noCacheId); libs != nil {
						for _, id := range noCacheId {
							if v, ok := libs[id]; ok && len(v) > 0 {
								libList[id] = v
								show = append(show, strings.Join(v, "-"))
							} else {
								libList[id] = []string{"unknown"}
								show = append(show, "unknown")
							}
						}
					}
				}
				return strings.Join(show, ", ")
			}
			SystemTagFunc = func(systemTags []uint32) string {
				var systemTag = "-"
				if len(systemTags) > 0 {
					var TagList []string
					for _, v := range systemTags {
						TagList = append(TagList, lang.FormatText(ctx, pb.TicketSystemTag_name[int32(v)]))
					}
					systemTag = strings.Join(TagList, ",")
				}
				return systemTag
			}
			// IssueFunc 问题描述
			IssueFunc = func(issue, lang string) string {
				issueDesp := "-"
				var fieldMap map[string]interface{}
				if issue != "" {
					_ = json.Unmarshal([]byte(issue), &fieldMap)
				}
				for key, value := range fieldMap {
					for _, issue := range models.IssueDescriptionMap {
						if strings.ToLower(key) == issue {
							if _, ok := value.(string); ok {
								issueDesp = value.(string)
							}
							break
						}
					}

				}
				return issueDesp
			}
			// LastReplyFunc 关单/重开单的最后一句客服回复
			LastReplyFunc = func(tk *models.TicketsDoc) string {
				var lastReply = "-"
				if len(tk.Commus) == 0 {
					return lastReply
				}
				// 判断工单是否关单或重开单
				if tk.Stage != uint32(pb.TkStage_TkStageAgentReopen) && tk.Stage != uint32(pb.TkStage_TkStageAgentCompleted) {
					return lastReply
				}
				// 获取最后一条客服回复
				maxCreatedAt := tk.Commus[0].CreatedAt
				lastReply = tk.Commus[0].Detail
				for _, v := range tk.Commus {
					if v.FromRole == 2 && v.CommuType == "CommuTypeDialogue" && v.CreatedAt > maxCreatedAt {
						maxCreatedAt = v.CreatedAt
						lastReply = v.Detail
					}
				}
				return lastReply
			}
			// ZoneVipLevelFunc 私域R级
			ZoneVipLevelFunc = func(zonVipLevel uint64) string {
				var zoneVipLevelRes = "-"
				if zonVipLevel > 0 {
					zoneVipLevelRes = fmt.Sprintf("R%d", zonVipLevel)
				}
				return zoneVipLevelRes
			}
			//CommusFunc = func(tk *models.TicketsDoc) string {
			//	// 组装对话
			//	var dialogue []map[string]string
			//	var role string
			//	for _, commu := range tk.Commus {
			//		if commu.CommuType == "CommuTypeRemark" {
			//			continue
			//		}
			//		if commu.FromRole == 2 {
			//			role = "assistant"
			//		} else {
			//			role = "user"
			//		}
			//		if commu.Detail == "" {
			//			continue
			//		}
			//		dialogue = append(dialogue, map[string]string{role: commu.Detail})
			//
			//	}
			//	dialogueJSON, _ := json.Marshal(dialogue)
			//	return string(dialogueJSON)
			//}
		)

		for tk := range tickets {
			var playerCommu []*models.CommuDoc
			for _, v := range tk.Commus {
				if v.FromRole == uint32(pb.UserRole_PlayerRole) {
					_playerv := v
					playerCommu = append(playerCommu, &_playerv)
				}
			}
			// 等待时长：
			waitTime := "-"
			if tk.SortWaitStartAt <= now {
				waitTime = utils.ResolveTimeStr(now-tk.SortWaitStartAt, 1)
			}
			// 处理时长：是处理完成的时间减去玩家提交工单的时间。处理完成有三种情况，一种是点击“回复&关单”，还有拒单和超时关闭。
			costTime, closeTime := "-", "-"
			if tk.ClosedAt > 0 {
				closeTime = utils.TimeFormatInLoc(int64(tk.ClosedAt))
				costTime = utils.ResolveTimeStr(tk.ClosedAt-tk.CreatedAt, 1)
			}
			acceptor := "-"
			if tk.Acceptor != "" {
				acceptor = tk.Acceptor
			}

			ticket := []string{
				cast.ToString(tk.TicketID),
				cast.ToString(tk.AccountID) + "\t",
				cast.ToString(tk.UID) + "\t",
				cast.ToString(tk.Nickname),
				cast.ToString(utils.FloatRound(float64(tk.Recharge)/code.RechargeRate, 2)),
				lang.FormatText(ctx, pb.TkStage_name[int32(tk.Stage)]),
				lang.FormatText(ctx, pb.SolveType_name[int32(tk.SolveType)]),
				waitTime,
				costTime,
				fmt.Sprintf("\"%v\"", strings.ReplaceAll(utils.ToJson(playerCommu), "\"", "\\\"")),
				libFunc(tk.Tags),
				SystemTagFunc(tk.SystemTags),
				ZoneVipLevelFunc(tk.ZoneVipLevel),
				catFunc(tk.CatID),
				IssueFunc(tk.Field, tk.Lang), //问题描述
				LastReplyFunc(tk),            //客服回复
				cast.ToString(tk.Csi),
				cast.ToString(tk.Nps),
				cast.ToString(tk.Channel),
				cast.ToString(tk.PackageID),
				cast.ToString(tk.Sid),
				cast.ToString(tk.Lang),
				cast.ToString(tk.Country),
				cast.ToString(tk.AppVersion),
				cast.ToString(tk.DeviceType),
				cast.ToString(tk.OsVersion),
				acceptor,
				utils.TimeFormatInLoc(int64(tk.CreatedAt)),
				closeTime,
				cast.ToString(tk.ReopenNum),
				cast.ToString(tk.Comment),
				//CommusFunc(tk),
			}
			ticketsRecord <- ticket
		}
		return
	}()
	return ticketsRecord, nil
}
