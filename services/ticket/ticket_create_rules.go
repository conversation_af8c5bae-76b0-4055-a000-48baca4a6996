package ticket

import (
	"context"
	"fmt"
	"github.com/labstack/echo/v4"
	"github.com/spf13/cast"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"ops-ticket-api/internal/code"
	"ops-ticket-api/internal/framework/cache/keys"
	"ops-ticket-api/internal/framework/cache/rds"
	"ops-ticket-api/internal/framework/xerrors"
	"ops-ticket-api/models"
	"time"
)

func NewTicketCreateRules() *ticketCreateRules {
	return &ticketCreateRules{}
}

type (
	ticketCreateRules       struct{}
	TkCreatRuleCheckParamDf struct {
		Uid         uint64 `json:"uid"`
		AccountId   string `json:"account_id"`
		GameProject string `json:"game_project"`
		DeviceId    string `json:"device_id"`
		Scene       uint32 `json:"scene"`
		Ip          string `json:"ip"`
	}
)

// CheckCreateTicketRule check the request is allowed to create a ticket
// return true if the request is allowed to create a ticket
// return false if the request is not allowed to create a ticket
// return error either the request is not allowed or an error occurred
func (rule ticketCreateRules) CheckCreateTicketRule(ctx echo.Context, param *models.TicketCreateParam) (bool, error) {
	fun := "ticketCreateRules.CheckCreateTicketRule -->"
	now := time.Now()

	rl := []struct {
		Key    string
		Limit  int
		Expire time.Duration
	}{
		{Key: keys.TicketCreate5MinLimit, Limit: 3, Expire: 5 * time.Minute}, // 5 min 单用户&单场景 最多创建 3 次
		{Key: keys.TicketCreate1HLimit, Limit: 5, Expire: time.Hour},         // 1h 内限定提交 5 个工单；
	}

	ticketCreateUniqueKey := fmt.Sprintf("%s_%d_%d_%s_%s", param.Input.FpxAppId, param.Input.Scene, param.Input.Uid, param.Input.AccountId, param.Input.Uuid)
	fiveTmFmt := fmt.Sprintf("%s%02d", now.Format("15"), now.Minute()/5)
	rl[0].Key = fmt.Sprintf(rl[0].Key, fiveTmFmt, ticketCreateUniqueKey)
	rl[1].Key = fmt.Sprintf(rl[1].Key, now.Format("15"), ticketCreateUniqueKey)
	var _kys = []string{rl[0].Key, rl[1].Key}
	res, err := rds.RCli.Client.MGet(ctx.Request().Context(), _kys...).Result()
	if err != nil {
		logger.Errorf(ctx.Request().Context(), "%s rds mget return err. keys:%+v. err:%v", fun, _kys, err)
		return false, xerrors.New(code.StatusText(code.DbError), code.DbError)
	}
	for i, v := range res {
		if v == nil { // 已存在
			continue
		}
		if i >= len(rl) {
			continue
		}
		//fmt.Println(i, v, rl[i].Limit, cast.ToInt(v))
		if cast.ToInt(v) >= rl[i].Limit {
			logger.Infof(ctx.Request().Context(), "%s ticket create limit exceeded. row:%+v. v:%v", fun, rl[i], v)
			return false, xerrors.New(code.StatusText(code.DbError), code.DbError)
		}
	}
	return true, nil
}

func (srv ticketCreateRules) TicketCreateSucSetRule(ctx context.Context, param *models.TicketCreateParam) error {
	fun := "ticketCreateRules.TicketCreateSucSetRule -->"
	now := time.Now()
	rl := []struct {
		Key    string
		Limit  int
		Expire time.Duration
	}{
		{Key: keys.TicketCreate5MinLimit, Limit: 3, Expire: 5 * time.Minute}, // 5 min 单用户&单场景 最多创建 3 次
		{Key: keys.TicketCreate1HLimit, Limit: 5, Expire: time.Hour},         // 1h 内限定提交 5 个工单；
	}

	ticketCreateUniqueKey := fmt.Sprintf("%s_%d_%d_%s_%s", param.Input.FpxAppId, param.Input.Scene, param.Input.Uid, param.Input.AccountId, param.Input.Uuid)
	fiveTmFmt := fmt.Sprintf("%s%02d", now.Format("15"), now.Minute()/5)
	rl[0].Key = fmt.Sprintf(rl[0].Key, fiveTmFmt, ticketCreateUniqueKey)
	rl[1].Key = fmt.Sprintf(rl[1].Key, now.Format("15"), ticketCreateUniqueKey)

	pipe := rds.RCli.Client.Pipeline()
	pipe.IncrBy(ctx, rl[0].Key, 1)
	pipe.Expire(ctx, rl[0].Key, rl[0].Expire)
	pipe.IncrBy(ctx, rl[1].Key, 1)
	pipe.Expire(ctx, rl[1].Key, rl[1].Expire)
	_, err := pipe.Exec(ctx)

	if err != nil {
		logger.Errorf(ctx, "%s rds incr return err. keys:%+v. err:%v", fun, rl, err)
	}
	return err
}
