package ticket_llm

import (
	"context"
	"encoding/json"
	"github.com/avast/retry-go"
	"github.com/darcyx/go-openai"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	ai_model "ops-ticket-api/internal/ai"
	"ops-ticket-api/models"
	"strings"
	"time"
)

// CheckMeaninglessQuery 使用 OpenAI 接口判断玩家提问是否为无意义提问
// 当返回 true 时，表示该提问被判定为无意义，否则为有效提问
func CheckMeaninglessQuery(ctx context.Context, query string) bool {
	if strings.TrimSpace(query) == "" {
		return true
	}
	client := ai_model.Engine(ai_model.SmartModel)
	req := openai.ChatCompletionRequest{
		Model:       "gpt-4o",
		Temperature: 0,
		Messages: []openai.ChatCompletionMessage{
			{
				Role:    "system",
				Content: "你是一个多语言游戏客服助手，专注于判断游戏中玩家的提问是否具有实际意义，主要关注玩家是否表达了与游戏相关的客诉、问题或紧急需求。",
			},
			{
				Role: "user",
				Content: "请根据玩家提问内容，返回‘true‘或‘false‘。‘true‘代表无效提问，‘false’代表有效提问。‘false‘的判断条件是：玩家提问为有实际含义的内容。" +
					"‘true‘的判断条件是：无实际含义或非常模糊的提问内容，问候，简短的名词，例如‘thank you’、‘thanks’、‘hello’、‘ship suggestion’、‘错误’等。" +
					"当无法判断玩家提问是否有效时，请返回“false“。结果请只返回 'true'（表示无效）或 'false'（表示有效），不附带其他任何字符或说明。\n\n玩家提问内容为：" + query,
			},
		},
		Stop: []string{"\n"},
	}

	var resp openai.ChatCompletionResponse
	var _innErr error
	err := retry.Do(func() error {
		if resp, _innErr = client.CreateChatCompletion(ctx, req); _innErr != nil {
			logger.Warn(ctx, "CheckMeaninglessQuery-> err.", logger.Any("err", _innErr), logger.Any("query", query))
			return _innErr
		}
		return nil
	}, retry.Attempts(3), retry.Delay(2*time.Second), retry.LastErrorOnly(true))
	if err != nil {
		logger.Errorf(ctx, "CheckMeaninglessQuery->retry err. query:%s. err:%v", query, err)
		return false
	}

	// 检查返回结果是否至少有一个回答
	if len(resp.Choices) == 0 {
		logger.Errorf(ctx, "CheckMeaninglessQuery->no answer. query:%s. err:%v", query, err)
		return false
	}

	// 取第一个回答，并进行标准化处理（去除首尾空格并转换为小写）
	reply := strings.TrimSpace(strings.ToLower(resp.Choices[0].Message.Content))
	logger.Info(ctx, "CheckMeaninglessQuery->reply", logger.Any("reply", reply))
	if reply == "true" {
		return true
	}
	return false
}

// CheckCategoryMatch 使用 GPT 判断玩家的问题描述是否与指定分类匹配
// 参数 issueDesc 为玩家的问题描述，category为登录/建议分类
// 返回 true 表示匹配，否则返回 false
func CheckCategoryMatch(ctx context.Context, issueDesc string, category string) bool {

	prompt := "请判断以下玩家的问题描述是否与\"" + category + "\"匹配。你的回答必须仅为 'true' 或 'false'，不允许附带其他任何字符或说明。\n\n" +
		"【定义及示例】\n" +
		"1. 如果分类为 \"登录问题\"：该问题描述主要涉及玩家在登录游戏时遇到的各种困难，例如无法登录、登录超时、登录游戏前的更新、加载及卡顿等问题、提示账号错误、密码问题、验证码错误、账号锁定，提示服务器断开连接。" +
		"需要注意的是，account ban/帐号封禁这类问题描述不应该属于'登录问题'。" +
		"、数据错误、试加载游戏时出现错误代码等。例如：\n" +
		"   - \"我无法登录游戏账号\"\n" +
		"   - \"登录时提示密码错误\"\n" +
		"   - \"无法下载游戏\"\n\n" +
		"   - \"进度卡在50%\"\n\n" +
		"   - \"加载50%\"\n\n" +
		"   - \"更新错误\"\n\n" +
		"2. 如果分类为 \"建议问题\"：\n" +
		"   - 建议 (都应返回 true)：玩家对游戏功能、体验、界面提出改进意见或新需求。例如：\n" +
		"       - \"建议增加好友互动功能\"\n" +
		"       - \"希望改进游戏加载速度\"\n\n" +
		"   - 非建议 (都应返回 false)：\n" +
		"       1) Bug 反馈：描述功能异常、无法操作、报错等。例如：\n" +
		"           - \"在攻击港口时无法移动，即使网络正常\"\n" +
		"           - \"客户端崩溃后无法重连\"\n" +
		"       2) 投诉/客诉：表达不满或指责运营、充值问题。例如：\n" +
		"           - \"充值了没到账，给我解决！\"\n" +
		"           - \"每次更新都卡死，太垃圾了\"\n\n" +
		"请根据上述定义判断玩家问题描述是否与所选分类匹配。\n\n玩家问题描述：" + issueDesc

	req := openai.ChatCompletionRequest{
		Model:       "gpt-4o",
		Temperature: 0,
		Messages: []openai.ChatCompletionMessage{
			{
				Role:    "system",
				Content: "你是一个多语言游戏客服助手，专注于判断玩家的问题描述与选择的问题分类的匹配情况。",
			},
			{
				Role:    "user",
				Content: prompt,
			},
		},
		Stop: []string{"\n"},
	}

	client := ai_model.Engine(ai_model.SmartModel)
	var resp openai.ChatCompletionResponse
	err := retry.Do(func() error {
		var err error
		resp, err = client.CreateChatCompletion(ctx, req)
		return err
	}, retry.Attempts(3), retry.Delay(2*time.Second), retry.LastErrorOnly(true))
	if err != nil || len(resp.Choices) == 0 {
		logger.Errorf(ctx, "CheckCategoryMatch->retry err. issueDesc:%s. category:%s. err:%v", issueDesc, category, err)
		return false
	}

	reply := strings.TrimSpace(strings.ToLower(resp.Choices[0].Message.Content))
	// 仅当返回值严格为 "true" 时认为匹配，否则不匹配
	if reply == "true" {
		return true
	}
	return false
}

// HasTicketDesc 判断是否有工单描述
func HasTicketDesc(ticket *models.FpOpsTickets) (string, bool) {
	// 跳过没有问题描述的单
	var fieldMap map[string]interface{}
	_ = json.Unmarshal([]byte(ticket.Fields.Field), &fieldMap)
	var flag bool
	var qst string
	for key, value := range fieldMap {
		for _, issue := range models.IssueDescriptionMap {
			if strings.Contains(strings.ToLower(key), issue) {
				if _, ok := value.(string); ok {
					qst = value.(string)
					flag = true
					break
				}
			}
		}
	}
	return qst, flag
}
