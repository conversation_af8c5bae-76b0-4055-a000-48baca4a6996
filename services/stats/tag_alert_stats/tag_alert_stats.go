package tag_alert_stats

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"github.com/avast/retry-go"
	"github.com/darcyx/go-openai"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"io"
	"net/http"
	ai_model "ops-ticket-api/internal/ai"
	"ops-ticket-api/internal/persistence"
	"ops-ticket-api/models"
	"ops-ticket-api/proto/pb"
	"ops-ticket-api/services/ticket_llm"
	"ops-ticket-api/utils"
	"sort"
	"strings"
	"time"
)

const (
	webHookUrl = "https://open.feishu.cn/open-apis/bot/v2/hook/8a78da7e-16b9-4f9a-8c04-0f32bcb1111d"
	project    = "mo_global"
)

func TagAlertStats(ctx context.Context) error {
	logger.Info(ctx, "TagAlertStats start")
	elements := []map[string]interface{}{}

	//tm := time.Now().UTC()
	//year, month, day := tm.Date()
	//tm = time.Date(year, month, day, 0, 0, 0, 0, tm.Location()) // 只保留年月日部分
	//endTs := uint64(tm.Unix())
	//fmt.Println(endTs)
	//
	//endTime := time.Unix(int64(endTs), 0)
	//startTs := uint64(endTime.AddDate(0, 0, -14).Unix()) // 13 天前 0 时起
	//fmt.Println(startTs)
	startTs := uint64(1745366400)
	endTs := uint64(1747180800)

	// 1. 绘制每日工单量图
	elements, err := GenTicketCountBar(ctx, elements, startTs, endTs)
	if err != nil {
		logger.Errorf(ctx, "GenTicketCount failed: %v", err)
		return err
	}
	// 2.获取数据：（1.ticket表双周所有数据  2. ticketTag表双周所有数据 3.统计特殊模块标签工单数量）
	tickets, err := persistence.NewTicket().GetWeeklyTickets(ctx, project, startTs, endTs)
	if err != nil {
		logger.Errorf(ctx, "GetWeeklyTickets failed: %v", err)
		return err
	}
	// 3.去除未打标或打标unk的工单
	var filtered []*models.FpOpsTickets
	for _, t := range tickets {
		if t.AiTag.TagID != 0 && t.AiTag.TagType != uint8(pb.TicketAiTagType_TicketAiTagNotTag) {
			filtered = append(filtered, t)
		}
	}
	tickets = filtered

	// 4.绘制标签库分布
	elements = GenTicketTagLibPie(ctx, elements, tickets)

	// 5.绘制客询客诉建议分布图
	elements = GenTicketTagTypePie(ctx, elements, tickets)

	// 6.一级标签分类状况图
	elements, allTickets := GenTopTicketsCountBar(ctx, elements, tickets)

	// 7.一级标签分类客询类
	elements = GenTopTicketsCountAskBar(ctx, elements, tickets)
	// 8. 一级标签分类客诉类
	elements = GenTopTicketsCountComplainBar(ctx, elements, tickets)

	//  9.取前6名一级标签，二级标签细分状况分析
	cnt := 6
	if len(allTickets) < 6 {
		cnt = len(allTickets)
	}
	for i := 0; i < cnt; i++ {
		firstTag := allTickets[i]["分类"]
		elements = GenSecondTicketsCountBar(ctx, firstTag.(string), tickets, elements)
	}
	// 10.获取特殊标签集中异常情况
	elements, err = GenSpecialTicketsCountTable(ctx, tickets, elements)
	if err != nil {
		logger.Errorf(ctx, "GenSpecialTicketsCountBar failed: %v", err)
		return err
	}
	// 12.发送飞书消息
	err = sendFeishuMessage(elements)
	if err != nil {
		logger.Errorf(ctx, "sendFeishuMessage failed: %v", err)
		return err
	}
	// 11.获取每条普客建议并总结
	err = GenSuggestionTicketsTable(ctx, tickets)
	if err != nil {
		logger.Errorf(ctx, "GenSpecialTicketsCountBar failed: %v", err)
		return err
	}

	logger.Info(ctx, "TagAlertStats finished")
	return nil
}

func sendFeishuMessage(elements []map[string]interface{}) error {
	// 构造卡片 JSON
	payload := map[string]interface{}{
		"msg_type": "interactive",
		"card": map[string]interface{}{
			"schema": "2.0",
			"config": map[string]interface{}{
				"wide_screen_mode": true,
			},
			"header": map[string]interface{}{
				"title": map[string]interface{}{
					"tag":     "plain_text",
					"content": "双图表示例：柱状图 + 饼状图",
				},
			},
			"body": map[string]interface{}{
				"elements": elements,
			},
		},
	}

	// 序列化并发送请求
	bodyBytes, _ := json.Marshal(payload)
	fmt.Println("请求 Body:", string(bodyBytes))

	resp, err := http.Post(webHookUrl, "application/json; charset=utf-8", bytes.NewBuffer(bodyBytes))
	if err != nil {
		fmt.Println("发送失败：", err)
		return err
	}
	defer resp.Body.Close()

	respBody, _ := io.ReadAll(resp.Body)
	fmt.Printf("响应状态：%s\n响应 Body：%s\n", resp.Status, string(respBody))
	return nil
}

// GenTicketCountBar 获取双周工单每日个数并绘图
func GenTicketCountBar(ctx context.Context, elements []map[string]interface{}, startTs, endTs uint64) ([]map[string]interface{}, error) {
	counts, err := persistence.NewTicket().GetTwoWeeksTicketsCount(ctx, project, startTs, endTs)
	if err != nil {
		logger.Errorf(ctx, "GetTwoWeeksTicketsCount failed: %v", err)
		return elements, err
	}
	var ticketCountMap []map[string]interface{}
	for _, countInfo := range counts {
		ticketCountMap = append(ticketCountMap, map[string]interface{}{
			"分类": countInfo.Date,
			"数值": countInfo.Count,
		})
	}
	elements = append(elements, map[string]interface{}{
		// 工单量
		"tag": "chart",
		"chart_spec": map[string]interface{}{
			"type": "bar",
			"title": map[string]interface{}{
				"text": "工单量",
			},
			"data": map[string]interface{}{
				"values": ticketCountMap,
			},
			"xField": "分类",
			"yField": "数值",
		},
	})
	return elements, nil
}

// GenTicketTagLibPie  生成“标签库分布占比”饼图，并返回更新后的 elements 列表
func GenTicketTagLibPie(ctx context.Context, elements []map[string]interface{}, tickets []*models.FpOpsTickets) []map[string]interface{} {
	categories := []string{
		"SOC-游戏模块库",
		"SOC-非游戏模块库",
		"无效单",
		"特殊问题标签库",
	}
	counts := make(map[string]int, len(categories))
	for _, cat := range categories {
		counts[cat] = 0
	}

	// 2. 遍历 tickets 累加计数
	for _, ticket := range tickets {
		lib := ticket.AiTag.LibName
		for _, cat := range categories {
			if strings.Contains(lib, cat) {
				counts[cat]++
				break
			}
		}
	}

	// 3. 组织饼图数据
	pieValues := make([]map[string]interface{}, 0, len(categories))
	for _, cat := range categories {
		if counts[cat] == 0 {
			continue
		}
		percent := utils.DivInt(counts[cat], len(tickets))
		pieValues = append(pieValues, map[string]interface{}{
			"type":  cat,
			"value": percent,
		})
	}

	// 4. 构造 chart 元素并 append
	pieChart := map[string]interface{}{

		"tag":          "chart",
		"aspect_ratio": "4:3",
		"chart_spec": map[string]interface{}{
			"type": "pie",
			"title": map[string]interface{}{
				"text": "标签库分布占比图",
			},
			"data": map[string]interface{}{
				"values": pieValues,
			},
			"valueField":    "value",
			"categoryField": "type",
			"outerRadius":   0.9,
			// 如果需要环图效果可以加 innerRadius
			"innerRadius": 0.3,
			"legends": map[string]interface{}{
				"visible": true,
				"orient":  "right",
			},
			"padding": map[string]interface{}{
				"left":   10,
				"top":    10,
				"bottom": 5,
				"right":  0,
			},
			"label": map[string]interface{}{
				"visible": true,
			},
		},
	}
	elements = append(elements, pieChart)
	return elements
}

// GenTicketTagTypePie 绘制客询客诉建议分布图，并返回更新后的 elements 列表
func GenTicketTagTypePie(ctx context.Context, elements []map[string]interface{}, tickets []*models.FpOpsTickets) []map[string]interface{} {
	tagTypes := []string{
		"-",
		"客询类工单",
		"客诉类工单",
		"建议类工单",
	}
	counts := make(map[string]int, len(tagTypes))
	for _, cat := range tagTypes {
		counts[cat] = 0
	}

	// 2. 遍历 tickets 累加计数
	for _, ticket := range tickets {
		for i, tagType := range tagTypes {
			if i == 0 {
				continue
			}
			if ticket.AiTag.TagType == uint8(i) {
				counts[tagType]++
				break
			}
		}
	}

	// 3. 组织饼图数据
	pieValues := make([]map[string]interface{}, 0, len(tagTypes))
	for _, tagType := range tagTypes {
		if counts[tagType] == 0 {
			continue
		}
		percent := utils.DivInt(counts[tagType], counts["客询类工单"]+counts["客诉类工单"]+counts["建议类工单"])
		pieValues = append(pieValues, map[string]interface{}{
			"type":  tagType,
			"value": percent,
		})
	}

	// 4. 构造 chart 元素并 append
	pieChart := map[string]interface{}{

		"tag":          "chart",
		"aspect_ratio": "4:3",
		"chart_spec": map[string]interface{}{
			"type": "pie",
			"title": map[string]interface{}{
				"text": "建议&客诉&客询分布占比图",
			},
			"data": map[string]interface{}{
				"values": pieValues,
			},
			"valueField":    "value",
			"categoryField": "type",
			"outerRadius":   0.9,
			// 如果需要环图效果可以加 innerRadius
			"innerRadius": 0.3,
			"legends": map[string]interface{}{
				"visible": true,
				"orient":  "right",
			},
			"padding": map[string]interface{}{
				"left":   10,
				"top":    10,
				"bottom": 5,
				"right":  0,
			},
			"label": map[string]interface{}{
				"visible": true,
			},
		},
	}
	elements = append(elements, pieChart)
	return elements
}

// GenTopTicketsCountBar 生成“一级标签分类状况图”柱状图，并返回更新后的 elements 列表
func GenTopTicketsCountBar(ctx context.Context, elements []map[string]interface{}, tickets []*models.FpOpsTickets) ([]map[string]interface{}, []map[string]interface{}) {
	ticketFirstMap := make(map[string]int)
	// 遍历工单
	for _, ticket := range tickets {
		ticketFirstMap[ticket.AiTag.FirstTag]++
	}
	// 2. 转 slice
	ticketFirstTagCountMap := []map[string]interface{}{}
	for firstTag, cnt := range ticketFirstMap {
		ticketFirstTagCountMap = append(ticketFirstTagCountMap, map[string]interface{}{
			"分类": firstTag,
			"数值": cnt,
		})
	}

	// 3. 按“数值”从大到小排序
	sort.Slice(ticketFirstTagCountMap, func(i, j int) bool {
		return ticketFirstTagCountMap[i]["数值"].(int) > ticketFirstTagCountMap[j]["数值"].(int)
	})
	elements = append(elements, map[string]interface{}{
		// 工单量
		"tag": "chart",
		"chart_spec": map[string]interface{}{
			"type": "bar",
			"title": map[string]interface{}{
				"text": "一级标签分类状况图",
			},
			"data": map[string]interface{}{
				"values": ticketFirstTagCountMap,
			},
			"xField": "分类",
			"yField": "数值",
		},
	})
	return elements, ticketFirstTagCountMap
}

// GenTopTicketsCountAskBar 生成“一级标签分类客询类状况图”柱状图，并返回更新后的 elements 列表
func GenTopTicketsCountAskBar(ctx context.Context, elements []map[string]interface{}, tickets []*models.FpOpsTickets) []map[string]interface{} {
	ticketFirstMap := make(map[string]int)
	// 遍历工单
	for _, ticket := range tickets {
		if ticket.AiTag.TagType != 1 {
			continue
		}
		ticketFirstMap[ticket.AiTag.FirstTag]++
	}
	// 2. 转 slice
	ticketFirstTagCountMap := make([]map[string]interface{}, 0, len(ticketFirstMap))
	for firstTag, cnt := range ticketFirstMap {
		ticketFirstTagCountMap = append(ticketFirstTagCountMap, map[string]interface{}{
			"分类": firstTag,
			"数值": cnt,
		})
	}

	// 3. 按“数值”从大到小排序
	sort.Slice(ticketFirstTagCountMap, func(i, j int) bool {
		return ticketFirstTagCountMap[i]["数值"].(int) > ticketFirstTagCountMap[j]["数值"].(int)
	})
	elements = append(elements, map[string]interface{}{
		// 工单量
		"tag": "chart",
		"chart_spec": map[string]interface{}{
			"type": "bar",
			"title": map[string]interface{}{
				"text": "客询类工单",
			},
			"data": map[string]interface{}{
				"values": ticketFirstTagCountMap,
			},
			"xField": "分类",
			"yField": "数值",
		},
	})
	return elements
}

// GenTopTicketsCountComplainBar 生成“一级标签分类客诉类状况图”柱状图，并返回更新后的 elements 列表
func GenTopTicketsCountComplainBar(ctx context.Context, elements []map[string]interface{}, tickets []*models.FpOpsTickets) []map[string]interface{} {
	ticketFirstMap := make(map[string]int)
	// 遍历工单
	for _, ticket := range tickets {
		if ticket.AiTag.TagType != 2 {
			continue
		}
		ticketFirstMap[ticket.AiTag.FirstTag]++
	}
	// 2. 转 slice
	ticketFirstTagCountMap := make([]map[string]interface{}, 0, len(ticketFirstMap))
	for firstTag, cnt := range ticketFirstMap {
		ticketFirstTagCountMap = append(ticketFirstTagCountMap, map[string]interface{}{
			"分类": firstTag,
			"数值": cnt,
		})
	}

	// 3. 按“数值”从大到小排序
	sort.Slice(ticketFirstTagCountMap, func(i, j int) bool {
		return ticketFirstTagCountMap[i]["数值"].(int) > ticketFirstTagCountMap[j]["数值"].(int)
	})
	elements = append(elements, map[string]interface{}{
		// 工单量
		"tag": "chart",
		"chart_spec": map[string]interface{}{
			"type": "bar",
			"title": map[string]interface{}{
				"text": "客诉类工单",
			},
			"data": map[string]interface{}{
				"values": ticketFirstTagCountMap,
			},
			"xField": "分类",
			"yField": "数值",
		},
	})
	return elements
}

// GenSecondTicketsCountBar 生成“二级标签”柱状图，并返回更新后的 elements 列表
func GenSecondTicketsCountBar(ctx context.Context, firstTag string, tickets []*models.FpOpsTickets, elements []map[string]interface{}) []map[string]interface{} {
	ticketSecondMap := make(map[string]int)
	// 遍历工单
	for _, ticket := range tickets {
		if ticket.AiTag.FirstTag == firstTag {
			if ticket.AiTag.SecondTag != "" {
				ticketSecondMap[ticket.AiTag.SecondTag]++
			}
		}
	}
	if len(ticketSecondMap) == 0 {
		return elements
	}
	// 2. 转 slice
	ticketSecondTagCountMap := make([]map[string]interface{}, 0, len(ticketSecondMap))
	for secondTag, cnt := range ticketSecondMap {
		ticketSecondTagCountMap = append(ticketSecondTagCountMap, map[string]interface{}{
			"分类": secondTag,
			"数值": cnt,
		})
	}

	// 3. 按“数值”从大到小排序
	sort.Slice(ticketSecondTagCountMap, func(i, j int) bool {
		return ticketSecondTagCountMap[i]["数值"].(int) > ticketSecondTagCountMap[j]["数值"].(int)
	})
	elements = append(elements, map[string]interface{}{
		// 工单量
		"tag": "chart",
		"chart_spec": map[string]interface{}{
			"type": "bar",
			"title": map[string]interface{}{
				"text": firstTag,
			},
			"data": map[string]interface{}{
				"values": ticketSecondTagCountMap,
			},
			"xField": "分类",
			"yField": "数值",
		},
	})
	return elements
}

// GenSpecialTicketsCountTable 生成“特殊标签”表格，并返回更新后的 elements 列表
func GenSpecialTicketsCountTable(ctx context.Context, tickets []*models.FpOpsTickets, elements []map[string]interface{}) ([]map[string]interface{}, error) {
	specialCountMap := make(map[string]int)
	// 查询特殊标签
	for _, ticket := range tickets {
		if !strings.Contains(ticket.AiTag.LibName, "特殊问题标签库") {
			continue
		}
		if ticket.Vip > 0 {
			continue
		}
		specialCountMap[ticket.AiTag.SecondTag]++

	}

	tableRows := make([]map[string]interface{}, 0)
	// 查询每个特殊标签第一次打的时间
	for secondTag, cnt := range specialCountMap {
		firstTimeUnix, err := persistence.NewTags().GetSpecialTagFirstTime(ctx, secondTag)
		if err != nil {
			logger.Errorf(ctx, "GetSpecialTagFirstTime error:%v", err)
			continue
		}
		firstTime := time.Unix(int64(firstTimeUnix), 0).Format("2006-01-02")
		tableRows = append(tableRows, map[string]interface{}{
			"first_record_time": firstTime,
			"issue":             secondTag,
			"no_vip_cnt":        cnt,
		})

	}

	elements = append(elements, map[string]interface{}{
		"tag":       "table",
		"page_size": 15,
		"columns": []map[string]interface{}{
			{ // 添加列，列的数据类型为不带格式的普通文本。
				"name":         "first_record_time", // 自定义列的标记。必填。用于唯一指定行数据对象数组中，需要将数据填充至这一行的具体哪个单元格中。
				"display_name": "首次记录时间",            // 列名称。为空时不展示列名称。
				"width":        "auto",
			},
			{
				"name":         "issue",
				"display_name": "问题描述",
				"width":        "auto",
			},
			{
				"name":         "no_vip_cnt",
				"display_name": "普客客诉数量",
				"width":        "auto",
			},
		},
		"rows": tableRows,
	})
	return elements, nil
}

func GenSuggestionTicketsTable(ctx context.Context, tickets []*models.FpOpsTickets) error {
	// 先把列定义抽出来，重用
	columns := []map[string]interface{}{
		{
			"name":         "date",
			"display_name": "日期",
			"width":        "auto",
		},
		{
			"name":         "acceptor",
			"display_name": "登记人",
			"width":        "auto",
		},
		{
			"name":         "from",
			"display_name": "反馈渠道",
			"width":        "auto",
		},
		{
			"name":         "uid",
			"display_name": "玩家UID",
			"width":        "auto",
		},
		{
			"name":         "recharge",
			"display_name": "付费金额",
			"width":        "auto",
		},
		{
			"name":         "issue_trans",
			"display_name": "简述问题内容",
			"width":        "auto",
		},
		{
			"name":         "issue",
			"display_name": "玩家原文",
			"width":        "auto",
		},
	}

	batchSize := 200
	batchRows := make([]map[string]interface{}, 0, batchSize)

	for _, ticket := range tickets {
		if ticket.Vip > 0 || ticket.CatID != 1077 {
			continue
		}
		issueDesc, ok := ticket_llm.HasTicketDesc(ticket)
		if !ok {
			continue
		}
		issueSummary, err := GetIssueSummary(ctx, issueDesc)
		if err != nil {
			logger.Errorf(ctx, "GetIssueSummary error: %v", err)
			continue
		}

		// append 一行
		batchRows = append(batchRows, map[string]interface{}{
			"date":        time.Unix(int64(ticket.CreatedAt), 0).Format("2006-01-02"),
			"acceptor":    "系统",
			"from":        "工单",
			"uid":         ticket.UID,
			"recharge":    ticket.Recharge,
			"issue_trans": issueSummary,
			"issue":       issueDesc,
		})

		// 达到 batchSize，就发一次消息
		if len(batchRows) >= batchSize {
			elements := []map[string]interface{}{
				{
					"tag":       "table",
					"page_size": 10,
					"columns":   columns,
					"rows":      batchRows,
				},
			}
			if err := sendFeishuMessage(elements); err != nil {
				return err
			}
			// 清空，准备下一批
			batchRows = batchRows[:0]
		}
	}

	// 循环结束，最后不足 batchSize 的也发一次
	if len(batchRows) > 0 {
		elements := []map[string]interface{}{
			{
				"tag":       "table",
				"page_size": 10,
				"columns":   columns,
				"rows":      batchRows,
			},
		}
		if err := sendFeishuMessage(elements); err != nil {
			return err
		}
	}

	return nil
}

func GetIssueSummary(ctx context.Context, issue string) (string, error) {
	prompt := fmt.Sprintf("请将以下玩家问题用一句话进行总结，并翻译成中文。\n\n"+
		"要求不要带有原文，直接给出翻译即可。\n玩家问题为:%s", issue)

	req := openai.ChatCompletionRequest{
		Model:       "gpt-4o",
		Temperature: 0,
		Messages: []openai.ChatCompletionMessage{
			{
				Role:    "system",
				Content: "你是一个专业的客服助理，擅长提炼要点并翻译。",
			},
			{
				Role:    "user",
				Content: prompt,
			},
		},
		Stop: []string{"\n"},
	}
	client := ai_model.EngineTag(ai_model.SmartModel)
	//client := ai_model.Engine(ai_model.SmartModel)
	var resp openai.ChatCompletionResponse
	err := retry.Do(func() error {
		var err error
		resp, err = client.CreateChatCompletion(ctx, req)
		return err
	}, retry.Attempts(3), retry.Delay(2*time.Second), retry.LastErrorOnly(true))
	if err != nil {
		return "", fmt.Errorf("AI 摘要失败：%w", err)
	}
	if len(resp.Choices) == 0 {
		return "", fmt.Errorf("AI 未返回任何内容")
	}
	return resp.Choices[0].Message.Content, nil
}
