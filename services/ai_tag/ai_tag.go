package ai_tag

import (
	"bytes"
	"context"
	"embed"
	"encoding/json"
	"fmt"
	"github.com/avast/retry-go"
	"github.com/darcyx/go-openai"
	"github.com/labstack/gommon/log"
	"github.com/spf13/cast"
	"github.com/xuri/excelize/v2"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	ai_model "ops-ticket-api/internal/ai"
	"ops-ticket-api/internal/persistence"
	"ops-ticket-api/models"
	"ops-ticket-api/proto/pb"
	"ops-ticket-api/services/ticket_llm"
	"strings"
	"time"
)

type Example struct {
	Category string
	Dialogue string
	Label    string
	Issue    string
}

const (
	NotGameLibId = 14
	GameLibId    = 15
	InvalidTag   = "SOC-无效单库-无效单"
)

//go:embed data/*.xlsx
var marchXLSX []byte

//go:embed data/*.txt
var docsFS embed.FS

func AiTagHandle(ctx context.Context, ticketId uint64) error {
	// 记录开始时间
	startTime := time.Now()
	logger.Infof(ctx, "ticket %d start to tag, startTime: %v", ticketId, startTime.Format("2006-01-02 15:04:05"))
	tagType := "AI"
	fun := "AiTagHandle"
	// 1.check AI是否已打标，直接保存DB返回
	exists, err := persistence.NewAITag().CheckHasLabel(ctx, ticketId)
	if err != nil {
		logger.Errorf(context.TODO(), "%s : check ticket %d has label err:%v", fun, ticketId, err)
		return err
	}
	if exists {
		logger.Errorf(ctx, "%s : ticket %d has label", fun, ticketId)
		return nil
	}
	// 2.check是否为特殊标签，直接同步
	tagId, err := persistence.NewTicket().GetSpecialTicketTag(ctx, ticketId)
	if err != nil {
		logger.Errorf(ctx, "%s : get ticket %d tagIds err:%v", fun, ticketId, err)
		return err
	}
	if tagId != 0 {
		tagType = "origin"
		// 组装数据，save aiTag db
		lib, first, second, third, all, err := persistence.NewTags().GetTagLibNameByTagId(ctx, tagId)
		if err != nil {
			logger.Errorf(ctx, "%s : get ticket %d tagIds err:%v", fun, ticketId, err)
			return err
		}
		now := uint64(time.Now().Unix())
		aiTagInfo := &models.FpOpsTicketsAiTag{
			TicketID:  ticketId,
			LibName:   lib,
			FirstTag:  first,
			SecondTag: second,
			ThirdTag:  third,
			CreatedAt: now,
			UpdatedAt: now,
		}
		last := all[len(all)-1]
		switch {
		case strings.Contains(last, "客询"):
			aiTagInfo.TagType = uint8(pb.TicketAiTagType_TicketAiTagTypeAsk)
		case strings.Contains(last, "客诉"):
			aiTagInfo.TagType = uint8(pb.TicketAiTagType_TicketAiTagTypeComplaint)
		case strings.Contains(last, "建议"):
			aiTagInfo.TagType = uint8(pb.TicketAiTagType_TicketAiTagTypeSuggestion)
		default:
			aiTagInfo.TagType = uint8(pb.TicketAiTagType_TicketAiTagTypeElse)
		}
		err = persistence.NewAITag().SaveAIPortrait(ctx, aiTagInfo)
		if err != nil {
			logger.Errorf(ctx, "%s : save ticket %d ai tag err:%v", fun, ticketId, err)
			return err
		}

	} else {
		// 3.Ai打标
		// 调用 AI 打标并保存DB
		err = AiTagLLm(ctx, ticketId)
		if err != nil {
			logger.Errorf(ctx, "%s : ai tag ticket %d err:%v", fun, ticketId, err)
			return err
		}
	}
	endTime := time.Now()
	duration := endTime.Sub(startTime)
	logger.Infof(ctx, "type %s ticket %d tag success. startTime: %v,endTime: %v,spentTime: %v",
		tagType,
		ticketId,
		startTime.Format("2006-01-02 15:04:05"),
		endTime.Format("2006-01-02 15:04:05"),
		duration)
	return nil
}

// AiTagLLm 依次对 8 类分别调用 classifyWithDoc
func AiTagLLm(ctx context.Context, ticketId uint64) error {
	// 最终打标结果
	var finalTag string
	topTags := []string{
		"养成线",
		"商业化",
		"个人玩法",
		"帮会",
		"大地图",
		"赛季",
		"社交",
		"杂项",
	}

	// 获取tkInfo
	ticket, err := persistence.NewTicket().GetTicketInfoFieldsFromMaster(ctx, ticketId)
	if err != nil {
		logger.Errorf(ctx, "failed to get ticket info: %v")
		return err
	}

	// 组装对话 JSON
	commus, _ := persistence.NewTicket().GetTicketCommuns(ctx, ticketId)
	var dialogue []map[string]string
	var role string
	for _, commu := range commus {
		if commu.FromRole == 2 {
			role = "assistant"
		} else {
			role = "user"
		}
		if commu.Detail == "" {
			continue
		}
		dialogue = append(dialogue, map[string]string{role: commu.Detail})
	}
	dialogueJSON, _ := json.Marshal(dialogue)

	// 问题分类
	catDesc, err := persistence.NewCat().CatNameBackendSlice(ctx, ticket.CatID)
	if err != nil {
		logger.Errorf(ctx, "TicketUserInfo get cateShow return err. catId:%d. err:%v", ticket.CatID, err)
		return err
	}

	// 0.获取问题描述
	skipMeaningLess := false
	issueDesc, flag := ticket_llm.HasTicketDesc(ticket)
	if !flag {
		issueDesc = ""
		skipMeaningLess = true
	}
	// 1.若为无效单则打完直接返回
	if !skipMeaningLess {
		if ticket_llm.CheckMeaninglessQuery(ctx, issueDesc) {
			finalTag = InvalidTag
			// save DB
			err := SaveAIPortrait(ctx, ticketId, finalTag)
			if err != nil {
				logger.Errorf(ctx, "failed to save ai tag: %v", err)
				return err
			}
			return nil
		}
	}

	// 2.llm判断是否为游戏模块库
	isGame := IsIncludeByGameSorts(ctx, catDesc[0], issueDesc, string(dialogueJSON), "data/extra.txt")

	if isGame {
		// 依次分类
		var interimTags []string
		for _, topTag := range topTags {
			allTags, err := persistence.NewTags().GetTagPathsByTopCategories(ctx, GameLibId, topTag)
			if err != nil {
				logger.Errorf(ctx, "failed to get tags: %v", err)
				return err
			}
			tag := classifyWithDoc(ctx, catDesc[0], issueDesc, string(dialogueJSON), strings.Join(allTags, ","), topTag)
			interimTags = append(interimTags, tag)
		}

		// 最后一次汇总
		finalTag = selectBestTag(ctx, interimTags, catDesc[0], issueDesc, string(dialogueJSON), "data/extra.txt")

	} else {
		// 非游戏模块llm打标
		allTags, err := persistence.NewTags().GetAllTagPathsByLibID(ctx, NotGameLibId)
		if err != nil {
			logger.Errorf(ctx, "failed to get tags: %v", err)
			return err
		}
		finalTag = aiPortrait(ctx, catDesc[0], issueDesc, string(dialogueJSON), strings.Join(allTags, ","), "data/normal.txt")
	}

	// save db
	err = SaveAIPortrait(ctx, ticketId, finalTag)
	if err != nil {
		logger.Errorf(ctx, "failed to save ai tag: %v", err)
		return err
	}
	return nil
}

func GetMarchInfoByFirst() string {
	examples := []*Example{}
	// 打开 Excel 文件
	f, err := excelize.OpenReader(bytes.NewReader(marchXLSX))
	if err != nil {
		log.Printf("打开 embedded Excel 失败: %v", err)
		return ""
	}
	defer func() {
		if cerr := f.Close(); cerr != nil {
			log.Printf("关闭 Excel 失败: %v", cerr)
		}
	}()

	// 获取第一个工作表的名字
	sheetName := f.GetSheetName(0)
	if sheetName == "" {
		log.Printf("无法读取第一个工作表名")
		return ""
	}

	// 获取所有行
	rows, err := f.GetRows(sheetName)
	if err != nil {
		log.Printf("读取行失败: %v", err)
	}
	cnt := 0
	for i, row := range rows {
		// 跳过表头
		if i == 0 {
			continue
		}
		if cnt > 50 {
			break
		}
		if len(row) > 10 {
			if strings.Contains(row[10], "非游戏模块库") || strings.Contains(row[10], "无效单") {
				continue
			}
		}

		cnt++
		example := &Example{}
		if len(row) > 10 {
			example.Label = row[10]
		}
		if len(row) > 13 {
			example.Category = row[13]
		}
		if len(row) > 14 {
			example.Issue = row[14]
		}
		// 直接拿原始字符串
		if len(row) > 31 {
			example.Dialogue = row[31]
		}

		examples = append(examples, example)
	}
	var sb strings.Builder
	sb.WriteString("下面是工单的一些人工标注示例，请参考：\n\n")

	for _, ex := range examples {
		sb.WriteString(fmt.Sprintf("例子：\n"))
		sb.WriteString(fmt.Sprintf("- 玩家选择的问题分类: \"%s\"\n", ex.Category))
		sb.WriteString(fmt.Sprintf("- 玩家的问题描述: \"%s\"\n", ex.Issue))
		sb.WriteString(fmt.Sprintf("- 玩家与客服对话内容: \"%s\"\n", ex.Dialogue))
		sb.WriteString(fmt.Sprintf("- 人工标签: \"%s\"\n", ex.Label))
	}

	return sb.String()
}

func GetMarchInfoBySecond(topTag string) string {
	examples := []*Example{}

	// 打开 Excel 文件
	f, err := excelize.OpenReader(bytes.NewReader(marchXLSX))
	if err != nil {
		log.Printf("打开 embedded Excel 失败: %v", err)
		return ""
	}
	defer func() {
		if cerr := f.Close(); cerr != nil {
			log.Printf("关闭 Excel 失败: %v", cerr)
		}
	}()

	// 获取第一个工作表的名字
	sheetList := f.GetSheetList()
	sheetName := sheetList[0]
	tagName := fmt.Sprintf("SOC-游戏模块库-%s", topTag)

	// 获取所有行
	rows, err := f.GetRows(sheetName)
	if err != nil {
		log.Printf("读取行失败: %v", err)
	}
	cnt := 0
	for i, row := range rows {
		// 跳过表头
		if i == 0 {
			continue
		}
		if cnt > 50 {
			break
		}
		if len(row) > 10 {
			if !strings.Contains(cast.ToString(row[10]), tagName) {
				continue
			}
		}

		cnt++
		example := &Example{}
		if len(row) > 10 {
			example.Label = row[10]
		}
		if len(row) > 13 {
			example.Category = row[13]
		}
		if len(row) > 14 {
			example.Issue = row[14]
		}
		if len(row) > 31 {
			example.Dialogue = row[31]
		}
		examples = append(examples, example)
	}
	var sb strings.Builder
	sb.WriteString("下面是工单的一些人工标注示例，请参考：\n\n")

	for _, ex := range examples {
		sb.WriteString(fmt.Sprintf("例子：\n"))
		sb.WriteString(fmt.Sprintf("- 玩家选择的问题分类: \"%s\"\n", ex.Category))
		sb.WriteString(fmt.Sprintf("- 玩家的问题描述: \"%s\"\n", ex.Issue))
		sb.WriteString(fmt.Sprintf("- 玩家与客服对话内容: \"%s\"\n", ex.Dialogue))
		sb.WriteString(fmt.Sprintf("- 人工标签: \"%s\"\n", ex.Label))
	}

	return sb.String()
}

func classifyWithDoc(ctx context.Context, catDesc, issueDesc, dialogJson, allTags, topTag string) string {
	// 读取对应文档
	doc := "无"
	path := fmt.Sprintf("data/%s.txt", topTag)
	if b, err := docsFS.ReadFile(path); err == nil {
		doc = string(b)
	}

	examples := GetMarchInfoBySecond(topTag)

	// 构建 Prompt
	prompt := fmt.Sprintf(`
你是一个资深的多语言游戏客服分类助手，任务是根据用户选择的"问题分类"、"问题描述"、"对话内容"信息进行推演和分析，给工单进行分类打标签。一些游戏内的资料给你提供在了玩法资料当中，请严谨地根据玩法资料进行标签选择：

%s

【玩法资料】:
%s

【候选标签】:
%s

【工单信息】
- 玩家选择的问题分类: "%s"
- 玩家问题描述: "%s"
- 玩家与客服对话内容: "%s"

要求：
- 文件的内容仔细阅读后推理出最合适的标签;
- 只从候选标签中选一个，找不到最合适的则返回"unknown"。
- 划分的类别只能从"候选的标签"选择，不能有任何编造的的类别;
- 输出标签名称，不要其它内容。
- 需要仔细分析和推断，当用户选择的"问题分类"和其他"问题描述"或者"对话内容"不一致时，请以"问题描述"或者"对话内容"为判断依据;
- 关于"客诉"和"客询"的区分标准: 如果玩家明显有抱怨情绪、提出要投诉的就是"客诉"；剩余的是"客询";
- 有关与他人对战中战力或等级差距的问题，相关pvp内容应该是“SOC-游戏模块库-大地图-不属任何模块的大地图PVP”这个模块下的;
`, examples, doc, allTags, catDesc, issueDesc, dialogJson)

	// 调用 LLM
	req := openai.ChatCompletionRequest{
		Model:       "gpt-4.1",
		Temperature: 0,
		Messages: []openai.ChatCompletionMessage{
			{Role: "system", Content: "你是资深游戏客服分类助手。"},
			{Role: "user", Content: prompt},
		},
		Stop: []string{"\n"},
	}
	client := ai_model.EngineTag(openai.GPT4)
	var resp openai.ChatCompletionResponse
	if err := retry.Do(func() error {
		var er error
		resp, er = client.CreateChatCompletion(ctx, req)
		return er
	}, retry.Attempts(3), retry.Delay(2*time.Second)); err != nil || len(resp.Choices) == 0 {
		logger.Infof(ctx, "分类失败：%v", err)
		return "unknown"
	}

	tag := strings.Trim(resp.Choices[0].Message.Content, `" `)
	return tag
}

// 1. 新增：从候选标签里选最合适的
func selectBestTag(ctx context.Context, candidates []string, catDesc, issueDesc, dialogJson, docPath string) string {
	// 读取全局文档
	var doc string
	if b, err := docsFS.ReadFile(docPath); err == nil {
		doc = string(b)
	} else {
		logger.Errorf(ctx, "未能读取 embedded 文档 %s: %v", docPath, err)
	}

	// 构造编号列表
	var opts strings.Builder
	opts.WriteString("以下是八个候选标签，请从中选出最合适的一个返回名称（不要引号）：\n")
	for i, t := range candidates {
		opts.WriteString(fmt.Sprintf("%d. %s\n", i+1, t))
	}

	prompt := fmt.Sprintf(`
请严谨地根据玩法资料进行标签选择。从下面候选标签中选出最合适的一个并仅返回标签名称，不要其他内容。

【候选标签】:
%s

【玩法资料】:
%s

【工单信息】
- 玩家选择的问题分类: "%s"
- 玩家问题描述: "%s"
- 玩家与客服对话内容: "%s"
要求：
- 文件的内容仔细阅读后推理出最合适的标签;
- 只从候选标签中选一个，不能有任何编造的的类别;
- 输出标签名称，不要其它内容。
- 需要仔细分析和推断，当用户选择的"问题分类"和其他"问题描述"或者"对话内容"不一致时，请以"问题描述"或者"对话内容"为判断依据;
`, opts.String(), doc, catDesc, issueDesc, dialogJson)

	req := openai.ChatCompletionRequest{
		Model:       "gpt-4.1",
		Temperature: 0,
		Messages: []openai.ChatCompletionMessage{
			{Role: "system", Content: "你是资深游戏客服分类助手。"},
			{Role: "user", Content: prompt},
		},
		Stop: []string{"\n"},
	}
	client := ai_model.EngineTag(openai.GPT4)
	var resp openai.ChatCompletionResponse
	if err := retry.Do(func() error {
		var er error
		resp, er = client.CreateChatCompletion(ctx, req)
		return er
	}, retry.Attempts(3), retry.Delay(2*time.Second)); err != nil || len(resp.Choices) == 0 {
		logger.Infof(ctx, "selectBestTag->retry err:%v", err)
		return "unknown"
	}

	tag := strings.Trim(resp.Choices[0].Message.Content, `" `)
	return tag
}

// IsIncludeByGameSorts 判断工单分类是否属于游戏模块库
func IsIncludeByGameSorts(ctx context.Context, catDesc, issueDesc, dialogJson, docPath string) bool {
	// 构造五大分类及其标签说明
	var sb strings.Builder
	// 五大一级分类
	topCategoriesName := []string{"SOC-游戏模块库-养成线", "SOC-游戏模块库-大地图", "SOC-游戏模块库-个人玩法", "SOC-游戏模块库-帮会", "SOC-游戏模块库-赛季", "SOC-游戏模块库-社交", "SOC-游戏模块库-商业化", "SOC-游戏模块库-杂项"}
	sb.WriteString("下面是游戏的八大一级分类及其对应的所有标签：\n")

	for i, cat := range topCategoriesName {
		sli := strings.Split(cat, "-")
		topTag := sli[2]
		categories, _ := persistence.NewTags().GetTagPathsByTopCategories(ctx, GameLibId, topTag)
		tagStr := strings.Join(categories, ",")
		sb.WriteString(fmt.Sprintf("第%d类：%v\n", i+1, topCategoriesName[i]))
		sb.WriteString("该类的候选标签为:\n")
		sb.WriteString(tagStr)
		sb.WriteString("\n")
	}
	sb.WriteString("\n")

	var doc string
	if b, err := docsFS.ReadFile(docPath); err == nil {
		doc = string(b)
	} else {
		logger.Errorf(ctx, "未能读取 embedded 文档 %s: %v", docPath, err)
	}
	examples := GetMarchInfoByFirst()

	prompt := sb.String() + fmt.Sprintf(`请根据我提供给你的八大分类下面的标签、"工单信息"以及给你提供的玩法资料判断当前工单是否属于这八个一级分类中。我还在给你提供了一些打标签的"参考例子"便于你去分类。
仅返回 "true"（是）或 "false"（否），不要其他内容。\n
有关与他人对战中战力或等级差距的问题，相关pvp内容应该是“SOC-游戏模块库-大地图-不属任何模块的大地图PVP”这个模块下的，所以应该判属于；

%s

【玩法资料】:
%s

【工单信息】
- 玩家选择的问题分类: "%s"
- 玩家问题描述: "%s"
- 玩家与客服对话内容: "%s"
`, examples, doc, catDesc, issueDesc, dialogJson)
	req := openai.ChatCompletionRequest{
		Model:       "gpt-4.1",
		Temperature: 0,
		Messages: []openai.ChatCompletionMessage{
			{Role: "system", Content: "你是资深游戏客服分类助手。"},
			{Role: "user", Content: prompt},
		},
		Stop: []string{"\n"},
	}
	//todo 换token
	client := ai_model.EngineTag(openai.GPT4)
	var resp openai.ChatCompletionResponse
	if err := retry.Do(func() error {
		var er error
		resp, er = client.CreateChatCompletion(ctx, req)
		return er
	}, retry.Attempts(3), retry.Delay(2*time.Second)); err != nil || len(resp.Choices) == 0 {
		logger.Errorf(ctx, "IsIncludeBySixSorts->retry err:%v", err)
		return false
	}

	// 只取第一个回答，并标准化
	reply := strings.TrimSpace(strings.ToLower(resp.Choices[0].Message.Content))
	logger.Info(ctx, "IsIncludeBySixSorts->reply", logger.Any("reply", reply))
	return reply == "true"
}

// 通用版打标llm
func aiPortrait(ctx context.Context, catDesc, issueDesc, dialogJson, allTags, path string) string {
	var extraContent string
	if fileBytes, err := docsFS.ReadFile(path); err == nil {
		extraContent = string(fileBytes)
	} else {
		logger.Errorf(ctx, "未能读取 embedded 文档 %s: %v", path, err)
	}

	prompt := fmt.Sprintf(`
你是一个资深的多语言游戏客服分类助手，任务是根据用户选择的"问题分类"、"问题描述"、"对话内容"信息进行推演和分析，从候选的标签中给工单进行分类打标签。一些游戏内的资料给你提供在了附加文件内容当中，请仔细阅读。

【候选的标签】:
%s

【附加文件内容】:
  %s
【信息】
- 玩家选择的问题分类: "%s"
- 玩家问题描述: "%s"
- 玩家与客服对话内容: "%s"

【要求】
- 玩家信息以及文件的内容仔细阅读后推理出最合适的标签;
- 只从候选标签中选一个，找不到最合适的则返回"unknown"。
- 划分的类别只能从"候选的标签"选择，不能有任何编造的的类别;
- 输出标签名称，不要其它内容。
- 需要仔细分析和推断，当用户选择的"问题分类"和其他"问题描述"或者"对话内容"不一致时，请以"问题描述"或者"对话内容"为判断依据;
- 关于"客诉"和"客询"的区分标准: 如果玩家明显有抱怨情绪、提出要投诉的就是"客诉"；剩余的是"客询";
- 关于"无法登录"和"网络不畅"的区分标准: 无法进入游戏或无法连接游戏的情况下打"SOC-非游戏模块库-游戏全局问题-无法登录"，可以登录进入游戏但卡顿打"SOC-非游戏模块库-游戏全局问题-网络不畅"标签；
- 如果是游戏断开、黑屏或者踢出游戏的场景打"SOC-非游戏模块库-游戏全局问题-闪退"；
- 如果账号被他人访问了有相关的安全问题，一定是"SOC-非游戏模块库-账号-被盗"标签;
- 有关充值的问题，若是你的东西没有收到或是没有发下来，则应该是"SOC-非游戏模块库-充值-重复扣款/未到账"标签;
请根据以上信息对上述工单数据进行分类。并按照约定的格式输出`, allTags, extraContent, catDesc, issueDesc, dialogJson)

	req := openai.ChatCompletionRequest{
		Model:       "gpt-4.1",
		Temperature: 0,
		Messages: []openai.ChatCompletionMessage{
			{
				Role:    "system",
				Content: "你是一个专注于游戏客服问题归类的智能助手，能够准确给工单打标签。",
			},
			{
				Role:    "user",
				Content: prompt,
			},
		},
		Stop: []string{"\n"},
	}

	client := ai_model.EngineTag(openai.GPT4)
	//client := ai_model.Engine(ai_model.SmartModel)
	var resp openai.ChatCompletionResponse
	err := retry.Do(func() error {
		var err error
		resp, err = client.CreateChatCompletion(ctx, req)
		return err
	}, retry.Attempts(3), retry.Delay(2*time.Second), retry.LastErrorOnly(true))
	if err != nil || len(resp.Choices) == 0 {
		logger.Errorf(ctx, "aiPortrait->retry err. issueDesc:%s. category:%s. err:%v", issueDesc, catDesc, err)
		return "unknown"
	}

	reply := resp.Choices[0].Message.Content
	reply = strings.Replace(reply, `"`, "", -1)
	fmt.Println(reply)
	//fmt.Println("input token：", resp.Usage.PromptTokens)
	return reply
}

func SaveAIPortrait(ctx context.Context, ticketId uint64, label string) error {
	now := uint64(time.Now().Unix())
	aiTagInfo := &models.FpOpsTicketsAiTag{
		TicketID:  ticketId,
		CreatedAt: now,
		UpdatedAt: now,
	}
	// AI打标失败
	if strings.Contains(strings.ToLower(label), "unknown") {
		aiTagInfo.TagType = uint8(pb.TicketAiTagType_TicketAiTagNotTag)
		return persistence.NewAITag().SaveAIPortrait(ctx, aiTagInfo)
	}

	// 获取type
	labelSli := strings.Split(label, "-")
	if len(labelSli) < 3 {
		return fmt.Errorf("invalid label format: %s", label)
	}
	aiTagInfo.LibName = fmt.Sprintf("%s-%s", labelSli[0], labelSli[1])
	switch last := labelSli[len(labelSli)-1]; last {
	case "客询":
		aiTagInfo.TagType = uint8(pb.TicketAiTagType_TicketAiTagTypeAsk)
	case "客诉":
		aiTagInfo.TagType = uint8(pb.TicketAiTagType_TicketAiTagTypeComplaint)
	case "建议":
		aiTagInfo.TagType = uint8(pb.TicketAiTagType_TicketAiTagTypeSuggestion)
	default:
		aiTagInfo.TagType = uint8(pb.TicketAiTagType_TicketAiTagTypeElse)
	}
	levels := labelSli[2:]

	// 4. 非 Else 时，合并最后两级
	if aiTagInfo.TagType != uint8(pb.TicketAiTagType_TicketAiTagTypeElse) && len(levels) >= 2 {
		n := len(levels)
		merged := fmt.Sprintf("%s-%s", levels[n-2], levels[n-1])
		levels = append(levels[:n-2], merged)
	}

	// 5. 依次填到 First/Second/Third
	if len(levels) > 0 {
		aiTagInfo.FirstTag = levels[0]
	}
	if len(levels) > 1 {
		aiTagInfo.SecondTag = levels[1]
	}
	if len(levels) > 2 {
		aiTagInfo.ThirdTag = levels[2]
	}
	return persistence.NewAITag().SaveAIPortrait(ctx, aiTagInfo)
}
