package survey

import (
	"crypto/sha256"
	"encoding/hex"
	"fmt"
	"github.com/go-sql-driver/mysql"
	"github.com/labstack/echo/v4"
	gonanoid "github.com/matoous/go-nanoid/v2"
	"github.com/spf13/cast"
	"github.com/spf13/viper"
	"ops-ticket-api/internal/cst"
	"ops-ticket-api/internal/framework/cache/keys"
	"ops-ticket-api/internal/framework/cache/rds"
	dto "ops-ticket-api/internal/persistence"
	"ops-ticket-api/models"
	"ops-ticket-api/proto/pb"
	"ops-ticket-api/utils"
	"strings"
	"time"
)

// SurveyConfigList 问卷调查配置列表
func SurveyConfigList(ctx echo.Context, req *pb.SurveyListReq) (*pb.SurveyListResp, error) {
	list, count, err := dto.NewSurveyConfig().GetSurveyConfigList(ctx.Request().Context(), req)
	if err != nil {
		return nil, err
	}
	var datas = make([]*pb.SurveyListResp_SurveyInfo, 0)
	for _, row := range list {
		var dt = &pb.SurveyEditReq{
			Id:               uint64(row.ID),
			Project:          row.GameProject,
			PushCycle:        pb.SurveyPushCycle(row.PushCycle),
			PushWeek:         row.PushWeek,
			PushTime:         row.PushTime,
			PushTimeWeb:      fmt.Sprintf("%02d:00", row.PushTime),
			EffectiveTime:    time.Unix(int64(row.EffectiveTime), 0).Local().Format("2006-01-02"),
			ExpireTime:       pb.SurveyEffective(row.ExpireTime),
			SurveyTitles:     dto.FormatSurveyI18NText(row.Titles),
			PushContents:     dto.FormatSurveyI18NText(row.Contents),
			ProductQuestions: dto.FormatSurveyI18NText(row.ProductQuestions),
			ServiceQuestions: dto.FormatSurveyI18NText(row.ServiceQuestions),
			Reasons:          dto.FormatSurveyI18NText(row.Reasons),
		}
		data := &pb.SurveyListResp_SurveyInfo{
			Id:         uint64(row.ID),
			Project:    row.GameProject,
			UpdatedAt:  utils.TimeFormat(row.UpdatedAt.Unix()),
			Op:         row.Operator,
			Enable:     row.Enable > 0,
			CanGenLink: CheckCanGenLink(dt) == nil,
		}
		datas = append(datas, data)
	}
	result := &pb.SurveyListResp{Data: datas, CurrentPage: req.Page, PerPage: req.PageSize, Total: uint32(count)}
	return result, nil
}

func CheckCanGenLink(detail *pb.SurveyEditReq) error {
	if len(detail.SurveyTitles) == 0 || detail.SurveyTitles["en"] == "" ||
		(len(detail.ProductQuestions) == 0 && len(detail.ServiceQuestions) == 0) ||
		len(detail.Reasons) == 0 || detail.Reasons["en"] == "" {
		return fmt.Errorf("survey config not complete. forbidden enable")
	}
	return nil
}

// SurveyConfigEnable 问卷调查配置禁用启用接口
func SurveyConfigEnable(ctx echo.Context, req *pb.EnableReq) error {
	//
	detail, err := SurveyConfigInfo(ctx, uint64(req.ObjectId))
	if err != nil {
		return err
	}
	if detail == nil || detail.Data == nil {
		return fmt.Errorf("survey config not found")
	}
	if req.Enable == true {
		if err := CheckCanGenLink(detail.Data); err != nil {
			return err
		}
	}
	defer rds.NewRCache().Del(ctx.Request().Context(), fmt.Sprintf(keys.SurveyConfigCacheKey, uint64(req.ObjectId)))
	return dto.NewSurveyConfig().EnableSurveyConfig(ctx.Request().Context(), req.ObjectId, req.Enable, cast.ToString(ctx.Get(cst.AccountInfoCtx)))
}

// SurveyConfigInfo 问卷调查配置详情
func SurveyConfigInfo(ctx echo.Context, id uint64) (*pb.SurveyInfoResp, error) {
	list, err := dto.NewSurveyConfig().GetSurveyConfigInfo(ctx.Request().Context(), id)
	if err != nil {
		return nil, err
	}
	return list, nil
}

// SurveyConfigEdit 问卷调查配置修改
func SurveyConfigEdit(ctx echo.Context, req *pb.SurveyEditReq) error {
	for k, v := range req.ProductQuestions {
		if strings.TrimSpace(v) == "" {
			delete(req.ProductQuestions, k)
		}
	}
	for k, v := range req.ServiceQuestions {
		if strings.TrimSpace(v) == "" {
			delete(req.ServiceQuestions, k)
		}
	}

	if len(req.ProductQuestions) == 0 && len(req.ServiceQuestions) == 0 {
		return fmt.Errorf("both product and service empty")
	}
	return dto.NewSurveyConfig().UpdateSurveyConfigInfoById(ctx.Request().Context(), req, cast.ToString(ctx.Get(cst.AccountInfoCtx)))
}

// SurveyGenLinks 问卷调查生成链接
func SurveyGenLinks(ctx echo.Context, req *pb.SurveyGenLinkReq, momentAttrs *pb.SurveyLinkParamDf_Attrs) (*pb.SurveyGenLinkResp, error) {

	//token := fmt.Sprintf("project=%s&uid=%d&lang=%s&survey_id=%d", req.Project, req.Uid, req.Lang, req.SurveyId)
	//hash := sha256.Sum256([]byte(token))
	//encryptedToken := hex.EncodeToString(hash[:])
	//link := fmt.Sprintf("%s?token=%s", baseURL, encryptedToken)
	var curr int
AGAIN:
	encryptedToken, link, tokenParam := GenSurveyToken(req.Project, req.Uid, req.Lang, req.SurveyId, req.AccountName, req.IsPublic)
	tokenParam["moment_attrs"] = momentAttrs
	data := &models.FpDscSurveyLinks{
		Project:        req.Project,
		IsPublic:       cast.ToInt8(req.IsPublic),
		EncryptedToken: encryptedToken,
		BatchID:        req.BatchId,
		UID:            req.Uid,
		DscUserID:      req.DscUserId,
		SurveyID:       req.SurveyId,
		Creator:        cast.ToString(ctx.Get(cst.AccountInfoCtx)),
		LinkURL:        link,
		TokenParam:     utils.ToJson(tokenParam),
		ExpireAt:       time.Unix(req.ExpireAt, 0),
		CreatedAt:      time.Now(),
		UpdatedAt:      time.Now(),
	}

	if req.IsPublic { // 公开链接 校验方式
		if has, err := dto.NewSurveyConfig().SurveyLinksExistCheck(ctx.Request().Context(), map[string]interface{}{
			"project":         req.Project,
			"encrypted_token": data.EncryptedToken,
			"is_public":       data.IsPublic,
		}); err != nil {
			return nil, err
		} else if has {
			return &pb.SurveyGenLinkResp{Link: link}, nil
		}
	}

	err := dto.NewSurveyConfig().InsertSurveyLinksByUid(ctx.Request().Context(), data)
	if err != nil {
		if !req.IsPublic { // 非 public ， duplicate key
			if _sqlErr, ok := err.(*mysql.MySQLError); ok && _sqlErr.Number == 1062 {
				curr = curr + 1
				if curr < 3 {
					goto AGAIN
				}
			}
		}
		return nil, err
	}

	return &pb.SurveyGenLinkResp{Link: link}, nil
}

// GenSurveyToken 生成问卷调查token 和私密链接
// return encryptedToken, link
func GenSurveyToken(project string, uid int64, lang string, surveyId int64, account string, isPublic bool) (string, string, map[string]interface{}) {
	// 生成的链接写入表 fp_dsc_survey_links
	// 私密问卷链接 参数: project + uid + lang + 问卷ID

	baseURL := viper.GetString("thirdparty.survey_base_url") // "https://example.com/survey" // 调查问卷的基础URL
	token := fmt.Sprintf("project=%s&uid=%d&lang=%s&account=%s&survey_id=%d", project, uid, lang, account, surveyId)
	hash := sha256.Sum256([]byte(token))
	encryptedToken := hex.EncodeToString(hash[:])
	var prefix string
	if !isPublic { // 非 public
		encryptedToken = genAnoid()
		prefix = _getSurveyLinKMsgPrex(lang) + " "
	}

	return encryptedToken,
		fmt.Sprintf("%s%s?survey_token=%s", prefix, baseURL, encryptedToken),
		map[string]interface{}{
			"project":   project,
			"lang":      lang,
			"uid":       uid,
			"account":   account,
			"survey_id": surveyId,
		}
}
func genAnoid() string {
	auid, _ := gonanoid.Generate("ABCDSEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789", 32)
	return auid
}
func _getSurveyLinKMsgPrex(l string) string {
	_prex := map[string]string{
		"en":    "The satisfaction survey is here! Take a moment to complete it and claim your gifts!",
		"zh-cn": "本次满意度调查来啦，完成后有小礼物送上哦！请您花费1分钟快速回答一下吧：",
		"zh-tw": "本次滿意度調查來啦，完成後有小禮物送上哦！請您花費1分鐘快速回答一下吧：",
		"ja":    "満足度調査がここにあります！ ぜひ一瞬でそれを完了して、あなたのギフトを請求してください！",
		"ko":    "여기 만족도 설문조사가 있습니다! 잠시 시간을 내어 완료하고 선물을 받아가세요!",
		"ru":    "Опрос удовлетворенности здесь! Уделите минуту для его заполнения и получите свои подарки!",
		"de":    "Die Zufriedenheitsumfrage ist hier! Nehmen Sie sich einen Moment Zeit, um sie auszufüllen und Ihre Geschenke einzufordern!",
		"fr":    "Le sondage de satisfaction est ici! Prenez un moment pour le compléter et réclamez vos cadeaux!",
		"it":    "Il sondaggio di soddisfazione è qui! Prenditi un momento per completarlo e reclama i tuoi regali!",
		"pl":    "Ankieta satysfakcji jest tutaj! Poświęć chwilę, aby ją wypełnić i odebrać swoje prezenty!",
		"es":    "¡La encuesta de satisfacción está aquí! Tómate un momento para completarla y reclama tus regalos!",
		"tr":    "Memnuniyet anketi burada! Biraz zaman ayırıp tamamlayın ve hediyelerinizi alın!",
		"pt":    "A pesquisa de satisfação está aqui! Reserve um momento para preenchê-la e reivindique seus presentes!",
		"sv":    "The satisfaction survey is here! Take a moment to complete it and claim your gifts!",
		"th":    "แบบสำรวจความพึงพอใจอยู่ที่นี่! ใช้เวลาเพียงสักครู่ในการกรอกและรับของขวัญของคุณ!",
		"ar":    "The satisfaction survey is here! Take a moment to complete it and claim your gifts!",
		"id":    "Survei kepuasan ada di sini! Luangkan waktu sejenak untuk mengisinya dan klaim hadiah Anda!",
		"nl":    "De tevredenheidsenquête is hier! Neem een moment om deze in te vullen en claim je cadeaus!",
		"vi":    "Cuộc khảo sát về sự hài lòng đã sẵn sàng! Hãy dành chút thời gian để hoàn thành nó và nhận những món quà của bạn!",
		"my":    "Tinjauan kepuasan ada di sini! Luangkan seketika untuk melengkapkannya dan tuntut hadiah anda!",
	}
	l = strings.ReplaceAll(strings.ToLower(l), "_", "-")
	if v, ok := _prex[l]; ok {
		return v
	}
	return _prex["en"]
}
