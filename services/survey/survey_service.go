package survey

import (
	"context"
	"fmt"
	"github.com/labstack/echo/v4"
	"github.com/spf13/cast"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"go.uber.org/zap"
	"ops-ticket-api/internal/code"
	"ops-ticket-api/internal/framework/cache/keys"
	"ops-ticket-api/internal/framework/cache/rds"
	"ops-ticket-api/internal/framework/lang"
	"ops-ticket-api/internal/framework/xerrors"
	"ops-ticket-api/internal/persistence"
	"ops-ticket-api/models"
	"ops-ticket-api/proto/pb"
	"ops-ticket-api/utils"
	"strings"
	"time"
)

func NewSurveySrv() *surveyService {
	return &surveyService{}
}

type surveyService struct {
}

func (srv *surveyService) SurveyEgressConfig(ctx echo.Context, param *pb.Empty) (*pb.SurveyEgressConfigResp, error) {
	// get lang
	var resp = &pb.SurveyEgressConfigResp{Langs: make([]*pb.SurveyEgressConfigResp_EnumLang, 0)}
	list, err := persistence.NewAddons().LanguageList(ctx.Request().Context())
	if err != nil {
		return nil, err
	}
	for _, item := range list {
		resp.Langs = append(resp.Langs, &pb.SurveyEgressConfigResp_EnumLang{Code: item.Code, Name: strings.ToUpper(item.Code)})
	}
	return resp, nil
}

// SubmitDcSurvey Dc调查问卷提交
func (srv *surveyService) SubmitDcSurvey(ctx echo.Context, param *pb.SurveySubmitReq) (*pb.Empty, error) {
	if len(param.SurveyToken) == 0 {
		return nil, xerrors.New(lang.FormatText(ctx, "RespInvalidParams"), code.InvalidParams)
	}

	// 2. 校验问卷内容
	rating := cast.ToInt32(param.ProductRating)
	if rating < 4 && len(param.ProductAnswer) < 0 {
		return nil, xerrors.New(lang.FormatText(ctx, "RespInvalidParams"), code.MissingParams)

	}
	rating = cast.ToInt32(param.ServiceRating)
	if rating < 4 && len(param.ServiceAnswer) < 0 {
		return nil, xerrors.New(lang.FormatText(ctx, "RespInvalidParams"), code.MissingParams)
	}

	linkDetail, paramDt, err := srv.getTokenDetail(ctx.Request().Context(), param.SurveyToken)
	if err != nil {
		logger.Errorf(ctx.Request().Context(), "get token detail err: %v. token:%s", err, param.SurveyToken)
		return nil, xerrors.New(lang.FormatText(ctx, "RespInvalidParams"), code.DbError)
	}

	if linkDetail == nil || linkDetail.ID == 0 {
		logger.Warn(ctx.Request().Context(), "get token detail is nil", zap.String("token", param.SurveyToken))
		return nil, xerrors.New(lang.FormatText(ctx, "RespInvalidParams"), code.InvalidParams)
	}
	if linkDetail.IsPublic > 0 { // 公共部分
		if param.Uid == 0 { // 1. 校验问卷提交人
			return nil, xerrors.New(lang.FormatText(ctx, "RespInvalidParams"), code.InvalidParams)
		}
	} else { // discord 内部
		if !linkDetail.CheckCanSaveSurvey(ctx.Request().Context()) {
			logger.Infof(ctx.Request().Context(), "checkCanSaveSurvey return false. token:%s. linkDetail:%+v.", param.SurveyToken, linkDetail)
			return nil, xerrors.New(lang.FormatText(ctx, "RespSurveyExpired"), code.InvalidParams)
		}
		// 只能评价一次
		if boo, _ := srv.checkTokenHasSurveyed(ctx.Request().Context(), param.SurveyToken); boo {
			return nil, xerrors.New(lang.FormatText(ctx, "RespSurveyHasDone"), code.InvalidParams)
		}
	}

	var (
		now         = time.Now()
		details     []*models.FpDscSurveyDetail
		initiator   = "system" // 问卷发起人：默认系统， 公开问卷时为提交人
		operator    string     // 处理人
		maintainer  string     // 维护人
		operators   []string   // 经手人
		dscUserId   string     // dsc user id
		dscUserName string     // dsc user name
		uid         int64      // uid
		accountId   string     // account id
	)
	if linkDetail.IsPublic == 0 { // 非公开链接
		operator = paramDt.MomentAttrs.LastReplyService
		maintainer = paramDt.MomentAttrs.Maintainer
		operators = paramDt.MomentAttrs.Processors
		dscUserId = paramDt.MomentAttrs.DscUserId
		dscUserName = paramDt.MomentAttrs.DscUserName
		uid = int64(paramDt.MomentAttrs.Uid)
		accountId = paramDt.MomentAttrs.AccountId
	}
	// 公开链接 - 重新获取当前 uid 对应 dsc 信息
	if linkDetail.IsPublic == 1 {
		uid = param.Uid
		initiator = paramDt.Account
		surveyInfoResp, err := srv.getSurveyConfigWithCache(ctx.Request().Context(), uint64(linkDetail.SurveyID))
		if err != nil {
			logger.Errorf(ctx.Request().Context(), "getSurveyConfigWithCacheget survey config err: %v. surveyId:%d", err, linkDetail.SurveyID)
			return nil, xerrors.New(lang.FormatText(ctx, "RespInvalidParams"), code.DbError)
		}
		// 公开链接-校验周期内同一个uid只能填写一次
		boo, err := srv.CanPublicSubmit(ctx.Request().Context(), surveyInfoResp.Data.PushCycle, uid)
		if err != nil {
			logger.Errorf(ctx.Request().Context(), "CanPublicSubmit err: %v. uid:%d", err, uid)
			return nil, xerrors.New(lang.FormatText(ctx, "RespInternalServer"), code.DbError)
		}
		if !boo {
			return nil, xerrors.New(lang.FormatText(ctx, "RespSurveyHasDone"), code.InvalidParams)
		}

		// todo get dsc_user_info by uid
		start, end := srv._getStartEndTmF(surveyInfoResp.Data.PushCycle)
		player, err := persistence.NewDiscordInteract().FetchSurveyDscPlayerDetail(ctx.Request().Context(), paramDt.Project, param.Uid, start, end)
		if err != nil {
			logger.Errorf(ctx.Request().Context(), "FetchSurveyDscPlayerDetail err: %v. uid:%d", err, param.Uid)
			return nil, xerrors.New(lang.FormatText(ctx, "RespInternalServer"), code.DbError)
		}
		if player != nil { // 不为空 则去赋值
			operator = player.LastReplyService
			maintainer = player.Maintainer
			operators = player.Processors
			dscUserId = player.DscUserId
			dscUserName = player.DscNickName
			accountId = player.AccountId
		}
	}

	if param.ProductRating > 0 {
		details = append(details, &models.FpDscSurveyDetail{
			Project:          paramDt.Project,
			EvaluationDate:   now.Format(time.DateOnly),
			DscUserID:        dscUserId,
			NickName:         dscUserName,
			UID:              uid,
			AccountID:        accountId,
			Rating:           int8(param.ProductRating),
			EvaluationTarget: int8(pb.SurveyQstType_SurveyQstProduct), // 产品题
			RatingReason:     param.ProductAnswer,
			Operator:         operator,
			Maintainer:       maintainer,
			Operators:        utils.ToJson(operators),
			EncryptedToken:   linkDetail.EncryptedToken,
			SurveyID:         linkDetail.SurveyID,
			Initiator:        initiator,
			CreatedAt:        now,
		})
	}
	if param.ServiceRating > 0 {
		details = append(details, &models.FpDscSurveyDetail{
			Project:          paramDt.Project,
			EvaluationDate:   now.Format(time.DateOnly),
			DscUserID:        dscUserId,
			NickName:         dscUserName,
			UID:              uid,
			AccountID:        accountId,
			Rating:           int8(param.ServiceRating),
			EvaluationTarget: int8(pb.SurveyQstType_SurveyQstService), // 服务题
			RatingReason:     param.ServiceAnswer,
			Operator:         operator,
			Maintainer:       maintainer,
			Operators:        utils.ToJson(operators),
			EncryptedToken:   linkDetail.EncryptedToken,
			SurveyID:         linkDetail.SurveyID,
			Initiator:        initiator,
			CreatedAt:        now,
		})
	}
	if len(details) == 0 {
		logger.Errorf(ctx.Request().Context(), "submit survey details is empty. token:%s. param:%+v", param.SurveyToken, param)
		return nil, xerrors.New(code.MissingParams, code.MissingParams)
	}

	cleanRdsKeys := []string{fmt.Sprintf(keys.SurveyTokenUsedCacheKey, param.SurveyToken)}
	defer func() {
		go func() {
			time.Sleep(time.Millisecond * 500)
			rds.NewRCache().Del(context.TODO(), cleanRdsKeys...)
		}()
	}()
	// 3. 保存问卷内容
	if err := persistence.NewDcSatisfactionSurvey().SaveDiscordSatisfactionSurvey(ctx, details); err != nil {
		logger.Errorf(ctx.Request().Context(), "save survey details err: %v. details:%+v. token:%s. param:%+v", err, details, param.SurveyToken, param)
		return nil, xerrors.New(code.DbError, code.DbError)
	}
	rds.NewRCache().Del(ctx.Request().Context(), cleanRdsKeys...)
	return &pb.Empty{}, nil
}

func (srv *surveyService) _getStartEndTmF(pushCycle pb.SurveyPushCycle) (int64, int64) {
	var (
		now      = time.Now()
		_startTm int64
		_endTm   int64
	)
	switch pushCycle {
	case pb.SurveyPushCycle_SurveyPushCycleEveryWeek:
		_startTm, _endTm = now.AddDate(0, 0, -7).Unix(), now.Unix()
	case pb.SurveyPushCycle_SurveyPushCycleEveryTwoWeeks:
		_startTm, _endTm = now.AddDate(0, 0, -14).Unix(), now.Unix()
	case pb.SurveyPushCycle_SurveyPushCycleEveryMonth:
		_startTm, _endTm = now.AddDate(0, -1, 0).Unix(), now.Unix()
	default:
		_startTm, _endTm = now.Unix(), now.Unix()
	}
	return _startTm, _endTm
}

// TemplateDcSurvey Dc调查问卷模版获取
func (srv *surveyService) TemplateDcSurvey(ctx echo.Context, param *pb.SurveyTemplateReq) (*pb.SurveyTemplateResp, error) {
	fun := "TemplateDcSurvey -->"
	// 获取 token link 详情
	linkDetail, tokenParam, err := srv.getTokenDetail(ctx.Request().Context(), param.SurveyToken)
	if err != nil {
		logger.Errorf(ctx.Request().Context(), "%s get token detail err: %v. token:%s", fun, err, param.SurveyToken)
		return nil, xerrors.New(lang.FormatText(ctx, "RespInvalidParams"), code.DbError)
	}
	if linkDetail == nil || linkDetail.ID == 0 {
		logger.Errorf(ctx.Request().Context(), "%s get token detail is nil. token:%s", fun, param.SurveyToken)
		return nil, xerrors.New(lang.FormatText(ctx, "RespInvalidParams"), code.InvalidParams)
	}
	if tokenParam == nil || linkDetail.SurveyID == 0 {
		logger.Errorf(ctx.Request().Context(), "%s get token param is nil. token:%s", fun, param.SurveyToken)
		return nil, xerrors.New(lang.FormatText(ctx, "RespInvalidParams"), code.InvalidParams)
	}

	// 获取调查问卷配置
	surveyInfoResp, err := srv.getSurveyConfigWithCache(ctx.Request().Context(), uint64(linkDetail.SurveyID))
	if err != nil {
		logger.Errorf(ctx.Request().Context(), "%s get survey config err: %v. surveyId:%d", fun, err, linkDetail.SurveyID)
		return nil, xerrors.New(lang.FormatText(ctx, "RespInvalidParams"), code.DbError)
	}
	data := surveyInfoResp.Data
	var _lan = tokenParam.Lang
	if _lan == "" {
		_lan = param.Lang
	}

	resp := &pb.SurveyTemplateResp{
		SurveyId:         data.Id,
		SurveyToken:      param.SurveyToken,
		Project:          data.Project,
		Lang:             _lan,
		IsShowProduct:    len(data.ProductQuestions) > 0,
		IsShowService:    len(data.ServiceQuestions) > 0,
		SurveyTitles:     getShowTiMsg(_lan, data.SurveyTitles),
		PushContents:     getShowTiMsg(_lan, data.PushContents),
		ProductQuestions: getShowTiMsg(_lan, data.ProductQuestions),
		ServiceQuestions: getShowTiMsg(_lan, data.ServiceQuestions),
		Reasons:          getShowTiMsg(_lan, data.Reasons),
		CanSubmit:        true,
		NeedUid:          linkDetail.IsPublic > 0,
	}
	if linkDetail.IsPublic == 0 {
		if linkDetail.CheckCanSaveSurvey(ctx.Request().Context()) == false {
			resp.CanSubmit = false
		}
		if resp.CanSubmit {
			if boo, _ := srv.checkTokenHasSurveyed(ctx.Request().Context(), param.SurveyToken); boo {
				resp.CanSubmit = false
			}
		}
	}
	return resp, nil
}

func (srv *surveyService) checkTokenHasSurveyed(ctx context.Context, token string) (bool, error) {
	var dest int64
	if err := rds.RCli.QueryRow(ctx, fmt.Sprintf(keys.SurveyTokenUsedCacheKey, token), &dest, func(v interface{}) error {
		cout, err := persistence.NewDcSatisfactionSurvey().CheckTokenHasSurveyed(ctx, token)
		if err != nil {
			return xerrors.New(err.Error(), code.DbError)
		}
		*v.(*int64) = *cout
		return nil
	}); err != nil {
		return false, err
	}
	return dest > 0, nil
}

func (srv *surveyService) getTokenDetail(ctx context.Context, token string) (*models.FpDscSurveyLinks, *pb.SurveyLinkParamDf, error) {
	dest := models.FpDscSurveyLinks{}
	param := &pb.SurveyLinkParamDf{}
	if err := rds.RCli.QueryRow(ctx, fmt.Sprintf(keys.SurveyLinkCacheKey, token), &dest, func(v interface{}) error {
		dt, _err := persistence.NewSurveyConfig().GetSurveyLinksByToken(ctx, token)
		if _err != nil {
			return xerrors.New(_err.Error(), code.DbError)
		}
		*v.(*models.FpDscSurveyLinks) = *dt
		return nil
	}); err != nil {
		return nil, nil, err
	}
	if dest.ID > 0 {
		param = dest.DecodeTokenParam(ctx)
	}
	return &dest, param, nil
}

func (srv *surveyService) getSurveyConfigWithCache(ctx context.Context, surveyId uint64) (*pb.SurveyInfoResp, error) {
	dest := pb.SurveyInfoResp{}
	if err := rds.RCli.QueryRow(ctx, fmt.Sprintf(keys.SurveyConfigCacheKey, surveyId), &dest, func(v interface{}) error {
		dt, err := persistence.NewSurveyConfig().GetSurveyConfigInfo(ctx, surveyId)
		if err != nil {
			return xerrors.New(err.Error(), code.DbError)
		}
		*v.(*pb.SurveyInfoResp) = *dt
		return nil
	}); err != nil {
		return nil, err
	}
	return &dest, nil
}
func getShowTiMsg(lan string, origin map[string]string) string {
	if origin == nil {
		return ""
	}
	if v, ok := origin[lan]; ok {
		return v
	}
	if v, ok := origin["en"]; ok {
		return v
	}
	return ""
}

func (srv *surveyService) CanPublicSubmit(ctx context.Context, pushCycle pb.SurveyPushCycle, uid int64) (bool, error) {

	// 获取该用户的上次提交时间
	lastSubmit, err := persistence.NewDcSatisfactionSurvey().GetLastSurveyTime(ctx, uid)
	if err != nil {
		logger.Errorf(ctx, "CanPublicSubmit get last submit time err: %v. uid:%d", err, uid)
		return false, xerrors.New(err.Error(), code.DbError)
	}
	// 若之前没有提交过，则直接返回true
	if lastSubmit == 0 {
		return true, nil
	}

	var nextAllowed time.Time
	lastSubmitTime := time.Unix(lastSubmit, 0)

	switch pushCycle {
	case pb.SurveyPushCycle_SurveyPushCycleEveryWeek:
		nextAllowed = lastSubmitTime.AddDate(0, 0, 7)
	case pb.SurveyPushCycle_SurveyPushCycleEveryTwoWeeks:
		nextAllowed = lastSubmitTime.AddDate(0, 0, 14)
	case pb.SurveyPushCycle_SurveyPushCycleEveryMonth:
		nextAllowed = lastSubmitTime.AddDate(0, 1, 0)
	default:
		return true, nil
	}

	if time.Now().Before(nextAllowed) {
		return false, nil
	}
	return true, nil
}
