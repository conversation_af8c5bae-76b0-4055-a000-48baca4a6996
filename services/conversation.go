package services

import (
	"context"
	"encoding/json"
	"regexp"
	"strings"
	"time"

	"ops-ticket-api/internal/framework/database"

	"ops-ticket-api/internal/code"
	"ops-ticket-api/internal/framework/xerrors"
	"ops-ticket-api/internal/persistence"
	dto "ops-ticket-api/internal/persistence"
	"ops-ticket-api/models"
	"ops-ticket-api/proto/pb"
	"ops-ticket-api/services/configure"
	"ops-ticket-api/services/llm"

	"github.com/google/uuid"
	jsoniter "github.com/json-iterator/go"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"gorm.io/gorm"
)

func GetConversationService() *ConversationService {
	llmAdapter, _ := llm.NewAdapter()

	repo := persistence.NewConversationRepo(database.Db())

	return NewConversationService(repo, llmAdapter)
}

// ConversationService 对话服务
// ConversationService 对话服务
type ConversationService struct {
	repo      *persistence.ConversationRepo
	llmClient *llm.Adapter
}

// LLMClient LLM客户端接口
type LLMClient interface {
	AnalyzeAnswer(ctx context.Context, req *AnalyzeRequest) (*AnalyzeResponse, error)
}

// AnalyzeRequest LLM分析请求
type AnalyzeRequest struct {
	NowQuestionKey     string
	NowQuestionContent string
	NowAnswerContent   string
}

// AnalyzeResponse LLM分析响应
type AnalyzeResponse struct {
	HitQuestion      bool
	NowAnswerContent string
	NextQuestionKey  string
}

// NewConversationService 创建对话服务
func NewConversationService(repo *persistence.ConversationRepo, llmClient *llm.Adapter) *ConversationService {
	return &ConversationService{
		repo:      repo,
		llmClient: llmClient,
	}
}

// UpdateConversation 创建或更新对话
func (s *ConversationService) UpdateConversation(ctx context.Context, req *pb.UpdateConversationRequest, gameProject, account, role string) (*pb.UpdateConversationResponse, error) {
	conversationID := req.ConversationId
	if conversationID == "" {
		conversationID = uuid.New().String()
	}

	// 构建问题进度记录
	questionProgress := &models.QuestionProgress{
		QuestionGetList: make([]*models.QuestionGet, 0, len(req.QuestionGetList)),
	}

	// 从请求中构建问题列表
	for _, q := range req.QuestionGetList {
		questionProgress.QuestionGetList = append(questionProgress.QuestionGetList, &models.QuestionGet{
			QuestionKey:      q.QuestionKey,
			Answer:           q.Answer,
			HasAnswer:        q.HasAnswer,
			QuestionAskCount: q.QuestionAskCount,
		})
	}

	progressBytes, err := json.Marshal(questionProgress)
	if err != nil {
		return nil, err
	}

	conversation := &models.Conversation{
		UserID:           role,
		CatID:            req.CatId,
		TicketID:         req.TicketId,
		ConversationID:   conversationID,
		GameProject:      gameProject,
		AccountID:        account,
		Status:           int32(pb.ConversationStatus_ConversationStatusOn), // 进行中
		QuestionProgress: string(progressBytes),
	}

	// 使用事务保存会话信息和历史记录
	err = s.repo.Transaction(ctx, func(tx *gorm.DB) error {
		// 关闭之前的会话
		if err := s.repo.CloseEarlyConversation(ctx, conversationID, account); err != nil {
			return err
		}
		//// 进行中状态
		//conversation.Status = int32(pb.ConversationStatus_ConversationStatusOn)
		//// 最后一条对话 是否用户自己
		//if len(req.History) > 0 && req.History[len(req.History)-1].Role == "user" {
		//	// 等待回答
		//	conversation.Status = int32(pb.ConversationStatus_ConversationStatusWait)
		//}
		if err := s.repo.UpsertConversation(ctx, tx, conversation); err != nil {
			return err
		}

		if len(req.History) > 0 {
			// 单条保存 保证顺序
			for _, h := range req.History {
				histories := &models.ConversationHistory{
					ConversationID: conversationID,
					HistoryID:      uuid.New().String(),
					Role:           h.Role,
					Content:        h.Content,
					QuestionKey:    h.QuestionKey,
				}

				if err := s.repo.SaveHistory(ctx, tx, histories); err != nil {
					return err
				}
			}
		}
		return nil
	})

	if err != nil {
		return nil, err
	}

	return &pb.UpdateConversationResponse{
		// Uuid:           uuid.New().String(),
		ConversationId: conversationID,
		Timestamp:      time.Now().Unix(),
	}, nil
}

// GetConversationHistory 获取对话历史
func (s *ConversationService) GetConversationHistory(ctx context.Context, req *pb.GetHistoryRequest, project string) (*pb.GetHistoryResponse, error) {
	conversation, err := s.repo.GetConversation(ctx, req.ConversationId)
	if err != nil {
		return nil, err
	}

	histories, err := s.repo.GetHistories(ctx, req.ConversationId)
	if err != nil {
		return nil, err
	}

	historyList := make([]*pb.HistoryItem, 0, len(histories))
	for _, h := range histories {
		historyList = append(historyList, &pb.HistoryItem{
			Role:        h.Role,
			Content:     h.Content,
			QuestionKey: h.QuestionKey,
			Timestamp:   h.CreatedAt.Unix(),
			Uuid:        h.HistoryID,
		})
	}

	var questionProgress models.QuestionProgress
	if err := json.Unmarshal([]byte(conversation.QuestionProgress), &questionProgress); err != nil {
		return nil, err
	}

	// 获取分类信息
	catInfo, err := configure.OldTicketCatInfo(ctx, project, uint32(conversation.CatID), make(map[string]string))
	if err != nil {
		return nil, err
	}

	// 获取分类模板
	tpl, err := dto.NewTplLang(ctx).TplInfoMultiLang(ctx, catInfo.TplID, req.GetLang())
	if err != nil {
		return nil, err
	}

	fields := make([]*FieldsDetail, 0)
	if err := jsoniter.ConfigFastest.UnmarshalFromString(tpl.Fields, &fields); err != nil {
		logger.Errorf(ctx, "fields unmarshal err", err.Error())
		return nil, xerrors.New(err.Error(), code.Error)
	}
	// 构建问题列表
	questionList := make([]*pb.QuestionInfo, 0)
	for _, field := range fields {
		questionList = append(questionList, &pb.QuestionInfo{
			QuestionKey:  field.FieldKey,
			QuestionName: field.FieldName,
		})
	}

	questionGetList := make([]*pb.QuestionGet, 0)

	for _, v := range questionList {
		hasAnswer := false
		answer := ""
		questionAskCount := 0
		for _, q := range questionProgress.QuestionGetList {
			if q.QuestionKey == v.QuestionKey {
				hasAnswer = q.HasAnswer
				answer = q.Answer
				questionAskCount = int(q.QuestionAskCount)
			}
		}
		questionGetList = append(questionGetList, &pb.QuestionGet{
			QuestionKey:      v.QuestionKey,
			Answer:           answer,
			HasAnswer:        hasAnswer,
			QuestionAskCount: int32(questionAskCount),
		})
	}

	return &pb.GetHistoryResponse{
		History:            historyList,
		QuestionGetList:    questionGetList,
		ConversationStatus: int32(conversation.Status),
		TicketId:           conversation.TicketID,
	}, nil
}

// HandleChat 处理对话
func (s *ConversationService) HandleChat(ctx context.Context, req *pb.ChatRequest) (*pb.ChatResponse, error) {
	convInfo, err := s.repo.GetConversation(ctx, req.ConversationId)
	if err != nil {
		return nil, err
	}

	// 构建消息内容
	answerContents := make([]llm.MessageContent, 0, len(req.NowAnswerContent))
	for _, content := range req.NowAnswerContent {
		answerContents = append(answerContents, llm.MessageContent{
			Type: "text",
			Text: content.Text,
		})
	}

	// 构建历史消息
	histories, err := s.repo.GetHistories(ctx, req.ConversationId)
	if err != nil {
		return nil, err
	}

	hists := make([]llm.HistoryMessage, 0, len(histories))
	for _, h := range histories {
		hists = append(hists, llm.HistoryMessage{
			Role: h.Role,
			Content: []llm.MessageContent{{
				Type: "text",
				Text: h.Content,
			}},
		})
	}

	// 构建问题列表
	questions := make([]llm.Question, 0)
	for _, q := range req.GetQuestionList() {
		questions = append(questions, llm.Question{
			QuestionKey:     q.QuestionKey,
			QuestionContent: q.QuestionContent,
		})
	}

	// 收集所有的文本内容
	var texts []string
	for _, content := range req.NowAnswerContent {
		texts = append(texts, content.Text)
	}
	answerText := strings.Join(texts, "\n")

	// 调用 LLM 分析
	llmResp, err := s.llmClient.AnalyzeAnswer(ctx, &llm.AnalyzeRequest{
		MsgID:              uuid.New().String(),
		HelloWord:          questions[0].QuestionContent,
		QuestionList:       questions,
		NowQuestionKey:     req.NowQuestionKey,
		NowQuestionContent: req.NowQuestionContent,
		NowAnswerContent:   answerText,
		Hists:              hists,
	})
	if err != nil {
		return nil, err
	}

	questionGetList := make([]*pb.QuestionGet, 0)
	for _, q := range llmResp.Data.HistoryAnswerList {
		questionGetList = append(questionGetList, &pb.QuestionGet{
			QuestionKey: q.QuestionKey,
			Answer:      q.QuestionAnswer,
		})
	}

	var questionProgress models.QuestionProgress
	if err := json.Unmarshal([]byte(convInfo.QuestionProgress), &questionProgress); err != nil {
		return nil, err
	}

	//---------- 问题进度处理 -----------
	hasAnswerNum := 0
	noAnswerKey := ""
	// get
	for k, v := range questionProgress.QuestionGetList {
		if v.HasAnswer {
			hasAnswerNum++
		} else {
			noAnswerKey = questionProgress.QuestionGetList[k].QuestionKey
		}
	}
	completed := false
	// 所有问题命中
	if len(questionProgress.QuestionGetList) == hasAnswerNum {
		completed = true
	}
	// 最后一个问题命中
	if len(questionProgress.QuestionGetList)-1 == hasAnswerNum && noAnswerKey == llmResp.Data.NowQuestionKey && llmResp.Data.HitQuestion {
		completed = true
	}

	answerContent := llmResp.Data.NowAnswerContent

	// NowAnswerContent 中提取 地址， 有多个地址则返回 逗号分隔 字符串
	// 正则表达式匹配所有https文件地址
	re := regexp.MustCompile(`https://[^/\s]+/[^/\s]+(?:/[^/\s]+)*\.[a-zA-Z0-9]+`)

	matches := re.FindAllString(answerContent, -1)
	imageUrls := make([]string, 0)
	for _, match := range matches {
		imageUrls = append(imageUrls, match)
	}
	if len(imageUrls) > 0 && req.NowQuestionKey == "picture" {
		answerContent = strings.Join(imageUrls, ",")
	}

	//---------- 问题进度处理 -----------
	return &pb.ChatResponse{
		HistoryAnswerList: questionGetList,
		NowQuestionKey:    llmResp.Data.NowQuestionKey,
		NowAnswerContent:  answerContent,
		HitQuestion:       llmResp.Data.HitQuestion,
		Completed:         completed,
	}, nil
}

func (s *ConversationService) GetUnFinishConversion(ctx context.Context, gameProject, account string) (*pb.ContinueChatResponse, error) {
	conInfo, err := s.repo.GetUnFinishConversion(ctx, gameProject, account)
	if err != nil {
		return nil, err
	}
	return &pb.ContinueChatResponse{
		ConversationId:        conInfo.ConversationID,
		CatId:                 conInfo.CatID,
		HasActiveConversation: conInfo.ID != 0,
	}, nil
}

func (s *ConversationService) CloseConversation(ctx context.Context, conversationID string) error {
	return s.repo.CloseConversation(ctx, conversationID)
}
