package services

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/bsm/redislock"
	"github.com/go-redis/redis/v8"
	"github.com/labstack/echo/v4"
	"github.com/olivere/elastic/v7"
	"github.com/spf13/cast"
	"github.com/spf13/viper"
	"github.com/tidwall/gjson"
	"github.com/zeromicro/go-zero/core/fx"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"gitlab-ee.funplus.io/ops-tools/compkg/httpclient"
	"gitlab-ee.funplus.io/ops-tools/compkg/sign"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"ops-ticket-api/services/communicate"
	"ops-ticket-api/services/ticket_llm"

	"ops-ticket-api/internal/code"
	"ops-ticket-api/internal/cst"
	"ops-ticket-api/internal/elasticsearch"
	"ops-ticket-api/internal/framework/cache/keys"
	"ops-ticket-api/internal/framework/cache/rds"
	"ops-ticket-api/internal/framework/cfg"
	"ops-ticket-api/internal/framework/database"
	"ops-ticket-api/internal/framework/lang"
	"ops-ticket-api/internal/framework/xerrors"
	dto "ops-ticket-api/internal/persistence"
	"ops-ticket-api/models"
	aielfin "ops-ticket-api/pkg/elfin"
	"ops-ticket-api/pkg/workflow"
	"ops-ticket-api/proto/pb"
	"ops-ticket-api/services/ai"
	"ops-ticket-api/services/commsrv"
	"ops-ticket-api/services/reporter"
	"ops-ticket-api/utils"
	"sort"
	"strings"
	"sync"
	"time"
)

// Reassign 指派
func (srv *ticketService) Reassign(ctx echo.Context, ticketId uint64, acceptor string) error {
	var (
		err    error
		tkInfo *models.FpOpsTickets
		fun    = "ticketService.Reassign"
	)
	// ticket process lock
	mux, err := rds.GetRdsMux(ctx.Request().Context(), keys.TicketProcessLock+cast.ToString(ticketId))
	if err != nil {
		logger.Error(ctx.Request().Context(), "get ticket reassign lock err",
			logger.Uint64("ticketId", ticketId), logger.String("fun", fun), logger.Any("err", err))
		return err
	}
	defer mux.Release(ctx.Request().Context())

	// 1.0 check 操作人是否有权限
	//if err := srv.opBefore(ctx); err != nil {
	//	return err
	//}
	// check status
	if tkInfo, err = dto.NewTicket().TicketStDoneCheck(ctx.Request().Context(), ticketId); err != nil {
		return err
	}
	if tkInfo.Acceptor == acceptor {
		return xerrors.New(code.StatusText(code.RepeatData), code.RepeatData)
	}
	if !ctx.Get(cst.AccountIsAdminCtx).(bool) { // 非超管 - check
		if boo, err := srv.reassignBaseCheck(ctx, acceptor, tkInfo.Project, tkInfo.Lang); err != nil {
			logger.Errorf(ctx.Request().Context(), "%s reassignBaseCheck return err. acceptor:%s. err:%v", fun, acceptor, err)
			return xerrors.New(err, code.DbError)
		} else if boo == false {
			logger.Warn(ctx.Request().Context(), "reassignBaseCheck return false. account no online", logger.Any("fun", fun), logger.Any("ticketId", ticketId), logger.Any("account", acceptor))
			return xerrors.New(code.StatusText(code.UserIsNotOnline), code.UserIsNotOnline)
		}
	}

	// 过滤非转人工单&&自动回复单
	if tkInfo.AutoReplyID > 0 || (tkInfo.SolveType != uint32(pb.SolveType_SolveTypeManualTicket) && tkInfo.ReopenNum < 1) {
		return xerrors.New(code.StatusText(code.TicketReassignReply), code.TicketReassignReply)
	}

	dest := map[string]interface{}{
		"acceptor":   acceptor,
		"status":     uint32(pb.TkStatus_TkStatusProcessing),
		"updated_at": time.Now().Unix(),
	}

	switch pb.TkStage(tkInfo.ConversionNode) {
	case pb.TkStage_TkStageNew, pb.TkStage_TkStageAgentReopen:
		dest["conversion_node"] = uint32(pb.TkStage_TkStageNewForAgent)
	}

	if err := dto.NewTicket().TicketPropsUpdate(ctx.Request().Context(), ticketId, dest, func(txDb *gorm.DB) error {
		return dto.NewGroupRepo().UserAssignedTicket(ctx.Request().Context(), txDb, acceptor)
	}); err != nil {
		logger.Errorf(ctx.Request().Context(), "%s TicketPropsUpdate return err. ticketId:%d. dest:%+v. err:%v", fun, ticketId, dest, err)
		return err
	}

	defer func() {
		if err := rds.RCli.ZAdd(ctx.Request().Context(), keys.TicketAcceptor2HNoActionListV3, &redis.Z{
			Score: float64(time.Now().Unix() + cfg.GetAllocNoOpTimeout()), Member: ticketId,
		}).Err(); err != nil {
			logger.Errorf(ctx.Request().Context(), "%s add acceptor alloc watch list. err:%v. ticketId:%d. acceptor:%s", fun, ticketId, acceptor)
		}
	}()
	newCtx := utils.ContextOnlyValue{ctx.Request().Context()}
	reporter.PubAssign(newCtx, ticketId, tkInfo.ConversionNode, tkInfo.Acceptor, acceptor, cast.ToString(ctx.Get(cst.AccountInfoCtx)))
	elasticsearch.DefaultTicketSyncSvc.UpdateTicket(newCtx, ticketId, dest)
	return nil
}

// BatchReassign 批量指派
func (srv *ticketService) BatchReassign(ctx echo.Context, ticketIds []uint64, acceptor string) error {
	var (
		err      error
		fun      = "ticketService.BatchReassign"
		operator = cast.ToString(ctx.Get(cst.AccountInfoCtx))
	)

	// 1.0 check 操作人是否有权限
	if err := srv.opBefore(ctx); err != nil {
		return err
	}

	ticketIds, err = srv.retrieveCanReassignTicket(ctx, ticketIds, acceptor)
	if err != nil {
		return err
	}
	for _, ticketId := range ticketIds {
		tkId := ticketId
		// ticket process lock
		mux, err := rds.GetRdsMux(ctx.Request().Context(), keys.TicketProcessLock+cast.ToString(tkId))
		if err != nil {
			logger.Warn(ctx.Request().Context(), "get ticket reassign lock err",
				logger.Uint64("ticketId", ticketId), logger.String("fun", fun), logger.Any("err", err))
			continue
		}
		defer mux.Release(context.TODO())

		// check status
		tkInfo, err := dto.NewTicket().GetTicketInfoFromMaster(ctx.Request().Context(), ticketId)
		if err != nil {
			return err
		}
		if tkInfo.Acceptor == acceptor || tkInfo.Status == uint32(pb.TkStatus_TkStatusDone) {
			continue
		}

		// 过滤非转人工单&&自动回复单
		if tkInfo.AutoReplyID > 0 || (tkInfo.SolveType != uint32(pb.SolveType_SolveTypeManualTicket) && tkInfo.ReopenNum < 1) {
			return xerrors.New(code.StatusText(code.TicketReassignReply), code.TicketReassignReply)
		}

		dest := map[string]interface{}{
			"acceptor":   acceptor,
			"status":     uint32(pb.TkStatus_TkStatusProcessing),
			"updated_at": time.Now().Unix(),
		}
		switch pb.TkStage(tkInfo.ConversionNode) {
		case pb.TkStage_TkStageNew, pb.TkStage_TkStageAgentReopen:
			dest["conversion_node"] = uint32(pb.TkStage_TkStageNewForAgent)
		}

		hist := &models.FpOpsTicketsHistory{
			TicketID:  ticketId,
			Acceptor:  acceptor,
			Operate:   uint8(pb.TkEvent_TkEventAssign),
			OpDetail:  tkInfo.ConversionNode,
			OpRole:    uint8(pb.UserRole_ServiceRole),
			Operator:  operator,
			Remark:    "from=" + tkInfo.Acceptor,
			CreatedAt: uint64(time.Now().Unix()),
			UpdatedAt: uint64(time.Now().Unix()),
		}

		if err := dto.NewTicket().TicketPropsUpdate(ctx.Request().Context(), ticketId, dest, func(txDb *gorm.DB) error {
			if _err := elasticsearch.DefaultTicketSyncSvc.UpdateTicket(ctx.Request().Context(), ticketId, dest); _err != nil {
				logger.Warn(ctx.Request().Context(), "BatchReassign update es err", logger.Any("ticketId", ticketId), logger.Any("dest", dest), logger.Any("err", _err))
				return _err
			}
			if _err := dto.NewTicket().AddTicketHistory(ctx.Request().Context(), txDb, hist); _err != nil {
				logger.Warn(ctx.Request().Context(), "BatchReassign add ticket history err", logger.Any("ticketId", ticketId), logger.Any("hist", hist), logger.Any("err", _err))
				return _err
			}
			if _err := dto.NewGroupRepo().UserAssignedTicket(ctx.Request().Context(), txDb, acceptor); _err != nil {
				logger.Warn(ctx.Request().Context(), "BatchReassign user assigned ticket update last alloc time return err", logger.Any("ticketId", ticketId), logger.Any("acceptor", acceptor), logger.Any("err", _err))
				return _err
			}
			return nil
		}); err != nil {
			logger.Errorf(ctx.Request().Context(), "%s TicketPropsUpdate return err. ticketId:%d. dest:%+v. err:%v", fun, ticketId, dest, err)
			return err
		}

		go func(newCtx context.Context, tkId uint64) {
			// 2h 无操作超时回收
			if err := rds.RCli.ZAdd(newCtx, keys.TicketAcceptor2HNoActionListV3, &redis.Z{
				Score: float64(time.Now().Unix() + cfg.GetAllocNoOpTimeout()), Member: tkId,
			}).Err(); err != nil {
				logger.Errorf(newCtx, "%s add acceptor alloc watch list. err:%v. ticketId:%d. acceptor:%s", fun, tkId, acceptor)
			}
		}(utils.ContextOnlyValue{ctx.Request().Context()}, tkId)
	}
	return nil
}

func (srv *ticketService) retrieveCanReassignTicket(ctx echo.Context, ticketIds []uint64, acceptor string) ([]uint64, error) {
	//var (
	//	fun = "ticketService.retrieveCanReassignTicket"
	//)
	if len(ticketIds) == 0 {
		return nil, nil
	}
	tkDetails, err := dto.NewTicket().GetTicketInfosFromMaster(ctx, ticketIds)
	if err != nil {
		return nil, err
	}
	if len(tkDetails) == 0 {
		return nil, nil
	}
	if !ctx.Get(cst.AccountIsAdminCtx).(bool) { // 非超管 - check
		users, _, _, err := dto.NewUserAssignTicketRepo().FetchUserState(ctx.Request().Context(), acceptor, "", "")
		if err != nil {
			return nil, err
		}
		//if state != pb.UserLoginStatus_UserLoginStatusYes {
		//	logger.Warn(ctx.Request().Context(), "return false. account no online", logger.String("fun", fun), logger.Any("account", acceptor))
		//	return nil, xerrors.New(code.StatusText(code.UserIsNotOnline), code.UserIsNotOnline)
		//}
		hasPerTks := make([]*models.FpOpsTickets, 0)
		for _, tk := range tkDetails {
			if tk.Status == uint32(pb.TkStatus_TkStatusDone) || tk.Acceptor == acceptor {
				continue
			}
			var flag bool = false // can alloc
			for _, user := range users {
				if tk.Project != "" {
					if utils.GroupGameMatch(tk.Project, user.Game) && utils.GroupLangMatch(tk.Lang, user.Lang) {
						flag = true
						break
					}
				}
			}
			if flag {
				hasPerTks = append(hasPerTks, tk)
			}
		}
		tkDetails = hasPerTks
	}
	var canTicketsIds []uint64
	for _, row := range tkDetails {
		canTicketsIds = append(canTicketsIds, row.TicketID)
	}
	return canTicketsIds, nil
}

func (srv *ticketService) reassignBaseCheck(ctx echo.Context, acceptor, project, lang string) (bool, error) {
	// 用户是否有游戏权限
	gmList, err := commsrv.LoadPermGmList(ctx.Request().Context(), acceptor)
	if err != nil {
		return false, err
	}
	if len(gmList) > 0 {
		if utils.InArrayAny(project, gmList) == false {
			return false, fmt.Errorf("当前用户没有该游戏权限. %s - %s", acceptor, project)
		} else {
			return true, nil
		}
	}
	// 超管的游戏列表为空
	return true, nil
	// 是否在线
	//_, state, _, err := dto.NewGroupRepo().GetUserState(ctx.Request().Context(), acceptor, project, lang)
	//if err != nil {
	//	return false, err
	//}
	//return state == pb.UserLoginStatus_UserLoginStatusYes, nil
}

func (srv *ticketService) checkTurn(ctx echo.Context, acceptor, project, lang string) (bool, error) {
	// 1. 被流转人有游戏权限
	gmList, err := commsrv.LoadPermGmList(ctx.Request().Context(), acceptor)
	if err != nil {
		return false, errors.New("接口错误:当前用户无游戏权限")
	}
	if len(gmList) > 0 {
		if utils.InArrayAny(project, gmList) == false {
			return false, fmt.Errorf("当前用户没有该游戏权限. %s - %s", acceptor, project)
		}
	}
	// 1. 被流转人 登录 & 接单上限
	_, _, upperLimit, err := dto.NewUserAssignTicketRepo().FetchUserState(ctx.Request().Context(), acceptor, project, lang)
	if err != nil {
		return false, err
	}
	//if state != pb.UserLoginStatus_UserLoginStatusYes {
	//	return false, nil
	//}
	// get account current doing tickets Num
	num, err := dto.NewTicket().GetDoingTicketsNum(ctx.Request().Context(), []string{acceptor})
	if err != nil {
		return false, err
	}
	logger.Warn(ctx.Request().Context(), "ticketService.checkTurn acceptor current doing ticket num gt upper_limit. ", logger.Any("acceptor", acceptor), logger.Any("upper_limiter", upperLimit), logger.Any("cur_num", num))
	if num[acceptor] >= upperLimit {
		return false, fmt.Errorf("当前用户处理中工单数已达上限")
	}
	// 可接单
	return true, nil
}

// TicketTurn 工单流转给某个客服 - 逻辑类似于指派
func (srv *ticketService) TicketTurn(ctx echo.Context, ticketId uint64, acceptor string) error {
	var (
		err     error
		tkInfo  *models.FpOpsTickets
		operate = cast.ToString(ctx.Get(cst.AccountInfoCtx))
		fun     = "ticketService.Turn"
	)
	// ticket process lock
	mux, err := rds.GetRdsMux(ctx.Request().Context(), keys.TicketProcessLock+cast.ToString(ticketId))
	if err != nil {
		logger.Error(ctx.Request().Context(), "get ticket reassign lock err",
			logger.Uint64("ticketId", ticketId), logger.String("fun", fun), logger.Any("err", err))
		return err
	}
	defer mux.Release(ctx.Request().Context())

	// 1.0 check 操作人是否有权限
	//if err := srv.opBefore(ctx); err != nil {
	//	return err
	//}

	if tkInfo, err = dto.NewTicket().TicketStDoneCheck(ctx.Request().Context(), ticketId); err != nil {
		return err
	}
	if tkInfo.Acceptor == "" || tkInfo.Acceptor != operate {
		logger.Errorf(ctx.Request().Context(), "%s ticket acceptor not cur operator. id:%d. tkInfo.Acceptor:%s. op:%s. detail:%+v", fun, ticketId, tkInfo.Acceptor, operate, tkInfo)
		return xerrors.New(code.StatusText(code.NoPermission), code.NoPermission)
	}
	if tkInfo.Acceptor == acceptor {
		logger.Errorf(ctx.Request().Context(), "%s ticket acceptor eq new_acceptor. id:%d. tkInfo.Acceptor:%s. op:%s. detail:%+v", fun, ticketId, tkInfo.Acceptor, operate, tkInfo)
		return xerrors.New(code.StatusText(code.RepeatData), code.RepeatData)
	}

	if !ctx.Get(cst.AccountIsAdminCtx).(bool) { // 非超管 - check
		if boo, err := srv.checkTurn(ctx, acceptor, tkInfo.Project, tkInfo.Lang); err != nil {
			logger.Errorf(ctx.Request().Context(), "%s checkTurn return err. acceptor:%s. err:%v", fun, acceptor, err)
			return xerrors.New(err, code.DbError)
		} else if boo == false {
			logger.Warn(ctx.Request().Context(), "checkTurn return false. account no online or overload", logger.Any("fun", fun), logger.Any("ticketId", ticketId), logger.Any("account", acceptor))
			return xerrors.New(code.StatusText(code.UserIsNotOnline), code.UserIsNotOnline)
		}
	}

	dest := map[string]interface{}{
		"acceptor":   acceptor,
		"status":     uint32(pb.TkStatus_TkStatusProcessing),
		"updated_at": time.Now().Unix(),
	}

	if err := dto.NewTicket().TicketPropsUpdate(ctx.Request().Context(), ticketId, dest,
		func(txDb *gorm.DB) error {
			return dto.NewGroupRepo().UserAssignedTicket(ctx.Request().Context(), txDb, acceptor)
		}); err != nil {
		logger.Errorf(ctx.Request().Context(), "%s TicketPropsUpdate return err. ticketId:%d. dest:%+v. err:%v", fun, ticketId, dest, err)
		return err
	}

	defer func() {
		// 2h 无操作超时回收
		if err := rds.RCli.ZAdd(ctx.Request().Context(), keys.TicketAcceptor2HNoActionListV3, &redis.Z{
			Score: float64(time.Now().Unix() + cfg.GetAllocNoOpTimeout()), Member: ticketId,
		}).Err(); err != nil {
			logger.Errorf(ctx.Request().Context(), "%s add acceptor alloc watch list. err:%v. ticketId:%d. acceptor:%s", fun, ticketId, acceptor)
		}
	}()

	newCtx := utils.ContextOnlyValue{ctx.Request().Context()}
	reporter.PubTurn(newCtx, ticketId, tkInfo.ConversionNode, tkInfo.Acceptor, acceptor, cast.ToString(ctx.Get(cst.AccountInfoCtx)))
	elasticsearch.DefaultTicketSyncSvc.UpdateTicket(newCtx, ticketId, dest)
	return nil
}

// TicketsUpgrade 工单升级
func (srv *ticketService) TicketsUpgrade(ctx echo.Context, ticketId uint64, upgrade int) error {
	fun := "ticketService.TicketsUpgrade"
	repo := dto.NewTicket()
	operate := cast.ToString(ctx.Get(cst.AccountInfoCtx))

	tkDetail, err := repo.TicketStDoneCheck(ctx.Request().Context(), ticketId)
	if err != nil {
		return err
	}
	// 1.0 check 操作人是否有权限
	if err = srv.opBefore(ctx); err != nil {
		return err
	}
	// 保存
	err = repo.TicketsUpgrade(ctx.Request().Context(), ticketId, upgrade, operate, tkDetail.UpgradeNum+1)
	if err != nil {
		logger.Errorf(ctx.Request().Context(), "%s repo.TicketsUpgrade return err. %d. upgrade:%d. operate:%s. err:%v", fun, ticketId, upgrade, operate, err)
		return err
	}
	defer func() {
		if upgrade != 0 || tkDetail.ConversionNode != uint32(pb.TkStage_TkStageAgentReplied) {
			return
		}
		// 根据配置获取过期时间
		expireTime, remindTime, err := cfg.GetRefillTimeoutByConfig(ctx, ticketId, map[string]string{})
		overTime := utils.NowTimestamp() + expireTime*3600
		if err != nil {
			logger.Errorf(ctx.Request().Context(), "%s get ticket expire time err. ticketId:%d. err:%v", fun, ticketId, err)
			return
		}
		// 降级 & 工单状态:客服已回复 & 3d超时关闭队列不存在 --> 设置 3d 超时关闭队列
		curScore, err := rds.RCli.ZScore(ctx.Request().Context(), keys.TicketTimeWatchPlayerListV3, cast.ToString(ticketId)).Result()
		if errors.Is(err, redis.Nil) { // 不存在
			if err := rds.RCli.ZAdd(ctx.Request().Context(), keys.TicketTimeWatchPlayerListV3, &redis.Z{
				Score:  float64(overTime),
				Member: ticketId,
			}).Err(); err != nil {
				logger.Errorf(ctx.Request().Context(), "%s add ticket overtime watch list. err:%v. ticketId:%d", fun, ticketId, err)
			}
		} else if err != nil {
			logger.Errorf(ctx.Request().Context(), "%s get overtime zset score by ticketId err. ticketId:%d. err:%v", fun, ticketId, err)
			return
		} else {
			logger.Infof(ctx.Request().Context(), "TicketsUpgrade ticketId has in overtime watch list. continue. ticketId:%d. score:%.02f. err:%v", ticketId, curScore, err)
		}

		// 降级 & 工单状态:客服已回复 & 3d超时提醒队列不存在 --> 设置 3d 超时提醒队列
		if remindTime > 0 {
			curScore, err = rds.RCli.ZScore(ctx.Request().Context(), keys.TicketOvertimeRemarkWatchPlayerList, cast.ToString(ticketId)).Result()
			if errors.Is(err, redis.Nil) { // 不存在
				if err := rds.RCli.ZAdd(ctx.Request().Context(), keys.TicketOvertimeRemarkWatchPlayerList, &redis.Z{
					Score:  float64(overTime - remindTime*60),
					Member: ticketId,
				}).Err(); err != nil {
					logger.Errorf(ctx.Request().Context(), "%s add ticket remindtime watch list. err:%v. ticketId:%d", fun, ticketId, err)
				}
			} else if err != nil {
				logger.Errorf(ctx.Request().Context(), "%s get remindtime zset score by ticketId err. ticketId:%d. err:%v", fun, ticketId, err)
				return
			} else {
				logger.Infof(ctx.Request().Context(), "TicketsUpgrade ticketId has in remindtime watch list. continue. ticketId:%d. score:%.02f. err:%v", ticketId, curScore, err)
			}
		}
	}()

	return nil
}

// 后端所有操作 - 校验当前账户
func (srv *ticketService) opBefore(ctx echo.Context) error {
	//fun := "ticketService.opBefore ->"
	//// 1.0 后端操作按钮 - 非超管，不许是在线状态
	//operator := ctx.Get(cst.AccountInfoCtx).(string)
	//if !ctx.Get(cst.AccountIsAdminCtx).(bool) {
	//	// 校验 是否在线
	//	state, _, err := dto.NewGroupRepo().GetUserState(ctx.Request().Context(), operator, "", "")
	//	if err != nil {
	//		logger.Errorf(ctx.Request().Context(), "%s get user login state return err. err:%v. operator:%s", fun, err, operator)
	//		return err
	//	}
	//	if state != pb.UserLoginStatus_UserLoginStatusYes {
	//		return xerrors.New(code.StatusText(code.UserIsNotOnline), code.UserIsNotOnline)
	//	}
	//}
	return nil
}

// 工单标签保存
func (srv *ticketService) TicketReTags(ctx echo.Context, req *pb.TicketRetaggingReq) error {
	fun := "ticketService.TicketReTags"
	operator := ctx.Get(cst.AccountInfoCtx).(string)
	// 1.0 check 操作人是否有权限
	if err := srv.opBefore(ctx); err != nil {
		return err
	}
	// 1、get current tag list
	oldTags, err := dto.NewTicket().GetTicketTags(ctx.Request().Context(), req.TicketId)
	if err != nil {
		logger.Errorf(ctx.Request().Context(), "%s GetTicketTags return err. err:%v. req:%+v", fun, err, req)
		return err
	}
	// 2、compare get diff. add and del list
	delTags, addTags := compareGenDiffTag(oldTags, req.LabelId)

	// 3、save to db & es
	if err := dto.NewTicket().SaveTicketTags(ctx.Request().Context(), req.TicketId, delTags, addTags, operator); err != nil {
		logger.Errorf(ctx.Request().Context(), "%s repo.SaveTicketTags return err. err:%v. req:%+v", fun, err, req)
		return err
	}
	return err
}

// 删除、 新增
func compareGenDiffTag(oldTags, newTags []uint32) (delTags, addTags []uint32) {
	// del tag
	for _, oldT := range oldTags {
		var has bool
		for _, newT := range newTags {
			if oldT == newT {
				has = true
				break
			}
		}
		if !has {
			delTags = append(delTags, oldT)
		}
	}
	// add tag
	for _, newT := range newTags {
		var has bool
		for _, oldT := range oldTags {
			if oldT == newT {
				has = true
				break
			}
		}
		if !has {
			addTags = append(addTags, newT)
		}
	}
	return
}

// TicketRemark 工单备注
func (srv *ticketService) TicketRemark(ctx echo.Context, req *pb.TicketRemarkReq) error {
	if err := srv.opBefore(ctx); err != nil {
		return err
	}

	err := dto.NewTicket().TicketRemark(ctx.Request().Context(), req, cast.ToString(ctx.Get(cst.AccountInfoCtx)))
	if err != nil {
		return err
	}
	return nil
}

func (srv *ticketService) TicketReturnPool(ctx echo.Context, req *pb.TicketReturnPoolReq) error {
	fun := "ticketService.TicketReturnPool"
	now := utils.NowTimestamp()
	operator := cast.ToString(ctx.Get(cst.AccountInfoCtx))
	tkDetail, err := dto.NewTicket().GetTicketInfoFromMaster(ctx.Request().Context(), req.TicketId)
	if err != nil {
		return err
	}
	// 已完成的工单不允许退回
	if tkDetail.Status == uint32(pb.TkStatus_TkStatusDone) {
		logger.Warn(ctx.Request().Context(), "finished ticket is not allowed to return to pool.", zap.Uint64("ticket_id", req.TicketId))
		return xerrors.New(code.StatusText(code.InvalidParams), code.InvalidParams)
	}
	// 校验工单状态，仅工单状态为：「待处理/处理中-玩家已回复/重开」可以退回工单池
	if tkDetail.ConversionNode != uint32(pb.TkStage_TkStageNewForAgent) && tkDetail.ConversionNode != uint32(pb.TkStage_TkStageWaitingForAgent) && tkDetail.ConversionNode != uint32(pb.TkStage_TkStageAgentReopen) {
		logger.Warn(ctx.Request().Context(), "only TkStageNewForAgent or TkStageWaitingForAgent or TkStageAgentReopen tickets are allowed to return to pool.", zap.Uint64("ticket_id", req.TicketId))
		return xerrors.New(code.StatusText(code.InvalidParams), code.InvalidParams)
	}
	// 该工单清空当前处理人，回到工单池流转节点变为「待接单」，跟随工单池分单规则分单(定时任务自动分单)，加操作日志
	// 将工单状态变为待接入工单，因为自动分单只针对待处理工单
	dest := map[string]interface{}{
		"conversion_node": uint32(pb.TkStage_TkStageNew),
		"acceptor":        "",
		"status":          uint32(pb.TkStatus_TkStatusUntreated),
		"updated_at":      now,
	}
	// es中流转节点叫stage, 不叫conversion_node
	esDest := map[string]interface{}{
		"stage":      uint32(pb.TkStage_TkStageNew),
		"acceptor":   "",
		"status":     uint32(pb.TkStatus_TkStatusUntreated),
		"updated_at": now,
	}
	// 使用脚本更新es
	scripts := []string{
		"ctx._source.stage=params.stage",
		"ctx._source.acceptor=params.acceptor",
		"ctx._source.status=params.status",
		"ctx._source.updated_at=params.updated_at",
	}
	// 更新DB数据，新增工单日志并同步到es
	lastUserReplyAt, err := dto.NewTicket().GetTicketLastUserOpAt(ctx.Request().Context(), req.TicketId)
	if err != nil {
		return err
	}
	// history
	hist := &models.FpOpsTicketsHistory{
		TicketID:  req.TicketId,
		Acceptor:  "",
		Operate:   uint8(pb.TkEvent_TkEventReturnPool),
		OpRole:    uint8(pb.UserRole_ServiceRole),
		Operator:  operator,
		OpObject:  cast.ToString(lastUserReplyAt),
		CreatedAt: now,
		UpdatedAt: now,
	}
	// save data
	if err = dto.NewTicket().TicketReturnPool(ctx.Request().Context(), req.TicketId, dest, hist, func(txDb *gorm.DB) error {
		// update es ticket
		if err = elasticsearch.DefaultTicketSyncSvc.UpdateTicketByScripts(ctx.Request().Context(), req.TicketId, scripts, esDest); err != nil {
			return err
		}
		return nil
	}); err != nil {
		logger.Errorf(ctx.Request().Context(), "%s TicketReturnPool return err. ticketId:%d. dest:%+v. err:%v", fun, req.TicketId, dest, err)
		return err
	}
	return nil
}

// TicketTransfer 工单回复、状态流转
func (srv *ticketService) TicketTransfer(ctx echo.Context, param *pb.TicketTransferReq, isBatch bool) error {
	var (
		err      error
		tkInfo   *models.FpOpsTickets
		tkCommu  *models.FpOpsTicketsCommu
		operate  = cast.ToString(ctx.Get(cst.AccountInfoCtx))
		fun      = "ticketService.TicketReply"
		opEvent  = pb.TkEvent_TkEventCommu
		finished bool
		now      = utils.NowTimestamp()
	)
	if err = srv.opBefore(ctx); err != nil {
		return err
	}
	if tkInfo, err = dto.NewTicket().TicketStDoneCheck(ctx.Request().Context(), param.TicketId); err != nil {
		return err
	}
	// 批量操作不校验工单处理人
	if !isBatch {
		if tkInfo.Acceptor == "" || tkInfo.Acceptor != operate {
			logger.Errorf(ctx.Request().Context(), "%s ticket acceptor not cur operator. id:%d. tkInfo.Acceptor:%s. op:%s. detail:%+v", fun, param.TicketId, tkInfo.Acceptor, operate, tkInfo)
			return xerrors.New(code.StatusText(code.NoPermission), code.NoPermission)
		}
	}
	dest := map[string]interface{}{
		"status":     uint32(pb.TkStatus_TkStatusProcessing),
		"updated_at": now,
	}
	switch param.OpType {
	case pb.TkTransferOpType_TkTransferOpTypeRejected: // 拒单
		finished = true
		opEvent = pb.TkEvent_TkEventRefused
		dest["status"] = pb.TkStatus_TkStatusDone
		dest["conversion_node"] = pb.TkStage_TkStageAgentRejected
		dest["sort_wait_start_at"] = code.WaitStartAtForFinished
	case pb.TkTransferOpType_TkTransferOpTypeCommuClose: // 回复 & 关单
		finished = true
		opEvent = pb.TkEvent_TkEventCommuClose
		dest["status"] = pb.TkStatus_TkStatusDone
		dest["conversion_node"] = pb.TkStage_TkStageAgentCompleted
		dest["replied"] = 1
		dest["sort_wait_start_at"] = code.WaitStartAtForFinished
	case pb.TkTransferOpType_TkTransferOpTypeCommu: // 只回复
		opEvent = pb.TkEvent_TkEventCommu
		dest["status"] = pb.TkStatus_TkStatusProcessing
		dest["conversion_node"] = pb.TkStage_TkStageAgentReplied
		dest["replied"] = 1
		dest["sort_wait_start_at"] = now
	default:
		return fmt.Errorf("unknown op_type:%d", param.OpType)
	}
	if finished {
		dest["closed"] = uint32(pb.UserRole_ServiceRole)
		dest["closed_at"] = now
		if tkInfo.FirstClosedAt == 0 {
			dest["first_closed_at"] = now
		}
	}
	if tkInfo.FirstReplyAt == 0 {
		dest["first_reply_at"] = now
	}
	dest["last_reply_at"] = now

	lastUserReplyAt, err := dto.NewTicket().GetTicketLastUserOpAt(ctx.Request().Context(), param.TicketId)
	if err != nil {
		return err
	}
	// commu
	if param.OpType == pb.TkTransferOpType_TkTransferOpTypeCommu || param.OpType == pb.TkTransferOpType_TkTransferOpTypeCommuClose {
		if param.GetContent() == "" {
			logger.Errorf(ctx.Request().Context(), "%s transfer content empty. param:%+v", fun, param)
			return xerrors.New(lang.FormatText(ctx, "RespBindParamsFail"), code.InvalidParams)
		} else if strings.Contains(param.Content, "<img src=\"data:") {
			return xerrors.New("请使用图片上传，禁止直接粘贴图片", code.InvalidParams)
		}
		tkCommu = &models.FpOpsTicketsCommu{
			TicketID:  tkInfo.TicketID,
			FromRole:  uint32(pb.UserRole_ServiceRole),
			CommuType: pb.CommuType_CommuTypeDialogue.String(),
			OpType:    uint8(opEvent),
			Detail:    param.GetContent(),
			Operator:  operate,
			CreatedAt: now,
			UpdatedAt: now,
		}
	}
	// 批量操作的一律将工单的当前处理人修改为操作人
	if isBatch {
		dest["acceptor"] = operate
	}
	// history
	his := &models.FpOpsTicketsHistory{
		TicketID:  tkInfo.TicketID,
		Acceptor:  operate,
		Operate:   uint8(opEvent),
		OpRole:    uint8(pb.UserRole_ServiceRole),
		Operator:  operate,
		OpObject:  cast.ToString(lastUserReplyAt),
		Remark:    param.GetContent(),
		CreatedAt: now,
		UpdatedAt: now,
	}

	// save data
	if err := dto.NewTicket().TicketTransfer(ctx.Request().Context(), param.TicketId, dest, tkCommu, his, func(txDb *gorm.DB) error {
		// add sdk notice
		if tkCommu != nil {
			srv.TicketAddNoticeWithHook(ctx.Request().Context(), tkCommu.ID, tkInfo, pb.RedPointTypeEnum_RedPointTypeMessage)
		}
		// update es ticket
		if err := elasticsearch.DefaultTicketSyncSvc.UpdateTicket(ctx.Request().Context(), param.TicketId, dest); err != nil {
			return err
		}

		//
		if param.OpType == pb.TkTransferOpType_TkTransferOpTypeCommu { // 仅回复 - 超时单 设置
			// 根据配置获取过期时间
			ExpireTime, remindTime, err := cfg.GetRefillTimeoutByConfig(ctx, param.TicketId, map[string]string{})
			Overtime := now + ExpireTime*3600
			if err != nil {
				logger.Errorf(ctx.Request().Context(), "%s get ticket expire time err. ticketId:%d. err:%v", fun, param.TicketId, err)
				return err
			}
			if err := rds.RCli.ZAdd(ctx.Request().Context(), keys.TicketTimeWatchPlayerListV3, &redis.Z{
				Score:  float64(Overtime),
				Member: param.TicketId,
			}).Err(); err != nil {
				return err
			}
			// 添加超时回复key
			if remindTime > 0 {
				if err := rds.RCli.ZAdd(ctx.Request().Context(), keys.TicketOvertimeRemarkWatchPlayerList, &redis.Z{
					Score:  float64(Overtime - remindTime*60),
					Member: param.TicketId,
				}).Err(); err != nil {
					return err
				}
			}
		} else if param.OpType == pb.TkTransferOpType_TkTransferOpTypeCommuClose { // 回复+关单 - 重开单
			if tkInfo.ReopenNum > 0 && tkCommu != nil { // 重开单 回填 response_reply_id
				reopenLog := &models.FpOpsTicketsReopen{}
				if err := txDb.Model(reopenLog).Where("ticket_id = ?", tkInfo.TicketID).Last(reopenLog).Error; err != nil {
					return err
				}
				reopenLog.ResponseReplyId = tkCommu.ID
				return txDb.Model(reopenLog).Save(reopenLog).Error
			}
		}
		return nil
	}); err != nil {
		logger.Errorf(ctx.Request().Context(), "%s TicketTransfer return err. ticketId:%d. dest:%+v. err:%v", fun, param.TicketId, dest, err)
		return err
	}
	if param.OpType == pb.TkTransferOpType_TkTransferOpTypeCommuClose {
		cc := utils.ContextOnlyValue{ctx.Request().Context()}
		newC := ctx.Echo().AcquireContext()
		newC.SetRequest(ctx.Request().WithContext(cc))
		newC.Set(cst.AccountInfoCtx, pb.UserRole_SystemRole.String())
		if param.IsSyncAiElfin {
			go func(newCtx context.Context, tkInfo *models.FpOpsTickets) {
				// 问题：默认填充AI总结的玩家提问内容（使用第一人称原语言）
				// 答案：默认填充AI总结的客服回复内容
				// 默认勾选“同步问答数据至AI精灵”，如果勾选了，则该语料入AI精灵筛选库；如果取消勾选则不入库

				claims := sign.NewClaims()
				claims.Time = time.Now().UTC()
				claims.Set("username", "cs_ticket")
				claims.Set("nickname", "cs_ticket")
				claims.Set("prd_id", "cs_ticket")
				claims.Set("game_project", tkInfo.Project)

				apiKey := viper.GetString("auth.admin_gateway")
				token, err := sign.JwtEncode(claims, apiKey)
				if err != nil {
					logger.Errorf(ctx.Request().Context(), "%s sync ai_elfin jwt encode fail. %v", fun, err)
					return
				}

				api := viper.GetString("thirdparty.ops_backend_api.ai_elfin_backend_server_host")

				path := api + "/elfin_egress/chat_log/set_swift_chatlog"
				client := httpclient.New().SetHeader("Authorization", "Bearer "+token)

				req := map[string]interface{}{
					"game_project": tkInfo.Project,
					"lang":         tkInfo.Lang,
					"qst_desc":     param.Question,
					"answer":       param.Answer,
				}
				res, err := client.PostJson(newCtx, path, req)
				if err != nil {
					logger.Errorf(ctx.Request().Context(), "%s sync ai_elfin post err. err:%v, req: %v, ticketId:%d", fun, err, req, tkInfo.TicketID)
					return
				}
				if res.StatusCode != 200 {
					logger.Errorf(ctx.Request().Context(), "%s sync ai_elfin status code not ok. code:%v, ticketId:%d", fun, res.StatusCode, tkInfo.TicketID)
					return
				}
			}(cc, tkInfo)
		} // 回复关单 - 同步AI精灵
		if tkInfo.Project == "mo_global" {
			go func(c echo.Context) {
				communicate.PubTicketClosed(c, param.TicketId, param)
			}(newC)
		}

	}
	return nil
}

func (srv *ticketService) TicketAddNoticeWithHook(ctx context.Context, replyId uint64, tkInfo *models.FpOpsTickets, redType pb.RedPointTypeEnum) {
	defer func() {
		for _, v := range afterNoticeExecHooks {
			if err := v(ctx, replyId, tkInfo, redType); err != nil {
				logger.Errorf(ctx, "afterNoticeExecHooks err: %v", err)
			}
		}
	}()

	rds.TicketAddNotice(ctx, replyId, tkInfo.TicketID, tkInfo.GmID, tkInfo.UUID, tkInfo.AccountID, tkInfo.Scene, redType)
	//todo before hook
}
func (srv *ticketService) TicketDraftSave(ctx echo.Context, req *pb.TicketDraftSaveReq, account string) error {
	return dto.NewTicket().TicketDraftSave(ctx, &models.FpOpsTicketsDraft{
		ID:        req.Id,
		TicketID:  req.TicketId,
		Content:   req.Content,
		Operator:  account,
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	})
}

func (srv *ticketService) TicketDraftInfo(ctx echo.Context, req *pb.TicketDraftInfoReq, account string) (*pb.TicketDraftInfoResp, error) {
	resp := pb.TicketDraftInfoResp{
		TicketId: req.TicketId,
	}
	info, err := dto.NewTicket().Info(ctx.Request().Context(), req.TicketId, account)
	if err != nil {
		return &resp, err
	}
	if info.ID == 0 {
		return &resp, nil
	}
	return &pb.TicketDraftInfoResp{
		Id:         info.ID,
		TicketId:   req.TicketId,
		Content:    info.Content,
		UpdateTime: info.UpdatedAt.Format(code.TimeLayout),
	}, nil
}

//func TicketDraftRemove(ctx echo.Context, ticketId uint64, action pb.TkDraftActionEnum) error {
//	account := ctx.Get(cst.AccountInfoCtx).(string)
//	if account == "" {
//		return xerrors.New(code.StatusText(code.MissingParams), code.MissingParams)
//	}
//	return dto.NewTicketDraft(ctx).Remove(ctx.Request().Context(), ticketId, uint8(action), account)
//}

// PlayerCommuTimeOutLogic 单一工单玩家超过时间未回复判断和处理
func (srv *ticketService) PlayerCommuTimeOutLogic(ctx echo.Context, ticketId uint64) (result pb.PlayerCommuWatchResult) {
	result.TicketId = ticketId
	tkDetail, err := dto.NewTicket().GetTicketInfoFromMaster(ctx.Request().Context(), ticketId)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			result.NeedDelWatchKey = true
			return
		}
		result.Error = err
		return
	}
	// to do logic
	result.NeedDelWatchKey = true
	if tkDetail.ConversionNode != uint32(pb.TkStage_TkStageAgentReplied) { // 已变更状态，不需要设置超时未回复
		return
	}
	if tkDetail.Priority == 1 { // 升级单 - 不做 3day 超时逻辑
		return
	}
	// todo add 超时逻辑
	now := utils.NowTimestamp()
	dest := map[string]interface{}{
		"status":             uint32(pb.TkStatus_TkStatusDone),
		"closed":             uint8(pb.UserRole_SystemRole),
		"conversion_node":    uint32(pb.TkStage_TkStageAgentResolved),
		"sort_wait_start_at": code.WaitStartAtForFinished,
		"closed_at":          now,
		"updated_at":         now,
	}
	if tkDetail.FirstClosedAt == 0 {
		dest["first_closed_at"] = now
	}
	repo := dto.NewTicket()
	if err := repo.TicketPropsUpdate(ctx.Request().Context(), tkDetail.TicketID, dest, func(txDb *gorm.DB) error {
		if err := elasticsearch.DefaultTicketSyncSvc.UpdateTicket(ctx.Request().Context(), tkDetail.TicketID, dest); err != nil {
			return err
		}
		hist := &models.FpOpsTicketsHistory{
			TicketID:  tkDetail.TicketID,
			Acceptor:  tkDetail.Acceptor,
			Operate:   uint8(pb.TkEvent_TkEventUserNoAnsTimout),
			OpRole:    uint8(pb.UserRole_SystemRole),
			Operator:  "system",
			CreatedAt: now,
			UpdatedAt: now,
		}
		if err := repo.AddTicketHistory(ctx.Request().Context(), txDb, hist); err != nil {
			return err
		}
		// 删除超时红点
		if err := rds.TicketDelNotice(ctx.Request().Context(), tkDetail.GmID, tkDetail.UUID, tkDetail.AccountID, tkDetail.Scene, ticketId, pb.RedPointTypeEnum_RedPointTypeOvertime); err != nil {
			return err
		}
		return nil
	}); err != nil {
		result.Error = err
		return
	}

	return
}

// AutoAllocAcceptor 自动分配处理人
func (srv *ticketService) AutoAllocAcceptor(ctx echo.Context) error {
	fun := "ticketService.AutoAllocAcceptor"
	// get lock

	lock, err := rds.GetRdsMux(ctx.Request().Context(), keys.TicketAutoAllocUniqLockV3)
	if err != nil {
		if errors.Is(err, redislock.ErrNotObtained) {
			logger.Infof(ctx.Request().Context(), "%s auto alloc get lock fail. %v", fun, err)
			return nil
		}
		return err
	}
	logger.Infof(ctx.Request().Context(), "%s auto alloc get lock success. ", fun)
	refreshC := make(chan struct{})
	defer lock.Release(ctx.Request().Context())
	go rds.RefreshRdsMux(ctx.Request().Context(), lock, refreshC)
	defer close(refreshC)

	// do logic
	fx.From(func(source chan<- any) { // get all game_project
		projects, _err := dto.NewTicket().GetAllProjects(ctx.Request().Context())
		if _err != nil {
			err = _err
		}
		for _, project := range projects {
			if project == "" {
				continue
			}
			source <- project
		}
		return
	}).Map(func(item any) any {
		project, ok := item.(string)
		if ok != true || project == "" {
			return ""
		}
		// do logic
		if err := srv.autoAllocSingleProject(ctx, project); err != nil {
			logger.Errorf(ctx.Request().Context(), "%s autoAllocSingleProject return err. project :%s. err:%v", fun, project, err)
		}
		return project
	}, fx.WithWorkers(1)).Reduce(func(pipe <-chan any) (any, error) {
		for v := range pipe {
			project, ok := v.(string)
			logger.Infof(ctx.Request().Context(), "%s mapReduce finished. project:%s. ok:%v", fun, project, ok)
		}
		return "", nil
	})
	return err
}

func (srv *ticketService) autoAllocSingleProject(ctx echo.Context, project string) error {
	var (
		fun    = "ticketService.autoAllocSingleProject"
		tkRepo = dto.NewTicket()

		// sort current group_user
		sortUser = func(gUser []*models.ProjectUserDf) []*models.ProjectUserDf {
			ngUser := make([]*models.ProjectUserDf, 0, len(gUser))
			for _, u := range gUser {
				if u.UpperLimit > u.CurrDoingTkNum {
					ngUser = append(ngUser, u)
				}
			}
			sort.SliceStable(ngUser, func(i, j int) bool {
				if ngUser[i].CurrDoingTkNum != ngUser[j].CurrDoingTkNum { // 处理中 - 少的先分配
					return ngUser[i].CurrDoingTkNum < ngUser[j].CurrDoingTkNum
				} else { // 数量相同 - 时间最常先分配
					return ngUser[i].LastAllocTkAt < ngUser[j].LastAllocTkAt
				}
			})
			return ngUser
		}

		// check 当前工单是否可以自动分配给该用户
		checkSingleCanAlloc = func(tkDetail *models.FpOpsTickets, u *models.ProjectUserDf) (bool, error) {
			tkSystemTagIDs := make([]uint32, 0, len(tkDetail.SystemTags))
			for _, tag := range tkDetail.SystemTags {
				tkSystemTagIDs = append(tkSystemTagIDs, tag.LabelID)
			}
			if len(tkSystemTagIDs) == 0 {
				tkSystemTagIDs = append(tkSystemTagIDs, 12)
			}
			//  game + lang + category 必须匹配
			for _, v := range u.GameCat {
				if v.Game == tkDetail.Project && utils.InArrayAny(tkDetail.Lang, u.Lang) {
					if len(v.Categories) == 0 || utils.InArrayAny(fmt.Sprintf("%d", tkDetail.CatID), v.Categories) {
						if len(v.SystemTags) == 0 || utils.IsSubset(tkSystemTagIDs, v.SystemTags) {
							// 2. 当前用户未因超时分配
							hists, err := tkRepo.GetTicketHists(ctx.Request().Context(), tkDetail.TicketID, pb.TkEvent_TkEventNoOpTimeout)
							if err != nil {
								return false, err
							}
							if len(hists) > 0 && hists[len(hists)-1].Remark == u.User {
								return false, nil
							}
							// 3. 过滤重开单
							if tkDetail.AutoReplyID > 0 {
								return false, nil
							}
							// 可以分配
							return true, nil
						}
					}
				}
			}
			// 如果没有匹配
			return false, nil
		}
	)

	// 获取所有待处理数据
	tickets, err := tkRepo.GetAllPendingTickets(ctx.Request().Context(), project)
	if err != nil {
		return err
	}

	// 2. 计算每个工单的优先级分数
	sortItems := make(TicketSortItems, 0, len(tickets))
	for _, ticket := range tickets {
		score := srv.calculateTicketPriority(ctx, ticket)
		sortItems = append(sortItems, &TicketSortItem{
			Ticket: ticket,
			Score:  score,
		})
	}

	// 3. 按优先级排序
	// 3. 使用 sort.SliceStable 进行分层排序
	sort.SliceStable(sortItems, func(i, j int) bool {
		// 先比较优先级分数 (降序，分数高的优先)
		if sortItems[i].Score != sortItems[j].Score {
			return sortItems[i].Score > sortItems[j].Score
		}
		// 如果优先级分数相同，则比较创建时间 (升序，时间早的优先)
		if sortItems[i].Ticket.CreatedAt != sortItems[j].Ticket.CreatedAt {
			return sortItems[i].Ticket.CreatedAt < sortItems[j].Ticket.CreatedAt // 时间戳小的 (早的) 优先
		}
		// 如果优先级和时间都相同，保持原始相对顺序
		return false
	})

	// 4. 获取排序后的工单列表
	sortedTickets := make([]*models.FpOpsTickets, len(sortItems))
	for i, item := range sortItems {
		sortedTickets[i] = item.Ticket
	}

	// 获取对应游戏的用户
	users, err := dto.NewUserAssignTicketRepo().ProjectUser(ctx.Request().Context(), project, true)
	if len(users) == 0 {
		// return fmt.Errorf("current project user empty. %s", project)
		logger.Info(ctx.Request().Context(), "current project user empty.", logger.Any("project", project), logger.String("fun", fun))
		return nil
	}
	acceptors := make([]string, 0)
	for _, u := range users {
		acceptors = append(acceptors, u.User)
	}
	accs, err := tkRepo.GetDoingTicketsNum(ctx.Request().Context(), acceptors)
	if err != nil {
		return err
	}
	for ac, num := range accs {
		for idx, u := range users {
			if u.User == ac {
				users[idx].CurrDoingTkNum = int32(num)
			}
		}
	}

	// 执行分单逻辑
	for _, tkInfo := range sortedTickets {
		users = sortUser(users)
		since := time.Now()
		logger.Info(ctx.Request().Context(), "start doing autoAllocAcceptor. ", zap.Any("fun", fun), zap.Any("ticketInfo", tkInfo), zap.Any("current_users", users))
		tkInfo, err := tkRepo.GetTicketSystemTagFromMaster(ctx.Request().Context(), tkInfo.TicketID)
		if err != nil {
			logger.Errorf(ctx.Request().Context(), "%s repo.GetTicketInfoFromMaster return err. tkId:%d. err:%v", fun, tkInfo.TicketID, err)
			continue
		}
		if tkInfo.Acceptor != "" || tkInfo.Status != uint32(pb.TkStatus_TkStatusUntreated) {
			logger.Warn(ctx.Request().Context(), "repo.GetTicketInfoFromMaster ticket status is not Untreated", zap.Any("fun", fun), zap.Any("ticketDetail", tkInfo))
			continue
		}

		for _idx, user := range users {
			if can, err := checkSingleCanAlloc(tkInfo, user); err == nil && can {
				// 系统自动分配处理人
				if err := srv.autoAllocAcceptor(ctx, tkInfo, user.User); err != nil {
					logger.Errorf(ctx.Request().Context(), "%s autoAllocAcceptor return err. err:%v. cost:%+v. tkInfo:%+v. user:%+v", fun, err, time.Since(since), tkInfo, user)
				} else {
					users[_idx].CurrDoingTkNum++
					users[_idx].LastAllocTkAt = utils.NowTimestamp()
					logger.Info(ctx.Request().Context(), "autoAllocAcceptor success. end", zap.Any("fun", fun),
						zap.Any("ticketInfo", tkInfo), zap.Any("acceptor", user.User), zap.Any("cost", time.Since(since)),
						zap.Any("after_cur_user", users[_idx]))
				}
				break
			}
		}
	}
	return nil
}

// 计算工单优先级分数
func (srv *ticketService) calculateTicketPriority(ctx echo.Context, ticket *models.FpOpsTickets) int64 {
	var score int64 = 0
	// 每级2倍减少 确保优先级高的工单优先分配

	// 1. 绿色通道判断 (权重最高: 50000)
	if srv.isGreenChannel(ticket) {
		score += 100000
	}

	// 2. VIP身份判断 (权重: 40000)
	if ticket.UserType == int8(pb.UserTypeEnum_UserTypeLongVipUser) ||
		ticket.UserType == int8(pb.UserTypeEnum_UserTypeLimitTimeUser) {
		score += 50000
	}

	// 3. 权益卡判断 (权重: 30000)
	if srv.hasPrivilegeCard(ticket) {
		score += 25000
	}

	// 4. R级判断 (权重: 20000)
	// R级越高，分数越高，假设R级范围是1-5
	if ticket.ZoneVipLevel > 0 {
		score += int64(10000 + ticket.ZoneVipLevel*100)
	}

	// 5. 时间判断 (权重最低，使用创建时间的时间戳)， 时间不参与权重分，在最外层排序的时候使用
	// score += int64(time.Now().Unix() - int64(ticket.CreatedAt))

	return score
}

// 判断是否是绿色通道
func (srv *ticketService) isGreenChannel(ticket *models.FpOpsTickets) bool {
	// 实现绿色通道判断逻辑
	if ticket.SystemTags != nil && len(ticket.SystemTags) > 0 {
		for _, tag := range ticket.SystemTags {
			if tag.LabelID == uint32(pb.TicketSystemTag_GreenChannelUser) {
				return true
			}
		}
	}
	return false
}

// 判断是否使用了权益卡
func (srv *ticketService) hasPrivilegeCard(ticket *models.FpOpsTickets) bool {
	// 实现权益卡判断逻辑
	if ticket.SystemTags != nil && len(ticket.SystemTags) > 0 {
		for _, tag := range ticket.SystemTags {
			if tag.LabelID == uint32(pb.TicketSystemTag_PrivateZoneCard) {
				return true
			}
		}
	}
	return false
}

type TicketSortItem struct {
	Ticket *models.FpOpsTickets
	Score  int64 // 用于排序的分数
}

// 实现排序接口
type TicketSortItems []*TicketSortItem

func (srv *ticketService) retryCreateESTicket(ticketId uint64) {
	time.Sleep(time.Second)
	fun := "retryCreateESTicket "
	ctx, cancel := context.WithCancel(context.TODO())
	defer cancel()
	time.Sleep(2 * time.Second)
	tkObj := &models.FpOpsTickets{}
	db := database.Db()
	err := db.WithContext(ctx).Model(&models.FpOpsTickets{}).
		Preload("Device").Preload("Fields").
		Preload("Tags").Preload("Tags.TagItem").
		Where("ticket_id = ?", ticketId).First(&tkObj).Error
	if err != nil {
		logger.Errorf(ctx, "%s get ticket detail return err. err:%s", fun, err)
		return
	}
	logger.Infof(ctx, "%s retrieved ticket details: %+v", fun, tkObj)
	elasticsearch.DefaultTicketSyncSvc.CreateTicket(ctx, tkObj)
	reporter.PubCreate(ctx, tkObj)
}

// autoAllocAcceptor 执行单一工单分配
func (srv *ticketService) autoAllocAcceptor(ctx echo.Context, tkInfo *models.FpOpsTickets, acceptor string) error {
	fun := "ticketService.autoAllocAcceptor"

	dest := map[string]interface{}{
		"acceptor":        acceptor,
		"status":          uint32(pb.TkStatus_TkStatusProcessing),
		"conversion_node": uint32(pb.TkStage_TkStageNewForAgent),
		"updated_at":      time.Now().Unix(),
	}

	if err := dto.NewTicket().TicketPropsUpdate(ctx.Request().Context(), tkInfo.TicketID, dest, func(txDb *gorm.DB) error {
		// save es
		if _err := elasticsearch.DefaultTicketSyncSvc.UpdateTicket(ctx.Request().Context(), tkInfo.TicketID, dest); _err != nil {
			if elastic.IsNotFound(_err) {
				logger.Errorf(ctx.Request().Context(), "UpdateTicket failed. ticketId:%d not found. err:%v", tkInfo.TicketID, _err)
				// 补偿同步es
				go srv.retryCreateESTicket(tkInfo.TicketID)
			} else {
				logger.Errorf(ctx.Request().Context(), "UpdateTicket return err. ticketId:%d. err:%v", tkInfo.TicketID, _err)
				return _err
			}
		}
		// save history log
		if _, _err := reporter.AutoAlloc(ctx.Request().Context(), tkInfo.TicketID, tkInfo.ConversionNode, acceptor); _err != nil {
			return _err
		}
		// save acceptor last alloc time
		if _err := dto.NewGroupRepo().UserAssignedTicket(ctx.Request().Context(), txDb, acceptor); _err != nil {
			return _err
		}
		return nil
	}); err != nil {
		logger.Errorf(ctx.Request().Context(), "%s TicketPropsUpdate return err. ticketId:%d. acceptor:%s dest:%+v. err:%v", fun, tkInfo.TicketID, acceptor, dest, err)
		return err
	}

	defer func() {
		if err := rds.RCli.ZAdd(ctx.Request().Context(), keys.TicketAcceptor2HNoActionListV3, &redis.Z{
			Score: float64(time.Now().Unix() + cfg.GetAllocNoOpTimeout()), Member: tkInfo.TicketID,
		}).Err(); err != nil {
			logger.Errorf(ctx.Request().Context(), "%s add acceptor alloc watch list. err:%v. ticketId:%d. acceptor:%s", fun, tkInfo.TicketID, acceptor)
		}
	}()
	return nil
}

// Over2HNoActionLogic 工单被分配人 2 个小时内未处理
func (srv *ticketService) Over2HNoActionLogic(ctx context.Context, ticketId uint64) (result pb.PlayerCommuWatchResult) {
	fun := "ticketService.Over2HNoActionLogic"
	result.TicketId = ticketId
	if ticketId == 0 {
		result.Error = fmt.Errorf("ticketId eq0 :%d", ticketId)
		return
	}

	// tk detail
	tkDetail, err := dto.NewTicket().GetTicketInfoFromMaster(ctx, ticketId)
	if err != nil {
		result.Error = err
		return
	}
	if tkDetail.TicketID > 0 && tkDetail.Acceptor == "" { // 玩家回复后 - 客服长时间未回复
		logger.Infof(ctx, "%s current ticket detail acceptor is empty. continue ticketId:%d", fun, ticketId)
		result.NeedDelWatchKey = true
		return
	}

	if !utils.InArrayAny(tkDetail.ConversionNode, workflow.TkCanGcAcceptorStage) { // check 当前工单状态 - 是否允许回收
		logger.Infof(ctx, "%s current ticket detail stage forbidden gc acceptor. continue ticketId:%d", fun, ticketId)
		result.NeedDelWatchKey = true
		return
	}
	if tkDetail.Priority == 1 { // 升级单 - 不做 2h 超时回收处理人逻辑
		logger.Infof(ctx, "%s current ticket detail is upgrade ticket. continue ticketId:%d", fun, ticketId)
		result.NeedDelWatchKey = true
		return
	}

	// check
	cout, err := dto.NewTicket().CheckLastAllocUserNoActionOverTm(ctx, ticketId, cfg.GetAllocNoOpTimeoutCheck()) // 临时方式，后期改回 GetAllocNoOpTimeout
	if err != nil {
		result.Error = err
		return
	}
	if cout > 0 {
		result.NeedDelWatchKey = true
		return
	}

	now := utils.NowTimestamp()
	// 执行取消执行人动作
	dest := map[string]interface{}{
		"acceptor":        "",
		"status":          uint32(pb.TkStatus_TkStatusUntreated),
		"conversion_node": uint32(pb.TkStage_TkStageNew),
		"updated_at":      now,
	}

	hist := &models.FpOpsTicketsHistory{
		TicketID:  ticketId,
		Acceptor:  "",
		Operate:   uint8(pb.TkEvent_TkEventNoOpTimeout),
		OpDetail:  tkDetail.ConversionNode,
		OpRole:    uint8(pb.UserRole_SystemRole),
		Remark:    tkDetail.Acceptor,
		Operator:  "system",
		CreatedAt: now,
		UpdatedAt: now,
	}

	if err := dto.NewTicket().TicketPropsUpdate(ctx, ticketId, dest, func(txDb *gorm.DB) error {
		// save es
		if _err := elasticsearch.DefaultTicketSyncSvc.UpdateTicket(ctx, ticketId, dest); _err != nil {
			return _err
		}
		// save history log
		if _err := dto.NewTicket().AddTicketHistory(ctx, txDb, hist); _err != nil {
			return _err
		}
		return nil
	}); err != nil {
		logger.Errorf(ctx, "%s TicketPropsUpdate return err. ticketId:%d. dest:%+v. err:%v", fun, ticketId, dest, err)
		result.Error = err
		return
	}
	// 确认未处理&已回收
	result.NeedDelWatchKey = true
	return
}

// SystemReply 自动回复
func SystemReply(c echo.Context, ticketId uint64, catId, replyTplId uint32, lang string, useNewDb bool) error {
	logger.Info(c.Request().Context(), "start create system reply",
		zap.Uint64("ticket_id", ticketId), zap.Uint32("cat_id", catId), zap.Uint32("reply_tpl_id", replyTplId))
	if replyTplId == 0 {
		logger.Info(c.Request().Context(), "replyTplId=0")
		return nil
	}
	replyTplLang, err := dto.NewReplyTplLang(c, useNewDb).TplInfoMultiLang(c.Request().Context(), replyTplId, lang)
	if err != nil {
		logger.Error(c.Request().Context(), "get reply tpl lang info error",
			zap.Uint64("ticket_id", ticketId),
			zap.Uint32("cat_id", catId),
			zap.Uint32("reply_tpl_id", replyTplId),
			zap.String("lang", lang),
			zap.String("err", err.Error()))
		return err
	}
	tpReq := &pb.TicketTransferReq{
		TicketId: ticketId,
		OpType:   pb.TkTransferOpType_TkTransferOpTypeCommuClose,
		Content:  replyTplLang.ReplyContent,
	}
	if err = NewTicketSrv().TicketTransfer(c, tpReq, false); err != nil {
		logger.Error(c.Request().Context(), "create system reply error",
			zap.Uint64("ticket_id", ticketId), zap.String("err", err.Error()))
	}
	return err
}

func (srv *ticketService) TicketBatchReTags(ctx echo.Context, req *pb.TicketRetaggingBatchReq) error {
	fun := "ticketService.TicketReTagsBatch"
	operator := ctx.Get(cst.AccountInfoCtx).(string)
	var wg sync.WaitGroup
	errChan := make(chan error, len(req.TicketIds))

	// 动态并发数控制，工单数量不超过一定上限的并发数
	maxConcurrency := 50
	concurrency := len(req.TicketIds)
	if concurrency > maxConcurrency {
		concurrency = maxConcurrency
	}
	sem := make(chan struct{}, concurrency)

	for _, ticketID := range req.TicketIds {
		wg.Add(1)
		go func(ticketID uint64) {
			defer wg.Done()
			// 占用一个通道位置，限制并发数
			sem <- struct{}{}
			// 释放通道位置
			defer func() {
				<-sem
			}()
			// 单个工单打标签业务逻辑
			if err := srv.processSingleTicket(ctx.Request().Context(), ticketID, req.LabelId, operator); err != nil {
				// 将ticketID包含在错误中
				errChan <- fmt.Errorf("ticketID %d: %w", ticketID, err)
			}
		}(ticketID)
	}

	wg.Wait()
	close(errChan)

	// 收集错误信息
	var errs []error
	for err := range errChan {
		errs = append(errs, err)
	}

	// 返回包含所有错误的汇总信息
	if len(errs) > 0 {
		return fmt.Errorf("%s errors: %v", fun, errs)
	}
	return nil
}

func (srv *ticketService) processSingleTicket(ctx context.Context, ticketID uint64, labelIDs []uint32, operator string) error {
	// 1. 获取工单的当前标签列表
	oldTags, err := dto.NewTicket().GetTicketTags(ctx, ticketID)
	if err != nil {
		logger.Errorf(ctx, "processSingleTicket GetTicketTags return err. err:%v. ticketID:%d", err, ticketID)
		return err
	}
	// 2. 比较标签，生成添加和删除的标签列表
	_, addTags := compareGenDiffTag(oldTags, labelIDs)
	// 3. 保存到数据库并同步至ES
	if err = dto.NewTicket().BatchSaveTicketTags(ctx, ticketID, addTags, operator); err != nil {
		logger.Errorf(ctx, "processSingleTicket BatchSaveTicketTags return err. err:%v. ticketID:%d", err, ticketID)
		return err
	}
	return nil
}

func (srv *ticketService) TicketBatchTransfer(ctx echo.Context, param *pb.TicketBatchTransferReq) error {
	var (
		fun         = "ticketService.TicketBatchTransfer"
		wg          sync.WaitGroup
		errChan     = make(chan error, len(param.TicketIds))
		validStages = map[uint32]bool{
			uint32(pb.TkStage_TkStageNew):             true,
			uint32(pb.TkStage_TkStageNewForAgent):     true,
			uint32(pb.TkStage_TkStageAgentReplied):    true,
			uint32(pb.TkStage_TkStageWaitingForAgent): true,
			uint32(pb.TkStage_TkStageAgentReopen):     true,
		}
	)
	// 可根据系统资源情况调整并发任务数
	maxConcurrency := 50
	concurrency := len(param.TicketIds)
	if concurrency > maxConcurrency {
		concurrency = maxConcurrency
	}
	sem := make(chan struct{}, concurrency)
	for _, ticketId := range param.TicketIds {
		wg.Add(1)
		go func(tk uint64) {
			defer wg.Done()
			// 占用一个通道位置，限制并发数
			sem <- struct{}{}
			// 释放通道位置
			defer func() {
				<-sem
			}()
			// 验证工单是否可以操作
			if err := srv.validateTicketForBatchOp(ctx, tk, validStages); err != nil {
				// 将ticketID包含在错误中
				errChan <- err
				return
			}
			// 调用单个工单的操作逻辑
			transferReq := &pb.TicketTransferReq{
				TicketId:      tk,
				OpType:        param.OpType,
				Content:       param.Content,
				IsSyncAiElfin: param.IsSyncAiElfin,
				Question:      param.Question,
				Answer:        param.Answer,
			}
			if err := srv.TicketTransfer(ctx, transferReq, true); err != nil {
				// 将ticketID包含在错误中
				errChan <- fmt.Errorf("ticketID %d: %w", tk, err)
				return
			}
		}(ticketId)
	}
	wg.Wait()
	close(errChan)
	// 收集错误信息
	var errs []error
	for err := range errChan {
		errs = append(errs, err)
	}
	// 返回包含所有错误的汇总信息
	if len(errs) > 0 {
		return fmt.Errorf("%s errors: %v", fun, errs)
	}
	return nil
}

func (srv *ticketService) validateTicketForBatchOp(ctx echo.Context, ticketId uint64, validStages map[uint32]bool) error {
	tkInfo, err := dto.NewTicket().GetTicketInfoFromMaster(ctx.Request().Context(), ticketId)
	if err != nil {
		return err
	}
	if _, ok := validStages[tkInfo.ConversionNode]; !ok {
		return fmt.Errorf("工单 %d 状态 %v 不允许批量操作", ticketId, tkInfo.ConversionNode)
	}
	return nil
}

func CrmVip(ctx echo.Context, project string, uid, fpid int64, gameId uint32, fpxAppId, accountId string) models.TicketUser {
	c := ctx.Request().Context()
	defaultTicketUser := models.TicketUser{
		VipCrm:         -1,
		VipCrmPriority: -1,
	}
	api := viper.GetString("thirdparty.ops_backend_api.crm_ops_info")
	if api == "" {
		return defaultTicketUser
	}

	claims := sign.NewClaims()
	claims.Time = time.Now().UTC()
	claims.Set("username", "cs_ticket")
	claims.Set("nickname", "cs_ticket")
	claims.Set("admin", false)
	claims.Set("prd_id", "cs_ticket")
	claims.Set("game_project", project)
	auth := viper.GetString("thirdparty.ops_backend_api.crm_auth_key")
	fmt.Println("auth:   ", auth)
	token, _ := sign.JwtEncode(claims, auth)

	client := httpclient.New()
	client.SetHeader(echo.HeaderAuthorization, "Bearer "+token)

	logger.Info(c, "CrmVip api call start",
		zap.Int64("uid", uid), zap.Int64("fpid", fpid),
		zap.Uint32("game_id", gameId), zap.String("fpx_app_id", fpxAppId),
		zap.String("account_id", accountId))
	if fpxAppId == "" && gameId > 0 {
		fpxAppId = cast.ToString(gameId)
	}
	if accountId == "" && fpid > 0 {
		accountId = cast.ToString(fpid)
	}
	ret, err := client.PostJson(c, api, map[string]interface{}{
		"uid":        uid,
		"account_id": accountId,
		"game_id":    fpxAppId,
	})
	if err != nil {
		logger.Info(c, "CrmVip api call error", zap.Error(err))
		return defaultTicketUser
	}
	respBody := ret.String()
	logger.Info(c, "CrmVip api call success", zap.String("data", respBody))
	jsonBody := gjson.Parse(respBody)
	var crmVip, crmVipPriority, crmVipType, svip int8
	var totalPay float64
	var channel string
	var userId int64
	if jsonBody.Get("data.uid").Exists() {
		userId = jsonBody.Get("data.uid").Int()
	}
	if jsonBody.Get("data.maintain_mode").Exists() {
		if jsonBody.Get("data.maintain_mode").Int() == 2 {
			// 已维护
			crmVip = 1
		}
	}
	if jsonBody.Get("data.vip_priority").Exists() {
		crmVipPriority = int8(jsonBody.Get("data.vip_priority").Int())
	}
	if jsonBody.Get("data.vip_type").Exists() {
		crmVipType = int8(jsonBody.Get("data.vip_type").Int()) // vip类型 1默认为非vip; 2:长期vip; 3:限时vip; 4:过期
	}
	if jsonBody.Get("data.svip").Exists() {
		svip = int8(jsonBody.Get("data.svip").Int())
		if svip != int8(pb.SVIP_SVIPYes) {
			svip = int8(pb.SVIP_SVIPNo)
		}
	}
	if jsonBody.Get("data.pay_total").Exists() {
		totalPay = jsonBody.Get("data.pay_total").Float()
	}
	if jsonBody.Get("data.channel").Exists() {
		channel = jsonBody.Get("data.channel").String()
	}
	userType := int8(pb.UserTypeEnum_UserTypeRegularUser)
	if crmVipType == 2 {
		userType = int8(pb.UserTypeEnum_UserTypeLongVipUser)
	} else if crmVipType == 3 {
		userType = int8(pb.UserTypeEnum_UserTypeLimitTimeUser)
	} else if totalPay > 0 {
		userType = int8(pb.UserTypeEnum_UserTypePaidUser)
	}
	return models.TicketUser{
		Uid:            userId,
		AccountID:      accountId,
		VipCrm:         crmVip,
		VipCrmPriority: crmVipPriority,
		VipType:        uint16(crmVipType),
		UserType:       userType,
		SVIP:           svip,
		TotalPay:       totalPay,
		Channel:        channel,
	}
}

func DwhUserInfo(ctx echo.Context, project string, uid int64) (accountID string, totalPay float64, channel string) {
	c := ctx.Request().Context()
	start := time.Now()

	api := viper.GetString("thirdparty.ops_backend_api.dwh_ops_info")
	if api == "" {
		return
	}

	client := httpclient.New()

	gameName := project
	projectArr := strings.Split(project, "_")
	if len(projectArr) >= 2 {
		gameName = projectArr[0]
	}
	ret, err := client.Post(c, api+fmt.Sprintf("?uid=%d&game_name=%s", uid, gameName), &httpclient.FormParams{}, nil)
	if err != nil {
		logger.Error(c, "DwhUserInfo api call error", zap.Error(err), zap.Int64("uid", uid), zap.String("project", project), zap.String("game_name", gameName), zap.Int64("cost", time.Since(start).Milliseconds()))
		return
	}
	respBody := ret.String()
	logger.Info(c, "DwhUserInfo api call success", zap.String("data", respBody), zap.Int64("cost", time.Since(start).Milliseconds()))
	jsonBody := gjson.Parse(respBody)
	if jsonBody.Get("errmsg").Str != "success" {
		return
	}
	if jsonBody.Get("data").IsArray() {
		if len(jsonBody.Get("data").Array()) > 0 {
			var dwhData models.DwhData
			s := jsonBody.Get("data").Array()[0].String()
			ss := jsonBody.Get("data").Array()
			fmt.Println(s, ss)
			err := json.Unmarshal([]byte(jsonBody.Get("data").Array()[0].String()), &dwhData)
			if err != nil {
				return
			}
			return dwhData.AccountID, cast.ToFloat64(dwhData.TotalRevenueUSD), dwhData.PackageChannel
		}
	}
	return
}

// PlayerOverTimeRemindLogic 执行超时红点
func (srv *ticketService) PlayerOverTimeRemindLogic(ctx echo.Context, ticketId uint64) (result pb.PlayerCommuWatchResult) {
	result.TicketId = ticketId
	tkDetail, err := dto.NewTicket().GetTicketInfoFromMaster(ctx.Request().Context(), ticketId)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			result.NeedDelWatchKey = true
			return
		}
		result.Error = err
		return
	}
	// to do logic
	result.NeedDelWatchKey = true
	if tkDetail.ConversionNode != uint32(pb.TkStage_TkStageAgentReplied) { // 已变更状态，不需要设置超时未回复
		return
	}
	if tkDetail.Priority == 1 { // 升级单 - 不做 3day 超时逻辑
		return
	}
	// 添加红点
	rds.TicketAddNotice(ctx.Request().Context(), 0, ticketId, tkDetail.GmID, tkDetail.UUID, tkDetail.AccountID, tkDetail.Scene, pb.RedPointTypeEnum_RedPointTypeOvertime)
	return
}

// TicketPublicTag 获取公共标签
func (srv *ticketService) TicketPublicTag(ctx echo.Context, req *pb.TicketPublicTagReq) (*pb.TicketPublicTagResp, error) {
	fun := "ticketService.TicketPublicTag"
	// 获取公共标签
	tags, err := dto.NewTicket().GetPublicTags(ctx.Request().Context(), req.TicketIds)
	if err != nil {
		logger.Errorf(ctx.Request().Context(), "%s GetPublicTags return err. err:%v", fun, err)
		return nil, err
	}
	return &pb.TicketPublicTagResp{Tags: tags}, nil
}

// TicketBatchDeleteTags 批量删除标签
func (srv *ticketService) TicketBatchDeleteTags(ctx echo.Context, req *pb.TicketTagBatchDelete) error {
	fun := "ticketService.TicketBatchDeleteTags"
	operator := ctx.Get(cst.AccountInfoCtx).(string)
	var wg sync.WaitGroup
	errChan := make(chan error, len(req.TicketIds))

	// 动态并发数控制，工单数量不超过一定上限的并发数
	maxConcurrency := 50
	concurrency := len(req.TicketIds)
	if concurrency > maxConcurrency {
		concurrency = maxConcurrency
	}
	sem := make(chan struct{}, concurrency)

	for _, ticketID := range req.TicketIds {
		wg.Add(1)
		go func(ticketID uint64) {
			defer wg.Done()
			// 占用一个通道位置，限制并发数
			sem <- struct{}{}
			// 释放通道位置
			defer func() {
				<-sem
			}()
			// 单个工单打标签业务逻辑
			if err := srv.processSingleDeleteTicket(ctx.Request().Context(), ticketID, req.TagIds, operator); err != nil {
				// 将ticketID包含在错误中
				errChan <- fmt.Errorf("ticketID %d: %w", ticketID, err)
			}
		}(ticketID)
	}

	wg.Wait()
	close(errChan)

	// 收集错误信息
	var errs []error
	for err := range errChan {
		errs = append(errs, err)
	}

	// 返回包含所有错误的汇总信息
	if len(errs) > 0 {
		return fmt.Errorf("%s errors: %v", fun, errs)
	}
	return nil
}

func (srv *ticketService) processSingleDeleteTicket(ctx context.Context, ticketID uint64, labelIDs []uint32, operator string) error {
	// 3. 保存到数据库并同步至ES
	if err := dto.NewTicket().BatchDeleteTicketTags(ctx, ticketID, labelIDs, operator); err != nil {
		logger.Errorf(ctx, "processSingleTicket BatchSaveTicketTags return err. err:%v. ticketID:%d", err, ticketID)
		return err
	}
	return nil
}

// TicketBatchRemark 工单备注
func (srv *ticketService) TicketBatchRemark(ctx echo.Context, req *pb.TicketBatchRemarkReq) error {
	fun := "ticketService.TicketBatchRemark"
	operator := ctx.Get(cst.AccountInfoCtx).(string)
	var wg sync.WaitGroup
	errChan := make(chan error, len(req.TicketIds))

	// 动态并发数控制，工单数量不超过一定上限的并发数
	maxConcurrency := 50
	concurrency := len(req.TicketIds)
	if concurrency > maxConcurrency {
		concurrency = maxConcurrency
	}
	sem := make(chan struct{}, concurrency)

	for _, ticketID := range req.TicketIds {
		wg.Add(1)
		go func(ticketID uint64) {
			defer wg.Done()
			// 占用一个通道位置，限制并发数
			sem <- struct{}{}
			// 释放通道位置
			defer func() {
				<-sem
			}()
			if err := srv.processSingleTicketRemark(ctx, ticketID, req.Content, operator); err != nil {
				// 将ticketID包含在错误中
				errChan <- fmt.Errorf("ticketID %d: %w", ticketID, err)
			}
		}(ticketID)
	}

	wg.Wait()
	close(errChan)

	// 收集错误信息
	var errs []error
	for err := range errChan {
		errs = append(errs, err)
	}

	// 返回包含所有错误的汇总信息
	if len(errs) > 0 {
		return fmt.Errorf("%s errors: %v", fun, errs)
	}
	return nil
}

func (srv *ticketService) processSingleTicketRemark(ctx echo.Context, ticketID uint64, content, operator string) error {
	// 3. 保存到数据库并同步至ES
	param := &pb.TicketRemarkReq{
		TicketId: ticketID,
		Content:  content,
	}
	err := dto.NewTicket().TicketRemark(ctx.Request().Context(), param, cast.ToString(ctx.Get(cst.AccountInfoCtx)))
	if err != nil {
		return err
	}
	return nil
}

// TicketNewAiProcess 新版ai客服处理流程
func (srv *ticketService) TicketNewAiProcess(ctx echo.Context) error {
	fun := "ticketService.TicketNewAiProcess"
	// get lock
	lock, err := rds.GetRdsMux(ctx.Request().Context(), keys.TicketNewAIUniqLock)
	if err != nil {
		if errors.Is(err, redislock.ErrNotObtained) {
			logger.Infof(ctx.Request().Context(), "%s auto alloc get lock fail. %v", fun, err)
			return nil
		}
		return err
	}
	logger.Infof(ctx.Request().Context(), "%s auto alloc get lock success. ", fun)
	refreshC := make(chan struct{})
	defer lock.Release(ctx.Request().Context())
	go rds.RefreshRdsMux(ctx.Request().Context(), lock, refreshC)
	defer close(refreshC)

	// 1. 获取需要处理的单子(未接入)
	ticketList, err := dto.NewTicket().GetUnSolvedNewAiTicketList(ctx, int(pb.TicketType_TicketTypeNewAi), int(pb.TkStage_TkStageNew), []int{int(pb.TkStatus_TkStatusUntreated)})
	if err != nil {
		logger.Error(ctx.Request().Context(), "GetUnSolvedNewAiTicketList error", zap.String("err", err.Error()))
		return err
	}
	for _, ticket := range ticketList {
		// 2. 是否有工单描述
		issueDesc, flag := ticket_llm.HasTicketDesc(ticket)
		if !flag {
			// 转人工处理
			err := srv.TicketTransferManual(ctx, ticket.TicketID)
			if err != nil {
				logger.Errorf(ctx.Request().Context(), "TicketTransferManual error. %v", err)
			}
			continue
		}
		// 3.判断是否为有效问题
		if ticket_llm.CheckMeaninglessQuery(ctx.Request().Context(), issueDesc) {
			content, err := dto.NewTicket().GetInvalidReplyContent(ctx, ticket.Project, ticket.Lang)
			if err != nil {
				logger.Errorf(ctx.Request().Context(), "GetInvalidReplyContent error. %v", err)
				continue
			}
			err = srv.TicketTransferNewType(ctx, ticket, pb.SolveType_SolveTypeInvalidTicket, content)
			if err != nil {
				logger.Errorf(ctx.Request().Context(), "TicketTransferNewType error. %v", err)
			}
			continue
		}

		// 4.分级策略1 ：判断是否为登录或者建议分类
		catInfo, err := dto.NewCat().GetDetailById(ctx, ticket.CatID)
		if err != nil {
			logger.Error(ctx.Request().Context(), "GetCatDetailById error", zap.String("err", err.Error()))
			continue
		}

		// 第一次提问登录或者建议分类则直接回复模版
		LoginCat := viper.GetString(fmt.Sprintf("strategy_first_cat_id.games.%s.login", ticket.Project))
		SuggestionCat := viper.GetString(fmt.Sprintf("strategy_first_cat_id.games.%s.suggestion", ticket.Project))
		if catInfo.Level == 3 && (catInfo.OneLevel == cast.ToUint32(LoginCat) || (catInfo.OneLevel == cast.ToUint32(SuggestionCat))) && ticket.ReopenNum < 1 {
			if catInfo.OneLevel == cast.ToUint32(SuggestionCat) && ticket.Recharge != 0 {
				err := srv.TicketTransferManual(ctx, ticket.TicketID)
				if err != nil {
					logger.Errorf(ctx.Request().Context(), "TicketTransferManual error. %v", err)
				}
				continue
			}
			isMatch := ticket_llm.CheckCategoryMatch(ctx.Request().Context(), issueDesc, catInfo.Category)
			if !isMatch {
				logger.Info(ctx.Request().Context(), "玩家选择的问题与实际问题描述不一致，转人工处理", zap.Uint64("ticketId", ticket.TicketID))
				err = srv.TicketTransferManual(ctx, ticket.TicketID)
				if err != nil {
					logger.Errorf(ctx.Request().Context(), "TicketTransferManual error. %v", err)
				}
				continue
			}
			tplInfo, err := dto.NewStrategyTpl().GetStrategyReplyContent(ctx.Request().Context(), ticket.Project, ticket.Lang, catInfo.OneLevel)
			if err != nil {
				logger.Error(ctx.Request().Context(), "GetReplyContent error", zap.String("err", err.Error()))
				continue
			}
			// ai润色再发送
			polishParam := &pb.AIPolishReq{
				TicketId: ticket.TicketID,
				Content:  tplInfo.ReplyContent,
				Style:    "更友善",
			}
			polishContent, err := ai.Polish(ctx, polishParam)
			if err != nil {
				logger.Errorf(ctx.Request().Context(), "Polish error. %v", err)
				continue
			}

			err = srv.TicketTransferNewType(ctx, ticket, pb.SolveType_SolveTypeStrategyTicket, polishContent)
			if err != nil {
				logger.Errorf(ctx.Request().Context(), "TicketTransferNewType error. %v", err)
				continue
			}
			continue
		}
		// 5.分级策略2：判断是否命中分级策略配置
		strategyInfo, err := dto.NewStrategyDao().GetStrategyByProject(ticket.Project)
		if err != nil {
			logger.Errorf(ctx.Request().Context(), "GetStrategyByProject error. %v", err)
			continue
		}
		// 若工单策略不为空
		if strategyInfo != nil {
			strategyFilter := strategyInfo.DecodeStrategyFilters()
			// 判断三个过滤条件
			if strategyFilter != nil && (strategyFilter.IsSeverAll ||
				models.CheckFilter(int64(ticket.Sid), strategyFilter.Server)) &&
				models.CheckFilter(int64(ticket.Recharge), strategyFilter.PayRange) {
				// todo 城堡等级暂无
				// &&models.CheckFilter(int64(ticket.CastleLevel), strategyFilter.CastleLevel){
				// 去历史库搜索
				content, err := srv.MatchTicketKnowledge(ctx, ticket, issueDesc)
				if err != nil {
					logger.Errorf(ctx.Request().Context(), "MatchTicketKnowledge error. %v", err)
					continue
				}
				// 未匹配
				if content == "UNK" {
					// 转人工处理
					err = srv.TicketTransferManual(ctx, ticket.TicketID)
					if err != nil {
						logger.Errorf(ctx.Request().Context(), "TicketTransferManual error. %v", err)
					}
					continue
				}
				err = srv.TicketTransferNewType(ctx, ticket, pb.SolveType_SolveTypeStrategyTicket, content)
				if err != nil {
					logger.Errorf(ctx.Request().Context(), "TicketTransferNewType error. %v", err)
				}
				continue
			}
		}

		// 未命中策略直接转人工
		err = srv.TicketTransferManual(ctx, ticket.TicketID)
		if err != nil {
			logger.Errorf(ctx.Request().Context(), "TicketTransferManual error. %v", err)
		}
		continue
	}
	return nil
}

// TicketTransferManual 转人工处理工单
func (srv *ticketService) TicketTransferManual(ctx echo.Context, ticketID uint64) error {
	// 更新处理类型进行分单或指派
	dest := map[string]interface{}{
		"solve_type": int(pb.SolveType_SolveTypeManualTicket),
	}
	if err := dto.NewTicket().TicketPropsUpdate(ctx.Request().Context(), ticketID, dest, func(txDb *gorm.DB) error {
		if _err := elasticsearch.DefaultTicketSyncSvc.UpdateTicket(ctx.Request().Context(), ticketID, dest); _err != nil {
			logger.Warn(ctx.Request().Context(), "TicketTransferNewType update es err", logger.Any("ticketId", ticketID), logger.Any("dest", dest), logger.Any("err", _err))
			return _err
		}
		return nil
	}); err != nil {
		logger.Errorf(ctx.Request().Context(), "TicketPropsUpdate return err. ticketId:%d. dest:%+v. err:%v", ticketID, dest, err)
		return err
	}
	logger.Infof(ctx.Request().Context(), "ticket has been transfered by Manual")
	return nil
}

// TicketTransferNewType 处理新类型工单
func (srv *ticketService) TicketTransferNewType(ctx echo.Context, ticket *models.FpOpsTickets, solveType pb.SolveType, content string) error {
	// 设置转人工状态、处理类型并插入db
	dest := map[string]interface{}{
		"acceptor":   ctx.Get(cst.AccountInfoCtx).(string),
		"solve_type": int(solveType),
		"updated_at": time.Now().Unix(),
	}

	if err := dto.NewTicket().TicketPropsUpdate(ctx.Request().Context(), ticket.TicketID, dest, func(txDb *gorm.DB) error {
		if _err := elasticsearch.DefaultTicketSyncSvc.UpdateTicket(ctx.Request().Context(), ticket.TicketID, dest); _err != nil {
			logger.Warn(ctx.Request().Context(), "TicketTransferNewType update es err", logger.Any("ticketId", ticket.TicketID), logger.Any("dest", dest), logger.Any("err", _err))
			return _err
		}
		return nil
	}); err != nil {
		logger.Errorf(ctx.Request().Context(), "TicketPropsUpdate return err. ticketId:%d. dest:%+v. err:%v", ticket.TicketID, dest, err)
		return err
	}

	// 调用transfer回复
	tpReq := &pb.TicketTransferReq{
		TicketId: ticket.TicketID,
		OpType:   pb.TkTransferOpType_TkTransferOpTypeCommuClose,
		Content:  content,
	}
	if err := NewTicketSrv().TicketTransfer(ctx, tpReq, false); err != nil {
		logger.Error(ctx.Request().Context(), "create system reply error",
			zap.Uint64("ticket_id", ticket.TicketID), zap.String("err", err.Error()))
		return err
	}
	return nil
}

// MatchTicketKnowledge 判断是否命中工单历史知识库
func (srv *ticketService) MatchTicketKnowledge(ctx echo.Context, ticket *models.FpOpsTickets, issueDesc string) (string, error) {
	// 请求elfin向量搜索接口
	req := &aielfin.ElfinTicketKnowledgeSearchElfReq{
		GameProject:    ticket.Project,
		Lang:           ticket.Lang,
		CatID:          ticket.CatID,
		QstDesc:        issueDesc,
		ScoreThreshold: 1.8,
	}
	answer, err := aielfin.TicketKnowledgeSearchElf(ctx.Request().Context(), "", req)
	if err != nil {
		logger.Errorf(ctx.Request().Context(), "TicketKnowledgeSearchElf error. %v", err)
		return "", err
	}
	return answer, nil
}
