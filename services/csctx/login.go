// Copyright 2021 funplus Authors. All Rights Reserved.
// @Author: <PERSON><PERSON>
// @Date: 2025/3/17 11:01

package csctx

import (
	"context"
)

// CtxFPIDAccountIDKey  GameTokenAuth 中间件 存储 fpid/account 数据， fpxsdk时为 account_id， 其他时候时 fpid
const CtxFPIDAccountIDKey = "cs_fpid_account_id"

// CtxUidRoleIDKey  GameTokenAuth 中间件 存储 uid/role_id 数据
const CtxUidRoleIDKey = "cs_uid_role_id"

// CtxGameProjectKey  GameTokenAuth 中间件 存储游戏项目数据
const CtxGameProjectKey = "cs_game_project"

// CtxGameIDOrFpxAppID  GameTokenAuth 中间件 存储游戏项目数据
const CtxGameIDOrFpxAppID = "cs_game_id"

// GetFPIDAccountID 获取fpid/account_id
func GetFPIDAccountID(ctx context.Context) string {
	fpid := ctx.Value(CtxFPIDAccountIDKey)
	fpidStr, ok := fpid.(string)
	if ok {
		return fpidStr
	}
	return ""
}

// GetUidRoleID 获取uid/role_id
func GetUidRoleID(ctx context.Context) string {
	uid := ctx.Value(CtxUidRoleIDKey)
	uidStr, ok := uid.(string)
	if ok {
		return uidStr
	}
	return ""
}

// GetGameProject 获取游戏项目
func GetGameProject(ctx context.Context) string {
	project := ctx.Value(CtxGameProjectKey)
	projectStr, ok := project.(string)
	if ok {
		return projectStr
	}
	return ""
}

// GetGameIDOrFpxAppID 获取游戏项目
func GetGameIDOrFpxAppID(ctx context.Context) string {
	gameID := ctx.Value(CtxGameIDOrFpxAppID)
	gameIDStr, ok := gameID.(string)
	if ok {
		return gameIDStr
	}
	return ""
}
