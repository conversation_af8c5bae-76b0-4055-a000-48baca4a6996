package services

import (
	"errors"
	"github.com/labstack/echo/v4"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"ops-ticket-api/internal/cst"
	"ops-ticket-api/internal/persistence"
	"ops-ticket-api/models"
	"ops-ticket-api/proto/pb"
	"ops-ticket-api/services/communication"
	"time"
)

func NewQuestionTrainingService() *questionTrainingService {
	return &questionTrainingService{}
}

type questionTrainingService struct {
}

func (q *questionTrainingService) Training(ctx echo.Context, req *pb.QuestionTrainingReq) error {
	if req.IsAll { // training_core games all
		// 获取所有lang
		ls, err := persistence.NewQuestionTicket().GetAllQuestionLang(req.Project)
		if err != nil {
			return err
		}
		req.Lang = ls
	}
	// do action
	for _, l := range req.Lang {
		if err := q.trainingSingle(ctx, req.Project, l, ctx.Get(cst.AccountInfoCtx).(string)); err != nil {
			return err
		}
	}
	return nil
}

func (q *questionTrainingService) trainingSingle(ctx echo.Context, gameProject string, l string, creator string) error {
	if boo, err := persistence.NewTrainingTask().CheckHasNotTrain(gameProject, l); err != nil {
		return err
	} else if boo == true {
		elog.Warnf("trainingSingle has init log. gameProject:%s. lang:%s", gameProject, l)
		return nil
	}
	// do action
	var log = &models.FpOpsTrainingTask{
		Project:    gameProject,
		Lang:       l,
		Status:     int32(pb.TrainingTaskStatus_ProcessTicketStatusInit),
		FailReason: "",
		Remark:     "",
		CreatedAt:  time.Now().Unix(),
		UpdatedAt:  time.Now().Unix(),
		Creator:    creator,
	}
	if err := persistence.NewTrainingTask().CreateTrainingLog(log); err != nil {
		return err
	}
	if log.LogID < 1 {
		return errors.New("return log id lte0")
	}
	communication.PushQuestionTraining(log.LogID)

	return nil
}

func (q *questionTrainingService) GetAllTrainingTaskList(ctx echo.Context, req *pb.QuestionTrainLogReq) (*pb.QuestionTrainLogResp, error) {
	lists, total, err := persistence.NewTrainingTask().GetAllTrainingTaskList(ctx, req)
	if err != nil {
		logger.Errorf(ctx.Request().Context(), "GetAllTrainingTaskList err:%v", err)
		return nil, err
	}
	return &pb.QuestionTrainLogResp{
		CurrentPage: req.Page,
		PerPage:     req.PageSize,
		Total:       total,
		Data:        lists,
	}, nil

}
