package server_channel_filter

import (
	"encoding/json"
	"fmt"
	"github.com/spf13/cast"
	"strings"
)

var FilterStrategySrv = &FilterStrategyService{
	replaceDelimiter: map[string]string{ // 容易输错字符
		"，":  ",", // 中文替换成英文
		"\n": ",", // 换行替换成英文 -- 换行也作切分
		"【":  "[",
		"】":  "]",
		" ":  "",
	},
	delimiter: ",",
}

// FilterStrategyService 后台过滤保存问题
type FilterStrategyService struct {
	replaceDelimiter map[string]string `json:"replace_delimiter`
	delimiter        string            `json:"delimiter"`
}

// 根据入参 整理成对应filters数据
func (f *FilterStrategyService) GenStructFilters(serverStr, pkgChannelStr string) *args.AnswerFilters {
	var detail = &args.AnswerFilters{
		Server:     f.GenFilterServer(serverStr),
		PkgChannel: f.GenAnswerFilterPkgChannel(pkgChannelStr),
	}
	return detail
}

// 格式化后的filter是数据 整理 成前端展示数据 -- uid
func (f *FilterService) FilterUidString(anf string) string {
	if anf == "" || anf == "{}" {
		return ""
	}
	var ansfilter = struct {
		Uid []int64 `json:"uid"`
	}{}
	json.Unmarshal([]byte(anf), &ansfilter)
	var uids string
	if len(ansfilter.Uid) > 0 {
		uids = strings.Join(cast.ToStringSlice(ansfilter.Uid), f.delimiter)
	}
	return uids
}

// 格式化后的filter是数据 整理 成前端展示数据 -- lang
func (f *FilterService) FilterLangString(anf string) string {
	if anf == "" || anf == "{}" {
		return ""
	}
	var ansfilter = struct {
		Lang []string `json:"lang"`
	}{}
	json.Unmarshal([]byte(anf), &ansfilter)
	var langs string
	if len(ansfilter.Lang) > 0 {
		langs = strings.Join(ansfilter.Lang, f.delimiter)
	}
	return langs
}

// 格式化后的filter是数据 整理 成前端展示数据 -- pkg_channel
func (f *FilterService) FilterPkgChannelString(ansfilter *args.AnswerFilters) string {
	var pkgChannels string
	if len(ansfilter.PkgChannel) > 0 {
		pkgChannels = strings.Join(ansfilter.PkgChannel, f.delimiter)
	}
	return pkgChannels
}

// 格式化后的filter是数据 整理 成前端展示数据 -- server
func (f *FilterService) FilterServerString(ansfilter *args.AnswerFilters) string {
	var servers []string
	for _, sv := range ansfilter.Server {
		if sv.Gte > 0 && sv.Lte > 0 { // 区间
			servers = append(servers, fmt.Sprintf("[%d-%d]", sv.Gte, sv.Lte))
		} else if sv.Gte > 0 { // >
			servers = append(servers, fmt.Sprintf("[%d-]", sv.Gte))
		} else if sv.Lte > 0 {
			servers = append(servers, fmt.Sprintf("[-%d]", sv.Lte))
		}
		for _, _s := range sv.Enum {
			if _s > 0 {
				servers = append(servers, cast.ToString(_s))
			}
		}
	}
	return strings.Join(servers, f.delimiter)
}

func (f *FilterService) GenFilterServer(serverStr string) []*args.FilterServer {
	var servers = []*args.FilterServer{}
	serverStr = f.replace(serverStr)
	if serverStr == "" {
		return servers
	}
	_sSlice := strings.Split(serverStr, f.delimiter)
	var btw []string
	var _enum = []int64{}
	for _, _sv := range _sSlice {
		if strings.Contains(_sv, "-") && strings.LastIndex(_sv, "[") == 0 && strings.Index(_sv, "]") == len(_sv)-1 {
			btw = append(btw, _sv)
			continue
		}
		if _v := cast.ToInt64(_sv); _v > 0 {
			_enum = append(_enum, _v)
		}
	}
	// 枚举
	if len(_enum) > 0 {
		servers = append(servers, &args.FilterServer{
			Enum: _enum,
		})
	}
	// 区间
	for _, _bt := range btw {
		__s := strings.Split(strings.TrimFunc(_bt, func(r rune) bool { return r == '[' || r == ']' }), "-")
		if len(__s) != 2 {
			continue
		}
		_start := cast.ToInt64(__s[0])
		_end := cast.ToInt64(__s[1])
		if (_start <= 0 && _end <= 0) || _start > _end {
			continue
		}
		servers = append(servers, &args.FilterServer{
			Gte:  _start,
			Lte:  _end,
			Enum: nil,
		})
	}
	return servers
}

func (f *FilterService) GenAnswerFilterPkgChannel(pkgChannelStr string) []string {
	var pkgChannels = []string{}
	pkgChannelStr = f.replace(pkgChannelStr)
	if pkgChannelStr == "" {
		return pkgChannels
	}
	sp := strings.Split(pkgChannelStr, f.delimiter)
	for _, _p := range sp {
		if _p == "" {
			continue
		}
		pkgChannels = append(pkgChannels, _p)
	}
	return pkgChannels
}

func (f *FilterService) GenUidSlice(uid string) []int64 {
	var uidSlice = []int64{}
	_uidStr := f.replace(uid)
	if _uidStr == "" {
		return uidSlice
	}
	sp := strings.Split(_uidStr, f.delimiter)
	for _, _p := range sp {
		if _p == "" {
			continue
		}
		if _u := cast.ToInt64(_p); _u > 0 {
			uidSlice = append(uidSlice, _u)
		}
	}
	return uidSlice
}

func (f *FilterService) GenLangSlice(langStr string) []string {
	var langSlice = []string{}
	langStr = f.replace(langStr)
	if langStr == "" {
		return langSlice
	}
	sp := strings.Split(langStr, f.delimiter)
	for _, _p := range sp {
		if _p == "" {
			continue
		}
		langSlice = append(langSlice, strings.ToLower(_p))
	}
	return langSlice
}

func (f *FilterService) replace(s string) string {
	for k, v := range f.replaceDelimiter {
		if strings.Contains(s, k) {
			s = strings.ReplaceAll(s, k, v)
		}
	}
	return s
}

func (f *FilterService) check(rule1, rule2 *args.AnswerFilters) error {
	var sFlag bool = len(rule1.Server) == 0 && len(rule2.Server) == 0
	var pFlag bool = len(rule1.PkgChannel) == 0 && len(rule2.PkgChannel) == 0

	// if len(rule1.Server) == 0 && len(rule2.Server) == 0 {
	// 	return ferr.New(respcode.FpRetCodeAnswerFilterErr)
	// }
	for _, s := range rule1.Server {
		// 区间
		if f._checkBtw(s.Gte, s.Lte, rule2) == true {
			sFlag = true
			break
			// return ferr.New(respcode.FpRetCodeAnswerFilterErr)
		}
		// 枚举
		for _, __s := range s.Enum {
			if f._checkIn(__s, rule2) == true {
				sFlag = true
				break
				// return ferr.New(respcode.FpRetCodeAnswerFilterErr)
			}
		}
	}
	for _, p := range rule1.PkgChannel {
		if p != "" && utils.InSliceString(p, rule2.PkgChannel) {
			pFlag = true
			break
			// return ferr.New(respcode.FpRetCodeAnswerFilterErr)
		}
	}
	if pFlag == true && sFlag == true {
		return ferrors.New("服务器/渠道重复", code.FpRetCodeAnswerFilterErr)
	}
	return nil
}

func (f *FilterService) _checkIn(sid int64, data *args.AnswerFilters) bool {
	if sid == 0 {
		return false
	}
	for _, row := range data.Server {
		if row.Lte > 0 {
			if sid <= row.Lte && sid >= row.Gte {
				return true
			}
		}
		if len(row.Enum) > 0 {
			if utils.InSliceInt64(sid, row.Enum) {
				return true
			}
		}
	}
	return false
}
func (f *FilterService) _checkBtw(start, end int64, data *args.AnswerFilters) bool {
	if start == 0 && end == 0 {
		return false
	}
	for _, row := range data.Server {
		if row.Lte > 0 {
			if !(row.Gte > end || row.Lte < start) { // 有重叠
				return true
			}
		}
		if len(row.Enum) > 0 {
			if inBetwen(start, end, row.Enum) {
				return true
			}
		}
	}
	return false
}

var inBetwen = func(start, end int64, arr []int64) bool {
	for _, v := range arr {
		if v >= start && v <= end {
			return true
		}
	}
	return false
}
