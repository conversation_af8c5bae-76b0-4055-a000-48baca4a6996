package services

import (
	"context"
	"errors"
	"github.com/avast/retry-go"
	"github.com/kevwan/mapreduce"
	"github.com/tmc/langchaingo/textsplitter"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog"
	uuid2 "gopkg.in/go-on/go.uuid.v1"
	"gorm.io/gorm"
	"ops-ticket-api/internal/persistence"
	"ops-ticket-api/models"
	aielfin "ops-ticket-api/pkg/elfin"
	"ops-ticket-api/proto/pb"
	"time"
)

// NewBatchTrainingSrv 模型训练批量操作
func NewBatchTrainingSrv(ctx context.Context, log *models.FpOpsTrainingTask) *batchTrainingSrv {
	return &batchTrainingSrv{
		ctx:         ctx,
		learnLog:    log,
		project:     log.Project,
		lang:        log.Lang,
		textSpliter: textsplitter.NewRecursiveCharacter(),
	}
}

type batchTrainingSrv struct {
	ctx         context.Context
	project     string
	lang        string
	learnLog    *models.FpOpsTrainingTask
	startTime   uint64
	textSpliter textsplitter.RecursiveCharacter
}

func (srv *batchTrainingSrv) Handle() error {
	fun := "batchTrainingSrv.Handle -->"
	// 重置开始时间
	if err := srv.ResetLastEndTime(); err != nil {
		elog.Errorf("%s ResetLastEndTime return err. gameProject:%s. lang:%s. logId:%d err:%v", fun, srv.project, srv.lang, srv.learnLog.LogID, err)
		return err
	}
	elog.Infof("%s logId:%d. gameProject:%s. lang:%s. startTime:%v", fun, srv.learnLog.LogID, srv.project, srv.lang, srv.startTime)
	// 删除向量库 updated，deleted 状态的数据
	if err := srv.ModifyDeleteEmbedding(); err != nil {
		elog.Errorf("%s ModifyDeleteEmbedding return err. gameProject:%s. lang:%s. logId:%d err:%v", fun, srv.project, srv.lang, srv.learnLog.LogID, err)
		return err
	}
	// 增量更新向量库
	if err := srv.ModifyEmbedding(); err != nil {
		elog.Errorf("%s ModifyEmbedding return err. gameProject:%s. lang:%s. logId:%d err:%v", fun, srv.project, srv.lang, srv.learnLog.LogID, err)
		return err
	}
	return nil
}

// ResetLastEndTime get last update end time
func (srv *batchTrainingSrv) ResetLastEndTime() error {
	lastUpdateTime, err := srv.GetLastEndTime(srv.learnLog)
	if err != nil {
		return err
	}
	if lastUpdateTime == 0 {
		lastUpdateTime = time.Now().AddDate(-5, 0, 0).Unix()
	}
	srv.startTime = uint64(lastUpdateTime)
	return nil
}

// GetLastEndTime 获取知识库中最后更新的qid，且在批任务中已成功更新向量库
func (srv *batchTrainingSrv) GetLastEndTime(log *models.FpOpsTrainingTask) (int64, error) {
	elog.Infof("batchTrainingSrv.GetLastEndTime --> logId:%d. log:%+v", log.LogID, log)
	var lastTime = time.Now().AddDate(-5, 0, 0).Unix()

	// 获取最近一次成功的批任务
	log, err := persistence.NewTrainingTask().GetRowTrainingLog(map[string]interface{}{
		"project": log.Project,
		"lang":    log.Lang,
		"status":  int32(pb.TrainingTaskStatus_ProcessTicketStatusSuccess),
		"log_id":  log.LogID,
	})
	if err != nil {
		if !errors.Is(err, gorm.ErrRecordNotFound) {
			return lastTime, err
		}

		return lastTime, nil
	}
	// 获取知识库最后一条改动数据的时间（已成功同步在向量库中）
	gptLog, err := persistence.NewTrainingTask().GetAiChatgptMaxLastTime(log.LogID, log.Project, log.Lang)
	if err != nil {
		if !errors.Is(err, gorm.ErrRecordNotFound) {
			return lastTime, err
		}
		return lastTime, nil
	}
	lastTime = int64(gptLog.LastUpdateTime)
	return lastTime, nil
}

// ModifyDeleteEmbedding delete has remove question embedding
func (srv *batchTrainingSrv) ModifyDeleteEmbedding() error {
	delQids, err := persistence.NewQuestionTicketLog().GetEditAndDeleteQuestionId(srv.project, srv.lang, srv.startTime)
	if err != nil {
		return err
	}
	// do logic
	if err := srv.DoDelQuestionIds(srv.learnLog, delQids); err != nil {
		return err
	}
	return nil
}

func (srv *batchTrainingSrv) DoDelQuestionIds(log *models.FpOpsTrainingTask, qstIds []int64) error {
	elog.Infof("batchTrainingSrv.DoDelQuestionIds --> logId:%d. log:%+v. qstIds:%+v", log.LogID, log, qstIds)
	var batchQstIds []int64
	for _, qid := range qstIds {
		batchQstIds = append(batchQstIds, qid)
		if len(batchQstIds) > 45 { // 单次不超过 50条
			if err := srv._batchDelQst(log.Creator, log.Project, batchQstIds); err != nil {
				return err
			}
			batchQstIds = make([]int64, 0)
		}
	}
	if len(batchQstIds) > 0 {
		if err := srv._batchDelQst(log.Creator, log.Project, batchQstIds); err != nil {
			return err
		}
	}
	return nil
}

func (srv *batchTrainingSrv) _batchDelQst(account, gameProject string, qstIds []int64) error {
	fun := "aiElfinKnowledgeBatchTrainingSrv._batchDelQst -->"
	if len(qstIds) == 0 {
		return nil
	}
	var delReq = &aielfin.ElfinTicketKnowledgeDelReq{
		GameProject: gameProject,
		QstIds:      qstIds,
	}
	err := retry.Do(func() error {
		if innErr := aielfin.DeleteKnowledge(srv.ctx, account, delReq); innErr != nil {
			elog.Warnf("%s aielfin.DelKnowledge return err. qstIds:%+v. err:%+v", fun, qstIds, innErr)
			return innErr
		}
		return nil
	}, retry.Attempts(3), retry.LastErrorOnly(true))
	if err != nil {
		elog.Errorf("%s. retry.Do aielfin.DelKnowledge return err. qstIds:%+v. err:%+v", fun, qstIds, err)
	}
	return err
}

func (srv *batchTrainingSrv) ModifyEmbedding() error {
	fun := "batchTrainingSrv.ModifyEmbedding -->"
	// 1. get update question
	qsts, err := srv.getAllUpdateQst()
	if err != nil {
		return err
	}
	if len(qsts) == 0 {
		return nil
	}

	// 2. modify embedding
	_, err = mapreduce.MapReduce(func(source chan<- interface{}) {
		for _, qst := range qsts {
			source <- qst
		}
	}, func(item interface{}, writer mapreduce.Writer, cancel func(error)) {
		qst := item.(*models.QstRelation)
		err := retry.Do(func() error {
			if err := srv.DoModifySingleQuestion(srv.learnLog, qst); err != nil {
				elog.Warnf("%s DoModifySingleQuestion return err. questionId:%d. err:%v", fun, qst.QuestionId, err)
				return err
			}
			return nil
		}, retry.Attempts(3), retry.LastErrorOnly(true))
		if err != nil {
			elog.Errorf("%s DoModifySingleQuestion retry.Do return err. questionId:%d. err:%v", fun, qst.QuestionId, err)
			cancel(err)
		}
		writer.Write(qst)
	}, func(pipe <-chan interface{}, writer mapreduce.Writer, cancel func(error)) {
		var batchSaveLog []*models.FpOpsTrainingTaskDetail
		for item := range pipe {
			qst := item.(*models.QstRelation)
			elog.Infof("%s modify embedding sucess. question_id: %d. %s.%s", fun, qst.QuestionId, qst.Project, qst.Lang)
			batchSaveLog = append(batchSaveLog, &models.FpOpsTrainingTaskDetail{
				Project:        qst.Project,
				Lang:           qst.Lang,
				LogID:          srv.learnLog.LogID,
				QuestionID:     int64(qst.QuestionId),
				CatID:          qst.CatID,
				LastUpdateTime: qst.UpdateTime,
				Status:         int32(pb.TrainingTaskStatus_ProcessTicketStatusSuccess),
				Remark:         "",
				CreatedAt:      time.Now().Unix(),
				UpdatedAt:      time.Now().Unix(),
				Creator:        srv.learnLog.Creator,
			})
		}
		if len(batchSaveLog) > 0 {
			if err := persistence.NewTrainingTask().BatchSaveTrainingDetailLog(batchSaveLog); err != nil {
				elog.Errorf("%s BatchSaveTrainingDetailLog return err. err:%v. len:%d", fun, err, len(batchSaveLog))
			}
		}
	}, mapreduce.WithWorkers(2), mapreduce.WithContext(srv.ctx))
	if err != nil && errors.Is(err, mapreduce.ErrReduceNoOutput) {
		return nil
	}
	return err
}

// getAllUpdateQst get all modify question and answer
func (srv *batchTrainingSrv) getAllUpdateQst() ([]*models.QstRelation, error) {
	qsts, err := persistence.NewQuestionTicket().GetModifiedQst(srv.project, srv.lang, srv.startTime)
	if err != nil {
		return nil, err
	}
	if len(qsts) == 0 {
		return nil, nil
	}
	var qids []uint64
	var rs = make([]*models.QstRelation, 0, len(qsts))
	for _, qst := range qsts {
		qids = append(qids, qst.QuestionID)
		rs = append(rs, &models.QstRelation{
			QuestionId:      qst.QuestionID,
			Project:         qst.Project,
			Lang:            qst.Lang,
			CatID:           qst.CatID,
			QuestionContent: qst.QuestionContent,
			UpdateTime:      qst.UpdatedAt,
			AnswerRichText:  qst.AnswerRichText,
		})
	}
	return rs, nil
}

func (srv *batchTrainingSrv) DoModifySingleQuestion(log *models.FpOpsTrainingTask, relation *models.QstRelation) error {
	fun := "batchTrainingSrv.DoModifySingleQuestion -->"
	if relation == nil || relation.QuestionId == 0 {
		return nil
	}

	var err error
	var req = &aielfin.ElfinTicketKnowledgeSaveReq{
		QstId:          int64(relation.QuestionId),
		Uuid:           uuid2.NewV4().String(),
		GameProject:    relation.Project,
		Lang:           relation.Lang,
		CatID:          relation.CatID,
		QstDesc:        relation.QuestionContent,
		AnswerRichText: relation.AnswerRichText,
	}
	if err = aielfin.ImportTicketKnowledge(srv.ctx, log.Creator, req); err != nil {
		elog.Errorf("%s aielfin.ImportKnowledge return err. req:%+v. err:%+v", fun, req, err)
		return err
	}
	return nil
}
