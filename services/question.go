package services

import (
	"errors"
	"fmt"
	"github.com/labstack/echo/v4"
	"github.com/spf13/cast"
	"github.com/spf13/viper"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"gitlab-ee.funplus.io/ops-tools/compkg/sign"
	"gorm.io/gorm"
	"ops-ticket-api/internal/code"
	"ops-ticket-api/internal/cst"
	"ops-ticket-api/internal/framework/database"
	"ops-ticket-api/internal/framework/xerrors"
	"ops-ticket-api/internal/persistence"
	"ops-ticket-api/models"
	"ops-ticket-api/proto/pb"
	"ops-ticket-api/utils"
	"strings"
	"time"
)

func NewQuestionTicketSrv() *questionTicketService {
	return &questionTicketService{}
}

type questionTicketService struct {
}

func (s *questionTicketService) SaveQuestionDetail(ctx echo.Context, req *pb.QuestionSaveReq) error {
	var question = &models.FpOpsTicketsQuestion{}
	var isEdit bool
	// edit
	if req.QuestionId > 0 {
		isEdit = true
		// 查old
		oldQuestionDetail, err := persistence.NewQuestionTicket().GetQuestionTicket(nil, req.QuestionId)
		if err != nil {
			return err
		}
		// check
		if oldQuestionDetail.Project != req.Project || oldQuestionDetail.Lang != req.Lang {
			return xerrors.New("project or lang not match", code.InvalidParams)
		}
	}
	// 3.校验游戏下是否存在该问题分类
	catInfo, err := persistence.NewCat().GetDetailById(ctx, req.CatId)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			logger.Errorf(ctx.Request().Context(), "GetDetailById err, err:%+v", err)
			return xerrors.New(code.StatusText(code.NotFound), code.NotFound)
		}
		logger.Errorf(ctx.Request().Context(), "GetDetailById err, err:%+v", err)
		return xerrors.New(code.StatusText(code.DbError), code.DbError)
	}
	if catInfo.Project != req.Project {
		return xerrors.New("保存失败！存在问题分类与游戏对应错误", code.InvalidParams)
	}
	// 组装数据
	creator := ctx.Get(cst.AccountInfoCtx).(string)
	// create question detail data
	questionContent := strings.TrimSpace(req.QuestionContent)
	var curTimMills = time.Now().Unix()
	question = &models.FpOpsTicketsQuestion{
		Project:             req.Project,
		Lang:                req.Lang,
		CatID:               req.CatId,
		QuestionContent:     questionContent,
		QuestionContentHash: sign.Md5(questionContent),
		AnswerRichText:      req.Answer,
		Creator:             creator,
		CreatedAt:           cast.ToUint64(curTimMills),
		UpdatedAt:           cast.ToUint64(curTimMills),
	}
	if isEdit {
		question.QuestionID = uint64(req.QuestionId)
	}
	// 开启事务更新
	tx := database.Db().Begin()
	defer tx.Rollback()
	err = persistence.NewQuestionTicket().SaveQuestionDetail(tx, question)
	if err != nil {
		return err
	}
	// 记录日志
	err = NewQuestionTicketLogSrv().QstSaveLog(tx, question.Project, question.QuestionContentHash, isEdit)
	if err != nil {
		return err
	}
	return tx.Commit().Error
}

// QuestionList 工单知识库列表
func (s *questionTicketService) QuestionList(ctx echo.Context, req *pb.QuestionListReq) (*pb.QuestionListResp, error) {
	list, total, err := persistence.NewQuestionTicket().QuestionList(ctx, req, false)
	if err != nil {
		return nil, err
	}
	return &pb.QuestionListResp{
		CurrentPage: req.Page,
		PerPage:     req.PageSize,
		Total:       total,
		Data:        list,
	}, nil
}

func QuestionExport(ctx echo.Context, req *pb.QuestionListReq) (string, error) {
	records, _, err := persistence.NewQuestionTicket().QuestionList(ctx, req, true)
	if err != nil {
		return "", err
	}
	var dest = make([][]interface{}, 0)
	for i := range records {
		detail := records[i]
		record := []interface{}{detail.QuestionContent, detail.Lang, detail.Project, detail.Category, detail.AnswerRichText}
		dest = append(dest, record)
	}
	_fileName := fmt.Sprintf("ticket_question_record_export_%d.csv", time.Now().UnixNano())
	err = utils.CreateCsvFile(_fileName, viper.GetStringSlice("export_header.TicketQuestionExport.zh-cn"), dest)
	if err != nil {
		return "", err
	}
	return _fileName, err
}

// QuestionDel 工单知识库del
func (s *questionTicketService) QuestionDel(ctx echo.Context, req *pb.QuestionDelReq) error {
	// check
	questionInfo, err := persistence.NewQuestionTicket().GetQuestionById(req.QuestionId)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return xerrors.New("参数错误，该语料不存在", code.InvalidParams)
		}
		return xerrors.New(code.StatusText(code.DbError), code.DbError)
	}

	// 开启事务更新
	tx := database.Db().Begin()
	defer tx.Rollback()
	err = persistence.NewQuestionTicket().DelQuestion(tx, req.QuestionId)
	if err != nil {
		return err
	}
	// 记录日志
	err = NewQuestionTicketLogSrv().QstDelLog(tx, questionInfo, ctx.Get(cst.AccountInfoCtx).(string))
	if err != nil {
		return err
	}
	return tx.Commit().Error
}

// QuestionBatchImport 工单知识库批量导入
func (s *questionTicketService) QuestionBatchImport(ctx echo.Context, param *pb.QuestionBatchImportReq) error {
	var (
		fun    = "QuestionBatchImport"
		header = []string{"语料", "语种", "关联游戏", "问题分类", "答案"}
		has    = map[string]struct{}{}
		reqs   []*pb.QuestionSaveReq
	)
	details, err := utils.ReadExcelOnline(param.FileName)
	if err != nil {
		return err
	}
	if len(details) < 2 {
		return fmt.Errorf("%s FileParse:excel file is empty", fun)
	}
	if len(details[0]) < 5 {
		return fmt.Errorf("%s FileParse:excel file row[0] unexpect:%v", fun, details[0])
	}
	if details[0][0] != header[0] || details[0][1] != header[1] || details[0][2] != header[2] || details[0][3] != header[3] || details[0][4] != header[4] {
		return fmt.Errorf("%s FileParse:excel file header unexpect2 :%v. expect:%v", fun, details[0], header)
	}
	if len(details[1:]) > 2000 {
		return fmt.Errorf("单次导入最多 2000条数据, 当前导入数据量：%d", len(details[1:]))
	}
	for _, row := range details[1:] {
		if len(row) < 5 {
			return xerrors.New("参数错误，存在空列", code.InvalidParams)
		}
		question := strings.TrimSpace(row[0]) // 语料
		lang := strings.TrimSpace(row[1])     // 语种
		project := strings.TrimSpace(row[2])  // 关联游戏
		catID := strings.TrimSpace(row[3])    // 问题分类
		answer := strings.TrimSpace(row[4])   // 富文本答案
		// 1.检查必要字段是否为空
		if question == "" || lang == "" || project == "" || catID == "" || answer == "" {
			return xerrors.New("保存失败！有空项", code.InvalidParams)
		}
		questionContentHash := sign.Md5(question)
		// 2.同一个游戏下语料不得相同
		key := fmt.Sprintf("%s-%s", project, questionContentHash)
		if _, ok := has[key]; ok {
			return xerrors.New("保存失败！语料重复", code.InvalidParams)
		}
		has[key] = struct{}{}
		// 3.校验游戏下是否存在该问题分类
		catInfo, err := persistence.NewCat().GetDetailById(ctx, cast.ToUint32(catID))
		if err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				logger.Errorf(ctx.Request().Context(), "catid is not found.project=%s, catID=%s, question=%s", project, catID, question)
				return xerrors.New("保存失败！不存在的问题分类", code.InvalidParams)
			}
			logger.Errorf(ctx.Request().Context(), "%s GetDetailById:project:%s,lang:%s,catID:%s,questionContentHash:%s, err:%+v", fun, project, lang, catID, questionContentHash, err)
			return err
		}
		if catInfo.Project != project {
			logger.Errorf(ctx.Request().Context(), "catid is not match.project=%s, catID=%s, question=%s", project, catID, question)
			return xerrors.New("保存失败！存在问题分类与游戏对应错误", code.InvalidParams)
		}
		if catInfo.Level != 3 {
			return xerrors.New("保存失败！存在非三级问题分类", code.InvalidParams)
		}
		// 4.若历史存在相同语料，则为编辑
		var questionId uint64
		questionInfo, err := persistence.NewQuestionTicket().GetQuestionByProjectAndQst(nil, project, questionContentHash)
		// 编辑
		if err != nil {
			logger.Errorf(ctx.Request().Context(), "%s GetQuestionByProjectAndQst:project:%s,lang:%s,catID:%s,questionContentHash:%s, err:%+v", fun, project, lang, catID, questionContentHash, err)
			return xerrors.New(code.StatusText(code.DbError), code.DbError)
		}
		if questionInfo != nil {
			questionId = questionInfo.QuestionID
		}
		// 收集数据
		reqs = append(reqs, &pb.QuestionSaveReq{
			QuestionId:      int64(questionId),
			Project:         project,
			Lang:            lang,
			QuestionContent: question,
			CatId:           cast.ToUint32(catID),
			Answer:          answer,
		})
	}
	for _, req := range reqs {
		err := s.SaveQuestionDetail(ctx, req)
		if err != nil {
			logger.Errorf(ctx.Request().Context(), "%s SaveQuestionDetail:project:%s,lang:%s,catID:%s, err:%+v", fun, req.Project, req.Lang, req.CatId, err)
			return err
		}
	}
	return nil
}
