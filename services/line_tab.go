package services

import (
	"encoding/json"
	"github.com/labstack/echo/v4"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"ops-ticket-api/internal/cst"
	"ops-ticket-api/internal/elasticsearch"
	p "ops-ticket-api/internal/persistence"
	"ops-ticket-api/models"
	"ops-ticket-api/proto/pb"
	"ops-ticket-api/services/commsrv"
	"ops-ticket-api/utils"
	"sync"
)

func LineTabSave(ctx echo.Context, req *pb.LineTabAddReq) error {
	return p.NewLineTab().LineTabSave(ctx, req)
}

func LineTabDel(ctx echo.Context, req *pb.LineTabDelReq) error {
	return p.NewLineTab().LineTabDel(ctx, req)
}

// LineTabList line 搜索tab列表
func LineTabList(ctx echo.Context) (*pb.LineTabListResp, error) {
	resp, err := p.NewLineTab().LineTabList(ctx)
	if err != nil {
		return nil, err
	}
	return resp, nil
}

// LineTabCount line tab数量
func LineTabCount(ctx echo.Context) (*pb.LineTabCountResp, error) {
	permList, _ := commsrv.LoadPermGmList(ctx.Request().Context(), ctx.Get(cst.AccountInfoCtx).(string))
	if len(permList) < 0 {
		logger.Infof(ctx.Request().Context(), "GetAllTabInfo No game permissions available.")
		return nil, nil
	}
	// 获取所有tab内容
	tabList, err := p.NewLineTab().GetAllTabInfo(ctx, permList)
	if err != nil {
		return nil, err
	}
	// 用 map 按项目分组存储结果
	projectMap := make(map[string][]*pb.LineTabCountResp_TabCountDetail)
	var wg sync.WaitGroup
	// 控制并发量为20
	sem := make(chan struct{}, 20)
	results := make(chan *pb.LineTabCountResp_LineTabCount, len(tabList))

	for _, tab := range tabList {
		wg.Add(1)
		sem <- struct{}{}
		go func(tab *models.FpLineTab) {
			defer func() {
				<-sem
				wg.Done()
			}()
			searchDetail := pb.LineUserListReq{}
			if err := json.Unmarshal([]byte(tab.Detail), &searchDetail); err != nil {
				logger.Infof(ctx.Request().Context(), "failed to unmarshal detail, err:%v", err)
				return
			}
			// 筛选条件
			uids := utils.PrepareStringUIDForPropertyTerm(searchDetail.Uids)
			opts, err := optionsLineFilterEs(ctx, &searchDetail, uids)
			if err != nil {
				logger.Errorf(ctx.Request().Context(), "LineTabCount optionsFilterEs err:%v", err)
				return
			}

			// 查询
			count, err := elasticsearch.DefaultLineEsSvc.GetLineTabCount(ctx.Request().Context(), opts...)
			if err != nil {
				return
			}

			// 按项目分组加入 map
			results <- &pb.LineTabCountResp_LineTabCount{
				Tab: []*pb.LineTabCountResp_TabCountDetail{
					{TabName: tab.TabName, Count: uint64(count)},
				},
				Project: tab.Project,
			}
		}(tab)
	}
	wg.Wait()
	close(results)

	// 收集结果并根据项目分组
	for result := range results {
		projectMap[result.Project] = append(projectMap[result.Project], result.Tab...)
	}

	resp := &pb.LineTabCountResp{
		Detail: make([]*pb.LineTabCountResp_LineTabCount, 0, len(projectMap)),
	}

	for project, tabInfo := range projectMap {
		resp.Detail = append(resp.Detail, &pb.LineTabCountResp_LineTabCount{
			Tab:     tabInfo,
			Project: project,
		})
	}

	return resp, nil
}

func LineTabEdit(ctx echo.Context, req *pb.LineTabEditReq) error {
	return p.NewLineTab().LineTabEdit(ctx, req)
}

func LineTabUpdateSort(ctx echo.Context, req *pb.LineTabUpdateSortReq) error {
	return p.NewLineTab().LineTabUpdateSort(ctx, req)
}
