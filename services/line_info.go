package services

import (
	"fmt"
	"github.com/labstack/echo/v4"
	"github.com/olivere/elastic/v7"
	"github.com/spf13/cast"
	"github.com/spf13/viper"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"ops-ticket-api/internal/cst"
	"ops-ticket-api/internal/elasticsearch"
	"ops-ticket-api/internal/persistence"
	"ops-ticket-api/pkg/filter"
	"ops-ticket-api/pkg/formatter"
	"ops-ticket-api/proto/pb"
	"ops-ticket-api/utils"
	"strings"
	"time"
)

// LineAccountsCount  line玩家账号总数
func LineAccountsCount(ctx echo.Context, projects []interface{}) (int64, error) {
	total, err := elasticsearch.DefaultLineEsSvc.GetLineAccountsCount(ctx.Request().Context(), projects)
	if err != nil {
		logger.Error(ctx.Request().Context(), "GetLineAccountsCount get data from elasticsearch err", logger.Any("err", err))
		return 0, err
	}
	return total, nil
}

// LineWaitReplyAccountsCount  待回复玩家账号总数
func LineWaitReplyAccountsCount(ctx echo.Context, projects []interface{}) (int64, error) {
	total, err := elasticsearch.DefaultLineEsSvc.WaitReplyAccountsCount(ctx.Request().Context(), projects)
	if err != nil {
		logger.Error(ctx.Request().Context(), "Line WaitReplyAccountsCount get data from elasticsearch err", logger.Any("err", err))
		return 0, err
	}
	return total, nil
}

// LineMineWaitReplyAccountsCount  我的待回复玩家账号总数
func LineMineWaitReplyAccountsCount(ctx echo.Context, projects []interface{}) (int64, error) {
	maintainer := cast.ToString(ctx.Get(cst.AccountInfoCtx))
	total, err := elasticsearch.DefaultLineEsSvc.MineWaitReplyAccountsCount(ctx.Request().Context(), maintainer, projects)
	if err != nil {
		logger.Error(ctx.Request().Context(), "Line MineWaitReplyAccountsCount get data from elasticsearch err", logger.Any("err", err))
		return 0, err
	}
	return total, nil
}

func LinePool(ctx echo.Context, req *pb.LineUserListReq) ([]*pb.LinePoolInfo, uint32, error) {
	uids := utils.PrepareStringUIDForPropertyTerm(req.Uids)
	opts, err := optionsLineFilterEs(ctx, req, uids)
	if err != nil {
		return nil, 0, err
	}
	var esFields = []string{"project"}
	var sort []elastic.Sorter
	switch req.SortBy {
	//case pb.DcPoolSort_DcPoolSortWaitTm:
	//	sort = []elastic.Sorter{
	//		elastic.NewFieldSort("sort_wait_start_at").Asc(),
	//	}
	//case pb.DcPoolSort_DcPoolSortLastReplyTm:
	//	if req.Order == "desc" {
	//		sort = []elastic.Sorter{
	//			elastic.NewFieldSort("last_reply_time").Desc(),
	//		}
	//	} else {
	//		sort = []elastic.Sorter{
	//			elastic.NewFieldSort("last_reply_time").Asc(),
	//		}
	//	}
	case pb.DcPoolSort_DcPoolSortpaidAmount:
		if req.Order == "desc" {
			sort = []elastic.Sorter{
				elastic.NewFieldSort("pay_all").Desc(),
			}
		} else {
			sort = []elastic.Sorter{
				elastic.NewFieldSort("pay_all").Asc(),
			}
		}
	}
	dest, total, err := elasticsearch.DefaultLineEsSvc.GetLinePool(ctx.Request().Context(), int(req.Page), int(req.PageSize), esFields, sort, opts...)
	if err != nil {
		logger.Error(ctx.Request().Context(), "get discord data from elasticsearch err", logger.Any("err", err))
		return nil, 0, err
	}

	resp, err := formatter.DefaultLineFormatter.PoolLineInfoFormatEs(ctx, dest)
	if err != nil {
		return nil, 0, err
	}
	return resp, uint32(total), nil
}

// LinePoolExport line数据导出
func LinePoolExport(ctx echo.Context, req *pb.LineUserListReq) (string, error) {
	uids := utils.PrepareStringUIDForPropertyTerm(req.Uids)
	opts, err := optionsLineFilterEs(ctx, req, uids)
	if err != nil {
		return "", err
	}
	records, err := LineInfoExport(ctx, opts)
	if err != nil && err.Error() != "EOF" {
		return "", err
	}
	var (
		tagIds      = make([]uint32, 0)
		tagHas      = map[uint32]struct{}{}
		tagShow     = map[uint32]string{}
		channelIds  = make([]string, 0)
		channelHas  = map[string]struct{}{}
		channelShow = map[string]string{}
	)
	for _, row := range records {
		for _, tag := range row.Tag {
			if _, ok := tagHas[tag]; !ok && tag > 0 {
				tagHas[tag] = struct{}{}
				tagIds = append(tagIds, tag)
			}
		}
		if _, ok := channelHas[row.ChannelID]; !ok && row.ChannelID != "" {
			channelHas[row.ChannelID] = struct{}{}
			channelIds = append(channelIds, row.ChannelID)
		}
	}
	if tgs := persistence.NewTags().GetTagsSlice(ctx.Request().Context(), tagIds); len(tgs) > 0 {
		for k, v := range tgs {
			tagShow[k] = strings.Join(v, "-")
		}
	}
	if chs, err := persistence.NewLineInteractions().FetchChannelShows(ctx.Request().Context(), channelIds); err == nil {
		for k, v := range chs {
			channelShow[k] = v
		}
	}

	// export
	var dest = make([][]interface{}, 0)
	for i := range records {
		user := records[i]
		payAll := utils.PayAmountToFloat64(user.PayAll)
		payLastThirtyDays := utils.PayAmountToFloat64(user.PayLastThirtyDays)
		// 1:未回复、2:已回复
		status := ""
		if user.ReplyType == 1 {
			status = "未回复"
		} else {
			status = "已回复"
		}
		lastLogin := utils.TimeFormat(user.LastLogin)
		_chnShow := cast.ToString(user.ChannelID)
		if v, ok := channelShow[user.ChannelID]; ok {
			_chnShow = v
		}
		var tags = make([]string, len(user.Tag))
		for _ti, tag := range user.Tag {
			if _tShow, ok := tagShow[tag]; ok {
				tags[_ti] = _tShow
			} else {
				tags[_ti] = fmt.Sprintf("unknown[%d]", tag)
			}
		}

		detail := []interface{}{user.LineUserID, user.DisplayName, cast.ToString(user.UID) + "\t", cast.ToString(user.Sid),
			user.ChannelID, _chnShow, user.Project, status,
			cast.ToString(payAll), cast.ToString(payLastThirtyDays), lastLogin, cast.ToString(user.VipLevel),
			strings.Join(tags, "; "),
		}
		dest = append(dest, detail)
	}
	_fileName := fmt.Sprintf("line_export_%d.csv", time.Now().UnixNano())
	err = utils.CreateCsvFile(_fileName, viper.GetStringSlice("export_header.LinePoolExport.zh-cn"), dest)
	if err != nil {
		return "", err
	}
	return _fileName, err
}

func optionsLineFilterEs(ctx echo.Context, req *pb.LineUserListReq, uids []string) ([]elasticsearch.Option, error) {
	option, err := filter.NewAdHocWrapper().LinePool(ctx.Request().Context(), req, uids)
	if err != nil {
		return nil, err
	}
	return option, nil
}
