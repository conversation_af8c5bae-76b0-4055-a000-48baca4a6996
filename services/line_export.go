package services

import (
	"github.com/labstack/echo/v4"
	"ops-ticket-api/internal/elasticsearch"
	"ops-ticket-api/models"
)

// LineInfoExport line数据导出
func LineInfoExport(ctx echo.Context, opts []elasticsearch.Option) ([]*models.FpLineUserDoc, error) {
	lines, err := elasticsearch.DefaultLineEsSvc.GetLinePoolScroll(ctx.Request().Context(), opts...)
	if err != nil {
		return nil, err
	}
	dest := []*models.FpLineUserDoc{}
	for line := range lines {
		dest = append(dest, line)
	}
	return dest, nil
}
