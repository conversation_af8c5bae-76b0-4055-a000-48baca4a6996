// Copyright 2021 funplus Authors. All Rights Reserved.
// @Description: c端服务
// @Author: Darcy
// @Date: 2021/11/2 2:56 PM

package services

import (
	"ops-ticket-api/internal/code"
	"ops-ticket-api/internal/framework/xerrors"
	"ops-ticket-api/proto/pb"

	"ops-ticket-api/services/configure"

	dto "ops-ticket-api/internal/persistence"

	jsoniter "github.com/json-iterator/go"
	"github.com/labstack/echo/v4"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
)

type egressSvc struct{}

var DefaultEgressSvc = newEgressSvc()

func newEgressSvc() *egressSvc {
	return &egressSvc{}
}

// CatInfo 获取问题分类
func (svc *egressSvc) CatInfo(ctx echo.Context, project string, params *pb.CatSubRequest) (*pb.EgressCatInfoResp, error) {
	catInfo, err := configure.OldTicketCatInfo(ctx.Request().Context(), project, params.CatId, map[string]string{
		"country": params.CountryCode,
		"channel": params.Channel,
		"sid":     params.Sid,
	})
	if err != nil {
		return nil, err
	}
	if catInfo.CatID == 0 {
		return nil, xerrors.New(code.StatusText(code.NotFound), code.NotFound)
	}
	if !(catInfo.Enable == true) {
		return nil, xerrors.New("the category is disable", code.NotFound)
	}
	if pb.CatLevel_ThreeClassification != pb.CatLevel(catInfo.Level) {
		return nil, xerrors.New(code.StatusText(code.MissingParams), code.MissingParams)
	}

	// 获取多级分类名
	catList, _ := dto.NewCat().CatNameSlice(ctx.Request().Context(), params.CatId, params.Lang)
	// 获取最后一层分类名
	catName := catList[len(catList)-1]

	if catInfo.RelateType == code.RelateTypeProcess {
		return &pb.EgressCatInfoResp{
			CatId:      catInfo.CatID,
			CatName:    catName,
			RelateType: catInfo.RelateType,
			ProcessId:  catInfo.ProcessID,
			Category:   catName,
		}, nil
	}
	tpl, err := dto.NewTplLang(ctx.Request().Context()).TplInfoMultiLang(ctx.Request().Context(), catInfo.TplID, params.Lang)
	if err != nil {
		return nil, err
	}
	// todo filed 新增相关内容 questionList ,  or
	//[[{
	//	"field_name": "时间选择测试",
	//	"field_map": "",
	//	"is_required": true,
	//	"required_rule": "时间选择测试",
	//	"field_type": 3,
	//	"hint_field": "",
	//	"field_extend": {
	//		"only_date": false
	//	},
	//	"option_field": []
	//}]

	fields := make([]*FieldsDetail, 0)

	if err := jsoniter.ConfigFastest.UnmarshalFromString(tpl.Fields, &fields); err != nil {
		logger.Errorf(ctx.Request().Context(), "fields unmarshal err", err.Error())
		return nil, xerrors.New(err.Error(), code.Error)
	}

	questionList := make([]*pb.QuestionInfo, 0)

	chat := true
	for _, field := range fields {
		// 如果没有配置fieldKey且fieldMap无映射则走表单
		if field.FieldKey == "" && field.FieldMap == "" {
			chat = false
			break
		}
		if field.FieldKey == "" {
			continue
		}
		questionList = append(questionList, &pb.QuestionInfo{
			QuestionKey:  field.FieldKey,
			QuestionName: field.FieldName,
		})
	}

	return &pb.EgressCatInfoResp{
		CatId:        catInfo.CatID,
		CatName:      catName,
		QuestionList: questionList,
		RelateType:   catInfo.RelateType,
		TplId:        tpl.TplID,
		Tpl:          tpl.Tpl,
		Fields:       tpl.Fields,
		Category:     catName,

		ChatOrForm: getChatOrForm(len(questionList), chat),
	}, nil
}

func getChatOrForm(formLen int, baseChat bool) pb.ChatOrFormType {
	// 问题分类的问题数量大于5个 || 问题本身无配置fieldKey且fieldMap无映射则走表单
	if formLen < 5 && baseChat {
		return pb.ChatOrFormType_ChatOrFormTypeChat
	}
	return pb.ChatOrFormType_ChatOrFormTypeForm
}

type FieldsDetail struct {
	FieldName string `json:"field_name"`
	FieldMap  string `json:"field_map"`
	// key
	FieldKey     string                 `json:"field_key"`
	IsRequired   bool                   `json:"is_required"`
	RequiredRule string                 `json:"required_rule"`
	FieldType    uint32                 `json:"field_type"`
	HintField    string                 `json:"hint_field"`
	FieldExtend  map[string]interface{} `json:"field_extend"`
	OptionField  []*FieldsDetailOpt     `json:"option_field"`
}

type FieldsDetailOpt struct {
	Name  string `json:"name"`
	Value string `json:"value"`
}
