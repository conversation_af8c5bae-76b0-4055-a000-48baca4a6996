package services

import (
	"github.com/spf13/cast"
	"gorm.io/gorm"
	"ops-ticket-api/internal/persistence"
	"ops-ticket-api/models"
	"ops-ticket-api/proto/pb"
	"ops-ticket-api/utils"
	"time"
)

func NewQuestionTicketLogSrv() *questionTicketLogService {
	return &questionTicketLogService{}
}

type questionTicketLogService struct {
}

func (q *questionTicketLogService) QstSaveLog(tx *gorm.DB, project, qstHash string, isEdit bool) error {
	// 查询详情:
	qst, err := persistence.NewQuestionTicket().GetQuestionByProjectAndQst(tx, project, qstHash)
	if err != nil {
		return err
	}
	if qst == nil {
		return nil
	}
	log := &models.FpOpsTicketsQuestionLog{
		UniqueID:     cast.ToString(qst.QuestionID),
		Project:      qst.Project,
		Lang:         qst.Lang,
		BeforeDetail: "{}",
		AfterDetail:  utils.To<PERSON>son(qst),
		CreatedAt:    qst.UpdatedAt,
		Operator:     qst.Creator,
	}
	if isEdit {
		log.OperationAction = int8(pb.OpAction_OpActionUpdate)
	} else {
		log.OperationAction = int8(pb.OpAction_OpActionAdd)
	}
	return persistence.NewQuestionTicketLog().AddQuestionOpLog(tx, log)
}

func (q *questionTicketLogService) QstDelLog(tx *gorm.DB, qst *models.FpOpsTicketsQuestion, operator string) error {
	if qst == nil {
		return nil
	}

	log := &models.FpOpsTicketsQuestionLog{
		UniqueID:        cast.ToString(qst.QuestionID),
		Project:         qst.Project,
		Lang:            qst.Lang,
		BeforeDetail:    utils.ToJson(qst),
		AfterDetail:     "{}",
		CreatedAt:       uint64(time.Now().Unix()),
		Operator:        operator,
		OperationAction: int8(pb.OpAction_OpActionDelete),
	}
	return persistence.NewQuestionTicketLog().AddQuestionOpLog(tx, log)
}
