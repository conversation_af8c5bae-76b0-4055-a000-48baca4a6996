package pc_assist

import (
	"encoding/json"
	"fmt"
	"ops-ticket-api/internal/framework/middleware"

	"github.com/spf13/viper"
	"golang.org/x/net/context"
)

type PushPcAssistRequest struct {
	// 游戏项目标识
	GameProject string `protobuf:"bytes,1,opt,name=game_project,json=gameProject,proto3" json:"game_project"`
	// 用户ID
	Uid string `protobuf:"bytes,2,opt,name=uid,proto3" json:"uid"`
	// FPID
	Fpid string `protobuf:"bytes,3,opt,name=fpid,proto3" json:"fpid"`
	// 工单ID
	TicketId string `protobuf:"bytes,4,opt,name=ticket_id,json=ticketId,proto3" json:"ticket_id"`
	// 跳转链接
	JumpUrl string `protobuf:"bytes,5,opt,name=jump_url,json=jumpUrl,proto3" json:"jump_url"`
}

func PushPcAssist(ctx context.Context, req *PushPcAssistRequest) error {
	// 请求成功则表示使用成功
	path := fmt.Sprintf("%s%s", viper.GetString("thirdparty.pc_assist.host"), viper.GetString("thirdparty.pc_assist.notice_push"))

	bodyBytes, err := json.Marshal(req)
	if err != nil {
		return err
	}

	sign := middleware.SignBodyWithSalt(req, viper.GetString("auth.pc_salt"))

	header := map[string]string{
		"Sign": sign,
	}

	_, err = acClient.PostJson(ctx, path, string(bodyBytes), header)
	if err != nil {
		return err
	}
	return nil
}
