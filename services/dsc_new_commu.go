package services

import (
	"fmt"
	"github.com/labstack/echo/v4"
	"github.com/spf13/cast"
	"github.com/spf13/viper"
	p "ops-ticket-api/internal/persistence"
	"ops-ticket-api/proto/pb"
	"ops-ticket-api/utils"
	"time"
)

func DiscordNewCommuSave(ctx echo.Context, req *pb.DiscordNewCommuRecordAddReq) error {
	return p.NewDiscordNewCommu().DiscordCommuSave(ctx, req)
}

func DiscordNewCommuEdit(ctx echo.Context, req *pb.DiscordNewCommuRecordEditReq) error {
	return p.NewDiscordNewCommu().DiscordCommuEdit(ctx, req)
}

// DiscordNewCommuList 沟通记录列表
func DiscordNewCommuList(ctx echo.Context, req *pb.DiscordNewCommuRecordListReq) (*pb.DiscordNewCommuRecordListResp, error) {
	list, total, err := p.NewDiscordNewCommu().DiscordCommuList(ctx, req, false)
	if err != nil {
		return nil, err
	}
	return &pb.DiscordNewCommuRecordListResp{
		CurrentPage: req.Page,
		PerPage:     req.PageSize,
		Total:       total,
		Data:        list,
	}, nil
}

func DiscordNewCommuListExport(ctx echo.Context, req *pb.DiscordNewCommuRecordListReq) (string, error) {
	records, _, err := p.NewDiscordNewCommu().DiscordCommuList(ctx, req, true)
	if err != nil {
		return "", err
	}
	var dest = make([][]interface{}, 0)
	for i := range records {
		detail := records[i]
		handleStatus := ""
		if detail.HandleStatus == 1 {
			handleStatus = "处理中"
		} else {
			handleStatus = "已完成"
		}
		//判断是否为图片
		remark := detail.Remark
		if utils.ContainsValidImage(remark) {
			remark = "【image】"
		}
		record := []interface{}{detail.CommuDate, detail.Project, cast.ToString(detail.Uid) + "\t", detail.Sid, detail.NickName, cast.ToString(detail.PayAll), detail.Question, detail.Category, handleStatus, remark, detail.Operator, detail.Maintainer}
		dest = append(dest, record)
	}
	_fileName := fmt.Sprintf("discord_commu_record_export_%d.csv", time.Now().UnixNano())
	err = utils.CreateCsvFile(_fileName, viper.GetStringSlice("export_header.DiscordCommuRecordExport.zh-cn"), dest)
	if err != nil {
		return "", err
	}
	return _fileName, err
}
