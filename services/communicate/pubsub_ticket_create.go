package communicate

import (
	"context"
	"github.com/cskr/pubsub/v2"
	"github.com/labstack/echo/v4"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"ops-ticket-api/proto/pb"
	"time"
)

var (
	ticketCreatePubSubTopic = "ticket_create"
	ticketCreatePubSub      = pubsub.New[string, *TicketPubSt](10240)
)

type TicketPubSt struct {
	Ctx        echo.Context
	TicketId   uint64
	QueryParam *pb.TkCreateReq
}

func PubTicketCreate(ctx echo.Context, ticketId uint64, param *pb.TkCreateReq) error {
	var detail = &TicketPubSt{
		Ctx:        ctx,
		TicketId:   ticketId,
		QueryParam: param,
	}
	//fmt.Println("---------start pub ticket create")
	ticketCreatePubSub.Pub(detail, ticketCreatePubSubTopic)
	return nil
}
func init() {
	// 订阅工单创建日志 - 后续异步逻辑统一走此， 多个任务通过多个 sub 来实现； 异步等待任务 通过启goroutine来实现
	go subCreateTicketToLog()
	// todo - 自动回复
	// todo - L项目 发送飞书通知
	// todo - L项目 自动生成 Tapd 任务单
}

// subCreateTicketToLog only console create log
func subCreateTicketToLog() {
	fun := "communicate.subCreateTicketToLog -->"
	for v := range ticketCreatePubSub.Sub(ticketCreatePubSubTopic) {
		//fmt.Println("---------start sub ticket create log")
		time.Sleep(time.Millisecond * 500)
		if v == nil {
			logger.Errorf(context.TODO(), "%s subCreateTicketToLog: detail is nil", fun)
			continue
		}
		if v.Ctx == nil {
			logger.Errorf(context.TODO(), "%s subCreateTicketToLog: detail.Ctx is nil", fun)
		}
		v.Ctx.Request().WithContext(context.WithValue(v.Ctx.Request().Context(), "ticket_id", v.TicketId))
		v.Ctx.Set("ticket_id", v.TicketId)
		logger.Infof(v.Ctx.Request().Context(), "%s ticket create pubsub log. ticketId:%d. param:%+v", fun, v.TicketId, v.QueryParam)
	}
}
