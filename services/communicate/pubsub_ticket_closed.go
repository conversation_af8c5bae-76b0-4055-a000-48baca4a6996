package communicate

import (
	"context"
	"github.com/cskr/pubsub/v2"
	"github.com/labstack/echo/v4"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"ops-ticket-api/proto/pb"
	"ops-ticket-api/services/ai_tag"
	"time"
)

var (
	ticketClosedPubSubTopic = "ticket_closed"
	ticketClosedPubSub      = pubsub.New[string, *TicketPubClosedSt](10240)
)

type TicketPubClosedSt struct {
	Ctx        echo.Context
	TicketId   uint64
	QueryParam *pb.TicketTransferReq
}

func PubTicketClosed(ctx echo.Context, ticketId uint64, param *pb.TicketTransferReq) error {
	var detail = &TicketPubClosedSt{
		Ctx:        ctx,
		TicketId:   ticketId,
		QueryParam: param,
	}
	//fmt.Println("---------start pub ticket create")
	ticketClosedPubSub.Pub(detail, ticketClosedPubSubTopic)
	return nil
}
func init() {
	// ai打标 - 用于双周报
	go processAiTag()
}

// processAiTag 对单条工单做 AI 打标并写库
func processAiTag() error {
	fun := "communicate.processAiTag -->"
	for detail := range ticketClosedPubSub.Sub(ticketClosedPubSubTopic) {
		time.Sleep(time.Millisecond * 200)
		if detail == nil {
			logger.Errorf(context.TODO(), "%s : detail is nil", fun)
			continue
		}
		if detail.Ctx == nil {
			logger.Errorf(context.TODO(), "%s : detail.Ctx is nil", fun)
		}
		ctx := context.Background()
		ticketID := detail.TicketId
		err := ai_tag.AiTagHandle(ctx, ticketID)
		if err != nil {
			logger.Errorf(ctx, "%s : ai tag ticket %d err:%v", fun, ticketID, err)
			continue
		}
		logger.Infof(ctx, "%s : ai tag ticket %d success", fun, ticketID)
	}

	return nil
}
