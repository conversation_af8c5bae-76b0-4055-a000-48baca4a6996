package communicate

import (
	"context"
	"github.com/asaskevich/EventBus"
	"github.com/cskr/pubsub/v2"
	"github.com/labstack/echo/v4"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"ops-ticket-api/proto/pb"
)

var (
	examineTaskPubSubTopic = "examine_task_pub"
	examineTaskPubSub      = pubsub.New[string, *ExamineTaskPubSt](10240)

	ExamineOrderFinishedTopic = "examine_order_finished"
	ExamineOrderFinishedBus   = EventBus.New()
)

type ExamineTaskPubSt struct {
	Ctx    echo.Context
	TaskId uint64
}
type ExamineOrderFinishedPubSt struct {
	Ctx         context.Context
	ExamineType pb.ExamineTaskGroupDf
	TaskId      uint64
	DetailId    uint64
}

func PubExamineTaskCreate(ctx echo.Context, taskId uint64) error {
	var detail = &ExamineTaskPubSt{
		Ctx:    ctx,
		TaskId: taskId,
	}
	//fmt.Println("---------start pub examine task create")
	examineTaskPubSub.Pub(detail, examineTaskPubSubTopic)
	return nil
}
func SubExamineTaskCreate() chan *ExamineTaskPubSt {
	return examineTaskPubSub.Sub(examineTaskPubSubTopic)
}

func PubExamineOrderFinished(ctx context.Context, taskId uint64, examineType pb.ExamineTaskGroupDf, detailId uint64) {
	detail := &ExamineOrderFinishedPubSt{
		Ctx:         ctx,
		ExamineType: examineType,
		TaskId:      taskId,
		DetailId:    detailId,
	}
	logger.Infof(ctx, "PubExamineOrderFinished: %v", detail)
	ExamineOrderFinishedBus.Publish(ExamineOrderFinishedTopic, detail)
}
