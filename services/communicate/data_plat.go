package communicate

import (
	"context"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"ops-ticket-api/internal/framework/cache/keys"
	"ops-ticket-api/internal/framework/cache/rds"
	"ops-ticket-api/utils"
)

type (
	DataPlatPubTp            string
	DataPlatItemAndI18nPubDf struct {
		Type    DataPlatPubTp `json:"type"`    // 类型： item/i18n
		Project string        `json:"project"` // 项目
	}
)

var (
	DataPlatPubTpItem DataPlatPubTp = "item"
	DataPlatPubTpI18n DataPlatPubTp = "i18n"
)

func PublicDataPlatItemI18nChange(ctx context.Context, tp DataPlatPubTp, project string) error {
	dt := DataPlatItemAndI18nPubDf{
		Type:    tp,
		Project: project,
	}
	err := rds.NewRCache().Publish(ctx, keys.DataPlatItemAndI18nPubKey, utils.ToJson(dt)).Err()
	if err != nil {
		logger.Errorf(ctx, "PublicDataPlatItemI18nChange err:%v. dt:%+v", err, dt)
	}
	return err
}
