{"go.useLanguageServer": true, "go.alternateTools": {"gopls": "/Users/<USER>/go/bin/gopls"}, "go.gopath": "/Users/<USER>/go", "go.toolsGopath": "/Users/<USER>/go", "gopls": {"ui.semanticTokens": true, "formatting.gofumpt": true, "ui.diagnostic.analyses": {"unusedparams": true, "shadow": true, "fieldalignment": true, "nilness": true, "unusedwrite": true, "useany": true}, "ui.completion.usePlaceholders": true, "ui.documentation.hoverKind": "FullDocumentation", "ui.completion.matcher": "fuzzy", "ui.completion.experimentalPostfixCompletions": true, "ui.diagnostic.annotations": {"bounds": true, "escape": true, "inline": true}, "ui.codelenses": {"gc_details": true, "regenerate_cgo": true, "generate": true, "test": true, "tidy": true, "upgrade_dependency": true, "vendor": true}, "ui.completion.completionBudget": "1s", "ui.navigation.importShortcut": "Definition", "ui.inlayhint.hints": {"assignVariableTypes": true, "compositeLiteralFields": true, "compositeLiteralTypes": true, "constantValues": true, "functionTypeParameters": true, "parameterNames": true, "rangeVariableTypes": true}, "imports.granularity": "package"}, "[go]": {"editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.organizeImports": "explicit", "source.fixAll": "explicit"}, "editor.snippetSuggestions": "inline", "editor.defaultFormatter": "golang.go", "editor.semanticHighlighting.enabled": true, "editor.suggestSelection": "recentlyUsed", "editor.suggest.snippetsPreventQuickSuggestions": false, "editor.suggest.localityBonus": true, "editor.wordBasedSuggestions": "matchingDocuments", "editor.parameterHints.enabled": true, "editor.tokenColorCustomizations": {"textMateRules": [{"scope": "variable.other.go", "settings": {"foreground": "#9876AA"}}, {"scope": "keyword.go", "settings": {"foreground": "#CC7832", "fontStyle": "bold"}}, {"scope": "entity.name.function.go", "settings": {"foreground": "#FFC66D"}}, {"scope": "string.quoted.double.go", "settings": {"foreground": "#6A8759"}}, {"scope": "constant.numeric.go", "settings": {"foreground": "#6897BB"}}, {"scope": "constant.language.go", "settings": {"foreground": "#CC7832", "fontStyle": "bold"}}, {"scope": "entity.name.type.go", "settings": {"foreground": "#A9B7C6"}}, {"scope": "comment.line.double-slash.go", "settings": {"foreground": "#808080", "fontStyle": "italic"}}, {"scope": "entity.name.package.go", "settings": {"foreground": "#057ef0"}}, {"scope": "keyword.other.todo.go", "settings": {"foreground": "#FF8C00", "fontStyle": "bold"}}, {"scope": ["comment.line.double-slash.go", "comment.block.go"], "settings": {"foreground": "#808080", "fontStyle": "italic"}}, {"scope": ["comment.line.double-slash.go", "comment.block.go"], "settings": {"foreground": "#FF8C00", "fontStyle": "bold italic", "background": "#3D3D3D"}, "name": "Special comments: TODO, FIXME, NOTE, HACK, WARN, XXX", "match": "(?i)(\\W|^)(todo|fixme|note|hack|warn|xxx)\\s*:?"}]}}, "go.toolsManagement.autoUpdate": false, "go.lintTool": "golangci-lint", "go.lintFlags": ["--fast"], "go.formatTool": "gofumpt", "go.coverOnSave": false, "go.testOnSave": false, "go.languageServerFlags": ["-rpc.trace", "-logfile=/tmp/gopls.log", "-mode=stdio", "-remote=auto"], "go.autocompleteUnimportedPackages": true, "go.gotoSymbol.includeGoroot": true, "go.gotoSymbol.includeImports": true, "go.inferGopath": false, "go.useCodeSnippetsOnFunctionSuggest": true, "go.useCodeSnippetsOnFunctionSuggestWithoutType": true, "go.suggestionStrategy": "auto", "workbench.colorCustomizations": {"editor.lineHighlightBackground": "#2F3641", "editor.selectionBackground": "#214283", "editor.selectionHighlightBackground": "#32374D", "editor.wordHighlightBackground": "#32374D", "editor.wordHighlightStrongBackground": "#32374D", "editorLineNumber.foreground": "#606366", "editorLineNumber.activeForeground": "#FF8C00"}, "editor.semanticTokenColorCustomizations": {"enabled": true, "rules": {"parameter": "#9876AA", "variable": "#9876AA", "property": "#9876AA", "function": "#FFC66D", "method": "#FFC66D", "class": "#A9B7C6", "enum": "#A9B7C6", "interface": "#A9B7C6", "typeParameter": "#A9B7C6", "namespace": "#8FB66B", "type": "#A9B7C6", "parameter.declaration": "#9876AA", "variable.declaration": "#9876AA", "property.declaration": "#9876AA", "*.declaration": {"bold": true}}}, "go.addTags": {"tags": "json", "options": "json=omitempty", "transform": "snakecase"}, "go.buildFlags": ["-tags=integration"], "go.toolsEnvVars": {"GO111MODULE": "on"}, "go.formatFlags": ["-s"], "go.editorContextMenuCommands": {"toggleTestFile": true, "addTags": true, "removeTags": true, "fillStruct": true, "testAtCursor": true, "testFile": true, "testPackage": true, "generateTestForFunction": true, "generateTestForFile": true, "generateTestForPackage": true, "addImport": true, "testCoverage": true, "playground": true}, "todo-tree.highlights.defaultHighlight": {"icon": "check", "type": "text", "foreground": "#FF8C00", "background": "#3D3D3D", "opacity": 50, "iconColour": "#FF8C00"}, "todo-tree.highlights.customHighlight": {"TODO": {"icon": "check", "foreground": "#FF8C00", "background": "#3D3D3D", "iconColour": "#FF8C00"}, "FIXME": {"foreground": "#FF0000", "background": "#3D3D3D", "iconColour": "#FF0000"}, "HACK": {"foreground": "#FF00FF", "background": "#3D3D3D", "iconColour": "#FF00FF"}}, "todo-tree.general.tags": ["TODO", "FIXME", "HACK", "NOTE", "XXX"], "todo-tree.regex.regex": "((//|#|<!--|;|/\\*|^)\\s*($TAGS)|^\\s*- \\[ \\])", "todo-tree.regex.regexCaseSensitive": false, "editor.tokenColorCustomizations": {"comments": {"foreground": "#808080", "fontStyle": "italic"}, "textMateRules": [{"scope": "comment.line.double-slash.go", "settings": {"foreground": "#808080", "fontStyle": "italic"}}, {"scope": "comment.line.todo.go", "settings": {"foreground": "#FF8C00", "fontStyle": "bold", "background": "#3D3D3D"}}]}, "better-comments.tags": [{"tag": "TODO", "color": "#FF8C00", "strikethrough": false, "underline": false, "backgroundColor": "#3D3D3D", "bold": true, "italic": false}, {"tag": "FIXME", "color": "#FF0000", "strikethrough": false, "underline": false, "backgroundColor": "#3D3D3D", "bold": true, "italic": false}]}