package utils

import (
	"fmt"
	"regexp"
	"strings"
)

type WebhookRequest struct {
	Destination string  `json:"destination"`
	Events      []Event `json:"events"`
}

type Event struct {
	Type              string             `json:"type"`
	Message           *Message           `json:"message,omitempty"`
	ReplyToken        string             `json:"replyToken,omitempty"`
	Timestamp         int64              `json:"timestamp"`
	Source            Source             `json:"source"`
	WebhookEventId    string             `json:"webhookEventId"`
	Mode              string             `json:"mode"`
	DeliveryContext   DeliveryContext    `json:"deliveryContext"`
	Unsend            Unsend             `json:"unsend,omitempty"`
	Follow            Follow             `json:"follow,omitempty"`
	Joined            *Joined            `json:"joined,omitempty"`
	Left              *Left              `json:"left,omitempty"`
	Postback          *Postback          `json:"postback,omitempty"`
	VideoPlayComplete *VideoPlayComplete `json:"videoPlayComplete,omitempty"`
	Beacon            *Beacon            `json:"beacon,omitempty"`
	Link              *AccountLink       `json:"link,omitempty"`
}

type Message struct {
	Type                string           `json:"type"`
	ID                  string           `json:"id,omitempty"`
	QuoteToken          string           `json:"quoteToken,omitempty"`
	Text                string           `json:"text,omitempty"`
	Emojis              []Emoji          `json:"emojis,omitempty"`
	ContentProvider     *ContentProvider `json:"contentProvider,omitempty"`
	Duration            int              `json:"duration,omitempty"`
	StickerId           string           `json:"stickerId,omitempty"`
	PackageId           string           `json:"packageId,omitempty"`
	StickerResourceType string           `json:"stickerResourceType,omitempty"`
	Keywords            []string         `json:"keywords,omitempty"`
	Title               string           `json:"title,omitempty"`
	Address             string           `json:"address,omitempty"`
	Latitude            float64          `json:"latitude,omitempty"`
	Longitude           float64          `json:"longitude,omitempty"`
	FileName            string           `json:"fileName,omitempty"`
	FileSize            int32            `json:"fileSize,omitempty"`
}

type Source struct {
	Type    string `json:"type"`
	UserId  string `json:"userId"`
	GroupId string `json:"groupId,omitempty"`
	RoomId  string `json:"roomId,omitempty"`
}

type ContentProvider struct {
	Type               string `json:"type"`
	OriginalContentUrl string `json:"originalContentUrl,omitempty"`
	PreviewImageUrl    string `json:"previewImageUrl,omitempty"`
}

type Emoji struct {
	Index     int    `json:"index"`
	Length    int    `json:"length"`
	ProductID string `json:"productId"`
	EmojiID   string `json:"emojiId"`
}

type DeliveryContext struct {
	IsRedelivery bool `json:"isRedelivery"`
}

type Unsend struct {
	MessageId string `json:"messageId"`
}

type Follow struct {
	IsUnblocked bool `json:"isUnblocked"`
}

type Joined struct {
	Members []Member `json:"members"`
}

type Left struct {
	Members []Member `json:"members"`
}

type Postback struct {
	Data   string                 `json:"data"`
	Params map[string]interface{} `json:"params,omitempty"`
}

type VideoPlayComplete struct {
	TrackingId string `json:"trackingId"`
}

type Beacon struct {
	Hwid string `json:"hwid"`
	Type string `json:"type"`
}

type AccountLink struct {
	Result string `json:"result"`
	Nonce  string `json:"nonce"`
}

type Member struct {
	Type   string `json:"type"`
	UserId string `json:"userId"`
}

func ReconstructMessage(msg *Message) string {
	reconstructed := strings.Builder{}
	// 定义正则表达式，匹配括号包裹的内容
	regex := regexp.MustCompile(`\([^)]*\)`)
	// 使用 ReplaceAllString 将表情符号统一替换为 $
	text := regex.ReplaceAllString(msg.Text, "$")
	stringList := strings.Split(text, "$")
	for i, emoji := range msg.Emojis {
		reconstructed.WriteString(stringList[i])
		// 插入 emoji 的占位符文本
		emojiPlaceholder := fmt.Sprintf("product_id:%s,emoji_id:%s,", emoji.ProductID, emoji.EmojiID)
		reconstructed.WriteString(emojiPlaceholder)
	}
	reconstructed.WriteString(stringList[len(stringList)-1])
	return reconstructed.String()
}

// CheckLineFile 校验文件是否合法
func CheckLineFile(fileType, format string, fileSize int64, isPreview bool) bool {
	switch fileType {
	case "image":
		if format != "jpeg" && format != "png" {
			return false
		}
		if !isPreview {
			if fileSize > 1024*1024*10 {
				return false
			}
		} else {
			if fileSize > 1024*1024 {
				return false
			}
		}
	case "video":
		if !isPreview {
			if format != "mp4" {
				return false
			}
			if fileSize > 1024*1024*200 {
				return false
			}
		} else {
			if format != "jpeg" && format != "png" {
				return false
			}
			if fileSize > 1024*1024 {
				return false
			}
		}
	case "audio":
		if format != "mp3" && format != "m4a" {
			return false
		}
		if fileSize > 1024*1024*200 {
			return false
		}
	}
	return true
}
