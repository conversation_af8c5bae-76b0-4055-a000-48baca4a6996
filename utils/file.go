// Copyright 2021 funplus Authors. All Rights Reserved.
// @Description: 工具类
// @Author: Darcy
// @Date: 2021/10/13 2:07 PM

package utils

import (
	"crypto/tls"
	"encoding/csv"
	"fmt"
	"github.com/xuri/excelize/v2"
	"io"
	"mime"
	"net/http"
	"net/url"
	"os"
	"path"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"github.com/google/uuid"
	"github.com/spf13/cast"
	"github.com/spf13/viper"
)

const maxFileSize = 5 * 1024 * 1024 // 5MB

// FileTypeCheck 文件类型检查
func FileTypeCheck(fileType, ext string) bool {
	switch fileType {
	case "image":
		for _, iter := range viper.GetStringSlice("allow_ext.image.ext") {
			if ext == iter {
				return true
			}
		}
	case "video":
		for _, iter := range viper.GetStringSlice("allow_ext.video.ext") {
			if ext == iter {
				return true
			}
		}
	}
	return false
}

// FileSizeCheck 文件大小检查
func FileSizeCheck(fileType string, size uint64) bool {
	switch fileType {
	case "image":
		return size < viper.GetUint64("allow_ext.image.size") && size > 0
	case "video":
		return size < viper.GetUint64("allow_ext.video.size") && size > 0
	default:
		return false
	}
}

func GenFileName(path string) string {
	uuidStr := uuid.New().String()
	now := cast.ToString(time.Now().Unix())
	ext := filepath.Ext(path)
	return strings.ReplaceAll(uuidStr, "-", "") + now + ext
}

func CreateCsv(filename, header, language string) (*csv.Writer, *os.File, error) {
	f, err := os.OpenFile("./"+filename, os.O_CREATE|os.O_WRONLY, 0644)
	if err != nil {
		return nil, nil, err
	}
	// 写入boom头，防止中文乱码
	if _, err := f.WriteString("\xEF\xBB\xBF"); err != nil {
		return nil, f, err
	}
	c := csv.NewWriter(f)
	// 写入列名称
	if err := c.Write(viper.GetStringSlice("export_header." + header + "." + language)); err != nil {
		return nil, f, err
	}
	c.Flush()
	return c, f, c.Error()
}

// ToCSV csv文件
func ToCSV(c *csv.Writer, record [][]string) error {
	for _, r := range record {
		col := make([]string, 0)
		for _, v := range r {
			col = append(col, v)
		}
		if err := c.Write(col); err != nil {
			continue
		}
	}
	c.Flush()
	return c.Error()
}

func ExtractFileNameFromURL(fileURL string) (string, error) {
	u, err := url.Parse(fileURL)
	if err != nil {
		return "", err
	}
	// 获取路径中的文件名
	fileName := path.Base(u.Path)
	// 如果URL没有提供文件名，可以尝试从Content-Disposition中获取
	if fileName == "" {
		resp, err := http.Head(fileURL)
		if err != nil {
			return "", err
		}
		defer resp.Body.Close()
		if cd := resp.Header.Get("Content-Disposition"); cd != "" {
			_, params, err := mime.ParseMediaType(cd)
			if err != nil {
				return "", err
			}
			fileName = params["filename"]
		}
	}
	return fileName, nil
}

func DownloadAndValidateFile(fileURL string) (io.ReadCloser, error) {
	resp, err := http.Get(fileURL)
	if err != nil {
		return nil, err
	}
	// 校验文件大小
	contentLength, err := strconv.Atoi(resp.Header.Get("Content-Length"))
	if err != nil || contentLength > maxFileSize {
		resp.Body.Close()
		return nil, fmt.Errorf("file size exceeds limit of 5MB")
	}
	return resp.Body, nil
}

func ReadExcelOnline(url string) ([][]string, error) {
	client := &http.Client{
		Transport: &http.Transport{
			TLSClientConfig: &tls.Config{InsecureSkipVerify: true},
		},
	}
	resp, err := client.Get(url)
	if err != nil {
		return nil, err

	}
	if resp == nil {
		return nil, nil
	}

	if resp.Body == nil {
		return nil, nil
	}
	defer resp.Body.Close()
	f, err := excelize.OpenReader(resp.Body)
	data, err := f.GetRows(f.GetSheetName(0))
	return data, err
}
