package utils

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/gabriel-vasile/mimetype"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"io"
	"net"
	"net/http"
	"ops-ticket-api/internal/framework/stores/s3"
	"strings"
	"time"
)

var (
	httpClient           = &http.Client{}
	linePushMsgURL       = "https://api.line.me/v2/bot/message/push"
	lineGetContentPrefix = "https://api-data.line.me/v2/bot/message"
)

// TextMessage 结构体定义文本消息
type TextMessage struct {
	Type   string  `json:"type"`
	Text   string  `json:"text"`
	Emojis []Emoji `json:"emojis,omitempty"`
}

func NewTextMessage(text string, emojis []Emoji) *TextMessage {
	return &TextMessage{
		Type:   "text",
		Text:   text,
		Emojis: emojis,
	}
}

// ImageMessage 结构体定义图片消息
type ImageMessage struct {
	Type               string `json:"type"`
	OriginalContentURL string `json:"originalContentUrl"`
	PreviewImageURL    string `json:"previewImageUrl"`
}

func NewImageMessage(originalContentURL, previewImageURL string) *ImageMessage {
	return &ImageMessage{
		Type:               "image",
		OriginalContentURL: originalContentURL,
		PreviewImageURL:    previewImageURL,
	}
}

// VideoMessage 结构体定义视频消息
type VideoMessage struct {
	Type               string `json:"type"`
	OriginalContentURL string `json:"originalContentUrl"`
	PreviewImageURL    string `json:"previewImageUrl"`
}

func NewVideoMessage(originalContentURL, previewImageURL string) *VideoMessage {
	return &VideoMessage{
		Type:               "video",
		OriginalContentURL: originalContentURL,
		PreviewImageURL:    previewImageURL,
	}
}

// AudioMessage 结构体定义音频消息
type AudioMessage struct {
	Type               string `json:"type"`
	OriginalContentURL string `json:"originalContentUrl"`
	Duration           int    `json:"duration"`
}

func NewAudioMessage(originalContentURL string, duration int) *AudioMessage {
	return &AudioMessage{
		Type:               "audio",
		OriginalContentURL: originalContentURL,
		Duration:           duration,
	}
}

// LocationMessage 结构体定义位置消息
type LocationMessage struct {
	Type      string  `json:"type"`
	Title     string  `json:"title"`
	Address   string  `json:"address"`
	Latitude  float64 `json:"latitude"`
	Longitude float64 `json:"longitude"`
}

func NewLocationMessage(title, address string, latitude, longitude float64) *LocationMessage {
	return &LocationMessage{
		Type:      "location",
		Title:     title,
		Address:   address,
		Latitude:  latitude,
		Longitude: longitude,
	}
}

// StickerMessage 结构体定义贴纸消息
type StickerMessage struct {
	Type      string `json:"type"`
	PackageID string `json:"packageId"`
	StickerID string `json:"stickerId"`
}

func NewStickerMessage(packageID, stickerID string) *StickerMessage {
	return &StickerMessage{
		Type:      "sticker",
		PackageID: packageID,
		StickerID: stickerID,
	}
}

type SentMessageResponse struct {
	SentMessages []struct {
		ID         string `json:"id"`
		QuoteToken string `json:"quoteToken"`
	} `json:"sentMessages"`
}

func LinePushMessage(to, accessToken string, messages []interface{}) (*SentMessageResponse, error) {
	// 创建消息体
	messageBody := struct {
		To       string        `json:"to"`
		Messages []interface{} `json:"messages"`
	}{
		To:       to,
		Messages: messages,
	}
	// 编码 JSON 消息
	var buf bytes.Buffer
	if err := json.NewEncoder(&buf).Encode(messageBody); err != nil {
		return nil, fmt.Errorf("LinePushMessage encoding JSON err: %v", err)
	}
	// 输出请求体用于调试
	fmt.Printf("Request Body is %v\n", buf.String())
	// 发送 HTTP 请求
	req, err := http.NewRequest("POST", linePushMsgURL, &buf)
	if err != nil {
		return nil, fmt.Errorf("LinePushMessage creating request err: %v", err)
	}
	retryKey, err := GenerateRetryKey()
	if err != nil {
		return nil, fmt.Errorf("LinePushMessager generate retry key err: %v", err)
	}
	req.Header.Set("Authorization", "Bearer "+accessToken)
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("X-Line-Retry-Key", retryKey)
	// 定义重试次数和间隔
	const maxRetries = 3
	const retryInterval = 500 * time.Millisecond
	for attempts := 0; attempts < maxRetries; attempts++ {
		// 发送请求
		resp, err := httpClient.Do(req)
		// 检查是否因超时或网络错误失败
		if err != nil {
			// 重试
			if errors.Is(err, context.DeadlineExceeded) || errors.Is(err, net.ErrClosed) {
				time.Sleep(retryInterval)
				continue
			}
			return nil, fmt.Errorf("request failed: %v", err)
		}
		// 确保响应资源的释放
		defer resp.Body.Close()
		// 检查响应状态码
		if resp.StatusCode == http.StatusOK {
			// 成功请求，不再重试
			body, err := io.ReadAll(resp.Body)
			if err != nil {
				return nil, fmt.Errorf("failed to read response: %v", err)
			}
			var sentResponse SentMessageResponse
			if err = json.Unmarshal(body, &sentResponse); err != nil {
				return nil, fmt.Errorf("failed to decode response: %v", err)
			}
			return &sentResponse, nil
		} else if resp.StatusCode == 500 {
			// 如果是500 错误状态码，延时重试
			time.Sleep(retryInterval)
			continue
		} else if resp.StatusCode >= 400 && resp.StatusCode < 500 {
			// 4xx错误，直接返回，不再重试
			body, _ := io.ReadAll(resp.Body)
			fmt.Printf("Resp Body is %v\n", string(body))
			return nil, fmt.Errorf("request returned client error: %v", resp.Status)
		}
	}
	return nil, fmt.Errorf("request failed after %d retries", maxRetries)
}

func LineGetOriginalContentUrl(ctx context.Context, accessToken, fileType, messageID string) (string, error) {
	url := fmt.Sprintf("%s/%s/content", lineGetContentPrefix, messageID)
	// 创建请求
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return "", fmt.Errorf("could not create request: %v", err)
	}
	// 设置请求头
	req.Header.Add("Authorization", "Bearer "+accessToken)
	// 发送请求
	resp, err := httpClient.Do(req)
	if err != nil {
		return "", fmt.Errorf("failed to send request: %v", err)
	}
	defer resp.Body.Close()
	if resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("unexpected status code: %d", resp.StatusCode)
	}
	// 获取文件格式
	formatInfo := resp.Header.Get("Content-Type")
	formatList := strings.Split(formatInfo, "/")
	format := ""
	if len(formatList) > 1 {
		format = formatList[1]
	}
	// 获取文件后缀
	ext := ""
	mime := mimetype.Lookup(formatInfo)
	if mime != nil {
		ext = mime.Extension()
	} else {
		logger.Errorf(ctx, "failed to lookup mime type: %s . messageId:%s", formatInfo, messageID)
	}
	// 读取文件内容
	buf, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("failed to read response body: %v", err)
	}
	// 上传文件到oss对象存储，获取文件url
	fileName := ""
	if format != "" {
		fileName = fmt.Sprintf("linef.rmt.%s_%s_%s", fileType, messageID, format)
	} else {
		fileName = fmt.Sprintf("line.rmt.%s_%s", fileType, messageID)
	}
	if ext != "" {
		fileName = fmt.Sprintf("%s%s", fileName, ext)
	}
	originalContentUrl, err := s3.Defaults3Sess.Upload(ctx, buf, "tickets/"+fileName)
	if err != nil {
		return "", err
	}
	return originalContentUrl, nil
}

func LineGetPreviewImageUrl(ctx context.Context, accessToken, fileType, messageID string) (string, error) {
	url := fmt.Sprintf("%s/%s/content/preview", lineGetContentPrefix, messageID)
	// 创建请求
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return "", fmt.Errorf("could not create request: %v", err)
	}
	// 设置请求头
	req.Header.Add("Authorization", "Bearer "+accessToken)
	// 发送请求
	resp, err := httpClient.Do(req)
	if err != nil {
		return "", fmt.Errorf("failed to send request: %v", err)
	}
	defer resp.Body.Close()
	if resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("unexpected status code: %d", resp.StatusCode)
	}
	// 获取文件格式
	formatInfo := resp.Header.Get("Content-Type")
	formatList := strings.Split(formatInfo, "/")
	format := ""
	if len(formatList) > 1 {
		format = formatList[1]
	}
	// 获取文件后缀
	ext := ""
	mime := mimetype.Lookup(formatInfo)
	if mime != nil {
		ext = mime.Extension()
	} else {
		logger.Errorf(ctx, "failed to lookup mime type: %s . messageId:%s", formatInfo, messageID)
	}
	// 读取文件内容
	buf, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("failed to read response body: %v", err)
	}
	// 上传文件到oss对象存储，获取文件url
	fileName := ""
	if format != "" {
		fileName = fmt.Sprintf("preview_%s_%s_%s", fileType, messageID, format)
	} else {
		fileName = fmt.Sprintf("preview_%s_%s", fileType, messageID)
	}
	if ext != "" {
		fileName = fmt.Sprintf("%s%s", fileName, ext)
	}
	previewImageUrl, err := s3.Defaults3Sess.Upload(ctx, buf, "tickets/"+fileName)
	if err != nil {
		return "", err
	}
	return previewImageUrl, nil
}
