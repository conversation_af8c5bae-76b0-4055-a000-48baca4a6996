// Copyright 2021 funplus Authors. All Rights Reserved.
// @Description:
// @Author: Darcy
// @Date: 2022/5/27 19:59

package utils

import (
	"context"
	"fmt"
	"github.com/opentracing/opentracing-go"
	"os"
	"strconv"
	"strings"
	"time"

	"github.com/spf13/cast"
	"golang.org/x/exp/constraints"
)

func EmptyStrFormat(source, symbol string) string {
	if len(source) == 0 {
		return symbol
	}
	return source
}

func StringJoinFormat(dest []string, symbol string) string {
	return strings.Trim(strings.Join(dest, symbol), symbol)
}

// TimeFormat ts format
func TimeFormat(ts int64) string {
	if ts == 0 {
		return "-"
	}
	return time.Unix(ts, 0).UTC().Format("2006-01-02 15:04:05")
}

// TimeFormatInLoc ts format
func TimeFormatInLoc(ts int64) string {
	if ts == 0 {
		return "-"
	}
	loc := time.UTC
	release := os.Getenv("release")
	if release == "cn" {
		// 设置北京时区
		location, err := time.LoadLocation("Asia/Shanghai")
		if err == nil {
			loc = location
		}
	}
	return time.Unix(ts, 0).In(loc).Format("2006-01-02 15:04:05")
}

// ResolveTime 将传入的“秒”解析为3种时间单位
func ResolveTime(seconds uint64) (hour uint64, minute uint64, second uint64) {
	hour = seconds / 3600
	minute = (seconds - hour*3600) / 60
	second = seconds - hour*3600 - minute*60
	return
}

func ResolveTimeStr(seconds uint64, count uint32) string {
	if seconds == 0 || count == 0 {
		return "0:0:0"
	}
	hour, minute, second := ResolveTime(seconds / uint64(count))
	return fmt.Sprintf("%d:%d:%d", hour, minute, second)
}

func MetricsRate[T constraints.Integer | constraints.Float](numerator, denominator T, keep int) float64 {
	if denominator == 0 {
		return 0.00
	}
	return cast.ToFloat64(strconv.FormatFloat(float64(numerator)/float64(denominator), 'f', keep, 64))
}

// FloatRound 截取小数位数
func FloatRound(f float64, n int) float64 {
	format := "%." + strconv.Itoa(n) + "f"
	res, _ := strconv.ParseFloat(fmt.Sprintf(format, f), 64)
	return res
}

func DivInt[T int | int32 | int64 | uint](dividend, divisor T) float64 {
	if divisor == 0 {
		return 0
	}
	return FloatRound(100.0*float64(dividend)/float64(divisor), 2)
}
func OriginDivInt[T int | int32 | int64](dividend, divisor T) float64 {
	if divisor == 0 {
		return 0
	}
	return 100.0 * float64(dividend) / float64(divisor)
}

func WithNextSpan(ctx context.Context) context.Context {
	var span []opentracing.StartSpanOption
	if nCtx := opentracing.SpanFromContext(ctx); nCtx != nil {
		span = append(span, opentracing.ChildOf(nCtx.Context().(opentracing.SpanContext)))
	}
	return opentracing.ContextWithSpan(ctx, opentracing.StartSpan("ops-ticket-api", span...))
}
