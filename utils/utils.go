// Copyright 2021 funplus Authors. All Rights Reserved.
// @Description: 工具类
// @Author: Darcy
// @Date: 2021/10/13 2:07 PM

package utils

import (
	"bytes"
	"context"
	"crypto/hmac"
	"crypto/md5"
	"crypto/rand"
	"crypto/sha256"
	"encoding/base64"
	"encoding/csv"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"github.com/jinzhu/now"
	jsoniter "github.com/json-iterator/go"
	"github.com/klauspost/compress/zlib"
	"github.com/shopspring/decimal"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog"
	"golang.org/x/net/html"
	"io"
	rand2 "math/rand"
	"ops-ticket-api/internal/code"
	"os"
	"regexp"
	"sort"
	"strconv"
	"strings"
	"time"
	"unicode"
	"unicode/utf8"
	"unsafe"

	"ops-ticket-api/proto/pb"

	"github.com/golang/protobuf/jsonpb"
	"github.com/golang/protobuf/proto"
	jsonIter "github.com/json-iterator/go"
	"github.com/spf13/cast"
	"golang.org/x/exp/constraints"
)

var (
	dayLayout    = "2006-01-02"
	minuteLayout = "2006-01-02 15:04"
	baseLayout   = "2006-01-02 15:04:05"
)

// String2bytes string convert to []byte rm copy
func String2bytes(s string) []byte {
	x := (*[2]uintptr)(unsafe.Pointer(&s))
	h := [3]uintptr{x[0], x[1], x[1]}
	return *(*[]byte)(unsafe.Pointer(&h))
}

// Bytes2String []byte convert to string rm copy
func Bytes2String(b []byte) string {
	return *(*string)(unsafe.Pointer(&b))
}

// HmacSha256 生成hmacSha256
func HmacSha256(message string, secret string) []byte {
	hash := hmac.New(sha256.New, []byte(secret))
	_, _ = hash.Write([]byte(message))
	return hash.Sum(nil)
}

func SplitServer(server string) *pb.ServerSplit {
	var detail = &pb.ServerSplit{
		Ids: []int64{},
		Btw: []*pb.ServerSplit_ServerBtw{},
	}
	if len(server) == 0 {
		return detail
	}
	server = strings.ReplaceAll(server, "，", ",")
	list := strings.Split(server, ",")
	for _, l := range list {
		if strings.Contains(l, "-") {
			__l := strings.Split(l, "-")
			if len(__l) != 2 {
				continue
			}
			detail.Btw = append(detail.Btw, &pb.ServerSplit_ServerBtw{
				Start: cast.ToInt64(strings.TrimSpace(__l[0])),
				End:   cast.ToInt64(strings.TrimSpace(__l[1])),
			})
		} else if __v := cast.ToInt64(strings.TrimSpace(l)); __v > 0 {
			detail.Ids = append(detail.Ids, __v)
		}
	}
	return detail
}

func SubTime(x, y uint64) uint64 {
	if x == 0 {
		x = cast.ToUint64(time.Now().Unix())
	}
	return x - y
}

func SubCompare[T constraints.Integer](x, y T) T {
	if x < y {
		return 0
	}
	return x - y
}

// Min returns the minimum of two comparable values.
func Min[T constraints.Ordered](a, b T) T {
	if a < b {
		return a
	}
	return b
}

// Max returns the maximum of two comparable values.
func Max[T constraints.Ordered](a, b T) T {
	if a > b {
		return a
	}
	return b
}

// SortSlice sorts the given slice of an ordered type.
// Sort is not guaranteed to be stable.
func SortSlice[S ~[]E, E constraints.Ordered](slice S) {
	sort.Slice(slice, func(i, j int) bool {
		return slice[i] < slice[j]
	})
}

func ToInterface[T any](target ...T) []interface{} {
	items := make([]interface{}, 0)
	for _, item := range target {
		items = append(items, item)
	}
	return items
}

func IntegerChange[T constraints.Integer, V constraints.Integer](target []T) []V {
	items := make([]V, 0)
	for _, item := range target {
		items = append(items, V(item))
	}
	return items
}

// IntersectAny 交集
func IntersectAny[T constraints.Ordered](dest, target []T) []T {
	m := make(map[T]struct{})
	for _, iter := range target {
		m[iter] = struct{}{}
	}
	ret := make([]T, 0)
	for _, iter := range dest {
		if _, ok := m[iter]; ok {
			ret = append(ret, iter)
		}
	}
	return ret
}

// IsSubset 子集
func IsSubset[T constraints.Ordered](subset, superset []T) bool {
	m := make(map[T]struct{})
	for _, item := range superset {
		m[item] = struct{}{}
	}
	for _, item := range subset {
		if _, ok := m[item]; ok {
			return true
		}
	}
	return false
}

func InArrayAny[T constraints.Ordered](val T, array []T) bool {
	for _, v := range array {
		if v == val {
			return true
		}
	}
	return false
}

func RmDupAny[T constraints.Ordered](slice []T) []T {
	hash := make(map[T]struct{})
	result := make([]T, 0, len(slice))
	for _, item := range slice {
		if _, ok := hash[item]; !ok {
			hash[item] = struct{}{}
			result = append(result, item)
		}
	}
	return result
}

// DiffAny 差集
// 返回dest相对于target的差集
func DiffAny[T constraints.Ordered](dest, target []T) []T {
	m := make(map[T]struct{})
	for _, iter := range target {
		m[iter] = struct{}{}
	}
	ret := make([]T, 0)
	for _, iter := range dest {
		if _, ok := m[iter]; !ok {
			ret = append(ret, iter)
		}
	}
	return ret
}

// Intersect 交集
// 返回dest相对于target的交集
func Intersect[T constraints.Ordered](dest, target []T) []T {
	m := make(map[T]struct{})
	for _, iter := range target {
		m[iter] = struct{}{}
	}
	ret := make([]T, 0)
	for _, iter := range dest {
		if _, ok := m[iter]; ok {
			ret = append(ret, iter)
		}
	}
	return ret
}

func UniqueValue[T constraints.Ordered](source []T) []T {
	var dest []T
	var has = make(map[T]struct{}, len(source))
	for _, item := range source {
		if _, ok := has[item]; !ok {
			has[item] = struct{}{}
			dest = append(dest, item)
		}
	}
	return dest
}

// Union 并集
func Union[T constraints.Ordered](dest, target []T) []T {
	m := make(map[T]struct{})
	for _, iter := range dest {
		m[iter] = struct{}{}
	}
	for _, iter := range target {
		if _, ok := m[iter]; !ok {
			dest = append(dest, iter)
		}
	}
	return dest
}

func SliceHash[K constraints.Ordered](slice []K) map[K]struct{} {
	m := make(map[K]struct{})
	for _, item := range slice {
		m[item] = struct{}{}
	}
	return m
}

func Pb2Map(msg proto.Message) (map[string]interface{}, error) {
	buffer := bytes.Buffer{}
	jsonbMarshaller := jsonpb.Marshaler{
		OrigName:     true,
		EnumsAsInts:  true,
		EmitDefaults: true,
	}
	jsonbMarshaller.Marshal(&buffer, msg)
	var out map[string]interface{}
	if err := json.Unmarshal(buffer.Bytes(), &out); err != nil {
		return nil, err
	}
	return out, nil
}

func StringSliceUpperLower(arr []string, upper bool) []string {
	if len(arr) == 0 {
		return arr
	}
	cTmp := strings.Join(arr, ",")
	if upper {
		cTmp = strings.ToUpper(cTmp)
	} else {
		cTmp = strings.ToLower(cTmp)
	}
	return strings.Split(cTmp, ",")
}

type ContextOnlyValue struct{ context.Context }

func (ContextOnlyValue) Deadline() (deadline time.Time, ok bool) { return }
func (ContextOnlyValue) Done() <-chan struct{}                   { return nil }
func (ContextOnlyValue) Err() error                              { return nil }

func ChannelFilter(chs, subChs []string) []string {
	var chsRemoved []string
	for _, subCh := range subChs {
		subList := strings.Split(subCh, ".")
		if len(subList) > 1 && InArrayAny(subList[0], chs) {
			chsRemoved = append(chsRemoved, subList[0])
		}
	}
	if len(chsRemoved) > 0 {
		chs = DiffAny(chs, chsRemoved)
	}
	return chs
}

func ToJson(v interface{}) string {
	_v, _ := jsoniter.ConfigFastest.MarshalToString(v)
	//_v, _ := json.Marshal(v)
	return string(_v)
}
func JsonToUint32Slice(t string) []uint32 {
	var s = []uint32{}
	json.Unmarshal([]byte(t), &s)
	return s
}
func JsonToStrSlice(t string) []string {
	var s = []string{}
	json.Unmarshal([]byte(t), &s)
	return s
}

func StrToSlice(s string) []string {
	var v = make([]string, 0)
	json.Unmarshal([]byte(s), &v)
	return v
}
func SliceValEqAny[T constraints.Ordered](v1, v2 []T) bool {
	for _, _v1 := range v1 {
		if !InArrayAny(_v1, v2) {
			return false
		}
	}
	for _, _v2 := range v2 {
		if !InArrayAny(_v2, v1) {
			return false
		}
	}
	return true
}

func Md5(s string) string {
	h := md5.New()
	h.Write([]byte(s))
	return hex.EncodeToString(h.Sum(nil))
}

func RandomSecond() time.Duration {
	// 初始化随机数生成器的种子
	rand2.Seed(time.Now().UnixNano())
	// 生成一个介于 1 到 5 的随机数
	randomSeconds := rand2.Intn(5) + 1 // rand.Intn(5) 生成 0 到 4 的随机数，加 1 变成 1 到 5
	// 将随机数转换成时间持续值
	sleepDuration := time.Duration(randomSeconds) * time.Second
	fmt.Println(">>>>>>>>> random time duration:", sleepDuration)
	return sleepDuration
}

// UniqueId 生成Guid字串
func UniqueId() string {
	b := make([]byte, 48)

	if _, err := io.ReadFull(rand.Reader, b); err != nil {
		return ""
	}
	return Md5(base64.URLEncoding.EncodeToString(b))
}

func ValidateNumberString(s string) bool {
	re := regexp.MustCompile(`^[0-9]+$`)
	return re.MatchString(s)
}

func ValidateTextString(s string) bool {
	if utf8.RuneCountInString(s) == 0 {
		return false
	}
	for _, r := range []rune(s) {
		if !unicode.IsLetter(r) && !unicode.IsNumber(r) {
			return false
		}
	}
	return true
}

func NowTimestamp() uint64 {
	return uint64(time.Now().UTC().Unix())
}

// GroupGameMatch group match  true:可以使用，false:不可以
func GroupGameMatch(game, gameList string) bool {
	if game == "" || gameList == "" || gameList == "[]" {
		return true
	}
	for _, v := range StrToSlice(gameList) {
		if strings.TrimSpace(v) == game {
			return true
		}
	}
	return false
}
func GroupLangMatch(l, langList string) bool {
	if l == "" || langList == "" {
		return true
	}
	for _, v := range StrToSlice(langList) {
		if strings.TrimSpace(v) == l {
			return true
		}
	}
	return false
}

func CompareArrays(arr1, arr2 []string) (lessInArr2, sameInArr, extraInArr2 []string) {
	// 创建一个map用于存储arr1中的元素
	arr1Map := make(map[string]bool)
	for _, str := range arr1 {
		arr1Map[str] = true
	}

	// 遍历arr2，检查是否在arr1中存在
	for _, str := range arr2 {
		if _, exists := arr1Map[str]; !exists {
			// 如果在arr1中不存在，则添加到extraInArr2切片中
			extraInArr2 = append(extraInArr2, str)
		} else {
			// 如果在arr1中存在，则从map中删除，用于后续判断arr1中缺失的元素
			delete(arr1Map, str)
			sameInArr = append(sameInArr, str)
		}
	}

	// 剩下的就是arr1中缺失的元素
	for str := range arr1Map {
		lessInArr2 = append(lessInArr2, str)
	}

	return lessInArr2, sameInArr, extraInArr2
}

// CreateCsvFile 创建文件
func CreateCsvFile(fileName string, exportHeader []string, record [][]interface{}) error {
	f, err := os.OpenFile("./"+fileName, os.O_CREATE|os.O_WRONLY, 0644)
	if err != nil {
		elog.Errorf("OpenFile err:%v", err.Error())
		return err
	}

	defer func() {
		if err := f.Close(); err != nil {
			elog.Errorf("Export close file fd err:%v", err.Error())
		}
	}()

	// 写入boom头，防止中文乱码
	if _, err := f.WriteString("\xEF\xBB\xBF"); err != nil {
		return err
	}
	c := csv.NewWriter(f)
	// 写入列名称
	if err := c.Write(exportHeader); err != nil {
		return err
	}
	c.Flush()
	if err := c.Error(); err != nil {
		return err
	}
	for _, r := range record {
		col := make([]string, 0)
		for _, v := range r {
			col = append(col, cast.ToString(v))
		}
		if err := c.Write(col); err != nil {
			continue
		}
	}
	c.Flush()
	if err := c.Error(); err != nil {
		return err
	}
	return nil
}
func Compress(data []byte) ([]byte, error) {
	var b bytes.Buffer
	w := zlib.NewWriter(&b)
	if _, err := w.Write(data); err != nil {
		return nil, err
	}
	w.Close()
	return b.Bytes(), nil
}

func Decompress(data []byte) ([]byte, error) {
	b := bytes.NewReader(data)
	r, err := zlib.NewReader(b)
	if err != nil {
		return nil, err
	}
	var out bytes.Buffer
	if _, err := io.Copy(&out, r); err != nil {
		return nil, err
	}
	return out.Bytes(), nil
}

func TimeStrToUnix(at string) uint64 {
	unix, _ := time.Parse(baseLayout, at)
	return uint64(unix.UTC().Unix())
}

func TimeStrToDayUnix(at string) uint64 {
	unix, _ := time.Parse(dayLayout, at)
	return uint64(unix.UTC().Unix())
}

func PayAmountToFloat64(payAmount int64) float64 {
	// 保留小数点后2位
	val := decimal.NewFromInt(payAmount).DivRound(decimal.NewFromInt(code.PlayerMoneyRate), int32(2)).InexactFloat64()
	// fmt.Println(payAmount, val)
	return val
}

func PayAmountThin(amount int64) int64 {
	return amount * code.PlayerMoneyRate
}

// CheckIfExceedsOneYear 函数用于检查两个日期字符串之间的时间范围是否超过一年
func CheckIfExceedsOneYear(start, end string) bool {
	// 解析日期字符串
	startDate, _ := time.Parse(dayLayout, start)
	endDate, _ := time.Parse(dayLayout, end)
	// 确保 startDate 是较早的日期
	if startDate.After(endDate) {
		return true
	}
	// 计算日期差
	oneYearLater := startDate.AddDate(1, 0, 0)
	if oneYearLater.Before(endDate) {
		return true
	}
	return false
}

// CheckIfExceedsMonth 函数用于检查两个日期字符串之间的时间范围是否超过指定的月份
func CheckIfExceedsMonth(start, end string, months int) bool {
	// 解析日期字符串
	startDate, _ := time.Parse(dayLayout, start)
	endDate, _ := time.Parse(dayLayout, end)
	// 确保 startDate 是较早的日期
	if startDate.After(endDate) {
		return true
	}
	// 计算指定的时间差
	durationLater := startDate.AddDate(0, months, 0) // 增加指定的月份
	if durationLater.Before(endDate) {
		return true
	}
	return false
}

func TransferTimeToDate(t time.Time) string {
	baseTime := t.Format(baseLayout)
	return strings.Split(baseTime, " ")[0]
}

func IsVipUser(_qfrom string) bool {
	// crmVip: ss-vip绿色通道过来请求
	return _qfrom == "crmVip"
}

func IsPrivateZone(_zone_from string) bool {
	// privZone: 私域来源请求
	return _zone_from == "privZone"
}
func GetYesterdayStartAndEnd() (time.Time, time.Time) {
	// 获取昨天的开始和结束时间
	yesterdayStart := time.Now().UTC().AddDate(0, 0, -1).Truncate(24 * time.Hour)
	yesterdayEnd := yesterdayStart.Add(24*time.Hour - time.Nanosecond)
	return yesterdayStart, yesterdayEnd
}

func GetWhichDayStartAndEnd(tm time.Time) (time.Time, time.Time) {
	// 获取昨天的开始和结束时间
	now := now.New(tm.Local())
	return now.BeginningOfDay(), now.EndOfDay()
}

// GenerateDelays 生成 4 个随机的时间点，并确保它们总和不超过 1000 毫秒
func GenerateDelays() []time.Duration {
	intervals := make([]int, 4)
	for i := 0; i < 4; i++ {
		intervals[i] = rand2.Intn(990)
	}
	sort.Ints(intervals)

	// 将这些值转换成间隔，保证总和是 1000 毫秒
	delays := make([]time.Duration, 5)
	delays[0] = time.Duration(intervals[0]) * time.Millisecond
	for i := 1; i < 4; i++ {
		delays[i] = time.Duration(intervals[i]-intervals[i-1]) * time.Millisecond
	}
	delays[4] = time.Duration(1000-intervals[3]) * time.Millisecond

	return delays
}

// PrepareStringUIDForPropertyTerm 处理 string UID 列表，返回有效的 UID slice
func PrepareStringUIDForPropertyTerm(uidStr string) []string {
	// 将中文逗号替换为英文逗号
	uidStr = strings.ReplaceAll(uidStr, "，", ",")

	// 拆分字符串为 UID 列表
	uidList := strings.Split(uidStr, ",")

	// 存储有效 UID
	var validUIDs []string

	// 遍历 UID 列表，过滤掉空字符串
	for _, uid := range uidList {
		uid = strings.TrimSpace(uid) // 去除空白字符
		if uid != "" {
			validUIDs = append(validUIDs, uid)
		}
	}

	return validUIDs
}

// GenerateRetryKey 生成一个16进制的UUID字符串，符合Line Messaging API的retry key要求
func GenerateRetryKey() (string, error) {
	uuid := make([]byte, 16)
	if _, err := rand.Read(uuid); err != nil {
		return "", err
	}

	// 设置UUID版本和变体
	uuid[6] = (uuid[6] & 0x0f) | 0x40 // 版本4
	uuid[8] = (uuid[8] & 0x3f) | 0x80 // 变体

	return fmt.Sprintf("%x-%x-%x-%x-%x", uuid[0:4], uuid[4:6], uuid[6:8], uuid[8:10], uuid[10:]), nil
}

func JsonEncode(data interface{}) string {
	var jsonLib = jsonIter.ConfigCompatibleWithStandardLibrary
	b, err := jsonLib.Marshal(data)
	if err != nil {
		return ""
	}
	return string(b)
}

func JsonDecode(data string) map[string]interface{} {
	var jsonLib = jsonIter.ConfigCompatibleWithStandardLibrary
	dataMap := make(map[string]interface{})
	jsonLib.Unmarshal([]byte(data), &dataMap)
	return dataMap
}

func TransferTimeToString(t time.Time) string {
	return t.Format(baseLayout)
}

// NowTimestampMill 生成当前标准时间毫秒级的时间戳
func NowTimestampMill() uint64 {
	return uint64(time.Now().UTC().UnixMilli())
}

// PrepareStringObjsForPropertyTerm 处理 string 某id 列表，返回有效的 id列表
func PrepareStringObjsForPropertyTerm(idMap map[string]string) map[string][]string {
	resMap := make(map[string][]string)
	for key, objStr := range idMap {
		if objStr == "" {
			continue
		}
		objStr = strings.ReplaceAll(objStr, "，", ",")
		objStr = strings.TrimSpace(objStr)
		// 拆分字符串为 obj 列表
		objList := strings.Split(strings.ReplaceAll(objStr, " ", ","), ",")
		// 存储有效 某id
		var validObjs []string

		// 遍历 某id 列表，过滤掉空字符串
		for _, obj := range objList {
			obj = strings.TrimSpace(obj) // 去除空白字符
			if obj != "" {
				validObjs = append(validObjs, obj)
			}
		}
		if len(validObjs) > 0 {
			resMap[key] = validObjs
		}
	}

	return resMap
}

// ContainsValidImage 判断富文本内容是否包含图片
func ContainsValidImage(htmlContent string) bool {
	reader := strings.NewReader(htmlContent)
	doc, err := html.Parse(reader)
	if err != nil {
		fmt.Println("Error parsing HTML:", err)
		return false
	}

	// 定义递归遍历函数
	var hasValidImage bool
	var traverse func(*html.Node)
	traverse = func(node *html.Node) {
		if node.Type == html.ElementNode && node.Data == "img" {
			for _, attr := range node.Attr {
				if attr.Key == "src" && attr.Val != "" {
					// 如果找到 <img> 标签且 src 属性非空，返回 true
					hasValidImage = true
					return
				}
			}
		}
		// 继续递归子节点
		for child := node.FirstChild; child != nil; child = child.NextSibling {
			traverse(child)
		}
	}
	traverse(doc)
	return hasValidImage
}
func TransferSecondTimestampToTime(timestamp int64) time.Time {
	return time.Unix(timestamp, 0)
}

func Int64SliceToStringSlice(slice []int64) []string {
	strSlice := make([]string, len(slice))
	for i, v := range slice {
		strSlice[i] = strconv.FormatInt(v, 10)
	}
	return strSlice
}

func SplitStrategyFilter(obj string) (*pb.StrategySplit, error) {
	var detail = &pb.StrategySplit{
		Ids: []int64{},
		Btw: []*pb.StrategySplit_ObjBtw{},
	}

	// 空字符串校验
	if len(obj) == 0 {
		return detail, nil
	}

	// 替换中文逗号
	obj = strings.ReplaceAll(obj, "，", ",")

	// 按逗号分割
	list := strings.Split(obj, ",")

	// 用于去重
	idSet := make(map[int64]struct{})
	btwSet := make(map[string]struct{})

	for _, l := range list {
		l = strings.TrimSpace(l)
		if len(l) == 0 {
			continue // 忽略空字符串
		}

		if strings.Contains(l, "-") { // 范围部分
			__l := strings.Split(l, "-")
			if len(__l) != 2 {
				return detail, fmt.Errorf("范围格式错误：%s", l)
			}

			start, err := cast.ToInt64E(strings.TrimSpace(__l[0]))
			if err != nil || start < 0 {
				return detail, fmt.Errorf("范围起始值无效：%s", __l[0])
			}

			end, err := cast.ToInt64E(strings.TrimSpace(__l[1]))
			if err != nil || end < 0 {
				return detail, fmt.Errorf("范围结束值无效：%s", __l[1])
			}

			if start > end {
				return detail, fmt.Errorf("范围起始值不能大于结束值：%s", l)
			}

			// 去重校验
			key := fmt.Sprintf("%d-%d", start, end)
			if _, ok := btwSet[key]; ok {
				return detail, fmt.Errorf("范围重复：%s", l)
			}
			btwSet[key] = struct{}{}

			detail.Btw = append(detail.Btw, &pb.StrategySplit_ObjBtw{
				Start: start,
				End:   end,
			})
		} else { // 单独 ID 部分
			__v, err := cast.ToInt64E(strings.TrimSpace(l))
			if err != nil || __v < 0 {
				return detail, fmt.Errorf("ID 值无效：%s", l)
			}

			// 去重校验
			if _, ok := idSet[__v]; ok {
				return detail, fmt.Errorf("ID 重复：%d", __v)
			}
			idSet[__v] = struct{}{}

			detail.Ids = append(detail.Ids, __v)
		}
	}

	return detail, nil
}

// ConvertFilterToString 将过滤器数据转换为字符串
func ConvertFilterToString(filter *pb.StrategySplit) string {
	var parts []string

	// 处理 Ids
	for _, id := range filter.Ids {
		parts = append(parts, fmt.Sprintf("%d", id))
	}

	// 处理 Btw
	for _, btw := range filter.Btw {
		parts = append(parts, fmt.Sprintf("%d-%d", btw.Start, btw.End))
	}

	return strings.Join(parts, ",")
}
