package utils

import (
	"context"
	"encoding/json"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"gitlab-ee.funplus.io/ops-tools/compkg/httpclient"
	"go.uber.org/zap"
	"time"
)

const (
	URL                          = "https://open.feishu.cn/open-apis/bot/v2/hook/"
	MSG_TAG_Interactive          = "interactive"
	MSG_CARD_HEADER_COLOR_orange = "orange"
	HttpTimeout                  = time.Second * 6
)

type FeishuRobot struct {
	App    string
	Name   string
	secret string
	token  string
}

type FeishuMsgText struct {
	MsgType string `json:"msg_type"`
	Content struct {
		Text string `json:"text"`
	} `json:"content"`
}

type FeishuMsgCard struct {
	MsgType string              `json:"msg_type"`
	Card    FeishuMsgCardDetail `json:"card"`
}

type FeishuMsgCardDetail struct {
	Config   FeishuMsgCardDetailConfig    `json:"config"`
	Header   FeishuMsgCardDetailHeader    `json:"header"`
	Elements []FeishuMsgCardDetailElement `json:"elements"`
	CardLink FeishuMsgCardDetailCardLink  `json:"card_link"`
}

type FeishuMsgCardDetailConfig struct {
	WideScreenMode bool `json:"wide_screen_mode"`
}

type FeishuMsgCardDetailHeader struct {
	Title struct {
		Tag     string `json:"tag"`
		Content string `json:"content"`
	} `json:"title"`
	Template string `json:"template"`
}

type FeishuMsgCardDetailElement struct {
	Tag     string `json:"tag"`
	Content string `json:"content,omitempty"`
	ImgKey  string `json:"img_key,omitempty"`
	Alt     Alt    `json:"alt,omitempty"`
	Title   Alt    `json:"title,omitempty"`
	Mode    string `json:"mode,omitempty"`
}

type Alt struct {
	Tag     string `json:"tag"`
	Content string `json:"content"`
}

type FeishuMsgCardDetailCardLink struct {
	Url        string `json:"url"`
	AndroidUrl string `json:"android_url"`
	IosUrl     string `json:"ios_url"`
	PCUrl      string `json:"pc_url"`
}

// https://open.feishu.cn/open-apis/bot/v2/hook/ecbed2e6-9e5b-4315-85d3-cf1f1dace40a
// 关键词 unfollow

// RobotTaskLineUnfollowPush  Line用户取关告警推送
var RobotTaskLineUnfollowPush = FeishuRobot{
	App:    "101",
	Name:   "Line用户取关告警推送",
	secret: "",
	token:  "ecbed2e6-9e5b-4315-85d3-cf1f1dace40a",
}

func (robot FeishuRobot) SendCard(ctx context.Context, card FeishuMsgCardDetail) {
	theUrl := robot.signUrl()
	feishuMsg := FeishuMsgCard{
		MsgType: MSG_TAG_Interactive,
		Card:    card,
	}
	client := httpclient.New()
	client.SetHeader("Content-Type", "application/json")
	client.SetTimeout(HttpTimeout)
	feishuMsgStr, _ := json.Marshal(feishuMsg)
	resp, err := client.PostJson(ctx, theUrl, string(feishuMsgStr))
	if err != nil {
		elog.Info("feishu send msg error", zap.Error(err))
		return
	}
	respBody := resp.String()
	logger.Info(ctx, "飞书推送结果", zap.String("data", respBody))
}

func (robot FeishuRobot) SendMarkdown(ctx context.Context, title string, color string, md string) {
	card := FeishuMsgCardDetail{
		Config: FeishuMsgCardDetailConfig{
			WideScreenMode: true,
		},
		Header: NewCardHeader(title, color),
		Elements: []FeishuMsgCardDetailElement{
			NewCardElementMarkdown(md),
		},
	}
	robot.SendCard(ctx, card)
}

func (robot FeishuRobot) SendText(ctx context.Context, msg string) {
	theUrl := robot.signUrl()
	feishuMsg := FeishuMsgText{
		MsgType: "text",
	}
	feishuMsg.Content.Text = msg
	client := httpclient.New()
	client.SetHeader("Content-Type", "application/json")
	client.SetTimeout(HttpTimeout)
	resp, err := client.PostJson(ctx, theUrl, JsonEncode(feishuMsg))
	if err != nil {
		logger.Errorf(ctx, "push to feishu error:%v", err)
	}
	respBody := resp.String()
	logger.Info(ctx, "飞书推送结果", zap.String("data", respBody))
}

func (robot FeishuRobot) signUrl() string {
	theUrl := URL + robot.token
	return theUrl
}

func NewCardHeader(text string, color string) FeishuMsgCardDetailHeader {
	return FeishuMsgCardDetailHeader{
		Title: struct {
			Tag     string `json:"tag"`
			Content string `json:"content"`
		}{Tag: "plain_text", Content: text},
		Template: color,
	}
}

func NewCardElementMarkdown(msg string) FeishuMsgCardDetailElement {
	return FeishuMsgCardDetailElement{
		Tag:     "markdown",
		Content: msg,
	}
}
