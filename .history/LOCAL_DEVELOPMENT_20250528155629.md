# 本地开发环境设置指南

本分支 (`feature/local-development-setup`) 包含了运行 ops-ticket-api 项目所需的本地开发环境配置。

## 🚀 快速启动

### 方式一：使用远程测试数据库（推荐）

```bash
# 直接使用远程数据库启动
go run main.go -c ./config/global-test.yaml
```

### 方式二：使用本地Docker数据库

#### 1. 启动依赖服务

```bash
# 启动 MySQL, Redis, Elasticsearch
docker-compose up -d

# 检查服务状态
docker-compose ps
```

#### 2. 启动API服务

```bash
# 使用 Makefile
make run

# 或直接使用 Go
go run main.go
```

### 3. 验证服务

- **API服务**: http://localhost:9000
- **健康检查**: http://localhost:9000/health
- **API文档**: http://localhost:9000/swagger/index.html

## 📋 依赖服务

| 服务 | 端口 | 用途 |
|------|------|------|
| MySQL 8.0 | 3306 | 主数据库 |
| Redis 7-alpine | 6379 | 缓存和消息队列 |
| Elasticsearch 7.17.0 | 9200 | 搜索引擎 |

## 🔧 配置文件

- `config/global-test.yaml`: 远程测试数据库配置，启用了 `skip_auth: true` 跳过认证
- `config/dev.yaml`: 本地开发环境配置，启用了 `skip_auth: true` 跳过认证
- `docker-compose.yml`: Docker 服务编排文件
- `config/local.yaml`: 本地配置文件（已更新）

## 📝 数据库

### 远程测试数据库（推荐）
- 使用AWS RDS测试环境数据库
- 包含完整的真实测试数据
- 数据库：`fp_ops_ticket_new_test`, `fp_ops_ticket_test`
- 无需额外配置，直接可用

### 本地Docker数据库
项目启动时会自动：
1. 创建所需的数据库：`fp_ops_ticket_new`, `fp_ops_ticket`
2. 导入基础表结构
3. 创建缺失的表（如 `fp_ops_tickets_tab`）

## 🧪 测试API

```bash
# 测试工单Tab列表
curl -X POST 'http://localhost:9000/api/ticket/tab/list' \
  -H 'content-type: application/json' \
  -H 'lang: zh-cn' \
  --data-raw '{}'

# 测试系统枚举
curl -X POST 'http://localhost:9000/api/addons/enum' \
  -H 'content-type: application/json' \
  -H 'lang: zh-cn' \
  --data-raw '{}'
```

## 🔄 分支管理

### 切换到开发分支
```bash
git checkout feature/local-development-setup
```

### 切换回主分支
```bash
git checkout master
```

### 更新开发分支
```bash
git checkout feature/local-development-setup
git add .
git commit -m "你的提交信息"
```

## 🛑 停止服务

```bash
# 停止API服务
# 在运行 make run 的终端按 Ctrl+C

# 停止依赖服务
docker-compose down

# 停止并删除数据卷（谨慎使用）
docker-compose down -v
```

## ⚠️ 注意事项

1. **认证跳过**: 当前配置跳过了JWT认证，仅用于开发测试
2. **外部依赖**: 某些API需要连接外部服务，在本地环境可能会失败
3. **数据持久化**: 数据库数据保存在Docker volumes中
4. **端口冲突**: 确保端口 3306, 6379, 9000, 9200 未被占用

## 🔍 故障排除

### 端口被占用
```bash
# 检查端口占用
lsof -i :9000
lsof -i :3306

# 停止占用端口的进程
kill -9 <PID>
```

### Docker 问题
```bash
# 重启 Docker 服务
# macOS: 重启 Docker Desktop
# Linux: sudo systemctl restart docker

# 清理 Docker 资源
docker system prune -f
```

### 数据库连接问题
```bash
# 检查 MySQL 容器日志
docker logs ops-ticket-mysql

# 手动连接测试
docker exec -it ops-ticket-mysql mysql -u root -pmysqluser
```

## 📞 支持

如果遇到问题，请检查：
1. Docker 是否正常运行
2. 所需端口是否可用
3. 配置文件是否正确
4. 依赖服务是否启动成功
