app:
  name: "ops-ticket-api.funplus.com"
  env: "local"
  debug: true
  version: "v1.0.0"
  environment: "internal"
  LogMode: 4
  lang: "zh-cn"

# Server
server:
  addr: ":9000"

# Db
db:
  ops_ticket:
    master: "root:mysqluser@tcp(127.0.0.1:3306)/fp_ops_ticket_new?charset=utf8mb4&parseTime=True&loc=UTC"
    slaver: "root:mysqluser@tcp(127.0.0.1:3306)/fp_ops_ticket_new?charset=utf8mb4&parseTime=True&loc=UTC"
  ops_ticket_old:
    master: "root:mysqluser@tcp(127.0.0.1:3306)/fp_ops_ticket?charset=utf8mb4&parseTime=True&loc=UTC"
    slaver: "root:mysqluser@tcp(127.0.0.1:3306)/fp_ops_ticket?charset=utf8mb4&parseTime=True&loc=UTC"
  max_open_conn: 50
  max_idle_conn: 30
  max_life_time: 10

# Elasticsearch
es:
  node:
    - "http://127.0.0.1:9200"
  user: ""
  pwd: ""
  index:
    ticket: "fp_ops_new_ticket_test"
    history: "fp_ops_new_tickets_history_test"
dsces:
  node:
    - "https://user-platform-all-games.kingsgroupgames.com"
  user: "elastic"
  pwd: "Ff1Wb35TW4150thgNJ8Y7gg5@"
  index:
    dscindex: "ops_dsc_user_test"
# Redis
redis:
  addr: "127.0.0.1:6379"
  password: ""
  db: 2

s3:
  access_key: "********************"
  access_key_secret: "ZGCBasdcsHPFo9ZQ1BxMsZIuBnnafdQoAacGBiHA"
  region: "us-west-2"
  bucket: "kg-webtools/prod/upload/"
  cdn_prefix_url: "https://kg-web-cdn.kingsgroupgames.com/prod/upload/"

pkg:
  gw_gameList_api: "http://admin-gateway.ops-tools-test.svc.cluster.local:8888/api/gameList"
  gw_perm_api: "http://admin-gateway.ops-tools-test.svc.cluster.local:8888/api/per/data"

alarm:
  alert: true
  webhook: "https://open.feishu.cn/open-apis/bot/v2/hook/0d6624b4-e6a1-484e-a23e-acca68c864cb"
  keywords:
    - "Alert"
    - "Global"
    - "ops-ticket-api"
    - "test"
  rules:
    ERROR:
      duration: 60
      num: 2
    WARN:
      duration: 60
      num: 5

auth:
  admin_gateway: mR34P1MlzTaQhWytUoAbxVdXpw8Fq7NK
  ticket_api_key: klh9XQyjvmts5doLeMP2z86O4cGZ0VNx
  8GyET0HD3cClqNmK: Qw4rUcnk6vJ3ZGWbIXRC
  7nSbse2J5CQUlKc2: gRz0sAcvrKpDpY0g2Fl9 # D项目
  U2FsdGVkX18sdTQ6: 03Gb9gWCcegTl7HepAXp # L项目
  BJQxggH958WdplJz: DHTq7Nu4gfEVYX6CEuNT # DC项目
  KGFjKXEssUedumN4: LWZolEIYH3r1CXJYEgkg # SSR项目
  shDUpzqA6jPkXmko: xl908zEEDamljfJcR2cW # Dino项目
  HJKjFieGxigwG6hc: Ws7KbHJKgEF1gZhBZGRg # SSCN Global ssgl.global.prod

thirdparty:
  ops_backend_api:
    crm_auth_key: "yWbIKJmgfdvtFV4nTLwDQ0sUSBxXMklC"
    crm_discord_info: "http://opscrm-api-test.funplus.com/api/player/discord"
    ai_elfin_backend_server_host: "http://127.0.0.1:9000"
    cs_api_server_host: "http://127.0.0.1:9000"
  algorithm_url: "http://chat-detect-internal.funplus.com"