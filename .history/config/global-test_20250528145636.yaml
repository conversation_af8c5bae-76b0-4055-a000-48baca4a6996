app:
    name: "ops-ticket-api.funplus.com"
    env: "dev"
    debug: true
    version: "v1.0.0"
    environment: "internal"
    LogMode: 4
    lang: "zh-cn"
#    skip_auth: true
# Server
server:
    addr: ":9000"

# Db
db:
    ops_ticket:
        master: "opstools:Funplus@888@tcp(all-service-offline.cluster-c6h1gwzealou.us-west-2.rds.amazonaws.com:3306)/fp_ops_ticket_new_test?charset=utf8mb4&parseTime=True&loc=Local"
        slaver: "opstools:Funplus@888@tcp(all-service-offline.cluster-ro-c6h1gwzealou.us-west-2.rds.amazonaws.com:3306)/fp_ops_ticket_new_test?charset=utf8mb4&parseTime=True&loc=Local"
    ops_ticket_old:
        master: "opstools:Funplus@888@tcp(all-service-offline.cluster-c6h1gwzealou.us-west-2.rds.amazonaws.com:3306)/fp_ops_ticket_test?charset=utf8mb4&parseTime=True&loc=Local"
        slaver: "opstools:Funplus@888@tcp(all-service-offline.cluster-ro-c6h1gwzealou.us-west-2.rds.amazonaws.com:3306)/fp_ops_ticket_test?charset=utf8mb4&parseTime=True&loc=Local"
    max_open_conn: 50
    max_idle_conn: 30
    max_life_time: 10

# Elasticsearch
es:
    node:
        - "http://*************:9200"
    user: ""
    pwd: ""
    index:
        ticket: "fp_ops_new_ticket_test"
        history: "fp_ops_new_tickets_history_test"
dsces:
    node:
        - "https://user-platform-all-games.kingsgroupgames.com"
    user: "elastic"
    pwd: "Ff1Wb35TW4150thgNJ8Y7gg5@"
    index:
        dscindex: "ops_dsc_user_test"

examinees:
    node:
        - "https://user-platform-all-games.kingsgroupgames.com"
    user: "elastic"
    pwd: "Ff1Wb35TW4150thgNJ8Y7gg5@"
    index:
        examine_dsc_index: "ops_examine_dsc_test"

line_es:
    node:
        - "https://user-platform-all-games.kingsgroupgames.com"
    user: "elastic"
    pwd: "Ff1Wb35TW4150thgNJ8Y7gg5@"
    index:
        line_index: "ops_line_user_test"

openai:
    type: "azure" # openai  azure
    token: "************************************************************************************************************************************" # openai token
    azure_apikey: "sk-ZuDJzRrq8CVXwfNk496e9f1bE056451dA25f10B8473c34C9"
    tag_azure_apikey: "sk-XpaGuWOPEyidOcNUC3F6D1Ae04894815A2Cf66Be783f5273"
    azure_endpoint: "http://oneapi.funplus.com/v1"


# Redis
redis:
    addr: "ops-tools-redis-offline.6vjslb.0001.usw2.cache.amazonaws.com:6379"
    password: ""
    db: 2

s3:
    access_key: "********************"
    access_key_secret: "ZGCBasdcsHPFo9ZQ1BxMsZIuBnnafdQoAacGBiHA"
    region: "us-west-2"
    bucket: "kg-webtools/prod/upload/"
    cdn_prefix_url: "https://kg-web-cdn.kingsgroupgames.com/prod/upload/"

pkg:
    gw_gameList_api: "http://admin-gateway.ops-tools-test.svc.cluster.local:8888/api/gameList"
    gw_perm_api: "http://admin-gateway.ops-tools-test.svc.cluster.local:8888/api/per/data"

alarm:
    alert: true
    webhook: "https://open.feishu.cn/open-apis/bot/v2/hook/0d6624b4-e6a1-484e-a23e-acca68c864cb"
    keywords:
        - "Alert"
        - "Global"
        - "OpsTicketApi"
        - "Test"
    rules:
        ERROR:
            duration: 60
            num: 2
        WARN:
            duration: 60
            num: 5

auth:
    admin_gateway: mR34P1MlzTaQhWytUoAbxVdXpw8Fq7NK
    ticket_api_key: klh9XQyjvmts5doLeMP2z86O4cGZ0VNx
    8GyET0HD3cClqNmK: Qw4rUcnk6vJ3ZGWbIXRC
    7nSbse2J5CQUlKc2: gRz0sAcvrKpDpY0g2Fl9 # D项目
    U2FsdGVkX18sdTQ6: 03Gb9gWCcegTl7HepAXp # L项目
    BJQxggH958WdplJz: DHTq7Nu4gfEVYX6CEuNT # DC项目
    KGFjKXEssUedumN4: LWZolEIYH3r1CXJYEgkg # SSR项目
    shDUpzqA6jPkXmko: xl908zEEDamljfJcR2cW # Dino项目
    HJKjFieGxigwG6hc: Ws7KbHJKgEF1gZhBZGRg # SSCN Global ssgl.global.prod
    TBg21pbr20hpCGlE: XXAdXyDdjrTULr0fs1R8 # worldx项目
    MzI0V1lqTmpNMk5t: WmpZek5ETXpaR1k9rA4e # foundation项目
    pc_salt: AsHx6XKcLJFQ0XWBrcb3
    priv_secret : 6Etgy78rDegGXAfpWgeX # 私域密钥

ticket_monitor:
    ops_ticket_inner_api_url: "https://ops-ticket-api-test.funplus.com"
    # ban_daily_report_game_projects:
    #     - "koa_global"
    games:
        ss_global:
            feishu_webhook: "https://open.feishu.cn/open-apis/bot/v2/hook/6300df96-53c3-469b-b47a-3b856d97e824"  # 飞书机器人webhook
            categories:
                login: [46]
                payment_not_credited: []
                payment_cannot_pay: []
        koa_global:
            feishu_webhook: "https://open.feishu.cn/open-apis/bot/v2/hook/ac377018-cd55-4c30-a95f-0b6f41f259d3"  # 飞书机器人webhook
            categories:
                login: []
                payment_not_credited: []
                payment_cannot_pay: []
        gog_global:
            feishu_webhook: "https://open.feishu.cn/open-apis/bot/v2/hook/6300df96-53c3-469b-b47a-3b856d97e824"  # 飞书机器人webhook
            categories:
                login: []
                payment_not_credited: []
                payment_cannot_pay: []
        mc_global:
            feishu_webhook: "https://open.feishu.cn/open-apis/bot/v2/hook/6300df96-53c3-469b-b47a-3b856d97e824"  # 飞书机器人webhook
            categories:
                login: []
                payment_not_credited: []
                payment_cannot_pay: []
        mce_global:
            feishu_webhook: "https://open.feishu.cn/open-apis/bot/v2/hook/6300df96-53c3-469b-b47a-3b856d97e824"  # 飞书机器人webhook
            categories:
                login: []
                payment_not_credited: []
                payment_cannot_pay: []
        mo_global:
            feishu_webhook: "https://open.feishu.cn/open-apis/bot/v2/hook/6300df96-53c3-469b-b47a-3b856d97e824"  # 飞书机器人webhook
            categories:
                login: []
                payment_not_credited: []
                payment_cannot_pay: []
        ts_global:
            feishu_webhook: "https://open.feishu.cn/open-apis/bot/v2/hook/6300df96-53c3-469b-b47a-3b856d97e824"  # 飞书机器人webhook
            categories:
                login: []
                payment_not_credited: []
                payment_cannot_pay: []
        pk.global.prod:
            feishu_webhook: "https://open.feishu.cn/open-apis/bot/v2/hook/6300df96-53c3-469b-b47a-3b856d97e824"  # 飞书机器人webhook
            categories:
                login: []
                payment_not_credited: []
                payment_cannot_pay: []
        dc.global.prod:
            feishu_webhook: "https://open.feishu.cn/open-apis/bot/v2/hook/6300df96-53c3-469b-b47a-3b856d97e824"  # 飞书机器人webhook
            categories:
                login: []
                payment_not_credited: []
                payment_cannot_pay: []
        entropy_global:
            feishu_webhook: "https://open.feishu.cn/open-apis/bot/v2/hook/6300df96-53c3-469b-b47a-3b856d97e824"  # 飞书机器人webhook
            categories:
                login: []
                payment_not_credited: []
                payment_cannot_pay: []
        foundation.global.prod:
            feishu_webhook: "https://open.feishu.cn/open-apis/bot/v2/hook/6300df96-53c3-469b-b47a-3b856d97e824"  # 飞书机器人webhook
            categories:
                login: []
                payment_not_credited: []
                payment_cannot_pay: []

thirdparty:
    ops_backend_api:
        crm_auth_key: "yWbIKJmgfdvtFV4nTLwDQ0sUSBxXMklC"
        crm_ops_info: "http://opscrm-api-test.funplus.com/api/player/simplify_detail"
        dwh_ops_info: "https://data-interface.funplus.com/v1/api/getUserInfo"
        crm_discord_info: "http://opscrm-api-test.funplus.com/api/player/discord"
        ai_elfin_backend_server_host: "http://elfin-admin-serv.ops-tools-test:9000"
        cs_api_server_host: "http://cs-api-test.funplus.com"
        private_zone_user_info: "https://priv-platform-api-test.funplus.com/auth/user_verify"
        private_zone_vip_level: "http://priv-platform-api-inner-test.funplus.com/inner/api/user/r_level"
        private_zone_Vip_level_key: "58554a59e17aac7d7ee803618df34dc1"
        data_plat_server_host: "************:8000"
    algorithm_url: "http://chat-detect-internal.funplus.com"
    ip2loc_url: "https://public-tools-api-test.funplus.com/api/ip/location"
    survey_base_url: "https://fpcs-web-test.funplus.com/qn/questionNaire"
    pc_assist:
        host: "https://pc-assist-api-test.funplus.com"
#        host: "http://127.0.0.1:9000"
        notice_push: "/api/inner/cs/push"
    priv:
        inner_host: "http://priv-platform-api-inner-test.funplus.com"
        card_use: "/inner/api/cs_card/use"

# LLM服务配置
llm:
    base_url: "http://chat-detect-test-internal.funplus.com"  # 使用已有的算法服务地址
    timeout: 30  # 超时时间(秒)
    retry_count: 3  # 重试次数
    endpoints:
        analyze: "/collect_data"  # 分析答案的endpoint

# 问题分类配置
strategy_first_cat_id:
    games:
        mo_global:
            login: -1
            suggestion: 1073


config:
    pc_notice_url : "https://fpcs-web-test.funplus.com/pc/ticket-detail"







