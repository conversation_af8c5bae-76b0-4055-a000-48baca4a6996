# Makfile
GO=go
GO_BUILD=$(GO) build
GO_CLEAN=$(GO) clean
GO_TEST=$(GO) test
GO_GET=$(GO) get

DIST=build
TARGET=main
STATICCHECK  := staticcheck

PROTO_IDL = proto
PROTO_IDL_OUT = proto/pb
proto_set := $(wildcard $(PROTO_IDL)/*.proto)
proto_files := $(notdir $(wildcard $(PROTO_IDL)/*.proto))
pb_set := $(patsubst %.proto, %.pb.go, $(proto_set))

# all: clean-build lint build build-api
all: clean-build build build-api

lint:
	golint -set_exit_status
	golint -set_exit_status handlers/...

check:
	#$(GO) install honnef.co/go/tools/cmd/staticcheck@latest
	$(STATICCHECK) ./...

run:
	go run main.go

build:
	@echo clean ops-ticket-api ......
#	@mkdir build
	@rm -rf main

build-api:
	@echo update ops-ticket-api code......
	@echo building ops-ticket-api service......
	@CGO_ENABLED=0 GOOS=linux GOARCH=amd64 $(GO_BUILD) -o  ${TARGET}
	@upx ${TARGET}

clean-build:
	$(GOCLEAN)
	@rm -rf ./$(DIST)

pb: $(pb_set)
	@echo all proto files: $(proto_files)
	@cd $(PROTO_IDL); go generate
	protoc -I $(PROTO_IDL) --openapiv2_out docs \
		--openapiv2_opt logtostderr=true \
		--openapiv2_opt json_names_for_fields=false \
		--openapiv2_opt allow_merge=true \
		--openapiv2_opt enums_as_ints=true \
		./proto/*.proto
	protoc-go-inject-tag -input="$(PROTO_IDL_OUT)/*.pb.go"
# 	protoc -I $(PROTO_IDL) --doc_out=docs --doc_opt=markdown,docs.md $(proto_files)

$(pb_set): $(proto_set)
	protoc -I $(PROTO_IDL)  --go_out=:$(PROTO_IDL_OUT) --go-grpc_out=:$(PROTO_IDL_OUT) $^

.PHONY : clean-build $(pb_set)

