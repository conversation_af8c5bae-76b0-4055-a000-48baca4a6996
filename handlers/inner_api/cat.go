// Copyright 2021 funplus Authors. All Rights Reserved.
// @Author: <PERSON><PERSON>
// @Date: 2025/4/7 19:06
package inner_api

import (
	"ops-ticket-api/internal/code"
	"ops-ticket-api/internal/framework/ctxresp"
	"ops-ticket-api/internal/framework/lang"
	"ops-ticket-api/internal/framework/validator"
	"ops-ticket-api/internal/framework/xerrors"
	dto "ops-ticket-api/internal/persistence"
	"ops-ticket-api/proto/pb"

	"github.com/labstack/echo/v4"
)

func GetCatTitles(c echo.Context) error {
	// 1. 获取请求参数
	// 2. 校验参数合法性
	// 3. 调用服务层逻辑
	// 4. 返回结果

	req := &pb.GetCatTitlesReq{}
	if err := validator.BindValidate(c, req); err != nil {
		return err
	}

	if len(req.GetCatIds()) == 0 {
		return ctxresp.Error(c, xerrors.New(lang.FormatText(c, "RespInvalidParams"), code.InvalidParams))
	}
	// 调用服务层逻辑

	catTitles, err := dto.NewCat().OldTicketCatTitleByIdS(c.Request().Context(), req.GetGameProject(), req.GetLang(), req.GetCatIds())
	if err != nil {
		return ctxresp.Error(c, err)
	}

	catResp := make([]*pb.CatTitle, 0)

	// 获取多语言
	for _, cat := range catTitles {
		catResp = append(catResp, &pb.CatTitle{
			CatId:   int32(cat.CatID),
			CatName: cat.Category,
		})
	}

	return ctxresp.Out(c, &pb.GetCatTitlesResp{
		CatTitles: catResp,
	})
}
