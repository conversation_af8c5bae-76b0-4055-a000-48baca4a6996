package inner

import (
	"encoding/base64"
	"encoding/json"
	"net/http"
	"time"

	"ops-ticket-api/internal/code"
	"ops-ticket-api/internal/cst"
	"ops-ticket-api/internal/framework/ctxresp"
	"ops-ticket-api/internal/framework/xerrors"
	"ops-ticket-api/proto/pb"
	"ops-ticket-api/services"
	"ops-ticket-api/services/commsrv"

	"github.com/labstack/echo/v4"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"go.uber.org/zap"
)

type BanExportReq struct {
	CreateAtStart string `json:"start_date"`
	CreateAtEnd   string `json:"end_date"`
	Project       string `json:"project"`
	CatId         int64  `json:"cat_id"`
}

func TicketBanExport(ctx echo.Context) error {
	exportCode := ctx.QueryParam("export_code")
	if len(exportCode) < 3 {
		return ctx.JSON(http.StatusBadRequest, map[string]any{"status": "invalid export code"})
	}
	logger.Info(ctx.Request().Context(), "exportCode", zap.String("exportCode", exportCode))
	codeBytes, err := base64.StdEncoding.DecodeString(exportCode[2:])
	if err != nil {
		logger.Errorf(ctx.Request().Context(), "[TicketBanExport Rrror] %s DecodeString err. err:%v", exportCode[2:], err)
		return ctx.JSON(http.StatusBadRequest, map[string]any{"status": "invalid export code"})
	}
	exportReq := BanExportReq{}
	err = json.Unmarshal(codeBytes, &exportReq)
	if err != nil {
		logger.Errorf(ctx.Request().Context(), "[TicketBanExport Rrror] %s Unmarshal err. err:%v", string(codeBytes), err)
		return ctx.JSON(http.StatusBadRequest, map[string]any{"status": "invalid export code"})
	}

	if exportReq.CreateAtStart == "" || exportReq.CreateAtEnd == "" || exportReq.Project == "" || exportReq.CatId == 0 {
		return ctx.JSON(http.StatusBadRequest, map[string]any{"status": "invalid export code"})
	}

	param := &pb.TicketPoolNewListReq{}
	param.CreatedAt = []string{exportReq.CreateAtStart, exportReq.CreateAtEnd}
	param.Project = []string{exportReq.Project}
	param.CatId = []uint32{uint32(exportReq.CatId)}

	if len(param.Project) == 0 {
		permList, _ := commsrv.LoadPermGmList(ctx.Request().Context(), ctx.Get(cst.AccountInfoCtx).(string))
		if len(permList) > 0 {
			param.Project = permList
		}
	}
	records, err := services.TicketWorkPoolExport(ctx, param)
	if err != nil {
		logger.Error(ctx.Request().Context(), "export workstation file err", logger.Any("err", err))
		return ctxresp.Error(ctx, xerrors.New(err.Error(), code.Error))
	}

	fileName := "ticket_export_" + time.Now().Format("**************") + ".csv"
	ctx.Response().Header().Add(echo.HeaderContentType, "text/plain; charset=utf-8")
	ctx.Response().Header().Add("filename", fileName)
	ctx.Response().Header().Set(echo.HeaderContentDisposition, "attachment; filename="+fileName)
	ctx.Response().WriteHeader(http.StatusOK)

	for res := range records {
		ctx.Response().Write(res)
		ctx.Response().Flush()
	}
	return nil
}
