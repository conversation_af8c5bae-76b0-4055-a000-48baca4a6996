package inner

import (
	"github.com/labstack/echo/v4"
	"ops-ticket-api/internal/framework/ctxresp"
	"ops-ticket-api/internal/framework/validator"
	"ops-ticket-api/proto/pb"
	"ops-ticket-api/services/configure"
)

func UserAssignTicketList(ctx echo.Context) error {
	param := &pb.UserAssignTicketListReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	resp, err := configure.UserAssignTicketList(ctx, param)
	if err != nil {
		return err
	}
	return ctxresp.Out(ctx, resp)
}

func UserAssignTicketInfo(ctx echo.Context) error {
	param := &pb.UserAssignTicketInfoReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	resp, err := configure.UserAssignTicketInfo(ctx, param)
	if err != nil {
		return err
	}
	return ctxresp.Out(ctx, resp)
}

func UserAssignTicketAdd(ctx echo.Context) error {
	param := &pb.UserAssignTicketAddReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	if err := configure.UserAssignTicketAdd(ctx, param); err != nil {
		return err
	}
	return ctxresp.Out(ctx, pb.Empty{})
}

func UserAssignTicketEdit(ctx echo.Context) error {
	param := &pb.UserAssignTicketEditReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	if err := configure.UserAssignTicketEdit(ctx, param); err != nil {
		return err
	}
	return ctxresp.Out(ctx, pb.Empty{})
}

func UserAssignTicketDel(ctx echo.Context) error {
	param := &pb.UserAssignTicketDelReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	if err := configure.UserAssignTicketDel(ctx, param); err != nil {
		return err
	}
	return ctxresp.Out(ctx, pb.Empty{})
}
