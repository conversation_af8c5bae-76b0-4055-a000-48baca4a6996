package inner

import (
	"github.com/labstack/echo/v4"
	"ops-ticket-api/internal/code"
	"ops-ticket-api/internal/framework/ctxresp"
	"ops-ticket-api/internal/framework/validator"
	"ops-ticket-api/internal/framework/xerrors"
	"ops-ticket-api/proto/pb"
	"ops-ticket-api/services/configure"
)

// AddTeamConfig 新增团队配置
func AddTeamConfig(ctx echo.Context) error {
	var req = &pb.TeamConfigAddReq{}
	if err := validator.BindValidate(ctx, req); err != nil {
		return err
	}
	// edit data
	if err := configure.AddTeamConfig(ctx, req); err != nil {
		return err
	}
	return ctxresp.Out(ctx, pb.Empty{})
}

// TeamConfigList 团队配置查询
func TeamConfigList(ctx echo.Context) error {
	var req = &pb.TeamConfigListReq{}
	if err := validator.BindValidate(ctx, req); err != nil {
		return err
	}
	// get data
	resp, err := configure.TeamConfigList(ctx, req)
	if err != nil {
		return err
	}
	return ctxresp.Out(ctx, resp)
}

// EditTeamConfig 编辑修改团队配置
func EditTeamConfig(ctx echo.Context) error {
	var req = &pb.TeamConfigEditReq{}
	if err := validator.BindValidate(ctx, req); err != nil {
		return err
	}
	// 参数校验
	if req.TeamId == 0 {
		return xerrors.New("params error", code.InvalidParams)
	}
	// edit data
	if err := configure.EditTeamConfig(ctx, req); err != nil {
		return err
	}
	return ctxresp.Out(ctx, pb.Empty{})
}

// DelTeamConfig 删除团队配置
func DelTeamConfig(ctx echo.Context) error {
	var req = &pb.TeamConfigDelReq{}
	if err := validator.BindValidate(ctx, req); err != nil {
		return err
	}
	// 参数校验
	if req.TeamId == 0 {
		return xerrors.New("params error", code.InvalidParams)
	}
	// del data
	if err := configure.DelTeamConfig(ctx, req); err != nil {
		return err
	}
	return ctxresp.Out(ctx, pb.Empty{})
}
