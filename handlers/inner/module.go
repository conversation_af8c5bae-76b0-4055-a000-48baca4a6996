// Copyright 2024 funplus Authors. All Rights Reserved.
// @Description: 回复模板配置
// @Author: jun.qiu
// @Date: 2024/06/04 3:34 PM

package inner

import (
	"ops-ticket-api/internal/code"
	"ops-ticket-api/internal/framework/ctxresp"
	"ops-ticket-api/internal/framework/validator"
	"ops-ticket-api/internal/framework/xerrors"
	"ops-ticket-api/proto/pb"
	"ops-ticket-api/services/configure"

	"github.com/labstack/echo/v4"
)

// ModuleList 模板列表
func ModuleList(ctx echo.Context) error {
	param := &pb.ModuleListReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	if param.Enable > 0 && param.Enable != code.ModuleEnable && param.Enable != code.ModuleDisable {
		return xerrors.New("参数错误", code.InvalidParams)
	}
	resp, err := configure.ModuleList(ctx, param)
	if err != nil {
		return err
	}
	return ctxresp.Out(ctx, resp)
}

// ModuleSave 模板添加
func ModuleSave(ctx echo.Context) error {
	param := &pb.ModuleSaveReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	if err := configure.ModuleSave(ctx, param); err != nil {
		return err
	}
	return ctxresp.Out(ctx, pb.Empty{})
}

// ModuleEdit 模板编辑
func ModuleEdit(ctx echo.Context) error {
	param := &pb.ModuleEditReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	if param.Enable > 0 {
		if param.Enable != code.ModuleEnable && param.Enable != code.ModuleDisable {
			return xerrors.New("参数错误", code.InvalidParams)
		}
	} else {
		if param.GameProject == "" || param.CatId == 0 {
			return xerrors.New("游戏项目或模板分类不能为空", code.InvalidParams)
		}
	}
	if err := configure.ModuleEdit(ctx, param); err != nil {
		return err
	}
	return ctxresp.Out(ctx, pb.Empty{})
}

// ModuleDel 模板删除
func ModuleDel(ctx echo.Context) error {
	param := &pb.ModuleDelReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	if err := configure.ModuleDel(ctx, param); err != nil {
		return err
	}
	return ctxresp.Out(ctx, pb.Empty{})
}

// ModuleOpts 模板下拉选
func ModuleOpts(ctx echo.Context) error {
	param := &pb.ModuleOptsReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	list, err := configure.ModuleOpts(ctx, param)
	if err != nil {
		return err
	}
	return ctxresp.Out(ctx, list)
}
