package inner

import (
	"github.com/labstack/echo/v4"
	"ops-ticket-api/internal/code"
	"ops-ticket-api/internal/cst"
	"ops-ticket-api/internal/framework/ctxresp"
	"ops-ticket-api/internal/framework/validator"
	"ops-ticket-api/internal/framework/xerrors"
	"ops-ticket-api/proto/pb"
	"ops-ticket-api/services"
	"strings"
)

// TicketReassign 强制指派
func TicketReassign(ctx echo.Context) error {
	param := &pb.AssignmentReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	if err := services.NewTicketSrv().Reassign(ctx, param.TicketId, param.Acceptor); err != nil {
		return err
	}
	return ctxresp.Out(ctx, pb.Empty{})
}

func TicketBatchReassign(ctx echo.Context) error {
	param := &pb.BatchAssignmentReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	if err := services.NewTicketSrv().BatchReassign(ctx, param.TicketId, param.Acceptor); err != nil {
		return err
	}
	return ctxresp.Out(ctx, pb.Empty{})
}

func TicketTurn(ctx echo.Context) error {
	var param = &pb.AssignmentReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	if err := services.NewTicketSrv().TicketTurn(ctx, param.TicketId, param.Acceptor); err != nil {
		return err
	}
	return ctxresp.Out(ctx, pb.Empty{})
}

func TicketTransfer(ctx echo.Context) error {
	var param = &pb.TicketTransferReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	if err := services.NewTicketSrv().TicketTransfer(ctx, param, false); err != nil {
		return err
	}
	return ctxresp.Out(ctx, pb.Empty{})
}

func TicketReturnPool(ctx echo.Context) error {
	var param = &pb.TicketReturnPoolReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	if err := services.NewTicketSrv().TicketReturnPool(ctx, param); err != nil {
		return err
	}
	return ctxresp.Out(ctx, pb.Empty{})
}

// TicketDraftSave 工单草稿保存接口
func TicketDraftSave(ctx echo.Context) error {
	param := &pb.TicketDraftSaveReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	account := ctx.Get(cst.AccountInfoCtx).(string)
	if account == "" {
		return xerrors.New(code.StatusText(code.MissingParams), code.MissingParams)
	}
	param.Content = strings.TrimSpace(param.Content)

	err := services.NewTicketSrv().TicketDraftSave(ctx, param, account)
	if err != nil {
		return err
	}
	return ctxresp.Out(ctx, pb.Empty{})
}

// TicketDraftInfo 工单草稿接口
func TicketDraftInfo(ctx echo.Context) error {
	param := &pb.TicketDraftInfoReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	account := ctx.Get(cst.AccountInfoCtx).(string)
	if account == "" {
		return xerrors.New(code.StatusText(code.MissingParams), code.MissingParams)
	}

	info, err := services.NewTicketSrv().TicketDraftInfo(ctx, param, account)
	if err != nil {
		return err
	}
	return ctxresp.Out(ctx, info)
}

func TicketBatchTransfer(ctx echo.Context) error {
	var param = &pb.TicketBatchTransferReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	if err := services.NewTicketSrv().TicketBatchTransfer(ctx, param); err != nil {
		return err
	}
	return ctxresp.Out(ctx, pb.Empty{})
}
