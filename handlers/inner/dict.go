// Copyright 2021 funplus Authors. All Rights Reserved.
// @Description:
// @Author: Darcy
// @Date: 2022/11/21 11:33

package inner

import (
	"github.com/labstack/echo/v4"
	"ops-ticket-api/internal/framework/ctxresp"
	"ops-ticket-api/internal/framework/validator"
	"ops-ticket-api/proto/pb"
	"ops-ticket-api/services/configure"
)

func DictOpts(ctx echo.Context) error {
	opts, err := configure.DefaultDictSvc.DictOpts(ctx)
	if err != nil {
		return err
	}
	return ctxresp.Out(ctx, &pb.DictOptsResp{
		List: opts,
	})
}

func DictList(ctx echo.Context) error {
	list, err := configure.DefaultDictSvc.DictList(ctx)
	if err != nil {
		return err
	}
	return ctxresp.Out(ctx, &pb.DictListResp{
		List: list,
	})
}

func DictInfo(ctx echo.Context) error {
	param := &pb.DictId{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	dest, err := configure.DefaultDictSvc.DictInfo(ctx, param.GetDictId())
	if err != nil {
		return err
	}
	return ctxresp.Out(ctx, dest)
}

func DictAdd(ctx echo.Context) error {
	param := &pb.DictAddReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	if err := configure.DefaultDictSvc.DictAdd(ctx, param); err != nil {
		return err
	}
	return ctxresp.Out(ctx, pb.Empty{})
}

func DictSave(ctx echo.Context) error {
	param := &pb.DictInfoResp{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	if err := configure.DefaultDictSvc.DictSave(ctx, param); err != nil {
		return err
	}
	return ctxresp.Out(ctx, pb.Empty{})

}

func DictEnable(ctx echo.Context) error {
	param := &pb.EnableReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	if err := configure.DefaultDictSvc.DictEnable(ctx, param); err != nil {
		return err
	}
	return ctxresp.Out(ctx, pb.Empty{})
}
