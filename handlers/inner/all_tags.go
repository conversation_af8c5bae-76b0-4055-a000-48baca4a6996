package inner

import (
	"ops-ticket-api/internal/framework/ctxresp"
	"ops-ticket-api/internal/framework/validator"
	"ops-ticket-api/proto/pb"
	"ops-ticket-api/services/configure"

	"github.com/labstack/echo/v4"
)

// AllTagOpts 标签筛选列表接口
func AllTagOpts(ctx echo.Context) error {
	param := &pb.TagOptsReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	resp, err := configure.AllTagOpts(ctx, param)
	if err != nil {
		return err
	}
	return ctxresp.Out(ctx, resp)
}

// AllTagLibList 标签库列表接口
func AllTagLibList(ctx echo.Context) error {
	param := &pb.TagLibListReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	dest, err := configure.AllTagLibList(ctx, param)
	if err != nil {
		return err
	}
	return ctxresp.Out(ctx, dest)
}

// AllTagLibEnable 标签库禁用启用接口
func AllTagLibEnable(ctx echo.Context) error {
	param := &pb.EnableReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	if err := configure.AllTagLibEnable(ctx, param); err != nil {
		return ctxresp.Error(ctx, err)
	}
	return ctxresp.Out(ctx, pb.Empty{})
}

// AllTagLibInfo 标签库信息接口
func AllTagLibInfo(ctx echo.Context) error {
	param := &pb.TagLibID{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	dest, err := configure.AllTagLibInfo(ctx, param.LibId)
	if err != nil {
		return err
	}
	return ctxresp.Out(ctx, dest)
}

// AllTagLibSave 标签库信息更新接口
func AllTagLibSave(ctx echo.Context) error {
	param := &pb.AllTagLibSaveReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	// 新逻辑实现
	tagId, err := configure.AllTagLibSaveNew(ctx, param)
	if err != nil {
		return ctxresp.Error(ctx, err)
	}
	return ctxresp.Out(ctx, &pb.TagLibID{LibId: tagId})
}
