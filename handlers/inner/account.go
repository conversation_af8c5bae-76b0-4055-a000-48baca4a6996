package inner

import (
	"github.com/labstack/echo/v4"
	"github.com/spf13/cast"
	"ops-ticket-api/internal/cst"
	"ops-ticket-api/services"
)

// UserInfoSave 用户信息更新
func UserInfoSave(ctx echo.Context) {
	account := cast.ToString(ctx.Get(cst.AccountInfoCtx))
	nickName := cast.ToString(ctx.Get(cst.AccountNameCtx))
	isAdmin := cast.ToBool(ctx.Get(cst.AccountIsAdminCtx))
	if account == "" {
		return
	}
	services.NewUserSrv().AccountSync(ctx, account, nickName, isAdmin)
}
