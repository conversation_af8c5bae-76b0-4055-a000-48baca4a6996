package inner

import (
	"errors"
	"github.com/labstack/echo/v4"
	"ops-ticket-api/internal/framework/ctxresp"
	"ops-ticket-api/internal/framework/validator"
	"ops-ticket-api/proto/pb"
	"ops-ticket-api/services/configure"
)

// CatModuleTree 回复模版树形返回
func CatModuleTree(ctx echo.Context) error {
	param := &pb.ModuleCatTreeReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	list, err := configure.CatModuleTree(ctx, param.Project)
	if err != nil {
		return err
	}
	return ctxresp.Out(ctx, list)
}

// CatModuleAdd 回复模版添加
func CatModuleAdd(ctx echo.Context) error {
	param := &pb.ModuleCatAddReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	if param.CatLevel > 1 && param.ParentId == 0 {
		return errors.New("parent_id is required")
	}
	if err := configure.CatModuleAdd(ctx, param); err != nil {
		return err
	}
	return ctxresp.Out(ctx, pb.Empty{})
}

// CatModuleSave 回复模版编辑
func CatModuleSave(ctx echo.Context) error {
	param := &pb.ChannelCatSaveReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	if err := configure.CatModuleSave(ctx, param); err != nil {
		return err
	}
	return ctxresp.Out(ctx, pb.Empty{})
}

// CatModuleDel 回复模版删除
func CatModuleDel(ctx echo.Context) error {
	param := &pb.CatIdReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	if err := configure.CatModuleDel(ctx, param.CatId); err != nil {
		return err
	}
	return ctxresp.Out(ctx, pb.Empty{})
}
