package inner

import (
	"github.com/labstack/echo/v4"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"ops-ticket-api/internal/code"
	"ops-ticket-api/internal/cst"
	"ops-ticket-api/internal/framework/ctxresp"
	"ops-ticket-api/internal/framework/validator"
	"ops-ticket-api/internal/framework/xerrors"
	"ops-ticket-api/proto/pb"
	"ops-ticket-api/services"
	"ops-ticket-api/services/commsrv"
	"os"
)

// QuestionSave 工单知识库-保存
func QuestionSave(ctx echo.Context) error {
	var param = &pb.QuestionSaveReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	// save question
	err := services.NewQuestionTicketSrv().SaveQuestionDetail(ctx, param)
	if err != nil {
		return err
	}
	return ctxresp.Out(ctx, pb.Empty{})
}

// QuestionTraining 工单知识库-训练
func QuestionTraining(ctx echo.Context) error {
	var param = &pb.QuestionTrainingReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	// save question
	err := services.NewQuestionTrainingService().Training(ctx, param)
	if err != nil {
		return err
	}
	return ctxresp.Out(ctx, pb.Empty{})
}

// QuestionList 工单知识库-列表
func QuestionList(ctx echo.Context) error {
	var param = &pb.QuestionListReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	if len(param.UpdateDate) > 0 && len(param.UpdateDate) != 2 {
		return xerrors.New("日期参数错误", code.InvalidParams)
	}
	if len(param.Project) == 0 {
		if permList, _ := commsrv.LoadPermGmList(ctx.Request().Context(), ctx.Get(cst.AccountInfoCtx).(string)); len(permList) > 0 {
			param.Project = permList
		}
	}
	resp, err := services.NewQuestionTicketSrv().QuestionList(ctx, param)
	if err != nil {
		return err
	}
	return ctxresp.Out(ctx, resp)
}

// QuestionExport 工单知识库-导出
func QuestionExport(ctx echo.Context) error {
	var param = &pb.QuestionListReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	if len(param.UpdateDate) > 0 && len(param.UpdateDate) != 2 {
		return xerrors.New("日期参数错误", code.InvalidParams)
	}
	if len(param.Project) == 0 {
		if permList, _ := commsrv.LoadPermGmList(ctx.Request().Context(), ctx.Get(cst.AccountInfoCtx).(string)); len(permList) > 0 {
			param.Project = permList
		}
	}
	fileName, err := services.QuestionExport(ctx, param)
	if err != nil || fileName == "" {
		logger.Errorf(ctx.Request().Context(), "question record export file:%v err:%v", fileName, err)
		return xerrors.New(err, code.RpcRespErr)
	}
	defer func() {
		if err = os.Remove("./" + fileName); err != nil {
			logger.Errorf(ctx.Request().Context(), "question record export remove file:%v err:%v", fileName, err)
		}
	}()
	ctx.Response().Header().Set("filename", fileName)
	return ctx.File(fileName)
}

// QuestionDel 工单知识库-删除
func QuestionDel(ctx echo.Context) error {
	var param = &pb.QuestionDelReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	// save question
	err := services.NewQuestionTicketSrv().QuestionDel(ctx, param)
	if err != nil {
		return err
	}
	return ctxresp.Out(ctx, pb.Empty{})
}

// QuestionBatchImport 工单知识库-批量导入
func QuestionBatchImport(ctx echo.Context) error {
	var param = &pb.QuestionBatchImportReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	// save question
	err := services.NewQuestionTicketSrv().QuestionBatchImport(ctx, param)
	if err != nil {
		return err
	}
	return ctxresp.Out(ctx, pb.Empty{})
}

func QuestionTrainLog(ctx echo.Context) error {
	var param = &pb.QuestionTrainLogReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	resp, err := services.NewQuestionTrainingService().GetAllTrainingTaskList(ctx, param)
	if err != nil {
		return err
	}
	return ctxresp.Out(ctx, resp)
}
