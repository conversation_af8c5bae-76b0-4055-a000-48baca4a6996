// Copyright 2024 funplus Authors. All Rights Reserved.
// @Description: 超时回复模版配置
// @Author: zyh
// @Date: 2024/12/04 3:34 PM

package inner

import (
	"ops-ticket-api/internal/code"
	"ops-ticket-api/internal/framework/ctxresp"
	"ops-ticket-api/internal/framework/validator"
	"ops-ticket-api/internal/framework/xerrors"
	"ops-ticket-api/proto/pb"
	"ops-ticket-api/services/configure"
	"strings"

	"github.com/labstack/echo/v4"
)

// OvertimeTplList 超时模板列表
func OvertimeTplList(ctx echo.Context) error {
	param := &pb.OverTimeTplListReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	resp, err := configure.OvertimeTplList(ctx, param)
	if err != nil {
		return err
	}
	return ctxresp.Out(ctx, resp)
}

// OvertimeTplAdd 超时模版添加
func OvertimeTplAdd(ctx echo.Context) error {
	param := &pb.OverTimeTplAddReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	if param.RemindTime < 1 || param.RemindTime > 2000 {
		return xerrors.New("保存失败！预提醒时间填写错误", code.InvalidParams)
	}
	if v, ok := param.Content["en"]; !ok || strings.TrimSpace(v) == "" {
		return xerrors.New("保存失败！预提醒文案填写错误", code.InvalidParams)
	}
	if err := configure.OvertimeTplAdd(ctx, param); err != nil {
		return err
	}
	return ctxresp.Out(ctx, pb.Empty{})
}

// OvertimeTplEdit 超时模板编辑
func OvertimeTplEdit(ctx echo.Context) error {
	param := &pb.OverTimeTplEditReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	if param.RemindTime < 1 || param.RemindTime > 2000 {
		return xerrors.New("保存失败！预提醒时间填写错误", code.InvalidParams)
	}
	if v, ok := param.Content["en"]; !ok || strings.TrimSpace(v) == "" {
		return xerrors.New("保存失败！预提醒文案填写错误", code.InvalidParams)
	}
	if err := configure.OvertimeTplEdit(ctx, param); err != nil {
		return err
	}
	return ctxresp.Out(ctx, pb.Empty{})
}

// OvertimeTplDel 超时模板删除
func OvertimeTplDel(ctx echo.Context) error {
	param := &pb.OverTimeTplDelReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	if err := configure.OvertimeTplDel(ctx, param); err != nil {
		return err
	}
	return ctxresp.Out(ctx, pb.Empty{})
}

// OvertimeTplEnable 超时模版禁用启用接口
func OvertimeTplEnable(ctx echo.Context) error {
	param := &pb.OverTimeTplEnableReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	if param.Enable > 1 || param.Enable < 0 {
		return xerrors.New("enable 参数错误", code.MissingParams)
	}
	if err := configure.OvertimeTplEnabel(ctx, param); err != nil {
		return err
	}
	return ctxresp.Out(ctx, pb.Empty{})
}

// OvertimeTplOpts 超时模版下拉选
func OvertimeTplOpts(ctx echo.Context) error {
	param := &pb.OverTimeTplOptsReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	res, err := configure.OvertimeTplOpts(ctx, param)
	if err != nil {
		return err
	}
	return ctxresp.Out(ctx, res)
}
