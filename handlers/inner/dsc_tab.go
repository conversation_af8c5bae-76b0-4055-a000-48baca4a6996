package inner

import (
	"errors"
	"github.com/labstack/echo/v4"
	"ops-ticket-api/internal/framework/ctxresp"
	"ops-ticket-api/internal/framework/validator"
	"ops-ticket-api/proto/pb"
	"ops-ticket-api/services"
)

// DiscordTabSave 创建discord 搜索tab
func DiscordTabSave(ctx echo.Context) error {
	param := &pb.DiscordTabAddReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	//游戏字段单选校验
	if len(param.Detail.Project) != 1 {
		return errors.New("自定义tab仅支持单个游戏，请检查选择的项是否正确！")
	}
	//permList, _ := configure.ProjectCfg(ctx).GameList()
	//games := int64(len(permList))
	if err := services.DiscordTabSave(ctx, param); err != nil {
		return err
	}
	return ctxresp.Out(ctx, pb.Empty{})
}

// DiscordTabDel 删除discord 搜索tab
func DiscordTabDel(ctx echo.Context) error {
	param := &pb.DiscordTabDelReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	if err := services.DiscordTabDel(ctx, param); err != nil {
		return err
	}
	return ctxresp.Out(ctx, pb.Empty{})
}

// DiscordTabList discord 搜索tab列表
func DiscordTabList(ctx echo.Context) error {
	resp, err := services.DiscordTabList(ctx)
	if err != nil {
		return err
	}
	return ctxresp.Out(ctx, resp)
}

func DiscordTabCount(ctx echo.Context) error {
	resp, err := services.DiscordTabCount(ctx)
	if err != nil {
		return err
	}
	return ctxresp.Out(ctx, resp)
}

// DiscordTabEdit 编辑tab
func DiscordTabEdit(ctx echo.Context) error {
	param := &pb.DiscordTabEditReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	if err := services.DiscordTabEdit(ctx, param); err != nil {
		return err
	}
	return ctxresp.Out(ctx, pb.Empty{})
}

// DscTabUpdateSort tab排序接口
func DscTabUpdateSort(ctx echo.Context) error {
	param := &pb.DiscordTabUpdateSortReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	if err := services.DscTabUpdateSort(ctx, param); err != nil {
		return err
	}
	return ctxresp.Out(ctx, pb.Empty{})
}
