package inner

import (
	"fmt"
	"github.com/spf13/cast"
	"ops-ticket-api/internal/cst"
	"ops-ticket-api/internal/framework/ctxresp"
	"ops-ticket-api/internal/framework/validator"
	"ops-ticket-api/proto/pb"
	"ops-ticket-api/services/survey"
	"strings"

	"github.com/labstack/echo/v4"
)

// SurveyConfigList 调查问卷配置列表接口
func SurveyConfigList(ctx echo.Context) error {
	param := &pb.SurveyListReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}

	dest, err := survey.SurveyConfigList(ctx, param)
	if err != nil {
		return err
	}
	return ctxresp.Out(ctx, dest)
}

// SurveyConfigEnable 调查问卷配禁用启用接口
func SurveyConfigEnable(ctx echo.Context) error {
	param := &pb.EnableReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	if err := survey.SurveyConfigEnable(ctx, param); err != nil {
		return ctxresp.Error(ctx, err)
	}
	return ctxresp.Out(ctx, pb.Empty{})
}

// SurveyConfigEdit 调查问卷配置信息修改
func SurveyConfigEdit(ctx echo.Context) error {
	param := &pb.SurveyEditReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	h := strings.Split(param.PushTimeWeb, ":")[0]
	if len(h) != 2 {
		return fmt.Errorf("push time error to number gt23. 0. %s", param.PushTimeWeb)
	}
	if h[0] == '0' {
		h = strings.TrimLeft(h, "0")
		param.PushTime = cast.ToUint64(h)
	} else {
		param.PushTime = cast.ToUint64(h)
	}
	//param.PushTime = cast.ToUint64(strings.Split(param.PushTimeWeb, ":")[0])
	if param.PushTime > 23 {
		return fmt.Errorf("push time error to number gt23. 1. %s. %d", param.PushTimeWeb, param.PushTime)
	}

	if err := survey.SurveyConfigEdit(ctx, param); err != nil {
		return ctxresp.Error(ctx, err)
	}
	return ctxresp.Out(ctx, pb.Empty{})
}

// SurveyConfigInfo 调查问卷配置信息接口
func SurveyConfigInfo(ctx echo.Context) error {
	param := &pb.SurveyInfoReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	dest, err := survey.SurveyConfigInfo(ctx, param.Id)
	if err != nil {
		return err
	}
	dest.Data.PushTimeWeb = fmt.Sprintf("%02d:00:00", dest.Data.PushTime)
	return ctxresp.Out(ctx, dest)
}

// SurveyGenLinks 调查问卷生成链接
func SurveyGenLinks(ctx echo.Context) error {
	param := &pb.SurveyGenLinkReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}

	// get survey link
	detail, err := survey.SurveyConfigInfo(ctx, uint64(param.SurveyId))
	if err != nil {
		return err
	}
	if err := survey.CheckCanGenLink(detail.Data); err != nil {
		return err
	}

	param.Uid = 0
	param.Lang = ""
	param.BatchId = 0
	param.IsPublic = true
	param.AccountName = ctx.Get(cst.AccountNameCtx).(string)
	resp, err := survey.SurveyGenLinks(ctx, param, &pb.SurveyLinkParamDf_Attrs{})
	if err != nil {
		return ctxresp.Error(ctx, err)
	}
	return ctxresp.Out(ctx, resp)
}
