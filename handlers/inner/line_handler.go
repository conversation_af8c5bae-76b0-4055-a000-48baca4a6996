package inner

import (
	"fmt"
	"github.com/disintegration/imaging"
	"github.com/labstack/echo/v4"
	"github.com/spf13/cast"
	"github.com/tcolgate/mp3"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"io"
	"math/rand"
	"ops-ticket-api/internal/code"
	"ops-ticket-api/internal/cst"
	"ops-ticket-api/internal/framework/ctxresp"
	"ops-ticket-api/internal/framework/validator"
	"ops-ticket-api/internal/framework/xerrors"
	"ops-ticket-api/proto/pb"
	"ops-ticket-api/services"
	"ops-ticket-api/services/commsrv"
	"ops-ticket-api/utils"
	"os"
	"os/exec"
	"path"
	"strings"
	"sync"
	"time"
)

type lineHandler struct {
}

func NewLineHandler() *lineHandler {
	return &lineHandler{}
}

func (h *lineHandler) UserList(ctx echo.Context) error {
	param := &pb.LineUserListReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	if param.Page*param.PageSize > 10000 {
		return xerrors.New(code.StatusText(code.RecordJumpLimit), code.RecordJumpLimit)
	}
	if len(param.Project) == 0 {
		if permList, _ := commsrv.LoadPermGmList(ctx.Request().Context(), ctx.Get(cst.AccountInfoCtx).(string)); len(permList) > 0 {
			param.Project = permList
		}
	}
	if strings.Contains(param.UserContent, "&&") && strings.Contains(param.UserContent, "||") {
		return xerrors.New("不能同时输入&&或||", code.InvalidParams)
	}
	if strings.Contains(param.UserDetailRemark, "&&") && strings.Contains(param.UserDetailRemark, "||") {
		return xerrors.New("不能同时输入&&或||", code.InvalidParams)
	}
	users, total, err := services.NewLineSrv().GetUserPool(ctx, param)
	if err != nil {
		return err
	}
	return ctxresp.Out(ctx, pb.LineUserListResp{
		CurrentPage: param.Page,
		PerPage:     param.PageSize,
		Total:       total,
		Data:        users,
	})
}

func (h *lineHandler) UserListExport(ctx echo.Context) error {
	param := &pb.LineUserListReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	if param.Page*param.PageSize > 10000 {
		return xerrors.New(code.StatusText(code.RecordJumpLimit), code.RecordJumpLimit)
	}
	if len(param.Project) == 0 {
		if permList, _ := commsrv.LoadPermGmList(ctx.Request().Context(), ctx.Get(cst.AccountInfoCtx).(string)); len(permList) > 0 {
			param.Project = permList
		}
	}
	fileName, err := services.LinePoolExport(ctx, param)
	if err != nil || fileName == "" {
		logger.Errorf(ctx.Request().Context(), "line export file:%v err:%v", fileName, err)
		return xerrors.New(err, code.RpcRespErr)
	}
	defer func() {
		if err = os.Remove("./" + fileName); err != nil {
			logger.Errorf(ctx.Request().Context(), "line export remove file:%v err:%v", fileName, err)
		}
	}()
	ctx.Response().Header().Set("filename", fileName)
	return ctx.File(fileName)
}

func (h *lineHandler) UserStats(ctx echo.Context) error {
	param := &pb.LineStatsReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	if len(param.Project) == 0 {
		if permList, _ := commsrv.LoadPermGmList(ctx.Request().Context(), ctx.Get(cst.AccountInfoCtx).(string)); len(permList) > 0 {
			param.Project = permList
		}
	}
	projects := make([]interface{}, len(param.Project))
	for i, project := range param.Project {
		projects[i] = project
	}
	var totalCount, waitReplyCount, mineWaitReplyCount int64
	var totalErr, waitReplyErr, mineWaitReplyErr error
	wg := &sync.WaitGroup{}
	wg.Add(3)
	// 获取当前游戏DC上所有玩家账号总数
	go func() {
		defer func() {
			ctx := ctx.Request().Context()
			if err := recover(); err != nil {
				logger.Errorf(ctx, "total.recover err:%v", err)
			}
		}()
		defer wg.Done()
		totalCount, totalErr = services.LineAccountsCount(ctx, projects)
	}()
	// 获取待回复信息的玩家账号总数
	go func() {
		defer func() {
			ctx := ctx.Request().Context()
			if err := recover(); err != nil {
				logger.Errorf(ctx, "waitReply.recover err:%v", err)
			}
		}()
		defer wg.Done()
		waitReplyCount, waitReplyErr = services.LineWaitReplyAccountsCount(ctx, projects)
	}()
	// 获取当前客服待回复信息的玩家账号总数
	go func() {
		defer func() {
			ctx := ctx.Request().Context()
			if err := recover(); err != nil {
				logger.Errorf(ctx, "mineWaitReply.recover err:%v", err)
			}
		}()
		defer wg.Done()
		mineWaitReplyCount, mineWaitReplyErr = services.LineMineWaitReplyAccountsCount(ctx, projects)
	}()

	wg.Wait()

	if totalErr != nil || waitReplyErr != nil || mineWaitReplyErr != nil {
		return xerrors.New(fmt.Sprintf("totalErr:%v. waitReplyErr:%v. mineWaitReplyErr:%v", totalErr, waitReplyErr, mineWaitReplyErr), code.EsQueryErr)
	}
	resp := new(pb.LineStatsResp)
	resp.LineUserCount = totalCount
	resp.WaitReplyAccounts = waitReplyCount
	resp.MineWaitReplyAccounts = mineWaitReplyCount
	return ctxresp.Out(ctx, resp)
}

func (h *lineHandler) SendTextMessage(ctx echo.Context) error {
	param := &pb.LineSendTextMessageReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	detail, err := services.NewLineSrv().SendTextMessage(ctx, param)
	if err != nil {
		return err
	}
	return ctxresp.Out(ctx, &pb.LineSendMessageResp{
		MsgId:     detail.MsgId,
		ChannelId: detail.ChannelId,
	})
}

func (h *lineHandler) SendFile(ctx echo.Context) error {
	var param = &pb.LineSendFileReq{}
	// 从请求头获取必要数据
	param.ChannelId = ctx.Request().Header.Get("channel-id")
	param.BotId = ctx.Request().Header.Get("bot-id")
	param.LineUserId = ctx.Request().Header.Get("line-user-id")
	param.FileType = ctx.Request().Header.Get("file-type")
	param.Format = ctx.Request().Header.Get("format")
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	operator := cast.ToString(ctx.Get(cst.AccountInfoCtx))
	file, err := ctx.FormFile("file")
	if err != nil {
		return err
	}
	// 校验文件大小，格式
	if !utils.CheckLineFile(param.FileType, param.Format, file.Size, false) {
		return ctxresp.Error(ctx, fmt.Errorf("original file check unqualified fileType:%s, format:%s, fileSize:%d", param.FileType, param.Format, file.Size))
	}
	// 打开上传的文件
	fileSrc, err := file.Open()
	if err != nil {
		return ctxresp.Error(ctx, fmt.Errorf("open file return err: %v", err))
	}
	defer fileSrc.Close()

	// 将上传的文件保存到本地目录
	fileName := file.Filename
	localFileName := genLineLocalFileName(fileName)
	logger.Infof(ctx.Request().Context(), "line.SendFile query file. fileName:%s. file.header:%+v. file.base:%s. ext:%s. localFileName:%s", fileName, file.Header, path.Base(fileName), path.Ext(fileName), localFileName)

	dst, err := os.Create(localFileName)
	if err != nil {
		return ctxresp.Error(ctx, fmt.Errorf("failed to create local file: %v", err))
	}
	defer dst.Close()
	// 将文件内容写入本地文件
	if _, err = dst.ReadFrom(fileSrc); err != nil {
		return ctxresp.Error(ctx, fmt.Errorf("failed to save file to local storage: %v", err))
	}
	previewFilePath := ""
	// 生成略缩图
	if param.FileType == "image" || param.FileType == "video" {
		previewFilePath = "./" + localFileName + "_preview.jpeg"
		previewWidth := 240
		if param.FileType == "image" {
			if err = generatePreviewImage(localFileName, previewFilePath, previewWidth); err != nil {
				return ctxresp.Error(ctx, fmt.Errorf("failed to generate preview image: %v", err))
			}
		} else {
			if err = generateVideoPreviewImage(localFileName, previewFilePath); err != nil {
				return ctxresp.Error(ctx, fmt.Errorf("failed to generate preview image: %v", err))
			}
		}
		fileInfo, err := os.Stat(previewFilePath)
		if err != nil {
			return fmt.Errorf("failed to get preview file info: %v", err)
		}
		// 校验略缩图文件的大小，格式
		if !utils.CheckLineFile(param.FileType, "jpeg", fileInfo.Size(), true) {
			return ctxresp.Error(ctx, fmt.Errorf("preview file check unqualified fileType:%s, fileSize:%d", param.FileType, fileInfo.Size()))
		}
	}
	// 延迟删除原始文件和缩略图文件
	defer func() {
		if err = os.Remove(localFileName); err != nil {
			logger.Errorf(ctx.Request().Context(), "failed to delete original file. err:%v. localFile:%s", err, localFileName)
		}
		if previewFilePath != "" {
			if err = os.Remove(previewFilePath); err != nil {
				logger.Errorf(ctx.Request().Context(), "failed to delete preview file: %v. previewFilePath:%s", err, previewFilePath)
			}
		}
	}()
	// 获取音频文件的时长
	var duration int64
	if param.FileType == "audio" {
		durationLength, err := getAudioDuration(localFileName)
		if err != nil {
			return err
		}
		duration = durationLength
	}
	// 读取原始文件的内容
	originalFileContent, err := os.ReadFile(localFileName)
	if err != nil {
		return ctxresp.Error(ctx, fmt.Errorf("failed to read original file: %v", err))
	}
	// 读取缩略图内容
	previewFileContent := []byte{}
	var e error
	if previewFilePath != "" {
		previewFileContent, e = os.ReadFile(previewFilePath)
		if e != nil {
			return ctxresp.Error(ctx, fmt.Errorf("failed to read preview file: %v", err))
		}
	}
	msg, err := services.NewLineSrv().LineSendFile(ctx, duration, localFileName, operator, originalFileContent, previewFileContent, param)
	if err != nil {
		return err
	}
	return ctxresp.Out(ctx, msg)
}

func genLineLocalFileName(fileName string) string {
	rand.Seed(time.Now().UnixNano())
	localFileName := fmt.Sprintf("line.%s.%d.%d%s", utils.Md5(fileName), time.Now().UnixNano(), rand.Intn(999)+1000, path.Ext(fileName))
	return localFileName
}

// generatePreviewImage 生成预览图并保存
func generatePreviewImage(inputPath, outputPath string, width int) error {
	// 加载原图
	srcImage, err := imaging.Open(inputPath)
	if err != nil {
		return fmt.Errorf("failed to open original image: %w", err)
	}
	// 调整图像大小，保持纵横比
	previewImage := imaging.Resize(srcImage, width, 0, imaging.Lanczos)
	// 将生成的预览图保存到指定路径
	err = imaging.Save(previewImage, outputPath)
	if err != nil {
		return fmt.Errorf("failed to save preview image: %w", err)
	}
	return nil
}

func generateVideoPreviewImage(inputPath, outputPath string) error {
	cmd := exec.Command("ffmpeg", "-i", inputPath, "-ss", "00:00:01", "-vframes", "1", outputPath)
	err := cmd.Run()
	if err != nil {
		return fmt.Errorf("failed to generate thumbnail: %v", err)
	}
	return nil
}

// getAudioDuration 获取音频文件的时长
func getAudioDuration(filePath string) (int64, error) {
	var t int64
	fd, err := os.Open(filePath)
	if err != nil {
		panic(err)
	}
	defer fd.Close()
	d := mp3.NewDecoder(fd)
	var f mp3.Frame
	skipped := 0
	for {
		if err = d.Decode(&f, &skipped); err != nil {
			if err == io.EOF {
				break
			}
			return 0, err
		}
		t = t + f.Duration().Microseconds()
	}
	return t, nil
}

func (h *lineHandler) DialogueHistory(ctx echo.Context) error {
	param := &pb.LineDialogueHistoryReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	if param.Before && param.TickTime == 0 {
		return xerrors.New("参数错误", code.InvalidParams)
	}
	resp, err := services.NewLineSrv().DialogueHistory(ctx, param)
	if err != nil {
		return err
	}
	return ctxresp.Out(ctx, resp)
}

func (h *lineHandler) DialogueRefresh(ctx echo.Context) error {
	param := &pb.LineDialogFreshReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	resp, err := services.NewLineSrv().DialogueRefresh(ctx, param)
	if err != nil {
		return err
	}
	return ctxresp.Out(ctx, resp)
}

// UserRemarkSave 工单池添加备注
func (h *lineHandler) UserRemarkSave(ctx echo.Context) error {
	param := &pb.LineUserRemarkAddReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	if err := services.LineRemarkSave(ctx, param); err != nil {
		return err
	}
	return ctxresp.Out(ctx, pb.Empty{})
}
