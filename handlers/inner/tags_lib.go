package inner

import (
	"ops-ticket-api/internal/framework/ctxresp"
	"ops-ticket-api/internal/framework/validator"
	"ops-ticket-api/proto/pb"
	"ops-ticket-api/services/configure"

	"github.com/labstack/echo/v4"
)

// TagOpts 标签筛选列表接口
func TagOpts(ctx echo.Context) error {
	param := &pb.ProjectTagLib{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	resp, err := configure.TagOpts(ctx, param.ProjectName, param.LibId)
	if err != nil {
		return err
	}
	return ctxresp.Out(ctx, resp)
}

// TagLibList 标签库列表接口
func TagLibList(ctx echo.Context) error {
	param := &pb.Project{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}

	dest, err := configure.TagLibList(ctx, param)
	if err != nil {
		return err
	}
	return ctxresp.Out(ctx, dest)
}

// TagLibEnable 标签库禁用启用接口
func TagLibEnable(ctx echo.Context) error {
	param := &pb.EnableReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	if err := configure.TagLibEnable(ctx, param); err != nil {
		return ctxresp.Error(ctx, err)
	}
	return ctxresp.Out(ctx, pb.Empty{})
}

// TagLibInfo 标签库信息接口
func TagLibInfo(ctx echo.Context) error {
	param := &pb.TagLibID{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	dest, err := configure.TagLibInfo(ctx, param.LibId)
	if err != nil {
		return err
	}
	return ctxresp.Out(ctx, dest)
}

// TagLibSave 标签库信息更新接口
func TagLibSave(ctx echo.Context) error {
	param := &pb.TagLibSaveReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	// 新逻辑实现
	tagId, err := configure.TagLibSaveNew(ctx, param)
	if err != nil {
		return ctxresp.Error(ctx, err)
	}
	return ctxresp.Out(ctx, &pb.TagLibID{LibId: tagId})
}

func TagConfigSave(ctx echo.Context) error {
	param := &pb.TagConfigAddReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	tagId, err := configure.TagConfigSave(ctx, param)
	if err != nil {
		return err
	}
	return ctxresp.Out(ctx, tagId)
}

func TagConfigInfo(ctx echo.Context) error {
	param := &pb.TagId{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	info, err := configure.TagConfigInfo(ctx, param.TagId)
	if err != nil {
		return err
	}
	return ctxresp.Out(ctx, info)
}

func TagConfigList(ctx echo.Context) error {
	param := &pb.TagLibID{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	dest, err := configure.TagConfigList(ctx, param.LibId)
	if err != nil {
		return err
	}
	return ctxresp.Out(ctx, dest)
}

func TagConfigDel(ctx echo.Context) error {
	param := &pb.TagId{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	if err := configure.TagConfigDel(ctx, param.TagId); err != nil {
		return ctxresp.Error(ctx, err)
	}
	return ctxresp.Out(ctx, pb.Empty{})
}

func TagConfigEdit(ctx echo.Context) error {
	param := &pb.TagConfigEditReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	if err := configure.TagConfigEdit(ctx, param); err != nil {
		return ctxresp.Error(ctx, err)
	}
	return ctxresp.Out(ctx, pb.Empty{})
}
