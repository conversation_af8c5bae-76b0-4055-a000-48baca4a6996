package inner

import (
	"errors"
	"github.com/labstack/echo/v4"
	"ops-ticket-api/internal/framework/ctxresp"
	"ops-ticket-api/internal/framework/validator"
	"ops-ticket-api/proto/pb"
	"ops-ticket-api/services"
)

// TicketTabSave 创建ticket 搜索tab
func TicketTabSave(ctx echo.Context) error {
	param := &pb.TicketTabAddReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	//游戏字段单选校验
	if len(param.Detail.Project) != 1 {
		return errors.New("自定义tab仅支持单个游戏，请检查选择的项是否正确！")
	}
	//permList, _ := configure.ProjectCfg(ctx).GameList()
	//games := int64(len(permList))
	if err := services.TicketTabSave(ctx, param); err != nil {
		return err
	}
	return ctxresp.Out(ctx, pb.Empty{})
}

// TicketTabDel 删除discord 搜索tab
func TicketTabDel(ctx echo.Context) error {
	param := &pb.TicketTabDelReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	if err := services.TicketTabDel(ctx, param); err != nil {
		return err
	}
	return ctxresp.Out(ctx, pb.Empty{})
}

// TicketTabList ticket 搜索tab列表
func TicketTabList(ctx echo.Context) error {
	resp, err := services.TicketTabList(ctx)
	if err != nil {
		return err
	}
	return ctxresp.Out(ctx, resp)
}

func TicketTabCount(ctx echo.Context) error {
	resp, err := services.TicketTabCount(ctx)
	if err != nil {
		return err
	}
	return ctxresp.Out(ctx, resp)
}

// TicketTabEdit 编辑tab
func TicketTabEdit(ctx echo.Context) error {
	param := &pb.TicketTabEditReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	if err := services.TicketTabEdit(ctx, param); err != nil {
		return err
	}
	return ctxresp.Out(ctx, pb.Empty{})
}

// TicketTabUpdateSort tab排序接口
func TicketTabUpdateSort(ctx echo.Context) error {
	param := &pb.TicketTabUpdateSortReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	if err := services.TicketTabUpdateSort(ctx, param); err != nil {
		return err
	}
	return ctxresp.Out(ctx, pb.Empty{})
}
