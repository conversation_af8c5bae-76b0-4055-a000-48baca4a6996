package inner

import (
	"github.com/labstack/echo/v4"
	"ops-ticket-api/internal/code"
	"ops-ticket-api/internal/framework/ctxresp"
	"ops-ticket-api/internal/framework/validator"
	"ops-ticket-api/internal/framework/xerrors"
	"ops-ticket-api/proto/pb"
	"ops-ticket-api/services/configure"
)

// DiscordPortraitInfo 玩家画像信息
func DiscordPortraitInfo(ctx echo.Context) error {
	param := &pb.PortraitInfoReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	resp, err := configure.PortraitInfo(ctx, param)
	if err != nil {
		return err
	}
	return ctxresp.Out(ctx, resp)
}

func EditDiscordPortrait(ctx echo.Context) error {
	param := &pb.PortraitEditReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	if param.Id == 0 {
		if param.DscUserId == "" {
			return xerrors.New("params error", code.InvalidParams)
		}
	}
	if err := configure.PortraitEditInfo(ctx, param); err != nil {
		return err
	}
	return ctxresp.Out(ctx, pb.Empty{})
}
