package inner

import (
	"github.com/labstack/echo/v4"
	"github.com/spf13/cast"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"golang.org/x/sync/errgroup"
	"net/http"
	"ops-ticket-api/internal/code"
	"ops-ticket-api/internal/cst"
	"ops-ticket-api/internal/framework/ctxresp"
	"ops-ticket-api/internal/framework/validator"
	"ops-ticket-api/internal/framework/xerrors"
	dto "ops-ticket-api/internal/persistence"
	"ops-ticket-api/proto/pb"
	"ops-ticket-api/services"
	"ops-ticket-api/services/commsrv"
	"strings"
	"sync"
	"time"
)

func TicketCount(ctx echo.Context) error {
	var (
		project   []string
		acceptors = []string{cast.ToString(ctx.Get(cst.AccountInfoCtx))}
		// 待处理状态
		pendingStage   = []uint32{uint32(pb.TkStage_TkStageNew), uint32(pb.TkStage_TkStageNewForAgent), uint32(pb.TkStage_TkStageWaitingForAgent), uint32(pb.TkStage_TkStageAgentReopen)}
		finishedStage  = []uint32{uint32(pb.TkStage_TkStageAgentResolved), uint32(pb.TkStage_TkStageAgentRejected), uint32(pb.TkStage_TkStageAgentCompleted)}
		systemAcceptor = []string{"SystemRole"}
	)

	if permList, _ := commsrv.LoadPermGmList(ctx.Request().Context(), ctx.Get(cst.AccountInfoCtx).(string)); len(permList) > 0 {
		project = permList
	}

	var wg sync.WaitGroup
	wg.Add(8)

	results := make(chan struct {
		Key   string
		Value uint64
	}, 8)

	go func() { // 总工单量
		defer wg.Done()
		count, _ := services.TicketCount(ctx, &pb.TicketCountReq{Project: project})
		results <- struct {
			Key   string
			Value uint64
		}{"Count", count}
	}()

	go func() { // 待处理工单条数
		defer wg.Done()
		pendingCount, _ := services.TicketCount(ctx, &pb.TicketCountReq{Project: project, Stage: pendingStage})
		results <- struct {
			Key   string
			Value uint64
		}{"PendingCount", pendingCount}
	}()

	go func() { // 当前用户待处理工单量
		defer wg.Done()
		userPendingCount, _ := services.TicketCount(ctx, &pb.TicketCountReq{Project: project, Acceptor: acceptors, Stage: pendingStage})
		results <- struct {
			Key   string
			Value uint64
		}{"UserPendingCount", userPendingCount}
	}()

	go func() { // 当前用户已完成工单量
		defer wg.Done()
		userCompletedCount, _ := services.TicketCount(ctx, &pb.TicketCountReq{Project: project, Acceptor: acceptors, Stage: finishedStage})
		results <- struct {
			Key   string
			Value uint64
		}{"UserCompletedCount", userCompletedCount}
	}()

	go func() { // 待处理中文工单条数
		defer wg.Done()
		pendingCnCount, _ := services.TicketCount(ctx, &pb.TicketCountReq{Project: project, Stage: pendingStage, Lang: []string{"zh-cn", "zh-tw"}})
		results <- struct {
			Key   string
			Value uint64
		}{"PendingCnCount", pendingCnCount}
	}()

	go func() { // 待处理vip工单条数
		defer wg.Done()
		pendingVipCount, _ := services.TicketCount(ctx, &pb.TicketCountReq{Project: project, Stage: pendingStage, Vip: []uint32{uint32(2)}})
		results <- struct {
			Key   string
			Value uint64
		}{"PendingVipCount", pendingVipCount}
	}()

	go func() { // 升级区工单条数
		defer wg.Done()
		// 升级且工单状态是「待接单，待处理，处理中-玩家已回复，重开」的工单
		// 1,2,4,6
		priorityCount, _ := services.TicketCount(ctx, &pb.TicketCountReq{Project: project, Priority: 1, Stage: []uint32{1, 2, 4, 6}})
		results <- struct {
			Key   string
			Value uint64
		}{"PriorityCount", priorityCount}
	}()
	go func() { // 自动回复工单条数
		defer wg.Done()
		AutoReplyCount, _ := services.TicketCount(ctx, &pb.TicketCountReq{Project: project, Acceptor: systemAcceptor})
		results <- struct {
			Key   string
			Value uint64
		}{"AutoReplyCount", AutoReplyCount}
	}()

	wg.Wait()
	close(results)

	sortedCounts := make(map[string]uint64)
	for result := range results {
		sortedCounts[result.Key] = result.Value
	}

	// 返回结果
	return ctxresp.Out(ctx, sortedCounts)
}

// TicketWorkPool 工单池列表
func TicketWorkPool(ctx echo.Context) error {
	param := &pb.TicketPoolNewListReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	if param.Page == 0 {
		param.Page = 1
	}
	if param.PageSize == 0 {
		param.PageSize = 30
	}
	if param.Page*param.PageSize > 10000 {
		return xerrors.New(code.StatusText(code.RecordJumpLimit), code.RecordJumpLimit)
	}
	if strings.Contains(param.Field, "&&") && strings.Contains(param.Field, "||") {
		return xerrors.New("不能同时输入&&或||", code.InvalidParams)
	}
	if strings.Contains(param.Remark, "&&") && strings.Contains(param.Remark, "||") {
		return xerrors.New("不能同时输入&&或||", code.InvalidParams)
	}
	if len(param.Project) == 0 {
		if permList, _ := commsrv.LoadPermGmList(ctx.Request().Context(), ctx.Get(cst.AccountInfoCtx).(string)); len(permList) > 0 {
			param.Project = permList
		}
	}

	dest, total, err := services.TicketWorkPool(ctx, param)
	if err != nil {
		return err
	}
	return ctxresp.Out(ctx, &pb.TicketPoolNewListResp{
		CurrentPage: param.Page,
		PerPage:     param.PageSize,
		Total:       total,
		Data:        dest,
	})
}

// TicketWorkPoolExport 工单池导出
func TicketWorkPoolExport(ctx echo.Context) error {
	param := &pb.TicketPoolNewListReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	if len(param.Project) == 0 {
		permList, _ := commsrv.LoadPermGmList(ctx.Request().Context(), ctx.Get(cst.AccountInfoCtx).(string))
		if len(permList) > 0 {
			param.Project = permList
		}
	}
	records, err := services.TicketWorkPoolExport(ctx, param)
	if err != nil {
		logger.Error(ctx.Request().Context(), "export workstation file err", logger.Any("err", err))
		return ctxresp.Error(ctx, xerrors.New(err.Error(), code.Error))
	}

	fileName := "ticket_export_" + time.Now().Format("**************") + ".csv"
	ctx.Response().Header().Add(echo.HeaderContentType, "text/plain; charset=utf-8")
	ctx.Response().Header().Add("filename", fileName)
	ctx.Response().Header().Set(echo.HeaderContentDisposition, "attachment; filename="+fileName)
	ctx.Response().WriteHeader(http.StatusOK)

	for res := range records {
		ctx.Response().Write(res)
		ctx.Response().Flush()
	}
	return nil
}

// IsAcceptor 修改客服在线状态
func IsAcceptor(ctx echo.Context) error {
	param := &pb.GroupUserReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	if param.Status > 0 {
		if err := services.IsAcceptorEdit(ctx, param); err != nil {
			return err
		}
		return ctxresp.Out(ctx, &pb.LoginStatus{IsLogin: int32(param.Status)})

	} else {
		status, err := services.GetAcceptorStatus(ctx, param)
		if err != nil {
			return err
		}

		return ctxresp.Out(ctx, status)
	}
}

// TicketInfo 工单数据
func TicketInfo(ctx echo.Context) error {
	param := &pb.TicketIdReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	response := &pb.TicketResponse{
		TopInfo:    &pb.TicketPoolTopResp{},
		UserInfo:   &pb.UserInfoResp{},
		RecordInfo: &pb.TicketRecordResp{},
		CommuInfo:  make([]*pb.TicketDialogueResp, 0),
		History:    make([]*pb.TicketHistResp, 0),
	}
	var wg = errgroup.Group{}
	wg.Go(func() error { // 顶部信息
		topInfo, err := services.TopTicketInfo(ctx, param)
		if topInfo != nil {
			response.TopInfo = topInfo
		}
		return err
	})

	wg.Go(func() error { // 基础数据
		userInfo, err := services.TicketUserInfo(ctx, param)
		if userInfo != nil {
			response.UserInfo = userInfo
		}
		return err
	})

	wg.Go(func() error { // 历史工单
		recordInfo, err := services.TicketsRecords(ctx, param.TicketId)
		if err != nil {
			return err
		}
		response.RecordInfo = recordInfo
		return nil
	})
	wg.Go(func() error {
		commuInfo, err := services.TicketsDialogue(ctx, param.TicketId)
		if err != nil {
			return err
		}
		response.CommuInfo = commuInfo
		return nil
	})
	wg.Go(func() error { // 工单变动历史
		hists, err := services.TicketsHists(ctx, param.TicketId)
		if err != nil {
			return err
		}
		response.History = hists
		return nil
	})

	if err := wg.Wait(); err != nil {
		return err
	}
	// 如果工单有评价，则新增工单评价信息
	if response.TopInfo.Csi > 0 {
		ticketAppraiseResp, err := services.TicketAppraiseInfo(ctx, param.TicketId)
		if err != nil {
			return err
		}
		response.TicketAppraise = ticketAppraiseResp
	}
	return ctxresp.Out(ctx, response)
}

// TicketTags 工单绑定标签查询接口
func TicketTags(ctx echo.Context) error {
	param := &pb.TicketIdReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	dest, err := services.TicketTags(ctx, param.TicketId)
	if err != nil {
		return err
	}
	return ctxresp.Out(ctx, dest)
}

// 工单标签绑定
func TicketBindTag(ctx echo.Context) error {
	param := &pb.TicketRetaggingReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	err := services.NewTicketSrv().TicketReTags(ctx, param)
	if err != nil {
		return err
	}

	return ctxresp.Out(ctx, pb.Empty{})
}

// TicketsUpgrade 工单升级
func TicketsUpgrade(ctx echo.Context) error {
	param := &pb.TicketUpgradeReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	var upGrade = 0
	if param.Upgrade == 2 {
		upGrade = 1
	}
	err := services.NewTicketSrv().TicketsUpgrade(ctx, param.TicketId, upGrade)
	if err != nil {
		return err
	}
	return ctxresp.Out(ctx, pb.Empty{})
}

// TicketsRemark 工单备注
func TicketsRemark(ctx echo.Context) error {
	param := &pb.TicketRemarkReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	if err := services.NewTicketSrv().TicketRemark(ctx, param); err != nil {
		return err
	}
	return ctxresp.Out(ctx, pb.Empty{})
}

// TicketsCountByGame 根据精灵报表条件查询
func TicketsCountByGame(ctx echo.Context) error {
	param := &pb.TicketCountStatisReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	tickets, err := dto.NewTicket().GetTicketCountByElfinReq(ctx, nil, param)
	if err != nil {
		return err
	}
	var response = &struct {
		TicketCount int64 `json:"ticket_count"` // 工单量
	}{
		TicketCount: int64(tickets.Cnt),
	}
	if err != nil {
		return err
	}
	return ctxresp.Out(ctx, response)
}

func TicketBatchBindTag(ctx echo.Context) error {
	param := &pb.TicketRetaggingBatchReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	err := services.NewTicketSrv().TicketBatchReTags(ctx, param)
	if err != nil {
		return err
	}
	return ctxresp.Out(ctx, pb.Empty{})
}

func TicketPublicTag(ctx echo.Context) error {
	param := &pb.TicketPublicTagReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	list, err := services.NewTicketSrv().TicketPublicTag(ctx, param)
	if err != nil {
		return err
	}
	return ctxresp.Out(ctx, list)
}

func TicketTagBatchDelete(ctx echo.Context) error {
	param := &pb.TicketTagBatchDelete{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	if len(param.TagIds) <= 0 {
		return xerrors.New("公共标签为空，不能删除", code.InvalidParams)
	}
	err := services.NewTicketSrv().TicketBatchDeleteTags(ctx, param)
	if err != nil {
		return err
	}
	return ctxresp.Out(ctx, pb.Empty{})
}

// TicketBatchRemark 批量备注
func TicketBatchRemark(ctx echo.Context) error {
	param := &pb.TicketBatchRemarkReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	if err := services.NewTicketSrv().TicketBatchRemark(ctx, param); err != nil {
		return err
	}
	return ctxresp.Out(ctx, pb.Empty{})
}
