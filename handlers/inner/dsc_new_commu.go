package inner

import (
	"github.com/labstack/echo/v4"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"ops-ticket-api/internal/code"
	"ops-ticket-api/internal/cst"
	"ops-ticket-api/internal/framework/ctxresp"
	"ops-ticket-api/internal/framework/validator"
	"ops-ticket-api/internal/framework/xerrors"
	"ops-ticket-api/proto/pb"
	"ops-ticket-api/services"
	"ops-ticket-api/services/commsrv"
	"os"
	"strings"
)

// DiscordNewCommuSave 创建discord沟通记录
func DiscordNewCommuSave(ctx echo.Context) error {
	param := &pb.DiscordNewCommuRecordAddReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	if err := services.DiscordNewCommuSave(ctx, param); err != nil {
		return err
	}
	return ctxresp.Out(ctx, pb.Empty{})
}

// DiscordNewCommuEdit 编辑discord沟通记录
func DiscordNewCommuEdit(ctx echo.Context) error {
	param := &pb.DiscordNewCommuRecordEditReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	if err := services.DiscordNewCommuEdit(ctx, param); err != nil {
		return err
	}
	return ctxresp.Out(ctx, pb.Empty{})
}

// DiscordNewCommuList discord沟通记录列表
func DiscordNewCommuList(ctx echo.Context) error {
	param := &pb.DiscordNewCommuRecordListReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	if len(param.CommuDate) > 0 && len(param.CommuDate) != 2 {
		return xerrors.New("参数错误", code.InvalidParams)
	}
	if len(param.Project) == 0 {
		if permList, _ := commsrv.LoadPermGmList(ctx.Request().Context(), ctx.Get(cst.AccountInfoCtx).(string)); len(permList) > 0 {
			param.Project = strings.Join(permList, ",")
		}
	}
	resp, err := services.DiscordNewCommuList(ctx, param)
	if err != nil {
		return err
	}
	return ctxresp.Out(ctx, resp)
}

// DiscordNewCommuListExport discord沟通记录导出
func DiscordNewCommuListExport(ctx echo.Context) error {
	param := &pb.DiscordNewCommuRecordListReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	if len(param.CommuDate) > 0 && len(param.CommuDate) != 2 {
		return xerrors.New("参数错误", code.InvalidParams)
	}
	if len(param.Project) == 0 {
		if permList, _ := commsrv.LoadPermGmList(ctx.Request().Context(), ctx.Get(cst.AccountInfoCtx).(string)); len(permList) > 0 {
			param.Project = strings.Join(permList, ",")
		}
	}
	fileName, err := services.DiscordNewCommuListExport(ctx, param)
	if err != nil || fileName == "" {
		logger.Errorf(ctx.Request().Context(), "discord commu record export file:%v err:%v", fileName, err)
		return xerrors.New(err, code.RpcRespErr)
	}
	defer func() {
		if err = os.Remove("./" + fileName); err != nil {
			logger.Errorf(ctx.Request().Context(), "discord commu record export remove file:%v err:%v", fileName, err)
		}
	}()
	ctx.Response().Header().Set("filename", fileName)
	return ctx.File(fileName)
}
