package inner

import (
	"ops-ticket-api/internal/framework/ctxresp"
	"ops-ticket-api/internal/framework/validator"
	"ops-ticket-api/proto/pb"
	"ops-ticket-api/services/configure"

	"github.com/labstack/echo/v4"
)

// AddDCSBotConfig 新增Discord机器人配置
func AddDCSBotConfig(ctx echo.Context) error {
	var req = &pb.DCSBotConfigAddReq{}
	if err := validator.BindValidate(ctx, req); err != nil {
		return err
	}

	if err := configure.DCBotConfigService.Add(ctx, req); err != nil {
		return err
	}

	return ctxresp.Out(ctx, pb.Empty{})
}

// CheckDCSBotConfig 验证Discord机器人配置
func CheckDCSBotConfig(ctx echo.Context) error {
	var req = &pb.DCSBotConfigAddReq{}
	if err := validator.BindValidate(ctx, req); err != nil {
		return err
	}

	if err := configure.DCBotConfigService.Check(ctx, req); err != nil {
		return err
	}

	return ctxresp.Out(ctx, pb.Empty{})
}

// DCSBotConfigList 获取Discord机器人配置列表
func DCSBotConfigList(ctx echo.Context) error {
	var req = &pb.DCSBotConfigListReq{}
	if err := validator.BindValidate(ctx, req); err != nil {
		return err
	}

	if req.Page == 0 {
		req.Page = 1
	}

	if req.PageSize == 0 {
		req.PageSize = 10
	}

	resp, err := configure.DCBotConfigService.List(ctx, req)
	if err != nil {
		return err
	}

	return ctxresp.Out(ctx, resp)
}

// UpdateDCSBotConfigWelcomeMessage 更新DC机器人配置欢迎消息
func UpdateDCSBotConfigWelcomeMessage(ctx echo.Context) error {
	var req = &pb.UpdateDCSBotConfigWelcomeMessageReq{}
	if err := validator.BindValidate(ctx, req); err != nil {
		return err
	}

	if err := configure.DCBotConfigService.UpdateWelcomeMessage(ctx, req); err != nil {
		return err
	}

	return ctxresp.Out(ctx, pb.Empty{})
}
