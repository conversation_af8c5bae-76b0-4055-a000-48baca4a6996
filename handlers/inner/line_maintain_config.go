package inner

import (
	"github.com/labstack/echo/v4"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"net/http"
	"ops-ticket-api/internal/code"
	"ops-ticket-api/internal/cst"
	"ops-ticket-api/internal/framework/ctxresp"
	"ops-ticket-api/internal/framework/validator"
	"ops-ticket-api/internal/framework/xerrors"
	"ops-ticket-api/proto/pb"
	"ops-ticket-api/services"
	"ops-ticket-api/services/commsrv"
	"ops-ticket-api/services/configure"
	"time"
)

// LineMaintainConfigList 玩家关系维护配置列表
func LineMaintainConfigList(ctx echo.Context) error {
	param := &pb.LineMaintainConfigListReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	if len(param.Project) == 0 {
		if permList, _ := commsrv.LoadPermGmList(ctx.Request().Context(), ctx.Get(cst.AccountInfoCtx).(string)); len(permList) > 0 {
			param.Project = permList
		}
	}
	resp, err := configure.LineMaintainConfigList(ctx, param)
	if err != nil {
		return err
	}
	return ctxresp.Out(ctx, resp)
}

func LineMaintainConfigListExport(ctx echo.Context) error {
	param := &pb.LineMaintainConfigListReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	records, err := configure.LineMaintainConfigListExport(ctx, param)
	if err != nil {
		logger.Error(ctx.Request().Context(), "export workstation file err", logger.Any("err", err))
		return ctxresp.Error(ctx, xerrors.New(err.Error(), code.Error))
	}

	fileName := "line_maintain_config_export_" + time.Now().Format("**************") + ".csv"
	ctx.Response().Header().Add(echo.HeaderContentType, "text/plain; charset=utf-8")
	ctx.Response().Header().Add("filename", fileName)
	ctx.Response().Header().Set(echo.HeaderContentDisposition, "attachment; filename="+fileName)
	ctx.Response().WriteHeader(http.StatusOK)

	for res := range records {
		ctx.Response().Write(res)
		ctx.Response().Flush()
	}
	return nil
}

// LineMaintainConfigEdit 玩家关系维护配置编辑
func LineMaintainConfigEdit(ctx echo.Context) error {
	param := &pb.LineMaintainConfigEditReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	if param.VipState > 0 && param.VipState != code.NonVip && param.VipState != code.Vip {
		return xerrors.New("参数错误", code.InvalidParams)
	}
	player, err := services.NewDscSrv().DiscordPlayer(ctx, param.Fpid, param.Project, param.Uid)
	if err != nil {
		return err
	}
	if err := configure.LineMaintainConfigEdit(ctx, param, player); err != nil {
		return err
	}
	return ctxresp.Out(ctx, pb.Empty{})
}

// LineMaintainConfigDel 玩家关系维护配置删除
func LineMaintainConfigDel(ctx echo.Context) error {
	param := &pb.LineMaintainConfigDelReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	err := configure.LineMaintainConfigDel(ctx, param)
	if err != nil {
		return err
	}
	return ctxresp.Out(ctx, pb.Empty{})
}

func LinePlayerAccounts(ctx echo.Context) error {
	param := &pb.LinePlayerAccountsReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	resp, err := configure.LinePlayerAccounts(ctx, param)
	if err != nil {
		return err
	}
	return ctxresp.Out(ctx, resp)
}

func LinePlayerUidList(ctx echo.Context) error {
	param := &pb.DiscordPlayerUidReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	resp, err := configure.LinePlayerUidList(ctx, param)
	if err != nil {
		return err
	}
	return ctxresp.Out(ctx, resp)
}
