package inner

import (
	"ops-ticket-api/internal/framework/ctxresp"
	"ops-ticket-api/internal/framework/validator"
	"ops-ticket-api/proto/pb"
	"ops-ticket-api/services/configure"

	"github.com/labstack/echo/v4"
)

// CatOpts 问题分类级联接口
func CatOpts(ctx echo.Context) error {
	param := &pb.CatOptsReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	if param.Lang == "" {
		param.Lang = ctx.Request().Header.Get("lang")
	}
	list, err := configure.CatTree(ctx, param.Lang, param.Project)
	if err != nil {
		return err
	}
	return ctxresp.Out(ctx, list.Data)
}

// CategoryRelease 配置更新接口
func CategoryRelease(ctx echo.Context) error {
	param := &pb.CatProjectLang{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	if err := configure.CategoryRelease(ctx, param); err != nil {
		return err
	}
	return ctxresp.Out(ctx, pb.Empty{})
}
