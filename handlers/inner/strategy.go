package inner

import (
	"github.com/labstack/echo/v4"
	"ops-ticket-api/internal/code"
	"ops-ticket-api/internal/cst"
	"ops-ticket-api/internal/framework/ctxresp"
	"ops-ticket-api/internal/framework/validator"
	"ops-ticket-api/internal/framework/xerrors"
	"ops-ticket-api/proto/pb"
	"ops-ticket-api/services"
	"ops-ticket-api/services/commsrv"
	"unicode/utf8"
)

// StrategySave 工单策略-保存
func StrategySave(ctx echo.Context) error {
	var param = &pb.StrategyAddReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	if utf8.RuneCountInString(param.StrategyName) > 100 {
		return xerrors.New("策略名称不能超过100个字符", code.InvalidParams)
	}
	err := services.NewStrategySrv().StrategySave(ctx, param)
	if err != nil {
		return err
	}
	return ctxresp.Out(ctx, pb.Empty{})
}

// StrategyDel 工单策略-删除
func StrategyDel(ctx echo.Context) error {
	var param = &pb.StrategyDelReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	// save question
	err := services.NewStrategySrv().StrategyDel(ctx, param)
	if err != nil {
		return err
	}
	return ctxresp.Out(ctx, pb.Empty{})
}

// StrategyEnable 工单策略-禁/启用
func StrategyEnable(ctx echo.Context) error {
	var param = &pb.StrategyEnabelReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	if param.Enable != code.ModuleDisable && param.Enable != code.ModuleEnable {
		return xerrors.New("参数错误", code.InvalidParams)
	}
	// enable
	err := services.NewStrategySrv().StrategyEnable(ctx, param)
	if err != nil {
		return err
	}
	return ctxresp.Out(ctx, pb.Empty{})
}

// StrategyList 工单策略-列表
func StrategyList(ctx echo.Context) error {
	var param = &pb.StrategyListReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	if len(param.Project) == 0 {
		if permList, _ := commsrv.LoadPermGmList(ctx.Request().Context(), ctx.Get(cst.AccountInfoCtx).(string)); len(permList) > 0 {
			param.Project = permList
		}
	}
	resp, err := services.NewStrategySrv().StrategyList(ctx, param)
	if err != nil {
		return err
	}
	return ctxresp.Out(ctx, resp)
}
