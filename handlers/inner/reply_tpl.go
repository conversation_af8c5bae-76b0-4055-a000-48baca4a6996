package inner

import (
	"github.com/labstack/echo/v4"
	"ops-ticket-api/internal/framework/ctxresp"
	"ops-ticket-api/internal/framework/validator"
	"ops-ticket-api/proto/pb"
	"ops-ticket-api/services/configure"
)

// ReplyTplOpts 模版筛选列表接口
func ReplyTplOpts(ctx echo.Context) error {
	list, err := configure.ReplyTplOpts(ctx)
	if err != nil {
		return err
	}
	return ctxresp.Out(ctx, list)
}

// ReplyTplList 模版列表接口
func ReplyTplList(ctx echo.Context) error {
	param := &pb.TplListReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	list, err := configure.ReplyTplList(ctx, param)
	if err != nil {
		return err
	}
	return ctxresp.Out(ctx, list)
}

// ReplyTplInfo 模版模版信息
func ReplyTplInfo(ctx echo.Context) error {
	param := &pb.TplIdReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	dest, err := configure.ReplyTplInfo(ctx, param.GetTplId())
	if err != nil {
		return err
	}
	return ctxresp.Out(ctx, dest)
}

// ReplyTplAdd 添加模版接口
func ReplyTplAdd(ctx echo.Context) error {
	param := &pb.ReplyTplAddReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	if err := configure.ReplyTplAdd(ctx, param); err != nil {
		return err
	}
	return ctxresp.Out(ctx, pb.Empty{})
}

// ReplyTplSave 修改模版接口
func ReplyTplSave(ctx echo.Context) error {
	param := &pb.ReplyTplSaveReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	if err := configure.ReplyTplSave(ctx, param); err != nil {
		return err
	}
	return ctxresp.Out(ctx, pb.Empty{})
}

// ReplyTplEnable 模版禁用启用接口
func ReplyTplEnable(ctx echo.Context) error {
	param := &pb.EnableReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	if err := configure.ReplyTplEnable(ctx, param); err != nil {
		return err
	}
	return ctxresp.Out(ctx, pb.Empty{})
}
