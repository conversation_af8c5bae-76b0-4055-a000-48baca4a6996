package inner

import (
	"github.com/labstack/echo/v4"
	"ops-ticket-api/internal/framework/ctxresp"
	"ops-ticket-api/internal/framework/validator"
	"ops-ticket-api/proto/pb"
	"ops-ticket-api/services/configure"
)

// AuthConfigSync 从老工单同步游戏的auth配置数据
func AuthConfigSync(ctx echo.Context) error {
	param := &pb.AuthConfigSyncReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	if err := configure.AuthConfigSave(ctx, param); err != nil {
		return err
	}
	return ctxresp.Out(ctx, pb.Empty{})
}
