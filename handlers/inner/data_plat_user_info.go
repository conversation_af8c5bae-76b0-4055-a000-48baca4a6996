package inner

import (
	"github.com/labstack/echo/v4"
	"ops-ticket-api/internal/framework/ctxresp"
	"ops-ticket-api/internal/framework/validator"
	"ops-ticket-api/proto/pb"
	"ops-ticket-api/services/dataplatusersrv"
)

func NewDataPlatUserInfoHandler() *dataPlatUserInfoHandler {
	return &dataPlatUserInfoHandler{}
}

type dataPlatUserInfoHandler struct {
}

func (h dataPlatUserInfoHandler) GameItemList(ctx echo.Context) error {
	param := &pb.DataPlatGameItemListReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	detail, err := dataplatusersrv.GameItemList(ctx, param)
	if err != nil {
		return err
	}
	return ctxresp.Out(ctx, detail)
}

func (h dataPlatUserInfoHandler) GameItemOpts(ctx echo.Context) error {
	param := &pb.DataPlatGameItemOptsReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	res, err := dataplatusersrv.GameItemOpts(ctx, param.Project)
	if err != nil {
		return err
	}
	return ctxresp.Out(ctx, &pb.DataPlatGameItemOptsResp{Data: res})
}

func (h dataPlatUserInfoHandler) GameItemBatchSave(ctx echo.Context) error {
	param := &pb.DataPlatGameItemBatchSaveReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}

	if err := dataplatusersrv.GameItemBatchSave(ctx, param); err != nil {
		return err
	}
	return ctxresp.Out(ctx, pb.Empty{})
}

func (h dataPlatUserInfoHandler) GoldInfo(ctx echo.Context) error {
	param := &pb.DataPlatUserGoldInfoReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	detail, err := dataplatusersrv.GoldInfo(ctx, param)
	if err != nil {
		return err
	}
	if len(detail) == 0 {
		detail = make([]*pb.DataPlatUserGoldInfoResp, 0)
	}
	return ctxresp.Out(ctx, detail)
}

func (h dataPlatUserInfoHandler) ItemInfo(ctx echo.Context) error {
	param := &pb.DataPlatUserItemInfoReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	detail, err := dataplatusersrv.ItemInfo(ctx, param)
	if err != nil {
		return err
	}
	if len(detail) == 0 {
		detail = make([]*pb.DataPlatUserItemInfoResp, 0)
	}
	return ctxresp.Out(ctx, detail)
}

func (h dataPlatUserInfoHandler) PayInfo(ctx echo.Context) error {
	param := &pb.DataPlatUserPayInfoReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	detail, err := dataplatusersrv.PayInfo(ctx, param)
	if err != nil {
		return err
	}
	if len(detail) == 0 {
		detail = make([]*pb.DataPlatUserPayInfoResp, 0)
	}
	return ctxresp.Out(ctx, detail)
}

func (h dataPlatUserInfoHandler) LoginInfo(ctx echo.Context) error {
	param := &pb.DataPlatUserLoginInfoReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	detail, err := dataplatusersrv.LoginInfo(ctx, param)
	if err != nil {
		return err
	}
	if detail == nil {
		detail = make([]*pb.DataPlatUserLoginInfoResp, 0)
	}
	return ctxresp.Out(ctx, detail)
}
