package inner

import (
	"errors"
	"github.com/labstack/echo/v4"
	"ops-ticket-api/internal/framework/ctxresp"
	"ops-ticket-api/internal/framework/validator"
	"ops-ticket-api/proto/pb"
	"ops-ticket-api/services"
)

// LineTabSave 创建Line 搜索tab
func LineTabSave(ctx echo.Context) error {
	param := &pb.LineTabAddReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	//游戏字段单选校验
	if len(param.Detail.Project) != 1 {
		return errors.New("自定义tab仅支持单个游戏，请检查选择的项是否正确！")
	}
	if err := services.LineTabSave(ctx, param); err != nil {
		return err
	}
	return ctxresp.Out(ctx, pb.Empty{})
}

// LineTabDel 删除Line 搜索tab
func LineTabDel(ctx echo.Context) error {
	param := &pb.LineTabDelReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	if err := services.LineTabDel(ctx, param); err != nil {
		return err
	}
	return ctxresp.Out(ctx, pb.Empty{})
}

// LineTabList Line 搜索tab列表
func LineTabList(ctx echo.Context) error {
	resp, err := services.LineTabList(ctx)
	if err != nil {
		return err
	}
	return ctxresp.Out(ctx, resp)
}

// LineTabCount Line 搜索tab数量
func LineTabCount(ctx echo.Context) error {
	resp, err := services.LineTabCount(ctx)
	if err != nil {
		return err
	}
	return ctxresp.Out(ctx, resp)
}

// LineTabEdit Line 编辑tab
func LineTabEdit(ctx echo.Context) error {
	param := &pb.LineTabEditReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	if err := services.LineTabEdit(ctx, param); err != nil {
		return err
	}
	return ctxresp.Out(ctx, pb.Empty{})
}

// LineTabUpdateSort tab排序接口
func LineTabUpdateSort(ctx echo.Context) error {
	param := &pb.LineTabUpdateSortReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	if err := services.LineTabUpdateSort(ctx, param); err != nil {
		return err
	}
	return ctxresp.Out(ctx, pb.Empty{})
}
