// Copyright 2021 funplus Authors. All Rights Reserved.
// @Description: 附件
// @Author: Darcy
// @Date: 2021/11/2 2:15 PM

package inner

import (
	"github.com/labstack/echo/v4"
	"ops-ticket-api/internal/framework/ctxresp"
	"ops-ticket-api/internal/framework/validator"
	"ops-ticket-api/proto/pb"
	"ops-ticket-api/services"
	"ops-ticket-api/services/configure"
)

// SysEnum 系统枚举值接口
func SysEnum(ctx echo.Context) error {
	list, err := services.NewAddonsSvc().EnumResp(ctx)
	if err != nil {
		return err
	}
	return ctxresp.Out(ctx, list)
}

// ChannelList 渠道列表接口
func ChannelList(ctx echo.Context) error {
	param := &pb.Project{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}

	dest := services.NewAddonsSvc().ChannelList(ctx, param.ProjectName)
	return ctxresp.Out(ctx, dest)
}

// ChannelTreeList 渠道列表接口
func ChannelTreeList(ctx echo.Context) error {
	param := &pb.Project{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}

	dest := services.NewAddonsSvc().SubChannelList(ctx, param.ProjectName)
	return ctxresp.Out(ctx, dest)
}

// ChannelPackageIdList 渠道号列表接口
func ChannelPackageIdList(ctx echo.Context) error {
	param := &pb.Project{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}

	dest := services.NewAddonsSvc().PackageIDList(ctx, param.ProjectName)
	return ctxresp.Out(ctx, dest)
}

// GameList 游戏列表接口
func GameList(ctx echo.Context) error {
	list, err := configure.ProjectCfg(ctx).GameList()
	if err != nil {
		return err
	}
	return ctxresp.Out(ctx, list)
}

// LanguageList 语言列表
func LanguageList(ctx echo.Context) error {
	dest, err := services.NewAddonsSvc().LanguageList(ctx)
	if err != nil {
		return err
	}
	return ctxresp.Out(ctx, dest)
}

// UserList 用户列表接口
func UserList(ctx echo.Context) error {
	list, err := configure.DefaultUserSvc.GetList(ctx)
	if err != nil {
		return err
	}
	return ctxresp.Out(ctx, list)
}

// DscBotIdList 机器人列表查询
func DscBotIdList(ctx echo.Context) error {
	param := &pb.Projects{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}

	dest, err := services.NewAddonsSvc().DscBotList(ctx, param.Projects)
	if err != nil {
		return err
	}
	return ctxresp.Out(ctx, dest)
}

func LineChannelList(ctx echo.Context) error {
	param := &pb.Projects{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	dest, err := services.NewAddonsSvc().LineChannelList(ctx, param.Projects)
	if err != nil {
		return err
	}
	return ctxresp.Out(ctx, dest)
}
