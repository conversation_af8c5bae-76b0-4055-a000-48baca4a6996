package inner

import (
	"errors"
	"github.com/jinzhu/now"
	"github.com/labstack/echo/v4"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"ops-ticket-api/internal/code"
	"ops-ticket-api/internal/cst"
	"ops-ticket-api/internal/framework/ctxresp"
	"ops-ticket-api/internal/framework/validator"
	"ops-ticket-api/internal/framework/xerrors"
	"ops-ticket-api/proto/pb"
	"ops-ticket-api/services"
	"ops-ticket-api/services/commsrv"
	"ops-ticket-api/utils"
	"os"
	"time"
)

// InteractStats discord玩家交互量统计
func InteractStats(ctx echo.Context) error {
	param := &pb.DiscordPlayerInteractStatsReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	if len(param.Date) > 0 && len(param.Date) != 2 {
		return xerrors.New(errors.New("there must be two dates"), code.InvalidParams)
	}
	if len(param.Date) == 2 && utils.CheckIfExceedsOneYear(param.Date[0], param.Date[1]) {
		return xerrors.New(errors.New("date range beyond one year"), code.InvalidParams)
	}
	if len(param.Project) == 0 {
		if permList, _ := commsrv.LoadPermGmList(ctx.Request().Context(), ctx.Get(cst.AccountInfoCtx).(string)); len(permList) > 0 {
			param.Project = permList
		}
	}
	resp, err := services.InteractStats(ctx, param)
	if err != nil {
		return err
	}
	return ctxresp.Out(ctx, resp)
}

func InteractDetailExport(ctx echo.Context) error {
	param := &pb.DiscordPlayerInteractStatsReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	if len(param.Date) > 0 && len(param.Date) != 2 {
		return xerrors.New(errors.New("there must be two dates"), code.InvalidParams)
	}
	if len(param.Date) == 2 && utils.CheckIfExceedsOneYear(param.Date[0], param.Date[1]) {
		return xerrors.New(errors.New("date range beyond one year"), code.InvalidParams)
	}
	if len(param.Project) == 0 {
		if permList, _ := commsrv.LoadPermGmList(ctx.Request().Context(), ctx.Get(cst.AccountInfoCtx).(string)); len(permList) > 0 {
			param.Project = permList
		}
	}
	fileName, err := services.InteractDetailExport(ctx, param)
	if err != nil || fileName == "" {
		logger.Errorf(ctx.Request().Context(), "discord interact detail export file:%v err:%v", fileName, err)
		return xerrors.New(err, code.RpcRespErr)
	}
	defer func() {
		if err = os.Remove("./" + fileName); err != nil {
			logger.Errorf(ctx.Request().Context(), "discord interact detail export remove file:%v err:%v", fileName, err)
		}
	}()
	ctx.Response().Header().Set("filename", fileName)
	return ctx.File(fileName)
}

func MessageCountDateStats(ctx echo.Context) error {
	param := &pb.DiscordMessageCountReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	if len(param.Date) > 0 && len(param.Date) != 2 {
		return xerrors.New(errors.New("there must be two dates"), code.InvalidParams)
	}
	if len(param.Date) == 2 && utils.CheckIfExceedsOneYear(param.Date[0], param.Date[1]) {
		return xerrors.New(errors.New("date range beyond one year"), code.InvalidParams)
	}
	if len(param.Project) == 0 {
		if permList, _ := commsrv.LoadPermGmList(ctx.Request().Context(), ctx.Get(cst.AccountInfoCtx).(string)); len(permList) > 0 {
			param.Project = permList
		}
	}
	resp, err := services.MessageCountDateStats(ctx, param)
	if err != nil {
		return err
	}
	return ctxresp.Out(ctx, resp)
}

func MessageCountOperatorStats(ctx echo.Context) error {
	param := &pb.DiscordMessageCountReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	if len(param.Date) > 0 && len(param.Date) != 2 {
		return xerrors.New(errors.New("there must be two dates"), code.InvalidParams)
	}
	if len(param.Date) == 2 && utils.CheckIfExceedsOneYear(param.Date[0], param.Date[1]) {
		return xerrors.New(errors.New("date range beyond one year"), code.InvalidParams)
	}
	if len(param.Project) == 0 {
		if permList, _ := commsrv.LoadPermGmList(ctx.Request().Context(), ctx.Get(cst.AccountInfoCtx).(string)); len(permList) > 0 {
			param.Project = permList
		}
	}
	resp, err := services.MessageCountOperatorStats(ctx, param)
	if err != nil {
		return err
	}
	return ctxresp.Out(ctx, resp)
}

func MessageCountDetailExport(ctx echo.Context) error {
	param := &pb.DiscordMessageCountReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	if len(param.Date) > 0 && len(param.Date) != 2 {
		return xerrors.New(errors.New("there must be two dates"), code.InvalidParams)
	}
	if len(param.Date) == 2 && utils.CheckIfExceedsOneYear(param.Date[0], param.Date[1]) {
		return xerrors.New(errors.New("date range beyond one year"), code.InvalidParams)
	}
	if len(param.Project) == 0 {
		if permList, _ := commsrv.LoadPermGmList(ctx.Request().Context(), ctx.Get(cst.AccountInfoCtx).(string)); len(permList) > 0 {
			param.Project = permList
		}
	}
	fileName, err := services.MessageCountDetailExport(ctx, param)
	if err != nil || fileName == "" {
		logger.Errorf(ctx.Request().Context(), "discord interact detail export file:%v err:%v", fileName, err)
		return xerrors.New(err, code.RpcRespErr)
	}
	defer func() {
		if err = os.Remove("./" + fileName); err != nil {
			logger.Errorf(ctx.Request().Context(), "discord interact detail export remove file:%v err:%v", fileName, err)
		}
	}()
	ctx.Response().Header().Set("filename", fileName)
	return ctx.File(fileName)
}

// ReplyTimeDetailStats 客服回复时间报表
func ReplyTimeDetailStats(ctx echo.Context) error {
	param := &pb.DiscordReplyTimeReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	if len(param.Date) == 0 {
		nw := time.Now().UTC()
		param.Date = []string{
			nw.AddDate(0, 0, -7).Format("2006-01-02"),
			nw.AddDate(0, 0, -1).Format("2006-01-02")}
	} else if len(param.Date) != 2 {
		return xerrors.New(errors.New("there must be two dates"), code.InvalidParams)
	} else if utils.CheckIfExceedsMonth(param.Date[0], param.Date[1], 3) {
		return xerrors.New(errors.New("date range beyond three month"), code.InvalidParams)
	}

	if len(param.Project) == 0 {
		if permList, _ := commsrv.LoadPermGmList(ctx.Request().Context(), ctx.Get(cst.AccountInfoCtx).(string)); len(permList) > 0 {
			param.Project = permList
		}
	}
	resp, err := services.ReplyTimeDetailStats(ctx, param)
	if err != nil {
		return err
	}
	return ctxresp.Out(ctx, resp)
}

// ReplyTimeDetailExport 客服回复时间报表下载
func ReplyTimeDetailExport(ctx echo.Context) error {
	param := &pb.DiscordReplyTimeReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	if len(param.Date) == 0 {
		nw := time.Now().UTC()
		param.Date = []string{
			nw.AddDate(0, 0, -7).Format("2006-01-02"),
			nw.AddDate(0, 0, -1).Format("2006-01-02")}
	} else if len(param.Date) != 2 {
		return xerrors.New(errors.New("there must be two dates"), code.InvalidParams)
	} else if utils.CheckIfExceedsMonth(param.Date[0], param.Date[1], 3) {
		return xerrors.New(errors.New("date range beyond three month"), code.InvalidParams)
	}

	if len(param.Project) == 0 {
		if permList, _ := commsrv.LoadPermGmList(ctx.Request().Context(), ctx.Get(cst.AccountInfoCtx).(string)); len(permList) > 0 {
			param.Project = permList
		}
	}
	fileName, err := services.ReplyTimeDetailExport(ctx, param)
	if err != nil || fileName == "" {
		logger.Errorf(ctx.Request().Context(), "reply time detail export file:%v err:%v", fileName, err)
		return xerrors.New(err, code.RpcRespErr)
	}
	defer func() {
		if err = os.Remove("./" + fileName); err != nil {
			logger.Errorf(ctx.Request().Context(), "reply time detail export remove file:%v err:%v", fileName, err)
		}
	}()
	ctx.Response().Header().Set("filename", fileName)
	return ctx.File(fileName)
}

// SatisfactionStats discord玩家调查问卷满意度报表
func SatisfactionStats(ctx echo.Context) error {
	param := &pb.DiscordPlayerSatisfactionStatsReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	if len(param.Project) == 0 {
		if permList, _ := commsrv.LoadPermGmList(ctx.Request().Context(), ctx.Get(cst.AccountInfoCtx).(string)); len(permList) > 0 {
			param.Project = permList
		}
	}
	if len(param.Date) == 0 || len(param.Date) != 2 {
		param.Date = []string{time.Now().AddDate(0, -2, 0).Format(time.DateOnly), time.Now().Format(time.DateOnly)}
	} else {
		start, _ := now.Parse(param.Date[0])
		end, _ := now.Parse(param.Date[1])
		if start.Before(end.AddDate(0, 0, -90)) {
			return xerrors.New(errors.New("date range beyond 90 days"), code.InvalidParams)
		}
	}

	resp, err := services.SatisfactionStats(ctx, param)
	if err != nil {
		return err
	}
	return ctxresp.Out(ctx, resp)
}

// SatisfactionStatsExport discord调查问卷满意度报表下载
func SatisfactionStatsExport(ctx echo.Context) error {
	param := &pb.DiscordPlayerSatisfactionStatsReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}

	if len(param.Date) == 0 || len(param.Date) != 2 {
		param.Date = []string{time.Now().AddDate(0, -2, 0).Format(time.DateOnly), time.Now().Format(time.DateOnly)}
	} else {
		start, _ := now.Parse(param.Date[0])
		end, _ := now.Parse(param.Date[1])
		if start.Before(end.AddDate(0, 0, -90)) {
			return xerrors.New(errors.New("date range beyond 90 days"), code.InvalidParams)
		}
	}

	if len(param.Project) == 0 {
		if permList, _ := commsrv.LoadPermGmList(ctx.Request().Context(), ctx.Get(cst.AccountInfoCtx).(string)); len(permList) > 0 {
			param.Project = permList
		}
	}
	fileName, err := services.SatisfactionStatsExport(ctx, param)
	if err != nil || fileName == "" {
		logger.Errorf(ctx.Request().Context(), "discord interact detail export file:%v err:%v", fileName, err)
		return xerrors.New(err, code.RpcRespErr)
	}
	defer func() {
		if err = os.Remove("./" + fileName); err != nil {
			logger.Errorf(ctx.Request().Context(), "discord interact detail export remove file:%v err:%v", fileName, err)
		}
	}()
	ctx.Response().Header().Set("filename", fileName)
	return ctx.File(fileName)
}
