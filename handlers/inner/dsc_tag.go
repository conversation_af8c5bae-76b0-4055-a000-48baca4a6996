package inner

import (
	"github.com/labstack/echo/v4"
	"ops-ticket-api/internal/code"
	"ops-ticket-api/internal/framework/ctxresp"
	"ops-ticket-api/internal/framework/validator"
	"ops-ticket-api/internal/framework/xerrors"
	"ops-ticket-api/proto/pb"
	"ops-ticket-api/services"
)

// DiscordTagSave 创建discord标签
func DiscordTagSave(ctx echo.Context) error {
	param := &pb.DiscordTagAddReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	if err := services.DiscordTagSave(ctx, param); err != nil {
		return err
	}
	return ctxresp.Out(ctx, pb.Empty{})
}

// DiscordTagEdit 编辑discord标签
func DiscordTagEdit(ctx echo.Context) error {
	param := &pb.DiscordTagEditReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	if err := services.DiscordTagEdit(ctx, param); err != nil {
		return err
	}
	return ctxresp.Out(ctx, pb.Empty{})
}

// DiscordTagList discord标签列表
func DiscordTagList(ctx echo.Context) error {
	param := &pb.DiscordTagListReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	resp, err := services.DiscordTagList(ctx, param)
	if err != nil {
		return err
	}
	return ctxresp.Out(ctx, resp)
}

func DiscordBatchTag(ctx echo.Context) error {
	param := &pb.DiscordBatchTagReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	if err := services.DiscordBatchTag(ctx, param); err != nil {
		return err
	}
	return ctxresp.Out(ctx, pb.Empty{})
}

func DiscordPublicTag(ctx echo.Context) error {
	param := &pb.DiscordPublicTagReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	list, err := services.DiscordPublicTag(ctx, param)
	if err != nil {
		return err
	}
	return ctxresp.Out(ctx, list)
}

func DiscordTagBatchDelete(ctx echo.Context) error {
	param := &pb.DiscordTagBatchDelete{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	if len(param.TagIds) <= 0 {
		return xerrors.New("公共标签为空，不能删除", code.InvalidParams)
	}
	err := services.DiscordBatchDeleteTags(ctx, param)
	if err != nil {
		return err
	}
	return ctxresp.Out(ctx, pb.Empty{})
}
