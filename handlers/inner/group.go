// Copyright 2021 funplus Authors. All Rights Reserved.
// @Description: 技能组配置
// @Author: Darcy
// @Date: 2021/10/26 3:13 PM

package inner

import (
	"ops-ticket-api/internal/framework/ctxresp"
	"ops-ticket-api/internal/framework/validator"
	"ops-ticket-api/proto/pb"
	"ops-ticket-api/services/configure"

	"github.com/labstack/echo/v4"
)

// GroupList 分单配置列表
func GroupList(ctx echo.Context) error {
	param := &pb.GroupListReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	resp, err := configure.GroupList(ctx, param)
	if err != nil {
		return err
	}
	return ctxresp.Out(ctx, resp)
}

// GroupInfo 团队分单信息接口
func GroupInfo(ctx echo.Context) error {
	param := &pb.GroupIdReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	resp, err := configure.GroupInfo(ctx, param.GroupId)
	if err != nil {
		return err
	}
	return ctxresp.Out(ctx, resp)
}

// GroupSave 添加技能组接口
func GroupSave(ctx echo.Context) error {
	param := &pb.GroupSaveReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	if err := configure.GroupSave(ctx, param); err != nil {
		return err
	}
	return ctxresp.Out(ctx, pb.Empty{})
}

// GroupDel 删除分单用户接口
func GroupDel(ctx echo.Context) error {
	param := &pb.GroupDelReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	if err := configure.GroupDel(ctx, param); err != nil {
		return err
	}
	return ctxresp.Out(ctx, pb.Empty{})
}
