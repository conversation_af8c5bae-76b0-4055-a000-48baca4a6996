package inner

import (
	"github.com/labstack/echo/v4"
	"ops-ticket-api/internal/framework/ctxresp"
	"ops-ticket-api/internal/framework/validator"
	"ops-ticket-api/proto/pb"
	"ops-ticket-api/services/configure"
)

// CatChannelTree 问题分类级联接口
func CatChannelTree(ctx echo.Context) error {
	param := &pb.CatProjectReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	list, err := configure.CatChannelTree(ctx, param.Project, param.CatType)
	if err != nil {
		return err
	}
	return ctxresp.Out(ctx, list)
}

// CatChannelAdd 添加分类接口
func CatChannelAdd(ctx echo.Context) error {
	param := &pb.ChannelCatAddReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	if err := configure.CatChannelAdd(ctx, param); err != nil {
		return err
	}
	return ctxresp.Out(ctx, pb.Empty{})
}

// CategorySave 编辑分类接口
func CatChannelSave(ctx echo.Context) error {
	param := &pb.ChannelCatSaveReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	if err := configure.CatChannelSave(ctx, param); err != nil {
		return err
	}
	return ctxresp.Out(ctx, pb.Empty{})
}

// CatChannelDel 问题分类删除接口
func CatChannelDel(ctx echo.Context) error {
	param := &pb.CatIdReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	if err := configure.CatChannelDel(ctx, param.CatId); err != nil {
		return err
	}
	return ctxresp.Out(ctx, pb.Empty{})
}
