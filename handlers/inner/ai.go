package inner

import (
	"github.com/labstack/echo/v4"
	"ops-ticket-api/internal/framework/ctxresp"
	"ops-ticket-api/internal/framework/validator"
	"ops-ticket-api/proto/pb"
	"ops-ticket-api/services/ai"
)

// AISummary ai总结
func AISummary(ctx echo.Context) error {
	param := &pb.AISummaryReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	summary, err := ai.Summary(ctx, param)
	if err != nil {
		return err
	}
	return ctxresp.Out(ctx, summary)
}

// AIPolish ai总结
func AIPolish(ctx echo.Context) error {
	param := &pb.AIPolishReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	polish, err := ai.Polish(ctx, param)
	if err != nil {
		return err
	}
	return ctxresp.Out(ctx, polish)
}

// AIPreReply ai预回复
func AIPreReply(ctx echo.Context) error {
	param := &pb.AIPreReplyReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	preReply, err := ai.PreReply(ctx, param)
	if err != nil {
		return err
	}
	return ctxresp.Out(ctx, preReply)
}

// AIFAQ ai生成问题和答案
func AIFaq(ctx echo.Context) error {
	param := &pb.AIFaqReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	faq, err := ai.AiFaq(ctx, param)
	if err != nil {
		return err
	}
	return ctxresp.Out(ctx, faq.Data)
}
