package inner

import (
	"bytes"
	"context"
	"errors"
	"fmt"
	"github.com/labstack/echo/v4"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"go.uber.org/zap"
	"io"
	"net/http"
	"ops-ticket-api/internal/code"
	"ops-ticket-api/internal/cst"
	"ops-ticket-api/internal/framework/cache/keys"
	"ops-ticket-api/internal/framework/cache/rds"
	"ops-ticket-api/internal/framework/ctxresp"
	"ops-ticket-api/internal/framework/validator"
	"ops-ticket-api/internal/framework/xerrors"
	"ops-ticket-api/proto/pb"
	"ops-ticket-api/services"
	"ops-ticket-api/services/commsrv"
	"ops-ticket-api/utils"
	"os"
	"strings"
	"sync"
	"time"
)

type dscHandler struct {
}

func NewDscHandle() *dscHandler {
	return &dscHandler{}
}

func (h *dsc<PERSON>andler) UserList(ctx echo.Context) error {
	param := &pb.DscUserListReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	if param.Page*param.PageSize > 10000 {
		return xerrors.New(code.StatusText(code.RecordJumpLimit), code.RecordJumpLimit)
	}
	if len(param.Project) == 0 {
		if permList, _ := commsrv.LoadPermGmList(ctx.Request().Context(), ctx.Get(cst.AccountInfoCtx).(string)); len(permList) > 0 {
			param.Project = permList
		}
	}
	if strings.Contains(param.UserContent, "&&") && strings.Contains(param.UserContent, "||") {
		return xerrors.New("不能同时输入&&或||", code.InvalidParams)
	}
	if strings.Contains(param.UserDetailRemark, "&&") && strings.Contains(param.UserDetailRemark, "||") {
		return xerrors.New("不能同时输入&&或||", code.InvalidParams)
	}
	users, total, err := services.NewDscSrv().GetUserPool(ctx, param)
	if err != nil {
		return err
	}
	return ctxresp.Out(ctx, pb.DscUserListResp{
		CurrentPage: param.Page,
		PerPage:     param.PageSize,
		Total:       total,
		Data:        users,
	})
}

func (h *dscHandler) UserListExport(ctx echo.Context) error {
	param := &pb.DscUserListReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	if param.Page*param.PageSize > 10000 {
		return xerrors.New(code.StatusText(code.RecordJumpLimit), code.RecordJumpLimit)
	}
	if len(param.Project) == 0 {
		if permList, _ := commsrv.LoadPermGmList(ctx.Request().Context(), ctx.Get(cst.AccountInfoCtx).(string)); len(permList) > 0 {
			param.Project = permList
		}
	}
	records, err := services.DscPoolExport(ctx, param)
	if err != nil {
		logger.Error(ctx.Request().Context(), "Dsc export workstation file err", logger.Any("err", err))
		return ctxresp.Error(ctx, xerrors.New(err.Error(), code.Error))
	}

	fileName := fmt.Sprintf("discord_export_%d.csv", time.Now().UnixNano())
	ctx.Response().Header().Add(echo.HeaderContentType, "text/plain; charset=utf-8")
	ctx.Response().Header().Add("filename", fileName)
	ctx.Response().Header().Set(echo.HeaderContentDisposition, "attachment; filename="+fileName)
	ctx.Response().WriteHeader(http.StatusOK)

	for res := range records {
		ctx.Response().Write(res)
		ctx.Response().Flush()
	}
	return nil
}

func (h *dscHandler) UserStats(ctx echo.Context) error {
	param := &pb.DiscordStatsReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	if len(param.Project) == 0 {
		if permList, _ := commsrv.LoadPermGmList(ctx.Request().Context(), ctx.Get(cst.AccountInfoCtx).(string)); len(permList) > 0 {
			param.Project = permList
		}
	}
	projects := make([]interface{}, len(param.Project))
	for i, project := range param.Project {
		projects[i] = project
	}
	var totalCount, waitReplyCount, mineWaitReplyCount int64
	var totalErr, waitReplyErr, mineWaitReplyErr error
	wg := &sync.WaitGroup{}
	wg.Add(3)
	// 获取当前游戏DC上所有玩家账号总数
	go func() {
		defer func() {
			ctx := ctx.Request().Context()
			if err := recover(); err != nil {
				logger.Errorf(ctx, "total.recover err:%v", err)
			}
		}()
		defer wg.Done()
		totalCount, totalErr = services.DiscordAccountsCount(ctx, projects)
	}()
	// 获取待回复信息的玩家账号总数
	go func() {
		defer func() {
			ctx := ctx.Request().Context()
			if err := recover(); err != nil {
				logger.Errorf(ctx, "waitReply.recover err:%v", err)
			}
		}()
		defer wg.Done()
		waitReplyCount, waitReplyErr = services.WaitReplyAccountsCount(ctx, projects)
	}()
	// 获取当前客服待回复信息的玩家账号总数
	go func() {
		defer func() {
			ctx := ctx.Request().Context()
			if err := recover(); err != nil {
				logger.Errorf(ctx, "mineWaitReply.recover err:%v", err)
			}
		}()
		defer wg.Done()
		mineWaitReplyCount, mineWaitReplyErr = services.MineWaitReplyAccountsCount(ctx, projects)
	}()

	wg.Wait()

	if totalErr != nil || waitReplyErr != nil || mineWaitReplyErr != nil {
		return xerrors.New(fmt.Sprintf("totalErr:%v. waitReplyErr:%v. mineWaitReplyErr:%v", totalErr, waitReplyErr, mineWaitReplyErr), code.EsQueryErr)
	}
	resp := new(pb.DiscordStatsResp)
	resp.DiscordUserCount = totalCount
	resp.WaitReplyAccounts = waitReplyCount
	resp.MineWaitReplyAccounts = mineWaitReplyCount
	return ctxresp.Out(ctx, resp)
}

func (h *dscHandler) UserRemarkSave(ctx echo.Context) error {
	param := &pb.DscUserRemarkAddReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	if err := services.DiscordRemarkSave(ctx, param); err != nil {
		return err
	}
	return ctxresp.Out(ctx, pb.Empty{})
}

func (h *dscHandler) ChannelDialog(ctx echo.Context) error {
	param := &pb.DscChannelDialogReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	hists, err := services.NewDscSrv().ChannelDialogForRemote(ctx, param)
	if err != nil {
		return err
	}
	return ctxresp.Out(ctx, &pb.DscDialogDetailResp{DialogList: hists})
}
func (h *dscHandler) DialogFresh(ctx echo.Context) error {
	param := &pb.DscChannelDialogFreshReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	res, err := services.NewDscSrv().DialogFresh(ctx, param)
	if err != nil {
		return err
	}
	return ctxresp.Out(ctx, res)
}

func (h *dscHandler) MessageCreate(ctx echo.Context) error {
	param := &pb.DscChannelMsgCreateReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	detail, err := services.NewDscSrv().MessageCreate(ctx, param)
	if err != nil {
		return err
	}
	waitDscEventWithTimeOut(ctx.Request().Context(), "dscHandler.MessageCreate", detail.ID)
	return ctxresp.Out(ctx, &pb.DscChannelMsgCreateResp{
		MsgId:     detail.ID,
		ChannelId: detail.ChannelID,
	})
}

func (h *dscHandler) MessageBatchCreate(ctx echo.Context) error {
	param := &pb.DscMsgBatchSendReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	if len(param.UidList) > 50 {
		return xerrors.New("uid批量发送不能超过50个", code.InvalidParams)
	}
	param.UidList = utils.UniqueValue(param.UidList)
	success, failedUidList, details, err := services.NewDscSrv().MessageBatchCreate(ctx, param)
	if err != nil {
		return err
	}
	data := []*pb.DscMsgBatchSendResp_MessageSendDetail{}
	for i := range details {
		detail := details[i]
		data = append(data, &pb.DscMsgBatchSendResp_MessageSendDetail{
			MsgId:     detail.ID,
			ChannelId: detail.ChannelID,
		})
		waitDscEventWithTimeOut(ctx.Request().Context(), "dscHandler.MessageCreate", detail.ID)
	}
	return ctxresp.Out(ctx, &pb.DscMsgBatchSendResp{
		Data:          data,
		Success:       int32(success),
		FailedUidList: failedUidList,
	})
}

func (h *dscHandler) AsyncMessageBatchCreate(ctx echo.Context) error {
	param := &pb.DscAscBatchSendReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	//uid去重
	param.UidList = utils.UniqueValue(param.UidList)
	if param.FileUrl != "" {
		// 从URL中提取文件名
		_, err := utils.ExtractFileNameFromURL(param.FileUrl)
		if err != nil {
			return fmt.Errorf("failed to extract file name: %v", err)
		}
	}
	if err := services.NewDscSrv().AsyncMessageBatchCreate(ctx, param); err != nil {
		return err
	}
	//waitDscEventWithTimeOut(ctx.Request().Context(), "dscHandler.MessageEdit", detail.ID)
	return ctxresp.Out(ctx, pb.Empty{})
}

func (h *dscHandler) MessageEdit(ctx echo.Context) error {
	param := &pb.DscChannelMsgEditReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	detail, err := services.NewDscSrv().MessageEdit(ctx, param)
	if err != nil {
		return err
	}
	// wait for discord gateway event has finished
	waitDscEventWithTimeOut(ctx.Request().Context(), "dscHandler.MessageEdit", detail.ID)
	return ctxresp.Out(ctx, &pb.DscChannelMsgCreateResp{
		MsgId:     detail.ID,
		ChannelId: detail.ChannelID,
	})
}

func (h *dscHandler) BatchSendFile(ctx echo.Context) error {
	param := &pb.DscFileBatchSendReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	if len(param.UidList) > 50 {
		return xerrors.New("uid批量发送不能超过50个", code.InvalidParams)
	}
	param.UidList = utils.UniqueValue(param.UidList)

	// 从URL中提取文件名
	fileName, err := utils.ExtractFileNameFromURL(param.FileUrl)
	if err != nil {
		return fmt.Errorf("failed to extract file name: %v", err)
	}
	// 下载文件并校验文件大小
	fileDst, err := utils.DownloadAndValidateFile(param.FileUrl)
	if err != nil {
		return fmt.Errorf("failed to download or validate file: %v\n", err)
	}
	defer fileDst.Close()
	success, failedUidList, details, err := services.NewDscSrv().DscFileBatchSend(ctx, param, fileName, fileDst)
	data := []*pb.DscMsgBatchSendResp_MessageSendDetail{}
	for i := range details {
		detail := details[i]
		data = append(data, &pb.DscMsgBatchSendResp_MessageSendDetail{
			MsgId:     detail.ID,
			ChannelId: detail.ChannelID,
		})
		waitDscEventWithTimeOut(ctx.Request().Context(), "dscHandler.SendFile", detail.ID)
	}
	return ctxresp.Out(ctx, &pb.DscMsgBatchSendResp{
		Data:          data,
		Success:       int32(success),
		FailedUidList: failedUidList,
	})
}

func (h *dscHandler) SendFile(ctx echo.Context) error {
	var param = &pb.DscChannelFileCreateReq{}
	param.ChannelId = ctx.Request().Header.Get("channel-id")
	param.BotId = ctx.Request().Header.Get("bot-id")
	if err := ctx.Validate(param); err != nil {
		return err
	}

	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	file, err := ctx.FormFile("file")
	if err != nil {
		return err
	}
	// 文件大小不能超过 5M
	if file.Size > 1024*1024*5 {
		return ctxresp.Error(ctx, fmt.Errorf("file size must less than 5M"))
	}
	fileDst, err := file.Open()
	if err != nil {
		return ctxresp.Error(ctx, fmt.Errorf("open file return err: %v", err))
	}
	defer fileDst.Close()
	name := file.Filename
	msg, err := services.NewDscSrv().DscFileSend(ctx, param, name, fileDst)
	if err != nil {
		return err
	}
	// wait for discord gateway event has finished
	waitDscEventWithTimeOut(ctx.Request().Context(), "dscHandler.SendFile", msg.ID)
	return ctxresp.Out(ctx, msg)
}

func (h *dscHandler) MessageReply(ctx echo.Context) error {
	param := &pb.DscChannelMsgEditReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	detail, err := services.NewDscSrv().MessageReply(ctx, param)
	if err != nil {
		return err
	}
	// wait for discord gateway event has finished
	waitDscEventWithTimeOut(ctx.Request().Context(), "dscHandler.MessageReply", detail.ID)
	return ctxresp.Out(ctx, &pb.DscChannelMsgCreateResp{
		MsgId:     detail.ID,
		ChannelId: detail.ChannelID,
	})
}

func (h *dscHandler) ProxyResource(ctx echo.Context) error {
	var param = &pb.DscProxyResourceReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	resp, err := http.Get(param.Url)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	if param.OnlyProxy { // only proxy
		for k, v := range resp.Header {
			for _, value := range v {
				ctx.Response().Header().Set(k, value)
			}
		}
		_, err = io.Copy(ctx.Response(), resp.Body)
		if err != nil {
			return err
		}
		return nil
	}
	var buf bytes.Buffer
	_, err = io.Copy(&buf, resp.Body)

	if err != nil {
		return err
	}
	return ctxresp.Out(ctx, buf.String())
}

func waitDscEventWithTimeOut(ctx context.Context, fun, msgId string) {
	didaTicker := time.NewTicker(time.Millisecond * 200)
	outTicker := time.NewTicker(time.Second * 5)
	defer didaTicker.Stop()
	defer outTicker.Stop()

	for {
		select {
		case <-didaTicker.C:
			if v, err := rds.NewRCache().Exists(ctx, fmt.Sprintf(keys.DiscordMsgHasGetKey, msgId)).Result(); err != nil {
				logger.Errorf(ctx, "%s redis exists err. msgId:%s. err:%v", fun, msgId, err)
				goto Finished
			} else if v == 1 { // has get
				goto Finished
			}
		case <-outTicker.C: // timeout
			logger.Warn(ctx, "wait dscEvent timeout", zap.String("fun", fun), zap.String("msgId", msgId))
			goto Finished
		}
	}
Finished:
}

// MessageTaskList 批量私信列表
func (h *dscHandler) MessageTaskList(ctx echo.Context) error {
	param := &pb.DiscordMessageTaskListReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	if len(param.Date) > 0 && len(param.Date) != 2 {
		return xerrors.New(errors.New("there must be two dates"), code.InvalidParams)
	}
	if len(param.Project) == 0 {
		if permList, _ := commsrv.LoadPermGmList(ctx.Request().Context(), ctx.Get(cst.AccountInfoCtx).(string)); len(permList) > 0 {
			param.Project = permList
		}
	}
	resp, err := services.MessageTaskList(ctx, param)
	if err != nil {
		return err
	}
	return ctxresp.Out(ctx, resp)
}

func (h *dscHandler) MessageTaskListExport(ctx echo.Context) error {
	param := &pb.DiscordMessageTaskListReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	if len(param.Date) > 0 && len(param.Date) != 2 {
		return xerrors.New("参数错误", code.InvalidParams)
	}
	if len(param.Project) == 0 {
		if permList, _ := commsrv.LoadPermGmList(ctx.Request().Context(), ctx.Get(cst.AccountInfoCtx).(string)); len(permList) > 0 {
			param.Project = permList
		}
	}
	fileName, err := services.MessageTaskListExport(ctx, param)
	if err != nil || fileName == "" {
		logger.Errorf(ctx.Request().Context(), "discord message task  record export file:%v err:%v", fileName, err)
		return xerrors.New(err, code.RpcRespErr)
	}
	defer func() {
		if err = os.Remove("./" + fileName); err != nil {
			logger.Errorf(ctx.Request().Context(), "discord message task record export remove file:%v err:%v", fileName, err)
		}
	}()
	ctx.Response().Header().Set("filename", fileName)
	return ctx.File(fileName)
}

func (h *dscHandler) MessageTaskDetailExport(ctx echo.Context) error {
	param := &pb.DiscordMessageTaskDetailExportReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	fileName, err := services.MessageTaskDetailExport(ctx, param)
	if err != nil || fileName == "" {
		logger.Errorf(ctx.Request().Context(), "discord message task  detail export file:%v err:%v", fileName, err)
		return xerrors.New(err, code.RpcRespErr)
	}
	defer func() {
		if err = os.Remove("./" + fileName); err != nil {
			logger.Errorf(ctx.Request().Context(), "discord message task detail export remove file:%v err:%v", fileName, err)
		}
	}()
	ctx.Response().Header().Set("filename", fileName)
	return ctx.File(fileName)
}
func (h *dscHandler) ReplyStatusRectify(ctx echo.Context) error {
	param := &pb.DiscordReplyStatusRectifyReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	if param.OldReplyStatus == param.NewReplyStatus {
		return xerrors.New("params error", code.InvalidParams)
	}
	if err := services.DiscordReplyStatusRectify(ctx, param); err != nil {
		return err
	}
	return ctxresp.Out(ctx, pb.Empty{})
}
