package inner

import (
	"github.com/labstack/echo/v4"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"ops-ticket-api/internal/code"
	"ops-ticket-api/internal/cst"
	"ops-ticket-api/internal/framework/ctxresp"
	"ops-ticket-api/internal/framework/validator"
	"ops-ticket-api/internal/framework/xerrors"
	"ops-ticket-api/proto/pb"
	"ops-ticket-api/services"
	"ops-ticket-api/services/commsrv"
	"os"
	"strings"
)

// LineCommuSave 创建line沟通记录
func LineCommuSave(ctx echo.Context) error {
	param := &pb.LineCommuRecordAddReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	if err := services.LineCommuSave(ctx, param); err != nil {
		return err
	}
	return ctxresp.Out(ctx, pb.Empty{})
}

// LineCommuEdit 编辑line沟通记录
func LineCommuEdit(ctx echo.Context) error {
	param := &pb.LineCommuRecordEditReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	if err := services.LineCommuEdit(ctx, param); err != nil {
		return err
	}
	return ctxresp.Out(ctx, pb.Empty{})
}

// LineCommuList line沟通记录列表
func LineCommuList(ctx echo.Context) error {
	param := &pb.LineCommuRecordListReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	if len(param.CommuDate) > 0 && len(param.CommuDate) != 2 {
		return xerrors.New("参数错误", code.InvalidParams)
	}
	if len(param.Project) == 0 {
		if permList, _ := commsrv.LoadPermGmList(ctx.Request().Context(), ctx.Get(cst.AccountInfoCtx).(string)); len(permList) > 0 {
			param.Project = strings.Join(permList, ",")
		}
	}
	resp, err := services.LineCommuList(ctx, param)
	if err != nil {
		return err
	}
	return ctxresp.Out(ctx, resp)
}

// LineCommuListExport line沟通记录导出
func LineCommuListExport(ctx echo.Context) error {
	param := &pb.LineCommuRecordListReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	if len(param.CommuDate) > 0 && len(param.CommuDate) != 2 {
		return xerrors.New("参数错误", code.InvalidParams)
	}
	if len(param.Project) == 0 {
		if permList, _ := commsrv.LoadPermGmList(ctx.Request().Context(), ctx.Get(cst.AccountInfoCtx).(string)); len(permList) > 0 {
			param.Project = strings.Join(permList, ",")
		}
	}
	fileName, err := services.LineCommuListExport(ctx, param)
	if err != nil || fileName == "" {
		logger.Errorf(ctx.Request().Context(), "line commu record export file:%v err:%v", fileName, err)
		return xerrors.New(err, code.RpcRespErr)
	}
	defer func() {
		if err = os.Remove("./" + fileName); err != nil {
			logger.Errorf(ctx.Request().Context(), "line commu record export remove file:%v err:%v", fileName, err)
		}
	}()
	ctx.Response().Header().Set("filename", fileName)
	return ctx.File(fileName)
}
