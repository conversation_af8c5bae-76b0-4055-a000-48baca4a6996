package inner

import (
	"github.com/avast/retry-go"
	"github.com/labstack/echo/v4"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"net/http"
	"ops-ticket-api/internal/code"
	"ops-ticket-api/internal/cst"
	"ops-ticket-api/internal/framework/ctxresp"
	"ops-ticket-api/internal/framework/validator"
	"ops-ticket-api/internal/framework/xerrors"
	"ops-ticket-api/proto/pb"
	"ops-ticket-api/services"
	"ops-ticket-api/services/commsrv"
	"ops-ticket-api/services/configure"
	"time"
)

// MaintainConfigList 玩家关系维护配置列表
func MaintainConfigList(ctx echo.Context) error {
	param := &pb.MaintainConfigListReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	if len(param.GameProject) == 0 {
		if permList, _ := commsrv.LoadPermGmList(ctx.Request().Context(), ctx.Get(cst.AccountInfoCtx).(string)); len(permList) > 0 {
			param.GameProject = permList
		}
	}
	resp, err := configure.MaintainConfigList(ctx, param)
	if err != nil {
		return err
	}
	return ctxresp.Out(ctx, resp)
}

func MaintainConfigListExport(ctx echo.Context) error {
	param := &pb.MaintainConfigListReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	records, err := configure.MaintainConfigListExport(ctx, param)
	if err != nil {
		logger.Error(ctx.Request().Context(), "export workstation file err", logger.Any("err", err))
		return ctxresp.Error(ctx, xerrors.New(err.Error(), code.Error))
	}

	fileName := "player_maintain_config_export_" + time.Now().Format("**************") + ".csv"
	ctx.Response().Header().Add(echo.HeaderContentType, "text/plain; charset=utf-8")
	ctx.Response().Header().Add("filename", fileName)
	ctx.Response().Header().Set(echo.HeaderContentDisposition, "attachment; filename="+fileName)
	ctx.Response().WriteHeader(http.StatusOK)

	for res := range records {
		ctx.Response().Write(res)
		ctx.Response().Flush()
	}
	return nil
}

// MaintainConfigSave 玩家关系维护配置添加
func MaintainConfigSave(ctx echo.Context) error {
	param := &pb.MaintainConfigNewReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	if err := configure.MaintainConfigSave(ctx, param); err != nil {
		return err
	}
	return ctxresp.Out(ctx, pb.Empty{})
}

// MaintainConfigEdit 玩家关系维护配置编辑
func MaintainConfigEdit(ctx echo.Context) error {
	param := &pb.MaintainConfigEditReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	if param.VipState > 0 && param.VipState != code.NonVip && param.VipState != code.Vip {
		return xerrors.New("参数错误", code.InvalidParams)
	}
	player, err := services.NewDscSrv().DiscordPlayer(ctx, param.Fpid, param.GameProject, param.Uid)
	if err != nil {
		return err
	}
	if err := configure.MaintainConfigEdit(ctx, param, player); err != nil {
		return err
	}
	return ctxresp.Out(ctx, pb.Empty{})
}

// MaintainConfigDel 玩家关系维护配置删除
func MaintainConfigDel(ctx echo.Context) error {
	param := &pb.MaintainConfigDelReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	users, err := configure.MaintainConfigDel(ctx, param)
	if err != nil {
		return err
	}
	// 将该玩家从服务器剔除
	for _, user := range users {
		e := retry.Do(func() error {
			if _innErr := services.NewDscSrv().DelGuildMember(ctx.Request().Context(), user.AppID, user.GuildID, param.DscUserId); _innErr != nil {
				logger.Errorf(ctx.Request().Context(), "DelGuildMember-> err.", logger.Any("err", _innErr), logger.Any("param", param))
				return _innErr
			}
			return nil
		}, retry.Attempts(3), retry.Delay(200*time.Millisecond), retry.LastErrorOnly(true))
		if e != nil {
			logger.Errorf(ctx.Request().Context(), "DelGuildMember->retry err:%v. param:%+v", e, param)
			return err
		}
	}
	return ctxresp.Out(ctx, pb.Empty{})
}

func DiscordPlayerAccounts(ctx echo.Context) error {
	param := &pb.DiscordPlayerAccountsReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	resp, err := configure.DiscordPlayerAccounts(ctx, param)
	if err != nil {
		return err
	}
	return ctxresp.Out(ctx, resp)
}

func DiscordPlayerUidList(ctx echo.Context) error {
	param := &pb.DiscordPlayerUidReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	resp, err := configure.DiscordPlayerUidList(ctx, param)
	if err != nil {
		return err
	}
	return ctxresp.Out(ctx, resp)
}
