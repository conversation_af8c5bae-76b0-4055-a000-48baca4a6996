// Copyright 2024 funplus Authors. All Rights Reserved.
// @Description: discord沟通记录
// @Author: jun.qiu
// @Date: 2024/08/19 11:54 AM

package inner

import (
	"github.com/labstack/echo/v4"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"ops-ticket-api/internal/code"
	"ops-ticket-api/internal/cst"
	"ops-ticket-api/internal/framework/ctxresp"
	"ops-ticket-api/internal/framework/validator"
	"ops-ticket-api/internal/framework/xerrors"
	"ops-ticket-api/proto/pb"
	"ops-ticket-api/services"
	"ops-ticket-api/services/commsrv"
	"os"
)

// DiscordCommuSave 创建discord沟通记录
func DiscordCommuSave(ctx echo.Context) error {
	param := &pb.DiscordCommuRecordAddReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	if err := services.DiscordCommuSave(ctx, param); err != nil {
		return err
	}
	return ctxresp.Out(ctx, pb.Empty{})
}

// DiscordCommuEdit 编辑discord沟通记录
func DiscordCommuEdit(ctx echo.Context) error {
	param := &pb.DiscordCommuRecordEditReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	if err := services.DiscordCommuEdit(ctx, param); err != nil {
		return err
	}
	return ctxresp.Out(ctx, pb.Empty{})
}

// DiscordCommuList discord沟通记录列表
func DiscordCommuList(ctx echo.Context) error {
	param := &pb.DiscordCommuRecordListReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	if len(param.CommuDate) > 0 && len(param.CommuDate) != 2 {
		return xerrors.New("参数错误", code.InvalidParams)
	}
	if len(param.Project) == 0 {
		if permList, _ := commsrv.LoadPermGmList(ctx.Request().Context(), ctx.Get(cst.AccountInfoCtx).(string)); len(permList) > 0 {
			param.Project = permList
		}
	}
	resp, err := services.DiscordCommuList(ctx, param)
	if err != nil {
		return err
	}
	return ctxresp.Out(ctx, resp)
}

// DiscordCommuListExport discord沟通记录导出
func DiscordCommuListExport(ctx echo.Context) error {
	param := &pb.DiscordCommuRecordListReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	if len(param.CommuDate) > 0 && len(param.CommuDate) != 2 {
		return xerrors.New("参数错误", code.InvalidParams)
	}
	if len(param.Project) == 0 {
		if permList, _ := commsrv.LoadPermGmList(ctx.Request().Context(), ctx.Get(cst.AccountInfoCtx).(string)); len(permList) > 0 {
			param.Project = permList
		}
	}
	fileName, err := services.DiscordCommuListExport(ctx, param)
	if err != nil || fileName == "" {
		logger.Errorf(ctx.Request().Context(), "discord commu record export file:%v err:%v", fileName, err)
		return xerrors.New(err, code.RpcRespErr)
	}
	defer func() {
		if err = os.Remove("./" + fileName); err != nil {
			logger.Errorf(ctx.Request().Context(), "discord commu record export remove file:%v err:%v", fileName, err)
		}
	}()
	ctx.Response().Header().Set("filename", fileName)
	return ctx.File(fileName)
}
