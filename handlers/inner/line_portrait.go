package inner

import (
	"github.com/labstack/echo/v4"
	"ops-ticket-api/internal/framework/ctxresp"
	"ops-ticket-api/internal/framework/validator"
	"ops-ticket-api/proto/pb"
	"ops-ticket-api/services/configure"
)

// LinePortraitInfo 玩家画像信息
func LinePortraitInfo(ctx echo.Context) error {
	param := &pb.LinePortraitInfoReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	resp, err := configure.LinePortraitInfo(ctx, param)
	if err != nil {
		return err
	}
	return ctxresp.Out(ctx, resp)
}

func LinePortraitEditTag(ctx echo.Context) error {
	param := &pb.LinePortraitEditTagReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	err := configure.LinePortraitEditTag(ctx, param)
	if err != nil {
		return err
	}
	return ctxresp.Out(ctx, pb.Empty{})
}

func LinePortraitEditBasic(ctx echo.Context) error {
	param := &pb.LinePortraitEditBasicReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	err := configure.LinePortraitEditBasic(ctx, param)
	if err != nil {
		return err
	}
	return ctxresp.Out(ctx, pb.Empty{})
}

func LinePortraitEditRemark(ctx echo.Context) error {
	param := &pb.LinePortraitEditRemarkReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	err := configure.LinePortraitEditRemark(ctx, param)
	if err != nil {
		return err
	}
	return ctxresp.Out(ctx, pb.Empty{})
}
