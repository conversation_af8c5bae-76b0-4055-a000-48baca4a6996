// Copyright 2021 funplus Authors. All Rights Reserved.
// @Description: C端接口
// @Author: Darcy
// @Date: 2021/11/17 5:18 PM

package egress

import (
	"errors"
	"fmt"
	"strings"
	"time"

	"ops-ticket-api/internal/code"
	"ops-ticket-api/internal/cst"
	"ops-ticket-api/internal/framework/cache/local"
	"ops-ticket-api/internal/framework/ctxresp"
	"ops-ticket-api/internal/framework/lang"
	"ops-ticket-api/internal/framework/validator"
	"ops-ticket-api/internal/framework/xerrors"
	"ops-ticket-api/models"
	"ops-ticket-api/pkg/game"
	"ops-ticket-api/proto/pb"
	"ops-ticket-api/services"
	"ops-ticket-api/services/communicate"
	"ops-ticket-api/utils"

	"github.com/labstack/echo/v4"
	"github.com/spf13/cast"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"go.uber.org/zap"
)

func NewTicketHandle() *ticketHandle {
	return &ticketHandle{}
}

type ticketHandle struct{}

// TicketDetail C端 - 工单详情
func (h *ticketHandle) TicketDetail(ctx echo.Context) error {
	param := &pb.TkDetailReq{}
	if err := validator.BindValidateCs(ctx, param); err != nil {
		return err
	}
	if param.Lang == "zh" {
		param.Lang = "zh-cn"
	}
	resp, err := services.NewTicketSrv().TicketDetail(ctx, param)
	if err != nil {
		logger.Error(ctx.Request().Context(), "new get ticket detail info err",
			zap.Uint64("ticket_id", param.TicketId), zap.String("language", param.Lang),
			zap.String("fpx_app_id", param.FpxAppId), zap.String("account_id", param.AccountId),
			zap.String("err", err.Error()))
		var xerr xerrors.Error
		if errors.As(err, &xerr) && xerr.Code() == code.InvalidParams {
			return err
		}
		return xerrors.New(lang.FormatText(ctx, "RespInternalServer"), xerr.Code())
	}
	return ctxresp.Out(ctx, resp)
}

// TicketMine C端 - 工单历史列表
func (h *ticketHandle) TicketMine(ctx echo.Context) error {
	param := &pb.TkMineReq{}
	if err := validator.BindValidateCs(ctx, param); err != nil {
		return err
	}
	project, err := game.NewGameInfo().GetGameProjectById(param.FpxAppId)
	if err != nil {
		return ctxresp.Error(ctx, xerrors.New(lang.FormatText(ctx, "RespInvalidParams"), code.DbError))
	}
	if param.Lang == "zh" {
		param.Lang = "zh-cn"
	}
	resp, err := services.NewTicketSrv().TicketMine(ctx, project, param)
	if err != nil {
		logger.Errorf(ctx.Request().Context(), "TicketMine return err. err:%v", err)
		return err
	}
	return ctxresp.Out(ctx, resp)
}

// TicketCreate C端 - 工单创建
func (h *ticketHandle) TicketCreate(ctx echo.Context) error {
	param := &pb.TkCreateReq{}
	if err := validator.BindValidateCs(ctx, param); err != nil {
		return err
	}
	project, err := game.NewGameInfo().GetGameProjectById(param.FpxAppId)
	if err != nil {
		return ctxresp.Error(ctx, xerrors.New(lang.FormatText(ctx, "RespInvalidParams"), code.DbError))
	}
	result := pb.TkCreateResp{TicketSysType: pb.TicketSys_ticketSysNew}
	defer func() {
		fmt.Println("ticket_id:", result.TicketId)
	}()
	// 从请求头中获取玩家的IP地址
	ip := ""
	ips := ctx.Request().Header.Get("X-Forwarded-For")
	ipList := strings.Split(ips, ",")
	if len(ipList) > 0 {
		ip = ipList[0]
	}
	// 针对L，roleid赋值到uid上
	if project == "pk.global.prod" {
		if param.Uid == 0 && param.RoleId != "" {
			param.Uid = cast.ToUint64(param.RoleId)
		}
	}

	// 私域单 回填 玩家昵称 和 server id
	if param.ZoneFrom != "" && param.ZoneToken != "" {
		privateZoneInfo := fmt.Sprintf("%v_%v_%v", param.Uid, param.AccountId, param.ZoneToken)
		exist, _name, _server, _ := local.PrivateZoneCache.FetchPrivateZoneC(privateZoneInfo)
		if exist {
			logger.Infof(ctx.Request().Context(), "hit local privateZone cache:%v", privateZoneInfo)
			if param.Nickname == "" && _name != "" {
				param.Nickname = _name
			}
			if (param.Sid == "" || param.Sid == "0") && _server != 0 {
				param.Sid = cast.ToString(_server)
			}
		}
	}

	ticketCrtParam := models.TicketCreateParam{Input: *param, Ip: ip}
	ticketId, useNewDb, err := services.NewTicketSrv().TicketCreate(ctx, project, &ticketCrtParam)
	if err != nil {
		if ip == "***********" { // 俄罗斯一个异常用户的 ip
			logger.Infof(ctx.Request().Context(), "TicketCreate return err. err:%v", err)
			return err
		}
		logger.Errorf(ctx.Request().Context(), "TicketCreate return err. err:%v", err)
		return err
	}
	result.TicketId = ticketId

	cc := utils.ContextOnlyValue{ctx.Request().Context()}
	newC := ctx.Echo().AcquireContext()
	newC.SetRequest(ctx.Request().WithContext(cc))
	newC.Set(cst.AccountInfoCtx, pb.UserRole_SystemRole.String())

	// 订单成功推送 pubsub 通知
	go func(c echo.Context) {
		communicate.PubTicketCreate(c, ticketId, param)
	}(newC)

	// 自动回复
	replyTplId := cast.ToUint32(ctx.Get(cst.TicketReplyTplCtx))
	if replyTplId == 0 {
		return ctxresp.Out(ctx, result)
	}

	replyTm := time.NewTimer(time.Minute)
	go func(c echo.Context) {
		// 设置当前用户为系统用户
		<-replyTm.C
		services.SystemReply(c, ticketId, param.CatId, replyTplId, param.Lang, useNewDb)
	}(newC)
	return ctxresp.Out(ctx, result)
}

func (h *ticketHandle) TicketReplenish(ctx echo.Context) error {
	param := &pb.TkReplenishReq{}
	if err := validator.BindValidateCs(ctx, param); err != nil {
		return err
	}
	if err := services.NewTicketSrv().TicketReplenish(ctx, param); err != nil {
		logger.Error(ctx.Request().Context(), "replenish ticket err", zap.Uint64("ticketId", param.TicketId), zap.String("err", err.Error()))
		return err
	}
	return ctxresp.Out(ctx, &pb.TkUpResp{TicketId: param.GetTicketId(), TicketSysType: pb.TicketSys_ticketSysNew})
}

func (h *ticketHandle) TicketReopen(ctx echo.Context) error {
	param := &pb.TkReplenishReq{}
	if err := validator.BindValidateCs(ctx, param); err != nil {
		return err
	}
	if err := services.NewTicketSrv().TicketReopen(ctx, param); err != nil {
		logger.Error(ctx.Request().Context(), "TicketReopen ticket err", zap.Uint64("ticketId", param.TicketId), zap.String("err", err.Error()))
		return err
	}
	return ctxresp.Out(ctx, &pb.TkUpResp{TicketId: param.GetTicketId(), TicketSysType: pb.TicketSys_ticketSysNew})
}

func (h *ticketHandle) TicketAppraise(ctx echo.Context) error {
	param := &pb.TkAppraiseReq{}
	if err := validator.BindValidateCs(ctx, param); err != nil {
		return err
	}
	if param.RecommendationLevel == 0 && param.ServiceRating > 0 { // 兼容老客服评价页 - 评价
		param.RecommendationLevel = param.ServiceRating
	}
	if err := services.NewTicketSrv().TkAppraise(ctx, param); err != nil {
		logger.Error(ctx.Request().Context(), "TkAppraise ticket err", zap.Uint64("ticketId", param.TicketId), zap.String("app_id", param.FpxAppId), zap.String("err", err.Error()))
		return err
	}
	return ctxresp.Out(ctx, pb.TkUpResp{TicketId: param.GetTicketId(), TicketSysType: pb.TicketSys_ticketSysNew})
}

func (h *ticketHandle) TkAppraiseFeedback(ctx echo.Context) error {
	param := &pb.TkAppraiseFeedbackReq{}
	if err := validator.BindValidateCs(ctx, param); err != nil {
		logger.Error(ctx.Request().Context(), "TkAppraiseFeedback ticket err", zap.Uint64("ticketId", param.TicketId), zap.String("app_id", param.FpxAppId), zap.String("err", err.Error()))
		return err
	}
	if err := services.NewTicketSrv().TkAppraiseFeedback(ctx, param); err != nil {
		logger.Error(ctx.Request().Context(), "TkAppraiseFeedback ticket err", zap.Uint64("ticketId", param.TicketId), zap.String("app_id", param.FpxAppId), zap.String("err", err.Error()))
		return err
	}
	return ctxresp.Out(ctx, pb.TkUpResp{TicketId: param.GetTicketId(), TicketSysType: pb.TicketSys_ticketSysNew})
}

// TkCommunicate 玩家端发送消息
func (h *ticketHandle) TkCommunicate(ctx echo.Context) error {
	param := &pb.TkCommunicateReq{}
	if err := validator.BindValidateCs(ctx, param); err != nil {
		logger.Error(ctx.Request().Context(), "TkCommunicate ticket err", zap.Uint64("ticketId", param.TicketId), zap.String("app_id", param.FpxAppId), zap.String("err", err.Error()))
		return err
	}
	if err := services.NewTicketSrv().TkCommunicate(ctx, param); err != nil {
		logger.Warn(ctx.Request().Context(), "TkCommunicate ticket err", zap.Uint64("ticketId", param.TicketId), zap.String("app_id", param.FpxAppId), zap.String("err", err.Error()))
		return err
	}
	return ctxresp.Out(ctx, pb.TkUpResp{TicketId: param.GetTicketId(), TicketSysType: pb.TicketSys_ticketSysNew})
}

func (h *ticketHandle) TicketCreateV2(ctx echo.Context) error {
	param := &pb.TkCreateReq{}
	if err := validator.BindValidateCs(ctx, param); err != nil {
		return err
	}
	project, err := game.NewGameInfo().GetGameProjectById(param.FpxAppId)
	if err != nil {
		return ctxresp.Error(ctx, xerrors.New(lang.FormatText(ctx, "RespInvalidParams"), code.DbError))
	}
	result := pb.TkCreateResp{TicketSysType: pb.TicketSys_ticketSysNew}
	defer func() {
		fmt.Println("ticket_id:", result.TicketId)
	}()
	// 从请求头中获取玩家的IP地址
	ip := ""
	ips := ctx.Request().Header.Get("X-Forwarded-For")
	ipList := strings.Split(ips, ",")
	if len(ipList) > 0 {
		ip = ipList[0]
	}
	// 私域单 回填 玩家昵称 和 server id
	if param.ZoneFrom != "" && param.ZoneToken != "" {
		privateZoneInfo := fmt.Sprintf("%v_%v_%v", param.Uid, param.AccountId, param.ZoneToken)
		exist, _name, _server, _ := local.PrivateZoneCache.FetchPrivateZoneC(privateZoneInfo)
		if exist {
			logger.Infof(ctx.Request().Context(), "hit local privateZone cache:%v", privateZoneInfo)
			if param.Nickname == "" && _name != "" {
				param.Nickname = _name
			}
			if (param.Sid == "" || param.Sid == "0") && _server != 0 {
				param.Sid = cast.ToString(_server)
			}
		}
	}

	ticketCrtParam := models.TicketCreateParam{Input: *param, Ip: ip}
	ticketId, useNewDb, err := services.NewTicketSrv().TicketCreate(ctx, project, &ticketCrtParam)
	if err != nil {
		if ip == "***********" { // 俄罗斯一个异常用户的 ip
			logger.Infof(ctx.Request().Context(), "TicketCreate return err. err:%v", err)
			return err
		}
		logger.Errorf(ctx.Request().Context(), "TicketCreate return err. err:%v", err)
		return err
	}
	result.TicketId = ticketId

	cc := utils.ContextOnlyValue{ctx.Request().Context()}
	newC := ctx.Echo().AcquireContext()
	newC.SetRequest(ctx.Request().WithContext(cc))
	newC.Set(cst.AccountInfoCtx, pb.UserRole_SystemRole.String())

	// 订单成功推送 pubsub 通知
	go func(c echo.Context) {
		communicate.PubTicketCreate(c, ticketId, param)
	}(newC)

	// 自动回复
	replyTplId := cast.ToUint32(ctx.Get(cst.TicketReplyTplCtx))
	if replyTplId == 0 {
		return ctxresp.Out(ctx, result)
	}

	replyTm := time.NewTimer(time.Minute)
	go func(c echo.Context) {
		// 设置当前用户为系统用户
		<-replyTm.C
		services.SystemReply(c, ticketId, param.CatId, replyTplId, param.Lang, useNewDb)
	}(newC)
	return ctxresp.Out(ctx, result)
}
