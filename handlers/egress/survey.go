package egress

import (
	"github.com/labstack/echo/v4"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"ops-ticket-api/internal/code"
	"ops-ticket-api/internal/framework/ctxresp"
	"ops-ticket-api/internal/framework/validator"
	"ops-ticket-api/internal/framework/xerrors"
	"ops-ticket-api/proto/pb"
	"ops-ticket-api/services/survey"
)

func DcSurveyConfig(ctx echo.Context) error {
	req := &pb.Empty{}
	if err := validator.BindValidateCs(ctx, req); err != nil {
		return xerrors.New(code.StatusText(code.InvalidParams), code.InvalidParams)
	}

	resp, err := survey.NewSurveySrv().SurveyEgressConfig(ctx, req)
	if err != nil {
		logger.Errorf(ctx.Request().Context(), "SurveyEgressConfig error: %v. req:%+v.", err, req)
		return xerrors.New(code.StatusText(code.DbError), code.DbError)
	}
	return ctxresp.Out(ctx, resp)
}

// DcSurveySubmit Dc提交问卷调查
func DcSurveySubmit(ctx echo.Context) error {
	req := &pb.SurveySubmitReq{}
	if err := validator.BindValidateCs(ctx, req); err != nil {
		return xerrors.New(code.StatusText(code.InvalidParams), code.InvalidParams)
	}

	resp, err := survey.NewSurveySrv().SubmitDcSurvey(ctx, req)
	if err != nil {
		return err
	}
	return ctxresp.Out(ctx, resp)
}

// DcSurveyTemplate Dc问卷调查获取模版
func DcSurveyTemplate(ctx echo.Context) error {
	req := &pb.SurveyTemplateReq{}
	if err := validator.BindValidateCs(ctx, req); err != nil {
		return xerrors.New(code.StatusText(code.InvalidParams), code.InvalidParams)
	}

	if req.Lang == "" {
		req.Lang = "en"
	}
	resp, err := survey.NewSurveySrv().TemplateDcSurvey(ctx, req)
	if err != nil {
		return ctxresp.ErrorData(ctx, err, resp)
	}
	return ctxresp.Out(ctx, resp)
}
