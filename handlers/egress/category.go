package egress

import (
	"ops-ticket-api/internal/code"
	"ops-ticket-api/internal/framework/ctxresp"
	"ops-ticket-api/internal/framework/lang"
	"ops-ticket-api/internal/framework/validator"
	"ops-ticket-api/internal/framework/xerrors"
	"ops-ticket-api/pkg/filter"
	"ops-ticket-api/pkg/game"
	"ops-ticket-api/proto/pb"
	"ops-ticket-api/services"

	"github.com/labstack/echo/v4"
	"github.com/spf13/cast"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
)

// CategoryInfo 三级分类查询接口
func CategoryInfo(ctx echo.Context) error {
	param := &pb.CatSubRequest{}
	if err := validator.BindValidateCs(ctx, param); err != nil {
		return err
	}
	if param.CountryCode == "" {
		param.CountryCode = filter.NewJsonDataFilter(param.JsonData).GetCountryCode()
	}
	project, err := game.NewGameInfo().GetGameProjectByMix(cast.ToInt(param.GameId), param.FpxAppId)
	if err != nil {
		if ctx.Echo().Debug {
			return err
		}
		return ctxresp.Error(ctx, xerrors.New(lang.FormatText(ctx, "RespInvalidParams"), code.InvalidParams))
	}
	if param.Lang == "zh" {
		param.Lang = "zh-cn"
	}

	resp, err := services.DefaultEgressSvc.CatInfo(ctx, project, param)
	if err != nil {
		if ctx.Echo().Debug {
			return err
		}
		logger.Error(ctx.Request().Context(), "get egress category info err",
			logger.String("project", project),
			logger.String("lang", param.Lang),
			logger.Uint32("catId", param.CatId),
			logger.String("err", err.Error()),
		)
		return ctxresp.Error(ctx, xerrors.New(lang.FormatText(ctx, "RespInternalServer"), code.DbError))
	}
	return ctxresp.Out(ctx, resp)
}
