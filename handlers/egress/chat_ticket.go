// Copyright 2021 funplus Authors. All Rights Reserved.
// @Author: <PERSON><PERSON>
// @Date: 2025/3/14 23:56

package egress

import (
	"fmt"
	"strings"
	"time"

	"ops-ticket-api/internal/code"
	"ops-ticket-api/internal/cst"
	"ops-ticket-api/internal/framework/cache/local"
	"ops-ticket-api/internal/framework/ctxresp"
	"ops-ticket-api/internal/framework/lang"
	"ops-ticket-api/internal/framework/validator"
	"ops-ticket-api/internal/framework/xerrors"
	"ops-ticket-api/models"
	"ops-ticket-api/pkg/game"
	"ops-ticket-api/proto/pb"
	"ops-ticket-api/services"
	"ops-ticket-api/services/communicate"
	"ops-ticket-api/utils"

	"github.com/labstack/echo/v4"
	"github.com/spf13/cast"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
)

func TicketCreateWithChat(ctx echo.Context) error {
	param := &pb.TkCreateReq{}
	if err := validator.BindValidateCs(ctx, param); err != nil {
		return err
	}
	// 获取游戏项目
	project, err := game.NewGameInfo().GetGameProjectById(param.FpxAppId)
	if err != nil {
		return ctxresp.Error(ctx, xerrors.New(lang.FormatText(ctx, "RespInvalidParams"), code.DbError))
	}
	result := pb.TkCreateResp{TicketSysType: pb.TicketSys_ticketSysNew}
	defer func() {
		fmt.Println("ticket_id:", result.TicketId)
	}()
	// 从请求头中获取玩家的IP地址
	ip := ""
	ips := ctx.Request().Header.Get("X-Forwarded-For")
	ipList := strings.Split(ips, ",")
	if len(ipList) > 0 {
		ip = ipList[0]
	}
	// 私域单 回填 玩家昵称 和 server id
	if param.ZoneFrom != "" && param.ZoneToken != "" {
		privateZoneInfo := fmt.Sprintf("%v_%v_%v", param.Uid, param.AccountId, param.ZoneToken)
		exist, _name, _server, _ := local.PrivateZoneCache.FetchPrivateZoneC(privateZoneInfo)
		if exist {
			logger.Infof(ctx.Request().Context(), "hit local privateZone cache:%v", privateZoneInfo)
			if param.Nickname == "" && _name != "" {
				param.Nickname = _name
			}
			if (param.Sid == "" || param.Sid == "0") && _server != 0 {
				param.Sid = cast.ToString(_server)
			}
		}
	}

	ticketCrtParam := models.TicketCreateParam{Input: *param, Ip: ip}
	ticketId, useNewDb, err := services.NewTicketSrv().TicketCreate(ctx, project, &ticketCrtParam)
	if err != nil {
		if ip == "***********" { // 俄罗斯一个异常用户的 ip
			logger.Infof(ctx.Request().Context(), "TicketCreate return err. err:%v", err)
			return err
		}
		logger.Errorf(ctx.Request().Context(), "TicketCreate return err. err:%v", err)
		return err
	}
	result.TicketId = ticketId

	cc := utils.ContextOnlyValue{ctx.Request().Context()}
	newC := ctx.Echo().AcquireContext()
	newC.SetRequest(ctx.Request().WithContext(cc))
	newC.Set(cst.AccountInfoCtx, pb.UserRole_SystemRole.String())

	// 订单成功推送 pubsub 通知
	go func(c echo.Context) {
		communicate.PubTicketCreate(c, ticketId, param)
	}(newC)

	// 自动回复
	replyTplId := cast.ToUint32(ctx.Get(cst.TicketReplyTplCtx))
	if replyTplId == 0 {
		return ctxresp.Out(ctx, result)
	}

	replyTm := time.NewTimer(time.Minute)
	go func(c echo.Context) {
		// 设置当前用户为系统用户
		<-replyTm.C
		services.SystemReply(c, ticketId, param.CatId, replyTplId, param.Lang, useNewDb)
	}(newC)
	return ctxresp.Out(ctx, result)
}
