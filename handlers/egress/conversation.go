package egress

import (
	"ops-ticket-api/internal/code"
	"ops-ticket-api/internal/framework/ctxresp"
	"ops-ticket-api/internal/framework/lang"
	"ops-ticket-api/internal/framework/validator"
	"ops-ticket-api/internal/framework/xerrors"
	"ops-ticket-api/pkg/game"
	"ops-ticket-api/proto/pb"
	"ops-ticket-api/services"
	"ops-ticket-api/services/csctx"

	"github.com/labstack/echo/v4"
	"github.com/spf13/cast"
)

// UpdateConversation 创建或更新对话
func UpdateConversation(ctx echo.Context) error {
	req := &pb.UpdateConversationRequest{}
	if err := validator.BindValidateCs(ctx, req); err != nil {
		return err
	}

	project := csctx.GetGameProject(ctx.Request().Context())

	account := csctx.GetFPIDAccountID(ctx.Request().Context())

	if account == "" || project == "" {
		return ctxresp.Error(ctx, xerrors.New(lang.FormatText(ctx, "RespInvalidParams"), code.InvalidParams))
	}
	role := csctx.GetUidRoleID(ctx.Request().Context())

	resp, err := services.GetConversationService().UpdateConversation(ctx.Request().Context(), req, project, account, role)
	if err != nil {
		return err
	}

	return ctxresp.Out(ctx, resp)
}

// GetConversationHistory 获取对话历史 todo 轮询兜底请求
func GetConversationHistory(ctx echo.Context) error {
	req := &pb.GetHistoryRequest{}
	if err := validator.BindValidateCs(ctx, req); err != nil {
		return err
	}
	project, err := game.NewGameInfo().GetGameProjectByMix(cast.ToInt(req.GameId), req.FpxAppId)
	if err != nil {
		if ctx.Echo().Debug {
			return err
		}
		return xerrors.New(code.StatusText(code.InvalidParams), code.InvalidParams)
	}

	if req.GetLang() == "zh" {
		req.Lang = "zh-cn"
	}
	resp, err := services.GetConversationService().GetConversationHistory(ctx.Request().Context(), req, project)
	if err != nil {
		return err
	}
	return ctxresp.Out(ctx, resp)
}

// HandleChat 处理对话
func HandleChat(ctx echo.Context) error {
	req := &pb.ChatRequest{}
	if err := validator.BindValidateCs(ctx, req); err != nil {
		return err
	}

	if len(req.GetQuestionList()) == 0 {
		return xerrors.New(code.StatusText(code.InvalidParams), code.InvalidParams)
	}

	resp, err := services.GetConversationService().HandleChat(ctx.Request().Context(), req)
	if err != nil {
		return err
	}

	return ctxresp.Out(ctx, resp)
}

// GetUnFinishConversation 获取对话列表
func GetUnFinishConversation(ctx echo.Context) error {
	project := csctx.GetGameProject(ctx.Request().Context())

	account := csctx.GetFPIDAccountID(ctx.Request().Context())

	if account == "" || project == "" {
		return ctxresp.Error(ctx, xerrors.New(lang.FormatText(ctx, "RespInvalidParams"), code.InvalidParams))
	}

	resp, err := services.GetConversationService().GetUnFinishConversion(ctx.Request().Context(), project, account)
	if err != nil {
		return xerrors.New(code.StatusText(code.InvalidParams), code.InvalidParams)
	}
	return ctxresp.Out(ctx, resp)
}

// CloseConversation 关闭对话
func CloseConversation(ctx echo.Context) error {
	req := &pb.CloseChatReq{}
	if err := validator.BindValidateCs(ctx, req); err != nil {
		return err
	}

	project := csctx.GetGameProject(ctx.Request().Context())

	account := csctx.GetFPIDAccountID(ctx.Request().Context())

	if account == "" || project == "" {
		return ctxresp.Error(ctx, xerrors.New(lang.FormatText(ctx, "RespInvalidParams"), code.InvalidParams))
	}
	err := services.GetConversationService().CloseConversation(ctx.Request().Context(), req.ConversationId)
	if err != nil {
		return xerrors.New(code.StatusText(code.InvalidParams), code.InvalidParams)
	}

	return ctxresp.Out(ctx, pb.Empty{})
}
