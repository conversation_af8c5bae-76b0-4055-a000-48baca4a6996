package egress

import (
	"fmt"
	"github.com/labstack/echo/v4"
	"github.com/spf13/cast"
	"github.com/spf13/viper"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"gorm.io/gorm"
	"net/http"
	"ops-ticket-api/internal/code"
	"ops-ticket-api/internal/framework/cache/rds"
	"ops-ticket-api/internal/framework/ctxresp"
	"ops-ticket-api/internal/framework/database"
	"ops-ticket-api/internal/framework/validator"
	"ops-ticket-api/internal/framework/xerrors"
	"ops-ticket-api/proto/pb"
	"ops-ticket-api/services"
	"ops-ticket-api/utils"
)

func NoticeV2(ctx echo.Context) error {
	param := &pb.NoticeRequestV2{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	var nReq = &pb.NoticeFpxRequestV3{
		FpxAppId:  cast.ToString(param.GameId),
		DeviceId:  param.DeviceId,
		AccountId: cast.ToString(param.Fpid),
		Scene:     param.Scene,
	}
	notice, err := services.NewTicketSrv().NoticeV3(ctx, nReq)
	if err != nil {
		return ctxresp.ErrorData(ctx, err, notice)
	}
	return ctxresp.Out(ctx, notice)
}

// NoticeWeb web端消息通知
func NoticeWeb(ctx echo.Context) error {
	req := &pb.BaseInfo{}
	if err := validator.BindValidateCs(ctx, req); err != nil {
		return err
	}

	var nReq = &pb.NoticeFpxRequestV3{
		FpxAppId:  req.FpxAppId,
		DeviceId:  req.Uuid,
		AccountId: req.AccountId,
		Scene:     req.Scene,
	}

	notice, err := services.NewTicketSrv().NoticeV3(ctx, nReq)
	if err != nil {
		return ctxresp.ErrorData(ctx, err, notice)
	}
	return ctxresp.Out(ctx, notice)
}

// NoticeFpx c端消息通知
// FpxAppId、AccountId、SID、RoleId维度
func NoticeFpx(ctx echo.Context) error {
	param := &pb.NoticeFpxRequestV3{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}

	notice, err := services.NewTicketSrv().NoticeV3(ctx, param)
	if err != nil {
		return ctxresp.ErrorData(ctx, err, notice)
	}
	return ctxresp.Out(ctx, notice)
}

// NoticeInner 服务端内部调用 - server 2 server
func NoticeInner(ctx echo.Context) error {
	fun := "NoticeInner -->"
	var param = &pb.InnerNoticeRequest{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}

	if !utils.InArrayAny(param.SourceAppId, viper.GetStringSlice("inner_notice_source_app_key")) {
		logger.Errorf(ctx.Request().Context(), "%s source_app_id unknown. source_app_id:%s. fpx_app_id:%s", fun, param.SourceAppId, param.FpxAppId)
		return ctxresp.ErrorData(ctx, xerrors.New("source_app_id unknown", code.InvalidParams), pb.Empty{})
	}

	var nReq = &pb.NoticeFpxRequestV3{
		FpxAppId:  param.FpxAppId,
		DeviceId:  param.DeviceId,
		AccountId: param.AccountId,
		Scene:     param.Scene,
	}
	notice, _ := services.NewTicketSrv().NoticeV3(ctx, nReq)

	// 返回数据
	var resp = &pb.InnerNoticeResp{HasUnread: false}
	if notice != nil && notice.ObjectId > 0 {
		resp.HasUnread = true
		resp.NoticeId = notice.NoticeId
		resp.ObjectId = notice.ObjectId
		resp.Scene = notice.Scene
	}
	return ctxresp.Out(ctx, resp)
}

// HealthCheck front 接口健康检查
func HealthCheck(ctx echo.Context) error {
	// health check - db
	for k, _db := range map[string]*gorm.DB{
		"oldTicketDbClient": database.GetOldTicketDb(),
		"newTicketDbClient": database.Db(),
	} {
		if _db == nil {
			return ctx.JSON(http.StatusBadRequest, echo.Map{
				"code": http.StatusBadRequest,
				"msg":  fmt.Sprintf("db %s is nil", k)})
		}
		if err := _db.WithContext(ctx.Request().Context()).Exec("select 1").Error; err != nil {
			return ctx.JSON(http.StatusBadRequest, echo.Map{
				"code": http.StatusBadRequest,
				"msg":  fmt.Sprintf("db %s exec `select 1` return err. %v", k, err)})
		}
	}

	// health check rds
	if _, err := rds.NewRCache().Ping(ctx.Request().Context()).Result(); err != nil {
		return ctx.JSON(http.StatusBadRequest, echo.Map{
			"code": http.StatusBadRequest,
			"msg":  fmt.Sprintf("redis exec `ping` return err. %v", err)})
	}

	// success
	return ctxresp.Out(ctx, "health check success")
}
