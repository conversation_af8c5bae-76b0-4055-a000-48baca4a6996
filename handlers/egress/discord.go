package egress

import (
	"bytes"
	"encoding/base64"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"

	"ops-ticket-api/internal/persistence"
	"ops-ticket-api/models"
	"ops-ticket-api/pkg/game"
	"ops-ticket-api/utils"

	"github.com/bwmarrin/discordgo"
	"github.com/labstack/echo/v4"
	"github.com/spf13/cast"
	"github.com/tidwall/gjson"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
)

//func init() {
//	go func() {
//		time.Sleep(time.Second * 5)
//		body := "body"
//		body = `{"app_permissions":"180224","application_id":"1250292748364419154","authorizing_integration_owners":{},"entitlements":[],"id":"1250393837910626408","token":"aW50ZXJhY3Rpb246MTI1MDM5MzgzNzkxMDYyNjQwODp3Q0hzMzJ6ems0SmZYZnFnSFoydjA1bFluUHhHYVNjbTNxOHlrNklQdVRTQTl5aFFncm1OR2lyYk1KVUlXWEZEbkdqY3QwUTBMUWNWdnZuRFRtQndVWWplN3hUY0tDNE4ydGJuaklGR243QVhST051RWdaMXJqRXhXQW9xSW5VZw","type":1,"user":{"avatar":"c6a249645d46209f337279cd2ca998c7","avatar_decoration_data":null,"bot":true,"clan":null,"discriminator":"0000","global_name":"Discord","id":"643945264868098049","public_flags":1,"system":true,"username":"discord"},"version":1}`
//		pubkey, privkey, err := ed25519.GenerateKey(nil)
//		//pubkey, _ = hex.DecodeString("dd8cfd57dd55a937ec033cf15f075e1548df81c95de29be9b17022eaa725970c")
//
//		pubkey, _ = hex.DecodeString("77a29e4172a1c128b8e1010afffff8e15dbd9a05e3a1628cb400738ea8dbea42")
//		privkey, _ = hex.DecodeString("672cd4ee986753cbd9148c928874840bb35bd769cdae3626df75a2a7fe8faeb177a29e4172a1c128b8e1010afffff8e15dbd9a05e3a1628cb400738ea8dbea42")
//		fmt.Println("pubkey:", hex.EncodeToString(pubkey), "\nprivkey:", hex.EncodeToString(privkey))
//		if err != nil {
//			fmt.Println("error generating signing keypair: ", err)
//			return
//		}
//
//		bodyR := strings.NewReader(body)
//
//		timestamp := "1718187522"
//		request := httptest.NewRequest("POST", "http://localhost/interaction", bodyR)
//		request.Header.Set("X-Signature-Timestamp", timestamp)
//
//		var msg bytes.Buffer
//		msg.WriteString(timestamp)
//		msg.WriteString(body)
//		signature := ed25519.Sign(privkey, msg.Bytes())
//		//signature, _ = hex.DecodeString(`04cd3a13085d5d706e5938ee3a605f0378e5583495374a3ed8d9dc64b4f978bcd8d73682c4684a300f9ed247ecd4cb61c673e6fc653372414c27e36c30d86e0a`)
//		request.Header.Set("X-Signature-Ed25519", hex.EncodeToString(signature[:ed25519.SignatureSize]))
//
//		fmt.Println(",,", msg.String())
//		fmt.Println("header sign:", request.Header.Get("X-Signature-Timestamp"), request.Header.Get("X-Signature-Ed25519"))
//		res := discordgo.VerifyInteraction(request, pubkey)
//		fmt.Println("----- boo:", res)
//		return
//
//		//request := httptest.NewRequest("POST", "http://localhost/interaction", strings.NewReader(`{"app_permissions":"180224","application_id":"1250292748364419154","authorizing_integration_owners":{},"entitlements":[],"id":"1250393837910626408","token":"aW50ZXJhY3Rpb246MTI1MDM5MzgzNzkxMDYyNjQwODp3Q0hzMzJ6ems0SmZYZnFnSFoydjA1bFluUHhHYVNjbTNxOHlrNklQdVRTQTl5aFFncm1OR2lyYk1KVUlXWEZEbkdqY3QwUTBMUWNWdnZuRFRtQndVWWplN3hUY0tDNE4ydGJuaklGR243QVhST051RWdaMXJqRXhXQW9xSW5VZw","type":1,"user":{"avatar":"c6a249645d46209f337279cd2ca998c7","avatar_decoration_data":null,"bot":true,"clan":null,"discriminator":"0000","global_name":"Discord","id":"643945264868098049","public_flags":1,"system":true,"username":"discord"},"version":1}`))
//		//request.Header.Set("X-Signature-Timestamp", "1718187522")
//		//request.Header.Set("X-Signature-Ed25519", "04cd3a13085d5d706e5938ee3a605f0378e5583495374a3ed8d9dc64b4f978bcd8d73682c4684a300f9ed247ecd4cb61c673e6fc653372414c27e36c30d86e0a")
//		//pubKeyBytes, _ := hex.DecodeString("dd8cfd57dd55a937ec033cf15f075e1548df81c95de29be9b17022eaa725970c")
//		//
//		//boo := discordgo.VerifyInteraction(request, pubKeyBytes)
//		//fmt.Println("-----aaa", boo)
//	}()
//}

func DsNoticeLog(ctx echo.Context) error {
	fun := "DsNoticeLog ->"
	appId := ctx.QueryParam("app_id")
	logger.Infof(ctx.Request().Context(), "%s param. app_id:%s. header:%+v", fun, appId, ctx.Request().Header)
	var bdBuff, body bytes.Buffer

	// copy body into buffers
	_, err := io.Copy(&bdBuff, io.TeeReader(ctx.Request().Body, &body))
	if err != nil {
		logger.Errorf(ctx.Request().Context(), "%s copy body err. app_id:%s. err:%v.", fun, appId, err)
		return ctx.JSON(http.StatusBadRequest, map[string]any{"status": "read body fail", "error": err.Error()})
	}

	ctx.Request().Body = io.NopCloser(&bdBuff)
	ctx.Request().Body.Close()
	logger.Infof(ctx.Request().Context(), "%s query param detail. app_id:%s. header:%+v.  body:%s.", fun, appId, ctx.Request().Header, bdBuff.String())
	log := &models.FpDsNotice{
		AppID:        appId,
		Handle:       ctx.Request().URL.Path,
		HeaderDetail: utils.ToJson(ctx.Request().Header),
		BodyDetail:   bdBuff.String(),
		CreatedAt:    time.Now(),
	}
	persistence.NewDscInteractions().AddDscNotice(ctx.Request().Context(), log)

	//if discordgo.InteractionType(gjson.ParseBytes(bdBuff.Bytes()).Get("type").Int()) == discordgo.InteractionPing {
	//	logger.Infof(ctx.Request().Context(), "%s InteractionPing2222. body:%s", fun, string(bdBuff.String()))
	//	return ctx.JSON(http.StatusOK, discordgo.InteractionResponse{Type: discordgo.InteractionResponsePong})
	//}

	pubKeyBytes, err := hex.DecodeString("dd8cfd57dd55a937ec033cf15f075e1548df81c95de29be9b17022eaa725970c")
	if err != nil {
		logger.Infof(ctx.Request().Context(), "%s public key bytes err. app_id:%s. err:%v.", fun, appId, err)
		return ctx.JSON(http.StatusInternalServerError, map[string]any{"status": "publicKey get fail", "error": err.Error()})
	}
	boo := discordgo.VerifyInteraction(ctx.Request(), pubKeyBytes)
	logger.Infof(ctx.Request().Context(), "%s VerifyInteraction. boo:%v.", fun, boo)
	if boo == false {
		logger.Errorf(ctx.Request().Context(), "%s VerifyInteraction return fail. app_id:%s. boo:%v. header:%+v. body:%s", fun, appId, boo, ctx.Request().Header, body.String())
		return ctx.JSON(http.StatusUnauthorized, map[string]any{"status": "invalid interaction", "error": "VerifyInteraction fail"})
	}

	bt, err := io.ReadAll(ctx.Request().Body)
	if err != nil {
		logger.Errorf(ctx.Request().Context(), "%s ReadAll err. err:%v", fun, err)
		return ctx.JSON(http.StatusBadRequest, map[string]any{"status": "read body fail2", "error": err.Error()})
	}
	logger.Infof(ctx.Request().Context(), "%s ReadAll. body:%s", fun, string(bt))
	if discordgo.InteractionType(gjson.ParseBytes(bt).Get("type").Int()) == discordgo.InteractionPing {
		logger.Infof(ctx.Request().Context(), "%s InteractionPing. body:%s", fun, string(bt))
		return ctx.JSON(http.StatusOK, discordgo.InteractionResponse{Type: discordgo.InteractionResponsePong})
	}
	logger.Infof(ctx.Request().Context(), "%s InteractionResponse. body:%s. header:%+v", fun, string(bt), ctx.Request().Header)
	return ctx.JSON(http.StatusOK, map[string]any{})
}

type User struct {
	FpxAppId string `json:"fpx_app_id"`
	GameID   int64  `json:"game_id"`
	Fpid     string `json:"fpid"`
	Uid      int64  `json:"uid"`
}

// DiscordVipLink vip专属链接
func DiscordVipLink(ctx echo.Context) error {
	redirectUrl := "https://discord.com/channels/@me/"

	code := ctx.QueryParam("code")
	if len(code) < 3 {
		http.Redirect(ctx.Response().Writer, ctx.Request(), redirectUrl, http.StatusSeeOther)
		return nil
	}
	codeBytes, err := base64.StdEncoding.DecodeString(code[2:])
	if err != nil {
		logger.Errorf(ctx.Request().Context(), "[DiscordVipLink Rrror] %s DecodeString err. err:%v", code[2:], err)
		http.Redirect(ctx.Response().Writer, ctx.Request(), redirectUrl, http.StatusSeeOther)
		return nil
	}
	user := User{}
	err = json.Unmarshal(codeBytes, &user)
	if err != nil {
		logger.Errorf(ctx.Request().Context(), "[DiscordVipLink Rrror] %s Unmarshal err. err:%v", string(codeBytes), err)
		http.Redirect(ctx.Response().Writer, ctx.Request(), redirectUrl, http.StatusSeeOther)
		return nil
	}

	// 再取fpx_app_id
	if user.FpxAppId == "" {
		if user.GameID != 0 {
			user.FpxAppId = cast.ToString(user.GameID)
		} else {
			logger.Errorf(ctx.Request().Context(), "[DiscordVipLink Rrror] %s invalid code. user:%+v", string(codeBytes), user)
			http.Redirect(ctx.Response().Writer, ctx.Request(), redirectUrl, http.StatusSeeOther)
			return nil
		}
	}
	project, err := game.NewGameInfo().GetGameProjectById(user.FpxAppId)
	if err != nil {
		logger.Errorf(ctx.Request().Context(), "[DiscordVipLink Rrror] %s GetGameProjectById err. err:%v", user.FpxAppId, err)
		http.Redirect(ctx.Response().Writer, ctx.Request(), redirectUrl, http.StatusSeeOther)
		return nil
	}

	userInfo, err := persistence.NewDiscordInteract().FetchUserInfo(user.Uid, user.Fpid, project)
	if err != nil {
		logger.Errorf(ctx.Request().Context(), "[DiscordVipLink Rrror] %s FetchUserInfo err. err:%v", user.Fpid, err)
		http.Redirect(ctx.Response().Writer, ctx.Request(), redirectUrl, http.StatusSeeOther)
		return nil
	}

	dscUser, err := persistence.NewDscInteractions().GetDscUserForMaster(ctx.Request().Context(), userInfo.DscUserId, "")
	if err != nil {
		logger.Errorf(ctx.Request().Context(), "[DiscordVipLink Rrror] %s GetDscUserForMaster err. err:%v", userInfo.DscUserId, err)
		http.Redirect(ctx.Response().Writer, ctx.Request(), redirectUrl, http.StatusSeeOther)
		return nil
	}

	redirectUrl = fmt.Sprintf("https://discord.com/channels/@me/%s", dscUser.PrivChannelID)
	http.Redirect(ctx.Response().Writer, ctx.Request(), redirectUrl, http.StatusSeeOther)
	return nil
}
