package examine

import (
	"github.com/labstack/echo/v4"
	"ops-ticket-api/internal/framework/ctxresp"
	"ops-ticket-api/internal/framework/validator"
	"ops-ticket-api/internal/persistence"
	"ops-ticket-api/proto/pb"
	"ops-ticket-api/services/examinesrv"
)

func NewExamineHandle() *examineHandler {
	return &examineHandler{}
}

type examineHandler struct {
}

// TplSave 质检打分表配置 - add&edit
func (h *examineHandler) TplSave(ctx echo.Context) error {
	param := &pb.ExamineTplSaveReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	detail, err := examinesrv.NewExamineTplSrv().TplSave(ctx, param)
	if err != nil {
		return err
	}
	return ctxresp.Out(ctx, detail)
}

func (h *examineHandler) TplCopy(ctx echo.Context) error {
	var param = &pb.ExamineTplCopyReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	detail, err := examinesrv.NewExamineTplSrv().TplCopy(ctx, param)
	if err != nil {
		return err
	}
	return ctxresp.Out(ctx, detail)
}

// TplList 质检打分表配置 - list
func (h *examineHandler) TplList(ctx echo.Context) error {
	param := &pb.ExamineTplListReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	resp, err := examinesrv.NewExamineTplSrv().TplList(ctx, param)
	if err != nil {
		return err
	}
	return ctxresp.Out(ctx, resp)
}

// TplDetail 质检打分表配置 - detail
func (h *examineHandler) TplDetail(ctx echo.Context) error {
	var param = &pb.ExamineTplDetailReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	detail, err := examinesrv.NewExamineTplSrv().TplDetail(ctx, param.GetTplId())
	if err != nil {
		return err
	}
	return ctxresp.Out(ctx, detail)
}

// TplOpts 质检打分表配置 - opts
func (h *examineHandler) TplOpts(ctx echo.Context) error {

	detail, err := persistence.NewExamineSettings().TplOpts(ctx)
	if err != nil {
		return err
	}
	return ctxresp.Out(ctx, &pb.ExamineTplOptsResp{List: detail})
}

// TplEnable 质检打分表配置 - enable
func (h *examineHandler) TplEnable(ctx echo.Context) error {
	var param = &pb.EnableReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	if err := persistence.NewExamineSettings().TplEnable(ctx, param); err != nil {
		return err
	}
	return ctxresp.Out(ctx, pb.Empty{})
}

// TplDel 质检打分表配置 - del
func (h *examineHandler) TplDel(ctx echo.Context) error {
	var param = &pb.ExamineTplDelReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	if err := persistence.NewExamineSettings().TplDel(ctx, param.GetTplId()); err != nil {
		return err
	}
	return ctxresp.Out(ctx, pb.Empty{})
}
