package examine

import (
	"github.com/labstack/echo/v4"
	"ops-ticket-api/internal/framework/ctxresp"
	"ops-ticket-api/internal/framework/validator"
	"ops-ticket-api/proto/pb"
	"ops-ticket-api/services/examinesrv"
)

// TaskList 质检任务 task列表
func (h *examineHandler) TaskList(ctx echo.Context) error {
	var param = &pb.ExamineTaskListReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	res, err := examinesrv.NewExamineTaskSrv().TaskList(ctx, param)
	if err != nil {
		return err
	}
	return ctxresp.Out(ctx, res)
}

// TaskDetail 质检任务 task详情
func (h *examineHandler) TaskDetail(ctx echo.Context) error {
	var param = &pb.ExamineTaskDetailReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	detail, err := examinesrv.NewExamineTaskSrv().TaskDetail(ctx, param)
	if err != nil {
		return err
	}
	return ctxresp.Out(ctx, detail)
}

// TaskSave 质检任务 task保存
func (h *examineHandler) TaskSave(ctx echo.Context) error {
	var param = &pb.ExamineTaskSaveReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	task, err := examinesrv.NewExamineTaskSrv().TaskSave(ctx, param)
	if err != nil {
		return err
	}
	return ctxresp.Out(ctx, task)
}

func (h *examineHandler) TaskDscFilterCount(ctx echo.Context) error {
	var param = &pb.ExamineTaskDscFilterCountReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	res, err := examinesrv.NewExamineTaskSrv().ExamineTaskDscFilterCount(ctx, param)
	if err != nil {
		return err
	}
	return ctxresp.Out(ctx, res)
}

func (h *examineHandler) TaskDscPartFilterCount(ctx echo.Context) error {
	var param = &pb.ExamineTaskDscPartFilterCountReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	res, err := examinesrv.NewExamineTaskSrv().ExamineTaskDscPartFilterCount(ctx, param)
	if err != nil {
		return err
	}
	return ctxresp.Out(ctx, res)
}
