package examine

import (
	"github.com/labstack/echo/v4"
	"github.com/spf13/cast"
	"gitlab-ee.funplus.io/ops-tools/compkg/elog/logger"
	"ops-ticket-api/internal/code"
	"ops-ticket-api/internal/cst"
	"ops-ticket-api/internal/framework/ctxresp"
	"ops-ticket-api/internal/framework/validator"
	"ops-ticket-api/internal/framework/xerrors"
	"ops-ticket-api/proto/pb"
	"ops-ticket-api/services/commsrv"
	"ops-ticket-api/services/examinesrv"
	"os"
)

func (h *examineHandler) OrderNoticeAcceptor(ctx echo.Context) error {
	account := cast.ToString(ctx.Get(cst.AccountNameCtx))
	res, err := examinesrv.NewExamineOrderNoticeSrv().OrderNoticeAcceptor(ctx, account)
	if err != nil {
		return err
	}
	return ctxresp.Out(ctx, res)
}

func (h *examineHandler) OrderNoticeList(ctx echo.Context) error {
	var param = &pb.ExamineOrderNoticeListReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	res, err := examinesrv.NewExamineOrderNoticeSrv().OrderNoticeList(ctx, param)
	if err != nil {
		return err
	}
	return ctxresp.Out(ctx, res)
}
func (h *examineHandler) DscOrderSave(ctx echo.Context) error {
	var param = &pb.ExamineDscOrderSaveDf{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	if err := examinesrv.NewExamineOrderDscSrv().OrderSave(ctx, param); err != nil {
		return err
	}
	return ctxresp.Out(ctx, pb.Empty{})
}

func (h *examineHandler) DscOrderDetail(ctx echo.Context) error {
	var param = &pb.ExamineDscOrderDetailReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	detail, err := examinesrv.NewExamineOrderDscSrv().OrderDetail(ctx, param)
	if err != nil {
		return err
	}
	return ctxresp.Out(ctx, detail)
}
func (h *examineHandler) DscOrderStats(ctx echo.Context) error {
	var param = &pb.ExamineDscOrderStatsReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}

	if len(param.Project) == 0 {
		if permList, _ := commsrv.LoadPermGmList(ctx.Request().Context(), ctx.Get(cst.AccountInfoCtx).(string)); len(permList) > 0 {
			param.Project = permList
		}
	}
	resp, err := examinesrv.NewExamineOrderDscSrv().OrderDscState(ctx, param)
	if err != nil {
		return err
	}
	return ctxresp.Out(ctx, resp)
}

func (h *examineHandler) DscOrderList(ctx echo.Context) error {
	var param = &pb.ExamineDscOrderListReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	if param.Page == 0 {
		param.Page = 1
	}
	if param.PageSize == 0 {
		param.PageSize = 30
	}
	if param.Page*param.PageSize > 10000 {
		return xerrors.New(code.StatusText(code.RecordJumpLimit), code.RecordJumpLimit)
	}

	if len(param.Project) == 0 {
		if permList, _ := commsrv.LoadPermGmList(ctx.Request().Context(), ctx.Get(cst.AccountInfoCtx).(string)); len(permList) > 0 {
			param.Project = permList
		}
	}

	resp, err := examinesrv.NewExamineOrderDscSrv().DscOrderList(ctx, param)
	if err != nil {
		return err
	}
	return ctxresp.Out(ctx, resp)
}

func (h *examineHandler) DscOrderListExport(ctx echo.Context) error {
	var param = &pb.ExamineDscOrderListReq{}
	if err := validator.BindValidate(ctx, param); err != nil {
		return err
	}
	if param.Page == 0 {
		param.Page = 1
	}
	if param.PageSize == 0 {
		param.PageSize = 30
	}
	if param.Page*param.PageSize > 10000 {
		return xerrors.New(code.StatusText(code.RecordJumpLimit), code.RecordJumpLimit)
	}

	if len(param.Project) == 0 {
		if permList, _ := commsrv.LoadPermGmList(ctx.Request().Context(), ctx.Get(cst.AccountInfoCtx).(string)); len(permList) > 0 {
			param.Project = permList
		}
	}

	fileName, err := examinesrv.NewExamineOrderDscSrv().DscOrderListExport(ctx, param)
	if err != nil || fileName == "" {
		logger.Errorf(ctx.Request().Context(), "examine dsc export file:%v err:%v", fileName, err)
		return xerrors.New(err, code.RpcRespErr)
	}
	defer func() {
		if err = os.Remove("./" + fileName); err != nil {
			logger.Errorf(ctx.Request().Context(), "examine dsc export remove file:%v err:%v", fileName, err)
		}
	}()
	ctx.Response().Header().Set("filename", fileName)
	return ctx.File(fileName)
}
